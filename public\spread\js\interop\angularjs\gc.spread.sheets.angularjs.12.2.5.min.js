/*
 *
 * SpreadJS Library
 *
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 *
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 *
 *
 **/
var angular=angular,GC=GC,$=$;!function(){"use strict";var a,b,c,d,e,f,g,h,i,j,k,l,m,n=angular.module("gcspreadsheets",[]);function o(){this.headerText="",this.dataField=""}o.prototype={width:function(a){return 0!==arguments.length?(this.Vo=a,this.column&&this.column.width(a),this):this.column?this.column.width():void 0},visible:function(a){return 0!==arguments.length?(this.bz=a,this.column&&this.column.visible(a),this):this.column?this.column.visible():void 0},resizable:function(a){return 0!==arguments.length?(this.cz=a,this.column&&this.column.resizable(a),this):this.column?this.column.resizable():void 0},defaultStyle:function(a){return 0===arguments.length?this.sheet?this.sheet.getStyle(-1,this.index,GC.Spread.Sheets.SheetArea.viewport):null:(this.yy=a,this.sheet&&this.sheet.setStyle(-1,this.index,a,GC.Spread.Sheets.SheetArea.viewport),this)},dataValidation:function(a){if(0===arguments.length)return this.sheet?this.sheet.getDataValidator(-1,this.index,GC.Spread.Sheets.SheetArea.viewport):null;var b=a.validator;return this.wka=b,this.sheet&&this.sheet.setDataValidator(-1,this.index,b,GC.Spread.Sheets.SheetArea.viewport),this},attach:function(a,b,c){this.sheet=a,this.column=b,this.index=c,this.updata()},updata:function(){this.sheet.suspendEvent(),void 0!==this.Vo&&this.column.width(this.Vo),void 0!==this.bz&&this.column.visible(this.bz),void 0!==this.cz&&this.column.resizable(this.cz),this.yy&&this.sheet.setStyle(-1,this.index,this.yy,GC.Spread.Sheets.SheetArea.viewport),this.wka&&this.sheet.setDataValidator(-1,this.index,this.wka,GC.Spread.Sheets.SheetArea.viewport),this.autoFit&&this.sheet.autoFitColumn(this.index),this.sheet.resumeEvent()}},GC.Spread.Sheets.ColumnWrapper=o,GC.Spread.Sheets.DataValidatorWrapper=function(){this.validator=null};function p(a){var b,c;if(a)return a=a.trim(),b=a.indexOf(",")>=0?a.split(","):a.split(" "),c=new GC.Spread.Sheets.LineBorder,c.color=b[0].trim(),b.length>1&&(c.style=GC.Spread.Sheets.LineStyle[b[1].trim()]),c}function q(a,b,c){if(a&&b){b=b.toLowerCase();var d;"numbervalidator"===b?d=GC.Spread.Sheets.DataValidation.createNumberValidator(c.comparisonOperator,c.value1,c.value2,c.isIntegervalue):"datevalidator"===b?d=GC.Spread.Sheets.DataValidation.createDateValidator(c.comparisonOperator,c.value1,c.value2):"textlengthvalidator"===b?d=GC.Spread.Sheets.DataValidation.createTextLengthValidator(c.comparisonOperator,c.value1,c.value2):"formulavalidator"===b?d=GC.Spread.Sheets.DataValidation.createFormulaValidator(c.formula):"formulalistvalidator"===b?d=GC.Spread.Sheets.DataValidation.createFormulaListValidator(c.formulaList):"listvalidator"===b&&(d=GC.Spread.Sheets.DataValidation.createListValidator(c.list)),d&&(void 0!==c.ignoreBlank&&d.ignoreBlank(c.ignoreBlank),void 0!==c.inCellDropdown&&d.inCellDropdown(c.inCellDropdown),void 0!==c.showInputMessage&&d.showInputMessage(c.showInputMessage),void 0!==c.showErrorMessage&&d.showErrorMessage(c.showErrorMessage),void 0!==c.errorStyle&&d.errorStyle(c.errorStyle),void 0!==c.inputMessage&&d.inputMessage(c.inputMessage),void 0!==c.inputTitle&&d.inputTitle(c.inputTitle),void 0!==c.errorMessage&&d.errorMessage(c.errorMessage),void 0!==c.errorTitle&&d.errorTitle(c.errorTitle),a.validator=d)}}function r(a,b,c){a.Y2=c}function s(a,b,c){if(void 0!==c.text||void 0!==c.value){void 0===c.text?c.text=c.value:void 0===c.value&&(c.value=c.text);var d;a.items()?d=a.items():(d=[],a.items(d)),d.push(c)}}function t(a,b,c){b&&c.outlines&&(b=b.toLowerCase().trim(),angular.forEach(c.outlines,function(c){"rowoutlines"===b?a.rowOutlines.group(c.index,c.count):a.columnOutlines.group(c.index,c.count)}))}function u(a,b,c){a.outlines||(a.outlines=[]),a.outlines.push(c)}function v(a,b){return a[b]().name()}function w(a,b,c){a.Z2=c}function x(a,b,c){a._2=c,c.spread=a}function y(a,b,c){a.push(c)}function z(a,b,c){a.push(c),a.spread.addSheet(a.length-1,c)}function A(a,b,c){a.a3=c}function B(a,b,c){a.borderLeft||(a.borderLeft=c),a.borderTop||(a.borderTop=c),a.borderRight||(a.borderRight=c),a.borderBottom||(a.borderBottom=c)}function C(a,b){return a.options[b]}function D(a,b,c){a.options[b]=c}for(a={backcolor:{type:"string",name:"backColor"},forecolor:{type:"string",name:"foreColor"},halign:{type:"enum, HorizontalAlign",name:"hAlign"},valign:{type:"enum, VerticalAlign",name:"vAlign"},font:{type:"string",name:"font"},themefont:{type:"string",name:"themeFont"},formatter:{type:"string",name:"formatter"},border:{type:"LineBordeer",name:"border",getProperties:["borderLeft","borderTop","borderRight","borderBottom"],setFunction:B,converter:p},borderleft:{type:"LineBorder",name:"borderLeft",converter:p},bordertop:{type:"LineBorder",name:"borderTop",converter:p},borderright:{type:"LineBorder",name:"borderRight",converter:p},borderbottom:{type:"LineBorder",name:"borderBottom",converter:p},locked:{type:"boolean",name:"locked"},wordwrap:{type:"boolean",name:"wordWrap"},textindent:{type:"number",name:"textIndent"},shrinktofit:{type:"boolean",name:"shrinkToFit"},backgroundimage:{type:"string",name:"backgroundImage"},backgroundimagelayout:{type:"enum, ImageLayout",name:"backgroundImageLayout"},textcelltype:{type:"GC.Spread.Sheets.CellTypes.Text",name:"cellType",properties:{}},buttoncelltype:{type:"GC.Spread.Sheets.CellTypes.Button",name:"cellType",properties:{buttonbackcolor:{type:"string",name:"buttonBackColor",setFunction:"buttonBackColor"},marginleft:{type:"number",name:"marginLeft",setFunction:"marginLeft"},margintop:{type:"number",name:"marginTop",setFunction:"marginTop"},marginright:{type:"number",name:"marginRight",setFunction:"marginRight"},marginbottom:{type:"number",name:"marginBottom",setFunction:"marginBottom"},text:{type:"string",name:"text",setFunction:"text"}}},checkboxcelltype:{type:"GC.Spread.Sheets.CellTypes.CheckBox",name:"cellType",properties:{caption:{type:"string",name:"caption",setFunction:"caption"},isthreestate:{type:"boolean",name:"isThreeState",setFunction:"isThreeState"},textalign:{type:"enum,GC.Spread.Sheets.CellTypes.CheckBoxTextAlign",name:"textAlign",setFunction:"textAlign"},textfalse:{type:"string",name:"textFalse",setFunction:"textFalse"},textindeterminate:{type:"string",name:"textIndeterminate",setFunction:"textIndeterminate"},texttrue:{type:"string",name:"textTrue",setFunction:"textTrue"}}},comboboxcelltype:{type:"GC.Spread.Sheets.CellTypes.ComboBox",name:"cellType",properties:{editorvaluetype:{type:"enum,GC.Spread.Sheets.CellTypes.EditorValueType",name:"editorValueType",setFunction:"editorValueType"},item:{type:"object",name:"items",setFunction:s,properties:{value:{type:"string",name:"value"},text:{type:"string",name:"text"}}}}},hyperlinkcelltype:{type:"GC.Spread.Sheets.CellTypes.HyperLink",name:"cellType",properties:{linkcolor:{type:"string",name:"linkColor",setFunction:"linkColor"},linktooltip:{type:"string",name:"linkToolTip",setFunction:"linkToolTip"},text:{type:"string",name:"text",setFunction:"text"},visitedlinkcolor:{type:"string",name:"visitedLinkColor",setFunction:"visitedLinkColor"}}}},b={numbervalidator:{type:"object",name:"numberValidator",setFunction:q,properties:{comparisonoperator:{type:"enum,GC.Spread.Sheets.ConditionalFormatting.ComparisonOperators",name:"comparisonOperator"},value1:{type:"string",name:"value1"},value2:{type:"string",name:"value2"},isintegervalue:{type:"boolean",name:"isIntegerValue"}}},datevalidator:{type:"object",name:"dateValidator",setFunction:q,properties:{comparisonoperator:{type:"enum,GC.Spread.Sheets.ConditionalFormatting.ComparisonOperators",name:"comparisonOperator"},value1:{type:"string",name:"value1"},value2:{type:"string",name:"value2"}}},textlengthvalidator:{type:"object",name:"textLengthValidator",setFunction:q,properties:{comparisonoperator:{type:"enum,GC.Spread.Sheets.ConditionalFormatting.ComparisonOperators",name:"comparisonOperator"},value1:{type:"string",name:"value1"},value2:{type:"string",name:"value2"}}},formulavalidator:{type:"object",name:"formulaValidator",setFunction:q,properties:{formula:{type:"string",name:"formula"}}},formulalistvalidator:{type:"object",name:"formulaListValidator",setFunction:q,properties:{formulalist:{type:"string",name:"formulaList"}}},listvalidator:{type:"object",name:"listValidator",setFunction:q,properties:{list:{type:"string",name:"list"}}}},c=["numbervalidator","datevalidator","textlengthvalidator","formulavalidator","formulalistvalidator","listvalidator"],d=0;d<c.length;d++)e=b[c[d]].properties,e.ignoreblank={type:"boolean",name:"ignoreBlank"},e.incelldropdown={type:"boolean",name:"inCellDropdown"},e.showinputmessage={type:"boolean",name:"showInputMessage"},e.showerrormessage={type:"boolean",name:"showErrorMessage"},e.errorstyle={type:"enum, GC.Spread.Sheets.DataValidation.ErrorStyle",name:"errorStyle"},e.inputmessage={type:"string",name:"inputMessage"},e.inputtitle={type:"string",name:"inputTitle"},e.errormessage={type:"string",name:"errorMessage"},e.errortitle={type:"string",name:"errorTitle"};f={outline:{type:"object",name:"outline",setFunction:u,properties:{index:{type:"number",name:"index"},count:{type:"number",name:"count"}}}},g={datafield:{type:"string",name:"dataField"},headertext:{type:"string",name:"headerText"},width:{type:"number",name:"width",setFunction:"width",getFunction:"width"},visible:{type:"boolean",name:"visible",setFunction:"visible",getFunction:"visible"},resizable:{type:"boolean",name:"resizable",setFunction:"resizable",getFunction:"resizable"},defaultstyle:{type:"Style",name:"defaultStyle",setFunction:"defaultStyle",getFunction:"defaultStyle",properties:a},datavalidation:{type:"DataValidatorWrapper",name:"dataValidation",setFunction:"dataValidation",getFunction:"dataValidation",properties:b},autofit:{type:"boolean",name:"autoFit"}},h={column:{type:"ColumnWrapper",name:"column",setFunction:y,properties:g}},i={name:{type:"string",name:"name",setFunction:"name",getFunction:"name"},frozentrailingcolumncount:{type:"number",name:"frozenTrailingColumnCount",setFunction:"frozenTrailingColumnCount",getFunction:"frozenTrailingColumnCount"},frozentrailingrowcount:{type:"number",name:"frozenTrailingRowCount",setFunction:"frozenTrailingRowCount",getFunction:"frozenTrailingRowCount"},frozencolumncount:{type:"number",name:"frozenColumnCount",setFunction:"frozenColumnCount",getFunction:"frozenColumnCount"},frozenrowcount:{type:"number",name:"frozenRowCount",setFunction:"frozenRowCount",getFunction:"frozenRowCount"},defaultstyle:{type:"Style",name:"defaultStyle",setFunction:{name:"setDefaultStyle",args:["$value-replace$",GC.Spread.Sheets.SheetArea.viewport]},properties:a},rowheaderdefaultstyle:{type:"Style",name:"rowHeaderDefaultStyle",setFunction:{name:"setDefaultStyle",args:["$value-replace$",GC.Spread.Sheets.SheetArea.rowHeader]},properties:a},columnheaderdefaultstyle:{type:"Style",name:"columnHeaderDefaultStyle",setFunction:{name:"setDefaultStyle",args:["$value-replace$",GC.Spread.Sheets.SheetArea.colHeader]},properties:a},allowcelloverflow:{type:"boolean",name:"allowCellOverflow",setFunction:D,getFunction:C},frozenlinecolor:{type:"string",name:"frozenlineColor",setFunction:D,getFunction:C},sheettabcolor:{type:"string",name:"sheetTabColor",setFunction:D,getFunction:C},rowcount:{type:"number",name:"rowCount",setFunction:"setRowCount",getFunction:"getRowCount"},selectionpolicy:{type:"enum, SelectionPolicy",name:"selectionPolicy",setFunction:"selectionPolicy",getFunction:"selectionPolicy"},selectionunit:{type:"enum,SelectionUnit",name:"selectionUnit",setFunction:"selectionUnit",getFunction:"selectionUnit"},zoom:{type:"number",name:"zoom",setFunction:"zoom",getFunction:"zoom"},currenttheme:{type:"string",name:"currentTheme",setFunction:"currentTheme",getFunction:v},clipboardoptions:{type:"enum,ClipboardPasteOptions",name:"clipBoardOptions",setFunction:D,getFunction:C},rowheadervisible:{type:"boolean",name:"rowHeaderVisible",setFunction:D,getFunction:C},colheadervisible:{type:"boolean",name:"colHeaderVisible",setFunction:D,getFunction:C},rowheaderautotext:{type:"enum, HeaderAutoText",name:"rowHeaderAutoText",setFunction:D,getFunction:C},colheaderautotext:{type:"enum, HeaderAutoText",name:"colHeaderAutoText",setFunction:D,getFunction:C},rowheaderautotextindex:{type:"number",name:"rowHeaderAutoTextIndex",setFunction:D,getFunction:C},colheaderautotextindex:{type:"number",name:"colHeaderAutoTextIndex",setFunction:D,getFunction:C},isprotected:{type:"boolean",name:"isProtected",setFunction:D,getFunction:C},showrowoutline:{type:"boolean",name:"showRowOutline",setFunction:"showRowOutline",getFunction:"showRowOutline"},showcolumnoutline:{type:"boolean",name:"showColumnOutline",setFunction:"showColumnOutline",getFunction:"showColumnOutline"},rowoutlines:{type:"object",name:"rowOutlines",setFunction:t,properties:f},columnoutlines:{type:"object",name:"columnOutlines",setFunction:t,properties:f},selectionbackcolor:{type:"string",name:"selectionBackColor",setFunction:D,getFunction:C},selectionbordercolor:{type:"string",name:"selectionBorderColor",setFunction:D,getFunction:C},columns:{type:"[]",name:"columns",setFunction:w,properties:h},datasource:{type:"[]",name:"dataSource",setFunction:A},datasourcedeepwatch:{type:"boolean",name:"dataSourceDeepWatch"}},j={worksheet:{type:"Worksheet",name:"worksheet",setFunction:z,properties:i}},k={name:{type:"string",name:"name"},allowuserzoom:{type:"boolean",name:"allowUserZoom",setFunction:D,getFunction:C},allowuserresize:{type:"boolean",name:"allowUserResize",setFunction:D,getFunction:C},tabstripvisible:{type:"boolean",name:"tabStripVisible",setFunction:D,getFunction:C},tabeditable:{type:"boolean",name:"tabEditable",setFunction:D,getFunction:C},newtabvisible:{type:"boolean",name:"newTabVisible",setFunction:D,getFunction:C},allowusereditformula:{type:"boolean",name:"allowUserEditFormula",setFunction:D,getFunction:C},autofittype:{type:"enum, AutoFitType",name:"autoFitType",setFunction:D,getFunction:C},allowuserdragfill:{type:"boolean",name:"allowUserDragFill",setFunction:D,getFunction:C},allowuserdragdrop:{type:"boolean",name:"allowUserDragDrop",setFunction:D,getFunction:C},highlightinvaliddata:{type:"boolean",name:"highlightInvalidData",setFunction:D,getFunction:C},referencestyle:{type:"enum, ReferenceStyle",name:"referenceStyle",setFunction:D,getFunction:C},backcolor:{type:"string",name:"backColor",setFunction:D,getFunction:C},grayareabackcolor:{type:"string",name:"grayAreaBackColor",setFunction:D,getFunction:C},backgroundimage:{type:"string",name:"backgroundImage",setFunction:D,getFunction:C},backgroundimagelayout:{type:"enum, ImageLayout",name:"backgroundImageLayout",setFunction:D,getFunction:C},showverticalscrollbar:{type:"boolean",name:"showVerticalScrollbar",setFunction:D,getFunction:C},showhorizontalscrollbar:{type:"boolean",name:"showHorizontalScrollbar",setFunction:D,getFunction:C},showscrolltip:{type:"enum, ShowScrollTip",name:"showScrollTip",setFunction:D,getFunction:C},showresizetip:{type:"enum, ShowResizeTip",name:"showResizeTip",setFunction:D,getFunction:C},showdragdroptip:{type:"boolean",name:"showDragDropTip",setFunction:D,getFunction:C},showdragfilltip:{type:"boolean",name:"showDragFillTip",setFunction:D,getFunction:C},datavalidationresult:{type:"enum, GC.Spread.Sheets.DataValidation.DataValidationResult",name:"DataValidationResult",setFunction:r},worksheets:{type:"[]",name:"worksheets",setFunction:x,properties:j}},function(a){a.Ha=[],a.ELEMENT_NODE=1,a.ATTRIBUTE_NODE=2,a.TEXT_NODE=3,a.CDATA_SECTION_NODE=4,a.ENTITY_REFERENCE_NODE=5,a.ENTITY_NODE=6,a.PROCESSING_INSTRUCTION_NODE=7,a.COMMENT_NODE=8,a.DOCUMENT_NODE=9,a.DOCUMENT_TYPE_NODE=10,a.DOCUMENT_FRAGMENT_NODE=11,a.NOTATION_NODE=12}(l||(l={})),m=function(){},m.prototype.setValues=function(){var a=this;this.valueCatch&&angular.forEach(this.valueCatch,function(b){var c=b.target;angular.forEach(b.setting,function(b){var d=b.nodeDef,e=b.value;a.setPropertyValue(c,d,e)})})},m.prototype.setBindings=function(a){var b=a.$parent,c=this;this.bindings&&angular.forEach(this.bindings,function(a){var d,e;a.dynamicText&&(d=a.dynamicText.substring(2,a.dynamicText.length-2),a.target.b3||(a.target.b3={}),a.target.b3[a.name]=d,e=d,void 0===b[e]?b[e]=c.getPropertyValue(a.target,a.metadata):c.setPropertyValue(a.target,a.metadata,b[e]),b.$watch(d,function(b){c.setPropertyValue(a.target,a.metadata,b)}))})},m.prototype.initSpread=function(a,b){var c=b[0];this.c3(a,c,k,"worksheets",!1)},m.prototype.c3=function(a,b,c,d,e){var f,g=this;e||(this.valueCatch||(this.valueCatch=[]),angular.forEach(this.valueCatch,function(b){b.target===a&&(f=b)}),f||(f={target:a,setting:[]},this.valueCatch.push(f))),angular.forEach(b.attributes,function(b){g.d3(a,b,c,f,e)}),b.childNodes.length>0&&angular.forEach(b.childNodes,function(b){var d,h,i,j,k,l,m=b.nodeName.toLowerCase();if(m=g.normalizeName(m),d=c[m],d&&d.type){if("object"===d.type)h={};else if("[]"===d.type)h=[];else{if(i=d.type,i.indexOf(".")>0)for(j=i.split("."),k=window[j[0]],l=1;l<j.length&&(i=k[j[l]],i);l++)k=i;else i=GC.Spread.Sheets[d.type];if(!i)return;h=new i}"worksheets"===d.name||"worksheet"===d.name||"columns"===d.name||"column"===d.name?g.c3(h,b,d.properties,void 0,!1):g.c3(h,b,d.properties,void 0,!0),e?g.setPropertyValue(a,d,h):f.setting.push({nodeDef:d,value:h})}})},m.prototype.convertValue=function(a,b,c){var d,e,f,g,h,i;if(c)return c(a);if(void 0===a||void 0===b)return a;if("string"==typeof a&&(a=a.trim()),b.length>2&&"["===b[0]&&(d=b.substring(1,b.length-2),a.length>2)){for("["===a[0]&&"]"===a[a.length-1]&&(a=a.substring(1,a.length-2)),e=a.split(","),f=[],g=0;g<e.length;g++)f.push(this.convertValue(e[g],d,c));return f}switch(b){case"string":return a;case"boolean":return"boolean"==typeof a?a:"true"===a.toLowerCase()||"false"!==a.toLowerCase()&&!!a;case"number":return+a;case"color":return a;case"[]":return a}if(b.length>5&&"enum,"===b.substring(0,5)){if("number"==typeof a||"string"==typeof a&&void 0!==parseInt(a)&&!isNaN(parseInt(a)))f=parseInt(a);else{if(b=b.substring(5).trim(),h=GC.Spread.Sheets,b.indexOf(".")>0)for(h=window,i=b.split("."),g=0;g<i.length;g++)h=h[i[g]];else h=h[b];f=h[a],void 0===f&&(a=a[0].toUpperCase()+a.substring(1),f=h[a])}return f}return a},m.prototype.normalizeName=function(a){if(a.match(/-/)){var b=a.split("-");a=b.shift(),angular.forEach(b,function(b){a+=b})}return a},m.prototype.d3=function(a,b,c,d,e){var f,g,h,i,j=$(b);switch(b.nodeType){case l.ATTRIBUTE_NODE:f=j.val();break;case l.ELEMENT_NODE:f=j.text();break;default:return}if(g=b.nodeName||b.name,g=g.toLowerCase(),g=this.normalizeName(g),i=c[g])return g=i.name,!this.hasChildElements(b)&&f&&f.length>4&&"{{"===f.substring(0,2)&&"}}"===f.substring(f.length-2)?(this.bindings||(this.bindings=[]),void this.bindings.push({target:a,metadata:i,path:g,name:g,dynamicText:f})):void(f.match(/^[^\d]/)&&b.nodeType===l.ATTRIBUTE_NODE&&(i.changeEvent||i.twoWayBinding)?(this.bindings||(this.bindings=[]),this.bindings.push({target:a,path:(h&&h+".")+g,name:g,expression:f})):b.nodeType===l.ATTRIBUTE_NODE&&(e?this.setPropertyValue(a,i,f):d.setting.push({nodeDef:i,value:f})))},m.prototype.setPropertyValue=function(a,b,c){if(void 0!==c){a.$scopeObject&&(a=a.$scopeObject);try{c=this.convertValue(c,b.type,b.converter),b.setFunction?"function"==typeof b.setFunction?b.setFunction.call(this,a,b.name,c):this.setPropertyValueCore(a,c,void 0,b.setFunction):(this.setPropertyValueCore(a,c,b.name),a[b.name]=c)}catch(a){}}},m.prototype.setPropertyValueCore=function(a,b,c,d){var e,f,g;if(c)a[c]=b;else if(d)if("string"==typeof d)a[d](b);else{for(e=d.name,f=[],g=0;g<d.args.length;g++)"$value-replace$"===d.args[g]?f[g]=b:f[g]=d.args[g];switch(f.length){case 1:a[e](f[0]);break;case 2:a[e](f[0],f[1]);break;case 3:a[e](f[0],f[1],f[2]);break;case 4:a[e](f[0],f[1],f[2],f[3]);break;case 5:a[e](f[0],f[1],f[2],f[3],f[4])}}},m.prototype.getPropertyValue=function(a,b){a.$scopeObject&&(a=a.$scopeObject);var c="";try{if(b.getProperties)angular.forEach(b.getProperties,function(b){c=""===c?this.setPropertyValueCore(a,c,b):c+","+this.setPropertyValueCore(a,c,b)});else if(b.getFunction){if("function"==typeof b.getFunction)return b.getFunction.call(this,a,b.name);c=this.getPropertyValueCore(a,void 0,b.getFunction)}else c=this.getPropertyValueCore(a,name)}catch(a){}return c},m.prototype.getPropertyValueCore=function(a,b,c){return b?a[b]:c&&"string"==typeof c?a[c]():""},m.prototype.hasChildElements=function(a){var b,c,d;if(!a||!a.childNodes)return!1;for(b=a.childNodes.length,c=0;c<b;c++)if(d=a.childNodes[c],d.nodeType===l.ELEMENT_NODE)return!0;return!1},m.angularDerictive=function(){return{restrict:"E",replace:!0,transclude:!0,template:"<div ng-transclude/>",scope:{},controller:["$scope",function(){}],link:function(a,b,c){var d,e,f,g,h,i,j,k,l,n=new m;if(n.initSpread(a,b,c),d=new GC.Spread.Sheets.Workbook(b[0],{sheetCount:0}),e=GC.Spread.Sheets,f=a.$parent,a.$scopeObject=d,d.suspendCalcService(!0),d.suspendPaint(),n.setValues(),g=d._2,h=!1,g&&g.length>0)for(i=0;i<g.length;i++)j=g[i],j.suspendEvent(),o(j),j.a3&&(h=!0,k=!0,void 0!==j.dataSourceDeepWatch&&(k=j.dataSourceDeepWatch),(l=function(a,b){f.$watch(a.a3,function(b,c){p(a,c,b)},b)})(j,k)),j.resumeEvent();h||d.resumePaint(),d.resumeCalcService(!1);function o(b){var c,g=b.Z2&&b.Z2.length>0;if(g)for(b.setColumnCount(b.Z2.length),c=0;c<b.Z2.length;c++)b.Z2[c].attach(b,b.getRange(-1,c,-1,1),c);b.bind(e.Events.ValidationError,function(a,b){void 0!==d.Y2?b.validationResult=d.Y2:b.validationResult=e.DataValidation.DataValidationResult.discard}),b.bind(e.Events.ColumnWidthChanged,function(a,b){var c,d,e,g=b.sheet,h=b.colList;for(c=0;c<h.length;c++)d=g.Z2[h[c]],e=d.b3&&d.b3.width,e&&(f[e]=g.getColumnWidth(h[c]));f.$apply()}),b.bind(e.Events.SheetNameChanged,function(a,c){var d=b.b3&&b.b3.name;d&&(f[d]=c.newValue,f.$apply())}),b.bind(e.Events.UserZooming,function(a,c){var d=b.b3&&b.b3.zoom;d&&(f[d]=c.newZoomFactor,f.$apply())}),n.setBindings(a)}function p(a,b,c){var d,f,g,h,i,j,k=a.getParent();if(k.isPaintSuspended()||k.suspendPaint(),c)if(c!==a.getDataSource())if(d=a.Z2&&a.Z2.length>0)for(a.autoGenerateColumns=!1,a.setDataSource(c,!1),a.setColumnCount(a.Z2.length),f=0;f<a.Z2.length;f++)q(a,f);else{for(g=r(a),a.autoGenerateColumns=!0,a.setDataSource(c,!1),i=a.options.colHeaderAutoTextIndex,h=i<0?a.getRowCount(e.SheetArea.colHeader)-1:i,f=0;f<a.getColumnCount();f++)j=a.getValue(h,f,e.SheetArea.colHeader),0===j.indexOf("$$")&&(a.deleteColumns(f,1),f--);s(a,g)}else c&&b&&c.length!==b.length&&a.setRowCountCore(c.length);else b&&a.setDataSource(null,!0);k.resumePaint()}function q(a,b){var c=a.Z2[b];(c.dataField||c.headerText)&&a.bindColumn(b,{name:c.dataField,displayName:c.headerText}),c.updata()}function r(a){var b,c=[];for(b=0;b<a.getColumnCount();b++)c.push(a.getColumnWidth(b));return c}function s(a,b){if(a.getColumnCount()===b.length)for(var c=0;c<a.getColumnCount();c++)a.setColumnWidth(c,parseInt(b[c],10))}}}},n.directive("gcSpreadSheets",function(){return m.angularDerictive()})}();