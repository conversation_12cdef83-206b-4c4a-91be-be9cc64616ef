<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: y<PERSON>hengqi.ex
 * @LastEditTime: 2025-04-07 16:40:16
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-tabs type="card" @change="callback">
      <a-tab-pane key="1" tab="外报目标值" v-if="target">
        <!-- 月度 -->
        <a-table
          :columns="monthColumns"
          :data-source="monthData"
          bordered
          :pagination="false"
          :scroll="{ x: 1000 }"
          v-if="monthSubmitData.length"
        >
          <template
            v-for="(zitem, zindex) in monthColumns"
            :slot="zitem.key"
            slot-scope="text, record"
          >
            <a-input-number
              v-model="record[zitem.key]"
              :key="zindex"
              style="width: 100%"
              v-if="!['year'].includes(zitem.key)"
            />
            <p :key="monthColumns[zindex].dataIndex" v-else>
              {{ record[zitem.key] }}
            </p>
          </template>
          <template slot="title">
            <h3>
              {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
            </h3>
            <div>目标值填报时，如果是百分数，则填写实际值。</div>
            <div>
              示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
            </div>
          </template>
        </a-table>
        <!-- 季度 -->
        <template v-if="quarterSubmitData.length">
          <a-table
            :columns="quarterColumns"
            :data-source="quarterData"
            bordered
            :pagination="false"
            :scroll="{ x: 1000 }"
          >
            <template
              v-for="(zitem, zindex) in quarterColumns"
              :slot="zitem.key"
              slot-scope="text, record"
            >
              <a-input-number
                v-model="record[zitem.key]"
                :key="zindex"
                style="width: 100%"
                v-if="!['year'].includes(zitem.key)"
              />
              <p :key="quarterColumns[zindex].dataIndex" v-else>
                {{ record[zitem.key] }}
              </p>
            </template>
            <template slot="title">
              <h3>
                {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
              </h3>
              <div>目标值填报时，如果是百分数，则填写实际值。</div>
              <div>
                示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
              </div>
            </template>
          </a-table>
        </template>
        <!-- 年度 -->
        <template v-if="yearSubmitData.length">
          <a-table
            :columns="yearColumns"
            :data-source="yearData"
            bordered
            :pagination="false"
            :scroll="{ x: 1000 }"
          >
            <template
              v-for="(zitem, zindex) in yearColumns"
              :slot="zitem.key"
              slot-scope="text, record"
            >
              <a-input-number
                v-model="record[zitem.key]"
                style="width: 100%"
                :key="zindex"
                v-if="!['year'].includes(zitem.key)"
              />
              <p :key="yearColumns[zindex].dataIndex" v-else>
                {{ record[zitem.key] }}
              </p>
            </template>
            <template slot="title">
              <h3>
                {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
              </h3>
              <div>目标值填报时，如果是百分数，则填写实际值。</div>
              <div>
                示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
              </div>
            </template>
          </a-table>
        </template>
      </a-tab-pane>
      <a-tab-pane key="2" tab="内控目标值" v-if="targetCIM">
        <!-- 月度 -->
        <a-table
          :columns="monthColumns"
          :data-source="monthData"
          bordered
          :pagination="false"
          :scroll="{ x: 1000 }"
          v-if="monthSubmitData.length"
        >
          <template
            v-for="(zitem, zindex) in monthColumns"
            :slot="zitem.key"
            slot-scope="text, record"
          >
            <a-input-number
              v-model="record[zitem.key]"
              :key="zindex"
              style="width: 100%"
              v-if="!['year'].includes(zitem.key)"
            />
            <span :key="monthColumns[zindex].dataIndex" v-else>{{
              record[zitem.key]
            }}</span>
          </template>
          <template slot="title">
            <h3>
              {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
            </h3>
            <div>目标值填报时，如果是百分数，则填写实际值。</div>
            <div>
              示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
            </div>
          </template>
        </a-table>
        <!-- 季度 -->
        <template v-if="quarterSubmitData.length">
          <a-table
            :columns="quarterColumns"
            :data-source="quarterData"
            bordered
            :pagination="false"
            :scroll="{ x: 1000 }"
          >
            <template
              v-for="(zitem, zindex) in quarterColumns"
              :slot="zitem.key"
              slot-scope="text, record"
            >
              <a-input-number
                v-model="record[zitem.key]"
                :key="zindex"
                style="width: 100%"
                v-if="!['year'].includes(zitem.key)"
              />
              <span :key="quarterColumns[zindex].dataIndex" v-else>{{
                record[zitem.key]
              }}</span>
            </template>
            <template slot="title">
              <h3>
                {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
              </h3>
              <div>目标值填报时，如果是百分数，则填写实际值。</div>
              <div>
                示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
              </div>
            </template>
          </a-table>
        </template>
        <!-- 年度 -->
        <template v-if="yearSubmitData.length">
          <a-table
            :columns="yearColumns"
            :data-source="yearData"
            bordered
            :pagination="false"
            :scroll="{ x: 1000 }"
          >
            <template
              v-for="(zitem, zindex) in yearColumns"
              :slot="zitem.key"
              slot-scope="text, record"
            >
              <a-input-number
                v-model="record[zitem.key]"
                style="width: 100%"
                :key="zindex"
                v-if="!['year'].includes(zitem.key)"
              />
              <span :key="yearColumns[zindex].dataIndex" v-else>{{
                record[zitem.key]
              }}</span>
            </template>
            <template slot="title">
              <h3>
                {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
              </h3>
              <div>目标值填报时，如果是百分数，则填写实际值。</div>
              <div>
                示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
              </div>
            </template>
          </a-table>
        </template>
      </a-tab-pane>
      <a-tab-pane key="3" tab="CBG目标值" v-if="targetCBG">
        <!-- 月度 -->
        <a-table
          :columns="monthColumns"
          :data-source="monthData"
          bordered
          :pagination="false"
          :scroll="{ x: 1000 }"
        >
          <template
            v-for="(zitem, zindex) in monthColumns"
            :slot="zitem.key"
            slot-scope="text, record"
          >
            <a-input-number
              v-model="record[zitem.key]"
              :key="zindex"
              style="width: 100%"
              v-if="!['year'].includes(zitem.key)"
            />
            <span :key="monthColumns[zindex].dataIndex" v-else>{{
              record[zitem.key]
            }}</span>
          </template>
          <template slot="title">
            <h3>
              {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
            </h3>
            <div>目标值填报时，如果是百分数，则填写实际值。</div>
            <div>
              示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
            </div>
          </template>
        </a-table>
        <!-- 季度 -->
        <template v-if="quarterSubmitData.length">
          <a-table
            :columns="quarterColumns"
            :data-source="quarterData"
            bordered
            :pagination="false"
            :scroll="{ x: 1000 }"
          >
            <template
              v-for="(zitem, zindex) in quarterColumns"
              :slot="zitem.key"
              slot-scope="text, record"
            >
              <a-input-number
                v-model="record[zitem.key]"
                :key="zindex"
                style="width: 100%"
                v-if="!['year'].includes(zitem.key)"
              />
              <span :key="quarterColumns[zindex].dataIndex" v-else>{{
                record[zitem.key]
              }}</span>
            </template>
          </a-table>
        </template>
        <!-- 年度 -->
        <template v-if="yearSubmitData.length">
          <a-table
            :columns="yearColumns"
            :data-source="yearData"
            bordered
            :pagination="false"
            :scroll="{ x: 1000 }"
          >
            <template
              v-for="(zitem, zindex) in yearColumns"
              :slot="zitem.key"
              slot-scope="text, record"
            >
              <a-input-number
                v-model="record[zitem.key]"
                style="width: 100%"
                :key="zindex"
                v-if="!['year'].includes(zitem.key)"
              />
              <span :key="yearColumns[zindex].dataIndex" v-else>{{
                record[zitem.key]
              }}</span>
            </template>
          </a-table>
        </template>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import createXHR from "xhr";
// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  props: ["record", "target", "targetCIM", "targetCBG"],
  data() {
    return {
      cmimId: "",
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      isEdit: false, // 是否编辑状态
      indexName: "某某某指标",
      indexUnit: "%",
      wd: "",
      monthColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left",
        },
        {
          title: "一月",
          dataIndex: "Jan",
          key: "Jan",
          num: "01",
          scopedSlots: { customRender: "Jan" },
          width: 120,
        },
        {
          title: "二月",
          dataIndex: "Feb",
          key: "Feb",
          num: "02",
          scopedSlots: { customRender: "Feb" },
          width: 120,
        },
        {
          title: "三月",
          key: "Mar",
          dataIndex: "Mar",
          num: "03",
          scopedSlots: { customRender: "Mar" },
          width: 120,
        },
        {
          title: "四月",
          key: "Apr",
          dataIndex: "Apr",
          num: "04",
          scopedSlots: { customRender: "Apr" },
          width: 120,
        },
        {
          title: "五月",
          key: "May",
          dataIndex: "May",
          num: "05",
          scopedSlots: { customRender: "May" },
          width: 120,
        },
        {
          title: "六月",
          key: "Jun",
          dataIndex: "Jun",
          num: "06",
          scopedSlots: { customRender: "Jun" },
          width: 120,
        },
        {
          title: "七月",
          key: "Jul",
          dataIndex: "Jul",
          num: "07",
          scopedSlots: { customRender: "Jul" },
          width: 120,
        },
        {
          title: "八月",
          key: "Aug",
          dataIndex: "Aug",
          num: "08",
          scopedSlots: { customRender: "Aug" },
          width: 120,
        },
        {
          title: "九月",
          key: "Sept",
          dataIndex: "Sept",
          num: "09",
          scopedSlots: { customRender: "Sept" },
          width: 120,
        },
        {
          title: "十月",
          key: "Oct",
          dataIndex: "Oct",
          scopedSlots: { customRender: "Oct" },
          num: "10",
          width: 120,
        },
        {
          title: "十一月",
          key: "Nov",
          dataIndex: "Nov",
          num: "11",
          scopedSlots: { customRender: "Nov" },
          width: 120,
        },
        {
          title: "十二月",
          key: "Dec",
          dataIndex: "Dec",
          num: "12",
          scopedSlots: { customRender: "Dec" },
          width: 120,
        },
      ], // 表格列
      monthData: [],
      monthSubmitData: [],
      quarterColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left",
        },
        {
          title: "第一季度",
          dataIndex: "firstQuarter",
          key: "firstQuarter",
          num: "01",
          scopedSlots: { customRender: "firstQuarter" },
          width: 225,
        },
        {
          title: "第二季度",
          dataIndex: "secondQuarter",
          key: "secondQuarter",
          num: "02",
          scopedSlots: { customRender: "secondQuarter" },
          width: 225,
        },
        {
          title: "第三季度",
          key: "threeQuarter",
          dataIndex: "threeQuarter",
          num: "03",
          scopedSlots: { customRender: "threeQuarter" },
          width: 225,
        },
        {
          title: "第四季度",
          key: "fourQuarter",
          dataIndex: "fourQuarter",
          num: "04",
          scopedSlots: { customRender: "fourQuarter" },
          width: 225,
        },
      ], // 表格列
      quarterData: [],
      quarterSubmitData: [],
      yearColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left",
        },
        {
          title: "上半年",
          dataIndex: "firstHalfYear",
          key: "firstHalfYear",
          num: "01",
          scopedSlots: { customRender: "firstHalfYear" },
          width: 450,
        },
        {
          title: "下半年",
          dataIndex: "secondHalfYear",
          key: "secondHalfYear",
          num: "02",
          scopedSlots: { customRender: "secondHalfYear" },
          width: 450,
        },
      ], // 表格列
      yearData: [],
      yearSubmitData: [],
      isLJ: false, // 是否累加目标值
      curTab: 1,
    };
  },
  methods: {
    show({ indexName, indexUnit, isLJ = false, cmimId }) {
      this.visible = true;
      this.isEdit = true;
      this.indexName = indexName;
      this.indexUnit = indexUnit;
      this.isLJ = isLJ;
      this.cmimId = cmimId;
      this.generateDataItem();
      this.$nextTick(() => {
        this.getIndexTarget(cmimId, "target");
      });
    },
    // 生成数据每一项
    generateDataItem() {
      const year = new Date().getFullYear();
      const common = {
        Jan: "",
        Feb: "",
        Mar: "",
        Apr: "",
        May: "",
        Jun: "",
        Jul: "",
        Aug: "",
        Sept: "",
        Oct: "",
        Nov: "",
        Dec: "",
      };
      const quarterCommon = {
        firstQuarter: "",
        secondQuarter: "",
        threeQuarter: "",
        fourQuarter: "",
      };
      const yearCommon = {
        firstHalfYear: "",
        secondHalfYear: "",
      };
      this.monthData.push({
        year: year - 1,
        ...common,
      });
      // this.quarterData.push({
      //   year: year - 1,
      //   ...quarterCommon
      // });
      // this.yearData.push({
      //   year: year - 1,
      //   ...yearCommon
      // });
      this.monthData.push({
        year,
        ...common,
      });
      this.quarterData.push({
        year: year,
        ...quarterCommon,
      });
      this.yearData.push({
        year: year,
        ...yearCommon,
      });
      this.monthData.push({
        year: year + 1,
        ...common,
      });
      // this.quarterData.push({
      //   year: year + 1,
      //   ...quarterCommon
      // });
      // this.yearData.push({
      //   year: year + 1,
      //   ...yearCommon
      // });
    },
    getIndexTarget(cmimId, type) {
      request(`/api/smc2/newTarget/searchChild`, {
        method: "POST",
        body: {
          cmimId,
          flag: this.isLJ ? "Y" : "N",
        },
      }).then((res) => {
        if (res) {
          this.wd = res.wd;
          const validMonthArr = res.listM;
          validMonthArr.forEach((element) => {
            const arr = element.indexDate.split("-");
            this.updateData(
              arr[0],
              arr[1],
              type === "target"
                ? element.target
                : type === "targetCIM"
                ? element.targetCIM
                : element.targetCBG,
              "month"
            );
          });
          const validQuarterArr = res.listQ;
          validQuarterArr.forEach((element) => {
            const arr = element.indexDate.split("-");
            this.updateData(
              arr[0],
              arr[1],
              type === "target"
                ? element.target
                : type === "targetCIM"
                ? element.targetCIM
                : element.targetCBG,
              "quarter"
            );
          });
          const validYearArr = res.listHF;
          validYearArr.forEach((element) => {
            const arr = element.indexDate.split("-");
            this.updateData(
              arr[0],
              arr[1],
              type === "target"
                ? element.target
                : type === "targetCIM"
                ? element.targetCIM
                : element.targetCBG,
              "year"
            );
          });
          const curYear = new Date().getFullYear();
          // this.monthSubmitData = res.listM;
          const years = [curYear - 1, curYear, curYear + 1];

          // 创建一个包含所有月份的数组
          const allMonths = Array.from(
            { length: 12 },
            (_, i) => `${i + 1 < 10 ? "0" : ""}${i + 1}`
          );

          // 为每个年份和月份生成完整的数据数组
          this.monthSubmitData = res.listM.length
            ? years.flatMap((year) => {
                return allMonths.map((month) => {
                  const existingItem = res.listM.find(
                    (item) =>
                      item.indexDate.startsWith(year) &&
                      item.indexDate.endsWith(month)
                  );
                  return (
                    existingItem || {
                      ...res.listM[0],
                      ...{
                        indexDate: `${year}-${month}`,
                      },
                    }
                  );
                });
              })
            : res.listM;
          this.quarterSubmitData = res.listQ;
          this.yearSubmitData = res.listHF;
        }
      });
    },
    updateData(year, month, target, sign) {
      this[`${sign}Data`].forEach((element) => {
        if (year === String(element.year)) {
          const enKey = this[`${sign}Columns`].filter((item) => {
            return item.num === month.padStart(2, "0");
          })[0].dataIndex;
          element[enKey] = target;
        }
      });
    },
    close() {
      this.monthData = [];
      this.quarterData = [];
      this.yearData = [];
      this.indexName = "";
      this.indexUnit = "";
      this.monthSubmitData = [];
      this.quarterSubmitData = [];
      this.yearSubmitData = [];
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 保存卡片信息
    saveCard() {
      ["month", "quarter", "year"].forEach((typeItem) => {
        console.log(this[`${typeItem}Data`], "======");
        this[`${typeItem}Data`].forEach((yearElement) => {
          for (const key in yearElement) {
            if (Object.hasOwnProperty.call(yearElement, key)) {
              const element = yearElement[key];
              if (key !== "year") {
                const numKey = this[`${typeItem}Columns`].filter(
                  (item) => item.dataIndex === key
                )[0]?.num;
                if (numKey) {
                  this[`${typeItem}SubmitData`].forEach((submitElement) => {
                    if (
                      submitElement.indexDate ===
                      `${yearElement["year"]}-${numKey}`
                    ) {
                      if (this.curTab == 1) {
                        submitElement.target =
                          element === undefined ||
                          element === null ||
                          element === ""
                            ? ""
                            : String(element);
                      } else if (this.curTab == 2) {
                        submitElement.targetCIM =
                          element === undefined ||
                          element === null ||
                          element === ""
                            ? ""
                            : String(element);
                      } else {
                        submitElement.targetCBG =
                          element === undefined ||
                          element === null ||
                          element === ""
                            ? ""
                            : String(element);
                      }
                    }
                  });
                }
              }
            }
          }
        });
      });
      request("/api/smc2/newTarget/update", {
        method: "POST",
        body: [
          ...this.monthSubmitData
            .filter((item) => item.target || item.targetCIM || item.targetCBG)
            .filter((item) => {
              if (this.curTab == 1) {
                return item.target;
              } else if (this.curTab == 2) {
                return item.targetCIM;
              } else {
                return item.targetCBG;
              }
            }),
          ...this.quarterSubmitData
            .filter((item) => item.target || item.targetCIM || item.targetCBG)
            .filter((item) => {
              if (this.curTab == 1) {
                return item.target;
              } else if (this.curTab == 2) {
                return item.targetCIM;
              } else {
                return item.targetCBG;
              }
            }),
          ...this.yearSubmitData
            .filter((item) => item.target || item.targetCIM || item.targetCBG)
            .filter((item) => {
              if (this.curTab == 1) {
                return item.target;
              } else if (this.curTab == 2) {
                return item.targetCIM;
              } else {
                return item.targetCBG;
              }
            }),
        ].map((item) => {
          item.flag = this.isLJ ? "Y" : "N";
          return item;
        }),
      }).then((res) => {
        // if (res && res.result === "success") {
        this.close();
        this.$emit("fetchData");
        // } else {
        //   this.$message.error(res.msg);
        // }
      });
    },
    callback(e) {
      this.curTab = e;
      if (e == "1") {
        this.getIndexTarget(this.cmimId, "target");
      } else if (e == "2") {
        this.getIndexTarget(this.cmimId, "targetCIM");
      } else {
        this.getIndexTarget(this.cmimId, "targetCBG");
      }
    },
  },
  mounted() {},
};
</script>
