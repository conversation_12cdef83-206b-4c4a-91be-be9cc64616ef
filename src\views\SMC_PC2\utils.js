/*
 * @Description: 工具
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 16:15:03
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-10 09:33:49
 */
import Decimal from "decimal.js";
import request from "@/utils/requestHttp";

/**
 * @description: 千位分割
 * @param {Number} num 要分割的数字
 * @return {String}
 */
export function thousandSplit(num) {
  var reg = /\d{1,3}(?=(\d{3})+$)/g;
  if (String(num).includes(".")) {
    const arr = String(num).split(".");
    return (arr[0] + "").replace(reg, "$&,") + "." + arr[1];
  } else {
    return (num + "").replace(reg, "$&,");
  }
}

/**
 * @description: 处理千位展示的数据
 * @param {Number} data 要处理的数据
 * @param {String} indexUnit 指标单位
 * @return {String} displayData 要展示的值
 */

export function dealThousandData(
  data,
  indexUnit,
  precisions,
  openThousandSplit = true
) {
  let fixedNum =
    typeof precisions === "number"
      ? precisions
      : typeof precisions === "string" && precisions
      ? parseInt(precisions)
      : 2;
  if ([null, undefined, "null", "undefined", "-"].includes(data)) {
    return `-`;
  } else {
    let value = new Decimal(data).mul(
      new Decimal(indexUnit === "%" ? 100 : indexUnit === "PPM" ? 1000000 : 1)
    );
    value = value.toFixed(fixedNum, Decimal.ROUND_HALF_UP);
    return openThousandSplit ? thousandSplit(value) : value;
  }
}

// 渲染到页面的style内联样式
export function styleObject(data, cardItem) {
  const indexType = cardItem.dwppCmTfIndexLibrary?.indexType;
  let style = {
    width: 0,
    height: 0,
    "border-right": "5px solid transparent",
    "border-left": "5px solid transparent",
    position: "relative",
    "margin-right": "4px"
  };
  if (data) {
    if (indexType === "正向") {
      if (data.includes("-")) {
        style["border-top"] = "5px solid #6495f9";
        style["top"] = "10px";
      } else {
        style["border-bottom"] = "5px solid #f75050";
        style["top"] = "-10px";
      }
    } else {
      if (data.includes("-")) {
        style["border-bottom"] = "5px solid #f75050";
        style["top"] = "-10px";
      } else {
        style["border-top"] = "5px solid #6495f9";
        style["top"] = "10px";
      }
    }
    return style;
  } else {
    return {};
  }
}

// 获取当前第几周
export function getYearWeek(dateTime = new Date()) {
  // 获取从1970年到现在的时间毫秒数
  var temp_ms = dateTime.getTime();
  let temptTime = new Date(temp_ms);
  // 今天周几，如果是周日，则设为7
  let weekday = temptTime.getDay() & 7;
  // 周1+5天=周六，得到本周6的日期,之所以以每周末的日期为基准，不能用每周日的日期为基准来计算
  // 当前日期的周六的日期
  temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
  // 每年的第一天，年/1/1，参数之中，0代表月份，介于0(1月) ~11(12月)之间的整数，getDay获取星期几同理
  // 第一天的日期
  let firstDay = new Date(temptTime.getFullYear(), 0, 1);
  let dayOfWeek = firstDay.getDay();
  let spendDay = 1;
  // 如果第一天不是星期日，那么就找到下一个星期日作为开始
  if (dayOfWeek != 0) {
    spendDay = 7 - dayOfWeek + 1;
  }
  let yearOfW = temptTime.getFullYear();
  firstDay = new Date(yearOfW, 0, 1 + spendDay);
  /*
    1.Math.ceil 取大于等于所给值的最小整数
    2.86400000是换算到天的基数，js的时间差值为时间戳，即毫秒数
      1000毫秒 * 60秒 * 60分钟* 24小时 = 86400000
    3.temptTime是当前日期，firstDay是当年第一天，周数计算公式就是（当前日期-第一天天数）/7 就是本年的第几周
    4.d是差距天数，res是周数
  */
  let d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
  let res = Math.ceil(d / 7) + 1;
  return res;
}

// 获取该卡片当前基地的报表链接
export const getCardReportList = cardItem => {
  return new Promise(resolve => {
    const postData = {
      sign: cardItem.sign,
      cmimId: cardItem.cmimId,
      signOrg: cardItem.signOrg,
      businessSegments: cardItem.businessSegments,
      org: cardItem.org,
      indexName: cardItem.postEndIndexName
    };
    request(`/api/smc2/cardInfo/getUrl`, {
      method: "POST",
      body: postData
    }).then(res => {
      // if (res && res.reportUrl) {
      //   resolve(res);
      // } else {
      //   resolve("");
      // }
      if (Array.isArray(res) && res.length) {
        resolve(res);
      } else {
        resolve([]);
      }
    });
  });
};

// 打开报表
export const openReport = (reportId, reportParmas = "") => {
  return new Promise(resolve => {
    if (window.self !== window.top) {
      window.parent.postMessage(
        {
          reportId,
          sourceType: "smc",
          reportParmas
        },
        "*"
      );
      resolve();
    } else {
      request(`/api/smc/dt/getDtReportUrl?reportId=${reportId}`).then(res => {
        if (res.url) {
          window.vm.$router.push({
            path: `${
              window.location.origin === "https://cmdt.hisense.com"
                ? "/k1jPJhwg7h"
                : "/mom_pc"
            }/reportPage2`, // 生产
            // path: "/smc_pc2/reportPage2", // 测试
            query: {
              reportUrl:
                decodeURIComponent(res.url) +
                (reportParmas ? `&${reportParmas}` : "")
            }
          });
          resolve();
        }
      });
    }
  });
};

// 查找指定节点
export function findNode(tree, func) {
  for (const node of tree) {
    if (func(node)) return node;
    if (node.list) {
      const res = findNode(node.list, func);
      if (res) return res;
    }
  }
  return null;
}

// 根据周数获取日期
// w 周
// y 年
export function getDateOfISOWeek(w, y) {
  var simple = new Date(y, 0, 1 + (w - 1) * 7);
  var dow = simple.getDay();
  var ISOweekStart = simple;
  if (dow <= 4) ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1);
  else ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay());
  const year = ISOweekStart.getFullYear();
  const month = ISOweekStart.getMonth() + 1;
  const date = ISOweekStart.getDate();
  return `${year}-${String(month).padStart(2, "0")}-${String(date).padStart(
    2,
    "0"
  )}`;
}

// str = new Date("xxxx-xx-xx")
// 获取周
export function getWeek(str) {
  let day = Date.parse(str);
  //如果不是当年的第一天不是星期一，则该日所属周数为上一年的最后一周
  day = new Date(day);

  if (day.getDay() !== 1) {
    day = day.getTime() - 24 * 60 * 60 * 1000;
    day = new Date(day);
  }
  day.setMonth(0);
  day.setDate(1);
  day.setHours(0);
  day.setMinutes(0);
  day.setSeconds(0); //到这里就得到该年的一月一日

  let today = Date.parse(str);
  today = new Date(today);

  //计算日期是一年中的第几天
  let rankDay = Math.ceil(
    (today.getTime() - day.getTime()) / (1000 * 24 * 60 * 60)
  );
  let rankWeek = Math.ceil(rankDay / 7);
  rankWeek = rankWeek < 10 ? "0" + rankWeek : rankWeek;
  // let year = day.getFullYear().toString();
  return rankWeek;
}

// sign = cardList/topCardList/recommendList
export function dealCardOriginData(item, sign) {
  const {
    dmId,
    indexId,
    indexName,
    indexDt,
    fullCode,
    indexFrequency,
    indexFrequencyId,
    org,
    orgId,
    businessSegments,
    businessSegmentsId,
    signOrgId,
    signOrg,
    actualValue,
    targetValue,
    targetCompletionRate,
    previousChangeRate,
    contemChangeRate,
    indexUnitId,
    indexSort,
    productAtt1,
    productAtt2,
    productAtt3,
    productAtt4,
    productAtt1Id,
    productAtt2Id,
    productAtt3Id,
    productAtt4Id,
    productAtt5Id,
    productAtt6Id,
    productAtt7Id,
    previousValue,
    contemValue,
    precisions,
    indexTypeId,
    indexNameInd,
    actualMolecule,
    actualDenominator,
    lable,
    cmimId,
    id,
    recommend,
    companyName,
    searchTimer,
    nowTimer,
    pj // 用于拖拽排序标记
  } = item;

  const normalWDList = [
    productAtt1,
    productAtt2,
    productAtt3,
    productAtt4
  ].filter(item => item && !item.includes("指标卡"));
  let wdInCardName = normalWDList
    .filter(item => item.includes("卡片名称"))
    .map(item => item.split("-")[2]);
  wdInCardName = wdInCardName.join("-");
  const wdInCardTag = normalWDList
    .filter(item => item.includes("卡片标签"))
    .map(item => item.split("-")[2]);

  let cardItem = {
    sign: `${companyName}概览`,
    indexId,
    indexName,
    indexDt,
    fullCode,
    indexFrequency,
    indexFrequencyId,
    org,
    orgId,
    businessSegments,
    businessSegmentsId,
    signOrg,
    signOrgId,
    actualValue: dealThousandData(actualValue, item.indexUnitId, precisions),
    targetValue: dealThousandData(targetValue, item.indexUnitId, precisions),
    targetCompletionRate: targetCompletionRate
      ? `${Decimal(targetCompletionRate)
          .mul(Decimal(100))
          .toFixed(2, Decimal.ROUND_HALF_UP)}%`
      : "",
    indexUnitId,
    indexNameInd,
    indexSort,
    displayIndexName: indexNameInd || indexName,
    pj,
    wdInCardName,
    wdInCardTag,
    normalWDList,
    lable,
    cmimId,
    recommend,
    companyName
  };
  if (sign === "cardList") {
    const displayIndexName = indexNameInd || indexName;
    const c = {
      postEndIndexName:
        (wdInCardName ? wdInCardName + " - " : "") +
        displayIndexName +
        (wdInCardTag.length ? " - " + wdInCardTag.join("-") : ""),
      productAtt1,
      productAtt2,
      productAtt3,
      productAtt4,
      productAtt1Id,
      productAtt2Id,
      productAtt3Id,
      productAtt4Id,
      productAtt5Id,
      productAtt6Id,
      productAtt7Id,
      canDragSort: searchTimer <= nowTimer, // 是否可以进行排序
      showYC: searchTimer > nowTimer // 搜索条件时间年月大于当前时间年月，认为所有指标都是预测
    };
    cardItem = {
      ...cardItem,
      ...c
    };
  }
  if (["cardList", "topCardList"].includes(sign)) {
    let fixedNum =
      typeof precisions === "number"
        ? precisions
        : typeof precisions === "string" && precisions
        ? parseInt(precisions)
        : 2;
    const c = {
      dmId,
      id,
      actualMolecule: actualMolecule
        ? new Decimal(actualMolecule).toFixed(fixedNum, Decimal.ROUND_HALF_UP)
        : "-",
      actualDenominator: actualDenominator
        ? new Decimal(actualDenominator).toFixed(
            fixedNum,
            Decimal.ROUND_HALF_UP
          )
        : "-",
      previousChangeRate,
      isPreviousRate: "Y",
      isContemRate: "Y",
      contemChangeRate,
      indexType: indexTypeId,
      previousValue: previousValue
        ? new Decimal(previousValue).toFixed(fixedNum, Decimal.ROUND_HALF_UP)
        : "-",
      contemValue: contemValue
        ? new Decimal(contemValue).toFixed(fixedNum, Decimal.ROUND_HALF_UP)
        : "-",
      show: searchTimer <= nowTimer // 日月周三种时间，搜索条件时间大于当前时间所有指标都隐藏
    };
    cardItem = {
      ...cardItem,
      ...c
    };
  }
  return cardItem;
}
