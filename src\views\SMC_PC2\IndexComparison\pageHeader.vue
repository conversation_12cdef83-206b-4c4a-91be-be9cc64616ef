<!--
 * @Description: 页头
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:36:50
 * @LastEditors: yuy<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-12-27 09:44:25
-->
<template>
  <div class="_pageHeader">
    <!-- 标题 -->
    <div class="_flex">
      <span class="_title" style="margin-right: 12px;">{{ title }}</span>
      <!-- 引导 -->
      <a-tooltip placement="top">
        <template slot="title">
          <span>操作引导</span>
        </template>
        <a-icon
          @click="showZZ = true"
          type="book"
          style="color: rgba(0,0,0,0.35);"
        />
      </a-tooltip>
    </div>
    <div class="_right _flex">
      <!-- 分析维度 -->
      <div class="fxwd _flex">
        <span>分析维度：</span>
        <a-select v-model="dimension">
          <a-select-option value="orgDimension">
            组织维度
          </a-select-option>
          <a-select-option value="otherDimension">
            其他维度
          </a-select-option>
        </a-select>
      </div>
      <!-- 模式 -->
      <!-- <div class="mode _flex">
        <span>模式：</span>
        <a-radio-group
          v-model="mode"
          style="margin-right: 8px;"
          button-style="solid"
          @change="
            e => {
              this.$emit('modeChange', e.target.value);
            }
          "
        >
          <a-radio-button
            :value="item.key"
            v-for="item in modeTypeOptions"
            :key="item.key"
          >
            {{ item.value }}
          </a-radio-button>
        </a-radio-group>
      </div> -->
      <!-- 时间 -->
      <div class="time _flex">
        <span>时间：</span>
        <a-radio-group
          v-model="searchForm.timeType"
          style="margin-right: 8px;"
          button-style="solid"
          @change="timeTypeChange"
        >
          <a-radio-button
            :value="item.key"
            v-for="item in timeTypeOptions"
            :key="item.key"
          >
            {{ item.value }}
          </a-radio-button>
        </a-radio-group>
        <!-- 月选择器 -->
        <a-month-picker
          v-model="searchForm.time"
          :allowClear="false"
          :disabled-date="disabledDate"
          v-if="searchForm.timeType === 'month'"
          :format="monthFormat"
        />
        <a-select
          show-search
          placeholder="选择年"
          style="width: 90px"
          :filter-option="filterOption"
          v-model="selectedYearWeek[0]"
          v-if="searchForm.timeType === 'week'"
        >
          <a-select-option
            :value="item"
            v-for="item in yearSelectOptions"
            :key="item"
          >
            {{ item }}年
          </a-select-option>
        </a-select>
        <a-select
          show-search
          placeholder="选择周"
          style="width: 90px"
          :filter-option="filterOption"
          v-model="selectedYearWeek[1]"
          v-if="searchForm.timeType === 'week'"
        >
          <a-select-option
            :value="item"
            v-for="item in weekSelectOptions"
            :key="item"
          >
            {{ item }}周
          </a-select-option>
        </a-select>
        <!-- 日选择器 -->
        <a-date-picker
          v-model="searchForm.time"
          :allowClear="false"
          :disabled-date="disabledDate"
          v-if="searchForm.timeType === 'day'"
          :format="dateFormat"
        />
      </div>
      <!-- 组织 -->
      <div class="org _flex">
        <span>维度选择：</span>
        <template v-if="dimension === 'orgDimension'">
          <a-tree-select
            v-model="searchForm.org"
            style="width: 130px"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="orgList"
            :dropdownMatchSelectWidth="false"
            placeholder="请选择组织"
            :treeExpandedKeys.sync="treeExpandedKeys"
            :replaceFields="replaceFields"
          >
          </a-tree-select>
        </template>
        <template v-else>
          <a-select v-model="searchForm.wd" style="width: 130px;">
            <a-select-option
              :value="item.key"
              v-for="item in wdList"
              :key="item.key"
              :title="item.key"
            >
              {{ item.key }}
            </a-select-option>
          </a-select>
        </template>
      </div>
    </div>
    <!-- 引导遮罩 -->
    <div class="guidezz" @click="closeZZ" v-if="showZZ">
      <div>
        <div class="title">
          <span>功能操作视频引导，点击非视频任意处退出播放</span>
          <a-icon type="close" class="icon" @click="closeZZ" />
        </div>
        <div class="img"></div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { getYearWeek } from "../utils";
export default {
  props: {
    companyName: String,
    orgList: {
      type: Array,
      default() {
        return [];
      }
    },
    ppOrgList: {
      type: Array,
      default() {
        return [];
      }
    },
    wdList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  watch: {
    // 监听组织列表变化
    orgList: {
      handler(newVal) {
        if (Array.isArray(newVal) && newVal.length) {
          if (
            !this.searchForm.org ||
            this.ppOrgList.filter(item => item.fullCode === this.searchForm.org)
              .length === 0
          ) {
            // 赋值saerchForm.org 组织为多选
            this.searchForm.org = newVal.map(item => item.fullCode)[0];
            this.isBaseChange = true;
          } else {
            this.sendRequest();
          }
        } else {
          this.searchForm.org = "";
        }
      },
      deep: true
    },
    // 监听维度列表变化
    wdList: {
      handler(newVal) {
        if (Array.isArray(newVal) && newVal.length) {
          // 赋值saerchForm.wd 组织为单选
          if (
            !this.searchForm.wd ||
            newVal.filter(item => item.key === this.searchForm.wd).length === 0
          ) {
            this.searchForm.wd = newVal[0].key;
          } else {
            this.sendRequest();
          }
        } else {
          this.searchForm.wd = "";
        }
      },
      deep: true
    },
    // 监听searchForm值改变
    searchForm: {
      handler(val) {
        //  && this.isBaseChange === false
        if (val.org) {
          // 保存搜索条件到本地
          localStorage.setItem(
            `${this.$route.name}-searchForm`,
            JSON.stringify(this.searchForm)
          );
          // 改变后向父组件发出请求
          this.debounce(this.sendRequest());
        } else {
          this.isBaseChange = false;
        }
      },
      deep: true
    },
    // 监听周模式下的selectedYearWeek值改变
    selectedYearWeek(val) {
      // 根据选中年份去动态生成周下拉框
      this.initWeekSelect(val[0]);
      // 如果周下拉框中没有当前已选中的周值，则默认选成第一周
      if (!this.weekSelectOptions.includes(val[1])) {
        this.selectedYearWeek[1] = this.weekSelectOptions[0];
      }
      // searchFrom.time赋值
      this.searchForm.time = `${val[0]}-${String(val[1]).padStart(2, "0")}`;
    },
    // 监听分析维度改变
    dimension: {
      handler(val) {
        this.$emit("dimensionChange", val);
      },
      immediate: true
    }
  },
  data() {
    return {
      moment,
      title: `${this.companyName}核心KPI横比`,
      showZZ: false,
      mode: "chart",
      searchForm: {
        // 页面中查询条件
        org: "",
        wd: "",
        timeType: "month",
        time: moment(new Date().getTime(), this.monthFormat)
      },
      isBaseChange: false, // 在组织下拉框更新时和searchForm表单更新时做判断防止重复请求
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: "month",
          value: "按月"
        },
        {
          key: "week",
          value: "按周"
        },
        {
          key: "day",
          value: "按日"
        }
      ],
      modeTypeOptions: [
        // 模式下拉框
        {
          key: "chart",
          value: "图表"
        },
        {
          key: "table",
          value: "表格"
        }
      ],
      dimension: "orgDimension", // 分析维度
      dateFormat: "YYYY-MM-DD", // 日期、周格式化
      monthFormat: "YYYY-MM", // 月格式化
      weekFormat: "YYYY-ww", // 周格式化
      yearSelectOptions: [], // 周模式下年下拉框
      weekSelectOptions: [], // 周模式下周下拉框
      selectedYearWeek: ["", ""], // 周模式下选中的年周
      treeExpandedKeys: [], // 组织展开的树节点
      replaceFields: {
        children: "list",
        title: "org",
        key: "fullCode",
        value: "fullCode"
      }
    };
  },
  created() {
    // 初始化年周下拉框
    this.initYearSelect();
    this.initWeekSelect();

    // 如果没有看过视频引导，展示视频引导
    if (!localStorage.getItem("IndexComparisonGuide")) {
      this.showZZ = true;
    }
  },
  methods: {
    debounce: function(fn, wait = 1000) {
      if (this.fun !== null) {
        clearTimeout(this.fun);
      }
      this.fun = setTimeout(fn, wait);
    },
    // 下拉框输入搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 禁用日历
    disabledDate(current) {
      // Can not select days before today and today
      // 今天之后或者两年前
      return (
        (current && current > moment().endOf("day")) ||
        moment().subtract(2, "years") > current
      );
    },
    // 关闭遮罩层
    closeZZ() {
      this.showZZ = false;
      localStorage.setItem("IndexComparisonGuide", "read");
    },
    // 时间类型改变
    timeTypeChange(e) {
      const val = e.target.value;
      this.$emit("treeSelect");
      if (val === "day") {
        this.searchForm.time = moment(new Date().getTime() - 86400000).format(
          this.dateFormat
        );
      } else if (val === "week") {
        this.initWeekSelect(new Date().getFullYear());
        this.selectedYearWeek = [new Date().getFullYear(), getYearWeek()];
      } else {
        this.searchForm.time = moment(new Date().getTime()).format(
          this.monthFormat
        );
      }
    },
    // 发起请求
    sendRequest() {
      this.$emit("pageHeaderChange", this.searchForm);
    },
    // 获取当前第几周
    getYearWeek(dateTime = new Date()) {
      // 获取从1970年到现在的时间毫秒数
      var temp_ms = dateTime.getTime();
      let temptTime = new Date(temp_ms);
      // 今天周几，如果是周日，则设为7
      let weekday = temptTime.getDay() & 7;
      // 周1+5天=周六，得到本周6的日期,之所以以每周末的日期为基准，不能用每周日的日期为基准来计算
      // 当前日期的周六的日期
      temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
      // 每年的第一天，年/1/1，参数之中，0代表月份，介于0(1月) ~11(12月)之间的整数，getDay获取星期几同理
      // 第一天的日期
      let firstDay = new Date(temptTime.getFullYear(), 0, 1);
      let dayOfWeek = firstDay.getDay();
      let spendDay = 1;
      // 如果第一天不是星期日，那么就找到下一个星期日作为开始
      if (dayOfWeek != 0) {
        spendDay = 7 - dayOfWeek + 1;
      }
      let yearOfW = temptTime.getFullYear();
      firstDay = new Date(yearOfW, 0, 1 + spendDay);
      /*
        1.Math.ceil 取大于等于所给值的最小整数
        2.86400000是换算到天的基数，js的时间差值为时间戳，即毫秒数
          1000毫秒 * 60秒 * 60分钟* 24小时 = 86400000
        3.temptTime是当前日期，firstDay是当年第一天，周数计算公式就是（当前日期-第一天天数）/7 就是本年的第几周
        4.d是差距天数，res是周数
      */
      let d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
      let res = Math.ceil(d / 7) + 1;
      return res;
    },
    initWeekSelect(year = new Date().getFullYear()) {
      let weekSelectOptions = this.createWeeks(year);
      if (
        year === this.yearSelectOptions[this.yearSelectOptions.length - 1] &&
        this.yearSelectOptions.length === 3
      ) {
        // 如果是最前边的一年且年份下拉框为三个时，要处理月份选择选两年前的周
        this.weekSelectOptions = weekSelectOptions.filter(
          item => item > getYearWeek()
        );
      } else {
        this.weekSelectOptions = weekSelectOptions;
      }
    },
    initYearSelect() {
      let yearSelectOptions = [];
      // 两年前的年份不允许选择
      let beginningYear = moment().get("year") - 2;
      // 如果当前月份是12月则，年份选择扣除一年
      if (moment().get("month") + 1 === 12) {
        beginningYear++;
      }
      for (let i = beginningYear; i <= new Date().getFullYear(); i++) {
        yearSelectOptions.unshift(i);
      }
      this.yearSelectOptions = yearSelectOptions;
    },
    // 获取自然周
    createWeeks(year) {
      var d = new Date(year, 0, 1);
      while (d.getDay() != 1) {
        d.setDate(d.getDate() + 1);
      }
      var to = new Date(year + 1, 0, 1);
      var i = 1;
      let arr = [];
      for (var from = d; from < to; ) {
        from.setDate(from.getDate() + 6);
        if (from < to) {
          from.setDate(from.getDate() + 1);
        } else {
          to.setDate(to.getDate() - 1);
        }
        arr.push(i);
        i++;
      }
      return arr;
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexComparisonPage2 {
  ._pageHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    ._title {
      height: 32px;
      font-family: PingFangSC-Semibold;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 32px;
    }
    ._right {
      .fxwd,
      .time,
      .mode {
        margin-right: 12px;
      }
    }
    .guidezz {
      width: 100vw;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.35);
      z-index: 55;
      display: flex;
      align-items: center;
      justify-content: center;
      & > div {
        background-color: #fff;
        border-radius: 3px;
        overflow: hidden;
        .title {
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            display: block;
            font-weight: bold;
          }
          .icon {
            font-size: 16px;
          }
        }
        .img {
          width: 1024px;
          height: 449px;
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
          background-image: url("/minio/smcbucket/%E4%BA%91%E5%9B%BE%E6%A8%AA%E6%AF%94%E5%BD%95%E5%B1%8F%E5%B0%8F%E5%B1%8F%E5%B9%95.gif");
          cursor: pointer;
        }
      }
    }
  }
}
</style>
