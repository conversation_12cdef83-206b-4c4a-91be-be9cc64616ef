<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-12-10 15:49:32
 * @LastEditors: y<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-12 15:57:25
 * @Description: 
 * @FilePath: \pangea-component\src\views\boot-console\org-manage\dialogTreeSelect.vue
-->
<template>
    <div class="tree-node">
        <div class="node-content">
            <span class="node-label">{{ node.name }}:</span>
            <a-select :value="localLevelName" class="level-select" @change="onLevelNameChange">
                <a-select-option v-for="level in dict" :key="level.key" :value="level.value">
                    {{ level.value }}
                </a-select-option>
            </a-select>
            <a-select :value="localAngleOfView" mode="multiple" placeholder="请选择视角"
                style="width: 120px;margin-left: 10px;" @change="onAngleOfViewChange">
                <a-select-option value="管理视角">管理视角</a-select-option>
                <a-select-option value="制造视角">制造视角</a-select-option>
            </a-select>
            <a-input :value="localDhrCode" placeholder="请输入dhrCode" class="code-input" @change="onDhrCodeChange" />
            <div v-if="localLevelName === '线体'">
                <a-input :value="localSapCode" placeholder="请输入sapCode" class="code-input" @change="onSapCodeChange" />
            </div>
        </div>
        <div v-if="node.children && node.children.length" class="children-wrapper">
            <tree-node v-for="child in node.children" :key="child.code" :node="child" :dict="dict"
                @update="onChildUpdate" />
        </div>
    </div>
</template>

<script>
export default {
    name: 'TreeNode',
    props: {
        node: {
            type: Object,
            required: true
        },
        dict: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            localLevelName: this.node.levelName?this.node.levelName:undefined,
            localAngleOfView: this.node.angleOfView?this.node.angleOfView:undefined,
            localDhrCode: this.node.dhrCode?this.node.dhrCode:undefined,
            localSapCode: this.node.sapCode?this.node.sapCode:undefined
        }
    },
    methods: {
        onLevelNameChange(value) {
            this.localLevelName = value;
            this.emitUpdate();
        },
        onAngleOfViewChange(value) {
            this.localAngleOfView = value;
            this.emitUpdate();
        },
        onDhrCodeChange(e) {
            this.localDhrCode = e.target.value;
            this.emitUpdate();
        },
        onSapCodeChange(e) {
            this.localSapCode = e.target.value;
            this.emitUpdate();
        },
        onChildUpdate(childData) {
            // 直接将子节点的更新向上传递
            this.$emit('update', childData);
        },
        emitUpdate() {
            // 向父组件发送更新事件，确保包含所有必要的属性
            const updatedNode = {
                ...this.node,
                levelName: this.localLevelName,
                angleOfView: this.localAngleOfView,
                dhrCode: this.localDhrCode,
                sapCode: this.localSapCode
            };
            this.$emit('update', updatedNode);
        }
    },
    watch: {
        node: {
            handler(newVal) {
                this.localLevelName = newVal.levelName ? newVal.levelName : undefined;
                this.localAngleOfView = newVal.angleOfView ? newVal.angleOfView : undefined;
                this.localDhrCode = newVal.dhrCode ? newVal.dhrCode : undefined;
                this.localSapCode = newVal.sapCode ? newVal.sapCode : undefined;
            },
            deep: true,
            immediate: true
        }
    }
}
</script>

<style lang="less" scoped>
.tree-node {
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s;

    .node-content {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .node-label {
            min-width: 100px;
            font-weight: 500;
            color: #333;
        }

        .level-select {
            width: 200px;
            margin: 0 10px;
        }

        .code-input {
            width: 150px;
            margin-left: 10px;
        }
    }

    .children-wrapper {
        margin-left: 24px;
        padding-left: 12px;
        border-left: 1px dashed #d9d9d9;
    }
}
</style>