<template>
  <div class="CardDetectionInfo" :class="[skinStyle()]">
    <div class="drawerTitle" slot="title">
      <div class="_left">
        <div class="index-info _flex" v-if="cardItem.displayIndexName">
          <div class="title">
            {{ cardItem.wdInCardName ? cardItem.wdInCardName + " - " : ""
            }}{{ cardItem.displayIndexName }}
          </div>
          <div
            class="plate tag"
            :style="{
              backgroundColor: this.businessSegmentsColorMap()[
                cardItem.businessSegments
              ]?.bgColor,
              color: this.businessSegmentsColorMap()[cardItem.businessSegments]
                ?.color
            }"
          >
            {{ cardItem.businessSegments }}
          </div>
          <div class="wd tag" v-for="item in cardItem.wdInCardTag" :key="item">
            {{ item }}
          </div>
        </div>
        <a-button
          type="primary"
          size="small"
          style="margin-left: 10px;"
          @click="close"
        >
          <a-icon type="left" />返回
        </a-button>
      </div>
      <span>组织层级下探</span>
    </div>

    <div class="bottom-container" id="bottom-container">
      <!-- 左边部分 -->
      <div class="left-part" :class="chartShowControl ? 'open' : 'close'">
        <a-button
          type="primary"
          class="switchTable-btn"
          size="small"
          @click="$refs['modal'].showModal()"
        >
          <a-icon type="swap" />切换表格
        </a-button>
        <div class="__c" :style="{ zoom: zoom }" :ref="cardItem.pj || 'x00001'">
          <template v-if="Object.keys(datas).length">
            <vue2-org-tree
              ref="leftpartdiv"
              :data="datas"
              :horizontal="horizontal"
              :collapsable="collapsable"
              :label-class-name="labelClassName"
              :render-content="renderContent"
              @on-expand="onExpand"
              name="organ"
            />
          </template>
          <template v-else>
            <a-spin :spinning="dataLoading">
              <div style="height: calc(100% - 137px);width: 100%;"></div>
            </a-spin>
          </template>
        </div>
        <div class="operation-area">
          {{ (zoom * 100).toFixed(0) }}%
          <a-tooltip placement="left">
            <template slot="title">
              <span>放大</span>
            </template>
            <a-button icon="plus" @click="zoomOut"></a-button>
          </a-tooltip>
          <a-tooltip placement="left">
            <template slot="title">
              <span>缩小</span>
            </template>
            <a-button icon="minus" @click="zoomIn"></a-button>
          </a-tooltip>
          <a-tooltip placement="left">
            <template slot="title">
              <span>还原</span>
            </template>
            <a-button icon="undo" @click="zoom = 1"></a-button>
          </a-tooltip>
        </div>
        <!-- <template v-if="activeIndexId">
          <div class="tip-area">
            <div>
              <div class="color active"></div>
              已选中
            </div>
            <div>
              <div class="color parent"></div>
              父级
            </div>
            <div>
              <div class="color slide"></div>
              同层级
            </div>
            <div>
              <div class="color child"></div>
              子级
            </div>
          </div>
        </template> -->
      </div>
      <!-- 右边部分 -->
      <div class="right-part" :class="chartShowControl ? 'open' : 'close'">
        <div class="control" @click="controlClick">
          <a-icon type="right" />
        </div>
        <div class="container">
          <!-- 本层级完成情况 -->
          <a-spin :spinning="complateChartLoading">
            <div
              ref="complateDiv"
              style="width: 100%;height: 300px;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;"
            ></div>
          </a-spin>
          <a-spin :spinning="compareChartLoading">
            <div
              ref="compareDiv"
              style="width: 100%;height: 300px;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;"
            ></div>
          </a-spin>
          <div
            style="width: 100%;height: 300px;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;position: relative;"
          >
            <a-spin :spinning="wdChartLoading">
              <div ref="wdDiv" style="width: 100%;height: 300px;"></div>
            </a-spin>
            <a-select
              @change="wdChartSelectChange"
              v-model="wdChartSelect"
              style="position: absolute;right: 20px;top: 8px;width: 130px;"
            >
              <a-select-option
                :value="item"
                v-for="item in activeIndexItem.goDown"
                :key="item"
                :title="item"
                >{{ item }}</a-select-option
              >
            </a-select>
          </div>
        </div>
      </div>
    </div>
    <!-- 维度下探 -->
    <a-drawer
      title="维度下探"
      placement="right"
      :closable="true"
      :visible="visible"
      width="315px"
      @close="visible = false"
    >
      <!-- <template v-for="item in wdXTList">
        {{ renderContent(h, item) }}
      </template> -->
      <div
        class="card-in-detection-block"
        v-for="item in wdXTList"
        :key="item.indexId"
        style="margin-bottom: 10px"
      >
        <div class="card">
          <div class="top">
            <div class="_t">{{ item.wd }}</div>
          </div>
          <template v-if="item.isDelete === null || item.isDelete === 'Y'">
            <div
              class="sjz"
              :style="item.isDelete ? 'color: #5f8cc0;' : 'color: #a5a5a5;'"
            >
              不计算
            </div>
          </template>
          <template v-else>
            <div
              class="sjz"
              :style="{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                color: item.targetCompletionRate
                  ? parseFloat(item.targetCompletionRate) < 100
                    ? 'rgba(52, 145, 250, 1)'
                    : 'rgb(245, 63, 63)'
                  : ''
              }"
            >
              {{ item.actualValue }}
              {{ item.indexUnitId }}
            </div>
          </template>
          <div class="intro">
            <div>目标值：{{ item.targetValue }} {{ item.indexUnitId }}</div>
            <div>完成率：{{ item.targetCompletionRate }}</div>
          </div>
          <div class="compare _flex">
            <a-row style="width: 100%;">
              <a-col :span="12">
                <div class="_flex _c">
                  <span>同比</span>
                  <template v-if="item.isContemRate === 'Y'">
                    <div
                      :style="{
                        color: item.contemChangeRate
                          ? item.indexType === '正向'
                            ? item.contemChangeRate.includes('-')
                              ? '#6495F9'
                              : '#f75050'
                            : item.contemChangeRate.includes('-')
                            ? '#f75050'
                            : '#6495F9'
                          : 'rgba(0, 0, 0, 0.8)'
                      }"
                      class="_flex"
                    >
                      <a-icon
                        v-if="item.contemChangeRate"
                        :type="
                          item.indexType === '正向'
                            ? item.contemChangeRate.includes('-')
                              ? 'caret-down'
                              : 'caret-up'
                            : item.contemChangeRate.includes('-')
                            ? 'caret-up'
                            : 'caret-down'
                        "
                      />
                      <span>
                        {{
                          item.contemChangeRate
                            ? Math.abs(
                                Decimal(item.contemChangeRate)
                                  .mul(Decimal(100))
                                  .toFixed(2, Decimal.ROUND_HALF_UP)
                              ) + "%"
                            : "-"
                        }}
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <span
                      style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                    >
                      不对比
                    </span>
                  </template>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="_flex _c">
                  <span>同比</span>
                  <template v-if="item.isPreviousRate === 'Y'">
                    <div
                      :style="{
                        color: item.previousChangeRate
                          ? item.indexType === '正向'
                            ? item.previousChangeRate.includes('-')
                              ? '#6495F9'
                              : '#f75050'
                            : item.previousChangeRate.includes('-')
                            ? '#f75050'
                            : '#6495F9'
                          : 'rgba(0, 0, 0, 0.8)'
                      }"
                      class="_flex"
                    >
                      <a-icon
                        v-if="item.previousChangeRate"
                        :type="
                          item.indexType === '正向'
                            ? item.previousChangeRate.includes('-')
                              ? 'caret-down'
                              : 'caret-up'
                            : item.previousChangeRate.includes('-')
                            ? 'caret-up'
                            : 'caret-down'
                        "
                      />
                      <span>
                        {{
                          item.previousChangeRate
                            ? Math.abs(
                                Decimal(item.previousChangeRate)
                                  .mul(Decimal(100))
                                  .toFixed(2, Decimal.ROUND_HALF_UP)
                              ) + "%"
                            : "-"
                        }}
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <span
                      style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                    >
                      不对比
                    </span>
                  </template>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </a-drawer>
    <Modal ref="modal" :tableData="tableData" />
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
import * as echarts from "echarts";
// import axios from "axios";
import cloneDeep from "lodash/cloneDeep";
import { dealThousandData } from "../../../utils";
import Vue2OrgTree from "vue2-org-tree";
import Decimal from "decimal.js";
import sortBy from "lodash/sortBy";
import Modal from "./modal.vue";
// import html2Canvas from "html2canvas";
// import JsPDF from "jspdf";

export default {
  name: "IndexCardDetailInfo",
  props: {
    pageClass: String,
    dataItem: Object,
    companyName: String
  },
  components: { Vue2OrgTree, Modal },
  inject: ["businessSegmentsColorMap", "skinStyle"],
  data() {
    return {
      chartShowControl: true,
      Decimal,
      companySign: "", // 公司
      activeIndexId: "", // 点击的某组织id
      activeIndexItem: {}, // 点击的某组织数据
      cardItem: {},
      visible: false,
      SYS_NAME: window.system,
      horizontal: true, //横版 竖版
      collapsable: true,
      expandAll: true, //是否全部展开
      labelClassName: "白色", // 默认颜色
      datas: {},
      wdXTList: [], // 维度下探列表
      dataLoading: false,
      zoom: 1, // 组织树缩放
      complateChart: null,
      compareChart: null,
      wdChart: null,
      complateChartLoading: false,
      compareChartLoading: false,
      wdChartLoading: false,
      complateChartOptions: {
        title: {
          text: "完成情况",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["实际", "目标", "完成率"],
          top: 19,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisPointer: {
              type: "shadow"
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: "{value}"
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            }
          },
          {
            type: "value",
            axisLabel: {
              formatter: "{value} %"
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            }
          }
        ],
        series: [
          {
            name: "实际",
            type: "bar",
            data: [],
            itemStyle: {
              color: "#00AAA6"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          },
          {
            name: "目标",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgb(255, 192, 0)"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: 1,
            data: [],
            symbol: "none",
            itemStyle: {
              color: "rgba(214, 116, 255, 1)"
            }
          }
        ]
      },
      cloneComplateChartOptions: {},
      wdChartOptions: {
        title: {
          text: "维度对比",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        // legend: {
        //   data: ["目标"],
        //   top: 19,
        //   right: 20,
        //   itemWidth: 8,
        //   itemHeight: 8,
        //   itemStyle: {
        //     borderCap: "butt"
        //   },
        //   textStyle: {
        //     color: "#8C8C8C",
        //     fontSize: 12,
        //     lineHeight: 17,
        //     height: 17,
        //     borderRadius: 0
        //   },
        //   padding: 0,
        //   itemGap: 12.5
        // },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          axisPointer: {
            type: "shadow"
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: "value",
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: "{value}"
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          }
        },
        series: [
          {
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(21, 219, 195, 0.85)"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          }
        ]
      },
      wdChartSelect: "",
      cloneWdChartOptions: {},
      compareChartOptions: {
        title: {
          text: "同期&实际走势",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["同期", "实际", "目标"],
          top: 19,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          }
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            xAxisIndex: [0],
            height: 16
          },
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            yAxisIndex: [0],
            width: 16,
            orient: "vertical"
          }
        ],
        yAxis: {
          type: "value",
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: "{value}"
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          }
        },
        series: [
          {
            name: "同期",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(141, 180, 226)"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          },
          {
            name: "实际",
            type: "line",
            data: [],
            itemStyle: {
              color: "#00AAA6"
            },
            label: {
              show: true,
              position: "top"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          },
          {
            name: "目标",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(255, 192, 0)"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          }
        ]
      },
      ppResData: [], // 平铺后的查询结果
      cloneCompareChartOptions: {}
    };
  },
  watch: {
    dataItem: {
      handler(val) {
        // eslint-disable-next-line no-prototype-builtins
        if (val.hasOwnProperty("indexId")) {
          this.cardItem = val;
          this.dataLoading = true;
          this.getDetectionList();
        } else {
          // 重置数据
          this.cardItem = {};
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    tableData() {
      const arr = [];
      if (Object.keys(this.activeIndexItem).length) {
        // 把同层级的添加到数组中，并把当前选中数据的所有子级添加到数组中
        arr.push(
          ...[
            ...cloneDeep(
              this.ppResData.filter(
                item => item.parentSign === this.activeIndexItem.parentSign
              )
            ),
            ...cloneDeep(
              this.extractTree(this.activeIndexItem.children, "children")
            )
          ]
        );
      }
      arr.forEach((item, index) => {
        // eslint-disable-next-line no-prototype-builtins
        if (item.hasOwnProperty("children")) {
          delete item["children"];
        }
        item["index"] = index + 1;
        const deal100ParamsArr = [
          "contemChangeRate",
          "previousChangeRate",
          "contemValue",
          "previousValue",
          "actualMolecule",
          "actualDenominator"
        ];
        // item["signOrg"] = item["signOrg"] || this.companyName;
        deal100ParamsArr.forEach(zitem => {
          const dataItem = item;
          item[zitem] = dataItem[zitem]
            ? ["actualMolecule", "actualDenominator"].includes(zitem)
              ? Decimal(dataItem[zitem]).toFixed(2, Decimal.ROUND_HALF_UP)
              : Decimal(dataItem[zitem])
                  .mul(Decimal(100))
                  .toFixed(2, Decimal.ROUND_HALF_UP) + "%"
            : "";
        });
      });
      return arr;
    }
  },
  mounted() {
    // 初始化
    this.complateChart = echarts.init(this.$refs["complateDiv"]);
    this.compareChart = echarts.init(this.$refs["compareDiv"]);
    this.cloneComplateChartOptions = cloneDeep(this.complateChartOptions);
    this.cloneCompareChartOptions = cloneDeep(this.compareChartOptions);
    this.wdChart = echarts.init(this.$refs["wdDiv"]);
    this.cloneWdChartOptions = cloneDeep(this.wdChartOptions);
    setTimeout(() => {
      this.initComplateChart();
      this.initCompareChart();
      this.initWdChart();
      this.complateChart.resize();
      this.compareChart.resize();
      this.wdChart.resize();
    }, 1000);
    window.onresize = () => {
      this.complateChart.resize();
      this.compareChart.resize();
      this.wdChart.resize();
    };
  },
  methods: {
    // 初始化完成情况图表
    initComplateChart(options) {
      this.complateChart.setOption(options || this.complateChartOptions);
    },
    // 初始化同期情况表
    initCompareChart(options) {
      this.compareChart.setOption(options || this.compareChartOptions);
    },
    // 初始化维度对比情况表
    initWdChart(options) {
      this.wdChart.setOption(options || this.wdChartOptions);
    },
    // 树图渲染
    renderContent(h, data) {
      return (
        <div>
          {data.isRoot ? (
            <div>根节点</div>
          ) : (
            <div
              vOn:click_stop_prevent={() => this.cardClick(data)}
              class={["card-in-detection-block"]}
            >
              <div
                class={[
                  "card",
                  data.indexSign === this.activeIndexId
                    ? "active-card"
                    : data.indexSign.includes(this.activeIndexId)
                    ? "child-card"
                    : data.indexSign.split("~!~").length ===
                      this.activeIndexId.split("~!~").length
                    ? "slide-card"
                    : "parent-card"
                ]}
              >
                <div class="top">
                  <div class="_t">{data.org}</div>
                  {data.goDown &&
                    data.goDown.map(item => {
                      return (
                        <div class="wd-container">
                          <div
                            class="wd-tag"
                            vOn:click_stop_prevent={() =>
                              this.clickWD(data, item)
                            }
                          >
                            {item}
                          </div>
                        </div>
                      );
                    })}
                </div>
                {data.isDelete === null || data.isDelete === "Y" ? (
                  <div
                    class="sjz"
                    style={
                      data.isDelete ? "color: #5f8cc0;" : "color: #a5a5a5;"
                    }
                  >
                    不计算
                  </div>
                ) : (
                  <div
                    class="sjz"
                    style={
                      data.targetCompletionRate
                        ? parseFloat(data.targetCompletionRate) < 100
                          ? "color: rgba(52, 145, 250, 1);"
                          : "color: rgb(245, 63, 63);"
                        : ""
                    }
                  >
                    {data.actualValue}
                    {data.indexUnitId}
                  </div>
                )}

                <div class="intro">
                  <div>
                    目标值：{data.targetValue} {data.indexUnitId}
                  </div>
                  <div>完成率：{data.targetCompletionRate}</div>
                </div>
                <div class="compare _flex">
                  <a-row style="width: 100%;">
                    <a-col span={12}>
                      <div class="_flex _c">
                        <span>同比</span>
                        {data.isContemRate === "Y" ? (
                          <div
                            style={
                              data.contemChangeRate
                                ? data.indexType === "正向"
                                  ? data.contemChangeRate.includes("-")
                                    ? "color: #6495F9;"
                                    : "color: #f75050;"
                                  : data.contemChangeRate.includes("-")
                                  ? "color: #f75050;"
                                  : "color: #6495F9;"
                                : "color: rgba(0, 0, 0, 0.8);"
                            }
                            class="_flex"
                          >
                            {data.contemChangeRate ? (
                              <a-icon
                                type={
                                  data.indexType === "正向"
                                    ? data.contemChangeRate.includes("-")
                                      ? "caret-down"
                                      : "caret-up"
                                    : data.contemChangeRate.includes("-")
                                    ? "caret-up"
                                    : "caret-down"
                                }
                              />
                            ) : null}
                            <span>
                              {data.contemChangeRate
                                ? Math.abs(
                                    Decimal(data.contemChangeRate)
                                      .mul(Decimal(100))
                                      .toFixed(2, Decimal.ROUND_HALF_UP)
                                  ) + "%"
                                : "-"}
                            </span>
                          </div>
                        ) : (
                          <span style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;">
                            不对比
                          </span>
                        )}
                      </div>
                    </a-col>
                    <a-col span={12}>
                      <div class="_flex _c">
                        <span>环比</span>
                        {data.isPreviousRate === "Y" ? (
                          <div
                            style={
                              data.previousChangeRate
                                ? data.indexType === "正向"
                                  ? data.previousChangeRate.includes("-")
                                    ? "color: #6495F9;"
                                    : "color: #f75050;"
                                  : data.previousChangeRate.includes("-")
                                  ? "color: #f75050;"
                                  : "color: #6495F9;"
                                : "color: rgba(0, 0, 0, 0.8);"
                            }
                            class="_flex"
                          >
                            {data.previousChangeRate ? (
                              <a-icon
                                type={
                                  data.indexType === "正向"
                                    ? data.previousChangeRate.includes("-")
                                      ? "caret-down"
                                      : "caret-up"
                                    : data.previousChangeRate.includes("-")
                                    ? "caret-up"
                                    : "caret-down"
                                }
                              />
                            ) : null}
                            <span>
                              {data.previousChangeRate
                                ? Math.abs(
                                    Decimal(data.previousChangeRate)
                                      .mul(Decimal(100))
                                      .toFixed(2, Decimal.ROUND_HALF_UP)
                                  ) + "%"
                                : "-"}
                            </span>
                          </div>
                        ) : (
                          <span style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;">
                            不对比
                          </span>
                        )}
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>
            </div>
          )}
        </div>
      );
    },
    // 开关点击
    controlClick() {
      this.chartShowControl = !this.chartShowControl;
      if (this.chartShowControl) {
        this.$nextTick(() => {
          if (this.$refs["leftpartdiv"]) {
            setTimeout(() => {
              this.$refs["leftpartdiv"].$el.scrollTo(
                (this.activeIndexItem.levelIndex - 1) * 240,
                this.$refs["leftpartdiv"].$el.scrollTop
              );
            }, 800);
          }
        });
      }
    },
    // 卡片点击
    cardClick(indexItem) {
      if (indexItem.isDelete === "Y" || indexItem.isDelete === null) {
        return;
      }
      // 卡片点击的时候永远展开该子级
      this.onExpand(null, indexItem, true);
    },
    // 维度点击
    clickWD(indexItem, wd) {
      this.getDetectionWDList(indexItem.groupId, wd);
    },
    // onlyExpand 只展开操作，在cardClick方法内使用
    onExpand(e, data, onlyExpand = false) {
      if ("expand" in data) {
        data.expand = onlyExpand || data.isRoot ? true : !data.expand;
        if (!data.expand && data.children) {
          this.collapse(data.children);
        }
        if (data.expand && onlyExpand) {
          // 设置位移且获取右侧数据
          this.activeIndexId = data.indexSign;
          this.activeIndexItem = data;
          this.wdChartSelect = "";
          this.$nextTick(() => {
            if (this.$refs["leftpartdiv"]) {
              setTimeout(() => {
                this.getEachrtsDatas();
                this.$refs["leftpartdiv"].$el.scrollTo(
                  (data.levelIndex - 1) * 240,
                  this.$refs["leftpartdiv"].$el.scrollTop
                );
              }, 800);
            }
          });
        }
      } else {
        this.$set(data, "expand", true);
      }
    },
    // 获取图表数据
    getEachrtsDatas() {
      this.getTopChartDatas();
      this.getMiddleChartDatas();
      if (this.activeIndexItem.goDown.length) {
        this.wdChartSelectChange(this.activeIndexItem.goDown[0]);
      } else {
        this.wdChartOptions = cloneDeep(this.cloneWdChartOptions);
        this.initWdChart();
      }
    },
    // 获取顶部图表数据
    getTopChartDatas() {
      this.complateChartLoading = true;
      const indexItem = this.activeIndexItem;
      const postData = {
        indexId: indexItem.indexId,
        businessSegmentsId: indexItem.businessSegmentsId,
        fullCode: indexItem.fullCode,
        indexDt: indexItem.indexDt,
        indexFrequencyId: indexItem.indexFrequencyId,
        cmimId: indexItem.cmimId,
        productAtt1Id: indexItem.productAtt1Id,
        productAtt2Id: indexItem.productAtt2Id,
        productAtt3Id: indexItem.productAtt3Id,
        productAtt4Id: indexItem.productAtt4Id,
        productAtt5Id: indexItem.productAtt5Id,
        productAtt6Id: indexItem.productAtt6Id,
        productAtt7Id: indexItem.productAtt7Id,
        org: indexItem.org,
        businessSegments: indexItem.businessSegments,
        sign: indexItem.sign,
        signOrg: indexItem.signOrg,
        indexName: indexItem.postEndIndexName
      };
      request(`/api/smc2/newIndexLibrary/sameLayerCompleteList`, {
        method: "POST",
        body: postData
      })
        .then(res => {
          if (Array.isArray(res) && res.length) {
            res = sortBy(res, item => item.targetCompletionRate || "0");
            res.reverse();
            const xAxis = res.map(item => {
              return item.org;
            });
            this.complateChartOptions.xAxis[0].data = xAxis;
            const baseActualData = res.map(item => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.complateChartOptions.series[0].data = baseActualData;
            const targetValueData = res.map(item => {
              return dealThousandData(
                item.targetValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.complateChartOptions.series[1].data = targetValueData;
            const completionRateData = res.map(item => {
              return item.targetCompletionRate
                ? Decimal(parseFloat(item.targetCompletionRate))
                    .mul(Decimal.Decimal(100))
                    .toFixed(2, Decimal.ROUND_HALF_UP)
                : "0";
            });
            this.complateChartOptions.series[2].data = completionRateData;
            this.complateChartOptions.yAxis[0].axisLabel.formatter = `{value} ${
              res[0].indexUnitId === null ? "" : res[0].indexUnitId
            }`;
            this.initComplateChart();
          } else {
            this.complateChartOptions = cloneDeep(
              this.cloneComplateChartOptions
            );
            this.initComplateChart();
          }
          this.complateChartLoading = false;
        })
        .catch(() => {
          this.complateChartOptions = cloneDeep(this.cloneComplateChartOptions);
          this.initComplateChart();
          this.complateChartLoading = false;
        });
    },
    // 获取中间图表数据
    getMiddleChartDatas() {
      this.compareChartLoading = true;
      const indexItem = this.activeIndexItem;
      const {
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id
      } = this.cardItem;
      const arr = [
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id
      ];
      arr.forEach((item, index) => {
        arr[index] = item
          ? item.includes("卡片名称") || item.includes("卡片标签")
            ? item
            : null
          : null;
      });
      const postData = {
        signOrgId: indexItem.signOrgId,
        businessSegmentsId: indexItem.businessSegmentsId,
        indexId: indexItem.indexId,
        fullCode: indexItem.fullCode,
        indexDt: indexItem.indexDt,
        indexFrequencyId: indexItem.indexFrequencyId,
        org: indexItem.org,
        businessSegments: indexItem.businessSegments,
        sign: indexItem.indexItem,
        signOrg: indexItem.signOrg,
        indexName: indexItem.postEndIndexName,
        productAtt1Id: arr[0],
        productAtt2Id: arr[1],
        productAtt3Id: arr[2],
        productAtt4Id: arr[3],
        productAtt5Id: arr[4],
        productAtt6Id: arr[5],
        productAtt7Id: arr[6]
      };
      request(`/api/smc2/newIndexLibrary/searchTrend`, {
        method: "POST",
        body: postData
      })
        .then(res => {
          if (Array.isArray(res) && res.length > 0) {
            res = sortBy(res, function(item) {
              return item.indexDt;
            });
            const xAxis = res.map(item => {
              return item.indexDt;
            });
            this.compareChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map(item => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.compareChartOptions.series[1].data = baseActualData;
            const targetValueData = res.map(item => {
              return dealThousandData(
                item.targetValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.compareChartOptions.series[2].data = targetValueData;
            const contemValueData = res.map(item => {
              return dealThousandData(
                item.contemValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.compareChartOptions.dataZoom[0].end = 100;
            // 根据数据条数 判断是否显示dataZoom组件
            if (res.length > 6) {
              this.compareChartOptions.dataZoom[0].show = true;
              this.compareChartOptions.grid.bottom = 52;
              if (res.length > 13) {
                this.compareChartOptions.dataZoom[0].start = 50;
              } else {
                this.compareChartOptions.dataZoom[0].start = 0;
              }
            }

            this.compareChartOptions.series[0].data = contemValueData;
            this.compareChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].indexUnitId === "null" ? "" : res[0].indexUnitId
            }`;
            this.initCompareChart();
          } else {
            this.compareChartOptions = cloneDeep(this.cloneCompareChartOptions);
            this.initCompareChart();
          }
          this.compareChartLoading = false;
        })
        .catch(() => {
          this.compareChartOptions = cloneDeep(this.cloneCompareChartOptions);
          this.initCompareChart();
          this.compareChartLoading = false;
        });
    },
    // 获取底部图表数据
    getBottomChartDatas() {
      this.wdChartLoading = true;
      const indexItem = this.activeIndexItem;
      const postData = {
        indexId: indexItem.indexId,
        businessSegmentsId: indexItem.businessSegmentsId,
        fullCode: indexItem.fullCode,
        indexDt: indexItem.indexDt,
        indexFrequencyId: indexItem.indexFrequencyId,
        cmimId: indexItem.cmimId,
        productAtt1Id: indexItem.productAtt1Id,
        productAtt2Id: indexItem.productAtt2Id,
        productAtt3Id: indexItem.productAtt3Id,
        productAtt4Id: indexItem.productAtt4Id,
        productAtt5Id: indexItem.productAtt5Id,
        productAtt6Id: indexItem.productAtt6Id,
        productAtt7Id: indexItem.productAtt7Id,
        org: indexItem.org,
        businessSegments: indexItem.businessSegments,
        sign: indexItem.sign,
        signOrg: indexItem.signOrg,
        indexName: indexItem.postEndIndexName
      };
      request("/api/smc2/newIndexLibrary/dimensionContrast", {
        method: "POST",
        body: postData
      })
        .then(res => {
          res = res["维度列表"] || [];
          if (Array.isArray(res) && res.length) {
            const xAxis = res.map(item => {
              return item.productAtt1.split("-")[1];
            });
            this.wdChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map(item => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.wdChartOptions.series[0].data = baseActualData;
            this.wdChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].indexUnitId === null ? "" : res[0].indexUnitId
            }`;
            this.initWdChart();
          } else {
            this.wdChartOptions = cloneDeep(this.cloneWdChartOptions);
            this.initWdChart();
          }
          this.wdChartLoading = false;
        })
        .catch(() => {
          this.wdChartOptions = cloneDeep(this.cloneWdChartOptions);
          this.initWdChart();
          this.wdChartLoading = false;
        });
    },
    wdChartSelectChange(value) {
      this.wdChartSelect = value;
      this.getBottomChartDatas();
    },
    // 折叠
    collapse(list) {
      var _this = this;
      list.forEach(function(child) {
        if (child.expand) {
          child.expand = false;
        }
        child.children && _this.collapse(child.children);
      });
    },
    // 关闭抽屉
    close() {
      this.cardItem = {};
      this.wdChartSelect = "";
      this.chartShowControl = true;
      this.datas = {};
      this.zoom = 1;
      this.complateChart.setOption(this.cloneComplateChartOptions);
      this.compareChart.setOption(this.cloneCompareChartOptions);
      this.wdChart.setOption(this.cloneWdChartOptions);
      this.$emit("close");
    },
    /*
     *  扁平化
     *  bossArr  树形数据
     * */
    extractTree(bossArr, children) {
      //如果为空 返回空（防止 children 递归报错）
      if (!Array.isArray(bossArr) && !bossArr.length) return [];
      let list = [];
      const getObj = arr => {
        arr.forEach(row => {
          let obj = {};
          obj = JSON.parse(JSON.stringify(row));
          list.push(obj);
          if (row[children]) {
            getObj(row[children]);
          }
        });
        return list;
      };
      return getObj(bossArr);
    },
    // 循环处理数据
    loopData(arr, wd, index = 0, parentSign = "") {
      index++;
      return arr.map(item => {
        const {
          dmId,
          indexId,
          indexName,
          indexDt,
          fullCode,
          levelName,
          parentName,
          indexFrequency,
          indexFrequencyId,
          org,
          orgId,
          businessSegments,
          businessSegmentsId,
          signOrgId,
          signOrg,
          actualValue,
          targetValue,
          targetCompletionRate,
          previousChangeRate,
          contemChangeRate,
          indexUnitId,
          indexSort,
          productAtt1,
          productAtt2,
          productAtt3,
          productAtt4,
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id,
          precisions,
          actualMolecule,
          actualDenominator,
          contemValue,
          previousValue,
          indexTypeId,
          indexNameInd,
          isDelete,
          groupId,
          goDown,
          cmimId,
          pj // 用于拖拽排序标记
        } = item;
        let indexSign = parentSign
          ? parentSign + "~!~" + `${orgId}`
          : `${orgId}`;
        const normalWDList = [
          productAtt1,
          productAtt2,
          productAtt3,
          productAtt4
        ].filter(item => item && !item.includes("指标卡"));
        let wdInCardName = normalWDList
          .filter(item => item.includes("卡片名称"))
          .map(item => item.split("-")[2]);
        wdInCardName = wdInCardName.join("-");
        const wdInCardTag = normalWDList
          .filter(item => item.includes("卡片标签"))
          .map(item => item.split("-")[2]);
        const wdInXT = [
          productAtt1,
          productAtt2,
          productAtt3,
          productAtt4
        ].filter(item => item && item.split("-").length === 2);
        const displayIndexName = indexNameInd || indexName;
        return {
          dmId,
          postEndIndexName:
            (wdInCardName ? wdInCardName + " - " : "") +
            displayIndexName +
            (wdInCardTag.length ? " - " + wdInCardTag.join("-") : ""),
          indexId,
          indexSign,
          indexName,
          indexDt,
          fullCode,
          indexFrequency,
          indexFrequencyId,
          org,
          orgId,
          businessSegments,
          businessSegmentsId,
          signOrgId,
          signOrg,
          actualMolecule,
          actualDenominator,
          contemValue,
          previousValue,
          levelName,
          parentName,
          actualValue: dealThousandData(
            actualValue,
            item.indexUnitId,
            precisions
          ),
          targetValue: dealThousandData(
            targetValue,
            item.indexUnitId,
            precisions
          ),
          targetCompletionRate: targetCompletionRate
            ? `${Decimal(targetCompletionRate)
                .mul(Decimal.Decimal(100))
                .toFixed(2, Decimal.ROUND_HALF_UP)}%`
            : "",
          previousChangeRate,
          isPreviousRate: "Y",
          isContemRate: "Y",
          contemChangeRate,
          indexUnitId,
          indexType: indexTypeId,
          indexNameInd,
          displayIndexName,
          indexSort,
          normalWDList,
          wdInCardName,
          wdInCardTag,
          groupId,
          cmimId,
          isDelete,
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id,
          goDown: goDown ? goDown.split(",") : [],
          wd: wd
            ? wd + "-" + (wdInXT.length ? wdInXT[0].split("-")[1] : "")
            : "",
          companyName: this.companyName,
          label: "同环比恶化,未达标",
          pj,
          parentSign,
          children: this.loopData(item.list || [], wd, index, indexSign),
          levelIndex: index,
          expand: false,
          sign: this.companySign
        };
      });
    },
    // 获取组织下探列表
    getDetectionList() {
      const {
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id
      } = this.cardItem;
      const postData = {
        indexId: this.cardItem.indexId,
        businessSegmentsId: this.cardItem.businessSegmentsId,
        fullCode: this.cardItem.fullCode,
        indexDt: this.cardItem.indexDt,
        indexFrequencyId: this.cardItem.indexFrequencyId,
        cmimId: this.cardItem.cmimId,
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id,
        org: this.cardItem.org,
        businessSegments: this.cardItem.businessSegments,
        sign: this.cardItem.sign,
        signOrg: this.cardItem.signOrg,
        indexName: this.cardItem.postEndIndexName
      };
      this.companySign = this.cardItem.sign;
      request("/api/smc2/newIndexLibrary/orgList", {
        method: "POST",
        body: postData
      }).then(res => {
        this.dataLoading = false;
        if (Array.isArray(res) && res.length) {
          const data = this.loopData(res);
          this.datas = {
            isRoot: true,
            children: data,
            expand: true
          };
          this.ppResData = this.extractTree(data, "children");
          console.log(
            "this.extractTree(res)----->",
            this.ppResData.map(item => String(item.parentSign))
          );
          console.log(this.ppResData.map(item => String(item.indexSign)));
          // 筛选出点击该卡片的数据
          const indexItem = this.datas.children.filter(
            item =>
              `${this.cardItem.indexId}~&~${this.cardItem.orgId}` ===
              `${item.indexId}~&~${item.orgId}`
          )[0];
          this.$nextTick(() => {
            if (indexItem) {
              this.onExpand(null, indexItem, true);
            }
          });
        }
      });
    },
    // 获取维度下探列表
    getDetectionWDList(groupId, wd) {
      request("/api/smc2/newIndexLibrary/dimensionGoDown", {
        method: "POST",
        body: {
          groupId,
          goDown: wd,
          indexFrequencyId: this.cardItem.indexFrequencyId,
          indexDt: this.cardItem.indexDt
        }
      }).then(res => {
        if (res.length) {
          this.wdXTList = this.loopData(res, wd);
          this.visible = true;
        } else {
          this.$message.warning("暂无维度下探信息");
        }
      });
    },
    // 放大
    zoomOut() {
      if (this.zoom >= 2) {
        return;
      }
      this.zoom = this.zoom + 0.1;
    },
    // 缩小
    zoomIn() {
      if (this.zoom <= 0.2) {
        return;
      }
      this.zoom = this.zoom - 0.1;
    }
  }
};
</script>
<style lang="less">
@import url("./styles.css");
@active-bc: rgba(198, 255, 221, 0.5);
@parent-bc: rgba(251, 215, 134, 0.5);
@slide-bc: rgba(109, 213, 250, 0.5);
@child-bc: rgba(247, 121, 125, 0.5);
.detection-wd-tooltip {
  .ant-tooltip-inner {
    color: #1d2129;
    background-color: #fff;
  }
  .ant-tooltip-arrow::before {
    background-color: #fff;
  }
}
.card-in-detection-block {
  cursor: pointer;
  display: flex;
  ._flex {
    display: flex;
    align-items: center;
  }
  .card {
    width: 200px;
    height: 143px;
    border-radius: 3px;
    box-sizing: border-box;
    // background: rgba(243, 254, 255, 1);
    // background: rgba(255, 255, 255, 1);
    background-color: rgb(242, 243, 245);
    &.active-card {
      background-color: @active-bc;
    }
    // &.parent-card {
    //   background-color: @parent-bc;
    // }
    // &.child-card {
    //   background-color: @child-bc;
    // }
    // &.slide-card {
    //   background-color: @slide-bc;
    // }
    box-shadow: 2px 0px 6px rgba(0, 21, 41, 0.12);
    position: relative;
    padding: 16px;
    text-align: left;
    .top {
      display: flex;
      align-items: center;
      white-space: nowrap;
      justify-content: space-between;
      ._t {
        font-size: 12px;
        height: 16px;
        line-height: 16px;
        color: rgba(29, 33, 41, 1);
      }
      .wd-container {
        display: flex;
        align-items: center;
        .wd-tag {
          height: 14px;
          font-size: 12px;
          color: #53667a;
          line-height: 14px;
          border-radius: 2px;
          padding: 0 3px;
          background-color: #e0e3ea;
          margin-left: 0 !important;
          cursor: pointer;
          &:hover {
            background-color: #fff !important;
          }
        }
      }
      margin-bottom: 3px;
    }
    .sjz {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 24px;
      font-size: 16px;
      margin-bottom: 3px;
      font-weight: bold;
    }
    .intro {
      padding-bottom: 5px;
      border-bottom: 1px solid rgb(229, 231, 235);
      margin-bottom: 5px;
      & > div {
        font-size: 12px;
        height: 16px;
        line-height: 16px;
        color: rgb(78, 89, 105);
        &:first-child {
          margin-bottom: 5px;
        }
      }
    }
    .compare {
      font-size: 12px;
      ._c > span {
        color: rgba(0, 0, 0, 0.65);
        display: block;
        margin-right: 2px;
        white-space: nowrap;
      }
      ._c > div {
        .anticon {
          position: relative;
          top: 1px;
        }
        white-space: nowrap;
        span {
          white-space: nowrap;
        }
      }
    }
  }
}
.CardDetectionInfo {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  height: 100%;
  display: flex;
  flex-direction: column;
  &.hisense-style.dark {
    .drawerTitle {
      background: #565b60;
      color: #ffffff;
      height: 50px;
      border-bottom: 1px solid #565b60;
    }
    .index-info {
      background: #313335;
      height: 55px;
      border-bottom: none;
      .title {
        color: #e2e8ea;
        font-weight: normal;
        font-size: 18px;
      }
      .tag {
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    .bottom-container {
      height: calc(100% - 105px);
      background-color: #070707;
      display: flex;
      .org-tree-node-btn {
        background-color: #070707;
      }
      .__c .org-tree-node-btn:before,
      .__c .org-tree-node-btn:after {
        background: #ffffff;
      }

      .card-in-detection-block .card {
        background: #313335;
        .top ._t,
        .intro > div {
          color: #a1a6ac;
        }
        .sjz {
          color: #ffffff;
        }
        .compare {
          ._c > span {
            color: #a1a6ac;
          }
        }
      }
    }
  }
  .drawerTitle {
    position: relative;
    padding: 16px 24px;
    padding-right: 48px !important;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 57px;
    ._left {
      display: flex;
      align-items: center;
      .index-info {
        box-sizing: border-box;
        align-items: center;
        .title {
          font-size: 24px;
          font-weight: bold;
        }
        .tag {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 2px;
          margin-left: 8px;
          &.plate {
            background-color: rgba(253, 238, 234, 1);
            color: rgb(238, 115, 79);
          }
          &.wd {
            background-color: rgba(242, 243, 245, 1);
            color: rgb(78, 89, 105);
          }
        }
      }
    }
  }
  ._flex {
    display: flex;
    align-items: center;
  }

  .bottom-container {
    // background-color: rgb(242, 243, 245);
    height: calc(100% - 57px);
    width: 100%;
    overflow: auto;
    display: flex;
    position: relative;
    & > div {
      height: 100%;
      &.left-part {
        overflow: hidden;
        width: calc(100% - 20px) !important;
        transition: width ease-in-out 0.3s;
        position: relative;
        &.open {
          width: calc(100% - 845px) !important;
        }
        .__c {
          min-width: 100%;
          height: 100%;
          overflow: auto;
          display: flex;
          justify-content: center;
          box-sizing: border-box;
          padding: 40px 0 0 0 !important;
          position: relative;
          .org-tree-node-btn:before {
            top: 50%;
            left: 50%;
            right: auto;
            width: 60%;
            height: 2px;
            border-top: 0px;
            background: #000;
            transform: translate(-50%, -50%);
          }
          .org-tree-node-btn:after {
            top: 50%;
            left: 50%;
            bottom: auto;
            width: 2px;
            border-left: 0px;
            height: 60%;
            background: #000;
            transform: translate(-50%, -50%);
          }
          .org-tree-node-btn.expanded:after {
            background: transparent !important;
          }
          .org-tree-container {
            background-color: transparent;
            width: 100%;
            padding: 0 !important;
            overflow-x: auto;
          }
          .org-tree-node-label .org-tree-node-label-inner {
            padding: 0;
            box-shadow: none;
          }
          .org-tree-node-children:before,
          .org-tree-node:not(:first-child):before,
          .org-tree-node:not(:last-child):after,
          .org-tree-node:not(:first-child):before,
          .org-tree-node:not(:last-child):after,
          .org-tree-node:after {
            border-color: rgb(78, 89, 105);
          }
        }
        .switchTable-btn {
          position: absolute;
          right: 10px;
          top: 10px;
          z-index: 10;
        }
        .operation-area {
          position: absolute;
          bottom: 30px;
          right: 10px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          & > button {
            margin-top: 5px;
          }
        }
        .tip-area {
          position: absolute;
          top: 0;
          right: 0;
          height: 40px;
          display: flex;
          align-items: center;
          & > div {
            display: flex;
            align-items: center;
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            color: #53667a;
            margin-right: 8px;
            .color {
              width: 16px;
              height: 10px;
              border-radius: 2px;
              margin-right: 4px;
              &.active {
                background-color: @active-bc;
              }
              &.parent {
                background-color: @parent-bc;
              }
              &.child {
                background-color: @child-bc;
              }
              &.slide {
                background-color: @slide-bc;
              }
            }
          }
        }
      }
      &.right-part {
        overflow-y: auto;
        box-shadow: -1px 0 8px 2px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        &.close {
          .container {
            width: 0;
          }
          .anticon {
            transform: rotateZ(180deg);
          }
        }
        .container {
          width: 820px;
          transition: width ease-in-out 0.3s;
          height: 100%;
          overflow-y: auto;
          & > div {
            width: 800px;
          }
        }
        position: absolute;
        top: 0;
        right: 0;
        z-index: 12;
        background-color: #fff;
        .control {
          cursor: pointer;
          &:hover {
            background-color: #e0e3ea;
          }
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          background-color: #eee;
          // border: 1px solid #9b9b9b;
          border-right: none;
          width: 20px;
          border-radius: 3px 0 0 3px;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.65);
          .anticon {
            transition: all ease-in-out 0.3s;
          }
        }
      }
    }
  }
}
</style>
