<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2024-09-30 09:51:54
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-24 14:33:37
 * @FilePath: \localdev\pangea-component\src\views\cloudSetting\indexTargetFillIn\tableCell.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <a-tag color="blue">
      {{ record[name]}}
    </a-tag>
    <!-- <a-tag :color="this.nth2">
      {{ record[name]?.split(" ")[0]}}
    </a-tag>
    <a-tag :color="this.nth3">
      {{ record[name]?.split(" ")[0]}}
    </a-tag> -->
  </div>
</template>
<script>
export default {
  props: {
    record: Object, // 当前操作行
    text: String,
    name: String, // 当前字段名
  },
  computed: {
    nth1() {
      console.log(record[name].split(" ")[0].split("：")[1]);
      return record[name].split(" ")[0].split("：")[1] === "是"
        ? "green"
        : "red";
    },
    nth2() {
      return record[name].split(" ")[1].split("：")[1] === "是"
        ? "green"
        : "red";
    },
    nth3() {
      return record[name].split(" ")[2].split("：")[1] === "是"
        ? "green"
        : "red";
    },
  },
  data() {
    return {
      recordName: "外报：否 内控：否 CBG：否",
    };
  },
};
</script>
