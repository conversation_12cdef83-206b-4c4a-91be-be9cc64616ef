<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2024-10-18 16:06:35
 * @LastEditors: y<PERSON>hengqi.ex
 * @LastEditTime: 2024-12-09 09:12:48
 * @FilePath: \localdev\pangea-component\src\views\boot-console\SXKJ-fillpage\README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->



[责任人维护]这个按钮三个组件都在共同使用：  
直通率key：handle1700620456594
不良率key：handle1729475057076
隔离率key: handle1729475283882
这三个组件在更换key的同时还需要更换model1组件中[sign]字段




不良率的编辑和隔离率的表格行[编辑]公用一个组件只需要更换组件的key不需要改内部的逻辑
不良率是：handle1729230436697 隔离率是：handle1729232486159

不良率的编辑和隔离率的[审核]用的是同一个组件更换key的同时需要更换接口：`/api/smc2/gate/process`的[sgin]字段：
不良率："OQC抽检不良率",隔离率是："隔离率"
直通率：handle1729221529058
不良率：handle1729236313380
隔离率：handle1729236347670

