<template>
  <div class="org-selects-container">
    <div class="org-selects-row">
      <!-- 基地选择 -->
      <a-select v-model="localSearchForm.baseName" placeholder="请选择基地" style="width: 120px; margin-right: 8px;"
        @change="handleBaseChange" :loading="localLoadingStatus.base" :disabled="localLoadingStatus.base">
        <a-select-option v-for="item in orgLists.baseList" :key="item.code" :value="item.name">
          {{ item.name }}
        </a-select-option>
      </a-select>

      <!-- 工厂选择 -->
      <a-select v-if="shouldShowFactory" v-model="localSearchForm.factoryName" placeholder="请选择工厂"
        style="width: 140px; margin-right: 8px;" @change="handleFactoryChange"
        :loading="localLoadingStatus.factory">
        <a-select-option v-for="item in orgLists.factoryList" :key="item.code" :value="item.name">
          {{ item.name }}
        </a-select-option>
      </a-select>

      <!-- 分厂选择 -->
      <a-select v-if="shouldShowBranch" v-model="localSearchForm.branchName" placeholder="请选择分厂"
        style="width: 120px; margin-right: 8px;" @change="handleBranchChange"
        :loading="localLoadingStatus.branch">
        <a-select-option v-for="item in orgLists.branchList" :key="item.code" :value="item.name">
          {{ item.name }}
        </a-select-option>
      </a-select>

      <!-- 车间选择 -->
      <a-select v-if="shouldShowWorkshop" v-model="localSearchForm.workshopName" placeholder="请选择车间"
        style="width: 100px; margin-right: 8px;" @change="handleWorkshopChange"
        :loading="localLoadingStatus.workshop">
        <a-select-option v-for="item in orgLists.workshopList" :key="item.code" :value="item.name">
          {{ item.name }}
        </a-select-option>
      </a-select>

      <!-- 线体选择 -->
      <a-select v-if="shouldShowLine" v-model="localSearchForm.lineName" placeholder="请选择线体"
        style="width: 100px; margin-right: 8px;" @change="handleLineChange"
        :loading="localLoadingStatus.line">
        <a-select-option v-for="item in orgLists.lineList" :key="item.code" :value="item.name">
          {{ item.name }}
        </a-select-option>
      </a-select>

      <!-- 班组选择 -->
      <a-select v-if="shouldShowTeam" v-model="localSearchForm.teamName" placeholder="请选择班组" style="width: 140px;"
        @change="handleTeamChange" :loading="localLoadingStatus.team" :filter-option="filterTeamOption"
        option-filter-prop="children">
        <a-select-option v-for="item in orgLists.teamList" :key="item.code" :value="item.name">
          <span :title="item.parentName ? `${item.parentName} - ${item.name}` : item.name">
            {{ item.name }}
            <span v-if="item.parentName" style="color: #999; font-size: 12px;">
              ({{ item.parentName }})
            </span>
          </span>
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<script>
import { getAllFactory } from "../Api";

export default {
  name: "OrgStructureSelect",
  props: {
    // 初始值
    value: {
      type: Object,
      default: () => ({
        baseName: '',
        factoryName: '',
        branchName: '',
        workshopName: '',
        lineName: '',
        teamName: '',
        baseCode: '',
        factoryCode: '',
        branchCode: '',
        workshopCode: '',
        lineCode: '',
        teamCode: ''
      })
    }
  },
  data() {
    return {
      // 组织层级数据
      orgLists: {
        baseList: [], // 基地列表，将从API动态获取
        factoryList: [], // 工厂列表
        branchList: [], // 分厂列表
        workshopList: [], // 车间列表
        lineList: [], // 线体列表
        teamList: [] // 班组列表
      },
      // 加载状态
      localLoadingStatus: {
        base: false,
        factory: false,
        branch: false,
        workshop: false,
        line: false,
        team: false
      },
      // 本地表单数据
      localSearchForm: {
        baseName: '',
        factoryName: '',
        branchName: '',
        workshopName: '',
        lineName: '',
        teamName: '',
        baseCode: '',
        factoryCode: '',
        branchCode: '',
        workshopCode: '',
        lineCode: '',
        teamCode: ''
      },
      // 初始化标志
      isInitializingOrg: false,
      // 存储API返回的完整数据
      allOrgData: null
    };
  },
  computed: {
    /**
     * 是否显示工厂选择器
     * 当有工厂数据或者有工厂名称/代码时显示
     */
    shouldShowFactory() {
      // 如果初始化还未完成，暂时显示
      if (this.isInitializingOrg) {
        return true;
      }
      // 如果有工厂列表数据，显示
      if (this.orgLists.factoryList.length > 0) {
        return true;
      }
      // 如果有工厂名称或代码（来自反查），显示
      if (this.localSearchForm.factoryName || this.localSearchForm.factoryCode) {
        return true;
      }
      return false;
    },

    /**
     * 是否显示分厂选择器
     * 当有分厂数据或者有分厂名称/代码时显示
     */
    shouldShowBranch() {
      // 如果初始化还未完成，暂时显示
      if (this.isInitializingOrg) {
        return true;
      }
      // 如果有分厂列表数据，显示
      if (this.orgLists.branchList.length > 0) {
        return true;
      }
      // 如果有分厂名称或代码（来自反查），显示
      if (this.localSearchForm.branchName || this.localSearchForm.branchCode) {
        return true;
      }
      return false;
    },

    /**
     * 是否显示车间选择器
     * 当有车间数据或者有车间名称/代码时显示
     */
    shouldShowWorkshop() {
      // 如果初始化还未完成，暂时显示
      if (this.isInitializingOrg) {
        return true;
      }
      // 如果有车间列表数据，显示
      if (this.orgLists.workshopList.length > 0) {
        return true;
      }
      // 如果有车间名称或代码（来自反查），显示
      if (this.localSearchForm.workshopName || this.localSearchForm.workshopCode) {
        return true;
      }
      return false;
    },

    /**
     * 是否显示线体选择器
     * 当有线体数据或者有线体名称/代码时显示
     */
    shouldShowLine() {
      // 如果初始化还未完成，暂时显示
      if (this.isInitializingOrg) {
        return true;
      }
      // 如果有线体列表数据，显示
      if (this.orgLists.lineList.length > 0) {
        return true;
      }
      // 如果有线体名称或代码（来自反查），显示
      if (this.localSearchForm.lineName || this.localSearchForm.lineCode) {
        return true;
      }
      return false;
    },

    /**
     * 是否显示班组选择器
     * 当有班组数据或者有班组名称/代码时显示
     */
    shouldShowTeam() {
      // 如果初始化还未完成，暂时显示
      if (this.isInitializingOrg) {
        return true;
      }
      // 如果有班组列表数据，显示
      if (this.orgLists.teamList.length > 0) {
        return true;
      }
      // 如果有班组名称或代码（来自反查），显示
      if (this.localSearchForm.teamName || this.localSearchForm.teamCode) {
        return true;
      }
      return false;
    }
  },
  watch: {
    // 监听外部传入的值变化
    value: {
      handler(newVal) {
        if (newVal) {
          this.localSearchForm = { ...newVal };
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // 初始化组织层级数据
    this.initOrgData();
  },
  methods: {
    /**
     * 初始化组织层级数据
     * 获取组织层级数据并初始化下拉选择框
     */
    async initOrgData() {
      try {
        // 设置初始化标志为true
        this.isInitializingOrg = true;

        // 设置所有加载状态为true
        this.localLoadingStatus.base = true;

        // 确定要使用的基地代码
        let targetBaseCode = this.localSearchForm.baseCode;

        // 如果没有基地代码，尝试从其他层级反向查询
        if (!targetBaseCode && (
          this.localSearchForm.factoryCode ||
          this.localSearchForm.branchCode ||
          this.localSearchForm.workshopCode ||
          this.localSearchForm.lineCode ||
          this.localSearchForm.teamCode
        )) {
          targetBaseCode = await this.reverseQueryBaseInfo();
        }

        // 如果还是没有基地代码，使用默认值
        if (!targetBaseCode) {
          targetBaseCode = 'KTGS'; // 默认基地代码
        }

        // 获取组织层级数据
        const result = await getAllFactory(targetBaseCode);
        console.log('获取到的所有组织层级数据类型:', typeof result, '值:', result);

        if (!result) {
          console.error('获取组织层级数据失败: result 为空');
          return;
        }

        // 根据 result 的类型和结构进行处理
        let orgData;
        if (typeof result === 'object') {
          // 如果 result 是对象，检查它是否有 data 属性
          if (result.data) {
            orgData = result.data;
          } else {
            // 如果没有 data 属性，假设 result 本身就是数据
            orgData = result;
          }
        } else {
          console.error('获取组织层级数据失败: result 不是对象', result);
          return;
        }

        // 检查组织数据是否包含必要的属性
        if (!orgData.factory || !orgData.subfactory || !orgData.workshop || !orgData.line || !orgData.team) {
          console.error('组织数据结构不完整', orgData);
          return;
        }

        // 保存完整的组织数据
        this.allOrgData = orgData;

        // 从API返回的数据中提取基地信息并补全baseList
        this.extractAndUpdateBaseList(orgData, targetBaseCode);

        // 找到与默认值匹配的基地
        let selectedBase = null;

        // 如果有baseCode，优先使用code匹配
        if (this.localSearchForm.baseCode) {
          selectedBase = this.orgLists.baseList.find(item => item.factoryModelCode === this.localSearchForm.baseCode);
        }

        // 如果没有找到匹配的baseCode，尝试使用baseName匹配
        if (!selectedBase && this.localSearchForm.baseName) {
          selectedBase = this.orgLists.baseList.find(item => item.name === this.localSearchForm.baseName);
        }

        // 如果都没有找到，使用第一个基地
        if (!selectedBase && this.orgLists.baseList.length > 0) {
          selectedBase = this.orgLists.baseList[0];
        }

        if (selectedBase) {
          // 更新基地的name和code
          this.localSearchForm.baseName = selectedBase.name;
          this.localSearchForm.baseCode = selectedBase.factoryModelCode;

          console.log(`选中基地: ${selectedBase.name} (${selectedBase.factoryModelCode})`);

          // 获取工厂列表
          this.loadFactoryList();
        }

        console.log('组织层级数据初始化完成', {
          baseList: this.orgLists.baseList,
          factoryList: this.orgLists.factoryList,
          branchList: this.orgLists.branchList,
          workshopList: this.orgLists.workshopList,
          lineList: this.orgLists.lineList,
          teamList: this.orgLists.teamList
        });
      } catch (error) {
        console.error("初始化组织层级数据失败:", error);
      } finally {
        this.localLoadingStatus.base = false;

        // 只更新值，不触发change事件
        // 创建一个新的对象，避免引用问题
        const formData = { ...this.localSearchForm };
        // 只发送input事件更新v-model绑定的值
        this.$emit('input', formData);

        // 通知父组件初始化完成
        this.$emit('init-complete');

        // 延迟设置初始化标志为false，确保父组件有足够的时间处理初始化完成事件
        setTimeout(() => {
          // console.log('组织层级组件初始化完成，设置isInitializingOrg为false');
          this.isInitializingOrg = false;
          // 初始化完成后，清理空的层级数据
          this.cleanupEmptyLevels();
        }, 100);
      }
    },

    /**
     * 从API返回的数据中提取基地信息并补全baseList
     * @param {Object} orgData 组织数据
     * @param {String} currentBaseCode 当前基地代码
     */
    extractAndUpdateBaseList(orgData, currentBaseCode) {
      try {
        console.log('开始提取基地信息，当前基地代码:', currentBaseCode);

        // 创建一个Set来存储已经添加的基地，避免重复
        const existingBaseCodes = new Set(this.orgLists.baseList.map(item => item.factoryModelCode));

        // 只从factory层级提取基地信息
        if (orgData.factory && Array.isArray(orgData.factory)) {
          for (const factory of orgData.factory) {
            // 检查是否有parentCode和parentName
            if (factory.parentCode && factory.parentName && !existingBaseCodes.has(factory.parentCode)) {
              // 添加到baseList
              this.orgLists.baseList.push({
                name: factory.parentName,
                factoryModelCode: factory.parentCode
              });
              existingBaseCodes.add(factory.parentCode);
              console.log(`从factory层级提取到基地信息: ${factory.parentName} (${factory.parentCode})`);
            }
          }
        }

        // 确保当前基地代码在baseList中
        if (currentBaseCode && !existingBaseCodes.has(currentBaseCode)) {
          // 如果当前基地代码不在列表中，尝试从第一个工厂的parentCode获取
          if (orgData.factory && orgData.factory.length > 0) {
            const firstFactory = orgData.factory[0];
            if (firstFactory.parentCode === currentBaseCode && firstFactory.parentName) {
              this.orgLists.baseList.push({
                name: firstFactory.parentName,
                factoryModelCode: currentBaseCode
              });
              console.log(`添加当前基地到列表: ${firstFactory.parentName} (${currentBaseCode})`);
            }
          }
        }

        console.log('基地列表更新完成:', this.orgLists.baseList);
      } catch (error) {
        console.error('提取基地信息失败:', error);
      }
    },

    /**
     * 反向查询基地信息
     * 当有任何层级的code但没有基地code时，通过查询所有基地来找到对应的基地
     * @returns {String|null} 找到的基地代码
     */
    async reverseQueryBaseInfo() {
      try {
        console.log('开始反向查询基地信息，当前表单数据:', this.localSearchForm);

        // 遍历所有可能的基地代码
        const possibleBaseCodes = ['KTGS', 'XYJGS']; // 可以根据实际情况扩展

        // 确定要查找的目标code
        const targetCode = this.localSearchForm.factoryCode ||
                          this.localSearchForm.branchCode ||
                          this.localSearchForm.workshopCode ||
                          this.localSearchForm.lineCode ||
                          this.localSearchForm.teamCode;

        if (!targetCode) {
          console.log('没有找到需要反向查询的code');
          return;
        }

        console.log('目标code:', targetCode);

        for (const baseCode of possibleBaseCodes) {
          try {
            const result = await getAllFactory(baseCode);

            if (!result) continue;

            // 处理返回数据
            let orgData;
            if (typeof result === 'object') {
              orgData = result.data || result;
            } else {
              continue;
            }

            // 查找目标code在哪个层级
            const foundInfo = this.findCodeInOrgData(orgData, targetCode);

            if (foundInfo) {
              // 找到了对应的基地
              const baseInfo = this.orgLists.baseList.find(base => base.factoryModelCode === baseCode);
              if (baseInfo) {
                this.localSearchForm.baseCode = baseCode;
                this.localSearchForm.baseName = baseInfo.name;
                console.log(`通过反向查询在${foundInfo.level}层级找到基地: ${baseInfo.name} (${baseCode})`);

                // 根据找到的层级更新相应的信息
                this.updateFormDataByLevel(foundInfo);

                return baseCode; // 返回找到的基地代码
              }
            }

          } catch (error) {
            console.warn(`查询基地 ${baseCode} 失败:`, error);
            continue;
          }
        }

        console.warn('未能通过反向查询找到对应的基地信息');
        return null; // 未找到时返回null
      } catch (error) {
        console.error('反向查询基地信息失败:', error);
        return null; // 出错时返回null
      }
    },

    /**
     * 在组织数据中查找指定的code
     * @param {Object} orgData 组织数据
     * @param {String} targetCode 目标code
     * @returns {Object|null} 找到的信息，包含层级和数据
     */
    findCodeInOrgData(orgData, targetCode) {
      // 按层级顺序查找
      const levels = [
        { name: 'factory', data: orgData.factory, level: '工厂' },
        { name: 'subfactory', data: orgData.subfactory, level: '分厂' },
        { name: 'workshop', data: orgData.workshop, level: '车间' },
        { name: 'line', data: orgData.line, level: '线体' },
        { name: 'team', data: orgData.team, level: '班组' }
      ];

      for (const levelInfo of levels) {
        if (levelInfo.data && Array.isArray(levelInfo.data)) {
          const found = levelInfo.data.find(item => item.factoryModelCode === targetCode);
          if (found) {
            return {
              level: levelInfo.level,
              levelName: levelInfo.name,
              data: found
            };
          }
        }
      }

      return null;
    },

    /**
     * 根据找到的层级更新表单数据
     * @param {Object} foundInfo 找到的信息
     */
    updateFormDataByLevel(foundInfo) {
      const { level, data } = foundInfo;

      // 清空所有层级的数据
      this.localSearchForm.factoryCode = '';
      this.localSearchForm.factoryName = '';
      this.localSearchForm.branchCode = '';
      this.localSearchForm.branchName = '';
      this.localSearchForm.workshopCode = '';
      this.localSearchForm.workshopName = '';
      this.localSearchForm.lineCode = '';
      this.localSearchForm.lineName = '';
      this.localSearchForm.teamCode = '';
      this.localSearchForm.teamName = '';

      // 根据层级设置对应的数据
      switch (level) {
        case '工厂':
          this.localSearchForm.factoryCode = data.factoryModelCode;
          this.localSearchForm.factoryName = data.factoryModelName;
          break;
        case '分厂':
          this.localSearchForm.branchCode = data.factoryModelCode;
          this.localSearchForm.branchName = data.factoryModelName;
          break;
        case '车间':
          this.localSearchForm.workshopCode = data.factoryModelCode;
          this.localSearchForm.workshopName = data.factoryModelName;
          break;
        case '线体':
          this.localSearchForm.lineCode = data.factoryModelCode;
          this.localSearchForm.lineName = data.factoryModelName;
          break;
        case '班组':
          this.localSearchForm.teamCode = data.factoryModelCode;
          this.localSearchForm.teamName = data.factoryModelName;
          break;
      }

      console.log(`已更新${level}信息:`, {
        code: data.factoryModelCode,
        name: data.factoryModelName
      });
    },

    /**
     * 清理空的层级数据
     * 当某个层级没有数据时，清空对应的名称和代码
     */
    cleanupEmptyLevels() {
      try {
        let hasChanges = false;

        // 检查工厂层级
        if (this.orgLists.factoryList.length === 0 && (this.localSearchForm.factoryName || this.localSearchForm.factoryCode)) {
          this.localSearchForm.factoryName = '';
          this.localSearchForm.factoryCode = '';
          hasChanges = true;
          console.log('清理空的工厂层级数据');
        }

        // 检查分厂层级
        if (this.orgLists.branchList.length === 0 && (this.localSearchForm.branchName || this.localSearchForm.branchCode)) {
          this.localSearchForm.branchName = '';
          this.localSearchForm.branchCode = '';
          hasChanges = true;
          console.log('清理空的分厂层级数据');
        }

        // 检查车间层级
        if (this.orgLists.workshopList.length === 0 && (this.localSearchForm.workshopName || this.localSearchForm.workshopCode)) {
          this.localSearchForm.workshopName = '';
          this.localSearchForm.workshopCode = '';
          hasChanges = true;
          console.log('清理空的车间层级数据');
        }

        // 检查线体层级
        if (this.orgLists.lineList.length === 0 && (this.localSearchForm.lineName || this.localSearchForm.lineCode)) {
          this.localSearchForm.lineName = '';
          this.localSearchForm.lineCode = '';
          hasChanges = true;
          console.log('清理空的线体层级数据');
        }

        // 检查班组层级
        if (this.orgLists.teamList.length === 0 && (this.localSearchForm.teamName || this.localSearchForm.teamCode)) {
          this.localSearchForm.teamName = '';
          this.localSearchForm.teamCode = '';
          hasChanges = true;
          console.log('清理空的班组层级数据');
        }

        // 如果有变化，强制更新视图并同步到父组件
        if (hasChanges) {
          this.$nextTick(() => {
            this.$forceUpdate();
            // 同步清理后的数据到父组件
            const formData = { ...this.localSearchForm };
            this.$emit('input', formData);
          });
        }
      } catch (error) {
        console.error('清理空的层级数据失败:', error);
      }
    },

    /**
     * 同步数据到父组件
     * 将本地表单数据同步到父组件，并通知父组件数据已变化
     */
    syncToParent() {
      // 创建一个新的对象，避免引用问题
      const formData = { ...this.localSearchForm };

      // 发送input事件更新v-model绑定的值
      this.$emit('input', formData);

      // 如果处于初始化阶段，只更新值但不触发change事件
      // 这样可以避免在初始化过程中触发不必要的请求
      if (this.isInitializingOrg) {
        // console.log('组织层级初始化中，只更新值但不触发change事件');
        return;
      }

      // 发送change事件通知父组件数据已变化
      // 添加一个时间戳，确保每次都被视为新的变化
      // console.log('组织层级选择变化，触发change事件');
      this.$emit('change', { ...formData, _timestamp: Date.now() });
    },

    // 班组搜索过滤
    filterTeamOption(input, option) {
      return option.componentOptions.children[0].children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },

    // 处理基地选择变化
    async handleBaseChange(baseName) {
      try {
        // 如果处于初始化阶段，不执行后续操作
        if (this.isInitializingOrg) {
          // console.log('组织层级初始化中，跳过基地选择变化处理');
          return;
        }

        // 清空下级选择
        this.localSearchForm.factoryName = '';
        this.localSearchForm.branchName = '';
        this.localSearchForm.workshopName = '';
        this.localSearchForm.lineName = '';
        this.localSearchForm.teamName = '';

        // 同时清空code字段
        this.localSearchForm.factoryCode = '';
        this.localSearchForm.branchCode = '';
        this.localSearchForm.workshopCode = '';
        this.localSearchForm.lineCode = '';
        this.localSearchForm.teamCode = '';

        // 清空下级列表
        this.orgLists.factoryList = [];
        this.orgLists.branchList = [];
        this.orgLists.workshopList = [];
        this.orgLists.lineList = [];
        this.orgLists.teamList = [];

        if (!baseName) return;

        // 根据名称找到对应的基地对象
        const selectedBaseItem = this.orgLists.baseList.find(item => item.name === baseName);
        if (!selectedBaseItem) {
          // console.error(`未找到名称为 ${baseName} 的基地`);
          return;
        }

        // 保存基地code
        const baseCode = selectedBaseItem.factoryModelCode;
        this.localSearchForm.baseCode = baseCode;

        // 设置工厂加载状态
        this.localLoadingStatus.factory = true;

        // console.log(`基地选择变化: ${baseName} (${baseCode})，开始获取工厂列表数据`);

        // 重新获取组织数据
        const result = await getAllFactory(baseCode);
        if (!result) {
          // console.error('获取组织层级数据失败: result 为空');
          return;
        }

        // 根据 result 的类型和结构进行处理
        let orgData;
        if (typeof result === 'object') {
          // 如果 result 是对象，检查它是否有 data 属性
          if (result.data) {
            orgData = result.data;
          } else {
            // 如果没有 data 属性，假设 result 本身就是数据
            orgData = result;
          }
        } else {
          console.error('获取组织层级数据失败: result 不是对象', result);
          return;
        }

        // 检查组织数据是否包含必要的属性
        if (!orgData.factory || !orgData.subfactory || !orgData.workshop || !orgData.line || !orgData.team) {
          console.error('组织数据结构不完整', orgData);
          return;
        }

        // 保存完整的组织数据
        this.allOrgData = orgData;

        // 加载工厂列表
        this.loadFactoryList();

        // 同步数据到父组件
        this.syncToParent();

        // 清理空的层级数据
        this.$nextTick(() => {
          this.cleanupEmptyLevels();
        });
      } catch (error) {
        console.error("处理基地选择变化失败:", error);
      } finally {
        this.localLoadingStatus.factory = false;
      }
    },

    // 加载工厂列表
    loadFactoryList() {
      try {
        if (!this.allOrgData || !this.allOrgData.factory) {
          // console.error("组织数据不完整，无法加载工厂列表");
          return;
        }

        // 从API返回的数据中提取工厂列表
        this.orgLists.factoryList = this.allOrgData.factory.map(item => ({
          name: item.factoryModelName,
          code: item.factoryModelCode,
          parentCode: item.parentCode,
          parentName: item.parentName
        }));

        if (this.orgLists.factoryList.length > 0) {
          // 优先选择匹配默认值的工厂，如果没有匹配的则选择第一个
          let selectedFactory = null;

          // 如果有默认的工厂code，尝试匹配
          if (this.localSearchForm.factoryCode) {
            selectedFactory = this.orgLists.factoryList.find(item => item.code === this.localSearchForm.factoryCode);
          }

          // 如果没有找到匹配的，选择第一个
          if (!selectedFactory) {
            selectedFactory = this.orgLists.factoryList[0];
          }

          this.localSearchForm.factoryName = selectedFactory.name;
          this.localSearchForm.factoryCode = selectedFactory.code;
          console.log(`选中工厂: ${selectedFactory.name} (${selectedFactory.code})`);

          // 处理工厂选择变化
          this.loadBranchList();
        } else {
          // 如果工厂列表为空，尝试直接加载分厂列表
          // console.log("工厂列表为空，尝试直接加载分厂列表");
          this.loadBranchList(true);
        }
      } catch (error) {
        console.error("获取工厂列表失败:", error);
      } finally {
        this.localLoadingStatus.factory = false;
      }
    },

    // 处理工厂选择变化
    handleFactoryChange(factoryName) {
      try {
        // 如果处于初始化阶段，不执行后续操作
        if (this.isInitializingOrg) {
          // console.log('组织层级初始化中，跳过工厂选择变化处理');
          return;
        }

        // 清空下级选择
        this.localSearchForm.branchName = '';
        this.localSearchForm.workshopName = '';
        this.localSearchForm.lineName = '';
        this.localSearchForm.teamName = '';

        // 同时清空code字段
        this.localSearchForm.branchCode = '';
        this.localSearchForm.workshopCode = '';
        this.localSearchForm.lineCode = '';
        this.localSearchForm.teamCode = '';

        // 清空下级列表
        this.orgLists.branchList = [];
        this.orgLists.workshopList = [];
        this.orgLists.lineList = [];
        this.orgLists.teamList = [];

        if (!factoryName) return;

        // 根据名称找到对应的工厂对象
        const selectedFactoryItem = this.orgLists.factoryList.find(item => item.name === factoryName);
        if (!selectedFactoryItem) {
          console.error(`未找到名称为 ${factoryName} 的工厂`);
          return;
        }

        // 保存工厂code
        const factoryCode = selectedFactoryItem.code;
        this.localSearchForm.factoryCode = factoryCode;

        // 设置分厂加载状态
        this.localLoadingStatus.branch = true;

        // console.log(`工厂选择变化: ${factoryName} (${factoryCode})，开始获取分厂列表数据`);

        // 加载分厂列表
        this.loadBranchList();

        // 同步数据到父组件
        this.syncToParent();

        // 清理空的层级数据
        this.$nextTick(() => {
          this.cleanupEmptyLevels();
        });
      } catch (error) {
        console.error("处理工厂选择变化失败:", error);
      } finally {
        this.localLoadingStatus.branch = false;
      }
    },

    // 加载分厂列表
    loadBranchList(skipParentFilter = false) {
      try {
        if (!this.allOrgData || !this.allOrgData.subfactory) {
          console.error("组织数据不完整，无法加载分厂列表");
          return;
        }

        // 从API返回的数据中提取分厂列表
        let filteredSubfactory;

        if (skipParentFilter || !this.localSearchForm.factoryCode) {
          // 如果跳过父级过滤或者没有选择工厂，则显示所有分厂
          // console.log("显示所有分厂，不根据父级过滤");
          filteredSubfactory = this.allOrgData.subfactory;
        } else {
          // 根据当前选中的工厂factoryModelCode过滤
          // console.log(`根据工厂代码 ${this.localSearchForm.factoryCode} 过滤分厂`);
          filteredSubfactory = this.allOrgData.subfactory.filter(
            item => item.parentCode === this.localSearchForm.factoryCode
          );
        }

        // 映射分厂数据
        this.orgLists.branchList = filteredSubfactory.map(item => ({
          name: item.factoryModelName,
          code: item.factoryModelCode, // 保存factoryModelCode作为code
          parentCode: item.parentCode,
          parentName: item.parentName
        }));

        if (this.orgLists.branchList.length > 0) {
          // 优先选择匹配默认值的分厂，如果没有匹配的则选择第一个
          let selectedBranch = null;

          // 如果有默认的分厂code，尝试匹配
          if (this.localSearchForm.branchCode) {
            selectedBranch = this.orgLists.branchList.find(item => item.code === this.localSearchForm.branchCode);
          }

          // 如果没有找到匹配的，选择第一个
          if (!selectedBranch) {
            selectedBranch = this.orgLists.branchList[0];
          }

          this.localSearchForm.branchName = selectedBranch.name;
          this.localSearchForm.branchCode = selectedBranch.code;
          // console.log(`选中分厂: ${selectedBranch.name} (${selectedBranch.code})`);

          // 处理分厂选择变化
          this.loadWorkshopList();
        } else {
          // 如果分厂列表为空，尝试直接加载车间列表
          // console.log("分厂列表为空，尝试直接加载车间列表");
          this.loadWorkshopList(true);
        }
      } catch (error) {
        // console.error("获取分厂列表失败:", error);
      } finally {
        this.localLoadingStatus.branch = false;
      }
    },

    // 处理分厂选择变化
    handleBranchChange(branchName) {
      try {
        // 如果处于初始化阶段，不执行后续操作
        if (this.isInitializingOrg) {
          // console.log('组织层级初始化中，跳过分厂选择变化处理');
          return;
        }

        // 清空下级选择
        this.localSearchForm.workshopName = '';
        this.localSearchForm.lineName = '';
        this.localSearchForm.teamName = '';

        // 同时清空code字段
        this.localSearchForm.workshopCode = '';
        this.localSearchForm.lineCode = '';
        this.localSearchForm.teamCode = '';

        // 清空下级列表
        this.orgLists.workshopList = [];
        this.orgLists.lineList = [];
        this.orgLists.teamList = [];

        if (!branchName) return;

        // 根据名称找到对应的分厂对象
        const selectedBranchItem = this.orgLists.branchList.find(item => item.name === branchName);
        if (!selectedBranchItem) {
          console.error(`未找到名称为 ${branchName} 的分厂`);
          return;
        }

        // 保存分厂code
        const branchCode = selectedBranchItem.code;
        this.localSearchForm.branchCode = branchCode;

        // 设置车间加载状态
        this.localLoadingStatus.workshop = true;

        // console.log(`分厂选择变化: ${branchName} (${branchCode})，开始获取车间列表数据`);

        // 加载车间列表
        this.loadWorkshopList();

        // 同步数据到父组件
        this.syncToParent();

        // 清理空的层级数据
        this.$nextTick(() => {
          this.cleanupEmptyLevels();
        });
      } catch (error) {
        console.error("处理分厂选择变化失败:", error);
      } finally {
        this.localLoadingStatus.workshop = false;
      }
    },

    // 加载车间列表
    loadWorkshopList(skipParentFilter = false) {
      try {
        if (!this.allOrgData || !this.allOrgData.workshop) {
          console.error("组织数据不完整，无法加载车间列表");
          return;
        }

        // 从API返回的数据中提取车间列表
        let filteredWorkshops;

        if (skipParentFilter || !this.localSearchForm.branchCode) {
          // 如果跳过父级过滤或者没有选择分厂，则显示所有车间
          // console.log("显示所有车间，不根据父级过滤");
          filteredWorkshops = this.allOrgData.workshop;
        } else {
          // 根据当前选中的分厂factoryModelCode过滤
          // console.log(`根据分厂代码 ${this.localSearchForm.branchCode} 过滤车间`);
          filteredWorkshops = this.allOrgData.workshop.filter(
            item => item.parentCode === this.localSearchForm.branchCode
          );
        }

        // 映射车间数据
        this.orgLists.workshopList = filteredWorkshops.map(item => ({
          name: item.factoryModelName,
          code: item.factoryModelCode, // 保存factoryModelCode作为code
          parentCode: item.parentCode,
          parentName: item.parentName
        }));

        if (this.orgLists.workshopList.length > 0) {
          // 优先选择匹配默认值的车间，如果没有匹配的则选择第一个
          let selectedWorkshop = null;

          // 如果有默认的车间code，尝试匹配
          if (this.localSearchForm.workshopCode) {
            selectedWorkshop = this.orgLists.workshopList.find(item => item.code === this.localSearchForm.workshopCode);
          }

          // 如果没有找到匹配的，选择第一个
          if (!selectedWorkshop) {
            selectedWorkshop = this.orgLists.workshopList[0];
          }

          this.localSearchForm.workshopName = selectedWorkshop.name;
          this.localSearchForm.workshopCode = selectedWorkshop.code;
          // console.log(`选中车间: ${selectedWorkshop.name} (${selectedWorkshop.code})`);

          // 处理车间选择变化
          this.loadLineList();
        } else {
          // 如果车间列表为空，尝试直接加载线体列表
          console.log("车间列表为空，尝试直接加载线体列表");
          this.loadLineList(true);
        }
      } catch (error) {
        console.error("获取车间列表失败:", error);
      } finally {
        this.localLoadingStatus.workshop = false;
      }
    },

    // 处理车间选择变化
    handleWorkshopChange(workshopName) {
      try {
        // 如果处于初始化阶段，不执行后续操作
        if (this.isInitializingOrg) {
          // console.log('组织层级初始化中，跳过车间选择变化处理');
          return;
        }

        // 清空线体和班组选择
        this.localSearchForm.lineName = '';
        this.localSearchForm.teamName = '';

        // 同时清空code字段
        this.localSearchForm.lineCode = '';
        this.localSearchForm.teamCode = '';

        // 清空线体和班组列表
        this.orgLists.lineList = [];
        this.orgLists.teamList = [];

        if (!workshopName) return;

        // 根据名称找到对应的车间对象
        const selectedWorkshopItem = this.orgLists.workshopList.find(item => item.name === workshopName);
        if (!selectedWorkshopItem) {
          console.error(`未找到名称为 ${workshopName} 的车间`);
          return;
        }

        // 保存车间code
        const workshopCode = selectedWorkshopItem.code;
        this.localSearchForm.workshopCode = workshopCode;

        // 设置线体加载状态
        this.localLoadingStatus.line = true;

        // console.log(`车间选择变化: ${workshopName} (${workshopCode})，开始获取线体列表数据`);

        // 加载线体列表
        this.loadLineList();

        // 同步数据到父组件
        this.syncToParent();

        // 清理空的层级数据
        this.$nextTick(() => {
          this.cleanupEmptyLevels();
        });
      } catch (error) {
        console.error("处理车间选择变化失败:", error);
      } finally {
        this.localLoadingStatus.line = false;
      }
    },

    // 加载线体列表
    loadLineList(skipParentFilter = false) {
      try {
        if (!this.allOrgData || !this.allOrgData.line) {
          console.error("组织数据不完整，无法加载线体列表");
          return;
        }

        // 从API返回的数据中提取线体列表
        let filteredLines;

        if (skipParentFilter || !this.localSearchForm.workshopCode) {
          // 如果跳过父级过滤或者没有选择车间，则显示所有线体
          // console.log("显示所有线体，不根据父级过滤");
          filteredLines = this.allOrgData.line;
        } else {
          // 根据当前选中的车间factoryModelCode过滤
          // console.log(`根据车间代码 ${this.localSearchForm.workshopCode} 过滤线体`);
          filteredLines = this.allOrgData.line.filter(
            item => item.parentCode === this.localSearchForm.workshopCode
          );
        }

        // 映射线体数据
        this.orgLists.lineList = filteredLines.map(item => ({
          name: item.factoryModelName,
          code: item.factoryModelCode, // 保存factoryModelCode作为code
          parentCode: item.parentCode,
          parentName: item.parentName
        }));

        if (this.orgLists.lineList.length > 0) {
          // 优先选择匹配默认值的线体，如果没有匹配的则选择第一个
          let selectedLine = null;

          // 如果有默认的线体code，尝试匹配
          if (this.localSearchForm.lineCode) {
            selectedLine = this.orgLists.lineList.find(item => item.code === this.localSearchForm.lineCode);
          }

          // 如果没有找到匹配的，选择第一个
          if (!selectedLine) {
            selectedLine = this.orgLists.lineList[0];
          }

          this.localSearchForm.lineName = selectedLine.name;
          this.localSearchForm.lineCode = selectedLine.code;
          console.log(`选中线体: ${selectedLine.name} (${selectedLine.code})`);

          // 处理线体选择变化
          this.loadTeamList();
        } else {
          // 如果没有线体，直接加载班组
          // console.log("线体列表为空，尝试直接加载班组列表");
          this.loadTeamList(true);
        }
      } catch (error) {
        console.error("获取线体列表失败:", error);
      } finally {
        this.localLoadingStatus.line = false;
      }
    },

    // 处理线体选择变化
    handleLineChange(lineName) {
      try {
        // 如果处于初始化阶段，不执行后续操作
        if (this.isInitializingOrg) {
          console.log('组织层级初始化中，跳过线体选择变化处理');
          return;
        }

        // 清空班组选择
        this.localSearchForm.teamName = '';
        this.localSearchForm.teamCode = '';

        // 清空班组列表
        this.orgLists.teamList = [];

        if (!lineName) return;

        // 根据名称找到对应的线体对象
        const selectedLineItem = this.orgLists.lineList.find(item => item.name === lineName);
        if (!selectedLineItem) {
          console.error(`未找到名称为 ${lineName} 的线体`);
          return;
        }

        // 保存线体code
        const lineCode = selectedLineItem.code;
        this.localSearchForm.lineCode = lineCode;

        // 设置班组加载状态
        this.localLoadingStatus.team = true;

        // console.log(`线体选择变化: ${lineName} (${lineCode})，开始获取班组列表数据`);

        // 加载班组列表
        this.loadTeamList();

        // 同步数据到父组件
        this.syncToParent();

        // 清理空的层级数据
        this.$nextTick(() => {
          this.cleanupEmptyLevels();
        });
      } catch (error) {
        console.error("处理线体选择变化失败:", error);
      } finally {
        this.localLoadingStatus.team = false;
      }
    },

    // 加载班组列表
    loadTeamList(skipParentFilter = false) {
      try {
        if (!this.allOrgData || !this.allOrgData.team) {
          console.error("组织数据不完整，无法加载班组列表");
          return;
        }

        // 从API返回的数据中提取班组列表
        let filteredTeams = [];

        if (skipParentFilter) {
          // 如果跳过父级过滤，则显示所有班组
          // console.log("显示所有班组，不根据父级过滤");
          filteredTeams = this.allOrgData.team;
        }
        // 如果有线体，则根据线体factoryModelCode过滤
        else if (this.localSearchForm.lineCode) {
          // console.log(`根据线体代码 ${this.localSearchForm.lineCode} 过滤班组`);
          filteredTeams = this.allOrgData.team.filter(item =>
            item.parentCode === this.localSearchForm.lineCode // lineCode存储的是上级的factoryModelCode
          );
        }
        // 如果没有线体但有车间，则根据车间factoryModelCode过滤
        else if (this.localSearchForm.workshopCode) {
          // console.log(`根据车间代码 ${this.localSearchForm.workshopCode} 查找线体并过滤班组`);
          // 获取所有线体
          const workshopLines = this.allOrgData.line.filter(item =>
            item.parentCode === this.localSearchForm.workshopCode // workshopCode存储的是上级的factoryModelCode
          );

          // 获取这些线体下的所有班组
          const lineCodeList = workshopLines.map(line => line.factoryModelCode);
          if (lineCodeList.length > 0) {
            // console.log(`找到线体代码列表: ${lineCodeList.join(', ')}`);
            filteredTeams = this.allOrgData.team.filter(item =>
              lineCodeList.includes(item.parentCode)
            );
          } else {
            // console.log(`未找到车间 ${this.localSearchForm.workshopCode} 下的线体，显示所有班组`);
            filteredTeams = this.allOrgData.team;
          }
        }
        // 如果既没有线体也没有车间，则显示所有班组
        else {
          console.log("没有选择线体和车间，显示所有班组");
          filteredTeams = this.allOrgData.team;
        }

        // 映射班组数据
        this.orgLists.teamList = filteredTeams.map(item => ({
          name: item.factoryModelName,
          code: item.factoryModelCode,
          parentCode: item.parentCode,
          parentName: item.parentName
        }));

        if (this.orgLists.teamList.length > 0) {
          // 优先选择匹配默认值的班组，如果没有匹配的则选择第一个
          let selectedTeam = null;

          // 如果有默认的班组code，尝试匹配
          if (this.localSearchForm.teamCode) {
            selectedTeam = this.orgLists.teamList.find(item => item.code === this.localSearchForm.teamCode);
          }

          // 如果没有找到匹配的，选择第一个
          if (!selectedTeam) {
            selectedTeam = this.orgLists.teamList[0];
          }

          this.localSearchForm.teamName = selectedTeam.name;
          this.localSearchForm.teamCode = selectedTeam.code;
          // console.log(`选中班组: ${selectedTeam.name} (${selectedTeam.code})`);

          // 处理班组选择变化
          this.handleTeamChange(selectedTeam.name);
        }
      } catch (error) {
        console.error("获取班组列表失败:", error);
      } finally {
        this.localLoadingStatus.team = false;
      }
    },

    /**
     * 处理班组选择变化
     * @param {string} teamName 选择的班组名称
     */
    handleTeamChange(teamName) {
      // 如果处于初始化阶段，不执行后续操作
      if (this.isInitializingOrg) {
        // console.log('组织层级初始化中，跳过班组选择变化处理');
        return;
      }

      if (!teamName) return;

      // 根据名称找到对应的班组对象
      const selectedTeamItem = this.orgLists.teamList.find(item => item.name === teamName);
      if (!selectedTeamItem) {
        console.error(`未找到名称为 ${teamName} 的班组`);
        return;
      }

      // 保存班组code
      const teamCode = selectedTeamItem.code;
      this.localSearchForm.teamCode = teamCode;

      // 在这里可以添加班组选择后的处理逻辑
      // console.log(`选中班组: ${teamName} (${teamCode})`);

      // 同步数据到父组件
      this.syncToParent();
    }
  }
}
</script>

<style lang="less" scoped>
.org-selects-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 8px;

  .org-selects-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
}
</style>
