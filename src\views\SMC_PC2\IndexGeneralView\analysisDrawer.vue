<!--
 * @Description: 专项分析抽屉
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 09:08:29
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-10 14:29:41
-->
<template>
  <a-drawer
    title="查看专项分析"
    placement="right"
    :visible="visible"
    @close="close"
    width="400px"
  >
    <div
      class="items"
      v-for="(item, index) in analysisList"
      :key="index"
      @click="toReport(item)"
    >
      {{ item.businessSegments }}专项分析
    </div>
  </a-drawer>
</template>
<script>
import { openReport } from "../utils";

export default {
  props: {
    // 角色控制的按钮权限
    analysisList: {
      type: Array,
      default() {
        return [];
      }
    },
    companyName: String
  },
  data() {
    return {
      visible: false,
      SYS_NAME: window.system
    };
  },
  methods: {
    // 打开抽屉
    show() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    // 打开报表
    toReport(item) {
      // 通用报表
      let paramsArr = [];
      if (item.reportName === "云图概览通用报表") {
        paramsArr.push(`companyName=${this.companyName}`);
        paramsArr.push(`businessSegments=${item.businessSegments}`);
        const roles =
          window.vm && window.vm.$store
            ? window.vm.$store.state.user.roles || []
            : [];
        paramsArr.push(`roles=${roles.join(",")}`);
      }
      window.vm.$message.success("正在打开报表....");
      openReport(item.reportId, paramsArr.join("&"));
      this.close();
    }
  }
};
</script>
<style lang="less" scoped>
.items {
  width: 352px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  box-sizing: border-box;
  background-image: url("~@/assets/images/plate_bg2.png");
  padding-left: 16px;
  color: #ffffff;
  line-height: 80px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 12px;
}
</style>
