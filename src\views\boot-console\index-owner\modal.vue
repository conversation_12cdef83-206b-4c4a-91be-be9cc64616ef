<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2022-07-29 17:39:56
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    title="编辑"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="mainForm"
      :model="mainForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row>
        <a-col :span="8">
          <a-form-model-item label="指标名称">
            <a-input v-model="mainForm.indexName" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="版块">
            <a-input v-model="mainForm.businessSegments" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="维度">
            <a-input v-model="mainForm.dimension" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-model-item label="注册组织">
            <a-input v-model="mainForm.company" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="指标主人" prop="managerLdap">
            <a-select
              show-search
              :value="mainForm.managerLdap"
              :dropdownMatchSelectWidth="false"
              placeholder="请输入主人名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleAccountSearch($event, 'managerLdap')"
              @change="handleAccountChange($event, 'managerLdap')"
            >
              <a-select-option
                v-for="d in managerLdapList"
                :key="`${d.name}-${d.account}`"
                :value="`${d.name}-${d.account}`"
              >
                {{ d.name }}({{ d.account }}) {{ d.o }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="分管领导" prop="leaderOwner">
            <a-select
              show-search
              :value="mainForm.leaderOwner"
              :dropdownMatchSelectWidth="false"
              placeholder="请输入主人名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleAccountSearch($event, 'leaderOwner')"
              @change="handleAccountChange($event, 'leaderOwner')"
            >
              <a-select-option
                v-for="d in leaderOwnerList"
                :key="`${d.name}-${d.account}`"
                :value="`${d.name}-${d.account}`"
              >
                {{ d.name }}({{ d.account }}) {{ d.o }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-model-item label="计算组织">
            <a-input v-model="mainForm.org" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
export default {
  data() {
    this.handleAccountSearch = debounce(this.handleAccountSearch, 800);
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 8 },
      wrapperCol: { span: 10 },
      mainForm: {
        indexName: "",
        businessSegments: "",
        dimension: "",
        company: "",
        managerLdap: "",
        leaderOwner: "",
        org: ""
      },
      rules: {
        managerLdap: [
          {
            required: true,
            message: "请填写指标主人",
            trigger: "change"
          }
        ],
        leaderOwner: [
          {
            required: true,
            message: "请填写分管领导",
            trigger: "change"
          }
        ]
      },
      managerLdapList: [],
      leaderOwnerList: []
    };
  },
  methods: {
    handleAccountSearch(value, key) {
      this.getAcountList(value).then(res => {
        this[`${key}List`] = res;
      });
    },
    handleAccountChange(value, key) {
      this.mainForm[key] = value;
    },
    getAcountList(account) {
      return new Promise(resolve => {
        request(`/api/smc2/ldap/searchLdapUser`, {
          method: "POST",
          body: {
            account
          }
        }).then(res => {
          console.log("res----->", res);
          resolve(res || []);
        });
      });
    },
    async show(formValue) {
      this.visible = true;
      if (formValue) {
        const {
          cmimId,
          indexName,
          businessSegments,
          dimension,
          company,
          managerLdap,
          leaderOwner,
          org
        } = formValue;
        this.mainForm = {
          cmimId,
          indexName,
          businessSegments,
          dimension,
          company,
          managerLdap,
          leaderOwner,
          org
        };
        if (managerLdap) {
          this.handleAccountSearch(managerLdap.split("-")[1], "managerLdap");
        }
        if (leaderOwner) {
          setTimeout(() => {
            this.handleAccountSearch(leaderOwner.split("-")[1], "leaderOwner");
          }, 1000);
        }
      }
    },
    close() {
      this.mainForm = {
        indexName: "",
        businessSegments: "",
        dimension: "",
        company: "",
        managerLdap: "",
        leaderOwner: "",
        org: ""
      };
      this.$refs.mainForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    // 提交
    submit() {
      this.$refs.mainForm.validate(async valid => {
        if (valid) {
          request(`/api/smc2/owner/update`, {
            method: "POST",
            body: {
              managerLdap: this.mainForm.managerLdap,
              leaderOwner: this.mainForm.leaderOwner,
              cmimId: this.mainForm.cmimId
            }
          }).then(res => {
            if (res.result === "success") {
              this.close();
              this.$emit("fetchData");
            } else {
              this.$message.error(res.result);
            }
          });
        }
      });
    }
  }
};
</script>
