<template>
  <div>
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal ref="modal" />
  </div>
</template>
<script>
import Modal from "./modal-new.vue";
import { showAlias } from "@/utils/intl.js";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      // let roleCode = "smc-admin";
      let roleCode = this.record.roleCode;
      this.$refs.modal.show({
        roleCode
      });
    }
  }
};
</script>
