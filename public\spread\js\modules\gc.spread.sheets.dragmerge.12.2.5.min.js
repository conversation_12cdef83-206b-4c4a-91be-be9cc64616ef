/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.DragMerge=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/dragMerge/dragMerge.entry.js")}({"./dist/plugins/dragMerge/dragMerge-action.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),b.Commands=d.Commands,e=d.Commands.ActionBase,f=d.Commands.h4,g="dragMerge",h=Math.max;function k(a,b){return a.Tq(b)}i=function(a){j(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){var a,b=this.kj,c=this.VQ,d=k(b,c.oldSelection),e=k(b,c.newSelection);if(d.equals(e)){if(a=b.getSpan(d.row,d.col),a&&d.equals(a))return!1;if(1===d.rowCount&&1===d.colCount)return!1}return!0},b.prototype.canUndo=function(){var a=d.Commands.bWa(this.kj.name()),b=this.VQ[a];return d.Commands.cWa(b)},b.prototype.undo=function(){var a,b,c=this;return!!c.canUndo()&&(a=c.kj,c.Lz(a,!0),b=d.Commands.bWa(a.name()),a.ITa.undo(c.VQ[b]),c.Mz(a,!0),void 0)},b.prototype.execute=function(){var a,b,c,e,f,g=this,i=g.kj,j=g.VQ,k=j.oldSelection,l=j.newSelection;return!!g.canExecute()&&(i.ITa.startTransaction(),g.Lz(i,!0),i.ITa.do("clearSpan",k.row,k.col,k.rowCount,k.colCount),a=l.row,b=l.col,c=l.rowCount,e=l.colCount,i.addSpan(a,b,c,e),i.setSelection(a,b,h(c,1),h(e,1)),g.Mz(i,!0),f=d.Commands.bWa(i.name()),j[f]=i.ITa.endTransaction(),void 0)},b}(e),d.Commands[g]={canUndo:!0,execute:function(a,b,c){return f(a,i,b,c)}},d.Commands.dja=function(a){a.register(g,d.Commands[g])}},"./dist/plugins/dragMerge/dragMerge-event.js":function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=d.GC$,f=null,g=void 0,h=Math.abs,i=d.kf;function j(a,b){a.Wq(d.Events.DragMerging,b)}function k(a,b){a.Wq(d.Events.DragMerged,b)}e.extend(d.iI.prototype,{gka:function(a){var b=this,c=b.kj,d=a,e=c.ITa.findSpan(a.row,a.col);return e&&e.containsRange(a)&&(d=e),d},Yia:function(a,b,c){var d,e,g,i,j,k=this.kj,l=k.parent,m=k.Ix(),n=k.yl,o=4,p=8,q=o/2,r=p/2;if(l&&l.options.allowUserDragMerge&&m&&!k.CH&&n.Xja){if(d=n.Xja,e=d.x,g=d.y,i=d.width,j=d.height,h(e+i-b)<=q&&h(g+j/2-c)<=r)return{right:!0};if(h(e+i/2-b)<=r&&h(g+j-c)<=q)return{bottom:!0}}return f},Zia:function(a){var b,c,d,e,f,g,h,i,j=this;return j.eG=!0,j.OG=!0,j.$ia=!0,j.rG={KG:a.rowViewportIndex,MG:a.colViewportIndex,sG:a.hitTestType},j.fja=a.dragMergeInfo,b=j.kj,c=b.ITa,d=b.Ix(),j.ika=j.gka(d),e=b.Tq(j.ika),f=e.row,g=e.col,h=e.rowCount,i=e.colCount,c.qu(f,g,h,i)?(j.OG=!1,void(j.$ia=!1)):(j._ja=b.getSpans(e),b.suspendPaint(),c.do("clearSpan",f,g,h,i),b.resumePaint(),j.aka=e,j.bka(),j.jka(e),void j.qG())},_ia:function(){var a,b,c,d,e,f,g,h,i=this,j=i.kj,k=j.parent;return k&&!k.options.allowUserDragMerge?void i.aja():(a=i.fH(),b=i.gH(),c=i.aka||i.ika,d=j.Tq(c),void(a>=0&&b>=0&&(e=i.fja,f=i.qja(d,a,b,e),i.kka=f,g=f.rowCount,h=f.colCount,g>0&&h>0&&(i.bka(),i.jka(f),i.aka=f,i.NG()))))},bka:function(){var a,b=this.kj,c=this.aka;c&&(a=b.cm(c),a.x-=2,a.y-=2,a.width+=4,a.height+=4,b.yl.dm(a))},qja:function(a,b,c,d){var e,f,g,h,j,k,l,m,n=this,o=n.kj,p=a.row,q=a.col,r=a.rowCount,s=a.colCount;return d.right?(e=p,f=q,g=r,h=c-q+1,j=e,k=q+s,l=g,m=c-q-s+1):d.bottom&&(e=p,f=q,g=b-p+1,h=s,j=p+r,k=f,l=b-p-r+1,m=h),o.ITa.MTa(j,k,l,m)&&(e=p,f=q,g=r,h=s),i(e,f,g,h)},aja:function(){var a,b,c,d=this,e=d.kj,f=e.parent,h=d._ja,i=d.ika,l=d.kka||i;d.eG=!1,d.OG=!1,d.$ia=!1,d.kka=g,d._ja=g,d.aka=g,d.ika=g,d.RG(),f&&f.options.allowUserDragMerge&&(a=e.ITa,e.suspendPaint(),h.length>0&&h.forEach(function(b){a.do("addSpan",b)}),e.resumePaint(),b={sheetName:e.name(),sheet:e,mergeRange:l,cancel:!1},j(e,b),b.cancel||(c={cmd:"dragMerge",sheetName:e.name(),oldSelection:i,newSelection:l},e.wu().execute(c),k(e,{sheetName:e.name(),sheet:e,mergeRange:l})))},jka:function(a){var b=this,c=b.kj,d=c.yl,e=d.hka;e(d,a)}})},"./dist/plugins/dragMerge/dragMerge.entry.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),c("./dist/plugins/dragMerge/dragMerge-action.js"),c("./dist/plugins/dragMerge/dragMerge.js"),c("./dist/plugins/dragMerge/dragMerge-event.js")},"./dist/plugins/dragMerge/dragMerge.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("./dist/plugins/dragMerge/dragMerge-action.js"),g=e.Common.pc,h=d.GC$,i="white";function l(a){var b=a.parent;return b&&b.options.backColor||i}h.extend(d.oJ.prototype,{cja:function(a){var b=this,c=b.kj,d=c.mm,e=c.Ix(),f=c.cm(d.gka(e));b.Wja(a,f)},Wja:function(a,b){var c,e,f,g,h,i,j,k,l=this,m=l.kj;a.save(),c=4,e=8,f=c/2,g=e/2,h=b.x,i=b.y,j=b.width,k=b.height,l.Xja=b,a.fillStyle=d.Rm.Om(m,m.getSelectionBorderColor()),a.beginPath(),a.rect(h+j-f,i+k/2-g,c,e),a.fill(),a.beginPath(),a.rect(h+j/2-g,i+k-f,e,c),a.fill(),a.restore()},hka:function(a,b){var c,e,f,h,i,j,k,m,n,o,p,q,r=a,s=r.kj,t=s.parent,u=s.mm,v=a.bm(),w=2,x=w/2;t&&t.options.allowUserDragMerge&&u.$ia&&(c=s.getActiveRowIndex(),e=s.getActiveColumnIndex(),f=s.getActualStyle(c,e),h=f&&f.backColor,i=l(s),j=g.ec(d.Rm.Om(s,h||i)),j.a*=.6/255,v.fillStyle=g.bc(j),v.strokeStyle=d.Rm.Om(s,s.getSelectionBorderColor()),v.lineWidth=2,v.beginPath(),k=t.Vv,m=s.cm(b),n=m.x,o=m.y,p=m.width,q=m.height,k>2007?(v.rect(n,o,p-x,q-x),v.strokeRect(n-1,o-1,p+1,q+1)):(v.rect(n+1,o+1,p-w-.5,q-w-.5),v.strokeRect(n-.5,o-.5,p,q)),v.fill(),v.restore(),r.Wja(v,m))}}),j={init:function(){f.Commands.dja(this.commandManager())}},d.Workbook.$n("dragMerge",j),k={paintAdornment:function(a){var b=a.ctx,c=this,d=c.yl,e=c.ITa.getSelections();c.parent&&c.parent.options.allowUserDragMerge&&1===e.length&&!c.CH&&d.cja(b)}},d.Worksheet.$n("dragMerge",k)},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});