<!--
 * @Description: 视像核心KPI概览图
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 14:26:03
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-09 11:25:50
-->
<template>
  <!-- 给这个div绑定公司名称的class是为了拖拽时候找到html元素 -->
  <div
    class="indexGeneralViewPage"
    :ref="`${companyName}indexGeneralViewPage`"
    :class="companyName"
  >
    <div class="_top">
      <!-- 顶部 -->
      <PageHeader
        :companyName="companyName"
        :operationGuideJSUrl="operationGuideJSUrl"
        @pageHeaderChange="searchConditionChange"
        :baseLabelName="attribute.baseLabelName"
        :baseList="baseList"
        :selfReportsJSUrl="selfReportsJSUrl"
        ref="pageHeader"
        @reportModalChange="
          e => {
            this.$refs['plate'].setStatus(e);
          }
        "
      />
      <!-- 置顶模块 -->
      <StickyIndex
        v-if="searchForm.mode === 'chart'"
        :cardListLoading="cardListLoading"
        :topCardList="topCardList"
      />
      <!-- 版块 -->
      <Plate
        v-if="searchForm.mode === 'chart'"
        @plateChange="plateChange"
        :plateList="plateList"
        :cardList="cardList"
        :searchIndexName.sync="searchIndexName"
        :companyName="companyName"
        :base="searchForm.base"
        ref="plate"
        @indexStatusChnage="
          e => {
            this.$refs['pageHeader'].setStatus(e);
          }
        "
      />
    </div>
    <!-- 底部表格区域 -->
    <div class="_bottom" v-if="searchForm.mode === 'table'">
      <a-spin :spinning="cardListLoading">
        <div
          class="_flex"
          style="justify-content: flex-end; margin-bottom: 12px;"
        >
          <a-button type="primary" @click="download">
            数据导出
          </a-button>
        </div>
        <a-table
          :pagination="false"
          size="small"
          :columns="columns"
          :data-source="tableData"
        >
        </a-table>
      </a-spin>
    </div>
    <!-- 底部卡片区域 -->
    <div
      class="_bottom"
      v-if="searchForm.mode === 'chart'"
      :style="{ 'padding-bottom': cardListDraged ? '80px' : '16px' }"
    >
      <CardList
        ref="cardList"
        :activePlate="activePlate"
        :indexCardDetailInfoJSUrl="indexCardDetailInfoJSUrl"
        :searchIndexName="searchIndexName"
        :cardListLoading="cardListLoading"
        :list="newCardList"
        @refreshData="searchConditionChange"
        :base="searchForm.base || baseList[0]"
        :companyName="companyName"
        @changeRecommend="changeRecommend"
        pageClass="indexGeneralViewPage"
        @onDraged="e => (cardListDraged = e)"
        @addToCardList="addToCardList"
        :sortLoading.sync="sortLoading"
        :frequency="
          this.searchForm.timeType === 'day'
            ? '日'
            : this.searchForm.timeType === 'week'
            ? '周'
            : '月'
        "
        :indexDt="this.searchForm.time"
      />
      <div class="saveCardSort" v-if="cardListDraged">
        <a-button style="margin-right: 16px;" @click="getCardList"
          >取消</a-button
        >
        <a-button
          type="primary"
          :loading="sortLoading"
          @click="$refs['cardList'].saveCardSort()"
        >
          保存排序
        </a-button>
      </div>
    </div>
  </div>
</template>
<script>
import PageHeader from "./pageHeader.vue";
import StickyIndex from "./stickyIndex.vue";
import Plate from "./plate.vue";
import CardList from "../Card/cardList.vue";
import request from "@/utils/requestHttp";
import { pureAxios } from "@/utils/requestHttp";
import moment from "moment";
import sortBy from "lodash/sortBy";
import { adminUserUrlPrefix } from "@/utils/utils";
import { dealThousandData } from "./utils";
import { showAlias } from "@/utils/intl.js";
import { getUrlParam } from "@/utils/utils.js";
import Decimal from "decimal.js";
export default {
  name: "IndexGeneralView",
  components: { PageHeader, StickyIndex, Plate, CardList },
  props: {
    data: Object,
    // 是否设计器里
    isDesign: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  provide() {
    return {
      isDesign: this.isDesign
    };
  },
  computed: {
    // 当前组件属性props对象
    attribute() {
      if (this.data && this.data.props) {
        return this.data.props;
      } else {
        return {};
      }
    },
    // 公司名称
    companyName() {
      return this.attribute.companyName || "冰箱";
    },
    // 操作引导组件地址url
    operationGuideJSUrl() {
      return (
        this.attribute.operationGuideJSUrl ||
        "http://smc.devapps.hisense.com/minio/smcbucket/VideoCom.umd.min.1.0.js"
      );
    },
    // 详情组件地址url
    indexCardDetailInfoJSUrl() {
      return (
        this.attribute.indexCardDetailInfoJSUrl ||
        "http://smc.devapps.hisense.com/minio/smcbucket/IndexCardDetailInfo.umd.min.1.0.js"
      );
    },
    // 专属报告jsUrl
    selfReportsJSUrl() {
      return (
        this.attribute.selfReportsJSUrl ||
        "http://smc.devapps.hisense.com/minio/smcbucket/SelfReports.umd.min.1.0.js"
      );
    },
    // 组合处理完的卡片列表
    newCardList() {
      // 有推荐就把推荐的第n个放到列表里进行展示
      return this.recommendList.length
        ? [this.recommendList[this.activeRecommendIndex], ...this.cardList]
        : this.cardList;
    }
  },
  data() {
    return {
      sortLoading: false,
      baseList: [], // 基地列表
      searchForm: {
        mode: "chart"
      },
      plateList: [], // 该公司下的所有版块列表
      activePlate: "全部", // 当前激活的版块
      cardList: [], // 卡片列表
      topCardList: [], // 顶部置顶卡片
      recommendList: [], // 推荐列表
      cardListLoading: true, // 卡片列表Loading
      activeRecommendIndex: 0, // 激活的推荐卡片下标
      cardListDraged: false, // 列表是否拖拽
      // 表格列展示
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index"
        },
        {
          title: "指标名称",
          dataIndex: "indexName",
          key: "indexName"
        },
        {
          title: "业务版块",
          dataIndex: "plateName",
          key: "plateName"
        },
        {
          title: "公司",
          key: "companyName",
          dataIndex: "companyName"
        },
        {
          title: "基地",
          dataIndex: "baseName",
          key: "baseName"
        },
        {
          title: "实际值",
          dataIndex: "baseActual",
          key: "baseActual"
        },
        {
          title: "实际分子",
          dataIndex: "actualMolecule",
          key: "actualMolecule"
        },
        {
          title: "实际分母",
          dataIndex: "actualDenominator",
          key: "actualDenominator"
        },
        {
          title: "单位",
          dataIndex: "unit",
          key: "unit"
        },
        {
          title: "目标值",
          dataIndex: "targetValue",
          key: "targetValue"
        },
        {
          title: "完成率",
          dataIndex: "completionRate",
          key: "completionRate"
        },
        {
          title: "同期值",
          dataIndex: "contemValue",
          key: "contemValue"
        },
        {
          title: "同比",
          dataIndex: "contemRate",
          key: "contemRate"
        },
        {
          title: "上期值",
          dataIndex: "previousValue",
          key: "previousValue"
        },
        {
          title: "环比",
          dataIndex: "previousRate",
          key: "previousRate"
        },
        {
          title: "频次",
          dataIndex: "frequency",
          key: "frequency"
        },
        {
          title: "指标时间",
          dataIndex: "indexDt",
          key: "indexDt"
        },
        {
          title: "指标类型",
          dataIndex: "indexType",
          key: "indexType"
        }
      ],
      tableData: [], // 表格数据
      searchIndexName: "", // 搜索的指标名称
      SYS_NAME: window.system // 系统名称
    };
  },
  created() {
    /**
     * 概览页面逻辑
     * index.vue请求版块和基地
     * 先获取版块和基地，基地获取成功后，传入到PageHeader组件(A)中，
     * A组件中监听baseList后修改searchForm通过$emit("pageHeaderChange")反馈给当前index.vue组件
     * 的searchConditionChange方法来进行卡片列表的请求
     */
    this.getPlateList();
    this.getBase();
    // 进入日志
    this.enterLog();
    // 设置表格列展示
    if (this.companyName === "集团") {
      this.columns.forEach(item => {
        if (item.key === "companyName") {
          item.title = "集团";
        } else if (item.key === "baseName") {
          item.title = "公司";
        }
      });
    }
    this.$nextTick(() => {
      this.getAndSetWindowHeight();
    });
    window.addEventListener("resize", this.getAndSetWindowHeight);
  },
  mounted() {
    // 信数融合云图，如果连接上有indexStatus则执行相应查询
    if (getUrlParam("indexStatus") && getUrlParam("frequency")) {
      this.$refs["plate"].setStatus(getUrlParam("indexStatus"));
      const frequency =
        getUrlParam("frequency") === "月"
          ? "month"
          : getUrlParam("frequency") === "周"
          ? "week"
          : "day";
      this.$refs["pageHeader"].timeTypeChange(frequency, () => {
        this.$refs["pageHeader"].setStatus(getUrlParam("indexStatus"));
      });
    }
  },
  destroyed() {
    window.removeEventListener("resize");
  },
  methods: {
    getAndSetWindowHeight() {
      let height = "";
      if (window.self === window.top) {
        // 在盘古内部使用
        height = document.getElementsByClassName("100heightDiv")[0]
          ?.offsetHeight;
        height = height ? `${height}px` : "100vh";
      } else {
        // 在信数内使用
        height = window.innerHeight + "px";
      }
      this.$refs[`${this.companyName}indexGeneralViewPage`].style.setProperty(
        "--realHeight",
        height
      );
    },
    getPlateList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/businessSegments/getAllBusinessSegments`
      ).then(res => {
        if (typeof res === "object" && Object.keys(res).length > 0) {
          this.plateList = res[this.companyName];
        }
      });
    },
    // 进入页面日志
    enterLog() {
      request(`${adminUserUrlPrefix["lxp"]}/sysOperLog/saveLog`, {
        method: "POST",
        body: {
          menu: `${this.companyName}核心KPI概览${
            this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""
          }`
        }
      });
    },
    // 根据公司获取基地
    getBase() {
      return new Promise(resolve => {
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getBaseBycompanyName?companyName=${this.companyName}`
        ).then(res => {
          if (Array.isArray(res) && res.length) {
            this.baseList = res;
          }
          resolve(res);
        });
      });
    },
    // 根据公司获取置顶卡片列表
    getTopCardList() {
      return new Promise(resolve => {
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getCompanyTopInex`,
          {
            method: "POST",
            body: {
              company: this.companyName,
              base: this.searchForm.base,
              frequency:
                this.searchForm.timeType === "day"
                  ? "日"
                  : this.searchForm.timeType === "week"
                  ? "周"
                  : "月",
              indexDt: this.searchForm.time
            }
          }
        ).then(res => {
          if (Array.isArray(res) && res.length) {
            res.forEach(item => {
              if (item.dwppCmTfIndexLibrary === null) {
                item.dwppCmTfIndexLibrary = {};
              } else {
                // 处理实际值和目标值
                item["realBaseActual"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.baseActual,
                  item.dwppCmTfIndexLibrary?.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
              }
            });
            this.topCardList = sortBy(res, function(item) {
              return item.indexSort;
            });
          }
          resolve(res);
        });
      });
    },
    // 切换推荐
    changeRecommend() {
      if (this.activeRecommendIndex + 1 < this.recommendList.length) {
        this.activeRecommendIndex++;
      } else {
        this.activeRecommendIndex = 0;
      }
    },
    // 推荐卡片添加到自己的卡片列表中
    addToCardList() {
      // 根据版块过滤后的数组排序
      let currRow = this.recommendList.splice(this.activeRecommendIndex, 1)[0];
      currRow.recommend = false;
      this.changeRecommend();
      this.cardList.push(currRow);
      request(
        `${adminUserUrlPrefix["zcx"]}/SysAnalyseInfo/getAddRecommendIndex`,
        {
          method: "POST",
          body: {
            indexName: currRow.indexName,
            menu_name: `${this.companyName}核心KPI概览${
              this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""
            }`,
            company: this.companyName,
            base: currRow.dwppCmTfIndexLibrary.base,
            indexId: `${currRow.indexId}==${currRow.dwppCmTfIndexLibrary.base}`
          }
        }
      ).then(() => {
        this.$nextTick(() => {
          this.$refs["cardList"].refreshData();
        });
      });
    },
    // 获取卡片列表
    getCardList() {
      this.cardListDraged = false;
      return new Promise(resolve => {
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getDwppCmTfIndexLibraryByUserId`,
          {
            method: "POST",
            body: {
              company: this.companyName,
              base: this.searchForm.base,
              frequency:
                this.searchForm.timeType === "day"
                  ? "日"
                  : this.searchForm.timeType === "week"
                  ? "周"
                  : "月",
              indexDt: this.searchForm.time,
              type: "0",
              status: this.searchForm.indexStatus
            }
          }
        )
          .then(res => {
            if (res && Array.isArray(res)) {
              res.forEach(item => {
                item["recommend"] = false;
                if (item.dwppCmTfIndexLibrary === null) {
                  item.dwppCmTfIndexLibrary = {};
                }
                item["baseName"] = this.searchForm.base;
                // 处理实际值和目标值
                item["realBaseActual"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.baseActual,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
                item["realTargetValue"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.targetValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
                if (this.companyName === "集团") {
                  item["companyName"] = "集团";
                }
                item["dataFrequency"] =
                  this.searchForm.timeType === "day"
                    ? "日"
                    : this.searchForm.timeType === "week"
                    ? "周"
                    : "月";
                item["indexDt"] = this.searchForm.time;
              });
              // 下边处理数据是为了把没数据的卡片放到最后展示
              const hasAPKDataArr = res.filter(item => {
                return Object.keys(item.dwppCmTfIndexLibrary).length > 0;
              });
              const noAPKDataArr = res.filter(item => {
                return Object.keys(item.dwppCmTfIndexLibrary).length === 0;
              });
              this.cardList = sortBy(hasAPKDataArr, function(item) {
                return item.indexSort;
              });
              this.cardList = [...this.cardList, ...noAPKDataArr];

              // 单独为冰箱公司做处理
              if (this.companyName === "冰箱") {
                // 查询条件月类型且当前月的1-19号以及当前月的往后月份，以下指标名称添加“预测”两字
                if (this.searchForm.timeType === "month") {
                  const nowYear = new Date().getFullYear();
                  const nowMonth = new Date().getMonth() + 1;
                  const nowDay = new Date().getDate();
                  const searchYear = parseInt(
                    this.searchForm.time.split("-")[0]
                  );
                  const searchMonth = parseInt(
                    this.searchForm.time.split("-")[1]
                  );
                  if (
                    (`${nowYear}-${(nowMonth + "").padStart(2, 0)}` ===
                      this.searchForm.time &&
                      nowDay < 20) ||
                    (nowYear === searchYear && searchMonth > nowMonth) ||
                    nowYear < searchYear
                  ) {
                    this.cardList.forEach(item => {
                      if (
                        item.indexName.includes("90天") ||
                        item.indexName.includes("AFR")
                      ) {
                        item["showYC"] = true;
                      }
                    });
                  }
                }
              }
            } else {
              this.cardList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.cardList = [];
            // this.topCardList = [];
            resolve([]);
          });
      });
    },
    // 获取推荐列表
    getRecommendList() {
      return new Promise(resolve => {
        this.activeRecommendIndex = 0;
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getRecommendIndexList`,
          {
            method: "POST",
            body: {
              company: this.companyName,
              base: this.searchForm.base,
              frequency:
                this.searchForm.timeType === "day"
                  ? "日"
                  : this.searchForm.timeType === "week"
                  ? "周"
                  : "月",
              indexDt: this.searchForm.time,
              type: "0"
            }
          }
        )
          .then(res => {
            if (res && Array.isArray(res)) {
              res.forEach(item => {
                item["recommend"] = true;
                if (item.dwppCmTfIndexLibrary === null) {
                  item.dwppCmTfIndexLibrary = {};
                }
                item["baseName"] = this.searchForm.base;
                item["unit"] = item.dwppCmTfIndexLibrary?.unit;
                // 处理实际值和目标值
                item["realBaseActual"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.baseActual,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
                item["realTargetValue"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.targetValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
                item["dataFrequency"] =
                  this.searchForm.timeType === "day"
                    ? "日"
                    : this.searchForm.timeType === "week"
                    ? "周"
                    : "月";
                item["indexDt"] = this.searchForm.time;
              });
              this.recommendList = sortBy(res, function(item) {
                return item.indexSort;
              });
            } else {
              this.recommendList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.recommendList = [];
            resolve([]);
          });
      });
    },
    // 下载数据
    download() {
      pureAxios({
        url: "/smc/indexCardInfo/exportGLList",
        method: "post",
        data: {
          company: this.companyName,
          base: this.searchForm.base,
          frequency:
            this.searchForm.timeType === "day"
              ? "日"
              : this.searchForm.timeType === "week"
              ? "周"
              : "月",
          indexDt: this.searchForm.time,
          type: "0"
        },
        responseType: "blob"
      })
        .then(response => {
          if (!response.data) {
            return;
          }
          const fileName = `${this.companyName}核心KPI概览数据表.xlsx`;
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message
          });
        });
    },
    // 查询条件更改
    searchConditionChange(data) {
      this.searchForm = { ...this.searchForm, ...data };
      const timeType = this.searchForm.timeType || "day";
      // if (timeType === "week") {
      // let week = getYearWeek(new Date(this.searchForm.time));
      // this.searchForm.time = `${new Date(
      //   this.searchForm.time
      // ).getFullYear()}-${week < 10 ? "0" + week : week}`;
      // }
      if (timeType !== "week") {
        this.searchForm.time = moment(this.searchForm.time).format(
          timeType === "day" ? "YYYY-MM-DD" : "YYYY-MM"
        );
      }
      this.cardListLoading = true;
      this.getRecommendList();
      Promise.all([this.getTopCardList(), this.getCardList()]).then(() => {
        this.cardListLoading = false;
        if (this.searchForm.mode === "table") {
          const tableData = [...this.topCardList, ...this.cardList];
          for (let i = 0; i < tableData.length; i++) {
            let element = tableData[i];
            // 把dwppCmTfIndexLibrary中的字段取出来放到第一级
            tableData[i] = element.dwppCmTfIndexLibrary
              ? { ...element, ...element.dwppCmTfIndexLibrary }
              : element;
            // 补充后台没返回的字段
            tableData[i]["indexDt"] = this.searchForm.time;
            tableData[i]["frequency"] =
              this.searchForm.timeType === "day"
                ? "日"
                : this.searchForm.timeType === "week"
                ? "周"
                : "月";
            tableData[i]["baseName"] = this.searchForm.base;
            tableData[i]["index"] = i + 1;
            const deal100ParamsArr = [
              "completionRate",
              "contemRate",
              "previousRate"
            ];
            deal100ParamsArr.forEach(item => {
              const dataItem = tableData[i];
              tableData[i][item] = dataItem[item]
                ? Decimal(dataItem[item])
                    .mul(Decimal(100))
                    .toFixed(2, Decimal.ROUND_HALF_UP) + "%"
                : "";
            });
          }
          this.tableData = tableData;
        } else {
          this.tableData = [];
        }
      });
    },
    // 版块改变
    plateChange(data) {
      this.activePlate = data.plate;
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage {
  font-size: 14px;
  font-family: PingFangSC-Regular;
  color: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  min-width: 1280px;
  display: flex;
  flex-direction: column;
  height: var(--realHeight);
  ._flex {
    display: flex;
    align-items: center;
  }
  & > ._top {
    box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
    box-sizing: border-box;
    padding: 24px 24px 0 24px;
    position: relative;
    overflow: hidden;
    &::before {
      display: block;
      content: "";
      height: 0;
      width: 100%;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
    }
  }
  & > ._bottom {
    background-color: #fff;
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;
    flex: 1;
    .saveCardSort {
      position: absolute;
      bottom: 0;
      left: 0;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
      background-color: #fff;
      box-sizing: border-box;
      padding: 16px 24px;
      width: 100%;
      min-width: 1400px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
</style>
