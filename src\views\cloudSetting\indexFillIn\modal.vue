<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2022-07-29 17:39:56
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="mainForm"
      :model="mainForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="公司" prop="company">
            <a-select
              style="width: 100%"
              :disabled="isEdit ? true : false"
              v-model="mainForm.company"
              placeholder="请选择公司"
              @change="
                () => {
                  this.getBaseList(this.mainForm.company);
                  this.mainForm.base = '';
                  this.mainForm.businessSegments = '';
                }
              "
            >
              <a-select-option
                :value="item.key"
                v-for="item in companyList"
                :key="item.key"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="日/周目标逻辑" prop="dmTargetFormula">
            <a-select
              v-model="mainForm.dmTargetFormula"
              placeholder="请选择计算公式"
            >
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-dmTargetFormula'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="基地" prop="base">
            <a-select v-model="mainForm.base" placeholder="请选择基地">
              <a-select-option
                :value="item"
                v-for="item in baseList"
                :key="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否手工填报" prop="isManually">
            <a-select
              v-model="mainForm.isManually"
              placeholder="请选择是否手工填报"
            >
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-Y/N'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="业务模块" prop="businessSegments">
            <a-select
              v-model="mainForm.businessSegments"
              :disabled="isEdit ? true : false"
              placeholder="请选择业务版块"
            >
              <a-select-option
                :value="item.businessSegments"
                v-for="item in plateList[mainForm.company] || []"
                :key="item.businessSegments"
              >
                {{ item.businessSegments }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="是否冻结" prop="isDelete">
            <a-select v-model="mainForm.isDelete" placeholder="请选择是否冻结">
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-Y/N'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="指标名称" prop="indexName">
            <a-input
              :disabled="isEdit ? true : false"
              v-model="mainForm.indexName"
              placeholder="请填写指标名称"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="精度（小数点位数）" prop="precisions">
            <a-input-number
              v-model="mainForm.precisions"
              :min="0"
              :max="5"
              :step="1"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="责任人(LDAP账号)" prop="fillInPerson">
            <a-input
              v-model="mainForm.fillInPerson"
              placeholder="请填写责任人(LDAP账号)"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="指标单位" prop="indexUnit">
            <a-select v-model="mainForm.indexUnit" placeholder="请选择指标单位">
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-indexUnit'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item label="数据资产管理平台ID" prop="dmId">
            <a-input
              v-model="mainForm.dmId"
              placeholder="请填写数据资产管理平台ID"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="指标正反向" prop="indexType">
            <a-select
              v-model="mainForm.indexType"
              placeholder="请选择指标正反向"
            >
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-indexType'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-table
      :pagination="false"
      :columns="columns"
      :data-source="otherForm"
      :scroll="{ x: 1000 }"
    >
      <template
        v-for="(zitem, zindex) in columns"
        :slot="zitem.key"
        slot-scope="text, record"
      >
        <a-select
          :defaultValue="record[zitem.key]"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          :key="zindex"
          v-if="!['formula', 'targetFormula'].includes(zitem.key)"
          @change="tableColumnChange(zitem.key, $event)"
        >
          <a-select-option
            :value="item.key"
            v-for="item in dict['smc-Y/N'] || []"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
        <a-select
          :defaultValue="record[zitem.key]"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          :key="zindex"
          v-if="zitem.key === 'formula'"
          @change="tableColumnChange(zitem.key, $event)"
        >
          <a-select-option
            :value="item.key"
            v-for="item in dict['smc-indexFormula'] || []"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
        <a-select
          :defaultValue="record[zitem.key]"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          :key="zindex"
          v-if="zitem.key === 'targetFormula'"
          @change="tableColumnChange(zitem.key, $event)"
        >
          <a-select-option
            :value="item.key"
            v-for="item in dict['smc-indexTargetFormula'] || []"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </template>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 8 },
      wrapperCol: { span: 10 },
      mainForm: {
        businessSegments: "",
        indexName: "",
        dmId: "",
        company: "",
        base: "",
        indexUnit: "",
        indexType: "正向",
        dmTargetFormula: "",
        fillInPerson: "",
        isManually: "",
        isDelete: "N",
        precisions: 2
      },
      otherForm: [
        {
          dayFrequency: "",
          weekFrequency: "",
          monthFrequency: "",
          isCompanyCompare: "",
          isBaseCompare: "",
          isDayContemRate: "",
          isDayPreviousRate: "",
          isWeekContemRate: "",
          isWeekPreviousRate: "",
          isMonthContemRate: "",
          isMonthPreviousRate: "",
          isBaseSum: "",
          isCalculateSum: "",
          formula: "",
          targetFormula: "",
          isFactoryweek: ""
        }
      ],
      rules: {
        businessSegments: [
          {
            required: true,
            message: "请选择业务版块",
            trigger: "change"
          }
        ],
        indexName: [
          {
            required: true,
            message: "请填写指标名称",
            trigger: "blur"
          }
        ],
        company: [
          {
            required: true,
            message: "请选择公司",
            trigger: "change"
          }
        ],
        base: [
          {
            required: true,
            message: "请选择基地",
            trigger: "change"
          }
        ],
        indexUnit: [
          {
            required: true,
            message: "请选择指标单位",
            trigger: "change"
          }
        ],
        indexType: [
          {
            required: true,
            message: "请选择指标正反向",
            trigger: "change"
          }
        ],
        dmTargetFormula: [
          {
            required: true,
            message: "请选择计算公式",
            trigger: "change"
          }
        ],
        isManually: [
          {
            required: true,
            message: "请选择是否手工填报",
            trigger: "change"
          }
        ],
        isDelete: [
          {
            required: true,
            message: "请选择是否冻结",
            trigger: "change"
          }
        ],
        precisions: [
          {
            required: true,
            message: "请输入小数点精度",
            trigger: "blur"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      dict: {}, // 字典
      companyList: [],
      baseList: [],
      plateList: {},
      columns: [
        {
          title: "是否存在日度指标",
          dataIndex: "dayFrequency",
          key: "dayFrequency",
          scopedSlots: { customRender: "dayFrequency" },
          width: 120
        },
        {
          title: "是否存在周度指标",
          dataIndex: "weekFrequency",
          key: "weekFrequency",
          scopedSlots: { customRender: "weekFrequency" },
          width: 120
        },
        {
          title: "是否存在月度指标",
          key: "monthFrequency",
          dataIndex: "monthFrequency",
          scopedSlots: { customRender: "monthFrequency" },
          width: 120
        },
        {
          title: "是否公司间可对比",
          key: "isCompanyCompare",
          dataIndex: "isCompanyCompare",
          scopedSlots: { customRender: "isCompanyCompare" },
          width: 120
        },
        {
          title: "是否基地间可对比",
          key: "isBaseCompare",
          dataIndex: "isBaseCompare",
          scopedSlots: { customRender: "isBaseCompare" },
          width: 120
        },
        {
          title: "是否日度同比",
          key: "isDayContemRate",
          dataIndex: "isDayContemRate",
          scopedSlots: { customRender: "isDayContemRate" },
          width: 120
        },
        {
          title: "是否日度环比",
          key: "isDayPreviousRate",
          dataIndex: "isDayPreviousRate",
          scopedSlots: { customRender: "isDayPreviousRate" },
          width: 120
        },
        {
          title: "是否周度同比",
          key: "isWeekContemRate",
          dataIndex: "isWeekContemRate",
          scopedSlots: { customRender: "isWeekContemRate" },
          width: 120
        },
        {
          title: "是否周度环比",
          key: "isWeekPreviousRate",
          dataIndex: "isWeekPreviousRate",
          scopedSlots: { customRender: "isWeekPreviousRate" },
          width: 120
        },
        {
          title: "是否月度同比",
          key: "isMonthContemRate",
          dataIndex: "isMonthContemRate",
          scopedSlots: { customRender: "isMonthContemRate" },
          width: 120
        },
        {
          title: "是否月度环比",
          key: "isMonthPreviousRate",
          dataIndex: "isMonthPreviousRate",
          scopedSlots: { customRender: "isMonthPreviousRate" },
          width: 120
        },
        {
          title: "基地是否可累加",
          key: "isBaseSum",
          dataIndex: "isBaseSum",
          scopedSlots: { customRender: "isBaseSum" },
          width: 120
        },
        {
          title: "是否计算整体值",
          key: "isCalculateSum",
          dataIndex: "isCalculateSum",
          scopedSlots: { customRender: "isCalculateSum" },
          width: 120
        },
        {
          title: "计算公式",
          key: "formula",
          dataIndex: "formula",
          scopedSlots: { customRender: "formula" },
          width: 190
        },
        {
          title: "目标完成率计算公式",
          key: "targetFormula",
          dataIndex: "targetFormula",
          scopedSlots: { customRender: "targetFormula" },
          width: 190
        },
        {
          title: "是否使用工厂周",
          key: "isFactoryweek",
          dataIndex: "isFactoryweek",
          scopedSlots: { customRender: "isFactoryweek" },
          width: 120
        }
      ] // 表格列
    };
  },
  methods: {
    show(formValue) {
      this.getDICT();
      this.getCompanyList();
      this.getBaseList();
      this.getPlateList();
      this.visible = true;
      this.isEdit = false;
      if (formValue) {
        const {
          id,
          businessSegments,
          indexName,
          dmId,
          company,
          base,
          indexUnit,
          indexType,
          dayFrequency,
          weekFrequency,
          monthFrequency,
          isCompanyCompare,
          isBaseCompare,
          isDayContemRate,
          isDayPreviousRate,
          isWeekContemRate,
          isWeekPreviousRate,
          isMonthContemRate,
          isMonthPreviousRate,
          isBaseSum,
          isCalculateSum,
          formula,
          targetFormula,
          isFactoryweek,
          dmTargetFormula,
          fillInPerson,
          isManually,
          isDelete,
          precisions
        } = formValue;
        this.isEdit = true;
        this.mainForm = {
          id,
          businessSegments,
          indexName,
          dmId,
          company,
          base,
          indexUnit,
          indexType,
          dmTargetFormula,
          fillInPerson,
          isManually,
          isDelete,
          precisions
        };
        this.otherForm = [
          {
            dayFrequency,
            weekFrequency,
            monthFrequency,
            isCompanyCompare,
            isBaseCompare,
            isDayContemRate,
            isDayPreviousRate,
            isWeekContemRate,
            isWeekPreviousRate,
            isMonthContemRate,
            isMonthPreviousRate,
            isBaseSum,
            isCalculateSum,
            formula,
            targetFormula,
            isFactoryweek
          }
        ];
      }
    },
    // 获取所有公司版块列表
    getPlateList() {
      request(`/api/smc/businessSegments/getAllBusinessSegments`).then(res => {
        if (typeof res === "object" && Object.keys(res).length > 0) {
          this.plateList = res;
        }
      });
    },
    // 获取公司列表
    getCompanyList() {
      request(
        `/api/smc/indexCardInfo/getBaseBycompanyNameKV?companyName=集团`
      ).then(res => {
        this.companyList = res;
      });
    },
    // 获取公司和基地
    getBaseList(type) {
      request(
        `/api/smc/indexCardInfo/getBaseBycompanyName?companyName=${type}`
      ).then(res => {
        this.baseList = res;
      });
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-indexBusiness%2Csmc-indexUnit%2Csmc-indexType%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-Y%2FN%2Csmc-indexFormula%2Csmc-indexTargetFormula%2Csmc-dmTargetFormula%2Csmc-Y%2FN&languageCode=zh_CN"
        )
      ).then(res => {
        this.dict = res;
      });
    },
    close() {
      this.mainForm = {
        businessSegments: "",
        indexName: "",
        dmId: "",
        company: "",
        base: "",
        indexUnit: "",
        indexType: "正向",
        dmTargetFormula: "",
        fillInPerson: "",
        isManually: "",
        isDelete: "N",
        precisions: 2
      };
      this.otherForm = [
        {
          dayFrequency: "",
          weekFrequency: "",
          monthFrequency: "",
          isCompanyCompare: "",
          isBaseCompare: "",
          isDayContemRate: "",
          isDayPreviousRate: "",
          isWeekContemRate: "",
          isWeekPreviousRate: "",
          isMonthContemRate: "",
          isMonthPreviousRate: "",
          isBaseSum: "",
          isCalculateSum: "",
          formula: "",
          targetFormula: "",
          isFactoryweek: ""
        }
      ];
      this.companyList = [];
      this.baseList = [];
      this.$refs.mainForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    tableColumnChange(key, value) {
      this.otherForm[0][key] = value;
    },
    // 提交
    submit() {
      this.$refs.mainForm.validate(async valid => {
        const form = { ...this.mainForm, ...this.otherForm[0] };
        const otherFormDetail = this.otherForm[0];
        let hasError = false;
        for (const key in otherFormDetail) {
          if (Object.hasOwnProperty.call(otherFormDetail, key)) {
            const element = otherFormDetail[key];
            if (element === "") {
              hasError = true;
              const title = this.columns.filter(
                item => item.dataIndex === key
              )[0].title;
              window.vm.$message.error(`请选择${title}`);
              break;
            }
          }
        }
        if (hasError) return;
        if (valid) {
          request(`/api/smc/attributes/${this.isEdit ? "update" : "save"}`, {
            method: this.isEdit ? "PUT" : "POST",
            body: form
          }).then(() => {
            this.close();
            this.$emit("fetchData");
          });
        }
      });
    }
  }
};
</script>
