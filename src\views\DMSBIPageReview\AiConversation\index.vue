<template>
  <div class="ai-conversation" :data-theme="darkTheme ? 'dark' : 'light'">
    <!-- 复制提示 -->
    <div class="copy-toast" v-if="showCopyToast" :class="{ success: copySuccess }">
      <span>{{ copySuccess ? "复制成功" : "复制失败" }}</span>
    </div>

    <!-- 消息容器 -->
    <div class="message-container">
      <!-- 消息列表 -->
      <div v-for="(message, index) in aiConversationMessages" :key="index"
        :class="[message.type === 'ai' ? 'ai-message' : 'user-message']">
        <div class="avatar" v-if="message.type === 'ai'">
          <img :src="require('@/assets/images/workShop_standing_meeting/ai_avatar.png')
            " alt="AI Avatar" />
        </div>
        <div class="message-content">
          <div class="message-body">
            <template v-if="message.isMarkdown">
              <div class="markdown-content" v-html="renderMarkdown(message.content)"></div>
            </template>
            <template v-else>
              {{ message.content }}
            </template>
          </div>
          <div class="message-footer" :class="{
            'ai-footer': message.type === 'ai',
            'user-footer': message.type === 'user',
          }">
            <div class="speech-controls" v-if="message.type === 'ai'">
              <img v-if="!isMessagePlaying(message)" src="@/assets/images/workShop_standing_meeting/readText.png"
                alt="播放文本" class="footer-icon" @click="handlePlayText(message)" />
              <template v-else>
                <img v-if="isSpeechPaused" src="@/assets/images/workShop_standing_meeting/play.png" alt="继续播放"
                  class="footer-icon" @click="handleResumeSpeech" />
                <img v-else src="@/assets/images/workShop_standing_meeting/pause.png" alt="暂停播放" class="footer-icon"
                  @click="handlePauseSpeech" />
                <img src="@/assets/images/workShop_standing_meeting/stop.png" alt="停止播放" class="footer-icon"
                  @click="handleStopSpeech" />
              </template>
            </div>
            <img src="@/assets/images/workShop_standing_meeting/copy_icon.png" alt="复制文本" class="footer-icon2"
              @click="handleToMsgTool(message, 'copyText')" />
            <img v-if="message.type === 'ai'" src="@/assets/images/workShop_standing_meeting/good_answer.png"
              alt="较好的回答" class="footer-icon2" @click="handleToMsgTool(message, 'goodAnswer')" />
            <img v-if="message.type === 'ai'" src="@/assets/images/workShop_standing_meeting/bed_answer.png" alt="较差的回答"
              class="footer-icon2" @click="handleToMsgTool(message, 'badAnswer')" />
            <a-button v-if="message.type === 'ai'" class="adopt-btn"
              @click="handlePushToSummarize(message)">采纳</a-button>
          </div>
        </div>
        <div class="avatar" v-if="message.type === 'user'">
          <img :src="userAvatar" alt="User Avatar" />
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="conversation-footer">
      <div class="input-area">
        <a-textarea placeholder="发消息..." v-model="inputMessage" :auto-size="false" @pressEnter="handleEnterPress">
        </a-textarea>
        <div class="send-button" @click="sendMessage">
          <img :src="require('@/assets/images/workShop_standing_meeting/ai_conversation_sendBtnIcon.png')
            " alt="发送" />
        </div>
      </div>
      <div class="button-group">
        <a-button class="summary-btn" @click="handleSummaryClick('daily')">全天总结</a-button>
        <a-button class="summary-btn" @click="handleSummaryClick('periodOfTime')">时段总结</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  speak,
  handleAiCopy,
  pauseSpeech,
  resumeSpeech,
  stopSpeech,
  renderMarkdown,
  requestSSE
} from "./utils.js";
import moment from "moment";

export default {
  name: "AiConversation",
  props: {
    darkTheme: {
      type: Boolean,
      default: false,
    },
    userAvatar: {
      type: String,
      default: require("@/assets/images/workShop_standing_meeting/user_avatar.png"),
    },
    searchForm: {
      type: Object,
    },
    date: {
      type: String,
      default: moment().format('YYYY-MM-DD'),
    },
  },
  beforeDestroy() {
    // 组件销毁前取消任何正在进行的SSE请求
    if (this.currentSSEController) {
      this.currentSSEController.abort();
      this.currentSSEController = null;
    }
    // 停止任何正在播放的语音
    stopSpeech();
  },
  data() {
    return {
      inputMessage: "",
      activeButton: "",
      currentPlayingMessage: null,
      isSpeechPaused: false,
      showCopyToast: false,
      copySuccess: false,
      currentSSEController: null, // 用于控制SSE请求
      aiConversationMessages: [
      ],
    };
  },
  methods: {
    // 渲染Markdown内容
    renderMarkdown(content) {
      return renderMarkdown(content);
    },
    // 发送消息
    sendMessage() {
      if (!this.inputMessage.trim()) return;

      const userMessage = this.inputMessage;

      // 添加用户消息
      this.aiConversationMessages.push({
        type: "user",
        content: userMessage,
        isMarkdown: false,
      });

      // 清空输入框
      this.inputMessage = "";

      // 添加一个临时的AI消息，表示正在思考
      const tempMessageIndex = this.aiConversationMessages.length;
      this.aiConversationMessages.push({
        type: "ai",
        content: "思考中...",
        isMarkdown: false,
        isLoading: true,
      });

      // 使用SSE工具函数请求AI回复
      this.currentSSEController = requestSSE({
        url: '/api/mom-ai/ai/agent/run?clientKey=2ca95df979034c669d34b7ea8f37db72&appId=79f6161a58ca00a981559f5c9918d2da',
        method: 'POST',
        body: {
          // this.$store.state.user.info.loginName ||
          "user_id": this.$store.state.user.info.loginName || "yueshengqi.ex",
          // "conversation_id": ctx.state.conversation_id,
          "inputs": {
            //  this.$store.state.user.info.loginName ||
            userId: this.$store?.state.user.info.loginName || "yueshengqi.ex",
            companyCode: this.searchForm.baseCode,
            companyName: this.searchForm.baseName,
            plantName: this.searchForm.factoryName,
            plantCode: this.searchForm.factoryCode,
            subFactoryName: this.searchForm.branchName,
            subFactoryCode: this.searchForm.branchCode,
            workshopCode: this.searchForm.workshopCode,
            workshopName: this.searchForm.workshopName,
            teamCode: this.searchForm.teamCode,
            teamName: this.searchForm.teamName,
            lineName: this.searchForm.lineName,
            lineCode: this.searchForm.lineCode,
            targetTime: this.date,
            token: sessionStorage.getItem("Access-Token") || "53fe6c4f-5d00-491b-a48a-6366eeebb04a"
          },
          "query": userMessage,
          "response_mode": "streaming",
          "files": []
        },
        onOpen: () => {
          // SSE连接已打开
        },
        onMessage: (data) => {
          // 替换临时消息
          if (this.aiConversationMessages[tempMessageIndex].isLoading) {
            this.aiConversationMessages.splice(tempMessageIndex, 1, {
              type: "ai",
              content: typeof data === 'string' ? data : (data.content || ''),
              isMarkdown: true,
            });
          } else {
            // 如果是流式响应，可以累加内容
            const currentMessage = this.aiConversationMessages[tempMessageIndex];
            this.aiConversationMessages.splice(tempMessageIndex, 1, {
              ...currentMessage,
              content: currentMessage.content + (typeof data === 'string' ? data : (data.answer || '')),
            });
          }
        },
        onError: (error) => {
          // 替换临时消息为错误消息
          this.aiConversationMessages.splice(tempMessageIndex, 1, {
            type: "ai",
            content: "抱歉，请求出错了，请稍后再试。",
            isMarkdown: false,
          });
        },
        onComplete: () => {
          this.currentSSEController = null;
        }
      });
    },
    handleSummaryClick(type) {
      this.activeButton = type;

      // 根据类型发送不同的预设消息
      if (type === 'daily') {
        // 获取当前时间
        const now = moment();
        const currentHour = now.hour();
        const today = now.format('YYYY年MM月DD日');

        // 根据当前时间段生成不同的总结请求
        if (currentHour >= 8 && currentHour < 20) {
          // 8点到20点：生成当天8点到现在的总结
          this.inputMessage = `请帮我生成${today}总结`;
        } else {
          // 20点到次日8点：生成当天20点到现在的总结
          this.inputMessage = `请帮我生成${today}总结`;
        }

        // 直接调用发送消息方法
        this.sendMessage();
      } else if (type === "periodOfTime") {
        this.inputMessage = `请帮我生成时段总结`;
        this.sendMessage();
      }
    },
    // 将Markdown文本转换为纯文本
    markdownToPlainText(markdown) {
      // 先使用renderMarkdown将markdown转为HTML
      const html = renderMarkdown(markdown);

      // 创建一个临时的div元素来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // 获取纯文本内容
      const plainText = tempDiv.textContent || tempDiv.innerText || '';

      // 清理文本，去除多余空格和换行
      return plainText
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, ' ')
        .trim();
    },

    // 播放文本
    handlePlayText(message) {
      this.currentPlayingMessage = message;
      this.isSpeechPaused = false;

      // 根据消息类型选择合适的文本处理方式
      let textToSpeak;
      if (message.isMarkdown) {
        // 如果是Markdown格式，先转换为纯文本
        textToSpeak = this.markdownToPlainText(message.content);
      } else {
        // 普通文本直接使用
        textToSpeak = message.content;
      }

      speak({ text: textToSpeak }, () => {
        this.currentPlayingMessage = null;
        this.isSpeechPaused = false;
      });
    },
    // 暂停播放
    handlePauseSpeech() {
      pauseSpeech();
      this.isSpeechPaused = true;
    },
    // 继续播放
    handleResumeSpeech() {
      resumeSpeech();
      this.isSpeechPaused = false;
    },
    // 停止播放
    handleStopSpeech() {
      stopSpeech();
      this.currentPlayingMessage = null;
      this.isSpeechPaused = false;
    },
    // 判断当前消息是否正在播放
    isMessagePlaying(message) {
      return this.currentPlayingMessage === message;
    },
    // 将ai对话当作板块展示
    handlePushToSummarize(message) {
      this.$emit("pushToSummarize", message);
    },
    // 对话框底部工具
    async handleToMsgTool(message, type) {
      switch (type) {
        case "copyText": {
          const success = await handleAiCopy(message.content);
          this.showCopyToast = true;
          this.copySuccess = success;
          setTimeout(() => {
            this.showCopyToast = false;
          }, 2000);
          return;
        }
        case "goodAnswer":
          return;
        case "badAnswer":
          return;
      }
    },
    // 处理Enter键按下事件
    handleEnterPress(e) {
      // 如果按下的是Enter键且没有同时按下Shift键，则发送消息
      if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault(); // 阻止默认行为（换行）
        this.sendMessage(); // 调用发送消息方法
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ai-conversation {
  width: 400px;
  background-color: var(--bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
  min-height: 0;
  overflow: hidden;

  .message-container {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
  }

  .ai-message,
  .user-message {
    display: flex;
    margin-bottom: 24px;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
  }

  .ai-message {
    justify-content: flex-start;
  }

  .user-message {
    justify-content: flex-end;
  }

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    margin: 0 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-content {
    max-width: 70%;
    min-width: 100px;
    word-break: break-all;
    overflow-wrap: break-word;

    .message-body {
      font-size: 14px;
      line-height: 1.6;
      color: var(--text-color);
      background: var(--message-bg);
      padding: 12px;
      border-radius: 6px;
      word-wrap: break-word;
      display: inline-block;
      border: var(--message-border);

      ::v-deep .markdown-content {
        p {
          margin: 0 0 10px;
          white-space: pre-line;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 0;
          margin-bottom: 10px;
          font-weight: 600;
        }

        ul,
        ol {
          padding-left: 20px;
          margin-bottom: 10px;
        }

        pre {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto;
        }

        code {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 2px 4px;
          border-radius: 3px;
          font-family: monospace;
        }

        table {
          border-collapse: collapse;
          width: 100%;
          margin-bottom: 10px;

          th,
          td {
            border: 1px solid var(--text-color);
            padding: 8px;
            text-align: left;
          }

          th {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }

        blockquote {
          border-left: 4px solid var(--text-color);
          padding-left: 10px;
          margin-left: 0;
          margin-right: 0;
          opacity: 0.8;
        }

        img {
          max-width: 100%;
          height: auto;
        }

        a {
          color: #00aaa6;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .message-footer {
      margin-top: 4px;
      display: flex;
      align-items: center;

      &.ai-footer {
        justify-content: flex-start;
      }

      &.user-footer {
        justify-content: flex-end;
      }

      .footer-icon {
        width: 16px;
        height: 16px;
        opacity: 0.6;
        margin: 0 6px;
        cursor: pointer;
      }

      .footer-icon2 {
        width: 12px;
        height: 13px;
        opacity: 0.6;
        margin: 0 6px;
        cursor: pointer;
      }

      .adopt-btn {
        height: 28px;
        padding: 4px 12px;
        border-radius: 107px;
        background: #00aaa6;
        font-size: 14px;
        color: #ffffff;
        margin-left: auto;
      }
    }
  }

  .user-message .message-body {
    background: var(--user-message-bg);
    border: var(--message-border);
  }

  .conversation-footer {
    padding: 16px;
    flex-shrink: 0;

    .input-area {
      margin-bottom: 12px;
      position: relative;

      /deep/ .ant-input {
        width: 368px;
        height: 89px !important;
        border-radius: 16px;
        background: var(--input-bg);
        padding: 16px;
        padding-right: 48px;
        border: none;
        font-size: 14px;
        outline: none;
        resize: none;
        color: var(--text-color);

        &:focus {
          border-color: #00aaa6;
          box-shadow: none;
        }

        &::placeholder {
          color: var(--placeholder-color);
        }
      }

      .send-button {
        position: absolute;
        right: 16px;
        bottom: 16px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #00aaa6;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: #008784;
        }

        img {
          width: 20px;
          height: 20px;
        }
      }
    }

    .button-group {
      display: flex;
      gap: 12px;

      .summary-btn {
        //width: 88px;
        height: 32px;
        padding: 5px 16px;
        border-radius: 374px;
        background: var(--button-bg);
        color: var(--text-color);
        border: none;
        font-size: 14px;

        &:hover {
          background: var(--button-hover-bg);
          color: #00aaa6;
        }
      }
    }
  }
}

.ai-conversation {
  --bg-color: #fff;
  --text-color: #1d2129;
  --message-bg: #f2f3f5;
  --user-message-bg: #e8f3ff;
  --message-border: none;
  --input-bg: #f2f3f5;
  --placeholder-color: #86909c;
  --button-bg: #f2f3f5;
  --button-hover-bg: rgba(0, 170, 166, 0.15);

  &[data-theme="dark"] {
    --bg-color: #1d2129;
    --text-color: #fff;
    --message-bg: #313540;
    --user-message-bg: #164b7e;
    --message-border: 1px solid rgba(255, 255, 255, 0.15);
    --input-bg: #313540;
    --placeholder-color: #86909c;
    --button-bg: #313540;
    --button-hover-bg: rgba(0, 170, 166, 0.25);
  }
}

.speech-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10px 20px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  z-index: 1000;
  animation: fadeInOut 3s ease-in-out;

  &.success {
    background: rgba(0, 170, 166, 0.9);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }

  10% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  90% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

.structured-content {

  .production-section,
  .quality-section,
  .abnormal-section {
    margin-bottom: 12px;

    h4 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--text-color);
    }

    p {
      margin: 0;
      white-space: pre-line;
    }
  }
}
</style>
