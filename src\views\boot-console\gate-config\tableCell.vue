<template>
  <div>
    <template v-if="record.remark === 'freeze'">
      <a-tag color="#f50">
        是
      </a-tag>
    </template>
    <template v-if="record.remark !== 'freeze'">
      <a-tag color="#87d068">
        否
      </a-tag>
    </template>
  </div>
</template>
<script>
export default {
  props: {
    record: Object, // 当前操作行
    text: String,
    name: String // 当前字段名
  },
  data() {
    return {};
  }
};
</script>
