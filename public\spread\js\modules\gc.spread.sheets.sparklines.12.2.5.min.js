/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Sparklines=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/sparkline/sparkline-wrapper.entry.js")}({"./dist/plugins/sparkline/functions-sparkline.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("CalcEngine"),e=c("Core"),f=c("Common"),g=c("./dist/plugins/sparkline/sparkline-wrapper.js"),h=d.Convert.Fh,i=d.Functions&&d.Functions.Function,j=d.Functions&&d.Functions.bi,k=f.Common.j.Fa,l=e.GC$,m=l.inherit,n=l.extend,o=f.Sparklines.SparklineRender,p=f.Sparklines.SparklineExRenders,q=null,r=isNaN,s=parseFloat,t=NaN,u="PIESPARKLINE",v="AREASPARKLINE",w="SCATTERSPARKLINE",x="BULLETSPARKLINE",y="SPREADSPARKLINE",z="STACKEDSPARKLINE",A="HBARSPARKLINE",B="VBARSPARKLINE",C="VARISPARKLINE",D="BOXPLOTSPARKLINE",E="CASCADESPARKLINE",F="PARETOSPARKLINE",G="MONTHSPARKLINE",H="YEARSPARKLINE",I="LINESPARKLINE",J="COLUMNSPARKLINE",K="WINLOSSSPARKLINE";function ea(a){return 0===a}function fa(a){return 0===a||2===a}function ga(a){var b,c,d,e,f,g,i=[];if(h(a))for(b=0,c=a.getRangeCount();b<c;b++)for(d=a.getRowCount(b),e=a.getColumnCount(b),f=0;f<d;f++)for(g=0;g<e;g++)i.push(a.getValue(b,f,g));return i}function ha(a,b){var c,d,e,f,g,i=[];if(h(a)&&(c=a.getRangeCount(),c>b))for(d=a.getRowCount(b),e=a.getColumnCount(b),f=0;f<d;f++)for(i[f]=[],g=0;g<e;g++)i[f].push(a.getValue(b,f,g));return i}function ia(a){var b;return h(a)?b=a.getValue(0,0,0):k(a)||(b=a),b}function ja(a){return{value:ia(a[0]),colorScheme:a[1]}}L=function(){function a(a,b){this.name=a,this.value=b,this.typeName="SparklineExValue"}return a.prototype.toString=function(){return""},a}(),b.SparklineExValue=L,M=function(){function a(){var a,b,c=this.createFunction();c&&(a=c.name,b=c.evaluate,c.evaluate=function(){var c=b.call(this,arguments);return c instanceof d.CalcError?c:c?new L(a,c):q},this.Cj=a,a&&!d.Functions.findGlobalFunction(a)&&(d.Functions.ci[a]=c)),this.typeName=""}return a.prototype.name=function(){return this.Cj},a.prototype.createFunction=function(){return q},a.prototype.paint=function(a,b,c,d,e,f,g){},a.prototype.toJSON=function(){var a,b={};for(a in this)this.hasOwnProperty(a)&&(b[a]=this[a]);return b},a.prototype.fromJSON=function(a){if(a)for(var b in a)k(a[b])||(this[b]=a[b])},a}(),b.SparklineEx=M,N=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.PieSparkline=N,n(N.prototype,{createFunction:function(){var a=new i(u,1,255);return a.evaluate=function(a){var b=a[0],c=ga(b);return c.length<=0&&!k(b)&&c.push(b),{values:c,colors:Array.prototype.slice.call(a,1)}},a.acceptsReference=ea,a},paint:p[u]}),O=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.AreaSparkline=O,n(O.prototype,{createFunction:function(){var a=new i(v,1,7);return a.evaluate=function(a){return{points:ga(a[0]),mini:a[1],maxi:a[2],line1:a[3],line2:a[4],colorPositive:a[5],colorNegative:a[6]}},a.acceptsReference=ea,a},paint:p[v]}),P=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.ScatterSparkline=P,n(P.prototype,{createFunction:function(){var a=new i(w,1,18);return a.evaluate=function(a){var b=ha(a[0],0),c=ha(a[1],0);return{points1:b,points2:c,minX:a[2],maxX:a[3],minY:a[4],maxY:a[5],hLine:a[6],vLine:a[7],xMinZone:a[8],xMaxZone:a[9],yMinZone:a[10],yMaxZone:a[11],tags:a[12],drawSymbol:a[13],drawLines:a[14],color1:a[15],color2:a[16],dash:a[17]}},a.acceptsReference=function(a){return 0===a||1===a},a},paint:p[w]}),Q=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.BulletSparkline=Q,n(Q.prototype,{createFunction:function(){var a=new i(x,3,9);return a.evaluate=function(a){return{measure:ia(a[0]),target:ia(a[1]),maxi:ia(a[2]),good:ia(a[3]),bad:ia(a[4]),forecast:ia(a[5]),tickUnit:ia(a[6]),colorScheme:a[7],vertical:a[8]}},a.acceptsReference=function(a){return 0<=a&&a<=6},a},paint:p[x]}),R=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.SpreadSparkline=R,n(R.prototype,{createFunction:function(){function a(a){var b,c,d,e,f=[];for(c=0,d=0,e=a.length;c<e;c++)b=s(a[c]),!r(b)&&isFinite(b)&&(f[d]=b,d++);return f}function b(a){var b,c,d,e,f,g={};for(b=0,c=void 0,d=a.length;b<d;b++)c=a[b],g[c]?g[c]++:g[c]=1;e=[];for(f in g)g.hasOwnProperty(f)&&e.push({key:s(f),value:g[f]});return e.sort(function(a,b){return a.key-b.key})}var c=new i(y,1,7);return c.evaluate=function(c){var d,e,f,g,h,i,j,l,m=ga(c[0]),n=[];if(!k(m)&&(m=a(m),n=b(m),d=c[4],3===d))for(e=0,f=n.length;e<f;e++){for(g=n[e],h=g.value,i=[],j=Math.max(100,10*h),l=0;l<j+h-1;l++)i.push(Math.random());g.randomNumbers=i}return{spreadData:n,showAverage:c[1],scaleStart:c[2],scaleEnd:c[3],style:d,colorScheme:c[5],vertical:c[6]}},c.acceptsReference=ea,c},paint:p[y]}),S=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.StackedSparkline=S,n(S.prototype,{createFunction:function(){var a=new i(z,1,13);return a.evaluate=function(a){return{points:ga(a[0]),colorRange:ga(a[1]),labelRange:ga(a[2]),maximum:a[3],targetRed:a[4],targetGreen:a[5],targetBlue:a[6],targetYellow:a[7],color:a[8],highlightPosition:a[9],vertical:a[10],textOrientation:a[11],textSize:a[12]}},a.acceptsReference=function(a){return 0<=a&&a<=2},a},paint:p[z]}),T=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.HBarSparkline=T,n(T.prototype,{createFunction:function(){var a=new i(A,1,2);return a.evaluate=ja,a.acceptsReference=ea,a},paint:p[A]}),U=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.VBarSparkline=U,n(U.prototype,{createFunction:function(){var a=new i(B,1,2);return a.evaluate=ja,a.acceptsReference=ea,a},paint:p[B]}),V=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.VariSparkline=V,n(V.prototype,{createFunction:function(){var a=new i(C,1,10);return a.evaluate=function(a){return{variance:ia(a[0]),reference:ia(a[1]),mini:ia(a[2]),maxi:ia(a[3]),mark:ia(a[4]),tickUnit:ia(a[5]),legend:a[6],colorPositive:a[7],colorNegative:a[8],vertical:a[9]}},a.acceptsReference=function(a){return 0<=a&&a<=5},a},paint:p[C]}),W=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.BoxPlotSparkline=W,n(W.prototype,{createFunction:function(){function a(a,b){return h(a)&&!r(b)&&0<=b&&b<=100?j.PERCENTILE.evaluate(a,b/100):t}function b(a){return h(a)?j.STDEVP.evaluate(a):t}var c=new i(D,1,10);return c.evaluate=function(c){var d=c[0];return{points:ga(d),boxPlotClass:c[1],showAverage:c[2],scaleStart:ia(c[3]),scaleEnd:ia(c[4]),acceptableStart:ia(c[5]),acceptableEnd:ia(c[6]),colorScheme:c[7],style:c[8],vertical:c[9],perc02:a(d,2),perc09:a(d,9),perc10:a(d,10),perc90:a(d,90),perc91:a(d,91),perc98:a(d,98),q1:a(d,25),q3:a(d,75),median:a(d,50),stDev:b(d)}},c.acceptsReference=ea,c},paint:p[D]}),X=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.CascadeSparkline=X,n(X.prototype,{createFunction:function(){var a=new i(E,1,8);return a.evaluate=function(a){return{points:ga(a[0]),pointIndex:ia(a[1]),labels:ga(a[2]),minimum:ia(a[3]),maximum:ia(a[4]),colorPositive:a[5],colorNegative:a[6],vertical:a[7]}},a.acceptsReference=fa,a.acceptsArray=fa,a},paint:p[E]}),Y=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.ParetoSparkline=Y,n(Y.prototype,{createFunction:function(){var a=new i(F,1,8);return a.evaluate=function(a){return{points:ga(a[0]),pointIndex:ia(a[1]),colorRange:ga(a[2]),target:ia(a[3]),target2:ia(a[4]),highlightPosition:ia(a[5]),label:a[6],vertical:a[7]}},a.acceptsReference=fa,a.acceptsArray=fa,a},paint:p[F]}),Z=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.MonthSparkline=Z,n(Z.prototype,{createFunction:function(){var a=new i(G,3,7);return a.evaluate=function(a){var b,c,d,e,f,g,i,j,k,l=a[0],m=a[1],n=a[2],o=n;for(h(n)&&(o=ha(n,0)),b=a[3],c=[],h(b)&&(c=ha(b,0)),d=[],e=[],f=0,g=o.length;f<g;f++)i=o[f],i&&(j=i[0],j&&j.getFullYear()===l&&j.getMonth()+1===m&&(k=j.getDate()-1,d[k]=i[1],c.length>0&&(e[k]=c[f][0])));return c.length>0?{year:l,month:m,values:d,colors:e}:{year:l,month:m,values:d,emptyColor:b,startColor:a[4],middleColor:a[5],endColor:a[6]}},a.acceptsReference=function(a){return 2===a||3===a},a},paint:p[G]}),$=function(a){da(b,a);function b(){return a.call(this)||this}return b.daysOfMonth=function(a,b){switch(b){case 2:var c=a%4===0&&a%100!==0||a%400===0;return c?29:28;case 4:case 6:case 9:case 11:return 30;default:return 31}},b.dayInYear=function(a){var c,d=a.getFullYear(),e=a.getMonth(),f=a.getDate();for(c=1;c<e+1;c++)f+=b.daysOfMonth(d,c);return f},b}(M),b.YearSparkline=$,n($.prototype,{createFunction:function(){var a=new i(H,2,6);return a.evaluate=function(a){var b,c,d,e,f,g,i,j,k,l=a[0],m=a[1],n=m;for(h(m)&&(n=ha(m,0)),b=a[2],c=[],h(b)&&(c=ha(b,0)),d=[],e=[],f=0,g=n.length;f<g;f++)i=n[f],i&&(j=i[0],j&&j.getFullYear()===l&&(k=$.dayInYear(j)-1,d[k]=i[1],c.length>0&&(e[k]=c[f][0])));return c.length>0?{year:l,values:d,colors:e}:{year:l,values:d,emptyColor:b,startColor:a[3],middleColor:a[4],endColor:a[5]}},a.acceptsReference=function(a){return 1===a||2===a},a},paint:p[H]});function ka(a,b){function c(a){var b,c,d,e,f,g={},h=!1,i=!0,j="",l="";if(a){for(a=a.substr(1,a.length-2),b=0,c=a.length;b<c;b++)d=a.charAt(b),":"===d?i=!1:","!==d||h?"'"===d||'"'===d||("("===d?h=!0:")"===d&&(h=!1),i?j+=d:l+=d):(g[j]=l,j="",l="",i=!0);j&&(g[j]=l);for(e in g)g.hasOwnProperty(e)&&(f=g[e],k(f)||("TRUE"===f.toUpperCase()?g[e]=!0:"FALSE"===f.toUpperCase()?g[e]=!1:!r(f)&&isFinite(f)&&(g[e]=s(f))))}return g}function d(a){return new e.Range(a.getRow(0),a.getColumn(0),a.getRowCount(0),a.getColumnCount(0))}var f=new i(a,2,5);return f.evaluate=function(a){var e,f,i,j,l,m,n,o,p,q,r,s,t,u=a[0];if(h(u)&&(e=u.getRangeCount(),e>0)){f=d(u),i=u.getSource().getSheet().name(),j=a[1],l=a[2],m=void 0,n=void 0,o=void 0,h(l)&&(o=l.getSource().getSheet().name(),e=l.getRangeCount(),e>0&&(m=d(l),n=a[3])),p=c(a[4]||"{}"),q={ac:"axisColor",fmc:"firstMarkerColor",hmc:"highMarkerColor",lastmc:"lastMarkerColor",lowmc:"lowMarkerColor",mc:"markersColor",nc:"negativeColor",sc:"seriesColor",deca:"displayEmptyCellsAs",rtl:"rightToLeft",dh:"displayHidden",dxa:"displayXAxis",sf:"showFirst",sh:"showHigh",slast:"showLast",slow:"showLow",sn:"showNegative",sm:"showMarkers",mmax:"manualMax",mmin:"manualMin",maxat:"maxAxisType",minat:"minAxisType",lw:"lineWeight"};for(r in q)q.hasOwnProperty(r)&&p.hasOwnProperty(r)&&(p[q[r]]=p[r]);return s=new g.SparklineSetting(p),t=s.options,1===t.maxAxisType&&(t.maxAxisType=0),1===t.minAxisType&&(t.minAxisType=0),{data:f,dataSheetName:i,dataOrientation:j,dateAxisData:m,dateAxisDataSheetName:o,dateAxisOrientation:n,sparklineType:b,displayDateAxis:m&&!k(n),setting:s,values:ha(u,0),dateValues:ha(l,0)}}},f.acceptsReference=fa,f}function la(a,b,c,d,f,h,i){var j,k,l=g.Sparkline.cT,m=i.sheet,n=m.parent.getSheetFromName(b.dataSheetName),p=m.parent.getSheetFromName(b.dateAxisDataSheetName),q=b.setting;q&&q.options||(q=new g.SparklineSetting(q)),j=q.options.displayHidden,k={sparklineType:b.sparklineType,displayDateAxis:b.displayDateAxis,zoomFactor:i.zoomFactor,values:l(b.values,n||m,j,b.data,b.dataOrientation),dateValues:l(b.dateValues,p||m,j,b.dateAxisData,b.dateAxisOrientation,!0),settings:q,getColor:function(a){if(m&&a){var b=e.Rm.Om(m,a);if(b)return b}return a}},(new o).paint(a,k,c,d,f,h)}_=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.LineSparkline=_,n(_.prototype,{createFunction:function(){return ka(I,0)},paint:la}),aa=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.ColumnSparkline=aa,n(aa.prototype,{createFunction:function(){return ka(J,1)},paint:la}),ba=function(a){da(b,a);function b(){return a.call(this)||this}return b}(M),b.WinlossSparkline=ba,n(ba.prototype,{createFunction:function(){return ka(K,2)},paint:la}),ca={},b.w_a=ca;function ma(a){a&&(ca[a.name()]=a)}i&&(ma(new N),ma(new O),ma(new P),ma(new Q),ma(new R),ma(new S),ma(new T),ma(new U),ma(new V),ma(new W),ma(new X),ma(new Y),ma(new Z),ma(new $),ma(new _),ma(new aa),ma(new ba)),n(e.Workbook.prototype,{addSparklineEx:function(a){this.dT||(this.dT={});var b=this.dT;a&&(b[a.name()]=a)},getSparklineEx:function(a){var b,c=ca[a];return c?c:(b=this.dT,b&&b[a])},removeSparklineEx:function(a){var b=this.dT;b&&(b[a]=void 0)}}),e.Workbook.$n("sparklineEx",{toJson:function(a){var b,c,d,e=this.dT,f=[];if(e){for(b in e)e.hasOwnProperty(b)&&(c=e[b],d=c.toJSON(),d&&d.typeName&&f.push(d));f.length>0&&(a.sparklineExs=f)}},fromJson:function(a){var b,c,d,f,g=a.sparklineExs;if(g)for(b=0;b<g.length;b++)c=g[b],d=e.getTypeFromString(c.typeName),d&&(f=new d,f.fromJSON(c),this.addSparklineEx(f))}})},"./dist/plugins/sparkline/sparkline-wrapper.entry.js":function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/sparkline/functions-sparkline.js"),b.w_a=d.w_a,b.PieSparkline=d.PieSparkline,b.AreaSparkline=d.AreaSparkline,b.ScatterSparkline=d.ScatterSparkline,b.BulletSparkline=d.BulletSparkline,b.SpreadSparkline=d.SpreadSparkline,b.StackedSparkline=d.StackedSparkline,b.HBarSparkline=d.HBarSparkline,b.VBarSparkline=d.VBarSparkline,b.VariSparkline=d.VariSparkline,b.BoxPlotSparkline=d.BoxPlotSparkline,b.CascadeSparkline=d.CascadeSparkline,b.ParetoSparkline=d.ParetoSparkline,b.MonthSparkline=d.MonthSparkline,b.YearSparkline=d.YearSparkline,b.LineSparkline=d.LineSparkline,b.ColumnSparkline=d.ColumnSparkline,b.WinlossSparkline=d.WinlossSparkline,b.SparklineEx=d.SparklineEx,b.SparklineExValue=d.SparklineExValue,e=c("./dist/plugins/sparkline/sparkline-wrapper.js"),b.EmptyValueStyle=e.EmptyValueStyle,b.SparklineAxisMinMax=e.SparklineAxisMinMax,b.SparklineSetting=e.SparklineSetting,b.SparklineType=e.SparklineType,b.DataOrientation=e.DataOrientation,b.Sparkline=e.Sparkline},"./dist/plugins/sparkline/sparkline-wrapper.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("CalcEngine"),g=c("SheetsCalc"),h=d.GC$.isEmptyObject,i=d.kf,j=e.Common.j.Fa,k=e.Common.k,l=k.Fb,m=k.ac,n=k.Bb,o=e.Sparklines.Vd,p=e.Sparklines.SparklineRender,q=null,r=void 0,s=Math.max,t=Number.MAX_VALUE,d.GC$.extend(d.lUa.prototype,{FUa:function(a){var b,c,d,e=this,f=e.zTa,g=arguments[1];if(f){switch(f.GUa||(f.GUa=[]),b={type:a},a){case 0:c=g,d=void 0,d=c&&c.sparkline?c.sparkline:q,b.HUa={row:c.row,col:c.col,sparkline:d};break;case 12:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:b.HUa=g}f.GUa.push(b)}},IUa:function(a){var b,c,d,e,f,g,h,j,k,n,o,p,r,s=this,t=a.HUa;switch(a.type){case 12:t.sparkline?(t.sparkline.row=t.row,t.sparkline.column=t.col,s.ZA.JUa(t.row,t.col,t.sparkline)):s.ZA.JUa(t.row,t.col,q);break;case 0:s.ZA.JUa(t.row,t.col,t.sparkline),t.sparkline&&!s.ZA.contains(t.sparkline.group())&&s.ZA.add(t.sparkline.group());break;case 1:for(c=void 0,d=void 0,e=void 0,f=m(t),g=0;g<f;g++)c=t[g].row,d=t[g].col,e=s.ZA.KUa(c,d),0===g&&e.cb.count()===f&&s.ZA.remove(e.cb),this.ZA.add(e.group(t[g].group).cb);break;case 2:for(h=[],j=0;j<m(t);j++)h.push(s.ZA.KUa(t[j].row,t[j].col));s.ZA.kj.groupSparkline(h);break;case 3:b=s.ZA.KUa(t.row,t.col),b.sparklineType(t.sparklineType);break;case 4:b=s.ZA.KUa(t.row,t.col),b.setting(t.setting);break;case 5:t.sparkline.data(t.data);break;case 15:b=s.ZA.KUa(t.row,t.col),b.dataSheetName(t.dataSheetName);break;case 6:b=s.ZA.KUa(t.row,t.col),b.dataOrientation(t.dataOrientation);break;case 7:b=s.ZA.KUa(t.row,t.col),b.displayDateAxis(t.displayDateAxis);break;case 8:k=void 0,n=t.rangeInfo,k=n?i(n.row,n.col,n.rowCount,n.colCount):n,t.group.dateAxisData(k);break;case 16:t.group.dateAxisDataSheetName(t.dateAxisDataSheetName);break;case 9:t.group.dateAxisOrientation(t.dateAxisOrientation);break;case 10:for(o=t.group.PS,o.forEach(function(a){s.ZA.vZa(a)}),t.group.PS=t.PS,p=t.group.PS,r=0;r<m(p);r++)p[r].group(t.group);p.forEach(function(a){s.ZA.tZa(a)});break;case 11:b=s.ZA.KUa(t.row,t.col),b.group(t.group);break;case 13:l(s.ZA.groups(),t),s.ZA.yZa(t);break;case 14:s.ZA.groups().push(t),s.ZA.zZa(t)}},LUa:function(a){var b,c=m(a);for(b=c-1;b>=0;b--)this.IUa(a[b])},MUa:function(a,b){this.ZA.rI(a,b)},NUa:function(a,b){this.ZA.GR(a,b)},OUa:function(a,b){this.ZA.tI(a,b)},PUa:function(a,b){this.ZA.HR(a,b)},QUa:function(a,b,c,d){this.ZA.clear(a,b,c,d)},wZa:function(){var a=this.zTa;return a?(a.xZa||(a.xZa=[]),a.xZa):q}}),d.lUa.$n("sparkline",{init:function(){var a=this.kj;this.ZA=new w(a,a)},undo:function(a){var b,c,d=a.GUa;d&&this.LUa(d),b=this.kj,c=a.xZa,c&&c.forEach(function(a){var c=b.parent.getSheetFromName(a.sheetName).ITa;c.undo(a.changes)})}}),u={init:function(){this.ZA=this.ITa.ZA},dispose:function(a){if(a.clearCache!==!1){var b=this;b.ZA&&(b.ZA.kj=q,b.ZA.evaluator=q,b.ZA=q),b.ITa&&(b.ITa.ZA=q)}},onLayoutChanged:function(a){var b,c=a.changeType,d=a.row,e=a.rowCount,f=a.col,g=a.colCount,h=this.ITa;"addRows"===c?h.MUa(d,e):"deleteRows"===c?h.NUa(d,e):"addColumns"===c?h.OUa(f,g):"deleteColumns"===c?h.PUa(f,g):"clear"===c&&(b=a.type,3===a.sheetArea&&16===(16&b)&&h.QUa(d,f,e,g))},toJson:function(a,b){var c=b&&b.ignoreStyle;c||(a.sparklineGroups=this.ZA.toJSON())},fromJson:function(a,b,c){var d=c&&c.ignoreStyle,e=this,f=e.ZA,g=a.sparklineGroupManager,h=b?g&&g.groups:a.sparklineGroups;h&&!d&&f.RUa(h,b)},setName:function(a){var b=a.oldName,c=a.newName;this.ZA.jOa(b,c)}},d.Worksheet.$n("sparkline",u);function H(a,b){var c=g.formulaToRanges(a,b,0,0),d=c[0],e=d&&d.ranges&&d.ranges[0];return e?{range:e,sheetName:d.sheetName}:q}v={getSparkline:function(a,b){return this.ZA?this.ZA.KUa(a,b):q},setSparkline:function(a,b,c,d,e,f,g,h){var i,k,l,m,n,o,p=this,r=p.ZA;if(!r)return q;if("string"==typeof c){if(k=H(p,c),!k)return q;c=k.range,i=k.sheetName}if(l=new G(a,b,c,d,e,f),i&&i!==p.name()&&l.dataSheetName(i),g&&!j(h)){if(m=void 0,"string"==typeof g){if(n=H(p,g),!n)return q;g=n.range,m=n.sheetName}l.dateAxisData(g),m&&m!==p.name()&&l.dateAxisDataSheetName(m),l.dateAxisOrientation(h),l.group().displayDateAxis=!0}return o=p.getSparkline(a,b),p.removeSparkline(a,b),I.call(p,a,b,o,l),p.$p(),l},removeSparkline:function(a,b){var c,d,e,f=this,g=f.ZA;g&&(c=f.getSparkline(a,b),d={row:a,col:b,sparkline:c},f.ITa.FUa(0,d),c&&(e=c.group(),e.remove(c),e.count()<=0&&g.remove(e),I.call(f,a,b,c,q)),f.$p())},groupSparkline:function(a){var b,c,d,e,f,g,h=this,i=h.ZA;if(!i)return q;for(b=q,c=[],d=h.ITa.zTa!==r,e=0;e<a.length;e++)f=a[e],f&&(d&&c.push({group:f.group().clone(),row:f.row,col:f.column}),b?(g=f.group(),g.remove(f),b.add(f),g.count()<=0&&i.remove(g)):b=f.group());return h.$p(),h.ITa.FUa(1,c),b},ungroupSparkline:function(a){var b,c,d,e,f,g,h=this,i=h.ZA;if(i&&a){for(b=[],c=[],d=h.ITa.zTa!==r,b=b.concat(a.PS),e=0;e<b.length;e++)f=b[e],f&&(d&&c.push({row:f.row,col:f.column}),a.remove(f),g=a.clone(),g.add(f),i.add(g));i.remove(a),h.ITa.FUa(2,c),h.$p()}}};function I(a,b,c,d){var e=this,f=e.ZA;f.JUa(a,b,d),d&&f.add(d.group()),e.Bq("sparkline",a,b,3,c,d)}d.GC$.extend(d.Worksheet.prototype,v),w=function(){function a(a,b){this.Ub=[],this.kj=a,this.evaluator=b,this.xn={},this.qZa=[],this.rZa=[]}return a.prototype.RUa=function(a,b){var c=this;c.fromJSON(a,b),c.groups().forEach(function(a){a.PS.forEach(function(a){c.SUa(a)})})},a.prototype.SUa=function(a){a&&this.JUa(a.row,a.column,a)},a.prototype.JUa=function(a,b,c){if(this.kj.ITa.zTa){var d={row:a,col:b,sparkline:this.KUa(a,b)};this.kj.ITa.FUa(12,d)}c?this.xn[J(a,b)]=c:delete this.xn[J(a,b)]},a.prototype.KUa=function(a,b){return this.xn[J(a,b)]||q},a.prototype.groups=function(a){return 0===arguments.length?this.Ub:void(this.Ub=a)},a.prototype.add=function(a){var b=this;b.kj.ITa.FUa(13,a),b.groups().push(a),a.ZA=b,a.QS(),b.zZa(a)},a.prototype.zZa=function(a){var b=this;b.sZa(a),a.all().forEach(function(a){b.tZa(a)})},a.prototype.remove=function(a){var b=this;b.kj.ITa.FUa(14,a),l(b.groups(),a),b.yZa(a)},a.prototype.yZa=function(a){var b=this;b.uZa(a),a.all().forEach(function(a){b.vZa(a)})},a.prototype.contains=function(a){return n(this.groups(),a)},a.prototype.count=function(){return this.groups().length},a.prototype.rI=function(a,b){M(this.Ub,a,b,!0,this),N(a,b,!0,this.rZa,this.qZa,this.kj.ITa.wZa())},a.prototype.tI=function(a,b){M(this.Ub,a,b,!1,this),N(a,b,!1,this.rZa,this.qZa,this.kj.ITa.wZa())},a.prototype.GR=function(a,b){R(this.Ub,a,b,!0,this),S(a,b,!0,this.rZa,this.qZa,this.kj.ITa.wZa())},a.prototype.HR=function(a,b){R(this.Ub,a,b,!1,this),S(a,b,!1,this.rZa,this.qZa,this.kj.ITa.wZa())},a.prototype.jOa=function(a,b){this.rZa.forEach(function(c){c.dateAxisDataSheetName()===a&&c.dateAxisDataSheetName(b)}),this.qZa.forEach(function(c){c.dataSheetName()===a&&c.dataSheetName(b)})},a.prototype.tZa=function(a){var b,c,d=this.kj,e=a.dataSheetName();e&&e!==d.name()&&(b=d.parent.getSheetFromName(e),b&&(c=b.ZA.qZa,c.indexOf(a)<0&&c.push(a)))},a.prototype.vZa=function(a){var b,c,d,e=a.dataSheetName();e&&(b=this.kj.parent.getSheetFromName(e),b&&(c=b.ZA.qZa,d=c.indexOf(a),d>=0&&c.splice(d,1)))},a.prototype.sZa=function(a){var b,c,d=this.kj,e=a.dateAxisDataSheetName();e&&e!==d.name()&&(b=d.parent.getSheetFromName(e),b&&(c=b.ZA.rZa,c.indexOf(a)<0&&c.push(a)))},a.prototype.uZa=function(a){var b,c,d,e=a.dateAxisDataSheetName();e&&(b=this.kj.parent.getSheetFromName(e),b&&(c=b.ZA.rZa,d=c.indexOf(a),d>=0&&c.splice(d,1)))},a.prototype.clear=function(a,b,c,d){var e,f,g,h,i,j,k,l,n,o=this;if(0===arguments.length){for(e=o.Ub,f=m(e)-1;f>-1;f--)if(g=e[f]){for(h=g.count()-1;h>-1;h--)i=g.PS[h],o.JUa(i.row,i.column,q),g.remove(i);o.remove(g)}}else for(j=a;j<a+c;j++)for(k=b;k<b+d;k++)l=o.RS(j,k),l&&(o.JUa(l.row,l.column,q),n=l.group(),n.remove(l),0===n.count()&&o.remove(n))},a.prototype.RS=function(a,b){var c,d,e,f,g=this.Ub;for(c=0;c<g.length;c++)for(d=g[c],e=0;e<d.count();e++)if(f=d.PS[e],f&&f.row===a&&f.column===b)return f;return q},a.prototype.Zz=function(a,b,c,e,f,g,h){var i,k,l,m,n,o,p,q,r,s,t,u,v=this,w=v.kj;if(w){for(i=new d.KTa,k=c-a,l=e-b,o=w.getRowCount(),p=w.getColumnCount(),q=w.ZA,m=0;m<f;m++)if(!(h&&w.Ps&&w.Ps(c+m)))for(n=0;n<g;n++)r=w.getSparkline(a+m,b+n),r&&(s=r.clone(),s.row=c+m,s.column=e+n,t=s.dateAxisData(),!j(t)&&v.SS(t,k,l,o,p)&&s.dateAxisData(t.offset(l,k)),u=s.data(),!j(u)&&v.SS(u,k,l,o,p)&&s.data(u.offset(l,k)),v.add(s.group()),i.set(m,n,s));for(m=0;m<f;m++)if(!(h&&w.Ps&&w.Ps(c+m)))for(n=0;n<g;n++)q.JUa(c+m,e+n,i.get(m,n))}},a.prototype.SS=function(a,b,c,d,e){var f,g,h,i,j=this.TS(a);return!!j&&(f=s(j.row,0),g=s(j.col,0),h=j.row<0?d:j.rowCount,i=j.col<0?e:j.colCount,!(f+b<0||g+c<0||f+h+b>d||g+i+c>e))},a.prototype.aB=function(a,b,c,e,f,g){var h,i,j,k,l,m=this.kj;if(m){for(this.US(a,b,c,e,f,g),h=m.ZA,i=new d.KTa,j=0;j<f;j++)for(k=0;k<g;k++)l=m.getSparkline(a+j,b+k),l&&(l.row=c+j,l.column=e+k,i.set(j,k,l)),h.JUa(a+j,b+k,q);for(j=0;j<f;j++)for(k=0;k<g;k++)m.removeSparkline(c+j,e+k),h.JUa(c+j,e+k,i.get(j,k))}},a.prototype.US=function(a,b,c,d,e,f){var g,h,j,k,l,m,n,o,p,q=this,r=i(a,b,e,f),s=c-a,t=d-b,u=q.Ub;for(g=0;g<u.length;g++)for(h=u[g],j=h.PS,k=0;k<j.length;k++)l=j[k],l&&(m=l.dateAxisData(),n=q.TS(m),n&&r.containsRange(n)&&r.contains(l.row,l.column)&&l.dateAxisData(m.offset(t,s)),o=l.data(),p=q.TS(o),p&&r.containsRange(p)&&r.contains(l.row,l.column)&&l.data(o.offset(t,s)))},a.prototype.TS=function(a){return a},a.prototype._A=function(a,b,c,d,e,f,g,h){var i,k,l,m,n,o,p,r,s,t,u,v=this,w=v.kj;if(w){if(a===w)return void v.Zz(b,c,d,e,f,g);for(i=d-b,k=e-c,l=w.ZA,m=w.getRowCount(),n=w.getColumnCount(),o=0;o<f;o++)if(!(h&&w.Ps&&w.Ps(d+o)))for(p=0;p<g;p++)r=a.getSparkline(b+o,c+p),r?(s=r.clone(),s.row=d+o,s.column=e+p,t=s.dateAxisData(),!j(t)&&v.SS(t,i,k,m,n)&&s.dateAxisData(t.offset(k,i)),u=s.data(),!j(u)&&v.SS(u,i,k,m,n)&&s.data(u.offset(k,i)),v.add(s.group()),l.JUa(d+o,e+p,s)):l.JUa(d+o,e+p,q)}},a.prototype.$A=function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o=this,p=o.kj;if(p){if(a===p)return void o.aB(b,c,d,e,f,g);for(h=a.ZA,i=p.ZA,o.VS(a,b,c,d,e,f,g),j=0;j<f;j++)for(k=0;k<g;k++)l=a.getSparkline(b+j,c+k),l?(l.row=d+j,l.column=e+k,m=l.group(),n=m.clone(),m.remove(l),m.count()<=0&&h.remove(m),n.add(l),o.add(n),i.JUa(d+j,e+k,l)):i.JUa(d+j,e+k,q),h.JUa(b+j,c+k,q)}},a.prototype.VS=function(a,b,c,d,e,f,g){var h,j,k,l,m,n,o,p,q,r,s,t,u=this,v=u.kj;if(a&&v){if(a===v)return void u.US(b,c,d,e,f,g);for(h=i(b,c,f,g),j=d-b,k=e-c,l=a.ZA.Ub,m=0;m<l.length;m++)for(n=l[m],o=0;o<n.PS.length;o++)p=n.PS[o],p&&(q=p.dateAxisData(),r=u.TS(q),r&&h.containsRange(r)&&h.contains(p.row,p.column)&&p.dateAxisData(q.offset(k,j)),s=p.data(),t=u.TS(s),t&&h.containsRange(t)&&h.contains(p.row,p.column)&&p.data(s.offset(k,j)))}},a.prototype.toJSON=function(){var a,b=this.Ub,c=[];for(a=0;b&&a<b.length;a++)c.push(b[a].toJSON());return 0===c.length?r:c},a.prototype.fromJSON=function(a,b){var c,d,e;for(this.Ub.length=0,this.xn={},c=0;a&&c<a.length;c++)d=a[c],e=new F,e.fromJSON(d,b),this.add(e)},a}();function J(a,b){return a+"_"+b}function K(a,b,c){return c?a>c.row+c.rowCount-1?c:a>c.row?i(c.row,c.col,c.rowCount+b,c.colCount):i(c.row+b,c.col,c.rowCount,c.colCount):q}function L(a,b,c){return c?a>c.col+c.colCount-1?c:a>c.col?i(c.row,c.col,c.rowCount,c.colCount+b):i(c.row,c.col+b,c.rowCount,c.colCount):q}function M(a,b,c,e,f){var g,h,i,j,k,l,m,n,o,p,r,s,t,u,v=new d.KTa;for(g=0;g<a.length;g++)for(h=a[g],i=h.PS,h.displayDateAxis&&(j=void 0,k=h.dateAxisData(),j=e?K(b,c,k):L(b,c,k),h.dateAxisData(j)),l=0;l<i.length;l++)m=i[l],n=e?m.row:m.column,b<=n&&(v.set(g,l,m),f.JUa(m.row,m.column,q),e?m.row+=c:m.column+=c),o=void 0,p=m.data(),o=e?K(b,c,p):L(b,c,p),m.data(o);for(r=0;r<a.length;r++)for(s=a[r],t=s.PS,u=0;u<t.length;u++)f.SUa(v.get(r,u))}function N(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,s,t,u;for(g=0;g<d.length;g++)h=d[g],h.displayDateAxis&&(i=h.OW(),j=i.ITa,k=!1,f&&!j.zTa&&(j.zTa=[],k=!0),l=void 0,m=h.dateAxisData(),l=c?K(a,b,m):L(a,b,m),h.dateAxisData(l),k&&(f.push({sheetName:i.name(),changes:j.zTa}),j.zTa=r));for(n=0;n<e.length;n++)o=e[n],p=o.bT(),q=p.ITa,s=!1,f&&!q.zTa&&(q.zTa=[],s=!0),t=void 0,u=o.data(),t=c?K(a,b,u):L(a,b,u),o.data(t),s&&(f.push({sheetName:p.name(),changes:q.zTa}),q.zTa=r)}function O(a,b,c){var d=Q(c.col,c.col+c.colCount-1,a,a+b-1);return d?i(c.row,d.start,c.rowCount,d.end-d.start+1):q}function P(a,b,c){var d=Q(c.row,c.row+c.rowCount-1,a,a+b-1);return d?i(d.start,c.col,d.end-d.start+1,c.colCount):q}function Q(a,b,c,d){var e,f,g,h,i,j,k=-1;if(d<a)return g=d-c+1,k=a-g,e=b-g,{start:k,end:e};if(c>b)return k=a,e=b,{start:k,end:e};if(c<=a){for(h=b-a+1,i=0,f=a;f<=d&&f<=b;f++)i++;return k=c,e=k+h-i-1,{start:k,end:e}}for(j=0,f=a;f<=b;f++)k===-1&&(f<c||f>d)&&(k=f),(f<c||f>d)&&j++;return k!==-1&&j>0?(e=k+j-1,{start:k,end:e}):q}function R(a,b,c,e,f){var g,h,i,k,l,n,o,p,r,t,u,v,w,x,y=new d.KTa,z=a.length,A=0;for(g=z-1;g>-1;g--){if(h=a[g],h.displayDateAxis){if(i=void 0,k=h.dateAxisData(),i=e?P(b,c,k):O(b,c,k),j(i)){h.clear(),f.remove(h);continue}h.dateAxisData(i)}for(l=[],l=l.concat(h.PS),n=m(l),A=s(n,A),o=n-1;o>-1;o--)p=l[o],r=e?p.row:p.column,r>=b&&r<b+c?(h.remove(p),f.JUa(p.row,p.column,q)):(b<=r&&(f.JUa(p.row,p.column,q),e?p.row-=c:p.column-=c,y.set(g,o,p)),t=void 0,u=p.data(),t=e?P(b,c,u):O(b,c,u),j(t)?h.remove(p):p.data(t));h.count()<=0&&f.remove(h)}for(w=0;w<z;w++)for(x=0;x<A;x++)v=y.get(w,x),v&&f.SUa(v)}function S(a,b,c,d,e,f){var g,h,i,k,l,m,n,o,p,q,s,t,u,v,w,x,y=d.length;for(g=y-1;g>-1;g--)h=d[g],h.displayDateAxis&&(i=h.OW(),k=i.ITa,l=!1,f&&!k.zTa&&(k.zTa=[],l=!0),m=void 0,n=h.dateAxisData(),m=c?P(a,b,n):O(a,b,n),j(m)?(h.clear(),h.ZA.remove(h)):h.dateAxisData(m),l&&(f.push({sheetName:i.name(),changes:k.zTa}),k.zTa=r));for(o=e.length,p=o-1;p>-1;p--)q=e[p],s=q.bT(),t=s.ITa,u=!1,f&&!t.zTa&&(t.zTa=[],u=!0),v=void 0,w=q.data(),v=c?P(a,b,w):O(a,b,w),j(v)?(x=q.group(),x.remove(q),x.count()<=0&&x.ZA.remove(x)):q.data(v),u&&(f.push({sheetName:s.name(),changes:t.zTa}),t.zTa=r)}!function(a){a[a.gaps=0]="gaps",a[a.zero=1]="zero",a[a.connect=2]="connect"}(x=b.EmptyValueStyle||(b.EmptyValueStyle={})),function(a){a[a.individual=0]="individual",a[a.group=1]="group",a[a.custom=2]="custom"}(y=b.SparklineAxisMinMax||(b.SparklineAxisMinMax={})),z=e.Common.pc.bc(255,149,179,215),A=e.Common.pc.bc(255,36,64,98),B={axisColor:"black",firstMarkerColor:z,highMarkerColor:"Blue",lastMarkerColor:z,lowMarkerColor:"Blue",markersColor:A,negativeColor:"Brown",seriesColor:A,displayEmptyCellsAs:0,rightToLeft:!1,displayHidden:!1,displayXAxis:!1,showFirst:!1,showHigh:!1,showLast:!1,showLow:!1,showNegative:!1,showMarkers:!1,manualMax:0,manualMin:0,maxAxisType:0,minAxisType:0,lineWeight:1},C=function(){function a(a){var b,c=this;c.options={};for(b in B)B.hasOwnProperty(b)&&(c.options[b]=a&&a[b]?a[b]:B[b]);c.WS=q}return a.prototype.clone=function(){return new a(this.options)},a.prototype.toJSON=function(){var a,b=this,c=b.options,d={};for(a in c)B[a]!==c[a]&&(d[a]=c[a]);return h(d)?r:d},a}(),b.SparklineSetting=C,function(a){a[a.line=0]="line",a[a.column=1]="column",a[a.winloss=2]="winloss"}(D=b.SparklineType||(b.SparklineType={})),function(a){a[a.vertical=0]="vertical",a[a.horizontal=1]="horizontal"}(E=b.DataOrientation||(b.DataOrientation={})),F=function(){function a(a,b){var c=this;c.displayDateAxis=!1,c.ZA=q,c.PS=[],c.XS=q,c.YS=1,c.setting=b,c.sparklineType=a}return a.prototype.OW=function(){var a=this,b;return a.ZA&&(b=a.ZA.kj),b},a.prototype.TUa=function(){var a,b=this,c=b.OW();c&&c.ITa.zTa&&(a={group:b,PS:b.PS.concat()},c.ITa.FUa(10,a))},a.prototype.add=function(a){var b,c=this;a&&(c.TUa(),c.PS.push(a),a.group(c),c.QS(),b=c.ZA,b&&b.tZa(a),c.onGroupChanged())},a.prototype.clear=function(){this.TUa();var a=this.ZA;a&&this.PS.forEach(function(b){a.vZa(b)}),this.PS=[]},a.prototype.all=function(){return this.PS},a.prototype.remove=function(a){var b,c=this,d=c.PS;return c.TUa(),l(d,a),a.onSparklineChanged(),a.cb=c.clone(),c.QS(),b=c.ZA,b&&b.vZa(a),c.onGroupChanged(),d},a.prototype.contains=function(a){return n(this.PS,a)},a.prototype.onGroupChanged=function(){var a,b,c=this.PS;if(c)for(a=0;a<c.length;a++)b=c[a],b&&b.onSparklineChanged()},a.prototype.clone=function(){var b=this,c=b.setting?b.setting.clone():q,d=new a(b.sparklineType,c);return d.displayDateAxis=b.displayDateAxis,d.XS=b.XS,d.nZa=b.nZa,d.YS=b.YS,d},a.prototype.dateAxisData=function(a){var b,c,d,e,f=this;return 0===arguments.length?f.XS:(b=f.OW(),b&&b.ITa.zTa&&(c=void 0,c=f.XS?{row:f.XS.row,col:f.XS.col,rowCount:f.XS.rowCount,colCount:f.XS.colCount}:q,d={group:f,rangeInfo:c},b.ITa.FUa(8,d)),e=f.XS,f.XS=a,void(e!==a&&f.onGroupChanged()))},a.prototype.dateAxisDataSheetName=function(a){var b,c,d,e=this;return 0===arguments.length?e.nZa:(b=e.nZa,c=e.OW(),c&&c.ITa.zTa&&(d={group:e,dateAxisDataSheetName:b},c.ITa.FUa(16,d)),b!==a&&(b&&c&&c.ZA.uZa(e),e.nZa=a,a&&c&&c.ZA.sZa(e),
e.onGroupChanged()),e)},a.prototype.dateAxisOrientation=function(a){var b,c,d,e=this;return 0===arguments.length?e.YS:(b=e.OW(),b&&b.ITa.zTa&&(c={group:e,dateAxisOrientation:e.YS},b.ITa.FUa(9,c)),d=e.YS,e.YS=a,void(d!==a&&e.onGroupChanged()))},a.prototype.count=function(){return this.PS.length},a.prototype.QS=function(){var a,b,c,d,e,f,g,h,i=this,j=i.setting,k=i.PS;if(j&&(a=j.options,a.groupMaxValue=-t,a.groupMinValue=t,b=1===a.maxAxisType,c=1===a.minAxisType,b||c))for(d=0;d<k.length;d++)e=k[d],f=i.ZS(e),g=f.min,h=f.max,b&&a.groupMaxValue<h&&(a.groupMaxValue=h),c&&a.groupMinValue>g&&(a.groupMinValue=g)},a.prototype.ZS=function(a){var b,c,d,e,f,g,h=-t,i=t,j=a.data();if(j)for(b=a.bT(),c=b&&b.parent.getSheetFromName(a.dataSheetName()),d=a.$S(j,a.dataOrientation(),!1,c||b),e=0;e<d.length;e++)f=d[e],f===o&&(f=0),"number"==typeof f&&(g=f,h=h<g?g:h,i=i>g?g:i);return{min:i,max:h}},a.prototype.toJSON=function(){var a,b,c,d=this,e={setting:d.setting?d.setting.toJSON():q,displayDateAxis:d.displayDateAxis,sparklineType:d.sparklineType,axisOrientation:d.YS,sparklines:d.PS.map(function(a){return a.toJSON()})},f=d.XS;f&&(e.axisReference={row:f.row,col:f.col,rowCount:f.rowCount,colCount:f.colCount,sheetName:d.nZa}),a={};for(b in e)e.hasOwnProperty(b)&&(c=e[b],T(b,c)||(a[b]=c));return h(a)?r:a},a.prototype.fromJSON=function(a,b){var c,d,e,f,g,h,k,l,m;if(a&&(c=this,d=a.displayDateAxis,e=a.sparklineType,f=a.axisReference,g=a.axisOrientation,h=a.sparklines,c.setting=new C(a.setting),j(d)||(c.displayDateAxis=d),j(e)||(c.sparklineType=e),j(f)||(c.XS=i(f.row,f.col,f.rowCount,f.colCount),c.nZa=f.sheetName),j(g)||(c.YS=g),h))for(c.PS=[],k=0,l=h.length;k<l;k++)m=new G,m.fromJSON(h[k]),c.add(m)},a}();function T(a,b){var c=!1;return"setting"===a||"axisReference"===a?c=b===q:"displayDateAxis"===a?c=b===!1:"axisOrientation"===a?c=1===b:"sparklines"===a&&(c=0===b.length),c}G=function(){function a(a,b,c,d,e,f){var g=this;g.row=a,g.column=b,g._S=d,g.aT=c,g.cb=new F(e,f),g.cb.add(g)}return a.prototype.group=function(a){var b,c,d,e,f=this;return 0===arguments.length?(b=f.cb,b||(b=new F,b.add(f),f.cb=b),b):(c=f.cb,d=f.bT(),d&&d.ITa.zTa&&(e={row:f.row,col:f.column,group:c},d.ITa.FUa(11,e)),a!==c&&(c&&c.remove(f),f.cb=a,a&&!a.contains(f)&&a.add(f),f.onSparklineChanged()),d&&d.$p(),f)},a.prototype.sparklineType=function(a){var b,c,d,e,f=this;return 0===arguments.length?f.group().sparklineType:(b=f.group(),c=b.sparklineType,c!==a&&(b.sparklineType=a,d=f.bT(),d&&d.ITa.zTa&&(e={row:f.row,col:f.column,sparklineType:c},d.ITa.FUa(3,e)),f.onSparklineChanged(),d&&d.$p()),f)},a.prototype.onSparklineChanged=function(){var a=this,b=a.bT();b&&b.Wq(d.Events.SparklineChanged,{sheet:b,sheetName:b.name(),sparkline:a})},a.prototype.setting=function(a){var b,c,e=this,f=e.group(),g=e.bT(),h=g&&g.ITa.zTa;return 0===arguments.length?(b=void 0,b=h?d.GC$.extend(!0,{},f.setting):f.setting):(h&&(c={row:e.row,col:e.column,setting:d.GC$.extend(!0,{},f.setting)},g.ITa.FUa(4,c)),f.setting=a,g&&g.$p(),e)},a.prototype.data=function(a){var b,c,e=this;return 0===arguments.length?e.aT:(b=e.bT(),b&&b.ITa.zTa&&(c={row:e.row,col:e.column,sparkline:e,data:d.GC$.extend(!0,{},e.aT)},b.ITa.FUa(5,c)),e.aT!==a&&(e.aT=a,e.onSparklineChanged()),b&&b.$p(),e)},a.prototype.dataSheetName=function(a){var b,c,d,e=this;return 0===arguments.length?e.oZa:(b=e.oZa,c=e.bT(),c&&c.ITa.zTa&&(d={row:e.row,col:e.column,dataSheetName:b},c.ITa.FUa(15,d)),b!==a&&(b&&c&&c.ZA.vZa(e),e.oZa=a,a&&c&&c.ZA.tZa(e),e.onSparklineChanged()),e)},a.prototype.dataOrientation=function(a){var b,c,d=this;return 0===arguments.length?d._S:(b=d.bT(),b&&b.ITa.zTa&&(c={row:d.row,col:d.column,dataOrientation:d._S},b.ITa.FUa(6,c)),d._S!==a&&(d._S=a,d.onSparklineChanged()),b&&b.$p(),d)},a.prototype.dateAxisData=function(a){var b=this,c=b.group();return 0===arguments.length?c.dateAxisData():(c.dateAxisData(a),b)},a.prototype.dateAxisDataSheetName=function(a){var b=this,c=b.group();return 0===arguments.length?c.dateAxisDataSheetName():(c.dateAxisDataSheetName(a),b)},a.prototype.dateAxisOrientation=function(a){var b=this,c=b.group();return 0===arguments.length?c.dateAxisOrientation():(c.dateAxisOrientation(a),b)},a.prototype.displayDateAxis=function(a){var b,c,d=this,e=d.group();return 0===arguments.length?e.displayDateAxis:(b=d.bT(),b&&b.ITa.zTa&&(c={row:d.row,col:d.column,displayDateAxis:e.displayDateAxis},b.ITa.FUa(7,c)),e.displayDateAxis=a,d)},a.prototype.clone=function(){var b=this,c=new a;return c.row=b.row,c.column=b.column,c.data(b.data()),c.dataSheetName(b.dataSheetName()),c.dataOrientation(b.dataOrientation()),c.group(b.group().clone()),c},a.prototype.paintSparkline=function(a,b,c,e,f){var g=this,h=g.bT(),i=h.parent.getSheetFromName(g.dataSheetName()),j=h.parent.getSheetFromName(g.dateAxisDataSheetName()),k={sparklineType:g.sparklineType(),displayDateAxis:g.displayDateAxis(),zoomFactor:h.zoom(),values:g.$S(g.data(),g.dataOrientation(),!1,i||h),dateValues:g.$S(g.dateAxisData(),g.dateAxisOrientation(),!0,j||h),settings:g.setting(),getColor:function(a){if(h&&a){var b=d.Rm.Om(h,a);if(b)return b}return a}};(new p).paint(a,k,b,c,e,f)},a.prototype.$S=function(b,c,d,e){var f=this,g=f.setting().options.displayHidden;return a.cT(r,e,g,b,c,d)},a.prototype.bT=function(){var a=this.group(),b=a&&a.ZA;return b?b.kj:q},a.prototype.toJSON=function(){var a=this;return{row:a.row,col:a.column,orientation:a._S,data:{row:a.aT.row,rowCount:a.aT.rowCount,col:a.aT.col,colCount:a.aT.colCount,sheetName:a.oZa}}},a.prototype.fromJSON=function(a){var b,c,d,e,f,g;a&&(b=this,c=a.row,d=a.col,e=a.orientation,f=a.data,j(c)||(b.row=c),j(d)||(b.column=d),j(e)||(b._S=e),f&&(g=f,b.aT=i(g.row,g.col,g.rowCount,g.colCount),b.oZa=g.sheetName))},a.cT=function(a,b,c,d,g,h){var i,k,l,m,n,p="number",q=[],r=1===g;if(b&&d)for(i=0,k=r?d.colCount:d.rowCount;i<k;i++)l=r?0:i,m=r?i:0,n=a?a[l][m]:b.getValue(d.row+l,d.col+m),n instanceof f.CalcError||(!c&&(b.getRowHeight(d.row+l)<=0||b.getColumnWidth(d.col+m)<=0)?n=NaN:j(n)||(h?n=typeof n===p?e.Common.l.Xb(n):Date.parse(n):typeof n!==p&&(n=o)),q.push(n));return q},a}(),b.Sparkline=G},CalcEngine:function(a,b){a.exports=GC.Spread.CalcEngine},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets},SheetsCalc:function(a,b){a.exports=GC.Spread.Sheets.CalcEngine}});