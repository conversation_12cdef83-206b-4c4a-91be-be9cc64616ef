/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.OutlineColumn=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/outlineColumn/outlineColumn.entry.js")}({"./dist/plugins/outlineColumn/outlineColumn-actions.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),b.commands=e.Commands,f=e.Commands.ActionBase,g=d.Common.j.Fa,h="increaseCellIndent",i="decreaseCellIndent",j="updateOutlineColumnCheckStatus",k="outlineColumnCheckStatus",b.outlineColumnCheckStatus=k,l=e.Commands.h4;function q(a,b,c,d){return s(a,b,c,d)}function r(a,b,c,d){a.Wq(e.Events.OutlineColumnCheckStatusChanged,{sheet:a,sheetName:a.name(),row:b,col:c,status:d})}function s(a,b,c,d){var e,f,h,i,j,k,l,m,n,o,p=g(d)?a.getActiveColumnIndex():d;if(!a.outlineColumn.XQa(p))return null;if(e=b?1:-1,f=a.getActiveRowIndex(),h=a.getSelections()[0].rowCount,j=!0,g(c))for(c={},j=!1,i=f;i<h+f;i++)0!==i&&(k=a.rowOutlines.getLevel(i),b&&(l=a.rowOutlines.getLevel(i-1),k>l&&!c[i-1])||(m=k+e,n=a.outlineColumn.options(),o=g(n.maxLevel)?a.outlineColumn.Oia().maxLevel:n.maxLevel,o&&m+1>o||m+1<0||(c[i]={row:i,original:k+1,current:m+1},j=!0)));return j?c:null}function t(a,b,c,d){var e,f,h,i=s(a,b,c,d);if(!i)return null;e=g(d)?a.getActiveColumnIndex():d,a.outlineColumn.Lia(),a.suspendPaint();for(f in i)Object.prototype.hasOwnProperty.call(i,f)&&(h=i[f],a.getCell(h.row,e).textIndent(h.current));a.resumePaint(),a.outlineColumn.Mia()}function u(a,b,c,d){var e,f,g,h,i,j=0,k=0,l=a.rowOutlines.getLevel(b);if(a.outlineColumn.Kia.updateCheckStatus(b,d),f=b<=a.getRowCount()-2?a.rowOutlines.find(b+1,l+1):null,null!==f&&null!==d)for(g=f.start;g<f.end+1;g++)a.outlineColumn.Kia.updateCheckStatus(g,d);for(f=a.rowOutlines.find(b,l);null!==f&&null!==f.parent;){if(f.start>0){for(j=f.start-1,k=a.rowOutlines.getLevel(j),a.outlineColumn.Kia.updateCheckStatus(j,d),e=a.rowOutlines.find(j+1,k+1),g=e.start;g<e.end+1;g++)if(h=a.outlineColumn.Kia.QR[g].checkBox.checkStatus,h!==d){i="number"==typeof h&&"number"==typeof d&&3,a.outlineColumn.Kia.updateCheckStatus(j,i);break}g===e.end+1&&d===!0&&a.outlineColumn.Kia.updateCheckStatus(j,!0)}f=f.parent}}b.setCheckStatus=u;function v(a){var b=a.kj,c=e.Commands.bWa(b.name()),d=a.VQ[c];b.ITa.undo(d)}m=function(a){p(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!!q(this.kj,!0,this.VQ.changingRows,this.VQ.changingColumns)},b.prototype.canUndo=function(){return!0},b.prototype.execute=function(){var a,b;return!!this.canExecute()&&(a=this.kj,a.ITa.startTransaction(),a.outlineColumn.Kia.JVa(),t(this.kj,!0,this.VQ.changingRows,this.VQ.changingColumns),b=e.Commands.bWa(a.name()),this.VQ[b]=a.ITa.endTransaction(),!0)},b.prototype.undo=function(){var a=this,b=a.kj,c=e.Commands.bWa(b.name()),d=a.VQ[c];return b.ITa.undo(d),!0},b}(f),n=function(a){p(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!!q(this.kj,!1,this.VQ.changingRows,this.VQ.changingColumns)},b.prototype.canUndo=function(){return!0},b.prototype.execute=function(){var a,b;return!!this.canExecute()&&(a=this.kj,a.ITa.startTransaction(),a.outlineColumn.Kia.JVa(),t(a,!1,this.VQ.changingRows,this.VQ.changingColumns),b=e.Commands.bWa(a.name()),this.VQ[b]=a.ITa.endTransaction(),!0)},b.prototype.undo=function(){return v(this),!0},b}(f),o=function(a){p(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!0},b.prototype.canUndo=function(){return!0},b.prototype.execute=function(){var a,b=this,c=b.kj,d=b.VQ,f=d.row,g=d.col,h=d.status;return c.ITa.startTransaction(),u(c,f,g,h),c.outlineColumn.refresh(),a=e.Commands.bWa(c.name()),d[a]=c.ITa.endTransaction(),r(c,f,g,h),!0},b.prototype.undo=function(){return v(this),this.kj.outlineColumn.refresh(),!0},b}(f),e.Commands[h]={canUndo:!0,execute:function(a,b,c){return b.cmd=h,l(a,m,b,c)}},e.Commands[i]={canUndo:!0,execute:function(a,b,c){return b.cmd=i,l(a,n,b,c)}},e.Commands[j]={canUndo:!0,execute:function(a,b,c){return b.cmd=j,l(a,o,b,c)}},e.Commands.$Qa=function(a){var b=e.Ul.sl(),c=!b,d=b;a.register(h,e.Commands[h],221,c,!1,!0,d),a.register(i,e.Commands[i],219,c,!1,!0,d),a.register(j,e.Commands[j])}},"./dist/plugins/outlineColumn/outlineColumn.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/outlineColumn/outlineColumn.js")),d(c("./dist/plugins/outlineColumn/outlineColumn-actions.js"))},"./dist/plugins/outlineColumn/outlineColumn.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("./dist/plugins/outlineColumn/outlineColumn-actions.js"),g=e.Common.k,h=g.ac,i=d.GC$.each,j=d.GC$.isEmptyObject,k=e.Common.j.Fa,l=5,m=6,n=6,o=3,p=16,q=16,r=16,s=16,t=void 0,u="number",v={columnIndex:t,showCheckBox:!1,showImage:!1,images:t,showIndicator:!0,expandIndicator:t,collapseIndicator:t,maxLevel:10};function B(a){return!(!a.expandIndicator&&!a.collapseIndicator)}function C(a,b){return a.outlineColumn&&a.outlineColumn.options()&&a.outlineColumn.options().columnIndex===b}function D(a,b,c){var d=b.x+c.x,e=c.y+c.height/2-b.height/2,f=b.height,g=b.width;c.x+c.width<d+g||(a.save(),a.beginPath(),a.strokeStyle="black",a.strokeRect(d,e,g,f),a.fillStyle="white",a.fillRect(d,e,g,f),3===b.checkStatus?(a.beginPath(),a.fillStyle="black",a.rect(d+g/5,e+f/5,g-.4*g,f-.4*f),a.fill()):b.checkStatus!==!0&&1!==b.checkStatus||(a.beginPath(),a.lineWidth=g/5,a.moveTo(d+g/6,e+f/2),a.lineTo(d+2*g/5,e+5/7*f),a.lineTo(d+5/6*g,e+f/6),a.stroke()),a.restore())}function E(a,b,c,d){var e=b.collapsed,f=b.x+c.x,g=c.y+c.height/2-b.height/2,h=b.height,i=b.width;c.x+c.width<f+i||(e?b.expandIndicator?F(a,b.expandIndicator,b,c,d):(a.save(),a.fillStyle="black",a.beginPath(),a.moveTo(f,g),a.lineTo(f,g+h),a.lineTo(f+i,g+h/2),a.fill(),a.restore()):b.collapseIndicator?F(a,b.collapseIndicator,b,c,d):(a.save(),a.fillStyle="black",a.beginPath(),a.moveTo(f,g+h),a.lineTo(f+i,g+h),a.lineTo(f+i,g),a.fill(),a.restore()))}function F(a,b,c,d,e){var f,g=c.x+d.x,h=d.y+d.height/2-c.height/2,i=c.width,j=c.height;if(!(d.x+d.width<g+i)&&b&&"none"!==b&&e)try{e.ko(b)?(f=e.lo(b),a.drawImage(f,g,h,i,j)):e.fo(b)}catch(a){}}function G(a,b,c){var d,e=a.rowOutlines.getLevel(b);k(c)&&(c=!a.rowOutlines.getCollapsed(b)),d={cmd:"expandRowOutline",collapsed:c,index:b,level:e+1,sheetName:a.name()},a.wu().execute(d)}function H(a,b,c,d){var e,f;k(d)&&(e=a.outlineColumn.getCheckStatus(b),d=typeof e===u?1===e?2:1:!e),f={cmd:"updateOutlineColumnCheckStatus",row:b,col:c,status:d,sheetName:a.name()},a.wu().execute(f)}function I(a,b,c){var d,e,f,g=a.getLevel(b);if(c!==g+1)for(d=c-(g+1),e=0,f=Math.abs(d);e<f;e++)d>0?a.group(b,1):a.ungroupRange(b,1)}function J(a,b,c,d,e,f,g){var h,i,j,k,n,p,q,t,u=K(c,"showIndicator");u?(h=a.zoom(),i=a.getRowCount(),j=!!a.rowOutlines.isCollapsed(b+1),k=l*h,n=m*h,p=null,q=null,c.expandIndicator&&(p=c.expandIndicator,j&&(k=r*h,n=s*h)),c.collapseIndicator&&(q=c.collapseIndicator,j||(k=r*h,n=s*h)),b<i-1&&(t=a.getStyle(b+1,d),t&&t.textIndent>e?g.indicator={x:f.value+o*h,y:-n/2,width:k,height:n,collapsed:j,collapseIndicator:q,expandIndicator:p}:delete g.indicator),f.value+=2*o*h+k):delete g.indicator}function K(a,b){return k(a[b])?v[b]:a[b]}function L(a,b){for(var c,d,e,f,g,h,i,j=a.rowOutlines,k=a.outlineColumn.Kia,l=j.getLevel(b),m=j.find(b,l),n=0,o=0,p=!1;null!==m&&null!==m.parent;){if(d=m.start,d>0)for(n=d-1,o=j.getLevel(n),k.updateCheckStatus(n,p),c=j.find(n+1,o+1),e=c.start;e<c.end+1;e++)if(f=k.QR[e],g=f&&f.checkBox,f&&g&&(h=g.checkStatus,h!==p)){i=typeof h===u&&typeof p===u&&3,k.updateCheckStatus(n,i);break}m=m.parent}}function M(a,b,c,d,e,f){var g,h,i=k(c.showCheckBox)?v.showCheckBox:c.showCheckBox;i?(g=!k(f.checkBox)&&1!==f.checkBox&&f.checkBox.checkStatus,h=a.zoom(),f.checkBox={x:e.value+o*h,y:-n*h,width:2*n*h,height:2*n*h,checkStatus:g},g===!1&&L(a,b),e.value+=2*o*h+2*n*h):delete f.checkBox}function N(a,b,c,d,e){var f,g,h,i,j,l=k(c.showImage)?v.showImage:c.showImage;l?(f=c.images,g=a.zoom(),f&&f.length&&(h=b>f.length-1?f[f.length-1]:f[b],i=d.value+o*g,j=0,e.images={image:h,x:i,y:j,width:p*g,height:q*g},d.value+=2*o*g+p*g)):delete e.images}function O(a,b,c){var d,e,f,g,h=a.outlineColumn.options(),i=h&&h.columnIndex;if(!k(i))return d={value:0},e=0,f=a.getStyle(b,i),f&&f.textIndent&&(e=f.textIndent),0!==b&&(g=a.rowOutlines.getLevel(b-1),e-g>=3&&(e=g+2)),d.value+=8*e,I(a.rowOutlines,b,e),J(a,b,h,i,e,d,c),M(a,b,h,i,d,c),N(a,e,h,d,c),c.cellContent={left:d.value,x:d.value,y:0,width:-d.value,height:0},c}w=function(){function a(a){this.kj=a,this.YQa={},this.$e=0}return a.prototype.LRa=function(a,b){return this.ZQa()?this.Kia.QR[a].cellContent.left:null},a.prototype.options=function(a){var b=this,c=b.YQa;return h(arguments)?(b.YQa=a,b.refresh(),b):c},a.prototype.refresh=function(){var a=this.kj;k(this.YQa)||k(this.YQa.columnIndex)||0===this.$e&&(a.suspendPaint(),a.outlineColumn.Kia.updateModel(),a.resumePaint())},a.prototype.setCheckStatus=function(a,b){var c=this.YQa.columnIndex;k(c)||H(this.kj,a,c,b)},a.prototype.Wlb=function(a){this.kj.outlineColumn.Kia.setAllTiemCheckStatus(a),this.refresh()},a.prototype.getCheckStatus=function(a){var b,c;if(h(arguments))return this.kj.outlineColumn.Kia.getCheckStatus(a);for(b=[],c=this.kj.getRowCount(),a=0;a<c;a++)b[a]=this.kj.outlineColumn.Kia.getCheckStatus(a);return b},a.prototype.setCollapsed=function(a,b){G(this.kj,a,b)},a.prototype.getCollapsed=function(a){var b,c;if(h(arguments))return this.kj.outlineColumn.Kia.getCollapsed(a);for(b=[],c=this.kj.getRowCount(),a=0;a<c;a++)b[a]=this.kj.outlineColumn.Kia.getCollapsed(a);return b},a.prototype.XQa=function(a){return this.YQa&&this.YQa.columnIndex===a},a.prototype.ZQa=function(){return this.YQa&&!k(this.YQa.columnIndex)},a.prototype.Lia=function(){this.$e++},a.prototype.Mia=function(){this.$e>0&&this.$e--,0===this.$e&&this.refresh()},a.prototype.Nia=function(a,b){this.kj.outlineColumn.Kia.insertRows(a,b)},a.prototype.Oia=function(){return v},a.prototype.Pia=function(){return{TRIANGLE_HEIGHT:l,TRIANGLE_BASE:m,CHECKBOX_RADIUS:n,GAP:o,IMAGE_WIDTH:p,IMAGE_HEIGHT:q,INDICATOR_IMAGE_WIDTH:r,INDICATOR_IMAGE_HEIGHT:s}},a.prototype.Gka=function(a){var b,c,d=this;for(b in a)a.hasOwnProperty(b)&&(c=d.Kia.QR[b]=d.Kia.QR[b]||{},c.checkBox={checkStatus:a[b].checked})},a.prototype.CXa=function(a,b){var c=this.YQa.columnIndex;k(c)||f.setCheckStatus(this.kj,a,c,b)},a.prototype.WZa=function(){return this.Kia.QR},a}(),b.OutlineColumn=w,x=function(){function a(a){var b=this;b.kj=a,b.QR={}}return a.prototype.updateIndicatorCollapsed=function(a,b){var c=this;k(c.QR[a])||k(c.QR[a].indicator)||(c.QR[a].indicator.collapsed=b)},a.prototype.updateCheckStatus=function(a,b){var c=this;c.JVa(a),k(c.QR[a])||(c.QR[a].checkBox.checkStatus=b)},a.prototype.setAllTiemCheckStatus=function(a){var b,c=this,d=c.QR;for(b in d)d.hasOwnProperty(b)&&(d[b].checkBox.checkStatus=a)},a.prototype.getCheckStatus=function(a){var b=this;return k(b.QR[a])||k(b.QR[a].checkBox)?null:b.QR[a].checkBox.checkStatus},a.prototype.getCollapsed=function(a){var b=this;return!k(b.QR[a])&&!k(b.QR[a].indicator)&&b.QR[a].indicator.collapsed},a.prototype.updateModel=function(){var a,b=this,c=b.kj,d=c.getRowCount();for(c.rowOutlines.direction(0),a=0;a<d;a++)b.QR[a]=b.QR[a]||{},O(c,a,b.QR[a])},a.prototype.insertRows=function(a,b){var c,d,e,f=this.kj,g=f.outlineColumn.options();k(g)||(c=g.columnIndex,k(c)||(d=f.getStyle(a-1,c),e=0,d&&d.textIndent&&(e=d.textIndent),f.getRange(a,c,b,1).textIndent(e)))},a.prototype.KVa=function(a,b){var c,d=this.QR[b];d&&!a[b]&&(c=d.checkBox,a[b]={checked:c&&c.checkStatus})},a.prototype.LVa=function(a){var b,c=this,d=this.QR;for(b in d)d.hasOwnProperty(b)&&c.KVa(a,b)},a.prototype.MVa=function(a){var b,c=this.QR;for(b in a)k(a[b].checked)||(c[b].checkBox.checkStatus=a[b].checked)},a.prototype.NVa=function(){var a=this.kj.ITa.zTa;return a?(a.OVa||(a.OVa={items:{}}),a.OVa):t},a.prototype.JVa=function(a){var b=this,c=b.NVa();c&&!c.all&&(k(a)?(c.all=!0,b.LVa(c.items)):b.KVa(c.items,a))},a.prototype.PVa=function(a){this.MVa(a.items)},a}(),d.GC$.extend(d.lUa.prototype,{PVa:function(a){var b=this,c=b.QVa,d=c.Kia;c.ZQa()&&(d.updateModel(),a&&d.PVa(a),b.kj.$p())}}),d.lUa.$n("outlineColumn",{init:function(){var a=this,b=a.kj;a.QVa=new w(b),a.QVa.Kia=new x(b)},undo:function(a){var b=a.OVa;b&&this.PVa(b)}}),y={init:function(){var a=this;a.outlineColumn=a.ITa.QVa},toJson:function(a){var b,c,d,e,f,g,h,l;function m(a,b){var c=v[a];return c===b}if(b=this.outlineColumn,c=b.YQa,d=b.Kia.QR,!k(c)){if(e={},i(v,function(a){f=c[a],k(f)||m(a,f)||(e[a]=f)}),g={},c.showCheckBox)for(h in d)d.hasOwnProperty(h)&&(l=d[h].checkBox.checkStatus,l&&(g[h]={checked:l}));j(g)||(e.data=g),a.outlineColumnOptions=e}},fromJson:function(a){var b,c,d,e=this.outlineColumn,f=a.outlineColumnOptions,g=this;if(f){if(f.kj=g,b=f.data,c={},b){for(d in f)"data"!==d&&(c[d]=f[d]);e.Gka(b)}e.options(k(c)?f:c),e.refresh()}},onLayoutChanged:function(a){var b,c,d=this,e=d.outlineColumn,f=a.changeType,g=a.rowCount,h=a.row;if("addRows"===f)e.Kia.JVa(),e.Nia(h,g),e.refresh();else if("deleteRows"===f)e.Kia.JVa(),e.refresh();else if("zoomSheet"===f)e.refresh();else if("invalidateLayout"===f&&e.Kia.QR&&!j(e.Kia.QR)&&d.rowOutlines.items&&!j(d.rowOutlines.items)){b=d.rowOutlines.items;for(c in b)b.hasOwnProperty(c)&&e.Kia.updateIndicatorCollapsed(parseInt(c,10),d.rowOutlines.isCollapsed(parseInt(c,10)+1))}},sortRangeChanged:function(a){for(var b=this.outlineColumn,c=a.column,d=a.columnCount,e=c;b.XQa(e)&&e<=c+d;)b.refresh(),e+=1}},d.Worksheet.$n("outlineColumn",y),z={paintCellPadding:function(a){var b,c=a.options.rect,d=a.options.context.sheet,e=a.options.context,f=e.row,g=e.col,h=a.ctx;C(d,g)&&c.width>0&&c.height>0&&(b=d.outlineColumn.Kia.QR[f],b&&(h.save(),b.indicator&&E(h,b.indicator,c,e.imageLoader),b.checkBox&&D(h,b.checkBox,c),b.images&&F(h,b.images.image,b.images,c,e.imageLoader),h.restore(),b.cellContent&&(c.x+=b.cellContent.left,c.width-=b.cellContent.left)))},getCellPaddingRect:function(a){var b,c=a.options.rect,d=a.options.context.sheet,e=a.options.context,f=e.row,g=e.col;C(d,g)&&c.width>0&&c.height>0&&(b=d.outlineColumn.Kia.QR[f],b&&b.cellContent&&(c.x+=b.cellContent.left,c.width-=b.cellContent.left))},getCellPaddingHitInfo:function(a){var b,c,d,e,f=a.context.sheet,g=a.context.col,h=a.context.row,i=a.x,j=a.y,k=a.cellRect,l=f.outlineColumn;if(a.paddingHitInfo=null,l&&l.YQa){for(b=l.Kia.QR[h],c=l.YQa.columnIndex,k=k.clone();g>c;)g--,k.x-=f.getColumnWidth(g);b&&(d=void 0,e=void 0,b.indicator&&(d=b.indicator.x,e=b.indicator.x+k.x,i>=e&&i<e+b.indicator.width&&(a.paddingHitInfo={x:i,y:j,row:h,col:g,outlineColumnHitInfo:{indicator:!0}})),b.checkBox&&(d||(d=b.checkBox.x),e=b.checkBox.x+k.x,i>=e&&i<e+b.checkBox.width&&(a.paddingHitInfo={x:i,y:j,row:h,col:g,outlineColumnHitInfo:{checkBox:!0}})),b.images&&(d||(d=b.images.x),e=b.images.x+k.x,i>=e&&i<e+b.images.width&&(a.paddingHitInfo={x:i,y:j,row:h,col:g,outlineColumnHitInfo:{image:!0}})),d&&i>=k.x&&i<k.x+d&&(a.paddingHitInfo={x:i,y:j,row:h,col:g,outlineColumnHitInfo:{blank:!0}}))}},processMouseDownOnCellPadding:function(a){var b=a.sheet,c=a.row,d=a.col,e=a.outlineColumnHitInfo;k(e)||(e.indicator?G(b,c):e.checkBox?H(b,c,d):e.image||e.blank)},getOutlineColumnOffset:function(a){var b,c=a.context.sheet,d=a.context.col;C(c,d)&&(b=c.outlineColumn.options(),K(b,"showIndicator")&&(a.value+=o,B(b)?a.value+=r:a.value+=l,a.value+=o),K(b,"showCheckBox")&&(a.value+=o,a.value+=2*n,a.value+=o),K(b,"showImage")&&(a.value+=o,a.value+=p,a.value+=o))}},d.CellTypes.Base.$n("outlineColumn",z),A={init:function(){f.commands.$Qa(this.commandManager())}},d.Workbook.$n("outlineColumn",A)},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});