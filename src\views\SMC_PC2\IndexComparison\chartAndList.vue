<!--
 * @Description: 图表和列表
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-30 14:02:22
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-12-27 09:14:14
-->
<template>
  <div class="_rightBox">
    <div class="_top">
      <div ref="kpiComplate"></div>
      <div style="position: relative;">
        <div ref="kpiCompare" style="width: 100%;height: 100%;"></div>
        <a-select
          @change="rightChartSelectChange"
          v-model="rightChartSelect"
          :disabled="disabledBaseSelect"
          style="position: absolute;right: 20px;top: 8px;width: 130px;"
        >
          <a-select-option
            :value="item.key"
            v-for="item in selectList"
            :key="item.key"
            :title="item.value"
            >{{ item.value }}</a-select-option
          >
        </a-select>
      </div>
    </div>
    <div class="_bottom">
      <CardList
        activePlate="全部"
        :cardListLoading="cardListLoading"
        :list="cardList"
        pageClass="indexComparison2"
        :frequency="this.frequency"
        :indexDt="this.indexDt"
      />
    </div>
  </div>
</template>
<script>
import CardList from "../Card/cardList.vue";
import * as echarts from "echarts";
import request from "@/utils/requestHttp";
import cloneDeep from "lodash/cloneDeep";
import sortBy from "lodash/sortBy";
import { dealThousandData } from "../utils";
import Decimal from "decimal.js";
export default {
  props: {
    searchForm: Object,
    cardList: Array,
    wdList: Array,
    cardListLoading: Boolean,
    disabledBaseSelect: Boolean, // 是否指标id为空则，禁用图表2右上角下拉框
    frequency: String,
    indexDt: String,
    selectList: Array
  },
  inject: ["timeMap", "signOrgId"],
  components: { CardList },
  computed: {
    dimension() {
      return this.searchForm.dimension || "orgDimension";
    }
  },
  data() {
    return {
      kpiComplateChart: null,
      kpiCompareChart: null,
      kpiComplateChartOptions: {
        title: {
          text: "完成情况",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["实际", "目标", "完成率"],
          top: 19,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisPointer: {
              type: "shadow"
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: "{value}"
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            }
          },
          {
            type: "value",
            axisLabel: {
              formatter: "{value} %"
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            }
          }
        ],
        series: [
          {
            name: "实际",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(98, 91, 249, 0.85)"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          },
          {
            name: "目标",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(21, 219, 195, 0.85)"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: 1,
            data: [],
            symbol: "none",
            itemStyle: {
              color: "rgba(214, 116, 255, 1)"
            }
          }
        ]
      },
      cloneKpiComplateChartOptions: {},
      kpiCompareChartOptions: {
        title: {
          text: "同期&实际走势",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["同期", "实际", "目标"],
          top: 19,
          right: 166,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          }
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            xAxisIndex: [0],
            height: 16
          },
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            yAxisIndex: [0],
            width: 16,
            orient: "vertical"
          }
        ],
        yAxis: {
          type: "value",
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: "{value}"
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          }
        },
        series: [
          {
            name: "同期",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(141, 180, 226)"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          },
          {
            name: "实际",
            type: "line",
            data: [],
            itemStyle: {
              color: "#00AAA6"
            },
            label: {
              show: true,
              position: "top"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          },
          {
            name: "目标",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(255, 192, 0)"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          }
        ]
      },
      cloneKpiCompareChartOptions: {},
      rightChartSelect: "", // 右侧图表的基地
      requestBody: {} // 图表请求数据
    };
  },
  mounted() {
    this.kpiComplateChart = echarts.init(this.$refs["kpiComplate"]);
    this.kpiCompareChart = echarts.init(this.$refs["kpiCompare"]);
    this.cloneKpiComplateChartOptions = cloneDeep(this.kpiComplateChartOptions);
    this.cloneKpiCompareChartOptions = cloneDeep(this.kpiCompareChartOptions);
    window.onresize = () => {
      this.kpiComplateChart.resize();
      this.kpiCompareChart.resize();
    };
  },
  watch: {
    // 右侧下拉框改变
    selectList(val) {
      if (val.length) {
        this.rightChartSelectChange(val[0].key);
      } else {
        this.rightChartSelect = "";
        this.kpiCompareChartOptions = cloneDeep(
          this.cloneKpiCompareChartOptions
        );
        this.initKpiCompare();
      }
    },
    // 监听卡片列表改变设置左侧图表数据
    cardList: {
      handler() {
        this.setLeftChartData();
      },
      deep: true
    }
  },
  methods: {
    setLeftChartData() {
      if (!this.cardList.length) {
        this.kpiComplateChartOptions = cloneDeep(
          this.cloneKpiComplateChartOptions
        );
        this.initKpiComplate();
        return;
      }
      let res = this.cardList;
      res = sortBy(res, item => item.targetCompletionRate || "0");
      res.reverse();
      const xAxis = res.map(item => {
        const title =
          this.dimension === "orgDimension"
            ? `${item.wdInCardName ? item.wdInCardName + " - " : ""}${
                item.displayIndexName
              }${
                item.wdInCardTag.length
                  ? "(" + item.wdInCardTag.join(",") + ")"
                  : ""
              }`
            : item.org;
        return title;
      });
      this.kpiComplateChartOptions.xAxis[0].data = xAxis;
      const baseActualData = res.map(item => {
        return dealThousandData(
          item.actualValue,
          item.indexUnitId,
          item.precisions,
          false
        );
      });
      this.kpiComplateChartOptions.series[0].data = baseActualData;
      const targetValueData = res.map(item => {
        return dealThousandData(
          item.targetValue,
          item.indexUnitId,
          item.precisions,
          false
        );
      });
      this.kpiComplateChartOptions.series[1].data = targetValueData;
      const completionRateData = res.map(item => {
        return Decimal(
          item.targetCompletionRate
            ? parseFloat(item.targetCompletionRate)
            : "0"
        ).toFixed(2, Decimal.ROUND_HALF_UP);
      });
      this.kpiComplateChartOptions.series[2].data = completionRateData;
      this.kpiComplateChartOptions.yAxis[0].axisLabel.formatter = `{value} ${
        res[0].indexUnitId === null ? "" : res[0].indexUnitId
      }`;
      this.initKpiComplate();
    },
    // 设置指标名称
    setIndexName(indexName) {
      this.kpiComplateChartOptions.title.text = `${indexName} 完成情况`;
      this.kpiCompareChartOptions.title.text = `${indexName} 同期&实际走势`;
    },
    getChartData(body) {
      return new Promise(resolve => {
        this.requestBody = body;
        request(`/api/smc2/newIndexLibrary/searchTrend`, {
          method: "POST",
          body
        }).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            res = sortBy(res, function(item) {
              return item.indexDt;
            });
            const xAxis = res.map(item => {
              return item.indexDt;
            });
            this.kpiCompareChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map(item => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.series[1].data = baseActualData;
            const targetValueData = res.map(item => {
              return dealThousandData(
                item.targetValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.series[2].data = targetValueData;
            const contemValueData = res.map(item => {
              return dealThousandData(
                item.contemValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.dataZoom[0].end = 100;
            // 根据数据条数 判断是否显示dataZoom组件
            this.kpiCompareChartOptions.dataZoom[0].show = true;
            this.kpiCompareChartOptions.dataZoom[1].show = true;
            this.kpiCompareChartOptions.grid.bottom = 52;

            const yearStart = xAxis.indexOf(`${new Date().getFullYear()}-01`);
            const process =
              yearStart === -1
                ? 0
                : Math.floor((100 / (xAxis.length - 1)) * yearStart);
            this.kpiCompareChartOptions.dataZoom[0].start = process;

            this.kpiCompareChartOptions.series[0].data = contemValueData;
            this.kpiCompareChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].indexUnitId === "null" ? "" : res[0].indexUnitId
            }`;
            this.initKpiCompare();
          } else {
            this.kpiCompareChartOptions = cloneDeep(
              this.cloneKpiCompareChartOptions
            );
            this.initKpiCompare();
          }
          resolve(res);
        });
      });
    },
    // 右侧图表基地改变
    rightChartSelectChange(value) {
      // this.requestBody["type"] = "2";
      // this.requestBody["baseStr"] = value;
      this.rightChartSelect = value;
      const body = {
        type: "1",
        signOrgId: this.signOrgId,
        businessSegmentsId: this.searchForm.indexId.split("-")[1],
        indexId: this.searchForm.indexId.split("-")[2],
        indexDt: this.searchForm.time,
        indexFrequencyId: this.timeMap[this.searchForm.timeType]
      };
      if (this.searchForm.dimension === "orgDimension") {
        body["fullCode"] = this.searchForm.org;
        const originData = this.selectList.filter(
          item => item.key === this.rightChartSelect
        )[0];
        for (let i = 1; i <= 7; i++) {
          body[`productAtt${i}Id`] = originData[`productAtt${i}Id`];
        }
      } else {
        body["fullCode"] = this.rightChartSelect;
        const originData = this.wdList.filter(
          item => item.key === this.searchForm.wd
        )[0];
        for (let i = 1; i <= 7; i++) {
          body[`productAtt${i}Id`] = originData[`productAtt${i}Id`];
        }
      }
      this.getChartData(body);
    },
    // 初始化kpi完成情况图表
    initKpiComplate(options) {
      this.kpiComplateChart.setOption(options || this.kpiComplateChartOptions);
    },
    // 初始化kpi同期情况表
    initKpiCompare(options) {
      this.kpiCompareChart.setOption(options || this.kpiCompareChartOptions);
    }
  }
};
</script>

<style lang="less">
@import url("../common.less");
.indexComparisonPage2 {
  & > ._bottom {
    & > ._right {
      ._rightBox {
        height: 100%;
        overflow-y: auto;
        box-sizing: border-box;
        padding: 16px;
        ._top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          & > div {
            flex: 1;
            height: 281px;
            background: #f4f5f7;
            border-radius: 8px;
            box-sizing: border-box;
            &:first-child {
              margin-right: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
