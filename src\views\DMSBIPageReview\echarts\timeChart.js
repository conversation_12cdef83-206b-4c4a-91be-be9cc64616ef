/*
 * @Author: othniel <EMAIL>
 * @Date: 2025-05-13 16:33:27
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-05-13 20:02:00
 * @FilePath: \pangea-component\src\views\DMSBIPageReview\echarts\timeChart.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import * as echarts from "echarts";

/**
 * 初始化工时损失-停产损失时间图表
 * @param {HTMLElement} el - 图表容器DOM元素
 * @param {boolean} isDarkTheme - 是否为暗色主题
 * @returns {echarts.ECharts} - echarts实例
 */
export const initTimeChart = (el, isDarkTheme = false) => {
  const chart = echarts.init(el);

  // 文本颜色根据主题设置
  const textColor = isDarkTheme ? "#ffffff" : "#333333";

  const option = {
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: isDarkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: isDarkTheme ? "#08343C" : "#fff",
      borderColor: isDarkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: isDarkTheme ? "#FFFFFF" : "#333",
      },
      z: 1, // 设置非常低的z-index值，确保不会覆盖模态框
    },
    legend: {
      data: ["停产时间", "停产次数", "达成率"],
      right: 10,
      top: 0,
      show: false,
      textStyle: {
        color: textColor,
      },
    },
    xAxis: {
      type: "category",
      data: [
        "油泵压力异常停机",
        "电机异常停机",
        "型材异常停机",
        "测试异常停机",
        "外观异常停机",
      ],
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: [
      {
        type: "value",
        // name: "时间(分钟)",
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLabel: {
          color: textColor,
        },
        axisLine: {
          lineStyle: {
            color: textColor,
          },
        },
      },
      {
        type: "value",
        // name: "百分比",
        axisLabel: {
          formatter: "{value}%",
          color: textColor,
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: textColor,
          },
        },
      },
    ],
    series: [
      {
        name: "停产时间",
        type: "bar",
        data: [35, 45, 30, 50, 30],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 40,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "达成率",
        type: "line",
        yAxisIndex: 1,
        smooth: true,
        data: [60, 60, 60, 85, 85],
        itemStyle: {
          color: "#F7B500",
        },
      },
    ],
  };

  chart.setOption(option);
  return chart;
};

export default initTimeChart;
