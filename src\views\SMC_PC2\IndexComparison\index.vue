<!--
 * @Description: 视像核心KPI横比
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 14:26:03
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-09 11:26:24
-->
<template>
  <div
    class="indexComparisonPage2"
    :class="[companyName, form.skinStyle]"
    :ref="`${companyName}indexComparisonPage2`"
  >
    <div class="_top">
      <!-- 页头 -->
      <PageHeader
        style="flex: 1;"
        :companyName="companyName"
        @pageHeaderChange="searchConditionChange"
        @modeChange="modeChange"
        :orgList="orgList"
        :ppOrgList="ppOrgList"
        :wdList="wdList"
        @treeSelect="treeSelect"
        @dimensionChange="dimensionChange"
      />
      <template v-if="signOrgIdInProps">
        <div
          class="close-div"
          @click="$emit('close')"
          style="display: flex;align-items: center;justify-content: center;width: 24px;height: 24px;cursor: pointer;margin-left: 8px;"
        >
          <a-tooltip placement="left">
            <template slot="title">
              <span>返回概览</span>
            </template>
            <a-icon type="close-square" />
          </a-tooltip>
        </div>
      </template>
    </div>
    <!-- 表格模式 -->
    <div
      class="_bottom"
      v-show="searchForm.mode === 'table'"
      style="padding: 16px; overflow-y: auto;"
    >
      <a-spin :spinning="tableDataLoading">
        <div
          class="_flex"
          style="justify-content: flex-end; margin-bottom: 12px;"
        >
          <a-button type="primary" @click="download">
            数据导出
          </a-button>
        </div>
        <a-table
          :pagination="false"
          size="small"
          :columns="columns"
          :data-source="pageSliceTableData"
        >
          <template slot="index" slot-scope="text, record, index">{{
            index + 1
          }}</template>
        </a-table>
        <div style="padding:10px; overflow: hidden">
          <a-pagination
            style="float:right;"
            size="small"
            v-model="pageNum"
            :pageSize="pageSize"
            :total="total"
            :showTotal="
              total =>
                `${showAlias('TOTAL', '共')} ${total} ${showAlias(
                  'ITEMS',
                  '条'
                )}`
            "
            :pageSizeOptions="['10', '20', '50', '100']"
            @change="pageChange"
            @showSizeChange="pageSizeChange"
          />
        </div>
      </a-spin>
    </div>
    <!-- 图表模式 -->
    <div class="_bottom" v-show="searchForm.mode === 'chart'">
      <!-- 左侧 -->
      <div class="_left" id="bottom-left" style="width: 208px;">
        <IndexTree
          ref="indexTree"
          :recommendListLoading="recommendListLoading"
          :companyName="companyName"
          :recommendList="recommendList"
          @clearData="clearData"
          @getRecommendList="getRecommendList"
          :signOrgId="signOrgId"
          :searchForm="searchForm"
          @refreshOrgList="dimensionChange(searchForm.dimension, false)"
        />
      </div>
      <!-- 右侧 -->
      <div class="_right" id="bottom-right" style="width: calc(100% - 210px);">
        <chart-and-list
          :searchForm="searchForm"
          :wdList="wdList"
          :selectList="rightChartSelectList"
          ref="chartAndList"
          :cardListLoading="cardListLoading"
          :cardList="cardList"
          :disabledBaseSelect="!searchForm.indexId"
          :frequency="
            this.searchForm.timeType === 'day'
              ? '日'
              : this.searchForm.timeType === 'week'
              ? '周'
              : '月'
          "
          :indexDt="this.searchForm.time"
        ></chart-and-list>
      </div>
    </div>
  </div>
</template>
<script>
import PageHeader from "./pageHeader.vue";
import IndexTree from "./indexTree.vue";
import request from "@/utils/requestHttp";
import moment from "moment";
import ChartAndList from "./chartAndList.vue";
import cloneDeep from "lodash/cloneDeep";
import { adminUserUrlPrefix } from "@/utils/utils";
import { showAlias } from "@/utils/intl.js";
import { dealThousandData } from "../utils";
import { pureAxios } from "@/utils/requestHttp";
import Decimal from "decimal.js";
export default {
  components: { PageHeader, IndexTree, ChartAndList },
  name: "IndexComparison",
  data() {
    return {
      businessSegmentsColorMap: {}, // 定义版块颜色map
      showAlias,
      orgList: [], // 组织列表
      ppOrgList: [], // 平铺组织列表
      wdList: [], // 维度列表
      rightChartSelectList: [], // 右侧echarts下拉框列表
      searchForm: {
        mode: "chart"
      },
      form: {
        skinStyle: "classic-style"
      },
      timeMap: {
        month: "M",
        week: "W",
        day: "D"
      },
      cardList: [], // 卡片列表
      cardListLoading: true, // 卡片列表Loading
      recommendList: [], // 推荐列表
      recommendListLoading: false, // 推荐Loading
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "index" }
        },
        {
          title: "指标名称",
          dataIndex: "indexName",
          key: "indexName"
        },
        {
          title: "业务版块",
          dataIndex: "businessSegments",
          key: "businessSegments"
        },
        {
          title: "公司",
          key: "company",
          dataIndex: "company"
        },
        {
          title: "组织",
          dataIndex: "base",
          key: "base"
        },
        {
          title: "实际值",
          dataIndex: "baseActual",
          key: "baseActual"
        },
        {
          title: "实际分子",
          dataIndex: "actualMolecule",
          key: "actualMolecule"
        },
        {
          title: "实际分母",
          dataIndex: "actualDenominator",
          key: "actualDenominator"
        },
        {
          title: "单位",
          dataIndex: "unit",
          key: "unit"
        },
        {
          title: "目标值",
          dataIndex: "targetValue",
          key: "targetValue"
        },
        {
          title: "完成率",
          dataIndex: "completionRate",
          key: "completionRate"
        },
        {
          title: "同期值",
          dataIndex: "contemValue",
          key: "contemValue"
        },
        {
          title: "同比",
          dataIndex: "contemRate",
          key: "contemRate"
        },
        {
          title: "上期值",
          dataIndex: "previousValue",
          key: "previousValue"
        },
        {
          title: "环比",
          dataIndex: "previousRate",
          key: "previousRate"
        },
        {
          title: "频次",
          dataIndex: "frequency",
          key: "frequency"
        },
        {
          title: "指标时间",
          dataIndex: "indexDt",
          key: "indexDt"
        },
        {
          title: "指标类型",
          dataIndex: "indexType",
          key: "indexType"
        }
      ],
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      tableDataLoading: false,
      SYS_NAME: window.system
    };
  },
  provide() {
    return {
      // 定义版块颜色map
      businessSegmentsColorMap: () => this.businessSegmentsColorMap,
      treeSelect: this.treeSelect,
      isDesign: this.isDesign,
      skinStyle: () => this.form.skinStyle,
      timeMap: this.timeMap,
      signOrgId: this.signOrgId
    };
  },
  props: {
    data: Object,
    signOrgIdInProps: String,
    companyNameInProps: String,
    isDesign: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  computed: {
    // 当前组件属性props对象
    attribute() {
      if (this.data && this.data.props) {
        return this.data.props;
      } else {
        return {};
      }
    },
    // 公司名称
    companyName() {
      return this.companyNameInProps || this.attribute.companyName || "宽带";
    },
    // 注册公司编码
    signOrgId() {
      return this.signOrgIdInProps || this.attribute.signOrgId || "H06";
    },
    // 因为后台分页不好用，前端进行分页
    pageSliceTableData() {
      return this.tableData.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageSize * this.pageNum
      );
    }
  },
  created() {
    /**
     * 横比页面的逻辑是
     * indexTree.vue组件中先获取当前角色拥有的指标树，传入数据到ownTree.vue中，首次获取默认选中第一个指标，当指标树选中数据发生改变后，
     * 通过inject的treeSelect方法调用index.vue的获取组织列表(getOrgList)方法，传入数据到pageHeader组件(A)中
     * A组件中监听orgList后修改searchForm通过$emit("pageHeaderChange")反馈给当前index.vue组件
     * 的searchConditionChange方法来进行卡片列表的请求
     */
    this.getBussinessColosMap();
    this.$nextTick(() => {
      this.getAndSetWindowHeight();
    });
    window.addEventListener("resize", this.getAndSetWindowHeight);
  },
  destroyed() {
    window.removeEventListener("resize");
  },
  methods: {
    // 获取盘古字典中维护的版块颜色
    getBussinessColosMap() {
      request(
        `${decodeURIComponent(
          "/api/system/dict/type/query?types=smc-bkColorMap&languageCode=zh_CN"
        )}`
      ).then(res => {
        const bkColorMap = {};
        res["smc-bkColorMap"].forEach(element => {
          bkColorMap[element.key] = {
            bgColor: element.value.split("~")[0],
            color: element.value.split("~")[1]
          };
        });
        this.businessSegmentsColorMap = bkColorMap;
      });
    },
    getAndSetWindowHeight() {
      let height = "";
      if (window.self === window.top) {
        // 在盘古内部使用
        height = document.getElementsByClassName("100heightDiv")[0]
          ?.offsetHeight;
        height = height ? `${height}px` : "100vh";
        if (this.signOrgIdInProps) {
          height = "100vh";
        }
      } else {
        // 在信数内使用
        height = window.innerHeight + "px";
      }
      this.$refs[`${this.companyName}indexComparisonPage2`].style.setProperty(
        "--realHeight",
        height
      );
    },
    // 分析维度改变
    dimensionChange(dimension, needRequest = true) {
      this.searchForm["dimension"] = dimension;
      if (dimension === "orgDimension") {
        this.$nextTick(() => {
          this.treeSelect();
        });
      } else {
        this.$nextTick(() => {
          this.getWDList();
          // 如果维度列表数据不改变，手动触发以下获取列表
          if (this.searchForm.wd && needRequest) {
            this.searchConditionChange();
          }
        });
      }
    },
    // 最高层级下探3层组织
    delFourthOrg(arr, index = 0) {
      index++;
      arr.forEach(item => {
        this.ppOrgList.push(item);
        if (index > 2) {
          item.list = [];
        } else {
          this.delFourthOrg(item.list, index);
        }
      });
    },
    // 根据指标获取组织
    getOrgList(indexId) {
      request(`/api/smc2/newIndexLibrary/searchDimension`, {
        method: "POST",
        body: {
          sign: `${this.companyName}横比`,
          signOrgId: this.signOrgId,
          indexId: indexId.split("-")[2],
          businessSegmentsId: indexId.split("-")[1]
        }
      })
        .then(res => {
          if (Array.isArray(res["组织机构"]) && res["组织机构"].length === 0) {
            this.cardListLoading = false;
            // 清空右边数据
            this.cardList = [];
            this.recommendList = [];
            this.$refs["chartAndList"].initKpiComplate(
              this.$refs["chartAndList"].cloneKpiComplateChartOptions
            );
            this.$refs["chartAndList"].initKpiCompare(
              this.$refs["chartAndList"].cloneKpiCompareChartOptions
            );
          }
          const orgList = res["组织机构"] || [];
          this.delFourthOrg(orgList);
          this.orgList = orgList;
        })
        .catch(() => {
          this.orgList = [];
        });
    },
    // 翻页
    pageChange(page, pageSize) {
      this.pageNum = page;
      this.pageSize = pageSize;
    },
    // 每页显示条数变化
    pageSizeChange(current, size) {
      this.pageNum = current;
      this.pageSize = size;
    },
    // 获取表格数据
    getTableData() {
      this.tableDataLoading = true;
      let postData = cloneDeep(this.searchForm);
      postData["timeType"] =
        postData.timeType === "day"
          ? "日"
          : postData.timeType === "week"
          ? "周"
          : "月";
      postData.base = postData.base.join();
      request(`${adminUserUrlPrefix["zcx"]}/indexCardInfo/getHBIndexTable`, {
        method: "POST",
        body: postData
      })
        .then(res => {
          this.tableDataLoading = false;
          if (res && res.rows) {
            this.total = res.total;
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item["index"] = index + 1;
            });
          }
        })
        .catch(() => {
          this.tableDataLoading = false;
        });
    },
    // 下载表格
    download() {
      let postData = cloneDeep(this.searchForm);
      postData["timeType"] =
        postData.timeType === "day"
          ? "日"
          : postData.timeType === "week"
          ? "周"
          : "月";
      postData.base = postData.base.join();
      pureAxios({
        url: "/smc/indexCardInfo/exportHBList",
        method: "post",
        data: postData,
        responseType: "blob"
      })
        .then(response => {
          if (!response.data) {
            return;
          }
          const fileName = `${this.companyName}核心KPI横比数据表.xlsx`;
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message
          });
        });
    },
    clearData() {
      this.cardListLoading = false;
      // 清空卡片列表，左右两张图表
      this.cardList = [];
      this.recommendList = [];
      this.orgList = [];
      this.rightChartSelectList = [];
      this.$refs["chartAndList"].initKpiComplate(
        this.$refs["chartAndList"].cloneKpiComplateChartOptions
      );
      this.$refs["chartAndList"].initKpiCompare(
        this.$refs["chartAndList"].cloneKpiCompareChartOptions
      );
    },
    /**
     * 代码生成并下载为zip
     * @param {String} url 链接
     * @param {String} tables 表名
     */
    resolveBlob(res, mimeType) {
      const aLink = document.createElement("a");
      var blob = new Blob([res.data], { type: mimeType });
      var fileName = `${this.companyName}核心KPI横比数据表`;
      aLink.href = window.URL.createObjectURL(blob);
      aLink.setAttribute("download", fileName); // 设置下载文件名称
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(aLink.href);
    },
    modeChange(val) {
      // 表格模式
      this.searchForm.mode = val;
      if (val === "table") {
        this.pageNum = 1;
        this.pageSize = 10;
        this.getTableData();
      }
    },
    // 查询条件改变
    searchConditionChange(data) {
      this.searchForm = { ...this.searchForm, ...data };
      this.searchForm.mode = "chart";
      const timeType = this.searchForm.timeType || "day";
      if (timeType !== "week") {
        this.searchForm.time = moment(this.searchForm.time).format(
          timeType === "day" ? "YYYY-MM-DD" : "YYYY-MM"
        );
      }
      if (this.searchForm.mode === "table") {
        return;
      }
      if (this.searchForm.indexId) {
        this.getRecommendList();
        Promise.all([this.getCardList(), this.getRightChartSelectList()]);
      } else {
        this.getRecommendList();
      }
    },
    // 获取右侧echart数据下拉框数据
    getRightChartSelectList() {
      // this.rightChartSelectList = [];
      const url =
        this.searchForm.dimension === "orgDimension"
          ? "/api/smc2/newIndexLibrary/searchTrendDis"
          : "/api/smc2/newIndexLibrary/searchOrgByDimension";
      const body = {
        signOrgId: this.signOrgId,
        indexId: this.searchForm.indexId.split("-")[2],
        sign: `${this.companyName}横比`,
        businessSegmentsId: this.searchForm.indexId.split("-")[1]
      };
      if (this.searchForm.dimension === "orgDimension") {
        const originData = this.ppOrgList.filter(
          item => item.fullCode === this.searchForm.org
        )[0];
        body["orgId"] = originData ? originData.key : "";
      } else {
        const originData = this.wdList.filter(
          item => item.key === this.searchForm.wd
        )[0];
        for (let i = 1; i <= 7; i++) {
          body[`productAtt${i}Id`] = originData[`productAtt${i}Id`];
        }
      }
      request(url, {
        method: "POST",
        body
      }).then(res => {
        if (Array.isArray(res) && res.length) {
          if (this.searchForm.dimension === "orgDimension") {
            this.rightChartSelectList = res
              .filter(item => item)
              .map(item => {
                const dimension = item.dimension
                  ? item.dimension.endsWith("-")
                    ? item.dimension.substring(0, item.dimension.length - 1)
                    : item.dimension
                  : "";
                return {
                  key: dimension,
                  value: dimension,
                  productAtt1Id: item.productAtt1Id,
                  productAtt2Id: item.productAtt2Id,
                  productAtt3Id: item.productAtt3Id,
                  productAtt4Id: item.productAtt4Id,
                  productAtt5Id: item.productAtt5Id,
                  productAtt6Id: item.productAtt6Id,
                  productAtt7Id: item.productAtt7Id
                };
              });
          } else {
            this.rightChartSelectList = res
              .filter(item => item)
              .map(item => {
                return {
                  key: item.fullCode,
                  value: item.org
                };
              });
          }
        } else {
          this.rightChartSelectList = [];
        }
      });
    },
    // 获取卡片列表
    getCardList() {
      return new Promise(resolve => {
        this.cardListLoading = true;
        const body = {
          indexDt: this.searchForm.time,
          indexId: this.searchForm.indexId.split("-")[2],
          sign: `${this.companyName}横比`,
          indexFrequencyId: this.timeMap[this.searchForm.timeType]
        };
        if (this.searchForm.dimension === "orgDimension") {
          body["orgId"] = this.searchForm.org;
        } else {
          const originData = this.wdList.filter(
            item => item.key === this.searchForm.wd
          )[0];
          for (let i = 1; i <= 7; i++) {
            body[`productAtt${i}Id`] = originData[`productAtt${i}Id`];
          }
        }
        request(`/api/smc2/newIndexLibrary/searchGL`, {
          method: "POST",
          body
        })
          .then(res => {
            this.cardListLoading = false;
            if (Array.isArray(res) && res.length) {
              const list = res.map(item => {
                const {
                  dmId,
                  indexId,
                  indexName,
                  indexDt,
                  fullCode,
                  indexFrequency,
                  indexFrequencyId,
                  org,
                  orgId,
                  businessSegments,
                  businessSegmentsId,
                  signOrgId,
                  signOrg,
                  actualValue,
                  targetValue,
                  targetCompletionRate,
                  previousChangeRate,
                  contemChangeRate,
                  indexUnitId,
                  indexSort,
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4,
                  productAtt1Id,
                  productAtt2Id,
                  productAtt3Id,
                  productAtt4Id,
                  productAtt5Id,
                  productAtt6Id,
                  productAtt7Id,
                  previousValue,
                  contemValue,
                  precisions,
                  indexTypeId,
                  indexNameInd,
                  actualMolecule,
                  actualDenominator,
                  label,
                  cmimId,
                  id,
                  pj // 用于拖拽排序标记
                } = item;
                const normalWDList = [
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4
                ].filter(item => item && !item.includes("指标卡"));
                let wdInCardName = normalWDList
                  .filter(item => item.includes("卡片名称"))
                  .map(item => item.split("-")[2]);
                wdInCardName = wdInCardName.join("-");
                const wdInCardTag = normalWDList
                  .filter(item => item.includes("卡片标签"))
                  .map(item => item.split("-")[2]);
                return {
                  dmId,
                  id,
                  indexId,
                  indexName,
                  indexDt,
                  fullCode,
                  indexFrequency:
                    indexFrequency || this.timeMap[this.searchForm.timeType],
                  indexFrequencyId,
                  org,
                  orgId,
                  businessSegments,
                  businessSegmentsId,
                  signOrgId,
                  signOrg,
                  actualValue: dealThousandData(
                    actualValue,
                    item.indexUnitId,
                    precisions
                  ),
                  actualMolecule: dealThousandData(
                    actualMolecule,
                    item.indexUnitId,
                    precisions
                  ),
                  actualDenominator: dealThousandData(
                    actualDenominator,
                    item.indexUnitId,
                    precisions
                  ),
                  targetValue: dealThousandData(
                    targetValue,
                    item.indexUnitId,
                    precisions
                  ),
                  targetCompletionRate: targetCompletionRate
                    ? `${Decimal(targetCompletionRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)}%`
                    : "",
                  previousChangeRate,
                  isPreviousRate: "Y",
                  isContemRate: "Y",
                  contemChangeRate,
                  indexUnitId,
                  indexType: indexTypeId,
                  indexNameInd,
                  displayIndexName: indexNameInd || indexName,
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4,
                  productAtt1Id,
                  productAtt2Id,
                  productAtt3Id,
                  productAtt4Id,
                  productAtt5Id,
                  productAtt6Id,
                  productAtt7Id,
                  previousValue: dealThousandData(
                    previousValue,
                    item.indexUnitId,
                    precisions
                  ),
                  contemValue: dealThousandData(
                    contemValue,
                    item.indexUnitId,
                    precisions
                  ),
                  indexSort,
                  normalWDList,
                  wdInCardName,
                  wdInCardTag,
                  cmimId,
                  companyName: this.companyName,
                  label,
                  pj,
                  recommend: false,
                  show: true
                  // show: searchTimer <= nowTimer, // 日月周三种时间，搜索条件时间大于当前时间所有指标都隐藏
                  // showYC: searchTimer > nowTimer // 搜索条件时间年月大于当前时间年月，认为所有指标都是预测
                };
              });
              this.cardList = [...list].filter(item => item.show);
            } else {
              this.cardList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.cardListLoading = false;
            this.cardList = [];
            resolve([]);
          });
      });
    },
    // 获取推荐列表数据
    getRecommendList() {
      return new Promise(resolve => {
        this.recommendListLoading = true;
        const body = {
          signOrgId: this.signOrgId,
          sign: `${this.companyName}横比`,
          indexDt: this.searchForm.time,
          indexFrequencyId: this.timeMap[this.searchForm.timeType]
        };
        if (this.searchForm.dimension === "orgDimension") {
          const originData = this.ppOrgList.filter(
            item => item.fullCode === this.searchForm.org
          )[0];
          body["orgId"] = originData ? originData.key : "";
        } else {
          const originData = this.wdList.filter(
            item => item.key === this.searchForm.wd
          )[0];
          for (let i = 1; i <= 7; i++) {
            body[`productAtt${i}Id`] = originData[`productAtt${i}Id`];
          }
        }
        request(`/api/smc2/newIndexLibrary/searchSubscribeable`, {
          method: "POST",
          body
        })
          .then(res => {
            this.recommendListLoading = false;
            if (res && Array.isArray(res)) {
              const list = res.map(item => {
                const {
                  indexId,
                  indexName,
                  indexDt,
                  indexFrequency,
                  indexFrequencyId,
                  org,
                  businessSegments,
                  fullCode,
                  businessSegmentsId,
                  signOrg,
                  signOrgId,
                  actualValue,
                  targetValue,
                  targetCompletionRate,
                  indexUnitId,
                  indexSort,
                  precisions,
                  indexNameInd,
                  label,
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4,
                  cmimId,
                  pj // 用于拖拽排序标记
                } = item;
                const normalWDList = [
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4
                ].filter(item => item && !item.includes("指标卡"));
                let wdInCardName = normalWDList
                  .filter(item => item.includes("卡片名称"))
                  .map(item => item.split("-")[2]);
                wdInCardName = wdInCardName.join("-");
                const wdInCardTag = normalWDList
                  .filter(item => item.includes("卡片标签"))
                  .map(item => item.split("-")[2]);
                return {
                  indexId,
                  indexName,
                  indexDt,
                  indexFrequency: indexFrequency || this.searchForm.timeType,
                  indexFrequencyId,
                  org,
                  businessSegments,
                  businessSegmentsId,
                  signOrg,
                  actualValue: dealThousandData(
                    actualValue,
                    item.indexUnitId,
                    precisions
                  ),
                  targetValue: dealThousandData(
                    targetValue,
                    item.indexUnitId,
                    precisions
                  ),
                  targetCompletionRate: targetCompletionRate
                    ? `${Decimal(targetCompletionRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)}%`
                    : "",
                  indexUnitId,
                  indexNameInd,
                  indexSort,
                  displayIndexName: indexNameInd || indexName,
                  pj,
                  fullCode,
                  signOrgId,
                  wdInCardName,
                  wdInCardTag,
                  label,
                  cmimId,
                  companyName: this.companyName,
                  recommend: true
                };
              });
              this.recommendList = list.splice(0, 3);
            } else {
              this.recommendList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.recommendListLoading = false;
            this.recommendList = [];
            resolve([]);
          });
      });
    },
    // 获取维度列表
    getWDList() {
      request(`/api/smc2/newIndexLibrary/searchTrendDis`, {
        method: "POST",
        body: {
          signOrgId: this.signOrgId,
          indexId: this.searchForm.indexId.split("-")[2],
          sign: `${this.companyName}横比`,
          businessSegmentsId: this.searchForm.indexId.split("-")[1]
        }
      }).then(res => {
        if (Array.isArray(res) && res.length) {
          this.wdList = res
            .filter(item => item)
            .map(item => {
              const dimension = item.dimension
                ? item.dimension.endsWith("-")
                  ? item.dimension.substring(0, item.dimension.length - 1)
                  : item.dimension
                : "";
              return {
                key: dimension,
                value: dimension,
                productAtt1Id: item.productAtt1Id,
                productAtt2Id: item.productAtt2Id,
                productAtt3Id: item.productAtt3Id,
                productAtt4Id: item.productAtt4Id,
                productAtt5Id: item.productAtt5Id,
                productAtt6Id: item.productAtt6Id,
                productAtt7Id: item.productAtt7Id
              };
            });
        }
      });
    },
    // 树形选择
    treeSelect(indexId) {
      const IndexId = indexId || this.$refs["indexTree"].getActiveKey();
      if (IndexId) {
        // this.$refs["chartAndList"].setIndexName(
        //   this.$refs["indexTree"].getIndexName()
        // );
        this.searchForm["indexId"] = IndexId;
        this.getOrgList(IndexId);
      }
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexComparisonPage2 {
  font-size: 14px;
  font-family: PingFangSC-Regular;
  color: rgba(0, 0, 0, 0.65);
  overflow-y: auto;
  height: var(--realHeight);
  // min-width: 1300px;
  overflow-y: hidden;

  ._flex {
    display: flex;
    align-items: center;
  }
  & > ._top {
    box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
    box-sizing: border-box;
    padding: 16px 24px;
    position: relative;
    &::before {
      display: block;
      content: "";
      height: 0;
      width: 0;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
    }
    display: flex;
    align-items: center;
  }
  & > ._bottom {
    background-color: #fff;
    width: 100%;
    height: calc(100% - 64px);
    box-sizing: border-box;
    overflow-x: hidden;
    & > ._left {
      height: 100%;
      // border-right: 1px solid #f0f0f0;
      box-shadow: inset -1px 0 0 0 #f0f0f0;
      float: left;
    }
    & > ._right {
      // flex: 1;
      height: 100%;
      float: left;
    }
  }
}
</style>
