/*
 * @Author: othniel <EMAIL>
 * @Date: 2025-05-13 16:32:39
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-05-15 17:50:10
 * @FilePath: \pangea-component\src\views\DMSBIPageReview\echarts\qualityChart.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import * as echarts from "echarts";

/**
 * 初始化质量图表
 * @param {HTMLElement} el - 图表容器DOM元素
 * @param {boolean} isDarkTheme - 是否为暗色主题
 * @returns {echarts.ECharts} - echarts实例
 */
export const initQualityChart = (el, isDarkTheme = false) => {
  const chart = echarts.init(el);

  const xData = [];
  for (let i = 1; i <= 24; i++) {
    xData.push(`冷藏室${i}号产线`);
  }

  // 文本颜色根据主题设置
  const textColor = isDarkTheme ? "#ffffff" : "#333333";

  const option = {
    grid: {
      left: "2%",
      right: "4%",
      bottom: "3%",
      top: "8%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: isDarkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: isDarkTheme ? "#08343C" : "#fff",
      borderColor: isDarkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: isDarkTheme ? "#FFFFFF" : "#333",
      },
      z: 1, // 设置非常低的z-index值，确保不会覆盖模态框
    },
    xAxis: {
      type: "category",
      data: xData,
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: [
      {
        type: "value",
        // name: "数量",
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLabel: {
          color: textColor,
        },
        axisLine: {
          lineStyle: {
            color: textColor,
          },
        },
      },
    ],
    series: [
      {
        name: "不良数",
        type: "bar",
        data: Array(24)
          .fill(0)
          .map(() => Math.floor(Math.random() * 40) + 10),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 20,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "良品率",
        type: "line",
        smooth: true,
        data: Array(24)
          .fill(0)
          .map(() => Math.floor(Math.random() * 20) + 80),
        itemStyle: {
          color: "#6495F9",
        },
      },
    ],
  };

  chart.setOption(option);
  return chart;
};

export default initQualityChart;
