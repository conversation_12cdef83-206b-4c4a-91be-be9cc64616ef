此暗色主题修改了以下组件：

1. 基础容器 - `.workshop-meeting.dark-theme`
2. 筛选器组件 - `.filter-item-container`
   - 选择框 - `.ant-select`
   - 日期选择输入框 - `.ant-calendar-picker-input`
   - 输入框 - `.ant-input`
   - 占位符样式

3. 布局组件
   - 筛选器包装器 - `.filter-wrapper.with-bg`
   - 按钮组 - `.button-group.with-bg`

4. 卡片组件 - `.card-container`
   - 卡片内容 - `.card-content`
   - 质量指标项 - `.quality-item`
   - 异常问题跟进项 - `.issue-item`
   - 图表 - `.chart`

5. 表格组件 - `.ant-table-wrapper`, `.table-container`
   - 表头 - `.ant-table-thead`
   - 表格行和单元格 - `.ant-table-tbody`
   - 偶数行样式
   - 悬停行样式

6. 表单控件
   - 下拉选择框 - `.ant-select`
   - 选择框内容区 - `.ant-select-selection`
   - 输入框 - `.ant-input`
   - 日期选择器 - `.ant-calendar-picker`
   - 选择框箭头图标
   - 下拉菜单 - `.ant-select-dropdown`
   - 下拉菜单项 - `.ant-select-dropdown-menu-item`

7. 日历组件 - `.ant-calendar`
   - 日期 - `.ant-calendar-date`
   - 列头和头部 - `.ant-calendar-column-header`, `.ant-calendar-header`

8. 模态框组件 - `.ant-modal-content`
   - 内容 - `.ant-modal-body`
   - 关闭按钮 - `.ant-modal-close-x`

9. 按钮组件 - `.ant-btn`
   - 非主要按钮 - `:not(.ant-btn-primary)`
   - 按钮悬停状态

总体风格采用深蓝色背景（#01141e, #001d2c）、白色文字、青色边框（#00fbfb）作为主要色调，形成统一的暗色主题视觉效果。
