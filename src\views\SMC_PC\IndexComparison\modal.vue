<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuyong<PERSON>e
 * @LastEditTime: 2021-11-19 09:01:02
-->
<template>
  <a-drawer
    class="cardChooseModal"
    :visible="visible"
    :width="600"
    title="勾选指标"
    @close="close"
  >
    <a-spin :spinning="loadTree">
      <div style="min-height: 500px;">
        <a-tree
          :blockNode="true"
          :selectable="false"
          checkable
          v-model="checkedKeys"
          :auto-expand-parent="false"
          :tree-data="treeData"
          :replaceFields="replaceFields"
          @check="onCheck"
        />
        <a-empty style="margin-top: 100px;" v-if="treeData.length === 0" />
      </div>
    </a-spin>
    <div
      :style="{
        position: 'absolute',
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e8e8e8',
        padding: '10px 16px',
        textAlign: 'right',
        left: 0,
        background: '#fff',
        borderRadius: '0 0 4px 4px'
      }"
    >
      <a-button style="marginRight: 8px" @click="close">
        {{ showAlias("CANCEL", "取消") }}
      </a-button>
      <a-button type="primary" @click="handleOk">
        {{ showAlias("CONFIRM", "确定") }}
      </a-button>
    </div>
  </a-drawer>
</template>
<script>
import request from "@/utils/requestHttp";
import { showAlias } from "@/utils/intl.js";
import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      showAlias,
      visible: false, // 打开关闭弹窗
      checkedKeys: [], // 树形组件选择的nodeId
      treeData: [], // 树形组件数据
      replaceFields: {
        title: "nodeName",
        key: "nodeId",
        id: "nodeId",
        pId: "parentNodeId",
        children: "childNodeList"
      },
      postTreeData: [], // 要提交的组件数据
      loadTree: false, // 加载树形组件loading
      SYS_NAME: window.system
    };
  },
  props: {
    companyName: String
  },
  methods: {
    show() {
      this.visible = true;
      this.loadTree = true;
      this.getAllIndexList();
    },
    close() {
      this.visible = false;
      this.postTreeData = [];
      this.checkedKeys = [];
      this.treeData = [];
    },
    async handleOk() {
      await this.saveCurrentRoleList();
      this.$emit("refreshData");
      this.close();
    },
    // 获取指标树列表
    getAllIndexList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/roleDetail/getHBIndexTreeListByRole?company=${this.companyName}`
      )
        .then(res => {
          this.loadTree = false;
          if (Array.isArray(res) && res.length) {
            this.treeData = res;
            this.getCurrentRoleTreeList();
          } else {
            this.treeData = [];
          }
        })
        .catch(() => {
          this.treeData = [];
        });
    },
    // 获取当前角色有的指标
    getCurrentRoleTreeList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/userIndexRelation/getUserIndexTreeList?company=${this.companyName}&type=1`
      ).then(res => {
        this.checkedKeys = [];
        if (res && Array.isArray(res)) {
          this.checkedKeys = res;
          this.dealPostTreeData();
          this.checkedKeys = this.postTreeData;
        }
        this.loadTree = false;
      });
    },
    // 保存当前角色选中的指标
    saveCurrentRoleList() {
      return new Promise(resolve => {
        let postData = {
          company: this.companyName,
          type: "1",
          list: this.postTreeData,
          menu_name: `${this.companyName}核心KPI横比${
            this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""
          }`
        };
        request(`${adminUserUrlPrefix["lxp"]}/userIndexRelation/saveAll`, {
          method: "POST",
          body: postData
        }).then(() => {
          resolve();
        });
      });
    },
    // 树形控件改变
    onCheck() {
      this.dealPostTreeData();
    },
    // 处理成后端要的值
    dealPostTreeData() {
      this.postTreeData = this.checkedKeys.filter(item => {
        return item.split("==").length - 1 === 2;
      });
    }
  }
};
</script>
<style lang="less">
.cardChooseModal {
  .ant-drawer-body {
    padding-bottom: 77px !important;
  }
}
</style>
