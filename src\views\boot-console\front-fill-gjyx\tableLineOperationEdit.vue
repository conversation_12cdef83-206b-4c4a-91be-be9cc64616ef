<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-11-18 09:57:02
-->
<template>
  <!-- 非累加目标值修改 -->
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      // console.log("this.record----->", JSON.stringify(this.record));
      // const record = {
      //   indexName: "直通率",
      //   indexUnit: "",
      //   cmimIdFront:
      //     "ZBY00451-H020115800-D-0000-0000-DIM_1259-0000-0000-0000-0000-0000-0000-0000-0000"
      // };
      const record = {
        indexName: this.record.indexName,
        indexUnit: this.record.unit,
        cmimIdFront: this.record.cmimIdFront,
        cmimId: this.record.cmimId,
        dataType: this.record.dataType,
        flag: this.record.flag
      };
      this.$refs["modal"].show(record);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex
        }
      });
    }
  }
};
</script>
