/*
 * @Author: y<PERSON><PERSON><PERSON><PERSON>.ex
 * @Date: 2024-09-30 14:27:08
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-09-30 15:36:50
 * @FilePath: \localdev\pangea-component\src\utils\publicCustomTool.js
 * @Description: public action
 */
/**
 * @Description：观察到通过某个key来获取字典后端返回可能会出现数据重复的情况，导致select的option出现重复，该函数的功能为根据返回的数据中每一项的key来去重
 * @array:DictItem[]  type DictItem = {value: string;key: string};
 * @key:string Repeat based on the field where the key is located
 */

const uniqueByKey = (array, key) => {
  const seen = new Set();
  return array.filter((item) => {
    return seen.has(item[key]) ? false : seen.add(item[key]);
  });
};
export { uniqueByKey };
