<!--
 * @Description: 指标走势图
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-10-09 14:00:54
 * @LastEditors: yuyong<PERSON>e
 * @LastEditTime: 2021-12-21 14:57:21
-->
<template>
  <a-modal
    class="kpiCompareInModal"
    v-model="visible"
    title=""
    :footer="null"
    @cancel="close"
  >
    <div ref="kpiCompareInModal" style="width: 655px;height: 281px;"></div>
  </a-modal>
</template>
<script>
import * as echarts from "echarts";
import request from "@/utils/requestHttp";
import cloneDeep from "lodash/cloneDeep";
import sortBy from "lodash/sortBy";
import { adminUserUrlPrefix } from "@/utils/utils";
import { dealThousandData } from "../IndexGeneralView/utils";
export default {
  data() {
    return {
      visible: false,
      cardItem: null,
      kpiCompareChart: null,
      kpiCompareChartOptions: {
        title: {
          text: "同期&实际走势",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["同期", "实际", "目标"],
          top: 19,
          right: 60,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          }
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            xAxisIndex: [0]
          },
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            yAxisIndex: [0, 1, 2, 3]
          }
        ],
        yAxis: {
          type: "value",
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: "{value}"
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          }
        },
        series: [
          {
            name: "同期",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgba(255, 206, 140, 1)"
            },
            symbol: "circle",
            symbolSize: 6
          },
          {
            name: "实际",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgba(255, 135, 128, 1)"
            },
            label: {
              show: true,
              position: "top"
            },
            symbol: "circle",
            symbolSize: 6
          },
          {
            name: "目标",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgba(159, 105, 246, 1)"
            },
            symbol: "circle",
            symbolSize: 6
          }
        ]
      },
      cloneKpiCompareChartOptions: {}
    };
  },
  methods: {
    show(item) {
      this.visible = true;
      this.$nextTick(() => {
        this.kpiCompareChart = echarts.init(this.$refs["kpiCompareInModal"]);
        this.cloneKpiCompareChartOptions = cloneDeep(
          this.kpiCompareChartOptions
        );
        this.cardItem = item;
        this.getChartData();
      });
    },
    close() {
      this.kpiCompareChartOptions = cloneDeep(this.cloneKpiCompareChartOptions);
      this.initKpiCompare();
      this.visible = false;
    },
    // 初始化kpi同期情况表
    initKpiCompare(options) {
      this.kpiCompareChart.setOption(options || this.kpiCompareChartOptions);
    },
    getChartData() {
      return new Promise(resolve => {
        let body = {
          baseStr: this.cardItem.baseName,
          frequency: this.cardItem.dataFrequency,
          date: this.cardItem.indexDt,
          indexNodeId: this.cardItem.indexId,
          company: this.cardItem.companyName,
          type: "2"
        };
        if (!body.baseStr) {
          return;
        }
        request(`${adminUserUrlPrefix["zcx"]}/indexCardInfo/getHBindexDetail`, {
          method: "POST",
          body
        }).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            res = sortBy(res, function(item) {
              return item.dwppCmTfIndexLibrary.indexDt;
            });
            const xAxis = res.map(item => {
              return item.dwppCmTfIndexLibrary.indexDt;
            });
            this.kpiCompareChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map(item => {
              return dealThousandData(
                item.dwppCmTfIndexLibrary.baseActual,
                item.unit,
                item.dwppCmTfIndexLibrary.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.series[1].data = baseActualData;
            const targetValueData = res.map(item => {
              return dealThousandData(
                item.dwppCmTfIndexLibrary.targetValue,
                item.unit,
                item.dwppCmTfIndexLibrary.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.series[2].data = targetValueData;
            const contemValueData = res.map(item => {
              return dealThousandData(
                item.dwppCmTfIndexLibrary.contemValue,
                item.unit,
                item.dwppCmTfIndexLibrary.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.dataZoom[0].end = 100;
            // 根据数据条数 判断是否显示dataZoom组件
            if (res.length > 6) {
              this.kpiCompareChartOptions.dataZoom[0].show = true;
              this.kpiCompareChartOptions.dataZoom[1].show = true;
              this.kpiCompareChartOptions.grid.bottom = 52;
              if (res.length > 13) {
                this.kpiCompareChartOptions.dataZoom[0].start = 50;
              } else {
                this.kpiCompareChartOptions.dataZoom[0].start = 0;
              }
            }

            this.kpiCompareChartOptions.series[0].data = contemValueData;
            this.kpiCompareChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].unit === "null" ? "" : res[0].unit
            }`;
            this.initKpiCompare();
          } else {
            this.kpiCompareChartOptions = cloneDeep(
              this.cloneKpiCompareChartOptions
            );
            this.initKpiCompare();
          }
          resolve(res);
        });
      });
    }
  }
};
</script>
<style lang="less">
.kpiCompareInModal {
  .ant-modal {
    width: 655px !important;
    height: 281px;
  }
  .ant-modal-body {
    padding: 4px;
  }
}
</style>
