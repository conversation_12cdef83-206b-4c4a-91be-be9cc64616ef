<template>
  <a-modal :visible="visible" width="500px" :footer="null" @cancel="handleCancel" :class="themeClasses">
    <template slot="title">
      <div class="modal-title">
        <span class="title-icon"></span>
        <span class="title-text">新建事件</span>
      </div>
    </template>
    <div class="modal-content">
      <a-form-model :model="formData" :rules="formRules" ref="formModel">
        <div class="form-row">
          <!-- 这里改成默认 -->
          <div class="form-item">
            <div class="form-label">创建人</div>
            <a-select show-search placeholder="请选择或搜索责任人" class="form-input" :disabled="true"
              v-model="formData.USER_SELECT_IHKXAAAQ" :filter-option="false" :show-arrow="true" :allow-clear="true"
              :loading="searchUserLoading" @search="handleSearchUser" notFoundContent="未找到匹配的责任人">
              <a-select-option v-for="item in responsiblePersonOptions" :key="item.id" :value="item.loginName">
                {{ item.name }} ({{ item.department }})
              </a-select-option>
              <template slot="suffixIcon">
                <a-icon type="loading" v-if="searchUserLoading" />
                <img v-else src="~@/assets/images/workShop_standing_meeting/user-icon.png" class="user-icon" />
              </template>
            </a-select>
          </div>
          <div class="form-item">
            <div class="form-label">处理问题层级</div>
            <a-select placeholder="请选择" class="form-input" v-model="formData.problemSource">
              <a-select-option v-for="item in allDictsData['mom-ECenterHandleLevel']" :value="item.key"
                :key="item.key">{{
                  item.value }}</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <div class="form-label required">发生时间</div>
            <a-form-model-item prop="happenTime">
              <a-date-picker v-model="formData.happenTime" show-time :format="dateFormatTime" class="form-input"
                placeholder="选择日期" />
            </a-form-model-item>
          </div>
          <div class="form-item">
            <div class="form-label">发生地点工厂</div>
            <a-select placeholder="请选择" class="form-input" v-model="formData.factoryCode">
              <a-select-option v-for="item in option_factoryNameList" :value="item.orgCode" :key="item.orgCode">{{
                item.orgName }}</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <div class="form-label">发生地点车间</div>
            <a-select placeholder="请选择" class="form-input" v-model="formData.branchFactoryCode"
              :disabled="!formData.factoryCode">
              <a-select-option v-for="item in option_workshopList" :value="item.factoryModelCode"
                :key="item.factoryModelId">{{ item.factoryModelName }}</a-select-option>
            </a-select>
          </div>
          <div class="form-item">
            <div class="form-label">发生地点线体</div>
            <a-select placeholder="请选择" class="form-input" v-model="formData.lineCode"
              :disabled="!formData.branchFactoryCode">
              <a-select-option v-for="item in option_lineList" :value="item?.factoryModelCode"
                :key="item?.factoryModelCode">{{ item?.factoryModelName }}</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <div class="form-label">发生地点班组</div>
            <a-select placeholder="请选择" class="form-input" v-model="formData.teamCode" :disabled="!formData.lineCode"
              :loading="teamListLoading">
              <a-select-option v-for="item in option_teamList" :value="item.code" :key="item.code">{{ item.name
              }}</a-select-option>
            </a-select>
          </div>
          <div class="form-item">
            <div class="form-label required">问题等级</div>
            <a-form-model-item prop="eventLevel">
              <a-select placeholder="请选择" class="form-input" v-model="formData.eventLevel">
                <a-select-option v-for="item in allDictsData['mom-ECenterIssueLevel']" :value="item.key"
                  :key="item.key">{{
                    item.value }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <div class="form-label required">问题分类</div>
            <a-form-model-item prop="eventType">
              <a-select placeholder="请选择" class="form-input" v-model="formData.eventType">
                <a-select-option v-for="item in allDictsData['mom-mom-ECenterIssueClass']" :value="item.key"
                  :key="item.key">{{ item.value }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </div>
          <div class="form-item">
            <div class="form-label required">第一责任人</div>
            <a-form-model-item prop="firstRespondsMu">
              <a-select show-search placeholder="请选择或搜索责任人" class="form-input" v-model="formData.firstRespondsMu"
                :filter-option="false" :show-arrow="true" :allow-clear="true" :loading="searchUserLoading"
                @search="handleSearchUser" notFoundContent="未找到匹配的责任人">
                <a-select-option v-for="item in responsiblePersonOptions" :key="item.id" :value="item.loginName">
                  {{ item.name }} ({{ item.department }})
                </a-select-option>
                <template slot="suffixIcon">
                  <a-icon type="loading" v-if="searchUserLoading" />
                  <img v-else src="~@/assets/images/workShop_standing_meeting/user-icon.png" class="user-icon" />
                </template>
              </a-select>
            </a-form-model-item>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <div class="form-label">责任科室</div>
            <a-input placeholder="请输入" class="form-input" v-model="formData.chamber" />
          </div>
          <div class="form-item">
            <div class="form-label">责任部门</div>
            <a-input placeholder="请输入" class="form-input" v-model="formData.department" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">对策有效审核人</div>
            <a-select show-search placeholder="请选择或搜索责任人" class="form-input" v-model="formData.validityAuditMu"
              :filter-option="false" :show-arrow="true" :allow-clear="true" :loading="searchUserLoading"
              @search="handleSearchUser" notFoundContent="未找到匹配的责任人">
              <a-select-option v-for="item in responsiblePersonOptions" :key="item.id" :value="item.loginName">
                {{ item.name }} ({{ item.department }})
              </a-select-option>
              <template slot="suffixIcon">
                <a-icon type="loading" v-if="searchUserLoading" />
                <img v-else src="~@/assets/images/workShop_standing_meeting/user-icon.png" class="user-icon" />
              </template>
            </a-select>
          </div>
          <div class="form-item">
            <div class="form-label">同步告知人</div>
            <a-select show-search placeholder="请选择或搜索责任人" class="form-input" v-model="formData.syncInformsMu"
              :filter-option="false" :show-arrow="true" :allow-clear="true" :loading="searchUserLoading"
              @search="handleSearchUser" notFoundContent="未找到匹配的责任人">
              <a-select-option v-for="item in responsiblePersonOptions" :key="item.id" :value="item.loginName">
                {{ item.name }} ({{ item.department }})
              </a-select-option>
              <template slot="suffixIcon">
                <a-icon type="loading" v-if="searchUserLoading" />
                <img v-else src="~@/assets/images/workShop_standing_meeting/user-icon.png" class="user-icon" />
              </template>
            </a-select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item form-width">
            <div class="form-label">问题描述</div>
            <a-textarea placeholder="请输入" :auto-size="{ minRows: 2, maxRows: 6 }" class="form-input"
              v-model="formData.eventDescription" />
          </div>
        </div>

        <div class="modal-footer">
          <a-button type="primary" class="confirm-btn" @click="handleSubmit" :loading="submitLoading">确认</a-button>
          <a-button class="cancel-btn" @click="handleCancel" :disabled="submitLoading">取消</a-button>
        </div>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
import { searchUser, getFactoryNameList, getWorkshopAndLineList, saveHandMade, getTeamList } from "../Api";
import moment from 'moment';

export default {
  name: "AddEventDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    darkTheme: {
      type: Boolean,
      default: false
    },
    allDictsData: {
      type: Object,
      default: () => ({})
    },
    searchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dateFormatTime: "YYYY-MM-DD HH:mm:ss",
      formData: {
        happenTime: "",
        USER_SELECT_IHKXAAAQ: "", // 创建人
        problemSource: "", // 处理问题层级
        factoryCode: "", // 发生地点工厂
        branchFactoryCode: "", // 发生地点车间
        lineCode: "", // 发生地点线体
        teamCode: "", // 发生地点班组
        eventLevel: "", // 问题等级
        eventType: "", // 问题分类
        firstRespondsMu: "", // 第一责任人
        validityAuditMu: "", // 对策有效审核人
        chamber: "", // 责任科室
        department: "", // 责任部门
        syncInformsMu: "", // 同步告知人
        eventDescription: "" // 问题描述
      },
      searchUserLoading: false,
      searchUserTimeout: null,
      responsiblePersonOptions: [],
      option_factoryNameList: [], // 工厂列表
      option_workshopList: [], // 车间列表
      option_lineList: [], // 线体列表
      option_teamList: [], // 班组列表
      teamListLoading: false, // 班组列表加载状态
      submitLoading: false, // 提交表单加载状态
      // 表单校验规则
      formRules: {
        happenTime: [
          { required: true, message: '请选择发生时间', trigger: 'change' }
        ],
        eventLevel: [
          { required: true, message: '请选择问题等级', trigger: 'change' }
        ],
        eventType: [
          { required: true, message: '请选择问题分类', trigger: 'change' }
        ],
        firstRespondsMu: [
          { required: true, message: '请选择第一责任人', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    themeClasses() {
      return {
        'dark-theme': this.darkTheme
      };
    }
  },
  watch: {
    // 监听工厂选择的变化
    'formData.factoryCode'(newVal, oldVal) {
      // 避免在初始化时触发清空操作
      if (oldVal === undefined) return;

      if (newVal) {
        // 只有在用户主动切换时才清空下级选择
        if (oldVal !== '') {
          this.formData.branchFactoryCode = '';
          this.formData.lineCode = '';
          this.formData.teamCode = '';
        }
        // 获取该工厂下的车间列表
        this.fetchWorkshopList(newVal);
      } else {
        // 如果工厂为空，清空车间列表和线体列表
        this.option_workshopList = [];
        this.option_lineList = [];
        this.option_teamList = [];
      }
    },
    // 监听车间选择的变化
    'formData.branchFactoryCode'(newVal, oldVal) {
      // 避免在初始化时触发清空操作
      if (oldVal === undefined) return;

      if (newVal) {
        // 只有在用户主动切换时才清空下级选择
        if (oldVal !== '') {
          this.formData.lineCode = '';
          this.formData.teamCode = '';
        }
        // 获取该车间下的线体列表
        this.fetchLineList(newVal);
      } else {
        // 如果车间为空，清空线体列表和班组列表
        this.option_lineList = [];
        this.option_teamList = [];
      }
    },
    // 监听线体选择的变化
    'formData.lineCode'(newVal, oldVal) {
      // 避免在初始化时触发清空操作
      if (oldVal === undefined) return;

      if (newVal) {
        // 只有在用户主动切换时才清空下级选择
        if (oldVal !== '') {
          this.formData.teamCode = '';
        }
        // 获取该线体下的班组列表
        this.fetchTeamList(newVal);
      } else {
        // 如果线体为空，清空班组列表
        this.option_teamList = [];
      }
    },
    // 监听visible属性变化
    visible(newVal) {
      if (newVal) {
        // 当对话框显示时，初始化数据
        this.initData();

        // 重新设置默认的创建人
        this.setDefaultCreator();

        // 通知父组件模态框已打开，用于隐藏tooltip
        this.$emit('dialog-opened');
      } else {
        // 当对话框关闭时，重置表单校验状态
        this.$nextTick(() => {
          if (this.$refs.formModel) {
            this.$refs.formModel.clearValidate();
          }
        });
      }
    }
  },
  created() {
    // 初始化工厂列表
    this.fetchFactoryList();
  },
  methods: {
    // 初始化数据
    initData() {
      // 重置表单数据，并设置来自父组件的默认值
      this.formData = {
        happenTime: moment(), // 设置为当前时间
        USER_SELECT_IHKXAAAQ: "",
        problemSource: "",
        factoryCode: this.searchForm.factoryCode || "", // 使用父组件的工厂code
        branchFactoryCode: this.searchForm.branchCode || "", // 使用父组件的分厂code
        lineCode: this.searchForm.lineCode || "", // 使用父组件的线体code
        teamCode: this.searchForm.teamCode || "", // 使用父组件的班组code
        eventLevel: "",
        eventType: "",
        firstRespondsMu: "",
        validityAuditMu: "",
        chamber: "",
        department: "",
        syncInformsMu: "",
        eventDescription: ""
      };

      // 如果有默认值，需要初始化对应的下拉选项
      this.initDefaultOptions();
    },
    // 初始化默认选项
    async initDefaultOptions() {
      try {
        console.log('初始化默认选项，searchForm:', this.searchForm);

        // 如果有工厂code，获取车间列表
        if (this.formData.factoryCode) {
          await this.fetchWorkshopList(this.formData.factoryCode);

          // 如果有分厂code，获取线体列表
          if (this.formData.branchFactoryCode) {
            await this.fetchLineList(this.formData.branchFactoryCode);

            // 如果有线体code，获取班组列表
            if (this.formData.lineCode) {
              await this.fetchTeamList(this.formData.lineCode);
            }
          }
        }
      } catch (error) {
        console.error('初始化默认选项失败:', error);
      }
    },
    // 设置默认创建人
    async setDefaultCreator() {
      try {
        // 确保用户选项已加载
        if (this.responsiblePersonOptions.length === 0) {
          await this.searchUserDirectly(this.$store.state.user.info.loginName || 'yueshengqi.ex');
        }

        // 设置默认值
        this.$nextTick(() => {
          this.formData.USER_SELECT_IHKXAAAQ = this.$store.state.user.info.loginName || 'yueshengqi.ex';
          // 强制更新视图
          this.$forceUpdate();
        });
      } catch (error) {
        console.error('设置默认创建人失败:', error);
      }
    },
    // 获取工厂列表
    async fetchFactoryList() {
      try {
        const result = await getFactoryNameList();
        if (result && Array.isArray(result)) {
          this.option_factoryNameList = result;
        }
      } catch (error) {
        console.error("获取工厂列表失败:", error);
      }
    },
    // 获取车间列表
    async fetchWorkshopList(factoryCode) {
      try {
        const result = await getWorkshopAndLineList(factoryCode);
        if (result && Array.isArray(result)) {
          this.option_workshopList = result;
        }
      } catch (error) {
        console.error("获取车间列表失败:", error);
      }
    },
    // 获取线体列表
    async fetchLineList(workshopCode) {
      try {
        const result = await getWorkshopAndLineList(workshopCode);
        if (result && Array.isArray(result)) {
          this.option_lineList = result;
        }
      } catch (error) {
        console.error("获取线体列表失败:", error);
      }
    },
    // 获取班组列表
    async fetchTeamList(lineCode) {
      try {
        this.teamListLoading = true;
        const result = await getTeamList(lineCode);

        // 根据API返回格式处理数据
        if (result && result.data && Array.isArray(result.data)) {
          this.option_teamList = result.data;
        } else if (result && Array.isArray(result)) {
          // 兼容直接返回数组的情况
          this.option_teamList = result;
        } else {
          this.option_teamList = [];
        }
      } catch (error) {
        console.error("获取班组列表失败:", error);
        this.option_teamList = [];
      } finally {
        this.teamListLoading = false;
      }
    },
    // 直接搜索用户（不使用防抖，用于初始化默认值）
    async searchUserDirectly(value) {
      console.log('开始搜索用户:', value);
      this.searchUserLoading = true;
      try {
        const result = await searchUser(value);
        console.log('搜索用户API返回结果:', result);

        if (result && result.userInfoList && Array.isArray(result.userInfoList)) {
          // 将API返回的用户数据转换为组件需要的格式
          this.responsiblePersonOptions = result.userInfoList.map(user => ({
            id: user.userId,
            name: user.userName,
            department: user.ldapFullPath || '未知部门',
            loginName: user.loginName
          }));
          console.log('转换后的用户选项:', this.responsiblePersonOptions);
        } else {
          console.log('API返回数据格式不正确或为空');
          this.responsiblePersonOptions = [];
        }
      } catch (error) {
        console.error('搜索用户失败:', error);
        this.responsiblePersonOptions = [];
      } finally {
        this.searchUserLoading = false;
      }
    },
    // 防抖搜索用户
    handleSearchUser(value) {
      // 清除之前的定时器
      if (this.searchUserTimeout) {
        clearTimeout(this.searchUserTimeout);
        this.searchUserTimeout = null;
      }

      // 设置新的定时器，300ms后执行搜索
      this.searchUserTimeout = setTimeout(async () => {
        await this.searchUserDirectly(value);
      }, 300); // 300ms的防抖延迟
    },
    // 提交表单
    async handleSubmit() {
      try {
        // 先进行表单校验
        await this.$refs.formModel.validate();

        // 检查happenTime是否为moment对象
        if (!this.formData.happenTime || !moment.isMoment(this.formData.happenTime)) {
          this.$message.error('请选择发生时间');
          return;
        }

        // 开始loading
        this.submitLoading = true;

        const res = await saveHandMade({
          ...this.formData,
          systemAccount: this.formData.USER_SELECT_IHKXAAAQ,
          happenTime: this.formData.happenTime.format(this.dateFormatTime),
          orgCode: window.localStorage.getItem('plantCode') || "6210", // 后续需要更换
          eventSource: "8"
        });

        if (res == null) {
          this.$message.success('添加成功');
          // 延迟1秒后关闭对话框并触发success事件（用于请求新数据）
          setTimeout(() => {
            this.$emit('update:visible', false);
            // 再延迟1秒后触发success事件，避免立即请求新数据
            setTimeout(() => {
              this.$emit('success');
            }, 1000);
          }, 1000);
        }
      } catch (error) {
        // 如果是表单校验错误，不显示添加失败的消息
        if (error.errorFields) {
          console.log('表单校验失败:', error.errorFields);
          return;
        }
        console.error("提交表单失败:", error);
        this.$message.error('添加失败');
      } finally {
        // 结束loading
        this.submitLoading = false;
      }
    },
    // 取消对话框
    handleCancel() {
      // 重置表单校验状态
      this.$refs.formModel.clearValidate();
      this.$emit('update:visible', false);
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="less" scoped>
// 模态框样式
.modal-content {
  padding: 10px;

  .form-row {
    display: flex;
    justify-content: space-between;

    .form-item {
      width: 48%;
    }

    .form-width {
      width: 100%;
    }
  }

  .form-item {
    margin-bottom: 15px;

    .form-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;

      &.required::before {
        content: '*';
        color: #ff4d4f;
        margin-right: 4px;
      }
    }

    .form-input {
      width: 100%;
      border-radius: 0;

      &:hover,
      &:focus {
        border-color: #00aaa6;
      }
    }

    // 重置Ant Design表单项的默认样式
    :deep(.ant-form-item) {
      margin-bottom: 0;
    }

    :deep(.ant-form-item-control) {
      line-height: normal;
    }

    :deep(.ant-form-item-control-input) {
      min-height: auto;
    }

    :deep(.ant-form-item-control-input-content) {
      display: block;
    }

    :deep(.ant-form-item-explain) {
      margin-top: 4px;
      font-size: 12px;
      min-height: auto;
    }

    :deep(.ant-form-item-explain-error) {
      color: #ff4d4f;
    }

    // 错误状态样式
    :deep(.ant-form-item-has-error .ant-input),
    :deep(.ant-form-item-has-error .ant-select-selector),
    :deep(.ant-form-item-has-error .ant-picker) {
      border-color: #ff4d4f;
    }

    :deep(.ant-form-item-has-error .ant-input:hover),
    :deep(.ant-form-item-has-error .ant-select-selector:hover),
    :deep(.ant-form-item-has-error .ant-picker:hover) {
      border-color: #ff4d4f;
    }

    :deep(.ant-form-item-has-error .ant-input:focus),
    :deep(.ant-form-item-has-error .ant-select-focused .ant-select-selector),
    :deep(.ant-form-item-has-error .ant-picker-focused) {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }

    // 确保日期选择器样式一致
    :deep(.ant-picker) {
      width: 100%;
      border-radius: 0;

      &:hover,
      &:focus {
        border-color: #00aaa6;
      }
    }

    // 确保文本域样式一致
    :deep(.ant-input) {
      border-radius: 0;

      &:hover,
      &:focus {
        border-color: #00aaa6;
      }
    }

    // 确保选择器样式一致
    :deep(.ant-select-selector) {
      border-radius: 0 !important;

      &:hover {
        border-color: #00aaa6;
      }
    }

    :deep(.ant-select-focused .ant-select-selector) {
      border-color: #00aaa6;
      box-shadow: 0 0 0 2px rgba(0, 170, 166, 0.2);
    }
  }

  .modal-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-btn {
      border-radius: 0;
    }

    .confirm-btn {
      margin-right: 10px;
      background-color: #00aaa6;
      border-color: #00aaa6;
      border-radius: 0;
      color: #fff;

      &:hover {
        background-color: #33c3bf;
        border-color: #33c3bf;
      }
    }
  }
}

.user-icon {
  width: 16px;
  height: 16px;
}

.modal-title {
  padding-left: 20px;
  height: 56px;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(270deg,
      rgba(0, 170, 166, 0) -22%,
      rgba(0, 170, 166, 0.6) 87%);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("~@/assets/images/workShop_standing_meeting/dialog_head_banner.png");
    z-index: 0;
  }

  .title-icon {
    width: 15px;
    height: 15px;
    background: url("~@/assets/images/workShop_standing_meeting/dialog_banner_title_icon.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 8px;
    position: relative;
    z-index: 1;
  }

  .title-text {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    position: relative;
    z-index: 1;
  }
}
</style>
