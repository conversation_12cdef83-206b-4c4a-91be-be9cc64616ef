<!--
 * @Description: 单个卡片
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 09:36:11
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 09:13:36
-->
<template>
  <!-- 卡片 -->
  <a-card
    class="card"
    :class="[item.recommend ? 'recommend' : '']"
    @click="showDetail"
  >
    <div class="container">
      <div class="_flex __top">
        <template v-if="item.showYC"><span class="YC">预测</span></template>
        <!-- 版块 -->
        <span
          class="businessSegments"
          :style="{
            backgroundColor:
              this.businessSegmentsColorMap()[item.businessSegments]?.bgColor ||
              '#e0e3ea',
            color:
              this.businessSegmentsColorMap()[item.businessSegments]?.color ||
              '#53667a'
          }"
          >{{ item.businessSegments }}</span
        >
        <template v-if="!item.recommend">
          <!-- 维度 -->
          <span
            class="wdTag"
            v-for="(tag, tagIdx) in item.wdInCardTag"
            :key="`${tag}${tagIdx}`"
          >
            {{ tag }}
          </span>
        </template>
        <template v-else>
          <!-- 推荐原因 -->
          <div class="sign _flex">
            <template v-if="item.label">
              <span
                v-for="(label, labelIdx) in item.label.split(',')"
                :key="labelIdx"
              >
                {{ label }}
              </span>
            </template>
          </div>
        </template>
      </div>
      <!-- 标题 -->
      <span
        class="___title"
        :title="
          `${item.wdInCardName ? item.wdInCardName + ' - ' : ''}${
            item.displayIndexName
          }`
        "
        >{{
          (item.wdInCardName ? item.wdInCardName + " - " : "") +
            item.displayIndexName
        }}
      </span>
      <template v-if="skinStyle().includes('classic-style')">
        <!-- 数据 -->
        <div class="_data">
          <span>{{ item.actualValue }}</span>
          <span>{{ item.indexUnitId }}</span>
        </div>
        <!-- 目标值 -->
        <div class="_target">
          目标值
          {{ item.targetValue }}
          {{ item.indexUnitId }}
        </div>
        <!-- 完成率 -->
        <div class="completRate _flex">
          <span style="white-space: nowrap;">完成率</span>
          <span class="process">
            <span
              :class="[
                item.targetCompletionRate
                  ? parseFloat(item.targetCompletionRate) < 100
                    ? 'b'
                    : 'r'
                  : ''
              ]"
              :style="{
                width: `${
                  item.targetCompletionRate
                    ? parseFloat(item.targetCompletionRate) > 100
                      ? 100
                      : parseFloat(item.targetCompletionRate)
                    : 0
                }%`
              }"
            ></span>
          </span>
          <span>{{ item.targetCompletionRate }}</span>
        </div>
      </template>
      <template v-if="skinStyle().includes('hisense-style')">
        <div class="__center _flex">
          <div class="_left _flex">
            <span>{{ item.actualValue }}</span>
            <span>{{ item.indexUnitId }}</span>
          </div>
          <div class="_right _flex">
            <a-progress
              stroke-linecap="square"
              type="dashboard"
              :width="90"
              :percent="
                item.targetCompletionRate &&
                !item.targetCompletionRate.includes('-')
                  ? parseFloat(item.targetCompletionRate)
                  : 0
              "
              :strokeColor="
                item.targetCompletionRate &&
                !item.targetCompletionRate.includes('-')
                  ? parseFloat(item.targetCompletionRate) < 100
                    ? '#597ef7'
                    : '#f83a3a'
                  : '#a9aeb8'
              "
              :gapDegree="150"
            >
              <template slot="format">
                <span
                  style="height: 19px;line-height: 19px;color: #1D2129;font-size:16px;"
                  >{{ item.targetCompletionRate }}</span
                >
                <br />
                <span
                  style="color: #A9AEB8;font-size:14px;height: 17px;line-height: 17px;"
                  >完成率</span
                >
              </template>
            </a-progress>
          </div>
        </div>
      </template>
      <template v-if="item.recommend">
        <template v-if="skinStyle().includes('hisense-style')">
          <span class="compare-topline"></span>
        </template>
        <!-- 操作 -->
        <div class="_flex _op">
          <template v-if="skinStyle().includes('hisense-style')">
            <div class="_flex _c">
              <span style="margin-right: 8px;">目标值</span>
              <div>{{ item.targetValue }}</div>
            </div>
            <div class="_flex _right-op">
              <div @click.stop="$emit('changeRecommend')">
                <img src="@/assets/images/Group <EMAIL>" alt="" srcset="" />
                换一个
              </div>
              <div @click.stop="$emit('addToCardList')">
                + 添加到列表
              </div>
            </div>
          </template>
          <template v-if="skinStyle().includes('classic-style')">
            <div class="_another" @click.stop="$emit('changeRecommend')">
              <a-icon type="sync" />
              换一个
            </div>
            <a-button
              @click.stop="$emit('addToCardList')"
              class="_addtolist"
              size="small"
              shape="round"
            >
              <a-icon type="plus" />
              添加到列表
            </a-button>
          </template>
        </div>
        <!-- 推荐图片 -->
        <div class="recommend_bg"></div>
      </template>
      <template v-if="!item.recommend">
        <span class="compare-topline"></span>
        <!-- 同比 -->
        <div class="compare _flex">
          <a-row style="width: 100%;">
            <a-col :span="skinStyle().includes('hisense-style') ? 8 : 12">
              <div class="_flex _c">
                <span>同比</span>
                <div
                  :style="{
                    color: item.contemChangeRate
                      ? item.indexType === '正向'
                        ? item.contemChangeRate.includes('-')
                          ? '#6495F9'
                          : '#f75050'
                        : item.contemChangeRate.includes('-')
                        ? '#f75050'
                        : '#6495F9'
                      : 'rgba(0, 0, 0, 0.8);'
                  }"
                  class="_flex"
                  v-if="item.isContemRate === 'Y'"
                >
                  <a-icon
                    v-if="item.contemChangeRate"
                    :type="
                      item.indexType === '正向'
                        ? item.contemChangeRate.includes('-')
                          ? 'caret-down'
                          : 'caret-up'
                        : item.contemChangeRate.includes('-')
                        ? 'caret-up'
                        : 'caret-down'
                    "
                    style="margin: 0 3px;"
                  />
                  <span>{{
                    item.contemChangeRate
                      ? Math.abs(
                          Decimal(item.contemChangeRate)
                            .mul(Decimal(100))
                            .toFixed(2, Decimal.ROUND_HALF_UP)
                        ) + "%"
                      : "-"
                  }}</span>
                </div>
                <span
                  v-if="item.isContemRate !== 'Y'"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                >
                  不对比
                </span>
              </div>
            </a-col>
            <a-col :span="skinStyle().includes('hisense-style') ? 8 : 12">
              <div class="_flex _c">
                <span>环比</span>
                <div
                  v-if="item.isPreviousRate === 'Y'"
                  :style="{
                    color: item.previousChangeRate
                      ? item.indexType === '正向'
                        ? item.previousChangeRate.includes('-')
                          ? '#6495F9'
                          : '#f75050'
                        : item.previousChangeRate.includes('-')
                        ? '#f75050'
                        : '#6495F9'
                      : 'rgba(0, 0, 0, 0.8);'
                  }"
                  class="_flex"
                >
                  <a-icon
                    v-if="item.previousChangeRate"
                    :type="
                      item.indexType === '正向'
                        ? item.previousChangeRate.includes('-')
                          ? 'caret-down'
                          : 'caret-up'
                        : item.previousChangeRate.includes('-')
                        ? 'caret-up'
                        : 'caret-down'
                    "
                    style="margin: 0 1px;"
                  />
                  <span>{{
                    item.previousChangeRate
                      ? Math.abs(
                          Decimal(item.previousChangeRate)
                            .mul(Decimal(100))
                            .toFixed(2, Decimal.ROUND_HALF_UP)
                        ) + "%"
                      : "-"
                  }}</span>
                </div>
                <span
                  v-if="item.isPreviousRate !== 'Y'"
                  style="color: #a9aeb8;border: 0.5px solid #a9aeb8;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                >
                  不对比
                </span>
              </div>
            </a-col>
            <template v-if="skinStyle().includes('hisense-style')">
              <a-col :span="8">
                <div class="_flex _c">
                  <span style="margin-right: 8px;">目标值</span>
                  <div>{{ item.targetValue }}</div>
                </div>
              </a-col>
            </template>
          </a-row>
        </div>
      </template>
    </div>
    <!-- 操作面板 -->
    <div v-if="!item.recommend" class="dragDel _flex">
      <template v-if="skinStyle().includes('classic-style')">
        <a-tooltip placement="top">
          <template slot="title">
            <span>组织下探</span>
          </template>
          <div class="xt" @click.stop="showDetection">
            <img src="~@/assets/images/icon-org-xt.png" alt="" srcset="" />
          </div>
        </a-tooltip>
        <template v-if="pageClass === 'indexGeneralViewPage2'">
          <a-tooltip placement="top">
            <template slot="title">
              <span>走势</span>
            </template>
            <img
              @click.stop="$emit('trendShow', item)"
              src="~@/assets/images/icon-qs.png"
              alt=""
              srcset=""
            />
          </a-tooltip>
        </template>
      </template>
      <template v-if="skinStyle().includes('hisense-style')">
        <div>
          <a-tooltip placement="top">
            <template slot="title">
              <span>组织下探</span>
            </template>
            <div class="xt" @click.stop="showDetection"></div>
          </a-tooltip>
          <a-tooltip placement="top">
            <template slot="title">
              <span>走势</span>
            </template>
            <div class="zs" @click.stop="$emit('trendShow', item)"></div>
          </a-tooltip>
        </div>
      </template>
    </div>
  </a-card>
</template>
<script>
import { thousandSplit } from "../../utils";
import Decimal from "decimal.js";

export default {
  props: {
    item: Object,
    index: Number, // 指标唯一值
    pageClass: String,
    companyName: String // 公司名称
  },
  inject: ["businessSegmentsColorMap", "skinStyle"],
  data() {
    return {
      thousandSplit,
      Decimal,
      mdyyShow: true, // 展示末端原因分析
      SYS_NAME: window.system
    };
  },
  computed: {
    // 当前登陆用户的账号
    nowLoginUserAccount() {
      return window.vm.$store
        ? window.vm.$store.state.user.info?.loginName
        : "yuyongjie.ex";
    }
  },
  methods: {
    /**
     * @description: 打开详情抽屉
     */
    showDetail() {
      this.$emit("showDetail", this.item);
    },
    // 打开下探
    showDetection() {
      if (this.pageClass === "indexGeneralViewPage2") {
        this.$emit("showDetection", this.item);
      }
    }
  }
};
</script>
