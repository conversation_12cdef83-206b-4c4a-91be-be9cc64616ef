<!--
 * @Description: 页头
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:36:50
 * @LastEditors: y<PERSON>hengqi.ex
 * @LastEditTime: 2025-03-31 15:47:08
-->
<template>
  <div class="_pageHeader">
    <!-- 标题 -->
    <div class="_left _flex">
      <span class="_title" :title="title">{{ title }}</span>
      <template v-if="skinStyle().includes('classic-style')">
        <!-- 查看横比 -->
        <!-- <a-tooltip placement="top">
          <template slot="title">
            <span>查看横比</span>
          </template>
          <img
            @click="$emit('showHB', true)"
            :src="HBImg"
            style="display: block;width: 16px;height: 16px;cursor: pointer;margin-right: 4px;margin-left: 4px;"
            alt=""
            srcset=""
          />
        </a-tooltip> -->
        <!-- 引导 -->
        <a-tooltip placement="top">
          <template slot="title">
            <span>操作引导</span>
          </template>
          <img
            @click="showZZ = true"
            :src="GuideImg"
            style="display: block;width: 24px;height: 24px;cursor: pointer;"
            alt=""
            srcset=""
          />
        </a-tooltip>
        <!-- 专属报告 -->
        <a-tooltip placement="top">
          <template slot="title">
            <span>个人专属报告</span>
          </template>
          <img
            @click="$refs['selfReports'].show()"
            :src="ReportImg"
            style="display: block;width: 24px;height: 24px;cursor: pointer;"
            alt=""
            srcset=""
          />
        </a-tooltip>
      </template>
      <template v-if="skinStyle().includes('hisense-style')">
        <div class="op _flex">
          <!-- <a-tooltip placement="top">
            <template slot="title">
              <span>查看横比</span>
            </template>
            <div class="_flex" @click="$emit('showHB', true)">
              <img
                :src="HBImg"
                style="width: 14px;height: 14px;"
                alt=""
                srcset=""
              />
            </div>
          </a-tooltip> -->
          <a-tooltip placement="top">
            <template slot="title">
              <span>操作引导</span>
            </template>
            <div class="_flex" @click="showZZ = true">
              <img src="@/assets/images/Group <EMAIL>" alt="" srcset="" />
            </div>
          </a-tooltip>
          <a-tooltip placement="top">
            <template slot="title">
              <span>个人专属报告</span>
            </template>
            <div class="_flex" @click="$refs['selfReports'].show()">
              <img src="@/assets/images/Group <EMAIL>" alt="" srcset="" />
            </div>
          </a-tooltip>
        </div>
      </template>
      <!-- 指标搜索 -->
      <div class="_flex index-search-input">
        <a-select
          mode="multiple"
          show-search
          :value="filterIndexArr"
          :placeholder="
            !isLocal ? $t('bc7e7ec2-1891-49f5-906f-8ad4c4c626ac') : '指标搜索'
          "
          style="width: 200px;"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          @search="handleIndexSearch"
          @change="handleIndexChange"
          :maxTagCount="1"
          :maxTagTextLength="4"
          :dropdownMatchSelectWidth="false"
          @keydown.enter.native="onKeyDown"
          :allowClear="true"
        >
          <a-select-option v-for="d in indexList" :key="d.pj" :value="d.pj">
            {{
              (d.wdInCardName ? d.wdInCardName + " - " : "") +
                d.displayIndexName +
                (d.wdInCardTag && d.wdInCardTag.length
                  ? "(" + d.wdInCardTag.join(",") + ")"
                  : "")
            }}
          </a-select-option>
        </a-select>
      </div>
    </div>
    <div class="_right _flex">
      <div class="_flex">
        <!-- 组织 -->
        <div class="org _flex">
          <!-- <span>{{ orgLabelName }}：</span> -->
          <span
            >{{
              !isLocal ? $t("82e9624b-47f4-40c9-a0b0-a6e8dab39a5a") : "组织"
            }}：</span
          >
          <div style="position: relative;">
            <div
              v-show="treeSelectLoading"
              style="position: absolute;width: 100%;height: 100%;background-color: rgba(242, 243, 245, 0.8);z-index: 1;"
            >
              <a-spin
                style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;"
              />
            </div>
            <a-tree-select
              v-model="searchForm.orgSign"
              style="width: 138px"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="orgTreeList"
              :dropdownMatchSelectWidth="false"
              placeholder="请选择组织"
              :treeExpandedKeys.sync="treeExpandedKeys"
              :replaceFields="replaceFields"
              @change="handleIndexChange([])"
            >
            </a-tree-select>
          </div>
        </div>
        <!-- 时间 -->
        <div class="time _flex">
          <template v-if="skinStyle().includes('classic-style')">
            <a-radio-group
              class="timetyep-radio-group"
              v-model="searchForm.timeType"
              button-style="solid"
              @change="timeTypeChange"
            >
              <a-radio-button
                :value="item.key"
                v-for="item in timeTypeOptions"
                :key="item.key"
              >
                {{ item.value }}
              </a-radio-button>
            </a-radio-group>
          </template>
          <template v-if="skinStyle().includes('hisense-style')">
            <div class="timetyep-radio-group _flex">
              <div
                :value="item.key"
                v-for="item in timeTypeOptions"
                :key="item.key"
                @click="
                  searchForm.timeType = item.key;
                  timeTypeChange(item.key);
                "
                :class="searchForm.timeType === item.key ? 'active' : ''"
              >
                {{ item.value }}
              </div>
            </div>
          </template>
          <!-- 月选择器 -->
          <a-month-picker
            :allowClear="false"
            v-model="searchForm.time"
            v-if="searchForm.timeType === 'month'"
            :format="monthFormat"
          />
          <!-- 周选择器 -->
          <a-select
            show-search
            placeholder="选择年"
            style="width: 90px;margin-right: 4px;"
            :filter-option="filterOption"
            v-model="selectedYearWeek[0]"
            v-if="searchForm.timeType === 'week'"
          >
            <a-select-option
              :value="item"
              v-for="item in yearSelectOptions"
              :key="item"
            >
              {{ item }}年
            </a-select-option>
          </a-select>
          <a-select
            show-search
            placeholder="选择周"
            style="width: 90px"
            :filter-option="filterOption"
            v-model="selectedYearWeek[1]"
            v-if="searchForm.timeType === 'week'"
          >
            <a-select-option
              :value="item"
              v-for="item in weekSelectOptions"
              :key="item"
            >
              {{ item }}周
            </a-select-option>
          </a-select>
          <!-- 日选择器 -->
          <a-date-picker
            :allowClear="false"
            v-model="searchForm.time"
            v-if="searchForm.timeType === 'day'"
            :format="dateFormat"
          />
        </div>
      </div>
      <!-- 模式 -->
      <div class="mode _flex">
        <div
          class="chart"
          :class="[searchForm.mode === 'chart' ? 'active' : '']"
          @click="searchForm.mode = 'chart'"
        >
          <a-tooltip placement="top">
            <template slot="title">
              <span>切换卡片</span>
            </template>
            <template v-if="skinStyle().includes('classic-style')">
              <svg
                width="16px"
                height="16px"
                viewBox="0 0 16 16"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <g
                  stroke="none"
                  stroke-width="1"
                  fill="none"
                  fill-rule="evenodd"
                >
                  <g
                    transform="translate(-1376.000000, -149.000000)"
                    fill-rule="nonzero"
                  >
                    <g transform="translate(1336.000000, 141.000000)">
                      <g transform="translate(40.000000, 8.000000)">
                        <rect x="0" y="0" width="16" height="16"></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="3.04166651"
                          y="3.04166651"
                          width="3.79166651"
                          height="3.79166651"
                        ></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="9.16666603"
                          y="3.04166651"
                          width="3.79166794"
                          height="3.79166651"
                        ></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="3.04166651"
                          y="9.16666603"
                          width="3.79166651"
                          height="3.79166794"
                        ></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="9.16666603"
                          y="9.16666603"
                          width="3.79166794"
                          height="3.79166794"
                        ></rect>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </template>
            <template v-if="skinStyle().includes('hisense-style')">
              <img
                :src="searchForm.mode === 'chart' ? ChartActiveImg : ChartImg"
                alt=""
                srcset=""
              />
            </template>
          </a-tooltip>
        </div>
        <div
          class="table"
          :class="[searchForm.mode === 'table' ? 'active' : '']"
          @click="searchForm.mode = 'table'"
        >
          <a-tooltip placement="top">
            <template slot="title">
              <span>切换表格</span>
            </template>
            <template v-if="skinStyle().includes('classic-style')">
              <svg
                width="16px"
                height="16px"
                viewBox="0 0 16 16"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <g
                  stroke="none"
                  stroke-width="1"
                  fill="none"
                  fill-rule="evenodd"
                >
                  <g transform="translate(-1344.000000, -149.000000)">
                    <g transform="translate(1336.000000, 141.000000)">
                      <g transform="translate(8.000000, 8.000000)">
                        <rect x="0" y="0" width="16" height="16"></rect>
                        <line
                          x1="2"
                          y1="3"
                          x2="14"
                          y2="3"
                          id="路径-2"
                          stroke="#4E5969"
                          stroke-width="1.5"
                        ></line>
                        <line
                          x1="2"
                          y1="8"
                          x2="14"
                          y2="8"
                          stroke="#4E5969"
                          stroke-width="1.5"
                        ></line>
                        <line
                          x1="2"
                          y1="13"
                          x2="14"
                          y2="13"
                          stroke="#4E5969"
                          stroke-width="1.5"
                        ></line>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </template>
            <template v-if="skinStyle().includes('hisense-style')">
              <img
                :src="searchForm.mode === 'table' ? TableActiveImg : TableImg"
                alt=""
                srcset=""
              />
            </template>
          </a-tooltip>
        </div>
      </div>
    </div>
    <!-- 引导遮罩 -->
    <div class="guidezz" @keydown.27="closeZZ" @click="closeZZ" v-if="showZZ">
      <div>
        <div class="title">
          <span>功能操作视频引导，点击非视频任意处退出播放</span>
          <a-icon type="close" class="icon" @click="closeZZ" />
        </div>
        <div class="video" @click.stop="clickVideo" :style="videoStyle">
          <!-- 线上运行 -->
          <component :is="videoComName" ref="videoCom"></component>
          <!-- 本地调试 -->
          <!-- <video-com ref="videoCom" /> -->
        </div>
      </div>
    </div>
    <!-- 个人专属报告 -->
    <!-- 线上运行 -->
    <component
      :is="selfReportsComName"
      ref="selfReports"
      :companyName="companyName"
      :signOrgId="signOrgId"
      @change="
        (data) => {
          timeTypeChange(data.frequency);
          searchForm.timeType = data.frequency;
          setStatus(data.type);
          $emit('reportModalChange', data.type);
        }
      "
    ></component>
    <!-- 本地调试 -->
    <!-- <self-reports
      ref="selfReports"
      :companyName="companyName"
      :signOrgId="signOrgId"
      @change="
        data => {
          timeTypeChange(data.frequency);
          searchForm.timeType = data.frequency;
          setStatus(data.type);
          $emit('reportModalChange', data.type);
        }
      "
    /> -->
  </div>
</template>
<script>
import ReportImg from "@/assets/images/icon-report2.png";
import HBImg from "@/assets/images/comparison_img.png";
import GuideImg from "@/assets/images/icon-guide2.png";
import ChartActiveImg from "@/assets/images/Group <EMAIL>";
import ChartImg from "@/assets/images/Group <EMAIL>";
import TableActiveImg from "@/assets/images/Group <EMAIL>";
import TableImg from "@/assets/images/Group <EMAIL>";
import moment from "moment";
// import VideoCom from "../VideoCom/index.vue"; // 本地调试
// import SelfReports from "./SelfReports/index.vue"; // 本地调试
import { publicPath, covertDate } from "@/utils/utils.js";
import { findNode, getWeek } from "../utils";
import debounce from "lodash/debounce";

export default {
  inject: ["skinStyle", "isLocal"],
  components: {
    // VideoCom
    // SelfReports
  }, // 本地调试
  props: {
    companyName: String,
    orgTreeList: {
      type: Array,
      default() {
        return [];
      },
    },
    // orgLabelName: {
    //   type: String,
    //   default() {
    //     return this.$t("82e9624b-47f4-40c9-a0b0-a6e8dab39a5a") || "组织";
    //   }
    // },
    signOrgId: String,
    activePlate: String,
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    isSelfReportInMail: {
      type: Boolean,
      default() {
        return false;
      },
    }, // 邮件中专属快报预警
    selfReportParams: {
      type: Object,
      default() {
        return {
          indexDt: "",
          indexFrequencyId: "",
          zsbg: "",
        };
      },
    },
    title: String,
  },
  computed: {
    videoStyle() {
      //输出当前窗口的宽
      let windowWidth =
        document.documentElement.clientWidth || document.body.clientWidth;
      return {
        width: windowWidth * 0.7 + "px",
        height: (windowWidth * 0.7 * 576) / 1024 + "px",
      };
    },
  },
  data() {
    this.handleIndexSearch = debounce(this.handleIndexSearch, 800);
    this.sendRequest = debounce(this.sendRequest);
    return {
      selfReportsJSUrl:
        (window.location.host.includes("localhost")
          ? "http://smc.devapps.hisense.com"
          : "") + "/minio/mombucket/SelfReports2.umd.min.1.0.js",
      operationGuideJSUrl:
        (window.location.host.includes("localhost")
          ? "http://smc.devapps.hisense.com"
          : "") + "/minio/mombucket/VideoCom.umd.min.1.0.js",
      HBImg,
      moment,
      GuideImg,
      ReportImg,
      ChartActiveImg,
      ChartImg,
      TableActiveImg,
      TableImg,
      showZZ: false, // 展示视频遮罩
      searchForm: {
        // 页面中查询条件
        orgSign: "", // 组织
        mode: "chart", // 模式
        timeType: "month", // 时间类型
        time: moment(new Date().getTime(), this.monthFormat), // 时间
      },
      indexStatus: "",
      yearSelectOptions: [], // 周模式下年下拉框
      weekSelectOptions: [], // 周模式下周下拉框
      selectedYearWeek: ["", ""], // 周模式下选中的年周
      baseOptions: [
        // 组织下拉框
      ],
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: "month",
          value: !this.isLocal
            ? this.$t("80432b97-2434-43f8-83d1-814dd893eb77")
            : "按月",
        },
        {
          key: "week",
          value: !this.isLocal
            ? this.$t("31efb687-7afd-41a0-9005-df0e19b17bfb")
            : "按周",
        },
        {
          key: "day",
          value: !this.isLocal
            ? this.$t("067b57ce-3cee-4f42-8690-24f6b5c03c09")
            : "按日",
        },
      ],
      dateFormat: "YYYY-MM-DD", // 日期、周格式化
      monthFormat: "YYYY-MM", // 月格式化
      weekFormat: "YYYY-ww", // 周格式化
      saveLocalSearchForm: false, // 搜索表条本地存储
      videoComName: "",
      selfReportsComName: "",
      treeExpandedKeys: [], // 组织展开的树节点
      treeSelectLoading: true,
      replaceFields: {
        children: "list",
        title: "org",
        key: "fullCode",
        value: "fullCode",
      },
      indexList: [], // 搜索后指标列表
      filterIndexArr: [], // 过滤指标数组
    };
  },
  created() {
    // 初始化年周下拉框
    this.initYearSelect();
    this.initWeekSelect();
    // 如果本地搜索条件有存储，获取搜索条件后赋值到searchForm（专属快报邮件预警时不需要读取本地存储）
    if (
      !this.isSelfReportInMail &&
      localStorage.getItem(`${this.signOrgId}-searchForm`)
    ) {
      try {
        const { orgSign, timeType, time } = JSON.parse(
          localStorage.getItem(`${this.signOrgId}-searchForm`)
        );
        this.searchForm.orgSign = orgSign;
        this.searchForm.timeType = timeType;
        this.searchForm.time = time;
        if (timeType === "week") {
          this.selectedYearWeek[1] = Number(time.split("-")[1] || "01");
          this.selectedYearWeek[0] = Number(
            time.split("-")[0] || new Date().getFullYear()
          );
        }
        // 修改本地存储标识
        this.saveLocalSearchForm = true;
      } catch (error) {
        console.error(error);
      }
    }
    if (this.isSelfReportInMail) {
      if (
        this.selfReportParams.indexFrequencyId &&
        this.selfReportParams.indexDt &&
        this.selfReportParams.zsbg
      ) {
        const map = {
          M: "month",
          W: "week",
          D: "day",
        };
        this.searchForm["timeType"] =
          map[this.selfReportParams.indexFrequencyId];
        this.searchForm["time"] = this.selfReportParams.indexDt;
        if (this.searchForm["timeType"] === "week") {
          this.selectedYearWeek[1] = Number(
            this.selfReportParams.indexDt.split("-")[1] || "01"
          );
          this.selectedYearWeek[0] = Number(
            this.selfReportParams.indexDt.split("-")[0] ||
              new Date().getFullYear()
          );
        }
        this.setStatus(this.selfReportParams.zsbg);
        this.$nextTick(() => {
          this.$emit("reportModalChange", this.selfReportParams.zsbg);
        });
      } else {
        // 发起一次空请求，只有在专属快报预警邮件页面点击进来且组件所需两个参数为空的情况下执行
        this.sendRequest();
      }
    }
  },
  mounted() {
    Promise.all([this.loadVideoCom(), this.loadSelfReportCom()]).then(() => {
      // 如果没有看过视频引导，展示视频引导
      if (!localStorage.getItem("IndexGeneralViewGuide")) {
        this.showZZ = true;
      } else {
        // 已读引导视频则，每日首次进入系统展示个人专属报告
        if (
          localStorage.getItem(`${this.companyName}IndexGeneralSeltReports`) !==
          `${covertDate(new Date().getTime(), 0)}`
        ) {
          this.$nextTick(() => {
            this.$refs["selfReports"].show();
          });
        }
      }
    });
  },
  watch: {
    // 监听组织列表变化
    orgTreeList: {
      handler(newVal) {
        this.treeSelectLoading = false;
        if (Array.isArray(newVal) && newVal.length) {
          console.timeEnd("transmit");
          // 组织树默认展开第一层
          console.time("treeExpandedKeys");
          newVal.forEach((item) => {
            this.treeExpandedKeys.push(item.fullCode);
          });
          console.timeEnd("treeExpandedKeys");
          // 非专属快报邮件预警时，需要设置组织默认值
          if (!this.isSelfReportInMail) {
            // 如果不是本地存储，则默认选中组织列表第一个
            if (!this.saveLocalSearchForm) {
              this.searchForm.orgSign = newVal[0].fullCode;
            } else {
              console.time("findNode");
              // 是本地存储且树数据中查找不到该节点，则默认选中组织列表第一个
              const data = findNode(newVal, (node) => {
                return node.fullCode === this.searchForm.orgSign;
              });
              console.timeEnd("findNode");
              if (!data) {
                this.searchForm.orgSign = newVal[0].fullCode;
              }
            }
          }
        } else {
          this.searchForm.orgSign = "";
        }
      },
      deep: true,
    },
    // 监听searchForm值改变
    searchForm: {
      handler() {
        // 保存搜索条件到本地
        localStorage.setItem(
          `${this.signOrgId}-searchForm`,
          JSON.stringify(this.searchForm)
        );
        // 保存上次查询日期
        let itemInListIndex;
        let searchFormCache = this.getSearchFormCache();
        if (searchFormCache.length) {
          for (let i = 0; i < searchFormCache.length; i++) {
            const element = searchFormCache[i];
            if (element.key === this.searchForm.timeType) {
              itemInListIndex = i;
            }
          }
        }
        const list = JSON.parse(JSON.stringify(searchFormCache));
        if (itemInListIndex === undefined) {
          list.push({
            key: this.searchForm.timeType,
            value: this.searchForm.time,
          });
        } else {
          list[itemInListIndex].value = this.searchForm.time;
        }
        localStorage.setItem(
          `${this.signOrgId}-searchFormCache`,
          JSON.stringify(list)
        );
        // 改变后向父组件发出请求
        this.sendRequest();
      },
      deep: true,
    },
    // 监听周模式下的selectedYearWeek值改变
    selectedYearWeek(val) {
      // 根据选中年份去动态生成周下拉框
      this.initWeekSelect(val[0]);
      // 如果周下拉框中没有当前已选中的周值，则默认选成第一周
      if (!this.weekSelectOptions.includes(val[1])) {
        this.selectedYearWeek[1] = this.weekSelectOptions[0];
      }
      this.searchForm.time = `${val[0]}-${String(val[1]).padStart(2, "0")}`;
    },
  },
  methods: {
    // 获取本地缓存
    getSearchFormCache() {
      let localCache = localStorage.getItem(
        `${this.signOrgId}-searchFormCache`
      );
      let cacheList = [];
      if (localCache) {
        try {
          cacheList = JSON.parse(localCache);
        } catch (error) {
          cacheList = [];
        }
      }
      return cacheList;
    },
    // 设置指标状态
    async setStatus(e) {
      this.indexStatus = e || "";
      this.sendRequest();
    },
    // 指标搜索
    handleIndexSearch(value) {
      console.log(value, "value");
      if (value) {
        const list = this.list.filter((item) => {
          const name =
            (item.wdInCardName ? item.wdInCardName + " - " : "") +
            item.displayIndexName +
            (item.wdInCardTag && item.wdInCardTag.length
              ? "(" + item.wdInCardTag.join(",") + ")"
              : "");
          return name.includes(value);
        });
        this.indexList = list;
      } else {
        this.indexList = [];
      }
    },
    // 指标搜索改变  value默认值为空数组时，清空过滤指标数据，组织下拉框改变的时候
    handleIndexChange(value) {
      this.filterIndexArr = value;
      // 组织的切换和指标的搜索公用了一个change，但是组织的切换会传递一个空array，依据这个判断来正确清空组织的option
      if (!value.length) {
        this.indexList = value;
      }
      this.$emit("indexSearchChanged", value);
    },
    // 指标搜索enter事件
    onKeyDown() {
      this.$nextTick(() => {
        this.handleIndexChange(
          Array.from(
            new Set([
              ...this.filterIndexArr,
              ...this.indexList.map((item) => item.pj),
            ])
          )
        );
      });
    },
    loadVideoCom() {
      return new Promise((resolve) => {
        // 加载操作引导视频组件
        if (!window["VideoCom"]) {
          const script = document.createElement("script");
          let fileUrl = this.operationGuideJSUrl;
          if (this.operationGuideJSUrl.indexOf("http") === -1) {
            fileUrl = `${publicPath}${this.operationGuideJSUrl}`;
          }
          script.src = fileUrl + `?t=${Date.now()}`;
          script.onload = () => {
            const exportCom = window["VideoCom"].default;
            this.videoComName = exportCom.myCom;
            resolve();
          };
          document.body.appendChild(script);
        } else {
          const exportCom = window["VideoCom"].default;
          this.videoComName = exportCom.myCom;
          resolve();
        }
      });
    },
    loadSelfReportCom() {
      return new Promise((resolve) => {
        // 加载个人专属报告组件
        if (!window["SelfReports2"]) {
          const script = document.createElement("script");
          let SelfReportsFileUrl = this.selfReportsJSUrl;
          if (this.selfReportsJSUrl.indexOf("http") === -1) {
            SelfReportsFileUrl = `${publicPath}${this.selfReportsJSUrl}`;
          }
          script.src = SelfReportsFileUrl + `?t=${Date.now()}`;
          script.onload = () => {
            const exportCom = window["SelfReports2"].default;
            this.selfReportsComName = exportCom.myCom;
            resolve();
          };
          document.body.appendChild(script);
        } else {
          const exportCom = window["SelfReports2"].default;
          this.selfReportsComName = exportCom.myCom;
          resolve();
        }
      });
    },
    debounce: function(fn, wait = 1000) {
      if (this.fun !== null) {
        clearTimeout(this.fun);
      }
      this.fun = setTimeout(fn, wait);
    },
    // 下拉框输入搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 禁用时间
    disabledDate(current) {
      // Can not select days before today and today
      // 今天之后或者两年前
      return (
        (current && current > moment().endOf("day")) ||
        moment().subtract(2, "years") > current
      );
    },
    // 视频点击
    clickVideo() {
      this.showZZ = true;
    },
    // 关闭遮罩层
    closeZZ() {
      this.showZZ = false;
      this.$refs.videoCom.$refs.videoPlayer.player.pause();
      // 本地存储
      localStorage.setItem("IndexGeneralViewGuide", "read");
    },
    // 时间类型改变
    timeTypeChange(e) {
      let searchFormCache = this.getSearchFormCache();
      const val = (typeof e === "object" ? e.target.value : e) || "";
      // 从本地取出来缓存时间赋值
      let cacheItem = searchFormCache.filter((item) => item.key === val)[0];
      if (val === "day") {
        this.searchForm.time = cacheItem
          ? cacheItem.value
          : moment(new Date().getTime() - 86400000).format(this.dateFormat);
      } else if (val === "week") {
        let year = cacheItem
          ? Number(cacheItem.value.split("-")[0])
          : new Date().getFullYear();
        let week = cacheItem
          ? Number(cacheItem.value.split("-")[1])
          : getWeek(moment().format("YYYY-MM-DD"));
        this.initWeekSelect(year);
        this.selectedYearWeek = [year, week];
      } else {
        this.searchForm.time = cacheItem
          ? cacheItem.value
          : moment(new Date().getTime()).format(this.monthFormat);
      }
    },
    // 发起请求
    sendRequest() {
      this.$emit("pageHeaderChange", {
        ...this.searchForm,
        indexStatus: this.indexStatus,
      });
    },
    // 根据年份更新周下拉框选项
    initWeekSelect(year = new Date().getFullYear()) {
      let weekSelectOptions = this.createWeeks(year);
      if (
        year === this.yearSelectOptions[this.yearSelectOptions.length - 1] &&
        this.yearSelectOptions.length === 3
      ) {
        // 如果是最前边的一年且年份下拉框为三个时，要处理月份选择选两年前的周
        this.weekSelectOptions = weekSelectOptions.filter(
          (item) => item > getWeek(moment().format("YYYY-MM-DD"))
        );
      } else {
        this.weekSelectOptions = weekSelectOptions;
      }
    },
    // 初始化年
    initYearSelect() {
      let yearSelectOptions = [];
      // 两年前的年份不允许选择
      let beginningYear = moment().get("year") - 2;
      // 如果当前月份是12月则，年份选择扣除一年
      if (moment().get("month") + 1 === 12) {
        beginningYear++;
      }
      for (let i = beginningYear; i <= new Date().getFullYear(); i++) {
        yearSelectOptions.unshift(i);
      }
      this.yearSelectOptions = yearSelectOptions;
    },
    // 获取自然周
    createWeeks(year) {
      var d = new Date(year, 0, 1);
      while (d.getDay() != 1) {
        d.setDate(d.getDate() + 1);
      }
      var to = new Date(year + 1, 0, 1);
      var i = 1;
      let arr = [];
      for (var from = d; from < to; ) {
        from.setDate(from.getDate() + 6);
        if (from < to) {
          from.setDate(from.getDate() + 1);
        } else {
          to.setDate(to.getDate() - 1);
        }
        arr.push(i);
        i++;
      }
      return arr;
    },
  },
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage2 {
  &.classic-style {
    & > ._top > ._pageHeader {
      padding-bottom: 18px;
      display: flex;
      .index-search-input {
        margin-left: 10px;
      }
      ._title {
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        height: 28px;
        color: rgba(29, 33, 41, 1);
        margin-right: 4px;
        max-width: 270px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      ._right {
        .timetyep-radio-group {
          margin-right: 8px;
        }
        .org {
          margin-right: 24px;
        }
        .time {
          i {
            color: rgb(78, 89, 105);
          }
        }
        .mode {
          margin-left: 24px;
          width: 64px;
          height: 32px;
          border-radius: 2px;
          background: rgba(242, 243, 245, 1);
          & > div {
            cursor: pointer;
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            border-radius: 2px;
            &.active {
              background-color: rgb(0, 170, 166);
            }
            &.table.active {
              svg {
                line {
                  stroke: #fff;
                }
              }
            }
            &.chart.active {
              svg {
                .rect {
                  stroke: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
  &.hisense-style {
    &.dark {
      & > ._top > ._pageHeader {
        background-color: #313335;
        ._left {
          ._title {
            color: #ffffff;
          }
        }
        ._right {
          color: #9e9e9e;
          .ant-select-search__field__wrap,
          .ant-select-selection-selected-value {
            color: #e2e8ea;
          }
          .timetyep-radio-group,
          .mode {
            border: 1px solid #4e5969;
          }
        }
      }
    }
    & > ._top > ._pageHeader {
      // display: block;
      border-bottom: none;
      ._left {
        align-items: flex-end;
        ._title {
          font-weight: 400;
          font-size: 24px;
          line-height: 34px;
          height: 34px;
          color: #1d2129;
          margin-right: 0px;
          width: 270px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .op {
          color: #20bdb5;
          font-size: 14px;
          line-height: 20px;
          height: 20px;
          margin-bottom: 2px;
          margin-right: 8px;
          cursor: pointer;
          img {
            display: block;
            margin-left: 8px;
            width: 18px;
            height: 18px;
          }
        }
      }
      ._right {
        justify-content: space-between;
        .org {
          margin-left: 16px;
          margin-right: 16px;
        }
        .timetyep-radio-group {
          margin-right: 16px;
          padding: 6px;
          height: 32px;
          color: #a9aeb8;
          font-size: 14px;
          box-sizing: border-box;
          border-radius: 3px;
          border: 1px solid #e5e6eb;
          & > div {
            height: 24px;
            padding: 0 10px;
            line-height: 24px;
            border-radius: 3px;
            cursor: pointer;
            &.active {
              background-color: #00aaa6;
              color: #fff;
            }
          }
        }
        .mode {
          margin-left: 16px;
          height: 32px;
          border-radius: 3px;
          border: 1px solid #e5e6eb;
          padding: 0 12px;
          & > div {
            &:not(:last-child) {
              margin-right: 8px;
            }
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              cursor: pointer;
              display: block;
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }
  }
  & > ._top > ._pageHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgb(229, 231, 235);
    ._left {
      ._title {
        font-family: PingFang SC;
        display: block;
      }
    }
    .guidezz {
      width: 100vw;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.35);
      z-index: 55;
      display: flex;
      align-items: center;
      justify-content: center;
      & > div {
        background-color: #fff;
        border-radius: 3px;
        overflow: hidden;
        .title {
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            display: block;
            font-weight: bold;
          }
          .icon {
            font-size: 16px;
          }
        }
        .img {
          width: 1024px;
          height: 471px;
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
          background-image: url("/minio/mombucket/%E4%BA%91%E5%9B%BE%E6%A6%82%E8%A7%88%E5%BD%95%E5%B1%8F%E5%B0%8F%E5%B1%8F%E5%B9%95.gif");
          cursor: pointer;
        }
        .video {
          .video-js .vjs-icon-placeholder {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }
  }
}
</style>
