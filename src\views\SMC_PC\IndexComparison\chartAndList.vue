<!--
 * @Description: 图表和列表
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-30 14:02:22
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-12-27 09:14:14
-->
<template>
  <div class="_rightBox">
    <div class="_top">
      <div ref="kpiComplate"></div>
      <div style="position: relative;">
        <div ref="kpiCompare" style="width: 100%;height: 100%;"></div>
        <a-select
          @change="rightChartBaseChange"
          v-model="rightChartBase"
          :disabled="disabledBaseSelect"
          style="position: absolute;right: 20px;top: 8px;width: 80px;"
        >
          <a-select-option :value="item" v-for="item in baseList" :key="item">{{
            item
          }}</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="_bottom">
      <CardList
        activePlate="全部"
        :cardListLoading="cardListLoading"
        :list="cardList"
        pageClass="indexComparison"
        :frequency="this.frequency"
        :indexDt="this.indexDt"
        :indexCardDetailInfoJSUrl="indexCardDetailInfoJSUrl"
      />
    </div>
  </div>
</template>
<script>
import CardList from "../Card/cardList.vue";
import * as echarts from "echarts";
import request from "@/utils/requestHttp";
import cloneDeep from "lodash/cloneDeep";
import sortBy from "lodash/sortBy";
import { adminUserUrlPrefix } from "@/utils/utils";
import { dealThousandData } from "../IndexGeneralView/utils";
import Decimal from "decimal.js";
export default {
  props: {
    cardList: Array,
    cardListLoading: Boolean,
    baseList: Array,
    disabledBaseSelect: Boolean, // 是否指标id为空则，禁用图表2右上角下拉框
    frequency: String,
    indexDt: String,
    indexCardDetailInfoJSUrl: String
  },
  components: { CardList },
  data() {
    return {
      kpiComplateChart: null,
      kpiCompareChart: null,
      kpiComplateChartOptions: {
        title: {
          text: "完成情况",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["实际", "目标", "完成率"],
          top: 19,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisPointer: {
              type: "shadow"
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: "{value}"
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            }
          },
          {
            type: "value",
            axisLabel: {
              formatter: "{value} %"
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            }
          }
        ],
        series: [
          {
            name: "实际",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(98, 91, 249, 0.85)"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          },
          {
            name: "目标",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(21, 219, 195, 0.85)"
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)"
            },
            barMaxWidth: 33
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: 1,
            data: [],
            symbol: "none",
            itemStyle: {
              color: "rgba(214, 116, 255, 1)"
            }
          }
        ]
      },
      cloneKpiComplateChartOptions: {},
      kpiCompareChartOptions: {
        title: {
          text: "同期&实际走势",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["同期", "实际", "目标"],
          top: 19,
          right: 116,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          }
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            xAxisIndex: [0],
            height: 16
          },
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            yAxisIndex: [0],
            width: 16,
            orient: "vertical"
          }
        ],
        yAxis: {
          type: "value",
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: "{value}"
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          }
        },
        series: [
          {
            name: "同期",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgba(255, 206, 140, 1)"
            },
            symbol: "circle",
            symbolSize: 6
          },
          {
            name: "实际",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgba(255, 135, 128, 1)"
            },
            symbol: "circle",
            symbolSize: 6
          },
          {
            name: "目标",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgba(159, 105, 246, 1)"
            },
            symbol: "circle",
            symbolSize: 6
          }
        ]
      },
      cloneKpiCompareChartOptions: {},
      rightChartBase: "", // 右侧图表的基地
      requestBody: {} // 图表请求数据
    };
  },
  mounted() {
    this.kpiComplateChart = echarts.init(this.$refs["kpiComplate"]);
    this.kpiCompareChart = echarts.init(this.$refs["kpiCompare"]);
    this.cloneKpiComplateChartOptions = cloneDeep(this.kpiComplateChartOptions);
    this.cloneKpiCompareChartOptions = cloneDeep(this.kpiCompareChartOptions);
    window.onresize = () => {
      console.log("resize--->");
      this.kpiComplateChart.resize();
      this.kpiCompareChart.resize();
    };
  },
  watch: {
    baseList(val) {
      if (val.length) {
        this.rightChartBase = val[0];
      }
    }
  },
  methods: {
    // 设置指标名称
    setIndexName(indexName) {
      this.kpiComplateChartOptions.title.text = `${indexName} 完成情况`;
      this.kpiCompareChartOptions.title.text = `${indexName} 同期&实际走势`;
    },
    getChartData(body) {
      return new Promise(resolve => {
        this.requestBody = body;
        if (!body.baseStr) {
          return;
        }
        request(`${adminUserUrlPrefix["zcx"]}/indexCardInfo/getHBindexDetail`, {
          method: "POST",
          body
        }).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            if (body.type === "1") {
              res = sortBy(
                res,
                item => item.dwppCmTfIndexLibrary?.completionRate || "0"
              );
              res.reverse();
              const xAxis = res.map(item => {
                return item.baseName;
              });
              this.kpiComplateChartOptions.xAxis[0].data = xAxis;
              const baseActualData = res.map(item => {
                return dealThousandData(
                  item.dwppCmTfIndexLibrary?.baseActual,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions,
                  false
                );
              });
              this.kpiComplateChartOptions.series[0].data = baseActualData;
              const targetValueData = res.map(item => {
                return dealThousandData(
                  item.dwppCmTfIndexLibrary?.targetValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions,
                  false
                );
              });
              this.kpiComplateChartOptions.series[1].data = targetValueData;
              const completionRateData = res.map(item => {
                return Decimal(item.dwppCmTfIndexLibrary?.completionRate || "0")
                  .mul(Decimal(100))
                  .toFixed(2, Decimal.ROUND_HALF_UP);
              });
              this.kpiComplateChartOptions.series[2].data = completionRateData;
              this.kpiComplateChartOptions.yAxis[0].axisLabel.formatter = `{value} ${
                res[0].unit === null ? "" : res[0].unit
              }`;
              this.initKpiComplate();
            } else {
              res = sortBy(res, function(item) {
                return item.dwppCmTfIndexLibrary.indexDt;
              });
              const xAxis = res.map(item => {
                return item.dwppCmTfIndexLibrary.indexDt;
              });
              this.kpiCompareChartOptions.xAxis.data = xAxis;
              const baseActualData = res.map(item => {
                return dealThousandData(
                  item.dwppCmTfIndexLibrary?.baseActual,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions,
                  false
                );
              });
              this.kpiCompareChartOptions.series[1].data = baseActualData;
              const targetValueData = res.map(item => {
                return dealThousandData(
                  item.dwppCmTfIndexLibrary?.targetValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions,
                  false
                );
              });
              this.kpiCompareChartOptions.series[2].data = targetValueData;
              const contemValueData = res.map(item => {
                return dealThousandData(
                  item.dwppCmTfIndexLibrary?.contemValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions,
                  false
                );
              });
              this.kpiCompareChartOptions.dataZoom[0].end = 100;
              // 根据数据条数 判断是否显示dataZoom组件
              if (res.length > 6) {
                this.kpiCompareChartOptions.dataZoom[0].show = true;
                this.kpiCompareChartOptions.dataZoom[1].show = true;
                this.kpiCompareChartOptions.grid.bottom = 52;
                if (res.length > 13) {
                  this.kpiCompareChartOptions.dataZoom[0].start = 50;
                } else {
                  this.kpiCompareChartOptions.dataZoom[0].start = 0;
                }
              }

              this.kpiCompareChartOptions.series[0].data = contemValueData;
              this.kpiCompareChartOptions.yAxis.axisLabel.formatter = `{value} ${
                res[0].unit === "null" ? "" : res[0].unit
              }`;
              this.initKpiCompare();
            }
          } else {
            if (body.type === "1") {
              this.kpiComplateChartOptions = cloneDeep(
                this.cloneKpiComplateChartOptions
              );
              this.initKpiComplate();
            } else {
              this.kpiCompareChartOptions = cloneDeep(
                this.cloneKpiCompareChartOptions
              );
              this.initKpiCompare();
            }
          }
          resolve(res);
        });
      });
    },
    // 右侧图表基地改变
    rightChartBaseChange(value) {
      this.requestBody["type"] = "2";
      this.requestBody["baseStr"] = value;
      this.getChartData(this.requestBody);
    },
    // 初始化kpi完成情况图表
    initKpiComplate(options) {
      this.kpiComplateChart.setOption(options || this.kpiComplateChartOptions);
    },
    // 初始化kpi同期情况表
    initKpiCompare(options) {
      this.kpiCompareChart.setOption(options || this.kpiCompareChartOptions);
    }
  }
};
</script>

<style lang="less">
@import url("../common.less");
.indexComparisonPage {
  & > ._bottom {
    & > ._right {
      ._rightBox {
        height: 100%;
        overflow-y: auto;
        box-sizing: border-box;
        padding: 16px;
        ._top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          & > div {
            flex: 1;
            height: 281px;
            background: #f4f5f7;
            border-radius: 8px;
            box-sizing: border-box;
            &:first-child {
              margin-right: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
