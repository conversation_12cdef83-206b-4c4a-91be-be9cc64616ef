<!--
 * @Description: 单个卡片
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 09:36:11
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-12-17 10:36:03
-->
<template>
  <!-- 卡片 -->
  <a-card
    class="card"
    :class="[item.recommend ? 'recommend' : '']"
    @click="showDetail"
  >
    <div class="_flex __top">
      <!-- 版块 -->
      <span class="plate">{{ item.plateName }}</span>
      <template v-if="item.showYC"><span class="YC">预测</span></template>
      <!-- 基地 -->
      <span class="_title" v-if="pageClass !== 'indexGeneralViewPage'">{{
        item.baseName
      }}</span>
      <!-- 拖拽、删除 -->
      <div class="dragDel _flex">
        <a-tooltip placement="top">
          <template slot="title">
            <span>打开报表</span>
          </template>
          <ReportIcon
            @click.stop="openReport(item)"
            class="_report"
            type="icon-cmdt-report"
          />
        </a-tooltip>
        <template
          v-if="!item.recommend && pageClass === 'indexGeneralViewPage'"
        >
          <a-tooltip placement="top">
            <template slot="title">
              <span>走势</span>
            </template>
            <a-icon
              @click.stop="$emit('trendShow', item)"
              type="line-chart"
              class="_zs"
            />
          </a-tooltip>
          <template v-if="!inHiData">
            <a-tooltip placement="top">
              <template slot="title">
                <span>拖拽</span>
              </template>
              <a-icon type="drag" class="_drag" />
            </a-tooltip>
            <a-tooltip placement="top">
              <template slot="title">
                <span>移除</span>
              </template>
              <a-icon
                @click.stop="delCard"
                type="delete"
                theme="filled"
                class="_del"
              />
            </a-tooltip>
          </template>
        </template>
      </div>
    </div>
    <template v-if="pageClass === 'indexGeneralViewPage'">
      <!-- 标题 -->
      <span class="___title">
        {{ item.indexName }}
      </span>
    </template>
    <!-- 数据 -->
    <div class="_data">
      {{ item.realBaseActual }}
      <span>{{ item.unit }}</span>
    </div>
    <template v-if="item.recommend">
      <!-- 推荐时完成率 -->
      <div class="c_completRate">
        完成率
        <b>{{
          `${
            item.dwppCmTfIndexLibrary.completionRate
              ? Decimal(item.dwppCmTfIndexLibrary.completionRate)
                  .mul(Decimal(100))
                  .toFixed(2, Decimal.ROUND_HALF_UP)
              : "-"
          }%`
        }}</b>
      </div>
      <!-- 推荐原因 -->
      <div class="sign _flex">
        <span v-if="item.dwppCmTfIndexLibrary.label1">
          {{ item.dwppCmTfIndexLibrary.label1 }}
        </span>
        <span v-if="item.dwppCmTfIndexLibrary.label2">
          {{ item.dwppCmTfIndexLibrary.label2 }}
        </span>
      </div>
      <!-- 操作 -->
      <div class="_flex _op">
        <div class="_another" @click.stop="$emit('changeRecommend')">
          <a-icon type="sync" />
          换一个
        </div>
        <a-button
          @click.stop="$emit('addToCardList')"
          class="_addtolist"
          size="small"
          type="primary"
          shape="round"
        >
          <a-icon type="plus" />
          添加到列表
        </a-button>
      </div>
      <!-- 推荐图片 -->
      <div class="recommend_bg"></div>
    </template>
    <template v-if="!item.recommend">
      <!-- 目标值 -->
      <div class="_target">
        目标值
        {{ item.realTargetValue }}
        {{ item.unit }}
      </div>
      <!-- 完成率 -->
      <div class="completRate _flex">
        <span style="white-space: nowrap;">完成率</span>
        <span class="process">
          <span
            :class="[
              item.dwppCmTfIndexLibrary.completionRate
                ? parseFloat(
                    Decimal(item.dwppCmTfIndexLibrary.completionRate)
                      .mul(Decimal(100))
                      .toFixed(2, Decimal.ROUND_HALF_UP)
                  ) < 100
                  ? 'b'
                  : 'r'
                : ''
            ]"
            :style="{
              width: `${
                item.dwppCmTfIndexLibrary.completionRate
                  ? parseFloat(item.dwppCmTfIndexLibrary.completionRate) * 100 >
                    100
                    ? 100
                    : parseFloat(item.dwppCmTfIndexLibrary.completionRate) * 100
                  : 0
              }%`
            }"
          ></span>
        </span>
        <span>{{
          `${
            item.dwppCmTfIndexLibrary.completionRate
              ? parseFloat(
                  Decimal(item.dwppCmTfIndexLibrary.completionRate)
                    .mul(Decimal(100))
                    .toFixed(2, Decimal.ROUND_HALF_UP)
                )
              : "-"
          }%`
        }}</span>
      </div>
      <span
        style="width: 100%;height: 1px;background: rgba(0,0,0,0.10);display: block;margin-bottom: 12px;"
      ></span>
      <!-- 同比 -->
      <div class="compare _flex">
        <a-row style="width: 100%;">
          <a-col :span="12">
            <div class="_flex _c">
              <span>同比</span>
              <div
                :style="{
                  color: item.dwppCmTfIndexLibrary.contemRate
                    ? item.dwppCmTfIndexLibrary.indexType === '正向'
                      ? item.dwppCmTfIndexLibrary.contemRate.includes('-')
                        ? '#6495F9'
                        : '#f75050'
                      : item.dwppCmTfIndexLibrary.contemRate.includes('-')
                      ? '#f75050'
                      : '#6495F9'
                    : 'rgba(0, 0, 0, 0.8);'
                }"
                class="_flex"
                v-if="item.isContemRate === 'Y'"
              >
                <a-icon
                  v-if="item.dwppCmTfIndexLibrary.contemRate"
                  :type="
                    item.dwppCmTfIndexLibrary.indexType === '正向'
                      ? item.dwppCmTfIndexLibrary.contemRate.includes('-')
                        ? 'caret-down'
                        : 'caret-up'
                      : item.dwppCmTfIndexLibrary.contemRate.includes('-')
                      ? 'caret-up'
                      : 'caret-down'
                  "
                />
                <span>{{
                  item.dwppCmTfIndexLibrary.contemRate
                    ? Math.abs(
                        Decimal(item.dwppCmTfIndexLibrary.contemRate)
                          .mul(Decimal(100))
                          .toFixed(2, Decimal.ROUND_HALF_UP)
                      ) + "%"
                    : "-"
                }}</span>
              </div>
              <span
                v-if="item.isContemRate !== 'Y'"
                style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
              >
                不对比
              </span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="_flex _c">
              <span>环比</span>
              <div
                v-if="item.isPreviousRate === 'Y'"
                :style="{
                  color: item.dwppCmTfIndexLibrary.previousRate
                    ? item.dwppCmTfIndexLibrary.indexType === '正向'
                      ? item.dwppCmTfIndexLibrary.previousRate.includes('-')
                        ? '#6495F9'
                        : '#f75050'
                      : item.dwppCmTfIndexLibrary.previousRate.includes('-')
                      ? '#f75050'
                      : '#6495F9'
                    : 'rgba(0, 0, 0, 0.8);'
                }"
                class="_flex"
              >
                <a-icon
                  v-if="item.dwppCmTfIndexLibrary.previousRate"
                  :type="
                    item.dwppCmTfIndexLibrary.indexType === '正向'
                      ? item.dwppCmTfIndexLibrary.previousRate.includes('-')
                        ? 'caret-down'
                        : 'caret-up'
                      : item.dwppCmTfIndexLibrary.previousRate.includes('-')
                      ? 'caret-up'
                      : 'caret-down'
                  "
                />
                <span>{{
                  item.dwppCmTfIndexLibrary.previousRate
                    ? Math.abs(
                        Decimal(item.dwppCmTfIndexLibrary.previousRate)
                          .mul(Decimal(100))
                          .toFixed(2, Decimal.ROUND_HALF_UP)
                      ) + "%"
                    : "-"
                }}</span>
              </div>
              <span
                v-if="item.isPreviousRate !== 'Y'"
                style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
              >
                不对比
              </span>
            </div>
          </a-col>
        </a-row>
      </div>
    </template>
  </a-card>
</template>
<script>
import { thousandSplit } from "../IndexGeneralView/utils";
import request from "@/utils/requestHttp";
import Decimal from "decimal.js";
import {
  adminUserUrlPrefix,
  getCardReportList,
  openReport
} from "@/utils/utils";
import { Icon } from "ant-design-vue";
import fontjs from "../../../assets/js/iconfont.js";

const ReportIcon = Icon.createFromIconfontCN({
  scriptUrl: fontjs
});

export default {
  props: {
    item: Object,
    base: String,
    index: Number, // 指标唯一值
    pageClass: String,
    companyName: String, // 公司名称
    // 信数速览内使用
    inHiData: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  components: { ReportIcon },
  data() {
    return {
      Decimal,
      thousandSplit,
      SYS_NAME: window.system
    };
  },
  methods: {
    /**
     * @description: 打开详情抽屉
     */
    showDetail() {
      this.$emit("showDetail", this.item);
    },
    /**
     * @description: 删除卡牌
     */
    delCard() {
      const _this = this;
      this.$confirm({
        title: "确定删除当前卡片?",
        content:
          "当卡片删除后，可通过添加指标卡片途径重新将该卡片显示到网页中。",
        okText: "确定",
        cancelText: "取消",
        onOk() {
          return new Promise(resolve => {
            setTimeout(() => {
              request(
                `${adminUserUrlPrefix["lxp"]}/userIndexRelation/removeByIndexId`,
                {
                  method: "DELETE",
                  body: {
                    indexId: `${_this.item.indexId}==${_this.base}`,
                    type: "0",
                    menu_name: `${_this.companyName}核心KPI概览${
                      _this.SYS_NAME === "industrialInternet"
                        ? "-工业互联网平台"
                        : ""
                    }`
                  }
                }
              ).then(() => {
                _this.$emit("refreshData");
              });
              resolve();
            }, 300);
          });
        },
        onCancel() {}
      });
    },
    // 打开报表
    openReport(item) {
      window.vm.$message.info("正在查询报表....");
      getCardReportList({ ...item, companyName: this.companyName }).then(
        res => {
          window.vm.$message.destroy();
          if (res) {
            window.vm.$message.success("正在打开报表....");
            openReport(item, res);
          } else {
            window.vm.$message.warning("当前卡片没有配置报表");
          }
        }
      );
    }
  }
};
</script>
<style scoped>
.___title {
  height: 20px;
  line-height: 20px;
  margin-right: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 3px;
  display: block;
}
</style>
