/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Bindings=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/data/data.entry.js")}({"./dist/plugins/data/binding.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Core"),f=e.GC$,g=c("Common"),h=c("./dist/plugins/data/data.ns.js"),i=f.isNumeric,j=g.Common.j.Fa,k=null,l=void 0,m="string",b.yL=n,o=b.yL=n={},p="cellBinding",q=new g.Common.ResourceManager(h.SR),r=q.getResource.bind(q),s=function(){function a(a){this.xf=a}return a.prototype.setValue=function(a,b){D(this.xf,a,b)},a.prototype.getValue=function(a){return E(this.xf,a)},a.prototype.getSource=function(){return this.xf},a}(),b.CellBindingSource=s,t=function(){function a(a){this.ad(a)}return a.prototype.bind=function(a,b){var c,d,e,g,h=this,i=h.kj,j=h.Ou;if(b&&h.tUa(b,{type:"bind",oldDataSource:j}),j&&(j.lka&&(j.subscribe=j.lka,delete j.mka,delete j.lka),C(j.dispose)&&j.dispose()),h.zL(h.AL),h.zL(h.BL),h.ad(i),h.Ou=a,a){if(c=h.CL=z(a),!c)throw Error(r().Exp_NotSupportedDataSource);h.DL=h.EL(),i&&f[f.sd]({tc:i.parent}),i&&(d=h.AL,e=a.currentPosition,a.subscribe&&d.push(a.subscribe(h.FL,h)),e&&e.subscribe&&d.push(e.subscribe(h.GL,h)),C(a.subscribe)&&!a.lka&&(g=a.lka=a.subscribe,a.mka=!1,a.subscribe=function(b,c){var d=[].concat.apply([],arguments),e=function(){if(!a.mka&&C(b))return b.apply(c,arguments)};return d[0]=e,g.apply(a,d)}),h.Lx()),h.HL=A(a,c)}return w(i,a)},a.prototype.Lx=function(){var a,b,c,d,e,f=this,g=f.kj,h=f.Ou;if(g&&h&&(a=f.CL,f.zL(f.BL),b=k,a!==p&&(c=g.getActiveRowIndex(),h.currentPosition?(-1<=c&&c<h.count()&&h.currentPosition(c),b=h.currentItem()):b=f.ru(c)),b)){d=function(){g&&g.repaint()};for(e in b)b[e]&&C(b[e].subscribe)&&f.BL.push(b[e].subscribe(d,k,k))}},a.prototype.zL=function(a){var b,c;if(a)for(b=0;b<a.length;b++)c=a[b],c&&C(c.dispose)&&c.dispose()},a.prototype.getRowCount=function(){var a=this,b=a.Ou,c=a.CL,d=0;return b&&c&&c!==p&&(d=o[c].getDataLength(b)),d},a.prototype.getColumnCount=function(){var a,b,c,d=this.HL;return d?d.length:(a=this.Ou,a&&(b=a[0],c=f.getType(b),"null"===c||c===m||"number"===c)?1:0)},a.prototype.getSource=function(){return this.Ou},a.prototype.rL=function(){return this.HL},a.prototype.Nka=function(a){this.HL=a},a.prototype.ru=function(a){var b=this.Ou,c=this.CL,d=k;return b&&c&&c!==p&&(d=o[c].getDataItem(b,a)),d},a.prototype.EL=function(){var a,b,c,d=this;if(!d.DL)for(a=d.getRowCount(),b=0;b<a;b++)if(c=d.ru(b)){d.DL=c.entityAspect&&c.entityType||c.constructor;break}return d.DL},a.prototype.getValue=function(a,b){return x(this,a,b)},a.prototype.setValue=function(a,b,c,d){var e,f=this;return d&&(e=x(f,a,b).value,f.tUa(d,{type:"setValue",row:a,col:b,oldValue:e})),y(f,a,b,c)},a.prototype.sL=function(){var a=o[this.CL];return a&&a.canAdd&&a.canAdd()},a.prototype.tL=function(){var a=o[this.CL];return a&&a.canInsert&&a.canInsert()},a.prototype.vL=function(){var a=o[this.CL];return a&&a.canRemove&&a.canRemove()},a.prototype.uL=function(a,b,c){var d,e,f=this,g=f.Ou;!g||a>f.getRowCount()||(d=f.CL,e=f.EL(),d&&d!==p&&!f.IL&&(f.JL=!0,c&&f.tUa(c,{type:"addItems",row:a,rowCount:b}),o[d].addItems(g,a,b,e),f.JL=!1))},a.prototype.wL=function(a,b,c){var d,e=this,f=e.CL,g=e.getRowCount(),h=e.Ou;!h||a>=g||f&&f!==p&&!e.IL&&(e.JL=!0,d=o[f].removeItems(h,a,b),c&&d&&d.length>0&&e.tUa(c,{type:"removeItems",row:a,removedItems:d}),e.JL=!1)},a.prototype.uUa=function(a,b){var c=this,d=c.Ou,e=c.CL;o[e].undoRemoveItems(d,a,b)},a.prototype.xL=function(a){return B(a||this.Ou)},a.prototype.ad=function(a){var b=this;b.Ou=k,b.CL="",b.DL=k,b.HL=k,b.AL=[],b.BL=[],b.qia=k,b.kj=a,v(a)},a.prototype.FL=function(a){var b,c,d,e,f=this,g=f.kj;if(g&&!f.JL){if(a){if(f.IL=!0,!isNaN(a.length)&&(g.setRowCountCore(a.length),b=g.ITa,j(f.HL)&&0===b.VTa(!1,3)&&(f.HL=A(f.Ou),g.setColumnCount(f.getColumnCount()),c=f.rL())))for(d=0,e=c.length;d<e;d++)b.do("setItem",!1,3,d,{name:c[d]});f.IL=!1}g.$p()}},a.prototype.GL=function(){var a,b=this,c=b.kj,d=b.Ou;c&&d&&d.currentPosition&&(a=d.currentPosition(),c.Jl!==a&&(c.Sr(a,c.Kl,l,l,2),c.$p()))},a.prototype.tUa=function(a,b){if(!(this.kj&&this.kj.ITa&&!j(this.kj.ITa.WTa)&&this.kj.ITa.WTa<=0)){var c=a.rUa;c||(c=a.rUa=[]),b.bindingManager=this,c.push(b)}},a.prototype.sUa=function(a){var b=this,c=a.type,d=a.row;"bind"===c?b.bind(a.oldDataSource):"setValue"===c?b.setValue(d,a.col,a.oldValue):"addItems"===c?b.wL(d,a.rowCount):"removeItems"===c&&b.uUa(d,a.removedItems)},a.prototype.toJSON=function(){var a,b=this.Ou;return b?(a=this.CL,b=a===p?b.getSource():o[a].toJSON(b),{type:a,source:b}):k},a.prototype.fromJSON=function(a){var b,c,d=a&&a.source;d&&(b=a.type,c=void 0,c=b===p?new s(d):o[b].fromJSON(d),c&&this.bind(c))},a}(),b.qL=t;function v(a){var b=a&&a.wq;b&&b.xL()&&a.Wq(d.Events.ResetBinding,{sheet:a})}function w(a,b){var c=a&&a.wq;return!(!c||!c.xL(b))&&(a.clearPendingChanges(),a.Wq(d.Events.InitBinding,{sheet:a,data:b}),!0)}function x(a,b,c){var d,e,f,g,h,i,j,l,m,n=a,q=n.Ou,r={value:k,hasBinding:!1};if(!q)return r;if(d=n.kj,e=n.CL,e===p){if(h=d?d.getBindingPath(b,c):k)return{value:q.getValue(h),hasBinding:!0}}else{if(b<0||a.getRowCount()<=b)return r;if(i=d?d.getColumnCount():a.getColumnCount(),c<0||i<=c)return r;if(d?(n.qia||(n.qia=d.ITa.Hp(!1,3)),f=n.qia[c],f&&(g=f.name)):a.HL&&(g=a.HL[c]),e)return j=o[e],l=f&&f.value,m=C(l)?l:k,j.getValue(q,m,g,b,c)}return r}function y(a,b,c,d){var e,f,g,h,i,j,l,m,n,q=a,r=q.Ou,s=!1;if(!r)return s;if(e=q.kj,f=q.CL,f===p)i=e?e.getBindingPath(b,c):k,i&&(r.setValue(i,d),s=!0);else{if(b<0||a.getRowCount()<=b)return s;if(j=e?e.getColumnCount():a.getColumnCount(),c<0||j<=c)return s;e?(q.qia||(q.qia=e.ITa.Hp(!1,3)),g=q.qia[c],g&&(h=g.name)):a.HL&&(h=a.HL[c]),f&&(l=o[f],m=g&&g.value,n=C(m)?m:k,s=l.setValue(r,n,h,b,c,d))}return s}function z(a){var b=k;return a&&(B(a)?b=p:f.each(o,function(c,d){if(d.isDataSource(a))return b=c,!1})),b}function A(a,b){return a&&b&&b!==p?o[b].getProperties(a):k}function B(a){return s&&a instanceof s}function C(a){return a instanceof Function}function D(a,b,c){var d,e,f,g;if(a&&b)for(d=b.split("."),e=d.length,f=0;f<e&&(g=d[f],a);f++)f===e-1?C(a[g])?a[g](c):a[g]=c:C(a[g])?(j(a[g]())&&a[g]({}),a=a[g]()):(j(a[g])&&(a[g]={}),a=a[g])}b.setValueByPath=D;function E(a,b,c){var d,e,f,g;if(!a||!b)return k;d=null,c?(d=c[b],d||(d=b.split("."),c[b]=d)):d=b.split("."),e=d.length,f=0;do{if(g=d[f],a=C(a[g])?a[g]():a[g],j(a))return k;++f}while(f<e);return a}b.LL=E,u=function(){function a(){this.pathCache={}}return a.prototype.isDataSource=function(a){return f.isArray(a)},a.prototype.getDataLength=function(a){return a.length},a.prototype.getDataItem=function(a,b){return a[b]},a.prototype.addItems=function(a,b,c,d){var e,f;for(e=0;e<c;e++)f={},d&&(f=d.createEntity?d.createEntity():new d),a.splice(b,0,f)},a.prototype.removeItems=function(a,b,c){var d=this.getDataLength(a);if(b<d)return a.splice(b,Math.min(c,d-b))},a.prototype.undoRemoveItems=function(a,b,c){for(var d=0,e=c.length;d<e;d++)a.splice(b+d,0,c[d])},a.prototype.getProperties=function(a){var b,c,d=a.length,e=k;if(d>0){e=[],b=a[0];for(c in b)C(b[c])||e.push(c)}return e},a.prototype.getValue=function(a,b,c,d,e){var f=k,g=!1,h=this.getDataItem(a,d);return j(h)||(b?(f=b(h),g=!0):c?(f=E(h,c,this.pathCache),g=!0):(typeof h===m||i(h))&&0===e&&(f=h,g=!0)),{value:f,hasBinding:g}},a.prototype.setValue=function(a,b,c,d,e,f){var g=!1,h=this.getDataItem(a,d);return j(h)||(b?(b(h,f),g=!0):c?(D(h,c,f),g=!0):(typeof h===m||i(h))&&0===e&&(a[d]=f,g=!0)),g},a.prototype.fromJSON=function(a){return a},a.prototype.toJSON=function(a){return a},a}(),o.defaultBindingSource=new u},"./dist/plugins/data/data.dataView.js":function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./dist/plugins/data/binding.js"),b.yL=e.yL,f=d.Common.j.Fa,g=null,h=function(){function a(){}return a.prototype.isDataSource=function(a){var b=window.wijmo;return b&&b.data&&b.data.isDataView&&b.data.isDataView(a)},a.prototype.getDataLength=function(a){return a.count()},a.prototype.getDataItem=function(a,b){return a.item(b)},a.prototype.addItems=function(a,b,c,d){if(d)for(var e=0;e<c;e++)a.mka=!0,a.add(d.createEntity?d.createEntity():new d),a.mka=!1,a.commitEdit()},a.prototype.removeItems=function(a,b,c){var d,e,f,g,h=this.getDataLength(a);if(b<h){for(d=[],e=Math.min(c,h-b),f=0;f<e;f++)g=a.item(b),a.remove(g),d.push(g);return d}},a.prototype.undoRemoveItems=function(a,b,c){var d,e,f=a.getSource();for(d=0,e=c.length;d<e;d++)f.splice(b+d,0,c[d]);a.refresh()},a.prototype.getProperties=function(a){var b,c=0,d=g,e=a.getProperties();if(e&&e.length>0)for(c=e.length,d=[],b=0;b<c;b++)d.push(e[b].name);return d},a.prototype.canInsert=function(){return!1},a.prototype.getValue=function(a,b,c,d,e){var h=g,i=!1,j=this.getDataItem(a,d);return f(j)||(b?(h=b(j),i=!0):c&&(h=a.getProperty(j,c),i=!0)),{value:h,hasBinding:i}},a.prototype.setValue=function(a,b,c,d,e,g){var h=!1,i=this.getDataItem(a,d);return f(i)||(b?(b(i,g),h=!0):c&&(a.setProperty(i,c,g),h=!0)),h},a.prototype.fromJSON=function(a){return a},a.prototype.toJSON=function(a){return a.local},a}(),e.yL.dataViewBinding=new h},"./dist/plugins/data/data.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),c("./dist/plugins/data/worksheet-databind.js");var e=c("./dist/plugins/data/binding.js");b.CellBindingSource=e.CellBindingSource,b.qL=e.qL,b.setValueByPath=e.setValueByPath,b.LL=e.LL,d(c("./dist/plugins/data/data.ko.js")),d(c("./dist/plugins/data/data.dataView.js")),d(c("./dist/plugins/data/data.ns.js"))},"./dist/plugins/data/data.ko.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("./dist/plugins/data/binding.js"),b.yL=f.yL,g=e.Common.j.Fa,h=d.GC$,i=h.isFunction,j=h.getType,k=h.isNumeric,l=null,m="string",n=window.ko,o=function(){function a(){}return a.prototype.isDataSource=function(a){return n&&n.isObservable(a)},a.prototype.getDataLength=function(a){return a().length},a.prototype.getDataItem=function(a,b){return a()[b]},a.prototype.addItems=function(a,b,c,d){var e,f;for(e=0;e<c;e++)f={},d&&(f=d.createEntity?d.createEntity():new d),a.splice(b,0,f)},a.prototype.removeItems=function(a,b,c){var d=this.getDataLength(a);if(b<d)return a.splice(b,Math.min(c,d-b))},a.prototype.undoRemoveItems=function(a,b,c){for(var d=0,e=c.length;d<e;d++)a.splice(b+d,0,c[d])},a.prototype.getProperties=function(a){var b,c,d=a(),e=d.length,f=[];if(e>0){b=d[0];for(c in b)i(b[c])?n&&n.isObservable(b[c])&&f.push(c):f.push(c)}return f},a.prototype.getValue=function(a,b,c,d,e){var h=l,i=!1,n=this.getDataItem(a,d);return g(n)||(b?(h=b(n),i=!0):c?(h=f.LL(n,c),i=!0):(j(n)===m||k(n))&&0===e&&(h=n,i=!0)),{value:h,hasBinding:i}},a.prototype.setValue=function(a,b,c,d,e,h){var i=!1,l=this.getDataItem(a,d);return g(l)||(b?(b(l,h),i=!0):c?(f.setValueByPath(l,c,h),i=!0):(j(l)===m||k(l))&&0===e&&(a()[d]=h,i=!0)),i},a.prototype.fromJSON=function(a){return n?n.observableArray(a):l},a.prototype.toJSON=function(a){return a()},a}(),f.yL.koBinding=new o,n&&(p=n.bindingHandlers,p["gc-spread-sheets"]=p["gcspread-sheets"]=p.wijspread={init:function(a,b,c,e){var f,g,i,j,k,l,n,o,p,q,r,s;if(!h(a).data("workbook")&&(f=b(),g=f&&f.sheets,i=new d.Workbook(h("#"+a.id)[0],f),g)){for(j=void 0,k=void 0,l=g.length,n=i.getSheetCount();n<l;)j=i.vv(i.wv(n)),i.ow(n,0,j),n=i.getSheetCount();for(o=0;o<l;o++)j=i.getSheet(o),k=g[o],p=k.name,q=k.autoGenerateColumns,r=k.data,s=k.columns,typeof p===m&&p.length>0&&j.cq(p),"boolean"==typeof q&&(j.autoGenerateColumns=q),r&&j.setDataSource(r),s&&s.length>0&&(j.autoGenerateColumns=!1,j.bindColumns(s))}},update:function(a,b,c,d){}})},"./dist/plugins/data/data.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/data/data.res.en.js");b.SR={en:d}},"./dist/plugins/data/data.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_NotSupportedDataSource="The data source is not supported!"},"./dist/plugins/data/worksheet-databind.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("./dist/plugins/data/binding.js");function o(a,b,c){"string"==typeof c&&(c={name:c}),a.ITa.do("setItem",!1,3,b,c);var d=c&&c.formatter,e=c&&c.cellType;d&&a.setFormatter&&a.setFormatter(-1,b,d),e&&a.setCellType(-1,b,e),c&&c.width&&a.setColumnWidth(b,c.width)}g=e.Common.j.Fa,h=d.GC$.isEmptyObject,i=d.GC$.extend,j=d.Worksheet.prototype,k=null,l="bindingPath",m=e.Common.l,n={getDataSource:function(){var a=this.wq;return a?a.getSource():k},setDataSource:function(a,b){var c,d,e,g,h,i=this,j=i.wq;if(j&&j.getSource()!==a){if(b&&i.vs(),a){if(j=i.wq,c=j.bind(a,i.ITa.zTa),!c&&(i.setRowCountCore(j.getRowCount()),i.autoGenerateColumns&&(i.setColumnCount(j.getColumnCount()),d=j.rL())))for(e=0,g=d.length;e<g;e++)i.ITa.do("setItem",!1,3,e,{name:d[e]})}else i.wq=new f.qL(i);i.clearPendingChanges(),i.recalcAll&&i.recalcAll(),h=i.tu(),h&&h.PA===i&&h.hZ(),i.$p()}},bindColumn:function(a,b){var c=this;c.suspendPaint();try{o(c,a,b)}finally{c.resumePaint()}},bindColumns:function(a){var b,c,d=this;d.suspendPaint();try{if(a)for(b=a.length,isNaN(b)||d.setColumnCount(b),c=0;c<b;c++)o(d,c,a[c])}finally{d.resumePaint()}},getDataItem:function(a){var b,c,d,e,f,h,i=this,j=i.wq;if(!j||0===j.getRowCount())return k;if(b=i.getColumnCount(),c={},d=j.ru(a))for(e in d)d.hasOwnProperty(e)&&"function"!=typeof e&&(c[e]=d[e]);for(f=0;f<b;f++)h=i.ITa.Jp(!1,3,f),h&&h.name&&h.name.length>0&&g(c[h.name])&&(c[h.name]=i.getValue(a,f));return c},getDataColumnName:function(a){var b,c=this;return 0<=a&&a<c.getColumnCount()&&c.getDataSource()?(b=c.ITa.Jp(!1,3,a),b&&(b.displayName||b.name)):k},getBindingPath:function(a,b){return this.ITa.getValueForKey(a,b,l)},setBindingPath:function(a,b,c){var d=this;return d.ITa.do("setValueForKey",a,b,l,c),d.$p(),d}},i(j,n),d.CellRange.prototype.bindingPath=d.CellRange.Pl(j.getBindingPath,j.setBindingPath);function p(a,b,c,d){var e,f,h,i,j=a.wq;for(e=0;e<c;e++)for(f=0;f<d;f++)h=j.getValue(e,f).value,g(h)||(i=h,m.Ska(h)&&(i=m.Daa(h)),a.qI(b,e,f,i))}function q(a,b,c,d){var e,f,h,i,j,k=a.options.colHeaderAutoTextIndex;for(e=0;e<c;e++)if(k>=0&&e===k||k===-1&&e===c-1)for(f=0;f<d;f++)h=b[e]&&b[e][f]&&b[e][f].value,i=a.ITa.Jp(!1,3,f),g(h)&&i&&(j=i.displayName||i.name,j&&a.qI(b,e,f,j))}d.Worksheet.$n("binding",{init:function(){var a=this;a.autoGenerateColumns=!0,a.wq=new f.qL(a)},dispose:function(a){var b=this;a.clearCache!==!1&&(b.wq=k)},onLayoutChanged:function(a){var b,c,d,e,f,g=this,h=a.changeType,i=a.row,j=a.rowCount,l=a.sheetArea,m=g.wq;if("addingRows"===h)m&&m.sL()===!1?a.canAdd=!1:m&&m.tL()===!1&&(a.newRow=g.getRowCount());else if("addRows"===h)m&&m.uL(i,j,g.ITa.zTa);else if("deletingRows"===h){if(m)if(m.vL()===!1)a.canDelete=!1;else for(g.Ts||(g.Ts=[]),b=g.Ts,c=g.ITa,d=m.getRowCount(),e=0;e<j&&i+e<c.getRowCount();e++)f=k,i+e<d&&(f=m.ru(i+e)),b.push({row:i+e,data:f})}else"deleteRows"===h?m&&m.wL(i,j,g.ITa.zTa):"settingRowCount"===h&&(!m.getSource()||m.xL()||3!==l&&2!==l||(a.canSet=!1))},toJson:function(a,b){var c,d,e,f=this,g=f.getColumnCount(),i=f.getDataSource();b&&b.includeBindingSource&&i&&(c=a.data.dataTable||{},p(f,c,f.getRowCount(),g),h(c)||(a.data.dataTable=c),d=a.colHeaderData.dataTable||{},q(f,d,f.getRowCount(1),g),h(d)||(a.colHeaderData.dataTable=d)),e=f.autoGenerateColumns,e!==!0&&(a.autoGenerateColumns=f.autoGenerateColumns)},fromJson:function(a,b){var c,d,e,h,i=this;i.wq=new f.qL(i),b&&(c=i.wq,d=a.dataSource,e=a.dataBinding,d&&c.bind(d),e&&c.fromJSON(e,b)),h=a.autoGenerateColumns,g(h)||(i.autoGenerateColumns=h)},preFromJson:function(){this.wq=new f.qL(this)}}),d.lUa.$n("binding",{priority:6e3,getValue:function(a){var b,c=a.row,d=a.col,e=a.sheetArea,f=this.kj.wq;3===e&&f&&f.Ou&&(b=f.getValue(c,d),a.isValueGet=b.hasBinding,a.value=b.value)},setValue:function(a){var b,c,d,e=a.row,f=a.col,g=a.value,h=a.sheetArea,i=this.kj,j=i.wq;3===h&&j&&j.Ou&&(b=j.getValue(e,f),b.hasBinding&&(c=b.value,g!==c&&(i.isDirtySuspended()||(d=i.getDataItem(e),this.Cp(e,f,{Dp:d,Ep:c},h)),j.setValue(e,f,g,i.ITa.zTa)),a.isValueSet=!0))},undo:function(a){var b,c,d=a.rUa;if(d)for(b=d.length-1;b>=0;b--)c=d[b],c.bindingManager.sUa(c)}})},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});