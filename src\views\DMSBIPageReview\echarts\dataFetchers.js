/**
 * 图表数据获取相关方法
 * 负责从API获取数据并进行必要的处理
 */

import {
  getDailyProductPlanRate,
  getPassThroughRate,
  getWeldingLeak,
  queryOqcSpotCheck,
  getDowntimeLoss,
  getStationLoss
} from '../Api';

/**
 * 获取plantCode，优先从localStorage获取，如果获取不到则使用默认值6210
 * @returns {string} plantCode
 */
function getPlantCode() {
  return window.localStorage.getItem("plantCode") || "6210";
}

/**
 * 获取日生产计划执行率数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期
 * @param {string} params.teamName 班组名称
 * @returns {Promise<Object>} 日生产计划执行率数据
 */
export async function fetchDailyProductPlanRate(params) {
  try {
    // 确保params是一个对象
    const queryParams = params || {};

    // 添加plantCode参数
    queryParams.plantCode = getPlantCode();

    // 调用接口获取数据
    const result = await getDailyProductPlanRate(queryParams);
    return result || {};
  } catch (error) {
    console.error("获取日生产计划执行率数据失败:", error);
    return {};
  }
}

/**
 * 获取直通率数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期
 * @param {string} params.teamName 班组名称
 * @returns {Promise<Object>} 直通率数据
 */
export async function fetchPassThroughRateData(params) {
  try {
    // 确保params是一个对象
    const queryParams = params || {};

    // 添加plantCode参数
    queryParams.plantCode = getPlantCode();

    // 调用接口获取数据
    const result = await getPassThroughRate(queryParams);
    return result || {};
  } catch (error) {
    console.error("获取直通率数据失败:", error);
    return {};
  }
}

/**
 * 获取OQC抽检不良率数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期
 * @param {string} params.teamName 班组名称
 * @returns {Promise<Object>} OQC抽检不良率数据
 */
export async function fetchOqcSpotCheckData(params) {
  try {
    // 确保params是一个对象
    const queryParams = params || {};

    // 添加plantCode参数
    queryParams.plantCode = getPlantCode();

    // 调用接口获取数据
    const result = await queryOqcSpotCheck(queryParams);
    return result || {};
  } catch (error) {
    console.error("获取OQC抽检不良率数据失败:", error);
    return {};
  }
}

/**
 * 获取焊接泄露率数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期
 * @param {string} params.teamName 班组名称
 * @returns {Promise<Object>} 焊接泄露率数据
 */
export async function fetchWeldingLeakData(params) {
  try {
    // 确保params是一个对象
    const queryParams = params || {};

    // 添加plantCode参数
    queryParams.plantCode = getPlantCode();

    // 调用接口获取数据
    const result = await getWeldingLeak(queryParams);
    return result || {};
  } catch (error) {
    console.error("获取焊接泄露率数据失败:", error);
    return {};
  }
}

/**
 * 获取停产损失时间数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期
 * @param {string} params.teamName 班组名称
 * @returns {Promise<Object>} 停产损失时间数据
 */
export async function fetchDowntimeLossData(params) {
  try {
    // 确保params是一个对象
    const queryParams = params || {};

    // 添加plantCode参数
    queryParams.plantCode = getPlantCode();

    // 调用接口获取数据
    const result = await getDowntimeLoss(queryParams);
    return result || {};
  } catch (error) {
    console.error("获取停产损失时间数据失败:", error);
    return {};
  }
}

/**
 * 获取过站损失时间数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期
 * @param {string} params.teamName 班组名称
 * @returns {Promise<Object>} 过站损失时间数据
 */
export async function fetchStationLossData(params) {
  try {
    // 确保params是一个对象
    const queryParams = params || {};

    // 添加plantCode参数
    queryParams.plantCode = getPlantCode();

    // 调用接口获取数据
    const result = await getStationLoss(queryParams);
    return result || {};
  } catch (error) {
    console.error("获取过站损失时间数据失败:", error);
    return {};
  }
}

// 默认导出所有方法
export default {
  fetchDailyProductPlanRate,
  fetchPassThroughRateData,
  fetchOqcSpotCheckData,
  fetchWeldingLeakData,
  fetchDowntimeLossData,
  fetchStationLossData
};
