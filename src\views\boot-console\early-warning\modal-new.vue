<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: y<PERSON>hengqi.ex
 * @LastEditTime: 2025-03-28 09:23:26
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    class="earlyModal"
    :width="1200"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <!-- 基础表单 -->
    <a-form-model
      ref="baseForm"
      :model="baseForm"
      :rules="baseFormRules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="公司" prop="signOrgId">
        <template v-if="this.isEdit">
          <a-input v-model="baseForm.signOrg" disabled />
        </template>
        <template v-else>
          <a-select
            v-model="baseForm.signOrgId"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
            @change="companyChange"
          >
            <a-select-option
              :value="item.key"
              v-for="item in companyList"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
      <a-form-model-item label="版块" prop="businessSegmentsId">
        <template v-if="this.isEdit">
          <a-input v-model="baseForm.businessSegments" disabled />
        </template>
        <template v-else>
          <a-select
            v-model="baseForm.businessSegmentsId"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
            @change="plateChange"
          >
            <a-select-option
              :value="item.businessSegmentsId"
              v-for="item in plateList"
              :key="item.businessSegmentsId"
            >
              {{ item.businessSegments }}
            </a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
      <!-- 选择指标 -->
      <a-form-model-item label="选择指标" prop="indexId">
        <div style="display: flex;align-items: center;height: 39.9999px;">
          <template v-if="!isEdit">
            <a-select
              show-search
              :filter-option="indexIdFilterOption"
              v-model="baseForm.indexId"
              :dropdownMatchSelectWidth="false"
              style="width: 100%"
              @change="indexChange"
            >
              <a-select-option
                :value="item.indexId"
                v-for="item in indexList"
                :key="item.indexId"
              >
                {{ item.indexName }}
              </a-select-option>
            </a-select>
          </template>
          <template v-else>
            <a-input
              disabled
              :value="baseForm.indexName"
              style="width: 100%;"
            />
          </template>
          <a-tag
            color="blue"
            v-if="baseForm.indexType"
            style="margin-left: 10px;margin-right: 0px;"
          >
            {{ baseForm.indexType }}指标
          </a-tag>
        </div>
      </a-form-model-item>
      <!-- 选择计算组织 -->
      <a-form-model-item label="计算组织" prop="org">
        <template v-if="!isEdit">
          <a-tree-select
            :disabled="isEdit"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            dropdownClassName="orgTreeSelect"
            :value="baseForm.org"
            :tree-data="orgTreeData"
            :showSearch="true"
            placeholder="请选择"
            @change="orgChange"
          />
        </template>
        <template v-else>
          <a-input disabled :value="baseForm.orgName" style="width: 100%;" />
        </template>
      </a-form-model-item>
      <!-- 选择维度 -->
      <a-form-model-item label="维度" prop="wd">
        <template v-if="!isEdit">
          <a-select
            show-search
            v-model="baseForm.wd"
            :dropdownMatchSelectWidth="false"
            style="width: 100%;"
            :filter-option="filterOption"
            @change="wdChange"
          >
            <a-select-option
              :value="item.key"
              v-for="item in wdList"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </template>
        <template v-else>
          <a-input disabled :value="baseForm.wdName" style="width: 100%;" />
        </template>
      </a-form-model-item>
      <a-form-model-item label="预警状态" prop="warnStatus">
        <a-switch
          checked-children="开"
          un-checked-children="关"
          v-model="baseForm.warnStatus"
        />
      </a-form-model-item>
      <!-- 预警类型 -->
      <a-form-model-item label="预警类型" prop="warnType">
        <a-select
          v-model="baseForm.warnType"
          style="width: 100%"
          @change="warnTypeChange"
        >
          <a-select-option :value="0" :key="0">
            通知
          </a-select-option>
          <a-select-option :value="1" :key="1">
            工单
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 预警等级 -->
      <!-- <a-form-model-item label="预警等级" prop="warnLevel">
        <a-select v-model="baseForm.warnLevel" style="width: 100%">
          <a-select-option :value="0" :key="0">
            普通
          </a-select-option>
          <a-select-option :value="1" :key="1">
            紧急
          </a-select-option>
          <a-select-option :value="2" :key="2">
            特急
          </a-select-option>
        </a-select>
      </a-form-model-item> -->
    </a-form-model>
    <a-divider></a-divider>
    <!-- 预警人数组 -->
    <a-form-model
      :rules="roleFormRules"
      ref="roleForm"
      style="width: 100%; box-size: border-box; padding-left: 100px;"
      layout="inline"
      class="emailSetting"
      :style="{
        '--borderColor':
          index !== emailArr.length - 1 ? 'gainsboro' : 'transparent',
      }"
      :model="item"
      v-for="(item, index) in emailArr"
      :key="`email${index}`"
    >
      <div style="display: flex;align-items: center;">
        <a-form-model-item label="预警接收人" prop="email">
          <a-select
            mode="multiple"
            :value="item.email"
            placeholder="预警接收人"
            style="width: 300px;margin-right: 20px"
            :filter-option="false"
            :not-found-content="fetching ? undefined : null"
            @search="fetchUser('email', $event)"
            @change="handleChange(index, 'email', $event)"
          >
            <a-spin v-if="fetching" slot="notFoundContent" size="small" />
            <a-select-option v-for="d in userData['email']" :key="d.mail">
              {{ d.cn }}({{ d.uid }}-{{ d.mail }})
              <div>{{ d.o }}</div>
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="同步告知人" prop="notifier">
          <a-select
            mode="multiple"
            :value="item.notifier"
            placeholder="同步告知人"
            style="width: 300px;margin-right: 20px"
            :filter-option="false"
            :not-found-content="fetching ? undefined : null"
            @search="fetchUser('notifier', $event)"
            @change="handleChange(index, 'notifier', $event)"
          >
            <a-spin v-if="fetching" slot="notFoundContent" size="small" />
            <a-select-option v-for="d in userData['notifier']" :key="d.mail">
              {{ d.cn }}({{ d.uid }}-{{ d.mail }})
              <div>{{ d.o }}</div>
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="拆分对策审核人" prop="reviewer">
          <a-select
            mode="multiple"
            :value="item.reviewer"
            placeholder="同步告知人"
            style="width: 300px;margin-right: 20px"
            :filter-option="false"
            :not-found-content="fetching ? undefined : null"
            @search="fetchUser('reviewer', $event)"
            @change="handleChange(index, 'reviewer', $event)"
          >
            <a-spin v-if="fetching" slot="notFoundContent" size="small" />
            <a-select-option v-for="d in userData['reviewer']" :key="d.mail">
              {{ d.cn }}({{ d.uid }}-{{ d.mail }})
              <div>{{ d.o }}</div>
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </div>
      <div style="display: flex;align-items: center;">
        <a-form-model-item label="预警推送频次" prop="indexPushFrequency">
          <a-select
            v-model="item.indexPushFrequency"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 150px;"
            @change="
              (value) => {
                $set(
                  emailArr[index],
                  'executionTime',
                  getIndexFrequencyIdZH(value) === '日' ? '' : '1'
                );
              }
            "
          >
            <a-select-option
              :value="zitem.indexFrequencyId"
              v-for="zitem in indexPushFrequencyList"
              :key="zitem.indexFrequencyId"
            >
              {{ zitem.indexFrequency }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item prop="executionTime">
          <template #label>
            <label class="ant-form-item-required">邮件推送时间</label>
          </template>
          <div style="padding-top: 4px;">
            <template
              v-if="
                getIndexFrequencyIdZH(item.indexPushFrequency) === '日' ||
                  !getIndexFrequencyIdZH(item.indexPushFrequency)
              "
            >
              <a-input
                style="width: 60px;margin-right: 5px;"
                disabled
                :value="'每日'"
              />
            </template>
            <template v-else>
              <a-select
                v-model="item.executionTime"
                :dropdownMatchSelectWidth="false"
                style="width: 120px"
              >
                <a-select-option
                  :value="zitem.key"
                  v-for="zitem in getIndexFrequencyIdZH(
                    item.indexPushFrequency
                  ) === '月'
                    ? monthSelectList
                    : weekSelectList"
                  :key="zitem.key"
                >
                  {{ zitem.value }}
                </a-select-option>
              </a-select>
            </template>
            <a-form-model-item prop="executionHour" required>
              <a-select
                v-model="item.executionHour"
                style="width: 80px;margin-right: 85px;"
              >
                <a-select-option :value="i" :key="i" v-for="i in 24">
                  {{ i }}点
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </div>
        </a-form-model-item>
        <a-form-model-item v-if="!isEdit">
          <a-button
            type="default"
            @click="removeEmail(index)"
            icon="minus"
            style="margin-right: 10px;"
            shape="circle"
            v-if="emailArr.length > 1"
          >
          </a-button
          ><a-button
            type="primary"
            v-if="index === emailArr.length - 1"
            @click="pushOneEmail"
            size="small"
          >
            <a-icon type="plus"></a-icon>
            新增一行
          </a-button>
        </a-form-model-item>
      </div>
    </a-form-model>
    <a-divider></a-divider>
    <!-- 预警条件 -->
    <a-button
      type="primary"
      v-if="!isEdit"
      style="margin-bottom: 20px;"
      @click="addOneCondition"
      >新增预警条件</a-button
    >
    <a-table
      :pagination="false"
      :columns="
        !isEdit
          ? [
              ...columns,
              {
                title: '操作',
                key: 'action',
                scopedSlots: { customRender: 'action' },
              },
            ]
          : columns
      "
      :data-source="conditionArr"
    >
      <!-- 序号 -->
      <template slot="index" slot-scope="text, record, index">
        <span>{{ index + 1 }}</span>
      </template>
      <!-- 完成率设置/同比设置/环比设置 -->
      <template slot="completionRate" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`completionRateFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 10px;"
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in equalList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
          <a-input-number
            style="margin-right: 3px;"
            v-model="record['completionRate']"
          />%
        </div>
      </template>
      <template slot="tb" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`tbFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 10px;"
            allowClear
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in otherEqualList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
          <a-input-number
            :min="0"
            style="margin-right: 3px;"
            v-model="record['tb']"
          />%
        </div>
      </template>
      <template slot="hb" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`hbFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 10px;"
            allowClear
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in otherEqualList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
          <a-input-number
            :min="0"
            style="margin-right: 3px;"
            v-model="record['hb']"
          />%
        </div>
      </template>
      <template slot="actualValueDeterioration" slot-scope="text, record">
        <a-input-number :min="0" v-model="record['actualValueDeterioration']" />
      </template>
      <template slot="noUpStandard" slot-scope="text, record">
        <a-input-number :min="0" v-model="record['noUpStandard']" />
      </template>
      <!-- 操作栏 -->
      <span slot="action" slot-scope="text, record, index">
        <a-tooltip placement="top">
          <template slot="title">
            <span>删除</span>
          </template>
          <a-icon @click="removeCondition(index)" type="delete" />
        </a-tooltip>
      </span>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import cloneDeep from "lodash/cloneDeep";
import debounce from "lodash/debounce";
import uniqBy from "lodash/uniqBy";
export default {
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      userData: {
        email: [],
        notifier: [],
        reviewer: [],
      }, // 下拉框用户数据
      companyList: [], // 公司列表
      plateList: [], // 版块列表
      fetching: false, // 查询用户loading
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      baseForm: {
        signOrgId: "",
        signOrg: "",
        businessSegmentsId: "",
        businessSegments: "",
        indexId: "",
        indexFrequencyId: "",
        indexType: "",
        org: "",
        orgName: "",
        warnStatus: false,
        wd: "",
        wdName: "",
        warnType: 0,
        // warnLevel: 0
      },
      emailArr: [
        {
          email: [],
          indexPushFrequency: "",
          executionTime: "",
          executionHour: "",
          notifier: [],
          reviewer: [],
        },
      ], // 预警人数组
      conditionArr: [
        {
          completionRateFlag: "<",
          completionRate: "",
          tbFlag: "恶化>",
          tb: "",
          hbFlag: "恶化>",
          hb: "",
          actualValueDeterioration: "",
          noUpStandard: "",
        },
      ], // 预警条件数组
      indexList: [], // 指标数据
      orgTreeData: [], // 组织数据
      ppOrgData: [], // 组织数据平铺
      ppCopyOrgData: [], // 用于检测数据
      wdList: [], // 维度列表
      monthSelectList: [], // 月下拉框
      weekSelectList: [], // 周下拉框
      baseFormRules: {
        signOrgId: [
          {
            required: true,
            message: "请选择公司",
            trigger: "change",
          },
        ],
        businessSegmentsId: [
          {
            required: true,
            message: "请选择版块",
            trigger: "change",
          },
        ],
        indexId: [
          {
            required: true,
            message: "请选择指标",
            trigger: "blur",
          },
        ],
        org: [
          {
            required: true,
            message: "请选择计算组织",
            trigger: "change",
          },
        ],
        wd: [
          {
            required: true,
            message: "请选择维度",
            trigger: "change",
          },
        ],
        warnStatus: [
          {
            required: true,
            message: "请选择指标预警开关",
            trigger: "change",
          },
        ],
        // warnLevel: [
        //   {
        //     required: true,
        //     message: "请选择指标预警等级",
        //     trigger: "change"
        //   }
        // ],
        warnType: [
          {
            required: true,
            message: "请选择指标预警类型",
            trigger: "change",
          },
        ],
      },
      roleFormRules: {
        email: [
          {
            required: true,
            message: "请填写预警接收人",
            trigger: ["change", "blur"],
          },
        ],
        indexPushFrequency: [
          {
            required: true,
            message: "请选择预警推送频次",
            trigger: ["change", "blur"],
          },
        ],
        executionHour: [
          {
            required: true,
            message: "请选择邮件推送时间",
            trigger: ["change", "blur"],
          },
        ],
      },
      isEdit: false, // 是否编辑状态
      frequencyList: [], // 频率列表
      equalList: [
        {
          key: "<",
          value: "<",
        },
        {
          key: "<=",
          value: "<=",
        },
        {
          key: "=",
          value: "=",
        },
        {
          key: ">",
          value: ">",
        },
        {
          key: ">=",
          value: ">=",
        },
      ],
      otherEqualList: [
        {
          key: "恶化<",
          value: "恶化<",
        },
        {
          key: "恶化<=",
          value: "恶化<=",
        },
        {
          key: "恶化=",
          value: "恶化=",
        },
        {
          key: "恶化>",
          value: "恶化>",
        },
        {
          key: "恶化>=",
          value: "恶化>=",
        },
        {
          key: "改善<",
          value: "改善<",
        },
        {
          key: "改善<=",
          value: "改善<=",
        },
        {
          key: "改善=",
          value: "改善=",
        },
        {
          key: "改善>",
          value: "改善>",
        },
        {
          key: "改善>=",
          value: "改善>=",
        },
      ],
      columns: [
        {
          title: "序号",
          key: "index",
          scopedSlots: { customRender: "index" },
          width: 80,
        },
        {
          title: "完成率设置",
          scopedSlots: { customRender: "completionRate" },
          width: 120,
        },
        {
          title: "同比设置",
          scopedSlots: { customRender: "tb" },
          width: 120,
        },
        {
          title: "环比设置",
          scopedSlots: { customRender: "hb" },
          width: 120,
        },
        {
          title: "实际值连续恶化设置",
          key: "actualValueDeterioration",
          dataIndex: "actualValueDeterioration",
          scopedSlots: { customRender: "actualValueDeterioration" },
        },
        {
          title: "连续未达标设置",
          key: "noUpStandard",
          dataIndex: "noUpStandard",
          scopedSlots: { customRender: "noUpStandard" },
        },
      ], // 表格列
      warnId: null, // 编辑时的warnId
    };
  },
  computed: {
    // 推送频率列表
    indexPushFrequencyList() {
      const i = this.frequencyList.filter(
        (item) =>
          item.indexFrequencyId ===
          (!this.isEdit
            ? this.baseForm.wd.split("~&~")[1]
            : this.baseForm.indexFrequencyId)
      )[0];
      return i
        ? ["月", "季", "半年", "年"].includes(i.indexFrequency)
          ? this.frequencyList.filter((item) =>
              ["月", "周", "日"].includes(item.indexFrequency)
            )
          : i.indexFrequency === "周"
          ? this.frequencyList.filter((item) =>
              ["周", "日"].includes(item.indexFrequency)
            )
          : this.frequencyList.filter((item) => item.indexFrequency === "日")
        : [];
    },
  },
  methods: {
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      this.genExecutionTimeList();
      this.getCompanyList();
      if (formValue) {
        console.log("formValue---->", formValue);
        let {
          businessSegmentsId,
          indexId,
          signOrgId,
          org,
          orgId,
          indexName,
          indexFrequencyId,
          warnStatus,
          warnReceiver,
          indexPushFrequency,
          executionTime,
          completionRate,
          tb,
          hb,
          wd,
          actualValueDeterioration,
          noUpStandard,
          signOrg,
          businessSegments,
          indexFrequency,
          // warnLevel,
          warnType,
          executionHour,
          notifier,
          reviewer,
        } = formValue;
        this.isEdit = true;
        this.warnId = formValue.id;
        this.baseForm.signOrgId = signOrgId;
        this.baseForm.businessSegmentsId = businessSegmentsId;
        this.baseForm.indexId = indexId;
        this.baseForm.indexName = indexName;
        this.baseForm.signOrg = signOrg;
        this.baseForm.businessSegments = businessSegments;
        this.baseForm.indexFrequency = indexFrequency;
        this.baseFormRules.indexId[0].required = false;
        this.baseFormRules.signOrgId[0].required = false;
        this.baseFormRules.businessSegmentsId[0].required = false;
        this.baseFormRules.org[0].required = false;
        this.baseFormRules.wd[0].required = false;
        this.baseForm.orgName = org;
        this.baseForm.wdName = wd;
        this.baseForm.org = orgId;
        this.baseFormRules.org[0].required = false;
        this.baseForm.indexFrequencyId = indexFrequencyId;
        this.baseForm.warnStatus = warnStatus === "0";
        this.baseForm.warnType = warnType || 0;
        if (this.baseForm.warnType === 0) {
          this.roleFormRules = {
            email: [
              {
                required: true,
                message: "请填写预警接收人",
                trigger: ["change", "blur"],
              },
            ],
            indexPushFrequency: [
              {
                required: true,
                message: "请选择预警推送频次",
                trigger: ["change", "blur"],
              },
            ],
            executionHour: [
              {
                required: true,
                message: "请选择邮件推送时间",
                trigger: ["change", "blur"],
              },
            ],
          };
        } else {
          this.roleFormRules = {
            email: [
              {
                required: true,
                message: "请填写预警接收人",
                trigger: ["change", "blur"],
              },
            ],
            indexPushFrequency: [
              {
                required: true,
                message: "请选择预警推送频次",
                trigger: ["change", "blur"],
              },
            ],
            executionHour: [
              {
                required: true,
                message: "请选择邮件推送时间",
                trigger: ["change", "blur"],
              },
            ],
            notifier: [
              {
                required: true,
                message: "请选择预同步告知人",
                trigger: ["change", "blur"],
              },
            ],
            reviewer: [
              {
                required: true,
                message: "请选择预拆分对策审核人",
                trigger: ["change", "blur"],
              },
            ],
          };
        }
        // this.baseForm.warnLevel = warnLevel || 0;
        // this.getWarnInfo(formValue.id);
        this.emailArr = [
          {
            email: warnReceiver?.split(" "),
            indexPushFrequency,
            executionTime: executionTime ? `${executionTime}` : "",
            executionHour: executionHour ? executionHour : 1,
            reviewer: reviewer?.split(","),
            notifier: notifier?.split(","),
          },
        ];
        const equalList = this.equalList.map((item) => item.key);
        const otherEqualList = this.otherEqualList.map((item) => item.key);
        let eqSign = [];
        completionRate = completionRate === null ? "" : completionRate;
        hb = hb === null ? "" : hb;
        tb = tb === null ? "" : tb;
        equalList.forEach((item) => {
          if (completionRate.includes(item)) {
            eqSign.push(item);
          }
        });
        eqSign =
          eqSign.length > 0 ? (eqSign.length > 1 ? eqSign[1] : eqSign[0]) : "<";
        let tbEqSign = [],
          hbEqSign = [];
        otherEqualList.forEach((item) => {
          if (tb.includes(item)) {
            tbEqSign.push(item);
          }
          if (hb.includes(item)) {
            hbEqSign.push(item);
          }
        });
        tbEqSign =
          tbEqSign.length > 0
            ? tbEqSign.length > 1
              ? tbEqSign[1]
              : tbEqSign[0]
            : "恶化<";
        hbEqSign =
          hbEqSign.length > 0
            ? hbEqSign.length > 1
              ? hbEqSign[1]
              : hbEqSign[0]
            : "恶化<";
        this.$set(this, "conditionArr", [
          {
            completionRateFlag: eqSign,
            completionRate: completionRate.split(eqSign)[1],
            tbFlag: tbEqSign,
            tb: tb.split(tbEqSign)[1],
            hbFlag: hbEqSign,
            hb: hb.split(hbEqSign)[1],
            actualValueDeterioration: actualValueDeterioration || "",
            noUpStandard: noUpStandard || "",
          },
        ]);
        this.getTimeType(() => {
          this.emailArr[0].indexPushFrequency = this.frequencyList.filter(
            (item) =>
              item.indexFrequency === this.emailArr[0].indexPushFrequency
          )[0].indexFrequencyId;
        });
        return;
      }
      this.getTimeType();
      // this.getPageSelectOptions();
    },
    // 维度改变
    wdChange(val) {
      this.baseForm.wd = val;
    },
    // 保存卡片信息
    saveCard() {
      let baseFormFlag = false;
      let roleFormFlagList = [];
      this.$refs.baseForm.validate(async (valid) => (baseFormFlag = valid));
      this.$refs.roleForm.map((item) => {
        let itemFlag = false;
        item.validate(async (valid) => (itemFlag = valid));
        roleFormFlagList.push(itemFlag);
      });
      if (baseFormFlag && roleFormFlagList.every((item) => item === true)) {
        const {
          indexId,
          org,
          indexFrequencyId,
          warnStatus,
          businessSegmentsId,
          signOrgId,
          wd,
          // warnLevel,
          warnType,
        } = cloneDeep(this.baseForm);
        let postData = {
          warnStatus: warnStatus ? "0" : "1",
          // warnLevel,
          warnType,
        };
        const indexFrequencyZH = this.frequencyList.filter(
          (item) => item.indexFrequencyId === indexFrequencyId
        )[0]?.indexFrequency;
        // 预警人员信息检查
        let emailArr = cloneDeep(this.emailArr);
        let emailHasErr = false;
        for (let i = 0; i < emailArr.length; i++) {
          const element = emailArr[i];
          console.log("emailArr----->", emailArr[i]);
          const indexPushFrequencyZH = this.frequencyList.filter(
            (item) => item.indexFrequencyId === element.indexPushFrequency
          )[0]?.indexFrequency;
          console.log("indexPushFrequencyZH----->", indexPushFrequencyZH);
          element.indexPushFrequency = indexPushFrequencyZH;
          // if (
          //   element.email.length === 0 ||
          //   element.notifier.length === 0 ||
          //   element.reviewer.length === 0 ||
          //   ["", undefined].includes(element.indexPushFrequency) ||
          //   (element.indexPushFrequency === "日" && !element.executionHour)
          // ) {
          //   emailHasErr = true;
          //   this.$message.warning(`请完善第${i + 1}行预警接收人信息！`);
          //   break;
          // } else
          if (
            (indexFrequencyZH === "周" && indexPushFrequencyZH === "月") ||
            (indexFrequencyZH === "日" &&
              ["月", "周"].includes(indexPushFrequencyZH))
          ) {
            emailHasErr = true;
            this.$message.warning(`第${i + 1}行预警频次有误，请重新选择！`);
            break;
          } else {
            element["email"] = element.email?.join(" ");
            element["reviewer"] = element.reviewer?.join(",");
            element["notifier"] = element.notifier?.join(",");
          }
        }
        if (emailHasErr) {
          return;
        }
        // 预警条件检查
        let conditionArr = cloneDeep(this.conditionArr);
        let conHasErr = false;
        for (let i = 0; i < conditionArr.length; i++) {
          const element = conditionArr[i];
          element.completionRate =
            element.completionRate === null ||
            element.completionRate === undefined
              ? ""
              : element.completionRate;
          element.tb =
            element.tb === null || element.tb === undefined ? "" : element.tb;
          element.hb =
            element.hb === null || element.hb === undefined ? "" : element.hb;
          element.actualValueDeterioration =
            element.actualValueDeterioration === null ||
            element.actualValueDeterioration === undefined
              ? ""
              : element.actualValueDeterioration;
          element.noUpStandard =
            element.noUpStandard === null || element.noUpStandard === undefined
              ? ""
              : element.noUpStandard;
          if (
            !element.completionRate &&
            element.completionRate !== 0 &&
            !element.tb &&
            element.tb !== 0 &&
            !element.hb &&
            element.hb !== 0 &&
            !element.actualValueDeterioration &&
            element.actualValueDeterioration !== 0 &&
            !element.noUpStandard &&
            element.noUpStandard !== 0
          ) {
            conHasErr = true;
            this.$message.warning(`请完善第${i + 1}行预警条件信息！`);
            break;
          }
        }
        if (conHasErr) {
          return;
        }
        // completionRate tb hb
        if (!this.isEdit) {
          postData = {
            ...postData,
            businessSegmentsId: businessSegmentsId,
            signOrgId: signOrgId,
            indexId: indexId,
            indexFrequencyId: wd.split("~&~")[1],
            indexTypeId: this.indexList.filter(
              (item) => item.indexId === indexId
            )[0].indexTypeId,
            orgId: `${org.split("-")[org.split("-").length - 1]}=${
              wd.split("~&~")[0]
            }`,
            wd: this.wdList.filter((item) => item.key === wd)[0].value,
            settings: JSON.stringify(
              conditionArr.map((item) => {
                return {
                  completionRate: `${item.completionRateFlag}${item.completionRate}`,
                  tb: `${item.tbFlag}${item.tb}`,
                  hb: `${item.hbFlag}${item.hb}`,
                  actualValueDeterioration: `${item.actualValueDeterioration}`,
                  noUpStandard: `${item.noUpStandard}`,
                };
              })
            ),
            warnReceiver: JSON.stringify(emailArr),
          };
        } else {
          postData = {
            ...postData,
            id: this.warnId,
            indexId: indexId.split("~&~")[2],
            indexPushFrequency: this.frequencyList.filter(
              (item) =>
                item.indexFrequencyId === this.emailArr[0].indexPushFrequency
            )[0]?.indexFrequency,
            warnReceiver: this.emailArr[0].email.join(" "),
            completionRate: `${conditionArr[0].completionRateFlag}${conditionArr[0].completionRate}`,
            tb: `${conditionArr[0].tbFlag}${conditionArr[0].tb}`,
            hb: `${conditionArr[0].hbFlag}${conditionArr[0].hb}`,
            actualValueDeterioration: `${conditionArr[0].actualValueDeterioration}`,
            noUpStandard: `${conditionArr[0].noUpStandard}`,
            executionTime: `${this.emailArr[0].executionTime}`,
            executionHour: this.emailArr[0].executionHour,
            notifier: this.emailArr[0].notifier?.join(","),
            reviewer: this.emailArr[0].reviewer?.join(","),
          };
        }
        request(
          `${
            this.isEdit
              ? "/api/smc2/indexWarn/update"
              : "/api/smc2/indexWarn/insert"
          }`,
          {
            method: "POST",
            body: postData,
          }
        ).then((res) => {
          if (res && res.result === "success") {
            this.close();
            this.$emit("fetchData");
          } else if (res && res.result !== "success") {
            this.$message.error(res.result);
          }
        });
      }
    },
    handleChange(index, keyWords, value) {
      this.emailArr[index][keyWords] = value;
      this.fetching = false;
      this.userData[keyWords] = [];
    },
    close() {
      this.baseForm = {
        signOrgId: "",
        signOrg: "",
        businessSegmentsId: "",
        businessSegments: "",
        indexId: "",
        indexFrequencyId: "",
        indexType: "",
        org: "",
        orgName: "",
        warnStatus: false,
        wd: "",
        wdName: "",
        warnType: 0,
      };
      this.monthSelectList = [];
      this.weekSelectList = [];
      this.companyList = [];
      this.plateList = [];
      this.indexList = [];
      this.frequencyList = [];
      this.orgTreeData = [];
      this.warnId = null;
      this.emailArr = [
        {
          email: [],
          indexPushFrequency: "",
          executionTime: "",
          executionHour: "",
          notifier: [],
          reviewer: [],
        },
      ]; // 预警人数组
      this.conditionArr = [
        {
          completionRateFlag: "<",
          completionRate: "",
          tbFlag: "恶化>",
          tb: "",
          hbFlag: "恶化>",
          hb: "",
          actualValueDeterioration: "",
          noUpStandard: "",
        },
      ];
      this.roleFormRules = {
        email: [
          {
            required: true,
            message: "请填写预警接收人",
            trigger: ["change", "blur"],
          },
        ],
        indexPushFrequency: [
          {
            required: true,
            message: "请选择预警推送频次",
            trigger: ["change", "blur"],
          },
        ],
        executionHour: [
          {
            required: true,
            message: "请选择邮件推送时间",
            trigger: ["change", "blur"],
          },
        ],
      };
      this.$refs.baseForm.resetFields();
      // this.$refs.roleForm.resetFields()
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 增加一行条件
    addOneCondition() {
      this.conditionArr.push({
        completionRateFlag: "<",
        completionRate: "",
        tbFlag: "恶化>",
        tb: "",
        hbFlag: "恶化>",
        hb: "",
        actualValueDeterioration: "",
        noUpStandard: "",
      });
    },
    // 删除条件
    removeCondition(index) {
      this.conditionArr.splice(index, 1);
    },
    // 生成预警推送时间列表
    genExecutionTimeList() {
      const map = {
        1: "一",
        2: "二",
        3: "三",
        4: "四",
        5: "五",
        6: "六",
        7: "日",
      };
      for (let i = 1; i <= 31; i++) {
        if (i <= 7) {
          this.weekSelectList.push({
            key: `${i}`,
            value: `每周${map[i]}`,
          });
        }
        this.monthSelectList.push({
          key: `${i}`,
          value: `每月${i}号`,
        });
      }
    },
    // 增加一行预警人
    pushOneEmail() {
      this.emailArr.push({
        email: [],
        indexPushFrequency: "",
        executionTime: "",
        executionHour: "",
      });
    },
    // 删除预警人
    removeEmail(index) {
      this.emailArr.splice(index, 1);
    },
    // 获取频次中文
    getIndexFrequencyIdZH(indexFrequencyId) {
      return this.frequencyList.filter(
        (item) => item.indexFrequencyId === indexFrequencyId
      )[0]?.indexFrequency;
    },
    // 查找用户
    fetchUser(keyWords, value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData[keyWords] = [];
      this.fetching = true;
      request(`/api/system/user/getLdapByName?name=${value}`).then((res) => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        this.userData[keyWords] = res;
        this.fetching = false;
      });
    },
    // 获取公司
    getCompanyList() {
      request("/api/smc2/codeValue/getRelation1").then((res) => {
        this.companyList = res || [];
      });
    },
    // 公司下拉框修改
    companyChange(value) {
      if (value) {
        this.baseForm.businessSegmentsId = "";
        this.plateList = [];
        this.baseForm.indexId = "";
        this.baseForm.indexType = "";
        this.indexList = [];
        this.baseForm.indexFrequencyId = "";
        this.orgTreeData = [];
        this.baseForm.org = "";
        this.wdList = [];
        this.baseForm.wd = "";
        this.baseForm.fullCode = this.companyList.filter(
          (item) => item.key === value
        )[0].code;
        this.publicGetDataFun({ name: "版块", signOrgId: value });
      }
    },
    // 版块改变
    plateChange(value) {
      if (value) {
        this.baseForm.indexId = "";
        this.baseForm.indexType = "";
        this.indexList = [];
        this.baseForm.indexFrequencyId = "";
        this.orgTreeData = [];
        this.baseForm.org = "";
        this.wdList = [];
        this.baseForm.wd = "";
        this.publicGetDataFun({
          name: "指标",
          signOrgId: this.baseForm.signOrgId,
          businessSegmentsId: value,
        });
      }
    },
    // 指标选择改变
    indexChange(value) {
      if (value) {
        this.baseForm.indexFrequencyId = "";
        this.orgTreeData = [];
        this.baseForm.org = "";
        this.wdList = [];
        this.baseForm.wd = "";
        this.baseForm.indexType = this.indexList.filter(
          (item) => item.indexId === value
        )[0].indexType;
        this.getOrgList();
      }
    },
    // 计算组织改变
    orgChange(value) {
      if (value) {
        this.wdList = [];
        this.baseForm.wd = "";
        this.$set(this.baseForm, "org", value);
        this.getWDList(value);
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 获取码值表中的年月日码值
    getTimeType(callback) {
      request(
        `/api/smc2/codeValue/searchCodeValue?pageSize=1000&pageNum=1`
      ).then((res) => {
        const arr = (res.rows || []).filter(
          (item) => item.remarks === "指标频次"
        );
        this.frequencyList = arr
          ? arr.map((item) => {
              return {
                indexFrequencyId: item.id,
                indexFrequency: item.val,
              };
            })
          : [];
        typeof callback === "function" && callback();
      });
    },
    // 根据组织、计算组织、版块、指标ID获取维度列表
    getWDList(fullCode) {
      if (!fullCode) {
        return;
      }
      request(`/api/smc2/cardInfo/getWDforWarn`, {
        method: "POST",
        body: {
          businessSegmentsId: this.baseForm.businessSegmentsId,
          indexId: this.baseForm.indexId,
          signOrgId: this.baseForm.signOrgId.split("/")[
            fullCode.split("/").length - 1
          ],
          orgId: fullCode.split("/")[fullCode.split("/").length - 1],
        },
      }).then((res) => {
        if (Array.isArray(res) && res.length) {
          this.wdList = res.map((item) => {
            return {
              key: `${item.key}~&~${item.title}`,
              value: item.value,
            };
          });
        }
      });
    },
    // 获取组织数据
    getOrgList() {
      // request(`/api/smc2/attributes/getTree`, {
      //   method: "POST",
      //   body: {
      //     signOrgId: this.baseForm.signOrgId,
      //     fullCode: this.baseForm.fullCode,
      //     businessSegmentsId: this.baseForm.businessSegmentsId,
      //     indexId: this.baseForm.indexId,
      //   },
      // }).then((res) => {
      //   this.ppOrgData = [];
      //   // this.ppCopyOrgData = [];
      //   this.orgTreeData = this.dealTreeData(res || []);
      //   // console.log(this.ppCopyOrgData);
      //   // console.log(this.ppOrgData);
      // });
      request(
        `/api/smc2/newIndexLibrary/searchOrg?tree=true&signOrgId=${this.baseForm.signOrgId}`
      ).then((res) => {
        this.ppOrgData = [];
        this.orgTreeData = this.dealTreeData(res || []);
      });
    },
    dealTreeData(arr) {
      console.log(arr);
      return arr.map((item) => {
        const itemData = {
          key: item.fullCode,
          disabled: item.isSelect === "N",
          children: this.dealTreeData(item.children || []),
          value: item.orgId,
          title: `${item.value}`,
        };
        let cloneItemData = cloneDeep(itemData);
        delete cloneItemData["children"];
        cloneItemData = Object.freeze(cloneItemData);
        this.ppOrgData = uniqBy([cloneItemData, ...this.ppOrgData], "key");
        // this.ppCopyOrgData = [cloneItemData, ...this.ppCopyOrgData];
        return itemData;
      });
    },
    // 获取页面数据公用方法
    publicGetDataFun(postData) {
      request(`/api/smc2/indexWarn/getSelect`, {
        method: "POST",
        body: postData,
      }).then((res) => {
        const { name } = postData;
        if (name === "版块") {
          this.plateList = res || [];
        } else if (name === "指标") {
          this.indexList = res || [];
        } else if (name === "频次") {
          this.frequencyList = res || [];
        }
      });
    },
    arrayToTree(items) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const id = item.key;
        const pid = item.parentKey || "x00001";

        if (!itemMap[id]) {
          itemMap[id] = {
            children: [],
          };
        }

        itemMap[id] = {
          ...item,
          disabled: item.value === null,
          children: itemMap[id]["children"],
        };

        const treeItem = itemMap[id];
        if (pid === this.baseForm.signOrgId) {
          result.push(Object.freeze(treeItem));
        } else {
          if (!itemMap[pid]) {
            itemMap[pid] = {
              children: [],
            };
          }
          itemMap[pid].children.push(Object.freeze(treeItem));
        }
      }
      return result;
    },
    // 选择指标selectFilter
    indexIdFilterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 预警类型改变会更新roleFormRules的规则
    warnTypeChange(e) {
      this.$refs.roleForm.map((item) => item.clearValidate());
      // 如果是通知
      if (e === 0) {
        this.roleFormRules = {
          email: [
            {
              required: true,
              message: "请填写预警接收人",
              trigger: ["change", "blur"],
            },
          ],
          indexPushFrequency: [
            {
              required: true,
              message: "请选择预警推送频次",
              trigger: ["change", "blur"],
            },
          ],
          executionHour: [
            {
              required: true,
              message: "请选择邮件推送时间",
              trigger: ["change", "blur"],
            },
          ],
        };
      } else {
        this.roleFormRules = {
          email: [
            {
              required: true,
              message: "请填写预警接收人",
              trigger: ["change", "blur"],
            },
          ],
          indexPushFrequency: [
            {
              required: true,
              message: "请选择预警推送频次",
              trigger: ["change", "blur"],
            },
          ],
          executionHour: [
            {
              required: true,
              message: "请选择邮件推送时间",
              trigger: ["change", "blur"],
            },
          ],
          notifier: [
            {
              required: true,
              message: "请选择预同步告知人",
              trigger: ["change", "blur"],
            },
          ],
          reviewer: [
            {
              required: true,
              message: "请选择预拆分对策审核人",
              trigger: ["change", "blur"],
            },
          ],
        };
      }
    },
  },
};
</script>
<style lang="less">
.conditionForm {
  .ant-form-item {
    margin-bottom: 0;
  }
}
.earlyModal .emailSetting {
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--borderColor);
}
.ant-form-item-required:before {
  display: inline-block;
  margin-right: 4px;
  color: #f53f3f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
</style>
