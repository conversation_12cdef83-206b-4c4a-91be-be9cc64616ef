<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-18 17:41:10
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    title="编辑"
  >
    <a-form-model
      ref="form"
      :rules="rules"
      :model="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="原因分析" prop="causeAnalysis">
        <a-input v-model="form.causeAnalysis" type="textarea" />
      </a-form-model-item>
      <a-form-model-item label="改善策略" prop="improvementMeasures">
        <a-input v-model="form.improvementMeasures" type="textarea" />
      </a-form-model-item>
      <a-form-model-item label="转办人员">
        <a-select
          show-search
          allowClear
          :value="form.transfer"
          :dropdownMatchSelectWidth="false"
          placeholder="请输入责任人名称"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          @search="handleAccountSearch($event, 'transfer')"
          @change="handleAccountChange($event, 'transfer')"
        >
          <a-select-option
            v-for="d in transferList"
            :key="`${d.account}`"
            :value="`${d.account}`"
          >
            {{ d.name }}({{ d.account }}) {{ d.o }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item
        label="转办人员直属领导"
        prop="personResponsibleLeader"
      >
        <a-select
          show-search
          allowClear
          :value="form.personResponsibleLeader"
          :dropdownMatchSelectWidth="false"
          placeholder="请输入直属领导名称"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          @search="handleAccountSearch($event, 'personResponsibleLeader')"
          @change="handleAccountChange($event, 'personResponsibleLeader')"
        >
          <a-select-option
            v-for="d in personResponsibleLeaderList"
            :key="`${d.account}`"
            :value="`${d.account}`"
          >
            {{ d.name }}({{ d.account }}) {{ d.o }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
    <template slot="footer">
        <a-button key="back" @click="close">
          取消
        </a-button>
        <a-button key="submit" type="primary" :disabled="form.state=='审核通过'||form.state=='待审核'" @click="handleOk">
          提交
        </a-button>
      </template>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
import debounce from "lodash/debounce";
export default {
  data() {
    this.handleAccountSearch = debounce(this.handleAccountSearch, 800);
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      dataId: "",
      form: {
        causeAnalysis: "",
        improvementMeasures: "",
        transfer: "",
        personResponsibleLeader: "",
        state:""
      },
      transferList: [],
      personResponsibleLeaderList: [],
      rules: {
        causeAnalysis: [
          {
            required: true,
            message: "请输入原因分析",
            trigger: "blur"
          }
        ],
        improvementMeasures: [
          {
            required: true,
            message: "请输入改善策略",
            trigger: "blur"
          }
        ],
        personResponsibleLeader: [
          {
            required: false,
            message: "请选择直属领导人",
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    handleAccountSearch(value, key) {
      this.getAcountList(value).then(res => {
        this[`${key}List`] = res;
      });
    },
    handleAccountChange(value, key) {
      const flag = value ? false : true;
      for (const key in this.rules) {
        if (Object.hasOwnProperty.call(this.rules, key)) {
          this.rules[key].forEach(item => {
            item["required"] = key === "personResponsibleLeader" ? !flag : flag;
          });
        }
      }
      console.log("personResponsibleLeader---->");
      this.$refs.form.validateField(["improvementMeasures", "causeAnalysis"]);
      this.$set(this.form, key, value);
    },
    getAcountList(account) {
      return new Promise(resolve => {
        request(`/api/smc2/ldap/searchLdapUser`, {
          method: "POST",
          body: {
            account
          }
        }).then(res => {
          resolve(res || []);
        });
      });
    },
    show({ dataId, causeAnalysis, improvementMeasures,state }) {
      this.dataId = dataId;
      this.form = {
        causeAnalysis,
        improvementMeasures,
        transfer: "",
        state
      };
      this.visible = true;
    },
    close() {
      this.form = {
        causeAnalysis: "",
        improvementMeasures: "",
        transfer: "",
        personResponsibleLeader: ""
      };
      this.transferList = [];
      this.personResponsibleLeaderList = [];
      this.$refs.form.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
      console.log(this.$store)
    },
    // 保存卡片信息
    saveCard() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const {
            transfer,
            causeAnalysis,
            improvementMeasures,
            personResponsibleLeader
          } = this.form;
          request(`/api/smc2/gate/transfer`, {
            method: "POST",
            body: {
              transfer,
              causeAnalysis,
              improvementMeasures,
              personResponsibleLeader,
              id: this.dataId || ""
            }
          }).then(() => {
            this.close();
            this.$emit("fetchData");
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  }
};
</script>
