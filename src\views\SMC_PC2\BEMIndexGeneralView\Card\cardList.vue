<!--
 * @Description: 卡片列表
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-08 09:45:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 09:03:37
-->
<template>
  <div
    class="card_list"
    :style="{ 'padding-bottom': cardListDraged ? '80px' : '0' }"
  >
    <div class="own-row">
      <div class="drag_box">
        <!-- 骨架屏 -->
        <template v-if="cardListLoading">
          <div
            v-for="item in pageClass === 'indexGeneralViewPage2' ? 5 : 3"
            :key="item"
            class="own-col"
          >
            <a-card class="card spin">
              <div class="container">
                <a-skeleton active />
              </div>
            </a-card>
          </div>
        </template>
        <template v-if="!cardListLoading">
          <!-- 指标 -->
          <div
            v-for="(item, index) in filterList"
            :key="`${item.pj}-${new Date().getTime() + index * 100000}`"
            class="own-col"
            :data-dragDisabled="item.recommend || !item.canDragSort"
          >
            <CardItem
              :pageClass="pageClass"
              :companyName="companyName"
              @refreshData="refreshData"
              :item="item"
              :index="index"
              @showDetail="showDetail"
              @trendShow="trendShow"
              @showDetection="showDetection"
              v-on="$listeners"
            />
          </div>
        </template>
      </div>
    </div>
    <!-- 卡片详情 -->
    <CardDetailDrawer
      :frequency="frequency"
      :companyName="companyName"
      :pageClass="pageClass"
      :signOrgId="signOrgId"
      ref="cardDetailDrawer"
      @subscribe="refreshData"
    />
    <IndexTrendModel
      ref="IndexTrendModel"
      :timeTypeOptionInDict="timeTypeOptionInDict"
    />
    <div class="saveCardSort" v-if="cardListDraged">
      <a-button
        style="margin-right: 16px;"
        @click="
          $emit('cancelDrag');
          cardListDraged = false;
        "
        >取消</a-button
      >
      <a-button type="primary" :loading="sortLoading" @click="saveCardSort">
        保存排序
      </a-button>
    </div>
    <CardDetectionDrawer ref="cardDetectionDrawer" />
  </div>
</template>
<script>
import CardItem from "./cardItem.vue";
import cloneDeep from "lodash/cloneDeep";
import CardDetailDrawer from "./CardInfo/cardDetailDrawer.vue";
import CardDetectionDrawer from "./DetectionInfo/infoDrawer.vue";
import request from "@/utils/requestHttp";
import IndexTrendModel from "./indexTrendModal.vue";

export default {
  inject: ["isDesign", "skinStyle"],
  components: {
    CardItem,
    CardDetailDrawer,
    IndexTrendModel,
    CardDetectionDrawer
  },
  props: {
    activePlate: String, // 当前激活的版块
    pageClass: String, // 当前是哪个页面（概览/横比）使用的cardList
    companyName: String, // 公司名称
    signOrgId: String, // 公司编码
    list: Array, // 卡片list
    cardListLoading: Boolean, // 卡片list Loading
    frequency: String, // 频率
    timeTypeOptionInDict: Array // 时间类型码值表
  },
  data() {
    return {
      filterList: [], // 根据版块筛选后的数据，用于版块筛选后拖拽排序后可以正常保存页面卡片顺序
      cardListDraged: false,
      pageShow: true, // 页面当前是否展示 （生命周期）
      sortLoading: false, // 拖拽排序后保存按钮的loading
      filterIndexArr: [] // pageHeader组件要过滤的指标
    };
  },
  watch: {
    // 监听卡片列表数据源
    list() {
      // 处理filterList
      console.timeEnd("transmitIndexData");
      console.time("filterIndex");
      this.setFilterList();
    },
    activePlate: {
      handler() {
        // 处理filterList
        this.setFilterList();
      }
    }
  },
  mounted() {
    // 概览页面
    if (this.pageClass === "indexGeneralViewPage2") {
      // 定义拖动
      this.$nextTick(() => {
        this.initSortable();
      });
    }
  },
  methods: {
    // 设置过滤指标数组
    setFilterIndex(e) {
      this.filterIndexArr = e || [];
      this.setFilterList();
    },
    setFilterList() {
      // 处理filterList
      let arr =
        this.activePlate === "全部"
          ? cloneDeep(this.list)
          : cloneDeep(this.list).filter(item =>
              this.activePlate === "制造"
                ? ["能源", "效率", "人资", "交付"].includes(
                    item.businessSegments
                  )
                : item.businessSegments === this.activePlate
            );
      // 根据pj过滤
      if (this.filterIndexArr.length) {
        arr = arr.filter(item => this.filterIndexArr.includes(item.pj));
      }
      this.filterList = arr;
      console.timeEnd("filterIndex");
    },
    // 初始化拖动
    initSortable() {
      const _this = this;
      var el = document.querySelector(
        `.${this.pageClass}.${this.companyName} .card_list .drag_box`
      );
      // eslint-disable-next-line no-undef
      Sortable.create(el, {
        animation: 400,
        ghostClass: "blue-background-class",
        easing: "cubic-bezier(1, 0, 0, 1)",
        handle: "._drag",
        onMove(evt) {
          if (evt.related.getAttribute("data-dragDisabled") === "true") {
            return false;
          }
        },
        onEnd(data) {
          const { newIndex, oldIndex } = data;
          const oldpj = _this.filterList[oldIndex].pj;
          const newpj = _this.filterList[newIndex].pj;
          // 根据版块过滤后的数组排序
          const currRow = _this.filterList.splice(oldIndex, 1)[0];
          _this.filterList.splice(newIndex, 0, currRow);
          // 全部的数组排序
          let oldIndexInAll = 0;
          let newIndexInAll = 0;
          _this.list.forEach((item, index) => {
            if (item.pj === oldpj) {
              oldIndexInAll = index;
            }
            if (item.pj === newpj) {
              newIndexInAll = index;
            }
          });
          const currRowInAll = _this.list.splice(oldIndexInAll, 1)[0];
          _this.list.splice(newIndexInAll, 0, currRowInAll);
          // _this.$emit("onDraged", true);
          _this.cardListDraged = true;
        }
      });
      document.body.ondrop = function(event) {
        event.preventDefault();
        event.stopPropagation();
      };
    },
    // 刷新数据
    refreshData() {
      this.$emit("refreshData");
    },
    // 保存卡片顺序
    saveCardSort() {
      if (this.list.length === 0 || this.isDesign) {
        return;
      }
      this.sortLoading = true;
      const postData = this.list
        .filter(item => !item.recommend)
        .map((item, index) => {
          const { pj } = item;
          return {
            sort: index + 1,
            val: pj,
            sign: `${this.companyName}概览`
          };
        });
      request(`/api/smc2/indexSort/updateSort`, {
        method: "POST",
        body: postData
      })
        .then(() => {
          this.sortLoading = false;
          this.cardListDraged = false;
          this.refreshData();
        })
        .catch(() => {
          this.sortLoading = false;
        });
    },
    // 展示详情
    showDetail(item) {
      console.log("showDetail----->", item);
      this.$refs.cardDetailDrawer.show(item);
    },
    // 展示下探
    showDetection(item) {
      this.$refs.cardDetectionDrawer.show(item);
    },
    // 展示指标走势
    trendShow(item) {
      this.$refs["IndexTrendModel"].show(item);
    }
  }
};
</script>
<style lang="less">
@import url("../../common.less");
.indexComparisonPage2 {
  .card_list {
    .card {
      border: 1px solid #e8e8e8 !important;
    }
  }
}
.indexComparisonPage2,
.indexGeneralViewPage2 {
  &.classic-style {
    .card_list {
      .own-row {
        .own-col {
          @media screen and (min-width: 1480px) and (max-width: 2000px) {
            width: calc((100% - 64px) / 5);
            margin: 0 8px;
            &:nth-child(5n) {
              margin-right: 0;
            }
            &:nth-child(5n + 1) {
              margin-left: 0;
            }
            margin-bottom: 16px;
          }

          @media screen and (max-width: 1479px) {
            width: calc((100% - 48px) / 4);
            margin: 0 8px;
            margin-bottom: 16px;
            &:nth-child(4n) {
              margin-right: 0;
            }
            &:nth-child(4n + 1) {
              margin-left: 0;
            }
          }
          @media screen and (min-width: 2000px) {
            width: calc((100% - 90px) / 6);
            margin: 0 8px;
            margin-bottom: 16px;
            &:nth-child(6n) {
              margin-right: 0;
            }
            &:nth-child(6n + 1) {
              margin-left: 0;
            }
          }
          .card {
            position: relative;
            .container {
              padding: 20px;
            }
            &:hover {
              background: #fff;
              box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
              .dragDel {
                display: flex !important;
              }
            }
            &.addCard,
            &.spin {
              background: #ffffff;
              box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
            }
            &.recommend {
              background: rgb(243, 254, 255);
            }
            .__top {
              margin-bottom: 8px;
              .businessSegments,
              .wdTag,
              .YC {
                padding: 0 4px;
                font-size: 12px;
              }
              .sign {
                font-size: 12px;
                & > {
                  padding: 0 4px;
                }
              }
            }
            .__title {
              margin-bottom: 3px;
              height: 22px;
              line-height: 22px;
              color: rgb(78, 89, 105);
            }
            ._data {
              height: 32px;
              font-size: 24px;
              color: rgba(0, 0, 0, 0.85);
              margin-bottom: 8px;
              display: flex;
              align-items: flex-end;
              span {
                display: inline-block;
              }
              span:first-child {
                max-width: 80%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                line-height: 24px;
              }
              span:last-child {
                font-size: 16px;
                line-height: 16px;
              }
            }
            ._target,
            .completRate {
              font-size: 12px;
              line-height: 18px;
              margin-bottom: 8px;
              &._target {
                margin-bottom: 4px;
              }
              &.completRate {
                margin-bottom: 11px;
              }
            }
            .compare-topline {
              width: 100%;
              height: 1px;
              background: rgba(0, 0, 0, 0.1);
              display: block;
              margin-bottom: 12px;
            }
            ._op {
              margin-top: 2px;
              justify-content: space-between;
              ._another {
                height: 20px;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.35);
                line-height: 20px;
                cursor: pointer;
                transition: all ease-in-out 0.3s;
                &:hover {
                  color: #00aaa6;
                }
              }
              ._addtolist {
                font-size: 12px;
                height: 28px;
                margin-top: 2px;
                border-color: rgba(0, 170, 166, 1);
                background: transparent;
                color: rgb(0, 170, 166);
              }
            }
            .mdyyfx-zz {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 100%;
              z-index: 9;
              background-color: #fff;
              padding: 10px;
              box-sizing: border-box;
              overflow-y: auto;
            }
          }
        }
      }
    }
  }
  &.hisense-style {
    &.dark {
      .table-area {
        .ant-table-wrapper {
          background: transparent !important;
          .ant-table-thead {
            background-color: rgba(250, 252, 255, 0.1);
          }
          .ant-table-thead > tr > th {
            color: #fff;
          }
          .ant-table {
            color: #e0e0e0 !important;
            border: none;
          }
        }
      }
      .card_list {
        .own-row {
          .own-col {
            .card {
              border: none;
              background-color: #222325;
              color: #a1a6ac;
              .___title {
                color: rgba(250, 252, 255, 0.8) !important;
              }
              .__top .businessSegments,
              .__top .wdTag,
              .__top .YC {
                background-color: #000000 !important;
              }
              .__center {
                ._left {
                  span {
                    color: #e4e4e4 !important;
                  }
                }
                ._right {
                  &::before {
                    background-color: #505050 !important;
                  }
                  .ant-progress-text span {
                    color: #e4e4e4 !important;
                  }
                }
              }
              .compare-topline {
                background: #505050;
              }
              .compare {
                ._c > span {
                  color: #6b7785;
                }
              }
              .dragDel {
                background-color: rgba(0, 0, 0, 0.7);
                & > div {
                  background: #1d1f22;
                }
              }
              &.addCard {
                background-color: #222325;
              }
            }
          }
        }
      }
    }
    &:not(.dark) {
      .card_list .own-row .own-col .card {
        background: transparent;
      }
    }
    .table-area {
      .ant-table-wrapper {
        .ant-table-thead {
          background-color: #e7f8f7;
        }
        .ant-table {
          border: none;
        }
      }
    }
    .card_list {
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      .own-row {
        .own-col {
          @media screen and (min-width: 1480px) and (max-width: 2000px) {
            width: calc((100% - 64px) / 4);
            margin: 0 8px;
            &:nth-child(4n) {
              margin-right: 0;
            }
            &:nth-child(4n + 1) {
              margin-left: 0;
            }
            margin-bottom: 16px;
          }

          @media screen and (max-width: 1479px) {
            width: calc((100% - 48px) / 3);
            margin: 0 8px;
            margin-bottom: 16px;
            &:nth-child(3n) {
              margin-right: 0;
            }
            &:nth-child(3n + 1) {
              margin-left: 0;
            }
          }
          @media screen and (min-width: 2000px) {
            width: calc((100% - 90px) / 5);
            margin: 0 8px;
            margin-bottom: 16px;
            &:nth-child(5n) {
              margin-right: 0;
            }
            &:nth-child(5n + 1) {
              margin-left: 0;
            }
          }
          .card {
            border-radius: 3px;
            border: 1px solid #e5e6eb;
            .container {
              padding: 10px 16px;
              padding-bottom: 0;
              .___title {
                color: #1d2129;
                font-size: 16px;
              }
            }
            .compare ._c > span {
              color: #6b7785;
            }
            &:hover {
              .dragDel {
                display: flex !important;
              }
            }
            .dragDel {
              display: none;
              height: 100%;
              align-items: flex-start;
              background: rgba(0, 0, 0, 0.2);
              & > div {
                background-color: #fff;
                border-radius: 7px;
                margin: 9px 7px 0 0;
                height: 40px;
                display: flex;
                align-items: center;
                & > div {
                  padding: 0 12px;
                  width: 24px;
                  height: 24px;
                  box-sizing: content-box;
                  position: relative;
                  img {
                    display: block;
                    width: 24px;
                    height: 24px;
                  }
                  background-repeat: no-repeat;
                  background-size: contain;
                  background-position: center center;
                  transition: all ease-in-out 0.3s;
                  &.mdyyfx {
                    background-image: url("~@/assets/images/<EMAIL>");
                    &:hover {
                      background-image: url("~@/assets/images/<EMAIL>");
                    }
                  }
                  &.xt {
                    background-image: url("~@/assets/images/Group <EMAIL>");
                    &:hover {
                      background-image: url("~@/assets/images/Group <EMAIL>");
                    }
                  }
                  &.bb {
                    background-image: url("~@/assets/images/Group <EMAIL>");
                    &:hover {
                      background-image: url("~@/assets/images/Group <EMAIL>");
                    }
                  }
                  &.zs {
                    background-image: url("~@/assets/images/Group <EMAIL>");
                    &:hover {
                      background-image: url("~@/assets/images/Group <EMAIL>");
                    }
                  }
                  &._drag {
                    background-image: url("~@/assets/images/Group <EMAIL>");
                    &:hover {
                      background-image: url("~@/assets/images/Group <EMAIL>");
                    }
                  }
                  &:not(:last-child) {
                    &::before {
                      display: block;
                      content: "";
                      position: absolute;
                      right: 0;
                      top: 50%;
                      transform: translateY(-50%);
                      width: 1px;
                      height: 16px;
                      background-color: #e5e6eb;
                    }
                  }
                }
              }
            }
            .__top {
              margin-bottom: 13px;
              .businessSegments,
              .wdTag,
              .YC {
                padding: 0 6px;
                font-size: 14px;
              }
              .sign {
                font-size: 14px;
                & > {
                  padding: 0 6px;
                }
              }
            }
            .__title {
              margin-bottom: 10px;
              height: 20px;
              line-height: 20px;
              color: #1d2129;
            }
            .__center {
              margin-top: 10px;
              & > div {
                width: 50%;
              }
              ._left {
                align-items: flex-end;
                justify-content: center;
                white-space: nowrap;
                margin-bottom: 8px;
                span {
                  display: block;
                  &:first-child {
                    height: 39px;
                    font-weight: 500;
                    font-size: 32px;
                    line-height: 39px;
                    color: #1d2129;
                  }
                  &:last-child {
                    height: 20px;
                    font-size: 14px;
                    line-height: 20px;
                    margin-left: 8px;
                    color: #1d2129;
                    margin-bottom: 3px;
                  }
                }
              }
              ._right {
                height: 80px;
                position: relative;
                &::before {
                  display: block;
                  content: "";
                  position: absolute;
                  left: 0px;
                  top: 17px;
                  background-color: #e5e6eb;
                  height: 33px;
                  width: 1px;
                }
                justify-content: center;
                align-items: center;
                .ant-progress-circle {
                  path {
                    &:first-child {
                      stroke: #f2f3f5 !important;
                    }
                  }
                }
              }
            }
            .compare-topline {
              width: calc(100% + 32px);
              margin: 0 -16px;
              height: 1px;
              background: rgba(0, 0, 0, 0.1);
              display: block;
              margin-bottom: 4px;
            }
            .compare {
              white-space: nowrap;
              padding: 10px 0;
            }
            ._op {
              justify-content: space-between;
              align-items: center;
              padding: 4px 0 7px 0;
              ._right-op {
                & > div {
                  height: 28px;
                  padding: 0 6px;
                  display: flex;
                  align-items: center;
                  img {
                    display: block;
                    width: 16px;
                    height: 16px;
                    margin-right: 6px;
                  }
                  font-size: 12px;
                  &:first-child {
                    color: #00aaa6;
                    border: 1px solid #00aaa6;
                    border-radius: 3px;
                    background-color: #e7f8f7;
                    margin-right: 10px;
                  }
                  &:last-child {
                    color: #fff;
                    background-color: #00aaa6;
                  }
                }
              }
            }
            .recommend_bg {
              right: -1px;
            }
            &.addCard {
              height: 202px;
              background-color: #f7f8fa;
              border: none;
              border-radius: 6px;
            }
          }
        }
      }
    }
  }
  .card_list {
    .saveCardSort {
      position: absolute;
      bottom: 0;
      left: 0;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
      background-color: #fff;
      box-sizing: border-box;
      padding: 16px 24px;
      width: 100%;
      min-width: 1200px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .own-row {
      .drag_box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }
      .own-col {
        .card {
          cursor: pointer;
          border: none;
          background: rgb(255, 255, 255);
          border-radius: 4px;
          box-sizing: border-box;
          transition: all ease-in-out 0.3s;
          overflow: hidden;
          .ant-card-body {
            padding: 0;
            position: relative;
          }
          .__top {
            position: relative;
            .businessSegments,
            .wdTag,
            .YC {
              height: 20px;
              font-size: 12px;
              color: #53667a;
              line-height: 20px;
              border-radius: 2px;
              background-color: #e0e3ea;
              margin-right: 8px;
              white-space: nowrap;
            }
            .sign {
              color: rgb(78, 89, 105);
              font-size: 12px;
              & > span {
                display: inline-block;
                height: 20px;
                line-height: 20px;
                padding: 0 4px;
                border-radius: 2px;
                background: rgb(220, 247, 250);
                &:not(:last-child) {
                  margin-right: 4px;
                }
              }
            }
            .businessSegments {
              background-color: rgb(218, 245, 236);
              color: rgb(0, 186, 128);
            }
            .YC {
              color: #53667a;
              background-color: #e0e3ea;
              color: #ee734f;
              background-color: #fdeeea;
            }
          }
          .___title {
            font-size: 14px;
            margin-right: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
          }
          .recommend_bg {
            width: 48px;
            height: 48px;
            position: absolute;
            right: 0;
            top: 0;
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
            background-image: url("~@/assets/images/recommend.png");
          }
          .completRate {
            & > span {
              display: block;
              &:nth-child(1) {
                margin-right: 8px;
              }
              &.process {
                position: relative;
                width: 44%;
                height: 8px;
                background: #f0f0f0;
                margin-right: 12px;
                & > span {
                  height: 8px;
                  display: block;
                  &.b {
                    background: #6495f9;
                  }
                  &.r {
                    background: #f75050;
                  }
                }
              }
            }
          }
          .compare {
            font-size: 12px;
            ._c > span {
              color: rgba(0, 0, 0, 0.65);
              display: block;
              margin-right: 2px;
              white-space: nowrap;
            }
            ._c > div {
              .anticon {
                position: relative;
                top: 1px;
              }
              white-space: nowrap;
              span {
                white-space: nowrap;
              }
            }
          }
          .dragDel {
            width: 100%;
            height: 72px;
            border-radius: 0px 0px 4px 4pxpx;
            background: linear-gradient(
              180deg,
              rgba(0, 0, 0, 0) 0%,
              rgba(0, 0, 0, 1) 100%
            );
            left: 0;
            bottom: 0;
            position: absolute;
            display: none;
            justify-content: flex-end;
            & > img {
              width: 28px;
              height: 28px;
              display: block;
              margin-right: 8px;
            }
            & > div.xt {
              background-color: #fff;
              border-radius: 5px;
              width: 28px;
              height: 28px;
              display: flex;
              margin-right: 8px;
              align-items: center;
              justify-content: center;
              & > img {
                display: block;
                width: 23px;
                height: 23px;
              }
            }
            .anticon {
              color: rgba(0, 0, 0, 0.25);
              transition: color ease-in-out 0.3s;
              &:hover {
                color: #00aaa6;
              }
            }
          }
          &.addCard {
            cursor: pointer;
            padding: 64.5px 0;
            background: #fff;
            color: rgba(0, 0, 0, 0.25);
            transition: all ease-in-out 0.3s;
            &:hover {
              background: #f4f5f7;
            }
            .ant-card-body {
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              .anticon {
                width: 40px;
                height: 40px;
                font-size: 40px;
                margin-bottom: 24px;
              }
              span {
                height: 20px;
                font-size: 14px;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
}
.indexComparisonPage {
  .own-col {
    width: calc(25% - 16px) !important;
    @media screen and (max-width: 1023px) {
      width: calc(30% - 16px) !important;
    }
    @media screen and (min-width: 2000px) {
      width: calc(20% - 16px) !important;
    }
  }
}
</style>
