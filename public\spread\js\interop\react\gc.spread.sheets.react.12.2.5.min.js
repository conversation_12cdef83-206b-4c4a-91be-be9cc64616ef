/*!
 * 
 * Spread.Sheets Wrapper Components for React 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/licensing/grapecity/
 * 
 */
!function a(b,c){"object"==typeof exports&&"object"==typeof module?module.exports=c():"function"==typeof define&&define.amd?define([],c):"object"==typeof exports?exports.SpreadSheetsComponents=c():b.SpreadSheetsComponents=c()}(this,function(){return function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={exports:{},id:d,loaded:!1};return a[d].call(e.exports,e,e.exports,c),e.loaded=!0,e.exports}return c.m=a,c.c=b,c.p="",c(0)}([function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),b.Column=b.Worksheet=b.SpreadSheets=void 0,d=c(1),e=j(d),f=c(5),g=j(f),h=c(7),i=j(h);function j(a){return a&&a.__esModule?a:{default:a}}b.SpreadSheets=e.default,b.Worksheet=g.default,b.Column=i.default},function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(2),f=j(e),g=c(3),h=j(g);function j(a){return a&&a.__esModule?a:{default:a}}function k(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function l(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function m(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}i=function(a){m(b,a);function b(a){k(this,b);var c=l(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a));return c.state={needFirstRender:!0},c.isEmitSpread=!1,c}return d(b,[{key:"componentDidMount",value:function a(){var b,c=h.default.filterReallyChild(this.props.children,h.default.sheetTagName),d=c.length?0:1;this.spread=h.default.createSpread(this.refs.spreadJs,d),b=h.default.watchChanges({},this.props),h.default.changeWorkBookByAPI(this.spread,b),h.default.bindEvent(this.spread,this.props),this.setState({needFirstRender:!1})}},{key:"componentDidUpdate",value:function a(){this.state.needFirstRender===!1&&this.isEmitSpread===!1&&(this.props&&this.props.workbookInitialized&&this.props.workbookInitialized(this.spread),this.isEmitSpread=!0)}},{key:"componentWillReceiveProps",value:function a(b){var c,d=h.default.filterReallyChild(this.props.children,h.default.sheetTagName).length,e=h.default.filterReallyChild(b.children,h.default.sheetTagName).length;0===d&&e>0?h.default.removeAllSheet(this.spread):0===e&&d>0&&h.default.addSheet(this.spread,0),c=h.default.watchChanges(this.props,b),h.default.changeWorkBookByAPI(this.spread,c)}},{key:"renderChildren",value:function a(){var b=this.spread?{spread:this.spread}:{};return h.default.getNewReallyChildren(this.props.children,h.default.sheetTagName,b)}},{key:"componentWillUnmount",value:function a(){this.spread.destroy()}},{key:"render",value:function a(){return f.default.createElement("div",{ref:"spreadJs",className:"container"+(this.props.hostClass||""),style:h.default.assign({width:"100%",height:"100%"},this.props.hostStyle)},this.renderChildren(this.props.children))}}]),b}(f.default.Component),b.default=i},function(a,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var c=window.React;b.default=c},function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=c(2),e=i(d),f=c(4),g=i(f);function i(a){return a&&a.__esModule?a:{default:a}}h={defaultRowCount:200,defaultColumnCount:20,workBookTagName:"SpreadSheets",sheetTagName:"Worksheet",columnTagName:"Column",createSpread:function a(b,c){return new g.default.Spread.Sheets.Workbook(b,{sheetCount:c})},addSheet:function a(b,c,d){var e=new g.default.Spread.Sheets.Worksheet;return 0===d&&e.setColumnCount(0),b.addSheet(c,e),e},removeAllSheet:function a(b){for(var c=0;c<b.sheets.length;c++)b.removeSheet(0)},removeSheet:function a(b,c){var d=c.name(),e=b.getSheetIndex(d);return b.removeSheet(e),null},addColumn:function a(b,c){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;b.addColumns(c,d)},removeColumn:function a(b,c){var d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;b.deleteColumns(c,d)},removeAllColumn:function a(b){b.setColumnCount(0)},bindEvent:function a(b,c){var d=".react",e=["ValidationError","CellClick","CellDoubleClick","EnterCell","LeaveCell","ValueChanged","TopRowChanged","LeftColumnChanged","InvalidOperation","RangeFiltering","RangeFiltered","TableFiltering","TableFiltered","RangeSorting","RangeSorted","ClipboardChanging","ClipboardChanged","ClipboardPasting","ClipboardPasted","ColumnWidthChanging","ColumnWidthChanged","RowHeightChanging","RowHeightChanged","DragDropBlock","DragDropBlockCompleted","DragFillBlock","DragFillBlockCompleted","EditStarting","EditChange","EditEnding","EditEnd","EditEnded","RangeGroupStateChanging","RangeGroupStateChanged","SelectionChanging","SelectionChanged","SheetTabClick","SheetTabDoubleClick","SheetNameChanging","SheetNameChanged","UserZooming","UserFormulaEntered","CellChanged","ColumnChanged","RowChanged","ActiveSheetChanging","ActiveSheetChanged","SparklineChanged","RangeChanged","ButtonClicked","EditorStatusChanged","FloatingObjectChanged","FloatingObjectSelectionChanged","PictureChanged","FloatingObjectRemoving","FloatingObjectRemoved","PictureSelectionChanged","FloatingObjectLoaded","TouchToolStripOpening","CommentChanged","CommentRemoving","CommentRemoved","SlicerChanged"];e.forEach(function(a){b.bind(a+d,function(a,b){var d=a.type,e=d[0].toLowerCase()+d.substr(1),f=c[e];f&&f(a,b)})})},watchChanges:function a(b,c){var d,e,f={};for(d in b)b.hasOwnProperty(d)&&b[d]!==c[d]&&(f[d]=c[d]);for(e in c)c.hasOwnProperty(e)&&b[e]!==c[e]&&(f[e]=c[e]);return f},changeWorkBookByAPI:function a(b,c){b.suspendEvent(),b.suspendPaint();for(var d in c)if(c.hasOwnProperty(d))switch(d){case"name":b.name=c[d];break;case"allowUserZoom":case"allowUserResize":case"allowUserDragMerge":case"allowUndo":case"allowSheetReorder":case"allowContextMenu":case"allowUserDeselect":case"allowCopyPasteExcelStyle":case"allowExtendPasteRange":case"tabStripVisible":case"tabEditable":case"tabStripRatio":case"tabNavigationVisible":case"newTabVisible":case"allowUserEditFormula":case"autoFitType":case"allowUserDragFill":case"allowUserDragDrop":case"highlightInvalidData":case"referenceStyle":case"backColor":case"grayAreaBackColor":case"backgroundImage":case"backgroundImageLayout":case"cutCopyIndicatorVisible":case"cutCopyIndicatorBorderColor":case"copyPasteHeaderOptions":case"defaultDragFillType":case"enableFormulaTextbox":case"hideSelection":case"resizeZeroIndicator":case"showDragFillSmartTag":case"scrollbarShowMax":case"scrollbarMaxAlign":case"scrollIgnoreHidden":case"showVerticalScrollbar":case"showHorizontalScrollbar":case"showScrollTip":case"showResizeTip":case"showDragDropTip":case"showDragFillTip":case"useTouchLayout":b.options[d]=c[d]}b.resumePaint(),b.resumeEvent()},changeWorkSheetByAPI:function a(b,c){var d,e;if(b){b.suspendPaint(),b.suspendEvent();for(d in c)if(c.hasOwnProperty(d))switch(e=c[d],d){case"frozenColumnCount":case"frozenRowCount":case"frozenTrailingColumnCount":case"frozenTrailingRowCount":case"zoom":case"selectionPolicy":case"selectionUnit":b[d](parseInt(e));break;case"name":case"currentTheme":case"showRowOutline":case"showColumnOutline":b[d](e);break;case"autoGenerateColumns":b[d]=e;break;case"allowCellOverflow":case"frozenlineColor":case"sheetTabColor":case"clipBoardOptions":case"rowHeaderAutoText":case"rowHeaderVisible":case"isProtected":case"selectionBackColor":case"selectionBorderColor":b.options[d]=e;break;case"rowHeaderAutoTextIndex":b.options[d]=parseInt(e);break;case"dataSource":b.setDataSource(e);break;case"rowCount":b.setRowCount(parseInt(e));break;case"colCount":b.setColumnCount(parseInt(e));break;case"defaultStyle":b.setDefaultStyle(e);break;case"columnHeaderVisible":b.options.colHeaderVisible=e;break;case"columnHeaderAutoText":b.options.colHeaderAutoText=e;break;case"columnHeaderAutoTextIndex":b.options.colHeaderAutoTextIndex=parseInt(e)}b.resumeEvent(),b.resumePaint()}},changeWorkSheetAfterInitColumnByAPI:function a(b,c){var d,e;if(b){b.suspendPaint(),b.suspendEvent();for(d in c)if(c.hasOwnProperty(d))switch(e=c[d],d){case"rowOutlineInfo":b.rowOutlines.ungroup(),e.forEach(function(a){b.rowOutlines.group(a.index,a.count)});break;case"columnOutlineInfo":b.columnOutlines.ungroup(),e.forEach(function(a){b.getColumnCount()>a.index&&b.columnOutlines.group(a.index,a.count)})}b.resumeEvent(),b.resumePaint()}},setColumnWidth:function a(b,c,d){var e=parseInt(d,10);b.setColumnWidth(c,e)},changeColumnByAPI:function a(b,c,d){var e,f;if(b){b.suspendPaint(),b.suspendEvent();for(e in d)if(d.hasOwnProperty(e))switch(f=d[e],e){case"width":h.setColumnWidth(b,c,f);break;case"visible":b.setColumnVisible(c,f);break;case"resizable":b.setColumnResizable(c,f);break;case"autoFit":f&&b.autoFitColumn(c);break;case"style":b.setStyle(-1,c,f);break;case"headerStyle":b.setStyle(-1,c,f,g.default.Spread.Sheets.SheetArea.colHeader);break;case"cellType":b.setCellType(-1,c,f);break;case"formatter":b.setFormatter(-1,c,f,g.default.Spread.Sheets.SheetArea.viewport);break;case"dataField":b.bindColumn(c,{name:f,displayName:d.headerText}),void 0!==d.width&&h.setColumnWidth(b,c,d.width),void 0!==d.visible&&b.setColumnVisible(c,d.visible),void 0!==d.resizable&&b.setColumnResizable(c,d.resizable);break;case"headerText":d.dataField&&(b.bindColumn(c,{name:d.dataField,displayName:f}),void 0!==d.width&&h.setColumnWidth(b,c,d.width),void 0!==d.visible&&b.setColumnVisible(c,d.visible),void 0!==d.resizable&&b.setColumnResizable(c,d.resizable))}b.resumeEvent(),b.resumePaint()}},filterReallyChild:function a(){var b=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],c=arguments[1],d=e.default.Children.map(b,function(a){if(a.type&&a.type.displayName===c)return a});return d||[]},getNewReallyChildren:function a(b,c,d){var f=this.filterReallyChild(b,c);return e.default.Children.map(f,function(a,b){if(a)return e.default.cloneElement(a,h.assign({index:b},d))})},assign:function a(){var b,c,d=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},f={};for(b in d)d.hasOwnProperty(b)&&(f[b]=d[b]);for(c in e)e.hasOwnProperty(c)&&(f[c]=e[c]);return f}},b.default=h},function(a,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var c=window.GC;b.default=c},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(2),f=n(e),g=c(3),h=n(g),i=c(6),j=n(i);function n(a){return a&&a.__esModule?a:{default:a}}function o(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function p(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function q(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}k=h.default.defaultColumnCount,l=h.default.defaultRowCount,m=function(a){q(b,a);function b(a){o(this,b);var c=p(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a));return c.isFirst=!0,c.reallyChildCount=null,c.state={needFirestRender:!0},c.columnIndexManager=new j.default,c.changes=null,c}return d(b,[{key:"componentDidMount",value:function a(){this.setState({needFirestRender:!1})}},{key:"componentWillUpdate",value:function a(b){var c,d;this.isFirst&&(c=h.default.filterReallyChild(this.props.children,h.default.columnTagName),d=c.length?0:null,this.sheet=h.default.addSheet(b.spread,b.index,d),this.changes=h.default.watchChanges({},b),h.default.changeWorkSheetByAPI(this.sheet,this.changes),0===d&&this.removeAllColumn(b),this.isFirst=!1)}},{key:"componentDidUpdate",value:function a(){this.changes&&(h.default.changeWorkSheetAfterInitColumnByAPI(this.sheet,this.changes),this.changes=null),this.needReSetDataSource&&(this.sheet.setDataSource(null),this.sheet.setDataSource(this.props.dataSource),this.needReSetDataSource=!1),this.columnIndexManager.setColumnCount(this.reallyChildCount)}},{key:"componentWillReceiveProps",value:function a(b){this.changes=h.default.watchChanges(this.props,b),h.default.changeWorkSheetByAPI(this.sheet,this.changes),this.changeColumnRowCount(b)}},{key:"changeColumnRowCount",value:function a(b){var c=h.default.filterReallyChild(this.props.children,h.default.columnTagName).length,d=h.default.filterReallyChild(b.children,h.default.columnTagName).length;d>0?null===this.props.dataSource&&null!==b.dataSource?this.sheet.setColumnCount(d):null!==this.props.dataSource&&null===b.dataSource&&this.sheet.setRowCount(b.rowCount||l):null!==this.props.dataSource&&null===b.dataSource&&(this.sheet.setRowCount(b.rowCount||l),this.sheet.setColumnCount(b.colCount||k)),0===c&&d>0?this.removeAllColumn(b):c>0&&0===d&&(b.dataSource?this.needReSetDataSource=!0:h.default.addColumn(this.sheet,0,b.colCount||k))}},{key:"componentWillUnmount",value:function a(){this.sheet.name()&&(this.sheet=h.default.removeSheet(this.props.spread,this.sheet)),this.isFirst=!0}},{key:"removeAllColumn",value:function a(b){h.default.removeAllColumn(this.sheet);var c=b.frozenColumnCount;c&&this.sheet.frozenColumnCount(parseInt(c))}},{key:"renderChildren",value:function a(){var b=this.sheet?{sheet:this.sheet,columnIndexManager:this.columnIndexManager}:{},c=h.default.getNewReallyChildren(this.props.children,h.default.columnTagName,b);return this.reallyChildCount=c.length,c}},{key:"render",value:function a(){return f.default.createElement("div",null,this.renderChildren())}}]),b}(f.default.Component),m.displayName=h.default.sheetTagName,b.default=m},function(a,b){"use strict";var c,d;Object.defineProperty(b,"__esModule",{value:!0}),c=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}();function e(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}d=function(){function a(){e(this,a)}return c(a,[{key:"setColumnCount",value:function a(b){var c,d=[];for(c=0;c<b;c++)d.push(c);this.columnIndexArray=d}},{key:"deleteColumn",value:function a(b){this.columnIndexArray.splice(b,0,"")}},{key:"getRealyColumnIndex",value:function a(b){if(b<this.columnIndexArray.length)return this.columnIndexArray[b]}}]),a}(),b.default=d},function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(2),f=j(e),g=c(3),h=j(g);function j(a){return a&&a.__esModule?a:{default:a}}function k(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function l(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function m(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}i=function(a){m(b,a);function b(a){k(this,b);var c=l(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a));return c.isFirst=!0,c.state={needFirestRender:!0},c}return d(b,[{key:"componentDidMount",value:function a(){this.props.sheet?this.initColumn(this.props):this.setState({needFirestRender:!1})}},{key:"initColumn",value:function a(b){var c,d=b.sheet,e=b.index;h.default.addColumn(b.sheet,b.index),c=h.default.watchChanges({},b),h.default.changeColumnByAPI(d,e,c),this.isFirst=!1}},{key:"componentWillUpdate",value:function a(b){this.isFirst&&this.initColumn(b)}},{key:"componentWillReceiveProps",value:function a(b){var c=this.props,d=c.sheet,e=c.index,f=h.default.watchChanges(this.props,b);h.default.changeColumnByAPI(d,e,f)}},{key:"componentWillUnmount",value:function a(){var b,c=this.props,d=c.sheet,e=c.index,f=c.columnIndexManager;d&&d.name()&&(b=f.getRealyColumnIndex(e),h.default.removeColumn(d,b),f.deleteColumn(e),this.isFirst=!0)}},{key:"render",value:function a(){return f.default.createElement("div",null)}}]),b}(f.default.Component),i.displayName=h.default.columnTagName,b.default=i}])});