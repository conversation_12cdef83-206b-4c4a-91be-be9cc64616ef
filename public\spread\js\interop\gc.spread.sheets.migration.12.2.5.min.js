var GcSpread;!function(a){!function(a){var b=window.GC.Spread.Commands;a.Key=b.Key}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);function d(){this.constructor=a}d.prototype=b.prototype,a.prototype=new d},c=window.GC.Spread.Common;a.Culture=c.CultureManager.culture,window.spreadJSEras=c.CultureInfo.eras,a.addCultureInfo=function(a,b){return e(b,"NumberFormat"),e(b,"DateTimeFormat"),c.CultureManager.addCultureInfo(a,b)},a.getCultureInfo=function(a){var b,e,f;c.CultureManager.getCultureInfo(a),b=this,e=b.NumberFormat,f=b.DateTimeFormat,d(b,e),d(b,f)},a.CultureInfo=function(a){b(c,a);function c(){var b,c,e;a.apply(this),b=this,c=b.NumberFormat,e=b.DateTimeFormat,d(b,c),d(b,e)}return c}(c.CultureInfo);function d(a,b){for(var c in b){if(!b.hasOwnProperty(c))break;a[c]=b[c]}}function e(a,b){var c,d=a[b]||{};for(c in d){if(!d.hasOwnProperty(c))break;a[b][c]=a[c]}}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d,e=null,f=void 0,g=window.GC.Spread.Formatter,h=GC.Spread.Common.CultureManager,i=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);function d(){this.constructor=a}d.prototype=b.prototype,a.prototype=new d};!function(a){a[a.CustomMode=0]="CustomMode",a[a.StandardDateTimeMode=1]="StandardDateTimeMode",a[a.StandardNumericMode=2]="StandardNumericMode"}(a.FormatMode||(a.FormatMode={})),b=a.FormatMode,function(a){a[a.General=0]="General",a[a.Number=1]="Number",a[a.DateTime=2]="DateTime",a[a.Text=3]="Text"}(a.NumberFormatType||(a.NumberFormatType={})),c=a.NumberFormatType,d={StandardNumberFormatter:{CurrencyPattern1:"c",CurrencyPattern2:"C",DecimalPattern1:"d",DecimalPattern2:"D",ScientificPattern1:"e",ScientificPattern2:"E",FixedPointPattern1:"f",FixedPointPattern2:"F",GeneralPattern1:"g",GeneralPattern2:"G",NumberPattern1:"n",NumberPattern2:"N",PercentPattern1:"p",PercentPattern2:"P",RoundTripPattern1:"r",RoundTripPattern2:"R",HexadecimalPattern1:"x",HexadecimalPattern2:"X"},DefaultTokens:{DoubleQuote:'"',SingleQuote:"'",Tab:"\t",LeftSquareBracket:"[",RightSquareBracket:"]",LessThanSign:"<",GreaterThanSign:">",EqualsThanSign:":",PlusSign:"+",HyphenMinus:"-",UnderLine:"_",LeftParenthesis:"(",RightParenthesis:")",Dollar:"$",Comma:",",Space:" ",SolidusSign:"/",ReverseSolidusSign:"\\",Zero:"0",QuestionMark:"?",Colon:":",Semicolon:",",Sharp:"#",CommercialAt:"@",NumberSign:"#",Asterisk:"*",Exponential1:"E+",Exponential2:"E-",DecimalSeparator:".",numberGroupSeparator:",",percentSymbol:"%",nanSymbol:"NaN",FormatSeparator:";",negativeSign:"-",ReplacePlaceholder:"@",ExponentialSymbol:"E"},getPrecision:function(a){var b,c;if(a&&a.length>0)return b=a.substr(1),c=parseInt(b),isNaN(c)?null:c},startWith:function(a,b){return a.substr(0,1)===b},addDecimalPrecision:function(a,b,c){var e,f=a;if(c>0)for(f+=d.DefaultTokens.DecimalSeparator,e=0;e<c;e++)f+=b;return f},addIntegralPrecision:function(a,b,c){var d,e,f=a;if(c>0){for(d="",e=0;e<c;e++)d+=b;f=d+f}return f},standardNumberFormatToExcelCompatibleFormatString:function(a,b){var c,f,g,h,i,j,k;return a?(c=a.toLowerCase(),f=b.NumberFormat,i=d.getPrecision(c),d.startWith(c,d.StandardNumberFormatter.CurrencyPattern1)?(g=f.currencySymbol+"#,##0",h=i!==e?i:f.currencyDecimalDigits,d.addDecimalPrecision(g,d.DefaultTokens.Zero,h)):d.startWith(c,d.StandardNumberFormatter.DecimalPattern1)?(g="0",h=i!==e?i:1,d.addIntegralPrecision(g,d.DefaultTokens.Zero,h-1)):d.startWith(c,d.StandardNumberFormatter.ScientificPattern1)?(g="0",j="E+000",i!==e?(k=d.addDecimalPrecision(g,d.DefaultTokens.Zero,i),k+j):"0.########################################E+000"):d.startWith(c,d.StandardNumberFormatter.FixedPointPattern1)?(g="0",h=i!==e?i:f.numberDecimalDigits,d.addDecimalPrecision(g,d.DefaultTokens.Zero,h)):d.startWith(c,d.StandardNumberFormatter.GeneralPattern1)?"General":d.startWith(c,d.StandardNumberFormatter.NumberPattern1)?(g="#,##0",h=i!==e?i:f.numberDecimalDigits,d.addDecimalPrecision(g,d.DefaultTokens.Zero,h)):d.startWith(c,d.StandardNumberFormatter.PercentPattern1)?(g="0",h=i!==e?i:f.numberDecimalDigits,d.addDecimalPrecision(g,d.DefaultTokens.Zero,h)+d.DefaultTokens.percentSymbol):d.startWith(c,d.StandardNumberFormatter.RoundTripPattern1)?"General":d.startWith(c,d.StandardNumberFormatter.HexadecimalPattern1)?"General":e):e},standardDateTimeFormatToExcelCompatibleFormatString:function(a,b){var c="",d=b.DateTimeFormat;switch(a){case"D":c=d.longDatePattern;break;case"d":c=d.shortDatePattern;break;case"F":c=d.fullDateTimePattern;break;case"f":c=d.longDatePattern+" "+d.shortTimePattern;break;case"G":c=d.shortDatePattern+" "+d.longTimePattern;break;case"g":c=d.shortDatePattern+" "+d.shortTimePattern;break;case"M":case"m":c=d.monthDayPattern;break;case"R":case"r":c=d.rfc1123Pattern;break;case"s":c=d.sortableDateTimePattern;break;case"T":c=d.longTimePattern;break;case"t":c=d.shortTimePattern;break;case"u":c=d.universalSortableDateTimePattern;break;case"U":c=d.fullDateTimePattern;break;case"Y":case"y":c=d.yearMonthPattern}return c.split("'").join('"')}},a.GeneralFormatter=function(a){i(f,a);function f(c,e,f){var g,i;this.formatMode=e,this.standardFormatStr=c,g=c,i=f?h.getCultureInfo(f):h.getCultureInfo(),e===b.StandardNumericMode?g=d.standardNumberFormatToExcelCompatibleFormatString(c,i):e===b.StandardDateTimeMode&&(g=d.standardDateTimeFormatToExcelCompatibleFormatString(c,i)),a.call(this,g,f)}return f.prototype.updateCulture=function(){var a,c,e;this.cultureName||(a=this.standardFormatStr,c=this.formatCached,e=h.getCultureInfo(),this.formatMode===b.StandardNumericMode?c=d.standardNumberFormatToExcelCompatibleFormatString(a,e):this.formatMode===b.StandardDateTimeMode&&(c=d.standardDateTimeFormatToExcelCompatibleFormatString(a,e)),this.formatCached=c,this.init())},f.prototype.Format=a.prototype.format,f.prototype.format=function(a,b){return this.updateCulture(),this.Format.call(this,a,b)},f.prototype.FormatString=a.prototype.formatString,f.prototype.HasFormatedColor=a.prototype.hasFormatedColor,f.prototype.Parse=a.prototype.parse,f.prototype.GetPreferredEditingFormatter=function(a){var b=this.getPreferredEditingFormatter.call(this,a);return b.Format=this.format,b.FormatString=this.formatString,b.HasFormatedColor=this.hasFormatedColor,b.Parse=this.parse,b},f.prototype.GetPreferredDisplayFormatter=function(a,b){var c=this.getPreferredDisplayFormatter.call(this,a,b);return c.Format=this.format,c.FormatString=this.formatString,c.HasFormatedColor=this.hasFormatedColor,c.Parse=this.parse,c},f.prototype.FormatMode=function(){return this.formatMode},f.prototype.ExcelCompatibleFormatString=function(){var a,c,d,f,g=this;switch(this.updateCulture(),g.init(),a=e,g.FormatMode()){case b.CustomMode:if(g.formatters)for(c=0;c<g.formatters.length;c++)d=g.formatters[c],j(d,"CustomNumberFormat")&&(a==e?a="":a+=";",f=d.ExcelCompatibleFormatString(),a+=f);break;case b.StandardDateTimeMode:if(j(g.formatters[0],"StandardDateTimeFormatter"))return g.formatters[0].ExcelCompatibleFormatString();break;case b.StandardNumericMode:if(j(g.formatters[0],"StandardNumberFormatter"))return g.formatters[0].ExcelCompatibleFormatString()}return a?""+a:""},f.prototype.IsDefaultFormat=function(){return"general"===this.formatCached.toLowerCase()},f.prototype.GetFormatType=function(a){var b,d;if(this.init(),b=this.Ud(a),j(b,"CustomNumberFormat")){if(d=b.Formatter(),j(d,"NumberFormatDigital"))return c.Number;if(j(d,"NumberFormatDateTime"))return c.DateTime;if(j(d,"NumberFormatText"))return c.Text}else{if(j(b,"NumberFormatDigital")||j(b,"StandardNumberFormatter"))return c.Number;if(j(b,"NumberFormatDateTime")||j(b,"StandardDateTimeFormatter"))return c.DateTime;if(j(b,"NumberFormatText"))return c.Text}return c.General},f}(g.GeneralFormatter),a.AutoFormatter=function(){function a(a){this.S5=a}return a.prototype.FormatString=function(){return this.S5?this.S5.FormatString():""},a.prototype.innerFormatter=function(a){return 0===arguments.length?this.S5:(this.S5=a,this)},a.prototype.Parse=function(a){return this.S5?this.S5.Parse(a):a},a.prototype.Format=function(a){return this.S5?this.S5.Format(a):a===f||a===e?"":""+a},a.prototype.toJSON=function(){return this.S5.toJSON()},a.prototype.fromJSON=function(a){this.S5=new GeneralFormatter(a.formatCached,a.formatModeType,a.customerCultureName)},a}();function j(a,b){var c,d;if(a===f||a===e)return"null"===b;if(!b)return!1;if(a&&a._classNames){for(c=0;c<a._classNames.length;c++)if(d=a._classNames[c],d===b)return!0;return!1}return"DateTime"===b||"TimeSpan"===b?a instanceof Date:!("function"!==b||!/^\s*\bfunction\b/.test(""+a))||(Object.prototype.toString.call(a).slice(8,-1).toLowerCase()===b.toLowerCase()||a instanceof b)}a.CustomFormatterBase=function(a){i(b,a);function b(){a.call(this)}return b.prototype.Format=function(a,b){return null},b.prototype.Parse=function(a){return null},b.prototype.format=function(a,b){return this.Format.apply(this,arguments)},b.prototype.parse=function(a){return this.Parse.apply(this,arguments)},b.prototype.FormatString=a.prototype.formatString,b}(g.FormatterBase)}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){var b,c=window.GC.Spread.Slicers;a.Slicer||(a.Slicer={}),a.Slicer.FilteredOutDataType=c.FilteredOutDataType,a.Slicer.SlicerAggregateType=c.SlicerAggregateType,b=c.GeneralSlicerData,b.prototype.changeData=b.prototype.onDataChanged,b.prototype.changeColumnName=b.prototype.onColumnNameChanged,b.prototype.addRows=b.prototype.onRowsAdded,b.prototype.removeRows=b.prototype.onRemovedRows,b.prototype.removeColumns=b.prototype.onRemovedColumns,a.Slicer.GeneralSlicerData=b,a.Slicer.SlicerDataItem=function(){function a(a,b,c){var d=this;d.columnName=a,d.rowIndex=b,d.data=c}return a}()}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets;function c(a,c,d,e,f,g,h,i,j,k,l,m,n){b.ColorScheme.apply(this,[a,e,f,c,d,g,h,i,j,k,l,m,n])}c.prototype=new b.ColorScheme,c.prototype.hyperline=c.prototype.hyperlink,c.prototype.followedHyperline=c.prototype.followedHyperlink,a.ThemeColor=c,a.SpreadTheme=b.Theme,a.ThemeColors=b.ThemeColors,a.SpreadThemes=b.Themes}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){function b(a){return a&&a[0]?a[0].toUpperCase()+a.substr(1):a}function c(a,c){var d,e,f={};for(d in a)a.hasOwnProperty(d)&&(e=c&&c[d]||b(d),f[e]=a[d]);return f}a.F5=c,a.I5=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);function d(){this.constructor=a}d.prototype=b.prototype,a.prototype=new d},a.CellPosition=function(a,b){this.row=a,this.col=b},a.defineProperty=function(a,b,c){var d={};c.forEach(function(c){"function"==typeof a[b][c]&&(d[c]={get:function(){return a[b][c]()},set:function(d){a[b][c](d)}})}),Object.defineProperties(a,d)},a.getPropertyValue=function(a){return"function"==typeof a?a():a},a._5=function(a,b,c,d,e){d||(d=c),a[c]=function(){var a=this[b][d].apply(this[b],arguments);return a&&"function"==typeof e&&(a=e(a)),a}},a.b6=function(a){!function(a){var b=window.GC.Spread.CalcEngine,c=b.Functions.Function,d=a.addCustomFunction,e=a.removeCustomFunction,f=a.clearCustomFunctions,g=a.getCustomFunction,h={};a.addCustomFunction=function(a){var b,c;a&&(h[a.name]=a,b=Object.getPrototypeOf(a),b&&b.evaluate&&!b.P5&&(c=b.evaluate,b.P5=c,b.evaluate=function(){if(b.evaluateAsync){var a=Array.prototype.slice.call(arguments,1),d=arguments[0];return d.SetAsyncResult=d.setAsyncResult,b.evaluateAsync.call(this,a,d)}return c.call(this,arguments)})),d.call(this,a)},a.removeCustomFunction=function(a){var b,c=g.call(this,a);c&&(delete h[a],e.call(this,a),b=Object.getPrototypeOf(c),b&&b.P5&&(b.evaluate=b.P5,delete b.P5))},a.clearCustomFunctions=function(){i(h,function(a){if(a){var b=Object.getPrototypeOf(a);delete b.___evaluate__}}),h={},f.call(this)},a.addCustomFunctionDescription=function(a){var b=a.name,c=g.call(this,b);c&&j(c,{description:a.description,parameters:a.parameters})},a.getCustomFunctionDescription=function(a){var b=g.call(this,a);if(b)return b.description()},a.removeCustomFunctionDescription=function(a){var b=g.call(this,a);b&&j(b,null)},a.clearCustomFunctionDescriptions=function(){i(h,function(a){a&&j(a,null)})};function i(a,b){var c,d;if(a&&"function"==typeof b)for(c in a)a.hasOwnProperty(c)&&(d=a[c],d&&b(d))}function j(a,b){a&&c.call(a,a.name,a.minArgs,a.maxArgs,b)}}(a)};var d=window.GC.Spread.Sheets;a.getTypeFromString=d.getTypeFromString,a.findControl=d.findControl,a.VisualState=c(d.VisualState),a.SortState=c(d.SortState),a.StorageType=c(d.StorageType),a.Point=d.Point,a.Rect=d.Rect,a.Range=d.Range,a.Events=d.Events,a.FormatConverter=function(){function a(){}a.IsNumber=function(a){return a instanceof Date||"number"==typeof a};function b(a){return a.getTime()/864e5+25569}return a.ToDouble=function(a){if(a instanceof Date)return b(a);var c=typeof a;switch(c){case"number":return a;case"boolean":return a?1:0;default:return a?parseFloat(a):0}},a.toString=function(a){if(null==a)return"";var b=typeof a;switch(b){case"string":return a;case"boolean":return a?"TRUE":"FALSE";default:return""+a}},a}(),a.KeyMap=function(){function a(a,b,c,d,e,f){var g=this;g.key=a,g.ctrl=b,g.shift=c,g.alt=d,g.meta=f&&e||!1,g.action=f||e}return a}()}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c=void 0,d=window.GC.Spread.Sheets;a.HorizontalAlign=d.HorizontalAlign,a.VerticalAlign=d.VerticalAlign,a.ImageLayout=a.F5(d.ImageLayout),a.LineStyle=d.LineStyle,a.TextDecorationType=a.F5(d.TextDecorationType);function e(a,b){d.LineBorder.apply(this,arguments)}e.prototype=new d.LineBorder,e.prototype.width=function(b){var c=a.LineStyle;if(b&&b.style)switch(b.style){case c.dashDot:case c.thin:case c.dashed:case c.dotted:case c.hair:case c.dashDotDot:return 1;case c.medium:case c.mediumDashDot:case c.mediumDashDotDot:case c.mediumDashed:case c.slantedDashDot:return 2;case c.thick:case c.double:return 3}return 0},a.LineBorder=e,a.buildFontString=function(a){var b,c,d,e=navigator.userAgent.toLowerCase();return e.indexOf("chrome")<0&&e.indexOf("PhantomJS")<0&&e.indexOf("webkit")>=0?a.font:(b="",c="normal",d="400",a.fontStyle!==c&&(b=a.fontStyle),a.fontVariant!==c&&(b+=(b?" ":"")+a.fontVariant),a.fontWeight!==c&&a.fontWeight!==d&&(b+=(b?" ":"")+a.fontWeight),b+=(b?" ":"")+a.fontSize,a.lineHeight!==c&&(b+="/"+a.lineHeight),b+=" "+a.fontFamily)},b=function(b){a.I5(c,b);function c(){b.apply(this,arguments)}return c.prototype.copyFrom=function(a){this.fromJSON(a.toJSON())},c}(d.Style),a.Style=b}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(b){var c,d,e,f,g,h,i,j=window.GC.Spread.Sheets;b.ShowResizeTip=b.F5(j.ShowResizeTip),b.ShowScrollTip=b.F5(j.ShowScrollTip),b.AutoFitType=b.F5(j.AutoFitType),b.InvalidOperationType=b.F5(j.InvalidOperationType),b.ResizeZeroIndicator=b.F5(j.ResizeZeroIndicator),b.HorizontalPosition=j.HorizontalPosition,b.VerticalPosition=j.VerticalPosition,c=j.Workbook,d=window.jQuery;function k(a,b){var c,d,e,f,g,h;b&&(c=a.defaults,d=a.options.gridline,"string"==typeof b.name&&b.name.length>0&&a.name(b.name),b.data&&a.setDataSource(b.data),"number"==typeof b.defaultRowHeight&&(c.rowHeight=b.defaultRowHeight),"number"==typeof b.defaultColWidth&&(c.colWidth=b.defaultColWidth),"number"==typeof b.defaultRowHeaderColWidth&&(c.rowHeaderColWidth=b.defaultRowHeaderColWidth),"number"==typeof b.defaultColHeaderRowHeight&&(c.colHeaderRowHeight=b.defaultColHeaderRowHeight),"number"==typeof b.rowCount&&a.setRowCount(b.rowCount),"number"==typeof b.colCount&&a.setColumnCount(b.colCount),"number"==typeof b.frozenRowCount&&a.frozenRowCount(b.frozenRowCount),"number"==typeof b.frozenColCount&&a.frozenColCount(b.frozenColCount),"number"==typeof b.frozenTrailingRowCount&&a.frozenTrailingRowCount(b.frozenTrailingRowCount),"number"==typeof b.frozenTrailingColCount&&a.frozenTrailingColCount(b.frozenTrailingColCount),b.gridlineColor&&(d.color=b.gridlineColor),"boolean"==typeof b.showVerticalGridline&&(d.showVerticalGridline=b.showVerticalGridline),"boolean"==typeof b.showHorizontalGridline&&(d.showHorizontalGridline=b.showHorizontalGridline),b.borderColor&&(a.options.borderColor=b.borderColor),"number"==typeof b.borderWidth&&(a.options.borderWidth=b.borderWidth),"number"==typeof b._zoomFactor&&a.zoom(b._zoomFactor),"boolean"==typeof b.rowHeaderVisible&&(a.options.rowHeaderVisible=b.rowHeaderVisible),"boolean"==typeof b.colHeaderVisible&&(a.options.colHeaderVisible=b.colHeaderVisible),"boolean"==typeof b.autoGenerateColumns&&(a.autoGenerateColumns=b.autoGenerateColumns),b.rowHeaderAutoText&&(a.rowHeaderAutoText=b.rowHeaderAutoText),b.colHeaderAutoText&&(a.colHeaderAutoText=b.colHeaderAutoText),"number"==typeof b.Jl&&(e=b.Jl),"number"==typeof b.Kl&&(f=b.Kl),a.setActiveCell(e,f),"boolean"==typeof b.d6&&(a.options.allowCellOverflow=b.d6),"boolean"==typeof b.isProtected&&(a.options.isProtected=b.isProtected),"boolean"==typeof b.allowUndo&&(g=a.getParent(),g&&(g.options.allowUndo=b.allowUndo)),h=b.columns,h&&h.length>0&&(a.autoGenerateColumns=!1,a.bindColumns(h)))}function l(a,b){var c,d,e=a.getSheetCount();for(c=0;c<e;c++)if(d=a.sheets[c],b===d.name())return!1;return!0}function m(a,b){for(var c="Sheet";!l(a,c+b);)b++;return c+b}function n(a,c){var d,e,f;if(c&&c.length>0){for(e=a.getSheetCount();e<c.length;)d=new b.Sheet(m(e)),a.addSheet(e,d),e=a.getSheetCount();for(f=0;f<c.length;f++)d=a.getSheet(f),k(d,c[f])}}function o(a,b){var e,f,g,h,i=this;i.customCommandIndex=1,"number"!=typeof b&&null!==b&&void 0!==b||(e={},"string"==typeof arguments[0]&&(e.name=arguments[0]),"number"==typeof arguments[1]&&(e.sheetCount=arguments[1]),b=e),"string"!=typeof a&&null!==a&&void 0!==a||(a=null,"object"==typeof arguments[2]&&(a=arguments[2])),c.apply(i,arguments),d&&d(a).data("spread",d(a).data("workbook")),b&&(f=b.name,f&&(i.name=f),g=b.activeSheetIndex,g&&i.setActiveSheetIndex(g),h=b.sheets,h&&n(i,b.sheets))}b.I5(o,c),e={canUserDragDrop:"allowUserDragDrop",canUserDragFill:"allowUserDragFill",allowUserZoom:"allowUserZoom",allowUserResize:"allowUserResize",allowUndo:"allowUndo",allowSheetReorder:"allowSheetReorder",defaultDragFillType:"defaultDragFillType",showDragFillSmartTag:"showDragFillSmartTag",showHorizontalScrollbar:"showHorizontalScrollbar",showVerticalScrollbar:"showVerticalScrollbar",scrollbarShowMax:"scrollbarShowMax",scrollbarMaxAlign:"scrollbarMaxAlign",tabStripVisible:"tabStripVisible",tabEditable:"tabEditable",newTabVisible:"newTabVisible",tabNavigationVisible:"tabNavigationVisible",cutCopyIndicatorVisible:"cutCopyIndicatorVisible",cutCopyIndicatorBorderColor:"cutCopyIndicatorBorderColor",backColor:"backColor",backgroundImage:"backgroundImage",backgroundImageLayout:"backgroundImageLayout",grayAreaBackColor:"grayAreaBackColor",showResizeTip:"showResizeTip",showDragDropTip:"showDragDropTip",showDragFillTip:"showDragFillTip",showScrollTip:"showScrollTip",scrollIgnoreHidden:"scrollIgnoreHidden",highlightInvalidData:"highlightInvalidData",useTouchLayout:"useTouchLayout",hideSelection:"hideSelection",resizeZeroIndicator:"resizeZeroIndicator",canUserEditFormula:"allowUserEditFormula",enableFormulaTextbox:"enableFormulaTextbox",autoFitType:"autoFitType",referenceStyle:"referenceStyle"};for(f in e)e.hasOwnProperty(f)&&!function(a,b){o.prototype[a]=function(a){return 0===arguments.length?this.options[b]:(this.options[b]=a,this)}}(f,e[f]);o.prototype.setTabStripRatio=function(a){this.options.tabStripRatio=a},o.prototype.getTabStripRatio=function(){return this.options.tabStripRatio},o.prototype.showCell=function(a,b,c,d){var e=this.getActiveSheet();e&&e.showCell(a,b,c,d)},o.prototype.showColumn=function(a,b){var c=this.getActiveSheet();c&&c.showColumn(a,b)},o.prototype.showActiveCell=function(a,b){var c=this.getActiveSheet();c&&c.showCell(c.getActiveRowIndex(),c.getActiveColumnIndex(),a,b)},o.prototype.showRow=function(a,b){var c=this.getActiveSheet();c&&c.showRow(a,b)},o.prototype.doCommand=function(a){var b,c,d=this.commandManager();d&&(a.customAction?(a.isRegistered||(b={canUndo:a.canUndo(),execute:function(b,c,d){return a.executeImp(b,c,d)}},c="customCommand_"+this.customCommandIndex,this.customCommandIndex++,d.register(c,b),a.name=c,a.isRegistered=!0),d.execute({cmd:a.name})):d.execute(a.options))},g=c.prototype.isPaintSuspended,o.prototype.isPaintSuspended=function(a){if(0===arguments.length)return g.call(this);if(a)this.isPaintSuspended()||this.suspendPaint();else for(;this.isPaintSuspended();)this.resumePaint();return this},o.prototype.fromJSON=function(a){var d=j.getTypeFromString,e=b.getTypeFromString===d;e||(j.getTypeFromString=b.getTypeFromString),c.prototype.fromJSON.call(this,a),e||(j.getTypeFromString=d)},o.prototype.vv=function(b){return new a.Sheets.Sheet(b)},b.b6(o.prototype),b.Spread=o,j.Workbook=o,window.wijmo||(window.wijmo={spread:b}),h=b.GcSpreadSheetsOptions=function(){var a=this;a.sheetCount=1,a.name="",a.font="10pt Arial",a.allowUserZoom=!0,a.allowUserResize=!0,a.tabStripVisible=!0,a.tabEditable=!0,a.newTabVisible=!0,a.tabStripRatio=.5,a.activeSheetIndex=0,a.sheets=[]},i={init:function(a){var b,c,d=new h,e={};if(a)for(b in d)d.hasOwnProperty(b)&&(e[b]=a[b]||d[b]);else e=d;c=this.get(0),new o(c,e)},spread:function(){return this.data("workbook")},destroy:function(){var a=this.data("workbook");a&&a.destroy()},refresh:function(){var a=this.data("workbook");a&&a.refresh()},repaint:function(){var a=this.data("workbook");a&&a.repaint()}},d&&(d.fn.wijspread=function(a){return i[a]?i[a].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof a&&a?void 0:i.init.apply(this,arguments)},void 0===d.wijmo&&(d.wijmo={wijspread:void 0}),d.wijmo.wijspread=b)}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c=window.GC.Spread.Sheets,d=c.Commands,e=a.KeyMap,f=a.I5;a.RangeChangedAction=a.F5(c.RangeChangedAction),a.SheetArea=c.SheetArea,a.HeaderAutoText=c.HeaderAutoText,b=function(b){var c,g,h;f(i,b);function i(){b.apply(this,arguments),this.rowRangeGroup=this.rowOutlines,this.colRangeGroup=this.columnOutlines,this.keyMap=[],this.T5=!0}i.prototype.addKeyMap=function(a,b,c,f,g,h){var i,j,k,l;"function"==typeof h&&(i=h.name||h.Vfa,j=this.getParent(),k=j&&j.commandManager(),k&&d[i]&&k.register("custom_"+i,d[i],a,b,c,f,g)),l=this.keyMap,l||(l=this.keyMap=[]),l.push(new e(a,b,c,f,g,h))},i.prototype.removeKeyMap=function(a,b,c,d,e){var f,g,h,i,j=this.getParent(),k=j&&j.commandManager();if(k&&k.setShortcutKey("",a,b,c,d,e),f=this.keyMap)for(g=0,h=f.length;g<h;g++)if(i=f[g],i&&i.key===a&&i.ctrl===b&&i.shift===c&&i.alt===d&&i.meta===e){f.splice(g,1);break}},c=b.prototype.isPaintSuspended,i.prototype.isPaintSuspended=function(a){if(0===arguments.length)return c.call(this);if(a)this.isPaintSuspended()||this.suspendPaint();else for(;this.isPaintSuspended();)this.resumePaint();return this},i.prototype.getName=function(){return this.name()},i.prototype.setName=function(a){this.name(a)},i.prototype.allowUndo=function(a){var b=this.getParent();return 0===arguments.length?!b||b.options.allowUndo:(b&&(b.options.allowUndo=a),this)},i.prototype.referenceStyle=function(b){var c=this.getParent();return 0===arguments.length?c?c.options.referenceStyle:a.ReferenceStyle.A1:(c&&(c.options.referenceStyle=b),this)},i.prototype.setGridlineOptions=function(a){for(var b in a)a.hasOwnProperty(b)&&(this.options.gridline[b]=a[b])},i.prototype.getGridlineOptions=function(){return this.options.gridline},i.prototype.setRowHeaderVisible=function(a){this.options.rowHeaderVisible=a},i.prototype.getRowHeaderVisible=function(){return this.options.rowHeaderVisible},i.prototype.setColumnHeaderVisible=function(a){this.options.colHeaderVisible=a},i.prototype.getColumnHeaderVisible=function(){return this.options.colHeaderVisible},i.prototype.setRowHeaderAutoText=function(a){this.options.rowHeaderAutoText=a},i.prototype.getRowHeaderAutoText=function(){return this.options.rowHeaderAutoText},i.prototype.setColumnHeaderAutoText=function(a){this.options.colHeaderAutoText=a},i.prototype.getColumnHeaderAutoText=function(){return this.options.colHeaderAutoText},i.prototype.setRowHeaderAutoTextIndex=function(a){this.options.rowHeaderAutoTextIndex=a},i.prototype.getRowHeaderAutoTextIndex=function(){return this.options.rowHeaderAutoTextIndex},i.prototype.setColumnHeaderAutoTextIndex=function(a){this.options.colHeaderAutoTextIndex=a},i.prototype.getColumnHeaderAutoTextIndex=function(){return this.options.colHeaderAutoTextIndex},i.prototype.setIsProtected=function(a){this.options.isProtected=a},i.prototype.getIsProtected=function(){return this.options.isProtected},i.prototype.setFrozenCount=function(a,b){this.frozenRowCount(a),this.frozenColumnCount(b)},i.prototype.setFrozenColumnCount=function(a){this.frozenColumnCount(a)},i.prototype.getFrozenColumnCount=function(){return this.frozenColumnCount()},i.prototype.setFrozenRowCount=function(a){this.frozenRowCount(a)},i.prototype.getFrozenRowCount=function(){return this.frozenRowCount()},i.prototype.setFrozenTrailingRowCount=function(a){this.frozenTrailingRowCount(a)},i.prototype.getFrozenTrailingRowCount=function(){return this.frozenTrailingRowCount()},i.prototype.setFrozenTrailingColumnCount=function(a){this.frozenTrailingColumnCount(a)},i.prototype.getFrozenTrailingColumnCount=function(){return this.frozenTrailingColumnCount()},g={allowCellOverflow:"allowCellOverflow",sheetTabColor:"sheetTabColor",frozenlineColor:"frozenlineColor",clipBoardOptions:"clipBoardOptions",protectionOption:"protectionOptions",selectionBackColor:"selectionBackColor",selectionBorderColor:"selectionBorderColor"};for(h in g)g.hasOwnProperty(h)&&!function(a,b){i.prototype[a]=function(a){return 0===arguments.length?this.options[b]:(this.options[b]=a,this)}}(h,g[h]);return i.prototype.undoManager=function(){return this.getParent().undoManager()},i.prototype.doCommand=function(a){return this.getParent().doCommand(a)},i.prototype.allowEditorReservedLocations=function(a){return 0===arguments.length?this.T5:(this.T5=a,this)},i.prototype.getDeleteRows=function(){return this.getDeletedRows()},i.prototype.updateEventsData=function(a,b){switch(a){case"RangeChanged":b.column=b.col,b.columnCount=b.colCount;break;case"ClipboardChanging":case"ClipboardChanged":b.copyData=b.copyData.text}},i}(c.Worksheet),a.Sheet=b,c.Worksheet=b,a.b6(b.prototype)}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d=window.GC.Spread.Sheets,e=a.I5;a.TextFileOpenFlags={None:0,IncludeRowHeader:1,IncludeColumnHeader:2,UnFormatted:8,ImportFormula:16},a.ClipboardPasteOptions=a.F5(d.ClipboardPasteOptions),a.CopyToOption=a.F5(d.CopyToOptions,{outline:"RangeGroup"}),b={},["cancelInput","changeFormulaReference","clear","clearAndEditing","commitArrayFormula","commitInputNavigationDown","commitInputNavigationUp","copy","cut","moveToNextCell","moveToNextCellThenControl","moveToPreviousCell","moveToPreviousCellThenControl","navigationBottom","navigationDown","navigationEnd","navigationEnd2","navigationFirst","navigationHome","navigationHome2","navigationLast","navigationLeft","navigationNextSheet","navigationPageDown","navigationPageUp","navigationPreviousSheet","navigationRight","navigationTop","navigationUp","paste","redo","selectionBottom","selectionDown","selectionEnd","selectionFirst","selectionHome","selectionLast","selectionLeft","selectionPageDown","selectionPageUp","selectionRight","selectionTop","selectionUp","selectNextControl","selectPreviousControl","undo"].forEach(function(a){b[a]=function(){var b,c,e;d.Commands[a]&&(b=this,c=b.getParent(),c&&(e=c&&c.commandManager(),e&&e.execute({cmd:a,sheetName:b.name()})))},b[a].Vfa=a}),a.SpreadActions=b,c={},c.ActionBase=function(){function a(){this.canExecuteChanged=null,this.customAction=!0,this.isRegistered=!1,this.name=null}return a.prototype.executeImp=function(a,b,c){if(c){if(this.canUndo())return this.undo.call(this,b)}else if(this.canExecute())return this.execute.call(this,b);return!1},a.prototype.execute=function(a){},a.prototype.canExecute=function(){return!0},a.prototype.canUndo=function(){return!0},a.prototype.saveState=function(){},a.prototype.undo=function(a){return!0},a}(),c.FloatingObjectUndoActionBase=function(a){e(b,a);function b(){a.call(this)}return b.prototype.init=function(a,b){this.kj=a,this.xS=b,this.yS=[]},b.prototype.refreshUI=function(a){var b=this.kj;b&&(b.invalidateLayout(),b.repaint())},b.prototype.hasFloatingObjectsSelected=function(){var a,b=this.kj.floatingObjects.all();for(a=0;a<b.length;a++)if(b[a].isSelected())return!0;return!1},b.prototype.canExecute=function(a){return!!this.hasFloatingObjectsSelected()},b.prototype.canUndo=function(){return!!(this.yS&&this.yS.length>0)},b.prototype.saveState=function(){var a,b,c,d=this;if(d.xS&&d.xS.names instanceof Array)for(a=0,b=d.xS.names.length;a<b;a++)c=d.kj.floatingObjects.get(d.xS.names[a]),c&&d.yS.push(c)},b}(c.ActionBase),c.DragFillExtent=function(a,b,c,d){this.startRange=a,this.fillRange=b,this.autoFillType=c,this.fillDirection=d},c.DragFillUndoAction=function(a,b){this.options={cmd:"dragFill",sheetName:a.name(),startRange:b.startRange,fillRange:b.fillRange,autoFillType:b.autoFillType,fillDirection:b.fillDirection}},c.DragDropUndoAction=function(a,b,c,d,e){this.options={cmd:"dragDrop",sheetName:a.name(),formRow:b.fromRow,fromColumn:b.fromColumn,toRow:b.toRow,tocolumn:b.toRow,rowCount:b.rowCount,columnCount:b.columnCount,copy:c,insert:d,option:e}},c.DragDropExtent=function(a,b,c,d,e,f){var g=this;g.fromRow=a,g.fromColumn=b,g.toRow=c,g.toColumn=d,g.rowCount=e,g.columnCount=f},c.ClipboardPasteUndoAction=function(a,b,c,d,e){this.options={cmd:"clipboardPaste",sheetName:a.name(),fromSheet:b,fromRanges:[d.fromRange],pastedRanges:d.pastedRanges,isCutting:d.isCutting,clipboardText:d.clipboardText,pasteOption:e}},c.ClipboardPasteRangeUndoAction=function(a,b,c,d,e){this.options={cmd:"clipboardPaste",sheetName:a.name(),fromSheet:b,fromRanges:d.sourceRange,pastedRanges:d.targetRange,isCutting:d.isCutting,clipboardText:d.clipboardText,pasteOption:e}},c.CellEditUndoAction=function(a,b){this.options={cmd:"editCell",sheetName:a.name(),row:b.row,col:b.col,newValue:b.newValue,autoFormat:b.autoFormat}},c.ClearRangeValueUndoAction=function(a,b){this.options={cmd:"clearValues",sheetName:a.name(),ranges:[b]}},c.ClearValueUndoAction=function(a,b){this.options={cmd:"clearValues",sheetName:a.name(),ranges:b}},c.ColumnResizeUndoAction=function(a,b,c,d){this.options={cmd:"resizeColumn",sheetName:a.name(),columns:b,size:c,rowHeader:d}},c.ColumnAutoFitUndoAction=function(a,b,c,d){this.options={cmd:"autoFitColumn",sheetName:a.name(),columns:b,rowHeader:c,autoFitType:d}},c.ZoomUndoAction=function(a,b){this.options={cmd:"zoom",sheetName:a.name(),zoomFactor:b}},c.SheetRenameUndoAction=function(a,b){this.options={cmd:"renameSheet",sheetName:a.name(),name:b}},c.GroupExtent=function(a,b){this.index=a,this.count=b},c.RowGroupUndoAction=function(a,b){this.options={cmd:"outlineRow",sheetName:a.name(),index:b.index,count:b.count}},c.ColumnGroupUndoAction=function(a,b){this.options={cmd:"outlineColumn",sheetName:a.name(),index:b.index,count:b.count}},c.RowUngroupUndoAction=function(a,b){this.options={cmd:"removeRowOutline",sheetName:a.name(),index:b.index,count:b.count}},c.ColumnUngroupUndoAction=function(a,b){this.options={cmd:"removeColumnOutline",sheetName:a.name(),index:b.index,count:b.count}},c.GroupExpandExtent=function(a,b,c){this.index=a,this.level=b,this.collapsed=c},c.RowGroupExpandUndoAction=function(a,b){this.options={cmd:"expandRowOutline",sheetName:a.name(),index:b.index,level:b.level,collapsed:b.collapsed}},c.ColumnGroupExpandUndoAction=function(a,b){this.options={cmd:"expandColumnOutline",sheetName:a.name(),index:b.index,level:b.level,collapsed:b.collapsed}},c.GroupHeaderExpandExtent=function(a){this.level=a},c.RowGroupHeaderExpandUndoAction=function(a,b){this.options={cmd:"expandRowOutlineForLevel",sheetName:a.name(),level:b.level}},c.ColumnGroupHeaderExpandUndoAction=function(a,b){this.options={cmd:"expandColumnOutlineForLevel",sheetName:a.name(),
level:b.level}},c.RowResizeUndoAction=function(a,b,c,d){this.options={cmd:"resizeRow",sheetName:a.name(),rows:b,size:c,columnHeader:d}},c.RowAutoFitUndoAction=function(a,b,c,d){this.options={cmd:"autoFitRow",sheetName:a.name(),rows:b,columnHeader:c,autoFitType:d}},c.FloatingObjectExtent=function(a){this.names=a},c.ReszingFloatingObjectExtent=function(a,b,c,d){this.offsetX=a,this.offsetY=b,this.offsetWidth=c,this.offsetHeight=d},c.ResizingFloatingObjectUndoAction=function(a,b,c){this.options={cmd:"resizeFloatingObjects",sheetName:a.name(),floatingObjects:b.names,offsetX:c.offsetX,offsetY:c.offsetY,offsetWidth:c.offsetWidth,offsetHeight:c.offsetHeight}},c.MovingFloatingObjectExtent=function(a,b){this.offsetX=a,this.offsetY=b},c.MovingFloatingObjectUndoAction=function(a,b,c){this.options={cmd:"moveFloatingObjects",sheetName:a.name(),floatingObjects:b.names,offsetX:c.offsetX,offsetY:c.offsetY}},c.DeleteFloatingObjectUndoAction=function(a,b){this.options={cmd:"deleteFloatingObjects",sheetName:a.name(),floatingObjects:b.names}},c.ClipboardPasteFloatingObjectUndoAction=function(a,b,c){this.options={cmd:"pasteFloatingObjects",sheetName:a.name(),floatingObjects:b.names,fromSheet:c}},c.DragCopyFloatingObjectUndoAction=function(a,b,c){this.options={cmd:"dragCopyFloatingObjects",sheetName:a.name(),floatingObjects:b.names,offsetX:c.offsetX,offsetY:c.offsetY}},c.PasteExtent=function(a,b,c,d){this.fromRange=a,this.pastedRanges=b,this.isCutting=c,this.clipboardText=d},c.PasteRangeExtent=function(a,b,c,d){this.sourceRange=a,this.targetRange=b,this.isCutting=c,this.clipboardText=d},a.UndoRedo=c}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){a.EditorStatus=a.F5(window.GC.Spread.Sheets.EditorStatus)}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets;a.SelectionPolicy=a.F5(b.SelectionPolicy),a.SelectionUnit=a.F5(b.SelectionUnit)}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d,e=window.GC.Spread.Sheets,f=a.I5,g=a.Sheet;g.prototype.setBorder=function(a,b,c,d){var f=new e.CellRange(this,a.row,a.col,a.rowCount,a.colCount,d);f.setBorder(b,c)};function h(a,b,c,d,e,f,g){var h,i,j,k,l;for(h=0,i=b;h<d;h++,i++)for(j=0,k=c;j<e;j++,k++)l=a.getStyle(i,k,g),l&&("dataValidator"===f&&(f="validator"),l[f]=void 0)}b=function(a){f(b,a);function b(b,c,d,e){a.call(this,b,c,d,1,1,e),Object.defineProperty(this,"row2",{get:function(){return c+this.rowCount-1}}),Object.defineProperty(this,"col2",{get:function(){return d+this.colCount-1}})}var c=b.prototype;return c.clearStyleProperty=function(a){var b=this;h(b.sheet,b.row,b.col,b.rowCount,b.colCount,a,b.sheetArea)},c.dataValidator=c.validator,b}(e.CellRange),a.Cell=b,c=function(a){f(b,a);function b(b,c,d){a.call(this,b,c,-1,1,-1,d),Object.defineProperty(this,"index",{get:function(){return this.row}}),Object.defineProperty(this,"index2",{get:function(){return this.row+this.rowCount-1}})}var c=b.prototype;return c.clearStyleProperty=function(a){var b=this;h(b.sheet,b.row,-1,b.rowCount,1,a,b.sheetArea)},c.dataValidator=c.validator,b}(e.CellRange),a.Row=c,d=function(a){f(b,a);function b(b,c,d){a.call(this,b,-1,c,-1,1,d),Object.defineProperty(this,"index",{get:function(){return this.col}}),Object.defineProperty(this,"index2",{get:function(){return this.col+this.colCount-1}})}var c=b.prototype;return c.clearStyleProperty=function(a){var b=this;h(b.sheet,-1,b.col,-1,b.colCount,a,b.sheetArea)},c.dataValidator=c.validator,b}(e.CellRange),a.Column=d,g.prototype.getCell=function(a,c,d){return new b(this,a,c,d)},g.prototype.getCells=function(a,b,c,d,e){var f=c>a?a:c,g=d>b?b:d,h=this.getCell(f,g,e);return h.rowCount=Math.abs(c-a)+1,h.colCount=Math.abs(d-b)+1,h},g.prototype.getRow=function(a,b){return new c(this,a,b)},g.prototype.getRows=function(a,b,c){var d=b>a?a:b,e=this.getRow(d,c);return e.rowCount=Math.abs(b-a)+1,e},g.prototype.getColumn=function(a,b){return new d(this,a,b)},g.prototype.getColumns=function(a,b,c){var d=b>a?a:b,e=this.getColumn(d,c);return e.colCount=Math.abs(b-a)+1,e}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){a.CellBindingSource=window.GC.Spread.Sheets.Bindings.CellBindingSource,a.Sheet.prototype.isColumnBound=function(a){return!!this.getDataColumnName(a)}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d,e,f,g=window.GC.Spread.Sheets;a.ReferenceStyle=a.F5(g.ReferenceStyle,{r1c1:"R1C1"}),a.NameInfo=g.NameInfo,b=window.GC.Spread.CalcEngine,c=a.Calc||(a.Calc={}),c.evaluateFormula=g.CalcEngine.evaluateFormula,c.rangeToFormula=g.CalcEngine.rangeToFormula,c.rangesToFormula=g.CalcEngine.rangesToFormula,c.RangeReferenceRelative=g.CalcEngine.RangeReferenceRelative,c.SheetParserContext=g.CalcEngine.SheetParserContext,c.ParserContext=b.ParserContext,c.Parser=b.Parser,c.EvaluateContext=b.EvaluateContext,c.AsyncEvaluateContext=b.AsyncEvaluateContext,c.Evaluator=b.Evaluator,c.CalcSource=b.CalcSource,c.CalcService=b.CalcService,c.CalcSourceModel=b.CalcSourceModel,c.CalcError=b.CalcError,c.Errors=b.Errors,c.CalcArray=b.CalcArray,c.CalcValueType={anyType:0,numberType:1,stringType:2,booleanType:3,dateType:4},c.Reference=b.CalcReference,c.SheetReference=b.CalcReference;function h(a){this.name=a}h.prototype.getName=function(){return this.name},h.prototype.compareTo=function(a){return this.name.toLowerCase()===a.name.toLowerCase()},c.Operator=h;function i(a){h.call(this,a)}i.prototype.evaluate=function(a,b){},c.UnaryOperator=i;function j(a,b){h.call(this,a),this.acceptsReference=b}j.prototype.evaluate=function(a,b,c){},c.BinaryOperator=j,d=c.Functions||(c.Functions={}),d.ArrayArgumentEvaluateMode=b.Functions.ArrayArgumentEvaluateMode,d.Function=b.Functions.Function,d.AsyncFunction=b.Functions.AsyncFunction,d.defineGlobalCustomFunction=b.Functions.defineGlobalCustomFunction,d.findGlobalFunction=b.Functions.findGlobalFunction,e=c.Expressions||(c.Expressions={}),f=e.Expression=b.Expression,e.ParenthesesExpression=function(a){f.call(this,b.ExpressionType.parentheses),this.value=a},e.FunctionExpression=function(a,c){f.call(this,b.ExpressionType.function),this.function=a,this.arguments=c,this.functionName=a.name},e.FunctionExpression.prototype.argCount=function(){return this.arguments?this.arguments.length:0},e.FunctionExpression.prototype.getArg=function(a){return this.arguments?this.arguments[a]:null},e.FunctionExpression.prototype.getFunctionName=function(){return this.functionName},e.NameExpression=function(a){f.call(this,b.ExpressionType.name),this.name=a},e.BangNameExpression=function(a){f.call(this,b.ExpressionType.name),this.name=a},e.ExternalNameExpression=function(a,c){f.call(this,b.ExpressionType.name),this.source=a,this.name=c},e.ConstantExpression=function(a){f.call(this,b.ExpressionType.unknow),this.value=a},e.BooleanExpression=function(a){f.call(this,b.ExpressionType.boolean),this.value=a},e.DoubleExpression=function(a,c){f.call(this,b.ExpressionType.number),this.value=a},e.StringExpression=function(a){f.call(this,b.ExpressionType.string),this.value=a},e.ErrorExpression=function(a){f.call(this,b.ExpressionType.error),this.value=a},e.ExternalErrorExpression=function(a,c){f.call(this,b.ExpressionType.error),this.value=c},e.SheetRangeErrorExpression=function(a,c,d){f.call(this,b.ExpressionType.error),this.value=d},e.BangErrorExpression=function(a){f.call(this,b.ExpressionType.error),this.value=a},e.ArrayExpression=function(a){f.call(this,b.ExpressionType.array),this.value=a},e.MissingArgumentExpression=function(){f.call(this,b.ExpressionType.missingArgument)},e.OperatorExpression=function(a){f.call(this,b.ExpressionType.operator),this.operatorType=a},e.UnaryOperatorExpression=function(a,c){f.call(this,b.ExpressionType.operator),this.operatorType=a,this.value=c},e.BinaryOperatorExpression=function(a,c,d){f.call(this,b.ExpressionType.operator),this.operatorType=a,this.value=c,this.value2=d},e.ReferenceExpression=function(){f.call(this,b.ExpressionType.reference)},e.ExternalReferenceExpression=function(a){f.call(this,b.ExpressionType.reference),this.source=a},e.CellExpression=function(a,c,d,e){f.call(this,b.ExpressionType.reference),this.row=a,this.column=c,this.rowRelative=d,this.columnRelative=e},e.BangCellExpression=function(a,c,d,e){f.call(this,b.ExpressionType.reference),this.row=a,this.column=c,this.rowRelative=d,this.columnRelative=e},e.ExternalCellExpression=function(a,c,d,e,g){f.call(this,b.ExpressionType.reference),this.source=a,this.row=c,this.column=d,this.rowRelative=e,this.columnRelative=g},e.RangeExpression=function(a,c,d,e,g,h,i,j){f.call(this,b.ExpressionType.reference),this.row=a,this.column=c,this.endRow=d,this.endColumn=e,this.rowRelative=g,this.columnRelative=h,this.endRowRelative=i,this.endColumnRelative=j},e.BangRangeExpression=function(a,c,d,e,g,h,i,j){f.call(this,b.ExpressionType.reference),this.row=a,this.column=c,this.endRow=d,this.endColumn=e,this.rowRelative=g,this.columnRelative=h,this.endRowRelative=i,this.endColumnRelative=j},e.ExternalRangeExpression=function(a,c,d,e,g,h,i,j,k){f.call(this,b.ExpressionType.reference),this.source=a,this.row=c,this.column=d,this.endRow=e,this.endColumn=g,this.rowRelative=h,this.columnRelative=i,this.endRowRelative=j,this.endColumnRelative=k},e.SheetRangeExpression=function(a,c,d,e,g,h,i,j,k,l){f.call(this,b.ExpressionType.reference),this.source=a,this.endSource=c,this.row=d,this.column=e,this.endRow=g,this.endColumn=h,this.rowRelative=i,this.columnRelative=j,this.endRowRelative=k,this.endColumnRelative=l},e.StructReferenceExpression=function(a,c){f.call(this,b.ExpressionType.structReference)},e.NameIdentityExpression=function(a){f.call(this,b.ExpressionType.unknow)},e.ExternalNameIdentityExpression=function(a,c){f.call(this,b.ExpressionType.unknow)}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets.CellTypes;a.BaseCellType=b.Base,a.TextCellType=b.Text,a.RowHeaderCellType=b.RowHeader,a.ColumnHeaderCellType=b.ColumnHeader,a.CornerCellType=b.Corner,a.CheckBoxTextAlign=b.CheckBoxTextAlign,a.CheckBoxCellType=b.CheckBox,a.ButtonCellType=b.Button,a.HyperLinkTargetType=a.F5(b.HyperLinkTargetType),a.HyperLinkCellType=b.HyperLink,a.EditorValueType=a.F5(b.EditorValueType),a.ComboBoxCellType=b.ComboBox,a.CustomCellType=a.BaseCellType}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c=window.GC.Spread.Sheets.Comments;a.Comment=c.Comment,a.CommentState=a.F5(c.CommentState),a.DisplayMode=a.F5(c.DisplayMode),a.Padding=c.Padding,b=a.Sheet,b.prototype.setComment=function(a,b,c){var d,e,f,g;if(void 0!==a&&void 0!==b)if(c){d=this.comments.add(a,b,c),e=["clone","toJSON","fromJSON","text","zIndex"];for(f in c)e.indexOf(f)===-1&&(g=c[f],"function"==typeof g&&d[f](c[f]()))}else this.comments.remove(a,b)},b.prototype.getComment=function(a,b){return this.comments.get(a,b)},b.prototype.getComments=function(){return this.comments.all()}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d,e,f,g,h,i=window.GC.Spread.Sheets.ConditionalFormatting,j=i.IconSetRule,k=a.I5,l=a.defineProperty,m=a.getPropertyValue,n=a._5,o=null,p=void 0;function q(a){l(a,"innerRule",["ranges","style","condition","type","operator","value1","value2","text","formula","rank"])}function r(b){var c={};switch(b.ruleType()){case 1:c=new a.CellValueRule;break;case 2:c=new a.SpecificTextRule;break;case 3:c=new a.FormulaRule;break;case 4:c=new a.DateOccurringRule;break;case 5:c=new a.Top10Rule;break;case 6:c=new a.UniqueRule;break;case 7:c=new a.DuplicateRule;break;case 8:c=new a.AverageRule;break;case 10:c=new a.TwoScaleRule;break;case 11:c=new a.ThreeScaleRule;break;case 12:c=new a.DataBarRule;break;case 13:c=new a.IconSetRule;break;default:c=new a.ConditionRuleBase}return c.innerRule=b,c}function s(a,b){var c=b;if(c&&!c.innerRule)return a.Y5.every(function(a){return a.innerRule!==c||(c=a,!1)}),c}function t(a){a.hasNoReference=function(){var a=this.innerRule.ranges();return!(a&&a.length>0)},["evaluate","initCondition","reset","contains","fromJSON","toJSON","priority","getExpected","intersects","stopIfTrue","getBaseCoordinate"].forEach(function(b){n(a,"innerRule",b)}),n(a,"innerRule","createCondition",o,function(a){return v(a)})}function u(b){a.defineProperty(b,"innerCondition",["compareType","item1","item2","ignoreBlank","expected","formula","treatNullValueAsZero","integerValue","forceValue2Text","useWildCards","ignoreCase","customValueType","expectTypeId","type","ranges","isPercent","regex"])}function v(b){var c={};switch(b.conType()){case 0:c=new a.RelationCondition;break;case 1:c=new a.NumberCondition;break;case 2:c=new a.TextCondition;break;case 3:c=new a.ColorCondition;break;case 4:c=new a.FormulaCondition;break;case 5:c=new a.DateCondition;break;case 6:c=new a.DateExCondition;break;case 7:c=new a.TextLengthCondition;break;case 8:c=new a.Top10Condition;break;case 10:c=new a.AverageCondition;break;case 11:c=new a.CellValueCondition;break;case 12:c=new a.AreaCondition}return c.innerCondition=b,c}function w(a){["evaluate","reset","getExpected","getValidList","fromJSON","toJSON"].forEach(function(b){n(a,"innerCondition",b)})}a.GeneralCompareType=a.F5(i.GeneralComparisonOperators),a.RelationCompareType=a.F5(i.LogicalOperators),a.ComparisonOperator=a.F5(i.ComparisonOperators),a.TextComparisonOperator=a.F5(i.TextComparisonOperators),a.TextCompareType=a.F5(i.TextCompareType),a.ColorCompareType=a.F5(i.ColorCompareType),a.CustomValueType=a.F5(i.CustomValueType),a.DateCompareType=a.F5(i.DateCompareType),a.Top10ConditionType=a.F5(i.Top10ConditionType),a.DateOccurringType=a.F5(i.DateOccurringType),a.QuarterType=a.F5(i.QuarterType),a.AverageConditionType=a.F5(i.AverageConditionType),a.ScaleValueType=a.F5(i.ScaleValueType),a.BarDirection=a.F5(i.BarDirection),a.DataBarAxisPosition=a.F5(i.DataBarAxisPosition),a.IconSetType=a.F5(i.IconSetType),a.IconValueType=a.F5(i.IconValueType),a.ConditionType=a.F5(i.ConditionType),a.RuleType=a.F5(i.RuleType),b=a.Condition=i.Condition,a.RelationCondition=function(a){k(c,a);function c(a,c,d){c&&c.innerCondition&&(c=c.innerCondition),d&&d.innerCondition&&(d=d.innerCondition),this.innerCondition=new b(0,{compareType:a,item1:c,item2:d,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.create=function(a,b,d){return new c(a,b,d)},w(c.prototype),c}(b),a.NumberCondition=function(a){k(c,a);function c(a,c,d){this.innerCondition=new b(1,{compareType:a,expected:c,formula:d,ignoreBlank:!1,integerValue:!1}),this.innerCondition.outer=this,u(this)}return w(c.prototype),c}(b),a.TextCondition=function(a){k(c,a);function c(a,c,d){this.innerCondition=new b(2,{compareType:a,expected:c,formula:d,forceValue2Text:!1,useWildCards:!0,ignoreCase:!1,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.getExpectedString=function(a,b,c){var d=this.getExpected(a,b,c);return d===p||d===o?o:""+d},w(c.prototype),c}(b),a.ColorCondition=function(a){k(c,a);function c(a,c){this.innerCondition=new b(3,{compareType:a,expected:c,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return w(c.prototype),c}(b),a.FormulaCondition=function(a){k(c,a);function c(a,c){this.innerCondition=new b(4,{customValueType:a,formula:c,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return w(c.prototype),c}(b),a.DateCondition=function(a){k(c,a);function c(a,c,d){this.innerCondition=new b(5,{compareType:a,expected:c,formula:d,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.getExpectedDateTime=function(a,b,c){var d=this.getExpected(a,b,c);return d instanceof Date?d:"string"==typeof d?new Date(d):o},w(c.prototype),c}(b),a.DateExCondition=function(a){k(c,a);function c(a){this.innerCondition=new b(6,{expected:a,formula:o,expectTypeId:0,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return w(c.prototype),c.prototype.getExpectedInt=function(a,b,c){var d=this.getExpected(a,b,c);return d=parseInt(d,10),isNaN(d)?o:d},c.fromDay=b.fromDay,c.fromMonth=b.fromMonth,c.fromQuarter=b.fromQuarter,c.fromWeek=b.fromWeek,c.fromYear=b.fromYear,c}(b),a.TextLengthCondition=function(a){k(c,a);function c(a,c,d){this.innerCondition=new b(7,{compareType:a,expected:c,formula:d,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.getExpectedInt=function(a,b,c){var d=this.getExpected(a,b,c);return d=parseInt(d,10),isNaN(d)?o:d},w(c.prototype),c}(b),a.Top10Condition=function(a){k(c,a);function c(a,c,d){this.innerCondition=new b(8,{type:a,expected:c,ranges:d,isPercent:!1,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.getExpectedInt=function(a,b,c){var d=this.getExpected(a,b,c);return d=parseInt(d,10),isNaN(d)||!isFinite(d)?o:d},w(c.prototype),c}(b),a.UniqueCondition=function(a){k(c,a);function c(a,c){this.innerCondition=new b(9,{expected:a,ranges:c,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.getExpectedBoolean=function(a,b,c){var d=this.getExpected(a,b,c);return!!d},w(c.prototype),c}(b),a.AverageCondition=function(a){k(c,a);function c(a,c){this.innerCondition=new b(10,{type:a,ranges:c,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.getExpectedDouble=function(a,b,c){var d=this.getExpected(a,b,c);return d=parseFloat(d),isNaN(d)?NaN:d},w(c.prototype),c}(b),a.CellValueCondition=function(a){k(c,a);function c(a,c,d){this.innerCondition=new b(11,{compareType:a,expected:c,formula:d,treatNullValueAsZero:!1}),this.innerCondition.outer=this,u(this)}return c.prototype.isSatisfyingCondition=function(a){return this.evaluate(o,0,0,a)},w(c.prototype),c}(b),a.AreaCondition=function(a){k(c,a);function c(a,c){this.innerCondition=new b(12,{expected:a,formula:c,ignoreBlank:!1}),this.innerCondition.outer=this,u(this)}return w(c.prototype),c.fromSource=b.fromSource,c.fromFormula=b.fromFormula,c}(b),c=a.ConditionRuleBase=function(a){k(b,a);function b(a){this.innerRule=new i.ConditionRuleBase(o,a),q(this)}return b.prototype.isScaleRule=function(){return!1},t(b.prototype),b}(i.ConditionRuleBase),d=a.NormalConditionRule=i.NormalConditionRule,a.AverageRule=function(a){k(b,a);function b(a,b){this.innerRule=new d(8,o,b,o,o,o,o,o,a),q(this)}return t(b.prototype),b}(d),a.CellValueRule=function(a){k(b,a);function b(a,b,c,e){this.innerRule=new d(1,o,e,a,b,c),q(this)}return b.prototype.isFormula=function(a){return a!==p&&a!==o&&"="===a[0]},t(b.prototype),b}(d),a.DateOccurringRule=function(a){k(b,a);function b(a,b){this.innerRule=new d(4,o,b,o,o,o,o,o,a),q(this)}return t(b.prototype),b}(d),a.DuplicateRule=function(a){k(b,a);function b(a){this.innerRule=new d(7,o,a),q(this)}return t(b.prototype),b}(d),a.FormulaRule=function(a){k(b,a);function b(a,b){this.innerRule=new d(3,o,b,o,o,o,o,a),q(this)}return t(b.prototype),b}(d),a.SpecificTextRule=function(a){k(b,a);function b(a,b,c){this.innerRule=new d(2,o,c,a,o,o,b),q(this)}return t(b.prototype),b}(d),a.Top10Rule=function(a){k(b,a);function b(a,b,c){this.innerRule=new d(5,o,c,o,o,o,o,o,a,b),q(this)}return t(b.prototype),b}(d),a.UniqueRule=function(a){k(b,a);function b(a){this.innerRule=new d(6,o,a),q(this)}return t(b.prototype),b}(d),e=a.ScaleRule=i.ScaleRule,a.TwoScaleRule=function(a){k(b,a);function b(a,b,c,d,f,g){0===arguments.length?this.innerRule=new e(10):this.innerRule=new e(10,a,b,c,o,o,o,d,f,g),q(this)}var c=["minType","minValue","minColor","maxType","maxValue","maxColor"];return["getMinimumType","setMinimumType","getMinimumValue","setMinimumValue","getMinimumColor","setMinimumColor","getMaximumType","setMaximumType","getMaximumValue","setMaximumValue","getMaximumColor","setMaximumColor"].forEach(function(a,d){n(b.prototype,"innerRule",a,c[d/2])}),t(b.prototype),b.prototype.isScaleRule=function(){return!0},b}(e),a.ThreeScaleRule=function(a){k(b,a);function b(a,b,c,d,f,g,h,i,j){0===arguments.length?this.innerRule=new e(11):this.innerRule=new e(11,a,b,c,d,f,g,h,i,j),q(this)}var c=["minType","minValue","minColor","midType","midValue","midColor","maxType","maxValue","maxColor"];return["getMinimumType","setMinimumType","getMinimumValue","setMinimumValue","getMinimumColor","setMinimumColor","getMidpointType","setMidpointType","getMidpointValue","setMidpointValue","getMidpointColor","setMidpointColor","getMaximumType","setMaximumType","getMaximumValue","setMaximumValue","getMaximumColor","setMaximumColor"].forEach(function(a,d){n(b.prototype,"innerRule",a,c[d/2])}),t(b.prototype),b}(e),a.DataBarRule=function(a){k(b,a);function b(a,b,c,d,e,f){0===arguments.length?this.innerRule=new i.DataBarRule:this.innerRule=new i.DataBarRule(a,b,c,d,e,f),q(this)}return[["minimumType","minType"],["minimumValue","minValue"],["maximumType","maxType"],["maximumValue","maxValue"]].forEach(function(a){n(b.prototype,"innerRule",a[0],a[1])}),["gradient","color","showBorder","borderColor","dataBarDirection","showBarOnly","negativeFillColor","useNegativeFillColor","negativeBorderColor","useNegativeBorderColor","axisPosition","axisColor"].forEach(function(a){n(b.prototype,"innerRule",a)}),t(b.prototype),b}(i.DataBarRule),f=a.IconSetRule=function(a){k(b,a);function b(a){0===arguments.length?this.innerRule=new j:this.innerRule=new j(a),q(this)}return b.prototype.iconCriteria=function(){return this.innerRule.iconCriteria()},["iconSetType","reverseIconOrder","showIconOnly"].forEach(function(a){n(b.prototype,"innerRule",a)}),t(b.prototype),b}(j),g=j.getIcon,j.getIcon=function(){return f.getIcon!==g?f.getIcon.apply(o,arguments):g.apply(o,arguments)},a.IconCriterion=function(a,b,c){this.isGreaterThanOrEqualTo=a,this.iconValueType=b,this.iconValue=c},h=a.ConditionalFormats=function(a){k(b,a);function b(a){this.sheet=a,this.Y5=[],this.cfs=a.conditionalFormats}return b.prototype.addRule=function(a){return a&&a.innerRule?(this.Y5.push(a),this.cfs.addRule(a.innerRule),a):(this.cfs.addRule(a),r(a))},b.prototype.removeRule=function(a){var b,c;if(a&&a.innerRule){for(b=this.Y5,c=b.length-1;c>=0;c--)b[c]&&b[c].innerRule===a&&b.splice(c,1);this.cfs.removeRule(a.innerRule)}else this.cfs.removeRule(a)},b.prototype.getRule=function(a){var b=this.cfs.getRule(a);return s(this,b)},b.prototype.getRules=function(a,b){var c,d=this,e=[];return c=0===arguments.length?d.cfs.getRules():d.cfs.getRules(a,b),c.forEach(function(a){e.push(s(d,a))}),e},b.prototype.containsRule=function(a,b,c){return a&&a.innerRule&&(a=a.innerRule),this.cfs.containsRule(a,b,c)},b.prototype.clearRule=function(){this.cfs.clearRule(),this.Y5.length=0},b.prototype.count=function(){return this.cfs.count()},b.prototype.removeRuleByRange=function(a,b,c,d){var e,f,g,h,i,j;for(this.cfs.removeRuleByRange(a,b,c,d),e=this.Y5,f=e.length,g=0,h=[];g<f;)i=this.getRule(g),i&&h.push(i),g++;for(j=h.length-1,g=f-1;g>=0;g--)e[g]===h[j]?j--:e.splice(g,1)},b}(i.ConditionalFormats),a.Sheet.prototype.getConditionalFormats=function(){return this.U5||(this.U5=new h(this)),this.U5}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(b){var c,d,e=window.GC.Spread.Sheets.Fill;b.AutoFillType=b.F5(e.AutoFillType),b.FillDirection=b.F5(e.FillDirection),b.FillSeries=b.F5(e.FillSeries),b.FillType=b.F5(e.FillType),b.FillDateUnit=b.F5(e.FillDateUnit),c=b.Sheet,d=c.prototype.fillAuto,c.prototype.fillAuto=function(c,e,f){f===a.Sheets.FillSeries.Column||f===a.Sheets.FillSeries.Row?d.apply(this,[c,e,{fillType:b.FillType.Auto,series:f}]):d.apply(this,arguments)},c.prototype.fillAutobyDirection=function(a,c,d){this.fillAuto(a,c,{fillType:b.FillType.Direction,direction:d})},c.prototype.fillLinear=function(a,c,d,e,f){this.fillAuto(a,c,{fillType:b.FillType.Linear,series:d,step:e,stop:f})},c.prototype.fillGrowth=function(a,c,d,e,f){this.fillAuto(a,c,{fillType:b.FillType.Growth,series:d,step:e,stop:f})},c.prototype.fillDate=function(a,c,d,e,f,g){this.fillAuto(a,c,{fillType:b.FillType.Date,series:d,step:f,stop:g,unit:e})}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d,e,f=window.GC.Spread.Sheets.ConditionalFormatting,g=window.GC.Spread.Sheets.Filter;a.FilterActionType=a.F5(g.FilterActionType),b=g.RowFilterBase,c=b.prototype.addFilterItem,b.prototype.addFilterItem=function(a,b){b&&b.innerCondition&&(b=b.innerCondition),c.call(this,a,b)},b.prototype.isHideRowFilter=function(){return!1},b.prototype.getShowFilterButton=function(){return this.filterButtonVisible()},b.prototype.setShowFilterButton=function(a){this.filterButtonVisible(a)},b.prototype.addAverageFilter=function(a,b){var c=new f.Condition(f.ConditionType.averageCondition,{type:b});this.addFilterItem(a,c)},b.prototype.addBackgroundFilter=function(a,b){var c=new f.Condition(f.ConditionType.colorCondition,{compareType:f.ColorCompareType.backgroundColor,expected:b});this.addFilterItem(a,c)},b.prototype.addDateFilter=function(a,b,c){var d=new f.Condition(f.ConditionType.dateCondition,{compareType:b,expected:c});this.addFilterItem(a,d)},b.prototype.addForegroundFilter=function(a,b){var c=new f.Condition(f.ConditionType.colorCondition,{compareType:f.ColorCompareType.foregroundColor,expected:b});this.addFilterItem(a,c)},b.prototype.addNumberFilter=function(a,b,c){var d=new f.Condition(f.ConditionType.numberCondition,{compareType:b,expected:c});this.addFilterItem(a,d)},b.prototype.addTextFilter=function(a,b,c){var d=new f.Condition(f.ConditionType.textCondition,{compareType:b,expected:c});this.addFilterItem(a,d)},b.prototype.addTop10Filter=function(a,b,c){var d=new f.Condition(f.ConditionType.top10Condition,{type:b,expected:c});this.addFilterItem(a,d)},b.prototype.isColumnFiltered=function(a){return this.isFiltered(a)},d=b.prototype.getFilterItems,b.prototype.getFilterItems=function(a){var b=d.call(this,a),c=[];return b.forEach(function(a){a&&a.outer?c.push(a.outer):c.push(a)}),c},a.RowFilterBase=b,e=g.HideRowFilter,e.prototype.isHideRowFilter=function(){return!0},a.HideRowFilter=e}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c=window.GC.Spread.Sheets.FloatingObjects,d=c.FloatingObject;d.prototype.position=function(a){return 0===arguments.length?{x:this.x(),y:this.y()}:(this.x(a.x),void this.y(a.y))},a.FloatingObject=d,a.Picture=c.Picture,a.CustomFloatingObject=d,a.CustomFloatingObject.prototype.Content=d.prototype.content,b=a.Sheet,b.prototype.addFloatingObject=function(a){return this.floatingObjects.add(a)},b.prototype.findFloatingObject=function(a){return this.floatingObjects.get(a)},b.prototype.removeFloatingObject=function(a){return this.floatingObjects.remove(a)},b.prototype.getFloatingObjects=function(){return this.floatingObjects.all()},b.prototype.setFloatingObjectZIndex=function(a,b){var c=this.pictures.get(a);return c?this.pictures.zIndex(a,b):this.floatingObjects.zIndex(a,b)},b.prototype.getFloatingObjectZIndex=function(a){var b=this.pictures.get(a);return b?this.pictures.zIndex(a):this.floatingObjects.zIndex(a)},b.prototype.addPicture=function(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q;function r(a,b,c){var d,e,f,g=a.getCellRect(b,c);if(void 0!==g.x&&void 0!==g.y)return{x:g.x,y:g.y};for(e=0,f={},d=0;d<c;d++)e+=a.getColumnWidth(d);for(f.x=e,e=0,d=0;d<b;d++)e+=a.getRowHeight(d);return f.y=e,f}return k=0,l=0,m=100,n=100,o=r(this,c,d),o&&(k=o.x+(h||0),l=o.y+(g||0)),p=r(this,e,f),p&&(m=p.x+(j||0)-k,n=p.y+(i||0)-l),q=r(this,0,0),q&&(k-=q.x,l-=q.y),this.pictures.add(a,b,k,l,m,n)},b.prototype.findPicture=function(a){return this.pictures.get(a)},b.prototype.removePicture=function(a){return this.pictures.remove(a)},b.prototype.getPictures=function(){return this.pictures.all()}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets.FormulaTextBox.FormulaTextBox;b.prototype.spread=function(a){return 0===arguments.length?this.workbook():void this.workbook(a)},a.FormulaTextBox=b}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d=window.GC.Spread.Sheets.Outlines;a.RangeGroupDirection=a.F5(d.OutlineDirection),a.GroupState=a.F5(d.OutlineState),a.RangeGroupDirection=a.F5(d.OutlineDirection),b=d.OutlineInfo,b.prototype.getState=function(){return this.state()},b.prototype.setState=function(a){this.state(a)},a.RangeGroupInfo=b,function(a){function b(a,b){if(a&&b){for(var c in a)if(a.hasOwnProperty(c)&&a[c]!==b[c])return!1;return!0}return!a&&!b}a.equals=function(a){var c,d,e,f,g,h,i=this;if(a)try{if(c=(i.items||[]).length,d=(a.items||[]).length,c!==d)return!1;if(i.direction()!==a.direction())return!1;for(e=[].concat(i.head,i.tail,i.items||[]),f=[].concat(a.head,a.tail,a.items||[]),g=0,h=e.length;g<h;g++)if(!b(e[g],f[g]))return!1}catch(a){return!1}return!1},a.getCollapsed=a.getCollapsed,a.getDirection=function(){return this.direction()},a.setDirection=function(a){this.direction(a)}}(d.Outline.prototype),a.RangeGroup=d.Outline,a.GroupedItemIndexEnumerator=function(){function a(a){this.isEOF=!1,this.rangeGroup=keyword_null,this.current=-1,this.rangeGroup=a}return a.prototype.nextToCurrent=function(){return this.current+1},a.prototype.moveNext=function(){var a,b,c=this;if(c.isEOF||!c.rangeGroup||!c.rangeGroup.items)return!1;for(a=!1,b=c.current+1;b<c.rangeGroup.items.length;b++)if(c.rangeGroup.items[b]){a=!0,c.current=b;break}return a||(c.current=-1),c.current>-1||(c.isEOF=!0,!1)},a.prototype.reset=function(){this.isEOF=!1,this.current=-1},a}(),c=a.Sheet,c.prototype.showRowRangeGroup=c.prototype.showRowOutline,c.prototype.showColumnRangeGroup=c.prototype.showColumnOutline}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets.Print;b&&(a.PrintVisibilityType=a.F5(b.PrintVisibilityType),a.PrintCentering=a.F5(b.PrintCentering),a.PrintPageOrientation=a.F5(b.PrintPageOrientation),a.PrintPageOrder=a.F5(b.PrintPageOrder),a.PaperKind=a.F5(b.PaperKind,{dlEnvelope:"DLEnvelope",usStandardFanfold:"USStandardFanfold"}),a.PaperSize=b.PaperSize,a.PrintInfo=b.PrintInfo)}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d=null,e=Math.max,f=Math.min,g=window.GC.Spread.Sheets.Search;a.SearchFlags=a.F5(g.SearchFlags),a.SearchOrder=a.F5(g.SearchOrder),a.SearchFoundFlags=a.F5(g.SearchFoundFlags),a.SearchResult=g.SearchResult,a.SearchCondition=g.SearchCondition,function(a){a[a.All=0]="All",a[a.HasValue=1]="HasValue",a[a.HasStyle=2]="HasStyle"}(a.EnumeratorOption||(a.EnumeratorOption={})),b=a.EnumeratorOption,c=a.SearchOrder,a.CellsEnumerator=function(){function g(a,c){if(this.actualEndRow=-1,this.isActualEndRowSet=!1,this.isBlockRange=!1,this.options=b.HasValue,!a)throw Error("sheet is null.");var d=this;d.worksheet=a,d.sheetArea=c.sheetArea,d.searchOrder=c.searchOrder,d.rowStart=c.rowStart,d.columnStart=c.columnStart,d.rowEnd=c.rowEnd,d.columnEnd=c.columnEnd,d.findBeginRow=c.findBeginRow,d.findBeginColumn=c.findBeginColumn,d.init(),d.block=d.worksheet}return g.prototype.init=function(){this.currentRow=-1,this.currentColumn=-1},g.prototype.moveNext=function(){var a=this;if(a.currentRow===-1&&a.currentColumn===-1&&a.rowStart<=a.rowEnd&&a.columnStart<=a.columnEnd&&(a.currentRow=a.findBeginRow,a.currentColumn=a.findBeginColumn,a.isIndexAcceptable(a.currentRow,a.currentColumn)&&!a.skipCurrent()))return!0;if(a.rowStart<=a.rowEnd&&a.columnStart<=a.columnEnd)for(;a.tryMoveNext();)if(!a.skipCurrent())return!0;return a.currentRow=-1,a.currentColumn=-1,!1},g.prototype.isIndexAcceptable=function(a,c){var e=this;return!(!((e.options&b.HasValue)>0&&e.block)||e.block.getValue(a,c,e.sheetArea)===d&&e.block.getFormula(a,c,e.sheetArea)===d&&e.block.getTag(a,c,e.sheetArea)===d)||e.options===b.All},g.prototype.skipCurrent=function(){return!1},g.prototype.tryMoveNext=function(){var a,c,d=this,e=d.currentRow,f=d.currentColumn,g=!1;return(d.options&b.HasValue)>0&&(a={value:e},c={value:f},d.nextValue(a,c)&&(e=a.value,f=c.value,g=!0)),d.options===b.HasValue&&(g?(d.currentRow=e,d.currentColumn=f):(d.currentRow=-1,d.currentColumn=-1)),!(d.currentRow===-1&&d.currentColumn===-1)},g.prototype.nextValue=function(a,b){for(;this.next(a,b);)if(this.isIndexAcceptable(a.value,b.value))return!0;return!1},g.prototype.nextValue=function(a,b){
for(;this.next(a,b);)if(this.isIndexAcceptable(a.value,b.value))return!0;return!1},g.prototype.getNextNonEmptyColumnInRow=function(a,b,c){for(var e=c;e<=this.columnEnd;e++)if(a.getValue(b,e)!==d)return e;return-1},g.prototype.isZOrderOver=function(a,b){var c=this;return c.isBlockRange?a>=c.rowStart&&a<=c.getActualEndRow()&&b>=c.columnStart&&b<=c.actualEndColumn():!(a>c.getActualEndRow())&&((a!==c.getActualEndRow()||!(b<0||b>c.actualEndColumn()))&&(!(a<c.rowStart)&&!(a===c.rowStart&&b<c.columnStart)))},g.prototype.getActualEndRow=function(){var a,c,d,g=this;return g.isActualEndRowSet?g.actualEndRow:(a=-1,c=!1,(g.options&b.HasValue)>0&&g.block&&(d=g.block.getRowCount(g.sheetArea)-1,a=e(a,d),c=!0),a=c?f(a,g.rowEnd):g.rowEnd,g.actualEndRow=a,g.isActualEndRowSet=!0,g.actualEndRow)},g.prototype.actualEndColumn=function(){return this.columnEnd},g.prototype.next=function(a,b){var d,e,f=this;return f.searchOrder===c.ZOrder?(d=f.getActualEndColumnZOrder(a.value),b.value+1<=d?(b.value+=1,f.isZOrderOver(a.value,b.value)):a.value+1<=f.getActualEndRow()&&(a.value+=1,f.isBlockRange?b.value=f.columnStart:b.value=0,f.isZOrderOver(a.value,b.value))):f.searchOrder===c.NOrder&&(e=f.getActualEndRowNOrder(b.value),a.value+1<=e?(a.value+=1,f.isNOrderOver(a.value,b.value)):b.value+1<=f.actualEndColumn()&&(b.value+=1,f.isBlockRange?a.value=f.rowStart:a.value=0,f.isNOrderOver(a.value,b.value)))},g.prototype.getActualEndColumnZOrder=function(a){var c,d,g=this;return a>=g.rowStart&&a<=g.rowEnd?(c=-1,d=!1,(g.options&b.HasValue)>0&&g.block&&(c=e(c,g.block.getColumnCount(g.sheetArea)-1),d=!0),c=a===g.rowEnd||g.isBlockRange?d?f(c,g.columnEnd):g.columnEnd:d?e(c,g.worksheet.getColumnCount(g.sheetArea)-1):g.worksheet.getColumnCount(g.sheetArea)-1):-1},g.prototype.getActualEndRowNOrder=function(a){var c,d,g=this;return a>=g.columnStart&&a<=g.columnEnd?(c=-1,d=!1,(g.options&b.HasValue)>0&&g.block&&(c=e(c,g.rowEnd),d=!0),c=a===g.columnEnd||g.isBlockRange?d?f(c,g.rowEnd):g.rowEnd:d?e(c,g.worksheet.getRowCount(g.sheetArea)-1):g.worksheet.getRowCount(g.sheetArea)-1):-1},g.prototype.isNOrderOver=function(a,b){var c=this;return c.isBlockRange?a>=c.rowStart&&a<=c.getActualEndRow()&&b>=c.columnStart&&b<=c.actualEndColumn():!(b>c.actualEndColumn())&&((b!==c.actualEndColumn()||!(a<0||a>c.getActualEndRow()))&&(!(b<c.columnStart)&&!(b===c.columnStart&&a<c.rowStart)))},g.prototype.current=function(){var b=this;return 0<=b.currentRow&&b.currentRow<b.worksheet.getRowCount(b.sheetArea)&&0<=b.currentColumn&&b.currentColumn<b.worksheet.getColumnCount(b.sheetArea)?new a.Cell(b.worksheet,b.currentRow,b.currentColumn,b.sheetArea):d},g}()}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d=window.GC.Spread.Sheets.Sparklines;a.Sparkline=d.Sparkline,a.SparklineGroup=d.SparklineGroup,a.EmptyValueStyle=a.F5(d.EmptyValueStyle),a.SparklineAxisMinMax=d.SparklineAxisMinMax,a.SparklineType=d.SparklineType,a.DataOrientation=a.F5(d.DataOrientation);function e(){var a,b;for(d.SparklineSetting.apply(this,arguments),a=["displayEmptyCellsAs","rightToLeft","displayHidden","displayXAxis","manualMax","manualMin","maxAxisType","minAxisType","groupMaxValue","groupMinValue","lineWeight"],b=0;b<a.length;b++)!function(a,b){Object.defineProperty(a,b,{get:function(){return a.options[b]},set:function(c){a.options[b]=c}})}(this,a[b])}for(e.prototype=new d.SparklineSetting,b=["axisColor","firstMarkerColor","highMarkerColor","lastMarkerColor","lowMarkerColor","markersColor","negativeColor","seriesColor","showFirst","showHigh","showLast","showLow","showNegative","showMarkers"],c=0;c<b.length;c++)!function(a){e.prototype[a]=function(b){return 0===arguments.length?this.options[a]:(this.options[a]=b,this)}}(b[c]);a.SparklineSetting=e}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets.Sparklines;a.SparklineEx=b.SparklineEx,a.LineSparkline=b.LineSparkline,a.ColumnSparkline=b.ColumnSparkline,a.WinlossSparkline=b.WinlossSparkline,a.PieSparkline=b.PieSparkline,a.AreaSparkline=b.AreaSparkline,a.ScatterSparkline=b.ScatterSparkline,a.BulletSparkline=b.BulletSparkline,a.SpreadSparkline=b.SpreadSparkline,a.StackedSparkline=b.StackedSparkline,a.HBarSparkline=b.HBarSparkline,a.VBarSparkline=b.VBarSparkline,a.BoxPlotSparkline=b.BoxPlotSparkline,a.VariSparkline=b.VariSparkline,a.CascadeSparkline=b.CascadeSparkline,a.ParetoSparkline=b.ParetoSparkline}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b,c,d,e=window.GC.Spread.Sheets.Tables,f=e.Table;f.prototype.refresh=function(){},a.SheetTable=f,a.TableColumnInfo=e.TableColumn,a.TableStyleInfo=e.TableStyle,a.TableStyle=e.TableTheme;function g(){}g.customStyles=function(){var a=g.K5;return a?a:keyword_null},g.addCustomStyles=function(a){var b,c,d,e;if(a){for(g.K5||(g.K5=[]),b=g.K5,c=b.length,e=0;e<c;e++)if(d=b[e],d.name()===a.name())throw Error("The style with the same name already exists in the styles.");b.push(a)}},g.removeCustomStyle=function(a){var b,c;return!!a&&(b=g.K5,!!(b&&(c=h(a,b),c>-1))&&(b.splice(c,1),!0))};function h(a,b,c){if(b){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(b,a,c);var d=b.length;for(c=c?c<0?Math_max(0,d+c):c:0;c<d;c++)if(c in b&&b[c]===a)return c}return-1}for(g.removeCustomStyleByName=function(a){var b,c,d,e=g.K5;if(e)for(b=e.length,d=0;d<b;d++)if(c=e[d],c.name()===a)return e.splice(d,1),!0;return!1},b=[],c=1;c<=21;c++)b.push("light"+c);for(c=1;c<=28;c++)b.push("medium"+c);for(c=1;c<=11;c++)b.push("dark"+c);for(c=0;c<b.length;c++)!function(a){g[a]=function(){return e.TableThemes[a]}}(b[c]);a.TableStyles=g,a.TableRemoveOptions=a.F5(e.TableRemoveOptions),d=a.Sheet,d.prototype.addTable=function(a,b,c,d,e,f){return this.tables.add(a,b,c,d,e,f)},d.prototype.addTableByDataSource=function(a,b,c,d,e){return this.tables.addFromDataSource(a,b,c,d,e)},d.prototype.findTable=function(a,b){return this.tables.find(a,b)},d.prototype.findTableByName=function(a){return this.tables.findByName(a)},d.prototype.removeTable=function(a,b){return this.tables.remove(a,b)},d.prototype.removeTableByName=function(a,b){var c=this.tables.findByName(a);if(c)return this.tables.remove(c,b)},d.prototype.moveTable=function(a,b,c){return this.tables.move(a,b,c)},d.prototype.moveTableByName=function(a,b,c){var d=this.tables.findByName(a);if(d)return this.tables.move(d,b,c)},d.prototype.resizeTable=function(a,b){var c=this.tables;return c.resize.apply(c,arguments)},d.prototype.resizeTableByName=function(a,b){return this.resizeTable.apply(this,arguments)},d.prototype.getTables=function(){return this.tables.all()}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(b){var c,d=window.GC.Spread.Sheets.Slicers,e=b.I5;b.ItemSlicer=function(b){e(c,b);function c(c){var d=[],e=[],f=new a.Slicer.GeneralSlicerData(e,d);b.call(this,c,f,"")}return c.prototype.setData=function(a,c){b.call(this,this.name(),a,c)},c}(d.ItemSlicer),b.SlicerStyleInfo=d.SlicerStyleInfo,b.SlicerBorder=d.SlicerBorder,b.SlicerStyle=d.SlicerStyle,b.SlicerStyles=d.SlicerStyles,b.TableSlicerData=d.TableSlicerData,b.Slicer=d.Slicer,c=b.Sheet,c.prototype.addSlicer=function(a,b,c,d){return this.slicers.add(a,b,c,d)},c.prototype.removeSlicer=function(a){return this.slicers.remove(a)},c.prototype.getSlicer=function(a){return this.slicers.get(a)},c.prototype.getSlicers=function(a,b){return this.slicers.all(a,b)}}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(a){var b=window.GC.Spread.Sheets.Touch;a.TouchToolStrip=b.TouchToolStrip,a.TouchToolStripItem=b.TouchToolStripItem,a.TouchToolStripSeparator=b.TouchToolStripSeparator}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={})),function(a){!function(b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p=window.GC.Spread.Sheets.DataValidation,q=b.getPropertyValue,r=b._5;b.CriteriaType=b.F5(p.CriteriaType),b.DataValidationResult=b.F5(p.DataValidationResult),b.ErrorStyle=b.F5(p.ErrorStyle);function s(a){b.defineProperty(a,"innerValidator",["condition","type","errorStyle","ignoreBlank","inCellDropdown","showInputMessage","showErrorMessage","inputTitle","errorTitle","inputMessage","errorMessage","comparisonOperator"])}function t(a){var b=new c;return a.outer=b,b.innerValidator=a,b}function u(b){if(!b)return null;if(b.validator&&b.validator.outer){var c=new a.Sheets.Style;return c.fromJSON(b.toJSON()),c.validator=b.validator.outer,c}return b}c=function(a){b.I5(c,a);function c(a){a&&a.innerCondition&&(a=a.innerCondition),this.innerValidator=new p.DefaultDataValidator(a),this.innerValidator.outer=this,s(this)}return r(c.prototype,"innerValidator","IgnoreBlank","ignoreBlank"),["value1","value2","isValid","reset","getValidList","highlightStyle","fromJSON","toJSON"].forEach(function(a){r(c.prototype,"innerValidator",a)}),c}(p.DefaultDataValidator),["createNumberValidator","createDateValidator","createTextLengthValidator","createFormulaValidator","createFormulaListValidator","createListValidator"].forEach(function(a){c[a]=function(){var b=p[a].apply(null,arguments);return t(b)}}),d=b.Sheet.prototype,e=d.setDataValidator,f=d.getDataValidator,g=d.setStyle,h=d.getStyle,i=d.setDefaultStyle,j=d.getDefualtStyle,k=d.addNamedStyle,l=d.getNamedStyle,m=b.Spread.prototype,n=m.addNamedStyle,o=m.getNamedStyle,d.setDataValidator=function(a,b,c,d){var f=c;f&&f.innerValidator&&(f=f.innerValidator),e.call(this,a,b,f,d)},d.getDataValidator=function(a,b,c){var d=f.call(this,a,b,c);return d?d.outer||d:null},d.setStyle=function(a,b,c,d){c&&c.validator&&c.validator.innerValidator&&(c.validator=c.validator.innerValidator),g.call(this,a,b,c,d)},d.getStyle=function(){var a=h.apply(this,arguments);return u(a)},d.setDefaultStyle=function(a,b){a&&a.validator&&a.validator.innerValidator&&(a.validator=a.validator.innerValidator),i.call(this,a,b)},d.getDefualtStyle=function(){var a=j.apply(this,arguments);return u(a)},d.addNamedStyle=function(a){a&&a.validator&&a.validator.innerValidator&&(a.validator=a.validator.innerValidator),k.call(this,a)},d.getNamdStyle=function(){var a=l.apply(this,arguments);return u(a)},m.addNamedStyle=function(a){a&&a.validator&&a.validator.innerValidator&&(a.validator=a.validator.innerValidator),n.call(this,a)},m.getNamedStyle=function(){var a=o.apply(this,arguments);return u(a)},b.DefaultDataValidator=c}(a.Sheets||(a.Sheets={}))}(GcSpread||(GcSpread={}));