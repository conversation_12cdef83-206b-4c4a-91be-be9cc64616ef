<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-18 16:19:11
-->
<template>
    <!-- 非累加目标值修改 -->
    <div style="display: inline-block;">
      <!-- <a-button @click="btClick">点击</a-button> -->
      <a-button :type="'link'" :size="size" @click="btClick">{{
        showAlias(data.showName, "编辑")
      }}</a-button>
      <Modal ref="modal" @fetchData="fetchData"/>
    </div>
  </template>
  <script>
  import { showAlias } from "@/utils/intl.js";
  import Modal from "./edit-modal2.vue";
  export default {
    components: { Modal },
    props: {
      data: Object,
      size: String,
      record: Object,
      pageName: String,
      comKey: String
    },
    data() {
      return {
        showAlias
      };
    },
    methods: {
      btClick() {
        this.$refs["modal"].show({
          dataId: this.record?.id,
          improvementMeasures: this.record?.improvementMeasures,
          causeAnalysis: this.record?.causeAnalysis,
          state:this.record?.state,
          numberDisk:this.record?.numberDisk
        });
      },
      // 请求数据表格
      fetchData() {
        this.$store.dispatch({
          type: `${this.pageName}/${this.comKey}/fetch`,
          payload: {
            pageIndex: this.$store.state[this.pageName][this.comKey].data
              .pagination.pageIndex
          }
        });
      }
    }
  };
  </script>
  