<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2022-07-29 17:39:56
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="600"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="mainForm"
      :model="mainForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="值" prop="val">
        <a-input v-model="mainForm.val" placeholder="请填写值" />
      </a-form-model-item>
      <template v-if="addType !== '0'">
        <a-form-model-item label="值1" prop="remarks">
          <div style="display: flex;align-items: center;margin-top: 5px;">
            <a-input v-model="mainForm.remarks" placeholder="请填写值1" />
            <a-tooltip placement="right">
              <template slot="title">
                <span>多值添加请以英文逗号(",")分隔</span>
              </template>
              <a-icon style="margin-left: 20px;" type="question-circle" />
            </a-tooltip>
          </div>
        </a-form-model-item>
        <a-form-model-item label="值类型" prop="type">
          <a-select
            v-model="mainForm.type"
            placeholder="请选择值类型"
            :disabled="isEdit"
          >
            <a-select-option
              :value="item.val"
              v-for="item in typeValueList"
              :key="`${item.val}-${item.remarks}`"
            >
              {{ item.val }} - {{ item.remarks }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </template>
      <template v-else>
        <a-form-model-item label="备注" prop="remarks">
          <a-input v-model="mainForm.remarks" placeholder="请填写备注" />
        </a-form-model-item>
      </template>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      mainForm: {
        val: "",
        remarks: "",
        type: "0"
      },
      rules: {
        val: [
          {
            required: true,
            message: "请填写值",
            trigger: "blur"
          }
        ],
        type: [
          {
            required: false,
            message: "请选择值类型",
            trigger: "change"
          }
        ],
        remarks: [
          {
            required: true,
            message: "请填写备注",
            trigger: "blur"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      typeValueList: [], // 码值类型列表
      addType: "0" // 0代表新增类型，1代表新增类型值
    };
  },
  methods: {
    show(addType, formValue) {
      this.getTypeValueList();
      this.visible = true;
      this.isEdit = false;
      this.addType = addType;
      if (addType === "1") {
        this.rules["type"][0].required = true;
        this.rules["remarks"][0].required = false;
        this.mainForm.type = "";
      }
      if (formValue) {
        console.log("formValue----->", formValue);
        const { id, val, type, remarks } = formValue;
        this.isEdit = true;
        this.mainForm = {
          id,
          val,
          type: type ? type.toString() : "",
          remarks
        };
      }
    },
    // 获取码值类型
    getTypeValueList() {
      request(`/api/smc2/codeValue/getSelectOption`, {
        method: "POST",
        body: { type: "0" }
      }).then(res => {
        this.typeValueList = res.result === "success" ? res.data || [] : [];
      });
    },
    close() {
      this.mainForm = {
        val: "",
        remarks: "",
        type: "0"
      };
      this.rules["type"][0].required = false;
      this.rules["remarks"][0].required = true;
      this.typeValueList = [];
      this.$refs.mainForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    // 提交
    submit() {
      this.$refs.mainForm.validate(async valid => {
        const form = { ...this.mainForm };
        if (valid) {
          request(
            this.isEdit
              ? "/api/smc2/codeValue/updateCodeValueDto"
              : "/api/smc2/codeValue/insertCodeValueDto",
            {
              method: "POST",
              body: form
            }
          ).then(res => {
            if (res && res.result === "success") {
              this.close();
              this.$emit("fetchData");
            } else {
              this.$message.error(res.message);
            }
          });
        }
      });
    }
  }
};
</script>
