/*
 * @Description: 图表通用工具函数
 */

/**
 * 检查图表是否有数据
 * @param {echarts.ECharts} chart - 图表实例
 * @returns {boolean} - 是否有数据
 */
export const checkChartHasData = (chart) => {
  if (!chart) return false;

  try {
    const option = chart.getOption();

    // 检查是否有系列数据
    if (!option || !option.series || !option.series.length) {
      return false;
    }

    // 检查所有系列是否都没有数据
    for (let i = 0; i < option.series.length; i++) {
      const series = option.series[i];
      // 检查该系列是否有数据
      if (series.data && series.data.length && series.data.some(item => item !== null && item !== undefined && item !== 0)) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('检查图表数据失败:', error);
    return false;
  }
};

/**
 * 设置图表鼠标事件监听
 * @param {Object} refs - 组件的$refs对象
 * @param {Object} tooltipControllers - tooltip控制器对象
 * @returns {void}
 */
export const setupChartMouseEvents = (refs, tooltipControllers) => {
  const setupChartEvents = (chartRef, tooltipController) => {
    if (!chartRef || !tooltipController) {
      return;
    }

    const chartDom = refs[chartRef];
    if (!chartDom) {
      return;
    }

    // 移除可能已存在的事件监听，防止重复绑定
    const mouseEnterHandler = () => {
      if (tooltipController) {
        tooltipController.pause();
      }
    };

    const mouseLeaveHandler = () => {
      if (tooltipController) {
        tooltipController.resume();
      }
    };

    // 保存事件处理函数引用，以便后续移除
    chartDom._mouseEnterHandler = mouseEnterHandler;
    chartDom._mouseLeaveHandler = mouseLeaveHandler;

    // 添加事件监听
    chartDom.addEventListener('mouseenter', mouseEnterHandler);
    chartDom.addEventListener('mouseleave', mouseLeaveHandler);
  };

  // 为三个图表设置事件监听
  setupChartEvents('deliveryChart', tooltipControllers.deliveryTooltip);
  setupChartEvents('qualityChart', tooltipControllers.qualityTooltip);
  setupChartEvents('timeChart', tooltipControllers.timeTooltip);
};

/**
 * 清理图表鼠标事件监听
 * @param {Object} refs - 组件的$refs对象
 * @returns {void}
 */
export const cleanupChartMouseEvents = (refs) => {
  const cleanupChartEvents = (chartRef) => {
    const chartDom = refs[chartRef];
    if (!chartDom) {
      return;
    }

    // 移除事件监听
    if (chartDom._mouseEnterHandler) {
      chartDom.removeEventListener('mouseenter', chartDom._mouseEnterHandler);
      chartDom._mouseEnterHandler = null;
    }

    if (chartDom._mouseLeaveHandler) {
      chartDom.removeEventListener('mouseleave', chartDom._mouseLeaveHandler);
      chartDom._mouseLeaveHandler = null;
    }
  };

  // 清理三个图表的事件监听
  cleanupChartEvents('deliveryChart');
  cleanupChartEvents('qualityChart');
  cleanupChartEvents('timeChart');
};

/**
 * 设置用户活动监听
 * @param {Function} handleUserActivity - 用户活动处理函数
 * @returns {Object} - 返回事件处理相关对象
 */
export const setupUserActivityListeners = (handleUserActivity) => {
  // 用户活动事件列表
  const userActivityEvents = [
    'mousedown', 'mousemove', 'keydown',
    'scroll', 'touchstart', 'click', 'keypress'
  ];

  // 为每个事件添加监听
  userActivityEvents.forEach(eventType => {
    document.addEventListener(eventType, handleUserActivity);
  });

  // 返回事件相关对象，以便后续移除
  return {
    handleUserActivity,
    userActivityEvents
  };
};

/**
 * 移除用户活动监听
 * @param {Object} activityListeners - setupUserActivityListeners返回的对象
 * @returns {void}
 */
export const removeUserActivityListeners = (activityListeners) => {
  if (activityListeners && activityListeners.handleUserActivity && activityListeners.userActivityEvents) {
    activityListeners.userActivityEvents.forEach(eventType => {
      document.removeEventListener(eventType, activityListeners.handleUserActivity);
    });
  }
};

/**
 * 重置特定图表的tooltip自动滚动
 * @param {Object} tooltipController - 特定图表的tooltip控制器
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Function} checkChartHasData - 检查图表是否有数据的函数
 * @param {boolean} enabled - 是否启用自动滚动
 * @returns {void}
 */
export const resetChartTooltip = (tooltipController, chart, checkChartHasData, enabled) => {
  if (!tooltipController) return;

  // 停止当前的自动滚动
  tooltipController.stop();

  // 延迟更新数据信息和启动自动滚动，确保图表已完全渲染
  setTimeout(() => {
    // 更新数据信息
    tooltipController.updateDataInfo();

    // 检查图表是否有数据
    const hasData = checkChartHasData(chart);

    // 如果启用了自动滚动且图表有数据，则重新启动
    if (enabled && hasData) {
      tooltipController.start();
    }
  }, 500); // 延迟500ms确保图表已渲染
};
