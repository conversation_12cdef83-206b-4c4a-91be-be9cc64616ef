<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-21 13:42:46
 * @LastEditors: y<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-06 11:09:47
 * @Description: 
 * @FilePath: \pangea-component\src\views\boot-console\org-manage\index.vue
-->
<template>
  <div class="boot-console org-manage-container">
    <div class="org-manage-header">
      <a-button type="primary" @click="ytSave">保存/同级添加/下级添加</a-button>
      <a-button type="primary" @click="ytDel">删除</a-button>
      <a-button type="primary" @click="ytEdit">编辑</a-button>
      <a-button type="primary" @click="postOrg" style="width: 150px;">
        机构推送
      </a-button>
    </div>
    <div class="org-manage-content">
      <div class="org-manage-content-left">
        <div class="treeBox dhr-tree">

        </div>
        <div class="treeBox factory-tree">
          <div class="_title" style="display: grid; grid-template-columns: 1fr auto; align-items: baseline;">
            <span>工厂建模组织层级</span>
            <a-button type="primary" title="取消选择" class="clear-select" @click="factoryClearSelect"
              :disabled="!factoryCheckedKeys.length"><a-icon type="stop" /></a-button>
          </div>
          <a-select placeholder="请输入" showSearch :value="factorySearchValue" style="width: 100%;margin-bottom: 5px;"
            :default-active-first-option="false" :show-arrow="false" :filter-option="false" :not-found-content="null"
            @search="handleFactorySearch" @change="handleFactorySelect">
            <a-select-option v-for="(node, index) in factorySearchList" :key="Math.random().toString(36).substring(2)"
              :value="node.key">
              {{ node.title }}
            </a-select-option>
          </a-select>
          <a-spin :spinning="factoryTreeDataLoading">
            <a-tree class="org-list-tree" height="34vh" checkable blockNode checkStrictly
              :expanded-keys="factoryExpandedKeys" v-model="factoryCheckedKeys" :tree-data="factoryTreeData"
              @expand="onFactoryExpand" @check="onFactoryTreeCheck">
              <span v-for="item in factoryTreeData" :key="item.id" :slot="item.slotTitle">{{ item.title }}
                <span style="font-size: 10px;color: red;">{{
                  item.value
                }}</span></span>
            </a-tree>
          </a-spin>
        </div>
      </div>
      <div class="org-manage-content-right">
        <div class="treeBox yt-tree">
          <div class="_title">云图组织层级</div>
          <a-select placeholder="请输入" showSearch :value="factorySearchValue" style="width: 100%;margin-bottom: 5px;"
            :default-active-first-option="false" :show-arrow="false" :filter-option="false" :not-found-content="null"
            @search="handleYtSearch" @change="handleYtSelect">
            <a-select-option v-for="(node, index) in ytSearchList" :key="index" :value="node.key">
              {{ node.title }}
            </a-select-option>
          </a-select>
          <a-spin :spinning="ytTreeDataLoading">
            <a-tree class="org-list-tree" height="70vh" blockNode show-line draggable :expanded-keys="ytExpandedKeys"
              :tree-data="ytTreeRenderData" @expand="onYtExpand" @select="onYtSelect" :selected-keys="ytSelectedKeys">
              <span v-for="item in flattenTree(ytTreeRenderData)" :key="item.id" :slot="item.slotTitle">{{
                item.realTitle }}
                <span style="font-size: 10px;color: red;">{{
                  item.endTime && moment(item.endTime).isBefore(moment()) ? "失效" : ""
                }}</span></span>
            </a-tree>
          </a-spin>
        </div>
      </div>
    </div>

    <a-modal title="编辑" :visible="ytEditVisible" @ok="ytEditOk" @cancel="ytEditCancel">
      <a-form-model :model="ytEditData" :rules="ytEditRules" ref="ytEditForm">
        <a-form-model-item label="组织名称" prop="name">
          <a-input v-model="ytEditData.name" />
        </a-form-model-item>
        <a-form-model-item label="组织层级" prop="levelName">
          <a-select v-model="ytEditData.levelName" :options="levelDictList" />
        </a-form-model-item>
        <a-form-model-item label="有效期" prop="endDate">
          <a-date-picker :default-value="moment(ytEditData.endDate, 'YYYY-MM-DD')" />
        </a-form-model-item>
        <a-form-model-item label="上级组织" prop="superiorCode">
          <a-checkbox v-model="changeYtLevelFlag">是否更变层级</a-checkbox>
          <a-tree-select v-model="ytEditData.superiorCode" :tree-data="ytTreeData" allow-clear
            :disabled="!changeYtLevelFlag" @change="handleYtSuperiorChange" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import request from "../../../utils/requestHttp";
import moment from "moment";
export default {
  name: 'OrgManage',
  data() {
    this.ytTreeData = []
    return {
      headerBtnList: [
      ],
      // 工厂树搜索
      factorySearchValue: undefined,
      factorySearchList: [],
      factoryTreeDataLoading: true,
      factoryExpandedKeys: [1],
      factoryTreeData: [],
      factoryCheckedKeys: [],
      // 
      // 云图树搜索
      ytSearchValue: undefined,
      ytSearchList: [],
      ytTreeDataLoading: true,
      ytExpandedKeys: [1],
      ytTreeRenderData: [],//点击加载的树数据
      ytSelectedKeys: [],
      // yt编辑
      ytEditVisible: false,
      ytEditData: {},
      levelDictList: [],// 组织层级字典列表
      ytEditRules: {
        name: [{ required: true, message: '请输入组织名称' }],
        levelName: [{ required: true, message: '请选择组织层级' }],
        endDate: [{ required: true, message: '请选择有效期' }],
      },
      changeYtLevelFlag: false,//是否更变层级
      changeLevelInstance: null,//更变层级实例
    }
  },
  methods: {
    moment,
    // 工厂树搜索处理
    handleFactorySearch(value) {
      if (!value) {
        this.factorySearchList = [];
        return;
      }

      // 递归搜索树节点
      const searchNodes = (nodes, keyword) => {
        let results = [];
        nodes.forEach(node => {
          // 检查节点标题是否包含关键字（不区分大小写）
          if (node.title.toLowerCase().includes(keyword.toLowerCase())) {
            results.push({
              key: node.key,
              value: node.key,
              title: node.title,
              ...node
            });
          }
          // 如果有子节点，继续搜索
          if (node.children && node.children.length) {
            results = results.concat(searchNodes(node.children, keyword));
          }
        });
        return results;
      };

      // 从工厂树数据中搜索
      this.factorySearchList = searchNodes(this.factoryTreeData, value);
    },
    // 工厂树选择节点处理
    handleFactorySelect(selectedKey) {
      // 找到节点的完整路径
      const findNodePath = (nodes, targetKey, path = []) => {
        for (let node of nodes) {
          if (node.key === targetKey) {
            return [...path, node.key];
          }
          if (node.children && node.children.length) {
            const foundPath = findNodePath(node.children, targetKey, [...path, node.key]);
            if (foundPath) return foundPath;
          }
        }
        return null;
      };

      // 获取节点路径
      const nodePath = findNodePath(this.factoryTreeData, selectedKey);

      if (nodePath) {
        // 展开到选中节点
        this.factoryExpandedKeys = [...new Set([...this.factoryExpandedKeys, ...nodePath])];

        // 选中节点
        const checkedKeys = [...this.factoryCheckedKeys];
        checkedKeys.push(selectedKey);
        this.factoryCheckedKeys = checkedKeys;

        // 触发选中事件
        this.onFactoryTreeCheck(this.factoryCheckedKeys);
      }

      // 清空搜索
      this.factorySearchValue = undefined;
      this.factorySearchList = [];
    },
    // 获取工厂组织层级
    getFactoryTreeData() {
      request(`/api/mom-data/momFactory/tree`, {
        method: "GET",
      }).then((res) => {
        this.factoryTreeData = res.map(item => {
          const processNode = (node) => {
            return {
              ...node,
              key: node.factoryModelCode,
              title: node.factoryModelName,
              children: node.children ? node.children.map(child => processNode(child)) : null
            }
          }
          return processNode(item)
        })
        this.factoryTreeDataLoading = false;
        // 默认展开第一层
        this.factoryExpandedKeys = this.factoryTreeData.map(item => item.key);
      });
    },
    onFactoryExpand(expandedKeys) {
      this.factoryExpandedKeys = expandedKeys;
    },
    onFactoryTreeCheck(checkedKeys) {
      if (checkedKeys.checked) {
        this.factoryCheckedKeys = checkedKeys.checked;
      } else {
        this.factoryCheckedKeys = checkedKeys;
      }
      if (this.factoryCheckedKeys.length) {
        const originDataArr = []; // dhr源数据
        const findCheckedNodes = (nodes) => {
          nodes.forEach(node => {
            if (this.factoryCheckedKeys.includes(node.key)) {
              originDataArr.push({ ...node });
            }
            if (node.children && node.children.length) {
              findCheckedNodes(node.children);
            }
          });
        };
        findCheckedNodes(this.factoryTreeData);
        // 打上是否顶层标签
        const markTopLevel = (node) => {
          if (this.factoryCheckedKeys.includes(node.superiorCode)) {
            node["notTopLevel"] = true;
          } else {
            node["notTopLevel"] = false;
          }
          if (node.children && node.children.length) {
            node.children.forEach(child => markTopLevel(child));
          }
        };
        originDataArr.forEach(item => markTopLevel(item));
        const findTopOrgCode = (nodes) => {
          for (const node of nodes) {
            if (!node.notTopLevel) {
              return node.key;
            }
            if (node.children && node.children.length) {
              const childResult = findTopOrgCode(node.children);
              if (childResult) return childResult;
            }
          }
          return null;
        };
        const topOrgCode = findTopOrgCode(originDataArr);
        this.setDisabledTrue(topOrgCode, this.factoryTreeData);
      } else {
        this.setDisabledFalse();
      }

    },
    setDisabledTrue(topOrgCode, arr) {
      const setDisabled = (nodes) => {
        if (!nodes || !nodes.length) return;

        nodes.forEach(node => {
          if (node.key === topOrgCode) {
            node.disabled = true;
          } else {
            node.disabled = true;
            if (node.children && node.children.length) {
              setDisabled(node.children);
            }
          }
        });
      };

      setDisabled(arr);
    },
    setDisabledFalse(arr = this.factoryTreeData) {
      const setNodeDisabled = (nodes) => {
        if (!nodes || !nodes.length) return;

        nodes.forEach(node => {
          // 如果节点未被选中,设置为可用
          if (!this.factoryCheckedKeys.includes(node.key)) {
            node.disabled = false;
          }
          // 递归处理子节点
          if (node.children) {
            setNodeDisabled(node.children);
          }
        });
      }

      setNodeDisabled(arr);
    },
    factoryClearSelect() {
      this.factoryCheckedKeys = [];
      this.setDisabledFalse(this.factoryTreeData);
    },



    // 云图树搜索处理
    handleYtSearch(value) {
      if (!value) {
        this.ytSearchList = [];
        return;
      }

      // 递归搜索树节点
      const searchNodes = (nodes, keyword) => {
        let results = [];
        nodes.forEach(node => {
          if (!node.title) return;
          // 检查节点标题是否包含关键字（不区分大小写）
          if (node.title.toLowerCase().includes(keyword.toLowerCase())) {
            results.push({
              key: node.key,
              value: node.key,
              title: node.title,
              ...node
            });
          }
          // 如果有子节点，继续搜索
          if (node.children && node.children.length) {
            results = results.concat(searchNodes(node.children, keyword));
          }
        });
        return results;
      };

      // 从工厂树数据中搜索
      this.ytSearchList = searchNodes(this.ytTreeData, value);
    },
    // 云图树选择节点处理
    handleYtSelect(selectedKey) {
      // 找到节点的完整路径
      const findNodePath = (nodes, targetKey, path = []) => {
        for (let node of nodes) {
          if (node.key === targetKey) {
            return [...path, node.key];
          }
          if (node.children && node.children.length) {
            const foundPath = findNodePath(node.children, targetKey, [...path, node.key]);
            if (foundPath) return foundPath;
          }
        }
        return null;
      };

      // 获取节点路径
      const nodePath = findNodePath(this.ytTreeData, selectedKey);

      if (nodePath) {
        // 展开到选中节点
        this.ytExpandedKeys = [...new Set([...this.ytExpandedKeys, ...nodePath])];
        // 选中节点
        this.ytSelectedKeys = [...new Set([...this.ytSelectedKeys, selectedKey])];
      }

      // 清空搜索
      this.ytSearchValue = undefined;
      this.ytSearchList = [];
    },
    onYtExpand(expandedKeys) {
      this.ytExpandedKeys = expandedKeys;
      this.setDisabledFalse(this.factoryTreeData);
    },
    // 获取云图组织层级
    getYtTreeData(first = false) {
      request(`/api/smc2/treeOrg/cmTreeList?date=${moment().format('YYYY-MM-DD')}`, {
        method: "GET",
      }).then((res) => {
        this.ytTreeData = res.map(item => {
          const processNode = (node) => {
            return {
              key: node.code,
              title: node.name,
              slots: {
                title: `show-state-${node.code}`,
              },
              slotTitle: `show-state-${node.code}`,
              realTitle: `${node.name}(${node.code})`,
              value: node.code,//这个key是给编辑的selectTree使用的字段
              label: `${node.name}(${node.code})`,//这个key是给编辑的selectTree使用的字段
              ...node,
              children: node.children ? node.children.map(child => processNode(child)) : []
            }
          }
          return processNode(item)
        });
        this.ytTreeDataLoading = false;
        this.$forceUpdate();
        // 默认展开第一层
        // if (first) {
        //   this.ytExpandedKeys = this.ytTreeData.map(item => item.key);
        // }
      })
    },

    onYtSelect(selectedKeys) {
      this.ytSelectedKeys = selectedKeys;
    },

    // 保存/同级添加/下级添加
    ytSave() {
      // 获取所有选中节点的数据
      const checkedNodes = this.factoryCheckedKeys.map(key => {
        const node = this.findItem(this.factoryTreeData, key);
        return { ...node };
      });
      // 构建父子关系
      const buildTree = (nodes) => {
        const nodeMap = new Map();
        const result = [];

        // 创建节点映射
        nodes.forEach(node => {
          nodeMap.set(node.factoryModelCode, {
            ...node,
            children: [],
            // 添加后端需要的字段
            code: node.factoryModelCode,
            superiorCode: node.parentCode,
            name: node.title,
            levelCode: null,
            levelName: '',
            beginDate: moment().format('YYYY-MM-DD'),
            endDate: '2100-12-31',
          });
        });

        // 建立父子关系
        nodes.forEach(node => {
          const currentNode = nodeMap.get(node.factoryModelCode);
          if (node.parentCode && nodeMap.has(node.parentCode)) {
            const parentNode = nodeMap.get(node.parentCode);
            parentNode.children.push(currentNode);
          } else {
            result.push(currentNode);
          }
        });

        // 清理空的children数组
        const cleanEmptyChildren = (node) => {
          if (node.children && node.children.length === 0) {
            delete node.children;
          } else if (node.children) {
            node.children.forEach(cleanEmptyChildren);
          }
        };

        result.forEach(cleanEmptyChildren);
        return result;
      };
      const factoryCheckedTree = buildTree(checkedNodes);
      //跨级，同级添加，新建一条code：null
      request('/api/smc2/treeOrg/saveTree1', {
        method: 'POST',
        body: {
          id: this.findItem(this.ytTreeData, this.ytSelectedKeys[0]).id,
          list: factoryCheckedTree
        }
      }).then((res) => {
        if (!res) {
          this.$message.success('保存成功');
          this.getYtTreeData()
        } else {
          this.$message.error(res.msg);
        }
      })

    },

    // 删除
    ytDel() {
      this.$confirm({
        title: "提示",
        content: `确定要删除"${this.findItem(this.ytTreeData, this.ytSelectedKeys[0]).title}"组织吗？`,
        onOk: () => {
          request(`/api/smc2/treeOrg/deleteCM`, {
            method: "POST",
            body: {
              fullCode: this.findParent(this.ytTreeData, this.ytSelectedKeys[0]).fullCode,
            }
          }).then(() => {
            this.$message.success('删除成功');
            this.getYtTreeData()
            this.$nextTick(() => {
              const parents = this.findParentKeys(this.ytSelectedKeys[0]);

              if (parents.length) {
                this.ytExpandedKeys = parents.reverse();
              }
              this.ytSelectedKeys = [];

            })

          })
        },
        onCancel: () => {
          this.$message.info("已取消操作");
        },
      })
    },
    // 编辑
    ytEdit() {
      this.ytEditVisible = true;
      this.ytEditData = JSON.parse(JSON.stringify(this.findItem(this.ytTreeData, this.ytSelectedKeys[0])));
    },
    // yt组织树编辑dialog上级组织改变
    handleYtSuperiorChange(value, label, extra) {
      // console.log(value, label, extra)
      //获取选择父级的节点
      this.changeLevelInstance = this.findItem(this.ytTreeData, value)
    },
    // yt编辑弹框关闭
    ytEditCancel() {
      this.changeYtLevelFlag = false;
      this.ytEditVisible = false;
      this.ytEditData = {};
      this.$refs.ytEditForm.resetFields();
    },
    // yt编辑弹框确认
    ytEditOk() {
      this.$refs.ytEditForm.validate().then(() => {
        // 默认提交字段
        let defaultPostKey = ['name', 'code', 'fullCode', 'levelCode', 'levelName', 'beginDate', 'endDate']
        // 如果更变层级
        let changeLevelKey = ['superiorCode', 'parentFullCode']
        // 从ytEditData中获取默认字段
        let postData = defaultPostKey.reduce((acc, key) => {
          if (this.ytEditData[key] !== undefined) {
            acc[key] = this.ytEditData[key];
          }
          return acc;
        }, {});

        // 如果选择了更变层级,则从changeLevelInstance中获取额外字段
        if (this.changeYtLevelFlag && this.changeLevelInstance) {
          postData.superiorCode = this.changeLevelInstance.code;
          postData.parentFullCode = this.changeLevelInstance.fullCode;
        }

        request('/api/smc2/treeOrg/updateCmTree', {
          method: 'POST',
          body: postData
        }).then(() => {
          this.$message.success('保存成功');
          this.getYtTreeData()
          this.ytEditVisible = false;
          this.changeYtLevelFlag = false;
          // 调整完层级要展开父级
          this.$nextTick(() => {
            const parents = this.findParentKeys(this.changeLevelInstance.code);
            // 如果存在父级，则展开父级
            if (parents.length) {
              this.ytExpandedKeys = parents.reverse();
            }
            this.ytSelectedKeys = [postData.code];
          })
        })


      })
    },
    // 机构推送
    postOrg() {
      request(`/api/smc2/treeOrg/sendMessage`, {
        method: "GET",
      }).then((res) => {
        if (!res) {
          this.$message.success("推送成功");
        } else {
          this.$message.error(res.msg);
        }
      });
    },


    // 扁平化树结构
    flattenTree(treeData) {
      return treeData.flatMap(item => [item, ...(item.children ? this.flattenTree(item.children) : [])]);
    },
    findItem(nodes, targetKey) {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return node;
        }
        if (node.children && node.children.length) {
          const result = JSON.parse(JSON.stringify(this.findItem(node.children, targetKey)));
          if (result) {
            return result;
          }
        }
      }
      return '';
    },
    findParent(nodes, targetKey) {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return node;
        }
        if (node.children && node.children.length) {
          const result = this.findParent(node.children, targetKey);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    // 祖先级到当前级的key序列，用来展开节点，需要手动reverse
    findParentKeys(targetKey) {
      const parents = [];
      const findParentKeys = (key) => {
        const findNode = (nodes, targetKey) => {
          for (let node of nodes) {
            if (node.key === targetKey) {
              return node;
            }
            if (node.children && node.children.length) {
              const found = findNode(node.children, targetKey);
              if (found) return found;
            }
          }
          return null;
        };
        const node = findNode(this.ytTreeData, key);
        if (!node) return;
        parents.push(node.key);
        findParentKeys(node.superiorCode);
      };
      findParentKeys(targetKey);
      return parents;
    },
    // 获取组织层级字典
    getDICT() {
      //1. 集团，2. 公司，3. 中心，4. 基地，5. 工厂，6. 部门，7. 科室，8. 车间，9. 二级车间，10. 线体，11. 二级线体，12. 班组，13. 大班组，14. 小班组，15. 班次，16.工段
      // 手动添加字典，因为mom-smc2Level后端没有返回
      this.levelDictList = [
        { value: '集团', label: '集团' },
        { value: '公司', label: '公司' },
        { value: '中心', label: '中心' },
        { value: '基地', label: '基地' },
        { value: '工厂', label: '工厂' },
        { value: '部门', label: '部门' },
        { value: '科室', label: '科室' },
        { value: '车间', label: '车间' },
        { value: '二级车间', label: '二级车间' },
        { value: '线体', label: '线体' },
        { value: '二级线体', label: '二级线体' },
        { value: '班组', label: '班组' },
        { value: '大班组', label: '大班组' },
        { value: '小班组', label: '小班组' },
        { value: '班次', label: '班次' },
        { value: '工段', label: '工段' },
      ]
    },
  },
  mounted() {
    // 加载工厂组织层级
    this.getFactoryTreeData();
    // 加载云图组织层级
    this.getYtTreeData(true);
    // 获取组织层级字典
    this.getDICT();
  }
}
</script>
<style lang="less" scoped>
.org-manage-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #fff;

  .org-manage-header {
    height: 80px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  .org-manage-content {
    display: flex;
    flex: 1;
    width: 100%;
    height: calc(100% - 80px);

    ._title {
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      margin: 10px 0;
    }

    .org-manage-content-left {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;

      .treeBox {
        width: 430px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
        padding: 5px 10px;
        height: 45vh;
        margin: 0 auto;

        .org-list-tree {
          width: 100%;
          height: 34vh;
          border: 1px solid rgba(0, 0, 0, 0.2);
          overflow-y: auto;
          border-radius: 2px;
        }
      }
    }

    .org-manage-content-right {
      flex: 1;
      background-color: #fff;

      .treeBox {
        width: 430px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
        padding: 5px 10px;
        margin: 0 auto;

        .org-list-tree {
          width: 100%;
          height: 70vh;
          border: 1px solid rgba(0, 0, 0, 0.2);
          overflow-y: auto;
          border-radius: 2px;
        }
      }
    }
  }
}
</style>

<style lang="less">
/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 3px;
  /* 滚动条宽度 */
  height: 6px;
  /* 滚动条高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* 轨道颜色 */
  border-radius: 5px;
  /* 轨道圆角 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #888;
  /* 滑块颜色 */
  border-radius: 5px;
  /* 滑块圆角 */
}

/* 鼠标悬停时的滚动条滑块 */
::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* 滑块悬停颜色 */
}

/* 滚动条按钮 */
::-webkit-scrollbar-button {
  display: none;
  /* 隐藏滚动条按钮 */
}

/* 对于水平滚动条 */
::-webkit-scrollbar:horizontal {
  height: 3px;
  /* 水平滚动条高度 */
}

/* 水平滚动条轨道 */
::-webkit-scrollbar-track:horizontal {
  background: #f1f1f1;
  /* 水平滚动条轨道颜色 */
}

/* 水平滚动条滑块 */
::-webkit-scrollbar-thumb:horizontal {
  background: #888;
  /* 水平滚动条滑块颜色 */
}

::v-deep .ant-form-item-label {
  height: 28px;
}
</style>
