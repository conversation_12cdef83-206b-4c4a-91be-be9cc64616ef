<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-12-24 10:51:10
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <!-- 月度 -->
    <a-table
      :columns="monthColumns"
      :data-source="monthData"
      bordered
      :pagination="false"
      :scroll="{ x: 1000 }"
    >
      <span slot="year" slot-scope="text, record">
        <span>{{ record["year"] }}</span></span
      >
      <!-- 一月 -->
      <template slot="Jan-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Jan-numerator']" style="width: 100%" />
      </template>
      <template slot="Jan-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Jan-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Jan-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Jan-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 二月 -->
      <template slot="Feb-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Feb-numerator']" style="width: 100%" />
      </template>
      <template slot="Feb-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Feb-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Feb-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Feb-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 三月 -->
      <template slot="Mar-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Mar-numerator']" style="width: 100%" />
      </template>
      <template slot="Mar-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Mar-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Mar-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Mar-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 四月 -->
      <template slot="Apr-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Apr-numerator']" style="width: 100%" />
      </template>
      <template slot="Apr-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Apr-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Apr-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Apr-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 五月 -->
      <template slot="May-numerator" slot-scope="text, record">
        <a-input-number v-model="record['May-numerator']" style="width: 100%" />
      </template>
      <template slot="May-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['May-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="May-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['May-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 六月 -->
      <template slot="Jun-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Jun-numerator']" style="width: 100%" />
      </template>
      <template slot="Jun-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Jun-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Jun-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Jun-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 七月 -->
      <template slot="Jul-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Jul-numerator']" style="width: 100%" />
      </template>
      <template slot="Jul-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Jul-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Jul-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Jul-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 八月 -->
      <template slot="Aug-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Aug-numerator']" style="width: 100%" />
      </template>
      <template slot="Aug-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Aug-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Aug-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Aug-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 九月 -->
      <template slot="Sept-numerator" slot-scope="text, record">
        <a-input-number
          v-model="record['Sept-numerator']"
          style="width: 100%"
        />
      </template>
      <template slot="Sept-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Sept-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Sept-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Sept-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 十月 -->
      <template slot="Oct-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Oct-numerator']" style="width: 100%" />
      </template>
      <template slot="Oct-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Oct-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Oct-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Oct-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 十一月 -->
      <template slot="Nov-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Nov-numerator']" style="width: 100%" />
      </template>
      <template slot="Nov-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Nov-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Nov-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Nov-actualValue']"
          style="width: 100%"
        />
      </template>
      <!-- 十二月 -->
      <template slot="Dec-numerator" slot-scope="text, record">
        <a-input-number v-model="record['Dec-numerator']" style="width: 100%" />
      </template>
      <template slot="Dec-denominator" slot-scope="text, record">
        <a-input-number
          v-model="record['Dec-denominator']"
          style="width: 100%"
        />
      </template>
      <template slot="Dec-actualValue" slot-scope="text, record">
        <a-input-number
          v-model="record['Dec-actualValue']"
          style="width: 100%"
        />
      </template>
      <template slot="title">
        <h3>
          {{ indexName }} {{ `(单位：${indexUnit})` }} 维度： {{ wd }}
          <a-tag style="margin-left: 10px;" color="#2db7f5">
            {{ vauleDataType }}
          </a-tag>
          <a-tag style="margin-left: 10px;" color="#87d068">
            {{ flag === "Y" ? "累计" : "非累计" }}
          </a-tag>
        </h3>
      </template>
    </a-table>
    <!-- 季度 -->
    <template v-if="quarterSubmitData.length">
      <a-table
        :columns="quarterColumns"
        :data-source="quarterData"
        bordered
        :pagination="false"
        :scroll="{ x: 1000 }"
      >
        <span slot="year" slot-scope="text, record">
          <span>{{ record["year"] }}</span></span
        >
        <!-- 一季度 -->
        <template slot="firstQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstQuarter-numerator']"
            style="width: 100%"
          />
        </template>
        <template slot="firstQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstQuarter-denominator']"
            style="width: 100%"
          />
        </template>
        <template slot="firstQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['firstQuarter-actualValue']"
            style="width: 100%"
          />
        </template>
        <!-- 二季度 -->
        <template slot="secondQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondQuarter-numerator']"
            style="width: 100%"
          />
        </template>
        <template slot="secondQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondQuarter-denominator']"
            style="width: 100%"
          />
        </template>
        <template slot="secondQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['secondQuarter-actualValue']"
            style="width: 100%"
          />
        </template>
        <!-- 三季度 -->
        <template slot="threeQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['threeQuarter-numerator']"
            style="width: 100%"
          />
        </template>
        <template slot="threeQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['threeQuarter-denominator']"
            style="width: 100%"
          />
        </template>
        <template slot="threeQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['threeQuarter-actualValue']"
            style="width: 100%"
          />
        </template>
        <!-- 四季度 -->
        <template slot="fourQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['fourQuarter-numerator']"
            style="width: 100%"
          />
        </template>
        <template slot="fourQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['fourQuarter-denominator']"
            style="width: 100%"
          />
        </template>
        <template slot="fourQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['fourQuarter-actualValue']"
            style="width: 100%"
          />
        </template>
      </a-table>
    </template>
    <!-- 年度 -->
    <template v-if="yearSubmitData.length">
      <a-table
        :columns="yearColumns"
        :data-source="yearData"
        bordered
        :pagination="false"
        :scroll="{ x: 1000 }"
      >
        <span slot="year" slot-scope="text, record">
          <span>{{ record["year"] }}</span></span
        >
        <!-- 上半年 -->
        <template slot="firstHalfYear-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstHalfYear-numerator']"
            style="width: 100%"
          />
        </template>
        <template slot="firstHalfYear-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstHalfYear-denominator']"
            style="width: 100%"
          />
        </template>
        <template slot="firstHalfYear-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['firstHalfYear-actualValue']"
            style="width: 100%"
          />
        </template>
        <!-- 下半年 -->
        <template slot="secondHalfYear-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondHalfYear-numerator']"
            style="width: 100%"
          />
        </template>
        <template slot="secondHalfYear-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondHalfYear-denominator']"
            style="width: 100%"
          />
        </template>
        <template slot="secondHalfYear-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['secondHalfYear-actualValue']"
            style="width: 100%"
          />
        </template>
      </a-table>
    </template>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      isEdit: false, // 是否编辑状态
      indexName: "某某某指标",
      indexUnit: "%",
      wd: "",
      flag: "",
      monthColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left"
        },
        {
          title: "一月",
          dataIndex: "Jan",
          key: "Jan",
          num: "01",
          scopedSlots: { customRender: "Jan" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Jan-numerator",
              key: "Jan-numerator",
              scopedSlots: { customRender: "Jan-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Jan-denominator",
              key: "Jan-denominator",
              scopedSlots: { customRender: "Jan-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Jan-actualValue",
              key: "Jan-actualValue",
              scopedSlots: { customRender: "Jan-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "二月",
          dataIndex: "Feb",
          key: "Feb",
          num: "02",
          scopedSlots: { customRender: "Feb" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Feb-numerator",
              key: "Feb-numerator",
              scopedSlots: { customRender: "Feb-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Feb-denominator",
              key: "Feb-denominator",
              scopedSlots: { customRender: "Feb-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Feb-actualValue",
              key: "Feb-actualValue",
              scopedSlots: { customRender: "Feb-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "三月",
          key: "Mar",
          dataIndex: "Mar",
          num: "03",
          scopedSlots: { customRender: "Mar" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Mar-numerator",
              key: "Mar-numerator",
              scopedSlots: { customRender: "Mar-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Mar-denominator",
              key: "Mar-denominator",
              scopedSlots: { customRender: "Mar-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Mar-actualValue",
              key: "Mar-actualValue",
              scopedSlots: { customRender: "Mar-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "四月",
          key: "Apr",
          dataIndex: "Apr",
          num: "04",
          scopedSlots: { customRender: "Apr" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Apr-numerator",
              key: "Apr-numerator",
              scopedSlots: { customRender: "Apr-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Apr-denominator",
              scopedSlots: { customRender: "Apr-denominator" },
              key: "Apr-denominator",
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Apr-actualValue",
              scopedSlots: { customRender: "Apr-actualValue" },
              key: "Apr-actualValue",
              width: 100
            }
          ]
        },
        {
          title: "五月",
          key: "May",
          dataIndex: "May",
          num: "05",
          scopedSlots: { customRender: "May" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "May-numerator",
              key: "May-numerator",
              scopedSlots: { customRender: "May-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "May-denominator",
              key: "May-denominator",
              scopedSlots: { customRender: "May-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "May-actualValue",
              key: "May-actualValue",
              scopedSlots: { customRender: "May-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "六月",
          key: "Jun",
          dataIndex: "Jun",
          num: "06",
          scopedSlots: { customRender: "Jun" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Jun-numerator",
              key: "Jun-numerator",
              scopedSlots: { customRender: "Jun-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Jun-denominator",
              key: "Jun-denominator",
              scopedSlots: { customRender: "Jun-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Jun-actualValue",
              key: "Jun-actualValue",
              scopedSlots: { customRender: "Jun-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "七月",
          key: "Jul",
          dataIndex: "Jul",
          num: "07",
          scopedSlots: { customRender: "Jul" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Jul-numerator",
              key: "Jul-numerator",
              scopedSlots: { customRender: "Jul-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Jul-denominator",
              key: "Jul-denominator",
              scopedSlots: { customRender: "Jul-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Jul-actualValue",
              key: "Jul-actualValue",
              scopedSlots: { customRender: "Jul-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "八月",
          key: "Aug",
          dataIndex: "Aug",
          num: "08",
          scopedSlots: { customRender: "Aug" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Aug-numerator",
              key: "Aug-numerator",
              scopedSlots: { customRender: "Aug-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Aug-denominator",
              key: "Aug-denominator",
              scopedSlots: { customRender: "Aug-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Aug-actualValue",
              key: "Aug-actualValue",
              scopedSlots: { customRender: "Aug-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "九月",
          key: "Sept",
          dataIndex: "Sept",
          num: "09",
          scopedSlots: { customRender: "Sept" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Sept-numerator",
              key: "Sept-numerator",
              scopedSlots: { customRender: "Sept-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Sept-denominator",
              key: "Sept-denominator",
              scopedSlots: { customRender: "Sept-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Sept-actualValue",
              key: "Sept-actualValue",
              scopedSlots: { customRender: "Sept-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "十月",
          key: "Oct",
          dataIndex: "Oct",
          scopedSlots: { customRender: "Oct" },
          num: "10",
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Oct-numerator",
              key: "Oct-numerator",
              scopedSlots: { customRender: "Oct-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Oct-denominator",
              key: "Oct-denominator",
              scopedSlots: { customRender: "Oct-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Oct-actualValue",
              key: "Oct-actualValue",
              scopedSlots: { customRender: "Oct-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "十一月",
          key: "Nov",
          dataIndex: "Nov",
          num: "11",
          scopedSlots: { customRender: "Nov" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Nov-numerator",
              key: "Nov-numerator",
              scopedSlots: { customRender: "Nov-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Nov-denominator",
              key: "Nov-denominator",
              scopedSlots: { customRender: "Nov-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Nov-actualValue",
              key: "Nov-actualValue",
              scopedSlots: { customRender: "Nov-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "十二月",
          key: "Dec",
          dataIndex: "Dec",
          num: "12",
          scopedSlots: { customRender: "Dec" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "Dec-numerator",
              key: "Dec-numerator",
              scopedSlots: { customRender: "Dec-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "Dec-denominator",
              key: "Dec-denominator",
              scopedSlots: { customRender: "Dec-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "Dec-actualValue",
              key: "Dec-actualValue",
              scopedSlots: { customRender: "Dec-actualValue" },
              width: 100
            }
          ]
        }
      ], // 表格列
      monthData: [],
      monthSubmitData: [],
      quarterColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left"
        },
        {
          title: "第一季度",
          dataIndex: "firstQuarter",
          key: "firstQuarter",
          num: "01",
          scopedSlots: { customRender: "firstQuarter" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "firstQuarter-numerator",
              key: "firstQuarter-numerator",
              scopedSlots: { customRender: "firstQuarter-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "firstQuarter-denominator",
              key: "firstQuarter-denominator",
              scopedSlots: { customRender: "firstQuarter-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "firstQuarter-actualValue",
              key: "firstQuarter-actualValue",
              scopedSlots: { customRender: "firstQuarter-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "第二季度",
          dataIndex: "secondQuarter",
          key: "secondQuarter",
          num: "02",
          scopedSlots: { customRender: "secondQuarter" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "secondQuarter-numerator",
              key: "secondQuarter-numerator",
              scopedSlots: { customRender: "secondQuarter-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "secondQuarter-denominator",
              key: "secondQuarter-denominator",
              scopedSlots: { customRender: "secondQuarter-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "secondQuarter-actualValue",
              key: "secondQuarter-actualValue",
              scopedSlots: { customRender: "secondQuarter-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "第三季度",
          key: "threeQuarter",
          dataIndex: "threeQuarter",
          num: "03",
          scopedSlots: { customRender: "threeQuarter" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "threeQuarter-numerator",
              key: "threeQuarter-numerator",
              scopedSlots: { customRender: "threeQuarter-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "threeQuarter-denominator",
              key: "threeQuarter-denominator",
              scopedSlots: { customRender: "threeQuarter-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "threeQuarter-actualValue",
              key: "threeQuarter-actualValue",
              scopedSlots: { customRender: "threeQuarter-actualValue" },
              width: 100
            }
          ]
        },
        {
          title: "第四季度",
          key: "fourQuarter",
          dataIndex: "fourQuarter",
          num: "04",
          scopedSlots: { customRender: "fourQuarter" },
          width: 300,
          children: [
            {
              title: "分子",
              dataIndex: "fourQuarter-numerator",
              key: "fourQuarter-numerator",
              scopedSlots: { customRender: "fourQuarter-numerator" },
              width: 100
            },
            {
              title: "分母",
              dataIndex: "fourQuarter-denominator",
              key: "fourQuarter-denominator",
              scopedSlots: { customRender: "fourQuarter-denominator" },
              width: 100
            },
            {
              title: "实际值",
              dataIndex: "fourQuarter-actualValue",
              key: "fourQuarter-actualValue",
              scopedSlots: { customRender: "fourQuarter-actualValue" },
              width: 100
            }
          ]
        }
      ], // 表格列
      quarterData: [],
      quarterSubmitData: [],
      yearColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left"
        },
        {
          title: "上半年",
          dataIndex: "firstHalfYear",
          key: "firstHalfYear",
          num: "01",
          scopedSlots: { customRender: "firstHalfYear" },
          width: 450,
          children: [
            {
              title: "分子",
              dataIndex: "firstHalfYear-numerator",
              key: "firstHalfYear-numerator",
              scopedSlots: { customRender: "firstHalfYear-numerator" },
              width: 150
            },
            {
              title: "分母",
              dataIndex: "firstHalfYear-denominator",
              key: "firstHalfYear-denominator",
              scopedSlots: { customRender: "firstHalfYear-denominator" },
              width: 150
            },
            {
              title: "实际值",
              dataIndex: "firstHalfYear-actualValue",
              key: "firstHalfYear-actualValue",
              scopedSlots: { customRender: "firstHalfYear-actualValue" },
              width: 150
            }
          ]
        },
        {
          title: "下半年",
          dataIndex: "secondHalfYear",
          key: "secondHalfYear",
          num: "02",
          scopedSlots: { customRender: "secondHalfYear" },
          width: 450,
          children: [
            {
              title: "分子",
              dataIndex: "secondHalfYear-numerator",
              key: "secondHalfYear-numerator",
              scopedSlots: { customRender: "secondHalfYear-numerator" },
              width: 150
            },
            {
              title: "分母",
              dataIndex: "secondHalfYear-denominator",
              key: "secondHalfYear-denominator",
              scopedSlots: { customRender: "secondHalfYear-denominator" },
              width: 150
            },
            {
              title: "实际值",
              dataIndex: "secondHalfYear-actualValue",
              key: "secondHalfYear-actualValue",
              scopedSlots: { customRender: "secondHalfYear-actualValue" },
              width: 150
            }
          ]
        }
      ], // 表格列
      yearData: [],
      yearSubmitData: [],
      vauleDataType: "",
      isLJ: false // 是否累加目标值
    };
  },
  methods: {
    show({ indexName, isLJ = false, cmimIdFront, cmimId, dataType, flag }) {
      this.visible = true;
      this.isEdit = true;
      this.indexName = indexName;
      this.isLJ = isLJ;
      this.flag = flag;
      this.setTableHeaderTitle(dataType);
      this.vauleDataType = dataType;
      this.generateDataItem();
      this.$nextTick(() => {
        this.getIndexTarget({ cmimIdFront, cmimId, dataType, flag });
      });
    },
    // 按照dataType动态修改表格表头title
    setTableHeaderTitle(dataType) {
      ["monthColumns", "quarterColumns", "yearColumns"].forEach(item => {
        this[item].forEach(columnsItem => {
          // eslint-disable-next-line no-prototype-builtins
          if (columnsItem.hasOwnProperty("children")) {
            columnsItem.children[2]["title"] = `${dataType}值`;
          }
        });
      });
    },
    // 生成数据每一项
    generateDataItem() {
      const year = new Date().getFullYear();
      const common = {};
      const quarterCommon = {};
      const yearCommon = {};
      ["numerator", "denominator", "actualValue"].forEach(item => {
        [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sept",
          "Oct",
          "Nov",
          "Dec"
        ].forEach(yearItem => {
          common[`${yearItem}-${item}`] = "";
        });
        [
          "firstQuarter",
          "secondQuarter",
          "threeQuarter",
          "fourQuarter"
        ].forEach(quarterItem => {
          quarterCommon[`${quarterItem}-${item}`] = "";
        });
        ["firstHalfYear", "secondHalfYear"].forEach(yearItem => {
          yearCommon[`${yearItem}-${item}`] = "";
        });
      });
      this.monthData.push({
        year: year - 2,
        ...common
      });
      this.monthData.push({
        year: year - 1,
        ...common
      });
      this.monthData.push({
        year,
        ...common
      });
      this.quarterData.push({
        year: year - 2,
        ...quarterCommon
      });
      this.quarterData.push({
        year: year - 1,
        ...quarterCommon
      });
      this.quarterData.push({
        year,
        ...quarterCommon
      });
      this.yearData.push({
        year: year - 2,
        ...yearCommon
      });
      this.yearData.push({
        year: year - 1,
        ...yearCommon
      });
      this.yearData.push({
        year,
        ...yearCommon
      });
      console.log(
        "this.monthData----->",
        this.monthData,
        this.quarterData,
        this.yearData
      );

      // this.quarterData.push({
      //   year: year + 1,
      //   ...quarterCommon
      // });
      // this.yearData.push({
      //   year: year + 1,
      //   ...yearCommon
      // });
    },
    getIndexTarget({ cmimIdFront, cmimId, dataType, flag }) {
      // cmimIdFront =
      //   "ZBY00451-H020115900-0000-0000-DIM_1255-0000-0000-0000-0000-0000-0000-0000-0000";
      request(
        `/api/smc2/gjyx/front/getDetailById?cmimIdFront=${cmimIdFront}&cmimId=${cmimId}&dataType=${dataType}&flag=${flag}`
      ).then(res => {
        if (res) {
          this.wd = res.wd;
          this.indexUnit = res.unit;
          console.log("res----->", res);
          // 分子 numerator
          // 分母 denominator
          // 实际值 actualValue
          const validMonthArr = res.M || [];
          validMonthArr.forEach(element => {
            const arr = element.m.split("-");
            this.updateData(
              arr[0],
              arr[1],
              element.numerator,
              "month",
              "numerator"
            );
            this.updateData(
              arr[0],
              arr[1],
              element.denominator,
              "month",
              "denominator"
            );
            this.updateData(
              arr[0],
              arr[1],
              element.actualValue,
              "month",
              "actualValue"
            );
          });
          console.log(this.monthData);
          const validQuarterArr = res.Q || [];
          validQuarterArr.forEach(element => {
            const arr = element.q.split("-");
            this.updateData(
              arr[0],
              arr[1],
              element.numerator,
              "quarter",
              "numerator"
            );
            this.updateData(
              arr[0],
              arr[1],
              element.denominator,
              "quarter",
              "denominator"
            );
            this.updateData(
              arr[0],
              arr[1],
              element.actualValue,
              "quarter",
              "actualValue"
            );
          });
          console.log(this.quarterData);
          const validYearArr = res.HF || [];
          validYearArr.forEach(element => {
            const arr = element.hf.split("-");
            this.updateData(
              arr[0],
              arr[1],
              element.numerator,
              "year",
              "numerator"
            );
            this.updateData(
              arr[0],
              arr[1],
              element.denominator,
              "year",
              "denominator"
            );
            this.updateData(
              arr[0],
              arr[1],
              element.actualValue,
              "year",
              "actualValue"
            );
          });
          console.log(this.yearData);
          this.monthSubmitData = res.M || [];
          this.quarterSubmitData = res.Q || [];
          this.yearSubmitData = res.HF || [];
        }
      });
    },
    updateData(year, month, value, sign, itemSign) {
      this[`${sign}Data`].forEach(element => {
        if (year === String(element.year)) {
          const enKey = this[`${sign}Columns`].filter(item => {
            return item.num === month.padStart(2, "0");
          })[0].dataIndex;
          element[`${enKey}-${itemSign}`] = value;
        }
      });
    },
    close() {
      this.monthData = [];
      this.quarterData = [];
      this.yearData = [];
      this.indexName = "";
      this.indexUnit = "";
      this.flag = "";
      this.monthSubmitData = [];
      this.quarterSubmitData = [];
      this.yearSubmitData = [];
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 保存卡片信息
    saveCard() {
      ["month", "quarter", "year"].forEach(timeType => {
        this[`${timeType}Data`].forEach(yearElement => {
          ["numerator", "denominator", "actualValue"].forEach(typeItem => {
            for (const key in yearElement) {
              if (
                Object.hasOwnProperty.call(yearElement, key) &&
                key.includes(typeItem)
              ) {
                const element = yearElement[key];
                const numKey = this[`${timeType}Columns`].filter(
                  item => item.dataIndex === key.split("-")[0]
                )[0]?.num;
                if (numKey) {
                  this[`${timeType}SubmitData`].forEach(submitElement => {
                    if (
                      timeType === "month" &&
                      submitElement.m === `${yearElement["year"]}-${numKey}`
                    ) {
                      submitElement[typeItem] = String(element || "");
                    }
                    if (
                      timeType === "quarter" &&
                      submitElement.q === `${yearElement["year"]}-${numKey}`
                    ) {
                      submitElement[typeItem] = String(element || "");
                    }
                    if (
                      timeType === "year" &&
                      submitElement.hf === `${yearElement["year"]}-${numKey}`
                    ) {
                      submitElement[typeItem] = String(element || "");
                    }
                  });
                }
              }
            }
          });
        });
      });
      request("/api/smc2/gjyx/front/batchInsert", {
        method: "POST",
        body: [
          ...this.monthSubmitData.map(item => {
            return {
              ...item,
              flag: this.flag
            };
          }),
          ...this.quarterSubmitData.map(item => {
            return {
              ...item,
              flag: this.flag
            };
          }),
          ...this.yearSubmitData.map(item => {
            return {
              ...item,
              flag: this.flag
            };
          })
        ]
      }).then(() => {
        // if (res && res.result === "success") {
        this.close();
        this.$emit("fetchData");
        // } else {
        //   this.$message.error(res.msg);
        // }
      });
    }
  }
};
</script>
