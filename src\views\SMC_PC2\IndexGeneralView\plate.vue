<!--
 * @Description: 版块
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 16:20:43
 * @LastEditors: Jhin.Yu <EMAIL>
 * @LastEditTime: 2023-02-17 09:53:13
-->
<template>
  <div class="_plate _flex">
    <!-- 筛选 -->
    <div class="_left">
      <div class="filter _flex">
        <span
          >{{
            !isLocal ? $t("723bb557-8520-42d0-a988-b675f9818bbf") : "业务版块"
          }}：</span
        >
        <div class="_flex">
          <span
            class="_item"
            :class="[activeIdx === index ? 'active' : '']"
            v-for="(item, index) in newPlateList"
            :key="index"
            @click="plateClick(index)"
          >
            {{ item.businessSegments }}
          </span>
        </div>
      </div>
      <div class="indexStatus _flex" style="margin-left: 24px;">
        {{
          !isLocal ? $t("318ca1ea-753b-4bd2-9256-2013d772c7db") : "指标筛选"
        }}：
        <a-select
          style="width: 160px"
          :placeholder="
            !isLocal ? $t('4155039a-b280-4271-8216-508465ce4f76') : '全部指标'
          "
          :dropdownMatchSelectWidth="false"
          v-model="indexStatus"
          allowClear
          @change="
            e => {
              this.$emit('indexStatusChnage', e);
            }
          "
        >
          <a-select-option
            :value="item.key"
            v-for="item in statusOption"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </div>
    </div>
    <!-- 分析 -->
    <template v-if="skinStyle().includes('classic-style')">
      <div class="analysis" @click="showDrawer">
        {{
          !isLocal ? $t("01ee0e91-78e8-4dbe-b644-53f286f3d4f7") : "查看专项分析"
        }}
        &#8594;
      </div>
    </template>
    <template v-else>
      <div class="analysis" @click="showDrawer">
        {{
          !isLocal ? $t("01ee0e91-78e8-4dbe-b644-53f286f3d4f7") : "查看专项分析"
        }}
        <img src="@/assets/images/Group <EMAIL>" alt="" srcset="" />
      </div>
    </template>
    <!-- 专项分析抽屉 -->
    <AnalysisDrawer
      :analysisList="analysisList"
      ref="AnalysisDrawer"
      :companyName="companyName"
    />
  </div>
</template>
<script>
import AnalysisDrawer from "./analysisDrawer.vue";
import request from "@/utils/requestHttp";

export default {
  components: { AnalysisDrawer },
  inject: ["skinStyle", "isLocal"],
  props: {
    // 卡片列表
    cardList: {
      type: Array,
      default() {
        return [];
      }
    },
    companyName: String,
    signOrgId: String
  },
  watch: {
    cardList: {
      handler(value) {
        if (value) {
          // 监听卡片列表重新点击版块
          let idx = 0;
          this.newPlateList.forEach((item, index) => {
            if (item.businessSegments === this.activeIdxTxt) {
              idx = index;
            }
          });
          this.plateClick(idx);
        }
      },
      deep: true
    }
  },
  computed: {
    // 增加全部选项
    newPlateList() {
      const list = Array.from(
        new Set(this.cardList.map(item => item.businessSegments))
      );
      return [
        {
          businessSegments: !this.isLocal
            ? this.$t("4155039a-b280-4271-8216-508465ce4f76")
            : "全部"
        },
        ...list.map(item => {
          return { businessSegments: item };
        })
      ];
    }
  },
  data() {
    return {
      activeIdx: 0,
      activeIdxTxt: "",
      indexStatus: undefined, // 指标状态
      analysisList: [], // 专项分析列表
      statusOption: [
        {
          key: "0",
          value: "订阅KPI未达标数量"
        },
        {
          key: "1",
          value: "未达标同环比恶化"
        }
      ]
    };
  },
  methods: {
    // 设置状态
    setStatus(e) {
      this.indexStatus = e;
    },
    /**
     * @description: 选中版块
     * @param {Number} index 下标
     */
    plateClick(index) {
      this.activeIdx = index;
      const txt = this.newPlateList[index].businessSegments;
      this.activeIdxTxt = txt;
      this.$emit("plateChange", {
        plate: txt
      });
    },
    // 打开抽屉
    showDrawer() {
      Promise.all([this.getPlateList(), this.getAnalysisList()]).then(res => {
        this.analysisList = [
          ...res[1],
          ...res[0]
            .filter(
              item =>
                item.businessSegments !==
                  (!this.isLocal
                    ? this.$t("4155039a-b280-4271-8216-508465ce4f76")
                    : "全部") &&
                !res[1]
                  .map(zitem => zitem.businessSegments)
                  .includes(item.value)
            )
            .map(item => {
              return {
                businessSegments: item.value,
                reportId: "2.0-2",
                reportName: "云图概览通用报表"
              };
            })
        ];
        this.$refs["AnalysisDrawer"].show();
      });
    },
    // 获取该公司底下的版块列表
    getPlateList() {
      return new Promise(resolve => {
        request(`/api/smc2/cardInfo/searchSEG?signOrg=${this.signOrgId}`)
          .then(res => {
            resolve(res && Array.isArray(res) ? res : []);
          })
          .catch(() => {});
      });
    },
    // 获取专项分析列表
    getAnalysisList() {
      return new Promise(resolve => {
        request(
          `/api/smc2/cardInfo/searchSpecialAnalysis?signOrgId=${this.signOrgId}`
        ).then(res => {
          resolve(res.rows && Array.isArray(res.rows) ? res.rows : []);
        });
      });
    },
    // 维度下拉框选中转obj
    wdSelectedToObj(value) {
      const obj = {};
      value.forEach(item => {
        let iAr = item.split("-");
        // eslint-disable-next-line no-prototype-builtins
        if (obj.hasOwnProperty(iAr[1])) {
          obj[iAr[1]].push(iAr[0]);
        } else {
          obj[iAr[1]] = [iAr[0]];
        }
      });
      return obj;
    },
    // 把维度下拉框选中值进行排列组合
    ermutation(source) {
      const arr = Object.keys(source);
      const result = [];
      const _result = {};
      const convert = (arr, index) => {
        if (source[arr[index]]) {
          for (let i = 0; i < source[arr[index]].length; i++) {
            if (source[arr[index]][i]) {
              _result[arr[index]] = source[arr[index]][i];
              if (index === arr.length - 1) {
                result.push(Object.values(_result));
              } else {
                convert(arr, index + 1);
              }
            }
          }
        }
      };
      convert(arr, 0);
      return result;
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage2 {
  &.classic-style {
    ._plate {
      padding-top: 16px;
      padding-bottom: 20px;
      ._left {
        .filter {
          & > span {
            margin-right: 16px;
          }
          height: 32px;
          & > div {
            height: 32px;
            box-sizing: border-box;
            padding: 3px;
            background-color: rgb(242, 243, 245);
            span {
              display: block;
              &._item {
                height: 26px;
                font-size: 14px;
                line-height: 26px;
                padding: 0 12px;
                background: transparent;
                color: rgb(78, 89, 105);
                border-radius: 2px;
                cursor: pointer;
                transition: all ease-in-out 0.3s;
                cursor: pointer;
                position: relative;
                &:not(:last-child):before {
                  content: "";
                  width: 1px;
                  height: 14px;
                  background-color: rgb(229, 230, 235);
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                  right: 0;
                  z-index: 1;
                }
                &:hover {
                  background: #dfe1e8;
                }
                &.active {
                  background: #fff;
                  color: rgb(0, 170, 166);
                  &::before {
                    background-color: transparent;
                  }
                  &::after {
                    content: "";
                    width: 1px;
                    height: 14px;
                    background-color: rgb(242, 243, 245);
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    left: -1px;
                    z-index: 2;
                  }
                }
              }
            }
          }
        }
      }
      .analysis {
        width: 155px;
        height: 32px;
        border-radius: 4px;
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        box-sizing: border-box;
        background-image: url("~@/assets/images/plate_bg2.png");
        padding-left: 16px;
        color: #ffffff;
        line-height: 32px;
        cursor: pointer;
      }
    }
  }
  &.hisense-style {
    &.dark {
      ._plate {
        background-color: #313335;
        color: #9e9e9e;
        ._left .filter > div ._item {
          color: #f0f5ff;
          background-color: #222325 !important;
        }
      }
    }
    ._plate {
      padding-bottom: 20px;
      ._left {
        .filter {
          & > span {
            margin-right: 8px;
          }
          & > div {
            ._item {
              cursor: pointer;
              font-size: 14px;
              color: #1d2129;
              display: block;
              padding: 0 10px;
              height: 32px;
              line-height: 32px;
              background: #f2f3f5;
              border-radius: 3px;
              &:not(:last-child) {
                margin-right: 16px;
              }
              &.active {
                color: #00aaa6;
                background-color: #e7f8f7;
              }
            }
          }
        }
      }
      .analysis {
        height: 32px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        color: #00aaa6;
        border-radius: 3px;
        border: 1px solid #00aaa6;
        line-height: 32px;
        font-size: 14px;
        img {
          display: block;
          width: 13px;
          height: 13px;
          margin-left: 8px;
        }
        cursor: pointer;
      }
    }
  }
  ._plate {
    justify-content: space-between;
    ._left {
      display: flex;
      align-items: center;
      .input-filter {
        width: 138px;
        margin-left: 10px;
        position: relative;
        .placeholder {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          height: 22px;
          color: rgba(133, 144, 156, 1);
          font-family: PingFang SC;
          font-size: 14px;
          z-index: 2;
        }
      }
    }
  }
}
</style>
