<!--
 * @Description: 批量更新
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-09-22 17:40:50
-->
<template>
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">11</a-button> -->
    <a-button
      :type="'default'"
      :disabled="!selectedRows.length"
      :size="size"
      @click="btClick"
    >
      批量更新
    </a-button>
    <modal-copy ref="modal-copy" @fetchData="fetchData"></modal-copy>
  </div>
</template>
<script>
import ModalCopy from "./modal-copy.vue";
import { showAlias } from "@/utils/intl.js";
export default {
  components: { ModalCopy },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
    selectedRows: Array
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      const selectedRowKeys = this.selectedRows.map(item => item.id);
      // const selectedRowKeys = [3263, 3262];
      this.$refs["modal-copy"].show(selectedRowKeys);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {}
      });
    }
  }
};
</script>
