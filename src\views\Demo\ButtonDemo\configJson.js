/*
 * @Description: 组件配置描述json
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-07 15:58:35
 * @LastEditors: yuy<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-01-07 16:21:23
 */
const comJson = {
  json: [
    {
      title: "基础配置",
      type: "collapse",
      coms: [
        {
          type: "input",
          col: "descript",
          label: "按钮描述",
          tips: "国际化Key",
          props: {
            placeholder: "国际化Key"
          }
        },
        {
          type: "radio",
          col: "size",
          label: "按钮尺寸",
          props: {
            // 选项
            options: [
              {
                key: "small",
                value: "小"
              },
              {
                key: "default",
                value: "默认"
              },
              {
                key: "large",
                value: "大"
              }
            ]
          }
        },
        {
          type: "icon",
          col: "icon",
          label: "按钮图标",
          props: {}
        },
        {
          type: "radio",
          col: "btntype",
          label: "按钮类型",
          props: {
            // 选项
            options: [
              {
                key: "primary",
                value: "主按钮"
              },
              {
                key: "default",
                value: "次按钮"
              },
              {
                key: "dashed",
                value: "虚线按钮"
              },
              {
                key: "danger",
                value: "危险按钮"
              },
              {
                key: "link",
                value: "链接按钮"
              }
            ]
          }
        },
        {
          type: "radio",
          col: "shape",
          label: "按钮形状",
          props: {
            // 选项
            options: [
              {
                key: "default",
                value: "默认"
              },
              {
                key: "circle",
                value: "圆形"
              },
              {
                key: "round",
                value: "环形"
              }
            ]
          }
        },
        {
          type: "radio",
          col: "block",
          label: "是否占满容器全部宽度",
          props: {
            // 选项
            options: [
              {
                key: "false",
                value: "否"
              },
              {
                key: "true",
                value: "是"
              }
            ]
          }
        }
      ]
    },
    {
      title: "事件配置",
      type: "collapse",
      coms: [
        {
          type: "event",
          col: "events",
          label: "事件",
          props: {
            eventList: [
              {
                name: "按钮事件", // 操作名称
                method: "handleClick" // 方法
              }
            ]
          }
        }
      ]
    }
  ],
  props: {
    descript: "",
    btntype: "primary",
    size: "default",
    type: "primary",
    shape: "default",
    icon: "",
    block: "false"
  }
};
export default comJson;
