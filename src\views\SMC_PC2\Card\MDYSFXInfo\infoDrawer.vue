<!--
 * @Description: 卡片详情
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 10:45:41
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-10 10:21:47
-->
<template>
  <div>
    <template v-if="skinStyle().includes('classic-style')">
      <a-drawer
        placement="right"
        width="400px"
        :visible="visible"
        :closable="false"
        class="MDYSFXDrawerWrap"
      >
        <!-- 线上运行 -->
        <component
          :is="compName"
          v-bind="$attrs"
          :dataItem="dataItem"
          @close="close"
        ></component>
        <!-- 本地调试 -->
        <!-- <MMYSFXInfo v-bind="$attrs" :dataItem="dataItem" @close="close" /> -->
      </a-drawer>
    </template>
    <template v-else>
      <a-modal
        class="MDYSFXModalWrap"
        v-model="visible"
        :title="null"
        :closable="false"
        :footer="null"
        width="500px"
      >
        <!-- 线上运行 -->
        <component
          :is="compName"
          v-bind="$attrs"
          :dataItem="dataItem"
          @close="close"
        ></component>
        <!-- 本地调试 -->
        <!-- <MMYSFXInfo v-bind="$attrs" :dataItem="dataItem" @close="close" /> -->
      </a-modal>
    </template>
  </div>
</template>
<script>
import { publicPath } from "@/utils/utils.js";
// import MMYSFXInfo from "./info.vue"; // 本地调试
export default {
  // components: { MMYSFXInfo }, // 本地调试
  inheritAttrs: true,
  inject: ["skinStyle"],
  data() {
    return {
      indexMDYSFXInfoJSUrl:
        (window.location.host.includes("localhost")
          ? "http://smc.devapps.hisense.com"
          : "") + "/minio/mombucket/IndexMDYSFXInfo.umd.min.1.0.js",
      visible: false,
      dataItem: {},
      compName: ""
    };
  },
  mounted() {
    if (!window["IndexMDYSFXInfo"]) {
      const script = document.createElement("script");
      let fileUrl = this.indexMDYSFXInfoJSUrl;
      if (this.indexMDYSFXInfoJSUrl.indexOf("http") === -1) {
        fileUrl = `${publicPath}${this.indexMDYSFXInfoJSUrl}`;
      }
      script.src = fileUrl + `?t=${Date.now()}`;
      script.onload = () => {
        const exportCom = window["IndexMDYSFXInfo"].default;
        this.compName = exportCom.myCom;
      };
      document.body.appendChild(script);
    } else {
      const exportCom = window["IndexMDYSFXInfo"].default;
      this.compName = exportCom.myCom;
    }
  },
  methods: {
    /**
     * @description: 打开抽屉
     * @param {Object} item 指标详情
     */
    show(item) {
      this.dataItem = item;
      this.visible = true;
    },
    // 关闭抽屉
    close() {
      this.dataItem = {};
      this.visible = false;
    }
  }
};
</script>
<style lang="less">
.MDYSFXDrawerWrap {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  .ant-drawer-content {
    height: 100vh !important;
    overflow: hidden !important;
    .ant-drawer-body {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 0 !important;
      .__center {
        flex: 1;
        overflow-y: auto;
        padding: 0 24px;
        margin-bottom: 10px;
      }
    }
  }
  .ant-drawer-body {
    height: 100%;
    overflow-y: auto;
    position: relative;
  }
}
.MDYSFXModalWrap {
  .ant-modal-body {
    padding: 0;
    height: 500px;
  }
}
</style>
