<template>
    <div class="boot-console org-manage-page">
        <div class="_right" v-if="activeOrgTreeId">
            <div class="_opera-area">
                <div class="__l">
                    <div class="dhrBox">
                        <div class="_title" style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <a-button type="primary" @click="TBDhrOrg" title="同步DHR">
                                    <a-icon type="sync" :spin="startMatchFullCode" />
                                    <!-- {{ startMatchFullCode ? "正在同步...." : "同步DHR" }} -->
                                </a-button>
                            </div>
                            <span style="margin: 0 10px;text-align: center;">DHR组织层级</span>
                            <div style="display: flex; gap: 8px;">
                                <a-button type="primary" title="取消选择" :disabled="dhrCheckedKeys.checked.length === 0"
                                    @click="publicFun('cancelSelect')">
                                    <a-icon type="stop" />
                                </a-button>
                                <a-button type="primary" title="下级添加" class="clear-select" @click="publicFun('zj')"
                                    :disabled="ytSelectedKeys.length === 0 ||
                                        dhrCheckedKeys.checked.length === 0 ||
                                        !searchTime"><a-icon type="enter" /></a-button>
                            </div>
                        </div>
                        <a-select placeholder="请输入" showSearch :value="dhrInputValue"
                            style="width: 100%;margin-bottom: 5px;" :default-active-first-option="false"
                            :show-arrow="false" :filter-option="false" :not-found-content="null"
                            @search="handleDhrSelectSearch" @change="handleDhrSelectChange">
                            <a-select-option v-for="d in dhrSelectDataList" :key="d.fullCode">
                                {{ `${d.name}(${d.code})` }}
                            </a-select-option>
                        </a-select>
                        <a-spin :spinning="dhrDataLoading">
                            <a-tree class="org-list-tree" style="width: 100%;" checkable blockNode checkStrictly
                                :expanded-keys="dhrExpandedKeys" :selected-keys="dhrSelectedKeys"
                                v-model="dhrCheckedKeys" :tree-data="dhrOrgList" @select="dhrTreeSelect"
                                @check="dhrTreeCheck" @expand="onDhrExpand">
                                <span v-for="item in dhrStateOrgList" :key="item.key" :slot="item.slotTitle">{{
                                    item.realTitle }}
                                    <span style="font-size: 10px;color: red;">{{
                                        item.state
                                        }}</span></span>
                            </a-tree>
                        </a-spin>
                    </div>
                    <div class="fractoryBox">
                        <div class="_title" style="display: flex; justify-content: space-between; align-items: center;">
                            <a-button type="primary" @click="getFactoryTreeData(true)" title="同步工厂">
                                <a-icon type="sync" :spin="factoryTreeDataLoading" />
                            </a-button>
                            <span>工厂建模组织层级</span>
                            <div style="display: flex; gap: 8px;">
                                <a-button type="primary" title="取消选择" class="clear-select" @click="factoryClearSelect"
                                    :disabled="!factoryCheckedKeys.length"><a-icon type="stop" /></a-button>
                                <a-button type="primary" title="下级添加" class="clear-select"
                                    @click="addFactoryItemToYtTree('factory')"
                                    :disabled="!factoryCheckedKeys.length || ytSelectedKeys.length === 0"><a-icon
                                        type="enter" /></a-button>
                            </div>
                        </div>
                        <a-spin :spinning="factoryTreeDataLoading">
                            <a-tree class="org-list-tree" style="width: 100%;" checkable blockNode checkStrictly
                                :expanded-keys="factoryExpandedKeys" v-model="factoryCheckedKeys"
                                :tree-data="factoryTreeData" @expand="onFactoryExpand" @check="onFactoryTreeCheck"
                                :replace-fields="replaceFactoryFields">
                            </a-tree>
                        </a-spin>
                    </div>
                </div>
                <div class="__r">
                    <div class="ytBox">
                        <div class="_title" style="display: flex; flex-direction: column; gap: 8px; padding: 8px 16px;">
                            <span class="title-text">云图组织层级</span>
                            <div style="display: flex; gap: 8px;justify-content: space-evenly;">
                                <a-button type="primary" @click="postOrg" title="机构推送">
                                    <a-icon type="cloud-upload" v-if="!postOrgLoading" />
                                    <a-icon type="loading" v-if="postOrgLoading" />
                                </a-button>
                                <a-button type="primary" title="新增" :disabled="ytSelectedKeys.length === 0 ||
                                    ytSelectedKeys[0] === 'H' ||
                                    !searchTime" @click="publicFun('cm')">
                                    <a-icon type="plus" />
                                </a-button>
                                <a-button type="primary" title="删除" :disabled="ytSelectedKeys.length === 0 ||
                                    ytSelectedKeys[0] === 'H' ||
                                    !searchTime" @click="publicFun('delete')">
                                    <a-icon type="delete" />
                                </a-button>
                                <a-button type="primary" title="编辑" :disabled="ytSelectedKeys.length === 0 ||
                                    ytSelectedKeys[0] === 'H' ||
                                    !searchTime" @click="publicFun('update')">
                                    <a-icon type="edit" />
                                </a-button>
                                <a-button type="primary" title="调整层级" :disabled="ytSelectedKeys.length === 0 ||
                                    ytSelectedKeys[0] === 'H' ||
                                    !searchTime" @click="publicFun('adjust')">
                                    <a-icon type="bars" />
                                </a-button>
                                <div class="date-picker">
                                    <a-date-picker :value="searchTime" placeholder="选择时间" style="flex: 1;width:140px;"
                                        valueFormat="YYYY-MM-DD" @change="onDateChange" format="YYYY-MM-DD"
                                        :size="'large'"  />
                                        <!--  -->
                                </div>
                                <!-- <a-button type="primary" title="停用" :disabled="ytSelectedKeys.length === 0 ||
                  ytSelectedKeys[0] === 'H' ||
                  !searchTime" @click="publicFun('stop')">
                  <a-icon type="poweroff" />
                </a-button> -->
                            </div>
                        </div>
                        <a-select show-search :value="ytInputValue" placeholder="请输入"
                            style="width: 100%;margin-bottom: 5px;" :default-active-first-option="false"
                            :show-arrow="false" :filter-option="false" :not-found-content="null"
                            @search="handleYTSelectSearch" @change="$event => handleYTSelectChange($event)">
                            <a-select-option v-for="d in ytSelectDataList" :key="d.fullCode">
                                {{ `${d.name}(${d.code}) 过期时间:${d.endDate.split(" ")[0]}` }}
                            </a-select-option>
                        </a-select>
                        <a-spin :spinning="ytDataLoading">
                            <a-tree class="org-list-tree" style="width: 100%;" blockNode show-line
                                :expanded-keys="ytExpandedKeys" :selected-keys="ytSelectedKeys" :tree-data="ytOrgList"
                                @select="ytTreeSelect" @expand="onYTExpand" @drop="onYTDrop">
                                <span v-for="item in ytStateOrgList" :key="item.id" :slot="item.slotTitle">{{
                                    item.realTitle }}
                                    <span style="font-size: 10px;color: red;">{{
                                        item.state === "N" ? "失效" : ""
                                        }}</span></span>
                            </a-tree>
                        </a-spin>
                    </div>
                </div>

            </div>
        </div>
        <Modal ref="modal" :activeOrgTreeId="activeOrgTreeId" @ok="modalOk" />
        <OrgModal ref="org-modal" @ok="getOrgViewList" />
        <TreeSelect ref="tree-select" :activeOrgTreeId="activeOrgTreeId" @choose="selectModalOk"
            :searchTime="searchTime" />
        <!--树形结构新增的层级选择 -->
        <a-modal title="下级添加" v-model="addNextLevelFlag" @ok="addNextLevelModalOk" @cancel="handleCancelAddNextLevel"
            width="1000px">
            <a-alert message="请为每个已选择节点指定层级" banner />
            </br>

            <div v-for="node in curAddNextLevelTreeName === 'dhr' ? originSelectDataArr : factoryCheckedTree"
                :key="node.id">
                <tree-node :node="node" :dict="dict['mom-smc2Level']" @update="updateNode" />
            </div>
        </a-modal>

    </div>
</template>
<script>
import request from "../../../utils/requestHttp";
import moment from "moment";
import uniqBy from "lodash/uniqBy";
import Modal from "./modal.vue";
import TreeSelect from "./tree-select.vue";
import OrgModal from "./orgModal.vue";
const DHRRquestUrl1 = "/api/smc2/treeOrg/searchTopLevel"; // 组织树顶级查询(左侧DHR)
const DHRRquestUrl2 = "/api/smc2/treeOrg/searchNextLevel"; // 组织树逐级查询 (左侧DHR)
const YTRquestUrl1 = "/api/smc2/treeOrg/searchTopLevelCM"; // 组织树顶级查询(右侧云图)
const YTRquestUrl2 = "/api/smc2/treeOrg/searchNextLevelCM"; // 组织树逐级查询 (右侧云图)
import debounce from "lodash/debounce";
import TreeNode from "./dialogTreeSelect.vue";




export default {
    components: { Modal, OrgModal, TreeSelect, TreeNode },
    data() {
        this.handleDhrSelectSearch = debounce(this.handleDhrSelectSearch, 800);
        this.handleYTSelectSearch = debounce(this.handleYTSelectSearch, 800);

        return {
            OrgViewList: [], // 组织视图列表
            activeOrgTreeId: undefined, // 当前选中的组织视图
            searchTime: moment().format("YYYY-MM-DD"),
            // dhr组织层级相关数据
            dhrDataLoading: false,
            dhrExpandedKeys: [],
            dhrSelectedKeys: [],
            dhrCheckedKeys: {
                checked: [],
            },
            dhrOrgList: [],
            dhrOrgPPList: [],
            dhrOriginDataList: [], // 源数据集合
            dhrInputValue: undefined,
            dhrSelectDataList: [],
            originSelectDataArr: [],// 工厂树形结构新增的层级选择


            // 云图组织层级相关数据
            ytDataLoading: false,
            ytExpandedKeys: [],
            ytSelectedKeys: [],
            ytOrgList: [],
            ytOrgPPList: [],
            ytOriginDataList: [],
            ytInputValue: undefined,
            ytSelectDataList: [],
            startMatchFullCode: false,
            // ytDetailForm
            ruleFormRefVisible: false,
            ytCurSelectItem: {},
            dict: {}, //层级字典

            // 工厂树搜索
            factoryTreeDataLoading: true,
            replaceFactoryFields: {
                children: "children",
                title: "realTitle",
                key: "key",
            },
            factoryExpandedKeys: [1],
            factoryTreeData: [],
            factoryCheckedKeys: [],
            factoryCheckedTree: [],// 工厂树形结构新增的层级选择

            postOrgLoading: false,
            addNextLevelFlag: false,
            dhrModalFlag: false,
            curAddNextLevelTreeName: "",

            addRightViewType: ""
        };
    },
    computed: {
        dhrStateOrgList() {
            return uniqBy(
                this.dhrOrgPPList.filter((item) => item.state),
                "key"
            );
        },
        ytStateOrgList() {
            return uniqBy(this.ytOrgPPList, "key");
        },
        isToday() {
            return (
                !this.searchTime || this.searchTime === moment().format("YYYY-MM-DD")
            );
        },
    },
    mounted() {
        this.getOrgViewList();
        this.getDHROrgList();
        this.getDICT();
        this.getFactoryTreeData()
        // this.getYTOrgList();
    },
    methods: {
        onDateChange(date) {
            this.searchTime = date;
            this.ytExpandedKeys = [];
            this.ytSelectedKeys = [];
            this.ytOrgList = [];
            this.ytOrgPPList = [];
            this.ytOriginDataList = [];
            this.ytInputValue = "";
            this.ytSelectDataList = [];
            this.$nextTick(() => {
                this.getYTOrgList();
            });
        },
        disabledDate(current) {
            // Can not select days before today and today
            return current && current > moment().endOf("day");
        },
        // 获取视图列表
        getOrgViewList() {
            request(`/api/smc2/treeOrg/searchOrgTree`, {
                method: "POST",
            })
                .then((res) => {
                    this.OrgViewList = res || [];
                })
                .then((res) => {
                    this.OrgViewList.map((item) => {
                        this.activeOrgTreeId = item.id;
                        // 对应上面对应的_left的结构，改成mountd后自动调用
                        this.getYTOrgList();
                    });
                });
        },
        // 获取DHR组织结构列表
        getDHROrgList(id, arr) {
            this.dhrDataLoading = true;
            let realCode = "";
            let fullCode = "";
            if (id) {
                if (typeof id === 'number') {
                    realCode = this.dhrOriginDataList.find(
                        (item) => item.id == id
                    )?.code;
                    this.dhrOrgPPList.filter(
                        (item) => item.id === id
                    )[0].fullCode;
                } else {
                    realCode = this.dhrOriginDataList.find(
                        (item) => item.code == id
                    )?.code;
                    fullCode = this.dhrOrgPPList.filter(
                        (item) => item.code == id
                    )[0].fullCode;
                }
            }
            request(`${id ? DHRRquestUrl2 : DHRRquestUrl1}`, {
                method: "POST",
                body: id
                    ? {
                        superiorCode: realCode,
                        fullCode: fullCode
                    }
                    : {},
            }).then((res) => {
                let treeData = res.map((item) => {
                    // 将返回的数据处理成dataItem的格式
                    const dataItem = {
                        fullCode: item.fullCode,
                        key: item.id,
                        id: item.id,
                        code: item.code,
                        disabled: false,
                        children: [],
                        slots: {
                            title: `show-state-${item.id}`,
                        },
                        slotTitle: `show-state-${item.id}`,
                        state: item.state,
                        realTitle: `${item.name}(${item.code})`,
                    };
                    if (!item.state) {
                        dataItem["title"] = `${item.name}(${item.code})`;
                    }
                    return dataItem;
                });
                treeData = uniqBy(treeData, "key");
                // dhrOrgPPList是包含父级的扁平array origin list
                this.dhrOrgPPList = [...treeData, ...this.dhrOrgPPList];
                // 打印看了一下dhrOrgPPList和dhrOriginDataList基本没有区别
                this.dhrOriginDataList = uniqBy(
                    [...res, ...this.dhrOriginDataList],
                    "id"
                );
                // 如果是mounted没有传递父层级code，则dhrOrgList = 父层级
                if (!id) {
                    this.dhrOrgList = treeData;
                    // 将父层级id传入并下探一层
                    this.getDHROrgList(treeData[0].key);
                } else {
                    // 如果不是mounted调用，并且下探到了子级节点，name将子级别节点放到父层级节点下，并将tree展开
                    if (treeData.length) {
                        this.treeDataDeepFillArr(this.dhrOrgList, id, treeData);
                        if (typeof id === 'string') {
                            id = this.dhrOriginDataList.filter(
                                (item) => item.code === id
                            )[0].id;
                        }
                        this.dhrExpandedKeys = [...this.dhrExpandedKeys, id];
                    }
                }
                if (arr) {
                    if (arr.length > 1) {
                        arr.splice(0, 1);
                        this.getDHROrgList(arr[0], arr);
                    } else if (arr.length === 1) {
                        if (typeof arr[0] === 'string') {
                            arr[0] = this.dhrOriginDataList.filter(
                                (item) => item.code === arr[0]
                            )[0].id;
                        }
                        this.dhrSelectedKeys = [arr[0]];
                    }
                }
                this.dhrDataLoading = false;
            }).catch((err) => {
                this.dhrDataLoading = false;
                this.$message.error("获取组织结构失败,请尝试重新点击");
            });
        },
        // dhr组织树复选框
        dhrTreeCheck(checkedKeys) {
            // 解构出来的的checked是一个array,item:checkKey
            const { checked } = checkedKeys;
            this.dhrCheckedKeys = checkedKeys;
            // 设定dhr树是否可选
            if (checked.length) {
                const originDataArr = []; // dhr源数据
                this.dhrOriginDataList.forEach((item) => {
                    if (checked.includes(item.code)) {
                        originDataArr.push({ ...item });
                    }
                });
                // 打上是否顶层标签
                originDataArr.forEach((item) => {
                    if (checked.includes(item.superiorCode)) {
                        item["notTopLevel"] = true;
                    } else {
                        item["notTopLevel"] = false;
                    }
                });
                // const topOrgCode = originDataArr.filter((item) => !item.notTopLevel)[0]
                //   .code;
                // this.setDisabledTrue(topOrgCode, this.dhrOrgList);
            } else {
                // this.setDisabledFalse();
            }
        },
        // dhr搜索框搜索
        handleDhrSelectSearch(name) {
            request(`/api/smc2/treeOrg/dropDown`, {
                method: "POST",
                body: {
                    name,
                    type: "DHR",
                },
            }).then((res) => {
                this.dhrSelectDataList = res || [];
            });
        },
        // dhr搜索框改变
        handleDhrSelectChange(value) {
            this.dhrInputValue = value;
            const topDhrCode = this.dhrOrgList[0].fullCode;
            if (value.includes(topDhrCode) && topDhrCode.length < value.length) {
                let noTopDhrFullCode = value.replace(`${topDhrCode}-`, "");
                const arr = noTopDhrFullCode.split("/");
                arr.shift('H')
                this.getDHROrgList(arr[0], arr);
            }
        },
        onDhrExpand(expandedKeys) {
            this.dhrExpandedKeys = expandedKeys;
        },
        setDisabledFalse(arr = this.dhrOrgList) {
            arr.forEach((item) => {
                if (!this.dhrCheckedKeys.checked.includes(item.key)) {
                    item.disabled = false;
                }
                if (item.children && item.children.length) {
                    this.setDisabledFalse(item.children);
                }
            });
        },
        setDisabledTrue(topOrgCode, arr) {
            arr.forEach((item) => {
                if (item.key === topOrgCode) {
                    item.disabled = true;
                } else {
                    item.disabled = true;
                    item.children.length &&
                        this.setDisabledTrue(topOrgCode, item.children);
                }
            });
        },
        // DHR组织树选择
        dhrTreeSelect(selectedKeys) {
            this.dhrSelectedKeys = selectedKeys;
            selectedKeys.length && this.getDHROrgList(selectedKeys[0]);
        },
        // 获取云图组织结构列表
        getYTOrgList(parentOrgId, arr = []) {
            // parentOrgCode 接收的是带版本号的code或者空 定义一个realCode来查找到数据的真实dhrCode不带版本号的
            let realCode = "";
            if (parentOrgId) {
                const ytOriginData = this.ytOriginDataList.filter(
                    (item) => {
                        if (typeof parentOrgId === 'string') {
                            return item.code === parentOrgId
                        } else {
                            return item.id === parentOrgId
                        }
                    }
                )[0];
                realCode = ytOriginData.code;
            }
            this.ytDataLoading = true;
            request(`${parentOrgId ? YTRquestUrl2 : YTRquestUrl1}`, {
                method: "POST",
                body: parentOrgId
                    ? {
                        superiorCode: realCode,
                        fullCode: this.ytOrgPPList.filter(
                            (item) => {
                                if (typeof parentOrgId === 'string') {
                                    return item.code === parentOrgId
                                } else {
                                    return item.id === parentOrgId
                                }
                            }
                        )[0].fullCode,
                        treeType: this.activeOrgTreeId,
                        endDate: this.searchTime || "",
                    }
                    : {
                        treeType: this.activeOrgTreeId,//首次请求
                        endDate: this.searchTime || "",
                    },
            }).then((res) => {
                let treeData = res.map((item) => {
                    return {
                        title: `${item.name}(${item.code})`,
                        fullCode: item.fullCode,
                        disabled: false,
                        superiorCode: item.superiorCode,
                        key: item.id,
                        id: item.id,
                        code: item.code,
                        children: [],
                        slots: {
                            title: `show-state-${item.id}`,
                        },
                        slotTitle: `show-state-${item.id}`,
                        state: item.invalid,
                        realTitle: `${item.name}(${item.code})`,
                        levelName: item.levelName,
                    };
                });
                this.ytOrgPPList = [...treeData, ...this.ytOrgPPList];
                this.ytOriginDataList = uniqBy(
                    [...res, ...this.ytOriginDataList],
                    "id"
                );
                // 首次加载两层
                if (!parentOrgId) {
                    this.ytOrgList = treeData;
                    treeData.length && this.getYTOrgList(treeData[0].id);
                } else {
                    // 展开节点
                    this.treeDataDeepFillArr(this.ytOrgList, parentOrgId, treeData);
                    if (treeData.length) {
                        this.ytExpandedKeys = [...this.ytExpandedKeys, parentOrgId];
                    }
                }
                if (arr.length) {
                    this.midWareFun(arr);
                }
                this.ytDataLoading = false;
            }).catch((err) => {
                this.ytDataLoading = false;
                this.$message.error("获取组织结构失败,请尝试重新点击");
            });
        },
        // YT组织树选择
        ytTreeSelect(selectedKeys) {
            this.ytSelectedKeys = selectedKeys;
            selectedKeys.length && this.getYTOrgList(selectedKeys[0]);
            // 拿到当前点击的节点详情
            if (selectedKeys.length) {
                // 这里判断的意义在如果取消选择数组为空下面的逻辑都会报错
                const ytOriginData = this.ytOriginDataList.filter(
                    (item) => item.code === this.ytSelectedKeys[0]
                )[0];
                this.ytCurSelectItem = ytOriginData;
            } else {
                this.ruleFormRefVisible = false;
            }
        },
        // yt搜索框搜索
        handleYTSelectSearch(name) {
            request(`/api/smc2/treeOrg/dropDown`, {
                method: "POST",
                body: {
                    name,
                    type: "CM",
                    treeType: this.activeOrgTreeId,
                },
            }).then((res) => {
                this.ytSelectDataList = res || [];
            });
        },
        // yt搜索框改变
        handleYTSelectChange(value) {
            let topYTCode = this.ytOrgList[0].fullCode;
            this.ytInputValue = value;
            if (value.includes(topYTCode) && topYTCode.length < value.length) {
                let noTopYTFullCode = value.replace(`${topYTCode}/`, "");
                const arr = noTopYTFullCode.split("/");
                let newArr = [];
                arr.forEach((item) => {
                    newArr.push(`${topYTCode}/${item}`);
                    topYTCode = `${topYTCode}/${item}`;
                });
                this.midWareFun(newArr);
            }
        },
        midWareFun(arr) {
            if (arr.length) {
                const ytOriginData = this.ytOriginDataList.filter(
                    (item) => {
                        if (typeof arr[0] === 'string') {
                            return item.fullCode === arr[0]
                        } else {
                            return item.id === arr[0]
                        }
                    }
                )[0];
                if (arr.length === 1 && ytOriginData) {
                    this.ytSelectedKeys = [ytOriginData.id];
                }
                arr.splice(0, 1);
                ytOriginData && this.getYTOrgList(ytOriginData.id, arr);
            }
        },
        onYTExpand(expandedKeys) {
            this.ytExpandedKeys = expandedKeys;
        },
        onYTDrop(info) {
            const _this = this;
            const dropKey = info.node.eventKey;//弹框中选的
            const dragKey = info.dragNode.eventKey;//云图树选的
            const dragParentCode = this.ytOriginDataList.filter(
                (item) => item.id === dragKey
            )[0].superiorCode;
            const dragParentOrg = this.ytOriginDataList.filter(
                (item) => {
                    if (typeof dragParentCode === 'string') {
                        return item.code === dragParentCode
                    } else {
                        return item.id === dragParentCode
                    }
                }
            )[0];
            // info.node["originData"]是用来提示的，从tree-select组件内获取的
            const dropOrg = info.node["inModalSelect"]
                ? info.node["originData"]
                : this.ytOriginDataList.filter((item) => item.id === dropKey)[0];
            const dropOrgParent = this.ytOriginDataList.filter((item) => item.id === dropOrg.parentId)[0];//如果是同级添加则需要获取父级的父级
            const dragOrg = this.ytOriginDataList.filter(
                (item) => item.id === dragKey
            )[0];
            this.$confirm({
                title: "提示",
                content: `确定要将"${dragOrg.name}(${dragOrg.code})"组织移动到"${dropOrg.name}(${dropOrg.code})"组织${info.node.isSameLevel ? "同级" : "下级"}吗？`,
                onOk() {
                    // 展开节点
                    return new Promise((resolve) => {
                        // 如果是弹框调整层级且选择了有效起止时间
                        if (info.node["inModalSelect"] && info.node["switchSign"]) {
                            // console.log({
                            // parentFullCode: dropOrg.fullCode,
                            // childFullCode: dragOrg.fullCode,
                            // code: dragOrg.code,
                            // beginDate: info.node.beginDate,
                            // endDate: info.node.endDate,
                            // });
                            request(`/api/smc2/treeOrg/updateCmTree`, {
                                method: "POST",
                                body: {
                                    id: dragOrg.id,
                                    code: dragOrg.code,
                                    fullCode: dragOrg.fullCode,
                                    name: dragOrg.name,
                                    levelName: dragOrg.levelName,
                                    levelCode: dragOrg.levelCode,
                                    beginDate: info.node.beginDate,
                                    endDate: info.node.endDate,
                                    parentFullCode: info.node.isSameLevel ? dropOrgParent.fullCode : dropOrg.fullCode,
                                    superiorCode: info.node.isSameLevel ? dropOrgParent.code : dropOrg.code,
                                    fullName: dragOrg.fullName,
                                    parentFullName: info.node.isSameLevel ? dropOrgParent.fullName : dropOrg.fullName
                                },
                            }).then((res) => {
                                if (!res) {
                                    // 如果本组件树列表中加载过得话重新获取本组件的云图列表
                                    if (
                                        _this.ytOriginDataList.filter(
                                            (item) => item.id === dropOrg.id
                                        ).length
                                    ) {
                                        _this.getYTOrgList(info.node.isSameLevel ? dropOrgParent.id : dropOrg.id);
                                    }
                                    _this.getYTOrgList(dragParentOrg.id);
                                } else {
                                }
                                resolve();
                            });
                        } else {
                            // else {
                            // const data2 = {
                            //   id: dropOrg.id,
                            //   treeType: _this.activeOrgTreeId,
                            //   code: dropOrg.code,
                            //   dhrCode: dropOrg.dhrCode,
                            //   fullCode: dropOrg.fullCode,
                            //   hid: dropOrg.hid,
                            // };
                            const data1 = {
                                id: dragOrg.id,
                                code: dragOrg.code,
                                fullCode: dragOrg.fullCode,
                                name: dragOrg.name,
                                levelName: dragOrg.levelName,
                                levelCode: dragOrg.levelCode,
                                beginDate: dragOrg.beginDate,
                                endDate: dragOrg.endDate,
                                parentFullCode: info.node.isSameLevel ? dropOrgParent.fullCode : dropOrg.fullCode,
                                superiorCode: info.node.isSameLevel ? dropOrgParent.code : dropOrg.code,
                                fullName: dragOrg.fullName,
                                parentFullName: info.node.isSameLevel ? dropOrgParent.fullName : dropOrg.fullName,
                            };
                            request("/api/smc2/treeOrg/updateCmTree", {
                                method: "POST",
                                body: data1,
                            }).then((res) => {
                                if (!res) {
                                    // 如果本组件树列表中加载过得话重新获取本组件的云图列表
                                    if (
                                        _this.ytOriginDataList.filter(
                                            (item) => item.id === dropOrg.id
                                        ).length
                                    ) {
                                        _this.getYTOrgList(info.node.isSameLevel ? dropOrgParent.id : dropOrg.id);
                                    }
                                    _this.getYTOrgList(dragParentOrg.id);
                                }
                                resolve();
                            });
                        }
                    });
                },
                onCancel() {
                    _this.$message.info("已取消操作");
                },
            });
        },
        // 树形组件深层次查找并赋值
        treeDataDeepFillArr(treeData, orgId, children) {
            for (let i = 0; i < treeData.length; i++) {
                const element = treeData[i];
                if (element.id === orgId || element.code === orgId) {
                    treeData[i].children = children;
                    break;
                } else {
                    this.treeDataDeepFillArr(element.children, orgId, children);
                }
            }
        },
        modalOk(type) {
            if (type === "add") {
                this.getYTOrgList(this.ytSelectedKeys[0]);
            } else {
                const ytOriginData = this.ytOriginDataList.filter(
                    (item) => {
                        if (typeof this.ytSelectedKeys[0] === 'string') {
                            return item.code === this.ytSelectedKeys[0]
                        } else {
                            return item.id === this.ytSelectedKeys[0]
                        }
                    }
                )[0];
                this.getYTOrgList(ytOriginData.parentId);
            }
        },
        publicFun(type) {
            // if (!this.isToday) {
            //   this.$message.warning("请清空时间后操作！");
            //   return;
            // }
            // dhr组织树勾选数据
            if (["pj", "zj"].includes(type)) {
                const { checked } = this.dhrCheckedKeys;
                const originDataArr = []; // dhr源数据
                this.dhrOriginDataList.forEach((item) => {
                    if (checked.includes(item.id)) {
                        originDataArr.push({ ...item });
                    }
                });
                const ytOriginData = this.ytOriginDataList.filter(
                    (item) => item.id === this.ytSelectedKeys[0]
                )[0];
                const {
                    fullCode,
                    dhrSuperiorCode,
                    dhrCode,
                    code,
                    parentId,
                    superiorCode
                } = ytOriginData;
                let realFullCode = ""; // 定义真实fullCode变量（根据pj/zj判断）
                if (type === "zj") {
                    this.curAddNextLevelTreeName = 'dhr'
                    realFullCode = fullCode;
                } else {
                    const parentOrgData = this.ytOriginDataList.filter(
                        (item) => item.code === superiorCode
                    )[0];
                    // 获取父级组织全路径
                    realFullCode = parentOrgData.fullCode;
                }
                let topDhrOrgFullName = ""; // 定义最顶层FullName变量
                // 打上是否顶层标签
                originDataArr.forEach((item) => {
                    if (checked.includes(item.parentId)) {
                        item["notTopLevel"] = true;
                    } else {
                        if (item.fullCode.includes("/")) {
                            const arr = item.fullCode.split("/");
                            topDhrOrgFullName = arr.slice(0, arr.length - 1).join("/");
                        }
                        item["notTopLevel"] = false;
                    }
                });
                originDataArr.forEach((item) => {
                    if (!item.notTopLevel) {
                        item.superiorCode = type === "pj" ? superiorCode : code;
                        item.dhrSuperiorCode = type === "pj" ? dhrSuperiorCode : dhrCode;
                    }
                    if (topDhrOrgFullName) {
                        item.fullCode = item.fullCode.replace(
                            topDhrOrgFullName,
                            realFullCode
                        );
                    } else {
                        item.fullCode = `${realFullCode}/${item.fullCode}`;
                    }
                    item["treeType"] = this.activeOrgTreeId;
                });
                this.originSelectDataArr = originDataArr.reduce((acc, item) => {
                    if (!item.notTopLevel) {
                        // 递归构建子树
                        const buildTree = (node) => {
                            const children = originDataArr.filter(child =>
                                child.notTopLevel && child.parentId === node.id
                            ).map(child => buildTree(child));

                            return {
                                ...node,
                                children
                            };
                        };

                        acc.push(buildTree(item));
                    }
                    return acc;
                }, []);
                this.addNextLevelFlag = true;

            } else if (type === "cancelSelect") {
                this.dhrCheckedKeys = {
                    checked: [],
                };
                this.setDisabledFalse();
            } else if (["cm", "update", "delete"].includes(type)) {
                const ytOriginData = this.ytOriginDataList.filter(
                    (item) => item.id === this.ytSelectedKeys[0]
                )[0];
                const parentOrg = this.ytOriginDataList.filter(
                    (item) => item.id === ytOriginData.parentId
                )[0];
                const {
                    name,
                    fullCode,
                    fullName,
                    code,
                    beginDate,
                    endDate,
                    dhrSuperiorCode,
                    dhrCode,
                    superiorCode,
                    hid,
                    sapCode,
                    levelName,
                    levelCode,
                    id,
                    angleOfView
                } = ytOriginData;
                if (type !== "delete") {
                    const addData = {
                        parentName: name,
                        parentFullCode: fullCode,
                        parentCode: code,
                        parentDhrCode: dhrCode,
                        beginDate,
                        endDate,
                        parentDhrSuperiorCode: dhrSuperiorCode,
                        levelCode: levelCode || 0,
                        id,
                        fullName,
                        angleOfView: ""
                    };
                    const editData = {
                        code,
                        name,
                        fullCode,
                        fullName,
                        parentFullName: parentOrg.fullName,
                        beginDate,
                        endDate,
                        dhrCode,
                        dhrSuperiorCode,
                        hid,
                        id,
                        levelName,
                        levelCode: levelCode || 0,
                        sapCode,
                        angleOfView
                    };
                    if (type === "cm") {
                        const _this = this;
                        this.$confirm({
                            title: "提示",
                            content: `确定要在"${ytOriginData.name}(${ytOriginData.code})"底下新增组织吗？`,
                            onOk() {
                                return new Promise((resolve) => {
                                    _this.$refs.modal.show(addData, false);
                                    resolve();
                                });
                            },
                            onCancel() {
                                _this.$message.info("已取消操作");
                            },
                        });
                    } else {
                        this.$refs.modal.show(editData, true);
                    }
                } else {
                    const _this = this;
                    this.$confirm({
                        title: "提示",
                        content: `确定要删除"${ytOriginData.name}(${ytOriginData.code})"组织吗？`,
                        onOk() {
                            return new Promise((resolve) => {
                                request(`/api/smc2/treeOrg/deleteCM`, {
                                    method: "POST",
                                    body: {
                                        fullCode,
                                        treeType: _this.activeOrgTreeId,
                                    },
                                }).then(() => {
                                    _this.getYTOrgList(superiorCode);
                                    resolve();
                                });
                            });
                        },
                        onCancel() {
                            _this.$message.info("已取消操作");
                        },
                    });
                }
            } else if (type === "adjust") {
                this.$refs["tree-select"].showDialog(
                    this.ytOriginDataList.filter(
                        (item) => {
                            if (typeof this.ytSelectedKeys[0] === 'string') {
                                return item.code === this.ytSelectedKeys[0]
                            } else {
                                return item.id === this.ytSelectedKeys[0]
                            }
                        }
                    )[0]
                );
            }
        },
        dhrModalOk() {
            request("/api/smc2/treeOrg/saveTree1", {
                method: "POST",
                body: {
                    list: this.originSelectDataArr,
                    id: ytOriginData.id,
                },
            }).then((res) => {
                if (res) {
                    _this.$message.error(res.message);
                } else {
                    _this.getYTOrgList(type === "pj" ? superiorCode : code);
                }

            });
        },
        delOrgTree(id, name) {
            request(`/api/smc2/treeOrg/updateCMOrgTree`, {
                method: "POST",
                body: {
                    id,
                    name,
                    delFlag: "1",
                },
            }).then(() => {
                this.getOrgViewList();
                this.activeOrgTreeId = "";
            });
        },
        TBDhrOrg() {
            request(`/api/smc2/treeOrg/matchFullCode`, {
                method: "POST",
            }).then(() => {
                this.startMatchFullCode = true;
                this.startSearchResult();
            });
        },
        postOrg() {
            this.postOrgLoading = true;
            request(`/api/smc2/treeOrg/sendMessage`, {
                method: "GET",
            }).then((res) => {
                if (res && res.data.code === '500') {
                    this.$message.error(res.data.msg);
                } else {
                    this.$message.success("推送成功");
                }
            }).finally(() => {
                this.postOrgLoading = false;
            })
        },
        startSearchResult() {
            setTimeout(() => {
                request(`/api/smc2/treeOrg/asyResult`, {
                    method: "POST",
                }).then((res) => {
                    if (res.result === "success") {
                        this.$notification["success"]({
                            message: "DHR组织同步执行完成",
                            duration: null,
                        });
                        this.startMatchFullCode = false;
                    } else {
                        this.startSearchResult();
                    }
                });
            }, 3000);
        },
        selectModalOk(e) {
            const info = {
                node: {
                    eventKey: e.dhrCode,
                    inModalSelect: true,
                    ...e,
                },
                dragNode: {
                    eventKey: this.ytSelectedKeys[0],
                },
            };
            this.onYTDrop(info);
        },
        // 获取当前层级->展示哪个form
        getLevelForShowWhichForm(levelName) {
            if (!levelName) return;
            let showForm = "lineForm";
            if (this.levelNameListOverLine.indexOf(levelName) !== -1) {
                showForm = "overLineForm";
            }
            if (this.levelNameBottomLine.indexOf(levelName) !== -1) {
                showForm = "bottomLineForm";
            }
            return showForm;
        },
        // 获取层级字典
        getDICT() {
            // 前人字典建立的有问题key是string，vlaue也是string
            // 1. 集团，2. 公司，3. 中心，4. 基地，5. 工厂，6. 部门，7. 科室，8. 车间，9. 二级车间，10. 线体，
            // 11. 二级线体，12. 班组，13. 大班组，14. 小班组，15. 班次 16.工段
            request(
                decodeURIComponent(
                    "/api/system/dict/type/query?types=mom-smc2Level&languageCode=zh_CN"
                )
            ).then((res) => {
                for (const key in res) {
                    if (Object.hasOwnProperty.call(res, key)) {
                        const element = res[key];
                        this.$set(this.dict, key, element);
                    }
                }
            });
        },
        // 获取工厂组织层级
        getFactoryTreeData(flag = false) {
            // /api/mom-data/momLineFactoryModel/listDataTreeByUser
            request(`/api/mom-data/momFactory/tree`, {
                method: "GET",
                // body: {
                //   treeFlag: 1
                // }
            }).then((res) => {
                if (res) {
                    this.factoryTreeData = res.map(item => {
                        const processNode = (node) => {
                            return {
                                ...node,
                                key: node.factoryModelCode,
                                title: node.factoryModelName,
                                realTitle: `${node.factoryModelName}(${node.factoryModelCode})`,
                                children: node.children ? node.children.map(child => processNode(child)) : null
                            }
                        }
                        return processNode(item)
                    })
                    this.factoryTreeDataLoading = false;
                    // 默认展开第一层
                    this.factoryExpandedKeys = this.factoryTreeData.map(item => item.key);
                    if (flag) {
                        this.$message.success("获取组织结构成功");
                    }
                } else {
                    this.$message.error("获取组织结构失败,请尝试重新点击");
                }
            }).catch((err) => {
                this.factoryTreeDataLoading = false;
                this.$message.error("获取组织结构失败,请尝试重新点击");
            });
        },
        onFactoryExpand(expandedKeys) {
            this.factoryExpandedKeys = expandedKeys;
        },
        onFactoryTreeCheck(checkedKeys) {
            if (checkedKeys.checked) {
                this.factoryCheckedKeys = checkedKeys.checked;
            } else {
                this.factoryCheckedKeys = checkedKeys;
            }
            if (this.factoryCheckedKeys.length) {
                const originDataArr = []; // dhr源数据
                const findCheckedNodes = (nodes) => {
                    nodes.forEach(node => {
                        if (this.factoryCheckedKeys.includes(node.key)) {
                            originDataArr.push({ ...node });
                        }
                        if (node.children && node.children.length) {
                            findCheckedNodes(node.children);
                        }
                    });
                };
                findCheckedNodes(this.factoryTreeData);
                // 打上是否顶层标签
                const markTopLevel = (node) => {
                    if (this.factoryCheckedKeys.includes(node.superiorCode)) {
                        node["notTopLevel"] = true;
                    } else {
                        node["notTopLevel"] = false;
                    }
                    if (node.children && node.children.length) {
                        node.children.forEach(child => markTopLevel(child));
                    }
                };
                originDataArr.forEach(item => markTopLevel(item));
                const findTopOrgCode = (nodes) => {
                    for (const node of nodes) {
                        if (!node.notTopLevel) {
                            return node.key;
                        }
                        if (node.children && node.children.length) {
                            const childResult = findTopOrgCode(node.children);
                            if (childResult) return childResult;
                        }
                    }
                    return null;
                };
                const topOrgCode = findTopOrgCode(originDataArr);
                // this.setFactoryDisabledTrue(topOrgCode, this.factoryTreeData);
            } else {
                // this.setFactoryDisabledFalse();
            }

        },
        setFactoryDisabledTrue(topOrgCode, arr) {
            const setDisabled = (nodes) => {
                if (!nodes || !nodes.length) return;

                nodes.forEach(node => {
                    if (node.key === topOrgCode) {
                        node.disabled = true;
                    } else {
                        node.disabled = true;
                        if (node.children && node.children.length) {
                            setDisabled(node.children);
                        }
                    }
                });
            };

            setDisabled(arr);
        },
        setFactoryDisabledFalse(arr = this.factoryTreeData) {
            const setNodeDisabled = (nodes) => {
                if (!nodes || !nodes.length) return;

                nodes.forEach(node => {
                    // 如果节点未被选中,设置为可用
                    if (!this.factoryCheckedKeys.includes(node.key)) {
                        node.disabled = false;
                    }
                    // 递归处理子节点
                    if (node.children) {
                        setNodeDisabled(node.children);
                    }
                });
            }

            setNodeDisabled(arr);
        },
        factoryClearSelect() {
            this.factoryCheckedKeys = [];
            this.setDisabledFalse(this.factoryTreeData);
        },
        addFactoryItemToYtTree(type = 'dhr') {
            // 获取所有选中节点的数据
            const checkedNodes = this.factoryCheckedKeys.map(key => {
                const node = this.findItem(this.factoryTreeData, key);
                return { ...node };
            });
            // 构建父子关系
            const buildTree = (nodes) => {
                const nodeMap = new Map();
                const result = [];

                // 创建节点映射
                nodes.forEach(node => {
                    nodeMap.set(node.factoryModelCode, {
                        ...node,
                        children: [],
                        // 添加后端需要的字段
                        code: node.factoryModelCode,
                        superiorCode: node.parentCode,
                        name: node.title,
                        levelCode: null,
                        levelName: '',
                        beginDate: moment().format('YYYY-MM-DD'),
                        endDate: '2999-12-31',
                        sapCode: type === 'factory' ? node.factoryModelCode : null
                    });
                });

                // 建立父子关系
                nodes.forEach(node => {
                    const currentNode = nodeMap.get(node.factoryModelCode);
                    if (node.parentCode && nodeMap.has(node.parentCode)) {
                        const parentNode = nodeMap.get(node.parentCode);
                        parentNode.children.push(currentNode);
                    } else {
                        result.push(currentNode);
                    }
                });

                // 清理空的children数组
                const cleanEmptyChildren = (node) => {
                    if (node.children && node.children.length === 0) {
                        delete node.children;
                    } else if (node.children) {
                        node.children.forEach(cleanEmptyChildren);
                    }
                };

                result.forEach(cleanEmptyChildren);
                return result;
            };
            this.factoryCheckedTree = buildTree(checkedNodes);
            this.curAddNextLevelTreeName = 'factory'
            this.addNextLevelFlag = true;
        },
        addNextLevelModalOk() {
            let treeList = []
            if (this.curAddNextLevelTreeName === 'factory') {
                treeList = this.factoryCheckedTree.map(node => {
                    return this.transformNodeToAddLevelCode(node);
                })
            } else {
                // DHR
                treeList = this.originSelectDataArr.map(node => {
                    return this.transformNodeToAddLevelCode(node);
                })
            }
            // 递归检查线体的sapCode是否为空
            const checkSapCode = (list) => {
                for (const item of list) {
                    if (item.levelName === '线体' && !item.sapCode) {
                        this.$message.error(`${item.name}的sapCode不能为空`);
                        return false;
                    }
                    if (item.children && item.children.length) {
                        const valid = checkSapCode(item.children);
                        if (!valid) return false;
                    }
                }
                return true;
            }
            // 递归检查levelName是否为空
            const checkLevelName = (list) => {
                for (const item of list) {
                    if (!item.levelName) {
                        this.$message.error(`${item.name}的层级不能为空`);
                        return false;
                    }
                    if (item.children && item.children.length) {
                        const valid = checkLevelName(item.children);
                        if (!valid) return false;
                    }
                }
                return true;
            }
            // 递归检查dhrCode是否为空
            const checkDhrCode = (list) => {
                for (const item of list) {
                    if (!item.dhrCode) {
                        this.$message.error(`${item.name}的dhrCode不能为空`);
                        return false;
                    }
                    if (item.children && item.children.length) {
                        const valid = checkDhrCode(item.children);
                        if (!valid) return false;
                    }
                }
                return true;
            }

            // if (!checkDhrCode(treeList)) {
            //   return;
            // }

            if (!checkLevelName(treeList)) {
                return;
            }
            // || !checkDhrCode(treeList)
            // !checkSapCode(treeList) ||
            if (!checkLevelName(treeList)) {
                return;
            } else {
                //跨级，同级添加，新建一条code：null
                request('/api/smc2/treeOrg/saveTree1', {
                    method: 'POST',
                    body: {
                        id: this.ytOriginDataList.filter(
                            (item) => item.id === this.ytSelectedKeys[0]
                        )[0].id,
                        list: treeList
                    }
                }).then((res) => {
                    if (!res) {
                        this.$message.success('保存成功');
                        this.getYTOrgList(this.ytOriginDataList.filter(
                            (item) => item.id === this.ytSelectedKeys[0]
                        )[0].id)
                        this.addNextLevelFlag = false;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
            }
        },
        // 查找节点
        findItem(nodes, targetKey) {
            for (const node of nodes) {
                if (node.key === targetKey) {
                    return node;
                }
                if (node.children && node.children.length) {
                    const result = JSON.parse(JSON.stringify(this.findItem(node.children, targetKey)));
                    if (result) {
                        return result;
                    }
                }
            }
            return '';
        },
        transformNodeToAddLevelCode(item) {
            const levelCodeMap = {
                '集团': '1',
                '公司': '2',
                '中心': '3',
                '基地': '4',
                '工厂': '5',
                '部门': '6',
                '科室': '7',
                '车间': '8',
                '二级车间': '9',
                '线体': '10',
                '二级线体': '11',
                '班组': '12',
                '大班组': '13',
                '小班组': '14',
                '班次': '15',
                '工段': '16',
                '分厂': '17'
            };
            const newItem = { ...item };

            if (newItem.levelName) {
                newItem.levelCode = levelCodeMap[newItem.levelName] || newItem.levelName;
            }

            if (newItem.children) {
                newItem.children = newItem.children.map(child => this.transformNodeToAddLevelCode(child));
            }

            if (newItem.angleOfView) {
                newItem.angleOfView = newItem.angleOfView.join(',')
            }
            if (newItem.loadDt) {
                newItem.loadDt = null
            }

            return newItem;
        },
        handleCancelAddNextLevel() {
            this.addNextLevelFlag = false;
        },
        updateNode(updatedNode) {
            // 递归查找并更新节点
            const updateTreeNode = (nodes, updatedNode) => {
                return nodes.map(node => {
                    if (node.code === updatedNode.code) {
                        // 保留原有的children
                        return {
                            ...updatedNode,
                            children: node.children
                        };
                    }
                    if (node.children && node.children.length) {
                        return {
                            ...node,
                            children: updateTreeNode(node.children, updatedNode)
                        };
                    }
                    return node;
                });
            };

            if (this.curAddNextLevelTreeName === 'dhr') {
                this.originSelectDataArr = updateTreeNode(this.originSelectDataArr, updatedNode);
            } else {
                this.factoryCheckedTree = updateTreeNode(this.factoryCheckedTree, updatedNode);
            }
        },
    },
};
</script>
<style lang="less" scoped>
.org-manage-page {
    height: 100%;
    width: 100%;
    background-color: #f5f7fa;
    display: flex;
    justify-content: center;
    align-items: center;

    ._right {
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        padding: 24px;

        ._opera-area {
            display: flex;
            justify-content: space-between;
            width: 100%;
            max-width: 1600px;
            height: calc(90vh - 48px);
            margin: 0 auto;
            gap: 24px;

            .__l {
                flex: 1;
                height: 100%;
                display: flex;
                flex-direction: column;
                gap: 24px;

                .dhrBox,
                .fractoryBox {
                    flex: 1;
                    background: #fff;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                    border-radius: 12px;
                    padding: 20px;
                    display: flex;
                    flex-direction: column;
                    max-height: calc(50% - 12px);
                    transition: all 0.3s ease;

                    &:hover {
                        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
                    }

                    ._title {
                        flex-shrink: 0;
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 16px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        color: #1a1a1a;

                        .a-button {
                            border-radius: 6px;

                            &:hover {
                                transform: translateY(-1px);
                            }
                        }
                    }

                    // .ant-select {
                    //   flex-shrink: 0;
                    //   margin-bottom: 12px;

                    //   // &:hover .ant-select-selector {
                    //   //   border-color: #40a9ff;
                    //   // }

                    //   .ant-select-selector {
                    //     border-radius: 6px;
                    //     transition: all 0.3s ease;
                    //   }
                    // }

                    .ant-spin-nested-loading {
                        flex: 1;
                        min-height: 0;
                        overflow: scroll;

                        .ant-spin-container {
                            height: 100%;

                            .org-list-tree {
                                height: 100%;
                                border: 1px solid #e8e8e8;
                                border-radius: 8px;
                                overflow: auto;
                                padding: 12px;
                                background: #fafafa;

                                &:hover {
                                    border-color: #d9d9d9;
                                }

                                // 树节点样式
                                :deep(.ant-tree-node-content-wrapper) {
                                    // padding: 6px 8px;
                                    border-radius: 4px;
                                    transition: all 0.3s ease;

                                    // &:hover {
                                    //   background: #e6f7ff;
                                    // }
                                }

                                // :deep(.ant-tree-node-selected) {
                                //   background: #bae7ff;
                                // }
                            }
                        }
                    }
                }
            }

            .__r {
                flex: 1;
                height: 100%;

                .ytBox {
                    height: 100%;
                    background: #fff;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                    border-radius: 12px;
                    padding: 20px;
                    display: flex;
                    flex-direction: column;
                    transition: all 0.3s ease;

                    &:hover {
                        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
                    }

                    ._title {
                        flex-shrink: 0;
                        padding: 8px 16px;
                        display: flex;
                        flex-direction: column;
                        gap: 12px;

                        .title-text {
                            font-size: 16px;
                            font-weight: 600;
                            color: #1a1a1a;
                            text-align: center;
                        }

                        .button-group {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 8px;
                            justify-content: center;

                            .a-button {
                                border-radius: 6px;
                                transition: all 0.3s ease;

                                &:hover {
                                    transform: translateY(-1px);
                                }
                            }

                            .date-picker {
                                min-width: 120px;

                                :deep(.ant-picker) {
                                    border-radius: 6px;
                                    transition: all 0.3s ease;

                                    // &:hover {
                                    //   border-color: #40a9ff;
                                    // }
                                }
                            }
                        }
                    }

                    .ant-select {
                        flex-shrink: 0;
                        margin-bottom: 12px;
                    }

                    .ant-spin-nested-loading {
                        flex: 1;
                        min-height: 0;
                        overflow: scroll;

                        .ant-spin-container {
                            height: 100%;

                            .org-list-tree {
                                height: 100%;
                                border: 1px solid #e8e8e8;
                                border-radius: 8px;
                                overflow: auto;
                                padding: 12px;
                                background: #fafafa;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 美化滚动条
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
        background: #bfbfbf;
    }
}

// 弹窗样式
.tree-node {
    padding: 16px;
    border-radius: 8px;
    background: #f8f9fa;
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .node-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .node-label {
            min-width: 120px;
            font-weight: 500;
            color: #262626;
        }

        .level-select {
            width: 200px;
        }

        .code-input {
            width: 150px;
        }
    }

    .children-wrapper {
        margin: 16px 0 0 24px;
        padding-left: 16px;
        border-left: 1px dashed #d9d9d9;
    }
}

// 响应式布局
@media screen and (max-width: 1366px) {
    .org-manage-page {
        ._opera-area {
            gap: 16px;

            .__l,
            .__r {

                .dhrBox,
                .fractoryBox,
                .ytBox {
                    padding: 16px;
                }
            }
        }
    }
}

@media screen and (max-width: 1024px) {
    .org-manage-page {
        ._opera-area {
            flex-direction: column;

            .__l {
                flex-direction: row;
                height: 40%;

                .dhrBox,
                .fractoryBox {
                    width: calc(50% - 12px);
                    max-height: 100%;
                }
            }

            .__r {
                height: 60%;
            }
        }
    }
}
</style>
