/*
 * @Description: 组件配置描述json
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-07 15:58:35
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-11-12 09:40:10
 */
const comJson = {
  json: [
    {
      type: "input",
      col: "companyName",
      label: "注册公司名称"
    },
    {
      type: "input",
      col: "signOrgId",
      label: "注册公司编码"
    }
    // {
    //   type: "input",
    //   col: "orgLabelName",
    //   label: "组织下拉框名称",
    //   props: {}
    // },
    // {
    //   type: "input",
    //   col: "operationGuideJSUrl",
    //   label: "操作引导组件jsUrl",
    //   props: {}
    // },
    // {
    //   type: "input",
    //   col: "indexCardDetailInfoJSUrl",
    //   label: "指标卡片详情组件jsUrl",
    //   props: {}
    // },
    // {
    //   type: "input",
    //   col: "indexDetectionInfoJSUrl",
    //   label: "指标卡片组织下探组件jsUrl",
    //   props: {}
    // },
    // {
    //   type: "input",
    //   col: "indexMDYSFXInfoJSUrl",
    //   label: "指标末端因素分析组件jsUrl",
    //   props: {}
    // },
    // {
    //   type: "input",
    //   col: "selfReportsJSUrl",
    //   label: "个人专属报告jsUrl",
    //   props: {}
    // }
  ],
  props: {
    companyName: "空调",
    signOrgId: "H0203"
    // orgLabelName: "组织"
    // operationGuideJSUrl: "/mombucket/VideoCom.umd.min.1.0.js",
    // indexCardDetailInfoJSUrl: "/mombucket/IndexCardDetailInfo2.umd.min.1.0.js",
    // selfReportsJSUrl: "/mombucket/SelfReports2.umd.min.1.0.js",
    // indexDetectionInfoJSUrl: "/mombucket/IndexDetectionInfo2.umd.min.1.0.js",
    // indexMDYSFXInfoJSUrl: "/mombucket/IndexMMYSFXInfo.umd.min.1.0.js"
  },
  buttonList: [],
  defaultIntl: [
    "bc7e7ec2-1891-49f5-906f-8ad4c4c626ac", // 指标搜索
    "82e9624b-47f4-40c9-a0b0-a6e8dab39a5a", // 组织
    "80432b97-2434-43f8-83d1-814dd893eb77", // 按月
    "31efb687-7afd-41a0-9005-df0e19b17bfb", // 按周
    "067b57ce-3cee-4f42-8690-24f6b5c03c09", // 按日
    "318ca1ea-753b-4bd2-9256-2013d772c7db", // 指标筛选
    "4155039a-b280-4271-8216-508465ce4f76", // 全部
    "26d9a1a4-c63a-4790-9235-7f1ed87e886e", // 目标值
    "8aa80973-1c0c-4497-bf26-d93a1f35c0a3", // 完成率
    "677ef4d6-cdb9-44f4-9144-bbd46d937068", // 同比
    "7d14b9da-3dff-4ec0-a348-199ed6fefb42", // 环比
    "723bb557-8520-42d0-a988-b675f9818bbf", // 业务版块
    "01ee0e91-78e8-4dbe-b644-53f286f3d4f7", // 查看专项分析
    "6b0e3430-9218-4775-90dd-f03e9814686e", // 海信集团核心KPI概览
    "cf332ef8-20ac-44ae-b27e-8c13c61f75a2", // 日立核心KPI概览
    "a4e6b067-6872-4b0b-8a95-6ea526004314", // 智动精工核心KPI概览
    "73922715-6b5a-46ea-9b65-c3c2a5d2d546", // 宽带核心KPI概览
    "cbb23b71-ab35-44d8-b37a-376209a8e6ba", // 模具核心KPI概览
    "cd632557-8ba5-43db-920c-d479229590e3", // 视像科技核心KPI概览
    "80966938-86d8-47e7-91e3-63ba8c57b76c", // 冰箱核心KPI概览
    "4bff057d-f7e4-4e20-8ab9-4ec2d47047a4", // 厨卫核心KPI概览
    "eb1805da-cdc1-45d8-b543-664961ec1345", // 空调核心KPI概览
    "805c22af-b568-4d6d-9470-596b79970ba5", // 洗衣机核心KPI概览
    "3aa577be-85f1-4e3e-8159-1aa21a55d056", // 国际营销核心KPI概览
    "6c6b1473-ca6f-4cae-a70b-7a0a1347d2d2", // 通信公司核心KPI概览
    "4e4874e3-f239-41af-9e97-27c513c2f21a", // 报表
    "a79bc3ff-96bc-4fd2-bb3b-0e51cba3bf03", // 组织下探
    "f2b68ca6-7ab0-47df-a62d-6d6c4c801282", // 走势
    "a5039920-fcab-4c01-8781-2246aa964f2a", // 拖拽
    "bbba4703-5430-4441-82be-733176267808", // 添加指标卡片
    "d257a594-2042-4082-b47a-3404f85e755e", // 换一个
    "e98264dd-aff8-4e0f-bbdd-a3136af6f6a7" // 添加到列表
  ]
};
export default comJson;
