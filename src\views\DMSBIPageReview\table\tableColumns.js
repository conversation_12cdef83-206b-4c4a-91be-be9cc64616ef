/*
 * @Author: othniel <EMAIL>
 * @Date: 2025-05-13 16:34:00
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-05-13 16:50:03
 * @FilePath: \pangea-component\src\views\DMSBIPageReview\table\tableColumns.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 异常问题跟进表格列定义
 */
export const tableColumns = [
  { title: "日期", dataIndex: "happenTime", key: "happenTime", width: 100 },
  {
    title: "分类",
    dataIndex: "currentState",
    key: "currentState",
    width: 100,
    scopedSlots: { customRender: "currentState" },
    filters: [
      { text: '待接单', value: "4" },
      { text: '进行中', value: "10" },
      { text: '已完成', value: "12" },
      { text: '已超时', value: "99" },
    ],
  },
  { title: "问题来源", dataIndex: "eventSource", key: "eventSource", width: 100,scopedSlots: { customRender: "eventSource" },},
  // { title: "创建人", dataIndex: "creator", key: "creator", width: 100 },//系统
  {
    title: "问题类型",
    dataIndex: "eventType",
    key: "eventType",
    width: 100,
  },
  { title: "问题描述", dataIndex: "eventDescription", key: "eventDescription", width: 100 },
  { title: "根因分析", dataIndex: "causeAnalysis", key: "causeAnalysis", width: 100 },
  { title: "临时对策", dataIndex: "temporary", key: "temporary", width: 100 },
  { title: "长期对策", dataIndex: "temporaryMeasures", key: "temporaryMeasures", width: 100 },
  // {
  //   title: "最新进度",
  //   dataIndex: "responsible",
  //   key: "responsible",
  //   width: 100,
  // },
  { title: "责任人", dataIndex: "partiName", key: "partiName", width: 80 },
  // { title: "纳期", dataIndex: "finishTime", key: "finishTime", width: 80 },
  // { title: "状态", dataIndex: "score", key: "score", width: 80 },
  {
    title: "操作",
    dataIndex: "score",
    key: "action",
    width: 80,
    scopedSlots: { customRender: "action" },
  },
];

export default tableColumns;
