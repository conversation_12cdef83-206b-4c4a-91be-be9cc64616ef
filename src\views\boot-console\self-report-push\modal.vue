<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2022-07-29 17:39:56
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="800"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="主题" prop="topic">
        <a-input v-model="form.topic" placeholder="请输入" />
      </a-form-model-item>
      <a-form-model-item label="推送频次" prop="pushFrequency">
        <a-select
          v-model="form.pushFrequency"
          placeholder="请选择推送频次"
          @change="
            () => {
              this.form.pushTimes = 1;
            }
          "
        >
          <a-select-option
            :value="item.key"
            v-for="item in dict['smc-frequency'] || []"
            :key="item.key"
          >
            {{ item.key }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="邮件推送时间" prop="pushTimes">
        <template v-if="form.pushFrequency === '日' || !form.pushFrequency">
          <a-input style="width: 100%" disabled value="每日" />
        </template>
        <template v-else>
          <a-select
            v-model="form.pushTimes"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in form.pushFrequency === '月'
                ? monthSelectList
                : weekSelectList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
      <a-form-model-item label="接收人" prop="recipient">
        <a-select
          mode="multiple"
          :value="form.recipient"
          placeholder="接收人"
          style="width: 100%"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUser"
          @change="handleChange"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option
            :value="d.uid"
            v-for="d in [...userData, ...selectUserData]"
            :key="d.uid"
          >
            {{ d.cn }}({{ d.uid }}-{{ d.mail }})
            <div>{{ d.o }}</div>
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="可编辑区" prop="remark">
        <!-- <a-textarea placeholder="请输入" v-model="form.remark" :rows="4" /> -->
        <!-- <Toolbar
          style="border-bottom: 1px solid #ccc"
          :editorId="editorId"
          :defaultConfig="toolbarConfig"
          :mode="mode"
        />
        <Editor
          style="height: 230px; overflow-y: auto;"
          :editorId="editorId"
          :default-config="editorConfig"
          :defaultHtml="form.content"
          :mode="mode"
          @onCreated="onCreated"
          @onChange="editorChange"
        /> -->
        <MyEditor :value="form.remark" ref="myEditor" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
// import { adminUserUrlPrefix } from "@/utils/utils";
import MyEditor from "./MyEditor.vue";
export default {
  components: { MyEditor },
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      form: {
        topic: "",
        pushFrequency: "日",
        pushTimes: 1,
        recipient: [],
        remark: ""
      },
      fetching: false, // 查询用户loading
      rules: {
        topic: [
          {
            required: true,
            message: "请填写主题",
            trigger: "blur"
          }
        ],
        recipient: [
          {
            required: true,
            message: "请选择推送接收人",
            trigger: "change"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      dict: {}, // 字典
      selectUserData: [],
      userData: [],
      monthSelectList: [],
      weekSelectList: []
    };
  },
  methods: {
    async show(formValue = {}) {
      this.isEdit = false;
      if (Object.keys(formValue).length) {
        this.isEdit = true;
        const {
          id,
          recipient,
          remark,
          pushFrequency,
          pushTimes,
          topic
        } = formValue;
        this.form = {
          id,
          recipient: recipient.includes(",")
            ? recipient.split(",")
            : [recipient],
          remark,
          pushFrequency,
          pushTimes,
          topic
        };
      }
      this.getDICT();
      this.genExecutionTimeList();
      this.visible = true;
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-frequency&languageCode=zh_CN"
        )
      ).then(res => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
            console.log("this.dict----> ", this.dict);
          }
        }
      });
    },
    // 生成预警推送时间列表
    genExecutionTimeList() {
      const map = {
        1: "一",
        2: "二",
        3: "三",
        4: "四",
        5: "五",
        6: "六",
        7: "日"
      };
      for (let i = 1; i <= 31; i++) {
        if (i <= 7) {
          this.weekSelectList.push({
            key: i,
            value: `每周${map[i]}`
          });
        }
        this.monthSelectList.push({
          key: i,
          value: `每月${i}号`
        });
      }
    },
    // 查找用户
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      request(`/api/system/user/getLdapByName?name=${value}`).then(res => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        this.userData = res;
        this.fetching = false;
      });
    },
    handleChange(value) {
      this.form.recipient = value;
      this.fetching = false;
      this.selectUserData = this.userData.filter(item =>
        value.includes(item.uid)
      );
      this.userData = [];
    },
    close() {
      this.form = {
        topic: "",
        pushFrequency: "日",
        pushTimes: 1,
        recipient: [],
        remark: ""
      };
      this.monthSelectList = [];
      this.weekSelectList = [];
      this.userData = [];
      this.selectUserData = [];
      this.fetching = false;
      this.$refs.form.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    // 提交
    submit() {
      this.$refs.form.validate(async valid => {
        console.log("this", this.$refs["myEditor"].editor?.getHtml());
        if (valid) {
          request(
            `/api/smc2/exclusiveReport/${
              this.isEdit
                ? "updateExclusiveExpressPush"
                : "insertExclusiveExpressPush"
            }`,
            {
              method: "POST",
              body: {
                ...this.form,
                remark: this.$refs["myEditor"].editor?.getHtml(),
                recipient: this.form.recipient.join(",")
              }
            }
          ).then(() => {
            // if (res && res.result === "success") {
            this.close();
            this.$emit("fetchData");
            // } else if (res && res.result !== "success") {
            //   this.$message.error(res.result);
            // }
          });
        }
      });
    }
  }
};
</script>
