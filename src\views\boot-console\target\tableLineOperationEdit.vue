<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-25 08:45:24
-->
<template>
  <!-- 非累加目标值修改 -->
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal
      ref="modal"
      @fetchData="fetchData"
      :record="this.record"
      :target="this.target"
      :targetCIM="this.targetCIM"
      :targetCBG="this.targetCBG"
    />
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
  },
  data() {
    return {
      showAlias,
      target: true,
      targetCIM: true,
      targetCBG: false,
    };
  },
  methods: {
    btClick() {
      // const record = {
      //   indexName: "单实物台制造费",
      //   cmimId:
      //     "ZBY00467-H0104-M-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000",
      //   indexUnit: "元"
      // };
      const record = {
        indexName: this.record.indexName,
        indexUnit: this.record.unit,
        cmimId: this.record.cmimId,
      };
      request("/api/smc2/newTarget/isModify").then((res) => {
        this.target = res.target;
        this.targetCIM = res.targetCIM;
        // this.targetCBG = res.targetCBG;
        this.targetCBG = false;
        this.$refs["modal"].show(record);
      });
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex,
        },
      });
    },
  },
};
</script>
