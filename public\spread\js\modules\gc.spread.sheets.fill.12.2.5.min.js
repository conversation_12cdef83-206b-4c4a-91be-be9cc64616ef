/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Fill=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/fill/fill.entry.js")}({"./dist/plugins/fill/drag-fill.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("./dist/plugins/fill/fill.js"),g=c("./dist/plugins/fill/fill.ns.js"),h=c("CalcEngine"),i=!!h,j=d.Common.j.Fa,k=null,l=e.kf,m=e.GC$,n=Math.max,o=Math.floor,p=e.Commands.ActionBase,q=e.Commands.xy,r=e.Commands.Bu,s=1023,t="startRange",u="fillRange",v="autoFillType",w="fillDirection",x="cancel",y="fill",z="dragDrop",A=m.isEmptyObject,B=new d.Common.ResourceManager(g.SR),C=B.getResource.bind(B);function I(a){return a.rowCount}function J(a){return a.colCount}function K(a,b){return a.getRowCount(b)}function L(a,b){return a.getColumnCount(b)}function M(a,b,c,d){return!!(a&&b&&b.containsRange(a))&&(!j(c)&&(1===c&&a.row===b.row&&I(a)===I(b)||0===c&&a.col===b.col&&J(a)===J(b))||!(j(d)||(2!==d&&3!==d||a.col!==b.col||J(a)!==J(b))&&(0!==d&&1!==d||a.row!==b.row||I(a)!==I(b))))}D={fillAuto:function(a,b,c){if(!b)throw Error(C().Exp_RangeIsNull);var e=this,g=c.fillType,h=c.series,i=c.direction,l=c.step,m=c.stop,n=new f.z$(e);if(M(a,b,i?k:h,i)){e.suspendPaint(),a&&(e.mm.A$=a);try{4===g?n.B$(b,h,!1,c.withTag,c.withoutStyle):0===g?n.C$(b,i):1===g||2===g?n.D$(1===g,b,h,l,m):3===g&&n.E$(b,h,3,l,j(m)?k:d.Common.l.Ra(m),c.unit)}finally{e.resumePaint()}}}},m.extend(e.Worksheet.prototype,D),e.Worksheet.$n(y,{init:function(){this.Fyb=new f.Gyb(this)},setHost:function(a){if(a){var b=this.wu();b[y]||b.register(y,e.Commands[y]),b[z]||b.register(z,e.Commands[z])}}}),E=function(a){H(b,a);function b(b,c){var d=a.call(this)||this,f=d,g=c[w],h=0===g,i=h||1===g,j=c[u],k=c[t],m=k.row,n=k.col,o=I(k),p=J(k);return f.kj=b,f.G$=c,4===c[v]?f.H$=new e.Commands.OA(b,{ranges:[c[u]]}):i?f.I$=l(m,h?j.col:n,o,p+J(j)):f.I$=l(2===g?j.row:m,n,o+I(j),p),f.J$=i?1:0,d}return b.prototype.canExecute=function(){var a=this,b=a.kj,c=a.G$,d=c[t],e=c[u];return!!b.Y3(e.row,e.col,I(e),J(e))&&(4===c[v]||!e.intersect(d.row,d.col,I(d),J(d)))},b.prototype.execute=function(){var a,b,c,d,f,g,h=this;if(h.canExecute()){a=h.G$,b=a[u],c=h.kj,c.ITa.startTransaction(),h.Lz(c,!0);try{i&&c.suspendCalcService(),d=c.getSelections(),c.isDirtySuspended()||(h.N$=new q(c,a[u],s)),4===a[v]?h.K$(c):h.L$(c),c.yu(d,c.getSelections())&&c.Au(d)}finally{h.Mz(c,!0),f=e.Commands.bWa(c.name()),a[f]=c.ITa.endTransaction(),i&&c.resumeCalcService(!1),c.t4&&c.t4(b.row,b.col,I(b),J(b)),g=[],c.isDirtySuspended()||(h.M$=new q(c,b,s),h.M$.uy(h.N$.sy()),g=h.M$.wy()),r(c,b.row,b.col,I(b),J(b),g,1)}}},b.prototype.K$=function(a){var b,c,d,e,f,g,h,i,j,k=this;k.H$.execute(a),b=k.G$,c=b[t],d=b[u],e=0===k.J$,g=I(c),h=J(c),i=e?n(1,g-I(d)):g,j=e?h:n(1,h-J(d)),c.equals(d)||(f=l(c.row,c.col,i,j),a.Kr(n(a.ar(),f.row),n(a.$q(),f.col),a.Nr,a.Pr),a.Tr(),a.addSelection(f.row,f.col,I(f),J(f)))},b.prototype.L$=function(a){var b,c,d,e,f,g,h=this,i=h.kj,j=h.G$[t],k=h.G$[u],l=h.G$[v],m=h.I$,p={fillType:4,series:h.J$,withTag:!0};if(1===l){if(i.fillAuto(j,m,p),i.ITa.MTa(j.row,j.col,j.rowCount,j.colCount))for(b=0;b<k.rowCount;b++)for(c=0;c<k.colCount;c++)d=i.Aj(j.row+o(b%j.rowCount),j.col+o(c%j.colCount)),d&&d.clone&&(d=d.clone()),i.Nq(k.row+b,k.col+c,d)}else 0===l?(e=4^s,h.O$(j,k,e)):2===l?h.O$(j,k,192):3===l&&(f=!(1!==I(j)||1!==J(j)||j.row===-1&&j.col!==-1||j.col===-1&&j.row!==-1),f?(g=187,h.O$(j,k,g)):(p.withoutStyle=!0,i.fillAuto(j,m,p)));i.Kr(n(a.ar(),m.row),n(a.$q(),m.col),a.Nr,a.Pr),a.Tr&&a.Tr(),i.addSelection(m.row,m.col,I(m),J(m))},b.prototype.O$=function(a,b,c){var d=this,e=d.kj,f=e.Tq(a),g=e.Tq(b),h=f.row,i=f.col,j=I(f),k=J(f),l=g.row,m=g.col,n=I(g),p=J(g),q=d.G$[w],r=3===q,s=1===q,t=o(n/j),u=n%j,v=o(p/k),x=p%k,y,z,A,B,C;if(0===d.J$){for(y=0;y<t;y++)B=r?l+y*j:l+n-(y+1)*j,e.ax(h,i,B,m,j,k,c,!0);0!==u&&(z=r?h:h+(t+1)*j-n,B=r?l+j*t:l+n-t*j-u,e.ax(z,i,B,m,u,k,c,!0))}else{for(y=0;y<v;y++)C=s?m+y*k:m+p-(y+1)*k,e.ax(h,i,l,C,j,k,c,!0);0!==x&&(A=s?i:i+(v+1)*k-p,C=s?m+k*v:m+p-v*k-x,e.ax(h,A,l,C,j,x,c,!0))}},b.prototype.undo=function(){var a,b,c,d,e=this,f=e.kj,g=!1;if(e.canUndo()){e.Lz(f,!0);try{i&&f.suspendCalcService(),a=f.getSelections(),b=e.G$,c=f.NF,d=b[u],g=4===b[v]?e.S$(f):e.T$(f),!f.U$&&c&&c.close(),r(f,d.row,d.col,I(d),J(d),e.M$?e.M$.wy():[],1),f.yu(a,f.getSelections())&&f.Au(a)}finally{e.Mz(f,!0),i&&f.resumeCalcService(!1)}}return g},b.prototype.S$=function(a){var b=this,c=b.G$[t],d=c.row,e=c.col,f=b.H$.undo();return a.Kr(n(a.ar(),d),n(a.$q(),e),a.Nr,a.Pr),f},b.prototype.T$=function(a){var b,c=this,d=c.G$,f=d[t],g=f.row,h=f.col;i&&a.suspendCalcService();try{b=e.Commands.bWa(a.name()),a.ITa.undo(d[b]),a.Kr(n(a.ar(),g),n(a.$q(),h),a.Nr,a.Pr)}finally{i&&a.resumeCalcService(!1)}return a.invalidateLayout(),!0},b}(p),F=function(a){H(b,a);function b(b,c){var d=a.call(this)||this,e=d;return e.kj=b,e.i4=c,e.W$=c[t],e.X$=c[u],e.Y$=c[v],e.Z$=c[w],d}return b.prototype.execute=function(){var a,b,c,d,f,g=this,h=!1;if(g.canExecute()){a=g.kj,b=g.X$,g.oUa(),g.Lz(a,!0),a.vxb=!0;try{c=a.getSelections(),d=a.mm,f={sheet:a,sheetName:a.name()},f[x]=!1,f[u]=b,f[v]=g.Y$,f[w]=g.Z$,a.Wq(e.Events.DragFillBlock,f),d.$$=f[v],d._$=f[x],g.a_(f),delete f[x],a.Wq(e.Events.DragFillBlockCompleted,f),h=!0,a.yu(c,a.getSelections())&&a.Au(c)}finally{g.Mz(a,!0),a.vxb=!1,g.IVa(a,b)}}return h},b.prototype.a_=function(a){var b,c,d=this,e=d.kj,f=e.mm,g=d.X$,h=d.Y$;f.$$=h,f._$||(b={},b[t]=d.W$,b[u]=g,b[v]=5===a[v]?h:a[v],b[w]=d.Z$,c=new E(e,b),e.suspendEvent(),c.execute(),e.resumeEvent(),d.i4.d_=c)},b.prototype.oUa=function(){var a=this;a.kj.isDirtySuspended()||(a.N$=new q(a.kj,a.X$,s))},b.prototype.undo=function(){var a,b,c,d=this,e=!1;if(d.canUndo()){a=d.kj,b=d.X$,d.oUa(),d.Lz(a,!0);try{c=a.getSelections(),a.suspendEvent(),e=d.i4.d_.undo(),a.resumeEvent(),a.yu(c,a.getSelections())&&a.Au(c)}finally{d.Mz(a,!0),d.IVa(a,b)}}return e},b.prototype.IVa=function(a,b){var c,d=this,e=[];a.isDirtySuspended()||(c=new q(a,b,s),c.uy(d.N$.sy()),e=c.wy()),r(a,b.row,b.col,I(b),J(b),e,1)},b}(p);function N(a,b,c,d,e){var f,g,h=a.getSelections();a.Tr(),a.addSelection(b,c,d,e),a.Au(h),f=b>0?b:a.ar(),g=c>0?c:a.$q(),a.Kr(f,g,a.Nr,a.Pr)}function O(a){var b=a.parent,c=b.getActiveSheet();c&&a!==c&&c.repaint()}G=function(a){H(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){var a,b,c,d=this,e=d.kj,f=d.VQ,g=f.fromRow,h=f.fromColumn,i=I(f),j=f.columnCount,l=f.toRow,m=f.toColumn,n=!f.copy,o={row:g,col:h,rowCount:i,colCount:j},p={row:l,col:m,rowCount:i,colCount:j};return(!e.tables||!e.tables.zxb(e,o,e,p,n))&&(!!e.pu(g,h,i,j,K(e),L(e))&&(!(!f.insert&&!e.pu(l,m,i,j,K(e),L(e)))&&(a=l>=0&&m<0&&g>=0&&h<0&&i>0&&j<0,b=l<0&&m>=0&&g<0&&h>=0&&i<0&&j>0,c=d.KZa(a,b),e.Y3(l,m,i,j,k,k,c)&&e.Y3(g,h,i,j,k,k,c))))},b.prototype.KZa=function(a,b){var c,d,e=this,f=e.VQ,g=e.kj,h=f.insert,i=g.options.isProtected,j=g.options.protectionOptions;return!(!i||A(j)||(c=j.allowDragInsertRows,d=j.allowDragInsertColumns,!h||!(c&&a||d&&b)))},b.prototype.execute=function(){var a,b,c,d,f,g,h,i,j,k,l,m,n,o,p,q,r=this,s=!1;if(r.canExecute()){a=r.kj,b=r.VQ,r.oUa(),c=void 0,d=void 0,f=void 0,g=b.fromRow,h=b.fromColumn,i=b.toRow,j=b.toColumn,k=I(b),l=b.columnCount,m=b.option,n=a.parent.sheets,e.Commands.Zxb(n),r.Lz(a,!0),a.vxb=!0;try{b.insert?h>=0&&g<0?(c=h,d=j,f=l,o=j,a.addColumns(d,f),b.copy?a.copyTo(-1,d<=c?c+f:c,-1,d,-1,f,m):(a.moveTo(-1,d<=c?c+f:c,-1,d,-1,f,m),a.deleteColumns(d<=c?c+f:c,f),c<d&&(o=d-f)),N(a,-1,o,K(a),f)):g>=0&&h<0&&(c=g,d=i,f=k,p=i,a.addRows(d,f),b.copy?a.copyTo(d<=c?c+f:c,-1,d,-1,f,-1,m):(a.moveTo(d<=c?c+f:c,-1,d,-1,f,-1,m),a.deleteRows(d<=c?c+f:c,f),c<d&&(p=d-f)),N(a,p,-1,f,L(a))):(b.copy?a.copyTo(g,h,i,j,k,l,m):a.moveTo(g,h,i,j,k,l,m),a&&(N(a,i,j,k,l),q=b.io,b.copy||a.isDirtySuspended()||(q._z=r.aA(a,q.cA,g,h,k,l)),a.isDirtySuspended()||(q.dA=r.aA(a,q.fA,i,j,k,l)))),s=!0}finally{a.mm.gA={},r.Mz(a,!0),a.vxb=!1,e.Commands.$xb(n,b)}O(a)}return s},b.prototype.aA=function(a,b,c,d,e,f){var g=new q(a,l(c,d,e,f),this.VQ.option);return g.uy(b.sy()),r(a,c,d,e,f,g.wy(),0),g},b.prototype.oUa=function(){var a=this,b=a.kj,c=a.VQ,d=c.io={},e=c.fromRow,f=c.fromColumn,g=c.toRow,h=c.toColumn,i=c.option,j=e<0?0:e,k=f<0?0:f,m=g<0?0:g,n=h<0?0:h,o=e<0?K(b):I(c),p=f<0?L(b):c.columnCount;c.insert||b.isDirtySuspended()||(c.copy||(d.cA=new q(b,l(j,k,o,p),i)),d.fA=new q(b,l(m,n,o,p),i)),d.rA=b.Nr,d.sA=b.Pr,d.tA=b.Jl,d.uA=b.Kl},b.prototype.undo=function(){var a,b,c,d,f,g,h,i,j,k,l,m,n,o,p,q,s,t,u,v,w=this;if(w.canUndo()){if(a=w.kj,b=w.VQ,c=b.fromRow,d=b.fromColumn,f=b.toRow,g=b.toColumn,h=I(b),i=b.columnCount,!a.pu(c,d,h,i,K(a),L(a)))return!1;if(!b.insert&&!a.pu(f,g,h,i,K(a),L(a)))return!1;j=void 0,k=b.io,w.Lz(a,!0),l=a.parent.sheets,e.Commands._xb(l,b);try{b.insert?d>=0&&c>=0||(d>=0?(m=d,j=i,a&&N(a,-1,m,K(a),j)):c>=0&&(j=h,n=c,a&&N(a,n,-1,j,L(a)))):(o=c<0?0:c,p=d<0?0:d,q=f<0?0:f,s=g<0?0:g,t=c<0?K(a):h,u=d<0?L(a):i,a&&(N(a,c,d,h,i),r(a,q,s,t,u,k.dA?k.dA.wy():[],0),r(a,o,p,t,u,k._z?k._z.wy():[],0))),a&&(k.tA!==-1&&k.uA!==-1&&(v=a.getSelections()[0],v.contains(k.tA,k.uA)?a.Jr(k.tA,k.uA):a.Jr(v.row,v.col)),k.rA!==-2&&k.sA!==-2&&k.tA!==-1&&k.uA!==-1&&a.showCell(k.tA,k.uA,3,3))}finally{w.Mz(a,!0)}O(a)}return!0},b}(p),e.Commands[y]={canUndo:!0,execute:function(a,b,c){return e.Commands.h4(a,F,b,c)}},e.Commands[z]={canUndo:!0,execute:function(a,b,c){return e.Commands.h4(a,G,b,c)}}},"./dist/plugins/fill/fill-event.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("SheetsCalc"),g=c("./dist/plugins/fill/fill-ui.js"),h=c("./dist/plugins/fill/fill.js"),i=c("./dist/plugins/fill/fill.ns.js"),j=c("CalcEngine"),k=!!j,l=e.GC$,m=e.kf,n=d.Common.j.Fa,o=null,p=void 0,q=Math.min,r=Math.abs,s=Math.max,t=5,u=3,v="drag",w="corner",x="black",y=new d.Common.ResourceManager(i.SR),z=y.getResource.bind(y);function A(a){return a.rowViewportIndex}function B(a){return a.colViewportIndex}function C(a){return a.frozenRowCount()}function D(a){return a.frozenColumnCount()}function E(a){return a.hitTestType}function F(a,b){return a.getRowCount(b)}function G(a,b){return a.getColumnCount(b)}function H(a){return a.colCount}function I(a){return a.rowCount}function J(a){return a.height}function K(a){return a.width}function L(a,b,c,d,f){a.save();var g,h,i;b===d?(a.rect(b-2,c,7,r(f-c)),a.clip(),a.beginPath(),c<f?(c-=3,f+=3):(c+=3,f-=3),g=e.zF(b-1,c-1,d-1,f+1,x,7),h=e.zF(b,c,d,f,x,7),i=e.zF(b+1,c-1,d+1,f+1,x,7)):c===f&&(a.rect(b,c-2,r(d-b),7),a.clip(),a.beginPath(),b<d?(b-=3,d+=3):(b+=3,d-=3),g=e.zF(b-1,c-1,d+1,f-1,x,7),h=e.zF(b,c,d,f,x,7),i=e.zF(b-1,c+1,d+1,f+1,x,7)),g&&g.vE(a),h&&h.vE(a),i&&i.vE(a),a.stroke(),a.beginPath(),a.restore()}function M(a,b,c,d,f,g,h,i,j,k,l){var m=b?e.Events.DragDropBlock:e.Events.DragDropBlockCompleted,n={sheet:a,sheetName:a.name(),fromRow:c,fromCol:d,toRow:f,toCol:g,rowCount:h,colCount:i,copy:j,insert:k,copyOption:l};return b&&(n.cancel=!1),a.Wq(m,n),n.cancel}function N(a,b,c,d,e){var f,g,h;for(f=0;f<c.rowCount;f++){for(g=0;g<c.colCount;g++){if(h=a.getSpan(c.row+f,c.col+g),!h||h.rowCount!==b.rowCount||h.colCount!==b.colCount)return!1;d&&(g+=b.colCount)}e&&(f+=b.rowCount)}return!0}function O(a,b,c){var d,e=b.row,f=b.rowCount,g=b.col,h=b.colCount,i=c.row,j=c.rowCount,k=c.col,l=c.colCount,m=e===i&&f===j,n=g===k&&h===l;return!(!m&&!n)&&(d=a.getSpan(e,g),!(!d||!N(a,d,b,m,n))&&(!a.ITa.qu(i,k,j,l)&&N(a,d,c,m,n)))}l.extend(e.iI.prototype,{gG:function(a){var b,c,d,e,f,g,h,i,j=this;j.zG||(b=j.kj,c=o,d=b.ITa.getSelections(),1===d.length?c=d[0]:d.length<1&&(c=b.ITa.getSpan(b.Jl,b.Kl)),c&&(j.zG=!0,j.OG=!0,j.L_=c,e=b.getRangeRect(A(a),B(a),c),f=j.gA,f.x=e.x,f.y=e.y,f.width=e.width-1,f.height=e.height-1,f.row=c.row,f.col=c.col,f.rowCount=I(c),f.colCount=H(c),g=b.Tq(c),h=a.row,i=a.col,h<g.row&&(h=g.row),h>=g.row+I(g)&&(h=g.row+I(g)-1),i<g.col&&(i=g.col),i>=g.col+H(g)&&(i=g.col+H(g)-1),f.hitRow=h,f.hitCol=i,f.rowOffset=h-g.row,f.colOffset=i-g.col,j.rG={KG:A(a),MG:B(a),sG:E(a)},j.qG()))},AG:function(){var a,b,c,d,e,f,g,h,i,j,k=this;k.rG&&k.OG&&k.zG&&k.L_&&(a=k.$F,b=k.gA,c=k.kj.getCellRect(b.hitRow,b.hitCol),d=c.width/2,e=c.x+d,f=c.height/2,g=c.y+f,b.hitTarget={x:a.x,y:a.y},h=k.fH(),i=k.gH(),j=!1,(r(e-a.x)>d+5||r(g-a.y)>f+5)&&(j=!0),h>=0&&i>=0&&j&&k.M_(h,i),k.NG())},M_:function(a,b){var c,d,e,f,g,h,i,j,k=this;k.zG&&k.L_&&(c=k.kj,d=k.gA,e=c.Ix(),e.row===-1&&e.col!==-1?(d.row=-1,d.col=s(0,q(G(c)-H(d),b-d.colOffset))):e.row!==-1&&e.col===-1?(d.row=s(0,q(F(c)-I(d),a-d.rowOffset)),d.col=-1):(d.row=d.row<0?-1:s(0,q(F(c)-I(d),a-d.rowOffset)),d.col=d.col<0?-1:s(0,q(G(c)-H(d),b-d.colOffset))),d.hitRow=a,d.hitCol=b,f=c.Tq(d),g=k.N_,g&&f.row===g.row&&f.col===g.col&&I(f)===I(g)&&H(f)===H(g)&&f.row>c.ar()&&f.col>c.$q()&&f.row+I(f)-1<c.ir()&&f.col+H(f)-1<c.dr()||(k.MI=f,c.yl.QH(),k.N_=f,h=c.parent,h&&h.options.showDragDropTip&&(i=d.x+d.width+t,j=d.y+d.height+t,h.uw(k.O_(f),i,j))))},O_:function(a){var b,c,d,e,g,h,i,j,l;return a&&k?(b=this.kj,c=b.Ix(),d=b.getActiveRowIndex(),e=b.getActiveColumnIndex(),g=void 0,d=d<0?0:d,e=e<0?0:e,1===I(c)&&1===H(c)?g=f.rf(o,o,a.row-d,a.col-e,!0,!0):(h=void 0,i=void 0,j=void 0,l=void 0,c.row===-1&&c.col>=0?(h=j=f.BAND_INDEX_CONST,i=a.col-e,l=a.col-e+H(c)-1):c.col===-1&&c.row>=0?(i=l=f.BAND_INDEX_CONST,h=a.row-d,j=a.row-d+I(c)-1):c.row>=0&&c.col>=0&&(h=a.row-d,j=a.row-d+I(c)-1,i=a.col-e,l=a.col-e+H(c)-1),g=f.uf(o,o,h,i,j,l,!0,!0,!0,!0,d,e)),b.Cf().unparse(b.yj(),g,d,e)):p},wH:function(){var a,b,c,d,f,g,h,i,j,l,n,p,q,r=this,s=!1,t="",u=!1,v=r.kj;r.rG=o,r.RG(),b=v.Ix(),a=b&&(I(b)>0||H(b)>0)?{r:b.row,c:b.col,rc:I(b),cc:H(b)}:{r:v.Jl,c:v.Kl,rc:1,cc:1},r.zG===!0&&r.OG===!0&&(c=a.r,d=a.c,f=a.rc,g=a.cc,h=r.gA.row,i=r.gA.col,j=v.options.protectionOptions,l=void 0,n={fromRow:c,fromColumn:d,rowCount:f,columnCount:g,toRow:h,toColumn:i,isDragInsert:r.OH,isDragCopy:r.PH},v.Wq(e.Events.BeforeDragDrop,n),t=n.invalidMessage,t&&0!==t.length||(!r.OH||c!==-1&&d!==-1?h===c&&i===d||((v.qu(c,d,f,g)||v.qu(h,i,f,g))&&(s=!0,t=z().Exp_ChangeMergedCell),s||k&&(v.Ns(c,d,f,g)||v.Ns(h,i,f,g))&&(s=!0,t=z().Exp_ChangePartOfArray),!s&&v.options.isProtected&&(!r.PH&&v.ou(m(c,d,f,g))||v.ou(m(h,i,f,g)))&&(s=!0,t=z().Exp_CellReadOnly),s||(l=M(v,!0,c,d,h,i,f,g,r.PH,!1,1023),l||(u=v.wu().execute({cmd:"dragDrop",sheetName:v.name(),fromRow:c,fromColumn:d,toRow:h,toColumn:i,rowCount:f,columnCount:g,copy:r.PH,insert:!1,option:1023}),M(v,!1,c,d,h,i,f,g,r.PH,!1,1023)))):d>=0?(r.PH&&(i<=d||i>=d+g)||!r.PH&&(i<d||i>d+g))&&((v.qu(-1,d,-1,g)||v.qu(-1,i,-1,0))&&(s=!0,t=z().Exp_ChangeMergedCell),p=j&&j.allowDragInsertColumns,!s&&v.options.isProtected&&p!==!0&&(s=!0,t=z().Exp_ColumnReadOnly),s||(l=M(v,!0,-1,d,-1,i,-1,g,r.PH,!0,1023),l||(u=v.wu().execute({cmd:"dragDrop",sheetName:v.name(),fromRow:-1,fromColumn:d,toRow:-1,toColumn:i,rowCount:-1,columnCount:g,copy:r.PH,insert:!0,option:1023}),M(v,!1,-1,d,-1,i,-1,g,r.PH,!0,1023)))):c>=0&&d<0&&(r.PH&&(h<=c||h>=c+f)||!r.PH&&(h<c||h>c+f))&&((v.qu(c,-1,f,-1)||v.qu(h,-1,0,-1))&&(s=!0,t=z().Exp_ChangeMergedCell),s||k&&(v.Ns(c,-1,f,-1)||v.Ns(h,-1,0,-1))&&(s=!0,t=z().Exp_ChangePartOfArray),q=j&&j.allowDragInsertRows,!s&&v.options.isProtected&&q!==!0&&(s=!0,t=z().Exp_RowReadOnly),s||(l=M(v,!0,c,-1,h,-1,f,-1,r.PH,!0,1023),l||(u=v.wu().execute({cmd:"dragDrop",sheetName:v.name(),fromRow:c,fromColumn:-1,toRow:h,toColumn:-1,rowCount:f,columnCount:-1,copy:r.PH,insert:!0,option:1023}),M(v,!1,c,-1,h,-1,f,-1,r.PH,!0,1023)))))),r.OG=!1,r.zG=!1,r.L_=o,r.OH=!1,r.PH=!1,r.N_=o,r.xw(),u||(r.gA={},v.repaint()),s&&v.Os(3,t)},hG:function(a){var b=this;b.BG!==!0&&b.OG!==!0&&(b.P_(),b.A$&&(b.OG=!0,b.BG=!0,b.$I=!0,b.Q_=a,b.R_=B(a),b.S_=A(a),b.T_=B(a),b.U_(),b.rG={KG:A(a),MG:B(a),sG:E(a)},b.qG()))},U_:function(){var a,b=this,c=C(b.kj),d=D(b.kj),e=b.V_();e>=0&&e<c?b.W_=0:e>=c&&e<=F(b.kj)&&(b.W_=1),b.X_()?b.Y_=1:b.Y_=b.Q_,a=b.Z_(),a>=0&&a<d?b.__=0:a>=d&&a<=G(b.kj)&&(b.__=1),b.a0()?b.b0=1:b.b0=b.R_},CG:function(){var a,b,c,d=this;d.rG&&d.BG&&d.OG&&d.A$&&(d.S_=d.iH(),d.T_=d.jH(),d.c0=d.fH(),d.d0=d.gH(),d.c0>=0&&d.d0>=0&&(d.e0(),d.f0(),a=d.kj.parent,b=!1,c=d.g0(),c&&(b=d.h0(d.A$,d.A_,!0)),a&&a.options.showDragFillTip&&b&&d.i0(),d.j0()),d.NG())},k0:function(){var a,b,c,e,f,g,i,j,k,l,m,n,p,q=this,r=q.kj,s=q.A$,t=q.A_,u=r.parent.options.defaultDragFillType,v=q.l0(3===u?5:u),w=q.m0(),x=q.g0();return 1===v?(a=0===w||1===w?1:0,c=new h.z$(r),e=c.B$(x,a,!0),b=q.n0(w,t,s),f=r.getActualStyle(b.row,b.col),g=f.cellType||r.Al(),i=f.formatter?f.formatter:f._autoFormatter,j=d.Formatter&&d.Formatter.GeneralFormatter,e instanceof Date&&j&&(i=new j(d.Common.CultureManager.q4().DateTimeFormat.shortDatePattern+" hh:mm:ss AM/PM;@","0")),k=q.rG,l=3,k&&(l=k.sG),m={sheet:r,row:b.row,col:b.col,sheetArea:l,quotePrefix:f.quotePrefix},n={},g.format(e,i,n,m)):0===v?(b=q.n0(w,t,s),p=r.getFormula(b.row,b.col),p?o:r.getText(b.row,b.col)):o},n0:function(a,b,c){var d,e=c.row,f=c.col,g=I(c),h=H(c),i=m(e,f,1,1);return 3===a?(d=I(b)%g,d=0===d?e+g-1:e+d-1,i.row=d,i.col=f):1===a?(d=H(b)%h,d=0===d?f+h-1:f+d-1,i.row=e,i.col=d):0===a?(d=H(b)%h,d=0===d?f:f+h-d,i.row=e,i.col=d):2===a&&(d=I(b)%g,d=0===d?e:e+g-d,i.row=d,i.col=f),i},i0:function(){var a,b,c,d,e,f,g=this,h=g.kj,i=g.l0();return 4===i||g.a0()||g.X_()?(g.xw(),o):(d=g.m0(),e=g.g0(),f=h.cm(e),c=g.k0(),3===d||1===d?(a=f.x+f.width+u,b=f.y+f.height+u):0===d?(a=f.x+u,b=f.y+f.height+u):2===d&&(a=f.x+f.width+u,b=f.y+u),void g.uw(c,a,b))},j0:function(){var a=this;a.o0(),a.p0(),a.q0(),a.r0=a.g0()},o0:function(){var a,b=this.kj;this.r0&&(a=b.cm(this.r0),a.x-=2,a.y-=2,a.width+=4,a.height+=4,b.yl.dm(a))},p0:function(a){var b=this.kj;b.yl.im(this.A$,o,a)},q0:function(){var a,b,c=this.kj,d=c.yl,e=this.g0();e&&(a=d.bm(),b=c.cm(e),a.save(),a.beginPath(),d.RI(a,b),a.restore())},f0:function(){this.A_=this._I()},a0:function(){return this.A$.col===-1&&this.A$.row!==-1},X_:function(){return this.A$.row===-1&&this.A$.col!==-1},ZI:function(){return 4===this.dJ||5===this.dJ},_I:function(){var a=this,b=-1,c=-1,d=-1,e=-1;switch(a.dJ){case 0:a.X_()?(b=-1,d=-1):(b=a.V_(),d=I(a.A$)),c=a.d0,e=a.Z_()-c;break;case 1:a.X_()?(b=-1,d=-1):(b=a.V_(),d=I(a.A$)),c=a.s0()+1,e=a.d0-c+1;break;case 2:b=a.c0,d=a.V_()-b,a.a0()?(c=-1,e=-1):(c=a.Z_(),e=H(a.A$));break;case 3:b=a.t0()+1,d=a.c0-b+1,a.a0()?(c=-1,e=-1):(c=a.Z_(),e=H(a.A$));break;case 5:b=a.c0,d=a.t0()-b+1,a.a0()?(c=-1,e=-1):(c=a.Z_(),e=H(a.A$));break;case 4:a.X_()?(b=-1,d=-1):(b=a.A$.row,d=I(a.A$)),c=a.d0,e=a.s0()-c+1}return m(b,c,d,e)},u0:function(){var a=this.t0();return a!==-1?this.kj.Gr(this.Y_).findRow(a):o},v0:function(){return this.kj.Gr(this.S_).findRow(this.w0())},w0:function(){return this.kj.getViewportBottomRow(this.S_)},x0:function(){var a=this.s0();return a!==-1?this.kj.Hr(this.b0).findCol(a):o},y0:function(){return this.kj.Hr(this.T_).findCol(this.z0())},z0:function(){return this.kj.getViewportRightColumn(this.T_)},e0:function(){var a,b,c,d,e,f,g,h,i,j,k,m,n,o,p,s,t,u,v,w,x=this,y=x.a0(),z=x.X_(),A=l(x.kj.Ws()).offset(),B=x.$F.e,C=x.$F.x,D=x.$F.y,E=B.pageX-A.left,F=B.pageY-A.top,G=!1;y||z?z?x.d0>=x.Z_()&&x.d0<=x.s0()?x.dJ=4:x.d0<x.Z_()?x.dJ=0:x.d0>x.s0()&&(x.dJ=1):y&&(x.c0>=x.V_()&&x.c0<=x.t0()?x.dJ=5:x.c0<x.V_()?x.dJ=2:x.c0>x.t0()&&(x.dJ=3)):x.c0>=x.V_()&&x.c0<=x.t0()?x.d0>=x.Z_()&&x.d0<=x.s0()?(a=r(x.d0-x.s0()),b=r(x.c0-x.t0()),b>a?x.dJ=5:b<a?x.dJ=4:(c=x.u0(),c||(c=x.v0()),D>c.y+c.height?x.dJ=3:(d=x.x0(),d||(d=x.y0()),e=d.x+d.width-C,f=c.y+c.height-D,E>=d.x&&E<=d.x+d.width&&F>=c.y&&F<=c.y+c.height?e>=f?x.dJ=4:x.dJ=5:G=!0))):x.d0<x.Z_()?x.dJ=0:x.d0>x.s0()&&(x.dJ=1):x.c0<x.V_()?x.d0>=x.Z_()&&x.d0<=x.s0()?x.dJ=2:x.d0<x.Z_()?(a=r(x.d0-x.Z_()),b=r(x.c0-x.V_()),b>=a?x.dJ=2:x.dJ=0):x.d0>x.s0()&&(a=r(x.d0-x.s0()),b=r(x.c0-x.V_()),b>=a?x.dJ=2:x.dJ=1):x.c0>x.t0()&&(x.d0>=x.Z_()&&x.d0<=x.s0()?x.dJ=3:x.d0<x.Z_()?(a=r(x.d0-x.Z_()),b=r(x.c0-x.t0()),b>=a?x.dJ=3:x.dJ=0):x.d0>x.s0()&&(a=r(x.d0-x.s0()),b=r(x.c0-x.t0()),b>=a?x.dJ=3:x.dJ=1)),g=x.kj.yl.rH,g&&(h=x.t0(),i=x.s0(),j=h+1,k=i+1,m=x.kj.getRowHeight(h,3),n=x.kj.getColumnWidth(i,3),o=x.kj.getRowHeight(j,3),p=x.kj.getColumnWidth(k,3),s=g.x+g.width/2-q(10,n/2),t=g.x+g.width/2+q(10,p/2),u=g.y+g.height/2-q(10,m/2),v=g.y+g.height/2+q(10,o/2),w=!1,w=y||z?z?s<=E&&E<=t:u<=F&&F<=v:s<=E&&E<=t&&u<=F&&F<=v,w||G?(x.$I=!0,x.dJ=4):x.$I=!1)},V_:function(){return this.A$?this.A$.row===-1?0:this.A$.row:-1},t0:function(){var a=this;return a.A$?a.A$.row===-1?F(a.kj)-1:a.A$.row+I(a.A$)-1:-1},Z_:function(){return this.A$?this.A$.col===-1?0:this.A$.col:-1},s0:function(){var a=this;return a.A$?a.A$.col===-1?G(a.kj)-1:a.A$.col+H(a.A$)-1:-1},hla:function(a,b){var c,d,e,f,g,h,i=m(b.row,b.col,b.rowCount,b.colCount),j=this.kj;return j.ITa.findSpan(a.row,a.col)&&(c=a.rowCount,d=i.rowCount,0!==Math.floor(d%c)&&(e=Math.ceil(d/c)*c,i.row+e<=j.getRowCount()&&(i.rowCount=e)),f=a.colCount,g=i.colCount,0!==Math.floor(g%f)&&(h=Math.ceil(g/f)*f,i.col+h<=j.getColumnCount()&&(i.colCount=h))),i},xH:function(){var a,b,c,d,e,f,g=this,h=g.kj;return g.rG=o,g.RG(),g.xw(),g.BG&&g.OG?(g.BG=!1,g.OG=!1,a=g.g0(),void(a&&(b=g.h0(g.A$,g.A_),!b||g.$I?(g.A0(),g.WA(a)):(c=h.mm.A$,d=g.hla(c,g.A_),e=g.l0(),h.wu().execute({cmd:"fill",sheetName:h.name(),startRange:c,fillRange:d,autoFillType:e,fillDirection:h.mm.m0()}),f=g._$,!f&&g.B0()&&h.parent.options.showDragFillSmartTag&&4!==g.$$?g.C0(g.$$):g.WA(a),g.A0())))):void g.A0()},C0:function(a){var b=this.kj,c=b.yl.rH,d={x:c.x+c.width,y:c.y+c.height,fillType:a};b.NF=new g.K_(b.parent.xv(),b,d),b.NF.OT()},P_:function(){var a=this.kj,b=a.ITa.getSelections();1===b.length?this.A$=b[0]:a.Jl>=0&&a.Kl>=0&&(this.A$=m(a.Jl,a.Kl,1,1))},A0:function(){this.OG=!1,this.BG=!1},WA:function(a){this.kj.yl.im(a)},l0:function(a){var b,c=this,d=c.kj,e=a!==p?a:d.parent.options.defaultDragFillType;return c.ZI()?4:5!==e?e:(b=1===I(c.A$)&&1===H(c.A$)&&!c.X_()&&!c.a0(),b?c.RH?1:0:c.RH?0:1)},g0:function(){var a,b,c,d,e=this;return e.A$?e.ZI()?e.A$:e.A_?(a=0,b=0,c=0,d=0,e.D0()?(a=2===e.dJ?e.A_.row:e.A$.row,b=I(e.A$)+I(e.A_),c=e.A$.col,d=H(e.A$)):(a=e.A$.row,b=I(e.A$),c=0===e.dJ?e.A_.col:e.A$.col,d=H(e.A$)+H(e.A_)),m(a,c,b,d)):o:o},h0:function(a,b,c){var d=this.kj,e=!0,f="";return d.ITa.MTa(b.row,b.col,I(b),H(b))&&!O(d,a,b)&&(e=!1,f=z().Exp_FillRangeContainsMergedCell),e&&d.options.isProtected&&d.ou(b)&&(e=!1,f=z().Exp_FillCellsReadOnly),e||c||d.Os(2,f),e},B0:function(){return this.E0()||this.F0()},E0:function(){return 3===this.dJ||1===this.dJ},F0:function(){return 0===this.dJ||2===this.dJ},D0:function(){return 2===this.dJ||3===this.dJ||5===this.dJ},m0:function(){var a=this.dJ;return a>=0&&a<=3?a:4===a?0:5===a?2:3},tr:function(a,b,c){var d,e,f,g=this,h=o,i=A(a),j=B(a),k=g.kj,l=k.parent;if(n(i)||n(j)||!l.options.allowUserDragDrop&&!l.options.allowUserDragFill)return h;if(d=k.Ix(),d.row===-1&&d.col===-1)return h;if(i>=0&&j>=0&&1===k.ITa.getSelections().length){if(g.qH(i,j,b,c))return h;e=k.getRangeRect(i,j,d),e.x-4<b&&b<e.x+4&&e.y<=c&&c<e.y+e.height&&(h={action:v,side:"left"}),h||(f=g.kj.yl.rH,f&&f.x<=b&&b<=f.x+f.width&&f.y<=c&&c<=f.y+f.height&&(h={action:v,side:w})),h||e.x+e.width-4<b&&b<e.x+e.width+4&&e.y<=c&&c<e.y+e.height&&(h={action:v,side:"right"}),h||e.y-4<c&&c<e.y+4&&e.x<=b&&b<e.x+e.width&&(h={action:v,side:"top"}),h||e.y+e.height-4<c&&c<e.y+e.height+4&&e.x<=b&&b<e.x+e.width&&(h={action:v,side:"bottom"}),h&&(b<e.x||b>e.x+e.width||c<e.y||c>e.y+e.height)&&(h.outside=!0)}return l&&l.options.allowUserDragDrop||h&&h.side!==w&&(h.side=o),l&&l.options.allowUserDragFill||h&&h.side===w&&(h.side=o),h},Hyb:function(a,b){var c=this,d=c.kj,e=d.getSpan(a,b,3);return e?!n(c.kj.getValue(e.row,e.col,3,0)):!n(c.kj.getValue(a,b,3,0))},Iyb:function(a,b,c,d){var e,f=this;for(e=b;e<b+d;e++)if(!f.Hyb(a,e))return!1;return!0},Jyb:function(a,b,c,d){var e,f=this;for(e=b;e<b+d;e++)if(f.Hyb(a,e))return!1;return!0},Kyb:function(a,b,c,d){var e,f,g,h,i,j,k,l=this,m=l.kj;if("left"===d){for(e=0;e<b;e++)l.Hyb(a,e)&&(c.searchArray[e]=!0);for(f=b-1,g=1;c.searchArray[f];)f--,g++;c.searchLength=g}if("right"===d){for(h=m.getColumnCount(),i=b;i<h;i++)l.Hyb(a,i)&&(c.searchArray[i-b]=!0);for(j=0,k=1;c.searchArray[j];)j++,k++;c.searchLength=k}return c},Lyb:function(a,b,c,d,e){var f,g,h,i,j,k=this,l=k.kj,m=0,n={searchArray:Array(d),searchLength:1},o={searchArray:Array(l.getColumnCount()-e),searchLength:1};for(f=b;f<c;f++)n=k.Kyb(f,a.col,n,"left"),o=k.Kyb(f,a.col+a.colCount-1,o,"right");for(;k.Jyb(c+m,a.col,1,a.colCount);){for(g=!0,h=!1,n=k.Kyb(c+m,d,n,"left"),o=k.Kyb(c+m,e,o,"right"),i=d-1;i>=d-n.searchLength;i--)if(k.Hyb(c+m,i)){m++,g=!1;break}if(g){for(j=e+1;j<=e+o.searchLength;j++)if(k.Hyb(c+m,j)){m++,h=!0;break}if(!h)return m}}return m},Myb:function(a){for(var b=this,c=0;b.Iyb(a.row+a.rowCount+c,a.col,1,a.colCount);)c++;return c},Nyb:function(a){var b,c,d,e,f=this;return f.Iyb(a.row+a.rowCount,a.col,1,a.colCount)?f.Myb(a):(d=a.row-2,e=a.row+a.rowCount,1===a.colCount?c=b=a.col:(b=a.col,c=a.col+a.colCount-1),f.Lyb(a,d,e,b,c))},cyb:function(){var a,b,c,d=this,e=d.kj,f=e.mm.A$,g=d.Nyb(f);0!==g&&(a=m(f.row+f.rowCount,f.col,g,f.colCount),d.dJ=3,d.A_=a,b=d.l0(),c=d._$,e.wu().execute({cmd:"fill",sheetName:e.name(),startRange:f,fillRange:a,autoFillType:b,fillDirection:d.dJ}),!c&&e.parent.options.showDragFillSmartTag&&4!==d.$$&&d.C0(d.$$))}}),l.extend(e.oJ.prototype,{gJ:function(a,b){var c,d=this.kj,f=a.x,g=a.y,h=K(a),i=J(a),j=d.Ix(),k=d.am(),l=4,m=-2.5;return b>2007&&(l=5,m=-3),c=new e.Rect((-4),(-4),l,l),j.col===-1?(c.x=k.kt,c.y=g+i+m):j.row===-1?(c.x=f+h+m,c.y=k.nt):(c.x=f+h+m,c.y=g+i+m),c},eJ:function(a,b,c,d,f){var g,h,i=this,j=i.kj,k=j.am(),l=j.parent,m=l&&l.Vv,n=i.gJ(d,m),o=n.x,p=n.y,q=K(n),r=J(n),t=k.Ft(b,c);f&&!n.intersectRect(f)||n.intersectRect(t)&&(g=-1.5,m>2007&&(g=-1),h=new e.Rect(o+g,p+g,q+2,r+2),h.x=s(h.x,t.x),h.y=s(h.y,t.y),i.dm(h),a.save(),f&&!f.containsRect(n)&&(a.rect(f.x,f.y,K(f),J(f)),a.clip()),a.beginPath(),a.fillStyle=e.Rm.Om(j,j.getSelectionBorderColor()),a.fillRect(o,p,q,r),i.rH=new e.Rect(n.x,n.y,4,4),a.beginPath(),a.restore())},RI:function(a,b){var c=b.x,d=b.y,e=c+K(b),f=d+J(b);L(a,c,d,e,d),L(a,c,f,e,f),L(a,c,d,c,f),L(a,e,d,e,f)},NI:function(a,b){var c,d,f,g,h,i,j,k,l,m,n,o=this,p=o.kj,q=p.mm.gA,r=q.hitTarget;q&&r&&(c=q.hitCol,d=q.hitRow,f=p.mm.OH,g=p.OI,a.save(),f!==!0||q.row!==-1&&q.col!==-1?(g&&(h=new e.Rect(g.x-2,g.y-2,K(g)+4,J(g)+4),o.dm(h,b),o.im(p.Ix(),b)),a.rect(b.x,b.y,K(b),J(b)),a.clip(),a.beginPath(),o.RI(a,q)):q.row===-1&&q.col!==-1?(i=p.Hr(0).findCol(c),i||(i=p.Hr(1).findCol(c)),i||(i=p.Hr(2).findCol(c)),i&&(j=i.x,k=K(i),q.col=c,r.x>j+k/2&&(q.col<p.cr()||q.col===p.dr())&&(j=i.x+K(i),q.col++),g&&0===K(g)||(g=p.mm.gA),g&&(h=new e.Rect(g.x-2,g.y-2,K(g)+4,J(g)+4),o.dm(h,b),o.im(p.Ix(),b)),p.QI=new e.Rect(j,q.y,0,J(q)),a.rect(b.x,b.y,K(b),J(b)),a.clip(),a.beginPath(),L(a,j,q.y,j,q.y+J(q)))):q.row!==-1&&q.col===-1&&(l=p.Gr(0).findRow(d),l||(l=p.Gr(1).findRow(d)),l||(l=p.Gr(2).findRow(d)),l&&(m=l.y,n=J(l),q.row=d,r.y>m+n/2&&(q.row<p.hr()||q.row===p.ir())&&(m=l.y+J(l),q.row++),g&&0===J(g)||(g=p.mm.gA),g&&(h=new e.Rect(g.x-2,g.y-2,K(g)+4,J(g)+4),o.dm(h,b),o.im(p.Ix(),b)),p.QI=new e.Rect(q.x,m,K(q),0),a.rect(b.x,b.y,K(b),J(b)),a.clip(),a.beginPath(),L(a,q.x,m,q.x+K(q),m))),a.beginPath(),a.restore())},QH:function(){var a,b,c,d,f,g,h,i,j,k,l,m,n=this,o=n.kj,p=o.yt;p&&p.dragInfo&&(a=n.bm(),b=o.am(),c=o.mm.MI,d=o.mm.gA,f=o.cm(c),g=o.frozenTrailingColumnCount(),h=o.frozenTrailingRowCount(),i=o.getColumnCount()-g,j=o.getRowCount()-h,k=b.Ft(1,1),g>0&&c.col+c.colCount<=i&&f.x+K(f)>k.x+K(k)&&f.intersectRect(k)&&(f=f.getIntersectRect(k)),h>0&&c.row+c.rowCount<=j&&f.y+J(f)>k.y+J(k)&&f.intersectRect(k)&&(f=f.getIntersectRect(k)),l=new e.Rect(b.kt,b.nt,b.lt+b.js,b.ot+b.gs),K(f)>0&&J(f)>0?(d.x=f.x,d.y=f.y,d.width=K(f)-1,d.height=J(f)-1,n.NI(a,l)):0!==K(f)&&0!==J(f)||(d.x=f.x,d.y=f.y,d.width=K(f),d.height=J(f),n.NI(a,l)),m=o.mm.OH,m===!0?o.OI=o.QI:o.OI=new e.Rect(d.x,d.y,K(d),J(d)))}})},"./dist/plugins/fill/fill-ui.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("./dist/plugins/fill/fill.ns.js"),g=e.GC$,h=e.Ul.Nl,i=e.Ul.vl,j=g(document),k=" btn btn-default",l="ui-state-hover ",m="gc-fill-type-item",n="gc-fill-menu-container",o=l+"gc-smartMenu-item-hover",p=l+"gc-smart-tag-hover",q="ui-icon ui-icon-check gc-check-image",r="gc-menu-item-input",s="ui-state-active gc-smart-tag-active",t="gc-tag-container",u="float",v="left",w="top",x="display",y="width",z="height",A="margin",B="change",C="input[type=radio]",D="mouseover",E="mouseout",F="click",G="fillTypeChanged",H="keydown.ui-fill",I=new d.Common.ResourceManager(f.SR),J=I.getResource.bind(I);function O(a,b,c,d,e,f){var i=a.CH,j=i?"160px":"150px",l=i?"25px":"20px",m=g(h("div")).css([x,y,z,"padding",A],["block",j,l,0,0]).addClass("gc-smartMenu-item-default "+c+" ui-state-default"+k),n=g(h("div")).addClass(r).appendTo(m),o=g(h("div")).addClass("gc-menu-item-text").appendTo(m),p=g(h("input")).attr({id:b,value:e,type:"radio",name:"fill-group",style:"display:none"}).appendTo(n);return g(h("span")).text(d).appendTo(o),f?(n.addClass(q),p.prop("checked","checked")):p.prop("checked",""),m}K=function(a){N(b,a);function b(b,c){var d,e=a.call(this,b,i(c.parent.qo))||this;return e.kj=c,e.v_=g("."+t),d=e.w_=e.yo(),d.addClass(n),O(c,"smartMenuCopyCells",m,J().CopyCells,"0",!0).appendTo(d),O(c,"smartMenuFillSeries",m,J().FillSeries,"1").appendTo(d),O(c,"smartMenuFillFormattingOnly",m,J().FillFormattingOnly,"2").appendTo(d),O(c,"smartMenuFillWithoutFormatting",m,J().FillWithoutFormatting,"3").appendTo(d),e}return b.prototype.Ao=function(){a.prototype.Ao.call(this)},b.prototype.QQ=function(){var a,b=this,c=g("."+m);c.bind(D,function(){g(this).addClass(o)}).bind(E,function(){g(this).removeClass(o)}).bind(F,function(){var a,c=g(this),d=b.v_,e=c.find(C),f=e[0];f.checked=!0,e.trigger(B),d.trigger(F),d.trigger(E),c.removeClass(o),a=parseInt(g(f).attr("value"),10),b.x_(a)}),a=b.w_.find(C),a.bind(B,function(){g("."+r).removeClass(q),g(this).parent().addClass(q)})},b.prototype.y_=function(){g("."+m).unbind(D).unbind(E).unbind(F),this.w_.find(C).unbind(B)},b.prototype.x_=function(a){var b=this,c=b.kj,d=c.mm;if(b.z_!==a){b.z_=a,c.U$=!0,c.suspendEvent();try{e.Commands.undo.execute(c.parent,{sheetName:c.name()})}finally{c.resumeEvent()}c.U$=!1,c.wu().execute({cmd:"fill",sheetName:c.name(),startRange:d.A$,fillRange:d.A_,autoFillType:a,fillDirection:d.dJ}),b.v_.trigger(G,a)}},b.prototype.MQ=function(){
return!!g("."+n).length},b}(e.Go),function(a){a[a.Tag=0]="Tag",a[a.Down=1]="Down"}(L||(L={}));function P(a){var b="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA",c="AAAASCAYAAA",d="AAAACXBIWXMAAA7",e="lDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjanVN";return a?b+"Ac"+c+"CXScT7"+d+"DAAAOwwHHb6hkAAAKTW"+e+"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":b+"BI"+c+"BWzo5X"+d+"EAAAOxAGVKw4bAAAKT2"+e+"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"}M=function(a){N(b,a);function b(b,c,d){var e,f=a.call(this,b,i(c.parent.qo))||this;return f.kj=c,f.B_=d,e=f.C_=f.yo().addClass("gc-smart-tag-default "+t+" gc-no-user-select ui-widget-header"+k),f.D_=g(h("img")).attr("src",P(0)).css(u,v).appendTo(e),f.E_=g(h("img")).attr("src",P(1)).css([u,x],[v,"none"]).appendTo(e),f}return b.prototype.F_=function(a,b,c){var d=this,e=d.kj.CH,f=g(b),h=d.G_,i=d.E_,j=e?"38px":"32px",k=e?"24px":"18px";h&&h.MQ()||(a?f.addClass(p):f.removeClass(p),f.css([y,z],[a?j:k,k]),c||(a?i.show():i.hide()))},b.prototype.H_=function(){var a=this,b=a.C_,c=e.Ul.rl(),d=e.Ul.Ml.safari&&(c.ipad||c.iphone);b.bind(D,function(){a.F_(!0,this,d)}).bind(E,function(){a.F_(!1,this,d)}),b.bind(F,function(){var b,c,e=g(this),f=a.G_,h=a.E_,i=f&&f.MQ();i?(d&&h.hide(),e.removeClass(s),a.I_()):(d&&h.show(),e.addClass(s),a.J_(),b=a.w_,c=b&&b.find(C)[a.B_.fillType],c&&!c.checked&&(c.checked=!0,g(c).trigger(B)))}),j.bind(H,function(b){27===b.keyCode&&(a.G_&&a.G_.MQ()?a.I_():a.close(),e.Ul.nl(b))})},b.prototype.OT=function(){var a=this,b=a.C_,c=a.B_,d=a.kj.CH,e=d?"3px":"0px",f=d?"24px":"18px";b&&(b.css([v,w],[c.x,c.y]),a.Ao(),a.H_(),a.D_.css(A,e),a.E_.css(z,f),a.hZ())},b.prototype.J_=function(){var a,b,c,d,e,f,g,h=this,i=h.kj;h.G_||(h.G_=new K(i.parent.xv(),i),h.w_=h.G_.yo()),a=h.C_,b=a.css(v),c=a.css(w),d=parseFloat(c),f=h.G_,g=h.B_,f.z_=g.fillType,isNaN(d)||(e=d+a.height()+2),h.w_.css([v,w],[b,e]),f.Ao(),f.QQ(),f.v_.unbind(G).bind(G,function(a,b){g.fillType=b})},b.prototype.I_=function(){var a=this.G_;a&&(a.y_(),a.close())},b.prototype.hZ=function(){var a=this,b=a.G_,c=a.C_;b&&(b.MQ()&&c.trigger(F),c.trigger(E))},b.prototype.close=function(){var b,c=this,d=c.kj;c.hZ(),j.unbind(H),a.prototype.close.call(this),b=d.Xs,b.style.cursor="default",d&&d.zt()},b}(e.Go),b.K_=M},"./dist/plugins/fill/fill.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),c("./dist/plugins/fill/drag-fill.js"),c("./dist/plugins/fill/fill-event.js"),d(c("./dist/plugins/fill/fill-ui.js")),d(c("./dist/plugins/fill/fill.js")),d(c("./dist/plugins/fill/fill.ns.js"))},"./dist/plugins/fill/fill.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("CalcEngine"),g=c("SheetsCalc"),h=c("./dist/plugins/fill/fill.ns.js"),i=!!f,j=d.Common.j,k=null,l=void 0,m=Math.floor,n=e.kf,o=j.Fa,p=j.Pa,q=d.Common.k,r=q.Eb,s=q.ac,t=d.Common.l,u=t.Ra,v=t.Xb,w="number",x="date",y="string",z="default",A=new d.Common.ResourceManager(h.SR),B=A.getResource.bind(A);function Z(a){return!$(a)&&j.Na(a)}function $(a){return"string"==typeof a}!function(a){a[a.copyCells=0]="copyCells",a[a.fillSeries=1]="fillSeries",a[a.fillFormattingOnly=2]="fillFormattingOnly",a[a.fillWithoutFormatting=3]="fillWithoutFormatting",a[a.clearValues=4]="clearValues",a[a.auto=5]="auto"}(C=b.AutoFillType||(b.AutoFillType={})),function(a){a[a.left=0]="left",a[a.right=1]="right",a[a.up=2]="up",a[a.down=3]="down"}(D=b.FillDirection||(b.FillDirection={})),function(a){a[a.column=0]="column",a[a.row=1]="row"}(E=b.FillSeries||(b.FillSeries={})),function(a){a[a.direction=0]="direction",a[a.linear=1]="linear",a[a.growth=2]="growth",a[a.date=3]="date",a[a.auto=4]="auto"}(F=b.FillType||(b.FillType={})),function(a){a[a.day=0]="day",a[a.weekday=1]="weekday",a[a.month=2]="month",a[a.year=3]="year"}(G=b.FillDateUnit||(b.FillDateUnit={}));function _(a){var b=[a];return b.rowCount=1,b.colCount=s(a),b}function aa(a){return a}function ba(a,b,c,e){var f=_(b),g=_(c),h=_([e]),i=a?d.Common.qc(f,g,h,!0,aa,k,k,k):d.Common.sc(f,g,h,!0,k,k,k),j=i&&i[0][0];return typeof j===w?d.Common.o.Lma(j,13):j}function ca(a){return a.toLowerCase()}H=function(){function a(a){this.e_=[],this.f_=[],this.Oyb=[],this.Nc=k,this.Pyb=[],this.Qyb=k,this.g_=o(a)?-1:a}return a.prototype.h_=function(){return s(this.e_)},a.prototype.Ey=function(){var a=this,b=[],c=a.f_,d,e;for(d=0;d<s(c);d++)e=c[d],b.push("object"===a.Nc?a.Wh(e):e);return b},a.prototype.i_=function(){var a,b,c,d=this,e=d.e_;if(s(e)>0){for(a=d.g_,b=[],c=void 0,a===-1&&(a=e[0]),c=0;c<s(e);c++)b[c]=e[c]-a+1;return b}return k},a.prototype.j_=function(){var a=this.f_;return s(a)?a:k},a.prototype.Eb=function(a,b,c){if(!Z(c))throw Error(B().Exp_NumberOnly);var d=this;o(d.Nc)&&(d.Nc=c instanceof Date?x:w),r(d.e_,a,b),r(d.f_,a,p(c))},a.prototype.Sb=function(a,b){var c=this,d=o(c.Nc);Z(b)?(d&&(b instanceof Date?c.Nc=x:c.Nc=w),c.f_.push(p(b))):d&&(c.Nc=$(b)?y:z),c.e_.push(a),c.Oyb.push(b)},a.prototype.k_=function(a){return this.Nc===x?this.Wh(a):a},a.prototype.Wh=function(a){var b=k;if(a instanceof Date)b=a;else if(f)try{b=f.Convert.Wh(a)}catch(c){b=a}return b},a}(),I=function(){function a(a){this.Ryb=-1,this.Syb=[],this.Tyb=-1,this.kj=a}return a.prototype.Uyb=function(){var a=this.kj;return a.parent&&a.parent.Txb()||[]},a.prototype.Vyb=function(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p=a.length,q=!1;if(a&&p>0)for(b=ca(a[0]),c=this.Uyb(),d=void 0,e=void 0,f=void 0,g=void 0,h=void 0,i=void 0,d=0;d<c.length;d++)if(e=c[d],f=e.map(function(a){return ca(a)}),g=f.indexOf(b),g>=0){for(h=g,j=f.length,k=void 0,l=void 0,m=void 0,n=1;n<p;n++){if(l=ca(a[n]),k=f.indexOf(l),!(k>=0)){q=!1;break}if(m=k-h+j,i){if(i!==m){q=!1;break}}else q=!0,i=m,h=k}if(o=1===p,q||o){q=!0,this.Ryb=g,this.Syb=e,this.Tyb=o?1:i;break}}return q},a.prototype.ge=function(a){var b=this.Syb,c=this.Ryb,d=this.Tyb,e=b.length,f=ea(a,e),g;return g=(c+d*f)%e,b[g]},a}(),J=function(){function a(){this.Uk="",this.Wyb="",this.Tyb=0,this.Xyb=1,this.nBa=""}return a.prototype.ge=function(a){var b=this.Tyb*(a-1)+this.Xyb,c=this.nBa.length,d=""+Math.abs(b),e=d.length,f=this.nBa+d,g=f.length;return d=e>c?d:f.substr(g-c),this.Uk+d+this.Wyb},a}();function da(a){var b,c,d=a.length,e=1;if(d>1)for(e=a[1]-a[0],c=2;c<d;c++)if(b=a[c]-a[c-1],e!==b){e=0;break}return e}function ea(a,b){return a>0?(a-1)%b:b-Math.abs(a)%b-1}function fa(a){for(var b="",c=0;c<a;)b+="0",c++;return b}function ga(a,b){return 12*(b.getFullYear()-a.getFullYear())+(b.getMonth()-a.getMonth())}function ha(a){return a.every(function(a){var b=a.getDate();return 1===b})}function ia(a){return a.every(function(a){var b=a.getFullYear(),c=a.getMonth(),d=a.getDate(),e=new Date(b,c+1,0).getDate();return d===e})}function ja(a){var b=0,c=a.every(function(a){var c=a.getDate();return b||(b=c),b===c});return c?b:0}function ka(a,b){var c,d,e=a.length,f=!0;for(d=2;d<e&&(c=ga(a[d-1],a[d]),f=b===c,f);d++);return f}function la(a,b){return new Date(a,b+1,0).getDate()}function ma(a){return"0"===a[0]}function na(a,b,c){var d,e,f,g,h,i=c?1:2,j=c?2:1;return b.every(function(b){return d=b.match(a.Yyb),!!d&&(e=d[i],h=e.length,h>a.Zyb&&ma(e)&&(a.Zyb=h),f=parseInt(e,10),g=d[j],o(a.Qyb)&&(a.Qyb=g),g!==a.Qyb?(a.$yb=!0,a.Qyb=k,a.Pyb=[]):a.Pyb.push(f),!0)})}K=function(){function a(){}return a.prototype.match=function(a){return!1},a.prototype.getTargetValue=function(a,b,c,d,e){},a.prototype.resetProp=function(){},a.prototype._yb=function(a,b){return a.indexOf(b+1)},a}(),L=function(a){Y(b,a);function b(){var b=null!==a&&a.apply(this,arguments)||this;return b.azb=0,b.Nc=x,b}return b}(K),M=function(a){Y(b,a);function b(){return null!==a&&a.apply(this,arguments)||this}return b.prototype.match=function(a){var b=this.azb=ga(a[0],a[1]),c=ha(a),d=a.length,e=!0;return c&&d>2&&(e=ka(a,b)),c&&e},b.prototype.getTargetValue=function(a,b){var c=a.Oyb,d=c[0],e=d.getFullYear(),f=d.getMonth(),g=b-1,h=this.azb,i=f+h*g;return new Date(e+parseInt(i/12,10),i%12,1)},b}(L),N=function(a){Y(b,a);function b(){return null!==a&&a.apply(this,arguments)||this}return b.prototype.match=function(a){var b=this.azb=ga(a[0],a[1]),c=ia(a),d=a.length,e=!0;return c&&d>2&&(e=ka(a,b)),c&&e},b.prototype.getTargetValue=function(a,b){var c=a.Oyb,d=c[0],e=d.getFullYear(),f=d.getMonth(),g=b-1,h=this.azb,i=f+h*g,j=e+parseInt(i/12,10),k=i%12;return new Date(j,k,la(j,k))},b}(L),O=function(a){Y(b,a);function b(){return null!==a&&a.apply(this,arguments)||this}return b.prototype.match=function(a){var b=this.azb=ga(a[0],a[1]),c=ja(a),d=c>0,e=a.length,f=!0;return this.bzb=c,d&&e>2&&(f=ka(a,b)),d&&f},b.prototype.getTargetValue=function(a,b){var c=a.Oyb,d=c[0],e=d.getFullYear(),f=d.getMonth(),g=b-1,h=this.azb,i=f+h*g,j=e+parseInt(i/12,10),k=i%12;return new Date(j,k,this.bzb)},b}(L),P=function(a){Y(b,a);function b(){var b=a.call(this)||this;return b.Yyb=k,b.Pyb=[],b.Qyb=k,b.$yb=!1,b.Zyb=0,b.Nc=y,b.t_=!1,b.czb=!1,b.dzb=new J,b}return b.prototype.resetProp=function(){this.Pyb=[],this.Zyb=0,this.Qyb=k,this.$yb=!1,this.t_=!1,this.czb=!1},b.prototype.getInternal=function(a){var b,c,d=!1,e=a[0];for(c=1;c<a.length;c++)if(b=a[c],1!==Math.abs(b-e)){d=!0;break}return d},b}(K),Q=function(a){Y(b,a);function b(b){var c=a.call(this)||this;return c.ezb=new I(b),c}return b.prototype.match=function(a){return this.ezb.Vyb(a)},b.prototype.getTargetValue=function(a,b,c,d,e){var f=a.Oyb,g=f.length,h=this._yb(c,d),i=k,j;return h!==-1&&(j=g*e+h+1,i=this.ezb.ge(j)),i},b}(P),R=function(a){Y(b,a);function b(){var b=null!==a&&a.apply(this,arguments)||this;return b.Yyb=/^\d+$/,b}return b.prototype.match=function(a){var b=this,c,d,e;return a.every(function(a){return c=a.match(b.Yyb),!!c&&(e=a.length,e>b.Zyb&&ma(a)&&(b.Zyb=e),d=parseInt(a,10),b.Pyb.push(d),!0)})},b.prototype.getTargetValue=function(a,b,c,d,e){var f=a.Oyb,g=this.dzb,h=da(this.Pyb),i=f.length,j=0!==h,l=j?0:ea(b,i),m=this._yb(c,d),n=k,o;return this.t_=j,this.czb=this.getInternal(a.e_),m!==-1&&(g.Tyb=h,g.Xyb=this.Pyb[l],g.nBa=fa(this.Zyb),o=i*e+m+1,n=g.ge(o)),n},b}(P),S=function(a){Y(b,a);function b(){var b=null!==a&&a.apply(this,arguments)||this;return b.Yyb=/^(.*\D+)(\d+)$/,b}return b.prototype.match=function(a){return na(this,a,!1)},b.prototype.getTargetValue=function(a,b,c,d,e){var f=a.Oyb,g=this.dzb,h=da(this.Pyb),i=f.length,j=0!==h,l=j?0:ea(b,i),m=this._yb(c,d),n=k,o;return this.t_=j,this.czb=this.getInternal(a.e_),m!==-1&&(g.Uk=this.Qyb,g.Tyb=h,g.Xyb=this.Pyb[l],g.nBa=fa(this.Zyb),o=i*e+m+1,n=g.ge(o)),n},b}(P),T=function(a){Y(b,a);function b(){var b=null!==a&&a.apply(this,arguments)||this;return b.Yyb=/^(\d+)(\D+.*)$/,b}return b.prototype.match=function(a){return na(this,a,!0)},b.prototype.getTargetValue=function(a,b,c,d,e){var f=a.Oyb,g=this.dzb,h=da(this.Pyb),i=f.length,j=0!==h,l=j?0:ea(b,i),m=this._yb(c,d),n=k,o;return this.t_=j,this.czb=this.getInternal(a.e_),m!==-1&&(g.Wyb=this.Qyb,g.Tyb=h,g.Xyb=this.Pyb[l],g.nBa=fa(this.Zyb),o=i*e+m+1,n=g.ge(o)),n},b}(P),U=function(a){Y(b,a);function b(){return null!==a&&a.apply(this,arguments)||this}return b.prototype.getTargetValue=function(a,b,c){return ba(!0,a.j_(),c,b)},b}(K),V=function(a){Y(b,a);function b(){var b=null!==a&&a.apply(this,arguments)||this;return b.t_=!0,b}return b.prototype.getTargetValue=function(a,b,c,d){var e=a.Oyb,f=e.length,g=ea(b,f);return e[g]},b}(K),W=function(){function a(a){this.fzb=[new M,new N,new O],this.gzb=[new Q(a),new R,new S,new T],this.hzb=new U,this.izb=new V}return a.prototype.getRule=function(a,b){var c,d,e,f=[],g=this.izb;for(a===x?(f=this.fzb,g=this.hzb):a===y?f=this.gzb:a===w&&(g=this.hzb),c=0;c<f.length;c++)if(d=f[c],e=void 0,d.resetProp(),d.match(b)){if(e=d.$yb,o(e)||e===!1)return d;if(e===!0)return g}return g},a}(),b.Gyb=W,X=function(){function a(a){this.kj=a}return a.prototype.D$=function(a,b,c,d,e){var f=this,g=a?1:2;o(d)&&o(e)?f.E$(b,c,g):f.E$(b,c,g,d,e,k)},a.prototype.E$=function(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r,t,u,v,w,x,y,z=this,A=z.kj,C=A.Tq(a),D=C.row,E=C.col,F=C.rowCount,G=C.colCount;if(A.ITa.MTa(D,E,F,G))throw Error(B().Exp_RangeContainsMergedCell);for(l=arguments.length>3,m=0,n=1===b,o=n?G:F,p=n?D:E,q=n?D+F:E+G,r=l?1:o,t=this.kj.mm.A$,u=t.row===D&&t.rowCount===F&&t.col>E||t.col===E&&t.colCount===G&&t.row>D,g=p;g<q;g++)if(v=u&&l?E+G-1:E,w=u&&l?D+F-1:D,x=n?z.l_(g,v,1,r,b):z.l_(w,g,r,1,b),x&&x.h_())for(j=x.e_[0],l?(k=u?x.f_[s(x.f_)-1]:x.f_[0],h=z.m_(u,k,o,c,d,e,f),u&&(m=o-s(h),j=x.e_[s(x.e_)-1])):h=z.n_(x,o,c),i=0;i<s(h);i++)y=x.k_(h[i]),n?z.o_(A,g,j,g,E+m+i,y,c):z.o_(A,j,g,D+m+i,g,y,c)},a.prototype.l_=function(a,b,c,d,e){for(var f=k,g=this.kj,h,i=1===e,j=i?b+d-1:a+c-1,l=i?b:a;j>=l;)h=i?g.getValue(a,j):g.getValue(j,b),Z(h)&&(f||(f=new H(l)),f.Eb(0,j,h)),j--;return f},a.prototype.m_=function(a,b,c,d,e,f,g){var h,i,j,k=[],l=b,m=e>=0,n=o(f);for(h=0;h<c;h++)i=l>=f,j=l<=f,a&&(n||m&&i||!m&&j)?(k.unshift(l),1===d?l-=e:2===d?l/=e:3!==d||o(g)||(l=this.p_(!0,g,b,l,e,h+1))):!a&&(n||m&&j||!m&&i)&&(k.push(l),1===d?l+=e:2===d?l*=e:3!==d||o(g)||(l=this.p_(!1,g,b,l,e,h+1)));return k},a.prototype.n_=function(a,b,c){var d=[],e,f=1===c;if(f||2===c){for(1===a.h_()&&a.Sb(a.e_[0]+1,f?a.k_(a.f_[0]+1):a.Ey()[0]),e=0;e<b;e++)d.push(ba(!!f,a.j_(),a.i_(),e+1));return d}return k},a.prototype.B$=function(a,b,c,d,e){var f=this,g=f.kj,h=g.ITa,i=g.Tq(a),j=i.row,k=i.col,l=i.rowCount,m=i.colCount,n=g.Tq(g.mm.A$),o=n.row,p=n.col;if(h.qu(o,p,n.rowCount,n.colCount)||h.qu(j,k,l,m))throw Error(B().Exp_ChangeMergedCell);return f.q_(n,l,m,b,c,!(j<o||k<p),d,e)},a.prototype.q_=function(a,b,c,d,e,f,g,h){var i=this,j=f?1:-1,o,p,q,r,s=a.row,t=a.col,u=a.rowCount,v=a.colCount;if(1===d){if(o=m(c/v),p=c%v,!e)for(r=1;r<o;r++)q=n(s,t+r*j*v,u,v),i.r_(a,q,d,4,e,l,l,l,l,g,h);if(e&&o>1&&0===p&&(o-=1,p=v),o>0&&p>0)return q=f?n(s,t+o*v,u,p):n(s,t-o*v+v-p,u,p),i.r_(a,q,d,4,e,f,k,f?0:v-p,l,g,h)}else{if(o=m(b/u),p=b%u,!e)for(r=1;r<o;r++)q=n(s+r*j*u,t,u,v),i.r_(a,q,d,4,e,k,k,k,!0,g,h);if(e&&o>1&&0===p&&(o-=1,p=u),o>0&&p>0)return q=f?n(s+o*u,t,p,v):n(s-o*u+u-p,t,p,v),i.r_(a,q,d,4,e,f,f?0:u-p,k,!0,g,h)}},a.prototype.r_=function(a,b,c,d,e,f,g,h,i,j,l){var m,n,p,q,r,s,t,u,v,z,A,B,C,D,E,F,G,I,J,K,L,M=this,N=M.kj,O=1===c,P=O?a.row:a.col,Q=O?a.col:a.row,R=O?b.row:b.col,S=O?b.col:b.row,T=O?a.rowCount:a.colCount,U=O?a.colCount:a.rowCount,V=O?b.colCount:b.rowCount;for(q=0;q<T;q++){for(s=k,t=k,u=0,v=P+q,z=R+q,A=v,B=z,C=(O?h:g)||0,D=O?0:C,E=O?k:i;u<U;)if(F=Q+u,G=S+u-C,I=k,J=F,K=G,O||(v=F,F=A,z=G,G=B),p=N.ITa.findSpan(v,F),m=N.getFormula&&N.getFormula(v,F),m&&""!==m||(I=N.getValue(v,F)),4!==d||!Z(I)&&!$(I)||(s||(s=new H),n=Z(I)?I instanceof Date?x:w:y,t||(t=n),t!==n))if(I&&s&&s.h_()){if(r=M.s_(O,a,b,A,B,s,e,f,D,E,j,l),e&&r)return r;s=k,t=k}else{if(!p||p&&(O?p.row:p.col)===A)if(L=K===S+V-1,e){if(p&&L||!p&&(L&&f||!f&&K===S))return I}else K<S+V&&K>=S&&(O||!O&&!(i&&N.Ps&&N.Ps(K)))&&M.o_(N,v,F,z,G,I,d,j,l);p?u+=O?p.colCount:p.rowCount:u++}else s.Sb(J,I),p?u+=O?p.colCount:p.rowCount:u++;if(s&&s.h_()&&(r=M.s_(O,a,b,A,B,s,e,f,D,E,j,l),e&&!o(r)))return r}return k},a.prototype.o_=function(a,b,c,d,e,f,h,j,l){var m,n,o,p=a.ITa;i&&(a.hasFormula(b,c)||a.hasFormula(d,e))&&(a.setFormula(d,e,k),0!==h&&4!==h||(m=a.Bj(),a.getFormula(b,c)&&g.CalcOperatorAdjustor.copyFormula(m,b,c,m,d,e,1,1))),a.setValue(d,e,f),o=l?0:64,j&&(o|=128),a.copyTo(b,c,d,e,1,1,o),a.removeSpan(d,e,3),n=p.findSpan(b,c),n&&b===n.row&&c===n.col&&a.Sq(d,e,n.rowCount,n.colCount,3)},a.prototype.s_=function(a,b,c,d,e,f,g,h,i,j,l,m){var n,o,p,q,r,s,t,u,v,w,z,A,B,C,D,E,F=this,G=F.kj,H=a?c.col:c.row,I=a?b.col:b.row,J=a?c.colCount:c.rowCount,K=a?b.colCount:b.rowCount;if(i=i||0,r=f.h_(),s=(H-I-i)/K,t=F.t_(f.e_,f.f_),u=f.Nc,t){for(p=[],o=0;o<r;o++)p[o]=o+1;n=r}else q=f.e_[0],n=f.e_[r-1]-q+1,1===r&&u!==y&&f.Sb(f.e_[0]+1,f.k_(f.f_[0]+1)),p=f.i_();for(o=0;o<n;o++)if(v=f.Oyb,w=void 0,z=void 0,A=void 0,u!==x&&u!==y||(w=G.Fyb&&G.Fyb.getRule(u,v)),w?(z=w.getTargetValue(f,n*s+o+1,p,o,s),t=w.t_?w.t_:t,B=!!w.czb&&w.czb,A=t&&!B):(z=ba(!0,f.j_(),p,n*s+o+1),A=t),C=H+J-1,D=A?f.e_[o]:q+o,E=D+s*K,g){if(t&&(h&&E===C||!h&&E===H)||!t&&E+i===C)return f.k_(z)}else E<H+J&&E>=H&&(a?F.o_(G,d,D,e,E,f.k_(z),4,l,m):j&&G.Ps&&G.Ps(E)||F.o_(G,D,d,E,e,f.k_(z),4,l,m));return k},a.prototype.t_=function(a,b){var c,d,e,f=s(b);if(f<=1||s(a)!==f)return!1;for(c=a[1]-a[0],d=b[1]-b[0],e=2;e<f;e++)if(a[e]-a[e-1]!==c||b[e]-b[e-1]!==d)return!1;return!0},a.prototype.C$=function(a,b){var c=this,d=this.kj,e=d.getSpans(),f=d.Tq(a),g=f.row,h=f.col,i=f.rowCount,j=f.colCount,k;k=0===b?n(g,h+j-1,i,1):1===b?n(g,h,i,1):2===b?n(g+i-1,h,1,j):n(g,h,1,j),k=d.su(e,k),k&&c.u_(k,g,h,i,j,b)},a.prototype.u_=function(a,b,c,d,e,f){var g=this,h=a.row,i=a.col,j=a.rowCount,k=a.colCount,l,o,p,q,r,s,t,u=g.kj.ITa,v;if(0===f?(v=u.MTa(b,c,d,e-k),s=0,t=-1*k,p=e,q=k,r=1):1===f?(v=u.MTa(b,c+k,d,e-k),s=0,t=k,p=e,q=k,r=1):2===f?(v=u.MTa(b,c,d-j,e),s=-1*j,t=0,p=d,q=j,r=0):(v=u.MTa(b+j,c,d-j,e),s=j,t=0,p=d,q=j,r=0),v)throw Error(B().Exp_TargetContainsMergedCells);if(p%q!==0)throw Error(B().Exp_MergedCellsIdentical);for(l=m(p/q),o=1;o<l;o++)g.r_(a,n(h+o*s,i+o*t,j,k),r,0)},a.prototype.p_=function(a,b,c,d,e,f){var g=a?-1:1,h=m(f*e),i=Math.abs(e),j=v(b>1?c:d);if(0===b)j.setDate(j.getDate()+g*e);else if(1===b)for(;i>0;)j.setDate(j.getDate()+g*(e>0?1:-1)),6!==j.getDay()&&0!==j.getDay()&&i--;else 2===b?j.setMonth(j.getMonth()+g*h):j.setFullYear(j.getFullYear()+g*h);return u(j)},a}(),b.z$=X},"./dist/plugins/fill/fill.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/fill/fill.res.en.js");b.SR={en:d}},"./dist/plugins/fill/fill.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.CopyCells="Copy Cells",b.FillSeries="Fill Series",b.FillFormattingOnly="Fill Formatting Only",b.FillWithoutFormatting="Fill Without Formatting",b.Exp_NumberOnly="Only works for Numbers",b.Exp_RangeContainsMergedCell="Range should not have merged cells.",b.Exp_TargetContainsMergedCells="Target range should not have merged cells.",b.Exp_MergedCellsIdentical="This operation requires the merged cells to be identically sized.",b.Exp_FillRangeContainsMergedCell="Cannot fill range that contains a merged cell.",b.Exp_FillCellsReadOnly="The cells you are trying to fill are protected and therefore read-only.",b.Exp_ChangeMergedCell="Cannot change part of merged cell.",b.Exp_ColumnReadOnly="The column you are trying to change is protected and therefore read-only.",b.Exp_RowReadOnly="The row you are trying to change is protected and therefore read-only.",b.Exp_CellReadOnly="The cell you are trying to change is protected and therefore read-only.",b.Exp_RangeIsNull="range is null",b.Exp_ChangePartOfArray="Cannot change part of an array."},CalcEngine:function(a,b){a.exports=GC.Spread.CalcEngine},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets},SheetsCalc:function(a,b){a.exports=GC.Spread.Sheets.CalcEngine}});