/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
!function(a){"object"==typeof module&&"object"==typeof module.exports?module.exports=a(require("@grapecity/spread-sheets")):"function"==typeof define&&define.amd?define(["@grapecity/spread-sheets"],a):"object"==typeof exports?exports.Spread=a(require("@grapecity/spread-sheets")):a(GC)}(function(a){a="object"==typeof a?a:{},a.Spread=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="",c(c.s="./resource.ko.entry.js")}({"./dist/core/core.res.ko.js":function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=d.GC$("meta[name='spreadjs culture']"),f.length>0&&(g=d.GC$(f[f.length-1]).attr("content"),null!==g&&void 0!==g&&"ko-kr"===g.toLowerCase()&&e.Common.CultureManager.culture("ko-kr")),b.Exp_NotSupported="NotSupportException",b.Exp_PasteExtentIsNull="pasteExtent\uac00 null\uc785\ub2c8\ub2e4.",b.Exp_InvalidPastedArea="\ubd99\uc5ec\ub123\uc744 \uc601\uc5ed\uc758 \ud06c\uae30\uac00 \ubcf5\uc0ac \ub610\ub294 \uc798\ub77c\ub0b8 \uc601\uc5ed\uacfc \uac19\uc544\uc57c \ud569\ub2c8\ub2e4.",b.Exp_MultipleSelections="\uc774 \uc791\uc5c5\uc740 \uc5ec\ub7ec \uc120\ud0dd \ud56d\ubaa9\uc5d0\uc11c \uc791\ub3d9\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.",b.Exp_ChangePartOfArray="\ubc30\uc5f4\uc758 \uc77c\ubd80\ub97c \ubcc0\uacbd\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_InvalidAndSpace="\uc798\ubabb\ub41c {0}: {1}({2}\uc5d0\uc11c {3} \uc0ac\uc774\uc5ec\uc57c \ud568).",b.Exp_SrcIsNull="\uc778\uc218 'src'\uac00 null\uc785\ub2c8\ub2e4.",b.Exp_DestIsNull="\uc778\uc218 'dest'\uac00 null\uc785\ub2c8\ub2e4.",b.Exp_InvalidCustomFunction="\uc798\ubabb\ub41c \uc0ac\uc6a9\uc790 \uc9c0\uc815 \ud568\uc218",b.Exp_InvalidCustomName="\uc798\ubabb\ub41c \uc0ac\uc6a9\uc790 \uc9c0\uc815 \uc774\ub984",b.Exp_IndexOutOfRange="\uc778\ub371\uc2a4\uac00 \ubc94\uc704\ub97c \ubc97\uc5b4\ub0ac\uc2b5\ub2c8\ub2e4!",b.Exp_InvalidRange="\uc798\ubabb\ub41c \ubc94\uc704",b.Exp_ArgumentOutOfRange="ArgumentOutOfRange",b.Exp_PasteSourceCellsLocked="\uc6d0\ubcf8 \uc2dc\ud2b8\uc758 \uc140\uc774 \uc7a0\uaca8 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_InvalidCopyPasteSize="\ubcf5\uc0ac \uc601\uc5ed\uacfc \ubd99\uc5ec\ub123\uae30 \uc601\uc5ed\uc758 \ud06c\uae30\uac00 \uac19\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.",b.Exp_PasteDestinationCellsLocked="\ubcc0\uacbd\ud558\ub824\ub294 \uc140\uc740 \ubcf4\ud638\ub418\uc5b4 \uc788\uc73c\ubbc0\ub85c \uc77d\uae30 \uc804\uc6a9\uc785\ub2c8\ub2e4.",b.Exp_PasteChangeMergeCell="\ubcd1\ud569\ub41c \uc140\uc758 \uc77c\ubd80\ub97c \ubcc0\uacbd\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Tip_Row="\ud589: ",b.Tip_Column="\uc5f4: ",b.Tip_Height="\ub192\uc774: {0}\ud53d\uc140",b.Tip_Width="\ub108\ube44: {0}\ud53d\uc140",b.NewTab="\uc0c8\ub85c \ub9cc\ub4e4\uae30...",b.Exp_EmptyNamedStyle="\uba85\uba85\ub41c \uc2a4\ud0c0\uc77c\uc758 \uc774\ub984\uc740 \ube44\uc6cc \ub450\uac70\ub098 null\ub85c \uc124\uc815\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_SheetNameInvalid="\uc2dc\ud2b8 \uc774\ub984\uc740 \ube44\uc6cc \ub450\uac70\ub098 *, :, [, ], ?, \\, / \ubb38\uc790\ub97c \ud3ec\ud568\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_ArrayFromulaSpan="\ubc30\uc5f4 \uc218\uc2dd\uc740 \ubcd1\ud569\ub41c \uc140\uc5d0 \uc0ac\uc6a9\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_DestSheetIsNull="destSheet\uac00 null\uc785\ub2c8\ub2e4.",b.Exp_SheetIsNull="\uc2dc\ud2b8\uac00 null\uc785\ub2c8\ub2e4.",b.Exp_OverlappingSpans="\uc774 \uc791\uc5c5\uc73c\ub85c \ubc94\uc704\uac00 \uacb9\uce60 \uc218 \uc788\uc2b5\ub2c8\ub2e4.",b.NeedCanvasSupport="SpreadJS\ub97c \uc2e4\ud589\ud558\ub824\uba74 HTML5 Canvas\ub97c \uc644\ubcbd\ud558\uac8c \uc9c0\uc6d0\ud558\ub294 \ube0c\ub77c\uc6b0\uc800\uac00 \ud544\uc694\ud569\ub2c8\ub2e4.",b.ls1=["00000000000000000000000000000000000000000000000000000000000000000000000000c700ccd5d3c700b8cec5b900bcd3d500c200c7c2b2b2000000c7c200bcd300d0b200d1c2d2c6c7b800c8acb4b2b20000000000000000000000000000000000000000000000000000c7b800c7bac7c700bcb000c8c2c2c600","506f776572656420627920477261706543697479205370726561642e5368656574732e0d0a7420b4d81040205cecd0cc2030ec6020182088b5c8e42e0d0a84dc2030ec20a494204ca4b8a93c5c201cf529c8e42e0d0a73616c65732d6b6f72406772617065636974792e636f6d3c5c2074547c4420f4b420fceddc242e"],b.ls2=["00000000000000000000000000000000000000000000000000000000000000000000000000c7c200bcd300d0ac00000000c700d6c500b9b8b4b2b200","506f776572656420627920477261706543697479205370726561642e5368656574732e0d0a84dc2030ec20a400207b307d7c20c4d020cccc29c8e42e"],b.ls3=["b7c7c1c2b900ccc700c200c5c7000000000000000000000000000000b900c2d5d5b8ba00c7d6d500b7c7c1c200d0ac00d5c6d5b2b2000000c7c200d0b200ccd5c6c7b800c8acb4b2b2000000b7c7c1c2b900adc7d500acc600adb900d6c700c7bac7c5c100d0b900d6c7d500c200c7c2b2b2000000b3c6c700d5c6d5b2ba000000000000000000000000000000000000000000000000c7b800c7bac7c700bcb000c8c2c2c600","7c7420a47c203e44201820c64c0d0a5370726561642e5368656574737c20e4895824742020a85c207c7420a420a40020449469c8e42e0d0a84dc20a49420b4d8a93c5c201cf529c8e42e0d0a7c7420a47c206c855c20bdb0206ce42055782074547cd01c20a47c2055786020182088b5c8e42e0d0ac4c07420449458e4742073616c65732d6b6f72406772617065636974792e636f6d3c5c2074547c4420f4b420fceddc242e"],b.ls4=["c7bab400b7c7c1c200d00000b3c6c700d5c6d5b2ba000000000000000000000000000000000000000000000000c7b800c7bac7c700bcb000c8c2c2c600","98bb1c207c7420a420a40d0ac4c07420449458e4742073616c65732d6b6f72406772617065636974792e636f6d3c5c2074547c4420f4b420fceddc242e"],b.ls5=["00000000000000000000000000000000000000000000000000000000000000000000000000c7c200bcd300d0ac00b9b8b4c5c2b2b2000000b3c6c700d5c6d5b2ba000000000000000000000000000000000000000000000000c7b800c7bac7c700bcb000c8c2c2c600","506f776572656420627920477261706543697479205370726561642e5368656574732e0d0a84dc2030ec20a40020cccc18c8b5c8e42e0d0ac4c07420449458e4742073616c65732d6b6f72406772617065636974792e636f6d3c5c2074547c4420f4b420fceddc242e"],b.ls6=["c8ac0000000000000000000000000000000000000000000000000000ccd5d30000bcd3c6c7b800d5acb4c900c5c7","1cf53a20477261706543697479205370726561642e53686565747320b4d8100d0a30eca93c5c20c80018c0204a4c"]},"./dist/plugins/conditional/conditional.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_RuleIsNull="\uc778\uc218 'rule'\uc774 null\uc785\ub2c8\ub2e4.",b.Exp_NotSupported="NotSupportException"},"./dist/plugins/contextMenu/context-menu.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.copy="\ubcf5\uc0ac",b.cut="\uc798\ub77c\ub0b4\uae30",b.pasteOptions="\ubd99\uc5ec\ub123\uae30 \uc635\uc158:",b.pasteAll="\ubaa8\ub450",b.pasteFormula="\uc218\uc2dd",b.pasteValues="\uac12",b.pasteFormatting="\uc11c\uc2dd",b.pasteValuesFormatting="\uac12&\uc11c\uc2dd",b.pasteFormulaFormatting="\uc218\uc2dd&\uc11c\uc2dd",b.clearContents="\ub0b4\uc6a9 \uc9c0\uc6b0\uae30",b.insertRows="\uc0bd\uc785",b.insertColumns="\uc0bd\uc785",b.deleteRows="\uc0ad\uc81c",b.deleteColumns="\uc0ad\uc81c",b.insertSheet="\uc0bd\uc785",b.deleteSheet="\uc0ad\uc81c",b.insertComment="\uba54\ubaa8 \uc0bd\uc785",b.filter="\ud544\ud130",b.sort="\uc815\ub82c",b.slicerSortAscend="\uc624\ub984\ucc28\uc21c \uc815\ub82c",b.slicerSortDescend="\ub0b4\ub9bc\ucc28\uc21c \uc815\ub82c",b.sortAscend="\uc624\ub984\ucc28\uc21c \uc815\ub82c",b.sortDescend="\ub0b4\ub9bc\ucc28\uc21c \uc815\ub82c",b.hideRows="\uc228\uae30\uae30",b.hideColumns="\uc228\uae30\uae30",b.hideSheet="\uc228\uae30\uae30",b.unhideSheet="\uc228\uae30\uae30 \ucde8\uc18c",b.unhideColumns="\uc228\uae30\uae30 \ucde8\uc18c",b.unhideRows="\uc228\uae30\uae30 \ucde8\uc18c",b.editComment="\uba54\ubaa8 \ud3b8\uc9d1",b.deleteComment="\uba54\ubaa8 \uc0ad\uc81c",b.toggleComment="\uba54\ubaa8 \ud45c\uc2dc/\uc228\uae30\uae30",b.removeSlicer="\uc81c\uac70",b.removeFloatingObject="\uc81c\uac70"},"./dist/plugins/data/data.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_NotSupportedDataSource="\ub370\uc774\ud130 \uc18c\uc2a4\uac00 \uc9c0\uc6d0\ub418\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4!"},"./dist/plugins/exportPDF/printPdf.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_FileIOError="\ud30c\uc77c \uc77d\uae30 \ubc0f \uc4f0\uae30 \uc608\uc678\uc785\ub2c8\ub2e4.",b.Exp_FontError="\uc9c0\uc6d0 \ub418\ub294 \uae00\uaf34 \ud615\uc2dd\uc774 \ub098 \ud45c\uc900 PDF \uae00\uaf34\uc774 \uc544\ub2d9\ub2c8\ub2e4."},"./dist/plugins/fill/fill.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.CopyCells="\uc140 \ubcf5\uc0ac",b.FillSeries="\uacc4\uc5f4 \ucc44\uc6b0\uae30",b.FillFormattingOnly="\uc11c\uc2dd\ub9cc \ucc44\uc6b0\uae30",b.FillWithoutFormatting="\uc11c\uc2dd \uc5c6\uc774 \ucc44\uc6b0\uae30",b.Exp_NumberOnly="\uc22b\uc790\uc5d0 \ub300\ud574\uc11c\ub9cc \uc791\ub3d9",b.Exp_RangeContainsMergedCell="\ubc94\uc704\ub294 \ubcd1\ud569\ub41c \uc140\uc744 \ud3ec\ud568\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_TargetContainsMergedCells="\ub300\uc0c1 \ubc94\uc704\ub294 \ubcd1\ud569\ub41c \uc140\uc744 \ud3ec\ud568\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_MergedCellsIdentical="\uc774 \uc791\uc5c5\uc744 \uc218\ud589\ud558\ub824\uba74 \ubcd1\ud569\ud558\ub824\ub294 \uc140\uc758 \ud06c\uae30\uac00 \ub3d9\uc77c\ud574\uc57c \ud569\ub2c8\ub2e4.",b.Exp_FillRangeContainsMergedCell="\ubcd1\ud569\ub41c \uc140\uc744 \ud3ec\ud568\ud558\ub294 \ubc94\uc704\ub97c \ucc44\uc6b8 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_FillCellsReadOnly="\ucc44\uc6b0\ub824\ub294 \uc140\uc740 \uc77d\uae30 \uc804\uc6a9\uc73c\ub85c \ubcf4\ud638\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_ChangeMergedCell="\ubcd1\ud569\ub41c \uc140\uc758 \uc77c\ubd80\ub97c \ubcc0\uacbd\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_ColumnReadOnly="\ubcc0\uacbd\ud558\ub824\ub294 \uc5f4\uc740 \uc77d\uae30 \uc804\uc6a9\uc73c\ub85c \ubcf4\ud638\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_RowReadOnly="\ubcc0\uacbd\ud558\ub824\ub294 \ud589\uc740 \uc77d\uae30 \uc804\uc6a9\uc73c\ub85c \ubcf4\ud638\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_CellReadOnly="\ubcc0\uacbd\ud558\ub824\ub294 \uc140\uc740 \uc77d\uae30 \uc804\uc6a9\uc73c\ub85c \ubcf4\ud638\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_RangeIsNull="\ubc94\uc704\uac00 null\uc785\ub2c8\ub2e4.",b.Exp_ChangePartOfArray="\ubc30\uc5f4\uc758 \uc77c\ubd80\ub97c \ubcc0\uacbd\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4."},"./dist/plugins/filter/filter.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidColumnIndex="\uc5f4 \uc778\ub371\uc2a4\uac00 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.SortAscending="\uc624\ub984\ucc28\uc21c \uc815\ub82c",b.SortDescending="\ub0b4\ub9bc\ucc28\uc21c \uc815\ub82c",b.OK="\ud655\uc778",b.Cancel="\ucde8\uc18c",b.Search="\uac80\uc0c9",b.CheckAll="\ubaa8\ub450 \uc120\ud0dd",b.UncheckAll="\ubaa8\ub450 \uc120\ud0dd \ucde8\uc18c",b.Blanks="(\uacf5\ubc31)",b.Exp_FilterItemIsNull="FilterItem\uc774 null\uc785\ub2c8\ub2e4.",b.Show="\ud45c\uc2dc",b.ShowRows="\ud589 \ud45c\uc2dc \uc704\uce58:",b.And="\ubc0f",b.Or="\ub610\ub294",b.SortColor="\uc0c9 \uae30\uc900 \uc815\ub82c",b.FilterColor="\uc0c9 \uae30\uc900 \ud544\ud130",b.FilterCellTitle="\uc140 \uc0c9 \uae30\uc900 \ud544\ud130",b.FilterFontTitle="\uae00\uaf34 \uc0c9 \uae30\uc900 \ud544\ud130",b.SortCellTitle="\uc140 \uc0c9 \uae30\uc900 \uc815\ub82c",b.SortFontTitle="\uae00\uaf34 \uc0c9 \uae30\uc900 \uc815\ub82c",b.FontColor="\ucd94\uac00 \uae00\uaf34 \uc0c9...",b.CellColor="\ucd94\uac00 \uc140 \uc0c9...",b.NoFill="\ucc44\uc6b0\uae30 \uc5c6\uc74c",b.Automatic="\uc790\ub3d9",b.Clear="\ud544\ud130 \uc9c0\uc6b0\uae30:{0}",b.TextFilter="\ud14d\uc2a4\ud2b8 \ud544\ud130",b.DateFilter="\ub0a0\uc9dc \ud544\ud130",b.NumberFilter="\uc22b\uc790 \ud544\ud130",b.Custom="\uc0ac\uc6a9\uc790 \uc815\uc758 \ud544\ud130...",b.Equal="\uacfc(\uc640) \uac19\uc74c...",b.NotEqual="\uac19\uc9c0 \uc54a\uc74c...",b.GreaterThan="\ubcf4\ub2e4 \ud07c...",b.GreaterOrEquals="\ubcf4\ub2e4 \ud06c\uac70\ub098 \uac19\uc74c...",b.LessThan="\ubcf4\ub2e4 \uc791\uc74c...",b.LessThanOrEquals="\ubcf4\ub2e4 \uc791\uac70\ub098 \uac19\uc74c...",b.Between="\ud574\ub2f9 \ubc94\uc704...",b.Top10="\uc0c1\uc704 10\uac1c...",b.AboveAverage="\ud3c9\uade0 \ucd08\uacfc",b.BelowAverage="\ud3c9\uade0 \ubbf8\ub9cc",b.Begin="(\uc73c)\ub85c \uc2dc\uc791...",b.End="(\uc73c)\ub85c \ub05d\ub0a8...",b.Contain="\ud3ec\ud568...",b.NotContain="\ud3ec\ud568 \uc548 \ud568...",b.Before="\uc774\uc804...",b.After="\uc774\ud6c4...",b.Tomorrow="\ub0b4\uc77c",b.Today="\uc624\ub298",b.Yesterday="\uc5b4\uc81c",b.NextWeek="\ub2e4\uc74c \uc8fc",b.ThisWeek="\uc774\ubc88 \uc8fc",b.LastWeek="\uc9c0\ub09c \uc8fc",b.NextMonth="\ub2e4\uc74c \ub2ec",b.ThisMonth="\uc774\ubc88 \ub2ec",b.LastMonth="\uc9c0\ub09c \ub2ec",b.NextQuarter="\ub2e4\uc74c \ubd84\uae30",b.ThisQuarter="\uc774\ubc88 \ubd84\uae30",b.LastQuarter="\uc9c0\ub09c \ubd84\uae30",b.NextYear="\ub0b4\ub144",b.ThisYear="\uc62c\ud574",b.LastYear="\uc791\ub144",b.YearToDate="\uc5f0\uac04 \ub204\uacc4",b.AllDates="\uae30\uac04\uc758 \ubaa8\ub4e0 \ub0a0\uc9dc",b.Top10Filter="\uc0c1\uc704 10\uac1c \uc790\ub3d9 \ud544\ud130",b.CustomTitle="\uc0ac\uc6a9\uc790 \uc815\uc758 \uc790\ub3d9 \ud544\ud130",b.ColorTitle="\uc0ac\uc6a9\ud560 \uc218 \uc788\ub294 \uc140 \uc0c9",b.top="\uc704\ucabd",b.bottom="\uc544\ub798\ucabd",b.SortCell="\uc815\ub82c \uae30\uc900\uc73c\ub85c \uc0ac\uc6a9\ud560 \uc140 \uc0c9 \uc120\ud0dd:",b.SortFont="\uc815\ub82c \uae30\uc900\uc73c\ub85c \uc0ac\uc6a9\ud560 \uae00\uaf34 \uc0c9 \uc120\ud0dd:",b.FilterCell="\ud544\ud130 \uae30\uc900\uc73c\ub85c \uc0ac\uc6a9\ud560 \uc140 \uc0c9 \uc120\ud0dd:",b.FilterFont="\ud544\ud130 \uae30\uc900\uc73c\ub85c \uc0ac\uc6a9\ud560 \uae00\uaf34 \uc0c9 \uc120\ud0dd:",b.Selected="\uc120\ud0dd \ud56d\ubaa9:",b.IsEquals="\uac19\uc74c",b.NotEquals="\uac19\uc9c0 \uc54a\uc74c",b.IsGreaterThan="\ubcf4\ub2e4 \ud07c",b.IsGreaterOrEqual="\ubcf4\ub2e4 \ud06c\uac70\ub098 \uac19\uc74c",b.IsLess="\ubcf4\ub2e4 \uc791\uc74c",b.LessOrEqual="\ubcf4\ub2e4 \uc791\uac70\ub098 \uac19\uc74c",b.IsBeginWith="(\uc73c)\ub85c \uc2dc\uc791",b.NotBeginWith="(\uc73c)\ub85c \uc2dc\uc791 \uc548 \ud568",b.IsEndWith="(\uc73c)\ub85c \ub05d\ub0a8",b.NotEndWith="(\uc73c)\ub85c \ub05d\ub098\uc9c0 \uc54a\uc74c",b.IsContain="\ud3ec\ud568",b.NotContains="\ud3ec\ud568 \uc548 \ud568",b.IsAfter="\uc774\ud6c4",b.AfterOrEqual="\uc774\ud6c4\uc774\uac70\ub098 \uac19\uc74c",b.IsBefore="\uc774\uc804",b.BeforeOrEqual="\uc774\uc804\uc774\uac70\ub098 \uac19\uc74c",b.Q1="1\ubd84\uae30",b.Q2="2\ubd84\uae30",b.Q3="3\ubd84\uae30",b.Q4="4\ubd84\uae30",b.Jan="1\uc6d4",b.Feb="2\uc6d4",b.Mar="3\uc6d4",b.Apr="4\uc6d4",b.May="5\uc6d4",b.Jun="6\uc6d4",b.Jul="7\uc6d4",b.Aug="8\uc6d4",b.Sep="9\uc6d4",b.Oct="10\uc6d4",b.Nov="11\uc6d4",b.Dec="12\uc6d4",b.Explain1="\ub2e8\uc77c \ubb38\uc790\ub97c \ub098\ud0c0\ub0b4\ub824\uba74 ? \uc0ac\uc6a9",b.Explain2="\uc77c\ub828\uc758 \ubb38\uc790\ub97c \ub098\ud0c0\ub0b4\ub824\uba74 * \uc0ac\uc6a9",b.Year="",b.Day=""},"./dist/plugins/floatingObject/floatingobject.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_FloatingObjectHasSameNameError="\ud604\uc7ac \uc6cc\ud06c\uc2dc\ud2b8\uc5d0 \ub3d9\uc77c\ud55c \uc774\ub984\uc744 \uac00\uc9c4 \ubd80\ub3d9 \uac1c\uccb4(FloatingObject)\uac00 \uc774\ubbf8 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_FloatingObjectNameEmptyError="\ubd80\ub3d9 \uac1c\uccb4(FloatingObject)\ub294 \uc774\ub984\uc774 \uc788\uc5b4\uc57c \ud569\ub2c8\ub2e4."},"./dist/plugins/group/group.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidIndex="\uc798\ubabb\ub41c \uc778\ub371\uc2a4",b.Exp_InvalidCount="\uc798\ubabb\ub41c \uac1c\uc218",b.Exp_InvalidLevel="\uc798\ubabb\ub41c \ub808\ubca8",b.Exp_GroupInfoIsNull="groupInfo\uac00 null\uc785\ub2c8\ub2e4."},"./dist/plugins/print/print.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidSheetIndex="\uc798\ubabb\ub41c \uc2dc\ud2b8 \uc778\ub371\uc2a4\uc785\ub2c8\ub2e4."},"./dist/plugins/shape/shape.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidConnectionSite="\uc798\ubabb\ub41c \uc5f0\uacb0 \uc0ac\uc774\ud2b8\uc785\ub2c8\ub2e4.",b.Exp_DuplicatedName="\uc911\ubcf5\ub41c \uc774\ub984\uc785\ub2c8\ub2e4.",b.Exp_EmptyName="\ube48 \uc774\ub984\uc785\ub2c8\ub2e4."},"./dist/plugins/slicer/slicer.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Blank="(\uacf5\ubc31)",b.Exp_SlicerNameInvalid="\uc2ac\ub77c\uc774\uc11c \uc774\ub984\uc774 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_SlicerNameExist="\uc2ac\ub77c\uc774\uc11c \uc774\ub984\uc740 \uc774\ubbf8 \uc0ac\uc6a9\ub418\uace0 \uc788\uc2b5\ub2c8\ub2e4. \uace0\uc720\ud55c \uc774\ub984\uc744 \uc785\ub825\ud558\uc2ed\uc2dc\uc624."},"./dist/plugins/statusBar/statusBar.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.cellMode="Cell Mode",b.cellModeReady="Ready",b.cellModeEnter="Enter",b.cellModeEdit="Edit",b.formulaAverage="Average",b.formulaCount="Count",b.formulaNumericalCount="Numerical Count",b.formulaMin="Min",b.formulaMax="Max",b.formulaSum="Sum",b.zoomSlider="Zoom Slider",b.zoom="Zoom",b.toolTipCellMode="In {0} mode",b.toolTipFormulaAverage="Average of selected cells",b.toolTipFormulaCount="Number of selected cells that contain data",b.toolTipFormulaNumericalCount="Number of selected cells that contain numerical data",b.toolTipFormulaMin="Minimum value in selection",b.toolTipFormulaMax="Maximum value in selection",b.toolTipFormulaSum="Sum of selected cells",b.toolTipZoomOut="Zoom Out",b.toolTipZoomIn="Zoom In",b.toolTipSlider="Zoom",b.toolTipZoomPanel="Zoom level"},"./dist/plugins/table/table.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_DragDropShiftTableCell="\uc774 \uc791\uc5c5\uc740 \ud5c8\uc6a9\ub418\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. \uc774 \uc791\uc5c5\uc740 \uc6cc\ud06c\uc2dc\ud2b8\uc758 \ud14c\uc774\ube14\uc5d0\uc11c \uc140\uc744 \ubcc0\ud658\ud558\ub824\uace0 \uc2dc\ub3c4\ud569\ub2c8\ub2e4.",b.Exp_DragDropChangePartOfTable="\uc791\uc5c5\uc744 \uc644\ub8cc\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4. \ud14c\uc774\ube14 \ud589 \ub610\ub294 \uc5f4\uc758 \uc77c\ubd80\ub97c \ud5c8\uc6a9\ub418\uc9c0 \uc54a\ub294 \ubc29\uc2dd\uc73c\ub85c \ubcc0\uacbd\ud558\ub824\uace0 \ud569\ub2c8\ub2e4.",b.Exp_TableEmptyNameError="\ud14c\uc774\ube14 \uc774\ub984\uc740 \ube44\uc6cc \ub458 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_TableNameInvalid="\ud14c\uc774\ube14 \uc774\ub984\uc774 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_TableInvalidRow="\ud589 \uc778\ub371\uc2a4 \ub610\ub294 \ud589 \uc218\uac00 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_TableInvalidColumn="\uc5f4 \uc778\ub371\uc2a4 \ub610\ub294 \uc5f4 \uc218\uac00 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_TableIntersectError="\ud14c\uc774\ube14\uc774 \uad50\ucc28\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_TableHasSameNameError="\ud604\uc7ac \uc6cc\ud06c\uc2dc\ud2b8\uac00 \ub3d9\uc77c\ud55c \uc774\ub984\uc744 \uac00\uc9c4 \ud14c\uc774\ube14\uc5d0 \uc774\ubbf8 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_TableDataSourceNullError="\ud14c\uc774\ube14 \uc6d0\ubcf8 \ub370\uc774\ud130\ub294 null\uc77c \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_TableMoveOutOfRange="\ud14c\uc774\ube14\uc744 \uc2dc\ud2b8 \ubc16\uc73c\ub85c \uc774\ub3d9\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_TableResizeOutOfRange="\uc798\ubabb\ub41c \ud589 \uc218, \uc5f4 \uc218 \ubc0f \ud14c\uc774\ube14\uc758 \ud06c\uae30\ub97c \uc2dc\ud2b8 \ubc16\uc73c\ub85c \uc870\uc815\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_ArrayFormulaTable="\ub2e4\uc911 \uc140 \ubc30\uc5f4 \uc218\uc2dd\uc740 \ud45c\uc5d0\uc11c \uc0ac\uc6a9\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_TableResizeInvalidRange="\uba38\ub9ac\uae00\uc740 \ub3d9\uc77c\ud55c \ud589\uc5d0 \uc720\uc9c0\ub418\uace0 \uacb0\uacfc \ud45c \ubc94\uc704\ub294 \uc6d0\ub798 \ud45c \ubc94\uc704\uc640 \uc77c\uce58\ud574\uc57c \ud569\ub2c8\ub2e4."},"./dist/plugins/touch/touch.res.ko.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.ToolStrip_PasteText="\ubd99\uc5ec\ub123\uae30",b.ToolStrip_CutText="\uc798\ub77c\ub0b4\uae30",b.ToolStrip_CopyText="\ubcf5\uc0ac",b.ToolStrip_AutoFillText="\uc790\ub3d9 \ucc44\uc6b0\uae30"},"./node_modules_local/@grapecity/js-calc/dist/gc.spread.calcengine.resources.ko.js":function(a,b){var c="object"==typeof c?c:{};c.Spread=c.Spread||{},c.Spread.CalcEngine=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/resource.ko.entry.ts")}({"./src/calcEngine.res.ko.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});function d(a,b){return{description:a,parameters:b}}function e(a,b){return{name:a,repeatable:b}}b.resource={Exp_InvalidCast:"\uc798\ubabb\ub41c \uce90\uc2a4\ud2b8 \uc608\uc678",Exp_FormulaInvalidChar:"\uc785\ub825\ud55c \uc218\uc2dd\uc5d0 \uc798\ubabb\ub41c \ubb38\uc790\uac00 \ud3ec\ud568\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4. {1}\uc5d0 \ub300\ud55c \uc778\ub371\uc2a4\uc5d0 '{0}'\uc774(\uac00) \ud3ec\ud568\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.",Exp_FormulaInvalid:"\uc785\ub825\ud55c \uc218\uc2dd\uc774 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",Exp_InvalidFunctionName:"\uc798\ubabb\ub41c \ud568\uc218 \uc774\ub984",Exp_InvalidOverrideFunction:"\uae30\ubcf8 \ud568\uc218\ub97c \uc7ac\uc815\uc758\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",Exp_InvalidArray:"\uc798\ubabb\ub41c \ubc30\uc5f4",Exp_OverrideNotAllowed:"\ud568\uc218\ub97c \uc7ac\uc815\uc758\ud558\ub824\uace0 \ud558\uc9c0\ub9cc \uc7ac\uc815\uc758\uac00 \ud5c8\uc6a9\ub418\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.",Exp_NoSyntax:"\uad6c\ubb38 '{1}'\uacfc(\uc640) \uc77c\uce58\ud558\ub294 \uad6c\ubb38 '{0}'\uc774(\uac00) \uc5c6\uc2b5\ub2c8\ub2e4.",Exp_IsValid:"'{0}'\uc774(\uac00) \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",Exp_InvalidParameters:"{0}\uc758 \ud568\uc218 \ub9e4\uac1c \ubcc0\uc218\uac00 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",Exp_InvalidArrayColumns:"{0}\uc5d0\uc11c \ubc30\uc5f4 \uc5f4\uc758 \uae38\uc774\uac00 \ub2e4\ub985\ub2c8\ub2e4.",Exp_ExprIsNull:"\uc778\uc218 'expr'\uc774(\uac00) null\uc785\ub2c8\ub2e4.",Exp_InvalidOperation:"\uc798\ubabb\ub41c \uc791\uc5c5 \uc608\uc678",Exp_ArgumentNull:"\uc778\uc218 Null \uc608\uc678",Exp_CriteriaIsNull:"\uae30\uc900\uc774 null\uc785\ub2c8\ub2e4.",Exp_Format:"\ud615\uc2dd",Exp_ArrayFromulaPart:"\ubc30\uc5f4\uc758 \uc77c\ubd80\ub97c \ubcc0\uacbd\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",Exp_NotSupported:"\uc608\uc678 \uc9c0\uc6d0 \uc548 \ud568",Fbx_Summary:"\uc694\uc57d",Fbx_TableName_Description:"\ud45c \uc774\ub984 ",Fbx_CustomName_Description:"\uc0ac\uc6a9\uc790 \uc9c0\uc815 \uc774\ub984 ",_h:{ABS:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \uc808\ub300\uac12\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),ACCRINT:d("\uc774 \ud568\uc218\ub294 \uc815\uae30\uc801\uc73c\ub85c \uc774\uc790\ub97c \uc9c0\uae09\ud558\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \uacbd\uacfc \uc774\uc790\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("issue"),e("first"),e("settle"),e("rate"),e("par"),e("frequency"),e("basis")]),ACCRINTM:d("\uc774 \ud568\uc218\ub294 \uc815\uae30\uc801\uc73c\ub85c \uc774\uc790\ub97c \uc9c0\uae09\ud558\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \ub9cc\uae30 \uc2dc \uacbd\uacfc \uc774\uc790\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("issue"),e("maturity"),e("rate"),e("par"),e("basis")]),ACOS:d("\uc774 \ud568\uc218\ub294 ACOS \uc989, COS\uc774 \uc9c0\uc815\ub41c \uac12\uc778 \uac01\ub3c4\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),ACOSH:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 ACOSH\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),ADDRESS:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \ud589/\uc5f4 \ubc88\ud638\ub97c \uac00\uc9c0\uace0 \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc758 \uc140 \uc8fc\uc18c\ub97c \ub9cc\ub4ed\ub2c8\ub2e4.",[e("row"),e("column"),e("absnum"),e("a1style"),e("sheettext")]),AMORDEGRC:d("\uc774 \ud568\uc218\ub294 \ube44\ub840 \ubc30\ubd84 \ub41c \uac10\uac00 \uc0c1\uac01\uc744 \uace0\ub824\ud558\uc5ec \ud68c\uacc4 \uae30\uac04\uc758 \uac10\uac00 \uc0c1\uac01\uc744 \ubc18\ud658\ud558\uace0 \uc790\uc0b0\uc758 \uc218\uba85\uc744 \uae30\uc900\uc73c\ub85c \uac10\uac00 \uc0c1\uac01 \uacc4\uc218\ub97c \uacc4\uc0b0\uc5d0 \uc801\uc6a9\ud569\ub2c8\ub2e4.",[e("cost"),e("datepurchased"),e("firstperiod"),e("salvage"),e("period"),e("drate"),e("basis")]),AMORLINC:d("\uc774 \ud568\uc218\ub294 \uc77c\ud560 \uacc4\uc0b0\ub41c \uac10\uac00 \uc0c1\uac01\uc561\uc744 \uace0\ub824\ud558\uc5ec \ud68c\uacc4 \uae30\uac04\uc5d0 \ub300\ud55c \uac10\uac00 \uc0c1\uac01\uc561\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("cost"),e("datepurchased"),e("firstperiod"),e("salvage"),e("period"),e("drate"),e("basis")]),AND:d("\ubaa8\ub4e0 \uc778\uc218\uac00 True\uc778\uc9c0 \ud655\uc778\ud558\uc2ed\uc2dc\uc624. \uc778\uc218\uac00 \ubaa8\ub450 True\uc774\uba74 True\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("logical1"),e("logical2")]),ASIN:d("\uc774 \ud568\uc218\ub294 ASIN \uc989, SIN\uc774 \uc9c0\uc815\ub41c \uac12\uc778 \uac01\ub3c4\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),ASINH:d("\uc774 \ud568\uc218\ub294 ASINH\uac12\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),ATAN:d("\uc774 \ud568\uc218\ub294 ATAN \uc989, TAN\uac00 \uc9c0\uc815\ub41c \uac12\uc778 \uac01\ub3c4\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),ATAN2:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c x \uc88c\ud45c\uc640 y \uc88c\ud45c\uc758 ATAN\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("x"),e("y")]),ATANH:d("\uc774 \ud568\uc218\ub294 ATANH\uac12\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value")]),AVEDEV:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \uc808\ub300 \ud3b8\ucc28\uc758 \ud3c9\uade0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),AVERAGE:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc22b\uc790 \uac12\uc758 \ud3c9\uade0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),AVERAGEA:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8, \ub17c\ub9ac\uac12, \uc22b\uc790\uac12\uc744 \ube44\ub86f\ud55c \uc9c0\uc815\ub41c \uac12\uc758 \ud3c9\uade0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),AVERAGEIF:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc22b\uc790 \uac12\uc774 \uc9c0\uc815\ub41c \uae30\uc900\uc744 \ucda9\uc871\ud560 \uacbd\uc6b0\uc758 \ud3c9\uade0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0),e("condition")]),AVERAGEIFS:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc5ec\ub7ec \uae30\uc900\uc744 \ucda9\uc871\ud558\ub294 \ubaa8\ub4e0 \uc140\uc758 \ud3c9\uade0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("condition1"),e("value2",!0),e("condition2...")]),BESSELI:d("\uc774 \ud568\uc218\ub294 \uc21c \ud5c8\uc218 \uc778\uc218\uc5d0 \ub300\ud574 \uacc4\uc0b0\ub418\ub294 \uccab \ubc88\uc9f8 \uc885\ub958\uc758 \uc218\uc815\ub41c Bessel \ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("order")]),BESSELJ:d("\uc774 \ud568\uc218\ub294 \uccab \ubc88\uc9f8 \uc885\ub958\uc758 Bessel \ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("order")]),BESSELK:d("\uc774 \ud568\uc218\ub294 \uc21c \ud5c8\uc218 \uc778\uc218\uc5d0 \ub300\ud574 \uacc4\uc0b0\ub418\ub294 \ub450 \ubc88\uc9f8 \uc885\ub958\uc758 \uc218\uc815\ub41c Bessel \ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("order")]),BESSELY:d("\uc774 \ud568\uc218\ub294 \ub450 \ubc88\uc9f8 \uc885\ub958\uc758 Bessel \ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("order")]),BETADIST:d("\uc774 \ud568\uc218\ub294 \ub204\uc801 \ubca0\ud0c0 \ubd84\ud3ec \ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("x"),e("alpha"),e("beta"),e("lower"),e("upper")]),BETAINV:d("\uc774 \ud568\uc218\ub294 \ub204\uc801 \ubca0\ud0c0 \ubd84\ud3ec \ud568\uc218\uc758 \uc5ed\ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("prob"),e("alpha"),e("beta"),e("lower"),e("upper")]),BIN2DEC:d("\uc774 \ud568\uc218\ub294 2\uc9c4\uc218\ub97c 10\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number")]),BIN2HEX:d("\uc774 \ud568\uc218\ub294 2\uc9c4\uc218\ub97c 16\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),BIN2OCT:d("\uc774 \ud568\uc218\ub294 2\uc9c4\uc218\ub97c 8\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),BINOMDIST:d("\uc774 \ud568\uc218\ub294 \uac1c\ubcc4\ud56d \uc774\ud56d \ubd84\ud3ec \ud655\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("x"),e("n"),e("p"),e("cumulative")]),CEILING:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \ubc30\uc218\uac00 \ub418\ub3c4\ub85d \uc808\ub300 \uac12\uc744 \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("signif")]),CHAR:d("\uc774 \ud568\uc218\ub294 \ubc88\ud638\uc5d0 \ud574\ub2f9\ud558\ub294 \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),CHIDIST:d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc758 \ub2e8\uce21 \ud655\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("deg")]),CHIINV:d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc758 \ub2e8\uce21 \ud655\ub960\uc758 \uc5ed\ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("prob"),e("deg")]),CHITEST:d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc5d0\uc11c \ub3c5\ub9bd \uac80\uc99d \uacb0\uacfc\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("obs_array"),e("exp_array")]),CHOOSE:d("\uc774 \ud568\uc218\ub294 \uac12 \ubaa9\ub85d\uc5d0\uc11c \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("index"),e("value1"),e("value2",!0)]),CLEAN:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8\uc5d0\uc11c \uc778\uc1c4\ud560 \uc218 \uc5c6\ub294 \ubb38\uc790\ub97c \ubaa8\ub450 \uc81c\uac70\ud569\ub2c8\ub2e4.",[e("text")]),CODE:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uccab\uc9f8 \ubb38\uc790\ub97c \ub098\ud0c0\ub0b4\ub294 \uc22b\uc790 \ucf54\ub4dc\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \ubc18\ud658\ub418\ub294 \ucf54\ub4dc\ub294 Windows \ubb38\uc790 \uc9d1\ud569(ANSI)\uc5d0 \ud574\ub2f9\ud569\ub2c8\ub2e4.",[e("text")]),COLUMN:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc601\uc5ed\uc758 \uc5f4 \ubc88\ud638\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("reference")]),COLUMNS:d("\uc774 \ud568\uc218\ub294 \ubc30\uc5f4\uc5d0 \uc788\ub294 \uc5f4 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array")]),
COMBIN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \ud56d\ubaa9 \uc218\uc5d0 \ub300\ud574 \uac00\ub2a5\ud55c \uc870\ud569 \uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("k"),e("n")]),COMPLEX:d("\uc774 \ud568\uc218\ub294 \uc2e4\uc218\ubd80\uc640 \ud5c8\uc218\ubd80\uc758 \uacc4\uc218\ub97c \ubcf5\uc18c\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("realcoeff"),e("imagcoeff"),e("suffix")]),CONCATENATE:d("\uc774 \ud568\uc218\ub294 \uc5ec\ub7ec \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4 \ub610\ub294 \uc22b\uc790\ub97c \ud558\ub098\uc758 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\ub85c \uacb0\ud569\ud569\ub2c8\ub2e4.",[e("text1"),e("text2"),e("....")]),CONFIDENCE:d("\uc774 \ud568\uc218\ub294 \ubaa8\uc9d1\ub2e8 \ud3c9\uade0\uc758 \uc2e0\ub8b0 \uad6c\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("alpha"),e("stdev"),e("size")]),CONVERT:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790\ub97c \ub2e4\ub978 \ub2e8\uc704 \uccb4\uacc4\uc758 \uc22b\uc790\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("from-unit"),e("to-unit")]),CORREL:d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc138\ud2b8\uc758 \uc0c1\uad00 \uacc4\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),COS:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac01\ub3c4\uc758 COS\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),COSH:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 COSH\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),COUNT:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790\uac00 \uc788\ub294 \uc140 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),COUNTA:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790, \ud14d\uc2a4\ud2b8 \ub610\ub294 \ub17c\ub9ac\uac12\uc774 \uc788\ub294 \uc140 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),COUNTBLANK:d("\uc774 \ud568\uc218\ub294 \uc2dc\ud2b8\uc758 \uc140 \ubc94\uc704 \ub0b4\uc5d0 \uc788\ub294 \ube48 \uc140\uc758 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cellrange")]),COUNTIF:d("\uc774 \ud568\uc218\ub294 \ud2b9\uc815 \uc870\uac74\uc744 \ub9cc\uc871\ud558\ub294 \uc140 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cellrange"),e("condition")]),COUNTIFS:d("\uc774 \ud568\uc218\ub294 \uc5ec\ub7ec \uc870\uac74\uc744 \ub9cc\uc871\ud558\ub294 \uc140 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cellrange"),e("condition")]),COUPDAYBS:d("\uc774 \ud568\uc218\ub294 \uc774\uc790 \uc9c0\uae09 \uae30\uac04\uc758 \uc2dc\uc791\uc77c\ubd80\ud130 \uacb0\uc0b0\uc77c\uae4c\uc9c0\uc758 \ub0a0\uc9dc \uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPDAYS:d("\uc774 \ud568\uc218\ub294 \uacb0\uc0b0\uc77c\uc774 \ub4e4\uc5b4 \uc788\ub294 \uc774\uc790 \uc9c0\uae09 \uae30\uac04\uc758 \ub0a0\uc9dc \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPDAYSNC:d("\uc774 \ud568\uc218\ub294 \uacb0\uc0b0\uc77c\ubd80\ud130 \ub2e4\uc74c \uc774\uc790 \uc9c0\uae09\uc77c\uae4c\uc9c0\uc758 \ub0a0\uc9dc \uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPNCD:d("\uc774 \ud568\uc218\ub294 \uacb0\uc0b0\uc77c \uc774\ud6c4\uc758 \ub2e4\uc74c \uc774\uc790 \uc9c0\uae09\uc77c\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("frequency"),e("basi")]),COUPNUM:d("\uc774 \ud568\uc218\ub294 \uacb0\uc0b0\uc77c\uacfc \ub9cc\uae30\uc77c \uc0ac\uc774\uc758 \uc774\uc790 \uc9c0\uae09 \ud69f\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPPCD:d("\uc774 \ud568\uc218\ub294 \uacb0\uc0b0\uc77c \ubc14\ub85c \uc804 \uc774\uc790 \uc9c0\uae09\uc77c\uc744 \ub098\ud0c0\ub0b4\ub294 \uc22b\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COVAR:d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \uac01 \ub370\uc774\ud130 \uc694\uc18c \uc30d\uc5d0 \ub300\ud55c \ud3b8\ucc28\ub97c \uacf1\ud55c \uac12\uc758 \ud3c9\uade0\uc744 \ub098\ud0c0\ub0b4\ub294 \uacf5 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),CRITBINOM:d("\uc774 \ud568\uc218\ub294 \ub204\uc801 \uc774\ud56d \ubd84\ud3ec\uac00 \uae30\uc900\uce58 \uc774\uc0c1\uc774 \ub418\ub294 \uac12 \uc911 \ucd5c\uc18c\uac12\uc744 \ub098\ud0c0\ub0b4\ub294 \uc870\uac74 \uc774\ud56d\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("n"),e("p"),e("alpha")]),CUMIPMT:d("\uc774 \ud568\uc218\ub294 \uc2dc\uc791 \uae30\uac04\uacfc \uc885\ub8cc \uae30\uac04 \uc0ac\uc774\uc5d0 \ub0a9\uc785\ud558\ub294 \ub300\ucd9c\uae08 \uc774\uc790\uc758 \ub204\uacc4\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("rate"),e("nper"),e("pval"),e("startperiod"),e("endperiod"),e("paytype")]),CUMPRINC:d("\uc774 \ud568\uc218\ub294 \uc2dc\uc791 \uae30\uac04\uacfc \uc885\ub8cc \uae30\uac04 \uc0ac\uc774\uc5d0 \ub0a9\uc785\ud558\ub294 \ub300\ucd9c\uae08 \uc6d0\uae08\uc758 \ub204\uacc4\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("rate"),e("nper"),e("pval"),e("startperiod"),e("endperiod"),e("paytype")]),DATE:d("\uc774 \ud568\uc218\ub294 \ud2b9\uc815 \ub0a0\uc9dc\uc5d0 \ub300\ud574 \ub144, \uc6d4, \uc77c\ub85c \uc9c0\uc815\ub41c DateTime \uac1c\uccb4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("year"),e("month"),e("day")]),DATEDIF:d("\uc774 \ud568\uc218\ub294 \ub450 \ub0a0\uc9dc \uc0ac\uc774\uc758 \uc77c \uc218, \uc6d4 \uc218 \ub610\ub294 \ub144 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date1"),e("date2"),e("outputcode")]),DATEVALUE:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \ub0a0\uc9dc\uc758 DateTime \uac1c\uccb4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date_string")]),DAVERAGE:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0 \uc788\ub294 \uac12\uc758 \ud3c9\uade0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DAY:d("\uc774 \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \ub2ec\uc758 \uc9c0\uc815\ub41c \ub0a0\uc9dc\uc5d0 \ud574\ub2f9\ud558\ub294 \uac12\uc744 1\uacfc 31 \uc0ac\uc774\uc758 \uc22b\uc790\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date")]),DAYS360:d("\uc774 \ud568\uc218\ub294 1\ub144\uc744 360\uc77c\ub85c \ud558\uc5ec, \ub450 \ub0a0\uc9dc \uc0ac\uc774\uc758 \ub0a0\uc9dc \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("startdate"),e("enddate"),e("method")]),DB:d("\uc774 \ud568\uc218\ub294 \uace0\uc815 \uc77c\ubcc4 \uade0\ud615 \ubc29\ubc95\uc744 \uc0ac\uc6a9\ud558\uc5ec \uc9c0\uc815\ud55c \uae30\uac04 \ub3d9\uc548 \uc790\uc0b0\uc758 \uac10\uac00 \uc0c1\uac01\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("cost"),e("salvage"),e("life"),e("period"),e("month")]),DCOUNT:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \uc22b\uc790\ub97c \ud3ec\ud568\ud558\ub294 \uc140\uc758 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DCOUNTA:d("\uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \ube44\uc5b4 \uc788\uc9c0 \uc54a\uc740 \uc140\uc758 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DDB:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ud55c \uae30\uac04 \ub3d9\uc548 \uc774\uc911 \uccb4\uac10\ubc95\uc774\ub098 \uc0ac\uc6a9\uc790\uac00 \uc815\ud558\ub294 \ub2e4\ub978 \ubc29\ubc95\uc73c\ub85c \uc790\uc0b0\uc758 \uac10\uac00 \uc0c1\uac01\uc561\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("cost"),e("salvage"),e("life"),e("period"),e("factor")]),DEC2BIN:d("\uc774 \ud568\uc218\ub294 10\uc9c4\uc218\ub97c 2\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),DEC2HEX:d("\uc774 \ud568\uc218\ub294 10\uc9c4\uc218\ub97c 16\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),DEC2OCT:d("\uc774 \ud568\uc218\ub294 10\uc9c4\uc218\ub97c 8\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),DEGREES:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc744 \ub77c\ub514\uc548 \ud615\ud0dc\uc758 \uac01\ub3c4\uc5d0\uc11c \ub3c4 \ub2e8\uc704\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),DELTA:d("\uc774 \ud568\uc218\ub294 \ub450 \uac12\uc774 \uac19\uc740\uc9c0 \uc2dd\ubcc4\ud569\ub2c8\ub2e4. \ub450 \uac12\uc774 \uac19\uc73c\uba74 1\uc744 \ubc18\ud658\ud558\uace0, \uadf8\ub807\uc9c0 \uc54a\uc73c\uba74 0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2")]),DEVSQ:d("\uc774 \ud568\uc218\ub294 \ud45c\ubcf8 \ud3c9\uade0\uc73c\ub85c\ubd80\ud130 \ub370\uc774\ud130 \uc694\uc18c \ub610\ub294 \ub370\uc774\ud130 \uc694\uc18c \ubc30\uc5f4\uc758 \ud3b8\ucc28\uc758 \uc81c\uacf1\uc758 \ud569\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),DGET:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \ub2e8\uc77c \uac12\uc744 \ucd94\ucd9c\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DISC:d("\uc774 \ud568\uc218\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \ud560\uc778\uc728\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("pricep"),e("redeem"),e("basis")]),DMAX:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \uac00\uc7a5 \ud070 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DMIN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \uac00\uc7a5 \uc791\uc740 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DOLLAR:d("\uc774 \ud568\uc218\ub294 \ud1b5\ud654 \ud615\uc2dd\uc744 \uc0ac\uc6a9\ud558\uc5ec \uc22b\uc790\ub97c \ud14d\uc2a4\ud2b8\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4. \uc18c\uc218\ub294 \uc9c0\uc815\ub41c \uc790\ub9bf\uc218\ub85c \ubc18\uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("digits")]),DOLLARDE:d("\uc774 \ud568\uc218\ub294 \ubd84\uc218\ub85c \ud45c\uc2dc\ub41c \uae08\uc561\uc744 \uc18c\uc218\ub85c \ud45c\uc2dc\ub41c \uae08\uc561\uc73c\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("fractionaldollar"),e("fraction")]),DOLLARFR:d("\uc774 \ud568\uc218\ub294 \uc18c\uc218\ub85c \ud45c\uc2dc\ub41c \uae08\uc561\uc744 \ubd84\uc218\ub85c \ud45c\uc2dc\ub41c \uae08\uc561\uc73c\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("decimaldollar"),e("fraction")]),DPRODUCT:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \uac12\ub4e4\uc758 \uacf1\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DSTDEV:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0 \uc788\ub294 \uc22b\uc790\ub97c \uc0ac\uc6a9\ud558\uc5ec \ud45c\ubcf8 \uc9d1\ub2e8\uc758 \ud45c\uc900 \ud3b8\ucc28\ub97c \uad6c\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DSTDEVP:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0 \uc788\ub294 \uc22b\uc790\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc804\uccb4 \ubaa8\uc9d1\ub2e8\uc758 \ud45c\uc900 \ud3b8\ucc28\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DSUM:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0\uc11c \uac12\ub4e4\uc758 \ud569\uc744 \uad6c\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DURATION:d("\uc774 \ud568\uc218\ub294 \uac00\uc815\ub41c \uc561\uba74\uac00 $100\uc5d0 \ub300\ud55c Macaulay \uae30\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("coupon"),e("yield"),e("frequency"),e("basis")]),DVAR:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0 \uc788\ub294 \uc22b\uc790\ub97c \uc0ac\uc6a9\ud558\uc5ec \ud45c\ubcf8 \uc9d1\ub2e8\uc758 \ubd84\uc0b0\uc744 \uad6c\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),DVARP:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc870\uac74\uc5d0 \ub9de\ub294 \ub370\uc774\ud130\ubca0\uc774\uc2a4\ub098 \ubaa9\ub85d\uc758 \uc5f4\uc5d0 \uc788\ub294 \uc22b\uc790\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc804\uccb4 \ubaa8\uc9d1\ub2e8\uc758 \ubd84\uc0b0\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("database"),e(" field"),e(" criteria")]),EDATE:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ud55c \ub0a0\uc9dc \uc804\uc774\ub098 \ud6c4\uc758 \uac1c\uc6d4 \uc218\ub97c \ub098\ud0c0\ub0b4\ub294 \ub0a0\uc9dc\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("startdate"),e("months")]),EFFECT:d("\uc774 \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \uba85\ubaa9\uc0c1\uc758 \uc5f0\uc774\uc728\uc5d0 \ub300\ud55c \uc2e4\uc9c8\uc801\uc778 \uc5f0\uc774\uc728\uacfc \uc5f0\uac04 \ubcf5\ub9ac \uacc4\uc0b0 \ud69f\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("nomrate"),e("comper")]),EOMONTH:d("\uc774 \ud568\uc218\ub294 \uc2dc\uc791 \ub0a0\uc9dc \uc804\uc774\ub098 \ud6c4\uc758 \uac1c\uc6d4 \uc218\ub97c \ub098\ud0c0\ub0b4\ub294 \uc6d4\ub9d0 \ub0a0\uc9dc\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("startdate"),e("months")]),ERF:d("\uc774 \ud568\uc218\ub294 \ud558\ud55c\uac12\uacfc \uc0c1\ud55c\uac12 \uc0ac\uc774\uc5d0 \ud1b5\ud569\ub41c \uc624\ucc28 \ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("limit"),e("upperlimit")]),ERFC:d("\uc774 \ud568\uc218\ub294 \ud558\ud55c\uac12\uacfc \ubb34\ud55c\ub300 \uc0ac\uc774\uc5d0 \ud1b5\ud569\ub41c \uc624\ucc28 \ud568\uc218\uc758 \uc5ec\uac12\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("lowerlimit")]),"ERROR.TYPE":d("\uc774 \ud568\uc218\ub294 \uc624\ub958 \uac12 \uc911 \ud558\ub098\uc5d0 \ud574\ub2f9\ud558\ub294 \uc22b\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("errorvalue")]),EURO:d("\uc774 \ud568\uc218\ub294 ISO \ud1b5\ud654 \ucf54\ub4dc\ub97c \uae30\uc900\uc73c\ub85c 1\uc720\ub85c\uc758 \ub4f1\uac00\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("code")]),EUROCONVERT:d("\uc774 \ud568\uc218\ub294 \uc720\ub85c \ud68c\uc6d0\uad6d \ud1b5\ud654(\uc720\ub85c \ud3ec\ud568) \uac04\uc5d0 \ud1b5\ud654\ub97c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("currency"),e("source"),e("target"),e("fullprecision"),e("triangulation")]),EVEN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc744 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc9dd\uc218\uc778 \uc815\uc218\ub85c \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value")]),EXACT:d("\uc774 \ud568\uc218\ub294 \ub450 \ubb38\uc790\uc5f4\uc774 \uac19\uc73c\uba74 true\ub97c \ubc18\ud658\ud558\uace0, \uadf8\ub807\uc9c0 \uc54a\uc73c\uba74 false\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("text1"),e("text2")]),EXP:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \uac70\ub4ed\uc81c\uacf1\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),EXPONDIST:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc218 \ubd84\ud3ec \ub610\ub294 \ud655\ub960 \ubc00\ub3c4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("lambda"),e("cumulative")]),FACT:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 \uacc4\uc2b9\uac12\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("number")]),FACTDOUBLE:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 \uc774\uc911 \uacc4\uc2b9\uac12\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("number")]),FALSE:d("\uc774 \ud568\uc218\ub294 \ub17c\ub9ac\uac12 FALSE\uc5d0 \ud574\ub2f9\ud558\ub294 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),FDIST:d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc9d1\ud569 \uc0ac\uc774\uc758 \ubd84\ud3ec\ub3c4\ub97c \ub098\ud0c0\ub0b4\ub294 F \ud655\ub960 \ubd84\ud3ec\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("degnum"),e("degden")]),FIND:d("\uc774 \ud568\uc218\ub294 \ub2e4\ub978 \ud14d\uc2a4\ud2b8 \uac12\uc5d0\uc11c \ud14d\uc2a4\ud2b8 \uac12\uc744 \ucc3e\uace0 \uac80\uc0c9\ud55c \ud14d\uc2a4\ud2b8\uc5d0\uc11c \ud574\ub2f9 \ud14d\uc2a4\ud2b8 \uac12\uc758 \uc704\uce58\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("findtext"),e("intext"),e("start")]),FINV:d("\uc774 \ud568\uc218\ub294 F \ud655\ub960 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("p"),e("degnum"),e("degden")]),FISHER:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc5d0 \ub300\ud55c Fisher \ubcc0\ud658\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),FISHERINV:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc5d0 \ub300\ud55c Fisher \ubcc0\ud658\uc758 \uc5ed\ubcc0\ud658 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),FIXED:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790\ub97c \uc9c0\uc815\ub41c \uc18c\uc218 \uc790\ub9bf\uc218\ub85c \ubc18\uc62c\ub9bc\ud558\uace0, \ub9c8\uce68\ud45c\uc640 \uc27c\ud45c(\uc9c0\uc815\ub41c \uacbd\uc6b0)\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc18c\uc218\uc758 \uc11c\uc2dd\uc744 \uc9c0\uc815\ud558\uace0, \uacb0\uacfc\ub97c \ud14d\uc2a4\ud2b8\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("num"),e("digits"),e("notcomma")]),FLOOR:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \ubc30\uc218\uac00 \ub418\ub3c4\ub85d \uc808\ub300 \uac12\uc744 \ub0b4\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("signif")]),FORECAST:d("\uc774 \ud568\uc218\ub294 \uae30\uc874 \uac12\uc744 \uc0ac\uc6a9\ud558\uc5ec \ubbf8\ub798 \uac00\uce58\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("Yarray"),e("Xarray")]),FREQUENCY:d("\uc774 \ud568\uc218\ub294 \uac12\uc758 \ubc94\uc704 \ub0b4\uc5d0\uc11c \uac12\uc758 \ube48\ub3c4\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4. \uc774 \ud568\uc218\ub294 \uc218\uc758 \uc138\ub85c \ubc30\uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("dataarray"),e("binarray")]),FTEST:d("\uc774 \ud568\uc218\ub294 \ub450 \ubc30\uc5f4\uc758 \ubd84\uc0b0\uc774 \ud06c\uac8c \ucc28\uc774\uac00 \ub098\uc9c0 \uc54a\ub294 \uacbd\uc6b0 \ub2e8\uce21 \ud655\ub960\uc778 F-\uac80\uc815 \uacb0\uacfc\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),FV:d("\uc774 \ud568\uc218\ub294 \ud604\uc7ac \uac00\uce58, \uc8fc\uae30\uc801\uc778 \ub0a9\uc785 \ubc0f \uc9c0\uc815\ub41c \uc774\uc790\uc728\uc5d0 \uc758\uac70\ud55c \ud22c\uc790\uc758 \ubbf8\ub798 \uac00\uce58\ub97c \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("rate"),e("numper"),e("paymt"),e("pval"),e("type")]),FVSCHEDULE:d("\uc774 \ud568\uc218\ub294 \ucd08\uae30 \uc6d0\uae08\uc5d0 \uc77c\ub828\uc758 \ubcf5\ub9ac \uc774\uc728\uc744 \uc801\uc6a9\ud588\uc744 \ub54c\uc758 \ubbf8\ub798 \uac00\uce58\ub97c \uc0b0\ucd9c\ud569\ub2c8\ub2e4. \ubcc0\ub3d9 \uae08\ub9ac\ub97c \uc801\uc6a9\ud558\uc5ec \ud22c\uc790\uc758 \ubbf8\ub798 \uac00\uce58\ub97c \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("principal"),e("schedule")]),GAMMADIST:d("\uc774 \ud568\uc218\ub294 \uac10\ub9c8 \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),GAMMAINV:d("\uc774 \ud568\uc218\ub294 \uac10\ub9c8 \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("p"),e("alpha"),e("beta")]),GAMMALN:d("\uc774 \ud568\uc218\ub294 \uac10\ub9c8 \ud568\uc218 G(x)\uc758 \uc790\uc5f0 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),GCD:d("\uc774 \ud568\uc218\ub294 \ub450 \uc218\uc758 \ucd5c\ub300 \uacf5\uc57d\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2")]),GEOMEAN:d("\uc774 \ud568\uc218\ub294 \uc591\uc218 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \uae30\ud558 \ud3c9\uade0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),GESTEP:d("\uc774 \ud568\uc218(\ubcf4\ub2e4 \ud06c\uac70\ub098 \uac19\uc74c)\ub294 \uc22b\uc790\uac00 \uc784\uacc4\uac12\uacfc \uac19\uc740\uc9c0 \uc5ec\ubd80\ub97c \ub098\ud0c0\ub0c5\ub2c8\ub2e4.",[e("number"),e("step")]),GROWTH:d("\uc774 \ud568\uc218\ub294 \uc608\uc0c1 \uc9c0\uc218 \uc99d\uac00\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4. \uc774 \ud568\uc218\ub294 \uae30\uc874 x \ubc0f y \uac12\uc744 \uc0ac\uc6a9\ud558\uc5ec \uc9c0\uc815\ud55c \uc0c8 x \uac12 \uacc4\uc5f4\uc5d0 \ub300\ud55c y \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("y"),e("x"),e("newx"),e("constant")]),HARMEAN:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \uc870\ud654 \ud3c9\uade0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),HEX2BIN:d("\uc774 \ud568\uc218\ub294 16\uc9c4\uc218\ub97c 2\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e(" places")]),HEX2DEC:d("\uc774 \ud568\uc218\ub294 16\uc9c4\uc218\ub97c 10\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number")]),HEX2OCT:d("\uc774 \ud568\uc218\ub294 16\uc9c4\uc218\ub97c 8\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e(" places")]),HLOOKUP:d("\uc774 \ud568\uc218\ub294 \uccab \ud589\uc5d0\uc11c \uac12\uc744 \uac80\uc0c9\ud55c \ub2e4\uc74c \uc9c0\uc815\ub41c \ud589\uc758 \ub3d9\uc77c\ud55c \uc5f4\uc5d0 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("array"),e("row"),e("approx")]),HOUR:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc2dc\uac04\uc5d0 \ud574\ub2f9\ud558\ub294 \uc2dc\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("time")]),HYPGEOMDIST:d("\uc774 \ud568\uc218\ub294 \ucd08\uae30 \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("n"),e("M"),e("N")]),IF:d("\uc774 \ud568\uc218\ub294 \uc81c\uacf5\ub41c \ub450 \uac12\uc744 \ube44\uad50\ud55c \ud6c4 \uacb0\uacfc\uc5d0 \ub530\ub77c \ub458 \uc911 \ud558\ub098\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("valueTest"),e("valueTrue"),e("valueFalse")]),IFERROR:d("\uc774 \ud568\uc218\ub294 \uc218\uc2dd\uc744 \ud3c9\uac00\ud55c \ud6c4 \uc81c\uacf5\ub41c \uac12(\uc624\ub958\uac00 \uc788\ub294 \uacbd\uc6b0) \ub610\ub294 \uc218\uc2dd \uacb0\uacfc\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("error")]),IMABS:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc808\ub300\uac12 \ub610\ub294 \ubaa8\ub4c8\ub7ec\uc2a4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMAGINARY:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \ud5c8\uc218\ubd80 \uacc4\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMARGUMENT:d("\uc774 \ud568\uc218\ub294 \uac01\ub3c4\ub97c \ub77c\ub514\uc548\uc73c\ub85c \ud45c\uc2dc\ud55c \uc778\uc218theta\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMCONJUGATE:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \ucf24\ub808 \ubcf5\uc18c\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMCOS:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 COS\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMDIV:d("\uc774 \ud568\uc218\ub294 \ub450 \ubcf5\uc18c\uc218\uc758 \ubaab\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum"),e("complexdenom")]),IMEXP:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc9c0\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMLN:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc790\uc5f0 \ub85c\uadf8\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMLOG2:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \ubc11\uc774 2\uc778 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMLOG10:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc0c1\uc6a9 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMPOWER:d("\uc774 \ud568\uc218\ub294 \uac70\ub4ed\uc81c\uacf1\ud55c \ubcf5\uc18c\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum"),e("powernum")]),IMPRODUCT:d("\uc774 \ud568\uc218\ub294 \ucd5c\ub300 29\uac1c \ubcf5\uc18c\uc218\uc758 \uacf1\uc744 x+yi \ub610\ub294 x+yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum1"),e("complexnum2",!0)]),IMREAL:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc2e4\uc218\ubd80 \uacc4\uc218\ub97c x+yi \ub610\ub294 x+yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMSIN:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc0ac\uc778\uc744 x+yi \ub610\ub294 x+yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMSQRT:d("\uc774 \ud568\uc218\ub294 \ubcf5\uc18c\uc218\uc758 \uc81c\uacf1\uadfc\uc744 x+yi \ub610\ub294 x+yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMSUB:d("\uc774 \ud568\uc218\ub294 \ub450 \ubcf5\uc18c\uc218\uc758 \ucc28\ub97c x+yi \ub610\ub294 x+yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum1"),e("complexnum2")]),IMSUM:d("\uc774 \ud568\uc218\ub294 \ub450 \uac1c \uc774\uc0c1\uc758 \ubcf5\uc18c\uc218\uc758 \ud569\uc744 x+yi \ub610\ub294 x+yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum1"),e("complexnum2",!0)]),INDEX:d("\uc774 \ud568\uc218\ub294 \ubc30\uc5f4 \ub610\ub294 \ubc94\uc704 \ub0b4\uc5d0\uc11c \uac12 \ub610\ub294 \uac12\uc5d0 \ub300\ud55c \ucc38\uc870\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("return"),e("row"),e("col"),e("area")]),INDIRECT:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\ub85c \uc9c0\uc815\ub41c \ucc38\uc870\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \ucc38\uc870\ub97c \uc989\uc2dc \uacc4\uc0b0\ud558\uc5ec \ucf58\ud150\uce20\ub97c \ud45c\uc2dc\ud569\ub2c8\ub2e4.",[e("ref_text"),e("a1_style")]),INT:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\ub97c \uc18c\uc218\uc810 \uc544\ub798\ub97c \ubc84\ub9ac\uace0 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc815\uc218\ub85c \ub0b4\ub9bc\ud569\ub2c8\ub2e4.",[e("value")]),INTERCEPT:d("\uc774 \ud568\uc218\ub294 \uae30\uc874 x \uac12\uacfc y \uac12\uc744 \uc0ac\uc6a9\ud558\uc5ec \uc120\uc774 y\ucd95\uacfc \uad50\ucc28\ud558\ub294 \uc810\uc758 \uc88c\ud45c\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("dependent"),e("independent")]),INTRATE:d("\uc774 \ud568\uc218\ub294 \uc644\uc804 \ud22c\uc790\ud55c \uc720\uac00 \uc99d\uad8c\uc758 \uc774\uc790\uc728\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("invest"),e("redeem"),e("basis")]),IPMT:d("\uc774 \ud568\uc218\ub294 \ub300\ucd9c \uc774\uc790 \ub0a9\uc785\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("rate"),e("per"),e("nper"),e("pval"),e("fval"),e("type")]),IRR:d("\uc774 \ud568\uc218\ub294 \ubc30\uc5f4\uc5d0 \uc22b\uc790\ub85c \ud45c\uc2dc\ub418\ub294 \uc77c\ub828\uc758 \ud604\uae08 \ud750\ub984\uc5d0 \ub300\ud55c \ub0b4\ubd80 \uc218\uc775\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("arrayvals"),e("estimate")]),ISBLANK:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc774 \ube44\uc5b4 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISERR:d("\uc774 \ud568\uc218(Is Error Other Than Not Available)\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \uc0ac\uc6a9 \ubd88\uac00\ub2a5(#N/A) \uc774\uc678 \uc624\ub958\uac00 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISERROR:d("\uc774 \ud568\uc218(Is Error of Any Kind)\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \uc624\ub958\uac00 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISEVEN:d("\uc774 \ud568\uc218(Is Number Even)\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc774 \uc9dd\uc218\uc778\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISLOGICAL:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc774 \ub17c\ub9ac\uac12(Boolean)\uc778\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISNA:d("\uc774 \ud568\uc218(Is Not Available)\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \uc0ac\uc6a9 \ubd88\uac00\ub2a5(#N/A) \uc624\ub958 \uac12\uc774 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISNONTEXT:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \ud14d\uc2a4\ud2b8 \uc774\uc678\uc758 \ub370\uc774\ud130 \ud615\uc2dd\uc774 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISNUMBER:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \uc22b\uc790 \ub370\uc774\ud130\uac00 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISODD:d("\uc774 \ud568\uc218(Is Number Odd)\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \uc22b\uc790 \ub370\uc774\ud130\uac00 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISPMT:d("\uc774 \ud568\uc218\ub294 \uc77c\uc815 \uae30\uac04 \ub3d9\uc548\uc758 \ud22c\uc790\uc5d0 \ub300\ud55c \uc774\uc790 \uc9c0\uae09\uc561\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("rate"),e("per"),e("nper"),e("pv")]),ISREF:d("\uc774 \ud568\uc218(Is Reference)\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc774 \ub2e4\ub978 \uc140\uc5d0 \ub300\ud55c \ucc38\uc870\uc778\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),ISTEXT:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc140\uc758 \uac12, \uc2dd \ub610\ub294 \ub0b4\uc6a9\uc5d0 \ud14d\uc2a4\ud2b8 \ub370\uc774\ud130\uac00 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud14c\uc2a4\ud2b8\ud569\ub2c8\ub2e4.",[e("cellreference")]),KURT:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \ucca8\ub3c4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2"),e("value3"),e("value4",!0)]),LARGE:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c n\ubc88\uc9f8\ub85c \ud070 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. n\uc740 \uc9c0\uc815\ub429\ub2c8\ub2e4.",[e("array"),e("n")]),LCM:d("\uc774 \ud568\uc218\ub294 \ub450 \uc218\uc758 \ucd5c\uc18c \uacf5\ubc30\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2")]),LEFT:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \uac12\uc5d0\uc11c \ub9e8 \uc67c\ucabd\uc5d0 \uc704\uce58\ud55c \uc9c0\uc815\ub41c \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("mytext"),e("num_chars")]),LEN:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uae38\uc774(\ubb38\uc790 \uc218)\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),LINEST:d("\uc774 \ud568\uc218\ub294 \uc904\uc5d0 \ub300\ud55c \ud1b5\uacc4\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("y"),e("x"),e("constant"),e("stats")]),LN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 \uc790\uc5f0 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),LOG:d("\uc774 \ud568\uc218\ub294 \uc218 X\uc5d0 \ub300\ud574 \ubc11\uc774 Y\uc778 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("base")]),LOG10:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc5d0 \ub300\ud574 \ubc11\uc774 10\uc778 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),LOGEST:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130\uc640 \uc77c\uce58\ud558\ub294 \uc9c0\uc218 \uace1\uc120\uc744 \uacc4\uc0b0\ud558\uace0 \uace1\uc120\uc744 \uc124\uba85\ud558\ub294 \uac12\uc758 \ubc30\uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("y"),e("x"),e("constant"),e("stats")]),LOGINV:d("\uc774 \ud568\uc218\ub294 x\uc5d0 \ub300\ud55c \ub85c\uadf8 \uc815\uaddc \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. \uc5ec\uae30\uc11c LN(x)\uc740 \uc9c0\uc815\ub41c \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\ub97c \uac16\ub294 \uc815\uaddc \ubd84\ud3ec\uc785\ub2c8\ub2e4.",[e("prob"),e("mean"),e("stdev")]),
LOGNORMDIST:d("\uc774 \ud568\uc218\ub294 x\uc5d0 \ub300\ud55c \ub204\uc801 \uc790\uc5f0 \ub85c\uadf8 \uc815\uaddc \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc5ec\uae30\uc11c LN(x)\uc740 \uc9c0\uc815\ub41c \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\ub97c \uac16\ub294 \uc815\uaddc \ubd84\ud3ec\uc785\ub2c8\ub2e4. \uc774 \ud568\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \ub300\uc218\uc801\uc73c\ub85c \ubcc0\ud658\ub41c \ub370\uc774\ud130\ub97c \ubd84\uc11d\ud569\ub2c8\ub2e4.",[e("x"),e("mean"),e("stdev")]),LOOKUP:d("\uc774 \ud568\uc218\ub294 \uac12\uc744 \uac80\uc0c9\ud558\uace0 \ub450 \ubc88\uc9f8 \uc601\uc5ed\uc758 \ub3d9\uc77c\ud55c \uc704\uce58\uc5d0\uc11c \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("lookupvalue"),e("lookupvector"),e("resultvector")]),LOWER:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8\ub97c \uc18c\ubb38\uc790\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("string")]),MATCH:d("\uc774 \ud568\uc218\ub294 \ubc94\uc704 \ub0b4\uc5d0\uc11c \uc9c0\uc815\ub41c \ud56d\ubaa9\uc758 \uc0c1\ub300 \uc704\uce58\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("array"),e("type")]),MAX:d("\uc774 \ud568\uc218\ub294 \uc778\uc218\uc758 \ubaa8\ub4e0 \uac12 \uc911\uc5d0\uc11c \uac00\uc7a5 \ud070 \uac12\uc778 \ucd5c\ub300\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),MAXA:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8\uc640 \ub17c\ub9ac\uac12\uc744 \ud3ec\ud568\ud558\uc5ec \uc778\uc218 \ubaa9\ub85d\uc5d0\uc11c \uac00\uc7a5 \ud070 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),MDETERM:d("\uc774 \ud568\uc218\ub294 \ubc30\uc5f4\uc758 \ud589\ub82c \uc2dd\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array")]),MDURATION:d("\uc774 \ud568\uc218\ub294 \uac00\uc815\ub41c \uc561\uba74\uac00\uac00 $100\uc778 \uc720\uac00 \uc99d\uad8c\uc758 \uc218\uc815\ub41c Macaulay \uae30\uac04\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("coupon"),e("yield"),e("frequency"),e("basis")]),MEDIAN:d("\uc774 \ud568\uc218\ub294 \uc81c\uacf5\ub41c \uc22b\uc790\ub4e4 \uc911 \uc911\uac04\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. \uc989, \uc22b\uc790\ub4e4 \uc911 \uc808\ubc18\uc740 \uc911\uac04\uac12\ubcf4\ub2e4 \ud070 \uac12\uc744 \uac16\uace0, \uc808\ubc18\uc740 \uc911\uac04\uac12\ubcf4\ub2e4 \uc791\uc740 \uac12\uc744 \uac16\uc2b5\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),MID:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc704\uce58\uc5d0\uc11c \uc2dc\uc791\ud558\uc5ec \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc5d0\uc11c \uc694\uccad\ub41c \uc218\uc758 \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("text"),e("start_num"),e("num_chars")]),MIN:d("\uc774 \ud568\uc218\ub294 \uc778\uc218\uc758 \ubaa8\ub4e0 \uac12 \uc911\uc5d0\uc11c \uac00\uc7a5 \uc791\uc740 \uac12\uc778 \ucd5c\uc18c\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),MINA:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8\uc640 \ub17c\ub9ac\uac12\uc744 \ud3ec\ud568\ud558\uc5ec \uc778\uc218 \ubaa9\ub85d\uc5d0\uc11c \ucd5c\uc18c\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),MINUTE:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc2dc\uac04\uc5d0 \ud574\ub2f9\ud558\ub294 \ubd84\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("time")]),MINVERSE:d("\uc774 \ud568\uc218\ub294 \ubc30\uc5f4\uc758 \uc5ed\ud589\ub82c\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array")]),MIRR:d("\uc774 \ud568\uc218\ub294 \uc77c\ub828\uc758 \uc8fc\uae30\uc801\uc778 \ud604\uae08 \ud750\ub984\uc5d0 \ub300\ud55c \uc218\uc815\ub41c \ub0b4\ubd80 \uc218\uc775\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("arrayvals"),e("payment_int"),e("income_int")]),MMULT:d("\uc774 \ud568\uc218\ub294 \ub450 \ubc30\uc5f4\uc758 \ud589\ub82c \uacf1\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),MOD:d("\uc774 \ud568\uc218\ub294 \ub098\ub217\uc148\uc758 \ub098\uba38\uc9c0\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("dividend"),e("divisor")]),MODE:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c \uac00\uc7a5 \uc790\uc8fc \ubc1c\uc0dd\ud558\ub294 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),MONTH:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \ub0a0\uc9dc \uac12\uc5d0 \ud574\ub2f9\ud558\ub294 \uc6d4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date")]),MROUND:d("\uc774 \ud568\uc218\ub294 \uc6d0\ud558\ub294 \ubc30\uc218\ub85c \ubc18\uc62c\ub9bc\ub41c \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("multiple")]),MULTINOMIAL:d("\uc774 \ud568\uc218\ub294 \uac12\uc758 \ud569\uacc4\uc640 \uacc4\uc2b9\uac12\uc758 \uacf1\uc758 \ube44\uc728\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),N:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790\ub85c \ubcc0\ud658\ub41c \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),NA:d("\uc774 \ud568\uc218\ub294 \ud3c9\uade0\uc774 \uc0ac\uc6a9 \ubd88\uac00\ud55c \uc624\ucc28 \uac12 #N/A\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),NEGBINOMDIST:d("\uc774 \ud568\uc218\ub294 \uc74c \uc774\ud56d \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("r"),e("p")]),NETWORKDAYS:d("\uc774 \ud568\uc218\ub294 \uc2dc\uc791 \ub0a0\uc9dc\uc640 \uc885\ub8cc \ub0a0\uc9dc \uc0ac\uc774\uc758 \uc804\uccb4 \uc791\uc5c5\uc77c\uc758 \ucd1d \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("startdate"),e("enddate"),e("holidays")]),NOMINAL:d("\uc774 \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \uc2e4\ud6a8 \uc774\uc728\uc5d0 \ub300\ud55c \uba85\ubaa9\uc0c1\uc758 \uc5f0\uc774\uc728\uacfc \uc5f0\uac04 \ubcf5\ub9ac \uacc4\uc0b0 \ud69f\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("effrate"),e("comper")]),NORMDIST:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ud55c \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\uc5d0 \uc758\uac70\ud558\uc5ec \uc815\uaddc \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("mean"),e("stdev"),e("cumulative")]),NORMINV:d("\uc774 \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\uc5d0 \uc758\uac70\ud558\uc5ec \uc815\uaddc \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("prob"),e("mean"),e("stdev")]),NORMSDIST:d("\uc774 \ud568\uc218\ub294 \ud45c\uc900 \uc815\uaddc \ub204\uc801 \ubd84\ud3ec \ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),NORMSINV:d("\uc774 \ud568\uc218\ub294 \ud45c\uc900 \uc815\uaddc \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \ubd84\ud3ec\uc758 \ud3c9\uade0\uc774 0\uc774\uace0 \ud45c\uc900 \ud3b8\ucc28\uac00 1\uc785\ub2c8\ub2e4.",[e("prob")]),NOT:d("\uc774 \ud568\uc218\ub294 \uc778\uc218\uc758 \ub17c\ub9ac\uac12\uc744 \ub418\ub3cc\ub9bd\ub2c8\ub2e4.",[e("value")]),NOW:d("\uc774 \ud568\uc218\ub294 \ud604\uc7ac \ub0a0\uc9dc\uc640 \uc2dc\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),NPER:d("\uc774 \ud568\uc218\ub294 \ud604\uc7ac \uac00\uce58, \ubbf8\ub798 \uac00\uce58, \uc8fc\uae30\uc801\uc778 \ub0a9\uc785 \ubc0f \uc9c0\uc815\ub41c \uc774\uc790\uc728\uc5d0 \uc758\uac70\ud55c \ud22c\uc790 \uae30\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("rate"),e("paymt"),e("pval"),e("fval"),e("type")]),NPV:d("\uc774 \ud568\uc218\ub294 \ud560\uc778\uc728\uacfc \uc77c\ub828\uc758 \ubbf8\ub798 \ud22c\uc790 \ubc0f \uc218\uc785\uc744 \uae30\uc900\uc73c\ub85c \ud22c\uc790\uc758 \uc21c \ud604\uc7ac \uac00\uce58\ub97c \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("discount"),e("value1"),e("value2",!0)]),OCT2BIN:d("\uc774 \ud568\uc218\ub294 8\uc9c4\uc218\ub97c 2\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),OCT2DEC:d("\uc774 \ud568\uc218\ub294 8\uc9c4\uc218\ub97c 10\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number")]),OCT2HEX:d("\uc774 \ud568\uc218\ub294 8\uc9c4\uc218\ub97c 16\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("places")]),ODD:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac12\uc744 \uac00\uc7a5 \uac00\uae4c\uc6b4 \ud640\uc218\uc778 \uc815\uc218\ub85c \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value")]),ODDFPRICE:d("\uc774 \ud568\uc218\ub294 \uccab \uc774\uc218 \uae30\uac04\uc774 \uacbd\uc0c1 \uc774\uc218 \uae30\uac04\uacfc \ub2e4\ub978(\uc9e7\uac70\ub098 \uae34) \uc720\uac00 \uc99d\uad8c\uc758 \uc561\uba74\uac00 $100\ub2f9 \uac00\uaca9\uc744 \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("issue"),e("first"),e("rate"),e("yield"),e("redeem"),e("freq"),e("basis")]),ODDFYIELD:d("\uc774 \ud568\uc218\ub294 \uccab \uc774\uc218 \uae30\uac04\uc774 \uacbd\uc0c1 \uc774\uc218 \uae30\uac04\uacfc \ub2e4\ub978(\uc9e7\uac70\ub098 \uae34) \uc720\uac00 \uc99d\uad8c\uc758 \uc5f0 \uc218\uc775\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("issue"),e("first"),e("rate"),e("price"),e("redeem"),e("freq"),e("basis")]),ODDLPRICE:d("\uc774 \ud568\uc218\ub294 \ub9c8\uc9c0\ub9c9 \uc774\uc218 \uae30\uac04\uc774 \uacbd\uc0c1 \uc774\uc218 \uae30\uac04\uacfc \ub2e4\ub978(\uc9e7\uac70\ub098 \uae34) \uc720\uac00 \uc99d\uad8c\uc758 \uc561\uba74\uac00 $100\ub2f9 \uac00\uaca9\uc744 \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("last"),e("rate"),e("yield"),e("redeem"),e("freq"),e("basis")]),ODDLYIELD:d("\uc774 \ud568\uc218\ub294 \ub9c8\uc9c0\ub9c9 \uc774\uc218 \uae30\uac04\uc774 \uacbd\uc0c1 \uc774\uc218 \uae30\uac04\uacfc \ub2e4\ub978(\uc9e7\uac70\ub098 \uae34) \uc720\uac00 \uc99d\uad8c\uc758 \uc5f0 \uc218\uc775\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("last"),e("rate"),e("price"),e("redeem"),e("freq"),e("basis")]),OFFSET:d("\uc774 \ud568\uc218\ub294 \ubc94\uc704\uc5d0 \ub300\ud55c \ucc38\uc870\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \ubc94\uc704\ub294 \uc140 \ub610\ub294 \uc140 \ubc94\uc704\uc5d0\uc11c \uc9c0\uc815\ub41c \ud589\uacfc \uc5f4\uc758 \uac1c\uc218\uc785\ub2c8\ub2e4. \uc774 \ud568\uc218\ub294 \ub2e8\uc77c \uc140 \ub610\ub294 \uc140 \ubc94\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("reference"),e("rows"),e("cols"),e("height"),e("width")]),OR:d("\uc774 \ud568\uc218\ub294 \ub17c\ub9ac\uac12 OR\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4. \uc778\uc218 \uc911 \ud558\ub098 \uc774\uc0c1\uc774 \ucc38\uc774\uba74 TRUE\ub97c \ubc18\ud658\ud558\uace0, \ubaa8\ub4e0 \uc778\uc218\uac00 \uac70\uc9d3\uc774\uba74 FALSE\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("argument1"),e("argument2...")]),PEARSON:d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc138\ud2b8\uc758 \uc120\ud615 \uad00\uacc4\ub97c \ub098\ud0c0\ub0b4\ub294 -1.0\uc5d0\uc11c 1.0 \uc0ac\uc774\uc758 \ubb34 \ucc28\uc6d0 \uc778\ub371\uc2a4 \uc778 Pearson \uacf1\uc148 \uc0c1\uad00 \uacc4\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array_ind"),e("array_dep")]),PERCENTILE:d("\uc774 \ud568\uc218\ub294 \ubc94\uc704\uc5d0\uc11c n\ubc88\uc9f8 \ubc31\ubd84\uc704\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("n")]),PERCENTRANK:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c \ubc31\ubd84\uc728 \uc21c\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("n"),e("sigdig")]),PERMUT:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \ud56d\ubaa9 \uc218\uc5d0 \ub300\ud574 \uac00\ub2a5\ud55c \uc21c\uc5f4\uc758 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("k"),e("n")]),PI:d("\uc774 \ud568\uc218\ub294 PI\ub97c 3.1415926536\uc73c\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),PMT:d("\uc774 \ud568\uc218\ub294 \ud604\uc7ac \uac00\uce58, \uc9c0\uc815\ub41c \uc774\uc790\uc728 \ubc0f \ud56d\ubaa9 \uc218\uc5d0 \uc758\uac70\ud55c \ub300\ucd9c \ub300\uae08 \uc9c0\uae09\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("rate"),e("nper"),e("pval"),e("fval"),e("type")]),POISSON:d("\uc774 \ud568\uc218\ub294 Poisson \ud655\ub960 \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("nevents"),e("mean"),e("cumulative")]),POWER:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ud55c \uc218\ub97c \uc9c0\uc815\ud55c \uac70\ub4ed\uc81c\uacf1\uc73c\ub85c \uc62c\ub9bd\ub2c8\ub2e4.",[e("number"),e("power")]),PPMT:d("\uc774 \ud568\uc218\ub294 \ud604\uc7ac \uac00\uce58, \uc9c0\uc815\ub41c \uc774\uc790\uc728 \ubc0f \ud56d\ubaa9 \uc218\uc5d0 \uc758\uac70\ud55c \ub300\ucd9c\uc758 \uc6d0\uae08 \ub0a9\uc785\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("rate"),e("per"),e("nper"),e("pval"),e("fval"),e("type")]),PRICE:d("\uc774 \ud568\uc218\ub294 \uc815\uae30\uc801\uc73c\ub85c \uc774\uc790\ub97c \uc9c0\uae09\ud558\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \uc561\uba74\uac00 $100\ub2f9 \uac00\uaca9\uc744 \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("settlement"),e("maturity"),e("rate"),e("yield"),e("redeem"),e("frequency"),e("basis")]),PRICEDISC:d("\uc774 \ud568\uc218\ub294 \ud560\uc778\ub41c \uc720\uac00 \uc99d\uad8c\uc758 \uc561\uba74\uac00 $100\ub2f9 \uac00\uaca9\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("discount"),e("redeem"),e("basis")]),PRICEMAT:d("\uc774 \ud568\uc218\ub294 \uc774\uc790\ub97c \uc9c0\uae09\ud558\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \uc561\uba74\uac00 $100\ub2f9 \ub9cc\uae30 \uc2dc \uac00\uaca9\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("issue"),e("rate"),e("yield"),e("basis")]),PROB:d("\uc774 \ud568\uc218\ub294 \uc601\uc5ed \ub0b4\uc758 \uac12\uc774 \ub450 \ud55c\uacc4\uac12 \uc0ac\uc774\uc5d0 \uc788\uc744 \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("probs"),e("lower"),e("upper")]),PRODUCT:d("\uc774 \ud568\uc218\ub294 \ubaa8\ub4e0 \uc778\uc218\uc758 \uacf1\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),PROPER:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uac01 \ub2e8\uc5b4\uc5d0\uc11c \uccab \uae00\uc790\ub97c \ub300\ubb38\uc790\ud654\ud569\ub2c8\ub2e4.",[e("text")]),PV:d("\uc774 \ud568\uc218\ub294 \uc774\uc790\uc728, \uc8fc\uae30\uc801\uc778 \ub0a9\uc785 \ud69f\uc218\uc640 \uae08\uc561, \ubbf8\ub798 \uac00\uce58\uc5d0 \uc758\uac70\ud55c \ud22c\uc790\uc758 \ud604\uc7ac \uac00\uce58\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \ud604\uc7ac \uac00\uce58\ub294 \uc77c\ub828\uc758 \ubbf8\ub798 \ud22c\uc790\uac00 \uc0c1\uc751\ud558\ub294 \ucd1d\ud569\uacc4\uc785\ub2c8\ub2e4.",[e("rate"),e("numper"),e("paymt"),e("fval"),e("type")]),QUARTILE:d("\uc774 \ud568\uc218\ub294 \uac12\uc774 \uc18d\ud558\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \ubc31\ubd84\uc704\uc218(1/4 \ub610\ub294 25%)\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("quart")]),QUOTIENT:d("\uc774 \ud568\uc218\ub294 \ub098\ub217\uc148 \ubaab\uc758 \uc815\uc218 \ubd80\ubd84\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. \ub098\ub217\uc148 \ubaab\uc758 \ub098\uba38\uc9c0\ub97c \ubb34\uc2dc\ud558\ub824\uba74 \uc774 \ud568\uc218\ub97c \uc0ac\uc6a9\ud569\ub2c8\ub2e4.",[e("numerator"),e("denominator")]),RADIANS:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\ub97c \ub3c4 \ub2e8\uc704\uc5d0\uc11c \ub77c\ub514\uc548 \ud615\ud0dc\uc758 \uac01\ub3c4\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("value")]),RAND:d("\uc774 \ud568\uc218\ub294 0\uacfc 1 \uc0ac\uc774\uc758 \uade0\ub4f1\ud558\uac8c \ubd84\ud3ec\ub41c \ub09c\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),RANDBETWEEN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ud55c \uc22b\uc790 \uc0ac\uc774\uc758 \ub09c\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("lower"),e("upper")]),RANK:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uc9d1\ud569 \ub0b4\uc5d0\uc11c \uc9c0\uc815\ud55c \uc218\uc758 \uc21c\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc9d1\ud569\uc744 \uc815\ub82c\ud560 \uacbd\uc6b0 \uc218\uc758 \uc21c\uc704\ub294 \ubaa9\ub85d \ub0b4\uc5d0\uc11c \ud574\ub2f9 \uc218\uc758 \uc704\uce58\uc785\ub2c8\ub2e4.",[e("number"),e("array"),e("order")]),RATE:d("\uc774 \ud568\uc218\ub294 \uc5f0\uae08\uc758 \uae30\uac04\ubcc4 \uc774\uc790\uc728\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("nper"),e("pmt"),e("pval"),e("fval"),e("type"),e("guess")]),RECEIVED:d("\uc774 \ud568\uc218\ub294 \uc644\uc804 \ud22c\uc790 \uc720\uac00 \uc99d\uad8c\uc5d0 \ub300\ud574 \ub9cc\uae30 \uc2dc \uc218\ub839\ud558\ub294 \uae08\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("invest"),e("discount"),e("basis")]),REPLACE:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uc77c\ubd80\ub97c \ub2e4\ub978 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\ub85c \ubc14\uafc9\ub2c8\ub2e4.",[e("old_text"),e("start_char"),e("num_chars"),e("new_text")]),REPT:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8\ub97c \uc9c0\uc815\ub41c \ud69f\uc218\ub9cc\ud07c \ubc18\ubcf5\ud569\ub2c8\ub2e4.",[e("text"),e("number")]),RIGHT:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \uac12\uc5d0\uc11c \ub9e8 \uc624\ub978\ucabd\uc5d0 \uc704\uce58\ud55c \uc9c0\uc815\ub41c \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("text"),e("num_chars")]),ROMAN:d("\uc774 \ud568\uc218\ub294 \uc544\ub77c\ube44\uc544 \uc22b\uc790\ub97c \ud14d\uc2a4\ud2b8\uc778 \ub85c\ub9c8 \uc22b\uc790\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("style")]),ROUND:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc18c\uc218 \uc790\ub9bf\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc9c0\uc815\ub41c \uac12\uc744 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc22b\uc790\ub85c \ubc18\uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("places")]),ROUNDDOWN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc18c\uc218 \uc790\ub9bf\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc9c0\uc815\ub41c \uac12\uc744 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc22b\uc790\ub85c \ub0b4\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("places")]),ROUNDUP:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc18c\uc218 \uc790\ub9bf\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc9c0\uc815\ub41c \uac12\uc744 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc22b\uc790\ub85c \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("places")]),ROW:d("\uc774 \ud568\uc218\ub294 \ucc38\uc870 \uc601\uc5ed\uc758 \ud589 \ubc88\ud638\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("reference")]),ROWS:d("\uc774 \ud568\uc218\ub294 \ubc30\uc5f4\uc5d0 \uc788\ub294 \ud589 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array")]),RSQ:d("\uc774 \ud568\uc218\ub294 y \uac12\uacfc x \uac12\uc774 \uc54c\ub824\uc9c4 \ub370\uc774\ud130 \uc694\uc18c\uc5d0 \uc758\uac70\ud558\uc5ec Pearson \uacf1 \uc21c\uac04 \uc0c1\uad00 \uacc4\uc218\uc758 \uc81c\uacf1(R \uc81c\uacf1)\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array_dep"),e("array_ind")]),SEARCH:d("\uc774 \ud568\uc218\ub294 \ub2e4\ub978 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc5d0\uc11c \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc744 \ucc3e\uc740 \ub2e4\uc74c \ubc1c\uacac\ub41c \ud14d\uc2a4\ud2b8\uc758 \uc2dc\uc791 \uc704\uce58\uc758 \uc9c0\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("string1"),e("string2")]),SECOND:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc2dc\uac04\uc5d0 \ub300\ud55c \ucd08(0~59) \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("time")]),SERIESSUM:d("\uc774 \ud568\uc218\ub294 \uba71\uae09\uc218\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("n"),e("m"),e("coeff")]),SIGN:d("\uc774 \ud568\uc218\ub294 \uc2dd \ub610\ub294 \uc218\uc758 \ubd80\ud638\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cellreference")]),SIN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac01\ub3c4\uc758 SIN\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),SINH:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 SINH\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),SKEW:d("\uc774 \ud568\uc218\ub294 \ubd84\ud3ec\uc758 \uc65c\uace1\ub3c4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2",!0)]),SLN:d("\uc774 \ud568\uc218\ub294 \ud55c \uae30\uac04 \ub3d9\uc548 \uc815\uc561\ubc95\uc5d0 \uc758\ud55c \uc790\uc0b0\uc758 \uac10\uac00 \uc0c1\uac01\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cost"),e("salvage"),e("life")]),SLOPE:d("\uc774 \ud568\uc218\ub294 \uc120\ud615 \ud68c\uadc0\uc120\uc758 \uae30\uc6b8\uae30\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("array_dep"),e("array_ind")]),SMALL:d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c n\ubc88\uc9f8\ub85c \uc791\uc740 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. n\uc740 \uc9c0\uc815\ub429\ub2c8\ub2e4.",[e("array"),e("n")]),SQRT:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 \uc591\uc758 \uc81c\uacf1\uadfc\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),SQRTPI:d("\uc774 \ud568\uc218\ub294 \ud30c\uc774(p)\uc758 \ubc30\uc218\uc758 \uc591\uc758 \uc81c\uacf1\uadfc\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("multiple")]),STANDARDIZE:d("\uc774 \ud568\uc218\ub294 \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\ub85c \ud2b9\uc9d5\ub418\ub294 \ubd84\ud3ec\uc5d0\uc11c \uc815\uaddc\ud654\ub41c \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("mean"),e("stdev")]),STDEVA:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790, \ud14d\uc2a4\ud2b8 \ub610\ub294 \ub17c\ub9ac\uac12\uc758 \uc9d1\ud569\uc5d0 \ub300\ud55c \ud45c\uc900 \ud3b8\ucc28\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),STDEVP:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uac12\uc758 \uc804\uccb4 \uc9c0\uc815\ub41c \ubaa8\uc9d1\ub2e8\uc5d0 \ub300\ud55c \ud45c\uc900 \ud3b8\ucc28\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),STDEVPA:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ub610\ub294 \ub17c\ub9ac\uac12\uacfc \uc22b\uc790 \uac12\uc744 \ube44\ub86f\ud558\uc5ec \uc804\uccb4 \uc9c0\uc815\ub41c \ubaa8\uc9d1\ub2e8\uc5d0 \ub300\ud55c \ud45c\uc900 \ud3b8\ucc28\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),STEYX:d("\uc774 \ud568\uc218\ub294 \uc608\uce21\ud55c y \uac12\uc758 \ud45c\uc900 \uc624\ucc28\ub97c \uac01 x \uac12\uc5d0 \ub300\ud558\uc5ec \ubc18\ud658\ud569\ub2c8\ub2e4. \ud45c\uc900 \uc624\ucc28\ub294 x \uac12\uc5d0 \ub300\ud574 \uc608\uce21\ud55c y \uac12\uc758 \uc624\ucc28 \ud06c\uae30\uc785\ub2c8\ub2e4.",[e("array_dep"),e("array_ind")]),SUBSTITUTE:d("\uc774 \ud568\uc218\ub294 \uae30\uc874 \ubb38\uc790\uc5f4\uc758 \uc9c0\uc815\ub41c \ubb38\uc790\ub97c \uc0c8 \ubb38\uc790\uc5f4\ub85c \ub300\uccb4\ud569\ub2c8\ub2e4.",[e("text"),e("old_piece"),e("new_piece"),e("instance")]),SUBTOTAL:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uae30\ubcf8 \ud568\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc22b\uc790 \ubaa9\ub85d\uc758 \ubd80\ubd84\ud569\uc744 \uc0b0\ucd9c\ud569\ub2c8\ub2e4.",[e("functioncode"),e("value1"),e("value2",!0)]),SUM:d("\uc774 \ud568\uc218\ub294 \uc140 \ub610\ub294 \uc140 \ubc94\uc704\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),SUMIF:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uae30\uc900\uc744 \uc0ac\uc6a9\ud558\uc5ec \uc140\ub4e4\uc758 \ud569\uc744 \uad6c\ud569\ub2c8\ub2e4.",[e("array"),e("condition"),e("sumrange")]),SUMIFS:d("\uc774 \ud568\uc218\ub294 \uc5ec\ub7ec \uae30\uc900\uc744 \uc0ac\uc6a9\ud558\uc5ec \ubc94\uc704 \ub0b4 \uc140\ub4e4\uc758 \ud569\uc744 \uad6c\ud569\ub2c8\ub2e4.",[e("array"),e("conditionarray"),e("condition",!0)]),SUMPRODUCT:d("\uc774 \ud568\uc218\ub294 \uc140\ub4e4\uc758 \uc81c\uacf1\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. \uc8fc\uc5b4\uc9c4 \ubc30\uc5f4\uc5d0\uc11c \ud574\ub2f9 \uc694\uc18c\ub4e4\uc758 \uc81c\uacf1\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2",!0)]),SUMSQ:d("\uc774 \ud568\uc218\ub294 \uc778\uc218\uc758 \uc81c\uacf1\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),SUMX2MY2:d("\uc774 \ud568\uc218\ub294 \ub450 \ubc30\uc5f4\uc5d0\uc11c \ud574\ub2f9 \uac12\uc758 \uc81c\uacf1\uc758 \ucc28\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array_x"),e("array_y")]),SUMX2PY2:d("\uc774 \ud568\uc218\ub294 \ub450 \ubc30\uc5f4\uc5d0\uc11c \ud574\ub2f9 \uac12\uc758 \uc81c\uacf1\uc758 \ud569\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array_x"),e("array_y")]),SUMXMY2:d("\uc774 \ud568\uc218\ub294 \ub450 \ubc30\uc5f4\uc5d0\uc11c \ud574\ub2f9 \uac12\uc758 \ucc28\uc758 \uc81c\uacf1\uc758 \ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array_x"),e("array_y")]),SYD:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uae30\uac04 \ub3d9\uc548 \ub144\uc218 \ud569\uacc4\ubc95\uc5d0 \uc758\ud55c \uc790\uc0b0\uc758 \uac10\uac00 \uc0c1\uac01\uc561\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cost"),e("salvage"),e("life"),e("period")]),T:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc140\uc5d0 \uc788\ub294 \ud14d\uc2a4\ud2b8\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),TAN:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uac01\ub3c4\uc758 TAN\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),TANH:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 TANH\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),TBILLEQ:d("\uc774 \ud568\uc218\ub294 \uad6d\ucc44\uc5d0 \ud574\ub2f9\ud558\ub294 \uc218\uc775\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("discount")]),TBILLPRICE:d("\uc774 \ud568\uc218\ub294 \uc7ac\ubb34\ubd80 \uccad\uad6c\uc11c (\ub610\ub294 T- \uccad\uad6c\uc11c)\uc758 \uc561\uba74\uac00 $ 100 \ub2f9 \uac00\uaca9\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("discount")]),TBILLYIELD:d("\uc774 \ud568\uc218\ub294 \uad6d\ucc44\uc5d0 \ub300\ud55c \uc218\uc775\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("settle"),e("mature"),e("priceper")]),TDIST:d("\uc774 \ud568\uc218\ub294 t-\ubd84\ud3ec\uc5d0 \ub300\ud55c \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("deg"),e("tails")]),TEXT:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790\uc758 \uc11c\uc2dd\uc744 \uc9c0\uc815\ud558\uace0 \uc22b\uc790\ub97c \ud14d\uc2a4\ud2b8\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("text")]),TIME:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc2dc\uac04\uc5d0 \ub300\ud55c TimeSpan \uac1c\uccb4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("hour"),e("minutes"),e("seconds")]),TIMEVALUE:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\ub85c \ud45c\uc2dc\ub41c \uc2dc\uac04\uc758 TimeSpan \uac1c\uccb4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("time_string")]),TINV:d("\uc774 \ud568\uc218\ub294 \ud559\uc0dd\uc758 t-\ubd84\ud3ec\uc5d0 \ub300\ud55c t \uac12\uc744 \ud655\ub960 \ud568\uc218\uc640 \uc790\uc720\ub3c4\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("prog"),e("deg")]),TODAY:d("\uc774 \ud568\uc218\ub294 \ud604\uc7ac \ub0a0\uc9dc\uc640 \uc2dc\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),TRANSPOSE:d("\uc774 \ud568\uc218\ub294 \uc140\uc758 \ud589\uacfc \uc5f4\uc744 \ubc14\uafc9\ub2c8\ub2e4.",[e("array")]),TREND:d("\uc774 \ud568\uc218\ub294 \uc120\ud615 \ucd94\uc138\ub97c \ub530\ub77c \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. \uc774 \ud568\uc218\ub294 x \uac12\uacfc y \uac12\uc774 \uc54c\ub824\uc9c4 \ubc30\uc5f4\uc5d0 \uc9c1\uc120\uc744 \ub9de\ucda5\ub2c8\ub2e4. \uc9c0\uc815\ub41c \uc0c8 x \uac12\uc758 \ubc30\uc5f4\uc5d0 \ub300\ud574 \ud574\ub2f9 \uc120\uc744 \ub530\ub77c y \uac12\uc744 \ubc18\ud658\ud558\ub294 \uac83\uc774 \ucd94\uc138\uc785\ub2c8\ub2e4.",[e("y"),e("x"),e("newx"),e("constant")]),TRIM:d("\uc774 \ud568\uc218\ub294 \ubb38\uc790\uc5f4\uc5d0\uc11c \ucd94\uac00 \uacf5\ubc31\uc744 \uc81c\uac70\ud558\uace0 \ub2e8\uc5b4 \uc0ac\uc774\uc758 \uacf5\ubc31\uc744 \ud55c \uce78\uc73c\ub85c \uc720\uc9c0\ud569\ub2c8\ub2e4.",[e("text")]),TRIMMEAN:d("\uc774 \ud568\uc218\ub294 \ub9e8 \uc704 \ub370\uc774\ud130\uc640 \ub9e8 \uc544\ub798 \ub370\uc774\ud130\ub97c \uc81c\uc678\ud558\uace0 \ub370\uc774\ud130 \ud558\uc704 \uc9d1\ud569\uc758 \ud3c9\uade0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("percent")]),TRUE:d("\uc774 \ud568\uc218\ub294 \ub17c\ub9ac\uac12 TRUE\uc5d0 \ud574\ub2f9\ud558\ub294 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[]),TRUNC:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \uc218\uc758 \uc9c0\uc815\ub41c \ubd84\uc218 \ubd80\ubd84\uc744 \uc81c\uac70\ud569\ub2c8\ub2e4.",[e("value"),e("precision")]),TTEST:d("\uc774 \ud568\uc218\ub294 t-\uac80\uc99d\uacfc \uad00\ub828\ub41c \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2"),e("tails"),e("type")]),TYPE:d("\uc774 \ud568\uc218\ub294 \uac12\uc758 \uc720\ud615\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),UPPER:d("\uc774 \ud568\uc218\ub294 \ud14d\uc2a4\ud2b8\ub97c \ub300\ubb38\uc790\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("string")]),VALUE:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790\uc778 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc744 \uc22b\uc790 \uac12\uc73c\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("text")]),VAR:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uac12\ub9cc \uc0ac\uc6a9\ud558\ub294 \ubaa8\uc9d1\ub2e8\uc758 \ud45c\ubcf8 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),VARA:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790, \ub17c\ub9ac\uac12 \ub610\ub294 \ud14d\uc2a4\ud2b8 \uac12\uc744 \ud3ec\ud568\ud558\ub294 \ubaa8\uc9d1\ub2e8\uc758 \ud45c\ubcf8 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),VARP:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uac12\ub9cc \uc0ac\uc6a9\ud558\ub294 \uc804\uccb4 \ubaa8\uc9d1\ub2e8\uc758 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),VARPA:d("\uc774 \ud568\uc218\ub294 \uc22b\uc790, \ub17c\ub9ac\uac12 \ub610\ub294 \ud14d\uc2a4\ud2b8 \uac12\uc744 \ud3ec\ud568\ud558\ub294 \uc804\uccb4 \ubaa8\uc9d1\ub2e8\uc758 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),VDB:d("\uc774 \ud568\uc218\ub294 \uac00\ubcc0 \uc815\uc728\ubc95\uc744 \uc0ac\uc6a9\ud558\uc5ec \uc9c0\uc815\ud55c \uae30\uac04 \ub3d9\uc548 \uc790\uc0b0\uc758 \uac10\uac00 \uc0c1\uac01\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cost"),e("salvage"),e("life"),e("start"),e("end"),e("factor"),e("switchnot")]),VLOOKUP:d("\uc774 \ud568\uc218\ub294 \uac00\uc7a5 \uc67c\ucabd \uc5f4\uc5d0\uc11c \uac12\uc744 \uac80\uc0c9\ud55c \ub2e4\uc74c \uc9c0\uc815\ub41c \uc5f4\uc758 \ub3d9\uc77c\ud55c \ud589\uc5d0 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("array"),e("colindex"),e("approx")]),WEEKDAY:d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ub41c \ub0a0\uc9dc\uc758 \uc694\uc77c\uc5d0 \ud574\ub2f9\ud558\ub294 \uc22b\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date"),e("type")]),WEEKNUM:d("\uc774 \ud568\uc218\ub294 \uc5f0\ub3c4\uc758 \uc8fc\uac04\uc744 \ub098\ud0c0\ub0b4\ub294 \uc22b\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date"),e("weektype")]),WEIBULL:d("\uc774 \ud568\uc218\ub294 \uc548\uc815\uc131 \ubd84\uc11d\uc5d0 \uc790\uc8fc \uc0ac\uc6a9\ub418\ub294 2\uac1c\uc758 \ub9e4\uac1c \ubcc0\uc218 WEIBULL \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),WORKDAY:d("\uc774 \ud568\uc218\ub294 \uc2dc\uc791 \ub0a0\uc9dc \uc774\uc804 \ub610\ub294 \uc774\ud6c4\uc758 \uc791\uc5c5\uc77c \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("startdate"),e("numdays"),e("holidays")]),XIRR:d("\uc774 \ud568\uc218\ub294 \ube44\uc815\uae30\uc801\uc77c \uc218\ub3c4 \uc788\ub294 \ud604\uae08 \ud750\ub984\uc758 \ub0b4\ubd80 \ud68c\uc218\uc728\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("values"),e("dates"),e("guess")]),XNPV:d("\uc774 \ud568\uc218\ub294 \ube44\uc815\uae30\uc801\uc77c \uc218\ub3c4 \uc788\ub294 \ud604\uae08 \ud750\ub984\uc758 \uc21c \ud604\uc7ac \uac00\uce58\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("rate"),e("values"),e("dates")]),YEAR:d("\uc774 \ud568\uc218\ub294 \uc5f0\ub3c4\ub97c \uc9c0\uc815\ub41c \ub0a0\uc9dc\uc5d0 \ub300\ud55c \uc815\uc218\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date")]),YEARFRAC:d("\uc774 \ud568\uc218\ub294 \uc2dc\uc791 \ub0a0\uc9dc\uc640 \uc885\ub8cc \ub0a0\uc9dc \uc0ac\uc774\uc758 \uc804\uccb4 \ub0a0\uc9dc \uc218\ub85c \ud45c\uc2dc\ub41c \uc5f0\ub3c4 \ubd80\ubd84\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("startdate"),e("enddate"),e("basis")]),YIELD:d("\uc774 \ud568\uc218\ub294 \uc815\uae30\uc801\uc73c\ub85c \uc774\uc790\ub97c \uc9c0\uae09\ud558\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \uc218\uc775\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("rate"),e("price"),e("redeem"),e("frequency"),e("basis")]),YIELDDISC:d("\uc774 \ud568\uc218\ub294 \ud560\uc778\ub41c \uc720\uac00 \uc99d\uad8c\uc758 \uc5f0\uac04 \uc218\uc775\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("price"),e("redeem"),e("basis")]),YIELDMAT:d("\uc774 \ud568\uc218\ub294 \ub9cc\uae30 \uc2dc \uc774\uc790\ub97c \uc9c0\uae09\ud558\ub294 \uc720\uac00 \uc99d\uad8c\uc758 \uc5f0\uac04 \uc218\uc775\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("settle"),e("maturity"),e("issue"),e("issrate"),e("price"),e("basis")]),
ZTEST:d("\uc774 \ud568\uc218\ub294 z-\uac80\uc99d\uc758 significance \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. z-\uac80\uc99d\uc5d0\uc11c\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0 \ub300\ud55c x\uc758 \ud45c\uc900 \uc131\uacfc\ub97c \uc0dd\uc131\ud558\uace0 \uc815\uaddc \ubd84\ud3ec\uc5d0 \ub300\ud55c \uc591\uce21 \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("x"),e("sigma")]),HBARSPARKLINE:d("\uc774 \ud568\uc218\ub294 Hbar \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("colorScheme")]),VBARSPARKLINE:d("\uc774 \ud568\uc218\ub294 Vbar \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("colorScheme")]),VARISPARKLINE:d("\uc774 \ud568\uc218\ub294 \ubd84\uc0b0 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("variance"),e("reference"),e("mini"),e("maxi"),e("mark"),e("tickunit"),e("legend"),e("colorPositive"),e("colorNegative"),e("vertical")]),PIESPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc6d0\ud615 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("range|percentage"),e("color",!0)]),AREASPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc601\uc5ed \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("points"),e("mini"),e("maxi"),e("line1"),e("line2"),e("colorPositive"),e("colorNegative")]),SCATTERSPARKLINE:d("\uc774 \ud568\uc218\ub294 \ubd84\uc0b0\ud615 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("points1"),e("points2"),e("minX"),e("maxX"),e("minY"),e("maxY"),e("hLine"),e("vLine"),e("xMinZone"),e("xMaxZone"),e("yMinZone"),e("yMaxZone"),e("tags"),e("drawSymbol"),e("drawLines"),e("color1"),e("color2"),e("dash")]),LINESPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc120 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("data"),e("dataOrientation"),e("dateAxisData"),e("dateAxisOrientation"),e("setting")]),COLUMNSPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc5f4 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("data"),e("dataOrientation"),e("dateAxisData"),e("dateAxisOrientation"),e("setting")]),WINLOSSSPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc2b9\ud328 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("data"),e("dataOrientation"),e("dateAxisData"),e("dateAxisOrientation"),e("setting")]),BULLETSPARKLINE:d("\uc774 \ud568\uc218\ub294 \uae00\uba38\ub9ac \uae30\ud638 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("measure"),e("target"),e("maxi"),e("good"),e("bad"),e("forecast"),e("tickunit"),e("colorScheme"),e("vertical")]),SPREADSPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc2a4\ud504\ub808\ub4dc \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("points"),e("showAverage"),e("scaleStart"),e("scaleEnd"),e("style"),e("colorScheme"),e("vertical")]),STACKEDSPARKLINE:d("\uc774 \ud568\uc218\ub294 \ub204\uc801 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("points"),e("colorRange"),e("labelRange"),e("maximum"),e("targetRed"),e("targetGreen"),e("targetBlue"),e("tragetYellow"),e("color"),e("highlightPosition"),e("vertical"),e("textOrientation"),e("textSize")]),BOXPLOTSPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc0c1\uc790 \uadf8\ub9bc \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("points"),e("boxPlotClass"),e("showAverage"),e("scaleStart"),e("scaleEnd"),e("acceptableStart"),e("acceptableEnd"),e("colorScheme"),e("style"),e("vertical")]),CASCADESPARKLINE:d("\uc774 \ud568\uc218\ub294 \uacc4\ub2e8\uc2dd \ubc30\uc5f4 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("pointsRange"),e("pointIndex"),e("labelsRange"),e("minimum"),e("maximum"),e("colorPositive"),e("colorNegative"),e("vertical")]),PARETOSPARKLINE:d("\uc774 \ud568\uc218\ub294 \ud30c\ub808\ud1a0 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("points"),e("pointIndex"),e("colorRange"),e("target"),e("target2"),e("highlightPosition"),e("label"),e("vertical")]),MONTHSPARKLINE:d("\uc774 \ud568\uc218\ub294 \uc6d4 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("year"),e("month"),e("dataRange"),e("emptyColor"),e("startColor"),e("middleColor"),e("endColor")]),YEARSPARKLINE:d("\uc774 \ud568\uc218\ub294 \ub144 \uc2a4\ud30c\ud06c\ub77c\uc778\uc744 \ud45c\uc2dc\ud558\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("year"),e("dataRange"),e("emptyColor"),e("startColor"),e("middleColor"),e("endColor")]),"CEILING.PRECISE":d("\uc774 \ud568\uc218\ub294 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc815\uc218 \ub610\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \uac00\uc7a5 \uac00\uae4c\uc6b4 \ubc30\uc218\uac00 \ub418\ub3c4\ub85d \uac12\uc744 \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("number"),e("signif")]),"COVARIANCE.S":d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \uac01 \ub370\uc774\ud130 \uc694\uc18c \uc30d\uc5d0 \ub300\ud55c \ud3b8\ucc28\ub97c \uacf1\ud55c \uac12\uc758 \ud3c9\uade0\uc744 \ub098\ud0c0\ub0b4\ub294 \ud45c\ubcf8 \uacf5 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),"FLOOR.PRECISE":d("\uc774 \ud568\uc218\ub294 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc815\uc218 \ub610\ub294 \uc9c0\uc815\ub41c \uac12\uc758 \uac00\uc7a5 \uac00\uae4c\uc6b4 \ubc30\uc218\uac00 \ub418\ub3c4\ub85d \uac12\uc744 \ub0b4\ub9bc\ud569\ub2c8\ub2e4.",[e("number"),e("signif")]),"PERCENTILE.EXC":d("\uc774 \ud568\uc218\ub294 \ubc94\uc704\uc5d0\uc11c n\ubc88\uc9f8 \ubc31\ubd84\uc704\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("n")]),"QUARTILE.EXC":d("\uc774 \ud568\uc218\ub294 \uac12\uc774 \uc18d\ud558\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \ubc31\ubd84\uc704\uc218(1/4 \ub610\ub294 25%)\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("quart")]),"RANK.AVG":d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uc9d1\ud569 \ub0b4\uc5d0\uc11c \uc9c0\uc815\ud55c \uc218\uc758 \uc21c\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc77c\ubd80 \uac12\uc758 \uc21c\uc704\uac00 \ub3d9\uc77c\ud55c \uacbd\uc6b0 \ud3c9\uade0 \uc21c\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("array"),e("order")]),"MODE.MULT":d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c \uac00\uc7a5 \uc790\uc8fc \ubc1c\uc0dd\ud558\ub294 \uac12 \ub610\ub294 \uac00\uc7a5 \uc790\uc8fc \ubc1c\uc0dd\ud558\ub294 \uc138\ub85c \ubc30\uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2",!0)]),"STDEV.P":d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uac12\uc758 \uc804\uccb4 \uc9c0\uc815\ub41c \ubaa8\uc9d1\ub2e8\uc5d0 \ub300\ud55c \ud45c\uc900 \ud3b8\ucc28\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),"VAR.P":d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uac12\ub9cc \uc0ac\uc6a9\ud558\ub294 \uc804\uccb4 \ubaa8\uc9d1\ub2e8\uc758 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),"COVARIANCE.P":d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \uac01 \ub370\uc774\ud130 \uc694\uc18c \uc30d\uc5d0 \ub300\ud55c \ud3b8\ucc28\ub97c \uacf1\ud55c \uac12\uc758 \ud3c9\uade0\uc744 \ub098\ud0c0\ub0b4\ub294 \uacf5 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),"MODE.SNGL":d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c \uac00\uc7a5 \uc790\uc8fc \ubc1c\uc0dd\ud558\ub294 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),"PERCENTILE.INC":d("\uc774 \ud568\uc218\ub294 \ubc94\uc704\uc5d0\uc11c n\ubc88\uc9f8 \ubc31\ubd84\uc704\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("n")]),"QUARTILE.INC":d("\uc774 \ud568\uc218\ub294 \uac12\uc774 \uc18d\ud558\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc758 \ubc31\ubd84\uc704\uc218(1/4 \ub610\ub294 25%)\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("quart")]),"RANK.EQ":d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uc9d1\ud569 \ub0b4\uc5d0\uc11c \uc9c0\uc815\ud55c \uc218\uc758 \uc21c\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc9d1\ud569\uc744 \uc815\ub82c\ud560 \uacbd\uc6b0 \uc218\uc758 \uc21c\uc704\ub294 \ubaa9\ub85d \ub0b4\uc5d0\uc11c \ud574\ub2f9 \uc218\uc758 \uc704\uce58\uc785\ub2c8\ub2e4.",[e("number"),e("array"),e("order")]),STDEV:d("\uc774 \ud568\uc218\ub294 \ud45c\ubcf8\uc5d0 \ub530\ub77c \uc608\uce21\ub418\ub294 \ud45c\uc900 \ud3b8\ucc28\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2",!0)]),"STDEV.S":d("\uc774 \ud568\uc218\ub294 \ud45c\ubcf8\uc5d0 \ub530\ub77c \uc608\uce21\ub418\ub294 \ud45c\uc900 \ud3b8\ucc28\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2",!0)]),"VAR.S":d("\uc774 \ud568\uc218\ub294 \uc22b\uc790 \uac12\ub9cc \uc0ac\uc6a9\ud558\ub294 \ubaa8\uc9d1\ub2e8\uc758 \ud45c\ubcf8 \ubd84\uc0b0\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value1"),e("value2",!0)]),"BETA.INV":d("\uc774 \ud568\uc218\ub294 \ub204\uc801 \ubca0\ud0c0 \ubd84\ud3ec \ud568\uc218\uc758 \uc5ed\ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("prob"),e("alpha"),e("beta"),e("lower"),e("upper")]),"BINOM.DIST":d("\uc774 \ud568\uc218\ub294 \uac1c\ubcc4\ud56d \uc774\ud56d \ubd84\ud3ec \ud655\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("x"),e("n"),e("p"),e("cumulative")]),"BINOM.INV":d("\uc774 \ud568\uc218\ub294 \ub204\uc801 \uc774\ud56d \ubd84\ud3ec\uac00 \uae30\uc900\uce58 \uc774\uc0c1\uc774 \ub418\ub294 \uac12 \uc911 \ucd5c\uc18c\uac12\uc744 \ub098\ud0c0\ub0b4\ub294 \uc870\uac74 \uc774\ud56d\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("n"),e("p"),e("alpha")]),"CHISQ.DIST.RT":d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc758 \ub2e8\uce21 \ud655\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("deg")]),"CHISQ.INV.RT":d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc758 \ub2e8\uce21 \ud655\ub960\uc758 \uc5ed\ud568\uc218\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("prob"),e("deg")]),"CHISQ.TEST":d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc5d0\uc11c \ub3c5\ub9bd \uac80\uc99d \uacb0\uacfc\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("obs_array"),e("exp_array")]),"CONFIDENCE.NORM":d("\uc774 \ud568\uc218\ub294 \ubaa8\uc9d1\ub2e8 \ud3c9\uade0\uc758 \uc2e0\ub8b0 \uad6c\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("alpha"),e("stdev"),e("size")]),"EXPON.DIST":d("\uc774 \ud568\uc218\ub294 \uc9c0\uc218 \ubd84\ud3ec \ub610\ub294 \ud655\ub960 \ubc00\ub3c4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("lambda"),e("cumulative")]),"F.DIST.RT":d("\uc774 \ud568\uc218\ub294 \ub450 \ub370\uc774\ud130 \uc9d1\ud569 \uc0ac\uc774\uc758 \ubd84\ud3ec\ub3c4\ub97c \ub098\ud0c0\ub0b4\ub294 F \ud655\ub960 \ubd84\ud3ec\ub97c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("value"),e("degnum"),e("degden")]),"F.INV.RT":d("\uc774 \ud568\uc218\ub294 F \ud655\ub960 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("p"),e("degnum"),e("degden")]),"F.TEST":d("\uc774 \ud568\uc218\ub294 \ub450 \ubc30\uc5f4\uc758 \ubd84\uc0b0\uc774 \ud06c\uac8c \ucc28\uc774\uac00 \ub098\uc9c0 \uc54a\ub294 \uacbd\uc6b0 \ub2e8\uce21 \ud655\ub960\uc778 F-\uac80\uc815 \uacb0\uacfc\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2")]),"GAMMA.DIST":d("\uc774 \ud568\uc218\ub294 \uac10\ub9c8 \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),"GAMMA.INV":d("\uc774 \ud568\uc218\ub294 \uac10\ub9c8 \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("p"),e("alpha"),e("beta")]),"LOGNORM.INV":d("\uc774 \ud568\uc218\ub294 x\uc5d0 \ub300\ud55c \ub85c\uadf8 \uc815\uaddc \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. \uc5ec\uae30\uc11c LN(x)\uc740 \uc9c0\uc815\ub41c \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\ub97c \uac16\ub294 \uc815\uaddc \ubd84\ud3ec\uc785\ub2c8\ub2e4.",[e("prob"),e("mean"),e("stdev")]),"NORM.DIST":d("\uc774 \ud568\uc218\ub294 \uc9c0\uc815\ud55c \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\uc5d0 \uc758\uac70\ud558\uc5ec \uc815\uaddc \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("mean"),e("stdev"),e("cumulative")]),"NORM.INV":d("\uc774 \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \ud3c9\uade0\uacfc \ud45c\uc900 \ud3b8\ucc28\uc5d0 \uc758\uac70\ud558\uc5ec \uc815\uaddc \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("prob"),e("mean"),e("stdev")]),"NORM.S.INV":d("\uc774 \ud568\uc218\ub294 \ud45c\uc900 \uc815\uaddc \ub204\uc801 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \ubd84\ud3ec\uc758 \ud3c9\uade0\uc774 0\uc774\uace0 \ud45c\uc900 \ud3b8\ucc28\uac00 1\uc785\ub2c8\ub2e4.",[e("prob")]),"PERCENTRANK.INC":d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0\uc11c \ubc31\ubd84\uc728 \uc21c\uc704\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("n"),e("signif")]),"POISSON.DIST":d("\uc774 \ud568\uc218\ub294POISSON \ud655\ub960 \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("nevents"),e("mean"),e("cumulative")]),"T.INV.2T":d("\uc774 \ud568\uc218\ub294 \ud559\uc0dd\uc758 t-\ubd84\ud3ec\uc5d0 \ub300\ud55c t \uac12\uc744 \ud655\ub960 \ud568\uc218\uc640 \uc790\uc720\ub3c4\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("prog"),e("deg")]),"T.TEST":d("\uc774 \ud568\uc218\ub294 t-\uac80\uc99d\uacfc \uad00\ub828\ub41c \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array1"),e("array2"),e("tails"),e("type")]),"WEIBULL.DIST":d("\uc774 \ud568\uc218\ub294 \uc548\uc815\uc131 \ubd84\uc11d\uc5d0 \uc790\uc8fc \uc0ac\uc6a9\ub418\ub294 2 \ub9e4\uac1c \ubcc0\uc218 WEIBULL \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),"Z.TEST":d("\uc774 \ud568\uc218\ub294 z-\uac80\uc99d\uc758 significance \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. z-\uac80\uc99d\uc5d0\uc11c\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0 \ub300\ud55c x\uc758 \ud45c\uc900 \uc131\uacfc\ub97c \uc0dd\uc131\ud558\uace0 \uc815\uaddc \ubd84\ud3ec\uc5d0 \ub300\ud55c \uc591\uce21 \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("x"),e("sigma")]),"T.DIST.RT":d("\uc774 \ud568\uc218\ub294 \uc6b0\uce21 \uac80\uc99d t-\ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("deg")]),"T.DIST.2T":d("\uc774 \ud568\uc218\ub294 \uc591\uce21 t-\ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("deg")]),"ISO.CEILING":d("\uc774 \ud568\uc218\ub294 significance\uc758 \ubd80\ud638\uc5d0 \uc0c1\uad00\uc5c6\uc774 \uac00\uc7a5 \uac00\uae4c\uc6b4 \uc815\uc218 \ub610\ub294 significance\uc758 \uac00\uc7a5 \uac00\uae4c\uc6b4 \ubc30\uc218\ub85c \uac12\uc744 \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("number"),e("signif")]),"BETA.DIST":d("\uc774 \ud568\uc218\ub294 \ubca0\ud0c0 \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("alpha"),e("beta"),e("cumulative"),e("lower"),e("upper")]),"GAMMALN.PRECISE":d("\uc774 \ud568\uc218\ub294 \uac10\ub9c8 \ud568\uc218\uc758 \uc790\uc5f0 \ub85c\uadf8\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),"ERF.PRECISE":d("\uc774 \ud568\uc218\ub294 \uc624\ucc28 \ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("lowerlimit")]),"ERFC.PRECISE":d("\uc774 \ud568\uc218\ub294 ERF \ud568\uc218\uc758 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("lowerlimit")]),"PERCENTRANK.EXC":d("\uc774 \ud568\uc218\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc5d0 \uc788\ub294 \uac12\uc758 \ubc31\ubd84\uc728 \uc21c\uc704(0..1, \ub2e8\ub3c5)\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("n"),e("signif")]),"HYPGEOM.DIST":d("\uc774 \ud568\uc218\ub294hypergeometric \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("n"),e("M"),e("N"),e("cumulative")]),"LOGNORM.DIST":d("\uc774 \ud568\uc218\ub294 x\uc758 \ub85c\uadf8 \uc815\uaddc \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("mean"),e("stdev"),e("cumulative")]),"NEGBINOM.DIST":d("\uc774 \ud568\uc218\ub294 \uc74c \uc774\ud56d \ubd84\ud3ec\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("r"),e("p"),e("cumulative")]),"NORM.S.DIST":d("\uc774 \ud568\uc218\ub294 \ud45c\uc900 \uc815\uaddc \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("z"),e("cumulative")]),"T.DIST":d("\uc774 \ud568\uc218\ub294 t-\ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("deg"),e("cumulative")]),"F.DIST":d("\uc774 \ud568\uc218\ub294 F \ud655\ub960 \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("degnum"),e("degden"),e("cumulative")]),"CHISQ.DIST":d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("deg"),e("cumulative")]),"F.INV":d("\uc774 \ud568\uc218\ub294 F \ud655\ub960 \ubd84\ud3ec\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("probability"),e("degnum"),e("degden")]),"T.INV":d("\uc774 \ud568\uc218\ub294 t-\ubd84\ud3ec\uc758 \uc88c\uce21 \uc5ed\ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("probability"),e("deg")]),"CHISQ.INV":d("\uc774 \ud568\uc218\ub294 \uce74\uc774 \uc81c\uacf1 \ubd84\ud3ec\uc758 \uc88c\uce21 \ud655\ub960\uc758 \uc5ed\ud568\uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("probability"),e("deg")]),"CONFIDENCE.T":d("\uc774 \ud568\uc218\ub294 \ud559\uc0dd \ubd84\ud3ec\uc5d0 \ub300\ud55c \uc2e0\ub8b0 \uad6c\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("alpha"),e("stdev"),e("size")]),"NETWORKDAYS.INTL":d("\uc774 \ud568\uc218\ub294 \uc778\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \uacf5\ud734\uc77c\uacfc \uc8fc\ub9d0\uc744 \ub098\ud0c0\ub0b4\uc5b4 \ub450 \ub0a0\uc9dc \uc0ac\uc774\uc758 \uc791\uc5c5\uc77c \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("startdate"),e("enddate"),e("weekend"),e("holidays")]),"WORKDAY.INTL":d("\uc774 \ud568\uc218\ub294 \uc0ac\uc6a9\uc790 \uc9c0\uc815 weekend \ub9e4\uac1c \ubcc0\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \ud2b9\uc815 \uc77c\uc758 \uc804\uc774\ub098 \ud6c4\uc5d0 \ud574\ub2f9\ud558\ub294 \ub0a0\uc9dc\uc758 \uc77c\ub828 \ubc88\ud638\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc774\ub7ec\ud55c \ub9e4\uac1c \ubcc0\uc218\ub294 \uc8fc\ub9d0\uacfc \uacf5\ud734\uc77c\uc744 \ub098\ud0c0\ub0c5\ub2c8\ub2e4.",[e("startdate"),e("numdays"),e("weekend"),e("holidays")]),REFRESH:d("\uc774 \ud568\uc218\ub294 \uc218\uc2dd\uc744 \ub2e4\uc2dc \uacc4\uc0b0\ud558\ub294 \ubc29\ubc95\uc744 \uacb0\uc815\ud569\ub2c8\ub2e4. evaluateMode \uc778\uc218\ub97c \uc0ac\uc6a9\ud558\uc5ec \ubcc0\uacbd\ub41c \ucc38\uc870 \uac12\uc5d0 \ub300\ud55c \uc218\uc2dd \uc7ac\uacc4\uc0b0\uc744 \ud55c \ubc88\ub9cc \uc218\ud589\ud558\uac70\ub098 \ud2b9\uc815 \uac04\uaca9\uc73c\ub85c \ub2e4\uc2dc \uacc4\uc0b0\ud558\ub3c4\ub85d \uc9c0\uc815\ud560 \uc218 \uc788\uc2b5\ub2c8\ub2e4.",[e("formula"),e("evaluateMode"),e("interval")]),DAYS:d("\ub450 \ub0a0\uc9dc \uc0ac\uc774\uc758 \uc77c \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("startdate"),e("enddate")]),ISOWEEKNUM:d("\uc9c0\uc815\ub41c \ub0a0\uc9dc\uc758 \uc5f0\ub3c4\uc5d0 \ud574\ub2f9\ud558\ub294 ISO \uc8fc \ubc88\ud638\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("date")]),BITAND:d('\ub450 \uc22b\uc790\uc758 \ube44\ud2b8 \ub2e8\uc704 "AND"\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.',[e("number1"),e("number2")]),BITLSHIFT:d("\uc9c0\uc815\ub41c \ube44\ud2b8\ub9cc\ud07c \uc67c\ucabd\uc73c\ub85c \uc774\ub3d9\ud55c \uc22b\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2")]),BITOR:d('\ub450 \uc22b\uc790\uc758 \ube44\ud2b8 \ub2e8\uc704 "OR"\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.',[e("number1"),e("number2")]),BITRSHIFT:d("\uc9c0\uc815\ub41c \ube44\ud2b8\ub9cc\ud07c \uc624\ub978\ucabd\uc73c\ub85c \uc774\ub3d9\ud55c \uc22b\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number1"),e("number2")]),BITXOR:d('\ub450 \uc22b\uc790\uc758 \ube44\ud2b8 \ub2e8\uc704 "XOR"\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.',[e("number1"),e("number2")]),IMCOSH:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ud558\uc774\ud37c\ubcfc\ub9ad \ucf54\uc0ac\uc778 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMCOT:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ucf54\ud0c4\uc820\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMCSC:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ucf54\uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMCSCH:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ud558\uc774\ud37c\ubcfc\ub9ad \ucf54\uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMSEC:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMSECH:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ud558\uc774\ud37c\ubcfc\ub9ad \uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMSINH:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ud558\uc774\ud37c\ubcfc\ub9ad \uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),IMTAN:d("x + yi \ub610\ub294 x + yj \ud14d\uc2a4\ud2b8 \ud615\uc2dd\uc778 \ubcf5\uc18c\uc218\uc758 \ud0c4\uc820\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("complexnum")]),PDURATION:d("\ud22c\uc790 \uae08\uc561\uc774 \uc9c0\uc815\ub41c \uac12\uc5d0 \ub3c4\ub2ec\ud560 \ub54c\uae4c\uc9c0 \ud544\uc694\ud55c \uae30\uac04\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("rate"),e("pval"),e("fval")]),RRI:d("\ud22c\uc790 \uc218\uc775\uc5d0 \ud574\ub2f9\ud558\ub294 \uc774\uc790\uc728\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("nper"),e("pval"),e("fval")]),ISFORMULA:d("\uc218\uc2dd\uc744 \ud3ec\ud568\ud558\ub294 \uc140\uc5d0 \ub300\ud55c \ucc38\uc870\uac00 \uc788\ub294\uc9c0 \uc5ec\ubd80\ub97c \ud655\uc778\ud558\uace0 TRUE \ub610\ub294 FALSE\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("cellreference")]),IFNA:d("\uc218\uc2dd\uc774 #N/A \uc624\ub958 \uac12\uc744 \ubc18\ud658\ud558\ub294 \uacbd\uc6b0 \uc9c0\uc815\ud55c \uac12\uc744 \ubc18\ud658\ud558\uace0, \uadf8\ub807\uc9c0 \uc54a\uc73c\uba74 \uc218\uc2dd \uacb0\uacfc\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("value_if_na")]),IFS:d("IFS \ud568\uc218\ub294 \ud558\ub098 \uc774\uc0c1\uc758 \uc870\uac74\uc774 \ucda9\uc871\ub418\ub294\uc9c0\ub97c \ud655\uc778\ud558\uace0 \uccab \ubc88\uc9f8 TRUE \uc870\uac74\uc5d0 \ud574\ub2f9\ud558\ub294 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("valueTest"),e("valueTrue"),e("....")]),SWITCH:d("SWITCH \ud568\uc218\ub294 \uac12\uc758 \ubaa9\ub85d\uc5d0 \ub300\ud55c \ud558\ub098\uc758 \uac12(\uc2dd\uc774\ub77c\uace0 \ud568)\uc744 \uacc4\uc0b0\ud558\uace0 \uccab \ubc88\uc9f8 \uc77c\uce58\ud558\ub294 \uac12\uc5d0 \ud574\ub2f9\ud558\ub294 \uacb0\uacfc\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. ",[e("convertvalue"),e("matchvalue"),e("matchtrue"),e("matchfalse")]),XOR:d("\ubaa8\ub4e0 \uc778\uc218\uc758 \ub17c\ub9ac \ubc30\ud0c0\uc801 OR\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("logical"),e("....")]),AREAS:d("\ucc38\uc870 \uc601\uc5ed \ub0b4\uc758 \uc601\uc5ed \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc601\uc5ed\uc740 \uc778\uc811\ud55c \uc140\uc758 \ubc94\uc704 \ub610\ub294 \ub2e8\uc77c \uc140\uc785\ub2c8\ub2e4.",[e("reference")]),FORMULATEXT:d("\uc218\uc2dd\uc744 \ubb38\uc790\uc5f4\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("reference")]),HYPERLINK:d("\ub124\ud2b8\uc6cc\ud06c \uc11c\ubc84, \uc778\ud2b8\ub77c\ub137 \ub610\ub294 \uc778\ud130\ub137\uc5d0 \uc800\uc7a5\ub41c \ubb38\uc11c\ub85c \uc774\ub3d9\ud560 \ubc14\ub85c \uac00\uae30 \ud0a4\ub97c \ub9cc\ub4ed\ub2c8\ub2e4. ",[e("link_location"),e("friendly_name")]),ACOT:d("\uc544\ud06c\ucf54\ud0c4\uc820\ud2b8, \uc989 \uc5ed \ucf54\ud0c4\uc820\ud2b8\uc758 \uc8fc\uce58\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),ACOTH:d("\uc5ed \ud558\uc774\ud37c\ubcfc\ub9ad \ucf54\ud0c4\uc820\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),ARABIC:d("\ub85c\ub9c8 \uc22b\uc790\ub97c \uc544\ub77c\ube44\uc544 \uc22b\uc790\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("text")]),BASE:d("\uc22b\uc790\ub97c \uc9c0\uc815\ub41c \uae30\uc218\uc758 \ud14d\uc2a4\ud2b8 \ud45c\ud604\uc73c\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("radix"),e("minLength")]),"CEILING.MATH":d("\uac00\uc7a5 \uac00\uae4c\uc6b4 \uc815\uc218 \ub610\ub294 \uac00\uc7a5 \uac00\uae4c\uc6b4 significance\uc758 \ubc30\uc218\ub85c \uc22b\uc790\ub97c \uc62c\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("signif"),e("mode")]),COMBINA:d("\uc8fc\uc5b4\uc9c4 \uac1c\uccb4 \uc218\ub85c \ub9cc\ub4e4 \uc218 \uc788\ub294 \uc870\ud569(\ubc18\ubcf5 \ud3ec\ud568)\uc758 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number"),e("number_choosen")]),COT:d("\uac01\ub3c4\uc758 \ucf54\ud0c4\uc820\ud2b8\ub97c \ub77c\ub514\uc548 \ub2e8\uc704\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),COTH:d("\ud558\uc774\ud37c\ubcfc\ub9ad \uac01\ub3c4\uc758 \ud558\uc774\ud37c\ubcfc\ub9ad \ucf54\ud0c4\uc820\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),CSC:d("\uac01\ub3c4\uc758 \ucf54\uc2dc\ucee8\ud2b8\ub97c \ub77c\ub514\uc548 \ub2e8\uc704\ub85c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),CSCH:d("\uac01\ub3c4\uac00 \ub77c\ub514\uc548\uc73c\ub85c \uc9c0\uc815\ub418\ub294 \ud558\uc774\ud37c\ubcfc\ub9ad \ucf54\uc2dc\ucee8\ud2b8\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),DECIMAL:d("\ud2b9\uc815 \uae30\uc218\uc758 \ud14d\uc2a4\ud2b8 \ud45c\ud604\uc744 10\uc9c4\uc218\ub85c \ubcc0\ud658\ud569\ub2c8\ub2e4.",[e("text"),e("radix")]),"FLOOR.MATH":d("\uac00\uc7a5 \uac00\uae4c\uc6b4 \uc815\uc218 \ub610\ub294 \uac00\uc7a5 \uac00\uae4c\uc6b4 significance\uc758 \ubc30\uc218\ub85c \uc22b\uc790\ub97c \ub0b4\ub9bc\ud569\ub2c8\ub2e4.",[e("value"),e("signif"),e("mode")]),SEC:d("\uac01\ub3c4\uc758 \uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("angle")]),SECH:d("\uac01\ub3c4\uc758 \ud558\uc774\ud37c\ubcfc\ub9ad \uc2dc\ucee8\ud2b8 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),"BINOM.DIST.RANGE":d("\uc774\ud56d \ubd84\ud3ec\ub97c \uc0ac\uc6a9\ud55c \uc2dc\ud589 \uacb0\uacfc\uc758 \ud655\ub960\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("x"),e("n"),e("p"),e("cumulative")]),GAMMA:d("\uac10\ub9c8 \ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number")]),MAXIFS:d("MAXIFS \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \uc870\uac74\uc5d0 \ub9de\ub294 \uc140\uc5d0\uc11c \ucd5c\ub300\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("conditionarray"),e("condition",!0)]),GAUSS:d("\ud45c\uc900 \uc815\uaddc \ubaa8\uc9d1\ub2e8 \uad6c\uc131\uc6d0\uc774 \ud3c9\uade0 \ubc0f \ud3c9\uade0\uc758 z \ud45c\uc900 \ud3b8\ucc28 \uc0ac\uc774\uc5d0 \uc788\uc744 \ud655\ub960\uc744 \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("number")]),MINIFS:d("MINIFS \ud568\uc218\ub294 \uc8fc\uc5b4\uc9c4 \uc870\uac74 \uc9d1\ud569\uc5d0 \ub9de\ub294 \uc140\uc5d0\uc11c \ucd5c\uc18c\uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("array"),e("conditionarray"),e("condition",!0)]),PERMUTATIONA:d("\uc804\uccb4 \uac1c\uccb4\uc5d0\uc11c \uc120\ud0dd\ud558\uc5ec \uc8fc\uc5b4\uc9c4 \uac1c\uccb4 \uc218(\ubc18\ubcf5 \ud3ec\ud568)\ub85c \ub9cc\ub4e4 \uc218 \uc788\ub294 \uc21c\uc5f4\uc758 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("k"),e("n")]),PHI:d("\ud45c\uc900 \uc815\uaddc \ubd84\ud3ec\uc758 \ubc00\ub3c4 \ud568\uc218 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),"SKEW.P":d("\ubaa8\uc9d1\ub2e8\uc744 \uae30\uc900\uc73c\ub85c \ubd84\ud3ec\uc758 \uc65c\uace1\ub3c4\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4. \uc65c\uace1\ub3c4\ub780 \ud3c9\uade0\uc5d0 \ub300\ud55c \ubd84\ud3ec\uc758 \ube44\ub300\uce6d \uc815\ub3c4\ub97c \ub098\ud0c0\ub0c5\ub2c8\ub2e4.",[e("number1"),e("number2",!0)]),BAHTTEXT:d('\uc22b\uc790\ub97c \ud0dc\uad6d\uc5b4 \ud14d\uc2a4\ud2b8\ub85c \ubcc0\ud658\ud558\uace0 "Baht" \uc811\ubbf8\uc0ac\ub97c \ucd94\uac00\ud569\ub2c8\ub2e4.',[e("number")]),CONCAT:d("CONCAT \ud568\uc218\ub294 \uc5ec\ub7ec \ubc94\uc704 \ubc0f/\ub610\ub294 \ubb38\uc790\uc5f4\uc758 \ud14d\uc2a4\ud2b8\ub97c \uacb0\ud569\ud558\uc9c0\ub9cc \uad6c\ubd84 \uae30\ud638\ub098 IgnoreEmpty \uc778\uc218\ub294 \uc81c\uacf5\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.",[e("text1"),e("text2"),e("....")]),FINDB:d("FINDB\ub294 DBCS\ub97c \uc9c0\uc6d0\ud558\ub294 \uc5b8\uc5b4\ub97c \ud3b8\uc9d1\ud560 \uc218 \uc788\ub3c4\ub85d \uc124\uc815\ud558\uace0 \uc774 \uc5b8\uc5b4\ub97c \uae30\ubcf8 \uc5b8\uc5b4\ub85c \uc124\uc815\ud55c \uacbd\uc6b0 \uac01 \ub354\ube14\ubc14\uc774\ud2b8 \ubb38\uc790\ub97c 2\ub85c \uacc4\uc0b0\ud569\ub2c8\ub2e4. \uc774\ub7ec\ud55c \uacbd\uc6b0\uac00 \uc544\ub2c8\uba74 FINDB\ub294 \uac01 \ubb38\uc790\ub97c 1\ub85c \uacc4\uc0b0\ud569\ub2c8\ub2e4.",[e("findtext"),e("intext"),e("start")]),LEFTB:d("LEFTB\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uccab \ubc88\uc9f8 \ubb38\uc790\ubd80\ud130 \uc2dc\uc791\ud558\uc5ec \uc9c0\uc815\ud55c \ubc14\uc774\ud2b8 \uc218\ub9cc\ud07c \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("mytext"),e("num_bytes")]),LENB:d("LENB\ub294 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \ubb38\uc790\ub97c \ub098\ud0c0\ub0b4\ub294 \ub370 \uc0ac\uc6a9\ub418\ub294 \ubc14\uc774\ud2b8 \uc218\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value")]),MIDB:d("MIDB\ub294 \uc9c0\uc815\ud55c \ubc14\uc774\ud2b8 \uc218\uc5d0 \ub530\ub77c \ubb38\uc790\uc5f4\uc758 \uc9c0\uc815\ud55c \uc704\uce58\ub85c\ubd80\ud130 \uc9c0\uc815\ud55c \uac1c\uc218\uc758 \ubb38\uc790\ub97c \ud45c\uc2dc\ud569\ub2c8\ub2e4.",[e("text"),e("start_num"),e("num_bytes")]),REPLACEB:d("REPLACEB\ub294 \uc9c0\uc815\ud55c \ubc14\uc774\ud2b8 \uc218\uc5d0 \ub530\ub77c \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uc77c\ubd80\ub97c \ub2e4\ub978 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\ub85c \ubc14\uafc9\ub2c8\ub2e4.",[e("old_text"),e("start_byte"),e("num_bytes"),e("new_text")]),RIGHTB:d("RIGHTB\ub294 \uc9c0\uc815\ud55c \ubc14\uc774\ud2b8 \uc218\uc5d0 \ub530\ub77c \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \ub9c8\uc9c0\ub9c9 \ubb38\uc790\ubd80\ud130 \uc9c0\uc815\ub41c \uae38\uc774\uc758 \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("text"),e("num_bytes")]),SEARCHB:d("\ud568\uc218\ub294 \ub450 \ubc88\uc9f8 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc5d0\uc11c \uc9c0\uc815\ub41c \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc744 \uac80\uc0c9\ud558\uace0, \ub450 \ubc88\uc9f8 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc758 \uccab \ubb38\uc790\ub97c \uae30\uc900\uc73c\ub85c \uba87 \ubc88\uc9f8 \uc704\uce58\uc5d0\uc11c \uccab \ubc88\uc9f8 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc774 \uc2dc\uc791\ud558\ub294\uc9c0 \ub098\ud0c0\ub0b4\ub294 \uac12\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4. ",[e("string1"),e("string2")]),
TEXTJOIN:d("TEXTJOIN \ud568\uc218\ub294 \uc5ec\ub7ec \ubc94\uc704 \ubc0f/\ub610\ub294 \ubb38\uc790\uc5f4\uc758 \ud14d\uc2a4\ud2b8\ub97c \uacb0\ud569\ud558\uba70, \uacb0\ud569\ud560 \uac01 \ud14d\uc2a4\ud2b8 \uac12 \uc0ac\uc774\uc5d0 \uc9c0\uc815\ub418\ub294 \uad6c\ubd84 \uae30\ud638\ub97c \ud3ec\ud568\ud569\ub2c8\ub2e4. \uad6c\ubd84 \uae30\ud638\uac00 \ube48 \ud14d\uc2a4\ud2b8 \ubb38\uc790\uc5f4\uc778 \uacbd\uc6b0 \uc774 \ud568\uc218\ub294 \ubc94\uc704\ub97c \ud6a8\uc728\uc801\uc73c\ub85c \uc5f0\uacb0\ud569\ub2c8\ub2e4.",[e("delimiter"),e("ignore_empty"),e("text1"),e("text2"),e("....")]),UNICHAR:d("\uc8fc\uc5b4\uc9c4 \uc22b\uc790 \uac12\uc774 \ucc38\uc870\ud558\ub294 \uc720\ub2c8\ucf54\ub4dc \ubb38\uc790\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("number")]),UNICODE:d("\ud14d\uc2a4\ud2b8\uc758 \uccab \ubb38\uc790\uc5d0 \ud574\ub2f9\ud558\ub294 \uc22b\uc790(\ucf54\ub4dc \ud3ec\uc778\ud2b8)\ub97c \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("text")]),ENCODEURL:d("URL\ub85c \uc778\ucf54\ub529\ub41c \ubb38\uc790\uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("text")]),BC_QRCODE:d("\uc774 \ud568\uc218\ub294 QRCode\ub97c \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("errorCorrectionLevel"),e("model"),e("version"),e("mask"),e("connection"),e("connectionNo"),e("charCode"),e("charset"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_EAN13:d("\uc774 \ud568\uc218\ub294 EAN13\uc744 \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("addOn"),e("addOnLabelPosition"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_EAN8:d("\uc774 \ud568\uc218\ub294 EAN8\uc744 \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODABAR:d("\uc774 \ud568\uc218\ub294 CODABAR\ub97c \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("checkDigit"),e("nwRatio"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE39:d("\uc774 \ud568\uc218\ub294 CODE39\ub97c \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("labelWithStartAndStopCharacter"),e("checkDigit"),e("nwRatio"),e("fullASCII"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE93:d("\uc774 \ud568\uc218\ub294 CODE93\uc744 \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("checkDigit"),e("fullASCII"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE128:d("\uc774 \ud568\uc218\ub294 CODE128\uc744 \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("codeSet"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_GS1_128:d("\uc774 \ud568\uc218\ub294 GS1_128\uc744 \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE49:d("\uc774 \ud568\uc218\ub294 CODE49\ub97c \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("grouping"),e("groupNo"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_PDF417:d("\uc774 \ud568\uc218\ub294 PDF417\uc744 \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("errorCorrectionLevel"),e("rows"),e("columns"),e("compact"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_DATAMATRIX:d("\uc774 \ud568\uc218\ub294 DATAMATRIX\ub97c \ub098\ud0c0\ub0b4\ub294 \ub370\uc774\ud130 \uc9d1\ud569\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4.",[e("value"),e("color"),e("backgroundColor"),e("eccMode"),e("ecc200SymbolSize"),e("ecc200EncodingMode"),e("ecc00_140SymbolSize"),e("structuredAppend"),e("structureNumber"),e("fileIdentifier"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")])},B2:{All:{name:"#All",description:"\uc5f4 \uba38\ub9ac\uae00, \ub370\uc774\ud130, \uc694\uc57d \ud589 \ub4f1 \uc9c0\uc815\ud55c \ud45c \uc5f4\uc774\ub098 \ud45c\uc758 \uc804\uccb4 \ub0b4\uc6a9\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4."},Data:{name:"#Data",description:"\ud45c\uc758 \ub370\uc774\ud130 \uc140\uc774\ub098 \uc9c0\uc815\ud55c \ud45c \uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4."},Headers:{name:"#Headers",description:"\ud45c\uc758 \uc5f4 \uba38\ub9ac\uae00\uc774\ub098 \uc9c0\uc815\ud55c \ud45c \uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4."},Totals:{name:"#Totals",description:"\ud45c\uc758 \uc694\uc57d \ud589\uc774\ub098 \uc9c0\uc815\ud55c \ud45c \uc5f4\uc744 \ubc18\ud658\ud569\ub2c8\ub2e4."},thisRow:{name:"#This Row",description:"\uc774 \ud589."}}}},"./src/resource.ko.entry.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./src/calcEngine.res.ko.ts");b.SR={ko:d.resource}}}),a.exports=c.Spread.CalcEngine},"./node_modules_local/@grapecity/js-sheets-common/dist/gc.spread.common.resources.ko.js":function(a,b){var c="object"==typeof c?c:{};c.Spread=c.Spread||{},c.Spread.Common=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/resource.ko.entry.ts")}({"./src/common/util/util.res.ko.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidDateFormat="\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \ud615\uc2dd\uc758 \ub0a0\uc9dc \ud328\ud134",b.Exp_InvalidExponentFormat="\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \ud615\uc2dd\uc758 \uc9c0\uc218 ",b.Exp_InvalidSemicolons="\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \ud615\uc2dd: \uc138\ubbf8\ucf5c\ub860\uc774 \ub108\ubb34 \ub9ce\uc74c",b.Exp_InvalidNumberGroupSize="NumberGroupSize\ub294 1\uacfc 9 \uc0ac\uc774 \uc5ec\uc57c\ud558\uba70 \ub9c8\uc9c0\ub9c9 \uc694\uc18c\ub294 0\uc774 \ub420 \uc218 \uc788\uc2b5\ub2c8\ub2e4.",b.Exp_BadFormatSpecifier="\uc798\ubabb\ub41c \ud615\uc2dd \uc9c0\uc815\uc790",b.Exp_InvalidNumberFormat="\uc798\ubabb\ub41c \uc22b\uc790 \uc11c\uc2dd \ud328\ud134",b.Exp_InvalidCast="\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \uce90\uc2a4\ud2b8 \uc608\uc678",b.Exp_Separator="\uc18c\uc218 \uc790\ub9bf\uc218 \uad6c\ubd84\uc790\uc640 \ubaa9\ub85d \uad6c\ubd84\uc790 \uadf8\ub9ac\uace0 \ubc30\uc5f4 \ubaa9\ub85d \uad6c\ubd84\uc790\ub294 cluture info\uc640 \ub2ec\ub77c\uc57c \ud569\ub2c8\ub2e4."},"./src/plugins/formatter/formatter.res.ko.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_TokenIsNull="token\uc740 null\uc785\ub2c8\ub2e4",b.Exp_InvalidBackslash="'\\'\ub294 \uc778\uc2dd\ud560\uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",b.Exp_FormatIllegal="\ud615\uc2dd\uc774 \uc798\ubabb \ub418\uc5c8\uc2b5\ub2c8\ub2e4..",b.Exp_ValueIsNull="\uac12\uc740 null \uc785\ub2c8\ub2e4",b.Exp_DuplicatedDescriptor="\uc124\uba85\uc790 \uc720\ud615\uc774 \ucd94\uac00 \ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_TokenIllegal="token\uc774 \uc798\ubabb\ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_ValueIllegal="value\uac00 \uc798\ubabb \ub418\uc5c8\uc2b5\ub2c8\ub2e4.",b.Exp_InvalidCast="\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 Cast \uc608\uc678\uc785\ub2c8\ub2e4."},"./src/resource.ko.entry.ts":function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/util.res.ko.ts"),e=c("./src/plugins/formatter/formatter.res.ko.ts"),f=d;for(g in e)e.hasOwnProperty(g)&&(f[g]=e[g]);b.SR={ko:f}}}),a.exports=c.Spread.Common},"./resource.ko.entry.js":function(b,c,d){function e(a,b,c){return f(a,b),f(a,c),a}function f(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);return a}a=a||{},a.Spread=a.Spread||{},a.Spread.Common&&(a.Spread.Common.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./node_modules_local/@grapecity/js-sheets-common/dist/gc.spread.common.resources.ko.js").SR.ko)),a.Spread.CalcEngine&&(a.Spread.CalcEngine.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./node_modules_local/@grapecity/js-calc/dist/gc.spread.calcengine.resources.ko.js").SR.ko)),a.Spread.Sheets&&(a.Spread.Sheets.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/core/core.res.ko.js"))),a.Spread.Sheets.Bindings&&(a.Spread.Sheets.Bindings.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/data/data.res.ko.js"))),a.Spread.Sheets.Outlines&&(a.Spread.Sheets.Outlines.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/group/group.res.ko.js"))),a.Spread.Sheets.Touch&&(a.Spread.Sheets.Touch.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/touch/touch.res.ko.js"))),a.Spread.Sheets.FloatingObjects&&(a.Spread.Sheets.FloatingObjects.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/floatingObject/floatingobject.res.ko.js"))),a.Spread.Sheets.ConditionalFormatting&&(a.Spread.Sheets.ConditionalFormatting.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/conditional/conditional.res.ko.js"))),a.Spread.Sheets.Filter&&(a.Spread.Sheets.Filter.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/filter/filter.res.ko.js"))),a.Spread.Sheets.Tables&&(a.Spread.Sheets.Tables.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/table/table.res.ko.js"))),a.Spread.Sheets.Slicers&&(a.Spread.Sheets.Slicers.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/slicer/slicer.res.ko.js"))),a.Spread.Sheets.Fill&&(a.Spread.Sheets.Fill.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/fill/fill.res.ko.js"))),a.Spread.Sheets.ContextMenu&&(a.Spread.Sheets.ContextMenu.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/contextMenu/context-menu.res.ko.js"))),a.Spread.Sheets.StatusBar&&(a.Spread.Sheets.StatusBar.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/statusBar/statusBar.res.ko.js"))),a.Spread.Sheets.Print&&(a.Spread.Sheets.Print.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/print/print.res.ko.js"))),a.Spread.Sheets.PDF&&(a.Spread.Sheets.PDF.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/exportPDF/printPdf.res.ko.js"))),a.Spread.Sheets.Shapes&&(a.Spread.Sheets.Shapes.SR.ko=e({},a.Spread.Common.SR.ko||{},d("./dist/plugins/shape/shape.res.ko.js"))),b.exports=a.Spread},Common:function(b,c){b.exports=a.Spread},Core:function(b,c){b.exports=a.Spread.Sheets}})});