/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.DataValidation=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/validation/datavalidation.entry.js")}({"./dist/plugins/validation/datavalidation-event.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("CalcEngine"),g=c("./dist/plugins/validation/datavalidation.js"),h=e.Common.j.Fa,i=e.Common.l,j=e.Common.u,k=d.Ul,l=d.kf,m=k.rl(),n=k.Ml,o=d.GC$,p=k.Nl,q=k.hZa,r=k.nl,s=document,t=Math.max,u=Math.min,v="position",w="absolute",x="border",y="padding",z="box-shadow",A="font",B="background-color",C="top",D="left",E="z-index",F="outline",G="auto",H="white",I="none",J="size",K="gcUIElement",o.extend(d.iI.prototype,{cs:function(a,b){var c,d,e,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua;function va(a,b){if(a&&a[b])return"function"==typeof a[b]?a[b]():a[b]}if(c=this,d=c.kj,e=d&&d.parent,e&&(L=d.hm(),L&&(M=e.getActiveSheet(),M&&d.name()===M.name()&&(N=d.getDataValidator(a,b),N&&(O=d.getCellRect(a,b),!c.oG(O)&&(P=e.xv(),Q=k.vl(e.getHost()),va(N,"showInputMessage")&&va(N,"inputMessage")&&(R=j.Gb,S=j.Ob,T=R(S(va(N,"inputTitle")),"\n","<br/>"),U=R(S(va(N,"inputMessage")),"\n","<br/>"),V=p("span"),o(V).css(v,w).css(x,"1px #C0C0C0 solid").css(y,"3px 8px 3px 8px").css(B,"#FFFFFF").css(z,"1px 2px 5px rgba(0,0,0,0.4)").css(A,"normal normal normal 12px/normal Arial").width(G).height(G).css(C,O.y+O.height+5).css(D,O.x+O.width/2).css(E,Q).html("<b>"+T+"</b><br/>"+U).attr(K,"gcValidationInputMessage").appendTo(P),d.KH=V),N&&3===va(N,"type")&&va(N,"inCellDropdown"))))))){for(W=va(N,"condition").getValidListImp(d,a,b),X=W.length,Y=p("select"),Z=d.getText(a,b),$=-1,_=s.createDocumentFragment(),aa=0;aa<X;aa++)ba=W[aa].text,h(ba)||f&&ba instanceof f.CalcError||($<0&&ba===Z&&($=aa),ba instanceof Date&&(ba=i.Vb(ba,"M/d/yyyy h:mm:ss")),ca=p("option"),ca.value=ba,ca.text=ba,_.appendChild(ca));Y.appendChild(_),Y.selectedIndex=$>0?$:0,da=d.getActualStyle(a,b),ea=d.yl,fa=da&&da.font?da.font:ea.Bl(),d.zoom()>1&&(fa=ea.Cl(fa)),ga=d.getSpans(l(a,b,1,1)),ha=1,ga&&ga.length>0&&ga[0]&&(ha=ga[0].colCount),ia=b+ha-1===d.getColumnCount()-1,ja=d.Sl(a),ka=d.parent&&d.parent.options.useTouchLayout,ja=ka?u(50,ja):u(15,ja),la=m,ma=n.safari&&(la.ipad||la.iphone),na=la.android,oa=void 0,pa=void 0,qa=void 0,ra=void 0,sa=void 0,ma?(oa=O.width+3,pa=O.height+3,qa=O.y-1.5,ra=O.x-1.5,sa=H):(oa=t(O.width+(ia?0:ja),d.defaults.colWidth),pa=X>8?140:G,qa=O.y+O.height,ra=O.x+O.width+(ia?0:ja)-oa,sa=""),ta=o(Y).css(F,I).css(v,w).css(A,q(fa)).css(E,Q).css(B,sa).width(oa).height(pa).css(C,qa).css(D,ra).attr(K,"gcValidationSelect").appendTo(P).attr(J,X>2?X:2).hide().bind("click",function(){na||(c.pG(Y,a,b,W),ma&&o(d.y$).show())}).bind("keydown",function(e){13!==e.keyCode||e.ctrlKey||e.shiftKey||e.altKey?27!==e.keyCode||e.ctrlKey||e.shiftKey||e.altKey||o(Y).hide():c.pG(Y,a,b,W),ma&&o(d.y$).show()}).bind("change",function(){na&&c.pG(Y,a,b,W)}),ma&&ta.bind("blur",function(){c.pG(Y,a,b,W),o(d.y$).show()}),d.lm=Y,ua=p("input"),ua.type="image",ua.src=g.zW(),ua.alt="v",o(ua).css(v,w).width(ja).height(ja).css(C,O.y+O.height-(ja+3)).css(D,O.x+O.width-(ia?ja:0)).css(E,Q).css(B,H).css(x,"1px solid gray").attr(K,"gcValidationButton").appendTo(P).bind("click",function(a){d.isEditing()&&!d.endEdit()||(o(d.lm).toggle().focus(),ma&&o(d.y$).toggle(),r(a))}),d.y$=ua}}})},"./dist/plugins/validation/datavalidation.entry.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),c("./dist/plugins/validation/datavalidation-event.js");var d=c("./dist/plugins/validation/datavalidation.js");b.CriteriaType=d.CriteriaType,b.DataValidationResult=d.DataValidationResult,b.ErrorStyle=d.ErrorStyle,b.DefaultDataValidator=d.DefaultDataValidator,b.HighlightType=d.HighlightType,b.HighlightPosition=d.HighlightPosition,b.zW=d.zW,b.bW=d.bW,b.createNumberValidator=d.createNumberValidator,b.createDateValidator=d.createDateValidator,b.createTimeValidator=d.createTimeValidator,b.createTextLengthValidator=d.createTextLengthValidator,b.createFormulaValidator=d.createFormulaValidator,b.createFormulaListValidator=d.createFormulaListValidator,b.createListValidator=d.createListValidator},"./dist/plugins/validation/datavalidation.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("ConditionalFormatting"),g=c("SheetsCalc"),h=c("CalcEngine"),i=!!h,j=f.$V,k=d.Common.u.Db,l=d.Common.j.Fa,m=d.Common.j.C4,n=e.GC$,o=null,p=void 0,q=e.Ul,r=q.Pl,s=q.Ol,t=q.T$a,u=e.kf,v=Math.min,w=Math.max,x=d.Common.l,y="red",z=1,A=5,B={type:0,color:y},C={type:1,color:y,position:z},D={type:2,color:y,position:A,image:null},E=window;function O(a){if(l(a))return 0;if("number"==typeof a)return a;if("string"==typeof a){var b=parseFloat(a);if(!isNaN(b)&&""+b===a)return b}return o}function P(a){var b,c,e,f,g;if(a instanceof Date)return a;if("string"==typeof a){if(b=d.Common.l.Qa(a),!b)if(isNaN(a)){if(b=new Date(a),isNaN(b.valueOf()))return o;try{if(c=/^[-+=\s]*(\d+)\W+(\d+)\W+(\d+)$/,e=d.Common.u,f=e.ib(d.Common.u.Db(a.replace(/ |\n/g,"")," ")," "),g=c.exec(f),g&&4===g.length&&(g.indexOf(""+b.getFullYear())===-1||g.indexOf(""+b.getMonth())===-1||g.indexOf(""+b.getDate())===-1))return o}catch(a){return o}}else if(b=d.Common.l.Xb(parseFloat(a)),!b)return o;return b}return o}function Q(a,b){var c,d,e;if(!l(a))return 1===a||2===a?c=U(b.operator,b.value1,b.value2,1===a):3===a?b.value?c=_(b.value):b.formula&&(c=$(b.formula)):4===a||5===a?(d=b.value1,e=b.value2,d&&"/OADate("===d.substr(0,8)&&(d=x.Rka(d)),e&&"/OADate("===e.substr(0,8)&&(e=x.Rka(e)),c=5===a?X(b.operator,d,e):W(b.operator,d,e)):6===a?c=Y(b.operator,b.value1,b.value2):7===a&&(c=Z(b.formula)),c?c.condition():void 0}!function(a){a[a.anyValue=0]="anyValue",a[a.wholeNumber=1]="wholeNumber",a[a.decimalValues=2]="decimalValues",a[a.list=3]="list",a[a.date=4]="date",a[a.time=5]="time",a[a.textLength=6]="textLength",a[a.custom=7]="custom"}(F=b.CriteriaType||(b.CriteriaType={})),function(a){a[a.forceApply=0]="forceApply",a[a.discard=1]="discard",a[a.retry=2]="retry"}(G=b.DataValidationResult||(b.DataValidationResult={})),function(a){a[a.stop=0]="stop",a[a.warning=1]="warning",a[a.information=2]="information"}(H=b.ErrorStyle||(b.ErrorStyle={})),function(a){a[a.circle=0]="circle",a[a.dogEar=1]="dogEar",a[a.icon=2]="icon"}(I=b.HighlightType||(b.HighlightType={})),function(a){a[a.topLeft=0]="topLeft",a[a.topRight=1]="topRight",a[a.bottomRight=2]="bottomRight",a[a.bottomLeft=3]="bottomLeft",a[a.outsideLeft=4]="outsideLeft",a[a.outsideRight=5]="outsideRight"}(J=b.HighlightPosition||(b.HighlightPosition={})),K=["errorStyle","ignoreBlank","inCellDropdown","showInputMessage","showErrorMessage","inputTitle","errorTitle","inputMessage","errorMessage","comparisonOperator","type","condition"];function R(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r,s,t,x,y,z,A,B,C,D,E;return a.intersect(b,c,d,e)?(g=a.rowCount,h=a.colCount,i=w(0,a.row),j=w(0,a.col),k=!1,l=!1,m=!1,b=w(0,b),c=w(0,c),f||(f=[-1,-1]),n=f[0],o=f[1],g===-1&&(g=n-i,l=!0),h===-1&&(h=o-j,k=!0),d===-1&&(d=n-b),e===-1&&(e=o-c,m=!0),p=i,q=i+g-1,r=j,s=j+h-1,t=b,x=b+d-1,y=c,z=c+e-1,A=[],p!==-1&&r!==-1&&t!==-1&&y!==-1&&(y-r>0&&(B=u(p,r,l?-1:g,y-r),A.push(B)),s-z>0&&(C=u(p,z+1,l?-1:g,s-z),A.push(C)),t-p>0&&(D=u(p,w(y,r),t-p,k&&m?-1:v(z,s)-w(y,r)+1),A.push(D)),q-x>0&&(E=u(x+1,w(y,r),q-x,k&&m?-1:v(z,s)-w(y,r)+1),A.push(E))),A.PAb=!0,A):[a]}L=function(){function a(b){var c=this;c.id=a.w$,a.w$++,c.QAb=m(B),b&&(c.condition(b),c.condition().ignoreBlank(c.ignoreBlank())),c.n5=[]}return a.prototype.value1=function(a,b){var c,d=this.condition(),e=d&&d.item1()?d.item1():d;return e?(c=e.formula(a,b),c&&c.length>0?"="+k(n.trim(""+c),"="):e.expected()):o},a.prototype.value2=function(a,b){var c,d=this.condition(),e=d&&d.item2()?d.item2():d;return e?(c=e.formula(a,b),c&&c.length>0?"="+k(n.trim(""+c),"="):e.expected()):o},a.prototype.isValid=function(a,b,c,d){var e,f,g,h=this,i=h.condition();if(i){if(h.ignoreBlank()&&(l(d)||""===d))return!0;if(e=d,f=void 0,!l(d))switch(h.type()){case 0:return!0;case 2:case 1:f=O(d),l(f)||(e=f);break;case 4:case 5:f=P(d),l(f)||(e=f);break;case 7:case 3:case 6:}a&&(a.lRa=!0),g=void 0;try{g=i.evaluate(a,b,c,e,e)}catch(a){g=!0}finally{a&&(a.lRa=!1)}return g}return!0},a.prototype.reset=function(){var a=this;a.errorStyle(0),a.ignoreBlank(!0),a.inCellDropdown(!0),a.showInputMessage(!0),a.showErrorMessage(!0),a.inputTitle(""),a.errorTitle(""),a.inputMessage(""),a.errorMessage(""),a.comparisonOperator(6),a.type(0),a.condition(o),a.n5.length=0},a.prototype.getValidList=function(a,b,c){var d=this.condition();return d!==o&&3===this.type()&&12===d.conType()?d.getValidList(a,b,c):o},a.prototype.highlightStyle=function(a){var b,c,d,e=this;if(0===arguments.length)return e.QAb;if(!l(a)){if(e.QAb.type!==a.type)switch(b=a.type){case 1:e.QAb=m(C);break;case 2:e.QAb=m(D);break;default:e.QAb=m(B)}c=e.QAb;for(d in c)c.hasOwnProperty(d)&&a.hasOwnProperty(d)&&(c[d]=a[d]);return e.kj&&e.kj.repaint(),e}},a.prototype.toJSON=function(a){var b,c=this,d={};return K.forEach(function(b){var e=c[b]();c[b].isDefault(e)||(d[b]=e&&e.toJSON?e.toJSON(a):e)}),b=c.n5.map(function(a){return a.row===-1&&a.col===-1?u(-1,0,a.rowCount,a.colCount):a}),d.ranges=g.rangesToFormula(b,0,0,15,!1),d.highlightStyle=JSON.stringify(c.QAb),n.isEmptyObject(d)?p:d},a.prototype.fromJSON=function(a,b,c){var d,e,f,h,i;a&&!n.isEmptyObject(a)&&(d=this,a.ranges&&(d.n5=g.formulaToRanges(c,a.ranges,0,0,!0)[0].ranges),a.highlightStyle&&(d.QAb=JSON.parse(a.highlightStyle)),K.forEach(function(e){var f,g,h=a[e];s(h)&&("condition"===e?(f=j(),f.fromJSON(a.condition,c,b),d.condition(f,!1),g=f.ranges(),g&&g.length?d.n5=g:f.ranges(d.n5),f.context(c)):d[e](h,!1))}),e=a.validatorInfo,e&&(f=c.Wu.useR1C1,h=Q(a.type,e),i=e.operator,h&&(h.ranges(d.n5),h.ignoreBlank(d.ignoreBlank()),h.context(c),f&&t(h),d.condition(h)),l(i)||d.comparisonOperator(i)))},a.prototype.clone=function(b){var c,d;return this.Nja?(c=this.condition(),c&&c.initExpression(),this):(d=new a,d.fromJSON(this.toJSON(b),!1,b||this.kj),d.n5=[],d)},a.prototype.context=function(a){if(a){this.xc=a;var b=this.condition();b&&(b.ranges(this.n5),b.context(a))}return this.xc},a.prototype.xja=function(a){var b,c;if(a){for(b=this.n5,c=0;c<b.length;c++)if(b[c].containsRange(a))return;b.push(a)}},a.prototype.IW=function(a,b){var c,d,e,f,g,h,i,j,k=this,l=k.n5;a&&l.length&&(c=a.row,d=a.col,e=a.rowCount,f=a.colCount,g=[],h=!1,i=k.kj,j=i&&[i.getRowCount(),i.getColumnCount()],l.forEach(function(a){var b=R(a,c,d,e,f,j);b.PAb&&(h=!0),g.push(b)}),k.n5=[].concat.apply([],g),h&&b.RAb(k))},a.prototype.l1=function(){return this.n5},a.prototype.rI=function(a,b){a<0||b<=0||(this.condition()&&this.condition().initExpression(),this.n5.forEach(function(c){var d=c.row;a<=d?c.row+=b:d<a&&a<d+c.rowCount&&(c.rowCount+=b)}),this.condition()&&this.condition().ranges(this.n5))},a.prototype.GR=function(a,b){if(!(a<0||b<=0)){this.condition()&&this.condition().initExpression();var c=[];this.n5.forEach(function(d){var e,f=d.row,g=d.rowCount;a<f?(e=a+b-f,e<=0?f-=b:(f-=e,d.rowCount-=e),f<0&&(d.rowCount+=f,f=0),d.row=f):f<=a&&a<f+g&&(d.rowCount-=v(b,f+g-a)),0!==d.rowCount&&c.push(d)}),this.n5=c,this.condition()&&this.condition().ranges(this.n5)}},a.prototype.tI=function(a,b){a<0||b<=0||(this.condition()&&this.condition().initExpression(),this.n5.forEach(function(c){var d=c.col;a<=d?c.col+=b:d<a&&a<d+c.colCount&&(c.colCount+=b)}),this.condition()&&this.condition().ranges(this.n5))},a.prototype.HR=function(a,b){if(!(a<0||b<=0)){this.condition()&&this.condition().initExpression();var c=[];this.n5.forEach(function(d){var e,f=d.col,g=d.colCount;a<f?(e=a+b-f,e<=0?f-=b:(f-=e,d.colCount-=e),f<0&&(d.colCount+=f,f=0),d.col=f):f<=a&&a<f+g&&(d.colCount-=v(b,f+g-a)),0!==d.colCount&&c.push(d)}),this.n5=c,this.condition()&&this.condition().ranges(this.n5)}},a.w$=1,a}(),b.DefaultDataValidator=L,M={errorStyle:r("errorStyle",0),ignoreBlank:r("ignoreBlank",!0,function(a){this.condition()&&this.condition().ignoreBlank(a)}),inCellDropdown:r("inCellDropdown",!0),showInputMessage:r("showInputMessage",!0),showErrorMessage:r("showErrorMessage",!0),inputTitle:r("inputTitle",""),errorTitle:r("errorTitle",""),inputMessage:r("inputMessage",""),errorMessage:r("errorMessage",""),comparisonOperator:r("comparisonOperator",6),condition:r("condition",o),type:r("type",0)},n.extend(L.prototype,M);function S(){return"data:image/png;base64,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"}b.zW=S;function T(a){return i&&a&&"="===a[0]}b.bW=T;function U(a,b,c,d){var e,f,g,h,i=o,l=o,m=o,n=o;return T(b)?i=k(b,"="):l=b,T(c)?m=k(c,"="):n=c,e=o,6===a&&(f=j(1,3,l,i),f.integerValue(d),g=j(1,5,n,m),g.integerValue(d),e=j(0,1,o,o,o,o,o,f,g)),7===a&&(f=j(1,4,l,i),f.integerValue(d),g=j(1,2,n,m),g.integerValue(d),e=j(0,0,o,o,o,o,o,f,g)),a>=0&&a<=5&&(e=j(1,a,l,i),e.integerValue(d)),h=new L(e),h.type(d?1:2),h.comparisonOperator(a),h}b.createNumberValidator=U;function V(a,b,c,d,e){var f,g,h,i,l,m=o,n=o,p=o,q=o;if(T(b)?m=k(b,"="):n=b,T(c)?p=k(c,"="):q=c,f=o,6===a&&(g=j(d,5,n,m),h=j(d,3,q,p),f=j(0,1,o,o,o,o,o,g,h)),7===a&&(g=j(d,2,n,m),h=j(d,4,q,p),f=j(0,0,o,o,o,o,o,g,h)),a>=0&&a<=5){switch(a){case 0:i=0;break;case 1:i=1;break;case 2:i=4;break;case 3:i=5;break;case 4:i=2;break;case 5:i=3}f=j(d,i,n,m)}return l=new L(f),l.type(e),l.comparisonOperator(a),l}function W(a,b,c){return V(a,b,c,5,4)}b.createDateValidator=W;function X(a,b,c){return V(a,b,c,13,5)}b.createTimeValidator=X;function Y(a,b,c){var d,e,f,g,h=o,i=o,l=o,m=o;return T(b)?h=k(b,"="):i=b,T(c)?l=k(c,"="):m=c,d=o,6===a&&(e=j(7,3,i,h),f=j(7,5,m,l),d=j(0,1,o,o,o,o,o,e,f)),7===a&&(e=j(7,4,i,h),f=j(7,2,m,l),d=j(0,0,o,o,o,o,o,e,f)),a>=0&&a<=5&&(d=j(7,a,i,h)),g=new L(d),g.type(6),g.comparisonOperator(a),g}b.createTextLengthValidator=Y;function Z(a){if(!i)return o;var b=new L(j(4,o,o,k(a,"="),4));return b.type(7),b}b.createFormulaValidator=Z;function $(a){if(!i)return o;var b=new L(f.Condition.fromFormula(a));return b.type(3),b}b.createFormulaListValidator=$;function _(a){var b=new L(f.Condition.fromSource(a));return b.type(3),b}b.createListValidator=_,n.extend(e.Worksheet.prototype,{getDataValidator:function(a,b,c){return l(c)&&(c=3),3===c?this.uja.Aja(a,b)[0]:p},setDataValidator:function(a,b,c,d,e,f){var g,h,i,j,k=this,m=k.uja;if(arguments.length<5&&(e=c,f=d,c=a===-1?-1:1,d=b===-1?-1:1),l(f)&&(f=3),3===f){if(g=k.getDataValidator(a,b,f),h=1===c&&1===d,h&&g===e)return;k.ITa.zVa(),i=u(a,b,c,d),m.Cja(i),e&&m.Bja(e,i),j="validator",h&&(k.Bq(j,a,b,f,g,e),a!==-1&&b===-1?k.kq(a,f,j,e,g):a===-1&&b!==-1&&k.pq(b,f,j,e,g))}},isValid:function(a,b,c){var d,e=this;try{if(e.Ku=a,e.Lu=b,e.Mu=c,e.Ju=!0,d=e.getDataValidator(a,b))return d.isValid(e,a,b,c)}finally{e.Ku=-1,e.Lu=-1,e.Mu=o,e.Ju=!1}return!0},hm:function(){var a,b,c,d,e=this;return!e.x$&&(e.x$=!0,a=e.KH,a&&(b=a.parentNode,b&&b.removeChild(a),e.KH=o),c=e.y$,c&&(n(c).unbind("click"),b=c.parentNode,b&&b.removeChild(c),e.y$=o),d=e.lm,d&&(n(d).unbind("keydown").unbind("blur").unbind("click").unbind("change"),b=d.parentNode,b&&b.removeChild(d),e.lm=o),e.x$=!1,!0)},tja:function(){var a,b,c,d=this,e=[],f=d.uja,g=f&&f.Dja;if(g)for(a in g)b=g[a],c=b&&b.condition(),c&&c.getExpressions&&c.getExpressions().length>0&&e.push({validator:b,sheet:d});return e}}),n.extend(e.Workbook.prototype,{tja:function(){var a=[],b=this,c=b.sheets;return c.forEach(function(b){var c=b.tja();c&&c.length&&c.forEach(function(b){a.push(b)})}),a}}),n.extend(e.CellRange.prototype,{validator:function(a){var b=this,c=b.sheet,d=b.sheetArea,e=b.row,f=b.col,g=b.rowCount,h=b.colCount;return 0===arguments.length?c.getDataValidator(e,f,d):(c.setDataValidator(e,f,g,h,a,d),b)}});function aa(a){return this.Tq(a)}function ba(a,b){if(a&&b&&b.length)return b.map(aa.bind(a))}function ca(a,b,c){var d=a.rowCount,e=a.colCount,f=u(a.row,a.col,d,e);return 0===a.row&&d===b&&(f.row=-1),0===a.col&&e===c&&(f.col=-1),f}function da(a,b){var c=a.getRowCount(),d=a.getColumnCount();return b.map(function(a){return ca(a,c,d)})}function ea(a,b,c,d,e){var f,g,h,i,j,k,l=d.Eja,m=a.row,n=a.col,o=a.rowCount,p=a.colCount;for(m+o-1>=b&&(o=b-m),n+p-1>=c&&(p=c-n),f=0,g=m;f<o;f++,g++)for(h=l[g],h||(l[g]=h={}),i=0,j=n;i<p;i++,j++)k=h[j],k&&k!==e&&k.IW(a,d),h[j]=e}function fa(a,b){var c,d,e,f,g,h,i,j,k=b.Eja;for(d=0,e=a.row,f=a.rowCount;d<f;d++,e++)if(c=k[e])for(g=0,h=a.col,i=a.colCount;g<i;g++,h++)j=c[h],j&&j.IW(a,b),c[h]=p}function ga(a){var b,c,d=[];if(a)for(b in a)c=a[b],c&&d.push(c);return d}N=function(){function a(a){this.WS=a,this.Dja={},this.Eja={},this.SAb=[]}return a.prototype.UV=function(a){return ba(this.WS,a)},a.prototype.EW=function(a){return da(this.WS,a)},a.prototype.QV=function(a){var b,c,d=this,e=d.Dja;if(d.Eja={},e)for(b in e)c=e[b],c&&(a&&delete c.yja,d.FW(c,!0));d.Fja()},a.prototype.RAb=function(a){var b=this.SAb;b.indexOf(a)===-1&&b.push(a)},a.prototype.FW=function(a,b){var c=this,d=c.UV(a.l1()),e=c.WS,f=e.getRowCount(),g=e.getColumnCount();d&&(c.SAb.length=0,d.forEach(function(b){ea(b,f,g,c,a)}),b||c.Fja())},a.prototype.GW=function(a){var b=this,c=b.UV(a.l1());b.SAb.length=0,c.forEach(function(a){fa(a,b)}),b.Fja()},a.prototype.HW=function(a){var b=this;a&&fa(b.WS.Tq(a),b),b.Fja()},a.prototype.Fja=function(){var a,b=this.SAb;b.length&&(a=this.Dja,b.forEach(function(b){b.l1().length?b.condition()&&b.condition().ranges(b.l1()):(delete a[b.id],delete b.kj)}),b.length=0)},a.prototype.Bja=function(a,b){var c=this,d=c.WS;if(b&&(a.kj&&d!==a.kj&&(a=a.clone(d)),a.kj=d,a.xja(b)),d)return a.context(d),c.Dja[a.id]=a,c.FW(a),d.$p(),a},a.prototype.Cja=function(a){if(a){var b=this,c=b.WS;b.HW(a),c.$p()}},a.prototype.$b=function(){var a=this,b=a.WS;b&&(a.Dja={},a.QV())},a.prototype.Aja=function(a,b){var c,d,e,f=this,g=f.Dja;return 0===arguments.length?ga(g):(c=[],d=f.Eja,a===p&&(a=-1),b===p&&(b=-1),a===-1&&b===-1?ga(g):(a!==-1&&b!==-1?(e=d[a],e&&e[b]&&c.push(e[b])):a===-1?n.each(d,function(a,d){var e=d[b];e&&c.indexOf(e)===-1&&c.push(e)}):(e=d[a],e&&n.each(e,function(a,b){b&&c.indexOf(b)===-1&&c.push(b)})),c))},a.prototype._V=function(a,b,c){var d,e,f=this,g=f.Dja;if(g)for(d in g)e=g[d],e&&(c?e.rI(a,b):e.tI(a,b));f.QV()},a.prototype.rI=function(a,b){this._V(a,b,!0)},a.prototype.tI=function(a,b){this._V(a,b,!1)},a.prototype.aW=function(a,b,c){var d,e,f=this,g=f.Dja;if(g)for(d in g)e=g[d],e&&(c?e.GR(a,b):e.HR(a,b));f.QV()},a.prototype.GR=function(a,b){this.aW(a,b,!0)},a.prototype.HR=function(a,b){this.aW(a,b,!1)},a.prototype.toJSON=function(a){var b,c,d=this.Dja,e=[];for(b in d)c=d[b],c&&e.push(c.toJSON(a));return e},a.prototype.fromJSON=function(a,b,c){var d=this,e=d.WS;d.Dja={},a&&a.length&&(a.forEach(function(a){var f=new L;f.fromJSON(a,b,c),f.kj=e,d.Dja[f.id]=f}),d.QV())},a.prototype.vja=function(a,b,c){this.WS.setDataValidator(a,b,c)},a}(),n.extend(e.lUa.prototype,{zVa:function(){var a,b,c,d,e,f=this,g=f.zTa;if(g&&!g.AVa){a=f.uja.Dja,b=[];for(c in a)d=a[c],d&&(e=d.n5.map(function(a){return u(a.row,a.col,a.rowCount,a.colCount)}),b.push({validator:d,ranges:e}));g.AVa=b}},BVa:function(a){var b,c;a&&(b=this.uja,c={},a.forEach(function(a){var b=a.validator;b.n5=a.ranges,c[b.id]=b}),b.Dja=c,b.QV(!1))},CVa:function(a,b){this.zVa(),this.uja.rI(a,b)},DVa:function(a,b){this.zVa(),this.uja.GR(a,b)},EVa:function(a,b){this.zVa(),this.uja.tI(a,b)},FVa:function(a,b){this.zVa(),this.uja.HR(a,b)},GVa:function(a){this.zVa(),this.uja.HW(a)},HVa:function(){this.zVa(),this.uja.QV()}}),e.lUa.$n("validator",{init:function(){this.uja=new N(this.kj)},undo:function(a){var b=a.AVa;b&&this.BVa(b)}}),e.Worksheet.$n("validator",{init:function(){var a=this;a.uja=a.ITa.uja,a.Fu(e.Events.FloatingElementSelected+".validator",function(b,c){"worksheet"!==c.type&&a.hm()})},dispose:function(a){var b=this;b.hm(),a.clearCache&&b.uja.$b(),a.clearCache!==!1&&b.Gu(e.Events.FloatingElementSelected+".validator")},onLayoutChanged:function(a){var b=a.changeType,c=a.row,d=a.rowCount,e=a.col,f=a.colCount,g=a.sheetArea,h=this.ITa;"addRows"===b?h.CVa(c,d):"deleteRows"===b?h.DVa(c,d):"addColumns"===b?h.EVa(e,f):"deleteColumns"===b?h.FVa(e,f):"clear"===b?3===g&&2===(2&a.type)&&h.GVa(u(c,e,d,f)):"setColumnCount"!==b&&"setRowCount"!==b||3!==g&&1!==g||h.HVa()},toJson:function(a,b){var c,d=this.uja,e=b&&b.ignoreStyle;d&&!e&&(c=d.toJSON(this),c&&c.length&&(a.validations=c))},fromJson:function(a,b,c){var d,e=c&&c.ignoreStyle;a&&a.validations&&!e&&(d=a.validations,this.uja.fromJSON(d,b,this))}}),e.Style.$n("validator",{fromJson:function(a){if("validator"===a.p){var b=new L;b.fromJSON(a.v,a.noSchema,a.context),this.validator=b,a.r=!0}}}),E.Yl.km.push(function(a,b){!b&&a.hm&&a.hm()})},CalcEngine:function(a,b){a.exports=GC.Spread.CalcEngine},Common:function(a,b){a.exports=GC.Spread},ConditionalFormatting:function(a,b){a.exports=GC.Spread.Sheets.ConditionalFormatting},Core:function(a,b){a.exports=GC.Spread.Sheets},SheetsCalc:function(a,b){a.exports=GC.Spread.Sheets.CalcEngine}});