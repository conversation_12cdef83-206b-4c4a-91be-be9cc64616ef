<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: y<PERSON>hengqi.ex
 * @LastEditTime: 2025-04-28 18:17:42
-->
<template>
  <div
    style="display: inline-block;"
    v-if="record.signOrg === '视像科技公司' ? record.warnType === 1 : true"
  >
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./edit-modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
  },
  data() {
    return {
      showAlias,
    };
  },
  computed() {
    //record.signOrg '? record.warnType == 1? true: false
  },
  methods: {
    btClick() {
      console.log(this.record);
      this.$refs["modal"].show({
        dataId: this.record?.id,
        improvementMeasures: this.record?.improvementMeasures,
        causeAnalysis: this.record?.causeAnalysis,
        state: this.record?.state,
      });
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex,
        },
      });
    },
  },
};
</script>
