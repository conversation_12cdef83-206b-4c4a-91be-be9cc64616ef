<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuyong<PERSON>e
 * @LastEditTime: 2021-11-10 14:17:04
-->
<template>
  <div>
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button
      :type="data.type || 'primary'"
      :size="size"
      @click="btClick"
      :disabled="
        (data.isSelected === 'any' && !selectedRows.length) ||
          (data.isSelected === 'one' && selectedRows.length !== 1)
      "
      >{{ showAlias(data.showName, "新增") }}</a-button
    >
    <!-- 弹窗 -->
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    },
    size: String,
    selectedRows: Array,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      this.$refs["modal"].show();
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {}
      });
    }
  }
};
</script>
