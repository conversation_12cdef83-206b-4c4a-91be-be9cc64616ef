<!--
 * @Description: 视像核心KPI横比
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 14:26:03
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-09 11:26:24
-->
<template>
  <div
    class="indexComparisonPage"
    :class="companyName"
    :ref="`${companyName}indexComparisonPage`"
  >
    <div class="_top">
      <!-- 页头 -->
      <PageHeader
        :companyName="companyName"
        @pageHeaderChange="searchConditionChange"
        @modeChange="modeChange"
        :baseList="baseList"
        @treeSelect="treeSelect"
        :baseLabelName="attribute.baseLabelName"
      />
    </div>
    <!-- 表格模式 -->
    <div
      class="_bottom"
      v-show="searchForm.mode === 'table'"
      style="padding: 16px; overflow-y: auto;"
    >
      <a-spin :spinning="tableDataLoading">
        <div
          class="_flex"
          style="justify-content: flex-end; margin-bottom: 12px;"
        >
          <a-button type="primary" @click="download">
            数据导出
          </a-button>
        </div>
        <a-table
          :pagination="false"
          size="small"
          :columns="columns"
          :data-source="pageSliceTableData"
        >
          <template slot="index" slot-scope="text, record, index">{{
            index + 1
          }}</template>
        </a-table>
        <div style="padding:10px; overflow: hidden">
          <a-pagination
            style="float:right;"
            size="small"
            v-model="pageNum"
            :pageSize="pageSize"
            :total="total"
            :showTotal="
              total =>
                `${showAlias('TOTAL', '共')} ${total} ${showAlias(
                  'ITEMS',
                  '条'
                )}`
            "
            :pageSizeOptions="['10', '20', '50', '100']"
            @change="pageChange"
            @showSizeChange="pageSizeChange"
          />
        </div>
      </a-spin>
    </div>
    <!-- 图表模式 -->
    <div class="_bottom" v-show="searchForm.mode === 'chart'">
      <!-- 左侧 -->
      <div class="_left" id="bottom-left" style="width: 208px;">
        <IndexTree
          ref="indexTree"
          :recommendListLoading="recommendListLoading"
          :companyName="companyName"
          :recommendList="recommendList"
          @stopRequest="getAllBaseRecommend"
          @getRecommendList="getRecommendList"
        />
      </div>
      <!-- 右侧 -->
      <div class="_right" id="bottom-right" style="width: calc(100% - 210px);">
        <chart-and-list
          :indexCardDetailInfoJSUrl="indexCardDetailInfoJSUrl"
          :baseList="searchForm.base"
          ref="chartAndList"
          :cardListLoading="cardListLoading"
          :cardList="cardList"
          :disabledBaseSelect="!searchForm.indexId"
          :frequency="
            this.searchForm.timeType === 'day'
              ? '日'
              : this.searchForm.timeType === 'week'
              ? '周'
              : '月'
          "
          :indexDt="this.searchForm.time"
        ></chart-and-list>
      </div>
    </div>
  </div>
</template>
<script>
import PageHeader from "./pageHeader.vue";
import IndexTree from "./indexTree.vue";
import request from "@/utils/requestHttp";
import moment from "moment";
import ChartAndList from "./chartAndList.vue";
import cloneDeep from "lodash/cloneDeep";
import uniqBy from "lodash/uniqBy";
import { adminUserUrlPrefix } from "@/utils/utils";
import { showAlias } from "@/utils/intl.js";
import { dealThousandData } from "../IndexGeneralView/utils";
import { pureAxios } from "@/utils/requestHttp";
export default {
  components: { PageHeader, IndexTree, ChartAndList },
  name: "IndexComparison",
  data() {
    return {
      showAlias,
      baseList: [], // 基地列表
      searchForm: {
        mode: "chart"
      },
      cardList: [], // 卡片列表
      cardListLoading: true, // 卡片列表Loading
      recommendList: [], // 推荐列表
      recommendListLoading: false, // 推荐Loading
      columns: [
        {
          title: "序号",
          scopedSlots: { customRender: "index" }
        },
        {
          title: "指标名称",
          dataIndex: "indexName",
          key: "indexName"
        },
        {
          title: "业务版块",
          dataIndex: "businessSegments",
          key: "businessSegments"
        },
        {
          title: "公司",
          key: "company",
          dataIndex: "company"
        },
        {
          title: "基地",
          dataIndex: "base",
          key: "base"
        },
        {
          title: "实际值",
          dataIndex: "baseActual",
          key: "baseActual"
        },
        {
          title: "实际分子",
          dataIndex: "actualMolecule",
          key: "actualMolecule"
        },
        {
          title: "实际分母",
          dataIndex: "actualDenominator",
          key: "actualDenominator"
        },
        {
          title: "单位",
          dataIndex: "unit",
          key: "unit"
        },
        {
          title: "目标值",
          dataIndex: "targetValue",
          key: "targetValue"
        },
        {
          title: "完成率",
          dataIndex: "completionRate",
          key: "completionRate"
        },
        {
          title: "同期值",
          dataIndex: "contemValue",
          key: "contemValue"
        },
        {
          title: "同比",
          dataIndex: "contemRate",
          key: "contemRate"
        },
        {
          title: "上期值",
          dataIndex: "previousValue",
          key: "previousValue"
        },
        {
          title: "环比",
          dataIndex: "previousRate",
          key: "previousRate"
        },
        {
          title: "频次",
          dataIndex: "frequency",
          key: "frequency"
        },
        {
          title: "指标时间",
          dataIndex: "indexDt",
          key: "indexDt"
        },
        {
          title: "指标类型",
          dataIndex: "indexType",
          key: "indexType"
        }
      ],
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      tableDataLoading: false,
      SYS_NAME: window.system
    };
  },
  provide() {
    return {
      treeSelect: this.treeSelect,
      isDesign: this.isDesign
    };
  },
  props: {
    data: Object,
    isDesign: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  computed: {
    // 当前组件属性props对象
    attribute() {
      if (this.data && this.data.props) {
        return this.data.props;
      } else {
        return {};
      }
    },
    // 公司名称
    companyName() {
      return this.attribute.companyName || "视像";
    },
    // 详情组件地址url
    indexCardDetailInfoJSUrl() {
      return (
        this.attribute.indexCardDetailInfoJSUrl ||
        "http://smc.devapps.hisense.com/minio/smcbucket/IndexCardDetailInfo.umd.min.1.0.js"
      );
    },
    // 因为后台分页不好用，前端进行分页
    pageSliceTableData() {
      return this.tableData.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageSize * this.pageNum
      );
    }
  },
  created() {
    /**
     * 横比页面的逻辑是
     * indexTree.vue组件中先获取当前角色拥有的指标树，传入数据到ownTree.vue中，首次获取默认选中第一个指标，当指标树选中数据发生改变后，
     * 通过inject的treeSelect方法调用index.vue的获取基地列表(getBase)方法，传入数据到pageHeader组件(A)中
     * A组件中监听baseList后修改searchForm通过$emit("pageHeaderChange")反馈给当前index.vue组件
     * 的searchConditionChange方法来进行卡片列表的请求
     */
    this.enterLog();
    this.$nextTick(() => {
      this.getAndSetWindowHeight();
    });
    window.addEventListener("resize", this.getAndSetWindowHeight);
  },
  destroyed() {
    window.removeEventListener("resize");
  },
  methods: {
    getAndSetWindowHeight() {
      let height = "";
      if (window.self === window.top) {
        // 在盘古内部使用
        height = document.getElementsByClassName("100heightDiv")[0]
          ?.offsetHeight;
        height = height ? `${height}px` : "100vh";
      } else {
        // 在信数内使用
        height = window.innerHeight + "px";
      }
      this.$refs[`${this.companyName}indexComparisonPage`].style.setProperty(
        "--realHeight",
        height
      );
    },
    // 进入页面日志
    enterLog() {
      request(`${adminUserUrlPrefix["lxp"]}/sysOperLog/saveLog`, {
        method: "POST",
        body: {
          menu: `${this.companyName}核心KPI横比${
            this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""
          }`
        }
      });
    },
    // 根据指标获取基地
    getBase(indexId) {
      let frequency = "月";
      if (this.searchForm.timeType) {
        frequency =
          this.searchForm.timeType === "day"
            ? "日"
            : this.searchForm.timeType === "week"
            ? "周"
            : "月";
      }
      this.baseList = [];
      request(
        `${adminUserUrlPrefix["lxp"]}/userIndexRelation/getHBBaseByIndexId`,
        {
          method: "POST",
          body: {
            indexNodeId: indexId,
            frequency
          }
        }
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          this.baseList = res;
        } else {
          // 清空右边数据
          this.cardList = [];
          this.recommendList = [];
          this.$refs["chartAndList"].initKpiComplate(
            this.$refs["chartAndList"].cloneKpiComplateChartOptions
          );
          this.$refs["chartAndList"].initKpiCompare(
            this.$refs["chartAndList"].cloneKpiCompareChartOptions
          );
        }
      });
    },
    // 翻页
    pageChange(page, pageSize) {
      this.pageNum = page;
      this.pageSize = pageSize;
    },
    // 每页显示条数变化
    pageSizeChange(current, size) {
      this.pageNum = current;
      this.pageSize = size;
    },
    // 获取表格数据
    getTableData() {
      this.tableDataLoading = true;
      let postData = cloneDeep(this.searchForm);
      postData["timeType"] =
        postData.timeType === "day"
          ? "日"
          : postData.timeType === "week"
          ? "周"
          : "月";
      postData.base = postData.base.join();
      request(`${adminUserUrlPrefix["zcx"]}/indexCardInfo/getHBIndexTable`, {
        method: "POST",
        body: postData
      })
        .then(res => {
          this.tableDataLoading = false;
          if (res && res.rows) {
            this.total = res.total;
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item["index"] = index + 1;
            });
          }
        })
        .catch(() => {
          this.tableDataLoading = false;
        });
    },
    // 下载表格
    download() {
      let postData = cloneDeep(this.searchForm);
      postData["timeType"] =
        postData.timeType === "day"
          ? "日"
          : postData.timeType === "week"
          ? "周"
          : "月";
      postData.base = postData.base.join();
      pureAxios({
        url: "/smc/indexCardInfo/exportHBList",
        method: "post",
        data: postData,
        responseType: "blob"
      })
        .then(response => {
          if (!response.data) {
            return;
          }
          const fileName = `${this.companyName}核心KPI横比数据表.xlsx`;
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message
          });
        });
    },
    // 获取当前公司下所有基地中的推荐指标
    async getAllBaseRecommend() {
      this.cardListLoading = false;
      this.getBaseByCompany();
      // 清空卡片列表，左右两张图表
      this.cardList = [];
      this.recommendList = [];
      this.$refs["chartAndList"].initKpiComplate(
        this.$refs["chartAndList"].cloneKpiComplateChartOptions
      );
      this.$refs["chartAndList"].initKpiCompare(
        this.$refs["chartAndList"].cloneKpiCompareChartOptions
      );
    },
    // 根据公司获取基地
    getBaseByCompany() {
      return new Promise(resolve => {
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getBaseBycompanyName?companyName=${this.companyName}`
        ).then(res => {
          if (Array.isArray(res) && res.length) {
            this.baseList = res;
          }
          resolve(res);
        });
      });
    },
    /**
     * 代码生成并下载为zip
     * @param {String} url 链接
     * @param {String} tables 表名
     */
    resolveBlob(res, mimeType) {
      const aLink = document.createElement("a");
      var blob = new Blob([res.data], { type: mimeType });
      var fileName = `${this.companyName}核心KPI横比数据表`;
      aLink.href = window.URL.createObjectURL(blob);
      aLink.setAttribute("download", fileName); // 设置下载文件名称
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(aLink.href);
    },
    modeChange(val) {
      console.log("modeChange----->", val);
      // 表格模式
      this.searchForm.mode = val;
      if (val === "table") {
        this.pageNum = 1;
        this.pageSize = 10;
        this.getTableData();
      }
    },
    // 查询条件改变
    searchConditionChange(data) {
      console.log("searchConditionChange----->", data);
      this.searchForm = { ...this.searchForm, ...data };
      this.searchForm.mode = "chart";
      const timeType = this.searchForm.timeType || "day";
      if (timeType !== "week") {
        this.searchForm.time = moment(this.searchForm.time).format(
          timeType === "day" ? "YYYY-MM-DD" : "YYYY-MM"
        );
      }
      if (this.searchForm.mode === "table") {
        return;
      }
      if (this.searchForm.indexId) {
        this.getRecommendList();
        Promise.all([
          this.getCardList(),
          this.getLeftChart(),
          this.getRightChart()
        ]);
      } else {
        this.getRecommendList();
      }
    },
    // 获取左侧echart数据
    getLeftChart() {
      return this.$refs["chartAndList"].getChartData({
        baseStr: this.searchForm.base.join(","),
        frequency:
          this.searchForm.timeType === "day"
            ? "日"
            : this.searchForm.timeType === "week"
            ? "周"
            : "月",
        date: this.searchForm.time,
        indexNodeId: this.searchForm.indexId,
        company: this.companyName,
        type: "1"
      });
    },
    // 获取右侧echart数据
    getRightChart() {
      return this.$refs["chartAndList"].getChartData({
        baseStr: this.searchForm.base[0],
        frequency:
          this.searchForm.timeType === "day"
            ? "日"
            : this.searchForm.timeType === "week"
            ? "周"
            : "月",
        date: this.searchForm.time,
        indexNodeId: this.searchForm.indexId,
        company: this.companyName,
        type: "2"
      });
    },
    // 获取卡片列表
    getCardList() {
      return new Promise(resolve => {
        this.cardListLoading = true;
        request(
          `${adminUserUrlPrefix["lxp"]}/userIndexRelation/getHBIndexList`,
          {
            method: "POST",
            body: {
              baseStr: this.searchForm.base.join(","),
              frequency:
                this.searchForm.timeType === "day"
                  ? "日"
                  : this.searchForm.timeType === "week"
                  ? "周"
                  : "月",
              date: this.searchForm.time,
              indexNodeId: this.searchForm.indexId
            }
          }
        )
          .then(res => {
            this.cardListLoading = false;
            if (Array.isArray(res) && res.length) {
              res.forEach(item => {
                item["recommend"] = false;
                item["dwppCmTfIndexLibrary"] = {
                  baseActual: item.baseActual,
                  completionRate: item.completionRate,
                  targetValue: item.targetValue,
                  contemRate: item.contemRate,
                  previousRate: item.previousRate,
                  indexType: item.indexType,
                  precisions: item.precisions
                };
                item["dataFrequency"] = item.frequency;
                item["plateName"] = item.businessSegments;
                if (this.companyName === "集团") {
                  item["companyName"] = "集团";
                } else {
                  item["companyName"] = item.company;
                }
                item["baseName"] = item.base;
                // 处理实际值和目标值
                item["realBaseActual"] = dealThousandData(
                  item.dwppCmTfIndexLibrary.baseActual,
                  item.unit,
                  item.dwppCmTfIndexLibrary.precisions
                );
                item["realTargetValue"] = dealThousandData(
                  item.dwppCmTfIndexLibrary.targetValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary.precisions
                );
              });
              this.cardList = res;
            } else {
              this.cardList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.cardListLoading = false;
            this.cardList = [];
            resolve([]);
          });
      });
    },
    // 获取推荐列表数据
    getRecommendList() {
      return new Promise(resolve => {
        this.recommendListLoading = true;
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getRecommendIndexList`,
          {
            method: "POST",
            body: {
              company: this.companyName,
              base: this.searchForm.base.join(","),
              frequency:
                this.searchForm.timeType === "day"
                  ? "日"
                  : this.searchForm.timeType === "week"
                  ? "周"
                  : "月",
              indexDt: this.searchForm.time,
              type: "1"
            }
          }
        )
          .then(res => {
            this.recommendListLoading = false;
            if (res && Array.isArray(res)) {
              res.forEach(item => {
                item["recommend"] = true;
                if (item.dwppCmTfIndexLibrary === null) {
                  item.dwppCmTfIndexLibrary = {};
                }
              });
              res = uniqBy(res, "indexId");
              this.recommendList = res.splice(0, 3);
            } else {
              this.recommendList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.recommendListLoading = false;
            this.recommendList = [];
            resolve([]);
          });
      });
    },
    // 树形选择
    treeSelect(indexId) {
      console.log("treeSelect---->", indexId);
      const IndexId = indexId || this.$refs["indexTree"].getActiveKey();
      this.$refs["chartAndList"].setIndexName(
        this.$refs["indexTree"].getIndexName()
      );
      this.searchForm["indexId"] = IndexId;
      IndexId && this.getBase(IndexId);
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexComparisonPage {
  font-size: 14px;
  font-family: PingFangSC-Regular;
  color: rgba(0, 0, 0, 0.65);
  overflow-y: auto;
  height: var(--realHeight);
  // min-width: 1300px;
  overflow-y: hidden;

  ._flex {
    display: flex;
    align-items: center;
  }
  & > ._top {
    box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
    box-sizing: border-box;
    padding: 16px 24px;
    position: relative;
    &::before {
      display: block;
      content: "";
      height: 0;
      width: 100%;
      box-shadow: 0 1px 16px 0 rgba(0, 0, 0, 0.1);
    }
  }
  & > ._bottom {
    background-color: #fff;
    width: 100%;
    height: calc(100% - 64px);
    box-sizing: border-box;
    overflow-x: hidden;
    & > ._left {
      height: 100%;
      // border-right: 1px solid #f0f0f0;
      box-shadow: inset -1px 0 0 0 #f0f0f0;
      float: left;
    }
    & > ._right {
      // flex: 1;
      height: 100%;
      float: left;
    }
  }
}
</style>
