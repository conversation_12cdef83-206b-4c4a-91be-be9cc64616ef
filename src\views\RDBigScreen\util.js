// 渲染到页面的style内联样式
export function styleObject(data, indexType) {
  let style = {
    width: 0,
    height: 0,
    "border-right": "0.1rem solid transparent",
    "border-left": "0.1rem solid transparent",
    position: "relative",
    "margin-right": "0.08rem"
  };
  if (data) {
    if (indexType === "正向") {
      if (data.includes("-")) {
        style["border-top"] = "0.1rem solid #6495f9";
        style["top"] = "0.2rem";
      } else {
        style["border-bottom"] = "0.1rem solid #f75050";
        style["top"] = "-0.2rem";
      }
    } else {
      if (data.includes("-")) {
        style["border-bottom"] = "0.1rem solid #f75050";
        style["top"] = "-0.2rem";
      } else {
        style["border-top"] = "0.1rem solid #6495f9";
        style["top"] = "0.2rem";
      }
    }
    return style;
  } else {
    return {};
  }
}
