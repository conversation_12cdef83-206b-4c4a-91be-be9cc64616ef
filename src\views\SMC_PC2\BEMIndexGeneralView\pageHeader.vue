<!--
 * @Description: 页头
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:36:50
 * @LastEditors: Jhin.Yu <EMAIL>
 * @LastEditTime: 2023-02-20 11:15:43
-->
<template>
  <div class="_pageHeader">
    <!-- 标题 -->
    <div class="_left _flex">
      <span class="_title"></span>
    </div>
    <div class="_right _flex">
      <div class="_flex">
        <!-- 组织 -->
        <div class="org _flex">
          <span>{{ orgLabelName }}：</span>
          <div style="position: relative;">
            <div
              v-show="treeSelectLoading"
              style="position: absolute;width: 100%;height: 100%;background-color: rgba(242, 243, 245, 0.8);z-index: 1;"
            >
              <a-spin
                style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;"
              />
            </div>
            <a-tree-select
              v-model="searchForm.orgSign"
              style="width: 138px"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="orgTreeList"
              :dropdownMatchSelectWidth="false"
              placeholder="请选择组织"
              :treeExpandedKeys.sync="treeExpandedKeys"
              :replaceFields="replaceFields"
              @change="handleIndexChange([])"
            >
            </a-tree-select>
          </div>
        </div>
        <!-- 时间 -->
        <div class="time _flex">
          <template v-if="skinStyle().includes('classic-style')">
            <a-radio-group
              class="timetyep-radio-group"
              v-model="searchForm.timeType"
              button-style="solid"
              @change="timeTypeChange"
            >
              <a-radio-button
                :value="item.key"
                v-for="item in timeTypeOptions"
                :key="item.key"
              >
                {{ item.value }}
              </a-radio-button>
            </a-radio-group>
          </template>
          <template v-if="skinStyle().includes('hisense-style')">
            <div class="timetyep-radio-group _flex">
              <div
                :value="item.key"
                v-for="item in timeTypeOptions"
                :key="item.key"
                @click="
                  searchForm.timeType = item.key;
                  timeTypeChange(item.key);
                "
                :class="searchForm.timeType === item.key ? 'active' : ''"
              >
                {{ item.value }}
              </div>
            </div>
          </template>
          <!-- 月选择器 -->
          <a-month-picker
            :allowClear="false"
            v-model="searchForm.time"
            v-if="searchForm.timeType === 'month'"
            :format="monthFormat"
          />
          <!-- 周选择器 -->
          <a-select
            show-search
            placeholder="选择年"
            style="width: 90px;margin-right: 4px;"
            :filter-option="filterOption"
            v-model="selectedYearWeek[0]"
            v-if="searchForm.timeType === 'week'"
          >
            <a-select-option
              :value="item"
              v-for="item in yearSelectOptions"
              :key="item"
            >
              {{ item }}年
            </a-select-option>
          </a-select>
          <a-select
            show-search
            placeholder="选择周"
            style="width: 90px"
            :filter-option="filterOption"
            v-model="selectedYearWeek[1]"
            v-if="searchForm.timeType === 'week'"
          >
            <a-select-option
              :value="item"
              v-for="item in weekSelectOptions"
              :key="item"
            >
              {{ item }}周
            </a-select-option>
          </a-select>
          <!-- 日选择器 -->
          <a-date-picker
            :allowClear="false"
            v-model="searchForm.time"
            v-if="searchForm.timeType === 'day'"
            :format="dateFormat"
          />
        </div>
      </div>
      <!-- 模式 -->
      <div class="mode _flex">
        <div
          class="chart"
          :class="[searchForm.mode === 'chart' ? 'active' : '']"
          @click="searchForm.mode = 'chart'"
        >
          <a-tooltip placement="top">
            <template slot="title">
              <span>切换卡片</span>
            </template>
            <template v-if="skinStyle().includes('classic-style')">
              <svg
                width="16px"
                height="16px"
                viewBox="0 0 16 16"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <g
                  stroke="none"
                  stroke-width="1"
                  fill="none"
                  fill-rule="evenodd"
                >
                  <g
                    transform="translate(-1376.000000, -149.000000)"
                    fill-rule="nonzero"
                  >
                    <g transform="translate(1336.000000, 141.000000)">
                      <g transform="translate(40.000000, 8.000000)">
                        <rect x="0" y="0" width="16" height="16"></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="3.04166651"
                          y="3.04166651"
                          width="3.79166651"
                          height="3.79166651"
                        ></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="9.16666603"
                          y="3.04166651"
                          width="3.79166794"
                          height="3.79166651"
                        ></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="3.04166651"
                          y="9.16666603"
                          width="3.79166651"
                          height="3.79166794"
                        ></rect>
                        <rect
                          class="rect"
                          stroke="#4E5969"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          x="9.16666603"
                          y="9.16666603"
                          width="3.79166794"
                          height="3.79166794"
                        ></rect>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </template>
            <template v-if="skinStyle().includes('hisense-style')">
              <img
                :src="searchForm.mode === 'chart' ? ChartActiveImg : ChartImg"
                alt=""
                srcset=""
              />
            </template>
          </a-tooltip>
        </div>
        <div
          class="table"
          :class="[searchForm.mode === 'table' ? 'active' : '']"
          @click="searchForm.mode = 'table'"
        >
          <a-tooltip placement="top">
            <template slot="title">
              <span>切换表格</span>
            </template>
            <template v-if="skinStyle().includes('classic-style')">
              <svg
                width="16px"
                height="16px"
                viewBox="0 0 16 16"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <g
                  stroke="none"
                  stroke-width="1"
                  fill="none"
                  fill-rule="evenodd"
                >
                  <g transform="translate(-1344.000000, -149.000000)">
                    <g transform="translate(1336.000000, 141.000000)">
                      <g transform="translate(8.000000, 8.000000)">
                        <rect x="0" y="0" width="16" height="16"></rect>
                        <line
                          x1="2"
                          y1="3"
                          x2="14"
                          y2="3"
                          id="路径-2"
                          stroke="#4E5969"
                          stroke-width="1.5"
                        ></line>
                        <line
                          x1="2"
                          y1="8"
                          x2="14"
                          y2="8"
                          stroke="#4E5969"
                          stroke-width="1.5"
                        ></line>
                        <line
                          x1="2"
                          y1="13"
                          x2="14"
                          y2="13"
                          stroke="#4E5969"
                          stroke-width="1.5"
                        ></line>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </template>
            <template v-if="skinStyle().includes('hisense-style')">
              <img
                :src="searchForm.mode === 'table' ? TableActiveImg : TableImg"
                alt=""
                srcset=""
              />
            </template>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ChartActiveImg from "@/assets/images/Group <EMAIL>";
import ChartImg from "@/assets/images/Group <EMAIL>";
import TableActiveImg from "@/assets/images/Group <EMAIL>";
import TableImg from "@/assets/images/Group <EMAIL>";
import moment from "moment";
import { findNode, getWeek } from "../utils";

export default {
  inject: ["skinStyle"],
  components: {
    // VideoCom
    // SelfReports
  }, // 本地调试
  props: {
    companyName: String,
    orgTreeList: {
      type: Array,
      default() {
        return [];
      }
    },
    orgLabelName: {
      type: String,
      default: "组织"
    },
    signOrgId: String,
    list: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      moment,
      ChartActiveImg,
      ChartImg,
      TableActiveImg,
      TableImg,
      title: `${this.companyName}核心KPI概览图`,
      searchForm: {
        // 页面中查询条件
        orgSign: "", // 组织
        mode: "chart", // 模式
        timeType: "month", // 时间类型
        time: moment(new Date().getTime(), this.monthFormat) // 时间
      },
      indexStatus: "",
      yearSelectOptions: [], // 周模式下年下拉框
      weekSelectOptions: [], // 周模式下周下拉框
      selectedYearWeek: ["", ""], // 周模式下选中的年周
      baseOptions: [
        // 组织下拉框
      ],
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: "month",
          value: "按月"
        },
        {
          key: "week",
          value: "按周"
        },
        {
          key: "day",
          value: "按日"
        }
      ],
      dateFormat: "YYYY-MM-DD", // 日期、周格式化
      monthFormat: "YYYY-MM", // 月格式化
      weekFormat: "YYYY-ww", // 周格式化
      saveLocalSearchForm: false, // 搜索表条本地存储
      videoComName: "",
      selfReportsComName: "",
      treeExpandedKeys: [], // 组织展开的树节点
      treeSelectLoading: true,
      replaceFields: {
        children: "list",
        title: "org",
        key: "fullCode",
        value: "fullCode"
      },
      indexList: [], // 搜索后指标列表
      filterIndexArr: [] // 过滤指标数组
    };
  },
  created() {
    // 初始化年周下拉框
    this.initYearSelect();
    this.initWeekSelect();
    // 如果本地搜索条件有存储，获取搜索条件后赋值到searchForm
    if (localStorage.getItem(`${this.signOrgId}-searchForm`)) {
      try {
        const { orgSign, timeType, time } = JSON.parse(
          localStorage.getItem(`${this.signOrgId}-searchForm`)
        );
        this.searchForm.orgSign = orgSign;
        this.searchForm.timeType = timeType;
        this.searchForm.time = time;
        if (timeType === "week") {
          this.selectedYearWeek[1] = Number(time.split("-")[1] || "01");
          this.selectedYearWeek[0] = Number(
            time.split("-")[0] || new Date().getFullYear()
          );
        }
        // 修改本地存储标识
        this.saveLocalSearchForm = true;
      } catch (error) {
        console.error(error);
      }
    }
  },
  watch: {
    // 监听组织列表变化
    orgTreeList: {
      handler(newVal) {
        this.treeSelectLoading = false;
        if (Array.isArray(newVal) && newVal.length) {
          console.timeEnd("transmit");
          // 组织树默认展开第一层
          console.time("treeExpandedKeys");
          newVal.forEach(item => {
            this.treeExpandedKeys.push(item.fullCode);
          });
          console.timeEnd("treeExpandedKeys");
          // 如果不是本地存储，则默认选中组织列表第一个
          if (!this.saveLocalSearchForm) {
            this.searchForm.orgSign = newVal[0].fullCode;
          } else {
            console.time("findNode");
            // 是本地存储且树数据中查找不到该节点，则默认选中组织列表第一个
            const data = findNode(newVal, node => {
              return node.fullCode === this.searchForm.orgSign;
            });
            console.timeEnd("findNode");
            console.log("节点---->", data);
            if (!data) {
              this.searchForm.orgSign = newVal[0].fullCode;
            }
          }
        } else {
          this.searchForm.orgSign = "";
        }
      },
      deep: true
    },
    // 监听searchForm值改变
    searchForm: {
      handler() {
        // 保存搜索条件到本地
        localStorage.setItem(
          `${this.signOrgId}-searchForm`,
          JSON.stringify(this.searchForm)
        );
        // 改变后向父组件发出请求
        this.debounce(this.sendRequest());
      },
      deep: true
    },
    // 监听周模式下的selectedYearWeek值改变
    selectedYearWeek(val) {
      // 根据选中年份去动态生成周下拉框
      this.initWeekSelect(val[0]);
      // 如果周下拉框中没有当前已选中的周值，则默认选成第一周
      if (!this.weekSelectOptions.includes(val[1])) {
        this.selectedYearWeek[1] = this.weekSelectOptions[0];
      }
      this.searchForm.time = `${val[0]}-${String(val[1]).padStart(2, "0")}`;
    }
  },
  methods: {
    debounce: function(fn, wait = 1000) {
      if (this.fun !== null) {
        clearTimeout(this.fun);
      }
      this.fun = setTimeout(fn, wait);
    },
    // 下拉框输入搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 禁用时间
    disabledDate(current) {
      // Can not select days before today and today
      // 今天之后或者两年前
      return (
        (current && current > moment().endOf("day")) ||
        moment().subtract(2, "years") > current
      );
    },
    // 时间类型改变
    timeTypeChange(e) {
      const val = (typeof e === "object" ? e.target.value : e) || "";
      if (val === "day") {
        this.searchForm.time = moment(new Date().getTime() - 86400000).format(
          this.dateFormat
        );
      } else if (val === "week") {
        this.initWeekSelect(new Date().getFullYear());
        this.selectedYearWeek = [
          new Date().getFullYear(),
          getWeek(moment().format("YYYY-MM-DD"))
        ];
      } else {
        this.searchForm.time = moment(new Date().getTime()).format(
          this.monthFormat
        );
      }
    },
    // 发起请求
    sendRequest() {
      this.$emit("pageHeaderChange", {
        ...this.searchForm,
        indexStatus: this.indexStatus
      });
    },
    // 根据年份更新周下拉框选项
    initWeekSelect(year = new Date().getFullYear()) {
      let weekSelectOptions = this.createWeeks(year);
      if (
        year === this.yearSelectOptions[this.yearSelectOptions.length - 1] &&
        this.yearSelectOptions.length === 3
      ) {
        // 如果是最前边的一年且年份下拉框为三个时，要处理月份选择选两年前的周
        this.weekSelectOptions = weekSelectOptions.filter(
          item => item > getWeek(moment().format("YYYY-MM-DD"))
        );
      } else {
        this.weekSelectOptions = weekSelectOptions;
      }
    },
    // 初始化年
    initYearSelect() {
      let yearSelectOptions = [];
      // 两年前的年份不允许选择
      let beginningYear = moment().get("year") - 2;
      // 如果当前月份是12月则，年份选择扣除一年
      if (moment().get("month") + 1 === 12) {
        beginningYear++;
      }
      for (let i = beginningYear; i <= new Date().getFullYear(); i++) {
        yearSelectOptions.unshift(i);
      }
      this.yearSelectOptions = yearSelectOptions;
    },
    // 获取自然周
    createWeeks(year) {
      var d = new Date(year, 0, 1);
      while (d.getDay() != 1) {
        d.setDate(d.getDate() + 1);
      }
      var to = new Date(year + 1, 0, 1);
      var i = 1;
      let arr = [];
      for (var from = d; from < to; ) {
        from.setDate(from.getDate() + 6);
        if (from < to) {
          from.setDate(from.getDate() + 1);
        } else {
          to.setDate(to.getDate() - 1);
        }
        arr.push(i);
        i++;
      }
      return arr;
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage2 {
  &.classic-style {
    & > ._top > ._pageHeader {
      padding-bottom: 18px;
      display: flex;
      ._title {
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        height: 28px;
        color: rgba(29, 33, 41, 1);
        margin-right: 4px;
      }
      ._right {
        .timetyep-radio-group {
          margin-right: 8px;
        }
        .org {
          margin-right: 24px;
        }
        .time {
          i {
            color: rgb(78, 89, 105);
          }
        }
        .mode {
          margin-left: 24px;
          width: 64px;
          height: 32px;
          border-radius: 2px;
          background: rgba(242, 243, 245, 1);
          & > div {
            cursor: pointer;
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            border-radius: 2px;
            &.active {
              background-color: rgb(0, 170, 166);
            }
            &.table.active {
              svg {
                line {
                  stroke: #fff;
                }
              }
            }
            &.chart.active {
              svg {
                .rect {
                  stroke: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
  &.hisense-style {
    &.dark {
      & > ._top > ._pageHeader {
        background-color: #313335;
        ._left {
          ._title {
            color: #ffffff;
          }
        }
        ._right {
          color: #9e9e9e;
          .ant-select-search__field__wrap,
          .ant-select-selection-selected-value {
            color: #e2e8ea;
          }
          .timetyep-radio-group,
          .mode {
            border: 1px solid #4e5969;
          }
        }
      }
    }
    & > ._top > ._pageHeader {
      // display: block;
      border-bottom: none;
      ._left {
        align-items: flex-end;
        ._title {
          font-weight: 400;
          font-size: 24px;
          line-height: 34px;
          height: 34px;
          color: #1d2129;
          margin-right: 0px;
        }
        .op {
          color: #20bdb5;
          font-size: 14px;
          line-height: 20px;
          height: 20px;
          margin-bottom: 2px;
          margin-right: 8px;
          cursor: pointer;
          img {
            display: block;
            margin-left: 8px;
            width: 18px;
            height: 18px;
          }
        }
      }
      ._right {
        justify-content: space-between;
        .org {
          margin-left: 16px;
          margin-right: 16px;
        }
        .timetyep-radio-group {
          margin-right: 16px;
          padding: 6px;
          height: 32px;
          color: #a9aeb8;
          font-size: 14px;
          box-sizing: border-box;
          border-radius: 3px;
          border: 1px solid #e5e6eb;
          & > div {
            height: 24px;
            padding: 0 10px;
            line-height: 24px;
            border-radius: 3px;
            cursor: pointer;
            &.active {
              background-color: #00aaa6;
              color: #fff;
            }
          }
        }
        .mode {
          margin-left: 16px;
          height: 32px;
          border-radius: 3px;
          border: 1px solid #e5e6eb;
          padding: 0 12px;
          & > div {
            &:not(:last-child) {
              margin-right: 8px;
            }
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              cursor: pointer;
              display: block;
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }
  }
  & > ._top > ._pageHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgb(229, 231, 235);
    ._left {
      ._title {
        font-family: PingFang SC;
        display: block;
      }
    }
  }
}
</style>
