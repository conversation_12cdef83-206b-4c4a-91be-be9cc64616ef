<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-11-18 13:59:23
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-11-18 14:34:20
-->
<template>
  <div style="display: inline-block;">
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "导出指标目标值")
    }}</a-button>
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import { pureAxios } from "@/utils/requestHttp";
export default {
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      const { cmimId } = this.record;
      pureAxios({
        url: `/smc2/gjyx/newTarget/export`,
        method: "post",
        responseType: "blob",
        data: {
          cmimId,
          flag: ""
        }
      })
        .then(response => {
          if (!response.data) {
            return;
          }
          const fileName = response.headers["content-disposition"].split(
            "filename="
          )[1];
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message
          });
        });
    }
  }
};
</script>
