/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
!function(a){"object"==typeof module&&"object"==typeof module.exports?module.exports=a(require("@grapecity/spread-sheets")):"function"==typeof define&&define.amd?define(["@grapecity/spread-sheets"],a):"object"==typeof exports?exports.Spread=a(require("@grapecity/spread-sheets")):a(GC)}(function(a){a="object"==typeof a?a:{},a.Spread=a.Spread||{},a.Spread.Sheets=a.Spread.Sheets||{},a.Spread.Sheets.Barcode=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/barcode/barcodeSparkline.entry.js")}({"./dist/plugins/barcode/barcodeSparkline.entry.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/barcode/barcodeSparkline.js");b.BarcodeBase=d.BarcodeBase,b.Codabar=d.Codabar,b.Code128=d.Code128,b.Code39=d.Code39,b.Code49=d.Code49,b.Code93=d.Code93,b.DataMatrix=d.DataMatrix,b.EAN13=d.EAN13,b.EAN8=d.EAN8,b.GS1_128=d.GS1_128,b.PDF417=d.PDF417,b.QRCode=d.QRCode},"./dist/plugins/barcode/barcodeSparkline.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("CalcEngine"),f=c("Common"),g=c("Sparkline"),h=c("./dist/plugins/barcode/lib/barcode.all.js"),i=d.GC$,j=i.isEmptyObject,k=e.Functions.Function,l=f.Common.j.Fa,m=f.Common.j.C4,n=e.Convert,o=n.Ca,p=e.Errors.Value;function E(a,b){var c=b,d={},e={},f={};return c.forEach(function(b,c){var g=a[c];if(!l(g))if(b.indexOf("quietZone")!==-1)switch(b){case"quietZoneLeft":e.left=g;break;case"quietZoneRight":e.right=g;break;case"quietZoneTop":e.top=g;break;case"quietZoneBottom":e.bottom=g}else if(b.indexOf("font")!==-1)switch(b){case"fontFontFamily":f.fontFamily=g;break;case"fontFontStyle":f.fontStyle=g;break;case"fontFontWeight":f.fontWeight=g;break;case"fontTextDecoration":f.textDecoration=g;break;case"fontTextAlign":f.textAlign=g;break;case"fontFontSize":f.fontSize=g}else d[b]=g}),j(e)||(d.quietZone=e),j(f)||(d.font=f),d}function F(a,b,c,d,e){return{plotX:a+e,plotY:b+e,plotWidth:c-2*e,plotHeight:d-2*e}}function G(a,b,c,d,e,f,g){var i,j,k,l,n,o,p,q,r,s=2,t=F(c,d,e,f,s),u=m(b),v=t.plotX,w=t.plotY,x=t.plotWidth,y=t.plotHeight;u.desiredSize={width:x,height:y,forceRounding:!1};try{i=new h(null,u)}catch(a){return}j=i.getSize(),k=parseInt(i.option.font.fontSize,10),k=isNaN(k)?12:k,l=window.screen,n=l.deviceXDPI,o=window.devicePixelRatio||(n?n/l.logicalXDPI:1),i.destroy(),u.desiredSize.width=x*o,u.desiredSize.height=y*o,u.font=u.font||{},u.font.fontSize=k*o*g.zoomFactor+"px",p=h.getDataUrl(u),q=g.sheet.bt,q&&(q.ko(p)?(r=q.lo(p),a.save(),a.rect(c,d,e,f),a.clip(),a.drawImage(r,0,0,r.width,r.height,v,w,j.width,j.height),a.restore()):q.fo(p))}q=["text","color","backgroundColor"],r=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return null},b.prototype.createFunction=function(){var a,b,c=this,d=this.getBarcodeInfo();if(d)return a=new k(d.fnName,d.minimum,d.maxmum),b=d.acceptsArrayArgIndex,a.acceptsArray=function(a){return a===b},a.evaluate=function(a){var e,f;a[b]=o(a[b])?a[b].array[0]:a[b],e=c.y_a(a,d.properties),e.type=d.type;try{f=m(e),new h(null,f)}catch(a){return p}return e},a},b.prototype.paint=function(a,b,c,d,e,f,g){G(a,b,c,d,e,f,g)},b.prototype.y_a=function(a,b){return E(a,b)},b}(g.SparklineEx),b.BarcodeBase=r,s=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_QRCODE",minimum:1,maxmum:16,type:"QRCode",acceptsArrayArgIndex:9,properties:q.concat(["errorCorrectionLevel","model","version","mask","connection","connectionNo","charCode","charset","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom","quietZoneAddOn"])}},b}(r),b.QRCode=s,t=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_EAN13",minimum:1,maxmum:18,type:"EAN13",properties:q.concat(["showLabel","labelPosition","addOn","addOnLabelPosition","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.EAN13=t,u=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_EAN8",minimum:1,maxmum:15,type:"EAN8",properties:q.concat(["showLabel","labelPosition","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.EAN8=u,v=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_CODABAR",minimum:1,maxmum:17,type:"Codabar",properties:q.concat(["showLabel","labelPosition","checkDigit","nwRatio","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.Codabar=v,w=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_CODE39",minimum:1,maxmum:19,type:"Code39",properties:q.concat(["showLabel","labelPosition","labelWithStartAndStopCharacter","checkDigit","nwRatio","fullASCII","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.Code39=w,x=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_CODE93",minimum:1,maxmum:17,type:"Code93",properties:q.concat(["showLabel","labelPosition","checkDigit","fullASCII","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.Code93=x,y=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_CODE128",minimum:1,maxmum:16,type:"Code128",properties:q.concat(["showLabel","labelPosition","codeSet","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.Code128=y,z=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_GS1_128",minimum:1,maxmum:16,type:"GS1_128",properties:q.concat(["showLabel","labelPosition","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.GS1_128=z,A=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_CODE49",minimum:1,maxmum:17,type:"Code49",properties:q.concat(["showLabel","labelPosition","grouping","groupNo","fontFontFamily","fontFontStyle","fontFontWeight","fontTextDecoration","fontTextAlign","fontFontSize","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.Code49=A,B=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_PDF417",minimum:1,maxmum:11,type:"PDF417",properties:q.concat(["errorCorrectionLevel","rows","columns","compact","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.PDF417=B,C=function(a){D(b,a);function b(){return a.call(this)||this}return b.prototype.getBarcodeInfo=function(){return{fnName:"BC_DATAMATRIX",minimum:1,maxmum:14,type:"DataMatrix",properties:q.concat(["eccMode","ecc200SymbolSize","ecc200EncodingMode","ecc00_140SymbolSize","structuredAppend","structureNumber","fileIdentifier","quietZoneLeft","quietZoneRight","quietZoneTop","quietZoneBottom"])}},b}(r),b.DataMatrix=C;function H(a){a&&(g.w_a[a.name()]=a)}k&&(H(new s),H(new t),H(new u),H(new v),H(new w),H(new x),H(new y),H(new z),H(new A),H(new B),H(new C))},"./dist/plugins/barcode/lib/barcode.all.js":function(a,b,c){!function b(c,d){a.exports=d()}("undefined"!=typeof self?self:this,function(){return function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{configurable:!1,enumerable:!0,get:d})},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="",c(c.s=20)}([function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},b.isFunction=f,b.isWindow=g,b.isDefined=h,b.isNaN=i,b.isNumberLike=j,b.sliceString=k,b.sliceArray=l,b.str2Array=m,b.combineTruthy=n,b.convertRadix=o,b.isEven=p,b.isOdd=q,b.toNumber=r,b.getUnit=s,b.getMaxValue=t,b.assign=u,b.deepMerge=v,b.strRepeat=w,b.isInteger=x,b.fillArray=y,b.strPadStart=z,b.registerPlugin=A,b.measureText=D,b.convertUnit=G,b.fixSize2PixelDefault=H,b.getQuietZoneRelativeValue=I,b.getQuietZonePixelValue=J;function f(a){return"function"==typeof a}function g(a){return!!a&&a===a.window}function h(a){return void 0!==a}function i(a){return f(Number.isNaN)?Number.isNaN(a):a!==a}function j(a){return!i(+a)}function k(){for(var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,c=arguments[2],d=0,e=a.length;d<e;)c(a.substring(d,d+b)),d+=b}function l(){for(var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,c=arguments[2],d=0,e=a.length;d<e;)c(a.slice(d,d+b)),d+=b}function m(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return f(Array.from)?Array.from(a):Array.prototype.slice.call(a)}function n(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",b=m(a),c=[];return b.forEach(function(a){if("0"===a)c.push(0);else if(c[c.length-1]&&0!==c[c.length]){var b=c.pop();c.push(++b)}else c.push(1)}),c}function o(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return a=+a,a.toString(b)}function p(a){return a%2===0}function q(a){return a%2===1}function r(){var a,b=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof b?b:(a=parseFloat(b),i(a)?c:a)}function s(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",b=/[a-zA-Z]+/.exec(a);return b?b[0]:"px"}function t(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"length",c=0;return a.forEach(function(a){a[b]>c&&(c=a[b])}),c}function u(a){var b,c,d,e,g,h,i;for(b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];if(f(Object.assign))return Object.assign.apply(Object,[a].concat(c));if(null==a)throw new TypeError("Cannot convert undefined or null to object");for(e=Object(a),g=0;g<c.length;g++)if(h=c[g],null!=h)for(i in h)Object.prototype.hasOwnProperty.call(h,i)&&(e[i]=h[i]);return e}function v(a){if(null==a)throw new TypeError("Cannot convert undefined or null to object");for(var b=arguments.length,c=Array(b>1?b-1:0),e=1;e<b;e++)c[e-1]=arguments[e];return c.forEach(function(b){if(b)for(var c in b)Object.prototype.hasOwnProperty.call(b,c)&&("object"===d(b[c])&&"object"===d(a[c])?a[c]=v({},a[c]||{},b[c]):a[c]=b[c])}),a}function w(a,b){var c,d,e;if(f(a.repeat))return a.repeat(b);if(c=""+a,b=+b,b!=b&&(b=0),b<0)throw new RangeError("repeat count must be non-negative");if(b==1/0)throw new RangeError("repeat count must be less than infinity");if(b=Math.floor(b),0==c.length||0==b)return"";if(c.length*b>=1<<28)throw new RangeError("repeat count must not overflow maximum string size");for(d="",e=0;e<b;e++)d+=c;return d}function x(a){return f(Number.isInteger)?Number.isInteger(a):"number"==typeof a&&isFinite(a)&&Math.floor(a)===a}function y(a,b){if(f(a.fill))return a.fill(b);for(var c=0;c<a.length;c++)a[c]=b;return a}function z(a,b,c){return f(a.padStart)?a.padStart(b,c):(b>>=0,c=(void 0!==c?c:" ")+"",a.length>b?a:(b-=a.length,b>c.length&&(c+=w(c,b/c.length)),c.slice(0,b)+(a+"")))}e={};function A(a,b){e[a]=b}function B(a,b){var c=r(b.fontSize,12);return 1.4*c}function C(a,b){return e.measureText(a,b)}function D(a,c){return f(e.measureText)?(b.measureText=D=C,D(a,c)):B(a,c)}function E(a){var b=r(a,12);return b}function F(a){return j(a)?r(a,12):e.convertUnit(a)}function G(a){return f(e.convertUnit)?(b.convertUnit=G=F,G(a)):E(a)}function H(a){return j(a)?a+"px":a}function I(a){return a.originIsAbsoluteValue?0:a.relativeValue}function J(a){return a.originIsAbsoluteValue?a.pixelValue:0}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p;Object.defineProperty(b,"__esModule",{value:!0});function q(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function r(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function s(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}d=b.BadArgumentsException=function(a){s(b,a);function b(a){var c,d,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return q(this,b),c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this)),c.name="BadArgumentsException",d=JSON.stringify(a),c.message=d+" is not a valid argument. "+e,c}return b}(Error),e=b.TypeNotSupportException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="TypeNotSupportException",c.message=a+" is not support!",c}return b}(Error),f=b.NotAValidBarcodeEncoderException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="NotAValidBarcodeEncoderException",c.message=a+" is not support!",c}return b}(Error),g=b.SubclassNotImplementException=function(a){s(b,a);function b(){q(this,b);var a=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return a.name="SubclassNotImplementException",a.message="must implement by subclass!",a}return b}(Error),h=b.UnrecognizedRenderException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="UnrecognizedRenderException",c.message=a+" is not support!",c}return b}(Error),i=b.MethodNotImplementException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="MethodNotImplementException",c.message=a+" doesn't have this method!",c}return b}(Error),j=b.InvalidTextException=function(a){s(b,a);function b(){var a,c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Empty text",d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return q(this,b),a=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this)),a.name="InvalidTextException",a.message=c+" is invalid. "+d,a}return b}(Error),k=b.InvalidCharException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="InvalidCharException",c.message=a+" is invalid.",c}return b}(Error),l=b.TextTooLargeException=function(a){s(b,a);function b(){q(this,b);var a=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return a.name="TextTooLargeException",a.message="Text is too larget to encode",a}return b}(Error),m=b.ConnectionOverflowException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="ConnectionOverflowException",c.message="Max Connection Number is "+(a-1),c}return b}(Error),n=b.GroupOverflowException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="GroupOverflowException",c.message="Max Group Number is "+(a-1),c}return b}(Error),o=b.GroupSizeException=function(a){s(b,a);function b(a){q(this,b);var c=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return c.name="GroupSizeException",c.message="Group size is "+a+". The max group size is 9.",c}return b}(Error),p=b.InvalidStructureNumberException=function(a){s(b,a);function b(){q(this,b);var a=r(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return a.name="InvalidStructureNumberException",a.message="InvalidStructureNumberException.",a}return b}(Error)},function(a,b,c){"use strict";(function(a){var d,e,f,g,h,i,j,k,l,m,n;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(0),f=c(1),g=c(23),h=o(g),i=c(6),j=o(i);function o(a){return a&&a.__esModule?a:{default:a}}function p(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}k={renderType:"canvas",unitSize:"1px",color:"rgb(0,0,0)",backgroundColor:"rgb(255,255,255)",font:{fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",textDecoration:"none",textAlign:"center",fontSize:"12px"}},l={},m={},n=function(){d(a,null,[{key:"getImageData",value:function b(){var c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},d=new a(c);return d.getImageData()}},{key:"getDataUrl",value:function b(){var c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},d=new a(c);return d.getDataUrl()}},{key:"setDefaultOptions",value:function a(){var b=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l=(0,e.deepMerge)({},b)}},{key:"registerEncoder",value:function b(c,d){m[c]=d,a.supportType.push(c)}},{key:"registerPlugin",value:function a(b,c){(0,e.registerPlugin)(b,c)}}]);function a(){var b,c,d,g,h,i;for(p(this,a),b=void 0,c=void 0,d=void 0,g=arguments.length,h=Array(g),i=0;i<g;i++)h[i]=arguments[i];if(h.length>=3)b=h[0],c=h[1],d=h[2];else if(2===h.length)(0,e.isFunction)(h[1])?(c=h[0],d=h[1]):(b=h[0],c=h[1]);else{if(1!==h.length)throw new f.BadArgumentsException(h);c=h[0]}"string"==typeof b?this.dom=document.querySelector(b):this.dom=b,this.callback=d&&d.bind(this),this.setOption(c)}return d(a,[{key:"mergeOption",value:function a(b){return this.option=(0,e.deepMerge)({},this.option,b),this.update(),this}},{key:"setOption",value:function a(b){return this.option=(0,e.deepMerge)({},k,l,b),this.update(),this}},{key:"update",value:function a(){var b=this.option,c=m[b.type];if(!c)throw new f.TypeNotSupportException(b.type);this.encoder=new c(b),this.render&&(this.render.destroy(),this.render=null),this.render=new h.default(this.dom,this.encoder),this.render.render(),(0,e.isFunction)(this.callback)&&this.callback()}},{key:"getImageData",value:function a(){return this.render.getImageData()}},{key:"getDataUrl",value:function a(){return this.render.getDataUrl()}},{key:"getSize",value:function a(){return this.render.size}},{key:"destroy",value:function a(){this.render&&this.render.destroy()}}]),a}(),b.default=n,n.supportType=[],n.constants=j.default,(0,e.isWindow)(a)&&(a.Barcode=n)}).call(b,c(22))},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;Object.defineProperty(b,"__esModule",{value:!0}),d=c(1),e=c(0),f=c(13),g=u(f);function u(a){return a&&a.__esModule?a:{default:a}}h={version:"auto",errorCorrectionLevel:"L",model:2,mask:"auto",connection:!1,connectionNo:0,charCode:void 0,charset:"UTF-8",quietZone:{top:4,left:4,right:4,bottom:4}},i={ECI:7,Numeric:1,Alphanumeric:2,"8BitByte":4,Kanji:8,StructuredAppend:3,FNC1First:5,FNC2Second:9,Terminator:0},j={L:1,M:0,Q:3,H:2};function v(a){return a>=48&&a<=57}function w(a){var b=" $%*+-./:";return!!b.match(String.fromCharCode(a))||(a>=48&&a<=57||a>=65&&a<=90)}function x(a){return a>=0&&a<=255}function y(a){return a>=33087&&a<=64587}k={Numeric:v,Alphanumeric:w,"8BitByte":x,Kanji:y};function z(a,b){var c=k[a];return 1===b.length?c(b):b.every(function(a){return c(a)})}function A(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"UTF-8";return v(a)?"Numeric":w(a)?"Alphanumeric":"Shift_JIS"===b&&y(a)?"Kanji":"8BitByte"}l=function a(b){return 4*b+17};function B(a){return a>=1&&a<=9?{Numeric:10,Alphanumeric:9,"8BitByte":8,Kanji:8}:a>=10&&a<=26?{Numeric:12,Alphanumeric:11,"8BitByte":16,Kanji:10}:{Numeric:14,Alphanumeric:13,"8BitByte":16,Kanji:12}}m={" ":36,$:37,"%":38,"*":39,"+":40,"-":41,".":42,"/":43,":":44};function C(a){if(a>=48&&a<=57)return+String.fromCharCode(a);if(a>=65&&a<=90)return a-65+10;var b=m[String.fromCharCode(a)];if(b)return b;throw new d.InvalidTextException(String.fromCharCode(a))}function D(a){var b,c=[];for(b=0;b<a;b++)c.push((0,e.fillArray)(Array(a),null));return c}n=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]];function E(a){return n[a-1]}o=236,p=17,q=[function(a,b){return(a+b)%2===0},function(a){return a%2===0},function(a,b){return b%3===0},function(a,b){return(a+b)%3===0},function(a,b){return(Math.floor(a/2)+Math.floor(b/3))%2===0},function(a,b){return a*b%2+a*b%3===0},function(a,b){return(a*b%2+a*b%3)%2===0},function(a,b){return(a*b%3+(a+b)%2)%2===0}],r=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],s=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,46,36],[1,46,30],[1,46,24],[1,46,16],[1,72,57],[1,72,44],[1,72,36],[1,72,24],[1,100,80],[1,100,60],[1,100,50],[1,100,34],[1,134,108],[1,134,82],[1,134,68],[2,67,23],[1,170,136],[2,85,53],[2,85,43],[2,85,29],[1,212,170],[2,106,66],[2,106,54],[3,70,24],[2,128,104],[2,128,80],[2,128,64],[3,85,29],[2,153,123],[2,153,93],[3,102,52],[3,102,34],[2,179,145],[2,179,111],[3,119,61],[4,89,31],[2,208,168],[4,104,64],[4,104,52],[5,83,29],[2,238,192],[4,119,73],[4,119,61],[5,95,33],[3,180,144],[4,135,83],[4,135,69],[6,90,32],[3,203,163],[4,152,92],[5,122,62],[6,101,35]];function F(a,b){var c,d,e,f,g,h,i,j=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,k=void 0;switch(b){case 1:k=0;break;case 0:k=1;break;case 3:k=2;break;case 2:k=3}for(c=2===j?r:s,d=c[4*(a-1)+k],e=[],f=d.length/3,g=0;g<f;g++)for(h=d[3*g],i=0;i<h;i++)e.push({total:d[3*g+1],data:d[3*g+2],ec:d[3*g+1]-d[3*g+2]});return e}function G(a){return q[a]}function H(a){return a.reduce(function(a,b){return a.push(b.slice(0)),a},[])}function I(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s=a.length,t=0;for(b=0;b<s;b++)for(c=0;c<s;c++){for(d=0,e=a[b][c],f=-1;f<=1;f++)if(!(b+f<0||s<=b+f))for(g=-1;g<=1;g++)c+g<0||s<=c+g||0==f&&0==g||e==a[b+f][c+g]&&(d+=1);d>5&&(t+=3+d-5)}for(h=0;h<s-1;h++)for(i=0;i<s-1;i++)j=0,a[h][i]&&(j+=1),a[h+1][i]&&(j+=1),a[h][i+1]&&(j+=1),a[h+1][i+1]&&(j+=1),0!=j&&4!=j||(t+=3);for(k=0;k<s;k++)for(l=0;l<s-6;l++)a[k][l]&&!a[k][l+1]&&a[k][l+2]&&a[k][l+3]&&a[k][l+4]&&!a[k][l+5]&&a[k][l+6]&&(t+=40);for(m=0;m<s;m++)for(n=0;n<s-6;n++)a[n][m]&&!a[n+1][m]&&a[n+2][m]&&a[n+3][m]&&a[n+4][m]&&!a[n+5][m]&&a[n+6][m]&&(t+=40);for(o=0,p=0;p<s;p++)for(q=0;q<s;q++)a[q][p]&&(o+=1);return r=Math.abs(100*o/s/s-50)/5,t+=10*r}function J(a,b,c,d){var f,h,i=a.length,j=H(a),k=(0,e.strPadStart)((0,e.convertRadix)(c,2),2,0),l=(0,e.strPadStart)((0,e.convertRadix)(b,2),3,0),m=k+l,n=g.default.getBCH15(parseInt(m,2),d);for(j[i-8][8]=1,f=15;f>0;f--)h=n>>f-1&1,f>9?(j[8][15-f]=h,j[i-1-15+f][8]=h):f>8?(j[8][15-f+1]=h,j[i-1-15+f][8]=h):f>6?(j[f][8]=h,j[8][i-f]=h):(j[f-1][8]=h,j[8][i-f]=h);return j}function K(a,b,c){var e,f,g,h,i=0,j=0,k=0,l=0;for(b.forEach(function(a){v(a)?i++:w(a)?j++:y(a)?l++:x(a)&&k++}),e=Math.ceil((5*j+3*i+13*l+8*k)/8),f=1;f<=40;f++)if(g=F(f,a,c),h=g.reduce(function(a,b){return a+=b.data},0),e<=h)return f;throw new d.TextTooLargeException}t={Alphanumeric:[[6,11],[7,15],[8,16]],Numeric:[[4,6,6,13],[4,7,8,15],[5,8,9,17]]};function L(a,b){var c=t[a];return b<=9?c[0]:b<=26?c[1]:c[2]}function M(a){var b,c,d,e=[];for(b=0,c=a.length;b<c;b++)d=a[b],d<128?e.push(d):d<2048?e.push(192|d>>6,128|63&d):d<55296||d>=57344?e.push(224|d>>12,128|d>>6&63,128|63&d):(b++,d=65536+((1023&d)<<10|1023&a[b]),e.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|63&d));return e}function N(a){var b,c,d=M(a),e=d[0];for(b=1,c=d.length;b<c;b++)e^=d[b];return e}function O(a){var b=[];return(0,e.sliceString)(a,1,function(a){b.push(a.charCodeAt(0))}),b}b.default={defaultConfig:h,MODE_INDICATOR:i,isMode:z,getSizeByVersion:l,getCharacterCountIndicatorbitsNumber:B,getAlphanumericCharValue:C,createModules:D,getMaskFunc:G,getAlignmentPattersPos:E,getErrorCorrectionCharacteristics:F,padCodewords0:o,padCodewords1:p,getMaskScore:I,maskFuncs:q,addFormatInformation:J,getEstimatedVersion:K,getCharMode:A,EC_INDICATOR:j,getModeCheckInfo:L,getParityData:N,utf8Encode:M,getCharCode:O}},function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(10),f=i(e),g=c(0);function i(a){return a&&a.__esModule?a:{default:a}}function j(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function k(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function l(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}h=function(a){l(b,a);function b(){return j(this,b),k(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}return d(b,[{key:"adjustDesiredSize",value:function a(b){var c,d,e,f=this.config,h=f.desiredSize,i=f.showLabel,j=this.quietZone,k=this.fontHeight,l=this.containerWidth,m=this.containerHeight;h&&(c=b.length+(0,g.getQuietZoneRelativeValue)(j.left)+(0,g.getQuietZoneRelativeValue)(j.right),l=l-(0,g.getQuietZonePixelValue)(j.left)-(0,g.getQuietZonePixelValue)(j.right),d=void 0,e=void 0,h.forceRounding?(d=~~(l/c),e=d<1?1:d):d=e=l/c,this.style.unitValue=e,this.style.fontSizeInUnit=k/e,m=i?m-k:m,m=m-(0,g.getQuietZonePixelValue)(j.top)-(0,g.getQuietZonePixelValue)(j.bottom),this.height=m/e-(0,g.getQuietZoneRelativeValue)(j.top)-(0,g.getQuietZoneRelativeValue)(j.bottom),Object.keys(j).forEach(function(a){j[a].originIsAbsoluteValue&&(j[a].relativeValue=j[a].pixelValue/e)}))}},{key:"convertToShape",value:function a(b){var c,d,e,f,h,i,j=this.label,k=this.quietZone,l=this.isLabelBottom,m=this.height,n=this.config.showLabel,o=this.style,p=o.fontSizeInUnit,q=o.textAlign;if(n||(p=0),c=[],d=k.left.relativeValue,e=k.top.relativeValue,f=void 0,l?f=e+m:(f=e,e+=p),(0,g.combineTruthy)(b).forEach(function(a){0!==a?(c.push({type:"rect",x:d,y:e,width:a,height:m}),d+=a):d++}),n){switch(h=k.left.relativeValue,i=b.length,q){case"center":h+=i/2;break;case"right":h+=i}c.push({type:"text",x:h,y:f,text:j,textAlign:q,
maxWidth:i})}l?this.size={width:d+k.right.relativeValue,height:f+p+k.bottom.relativeValue}:this.size={width:d+k.right.relativeValue,height:e+m+k.bottom.relativeValue},this.shapes=c}}]),b}(f.default),b.default=h},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n;Object.defineProperty(b,"__esModule",{value:!0}),d=c(1),e=c(0),f=c(6),g=o(f);function o(a){return a&&a.__esModule?a:{default:a}}h=[" ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/","0","1","2","3","4","5","6","7","8","9",":",";","<","=",">","?","@","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","[","\\","]","^","_","\0","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","\t","\n","\v","\f","\r","\x0e","\x0f","\x10","\x11","\x12","\x13","\x14","\x15","\x16","\x17","\x18","\x19","\x1a","\x1b","\x1c","\x1d","\x1e","\x1f"," "," "," "," "," "," "," "," "," "," "],i=[" ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/","0","1","2","3","4","5","6","7","8","9",":",";","<","=",">","?","@","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","[","\\","]","^","_","`","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","{","|","}","~","\x7f"," "," "," "," "," "," "," "," "," "," "],j=["212222","222122","222221","121223","121322","131222","122213","122312","132212","221213","221312","231212","112232","122132","122231","113222","123122","123221","223211","221132","221231","213212","223112","312131","311222","321122","321221","312212","322112","322211","212123","212321","232121","111323","131123","131321","112313","132113","132311","211313","231113","231311","112133","112331","132131","113123","113321","133121","313121","211331","231131","213113","213311","213131","311123","311321","331121","312113","312311","332111","314111","221411","431111","111224","111422","121124","121421","141122","141221","112214","112412","122114","122411","142112","142211","241211","221114","413111","241112","134111","111242","121142","121241","114212","124112","124211","411212","421112","421211","212141","214121","412121","111143","111341","131141","114113","114311","411113","411311","113141","114131","311141","411131","211412","211214","211232"],k="2331112",l={CodeC:99,CodeB:100,CodeA:101,FNC1:102,FNC2:97,FNC3:96,StartA:103,StartB:104,StartC:105},m={CodeC:String.fromCharCode(204),CodeB:String.fromCharCode(205),CodeA:String.fromCharCode(206),FNC1:g.default.FNC1,FNC2:g.default.FNC2,FNC3:g.default.FNC3,StartA:String.fromCharCode(208),StartB:String.fromCharCode(209),StartC:String.fromCharCode(210)};function p(a,b){if(a===m.FNC1)return l.FNC1;if(a===m.FNC2)return l.FNC2;if(a===m.FNC3)return l.FNC3;var c=void 0;switch(b){case"A":return c=h.indexOf(a);case"B":return c=i.indexOf(a);case"C":return+a}}function q(a,b){var c=j[p(a,b)];if(!c)throw new d.InvalidCharException(a);return c}function r(a){return j[a]}n={height:60,showLabel:!0,codeSet:"auto",quietZone:{top:0,right:10,bottom:0,left:10},labelPosition:"bottom"};function s(a){var b="";return(0,e.str2Array)(a).forEach(function(a,c){b+=(0,e.isEven)(c)?(0,e.strRepeat)("1",+a):(0,e.strRepeat)("0",+a)}),b}b.default={getCharValue:p,getCharPattern:q,getPatternByIndex:r,Code128Sym:l,stopPattern:k,defaultConfig:n,Code128Char:m,encode:s}},function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d="\xcf",e="\xca",f="\xc9",g="\u2000",h="\u2004",i="\u2005",b.default={FNC1:d,FNC2:e,FNC3:f,DataMatrixFNC1:g,DataMatrixMacro05:h,DataMatrixMacro06:i}},function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(10),f=i(e),g=c(0);function i(a){return a&&a.__esModule?a:{default:a}}function j(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function k(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function l(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}h=function(a){l(b,a);function b(){return j(this,b),k(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}return d(b,[{key:"adjustDesiredSize",value:function a(b){var c,d,e,f,h,i=this.config,j=i.desiredSize,k=i.showLabel,l=this.quietZone,m=this.fontHeight,n=this.containerWidth,o=this.containerHeight;j&&(c=b[0].length+(0,g.getQuietZoneRelativeValue)(l.left)+(0,g.getQuietZoneRelativeValue)(l.right),d=b.length+(0,g.getQuietZoneRelativeValue)(l.top)+(0,g.getQuietZoneRelativeValue)(l.bottom),n=n-(0,g.getQuietZonePixelValue)(l.left)-(0,g.getQuietZonePixelValue)(l.right),o=o-(0,g.getQuietZonePixelValue)(l.top)-(0,g.getQuietZonePixelValue)(l.bottom),e=void 0,f=void 0,h=void 0,j.forceRounding?(e=~~(n/c),f=~~(o/d),e=e<1?1:e,f=f<1?1:f):(e=n/c,f=o/d),this.style.unitValue=h=Math.min(e,f),this.style.fontSizeInUnit=m/h,o=k?o-m:o,this.height=o/h-(0,g.getQuietZoneRelativeValue)(l.top)-(0,g.getQuietZoneRelativeValue)(l.bottom),Object.keys(l).forEach(function(a){l[a].originIsAbsoluteValue&&(l[a].relativeValue=l[a].pixelValue/h)}))}},{key:"convertToShape",value:function a(b){var c=this.quietZone,d=[],e=c.top.relativeValue;b.forEach(function(a){var b=c.left.relativeValue;a.forEach(function(a){a&&d.push({type:"rect",x:b,y:e,width:1,height:1}),b++}),e++}),this.shapes=d,this.size={width:b[0].length+c.left.relativeValue+c.right.relativeValue,height:e+c.bottom.relativeValue}}}]),b}(f.default),b.default=h},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;Object.defineProperty(b,"__esModule",{value:!0}),d=c(0),e=c(1),f=c(6),g=u(f);function u(a){return a&&a.__esModule?a:{default:a}}h={FNC1Input:g.default.DataMatrixFNC1.charCodeAt(0),Macro05Input:g.default.DataMatrixMacro05.charCodeAt(0),Macro06Input:g.default.DataMatrixMacro06.charCodeAt(0),StructuredAppand:233,FileIdentifierMax:254,ASCIIPad:129,ASCIIUpperShift:235,ASCIIFNC1:232,Macro05:236,Macro06:237,TripletUppershift:158,TripletFNC1:155,TripletPad:0,InvalidTripletValue:255,LatchToC40:230,LatchToBase256:231,LatchToX12:238,LatchToText:239,LatchToEDIFACT:240,TripletUnlatch:254,EDIFACTUnlatch:31,PseudoRandomSeed:149,PadRandomBase:253,Base256RandomBase:255,Base256SmallBlockSize:249,EDIFACTMask:63,Unvisited:255,TripletShifts:[255,0,1,2],MaxCodeWrods:2178,MaxStructures:16,MaxLookAheadOffset:50},i={keys:["square10","square12","square14","square16","square18","square20","square22","square24","square26","square32","square36","square40","square44","square48","square52","square64","square72","square80","square88","square96","square104","square120","square132","square144","rectangular8x18","rectangular8x32","rectangular12x26","rectangular12x36","rectangular16x36","rectangular16x48"],values:[[10,10,3,5,1,1,8,8],[12,12,5,7,1,1,10,10],[14,14,8,10,1,1,12,12],[16,16,12,12,1,1,14,14],[18,18,18,14,1,1,16,16],[20,20,22,18,1,1,18,18],[22,22,30,20,1,1,20,20],[24,24,36,24,1,1,22,22],[26,26,44,28,1,1,24,24],[32,32,62,36,1,4,14,14],[36,36,86,42,1,4,16,16],[40,40,114,48,1,4,18,18],[44,44,144,56,1,4,20,20],[48,48,174,68,1,4,22,22],[52,52,204,84,2,4,24,24],[64,64,280,112,2,16,14,14],[72,72,368,144,4,16,16,16],[80,80,456,192,4,16,18,18],[88,88,576,224,4,16,20,20],[96,96,696,272,4,16,22,22],[104,104,816,336,6,16,24,24],[120,120,1050,408,6,36,18,18],[132,132,1304,496,8,36,20,20],[144,144,1558,620,10,36,22,22],[8,18,5,7,1,1,6,16],[8,32,10,11,1,2,6,14],[12,26,16,14,1,1,10,24],[12,36,22,18,1,2,10,16],[16,36,32,24,1,2,14,16],[16,48,49,28,1,2,14,22]]};function v(a){var b=i.values[i.keys.indexOf(a)];if(!b)throw new e.BadArgumentsException({ecc200SymbolSize:a});return{symbolRows:b[0],symbolColumns:b[1],symbolDataCapacity:b[2],eccLength:b[3],interleavedBlocks:b[4],regions:b[5],regionRows:b[6],regionColumns:b[7]}}function w(a,b){if("squareAuto"!==a&&"rectangularAuto"!==a)return v(a);for(var c="squareAuto"==a?0:24;c<i.keys.length;++c)if(i.values[c][2]>=b)return v(i.keys[c]);throw new e.TextTooLargeException}function x(a){var b=void 0,c=void 0;switch(a){case 1:case 2:b=1;break;case 4:b=2;break;case 16:b=4;break;case 36:b=6}return c=~~(a/b),{rowOfRegion:b,colOfRegion:c}}function y(a,b){var c,e;for(b||(b=a),c=[],e=0;e<a;e++)c.push((0,d.fillArray)(Array(b),null));return c}j=158,k=155,l=127,m=128,n=32,o=48,p=57,q=97,r=122,s=65,t=90;function z(a){return a>l?a==h.FNC1Input?"FNC1":"ExtendedASCII":a>=o&&a<=p?"Numeric":a>=q&&a<=r?"LowerCasedLetter":a>=s&&a<=t?"UpperCasedLetter":"ASCIIOther"}function A(a){var b=z(a);switch(b){case"Numeric":return a-44;case"UpperCasedLetter":return a-51;default:switch(a){case 13:return 0;case 42:return 1;case 62:return 2;case 32:return 3}}return h.InvalidTripletValue}function B(a,b){if("X12"==a)return A(b);if(b<32)return b;if(32==b)return 3;if(b<48)return b-33;if(b<58)return b-44;if(b<65)return b-43;if(b<91)return"C40"==a?b-51:b-64;if(b<96)return b-69;if(96==b)return 0;if(b<123)return"C40"==a?b-96:b-83;if(b<128)return b-96;if(b==k||b==j)return b-128;throw new e.InvalidCharException(String.fromCharCode(b))}function C(a){return a>=o&&a<=p}function D(a,b){return"X12"===a?0:b<32?1:32==b?0:b<48?2:b<58?0:b<65?2:b<91?"C40"==a?0:3:b<96?2:96==b?3:b<123?"C40"===a?3:0:b==h.TripletFNC1||b==h.TripletUppershift?2:3}function E(a,b){var c,d=[];if(b==h.FNC1Input){if("X12"==a)throw new e.InvalidCharException("FNC1");b=h.TripletFNC1}else if(b>l){if("C40"!=a&&"Text"!=a)throw new e.InvalidCharException(String.fromCharCode(b));d.push(h.TripletShifts[2]),d.push(B(a,h.TripletUppershift)),b-=m}return c=D(a,b),c>0&&d.push(h.TripletShifts[c]),d.push(B(a,b)),d}function F(a){return a<32||a>94?h.InvalidTripletValue:a&h.EDIFACTMask}function G(a,b,c){var d=a+h.PseudoRandomSeed*b%c+1;return d>c+1?d-c-1:d}function H(a){var b,c,e=[];if(a.length>0){for(b=a[0],c=1;c<a.length;++c)a[c].charSet!=b.charSet?(b.length>0&&e.push((0,d.assign)({},b)),b.charSet=a[c].charSet,b.start=a[c].start,b.length=a[c].length):b.length+=a[c].length;b.length>0&&e.push(b)}return e}b.default={getSymbolInfo:v,getInfoOfRegions:x,createModules:y,CONSTANTS:h,getTripletCharValue:B,getRandomizedValue:G,getCharType:z,getSuitableSymbolSize:w,isDigit:C,getTripletCharSetChannel:D,getX12Value:A,getEDIFACTValue:F,getTripletEncodeValues:E,mergeUnits:H,TripletUppershift:j,TripletFNC1:k,ASCIIMax:l,ExtendedASCIIMin:m,Space:n,NumericMin:o,NumericMax:p,LowerCasedLetterMin:q,LowerCasedLetterMax:r,UpperCasedLetterMin:s,UpperCasedLetterMax:t,setRegionData:J,setFinder:I};function I(a,b,c,e){var f,g,h,i,j,k=b.regionColumns,l=b.regionRows;for(l+=2,k+=2,c*=l,e*=k,f=a[c],g=e;g<e+k;g++)(0,d.isEven)(g)?f[g]=1:f[g]=0;for(h=a[c+l-1],i=e;i<e+k;i++)h[i]=1;for(j=c;j<c+l;j++)a[j][e]=1,(0,d.isEven)(j)?a[j][e+k-1]=0:a[j][e+k-1]=1}function J(a,b,c,d,e){var f,g,h=b.regionColumns,i=b.regionRows,j=c*(i+2)+1,k=d*(h+2)+1,l=c*i,m=d*h;for(f=0;f<i;++f)for(g=0;g<h;++g)a[j+f][k+g]=e[l+f][m+g]}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o;Object.defineProperty(b,"__esModule",{value:!0}),d="101",e="01010",f="1011",g="01",h={A:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],B:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],C:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"]},i=["AAAAAA","AABABB","AABBAB","AABBBA","ABAABB","ABBAAB","ABBBAA","ABABAB","ABABBA","ABBABA"],j="CCCCCC",k="AAAA",l=["BBAAA","BABAA","BAABA","BAAAB","ABBAA","AABBA","AAABB","ABABA","ABAAB","AABAB"],m={height:60,showLabel:!0,labelPosition:"bottom",addOnHeight:"auto",addOnLabelPosition:"top",quietZone:{top:0,right:7,bottom:0,left:11,addOn:5}},n=function a(b){switch(b=parseInt(b),b%4){case 0:return{leftStructure:"A",rightStructure:"A"};case 1:return{leftStructure:"A",rightStructure:"B"};case 2:return{leftStructure:"B",rightStructure:"A"};case 3:return{leftStructure:"B",rightStructure:"B"}}},o=function a(b){var c,d,e,f,g;return b+="",c=+b[0]+ +b[2]+ +b[4],d=3*c,e=+b[1]+ +b[3],f=9*e,g=d+f,l[g%10]},b.default={NORMAL_GUARD:d,CENTRE_GUARD:e,TABLE:h,leftStructure:i,rightStructure:j,defaultConfig:m,get2DigitAddOnTable:n,ean8LeftStructure:k,ADD_ON_DELINEATOR:g,get5DigitAddOnTable:o,ADD_ON_GUARD:f}},function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(1),f=c(0);function h(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}g=function(){function a(b){var c,d,e,g,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h(this,a),this.config=(0,f.deepMerge)({},w,b),c=this.config,d=c.text,e=c.quietZone,g=c.unitSize,i=c.height,j=c.backgroundColor,k=c.color,l=c.renderType,m=c.labelPosition,n=c.desiredSize,o=c.showLabel,p=c.font,(0,f.isDefined)(d)&&(this.config.text+=""),(0,f.isDefined)(m)&&(this.isLabelBottom="top"!==m),g=(0,f.fixSize2PixelDefault)(g),p.fontSize=(0,f.fixSize2PixelDefault)(p.fontSize),q=(0,f.convertUnit)(g),r=(0,f.measureText)(d,p),this.fontHeight=r,s=o?r/q:0,this.style=(0,f.assign)({},p,{backgroundColor:j,color:k,fontSizeInUnit:s,unitValue:q,renderType:l,fontHeight:r}),e=(0,f.assign)({},e);for(t in e)e.hasOwnProperty(t)&&(u=e[t],(0,f.isNumberLike)(u)?e[t]={relativeValue:+u,originIsAbsoluteValue:!1}:(v=(0,f.convertUnit)(u),e[t]={relativeValue:v/q,originIsAbsoluteValue:!0,pixelValue:v}));this.quietZone=e,(0,f.isDefined)(i)&&((0,f.isNumberLike)(i)?this.height=+i-s-e.top.relativeValue-e.bottom.relativeValue:this.height=(0,f.convertUnit)(i)/q-s-e.top.relativeValue-e.bottom.relativeValue),n&&(this.containerWidth=(0,f.convertUnit)((0,f.fixSize2PixelDefault)(n.width)),this.containerHeight=(0,f.convertUnit)((0,f.fixSize2PixelDefault)(n.height))),this.shapes=[],this.size={width:0,height:0},this.validate()}return d(a,[{key:"validate",value:function a(){throw new e.SubclassNotImplementException}},{key:"adjustDesiredSize",value:function a(){throw new e.SubclassNotImplementException}},{key:"convertToShape",value:function a(){throw new e.SubclassNotImplementException}}]),a}(),b.default=g},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(2),f=t(e),g=c(4),h=t(g),i=c(5),j=t(i),k=c(27),l=t(k),m=c(28),n=t(m),o=c(29),p=t(o),q=c(30),r=t(q);function t(a){return a&&a.__esModule?a:{default:a}}function u(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function v(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function w(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}s=function(a){w(b,a);function b(a){var c,d,e,f=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return u(this,b),c=v(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,j.default.defaultConfig)),d=c.config.text,c.isUccEan128=f,f&&d[0]!==j.default.Code128Char.FNC1&&(d=j.default.Code128Char.FNC1+d),c.text=d,c.label=d.replace(/[^\x20-\x7E]/g,""),e=c.calculateData(),c.adjustDesiredSize(e),c.convertToShape(e),c}return d(b,[{key:"validate",value:function a(){}},{key:"calculateData",value:function a(){var b=this.config,c=this.text,d=this.isUccEan128,e=void 0;if(d)e=new r.default(c);else switch(b.codeSet){case"A":e=new l.default(c);break;case"B":e=new n.default(c);break;case"C":e=new p.default(c);break;default:e=new r.default(c)}return e.getData()}}]),b}(h.default),b.default=s,f.default.registerEncoder("Code128",s)},function(a,b,c){"use strict";var d,e,f,g,h,i,j;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(4),f=k(e),g=c(0),h=c(9),i=k(h);function k(a){return a&&a.__esModule?a:{default:a}}function l(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function m(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function n(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}j=function(a){n(b,a);function b(a){var c,d,e;return l(this,b),c=m(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,i.default.defaultConfig)),d=c.style.textAlign,e=c.config.addOnLabelPosition,c.isTextGroup="group"===d,c.isAddOnLabelBottom="top"!==e,c}return d(b,[{key:"encode",value:function a(b,c){var d=(0,g.str2Array)(c),e=(0,g.str2Array)(b);return e.reduce(function(a,b,c){var e=i.default.TABLE[d[c]];return a+=e[b]},"")}},{key:"encodeChar",value:function a(b,c,d){var e=(0,g.str2Array)(c),f=i.default.TABLE[e[d]];return f[b]}},{key:"checksum",value:function a(b){var c=arguments.length>1&&void 0!==arguments[1]&&arguments[1],d=(0,g.str2Array)(b),e=c?g.isOdd:g.isEven,f=d.reduce(function(a,b,c){return b=+b,a+=e(c)?b:3*b},0),h=f%10;return 0===h?0:10-h}},{key:"convertToShape",value:function a(b){var c,d,e,f,h,i,j,k,l,m,n=this.isTextGroup,o=this.isLabelBottom,p=this.addOnHeight,q=this.isAddOnLabelBottom,r=this.height,s=this.quietZone,t=this.config.showLabel,u=this.style,v=u.fontSizeInUnit,w=u.textAlign;t||(v=0),p=p||0,c=[],d=void 0,e=s.left.relativeValue,f=s.top.relativeValue,h=void 0,i=void 0,j=void 0,k=void 0,l=void 0,m=void 0,o?(d=r+5,h=f+r,i=t?r+v:d,q?(j=f,l="auto"===p?t?r:d:p,k=j+l):(k=f,j=f+v,l="auto"===p?d-v:p)):(d=r,r-=5,h=f,f+=v,i=t?d+v:d,q?(j=s.top.relativeValue,l="auto"===p?t?r:d:p,k=j+l):(k=s.top.relativeValue,j=s.top.relativeValue+v,l="auto"===p?d:p)),m=t?l+v:l,b.forEach(function(a){var b,i=f,m=r,o=h,p=n?"center":w,q=void 0,u=e;switch(a.role){case"GUARD":m=d,q=a.binary.length;break;case"ADDON":i=j,m=l,o=k,q=a.binary.length;break;case"LEFT_QUIET_ZONE":p="left",q=s.left.relativeValue,u=0;break;case"RIGHT_QUIET_ZONE":case"NO_ADDON_RIGHT_QUIET_ZONE":p="right",q=s.right.relativeValue;break;case"ADDON_QUIET_ZONE":q=s.addOn.relativeValue;break;case"ADDON_RIGHT_QUIET_ZONE":p="right",o=k,q=s.right.relativeValue;break;default:q=a.binary.length}if(t&&a.text){switch(p){case"center":u+=q/2;break;case"right":u+=q}b={type:"text",x:u,y:o,text:a.text,textAlign:p,maxWidth:q},"NO_ADDON_RIGHT_QUIET_ZONE"!==a.role&&"ADDON_RIGHT_QUIET_ZONE"!==a.role||(b.fontStyle="normal",b.fontWeight="normal",b.textDecoration="none"),c.push(b)}a.binary?(0,g.combineTruthy)(a.binary).forEach(function(a){0!==a?(c.push({type:"rect",x:e,y:i,width:a,height:m}),e+=a):e++}):"ADDON_QUIET_ZONE"===a.role&&(e+=q)}),this.size={width:e+s.right.relativeValue,height:Math.max(i,m)+s.top.relativeValue+s.bottom.relativeValue},this.shapes=c}},{key:"adjustDesiredSize",value:function a(b){var c,d,e,f=this.config,h=f.desiredSize,i=f.showLabel,j=f.addOn,k=f.addOnHeight,l=this.fontHeight,m=this.containerWidth,n=this.containerHeight,o=this.addOnHeightInPiexl,p=this.quietZone;h&&(c=b.reduce(function(a,b){return b.binary&&(a+=b.binary.length),a},0),c=c+(0,g.getQuietZoneRelativeValue)(p.left)+(0,g.getQuietZoneRelativeValue)(p.right),j&&(c+=(0,g.getQuietZoneRelativeValue)(p.addOn),m-=(0,g.getQuietZonePixelValue)(p.addOn)),m=m-(0,g.getQuietZonePixelValue)(p.left)-(0,g.getQuietZonePixelValue)(p.right),d=void 0,e=void 0,h.forceRounding?(d=~~(m/c),e=d<1?1:d):d=e=m/c,this.style.unitValue=e,this.style.fontSizeInUnit=l/e,n=i?n-l:n,n=n-(0,g.getQuietZonePixelValue)(p.top)-(0,g.getQuietZonePixelValue)(p.bottom),this.height=n/e-(0,g.getQuietZoneRelativeValue)(p.top)-(0,g.getQuietZoneRelativeValue)(p.bottom),Object.keys(p).forEach(function(a){p[a].originIsAbsoluteValue&&(p[a].relativeValue=p[a].pixelValue/e)}),(0,g.isDefined)(j)&&"auto"!==k&&(k=(0,g.isNumberLike)(k)?+k:o/e,k>this.height?this.addOnHeight=this.height:this.addOnHeight=k))}}]),b}(f.default),b.default=j},function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=1335,e=7973,f=21522,g=10277;function h(a){for(var b=0;0!=a;)b+=1,a>>>=1;return b}function i(a,b){for(var c=2===b?f:g,e=a<<10;h(e)-h(d)>=0;)e^=d<<h(e)-h(d);return(a<<10|e)^c}function j(a){for(var b=a<<12;h(b)-h(e)>=0;)b^=e<<h(b)-h(e);return a<<12|b}b.default={getBCH15:i,getBCH18:j}},function(a,b,c){"use strict";var d,e,f,g,h,i,j;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(3),f=k(e),g=c(55),h=k(g),i=c(1);function k(a){return a&&a.__esModule?a:{default:a}}function l(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}j=function(){function a(b){var c,d,e,g,h;l(this,a),c=b.version,d=b.model,e=b.text,g=b.charCode,this.charCode=g?g:f.default.getCharCode(e),h=f.default.EC_INDICATOR[b.errorCorrectionLevel],this.config=b,this.errorCorrectionLevel=h,this.model=+d,c="auto"===c?this.getAutoVersion():+c,this.version=c,this.modulesCount=f.default.getSizeByVersion(c),this.charCountIndicatorBitsNumber=f.default.getCharacterCountIndicatorbitsNumber(c),this.errorCorrectionCharacteristics=f.default.getErrorCorrectionCharacteristics(c,h,this.model),this.totalDataCount=this.errorCorrectionCharacteristics.reduce(function(a,b){return a+=b.data},0),this.totalDataBits=8*this.totalDataCount}return d(a,[{key:"getConnections",value:function a(){for(var b,c,d,e=this.totalDataBits,f=this.charCode,g=e-20,h=f.length,i=0,j=1,k=[],l=f[0];j<=h;)b=f.slice(i,j),c=this.analysisData(b),d=this.encodeData(c),d.length>g?(k.push(l),i=j-1):j===h&&k.push(b),l=b,j++;return k}},{key:"processConnection",value:function a(b){var c,d,e,f,g,h=this.totalDataBits,j=this.config,k=j.connection,l=j.connectionNo;if(l=+l,k){if(c=Math.ceil(b.length/(h-20)),l>c-1)throw new i.ConnectionOverflowException(c);return d=this.getConnections(),e=d[l],f=this.analysisData(e),g=this.encodeData(f,{connectionNo:l,connectionCnt:c})}if(b.length>h)throw new i.TextTooLargeException;return b}},{key:"padBuffer",value:function a(b){var c=this.totalDataBits;for(b.length+4<=c&&b.put(f.default.MODE_INDICATOR.Terminator,4);b.length%8!=0;)b.putBit(!1);for(;;){if(b.length>=c)break;if(b.put(f.default.padCodewords0,8),b.length>=c)break;b.put(f.default.padCodewords1,8)}}},{key:"getAutoVersion",value:function a(){var b,c,d,e=this.errorCorrectionLevel,g=this.charCode,h=this.model,j=f.default.getEstimatedVersion(e,g,h);for(b=j;b<40;b++)if(this.version=b,this.modulesCount=f.default.getSizeByVersion(this.version),this.charCountIndicatorBitsNumber=f.default.getCharacterCountIndicatorbitsNumber(this.version),this.errorCorrectionCharacteristics=f.default.getErrorCorrectionCharacteristics(this.version,e),this.totalDataCount=this.errorCorrectionCharacteristics.reduce(function(a,b){return a+=b.data},0),this.totalDataBits=8*this.totalDataCount,c=this.analysisData(g),d=this.encodeData(c),!(d.length>this.totalDataBits))return b;throw new i.TextTooLargeException}},{key:"analysisData",value:function a(b){var c,d,e,g,h,i,j,k=this.version,l=this.config.charset,m=f.default.getCharMode(b[0],l);switch(m){case"Alphanumeric":c=f.default.getModeCheckInfo(m,k),d=b.slice(1,1+c[0]),f.default.isMode("8BitByte",d)&&(m="8BitByte");break;case"Numeric":e=f.default.getModeCheckInfo(m,k),g=b.slice(1,1+e[0]),h=b.slice(1,1+e[1]),f.default.isMode("8BitByte",g)?m="8BitByte":f.default.isMode("Alphanumeric",h)&&(m="Alphanumeric")}return i={mode:m,code:[]},j=[i],b.forEach(function(a,c){var d,e,g,h=f.default.getCharMode(a,l);i.mode===h?i.code.push(a):"8BitByte"===i.mode?"Kanji"===h?(i={mode:h,code:[a]},j.push(i)):"Numeric"===h?(d=f.default.getModeCheckInfo(h,k),f.default.isMode(h,b.slice(c,c+d[2]))?(i={mode:h,code:[a]},j.push(i)):i.code.push(a)):"Alphanumeric"===h?(e=f.default.getModeCheckInfo(h,k),f.default.isMode(h,b.slice(c,c+e[1]))?(i={mode:h,code:[a]},j.push(i)):i.code.push(a)):i.code.push(a):"Alphanumeric"===i.mode?"Kanji"===h?(i={mode:h,code:[a]},j.push(i)):"Alphanumeric"===h?(i={mode:h,code:[a]},j.push(i)):"Numeric"===h?(g=f.default.getModeCheckInfo(h,k),f.default.isMode(h,b.slice(c,c+g[2]))?(i={mode:h,code:[a]},j.push(i)):i.code.push(a)):i.code.push(a):(i={mode:h,code:[a]},j.push(i))}),j}},{key:"encodeData",value:function a(){throw new i.SubclassNotImplementException}},{key:"generateErrorCorrectionCode",value:function a(b){var c=this.errorCorrectionCharacteristics,d=[],e=0;return c.forEach(function(a){var c=b.getBuffer().slice(e,e+a.data);d.push({data:c,ec:(0,h.default)(c,a.ec,a.data)}),e+=a.data}),d}},{key:"getFinalMessage",value:function a(){throw new i.SubclassNotImplementException}},{key:"setModules",value:function a(){throw new i.SubclassNotImplementException}},{key:"maskModules",value:function a(){throw new i.SubclassNotImplementException}},{key:"autoMask",value:function a(){throw new i.SubclassNotImplementException}},{key:"addRectModule",value:function a(b,c,d,e){for(var f,g=arguments.length>4&&void 0!==arguments[4]&&arguments[4],h=this.modules,i=e+c,j=d+b;c<i;c++)for(f=b;f<j;f++)h[c][f]=+g}},{key:"addPositionDetectionPattern",value:function a(){var b,c,d=this.modules,e=d.length;this.addPattern(0,0,7),this.addRectModule(7,0,1,8,!1),this.addRectModule(0,7,8,1,!1),b=e-7,this.addPattern(b,0,7),this.addRectModule(b-1,0,1,8,!1),this.addRectModule(b-1,7,8,1,!1),c=e-7,this.addPattern(0,c,7),this.addRectModule(7,c-1,1,8,!1),this.addRectModule(0,c-1,8,1,!1)}},{key:"addTimingPattern",value:function a(){var b,c=this.modules,d=c.length,e=!0;for(b=8;b<d-7;b++)c[6][b]=+e,c[b][6]=+e,e=!e}},{key:"addPattern",value:function a(b,c,d){this.addRectModule(b,c,d,d,!0),this.addRectModule(b+1,c+1,d-2,d-2,!1),this.addRectModule(b+2,c+2,d-4,d-4,!0)}},{key:"getMatrix",value:function a(){throw new i.SubclassNotImplementException}}]),a}(),b.default=j},function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(3),f=h(e);function h(a){return a&&a.__esModule?a:{default:a}}function i(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}g=function(){function a(b){i(this,a),this.mode="8BitByte",this.data=b,this.bytes=f.default.utf8Encode(b)}return d(a,[{key:"getMode",value:function a(){return f.default.MODE_INDICATOR["8BitByte"]}},{key:"getLength",value:function a(){return this.bytes.length}},{key:"write",value:function a(b){this.bytes.forEach(function(a){b.put(a,8)})}}]),a}(),b.default=g},function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(3),f=i(e),g=c(0);function i(a){return a&&a.__esModule?a:{default:a}}function j(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}h=function(){function a(b){j(this,a),this.mode="Numeric",this.data=b}return d(a,[{key:"getMode",value:function a(){return f.default.MODE_INDICATOR.Numeric}},{key:"getLength",value:function a(){return this.data.length}},{key:"write",value:function a(b){(0,g.sliceArray)(this.data,3,function(a){var c=parseInt(a.reduce(function(a,b){return a+=String.fromCharCode(b)},""));switch(a.length){case 1:return void b.put(c,4);case 2:return void b.put(c,7);default:return void b.put(c,10)}})}}]),a}(),b.default=h},function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(3),f=i(e),g=c(0);function i(a){return a&&a.__esModule?a:{default:a}}function j(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}h=function(){function a(b){j(this,a),this.mode="Alphanumeric",this.data=b}return d(a,[{key:"getMode",value:function a(){return f.default.MODE_INDICATOR.Alphanumeric}},{key:"getLength",value:function a(){return this.data.length}},{key:"write",value:function a(b){(0,g.sliceArray)(this.data,2,function(a){var c,d,e=f.default.getAlphanumericCharValue(a[0]);2===a.length?(c=f.default.getAlphanumericCharValue(a[1]),d=45*e+c,b.put(d,11)):b.put(e,6)})}}]),a}(),b.default=h},function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(3),f=h(e);function h(a){return a&&a.__esModule?a:{default:a}}function i(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}g=function(){function a(b){i(this,a),this.mode="Kanji",this.data=b}return d(a,[{key:"getMode",value:function a(){return f.default.MODE_INDICATOR.Kanji}},{key:"getLength",value:function a(){return this.data.length}},{key:"write",value:function a(b){this.data.forEach(function(a){var c,d,e,f,g=void 0;a>33088&&a<40956?(a-=33088,c=a>>>8,d=255&a,g=192*c+d):a>57408&&a<60351&&(a-=49472,e=a>>>8,f=255&a,g=192*e+f),b.put(g,13)})}}]),a}(),b.default=g},function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}();function f(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}e=function(){function a(){var b=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];f(this,a),this.buffer=b,this.length=8*b.length,this.index=0}return d(a,[{key:"putBit",value:function a(b){var c=Math.floor(this.length/8);this.buffer.length<=c&&this.buffer.push(0),b&&(this.buffer[c]|=128>>>this.length%8),this.length+=1}},{key:"put",value:function a(b,c){for(var d=0;d<c;d+=1)this.putBit(1==(b>>>c-d-1&1))}},{key:"getAt",value:function a(b){var c=Math.floor(b/8);return 1==(this.buffer[c]>>>7-b%8&1)}},{key:"getBuffer",value:function a(){return this.buffer}},{key:"next",value:function a(){return this.index++,
this.getAt(this.index-1)}}]),a}(),b.default=e},function(a,b,c){c(21),c(11),c(31),c(33),c(35),c(37),c(45),c(48),c(49),c(53),c(57),c(58),a.exports=c(2)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(4),f=o(e),g=c(2),h=o(g),i=c(0),j=c(26),k=o(j),l=c(1);function o(a){return a&&a.__esModule?a:{default:a}}function p(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function q(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function r(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}m=/^([A-D]?)([0-9\-\$\:\.\+\/]+)([A-D]?)$/,n=function(a){r(b,a);function b(a){var c,d,e,f,g,h,i,j,l,m,n,o,r;return p(this,b),c=q(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,k.default.defaultConfig)),d=c.config,e=d.checkDigit,f=d.text,g=d.nwRatio,h=c.getTextEntity(f),i=h.originStartPattern,j=h.startPattern,l=h.content,m=h.originStopPattern,n=h.stopPattern,e?(o=c.checksum(l),c.text=j+l+o+n,c.label=i+l+o+m):(c.text=j+l+n,c.label=f),c.nwRatio=+g,r=c.calculateData(),c.adjustDesiredSize(r),c.convertToShape(r),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.text,d=b.nwRatio;if(!m.test(c))throw new l.InvalidTextException(c);if(2!=d&&3!=d)throw new l.BadArgumentsException({nwRatio:d},"NwRatio is 2 or 3")}},{key:"getTextEntity",value:function a(b){var c=m.exec(b),d=c[1],e=d||"A",f=c[2],g=c[3],h=g||"B";return{originStartPattern:d,startPattern:e,content:f,originStopPattern:g,stopPattern:h}}},{key:"encode",value:function a(b){var c=this.nwRatio,d="",e=(0,i.strRepeat)("1",c),f=(0,i.strRepeat)("0",c);return(0,i.str2Array)(b).forEach(function(a,b){d+=(0,i.isEven)(b)?"1"===a?e:"1":"1"===a?f:"0"}),d}},{key:"calculateData",value:function a(){var b=this,c=this.text,d="";return(0,i.sliceString)(c,1,function(a){d=d+b.encode(k.default.TABLE[a])+"0"}),d=d.substr(0,d.length-1)}},{key:"checksum",value:function a(b){var a=(0,i.str2Array)(b).filter(function(a){return(0,i.isInteger)(+a)}).reverse().reduce(function(a,b,c){var d,e=(0,i.toNumber)(b);return(0,i.isEven)(c)?(d=2*e,a+=d>9?d-9:d):a+=e,a},0)%10;return 0!==a&&(a=10-a),a}}]),b}(f.default),b.default=n,h.default.registerEncoder("Codabar",n)},function(a,b){var c;c=function(){return this}();try{c=c||Function("return this")()||(0,eval)("this")}catch(a){"object"==typeof window&&(c=window)}a.exports=c},function(a,b,c){"use strict";var d,e,f,g,h,i,j;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(24),f=k(e),g=c(25),h=k(g),i=c(1);function k(a){return a&&a.__esModule?a:{default:a}}function l(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function m(a,b){var c=void 0;return"svg"===a?(c=document.createElementNS("http://www.w3.org/2000/svg","svg"),c.setAttribute("xmlns","http://www.w3.org/2000/svg")):c=document.createElement("canvas"),c.setAttribute("width",b.width),c.setAttribute("height",b.height),c}j=function(){function a(b,c){var d,e;switch(l(this,a),this.container=b,this.barcode=c,this.style=c.style,d=this.style.unitValue,this.size={width:c.size.width*d,height:c.size.height*d},e=m(this.style.renderType,this.size),this.style.renderType){case"svg":this.context=new h.default(e,this.style,d);break;case"canvas":this.context=new f.default(e,this.style,d);break;default:throw new i.UnrecognizedRenderException(this.style.renderType)}b&&b.appendChild(e),this.renderDom=e}return d(a,[{key:"render",value:function a(){var b=this.style,c=this.barcode.shapes,d=this.context;return d.clear(),d.setColor(b.color),d.setBackgroundColor(b.backgroundColor),c.forEach(function(a){"rect"===a.type&&d.drawRect(a),"text"===a.type&&d.drawText(a)}),this}},{key:"getImageData",value:function a(){if(!this.context.getImageData)throw new i.MethodNotImplementException("getImageData");return this.context.getImageData()}},{key:"getDataUrl",value:function a(){return this.context.getDataUrl()}},{key:"destroy",value:function a(){this.container&&this.container.removeChild(this.renderDom)}}]),a}(),b.default=j},function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}();function f(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}e=function(){function a(b,c,d){f(this,a),this.dom=b,this.style=c,this.ctx=this.dom.getContext("2d"),this.scale=d}return d(a,[{key:"setColor",value:function a(b){this.ctx.fillStyle=b}},{key:"setBackgroundColor",value:function a(b){var c=this.ctx,d=this.dom;c.save(),c.fillStyle=b,c.fillRect(0,0,d.width,d.height),c.restore()}},{key:"drawRect",value:function a(b){var c=this.ctx,d=this.scale;c.fillRect(b.x*d,b.y*d,b.width*d,b.height*d)}},{key:"drawText",value:function a(b){var c,d,e=this.ctx,f=this.style,g=this.scale;e.save(),e.font=(b.fontStyle||f.fontStyle)+" "+(b.fontWeight||f.fontWeight)+" "+f.fontSize+" "+f.fontFamily,e.textAlign=b.textAlign||f.textAlign,e.textBaseline="top",c=b.x*g,d=b.y*g,e.fillText(b.text,c,d,b.maxWidth*g),this.drawTextDecorationLine(b.text,c,d,b.textDecoration||f.textDecoration),e.restore()}},{key:"drawTextDecorationLine",value:function a(b,c,d,e){var f,g,h,i,j=void 0;switch(e){case"underline":j=.8;break;case"overline":j=.1;break;case"line-through":j=.5;break;default:return}switch(f=this.ctx,g=this.style.fontHeight,h=f.measureText(b).width,f.textAlign){case"center":c-=h/2;break;case"right":c-=h}f.lineWidth=1,f.beginPath(),i=g*j+d,f.moveTo(c,i),f.lineTo(c+h,i),f.stroke()}},{key:"clear",value:function a(){var b=this.ctx,c=this.dom;b.clearRect(0,0,c.width,c.height)}},{key:"getImageData",value:function a(){var b=this.ctx,c=this.dom;return b.getImageData(0,0,c.width,c.height)}},{key:"getDataUrl",value:function a(){return this.dom.toDataURL()}}]),a}(),b.default=e},function(a,b,c){"use strict";var d,e,f;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}();function g(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}e={center:"middle",left:"start",right:"end"};function h(){return!!document.documentMode}f=function(){function a(b,c,d){g(this,a),this.dom=b,this.style=c,this.scale=d,this.color="rgb(0,0,0)",this.addGroup()}return d(a,[{key:"setColor",value:function a(b){this.color=b}},{key:"setBackgroundColor",value:function a(b){var c=this.dom;c.style.backgroundColor=b}},{key:"addGroup",value:function a(){var b=this.dom;this.g=document.createElementNS("http://www.w3.org/2000/svg","g"),this.g.setAttribute("shape-rendering","crispEdges"),b.appendChild(this.g)}},{key:"drawRect",value:function a(b){var c=this.g,d=this.scale,e=this.color,f=document.createElementNS("http://www.w3.org/2000/svg","rect");f.setAttribute("x",b.x*d),f.setAttribute("y",b.y*d),f.setAttribute("width",b.width*d),f.setAttribute("height",b.height*d),f.style.fill=e,c.appendChild(f)}},{key:"drawText",value:function a(b){var c,d=this.g,f=this.scale,g=this.color,i=this.style,j=this.dom,k=document.createElementNS("http://www.w3.org/2000/svg","text");k.style.fill=g,k.style.fontSize=i.fontSize,k.style.fontFamily=i.fontFamily,k.style.fontStyle=b.fontStyle||i.fontStyle,k.style.fontWeight=b.fontWeight||i.fontWeight,k.style.textDecoration=b.textDecoration||i.textDecoration,k.style.textAnchor=e[b.textAlign||i.textAlign],k.textContent=b.text,h()?(k.setAttribute("x",0),k.setAttribute("y",0),j.appendChild(k),c=k.getBBox(),k.setAttribute("y",b.y*f+Math.abs(c.y))):(k.style.alignmentBaseline="before-edge",k.setAttribute("y",b.y*f)),k.setAttribute("x",b.x*f),d.appendChild(k)}},{key:"clear",value:function a(){var b=this.dom;b.removeChild(this.g),this.addGroup()}},{key:"getDataUrl",value:function a(){return"data:image/svg+xml;base64,"+btoa(this.dom.outerHTML)}}]),a}(),b.default=f},function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d={height:60,showLabel:!0,labelPosition:"bottom",checkDigit:!1,quietZone:{top:0,right:10,bottom:0,left:10},nwRatio:3},e={0:"0000011",1:"0000110",2:"0001001",3:"1100000",4:"0010010",5:"1000010",6:"0100001",7:"0100100",8:"0110000",9:"1001000","-":"0001100",$:"0011000",":":"1000101","/":"1010001",".":"1010100","+":"0010101",A:"0011010",B:"0101001",C:"0001011",D:"0001110"},b.default={defaultConfig:d,TABLE:e}},function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(5),f=j(e),g=c(1),h=c(0);function j(a){return a&&a.__esModule?a:{default:a}}function k(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}i=function(){function a(b){k(this,a),this.text=b,this.validate()}return d(a,[{key:"validate",value:function a(){var b=this.text,c=/^[\x00-\x5F\xC8-\xCF]+$/;if(!c.test(b))throw new g.InvalidTextException(b)}},{key:"getData",value:function a(){var b=this.text,c=this.checksum(),d="",e=f.default.getPatternByIndex(f.default.Code128Sym.StartA);return d+=f.default.encode(e),(0,h.sliceString)(b,1,function(a){var b=f.default.getCharPattern(a,"A");d+=f.default.encode(b)}),d+=f.default.encode(f.default.getPatternByIndex(c)),d+=f.default.encode(f.default.stopPattern)}},{key:"checksum",value:function a(){var b=this.text,c=0,d=0;return(0,h.sliceString)(b,1,function(a){d+=f.default.getCharValue(a,"A")*++c}),d+=f.default.Code128Sym.StartA,d%103}}]),a}(),b.default=i},function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(5),f=j(e),g=c(1),h=c(0);function j(a){return a&&a.__esModule?a:{default:a}}function k(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}i=function(){function a(b){k(this,a),this.text=b,this.validate()}return d(a,[{key:"validate",value:function a(){var b=this.text,c=/^[\x20-\x7F\xC8-\xCF]+$/;if(!c.test(b))throw new g.InvalidTextException(b)}},{key:"getData",value:function a(){var b=this.text,c=this.checksum(),d="",e=f.default.getPatternByIndex(f.default.Code128Sym.StartB);return d+=f.default.encode(e),(0,h.sliceString)(b,1,function(a){var b=f.default.getCharPattern(a,"B");d+=f.default.encode(b)}),d+=f.default.encode(f.default.getPatternByIndex(c)),d+=f.default.encode(f.default.stopPattern)}},{key:"checksum",value:function a(){var b=this.text,c=0,d=0;return(0,h.sliceString)(b,1,function(a){d+=f.default.getCharValue(a,"B")*++c}),d+=f.default.Code128Sym.StartB,d%103}}]),a}(),b.default=i},function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(5),f=j(e),g=c(0),h=c(1);function j(a){return a&&a.__esModule?a:{default:a}}function k(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}i=function(){function a(b){k(this,a),this.text=b,this.validate()}return d(a,[{key:"validate",value:function a(){var b=this.text,c=/^(\xCF*[0-9]{2}\xCF*)+$/;if(!c.test(b))throw new h.InvalidTextException(b)}},{key:"getData",value:function a(){var b=this.text,c=this.checksum(),d="",e=f.default.getPatternByIndex(f.default.Code128Sym.StartC);return d+=f.default.encode(e),(0,g.sliceString)(b,2,function(a){var b=f.default.getCharPattern(a,"C");d+=f.default.encode(b)}),d+=f.default.encode(f.default.getPatternByIndex(c)),d+=f.default.encode(f.default.stopPattern)}},{key:"checksum",value:function a(){var b=this.text,c=0,d=0;return(0,g.sliceString)(b,2,function(a){d+=f.default.getCharValue(a,"C")*++c}),d+=f.default.Code128Sym.StartC,d%103}}]),a}(),b.default=i},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(5),f=m(e),g=c(0),h=c(1);function m(a){return a&&a.__esModule?a:{default:a}}function n(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}i="A",j="B",k="C",l=function(){function a(b){var c=arguments.length>1&&void 0!==arguments[1]&&arguments[1];n(this,a),this.text=b,this.isUccEan128=c,this.validate()}return d(a,[{key:"validate",value:function a(){var b=this.text,c=/^[\x00-\x7F\xC8-\xD3]+$/;if(!c.test(b))throw new h.InvalidTextException(b)}},{key:"calculateGroup",value:function a(){var b,c,d,e,h=this.text,l=this.isUccEan128,m={code:l?k:j,text:""},n=[];for(n.push(m),b=0,c=h.length;b<c;b++){if(d=m.code,e=h[b],m.code!==k&&b+3<c&&(0,g.isNumberLike)(h.substr(b,4))||m.code===k&&b+1<c&&(0,g.isNumberLike)(h.substr(b,2)))d=k,e+=h[++b];else if(d=m.code===k?j:m.code,f.default.getCharValue(e,d)<0&&(d=m.code===i?j:i,f.default.getCharValue(e,d)<0))continue;m.code!==d?(m={code:d,text:e},n.push(m)):m.text+=e}return n.filter(function(a){return a.text})}},{key:"getData",value:function a(){var b=this.calculateGroup(),c=this.checksum(b),d="";return b.forEach(function(a,b){var c,e;0===b?(c=f.default.getPatternByIndex(f.default.Code128Sym["Start"+a.code]),d+=f.default.encode(c)):(e=f.default.getPatternByIndex(f.default.Code128Sym["Code"+a.code]),d+=f.default.encode(e)),a.code===k?(0,g.sliceString)(a.text,2,function(b){d+=f.default.encode(f.default.getCharPattern(b,a.code))}):(0,g.sliceString)(a.text,1,function(b){d+=f.default.encode(f.default.getCharPattern(b,a.code))})}),d+=f.default.encode(f.default.getPatternByIndex(c)),d+=f.default.encode(f.default.stopPattern)}},{key:"checksum",value:function a(b){var c=0,d=0;return b.forEach(function(a,b){var e,h=a.code,i=a.text;0===b?d+=f.default.Code128Sym["Start"+h]:(e=f.default.Code128Sym["Code"+h],d+=e*++c),h===k?(0,g.sliceString)(i,2,function(a){d+=f.default.getCharValue(a,h)*++c}):(0,g.sliceString)(i,1,function(a){d+=f.default.getCharValue(a,h)*++c})}),d%103}}]),a}(),b.default=l},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(4),f=n(e),g=c(2),h=n(g),i=c(0),j=c(32),k=n(j),l=c(1);function n(a){return a&&a.__esModule?a:{default:a}}function o(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function p(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function q(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}m=function(a){q(b,a);function b(a){var c,d,e,f,g,h,i,j,l,m,n,q,r,s;return o(this,b),c=p(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,k.default.defaultConfig)),d=c.config,e=d.text,f=d.checkDigit,g=d.fullASCII,h=d.nwRatio,i=d.labelWithStartAndStopCharacter,j=g?k.default.getFullASCIIChar(e):e,f?(l=k.default.getMod43Val(j),c.text=k.default.START_STOP_CHARACTERS+j+l+k.default.START_STOP_CHARACTERS,m=e+l,n=k.default.START_STOP_CHARACTERS+m+k.default.START_STOP_CHARACTERS,c.label=i?n:m):(c.text=k.default.START_STOP_CHARACTERS+j+k.default.START_STOP_CHARACTERS,q=e,r=k.default.START_STOP_CHARACTERS+q+k.default.START_STOP_CHARACTERS,c.label=i?r:q),c.nwRatio=+h,s=c.calculateData(),c.adjustDesiredSize(s),c.convertToShape(s),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.text,d=b.fullASCII,e=b.nwRatio,f=/^[0-9A-Z\-\.\ \$\/\+\%]+$/;if(!d&&!f.test(c))throw new l.InvalidTextException(c);if(2!=e&&3!=e)throw new l.BadArgumentsException({nwRatio:e},"NwRatio is 2 or 3")}},{key:"encode",value:function a(b){var c=this.nwRatio,d="",e=(0,i.strRepeat)("1",c),f=(0,i.strRepeat)("0",c);return(0,i.str2Array)(b).forEach(function(a,b){d+=(0,i.isEven)(b)?"1"===a?e:"1":"1"===a?f:"0"}),d}},{key:"calculateData",value:function a(){var b=this,c=this.text,d="";return(0,i.sliceString)(c,1,function(a){d=d+b.encode(k.default.TABLE[a])+"0"}),d=d.substr(0,d.length-1)}}]),b}(f.default),b.default=m,h.default.registerEncoder("Code39",m)},function(a,b,c){"use strict";var d,e,f,g,h,i,j;Object.defineProperty(b,"__esModule",{value:!0}),d=c(1),e=c(0),f={height:60,showLabel:!0,checkDigit:!1,fullASCII:!1,labelPosition:"bottom",nwRatio:3,labelWithStartAndStopCharacter:!1,quietZone:{top:0,right:10,bottom:0,left:10}},g={0:"000110100",1:"100100001",2:"001100001",3:"101100000",4:"000110001",5:"100110000",6:"001110000",7:"000100101",8:"100100100",9:"001100100",A:"100001001",B:"001001001",C:"101001000",D:"000011001",E:"100011000",F:"001011000",G:"000001101",H:"100001100",I:"001001100",J:"000011100",K:"100000011",L:"001000011",M:"101000010",N:"000010011",O:"100010010",P:"001010010",Q:"000000111",R:"100000110",S:"001000110",T:"000010110",U:"110000001",V:"011000001",W:"111000000",X:"010010001",Y:"110010000",Z:"011010000","-":"010000101",".":"110000100"," ":"011000100",$:"010101000","/":"010100010","+":"010001010","%":"000101010","*":"010010100"},h="*",i=(0,e.str2Array)("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%");function k(a){var b=0;return(0,e.sliceString)(a,1,function(a){b+=i.indexOf(a)}),i[b%43]}j=["%U","$A","$B","$C","$D","$E","$F","$G","$H","$I","$J","$K","$L","$M","$N","$O","$P","$Q","$R","$S","$T","$U","$V","$W","$X","$Y","$Z","%A","%B","%C","%D","%E"," ","/A","/B","/C","/D","/E","/F","/G","/H","/I","/J","/K","/L","-",".","/O","0","1","2","3","4","5","6","7","8","9","/Z","%F","%G","%H","%I","%J","%V","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","%K","%L","%M","%N","%O","%W","+A","+B","+C","+D","+E","+F","+G","+H","+I","+J","+K","+L","+M","+N","+O","+P","+Q","+R","+S","+T","+U","+V","+W","+X","+Y","+Z","%P","%Q","%R","%S","%T"];function l(a){var b="";return(0,e.sliceString)(a,1,function(c){var e=j[c.charCodeAt(0)];if(!e)throw new d.InvalidTextException(a);b+=e}),b}b.default={TABLE:g,START_STOP_CHARACTERS:h,defaultConfig:f,getMod43Val:k,getFullASCIIChar:l}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(7),f=n(e),g=c(2),h=n(g),i=c(34),j=n(i),k=c(0),l=c(1);function n(a){return a&&a.__esModule?a:{default:a}}function o(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function p(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function q(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}m=function(a){q(b,a);function b(a){var c,d,e;return o(this,b),c=p(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,j.default.defaultConfig)),d=c.config.text,c.text=d,c.label=d.replace(/[\xc9-\xcf]/g,""),c.mode=j.default.isNumericOnly(d)?2:0,c.getModes(),e=c.calculateData(),c.adjustDesiredSize(e),c.convertToShape(e),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.text,d=b.grouping,e=/^[\x00-\x80\xcf\xca\xc9]+$/;if(!e.test(c))throw new l.InvalidTextException(c);if(!d&&c.length>81)throw new l.TextTooLargeException}},{key:"getModes",value:function a(){var b,c,d,e,f,g,h=this.text,i=this.config,k=i.grouping,m=i.groupNo;if(k&&(b=j.default.getTextGroup(h),b.length>1)){if(b.length>9)throw new l.GroupSizeException(b.length);if(m>b.length-1)throw new l.GroupOverflowException(b.length);this.groupCount=b.length,this.mode=3,h=b[m]}for(c=[],d=0,e=0,f=h.length;d<f;){for(;e<f&&!(h[e]<"0"||h[e]>"9");)e++;e-d>=5?(c.push({mode:"number",text:h.substring(d,e)}),d=e):(g=c[c.length-1],g&&"alpha"===g.mode?g.text+=h.substring(d,e+1):c.push({mode:"alpha",text:h.substring(d,e+1)}),d=++e)}this.modes=c}},{key:"encodeNumeric",value:function a(b){var c=void 0,d=void 0;switch(b.length){case 3:return c=+b,[~~(c/48),c%48];case 4:return c=+b+1e5,[~~(c/2304),~~(c%2304/48),c%48];case 5:return c=+b,[~~(c/2304),~~(c%2304/48),c%48];case 6:return c=b.substr(0,5),this.encodeNumeric(c).concat([+b[5]]);case 7:return c=b.substr(0,4),d=b.substr(4,3),this.encodeNumeric(c).concat(this.encodeNumeric(d));case 8:return c=b.substr(0,5),d=b.substr(5,3),this.encodeNumeric(c).concat(this.encodeNumeric(d));case 9:return c=b.substr(0,5),d=b.substr(5,4),this.encodeNumeric(c).concat(this.encodeNumeric(d));default:return c=b.substr(0,5),d=b.substr(5,b.length),this.encodeNumeric(c).concat(this.encodeNumeric(d))}}},{key:"encodeAlpha",value:function a(b){return(0,k.str2Array)(b).reduce(function(a,b){return a=a.concat(j.default.getCharValue(b))},[])}},{key:"calculateData",value:function a(){var b,c,d,e,f,g,h,i,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G=this,H=this.modes,I=this.mode,J=this.groupCount,K=this.config.groupNo,L=H.reduce(function(a,b,c){return"number"===b.mode?(0===c&&2!==I&&a.push(j.default.CODE_NS),a=a.concat(G.encodeNumeric(b.text))):a=a.concat(G.encodeAlpha(b.text)),a.push(j.default.CODE_NS),a},[]);for(L.pop(),3===I&&L.unshift(j.default.getGroupInfo(K,J)),b=L.length%7;L.length%7!==0;)L.push(j.default.CODE_NS);if(c=[],(0,k.sliceArray)(L,7,function(a){var b=a.reduce(function(a,b){return a+=b},0);a.push(b%49),c=c.concat(a)}),d=L.length/7,(d%8>=6||b>2||0==b||d%8===1)&&(c=c.concat((0,k.fillArray)(Array(8),j.default.CODE_NS)),d++),d>8)throw new l.TextTooLargeException;if(e=7*(d-2)+I,c[c.length-2]=e,f=j.default.getWeight(0,0),g=e*f.z,h=e*f.y,i=e*f.x,m=8*(d-1)/2,d>6){for(n=0,o=0;n<m;n++,o+=2)p=j.default.getWeight(~~(n/4)+1,n%4),g+=p.z*(49*c[o]+c[o+1]);g%=2401,c[c.length-8]=~~(g/49),c[c.length-7]=~~(g%49)}for(m++,q=0,r=0;q<m;q++,r+=2)s=j.default.getWeight(~~(q/4)+1,q%4),h+=s.y*(49*c[r]+c[r+1]);for(h%=2401,c[c.length-6]=~~(h/49),c[c.length-5]=~~(h%49),m++,t=0,u=0;t<m;t++,u+=2)v=j.default.getWeight(~~(t/4)+1,t%4),i+=v.x*(49*c[u]+c[u+1]);for(i%=2401,c[c.length-4]=~~(i/49),c[c.length-3]=~~(i%49),w=c.slice(8*d-8,8*d-1),c[c.length-1]=w.reduce(function(a,b){return a+=b},0)%49,x=[],y=0,z=c.length,A=0;y<z;y+=8,A++){for(B=c.slice(y,y+8),C=[],x.push(C),D=0,E=0;D<8;D+=2,E++)F=49*B[D]+B[D+1],C.push(j.default.getParityPattern(F,A,E,d));C.unshift(j.default.START_PATTERN),C.push(j.default.STOP_PATTERN)}return x}},{key:"convertToShape",value:function a(b){var c,d,e,f,g,h,i,j,l,m=this.label,n=this.isLabelBottom,o=this.height,p=this.quietZone,q=this.config.showLabel,r=this.style,s=r.fontSizeInUnit,t=r.textAlign;if(q||(s=0),c=[],d=1,e=(o-d)/b.length-d,f=70+p.left.relativeValue+p.right.relativeValue,g=0,h=p.top.relativeValue,i=void 0,n?i=h+o:(i=h,h+=s),c.push({type:"rect",x:g,y:h,width:f,height:d}),h+=d,b.forEach(function(a,f){g=p.left.relativeValue,a.forEach(function(a){(0,k.combineTruthy)(a).forEach(function(a){0!==a?(c.push({type:"rect",x:g,y:h,width:a,height:e}),g+=a):g++})}),0===f&&f===b.length-1||(c.push({type:"rect",x:p.left.relativeValue,y:h-d,width:70,height:d}),h+=e+d)}),c.push({type:"rect",x:0,y:h-d,width:f,height:d}),q){switch(j=0,l=f,t){case"center":j+=l/2;break;case"right":j+=l}c.push({type:"text",x:j,y:i,text:m,textAlign:t,maxWidth:l})}n?this.size={width:g+p.right.relativeValue,height:i+s+p.bottom.relativeValue}:this.size={width:g+p.right.relativeValue,height:h+p.bottom.relativeValue},this.shapes=c}},{key:"adjustDesiredSize",value:function a(b){var c,d,e=this.config,f=e.desiredSize,g=e.showLabel,h=this.quietZone,i=this.fontHeight,j=this.containerWidth,l=this.containerHeight;f&&(c=b[0].reduce(function(a,b){return a+=b.length},0),c=c+(0,k.getQuietZoneRelativeValue)(h.left)+(0,k.getQuietZoneRelativeValue)(h.right),j=j-(0,k.getQuietZonePixelValue)(h.left)-(0,k.getQuietZonePixelValue)(h.right),d=void 0,f.forceRounding?(d=~~(j/c),d=d<1?1:d):d=j/c,this.style.unitValue=d,this.style.fontSizeInUnit=i/d,l=g?l-i:l,this.height=l/d-(0,k.getQuietZoneRelativeValue)(h.top)-(0,k.getQuietZoneRelativeValue)(h.bottom),Object.keys(h).forEach(function(a){h[a].originIsAbsoluteValue&&(h[a].relativeValue=h[a].pixelValue/d)}))}}]),b}(f.default),b.default=m,h.default.registerEncoder("Code49",m)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p;Object.defineProperty(b,"__esModule",{value:!0}),d=c(6),e=q(d),f=c(0);function q(a){return a&&a.__esModule?a:{default:a}}g={showLabel:!0,grouping:!1,groupNo:0,quietZoneStart:10,quietZoneStop:1,labelPosition:"bottom",height:60,quietZone:{top:0,right:1,bottom:0,left:10}},h=[51520,62032,60576,64296,58784,63848,56128,63184,64948,50336,61736,39232,58960,63892,56480,63272,64970,35648,58064,52640,62312,47936,61136,64436,49744,36e3,58152,52816,62356,48288,61224,64458,34208,57704,50896,61876,40352,59240,63962,57040,63412,49448,34384,57748,50984,61898,40528,59284,57128,63434,33488,50024,36560,58292,53096,62426,48848,61364,33576,50068,36648,58314,53140,33128,49588,34664,57818,51124,40808,59354,57268,53568,62544,64788,59808,64104,55104,62928,64884,51360,61992,45376,60496,64276,37696,58576,63796,55712,63080,64922,52128,62184,46912,60880,64372,50256,61716,39072,58920,63882,56400,63252,35232,57960,52432,62260,47520,61032,64410,50640,61812,39840,59112,63930,56784,63348,49704,35920,58132,52776,62346,48208,61204,34e3,57652,50792,61850,40144,59188,56936,63386,49896,36304,58228,52968,62394,48592,61300,49428,34344,57738,50964,40488,59274,33384,49972,36456,58266,53044,48744,61338,49524,34536,57786,51060,40680,59322,57204,33556,50058,36628,33076,49562,34612,51098,40756,33652,50106,36724,53178,49012,53408,62504,64778,41792,59600,64052,54176,62696,64826,44864,60368,64244,51280,61972,45216,60456,64266,37280,58472,63770,55504,63028,51664,62068,45984,60648,64314,38816,58856,63866,56272,63220,50216,61706,38992,58900,56360,63242,35024,57908,52328,62234,47312,60980,50408,61754,39376,58996,56552,63290,35792,58100,52712,62330,48080,61172,49684,35880,58122,52756,33896,57626,50740,40040,59162,56884,49780,36072,58170,52852,48360,61242,34280,57722,50932,40424,59258,57076,49418,34324,50954,33332,49946,36404,53018,49466,34420,51002,40564,57146,33524,50042,36596,53114,48884,33546,33050,34586,33594,36666,33146,34682,40826,53328,62484,41376,59496,64026,53712,62580,42912,59880,64122,55248,62964,51240,61962,45136,60436,37072,58420,55400,63002,51432,62010,45520,60532,37840,58612,55784,63098,52200,62202,47056,60916,50196,38952,58890,34920,57882,52276,47208,60954,50292,39144,58938,56436,35304,57978,52468,47592,61050,50676,39912,59130,56820,49674,35860,33844,50714,39988,49722,35956,52794,48244,34036,50810,40180,56954,49914,36340,52986,48628,34314,33306,36378,34362,40506,33402,36474,48762,34554,40698,53288,62474,41168,59444,53480,62522,41936,59636,54248,62714,45008,60404,51220,36968,58394,55348,51316,45288,60474,37352,58490,55540,51700,46056,60666,38888,58874,56308,50186,34868,52250,50234,39028,56378,35060,52346,47348,50426,39412,56570,35828,52730,48116,33818,35898,33914,40058,36090,48378,34298,40442,62752,64840,56e3,63152,64940,51776,62096,60704,64328,52576,62296,47808,61104,64428,50464,61768,39488,59024,63908,56608,63304,64978,50864,61868,40288,59224,63958,57008,63404,49808,36128,58184,52880,62372,48416,61256,64466,50008,36528,58284,53080,62422,48816,61356,49480,34448,57764,51016,61906,40592,59300,57160,63442,49580,34648,57814,51116,40792,59350,57260,33608,50084,36680,58322,53156,48968,61394,59744,64088,54976,62896,64876,53824,62608,64804,60192,64200,37568,58544,63788,55648,63064,64918,52064,62168,46784,51488,62024,45632,60560,64292,38464,58768,63844,56096,63176,64946,35168,57944,52400,62252,47456,61016,64406,50608,61804,39776,50320,61732,39200,58952,63890,56464,63268,35616,58056,52624,62308,47904,61128,64434,33968,57644,50776,61846,40112,59180,56920,63382,49880,36272,49736,35984,58148,48560,52808,62354,48272,61220,34192,57700,50888,61874,40336,59236,57032,63410,33368,49964,36440,58262,53036,48728,61334,49516,34520,49444,34376,57746,40664,50980,40520,59282,57124,33480,50020,36552,58290,53092,48840,61362,33068,49558,34604,51094,40748,33644,33572,36716,50066,36644,49004,53138,33124,49586,34660,51122,40804,57266,41664,59568,64044,54112,62680,64822,53536,62536,64786,44736,60336,64236,42560,59792,64100,55072,62920,64882,37216,58456,63766,55472,63020,51632,62060,45920,51344,61988,45344,60488,64274,38752,58840,63862,37664,58568,63794,55696,63076,52112,62180,46880,60872,64370,34992,57900,52312,62230,47280,60972,50392,61750,39344,50248,61714,39056,58916,56392,63250,35760,58092,35216,57956,48048,52424,62258,47504,61028,50632,61810,39824,59108,56776,63346,33880,57622,50732,40024,59158,56876,49772,36056,49700,35912,58130,48344,52772,48200,61202,34264,57718,33992,57650,40408,50788,40136,59186,56932,49892,36296,58226,52964,48584,61298,33324,49942,36396,53014,49462,34412,49426,34340,40556,50962,40484,33516,33380,36588,49970,36452,48876,53042,48740,49522,34532,51058,40676,57202,33046,34582,33590,33554,36662,36626,33142,33074,34678,34610,40822,40754,33650,36722,49010,41312,59480,64022,53680,62572,53392,62500,42848,59864,64118,41760,59592,64050,55216,62956,54160,62692,44832,60360,64242,37040,58412,55384,62998,51416,62006,45488,51272,61970,45200,60452,37808,58604,37264,58468,55496,63026,52184,62198,47024,51656,62066,45968,60644,38800,58852,56264,63218,34904,57878,52268,47192,60950,50284,39128,50212,38984,58898,56356,35288,57974,35016,57906,47576,52324,47304,60978,50668,39896,50404,39368,58994,56548,35784,58098,52708,48072,61170,33836,50710,39980,49718,35948,49682,35876,48236,52754,34028,33892,40172,50738,40036,56882,49910,36332,49778,36068,48620,52850,48356,34276,50930,40420,57074,33302,36374,34358,34322,40502,33398,33330,36470,36402,48758,34550,34418,40694,40562,33522,36594,48882,41136,59436,53464,62518,53320,62482,41904,59628,41360,59492,54232,62710,53704,62578,44976,60396,42896,59876,55240,62962,36952,58390,55340,51308,45272,51236,45128,60434,37336,58486,37064,58418,55396,51692,46040,51428,45512,60530,38872,58870,37832,58610,55780,52196,47048,60914,34860,52246,50230,39020,50194,38948,35052,34916,47340,52274,47204,50422,39404,50290,39140,56434,35820,35300,48108,52466,47588,50674,39908,56818,33814,35894,35858,33910,33842,40054,39986,36086,35954,48374,48242,34294,34034,40438,40178,36338,48626,41048,59414,53356,53284,41432,59510,41160,59442,53740,53476,42968,59894,41928,59634,55276,54244,36908,51254,45164,51218,37100,36964,55346,51446,45548,51314,45284,37868,37348,55538,52214,47084,51698,46052,34838,38966,34934,34866,47222,39158,39026,35318,35058,47606,47346,39926,39410,6e4,64152,54336,62736,64836,60784,64348,38080,58672,63820,55904,63128,64934,51744,62088,46144,60688,64324,39648,59064,63918,56688,63324,35424,58008,52528,62284,47712,61080,64422,50448,61764,39456,59016,63906,56592,63300,36208,58204,52920,62382,48496,61276,34096,57676,50840,61862,40240,59212,56984,63398,49800,36112,58180,52872,62370,48400,61252,34488,57774,51036,40632,59310,57180,33432,49996,36504,58278,53068,48792,61350,49476,34440,57762,51012,40584,59298,57156,33628,50094,36700,53166,48988,33100,49574,34636,51110,40780,57254,33604,50082,36676,53154,48964,53984,62648,64814,44480,60272,64220,42176,59696,64076,54880,62872,64870,53792,62600,64802,44096,60176,64196,51568,62044,45792,60600,64302,38624,58808,63854,37472,58520,63782,55600,63052,52016,62156,46688,51472,62020,45600,60552,64290,38432,58760,63842,56080,63172,50360,61742,39280,58972,56504,63278,35696,58076,35120,57932,47984,52376,62246,47408,61004,50584,61798,39728,50312,61730,39184,58948,56456,63266,35600,58052,52616,62306,47888,61124,49756,36024,58158,52828,48312,61230,34232,57710,33944,57638,40376,50764,40088,59174,56908,49868,36248,49732,35976,58146,48536,52804,48264,61218,34184,57698,50884,40328,59234,57028,49454,34396,50990,40540,57134,33500,33356,36572,49958,36428,48860,53030,48716,49510,34508,49442,34372,40652,50978,40516,57122,33476,50018,36548,53090,48836,33582,36654,33134,33062,34670,34598,40814,40742,33638,33570,36710,36642,48998,33122,34658,40802,53616,62556,42720,59832,64110,41568,59544,64038,55152,62940,54064,62668,53520,62532,44640,60312,64230,42528,59784,64098,55056,62916,51384,61998,45424,60508,37744,58588,37168,58444,55448,63014,52152,62190,46960,51608,62054,45872,51336,61986,45328,60484,38704,58828,37648,58564,55688,63074,52104,62178,46864,60868,50268,39096,58926,56412,35256,57966,34968,57894,47544,52300,47256,60966,50652,39864,50380,39320,50244,39048,58914,56388,35736,58086,35208,57954,48024,52420,47496,61026,50628,39816,59106,56772,49710,35932,52782,48220,34012,33868,40156,50726,40012,56870,49902,36316,49766,36044,49698,48604,35908,48332,52770,48196,34252,33988,40396,50786,40132,56930,49890,36292,52962,48580,34350,40494,33390,33318,36462,36390,48750,34542,34406,40686,34338,40550,40482,33510,33378,36582,36450,48870,48738,34530,40674,53432,62510,41840,59612,41264,59468,54200,62702,53656,62566,53384,62498,44912,60380,42800,59852,41744,59588,55192,62950,54152,62690,44816,60356,51292,45240,60462,37304,58478,37016,58406,55372,51676,46008,51404,45464,51268,45192,60450,38840,58862,37784,58598,37256,58466,55492,52172,47e3,51652,45960,60642,38792,58850,56260,50222,39004,56366,35036,34892,47324,52262,47180,50414,39388,50278,39116,50210,38980,56354,35804,35276,48092,35012,47564,52322,47300,50662,39884,50402,39364,56546,35780,52706,48068,35886,33902,33830,40046,39974,36078,35942,48366,35874,48230,34286,34022,40430,33890,40166,40034,36326,36066,48614,48354,34274,40418,53340,41400,59502,41112,59430,53724,53452,53316,42936,59886,41880,59622,41352,59490,55260,54220,53700,44952,60390,42888,59874,51246,45148,37084,36940,55334,51438,45532,51302,45260,51234,45124,37852,37324,37060,55394,52206,47068,51686,46028,51426,45508,38860,37828,55778,38958,34926,34854,47214,39150,39014,38946,35310,35046,47598,34914,47334,47202,39918,39398,39138,35814,35298,48102,47586,53294,41180,41036,53486,53350,53282,41948,41420,41156,54254,53734,53474,45020,42956,41924,36974,36902,45294,45158,37358,37094,36962,46062,45542,45282,38894,37862,37346,54496,62776,64846,43200,59952,64140,54304,62728,64834,56048,63164,51824,62108,46304,60728,64334,37984,58648,63814,55856,63116,51728,62084,46112,60680,64322,52600,62302,47856,61116,50488,61774,39536,59036,56632,63310,35376,57996,52504,62278,47664,61068,50440,61762,39440,59012,56584,63298,50876,40312,59230,57020,49820,36152,58190,52892,48440,61262,34072,57670,50828,40216,59206,56972,49796,36104,58178,52868,48392,61250,50014,36540,53086,48828,49486,34460,51022,40604,57166,33420,49990,36492,53062,48780,49474,34436,51010,40580,57154,34654,40798,33614,36686,48974,33094,34630,40774,33602,36674,48962,59768,64094,55024,62908,53872,62620,44256,60216,64206,42080,59672,64070,54832,62860,53776,62596,44064,60168,64194,37616,58556,55672,63070,52088,62174,46832,51512,62030,45680,60572,38512,58780,37424,58508,55576,63046,51992,62150,46640,51464,62018,45584,60548,38416,58756,56072,63170,35192,57950,52412,47480,61022,50620,39800,50332,39224,58958,56476,35640,58062,35096,57926,47928,52364,47384,60998,50572,39704,50308,39176,58946,56452,35592,58050,52612,47880,61122,33980,50782,40124,56926,49886,36284,49742,35996,48572,52814,48284,34204,33932,40348,50758,40076,56902,49862,36236,49730,35972,48524,52802,48260,34180,50882,40324,57026,33374,36446,48734,34526,34382,40670,40526,33486,33350,36558,36422,48846,48710,34502,34370,40646,40514,33474,36546,48834,41712,59580,54136,62686,53560,62542,44784,60348,42608,59804,41520,59532,55096,62926,54040,62662,53512,62530,44592,60300,42512,59780,55048,62914,37240,58462,55484,51644,45944,51356,45368,60494,38776,58846,37688,58574,37144,58438,55436,52124,46904,51596,45848,51332,45320,60482,38680,58822,37640,58562,55684,52100,46856,60866,35004,52318,47292,50398,39356,50254,39068,56398,35772,35228,48060,34956,47516,52294,47244,50638,39836,50374,39308,50242,39044,56386,35724,35204,48012,52418,47492,50626,39812,56770,33886,40030,36062,35918,48350,48206,34270,33998,40414,33862,40142,40006,36302,36038,48590,35906,48326,48194,34246,33986,40390,40130,36290,48578,41336,59486,53692,53404,42872,59870,41784,59598,41240,59462,55228,54172,53644,53380,44856,60366,42776,59846,41736,59586,55180,54148,37052,55390,51422,45500,51278,45212,37820,37276,37004,55366,52190,47036,51662,45980,51398,45452,51266,45188,38812,37772,37252,55490,52166,46988,51650,45956,34910,47198,39134,38990,35294,35022,47582,34886,47310,47174,39902,39374,39110,38978,35790,35270,48078,35010,47558,47298,39878,39362,41148,53470,53326,41916,41372,41100,54238,53710,53446,53314,44988,42908,41868,41348,55246,54214,53698,36958,45278,45134,37342,37070,36934,46046,45518,45254,45122,38878,37838,37318,37058,47054,46022,45506,41054,41438,41166,41030,42974,41934,41414,41154,43488,60024,64158,54384,62748,43104,59928,64134,54288,62724,60796,38128,58684,55928,63134,51768,62094,46192,60700,37936,58636,55832,63110,51720,62082,46096,60676,39672,59070,56700,35448,58014,52540,47736,61086,50460,39480,59022,56604,35352,57990,52492,47640,61062,50436,39432,59010,56580,36220,52926,48508,34108,50846,40252,56990,49806,36124,52878,48412,34060,50822,40204,56966,49794,36100,52866,48388,34494,40638,33438,36510,48798,34446,40590,33414,36486,48774,34434,40578,54008,62654,44528,60284,42224,59708,54904,62878,53816,62606,44144,60188,42032,59660,54808,62854,53768,62594,44048,60164,51580,45816,60606,38648,58814,37496,58526,55612,52028,46712,51484,45624,60558,38456,58766,37400,58502,55564,51980,46616,51460,45576,60546,38408,58754,56068,50366,39292,56510,35708,35132,47996,52382,47420,50590,39740,50318,39196,56462,35612,35084,47900,52358,47372,50566,39692,50306,39172,56450,35588,52610,47876,36030,48318,34238,33950,40382,40094,36254,35982,48542,48270,34190,33926,40334,40070,36230,35970,48518,48258,34178,40322,53628,42744,59838,41592,59550,55164,54076,53532,44664,60318,42552,59790,41496,59526,55068,54028,53508,44568,60294,42504,59778,51390,45436,37756,37180,55454,52158,46972,51614,45884,51342,45340,38716,37660,37132,55430,52110,46876,51590,45836,51330,45316,38668,37636,55682,39102,35262,34974,47550,47262,39870,39326,39054,35742,35214,48030,34950,47502,47238,39822,39302,39042,35718,35202,48006,47490,53438,41852,41276,54206,53662,53390,44924,42812,41756,41228,55198,54158,53638,53378,44828,42764,41732,45246,37310,37022,46014,45470,45198,38846,37790,37262,36998,47006,45966,45446,45186,38798,37766,37250,41406,41118,42942,41886,41358,41094,44958,42894,41862,41346,54520,62782,43248,59964,54328,62734,43056,59916,54280,62722,56060,51836,46328,60734,38008,58654,55868,51740,46136,60686,37912,58630,55820,51716,46088,60674,52606,47868,50494,39548,56638,35388,52510,47676,50446,39452,56590,35340,52486,47628,50434,39428,56578,40318,36158,48446,34078,40222,36110,48398,34054,40198,36098,48386,59774,55036,53884,44280,60222,42104,59678,54844,53788,44088,60174,42008,59654,54796,53764,37628,55678,52094,46844,51518,45692,38524,37436,55582,51998,46652,51470,45596,38428,37388,55558,51974,46604,51458,45572,35198,47486,39806,39230,35646,35102,47934,47390,39710,39182,35598,35078,47886,47366,39686,39170,41724,54142,53566,44796],
i=[48732,49518,34524,49446,34380,40668,50982,40524,57126,33484,33348,36556,49954,36420,48844,53026,48708,49506,34500,51042,40644,57186,33070,34606,40750,33646,33574,36718,36646,49006,33126,33058,34662,34594,40806,40738,33634,36706,48994,41696,59576,64046,54128,62684,53552,62540,44768,60344,64238,42592,59800,64102,41504,59528,64034,55088,62924,54032,62660,44576,60296,64226,37232,58460,55480,63022,51640,62062,45936,51352,61990,45360,60492,38768,58844,37680,58572,37136,58436,55432,63010,52120,62182,46896,51592,62050,45840,60612,38672,58820,56200,63202,35e3,57902,52316,47288,60974,50396,39352,50252,39064,58918,56396,35768,58094,35224,57958,48056,34952,57890,47512,52292,47240,60962,50636,39832,50372,39304,58978,56516,35720,58082,52676,48008,61154,33884,50734,40028,56878,49774,36060,49702,35916,48348,52774,48204,34268,33996,40412,33860,40140,50722,40004,56866,49894,36300,49762,36036,48588,52834,48324,34244,50914,40388,57058,33326,36398,34414,34342,40558,40486,33518,33382,36590,33314,36454,48878,36386,48742,34534,34402,40678,40546,33506,36578,48866,41328,59484,53688,62574,53400,62502,42864,59868,41776,59596,41232,59460,55224,62958,54168,62694,53640,62562,44848,60364,42768,59844,55176,62946,37048,58414,55388,51420,45496,51276,45208,60454,37816,58606,37272,58470,37e3,58402,55364,52188,47032,51660,45976,51396,45448,60514,38808,58854,37768,58594,55748,52164,46984,60898,34908,52270,47196,50286,39132,50214,38988,56358,35292,35020,47580,34884,47308,52258,47172,50670,39900,50406,39372,50274,39108,56418,35788,35268,48076,52450,47556,50658,39876,56802,33838,39982,35950,35878,48238,34030,33894,40174,33826,40038,39970,36334,36070,48622,35938,48358,48226,34278,34018,40422,40162,36322,48610,41144,59438,53468,53324,41912,59630,41368,59494,41096,59426,54236,53708,53444,44984,60398,42904,59878,41864,59618,55244,54212,36956,55342,51310,45276,51238,45132,37340,37068,36932,55330,51694,46044,51430,45516,51298,45252,38876,37836,37316,55522,52198,47052,51682,46020,34862,39022,38950,35054,34918,47342,34850,47206,39406,39142,39010,35822,35302,48110,35042,47590,47330,39910,39394,41052,53358,53286,41436,41164,41028,53742,53478,53346,42972,41932,41412,55278,54246,53730,36910,45166,37102,36966,36898,45550,45286,45154,37870,37350,37090,47086,46054,45538,43456,60016,64156,54368,62744,64838,43072,59920,64132,60792,64350,38112,58680,63822,55920,63132,51760,62092,46176,60696,64326,37920,58632,63810,55824,63108,39664,59068,56696,63326,35440,58012,52536,62286,47728,61084,50456,61766,39472,59020,56600,63302,35344,57988,52488,62274,47632,61060,36216,58206,52924,48504,61278,34104,57678,50844,40248,59214,56988,49804,36120,58182,52876,48408,61254,34056,57666,50820,40200,59202,56964,34492,51038,40636,57182,33436,49998,36508,53070,48796,49478,34444,51014,40588,57158,33412,49986,36484,53058,48772,33630,36702,48990,33102,34638,40782,33606,36678,48966,33090,34626,40770,54e3,62652,44512,60280,64222,42208,59704,64078,54896,62876,53808,62604,44128,60184,64198,42016,59656,64066,54800,62852,51576,62046,45808,60604,38640,58812,37488,58524,55608,63054,52024,62158,46704,51480,62022,45616,60556,38448,58764,37392,58500,55560,63042,51976,62146,46608,60804,50364,39288,58974,56508,35704,58078,35128,57934,47992,52380,47416,61006,50588,39736,50316,39192,58950,56460,35608,58054,35080,57922,47896,52356,47368,60994,50564,39688,59074,56708,49758,36028,52830,48316,34236,33948,40380,50766,40092,56910,49870,36252,49734,35980,48540,52806,48268,34188,33924,40332,50754,40068,56898,49858,36228,52930,48516,34398,40542,33502,33358,36574,36430,48862,48718,34510,34374,40654,40518,33478,33346,36550,36418,48838,48706,34498,40642,53624,62558,42736,59836,41584,59548,55160,62942,54072,62670,53528,62534,44656,60316,42544,59788,41488,59524,55064,62918,54024,62658,44560,60292,51388,45432,60510,37752,58590,37176,58446,55452,52156,46968,51612,45880,51340,45336,60486,38712,58830,37656,58566,37128,58434,55428,52108,46872,51588,45832,60610,38664,58818,56196,50270,39100,56414,35260,34972,47548,52302,47260,50654,39868,50382,39324,50246,39052,56390,35740,35212,48028,34948,47500,52290,47236,50630,39820,50370,39300,56514,35716,52674,48004,35934,48222,34014,33870,40158,40014,36318,36046,48606,35910,48334,48198,34254,33990,40398,33858,40134,40002,36294,36034,48582,48322,34242,40386,53436,41848,59614,41272,59470,54204,53660,53388,44920,60382,42808,59854,41752,59590,41224,59458,55196,54156,53636,44824,60358,42760,59842,51294,45244,37308,37020,55374,51678,46012,51406,45468,51270,45196,38844,37788,37260,36996,55362,52174,47004,51654,45964,51394,45444,38796,37764,55746,39006,35038,34894,47326,47182,39390,39118,38982,35806,35278,48094,35014,47566,34882,47302,47170,39886,39366,39106,35782,35266,48070,47554,53342,41404,41116,53726,53454,53318,42940,41884,41356,41092,55262,54222,53702,53442,44956,42892,41860,45150,37086,36942,45534,45262,45126,37854,37326,37062,36930,47070,46030,45510,45250,38862,37830,37314,41182,41038,41950,41422,41158,41026,45022,42958,41926,41410,54512,62780,43232,59960,64142,54320,62732,43040,59912,64130,56056,63166,51832,62110,46320,60732,38e3,58652,55864,63118,51736,62086,46128,60684,37904,58628,55816,63106,52604,47864,61118,50492,39544,59038,56636,35384,57998,52508,47672,61070,50444,39448,59014,56588,35336,57986,52484,47624,61058,50878,40316,57022,49822,36156,52894,48444,34076,50830,40220,56974,49798,36108,52870,48396,34052,50818,40196,56962,36542,48830,34462,40606,33422,36494,48782,34438,40582,33410,36482,48770,59772,55032,62910,53880,62622,44272,60220,42096,59676,54840,62862,53784,62598,44080,60172,42e3,59652,54792,62850,37624,58558,55676,52092,46840,51516,45688,60574,38520,58782,37432,58510,55580,51996,46648,51468,45592,60550,38424,58758,37384,58498,55556,51972,46600,60802,35196,52414,47484,50622,39804,50334,39228,56478,35644,35100,47932,52366,47388,50574,39708,50310,39180,56454,35596,35076,47884,52354,47364,50562,39684,56706,33982,40126,36286,35998,48574,48286,34206,33934,40350,40078,36238,35974,48526,48262,34182,33922,40326,40066,36226,48514,41720,59582,54140,53564,44792,60350,42616,59806,41528,59534,55100,54044,53516,44600,60302,42520,59782,41480,59522,55052,54020,37244,55486,51646,45948,51358,45372,38780,37692,37148,55438,52126,46908,51598,45852,51334,45324,38684,37644,37124,55426,52102,46860,51586,45828,35006,47294,39358,39070,35774,35230,48062,34958,47518,47246,39838,39310,39046,35726,35206,48014,34946,47494,47234,39814,39298,41340,53694,53406,42876,41788,41244,55230,54174,53646,53382,44860,42780,41740,41220,55182,54150,53634,37054,45502,45214,37822,37278,37006,47038,45982,45454,45190,38814,37774,37254,36994,46990,45958,45442,41150,41918,41374,41102,44990,42910,41870,41350,41090,43504,60028,54392,62750,43120,59932,54296,62726,43024,59908,60798,38136,58686,55932,51772,46200,60702,37944,58638,55836,51724,46104,60678,37896,58626,55812,39676,56702,35452,52542,47740,50462,39484,56606,35356,52494,47644,50438,39436,56582,35332,52482,47620,36222,48510,34110,40254,36126,48414,34062,40206,36102,48390,34050,40194,54012,44536,60286,42232,59710,54908,53820,44152,60190,42040,59662,54812,53772,44056,60166,41992,59650,51582,45820,38652,37500,55614,52030,46716,51486,45628,38460,37404,55566,51982,46620,51462,45580,38412,37380,55554,39294,35710,35134,47998,47422,58528,63784,55616,63056,64916,52032,62160,60832,64360,35136,57936,52384,62248,47424,61008,64404,50592,61800,39744,59088,63924,56736,63336,64986,33952,57640,50768,61844,40096,59176,63946,56912,63380,49872,36256,58216,52944,62388,48544,61288,64474,33360,49960,36432,58260,53032,62410,48720,61332,49512,34512,57780,51048,61914,40656,59316,57192,63450,33064,49556,34600,57802,51092,40744,59338,33640,50100,36712,58330,53172,49e3,61402,59552,64040,54080,62672,64820,60320,64232,37184,58448,63764,55456,63016,64906,51616,62056,45888,60624,64308,38720,58832,63860,56224,63208,64954,34976,57896,52304,62228,47264,60968,64394,50384,61748,39328,58984,63898,56528,63284,35744,58088,52688,62324,48032,61160,64442,33872,57620,50728,61834,40016,59156,56872,63370,49768,36048,58164,52840,62362,48336,61236,34256,57716,50920,61882,40400,59252,57064,63418,33320,49940,36392,58250,53012,49460,34408,57754,50996,40552,59290,57140,33512,50036,36584,58298,53108,48872,61370,33044,49546,34580,51082,33588,50074,36660,53146,33140,49594,34676,51130,40820,57274,41280,59472,64020,53664,62568,64794,42816,59856,64116,55200,62952,64890,37024,58408,63754,55376,62996,51408,62004,45472,60520,64282,37792,58600,63802,55760,63092,52176,62196,47008,60904,64378,34896,57876,52264,62218,47184,60948,50280,61722,39120,58932,56424,63258,35280,57972,52456,62266,47568,61044,50664,61818,39888,59124,56808,63354,33832,57610,50708,39976,59146,49716,35944,58138,52788,48232,61210,34024,57658,50804,40168,59194,56948,49908,36328,58234,52980,48616,61306,33300,49930,36372,49434,34356,50970,40500,33396,49978,36468,53050,48756,49530,34548,51066,40692,57210,33034,34570,33562,36634,33082,34618,40762,33658,36730,49018,41120,59432,64010,53456,62516,41888,59624,64058,54224,62708,44960,60392,64250,36944,58388,55336,62986,51304,61978,45264,60468,37328,58484,55528,63034,51688,62074,46032,60660,38864,58868,56296,63226,34856,57866,52244,50228,39016,58906,56372,35048,57914,52340,47336,60986,50420,39400,59002,56564,35816,58106,52724,48104,61178,33812,50698,49690,35892,52762,33908,50746,40052,56890,49786,36084,52858,48372,34292,50938,40436,57082,33290,34330,33338,36410,34426,40570,33530,36602,48890,41040,59412,53352,62490,41424,59508,53736,62586,42960,59892,55272,62970,36904,58378,51252,45160,60442,37096,58426,55412,51444,45544,60538,37864,58618,55796,52212,47080,60922,34836,50202,38964,34932,52282,47220,50298,39156,56442,35316,52474,47604,50682,39924,56826,33802,35866,33850,39994,35962,48250,34042,40186,36346,48634,59968,64144,60768,64344,58656,63816,55872,63120,64932,39616,59056,63916,56672,63320,64982,35392,58e3,52512,62280,47680,61072,64420,36192,58200,52912,62380,48480,61272,64470,34080,57672,50832,61860,40224,59208,63954,56976,63396,34480,57772,51032,61910,40624,59308,57176,63446,33424,49992,36496,58276,53064,62418,48784,61348,33624,50092,36696,58326,53164,48984,61398,33096,49572,34632,57810,51108,40776,59346,57252,53952,62640,64812,60256,64216,59680,64072,54848,62864,64868,51552,62040,45760,60592,64300,38592,58800,63852,37440,58512,63780,55584,63048,64914,52e3,62152,46656,60816,64356,50352,61740,39264,58968,63894,56496,63276,35680,58072,35104,57928,47968,52368,62244,47392,61e3,64402,50576,61796,39712,59080,63922,56720,63332,49752,36016,58156,52824,62358,48304,61228,34224,57708,33936,57636,40368,50760,61842,40080,59172,56904,63378,49864,36240,58212,52936,62386,48528,61284,49452,34392,57750,50988,40536,59286,57132,33496,33352,36568,49956,36424,58258,48856,53028,48712,61330,49508,34504,57778,51044,40648,59314,57188,33580,50070,36652,53142,33132,33060,34668,49554,34596,40812,51090,40740,33636,50098,36708,53170,48996,53600,62552,64790,42688,59824,64108,41536,59536,64036,55136,62936,64886,54048,62664,64818,44608,60304,64228,51376,61996,45408,60504,64278,37728,58584,63798,37152,58440,63762,55440,63012,52144,62188,46944,51600,62052,45856,60616,64306,38688,58824,63858,56208,63204,50264,61718,39088,58924,56408,63254,35248,57964,34960,57892,47536,52296,62226,47248,60964,50648,61814,39856,50376,61746,39312,58980,56520,63282,35728,58084,52680,62322,48016,61156,49708,35928,58134,52780,48216,61206,34008,57654,33864,57618,40152,50724,40008,59154,56868,49900,36312,49764,36040,58162,48600,52836,48328,61234,34248,57714,50916,40392,59250,57060,49430,34348,50966,40492,33388,33316,36460,49938,36388,48748,53010,49526,34540,49458,34404,40684,50994,40548,57138,33508,50034,36580,53106,48868,33558,36630,33078,33042,34614,34578,40758,33654,33586,36726,36658,49014,33138,34674,40818,53424,62508,41824,59608,64054,41248,59464,64018,54192,62700,53648,62564,44896,60376,64246,42784,59848,64114,55184,62948,51288,61974,45232,60460,37296,58476,37008,58404,55368,62994,51672,62070,46e3,51400,62002,45456,60516,38832,58860,37776,58596,55752,63090,52168,62194,46992,60900,50220,39e3,58902,56364,35032,57910,34888,57874,47320,52260,47176,60946,50412,39384,50276,39112,58930,56420,35800,58102,35272,57970,48088,52452,47560,61042,50660,39880,59122,56804,49686,35884,52758,33900,33828,40044,50706,39972,49782,36076,49714,35940,48364,52786,48228,34284,34020,40428,50802,40164,56946,49906,36324,52978,48612,34326,33334,33298,36406,36370,34422,34354,40566,40498,33526,33394,36598,36466,48886,48754,34546,40690,53336,62486,41392,59500,41104,59428,53720,62582,53448,62514,42928,59884,41872,59620,55256,62966,54216,62706,44944,60388,51244,45144,60438,37080,58422,36936,58386,55332,51436,45528,51300,45256,60466,37848,58614,37320,58482,55524,52204,47064,51684,46024,60658,38856,58866,56292,50198,38956,34924,34852,47212,52242,50294,39148,50226,39012,56370,35308,35044,47596,52338,47332,50678,39916,50418,39396,56562,35812,52722,48100,35862,33846,33810,39990,35958,35890,48246,34038,33906,40182,40050,36342,36082,48630,48370,34290,40434,53292,41176,59446,41032,59410,53484,53348,41944,59638,41416,59506,54252,53732,45016,60406,42952,59890,51222,36972,36900,51318,45292,51250,45156,37356,37092,55410,51702,46060,51442,45540,38892,37860,55794,34870,34834,39030,38962,35062,34930,47350,47218,39414,39154,35830,35314,48118,47602,54464,62768,64844,59936,64136,56032,63160,64942,51808,62104,46272,60720,64332,37952,58640,63812,55840,63112,64930,52592,62300,47840,61112,64430,50480,61772,39520,59032,63910,56624,63308,35360,57992,52496,62276,47648,61064,64418,50872,61870,40304,59228,57016,63406,49816,36144,58188,52888,62374,48432,61260,34064,57668,50824,61858,40208,59204,56968,63394,50012,36536,58286,53084,48824,61358,49484,34456,57766,51020,40600,59302,57164,33416,49988,36488,58274,53060,48776,61346,49582,34652,51118,40796,57262,33612,50086,36684,53158,48972,33092,49570,34628,51106,40772,57250,59760,64092,55008,62904,64878,53856,62616,64806,44224,60208,64204,42048,59664,64068,54816,62856,64866,37600,58552,63790,55664,63068,52080,62172,46816,51504,62028,45664,60568,64294,38496,58776,63846,37408,58504,63778,55568,63044,51984,62148,46624,60808,64354,35184,57948,52408,62254,47472,61020,50616,61806,39792,50328,61734,39216,58956,56472,63270,35632,58060,35088,57924,47920,52360,62242,47376,60996,50568,61794,39696,59076,56712,63330,33976,57646,50780,40120,59182,56924,49884,36280,49740,35992,58150,48568,52812,48280,61222,34200,57702,33928,57634,40344,50756,40072,59170,56900,49860,36232,58210,52932,48520,61282,33372,49966,36444,53038],j=[[[20,16,38]],[[1,9,31],[9,31,26],[31,26,2],[26,2,12]],[[2,12,17],[12,17,23],[17,23,37],[23,37,18]],[[37,18,22],[18,22,6],[22,6,27],[6,27,44]],[[27,44,15],[44,15,43],[15,43,39],[43,39,11]],[[39,11,13],[11,13,5],[13,5,41],[5,41,33]],[[41,33,36],[33,36,8],[36,8,4],[8,4,32]],[[4,32,3],[32,3,19],[3,19,40],[19,40,25]],[[40,25,29],[25,29,10],[29,10,24],[10,24,30]],[[20,16,38],[0,0,0],[0,0,0],[0,0,0]]],k=[[1,0,0,1],[0,1,0,1],[1,1,0,0],[0,0,1,1],[1,0,1,0],[0,1,1,0],[1,1,1,1],[0,0,0,0]],l=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","\u2000","\u2001",e.default.FNC1,e.default.FNC2,e.default.FNC3,"\u2005"],m=["\u2000 ","\u2000A","\u2000B","\u2000C","\u2000D","\u2000E","\u2000F","\u2000G","\u2000H","\u2000I","\u2000J","\u2000K","\u2000L","\u2000M","\u2000N","\u2000O","\u2000P","\u2000Q","\u2000R","\u2000S","\u2000T","\u2000U","\u2000V","\u2000W","\u2000X","\u2000Y","\u2000Z","\u20001","\u20002","\u20003","\u20004","\u20005"," ","\u20006","\u20007","\u20008","$","%","\u20009","\u20000","\u2000-","\u2000.","\u2000$","+","\u2000/","-",".","/","0","1","2","3","4","5","6","7","8","9","\u2000+","\u20011","\u20012","\u20013","\u20014","\u20015","\u20016","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","\u20017","\u20018","\u20019","\u20010","\u2001-","\u2001.","\u2001A","\u2001B","\u2001C","\u2001D","\u2001E","\u2001F","\u2001G","\u2001H","\u2001I","\u2001J","\u2001K","\u2001L","\u2001M","\u2001N","\u2001O","\u2001P","\u2001Q","\u2001R","\u2001S","\u2001T","\u2001U","\u2001V","\u2001W","\u2001X","\u2001Y","\u2001Z","\u2001$","\u2001/","\u2001+","\u2001%","\u2001 "],n=48,o="10",p="1111";function r(a){var b=/^\d{5,81}$/;return b.test(a)}function s(a){var b,c,d=l.indexOf(a);return d>-1?[d]:(b=a.charCodeAt(0),c=m[b],(0,f.str2Array)(c).map(function(a){return l.indexOf(a)}))}function t(a,b){var c=j[a][b];return{x:c[0],y:c[1],z:c[2]}}function u(a,b,c,d){var e=b===d-1,f=void 0,g=void 0;return f=!e&&k[b][c],g=f?h[a]:i[a],g.toString(2)}function v(a,b){for(var c=2,d=1;c<b;)d+=c,c++;return d+a}function w(a){return(0,f.str2Array)(a).reduce(function(a,b){return l.indexOf(b)>-1?a++:a+=2,a},0)}function x(a){for(var b,c=0,d=1,e=a.length,f=[];d<e;)b=a.substring(c,d),w(b)>48&&(f.push(a.substring(c,d-1)),c=d-1),d++;return f.push(a.substring(c,d)),f}b.default={defaultConfig:g,isNumericOnly:r,getCharValue:s,CODE_NS:n,getWeight:t,getParityPattern:u,START_PATTERN:o,STOP_PATTERN:p,getGroupInfo:v,getTextGroup:x}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(4),f=n(e),g=c(2),h=n(g),i=c(0),j=c(36),k=n(j),l=c(1);function n(a){return a&&a.__esModule?a:{default:a}}function o(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function p(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function q(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}m=function(a){q(b,a);function b(a){var c,d,e,f,g,h,i,j,l;return o(this,b),c=p(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,k.default.defaultConfig)),d=c.config,e=d.text,f=d.checkDigit,g=d.fullASCII,h=g?k.default.getFullASCIIChar(e):e,f?(i=c.checksum(h),j=c.checksum(h+i),c.text=k.default.START_STOP_CHARACTERS+h+i+j+k.default.START_STOP_CHARACTERS):c.text=k.default.START_STOP_CHARACTERS+h+k.default.START_STOP_CHARACTERS,c.label=e,l=c.calculateData(),c.adjustDesiredSize(l),c.convertToShape(l),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.text,d=b.fullASCII,e=/^[0-9A-Z\-\.\ \$\/\+\%]+$/;if(!d&&!e.test(c))throw new l.InvalidTextException(c)}},{key:"encode",value:function a(b){var c="";return(0,i.str2Array)(b).forEach(function(a,b){c+=(0,i.isEven)(b)?(0,i.strRepeat)("1",+a):(0,i.strRepeat)("0",+a)}),c}},{key:"calculateData",value:function a(){var b=this,c=this.text,d="";return(0,i.sliceString)(c,1,function(a){d+=b.encode(k.default.getCode93Code(a))}),d+=k.default.TERMINATION}},{key:"checksum",value:function a(b){var c=1,d=(0,i.str2Array)(b).reduceRight(function(a,b){var d=k.default.getCode93Value(b);return c>20&&(c=1),a+=c*d,c++,a},0);return k.default.getCharByValue(d%47)}}]),b}(f.default),b.default=m,h.default.registerEncoder("Code93",m)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=c(1),e=c(0),f=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","[","]","{","}","*"],g=["131112","111213","111312","111411","121113","121212","121311","111114","131211","141111","211113","211212","211311","221112","221211","231111","112113","112212","112311","122112","132111","111123","111222","111321","121122","131121","212112","212211","211122","211221","221121","222111","112122","112221","122121","123111","121131","311112","311211","321111","112131","113121","211131","121221","312111","311121","122211","111141"],h=["]U","[A","[B","[C","[D","[E","[F","[G","[H","[I","[J","[K","[L","[M","[N","[O","[P","[Q","[R","[S","[T","[U","[V","[W","[X","[Y","[Z","]A","]B","]C","]D","]E"," ","{A","{B","{C","{D","{E","{F","{G","{H","{I","{J","{K","{L","{M","{N","{O","0","1","2","3","4","5","6","7","8","9","{Z","]F","]G","]H","]I","]J","]V","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","]K","]L","]M","]N","]O","]W","}A","}B","}C","}D","}E","}F","}G","}H","}I","}J","}K","}L","}M","}N","}O","}P","}Q","}R","}S","}T","}U","}V","}W","}X","}Y","}Z","]P","]Q","]R","]S","]T"];function l(a){var b=f.indexOf(a);if(b===-1)throw new d.InvalidCharException(a);return g[b]}function m(a){var b=f.indexOf(a);if(b===-1)throw new d.InvalidCharException(a);return b}function n(a){return f[a]}i={height:60,showLabel:!0,checkDigit:!1,fullASCII:!1,quietZone:{top:0,right:10,bottom:0,left:10},labelPosition:"bottom"},j="*",k="1";function o(a){var b="";return(0,e.sliceString)(a,1,function(c){var e=h[c.charCodeAt(0)];if(!e)throw new d.InvalidTextException(a);b+=e}),b}b.default={getCode93Code:l,START_STOP_CHARACTERS:j,defaultConfig:i,TERMINATION:k,getCode93Value:m,getCharByValue:n,getFullASCIIChar:o}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(7),f=r(e),g=c(2),h=r(g),i=c(38),j=r(i),k=c(39),l=r(k),m=c(42),n=r(m),o=c(1),p=c(0);function r(a){return a&&a.__esModule?a:{default:a}}function s(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function t(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function u(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}q=function(a){u(b,a);function b(a){var c,d,e,f,g,h,i,k,m,p,q,r;switch(s(this,b),c=t(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,j.default.defaultConfig)),d=c.config,e=d.text,f=d.eccMode,g=d.ecc200SymbolSize,h=d.ecc200EncodingMode,i=d.structuredAppend,k=d.structureNumber,m=d.fileIdentifier,p=d.ecc000_140SymbolSize,k=+k,m=+m,q=void 0,f){case"ECC200":q=new l.default(e,{ecc200SymbolSize:g,ecc200EncodingMode:h,structuredAppend:i,structureNumber:k,fileIdentifier:m});break;case"ECC000":case"ECC050":case"ECC080":case"ECC100":case"ECC140":q=new n.default(e,{ecc000_140SymbolSize:p,structuredAppend:i,structureNumber:k,fileIdentifier:m,eccMode:f});break;default:throw new o.BadArgumentsException(f)}return r=q.getMatrix(),c.adjustDesiredSize(r),c.convertToShape(r),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.text,d=b.eccMode,e=b.structureNumber,f=b.fileIdentifier,g=void 0;if(g="ECC200"===d?/^[\x00-\xFF\u2000\u2004\u2005]+$/:/^[\x00-\xFF]+$/,!g.test(c))throw new o.InvalidTextException(c);if(!(0,p.isNumberLike)(e))throw new o.BadArgumentsException({structureNumber:e});if(!(0,p.isNumberLike)(f))throw new o.BadArgumentsException({fileIdentifier:f})}}]),b}(f.default),b.default=q,h.default.registerEncoder("DataMatrix",q)},function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d={eccMode:"ECC200",ecc200SymbolSize:"squareAuto",ecc200EncodingMode:"auto",ecc000_140SymbolSize:"auto",structuredAppend:!1,structureNumber:0,fileIdentifier:0,quietZone:{top:2,left:2,right:2,bottom:2}};b.default={defaultConfig:d}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n;Object.defineProperty(b,"__esModule",{value:!0}),d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},e=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),f=c(8),g=o(f),h=c(40),i=o(h),j=c(1),k=c(41),l=o(k),m=c(0);function o(a){return a&&a.__esModule?a:{default:a}}function p(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function q(a){return a%256}function r(a){return Math.ceil(a-1e-5)}n=function(){function a(b,c){p(this,a),this.text=b,this.m_symbol=(0,m.str2Array)(b).map(function(a){return a.charCodeAt(0)}),this.symbolSize=c.ecc200SymbolSize,this.encodingMode=c.ecc200EncodingMode,this.structuredAppend=c.structuredAppend,this.structureNumber=c.structureNumber,this.fileIdentifier=c.fileIdentifier,this.symbolInfo=g.default.getSymbolInfo("square144"),this.m_code=[]}return e(a,[{key:"preEncode",value:function a(b,c){var d=this.m_symbol,e=this.m_code,f=d[0];return f!=g.default.CONSTANTS.Macro05Input&&f!=g.default.CONSTANTS.Macro06Input||(e[b++]=f==g.default.CONSTANTS.Macro05Input?g.default.CONSTANTS.Macro05:g.default.CONSTANTS.Macro06,c++),{c_pos:b,s_pos:c}}},{key:"checkValue",value:function a(b){var c,d=this.encodingMode,e=this.m_symbol;if("X12"==d||"EDIFACT"==d)for(c=b;c<e.length;++c)if(g.default.getTripletCharValue(d,e[c])==g.default.CONSTANTS.InvalidTripletValue)return!1;return!0}},{key:"getEncodingLength",value:function a(b,c,d,e){var f,h=this.m_symbol;if(c<0)return-1;switch(f=0,b){case"ASCII":f=c;break;case"C40":case"Text":f=2*~~((c+2)/3)+2;break;case"X12":f=2*~~((c+2)/3)+2,(c%3==1||c%3==2&&g.default.isDigit(h[d+e-2])&&g.default.isDigit(h[d+e-1]))&&--f;break;case"EDIFACT":++c,f=3*~~(c/4)+c%4+1;break;case"Base256":f=c+1+(c>g.default.CONSTANTS.Base256SmallBlockSize?2:1)}return f}},{key:"getMaxProperLength",value:function a(b,c){for(var d=this.m_symbol,e=this.encodingMode,f=b+1,g=d.length,h=~~((f+g)/2),i=0,j=0;f<g;)h=~~((f+g)/2),j=this.getEncodingLength(e,this.getCodeWordLength(e,b,h-b),b,h-b),j<0||j>c?g=h-1:f=h+1;return f>b&&(h<f?(j=this.getEncodingLength(e,this.getCodeWordLength(e,b,f-b),b,f-b),i=j<0||j>c?h-b:f-b):(h=Math.min(h,f),j=this.getEncodingLength(e,this.getCodeWordLength(e,b,h-b),b,h-b),i=j<0||j>c?h-b-1:h-b)),Math.max(0,i)}},{key:"getCodeWordLength",value:function a(b,c,d){var e,f,h=this.m_symbol,i=0;for(e=c;e<c+d;++e){switch(f=h[e],b){case"ASCII":i+=f>g.default.ASCIIMax&&f!=g.default.CONSTANTS.FNC1Input?2:1,e<c+d-1&&g.default.isDigit(f)&&g.default.isDigit(h[e+1])&&++e;break;case"C40":case"Text":f==g.default.CONSTANTS.FNC1Input?f=g.default.CONSTANTS.TripletFNC1:f>g.default.ASCIIMax&&(i+=2,f-=128),i+=0==g.default.getTripletCharSetChannel(b,f)?1:2;break;case"X12":g.default.getX12Value(f)==g.default.CONSTANTS.InvalidTripletValue?i=-1:i+=1;break;case"EDIFACT":g.default.getEDIFACTValue(f)==g.default.CONSTANTS.InvalidTripletValue?i=-1:i+=1;break;case"Base256":i+=1}if(i<0)return i}return i}},{key:"getCodeLength",value:function a(b){return this.getEncodingLength(b.charSet,this.getCodeWordLength(b.charSet,b.start,b.length),b.start,b.length)}},{key:"getEncodingUnitsInfomative",value:function a(b,c,d){var e,f,h,i,j,k,l,m,n,o,p,q,r,s=this.m_symbol;for(d=0,e=[],f={charSet:"ASCII",start:b,length:0,codeWords:0,encodingLength:0},h=0,i=b,j=!1,k=s.length;i<k;){switch(j=!1,f.charSet){case"ASCII":for(l=0;i+l<s.length-1&&h+~~(l/2)<c&&g.default.isDigit(s[i+l])&&g.default.isDigit(s[i+l+1]);)l+=2;if(l>0){if(f.codeWords+=l>>1,i+=l,h+~~(l/2)>=c)break;continue}break;case"C40":case"Text":case"X12":do s[i]>g.default.ASCIIMax&&(f.codeWords+=2),g.default.getTripletCharSetChannel(f.charSet,s[i])>0&&++f.codeWords,++f.codeWords,++i;while(f.codeWords%3!=0&&i<k);break;case"EDIFACT":do++f.codeWords,++i;while(0!=(3&f.codeWords)&&i<k);break;case"Base256":++f.codeWords,++i}m=0,n=c-(h+this.getEncodingLength(f.charSet,f.codeWords,f.start,i-f.start)),o=n,p=f.charSet;do q=this.lookAhead(f.charSet,i,o,m),m=q.d_len,p=q.newCharset;while(--o>0&&this.getEncodingLength(p,this.getCodeWordLength(p,i,m),i,m)>n);if(o<=0)break;if(f.charSet!=p&&(f.length=i-f.start,f.length>0&&(e.push(f),h+=this.getEncodingLength(f.charSet,f.codeWords,f.start,f.length)),f={charSet:p,start:i,length:0,codeWords:0,encodingLength:0},j=!0),r=c-1,"EDIFACT"==f.charSet&&--r,"Base256"==f.charSet&&++r,h+this.getEncodingLength(f.charSet,f.codeWords,f.start,f.length)>=r)break;j||"ASCII"!=f.charSet||(++f.codeWords,++i)}for(f.length=i-f.start,f.length>0&&(e.push(f),h+=this.getEncodingLength(f.charSet,f.codeWords,f.start,f.length)),f={charSet:"ASCII",start:i,length:0,codeWords:0,encodingLength:0};h<c&&i<s.length;){if(s[i]>g.default.ASCIIMax&&s[i]!=g.default.CONSTANTS.FNC1Input){if(!(h<c-1))break;++h}++h,++i}return i>f.start&&(f.length=i-f.start,e.push(f)),d=i-b,{s_taken:d,units:g.default.mergeUnits(e)}}},{key:"getEncodingUnits",value:function a(b,c){var d,e,f=this,g=this.encodingMode,h=this.m_symbol,i=void 0,j=0,k=0;return b<h.length&&("auto"!==g?("C40"!==g&&"Text"!=g&&"X12"!=g||c++,k=this.getMaxProperLength(b,c),d={charSet:g,start:b,length:k,codeWords:0,encodingLength:0},d.codeWords=this.getCodeWordLength(d.charSet,d.start,d.length),d.encodingLength=this.getEncodingLength(d.charSet,d.codeWords,d.start,d.length),"X12"==d.charSet&&d.encodingLength==c&&d.codeWords%3==2&&(--k,--d.length,--d.codeWords,--d.encodingLength),i=[d]):(e=this.getEncodingUnitsInfomative(b,c,k),i=e.units,k=e.s_taken),i.forEach(function(a,b){j+=f.getCodeLength(a),b===i.length-1&&("C40"===a.charSet||"Text"===a.charSet||"X12"===a.charSet&&1===(1&a.codeWords))&&j--})),{c_length:j,s_taken:k,units:i}}},{key:"encodeStructureHeader",value:function a(b,c,d){var e,f=this.m_code,h=this.structureNumber;return f[b++]=g.default.CONSTANTS.StructuredAppand,f[b++]=q(h<<4|16-c+1),e=this.generateFileIdentifier(c,d),f[b++]=q(e>>8),f[b++]=q(e),b}},{key:"generateFileIdentifier",value:function a(b,c){var d=this.m_symbol,e=this.fileIdentifier,f=c%g.default.CONSTANTS.FileIdentifierMax+1,h=0==e?d.length%g.default.CONSTANTS.FileIdentifierMax+1:e;return f<<8|h}},{key:"encode",value:function a(b,c){switch(b.charSet){case"ASCII":return this.encodeASCII(b.start,b.length,c);case"C40":return this.encodeC40(b.start,b.length,c);case"Text":return this.encodeText(b.start,b.length,c);case"X12":return this.encodeX12(b.start,b.length,c);case"EDIFACT":return this.encodeEDIFACT(b.start,b.length,c);case"Base256":return this.encodeBase256(b.start,b.length,c)}}},{key:"encodeASCII",value:function a(b,c,d){var e,f,h,i,j=this.m_code,k=this.m_symbol;for(e=b,f=b+c;e<f;e++)h=g.default.getCharType(k[e]),"FNC1"!==h?"ExtendedASCII"!==h?e+1<f&&"Numeric"===h&&"Numeric"===g.default.getCharType(k[e+1])?(i=10*(k[e]-48)+(k[e+1]-48)+130,j.push(i),d++,e++):(j.push(k[e]+1),d++):(j.push(g.default.CONSTANTS.ASCIIUpperShift),j.push(k[e]-g.default.ASCIIMax),d+=2):(j.push(g.default.CONSTANTS.ASCIIFNC1),d++);return d}},{key:"encodeTriplet",value:function a(b,c,d,e){var f,h,i,k,l,m=this.m_code,n=this.m_symbol,o=this.symbolInfo,p=0,q=0,r=null,s=[],t=c;switch(b){case"C40":m[e++]=g.default.CONSTANTS.LatchToC40;break;case"Text":m[e++]=g.default.CONSTANTS.LatchToText;break;case"X12":m[e++]=g.default.CONSTANTS.LatchToX12}for(;t<c+d;)r=g.default.getTripletEncodeValues(b,n[t++]),s=s.concat(r);for(;e<=o.symbolDataCapacity-2&&s.length>=3;){for(p=0,q=0;q<3;++q)p*=40,p+=s.length>0?s.shift():0;++p,m[e++]=p>>8,m[e++]=255&p}if(f=!0,s.length>0){if(h=o.symbolDataCapacity-e,i=c+d-s.length,k=s.length,0===h||s.length>3||s.length>=2&&h<2||"X12"===b&&this.getEncodingLength("ASCII",this.getCodeWordLength("ASCII",i,k),i,k)>(1==h?1:h-1))throw new j.TextTooLargeException;if(f=!1,h>=2)if("X12"==b)m[e++]=g.default.CONSTANTS.TripletUnlatch,e=this.encodeASCII(t-s.length,s.length,e);else{for(f=!0,q=0,p=0;q<3;++q)p*=40,
p+=s.length>0?s.shift():0;++p,m[e++]=p>>8,m[e++]=255&p}else l=n[t-1],"X12"===b?e=this.encodeASCII(t-1,1,e):(l>g.default.ASCIIMax&&(m[e-2]=g.default.CONSTANTS.TripletUnlatch,--e),e=this.encodeASCII(t-1,1,e))}return f&&e<o.symbolDataCapacity&&(m[e++]=g.default.CONSTANTS.TripletUnlatch),e}},{key:"encodeC40",value:function a(b,c,d){return this.encodeTriplet("C40",b,c,d)}},{key:"encodeText",value:function a(b,c,d){return this.encodeTriplet("Text",b,c,d)}},{key:"encodeX12",value:function a(b,c,d){return this.encodeTriplet("X12",b,c,d)}},{key:"encodeEDIFACT",value:function a(b,c,d){var e,f,h,i,k,l=this.m_code,m=this.m_symbol,n=this.symbolInfo,o=this.text;for(l[d++]=g.default.CONSTANTS.LatchToEDIFACT,e=0,f=0,h=b,i=!1;h<b+c&&n.symbolDataCapacity-d>2;){for(e=0,f=0;e<4;){if(++e,k=0,h<b+c){if(k=g.default.getEDIFACTValue(m[h++]),k==g.default.CONSTANTS.InvalidTripletValue)throw new j.InvalidTextException(o)}else{if(h!=b+c){--e;break}k=g.default.CONSTANTS.EDIFACTUnlatch,i=!0,++h}f<<=6,f|=k}switch(e){case 1:l[d++]=q(f<<2);break;case 2:l[d++]=q(f>>4),l[d++]=q(f<<4);break;case 3:l[d++]=q(f>>10),l[d++]=q(f>>2),l[d++]=q(f<<6);break;case 4:l[d++]=q(f>>16),l[d++]=q(f>>8),l[d++]=q(f)}}return h<b+c?d=this.encodeASCII(h,m.length-h,d):!i&&d<n.symbolDataCapacity-2&&(l[d++]=g.default.CONSTANTS.EDIFACTUnlatch<<2),d}},{key:"encodeBase256",value:function a(b,c,d){var e,f=this.m_code,h=this.m_symbol;for(f[d++]=g.default.CONSTANTS.LatchToBase256,c>g.default.CONSTANTS.Base256SmallBlockSize&&(f[d++]=g.default.getRandomizedValue(g.default.CONSTANTS.Base256SmallBlockSize+c/g.default.CONSTANTS.Base256SmallBlockSize,d,g.default.CONSTANTS.Base256RandomBase)),f[d++]=g.default.getRandomizedValue(c%(g.default.CONSTANTS.Base256SmallBlockSize+1),d,g.default.CONSTANTS.Base256RandomBase),e=b;e<b+c;++e)f[d++]=g.default.getRandomizedValue(h[e],d,g.default.CONSTANTS.Base256RandomBase);return d}},{key:"padRight",value:function a(b){var c,d=this.m_code,e=this.symbolInfo;for(c=b;c<e.symbolDataCapacity;c++)d[c]=c==b?g.default.CONSTANTS.ASCIIPad:g.default.getRandomizedValue(g.default.CONSTANTS.ASCIIPad,c+1,g.default.CONSTANTS.PadRandomBase)}},{key:"lookAhead",value:function a(b,c,e,f){var h,i,j,k,l,m,n,o,p,q,s,t={ASCII:0,C40:1,Text:2,X12:3,EDIFACT:4,Base256:5},u=["ASCII","C40","Text","X12","EDIFACT","Base256"],v=this.m_symbol;f=0,b=t[b],h=!1,i=c,j=[0,1,1,1,1,1.25],k=t.C40,b!=t.ASCII&&(j=j.map(function(a){return++a})),j[b]=0,l=function a(){var b,d,l,m,n,o,p,q,s,w,x,y,z;if(++f,b=v[c],d=b==g.default.CONSTANTS.FNC1Input,l=b>g.default.ASCIIMax,j.forEach(function(a,c){switch(c){case t.ASCII:l&&!d?j[c]=r(j[c])+2:g.default.isDigit(b)?j[c]+=.5:j[c]+=1;break;case t.C40:case t.Text:d?j[c]+=4/3:l?j[c]+=8/3:j[c]+=0==g.default.getTripletCharSetChannel(u[c],b)?2/3:4/3;break;case t.X12:l?j[c]+=13/3:g.default.getX12Value(b)!=g.default.CONSTANTS.InvalidTripletValue?j[c]+=2/3:j[c]+=10/3;break;case t.EDIFACT:l?j[c]+=4.25:g.default.getEDIFACTValue(b)!=g.default.CONSTANTS.InvalidTripletValue?j[c]+=.75:j[c]+=3.25;break;case t.Base256:j[c]+=1}}),m=[t.ASCII,t.Base256,t.EDIFACT,t.Text,t.X12,t.C40],f>3)for(n=0;n<m.length;++n){for(o=!0,p=m[n],q=j[p]+1,s=0;s<j.length;++s){switch(p){case t.ASCII:j[s]<q&&(o=!1);break;case t.Base256:(s==t.ASCII&&j[s]<q||j[s]<=q)&&(o=!1);break;case t.EDIFACT:case t.Text:case t.X12:j[s]<=q&&(o=!1);break;case t.C40:if(s!=t.X12&&j[s]<=q)o=!1;else if(q-=1,j[s]<q)o=!1;else if(j[s]==q){for(w=i;w<=c;){if(x=g.default.getX12Value(v[w++]),x<3)return{v:u[t.X12]};if(x==g.default.CONSTANTS.InvalidTripletValue)break}return{v:u[t.C40]}}}if(!o)break}if(o){k=p;break}}for(++c,y=0,z=0;z<j.length;++z)r(j[z])<r(j[y])&&(y=z);if(c<v.length&&r(j[y])>=e)return h=!0,"break"};a:for(;c<v.length&&c-i<=g.default.CONSTANTS.MaxLookAheadOffset;)switch(m=l()){case"break":break a;default:if("object"===(void 0===m?"undefined":d(m)))return m.v}if(c==v.length||c-i>=g.default.CONSTANTS.MaxLookAheadOffset||h){for(n=0,o=0;o<j.length;++o)j[o]=r(j[o]),j[o]<j[n]&&(n=o);if(0==n||n==t.C40)k=n;else{for(p=!0,q=j[n],s=0;s<j.length;++s)if(s!=n&&j[s]<=q){p=!1;break}p&&(k=n)}}return{d_len:f,newCharset:u[k]}}},{key:"getMatrix",value:function a(){var b,c,d,e,f,h,i,k,m,n,o,p,r,s,t,u,v,w,x=this,y=this.text,z=this.m_symbol,A=this.symbolSize,B=this.structuredAppend,C=this.structureNumber,D=this.symbolInfo;if(z.length>3116*(B?16:1))throw new j.TextTooLargeException;if(b=0,c=0,d=0,e=0,f=0,"squareAuto"!==A&&"rectangularAuto"!==A?D=g.default.getSymbolInfo(A):"rectangularAuto"===A&&(D=g.default.getSymbolInfo("rectangular16x48")),this.symbolInfo=D,B||(h=this.preEncode(b,c),b=h.s_pos,c=h.c_pos),i=D.symbolDataCapacity,B?(i-=4,d=4):(i-=c,d=c),!this.checkValue(b))throw new j.InvalidTextException(y);for(k=void 0;e<(B?g.default.CONSTANTS.MaxStructures:1)&&b<z.length;e++)f^=z[b],m=this.getEncodingUnits(b,i),n=m.s_taken,o=m.c_length,p=m.units,b+=n,e==C&&(k=p,d+=o);if(null==k)throw new j.InvalidStructureNumberException;if(b<z.length)throw new j.TextTooLargeException;return"squareAuto"!==A&&"rectangularAuto"!==A||(D=g.default.getSuitableSymbolSize(A,d),this.symbolInfo=D),B&&(c=this.encodeStructureHeader(c,e,q(f^z[z.length-1]))),k.forEach(function(a){return c=x.encode(a,c)}),c<D.symbolDataCapacity&&this.padRight(c),r=this.eccProcess(),s=g.default.getInfoOfRegions(D.regions),t=s.rowOfRegion,u=s.colOfRegion,v=new l.default(r,D.symbolRows-2*t,D.symbolColumns-2*u),w=v.ECC200(),this.placeModules(w)}},{key:"eccProcess",value:function a(){var b,c,d,e,f,g,h=this.symbolInfo,j=this.m_code,k=h.symbolDataCapacity/h.interleavedBlocks,l=h.eccLength/h.interleavedBlocks;for(144==h.symbolRows&&++k,b=[],c=0;c<h.interleavedBlocks;++c){for(d=0,e=c;d<k&&e<h.symbolDataCapacity;++d,e+=h.interleavedBlocks)b[d]=j[e];for(f=(0,i.default)(b,0,d,l),e=c,g=0;g<f.length;++g,e+=h.interleavedBlocks)j[h.symbolDataCapacity+e]=f[g]}return j}},{key:"placeModules",value:function a(b){var c,d,e=this.symbolInfo,f=g.default.createModules(e.symbolRows,e.symbolColumns),h=g.default.getInfoOfRegions(e.regions),i=h.rowOfRegion,j=h.colOfRegion;for(c=0;c<i;c++)for(d=0;d<j;d++)g.default.setFinder(f,e,c,d),g.default.setRegionData(f,e,c,d,b);return f}}]),a}(),b.default=n},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m;Object.defineProperty(b,"__esModule",{value:!0}),b.default=p,d=c(0),e=45,f=128,g=1,h=(0,d.fillArray)(Array(256),0),i=(0,d.fillArray)(Array(256),0),j=g,k=0;do j%=256,l=j,h[k]=j,i[j]=k%256,j<<=1,0!=(l&f)&&(j^=e);while(++k<=255);function n(a,b){return(a^b)%256}function o(a,b){return 0==a||0==b?0:h[(i[a]+i[b])%255]}m=[null,null,null,null,null,[228,48,15,111,62],null,[23,68,144,134,240,92,254],null,null,[28,24,185,166,223,248,116,255,110,61],[175,138,205,12,194,168,39,245,60,97,120],[41,153,158,91,61,42,142,213,97,178,100,242],null,[156,97,192,252,95,9,157,119,138,45,18,186,83,185],null,null,null,[83,195,100,39,188,75,66,61,241,213,109,129,94,254,225,48,90,188],null,[15,195,244,9,233,71,168,2,188,160,153,145,253,79,108,82,27,174,186,172],null,null,null,[52,190,88,205,109,39,176,21,155,197,251,223,155,21,5,172,254,124,12,181,184,96,50,193],null,null,null,[211,231,43,97,71,96,103,174,37,151,170,53,75,34,249,121,17,138,110,213,141,136,120,151,233,168,93,255],null,null,null,null,null,null,null,[245,127,242,218,130,250,162,181,102,120,84,179,220,251,80,182,229,18,2,4,68,33,101,137,95,119,115,44,175,184,59,25,225,98,81,112],null,null,null,null,null,[77,193,137,31,19,38,22,153,247,105,122,2,245,133,242,8,175,95,100,9,167,105,214,111,57,121,21,1,253,57,54,101,248,202,69,50,150,177,226,5,9,5],null,null,null,null,null,[245,132,172,223,96,32,117,22,238,133,238,231,205,188,237,87,191,106,16,147,118,23,37,90,170,205,131,88,120,100,66,138,186,240,82,44,176,87,187,147,160,175,69,213,92,253,225,19],null,null,null,null,null,null,null,[175,9,223,238,12,17,220,208,100,29,175,170,230,192,215,235,150,159,36,223,38,200,132,54,228,146,218,234,117,203,29,232,144,238,22,150,201,117,62,207,164,13,137,245,127,67,247,28,155,43,203,107,233,53,143,46],null,null,null,null,null,[242,93,169,50,144,210,39,118,202,188,201,189,143,108,196,37,185,112,134,230,245,63,197,190,250,106,185,221,175,64,114,71,161,44,147,6,27,218,51,63,87,10,40,130,188,17,163,31,176,170,4,107,232,7,94,166,224,124,86,47,11,204],null,null,null,null,null,[220,228,173,89,251,149,159,56,89,33,147,244,154,36,73,127,213,136,248,180,234,197,158,177,68,122,93,213,15,160,227,236,66,139,153,185,202,167,179,25,220,232,96,210,231,136,223,239,181,241,59,52,172,25,49,232,211,189,64,54,108,153,132,63,96,103,82,186]];function p(a,b,c,e){var f,g,h,i=(0,d.fillArray)(Array(e),0),j=m[e];for(f=b;f<b+c;++f){for(g=n(i[e-1],a[f]),h=e-1;h>0;--h)i[h]=n(o(g,j[h]),i[h-1]);i[0]=o(j[0],g)}return i.reverse(),i}},function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(8),f=h(e);function h(a){return a&&a.__esModule?a:{default:a}}function i(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}g=function(){function a(b,c,d){i(this,a),this.nrow=c,this.ncol=d,this.data=b,this.matrix=f.default.createModules(c,d)}return d(a,[{key:"ECC200",value:function a(){var b=this.nrow,c=this.ncol,d=this.matrix,e=4,f=0,g=0;do{e==b&&0==f&&this.corner1(g++),e==b-2&&0==f&&c%4&&this.corner2(g++),e==b-2&&0==f&&c%8==4&&this.corner3(g++),e!=b+4||2!=f||c%8||this.corner4(g++);do e<b&&f>=0&&null===d[e][f]&&this.utah(e,f,g++),e-=2,f+=2;while(e>=0&&f<c);e+=1,f+=3;do e>=0&&f<c&&null===d[e][f]&&this.utah(e,f,g++),e+=2,f-=2;while(e<b&&f>=0);e+=3,f+=1}while(e<b||f<c);return d[b-1][c-1]||(d[b-1][c-1]=d[b-2][c-2]=1,d[b-2][c-1]=d[b-1][c-2]=0),d}},{key:"module",value:function a(b,c,d,e){var f,g=this.nrow,h=this.ncol,i=this.matrix,j=this.data;b<0&&(b+=g,c+=4-(g+4)%8),c<0&&(c+=h,b+=4-(h+4)%8),f=(1<<8-e)%256,i[b][c]=0!=(j[d]&f)?1:0}},{key:"utah",value:function a(b,c,d){this.module(b-2,c-2,d,1),this.module(b-2,c-1,d,2),this.module(b-1,c-2,d,3),this.module(b-1,c-1,d,4),this.module(b-1,c,d,5),this.module(b,c-2,d,6),this.module(b,c-1,d,7),this.module(b,c,d,8)}},{key:"corner1",value:function a(b){var c=this.nrow,d=this.ncol;this.module(c-1,0,b,1),this.module(c-1,1,b,2),this.module(c-1,2,b,3),this.module(0,d-2,b,4),this.module(0,d-1,b,5),this.module(1,d-1,b,6),this.module(2,d-1,b,7),this.module(3,d-1,b,8)}},{key:"corner2",value:function a(b){var c=this.nrow,d=this.ncol;this.module(c-3,0,b,1),this.module(c-2,0,b,2),this.module(c-1,0,b,3),this.module(0,d-4,b,4),this.module(0,d-3,b,5),this.module(0,d-2,b,6),this.module(0,d-1,b,7),this.module(1,d-1,b,8)}},{key:"corner3",value:function a(b){var c=this.nrow,d=this.ncol;this.module(c-3,0,b,1),this.module(c-2,0,b,2),this.module(c-1,0,b,3),this.module(0,d-2,b,4),this.module(0,d-1,b,5),this.module(1,d-1,b,6),this.module(2,d-1,b,7),this.module(3,d-1,b,8)}},{key:"corner4",value:function a(b){var c=this.nrow,d=this.ncol;this.module(c-1,0,b,1),this.module(c-1,d-1,b,2),this.module(0,d-3,b,3),this.module(0,d-2,b,4),this.module(0,d-1,b,5),this.module(1,d-3,b,6),this.module(1,d-2,b,7),this.module(1,d-1,b,8)}}]),a}(),b.default=g},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(43),f=l(e),g=c(1),h=c(0),i=c(44),j=l(i);function l(a){return a&&a.__esModule?a:{default:a}}function m(a){if(Array.isArray(a)){for(var b=0,c=Array(a.length);b<a.length;b++)c[b]=a[b];return c}return Array.from(a)}function n(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}k=function(){function a(b,c){n(this,a),this.text=b,this.m_symbol=(0,h.str2Array)(b).map(function(a){return a.charCodeAt(0)}),this.symbolSize=c.ecc000_140SymbolSize,this.eccMode=c.eccMode,this.m_code=[],this.m_module=[]}return d(a,[{key:"getMatrix",value:function a(){var b,c,d,e,h,i,k,l,m,n=this.m_symbol,o=this.symbolSize,p=this.eccMode,q=this.m_code;if(n.length>569)throw new g.TextTooLargeException;if(b=f.default.chooseEncodationScheme(n),c="ECC000"===p?7:19,d=this.calculateDataBits(b),e=this.calculateTotalBits(b,c,d),h=this.sLa=f.default.getProperSymbolSize(o,e),e>f.default.getSymbolSizeInfo(h))throw new g.TextTooLargeException;return d+=30,i=[],k=new j.default(i),k.putBitsMSF(f.default.Constants.getFormatID(b)<<27,5),l=this.crcProcess(b),k.putBitsLSF(l,16),k.putBitsLSF(n.length,9),this.encode(b,k),m=new j.default(q),m.putBitsLSF(f.default.getECC(p).headerBits,c),k.offset=0,this.eccProcess(m,k,d),this.randomizeBits(),this.placeModule()}},{key:"eccProcess",value:function a(b,c,d){var e,g,h,i,j,k,l,m,n=this.eccMode;if("ECC000"!==n){e=f.default.getECC(n).eccInfo,g=[e.inputBits],h=!1;do{for(h=!1,i=0;i<e.inputBits;++i)g[i]>>=1,g[i]=f.default.setBit(g[i],e.registerBits[i],c.fetchBit());for(j=!1,k=0;k<e.outputBits;++k){for(j=!1,l=0;l<e.outputMasks[k].length;++l)j^=0!=(g[e.outputMasks[k][l].registerNumber]&e.outputMasks[k][l].mask);b.putBit(j)}if(c.offset>=d){for(m=0;m<g.length&&0==g[m];++m);m==g.length&&(h=!0)}}while(!h)}else for(;c.offset<d;)b.putBit(c.fetchBit())}},{key:"randomizeBits",value:function a(){var b,c=this.m_code,d=this.sLa,e=~~((f.default.getSymbolSizeInfo(d)+7)/8);for(b=0;b<e;++b)c[b]^=f.default.Constants.RandomizeBytes[b]}},{key:"crcProcess",value:function a(b){var c,d,e,g,h=this.m_symbol,i=0,j=[];for(j.push(f.default.Constants.getFormatID(b)+1),j.push(0),j.push.apply(j,m(h)),c=33800,d=0;d<j.length;++d)for(e=0;e<8;++e)g=0!=(1&i),i>>=1,0!=(j[d]&1<<e)^g&&(i^=c);return i}},{key:"calculateDataBits",value:function a(b){var c=this.m_symbol,d=f.default.Constants.GroupLengths[b];return~~(c.length/d)*f.default.Constants.BitLengths[b][0]+(c.length%d==0?0:f.default.Constants.BitLengths[b][c.length%d])}},{key:"calculateTotalBits",value:function a(b,c,d){var e=this.eccMode,g=30+d,h=f.default.getECC(e).eccInfo,i=(~~((g+h.inputBits-1)/h.inputBits)+h.registerBits[h.inputBits-1])*h.outputBits;return c+i}},{key:"encode",value:function a(b,c){var d,e,g,h=this.m_symbol,i=0,j=b,k=f.default.Constants.GroupLengths[j],l=f.default.Constants.BaseValues[j];for(d=0;d<h.length;){i=0,e=1;do i+=f.default.getCodeWord(b,h[d++])*e,e*=l;while(d%k!=0&&d<h.length);g=d%k,c.putBitsLSF(i,f.default.Constants.BitLengths[j][g])}}},{key:"placeModule",value:function a(){var b,c,d,e=this.m_module,g=this.sLa,h=this.m_code,i=f.default.getSymbolSizeInfo(g),k=new j.default(e),l=new j.default(h),m=f.default.getModuleMapping(g);for(b=0;b<i;++b)k.putBit(l.getBit(m[b]));return c=2*f.default.getSymbolSizeValue(g)+9,d=f.default.createModules(c),f.default.setFinder(d,c),f.default.setRegionData(d,c,k),d}}]),a}(),b.default=k},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l;Object.defineProperty(b,"__esModule",{value:!0}),d=c(8),e=m(d),f=c(0),g=c(1);function m(a){return a&&a.__esModule?a:{default:a}}h={Base11:0,Base27:1,Base37:2,Base41:3,ASCII:4,Byte:5},i=127;function n(a){return a>=48&&a<=57||32==a?h.Base11:a>=65&&a<=90?h.Base27:a>=44&&a<=47?h.Base41:a<=i?h.ASCII:h.Byte}function o(a){var b,c,d=h.Base11;for(b=0,c=a.length;b<c&&(d=p(d,n(a[b])),d!==h.Byte);b++);return d}function p(a,b){if(a==b)return a;if(a==h.Byte||b==h.Byte)return h.Byte;var c=a;switch(a){case h.Base11:b==h.Base27?c=h.Base37:b>c&&(c=b);break;case h.Base27:b==h.Base11?c=h.Base37:b>c&&(c=b);break;case h.Base37:case h.Base41:b>c&&(c=b)}return c}function q(a){return a%256}function r(a,b){if(a==h.Byte)return b;if(a==h.ASCII)return q(127&b);var c=0;if(32==b)return c;if(b>=48&&b<=57&&(c=q(b-47)),a==h.Base11)return c;if(c+=26,b>=65&&b<=90&&(c=q(b-64)),a!=h.Base41)return c;switch(b){case 44:return 38;case 45:return 39;case 46:return 37;case 47:return 40}return c}function s(a,b,c){var d=1<<b;return a&=~d,c&&(a|=d),a}function t(a,b){this.registerNumber=a,this.mask=1<<b}j={BaseValues:[11,27,37,41,128,256],GroupLengths:[6,5,4,4,1,1],HeaderBits:[126,57358,465934,523278,517006],BitLengths:[[21,4,7,11,14,18,21],[24,5,10,15,20,24],[21,6,11,16,21],[22,6,11,17,22],[7,7],[8,8]],SymbolCapacities:function(){var a,b=[];for(a=7;a<50;a+=2)b.push(a*a);return b}(),getFormatID:function a(b){return b===h.Base37?++b:b===h.Base41&&--b,b},ECCInfos:[{inputBits:1,registerBits:[0],outputBits:1,outputMasks:null},{inputBits:3,registerBits:[3,3,3],outputBits:4,outputMasks:[[new t(0,3),new t(1,0),new t(2,2),new t(2,1),new t(2,0)],[new t(0,1),new t(0,0),new t(1,3),new t(1,2),new t(1,0)],[new t(0,2),new t(0,1),new t(0,0),new t(1,2),new t(2,3),new t(2,2)],[new t(0,3),new t(0,2),new t(1,3),new t(1,2),new t(1,1),new t(2,3),new t(2,2),new t(2,0)]]},{inputBits:2,registerBits:[10,11],outputBits:3,outputMasks:[[new t(0,10),new t(0,9),new t(0,7),new t(0,5),new t(0,4),new t(0,3),new t(0,0),new t(1,8),new t(1,4),new t(1,3),new t(1,0)],[new t(0,9),new t(0,6),new t(0,5),new t(0,2),new t(0,1),new t(0,0),new t(1,11),new t(1,8),new t(1,5),new t(1,3),new t(1,2)],[new t(0,10),new t(0,5),new t(0,4),new t(0,3),new t(1,11),new t(1,10),new t(1,9),new t(1,7),new t(1,4),new t(1,2),new t(1,0)]]},{inputBits:1,registerBits:[15],outputBits:2,outputMasks:[[new t(0,15),new t(0,13),new t(0,10),new t(0,9),new t(0,8),new t(0,7),new t(0,6),new t(0,5),new t(0,0)],[new t(0,15),new t(0,14),new t(0,12),new t(0,11),new t(0,9),new t(0,4),new t(0,2),new t(0,1),new t(0,0)]]},{inputBits:1,registerBits:[13],outputBits:4,outputMasks:[[new t(0,13),new t(0,9),new t(0,6),new t(0,3),new t(0,1),new t(0,0)],[new t(0,13),new t(0,10),new t(0,9),new t(0,6),new t(0,5),new t(0,4),new t(0,3),new t(0,2),new t(0,0)],[new t(0,13),new t(0,12),new t(0,11),new t(0,9),new t(0,8),new t(0,6),new t(0,4),new t(0,2),new t(0,1),new t(0,0)],[new t(0,13),new t(0,12),new t(0,11),new t(0,9),new t(0,8),new t(0,6),new t(0,4),new t(0,3),new t(0,2),new t(0,1),new t(0,0)]]}],RandomizeBytes:[5,255,199,49,136,168,131,156,100,135,159,100,179,224,77,156,128,41,58,144,179,139,158,144,69,191,245,104,75,8,207,68,184,212,76,91,160,171,114,82,28,228,210,116,164,218,138,8,250,167,199,221,0,48,169,230,100,171,213,139,237,156,121,248,8,209,139,198,34,100,11,51,67,208,128,212,68,149,46,111,94,19,141,71,98,6,235,128,130,201,65,213,115,138,48,35,36,227,127,178,168,11,237,56,66,76,215,176,206,152,189,225,213,228,195,29,21,74,207,209,31,57,38,24,147,252,25,178,45,171,242,110,161,159,175,208,138,43,160,86,176,65,109,67,164,99,243,170,125,175,53,87,194,148,74,101,11,65,222,184,226,48,18,39,155,102,43,52,91,184,153,232,40,113,208,149,107,7,77,60,122,179,229,41,179,186,140,204,45,224,201,192,34,236,76,222,248,88,7,252,25,242,100,226,195,226,216,185,253,103,160,188,245,46,201,73,117,98,130,39,16,244,25,111,73,247,179,132,20,234,235,225,42,49,171,71,125,8,41,172,187,114,250,250,98,184,200,211,134,137,149,253,223,204,156,173,241,212,108,100,35,36,42,86,31,54,235,183,214,255,218,87,244,80,121,8,0],ModuleMapping:[[2,45,10,38,24,21,1,12,40,26,5,33,19,47,22,31,29,15,43,8,36,34,20,48,13,41,27,6,44,9,37,23,17,30,16,39,25,4,32,18,46,11,0,28,14,42,7,35,3],[2,19,55,10,46,28,64,73,1,62,17,53,35,71,8,80,44,26,49,31,67,4,76,40,22,58,13,69,6,78,42,24,60,15,51,33,74,38,20,56,11,47,29,65,37,25,61,16,52,34,70,7,79,43,12,48,30,66,63,75,39,21,57,32,68,5,77,41,23,59,14,50,0,72,36,18,54,9,45,27,3],[2,26,114,70,15,103,59,37,81,4,1,117,73,18,106,62,40,84,7,95,51,29,12,100,56,34,78,92,89,45,23,111,67,65,43,87,10,98,54,32,120,76,21,109,82,5,93,49,27,115,71,16,104,60,38,96,52,30,118,74,19,107,63,41,85,8,24,112,68,13,101,57,35,79,48,90,46,75,20,108,64,42,86,9,97,53,31,119,102,58,36,80,77,91,47,25,113,69,14,39,83,6,94,50,28,116,72,17,105,61,0,88,44,22,110,66,11,99,55,33,3],[2,159,29,133,81,16,120,68,42,146,94,91,1,37,141,89,24,128,76,50,154,102,11,115,63,167,83,18,122,70,44,148,96,5,109,57,161,31,135,125,73,47,151,99,8,112,60,164,34,138,86,21,40,144,92,107,105,53,157,27,131,79,14,118,66,103,12,116,64,168,38,142,90,25,129,77,51,155,110,58,162,32,136,84,19,123,71,45,149,97,6,165,35,139,87,22,126,74,48,152,100,9,113,61,132,80,15,119,67,41,145,93,55,106,54,158,28,23,127,75,49,153,101,10,114,62,166,36,140,88,69,43,147,95,4,108,56,160,30,134,82,17,121,150,98,7,111,59,163,33,137,85,20,124,72,46,0,104,52,156,26,130,78,13,117,65,39,143,3],[2,187,37,157,97,217,22,142,82,202,52,172,112,7,1,41,161,101,221,26,146,86,206,56,176,116,11,131,71,191,93,213,18,138,78,198,48,168,108,105,123,63,183,33,153,28,148,88,208,58,178,118,13,133,73,193,43,163,103,223,80,200,50,170,110,5,125,65,185,35,155,95,215,20,140,54,174,114,9,129,69,189,39,159,99,219,24,144,84,204,106,127,121,61,181,31,151,91,211,16,136,76,196,46,166,134,74,194,44,164,104,224,29,149,89,209,59,179,119,14,186,36,156,96,216,21,141,81,201,51,171,111,6,126,66,160,100,220,25,145,85,205,55,175,115,10,130,70,190,40,212,17,137,77,197,47,167,107,67,122,62,182,32,152,92,147,87,207,57,177,117,12,132,72,192,42,162,102,222,27,199,49,169,109,4,124,64,184,34,154,94,214,19,139,79,173,113,8,128,68,188,38,158,98,218,23,143,83,203,53,0,120,60,180,30,150,90,210,15,135,75,195,45,165,3],[2,69,205,35,171,103,239,18,154,86,222,52,188,120,256,273,1,220,50,186,118,254,33,169,101,237,67,203,135,271,16,288,152,84,178,110,246,25,161,93,229,59,195,127,263,8,280,144,76,212,42,250,29,165,97,233,63,199,131,267,12,284,148,80,216,46,182,114,157,89,225,55,191,123,259,4,276,140,72,208,38,174,106,242,21,235,65,201,133,269,14,286,150,82,218,48,184,116,252,31,167,99,193,125,261,6,278,142,74,210,40,176,108,244,23,159,91,227,57,265,10,282,146,78,214,44,180,112,248,27,163,95,231,61,197,129,274,138,70,206,36,172,104,240,19,155,87,223,53,189,121,257,137,83,219,49,185,117,253,32,168,100,236,66,202,134,270,15,287,151,41,177,109,245,24,160,92,228,58,194,126,262,7,279,143,75,211,113,249,28,164,96,232,62,198,130,266,11,283,147,79,215,45,181,20,156,88,224,54,190,122,258,255,275,139,71,207,37,173,105,241,98,234,64,200,132,268,13,285,149,81,217,47,183,115,251,30,166,56,192,124,260,5,277,141,73,209,39,175,107,243,22,158,90,226,128,264,9,281,145,77,213,43,179,111,247,26,162,94,230,60,196,0,272,136,68,204,34,170,102,238,17,153,85,221,51,187,119,3],[2,82,234,44,348,196,120,272,25,329,177,101,253,63,215,139,291,6,1,239,49,353,201,125,277,30,334,182,106,258,68,220,144,296,11,315,163,87,343,191,115,267,20,324,172,96,248,58,210,134,286,310,305,153,77,229,39,132,284,37,341,189,113,265,75,227,151,303,18,322,170,94,246,56,360,208,28,332,180,104,256,66,218,142,294,9,313,161,85,237,47,351,199,123,275,185,109,261,71,223,147,299,14,318,166,90,242,52,356,204,128,280,33,337,251,61,213,137,289,4,308,156,80,232,42,346,194,118,270,23,327,175,99,225,149,301,16,320,168,92,244,54,358,206,130,282,35,339,187,111,263,73,292,7,311,159,83,235,45,349,197,121,273,26,330,178,102,254,64,216,140,316,164,88,240,50,354,202,126,278,31,335,183,107,259,69,221,145,297,12,78,230,40,344,192,116,268,21,325,173,97,249,59,211,135,287,158,306,154,55,359,207,131,283,36,340,188,112,264,74,226,150,302,17,321,169,93,245,198,122,274,27,331,179,103,255,65,217,141,293,8,312,160,84,236,46,350,279,32,336,184,108,260,70,222,146,298,13,317,165,89,241,51,355,203,127,326,174,98,250,60,212,136,288,285,307,155,79,231,41,345,193,117,269,22,110,262,72,224,148,300,15,319,167,91,243,53,357,205,129,281,34,338,186,62,214,138,290,5,309,157,81,233,43,347,195,119,271,24,328,176,100,252,143,295,10,314,162,86,238,48,352,200,124,276,29,333,181,105,257,67,219,0,304,152,76,228,38,342,190,114,266,19,323,171,95,247,57,209,133,3],[2,88,424,256,46,382,214,130,298,25,361,193,109,277,67,403,235,151,319,4,1,437,269,59,395,227,143,311,38,374,206,122,290,80,416,248,164,332,17,353,185,101,49,385,217,133,301,28,364,196,112,280,70,406,238,154,322,7,343,175,91,427,259,222,138,306,33,369,201,117,285,75,411,243,159,327,12,348,180,96,432,264,54,390,295,22,358,190,106,274,64,400,232,148,316,340,337,169,85,421,253,43,379,211,127,377,209,125,293,83,419,251,167,335,20,356,188,104,440,272,62,398,230,146,314,41,115,283,73,409,241,157,325,10,346,178,94,430,262,52,388,220,136,304,31,367,199,78,414,246,162,330,15,351,183,99,435,267,57,393,225,141,309,36,372,204,120,288,236,152,320,5,341,173,89,425,257,47,383,215,131,299,26,362,194,110,278,68,404,333,18,354,186,102,438,270,60,396,228,144,312,39,375,207,123,291,81,417,249,165,344,176,92,428,260,50,386,218,134,302,29,365,197,113,281,71,407,239,155,323,8,97,433,265,55,391,223,139,307,34,370,202,118,286,76,412,244,160,328,13,349,181,254,44,380,212,128,296,23,359,191,107,275,65,401,233,149,317,172,338,170,86,422,397,229,145,313,40,376,208,124,292,82,418,250,166,334,19,355,187,103,439,271,61,135,303,30,366,198,114,282,72,408,240,156,324,9,345,177,93,429,261,51,387,219,35,371,203,119,287,77,413,245,161,329,14,350,182,98,434,266,56,392,224,140,308,192,108,276,66,402,234,150,318,315,339,171,87,423,255,45,381,213,129,297,24,360,289,79,415,247,163,331,16,352,184,100,436,268,58,394,226,142,310,37,373,205,121,405,237,153,321,6,342,174,90,426,258,48,384,216,132,300,27,363,195,111,279,69,158,326,11,347,179,95,431,263,53,389,221,137,305,32,368,200,116,284,74,410,242,0,336,168,84,420,252,42,378,210,126,294,21,357,189,105,273,63,399,231,147,3],[2,102,470,286,56,424,240,148,516,332,33,401,217,125,493,309,79,447,263,171,355,10,1,476,292,62,430,246,154,522,338,39,407,223,131,499,315,85,453,269,177,361,16,384,200,108,50,418,234,142,510,326,27,395,211,119,487,303,73,441,257,165,349,4,372,188,96,464,280,249,157,525,341,42,410,226,134,502,318,88,456,272,180,364,19,387,203,111,479,295,65,433,513,329,30,398,214,122,490,306,76,444,260,168,352,7,375,191,99,467,283,53,421,237,145,36,404,220,128,496,312,82,450,266,174,358,13,381,197,105,473,289,59,427,243,151,519,335,208,116,484,300,70,438,254,162,346,378,369,185,93,461,277,47,415,231,139,507,323,24,392,505,321,91,459,275,183,367,22,390,206,114,482,298,68,436,252,160,528,344,45,413,229,137,80,448,264,172,356,11,379,195,103,471,287,57,425,241,149,517,333,34,402,218,126,494,310,270,178,362,17,385,201,109,477,293,63,431,247,155,523,339,40,408,224,132,500,316,86,454,350,5,373,189,97,465,281,51,419,235,143,511,327,28,396,212,120,488,304,74,442,258,166,388,204,112,480,296,66,434,250,158,526,342,43,411,227,135,503,319,89,457,273,181,365,20,100,468,284,54,422,238,146,514,330,31,399,215,123,491,307,77,445,261,169,353,8,376,192,290,60,428,244,152,520,336,37,405,221,129,497,313,83,451,267,175,359,14,382,198,106,474,416,232,140,508,324,25,393,209,117,485,301,71,439,255,163,347,194,370,186,94,462,278,48,159,527,343,44,412,228,136,504,320,90,458,274,182,366,21,389,205,113,481,297,67,435,251,331,32,400,216,124,492,308,78,446,262,170,354,9,377,193,101,469,285,55,423,239,147,515,406,222,130,498,314,84,452,268,176,360,15,383,199,107,475,291,61,429,245,153,521,337,38,118,486,302,72,440,256,164,348,345,371,187,95,463,279,49,417,233,141,509,325,26,394,210,317,87,455,271,179,363,18,386,202,110,478,294,64,432,248,156,524,340,41,409,225,133,501,443,259,167,351,6,374,190,98,466,282,52,420,236,144,512,328,29,397,213,121,489,305,75,173,357,12,380,196,104,472,288,58,426,242,150,518,334,35,403,219,127,495,311,81,449,265,0,368,184,92,460,276,46,414,230,138,506,322,23,391,207,115,483,299,69,437,253,161,3],[2,603,103,503,303,53,453,253,153,553,353,28,428,228,128,528,328,78,478,278,178,578,378,375,1,123,523,323,73,473,273,173,573,373,48,448,248,148,548,348,98,498,298,198,598,398,23,423,223,623,311,61,461,261,161,561,361,36,436,236,136,536,336,86,486,286,186,586,386,11,411,211,611,111,511,467,267,167,567,367,42,442,242,142,542,342,92,492,292,192,592,392,17,417,217,617,117,517,317,67,155,555,355,30,430,230,130,530,330,80,480,280,180,580,380,5,405,205,605,105,505,305,55,455,255,370,45,445,245,145,545,345,95,495,295,195,595,395,20,420,220,620,120,520,320,70,470,270,170,570,433,233,133,533,333,83,483,283,183,583,383,8,408,208,608,108,508,308,58,458,258,158,558,358,33,139,539,339,89,489,289,189,589,389,14,414,214,614,114,514,314,64,464,264,164,564,364,39,439,239,326,76,476,276,176,576,376,403,401,201,601,101,501,301,51,451,251,151,551,351,26,426,226,126,526,499,299,199,599,399,24,424,224,624,124,524,324,74,474,274,174,574,374,49,449,249,149,549,349,99,187,587,387,12,412,212,612,112,512,312,62,462,262,162,562,362,37,437,237,137,537,337,87,487,287,393,18,418,218,618,118,518,318,68,468,268,168,568,368,43,443,243,143,543,343,93,493,293,193,593,406,206,606,106,506,306,56,456,256,156,556,356,31,431,231,131,531,331,81,481,281,181,581,381,6,621,121,521,321,71,471,271,171,571,371,46,446,246,146,546,346,96,496,296,196,596,396,21,421,221,509,309,59,459,259,159,559,359,34,434,234,134,534,334,84,484,284,184,584,384,9,409,209,609,109,65,465,265,165,565,365,40,440,240,140,540,340,90,490,290,190,590,390,15,415,215,615,115,515,315,252,152,552,352,27,427,227,127,527,327,77,477,277,177,577,377,203,402,202,602,102,502,302,52,452,572,372,47,447,247,147,547,347,97,497,297,197,597,397,22,422,222,622,122,522,322,72,472,272,172,35,435,235,135,535,335,85,485,285,185,585,385,10,410,210,610,110,510,310,60,460,260,160,560,360,241,141,541,341,91,491,291,191,591,391,16,416,216,616,116,516,316,66,466,266,166,566,366,41,441,529,329,79,479,279,179,579,379,4,404,204,604,104,504,304,54,454,254,154,554,354,29,429,229,129,94,494,294,194,594,394,19,419,219,619,119,519,319,69,469,269,169,569,369,44,444,244,144,544,344,282,182,582,382,7,407,207,607,107,507,307,57,457,257,157,557,357,32,432,232,132,532,332,82,482,588,388,13,413,213,613,113,513,313,63,463,263,163,563,363,38,438,238,138,538,338,88,488,288,188,0,400,200,600,100,500,300,50,450,250,150,550,350,25,425,225,125,525,325,75,475,275,175,575,3],[2,658,118,550,334,64,496,280,712,172,604,388,37,469,253,685,145,577,361,91,523,307,199,631,415,10,1,125,557,341,71,503,287,719,179,611,395,44,476,260,692,152,584,368,98,530,314,206,638,422,17,449,233,665,327,57,489,273,705,165,597,381,30,462,246,678,138,570,354,84,516,300,192,624,408,405,435,219,651,111,543,511,295,727,187,619,403,52,484,268,700,160,592,376,106,538,322,214,646,430,25,457,241,673,133,565,349,79,714,174,606,390,39,471,255,687,147,579,363,93,525,309,201,633,417,12,444,228,660,120,552,336,66,498,282,613,397,46,478,262,694,154,586,370,100,532,316,208,640,424,19,451,235,667,127,559,343,73,505,289,721,181,32,464,248,680,140,572,356,86,518,302,194,626,410,5,437,221,653,113,545,329,59,491,275,707,167,599,383,265,697,157,589,373,103,535,319,211,643,427,22,454,238,670,130,562,346,76,508,292,724,184,616,400,49,481,143,575,359,89,521,305,197,629,413,8,440,224,656,116,548,332,62,494,278,710,170,602,386,35,467,251,683,366,96,528,312,204,636,420,15,447,231,663,123,555,339,69,501,285,717,177,609,393,42,474,258,690,150,582,514,298,190,622,406,442,433,217,649,109,541,325,55,487,271,703,163,595,379,28,460,244,676,136,568,352,82,215,647,431,26,458,242,674,134,566,350,80,512,296,728,188,620,404,53,485,269,701,161,593,377,107,539,323,418,13,445,229,661,121,553,337,67,499,283,715,175,607,391,40,472,256,688,148,580,364,94,526,310,202,634,452,236,668,128,560,344,74,506,290,722,182,614,398,47,479,263,695,155,587,371,101,533,317,209,641,425,20,654,114,546,330,60,492,276,708,168,600,384,33,465,249,681,141,573,357,87,519,303,195,627,411,6,438,222,563,347,77,509,293,725,185,617,401,50,482,266,698,158,590,374,104,536,320,212,644,428,23,455,239,671,131,63,495,279,711,171,603,387,36,468,252,684,144,576,360,90,522,306,198,630,414,9,441,225,657,117,549,333,286,718,178,610,394,43,475,259,691,151,583,367,97,529,313,205,637,421,16,448,232,664,124,556,340,70,502,164,596,380,29,461,245,677,137,569,353,83,515,299,191,623,407,226,434,218,650,110,542,326,56,488,272,704,402,51,483,267,699,159,591,375,105,537,321,213,645,429,24,456,240,672,132,564,348,78,510,294,726,186,618,470,254,686,146,578,362,92,524,308,200,632,416,11,443,227,659,119,551,335,65,497,281,713,173,605,389,38,693,153,585,369,99,531,315,207,639,423,18,450,234,666,126,558,342,72,504,288,720,180,612,396,45,477,261,571,355,85,517,301,193,625,409,4,436,220,652,112,544,328,58,490,274,706,166,598,382,31,463,247,679,139,102,534,318,210,642,426,21,453,237,669,129,561,345,75,507,291,723,183,615,399,48,480,264,696,156,588,372,304,196,628,412,7,439,223,655,115,547,331,61,493,277,709,169,601,385,34,466,250,682,142,574,358,88,520,635,419,14,446,230,662,122,554,338,68,500,284,716,176,608,392,41,473,257,689,149,581,365,95,527,311,203,0,432,216,648,108,540,324,54,486,270,702,162,594,378,27,459,243,675,135,567,351,81,513,297,189,621,3],[2,703,123,587,355,819,65,529,297,761,181,645,413,36,500,268,732,152,616,384,94,558,326,790,210,674,442,7,1,141,605,373,837,83,547,315,779,199,663,431,54,518,286,750,170,634,402,112,576,344,808,228,692,460,25,489,257,721,359,823,69,533,301,765,185,649,417,40,504,272,736,156,620,388,98,562,330,794,214,678,446,11,475,243,707,127,591,76,540,308,772,192,656,424,47,511,279,743,163,627,395,105,569,337,801,221,685,453,18,482,250,714,134,598,366,830,293,757,177,641,409,32,496,264,728,148,612,380,90,554,322,786,206,670,438,435,467,235,699,119,583,351,815,61,525,201,665,433,56,520,288,752,172,636,404,114,578,346,810,230,694,462,27,491,259,723,143,607,375,839,85,549,317,781,419,42,506,274,738,158,622,390,100,564,332,796,216,680,448,13,477,245,709,129,593,361,825,71,535,303,767,187,651,513,281,745,165,629,397,107,571,339,803,223,687,455,20,484,252,716,136,600,368,832,78,542,310,774,194,658,426,49,730,150,614,382,92,556,324,788,208,672,440,5,469,237,701,121,585,353,817,63,527,295,759,179,643,411,34,498,266,632,400,110,574,342,806,226,690,458,23,487,255,719,139,603,371,835,81,545,313,777,197,661,429,52,516,284,748,168,96,560,328,792,212,676,444,9,473,241,705,125,589,357,821,67,531,299,763,183,647,415,38,502,270,734,154,618,386,335,799,219,683,451,16,480,248,712,132,596,364,828,74,538,306,770,190,654,422,45,509,277,741,161,625,393,103,567,204,668,436,471,465,233,697,117,581,349,813,59,523,291,755,175,639,407,30,494,262,726,146,610,378,88,552,320,784,463,28,492,260,724,144,608,376,840,86,550,318,782,202,666,434,57,521,289,753,173,637,405,115,579,347,811,231,695,478,246,710,130,594,362,826,72,536,304,768,188,652,420,43,507,275,739,159,623,391,101,565,333,797,217,681,449,14,717,137,601,369,833,79,543,311,775,195,659,427,50,514,282,746,166,630,398,108,572,340,804,224,688,456,21,485,253,586,354,818,64,528,296,760,180,644,412,35,499,267,731,151,615,383,93,557,325,789,209,673,441,6,470,238,702,122,836,82,546,314,778,198,662,430,53,517,285,749,169,633,401,111,575,343,807,227,691,459,24,488,256,720,140,604,372,532,300,764,184,648,416,39,503,271,735,155,619,387,97,561,329,793,213,677,445,10,474,242,706,126,590,358,822,68,771,191,655,423,46,510,278,742,162,626,394,104,568,336,800,220,684,452,17,481,249,713,133,597,365,829,75,539,307,640,408,31,495,263,727,147,611,379,89,553,321,785,205,669,437,239,466,234,698,118,582,350,814,60,524,292,756,176,55,519,287,751,171,635,403,113,577,345,809,229,693,461,26,490,258,722,142,606,374,838,84,548,316,780,200,664,432,273,737,157,621,389,99,563,331,795,215,679,447,12,476,244,708,128,592,360,824,70,534,302,766,186,650,418,41,505,164,628,396,106,570,338,802,222,686,454,19,483,251,715,135,599,367,831,77,541,309,773,193,657,425,48,512,280,744,381,91,555,323,787,207,671,439,4,468,236,700,120,584,352,816,62,526,294,758,178,642,410,33,497,265,729,149,613,573,341,805,225,689,457,22,486,254,718,138,602,370,834,80,544,312,776,196,660,428,51,515,283,747,167,631,399,109,791,211,675,443,8,472,240,704,124,588,356,820,66,530,298,762,182,646,414,37,501,269,733,153,617,385,95,559,327,682,450,15,479,247,711,131,595,363,827,73,537,305,769,189,653,421,44,508,276,740,160,624,392,102,566,334,798,218,0,464,232,696,116,580,348,812,58,522,290,754,174,638,406,29,493,261,725,145,609,377,87,551,319,783,203,667,3],[2,759,139,635,387,883,77,573,325,821,201,697,449,945,46,542,294,790,170,666,418,914,108,604,356,852,232,728,480,15,1,147,643,395,891,85,581,333,829,209,705,457,953,54,550,302,798,178,674,426,922,116,612,364,860,240,736,488,23,519,271,767,379,875,69,565,317,813,193,689,441,937,38,534,286,782,162,658,410,906,100,596,348,844,224,720,472,7,503,255,751,131,627,89,585,337,833,213,709,461,957,58,554,306,802,182,678,430,926,120,616,368,864,244,740,492,27,523,275,771,151,647,399,895,321,817,197,693,445,941,42,538,290,786,166,662,414,910,104,600,352,848,228,724,476,11,507,259,755,135,631,383,879,73,569,205,701,453,949,50,546,298,794,174,670,422,918,112,608,360,856,236,732,484,19,515,267,763,143,639,391,887,81,577,329,825,437,933,34,530,282,778,158,654,406,902,96,592,344,840,220,716,468,465,499,251,747,127,623,375,871,65,561,313,809,189,685,60,556,308,804,184,680,432,928,122,618,370,866,246,742,494,29,525,277,773,153,649,401,897,91,587,339,835,215,711,463,959,292,788,168,664,416,912,106,602,354,850,230,726,478,13,509,261,757,137,633,385,881,75,571,323,819,199,695,447,943,44,540,176,672,424,920,114,610,362,858,238,734,486,21,517,269,765,145,641,393,889,83,579,331,827,207,703,455,951,52,548,300,796,408,904,98,594,346,842,222,718,470,5,501,253,749,129,625,377,873,67,563,315,811,191,687,439,935,36,532,284,780,160,656,118,614,366,862,242,738,490,25,521,273,769,149,645,397,893,87,583,335,831,211,707,459,955,56,552,304,800,180,676,428,924,350,846,226,722,474,9,505,257,753,133,629,381,877,71,567,319,815,195,691,443,939,40,536,288,784,164,660,412,908,102,598,234,730,482,17,513,265,761,141,637,389,885,79,575,327,823,203,699,451,947,48,544,296,792,172,668,420,916,110,606,358,854,466,511,497,249,745,125,621,373,869,63,559,311,807,187,683,435,931,32,528,280,776,156,652,404,900,94,590,342,838,218,714,526,278,774,154,650,402,898,92,588,340,836,216,712,464,960,61,557,309,805,185,681,433,929,123,619,371,867,247,743,495,30,758,138,634,386,882,76,572,324,820,200,696,448,944,45,541,293,789,169,665,417,913,107,603,355,851,231,727,479,14,510,262,642,394,890,84,580,332,828,208,704,456,952,53,549,301,797,177,673,425,921,115,611,363,859,239,735,487,22,518,270,766,146,874,68,564,316,812,192,688,440,936,37,533,285,781,161,657,409,905,99,595,347,843,223,719,471,6,502,254,750,130,626,378,584,336,832,212,708,460,956,57,553,305,801,181,677,429,925,119,615,367,863,243,739,491,26,522,274,770,150,646,398,894,88,816,196,692,444,940,41,537,289,785,165,661,413,909,103,599,351,847,227,723,475,10,506,258,754,134,630,382,878,72,568,320,700,452,948,49,545,297,793,173,669,421,917,111,607,359,855,235,731,483,18,514,266,762,142,638,390,886,80,576,328,824,204,932,33,529,281,777,157,653,405,901,95,591,343,839,219,715,467,263,498,250,746,126,622,374,870,64,560,312,808,188,684,436,555,307,803,183,679,431,927,121,617,369,865,245,741,493,28,524,276,772,152,648,400,896,90,586,338,834,214,710,462,958,59,787,167,663,415,911,105,601,353,849,229,725,477,12,508,260,756,136,632,384,880,74,570,322,818,198,694,446,942,43,539,291,671,423,919,113,609,361,857,237,733,485,20,516,268,764,144,640,392,888,82,578,330,826,206,702,454,950,51,547,299,795,175,903,97,593,345,841,221,717,469,4,500,252,748,128,624,376,872,66,562,314,810,190,686,438,934,35,531,283,779,159,655,407,613,365,861,241,737,489,24,520,272,768,148,644,396,892,86,582,334,830,210,706,458,954,55,551,303,799,179,675,427,923,117,845,225,721,473,8,504,256,752,132,628,380,876,70,566,318,814,194,690,442,938,39,535,287,783,163,659,411,907,101,597,349,729,481,16,512,264,760,140,636,388,884,78,574,326,822,202,698,450,946,47,543,295,791,171,667,419,915,109,605,357,853,233,0,496,248,744,124,620,372,868,62,558,310,806,186,682,434,930,31,527,279,775,155,651,403,899,93,589,341,837,217,713,3],[2,265,793,133,661,397,925,67,595,331,859,199,727,463,991,34,562,298,826,166,694,430,958,100,628,364,892,232,760,496,1024,1057,1,824,164,692,428,956,98,626,362,890,230,758,494,1022,65,593,329,857,197,725,461,989,131,659,395,923,263,791,527,1055,32,1088,560,296,676,412,940,82,610,346,874,214,742,478,1006,49,577,313,841,181,709,445,973,115,643,379,907,247,775,511,1039,16,1072,544,280,808,148,948,90,618,354,882,222,750,486,1014,57,585,321,849,189,717,453,981,123,651,387,915,255,783,519,1047,24,1080,552,288,816,156,684,420,602,338,866,206,734,470,998,41,569,305,833,173,701,437,965,107,635,371,899,239,767,503,1031,8,1064,536,272,800,140,668,404,932,74,886,226,754,490,1018,61,589,325,853,193,721,457,985,127,655,391,919,259,787,523,1051,28,1084,556,292,820,160,688,424,952,94,622,358,738,474,1002,45,573,309,837,177,705,441,969,111,639,375,903,243,771,507,1035,12,1068,540,276,804,144,672,408,936,78,606,342,870,210,1010,53,581,317,845,185,713,449,977,119,647,383,911,251,779,515,1043,20,1076,548,284,812,152,680,416,944,86,614,350,878,218,746,482,565,301,829,169,697,433,961,103,631,367,895,235,763,499,1027,4,1060,532,268,796,136,664,400,928,70,598,334,862,202,730,466,994,37,855,195,723,459,987,129,657,393,921,261,789,525,1053,30,1086,558,294,822,162,690,426,954,96,624,360,888,228,756,492,1020,63,591,327,707,443,971,113,641,377,905,245,773,509,1037,14,1070,542,278,806,146,674,410,938,80,608,344,872,212,740,476,1004,47,575,311,839,179,979,121,649,385,913,253,781,517,1045,22,1078,550,286,814,154,682,418,946,88,616,352,880,220,748,484,1012,55,583,319,847,187,715,451,633,369,897,237,765,501,1029,6,1062,534,270,798,138,666,402,930,72,600,336,864,204,732,468,996,39,567,303,831,171,699,435,963,105,917,257,785,521,1049,26,1082,554,290,818,158,686,422,950,92,620,356,884,224,752,488,1016,59,587,323,851,191,719,455,983,125,653,389,769,505,1033,10,1066,538,274,802,142,670,406,934,76,604,340,868,208,736,472,1e3,43,571,307,835,175,703,439,967,109,637,373,901,241,1041,18,1074,546,282,810,150,678,414,942,84,612,348,876,216,744,480,1008,51,579,315,843,183,711,447,975,117,645,381,909,249,777,513,1058,530,266,794,134,662,398,926,68,596,332,860,200,728,464,992,35,563,299,827,167,695,431,959,101,629,365,893,233,761,497,1025,529,295,823,163,691,427,955,97,625,361,889,229,757,493,1021,64,592,328,856,196,724,460,988,130,658,394,922,262,790,526,1054,31,1087,559,147,675,411,939,81,609,345,873,213,741,477,1005,48,576,312,840,180,708,444,972,114,642,378,906,246,774,510,1038,15,1071,543,279,807,419,947,89,617,353,881,221,749,485,1013,56,584,320,848,188,716,452,980,122,650,386,914,254,782,518,1046,23,1079,551,287,815,155,683,73,601,337,865,205,733,469,997,40,568,304,832,172,700,436,964,106,634,370,898,238,766,502,1030,7,1063,535,271,799,139,667,403,931,357,885,225,753,489,1017,60,588,324,852,192,720,456,984,126,654,390,918,258,786,522,1050,27,1083,555,291,819,159,687,423,951,93,621,209,737,473,1001,44,572,308,836,176,704,440,968,110,638,374,902,242,770,506,1034,11,1067,539,275,803,143,671,407,935,77,605,341,869,481,1009,52,580,316,844,184,712,448,976,118,646,382,910,250,778,514,1042,19,1075,547,283,811,151,679,415,943,85,613,349,877,217,745,36,564,300,828,168,696,432,960,102,630,366,894,234,762,498,1026,1023,1059,531,267,795,135,663,399,927,69,597,333,861,201,729,465,993,326,854,194,722,458,986,128,656,392,920,260,788,524,1052,29,1085,557,293,821,161,689,425,953,95,623,359,887,227,755,491,1019,62,590,178,706,442,970,112,640,376,904,244,772,508,1036,13,1069,541,277,805,145,673,409,937,79,607,343,871,211,739,475,1003,46,574,310,838,450,978,120,648,384,912,252,780,516,1044,21,1077,549,285,813,153,681,417,945,87,615,351,879,219,747,483,1011,54,582,318,846,186,714,104,632,368,896,236,764,500,1028,5,1061,533,269,797,137,665,401,929,71,599,335,863,203,731,467,995,38,566,302,830,170,698,434,962,388,916,256,784,520,1048,25,1081,553,289,817,157,685,421,949,91,619,355,883,223,751,487,1015,58,586,322,850,190,718,454,982,124,652,240,768,504,1032,9,1065,537,273,801,141,669,405,933,75,603,339,867,207,735,471,999,42,570,306,834,174,702,438,966,108,636,372,900,512,1040,17,1073,545,281,809,149,677,413,941,83,611,347,875,215,743,479,1007,50,578,314,842,182,710,446,974,116,644,380,908,248,776,0,1056,528,264,792,132,660,396,924,66,594,330,858,198,726,462,990,33,561,297,825,165,693,429,957,99,627,363,891,231,759,495,3],[2,290,850,150,710,430,990,80,1200,640,360,920,220,780,500,1060,45,1165,605,325,885,185,745,465,1025,115,675,395,955,255,815,535,1095,10,1,859,159,719,439,999,89,1209,649,369,929,229,789,509,1069,54,1174,614,334,894,194,754,474,1034,124,684,404,964,264,824,544,1104,19,1139,579,299,701,421,981,71,1191,631,351,911,211,771,491,1051,36,1156,596,316,876,176,736,456,1016,106,666,386,946,246,806,526,1086,1130,1121,561,281,841,141,1014,104,1224,664,384,944,244,804,524,1084,69,1189,629,349,909,209,769,489,1049,139,699,419,979,279,839,559,1119,34,1154,594,314,874,174,734,454,1207,647,367,927,227,787,507,1067,52,1172,612,332,892,192,752,472,1032,122,682,402,962,262,822,542,1102,17,1137,577,297,857,157,717,437,997,87,376,936,236,796,516,1076,61,1181,621,341,901,201,761,481,1041,131,691,411,971,271,831,551,1111,26,1146,586,306,866,166,726,446,1006,96,1216,656,218,778,498,1058,43,1163,603,323,883,183,743,463,1023,113,673,393,953,253,813,533,1093,8,1128,568,288,848,148,708,428,988,78,1198,638,358,918,520,1080,65,1185,625,345,905,205,765,485,1045,135,695,415,975,275,835,555,1115,30,1150,590,310,870,170,730,450,1010,100,1220,660,380,940,240,800,48,1168,608,328,888,188,748,468,1028,118,678,398,958,258,818,538,1098,13,1133,573,293,853,153,713,433,993,83,1203,643,363,923,223,783,503,1063,617,337,897,197,757,477,1037,127,687,407,967,267,827,547,1107,22,1142,582,302,862,162,722,442,1002,92,1212,652,372,932,232,792,512,1072,57,1177,879,179,739,459,1019,109,669,389,949,249,809,529,1089,4,1124,564,284,844,144,704,424,984,74,1194,634,354,914,214,774,494,1054,39,1159,599,319,767,487,1047,137,697,417,977,277,837,557,1117,32,1152,592,312,872,172,732,452,1012,102,1222,662,382,942,242,802,522,1082,67,1187,627,347,907,207,1030,120,680,400,960,260,820,540,1100,15,1135,575,295,855,155,715,435,995,85,1205,645,365,925,225,785,505,1065,50,1170,610,330,890,190,750,470,689,409,969,269,829,549,1109,24,1144,584,304,864,164,724,444,1004,94,1214,654,374,934,234,794,514,1074,59,1179,619,339,899,199,759,479,1039,129,951,251,811,531,1091,6,1126,566,286,846,146,706,426,986,76,1196,636,356,916,216,776,496,1056,41,1161,601,321,881,181,741,461,1021,111,671,391,833,553,1113,28,1148,588,308,868,168,728,448,1008,98,1218,658,378,938,238,798,518,1078,63,1183,623,343,903,203,763,483,1043,133,693,413,973,273,1096,11,1131,571,291,851,151,711,431,991,81,1201,641,361,921,221,781,501,1061,46,1166,606,326,886,186,746,466,1026,116,676,396,956,256,816,536,1140,580,300,860,160,720,440,1e3,90,1210,650,370,930,230,790,510,1070,55,1175,615,335,895,195,755,475,1035,125,685,405,965,265,825,545,1105,20,282,842,142,702,422,982,72,1192,632,352,912,212,772,492,1052,37,1157,597,317,877,177,737,457,1017,107,667,387,947,247,807,527,1087,570,1122,562,173,733,453,1013,103,1223,663,383,943,243,803,523,1083,68,1188,628,348,908,208,768,488,1048,138,698,418,978,278,838,558,1118,33,1153,593,313,873,436,996,86,1206,646,366,926,226,786,506,1066,51,1171,611,331,891,191,751,471,1031,121,681,401,961,261,821,541,1101,16,1136,576,296,856,156,716,95,1215,655,375,935,235,795,515,1075,60,1180,620,340,900,200,760,480,1040,130,690,410,970,270,830,550,1110,25,1145,585,305,865,165,725,445,1005,637,357,917,217,777,497,1057,42,1162,602,322,882,182,742,462,1022,112,672,392,952,252,812,532,1092,7,1127,567,287,847,147,707,427,987,77,1197,939,239,799,519,1079,64,1184,624,344,904,204,764,484,1044,134,694,414,974,274,834,554,1114,29,1149,589,309,869,169,729,449,1009,99,1219,659,379,782,502,1062,47,1167,607,327,887,187,747,467,1027,117,677,397,957,257,817,537,1097,12,1132,572,292,852,152,712,432,992,82,1202,642,362,922,222,1071,56,1176,616,336,896,196,756,476,1036,126,686,406,966,266,826,546,1106,21,1141,581,301,861,161,721,441,1001,91,1211,651,371,931,231,791,511,1158,598,318,878,178,738,458,1018,108,668,388,948,248,808,528,1088,1085,1123,563,283,843,143,703,423,983,73,1193,633,353,913,213,773,493,1053,38,346,906,206,766,486,1046,136,696,416,976,276,836,556,1116,31,1151,591,311,871,171,731,451,1011,101,1221,661,381,941,241,801,521,1081,66,1186,626,189,749,469,1029,119,679,399,959,259,819,539,1099,14,1134,574,294,854,154,714,434,994,84,1204,644,364,924,224,784,504,1064,49,1169,609,329,889,478,1038,128,688,408,968,268,828,548,1108,23,1143,583,303,863,163,723,443,1003,93,1213,653,373,933,233,793,513,1073,58,1178,618,338,898,198,758,110,670,390,950,250,810,530,1090,5,1125,565,285,845,145,705,425,985,75,1195,635,355,915,215,775,495,1055,40,1160,600,320,880,180,740,460,1020,412,972,272,832,552,1112,27,1147,587,307,867,167,727,447,1007,97,1217,657,377,937,237,797,517,1077,62,1182,622,342,902,202,762,482,1042,132,692,254,814,534,1094,9,1129,569,289,849,149,709,429,989,79,1199,639,359,919,219,779,499,1059,44,1164,604,324,884,184,744,464,1024,114,674,394,954,543,1103,18,1138,578,298,858,158,718,438,998,88,1208,648,368,928,228,788,508,1068,53,1173,613,333,893,193,753,473,1033,123,683,403,963,263,823,0,1120,560,280,840,140,700,420,980,70,1190,630,350,910,210,770,490,1050,35,1155,595,315,875,175,735,455,1015,105,665,385,945,245,805,525,3],[2,302,894,154,1338,746,450,1042,80,1264,672,376,968,228,820,524,1116,43,1227,635,339,931,191,783,487,1079,117,1301,709,413,1005,265,857,561,1153,6,1,917,177,1361,769,473,1065,103,1287,695,399,991,251,843,547,1139,66,1250,658,362,954,214,806,510,1102,140,1324,732,436,1028,288,880,584,1176,29,1213,621,325,1343,751,455,1047,85,1269,677,381,973,233,825,529,1121,48,1232,640,344,936,196,788,492,1084,122,1306,714,418,1010,270,862,566,1158,11,1195,603,307,899,159,464,1056,94,1278,686,390,982,242,834,538,1130,57,1241,649,353,945,205,797,501,1093,131,1315,723,427,1019,279,871,575,1167,20,1204,612,316,908,168,1352,760,75,1259,667,371,963,223,815,519,1111,38,1222,630,334,926,186,778,482,1074,112,1296,704,408,1e3,260,852,556,1148,1190,1185,593,297,889,149,1333,741,445,1037,702,406,998,258,850,554,1146,73,1257,665,369,961,221,813,517,1109,147,1331,739,443,1035,295,887,591,1183,36,1220,628,332,924,184,1368,776,480,1072,110,1294,980,240,832,536,1128,55,1239,647,351,943,203,795,499,1091,129,1313,721,425,1017,277,869,573,1165,18,1202,610,314,906,166,1350,758,462,1054,92,1276,684,388,841,545,1137,64,1248,656,360,952,212,804,508,1100,138,1322,730,434,1026,286,878,582,1174,27,1211,619,323,915,175,1359,767,471,1063,101,1285,693,397,989,249,1119,46,1230,638,342,934,194,786,490,1082,120,1304,712,416,1008,268,860,564,1156,9,1193,601,305,897,157,1341,749,453,1045,83,1267,675,379,971,231,823,527,1253,661,365,957,217,809,513,1105,143,1327,735,439,1031,291,883,587,1179,32,1216,624,328,920,180,1364,772,476,1068,106,1290,698,402,994,254,846,550,1142,69,347,939,199,791,495,1087,125,1309,717,421,1013,273,865,569,1161,14,1198,606,310,902,162,1346,754,458,1050,88,1272,680,384,976,236,828,532,1124,51,1235,643,208,800,504,1096,134,1318,726,430,1022,282,874,578,1170,23,1207,615,319,911,171,1355,763,467,1059,97,1281,689,393,985,245,837,541,1133,60,1244,652,356,948,485,1077,115,1299,707,411,1003,263,855,559,1151,4,1188,596,300,892,152,1336,744,448,1040,78,1262,670,374,966,226,818,522,1114,41,1225,633,337,929,189,781,145,1329,737,441,1033,293,885,589,1181,34,1218,626,330,922,182,1366,774,478,1070,108,1292,700,404,996,256,848,552,1144,71,1255,663,367,959,219,811,515,1107,719,423,1015,275,867,571,1163,16,1200,608,312,904,164,1348,756,460,1052,90,1274,682,386,978,238,830,534,1126,53,1237,645,349,941,201,793,497,1089,127,1311,1024,284,876,580,1172,25,1209,617,321,913,173,1357,765,469,1061,99,1283,691,395,987,247,839,543,1135,62,1246,654,358,950,210,802,506,1098,136,1320,728,432,858,562,1154,7,1191,599,303,895,155,1339,747,451,1043,81,1265,673,377,969,229,821,525,1117,44,1228,636,340,932,192,784,488,1080,118,1302,710,414,1006,266,1177,30,1214,622,326,918,178,1362,770,474,1066,104,1288,696,400,992,252,844,548,1140,67,1251,659,363,955,215,807,511,1103,141,1325,733,437,1029,289,881,585,1196,604,308,900,160,1344,752,456,1048,86,1270,678,382,974,234,826,530,1122,49,1233,641,345,937,197,789,493,1085,123,1307,715,419,1011,271,863,567,1159,12,317,909,169,1353,761,465,1057,95,1279,687,391,983,243,835,539,1131,58,1242,650,354,946,206,798,502,1094,132,1316,724,428,1020,280,872,576,1168,21,1205,613,150,1334,742,446,1038,76,1260,668,372,964,224,816,520,1112,39,1223,631,335,927,187,779,483,1075,113,1297,705,409,1001,261,853,557,1149,598,1186,594,298,890,775,479,1071,109,1293,701,405,997,257,849,553,1145,72,1256,664,368,960,220,812,516,1108,146,1330,738,442,1034,294,886,590,1182,35,1219,627,331,923,183,1367,1053,91,1275,683,387,979,239,831,535,1127,54,1238,646,350,942,202,794,498,1090,128,1312,720,424,1016,276,868,572,1164,17,1201,609,313,905,165,1349,757,461,1284,692,396,988,248,840,544,1136,63,1247,655,359,951,211,803,507,1099,137,1321,729,433,1025,285,877,581,1173,26,1210,618,322,914,174,1358,766,470,1062,100,378,970,230,822,526,1118,45,1229,637,341,933,193,785,489,1081,119,1303,711,415,1007,267,859,563,1155,8,1192,600,304,896,156,1340,748,452,1044,82,1266,674,253,845,549,1141,68,1252,660,364,956,216,808,512,1104,142,1326,734,438,1030,290,882,586,1178,31,1215,623,327,919,179,1363,771,475,1067,105,1289,697,401,993,531,1123,50,1234,642,346,938,198,790,494,1086,124,1308,716,420,1012,272,864,568,1160,13,1197,605,309,901,161,1345,753,457,1049,87,1271,679,383,975,235,827,59,1243,651,355,947,207,799,503,1095,133,1317,725,429,1021,281,873,577,1169,22,1206,614,318,910,170,1354,762,466,1058,96,1280,688,392,984,244,836,540,1132,632,336,928,188,780,484,1076,114,1298,706,410,1002,262,854,558,1150,1147,1187,595,299,891,151,1335,743,447,1039,77,1261,669,373,965,225,817,521,1113,40,1224,958,218,810,514,1106,144,1328,736,440,1032,292,884,588,1180,33,1217,625,329,921,181,1365,773,477,1069,107,1291,699,403,995,255,847,551,1143,70,1254,662,366,792,496,1088,126,1310,718,422,1014,274,866,570,1162,15,1199,607,311,903,163,1347,755,459,1051,89,1273,681,385,977,237,829,533,1125,52,1236,644,348,940,200,1097,135,1319,727,431,1023,283,875,579,1171,24,1208,616,320,912,172,1356,764,468,1060,98,1282,690,394,986,246,838,542,1134,61,1245,653,357,949,209,801,505,1300,708,412,1004,264,856,560,1152,5,1189,597,301,893,153,1337,745,449,1041,79,1263,671,375,967,227,819,523,1115,42,1226,634,338,930,190,782,486,1078,116,435,1027,287,879,583,1175,28,1212,620,324,916,176,1360,768,472,1064,102,1286,694,398,990,250,842,546,1138,65,1249,657,361,953,213,805,509,1101,139,1323,731,269,861,565,1157,10,1194,602,306,898,158,1342,750,454,1046,84,1268,676,380,972,232,824,528,1120,47,1231,639,343,935,195,787,491,1083,121,1305,713,417,1009,574,1166,19,1203,611,315,907,167,1351,759,463,1055,93,1277,685,389,981,241,833,537,1129,56,1240,648,352,944,204,796,500,1092,130,1314,722,426,1018,278,870,0,1184,592,296,888,148,1332,740,444,1036,74,1258,666,370,962,222,814,518,1110,37,1221,629,333,925,185,777,481,1073,111,1295,703,407,999,259,851,555,3],[2,328,952,172,1420,796,484,1108,94,1342,718,406,1030,250,1498,874,562,1186,55,1303,679,367,991,211,1459,835,523,1147,133,1381,757,445,1069,289,913,601,1225,16,1,962,182,1430,806,494,1118,104,1352,728,416,1040,260,1508,884,572,1196,65,1313,689,377,1001,221,1469,845,533,1157,143,1391,767,455,1079,299,923,611,1235,26,1274,650,338,1410,786,474,1098,84,1332,708,396,1020,240,1488,864,552,1176,45,1293,669,357,981,201,1449,825,513,1137,123,1371,747,435,1059,279,903,591,1215,6,1254,630,318,942,162,499,1123,109,1357,733,421,1045,265,1513,889,577,1201,70,1318,694,382,1006,226,1474,850,538,1162,148,1396,772,460,1084,304,928,616,1240,31,1279,655,343,967,187,1435,811,89,1337,713,401,1025,245,1493,869,557,1181,50,1298,674,362,986,206,1454,830,518,1142,128,1376,752,440,1064,284,908,596,1220,11,1259,635,323,947,167,1415,791,479,1103,723,411,1035,255,1503,879,567,1191,60,1308,684,372,996,216,1464,840,528,1152,138,1386,762,450,1074,294,918,606,1230,21,1269,645,333,957,177,1425,801,489,1113,99,1347,1015,235,1483,859,547,1171,40,1288,664,352,976,196,1444,820,508,1132,118,1366,742,430,1054,274,898,586,1210,1264,1249,625,313,937,157,1405,781,469,1093,79,1327,703,391,1520,896,584,1208,77,1325,701,389,1013,233,1481,857,545,1169,155,1403,779,467,1091,311,935,623,1247,38,1286,662,350,974,194,1442,818,506,1130,116,1364,740,428,1052,272,565,1189,58,1306,682,370,994,214,1462,838,526,1150,136,1384,760,448,1072,292,916,604,1228,19,1267,643,331,955,175,1423,799,487,1111,97,1345,721,409,1033,253,1501,877,68,1316,692,380,1004,224,1472,848,536,1160,146,1394,770,458,1082,302,926,614,1238,29,1277,653,341,965,185,1433,809,497,1121,107,1355,731,419,1043,263,1511,887,575,1199,672,360,984,204,1452,828,516,1140,126,1374,750,438,1062,282,906,594,1218,9,1257,633,321,945,165,1413,789,477,1101,87,1335,711,399,1023,243,1491,867,555,1179,48,1296,1009,229,1477,853,541,1165,151,1399,775,463,1087,307,931,619,1243,34,1282,658,346,970,190,1438,814,502,1126,112,1360,736,424,1048,268,1516,892,580,1204,73,1321,697,385,1457,833,521,1145,131,1379,755,443,1067,287,911,599,1223,14,1262,638,326,950,170,1418,794,482,1106,92,1340,716,404,1028,248,1496,872,560,1184,53,1301,677,365,989,209,531,1155,141,1389,765,453,1077,297,921,609,1233,24,1272,648,336,960,180,1428,804,492,1116,102,1350,726,414,1038,258,1506,882,570,1194,63,1311,687,375,999,219,1467,843,121,1369,745,433,1057,277,901,589,1213,4,1252,628,316,940,160,1408,784,472,1096,82,1330,706,394,1018,238,1486,862,550,1174,43,1291,667,355,979,199,1447,823,511,1135,777,465,1089,309,933,621,1245,36,1284,660,348,972,192,1440,816,504,1128,114,1362,738,426,1050,270,1518,894,582,1206,75,1323,699,387,1011,231,1479,855,543,1167,153,1401,1070,290,914,602,1226,17,1265,641,329,953,173,1421,797,485,1109,95,1343,719,407,1031,251,1499,875,563,1187,56,1304,680,368,992,212,1460,836,524,1148,134,1382,758,446,924,612,1236,27,1275,651,339,963,183,1431,807,495,1119,105,1353,729,417,1041,261,1509,885,573,1197,66,1314,690,378,1002,222,1470,846,534,1158,144,1392,768,456,1080,300,1216,7,1255,631,319,943,163,1411,787,475,1099,85,1333,709,397,1021,241,1489,865,553,1177,46,1294,670,358,982,202,1450,826,514,1138,124,1372,748,436,1060,280,904,592,1280,656,344,968,188,1436,812,500,1124,110,1358,734,422,1046,266,1514,890,578,1202,71,1319,695,383,1007,227,1475,851,539,1163,149,1397,773,461,1085,305,929,617,1241,32,324,948,168,1416,792,480,1104,90,1338,714,402,1026,246,1494,870,558,1182,51,1299,675,363,987,207,1455,831,519,1143,129,1377,753,441,1065,285,909,597,1221,12,1260,636,178,1426,802,490,1114,100,1348,724,412,1036,256,1504,880,568,1192,61,1309,685,373,997,217,1465,841,529,1153,139,1387,763,451,1075,295,919,607,1231,22,1270,646,334,958,782,470,1094,80,1328,704,392,1016,236,1484,860,548,1172,41,1289,665,353,977,197,1445,821,509,1133,119,1367,743,431,1055,275,899,587,1211,640,1250,626,314,938,158,1406,1129,115,1363,739,427,1051,271,1519,895,583,1207,76,1324,700,388,1012,232,1480,856,544,1168,154,1402,778,466,1090,310,934,622,1246,37,1285,661,349,973,193,1441,817,505,1344,720,408,1032,252,1500,876,564,1188,57,1305,681,369,993,213,1461,837,525,1149,135,1383,759,447,1071,291,915,603,1227,18,1266,642,330,954,174,1422,798,486,1110,96,418,1042,262,1510,886,574,1198,67,1315,691,379,1003,223,1471,847,535,1159,145,1393,769,457,1081,301,925,613,1237,28,1276,652,340,964,184,1432,808,496,1120,106,1354,730,242,1490,866,554,1178,47,1295,671,359,983,203,1451,827,515,1139,125,1373,749,437,1061,281,905,593,1217,8,1256,632,320,944,164,1412,788,476,1100,86,1334,710,398,1022,891,579,1203,72,1320,696,384,1008,228,1476,852,540,1164,150,1398,774,462,1086,306,930,618,1242,33,1281,657,345,969,189,1437,813,501,1125,111,1359,735,423,1047,267,1515,1183,52,1300,676,364,988,208,1456,832,520,1144,130,1378,754,442,1066,286,910,598,1222,13,1261,637,325,949,169,1417,793,481,1105,91,1339,715,403,1027,247,1495,871,559,1310,686,374,998,218,1466,842,530,1154,140,1388,764,452,1076,296,920,608,1232,23,1271,647,335,959,179,1427,803,491,1115,101,1349,725,413,1037,257,1505,881,569,1193,62,354,978,198,1446,822,510,1134,120,1368,744,432,1056,276,900,588,1212,1209,1251,627,315,939,159,1407,783,471,1095,81,1329,705,393,1017,237,1485,861,549,1173,42,1290,666,230,1478,854,542,1166,152,1400,776,464,1088,308,932,620,1244,35,1283,659,347,971,191,1439,815,503,1127,113,1361,737,425,1049,269,1517,893,581,1205,74,1322,698,386,1010,834,522,1146,132,1380,756,444,1068,288,912,600,1224,15,1263,639,327,951,171,1419,795,483,1107,93,1341,717,405,1029,249,1497,873,561,1185,54,1302,678,366,990,210,1458,1156,142,1390,766,454,1078,298,922,610,1234,25,1273,649,337,961,181,1429,805,493,1117,103,1351,727,415,1039,259,1507,883,571,1195,64,1312,688,376,1e3,220,1468,844,532,1370,746,434,1058,278,902,590,1214,5,1253,629,317,941,161,1409,785,473,1097,83,1331,707,395,1019,239,1487,863,551,1175,44,1292,668,356,980,200,1448,824,512,1136,122,459,1083,303,927,615,1239,30,1278,654,342,966,186,1434,810,498,1122,108,1356,732,420,1044,264,1512,888,576,1200,69,1317,693,381,1005,225,1473,849,537,1161,147,1395,771,283,907,595,1219,10,1258,634,322,946,166,1414,790,478,1102,88,1336,712,400,1024,244,1492,868,556,1180,49,1297,673,361,985,205,1453,829,517,1141,127,1375,751,439,1063,605,1229,20,1268,644,332,956,176,1424,800,488,1112,98,1346,722,410,1034,254,1502,878,566,1190,59,1307,683,371,995,215,1463,839,527,1151,137,1385,761,449,1073,293,917,0,1248,624,312,936,156,1404,780,468,1092,78,1326,702,390,1014,234,1482,858,546,1170,39,1287,663,351,975,195,1443,819,507,1131,117,1365,741,429,1053,273,897,585,3],[2,332,1644,988,168,1480,824,496,1152,86,1398,742,414,1070,250,1562,906,578,1234,45,1357,701,373,1029,209,1521,865,537,1193,127,1439,783,455,1111,291,1603,947,619,1275,4,1,1677,1021,201,1513,857,529,1185,119,1431,775,447,1103,283,1595,939,611,1267,78,1390,734,406,1062,242,1554,898,570,1226,160,1472,816,488,1144,324,1636,980,652,1308,37,1349,693,365,181,1493,837,509,1165,99,1411,755,427,1083,263,1575,919,591,1247,58,1370,714,386,1042,222,1534,878,550,1206,140,1452,796,468,1124,304,1616,960,632,1288,17,1329,673,345,1657,1001,847,519,1175,109,1421,765,437,1093,273,1585,929,601,1257,68,1380,724,396,1052,232,1544,888,560,1216,150,1462,806,478,1134,314,1626,970,642,1298,27,1339,683,355,1667,1011,191,1503,1155,89,1401,745,417,1073,253,1565,909,581,1237,48,1360,704,376,1032,212,1524,868,540,1196,130,1442,786,458,1114,294,1606,950,622,1278,7,1319,663,335,1647,991,171,1483,827,499,1426,770,442,1098,278,1590,934,606,1262,73,1385,729,401,1057,237,1549,893,565,1221,155,1467,811,483,1139,319,1631,975,647,1303,32,1344,688,360,1672,1016,196,1508,852,524,1180,114,422,1078,258,1570,914,586,1242,53,1365,709,381,1037,217,1529,873,545,1201,135,1447,791,463,1119,299,1611,955,627,1283,12,1324,668,340,1652,996,176,1488,832,504,1160,94,1406,750,268,1580,924,596,1252,63,1375,719,391,1047,227,1539,883,555,1211,145,1457,801,473,1129,309,1621,965,637,1293,22,1334,678,350,1662,1006,186,1498,842,514,1170,104,1416,760,432,1088,903,575,1231,42,1354,698,370,1026,206,1518,862,534,1190,124,1436,780,452,1108,288,1600,944,616,1272,1316,1313,657,329,1641,985,165,1477,821,493,1149,83,1395,739,411,1067,247,1559,1270,81,1393,737,409,1065,245,1557,901,573,1229,163,1475,819,491,1147,327,1639,983,655,1311,40,1352,696,368,1680,1024,204,1516,860,532,1188,122,1434,778,450,1106,286,1598,942,614,1373,717,389,1045,225,1537,881,553,1209,143,1455,799,471,1127,307,1619,963,635,1291,20,1332,676,348,1660,1004,184,1496,840,512,1168,102,1414,758,430,1086,266,1578,922,594,1250,61,399,1055,235,1547,891,563,1219,153,1465,809,481,1137,317,1629,973,645,1301,30,1342,686,358,1670,1014,194,1506,850,522,1178,112,1424,768,440,1096,276,1588,932,604,1260,71,1383,727,215,1527,871,543,1199,133,1445,789,461,1117,297,1609,953,625,1281,10,1322,666,338,1650,994,174,1486,830,502,1158,92,1404,748,420,1076,256,1568,912,584,1240,51,1363,707,379,1035,896,568,1224,158,1470,814,486,1142,322,1634,978,650,1306,35,1347,691,363,1675,1019,199,1511,855,527,1183,117,1429,773,445,1101,281,1593,937,609,1265,76,1388,732,404,1060,240,1552,1204,138,1450,794,466,1122,302,1614,958,630,1286,15,1327,671,343,1655,999,179,1491,835,507,1163,97,1409,753,425,1081,261,1573,917,589,1245,56,1368,712,384,1040,220,1532,876,548,1460,804,476,1132,312,1624,968,640,1296,25,1337,681,353,1665,1009,189,1501,845,517,1173,107,1419,763,435,1091,271,1583,927,599,1255,66,1378,722,394,1050,230,1542,886,558,1214,148,456,1112,292,1604,948,620,1276,5,1317,661,333,1645,989,169,1481,825,497,1153,87,1399,743,415,1071,251,1563,907,579,1235,46,1358,702,374,1030,210,1522,866,538,1194,128,1440,784,325,1637,981,653,1309,38,1350,694,366,1678,1022,202,1514,858,530,1186,120,1432,776,448,1104,284,1596,940,612,1268,79,1391,735,407,1063,243,1555,899,571,1227,161,1473,817,489,1145,961,633,1289,18,1330,674,346,1658,1002,182,1494,838,510,1166,100,1412,756,428,1084,264,1576,920,592,1248,59,1371,715,387,1043,223,1535,879,551,1207,141,1453,797,469,1125,305,1617,1299,28,1340,684,356,1668,1012,192,1504,848,520,1176,110,1422,766,438,1094,274,1586,930,602,1258,69,1381,725,397,1053,233,1545,889,561,1217,151,1463,807,479,1135,315,1627,971,643,1320,664,336,1648,992,172,1484,828,500,1156,90,1402,746,418,1074,254,1566,910,582,1238,49,1361,705,377,1033,213,1525,869,541,1197,131,1443,787,459,1115,295,1607,951,623,1279,8,361,1673,1017,197,1509,853,525,1181,115,1427,771,443,1099,279,1591,935,607,1263,74,1386,730,402,1058,238,1550,894,566,1222,156,1468,812,484,1140,320,1632,976,648,1304,33,1345,689,997,177,1489,833,505,1161,95,1407,751,423,1079,259,1571,915,587,1243,54,1366,710,382,1038,218,1530,874,546,1202,136,1448,792,464,1120,300,1612,956,628,1284,13,1325,669,341,1653,1499,843,515,1171,105,1417,761,433,1089,269,1581,925,597,1253,64,1376,720,392,1048,228,1540,884,556,1212,146,1458,802,474,1130,310,1622,966,638,1294,23,1335,679,351,1663,1007,187,494,1150,84,1396,740,412,1068,248,1560,904,576,1232,43,1355,699,371,1027,207,1519,863,535,1191,125,1437,781,453,1109,289,1601,945,617,1273,660,1314,658,330,1642,986,166,1478,822,121,1433,777,449,1105,285,1597,941,613,1269,80,1392,736,408,1064,244,1556,900,572,1228,162,1474,818,490,1146,326,1638,982,654,1310,39,1351,695,367,1679,1023,203,1515,859,531,1187,757,429,1085,265,1577,921,593,1249,60,1372,716,388,1044,224,1536,880,552,1208,142,1454,798,470,1126,306,1618,962,634,1290,19,1331,675,347,1659,1003,183,1495,839,511,1167,101,1413,1095,275,1587,931,603,1259,70,1382,726,398,1054,234,1546,890,562,1218,152,1464,808,480,1136,316,1628,972,644,1300,29,1341,685,357,1669,1013,193,1505,849,521,1177,111,1423,767,439,1567,911,583,1239,50,1362,706,378,1034,214,1526,870,542,1198,132,1444,788,460,1116,296,1608,952,624,1280,9,1321,665,337,1649,993,173,1485,829,501,1157,91,1403,747,419,1075,255,608,1264,75,1387,731,403,1059,239,1551,895,567,1223,157,1469,813,485,1141,321,1633,977,649,1305,34,1346,690,362,1674,1018,198,1510,854,526,1182,116,1428,772,444,1100,280,1592,936,55,1367,711,383,1039,219,1531,875,547,1203,137,1449,793,465,1121,301,1613,957,629,1285,14,1326,670,342,1654,998,178,1490,834,506,1162,96,1408,752,424,1080,260,1572,916,588,1244,721,393,1049,229,1541,885,557,1213,147,1459,803,475,1131,311,1623,967,639,1295,24,1336,680,352,1664,1008,188,1500,844,516,1172,106,1418,762,434,1090,270,1582,926,598,1254,65,1377,1028,208,1520,864,536,1192,126,1438,782,454,1110,290,1602,946,618,1274,1271,1315,659,331,1643,987,167,1479,823,495,1151,85,1397,741,413,1069,249,1561,905,577,1233,44,1356,700,372,1553,897,569,1225,159,1471,815,487,1143,323,1635,979,651,1307,36,1348,692,364,1676,1020,200,1512,856,528,1184,118,1430,774,446,1102,282,1594,938,610,1266,77,1389,733,405,1061,241,549,1205,139,1451,795,467,1123,303,1615,959,631,1287,16,1328,672,344,1656,1e3,180,1492,836,508,1164,98,1410,754,426,1082,262,1574,918,590,1246,57,1369,713,385,1041,221,1533,877,149,1461,805,477,1133,313,1625,969,641,1297,26,1338,682,354,1666,1010,190,1502,846,518,1174,108,1420,764,436,1092,272,1584,928,600,1256,67,1379,723,395,1051,231,1543,887,559,1215,785,457,1113,293,1605,949,621,1277,6,1318,662,334,1646,990,170,1482,826,498,1154,88,1400,744,416,1072,252,1564,908,580,1236,47,1359,703,375,1031,211,1523,867,539,1195,129,1441,1138,318,1630,974,646,1302,31,1343,687,359,1671,1015,195,1507,851,523,1179,113,1425,769,441,1097,277,1589,933,605,1261,72,1384,728,400,1056,236,1548,892,564,1220,154,1466,810,482,1610,954,626,1282,11,1323,667,339,1651,995,175,1487,831,503,1159,93,1405,749,421,1077,257,1569,913,585,1241,52,1364,708,380,1036,216,1528,872,544,1200,134,1446,790,462,1118,298,636,1292,21,1333,677,349,1661,1005,185,1497,841,513,1169,103,1415,759,431,1087,267,1579,923,595,1251,62,1374,718,390,1046,226,1538,882,554,1210,144,1456,800,472,1128,308,1620,964,0,1312,656,328,1640,984,164,1476,820,492,1148,82,1394,738,410,1066,246,1558,902,574,1230,41,1353,697,369,1025,205,1517,861,533,1189,123,1435,779,451,1107,287,1599,943,615,3],[2,359,1735,1047,187,1563,875,531,1219,101,1477,789,445,1821,1133,273,1649,961,617,1305,58,1434,746,402,1778,1090,230,1606,918,574,1262,144,1520,832,488,1176,316,1692,1004,660,1348,15,1,1746,1058,198,1574,886,542,1230,112,1488,800,456,1832,1144,284,1660,972,628,1316,69,1445,757,413,1789,1101,241,1617,929,585,1273,155,1531,843,499,1187,327,1703,1015,671,1359,26,1402,714,370,176,1552,864,520,1208,90,1466,778,434,1810,1122,262,1638,950,606,1294,47,1423,735,391,1767,1079,219,1595,907,563,1251,133,1509,821,477,1165,305,1681,993,649,1337,4,1380,692,348,1724,1036,899,555,1243,125,1501,813,469,1845,1157,297,1673,985,641,1329,82,1458,770,426,1802,1114,254,1630,942,598,1286,168,1544,856,512,1200,340,1716,1028,684,1372,39,1415,727,383,1759,1071,211,1587,1222,104,1480,792,448,1824,1136,276,1652,964,620,1308,61,1437,749,405,1781,1093,233,1609,921,577,1265,147,1523,835,491,1179,319,1695,1007,663,1351,18,1394,706,362,1738,1050,190,1566,878,534,1491,803,459,1835,1147,287,1663,975,631,1319,72,1448,760,416,1792,1104,244,1620,932,588,1276,158,1534,846,502,1190,330,1706,1018,674,1362,29,1405,717,373,1749,1061,201,1577,889,545,1233,115,437,1813,1125,265,1641,953,609,1297,50,1426,738,394,1770,1082,222,1598,910,566,1254,136,1512,824,480,1168,308,1684,996,652,1340,7,1383,695,351,1727,1039,179,1555,867,523,1211,93,1469,781,1152,292,1668,980,636,1324,77,1453,765,421,1797,1109,249,1625,937,593,1281,163,1539,851,507,1195,335,1711,1023,679,1367,34,1410,722,378,1754,1066,206,1582,894,550,1238,120,1496,808,464,1840,1646,958,614,1302,55,1431,743,399,1775,1087,227,1603,915,571,1259,141,1517,829,485,1173,313,1689,1001,657,1345,12,1388,700,356,1732,1044,184,1560,872,528,1216,98,1474,786,442,1818,1130,270,625,1313,66,1442,754,410,1786,1098,238,1614,926,582,1270,152,1528,840,496,1184,324,1700,1012,668,1356,23,1399,711,367,1743,1055,195,1571,883,539,1227,109,1485,797,453,1829,1141,281,1657,969,44,1420,732,388,1764,1076,216,1592,904,560,1248,130,1506,818,474,1162,302,1678,990,646,1334,1391,1377,689,345,1721,1033,173,1549,861,517,1205,87,1463,775,431,1807,1119,259,1635,947,603,1291,773,429,1805,1117,257,1633,945,601,1289,171,1547,859,515,1203,343,1719,1031,687,1375,42,1418,730,386,1762,1074,214,1590,902,558,1246,128,1504,816,472,1848,1160,300,1676,988,644,1332,85,1461,1784,1096,236,1612,924,580,1268,150,1526,838,494,1182,322,1698,1010,666,1354,21,1397,709,365,1741,1053,193,1569,881,537,1225,107,1483,795,451,1827,1139,279,1655,967,623,1311,64,1440,752,408,247,1623,935,591,1279,161,1537,849,505,1193,333,1709,1021,677,1365,32,1408,720,376,1752,1064,204,1580,892,548,1236,118,1494,806,462,1838,1150,290,1666,978,634,1322,75,1451,763,419,1795,1107,913,569,1257,139,1515,827,483,1171,311,1687,999,655,1343,10,1386,698,354,1730,1042,182,1558,870,526,1214,96,1472,784,440,1816,1128,268,1644,956,612,1300,53,1429,741,397,1773,1085,225,1601,1284,166,1542,854,510,1198,338,1714,1026,682,1370,37,1413,725,381,1757,1069,209,1585,897,553,1241,123,1499,811,467,1843,1155,295,1671,983,639,1327,80,1456,768,424,1800,1112,252,1628,940,596,1521,833,489,1177,317,1693,1005,661,1349,16,1392,704,360,1736,1048,188,1564,876,532,1220,102,1478,790,446,1822,1134,274,1650,962,618,1306,59,1435,747,403,1779,1091,231,1607,919,575,1263,145,500,1188,328,1704,1016,672,1360,27,1403,715,371,1747,1059,199,1575,887,543,1231,113,1489,801,457,1833,1145,285,1661,973,629,1317,70,1446,758,414,1790,1102,242,1618,930,586,1274,156,1532,844,306,1682,994,650,1338,5,1381,693,349,1725,1037,177,1553,865,521,1209,91,1467,779,435,1811,1123,263,1639,951,607,1295,48,1424,736,392,1768,1080,220,1596,908,564,1252,134,1510,822,478,1166,1029,685,1373,40,1416,728,384,1760,1072,212,1588,900,556,1244,126,1502,814,470,1846,1158,298,1674,986,642,1330,83,1459,771,427,1803,1115,255,1631,943,599,1287,169,1545,857,513,1201,341,1717,1352,19,1395,707,363,1739,1051,191,1567,879,535,1223,105,1481,793,449,1825,1137,277,1653,965,621,1309,62,1438,750,406,1782,1094,234,1610,922,578,1266,148,1524,836,492,1180,320,1696,1008,664,1406,718,374,1750,1062,202,1578,890,546,1234,116,1492,804,460,1836,1148,288,1664,976,632,1320,73,1449,761,417,1793,1105,245,1621,933,589,1277,159,1535,847,503,1191,331,1707,1019,675,1363,30,352,1728,1040,180,1556,868,524,1212,94,1470,782,438,1814,1126,266,1642,954,610,1298,51,1427,739,395,1771,1083,223,1599,911,567,1255,137,1513,825,481,1169,309,1685,997,653,1341,8,1384,696,1067,207,1583,895,551,1239,121,1497,809,465,1841,1153,293,1669,981,637,1325,78,1454,766,422,1798,1110,250,1626,938,594,1282,164,1540,852,508,1196,336,1712,1024,680,1368,35,1411,723,379,1755,1561,873,529,1217,99,1475,787,443,1819,1131,271,1647,959,615,1303,56,1432,744,400,1776,1088,228,1604,916,572,1260,142,1518,830,486,1174,314,1690,1002,658,1346,13,1389,701,357,1733,1045,185,540,1228,110,1486,798,454,1830,1142,282,1658,970,626,1314,67,1443,755,411,1787,1099,239,1615,927,583,1271,153,1529,841,497,1185,325,1701,1013,669,1357,24,1400,712,368,1744,1056,196,1572,884,88,1464,776,432,1808,1120,260,1636,948,604,1292,45,1421,733,389,1765,1077,217,1593,905,561,1249,131,1507,819,475,1163,303,1679,991,647,1335,703,1378,690,346,1722,1034,174,1550,862,518,1206,815,471,1847,1159,299,1675,987,643,1331,84,1460,772,428,1804,1116,256,1632,944,600,1288,170,1546,858,514,1202,342,1718,1030,686,1374,41,1417,729,385,1761,1073,213,1589,901,557,1245,127,1503,1826,1138,278,1654,966,622,1310,63,1439,751,407,1783,1095,235,1611,923,579,1267,149,1525,837,493,1181,321,1697,1009,665,1353,20,1396,708,364,1740,1052,192,1568,880,536,1224,106,1482,794,450,289,1665,977,633,1321,74,1450,762,418,1794,1106,246,1622,934,590,1278,160,1536,848,504,1192,332,1708,1020,676,1364,31,1407,719,375,1751,1063,203,1579,891,547,1235,117,1493,805,461,1837,1149,955,611,1299,52,1428,740,396,1772,1084,224,1600,912,568,1256,138,1514,826,482,1170,310,1686,998,654,1342,9,1385,697,353,1729,1041,181,1557,869,525,1213,95,1471,783,439,1815,1127,267,1643,1326,79,1455,767,423,1799,1111,251,1627,939,595,1283,165,1541,853,509,1197,337,1713,1025,681,1369,36,1412,724,380,1756,1068,208,1584,896,552,1240,122,1498,810,466,1842,1154,294,1670,982,638,1433,745,401,1777,1089,229,1605,917,573,1261,143,1519,831,487,1175,315,1691,1003,659,1347,14,1390,702,358,1734,1046,186,1562,874,530,1218,100,1476,788,444,1820,1132,272,1648,960,616,1304,57,412,1788,1100,240,1616,928,584,1272,154,1530,842,498,1186,326,1702,1014,670,1358,25,1401,713,369,1745,1057,197,1573,885,541,1229,111,1487,799,455,1831,1143,283,1659,971,627,1315,68,1444,756,1078,218,1594,906,562,1250,132,1508,820,476,1164,304,1680,992,648,1336,1333,1379,691,347,1723,1035,175,1551,863,519,1207,89,1465,777,433,1809,1121,261,1637,949,605,1293,46,1422,734,390,1766,1629,941,597,1285,167,1543,855,511,1199,339,1715,1027,683,1371,38,1414,726,382,1758,1070,210,1586,898,554,1242,124,1500,812,468,1844,1156,296,1672,984,640,1328,81,1457,769,425,1801,1113,253,576,1264,146,1522,834,490,1178,318,1694,1006,662,1350,17,1393,705,361,1737,1049,189,1565,877,533,1221,103,1479,791,447,1823,1135,275,1651,963,619,1307,60,1436,748,404,1780,1092,232,1608,920,157,1533,845,501,1189,329,1705,1017,673,1361,28,1404,716,372,1748,1060,200,1576,888,544,1232,114,1490,802,458,1834,1146,286,1662,974,630,1318,71,1447,759,415,1791,1103,243,1619,931,587,1275,823,479,1167,307,1683,995,651,1339,6,1382,694,350,1726,1038,178,1554,866,522,1210,92,1468,780,436,1812,1124,264,1640,952,608,1296,49,1425,737,393,1769,1081,221,1597,909,565,1253,135,1511,1194,334,1710,1022,678,1366,33,1409,721,377,1753,1065,205,1581,893,549,1237,119,1495,807,463,1839,1151,291,1667,979,635,1323,76,1452,764,420,1796,1108,248,1624,936,592,1280,162,1538,850,506,1688,1e3,656,1344,11,1387,699,355,1731,1043,183,1559,871,527,1215,97,1473,785,441,1817,1129,269,1645,957,613,1301,54,1430,742,398,1774,1086,226,1602,914,570,1258,140,1516,828,484,1172,312,667,1355,22,1398,710,366,1742,1054,194,1570,882,538,1226,108,1484,796,452,1828,1140,280,1656,968,624,1312,65,1441,753,409,1785,1097,237,1613,925,581,1269,151,1527,839,495,1183,323,1699,1011,0,1376,688,344,1720,1032,172,1548,860,516,1204,86,1462,774,430,1806,1118,258,1634,946,602,1290,43,1419,731,387,1763,1075,215,1591,903,559,1247,129,1505,817,473,1161,301,1677,989,645,3],[2,370,1810,1090,190,1630,910,550,1990,1270,100,1540,820,460,1900,1180,280,1720,1e3,640,1360,55,1495,775,415,1855,1135,235,1675,955,595,1315,145,1585,865,505,1945,1225,325,1765,1045,685,1405,10,1,1838,1118,218,1658,938,578,2018,1298,128,1568,848,488,1928,1208,308,1748,1028,668,1388,83,1523,803,443,1883,1163,263,1703,983,623,1343,173,1613,893,533,1973,1253,353,1793,1073,713,1433,38,1478,758,398,196,1636,916,556,1996,1276,106,1546,826,466,1906,1186,286,1726,1006,646,1366,61,1501,781,421,1861,1141,241,1681,961,601,1321,151,1591,871,511,1951,1231,331,1771,1051,691,1411,16,1456,736,376,1816,1096,927,567,2007,1287,117,1557,837,477,1917,1197,297,1737,1017,657,1377,72,1512,792,432,1872,1152,252,1692,972,612,1332,162,1602,882,522,1962,1242,342,1782,1062,702,1422,27,1467,747,387,1827,1107,207,1647,1984,1264,94,1534,814,454,1894,1174,274,1714,994,634,1354,49,1489,769,409,1849,1129,229,1669,949,589,1309,139,1579,859,499,1939,1219,319,1759,1039,679,1399,4,1444,724,364,1804,1084,184,1624,904,544,131,1571,851,491,1931,1211,311,1751,1031,671,1391,86,1526,806,446,1886,1166,266,1706,986,626,1346,176,1616,896,536,1976,1256,356,1796,1076,716,1436,41,1481,761,401,1841,1121,221,1661,941,581,2021,1301,829,469,1909,1189,289,1729,1009,649,1369,64,1504,784,424,1864,1144,244,1684,964,604,1324,154,1594,874,514,1954,1234,334,1774,1054,694,1414,19,1459,739,379,1819,1099,199,1639,919,559,1999,1279,109,1549,1920,1200,300,1740,1020,660,1380,75,1515,795,435,1875,1155,255,1695,975,615,1335,165,1605,885,525,1965,1245,345,1785,1065,705,1425,30,1470,750,390,1830,1110,210,1650,930,570,2010,1290,120,1560,840,480,277,1717,997,637,1357,52,1492,772,412,1852,1132,232,1672,952,592,1312,142,1582,862,502,1942,1222,322,1762,1042,682,1402,7,1447,727,367,1807,1087,187,1627,907,547,1987,1267,97,1537,817,457,1897,1177,1025,665,1385,80,1520,800,440,1880,1160,260,1700,980,620,1340,170,1610,890,530,1970,1250,350,1790,1070,710,1430,35,1475,755,395,1835,1115,215,1655,935,575,2015,1295,125,1565,845,485,1925,1205,305,1745,1363,58,1498,778,418,1858,1138,238,1678,958,598,1318,148,1588,868,508,1948,1228,328,1768,1048,688,1408,13,1453,733,373,1813,1093,193,1633,913,553,1993,1273,103,1543,823,463,1903,1183,283,1723,1003,643,1509,789,429,1869,1149,249,1689,969,609,1329,159,1599,879,519,1959,1239,339,1779,1059,699,1419,24,1464,744,384,1824,1104,204,1644,924,564,2004,1284,114,1554,834,474,1914,1194,294,1734,1014,654,1374,69,406,1846,1126,226,1666,946,586,1306,136,1576,856,496,1936,1216,316,1756,1036,676,1396,1450,1441,721,361,1801,1081,181,1621,901,541,1981,1261,91,1531,811,451,1891,1171,271,1711,991,631,1351,46,1486,766,1169,269,1709,989,629,1349,179,1619,899,539,1979,1259,359,1799,1079,719,1439,44,1484,764,404,1844,1124,224,1664,944,584,2024,1304,134,1574,854,494,1934,1214,314,1754,1034,674,1394,89,1529,809,449,1889,1687,967,607,1327,157,1597,877,517,1957,1237,337,1777,1057,697,1417,22,1462,742,382,1822,1102,202,1642,922,562,2002,1282,112,1552,832,472,1912,1192,292,1732,1012,652,1372,67,1507,787,427,1867,1147,247,618,1338,168,1608,888,528,1968,1248,348,1788,1068,708,1428,33,1473,753,393,1833,1113,213,1653,933,573,2013,1293,123,1563,843,483,1923,1203,303,1743,1023,663,1383,78,1518,798,438,1878,1158,258,1698,978,146,1586,866,506,1946,1226,326,1766,1046,686,1406,11,1451,731,371,1811,1091,191,1631,911,551,1991,1271,101,1541,821,461,1901,1181,281,1721,1001,641,1361,56,1496,776,416,1856,1136,236,1676,956,596,1316,894,534,1974,1254,354,1794,1074,714,1434,39,1479,759,399,1839,1119,219,1659,939,579,2019,1299,129,1569,849,489,1929,1209,309,1749,1029,669,1389,84,1524,804,444,1884,1164,264,1704,984,624,1344,174,1614,1952,1232,332,1772,1052,692,1412,17,1457,737,377,1817,1097,197,1637,917,557,1997,1277,107,1547,827,467,1907,1187,287,1727,1007,647,1367,62,1502,782,422,1862,1142,242,1682,962,602,1322,152,1592,872,512,343,1783,1063,703,1423,28,1468,748,388,1828,1108,208,1648,928,568,2008,1288,118,1558,838,478,1918,1198,298,1738,1018,658,1378,73,1513,793,433,1873,1153,253,1693,973,613,1333,163,1603,883,523,1963,1243,1040,680,1400,5,1445,725,365,1805,1085,185,1625,905,545,1985,1265,95,1535,815,455,1895,1175,275,1715,995,635,1355,50,1490,770,410,1850,1130,230,1670,950,590,1310,140,1580,860,500,1940,1220,320,1760,1437,42,1482,762,402,1842,1122,222,1662,942,582,2022,1302,132,1572,852,492,1932,1212,312,1752,1032,672,1392,87,1527,807,447,1887,1167,267,1707,987,627,1347,177,1617,897,537,1977,1257,357,1797,1077,717,1460,740,380,1820,1100,200,1640,920,560,2e3,1280,110,1550,830,470,1910,1190,290,1730,1010,650,1370,65,1505,785,425,1865,1145,245,1685,965,605,1325,155,1595,875,515,1955,1235,335,1775,1055,695,1415,20,391,1831,1111,211,1651,931,571,2011,1291,121,1561,841,481,1921,1201,301,1741,1021,661,1381,76,1516,796,436,1876,1156,256,1696,976,616,1336,166,1606,886,526,1966,1246,346,1786,1066,706,1426,31,1471,751,1088,188,1628,908,548,1988,1268,98,1538,818,458,1898,1178,278,1718,998,638,1358,53,1493,773,413,1853,1133,233,1673,953,593,1313,143,1583,863,503,1943,1223,323,1763,1043,683,1403,8,1448,728,368,1808,1656,936,576,2016,1296,126,1566,846,486,1926,1206,306,1746,1026,666,1386,81,1521,801,441,1881,1161,261,1701,981,621,1341,171,1611,891,531,1971,1251,351,1791,1071,711,1431,36,1476,756,396,1836,1116,216,554,1994,1274,104,1544,824,464,1904,1184,284,1724,1004,644,1364,59,1499,779,419,1859,1139,239,1679,959,599,1319,149,1589,869,509,1949,1229,329,1769,1049,689,1409,14,1454,734,374,1814,1094,194,1634,914,1285,115,1555,835,475,1915,1195,295,1735,1015,655,1375,70,1510,790,430,1870,1150,250,1690,970,610,1330,160,1600,880,520,1960,1240,340,1780,1060,700,1420,25,1465,745,385,1825,1105,205,1645,925,565,2005,1532,812,452,1892,1172,272,1712,992,632,1352,47,1487,767,407,1847,1127,227,1667,947,587,1307,137,1577,857,497,1937,1217,317,1757,1037,677,1397,730,1442,722,362,1802,1082,182,1622,902,542,1982,1262,92,493,1933,1213,313,1753,1033,673,1393,88,1528,808,448,1888,1168,268,1708,988,628,1348,178,1618,898,538,1978,1258,358,1798,1078,718,1438,43,1483,763,403,1843,1123,223,1663,943,583,2023,1303,133,1573,853,1191,291,1731,1011,651,1371,66,1506,786,426,1866,1146,246,1686,966,606,1326,156,1596,876,516,1956,1236,336,1776,1056,696,1416,21,1461,741,381,1821,1101,201,1641,921,561,2001,1281,111,1551,831,471,1911,1742,1022,662,1382,77,1517,797,437,1877,1157,257,1697,977,617,1337,167,1607,887,527,1967,1247,347,1787,1067,707,1427,32,1472,752,392,1832,1112,212,1652,932,572,2012,1292,122,1562,842,482,1922,1202,302,639,1359,54,1494,774,414,1854,1134,234,1674,954,594,1314,144,1584,864,504,1944,1224,324,1764,1044,684,1404,9,1449,729,369,1809,1089,189,1629,909,549,1989,1269,99,1539,819,459,1899,1179,279,1719,999,82,1522,802,442,1882,1162,262,1702,982,622,1342,172,1612,892,532,1972,1252,352,1792,1072,712,1432,37,1477,757,397,1837,1117,217,1657,937,577,2017,1297,127,1567,847,487,1927,1207,307,1747,1027,667,1387,780,420,1860,1140,240,1680,960,600,1320,150,1590,870,510,1950,1230,330,1770,1050,690,1410,15,1455,735,375,1815,1095,195,1635,915,555,1995,1275,105,1545,825,465,1905,1185,285,1725,1005,645,1365,60,1500,1871,1151,251,1691,971,611,1331,161,1601,881,521,1961,1241,341,1781,1061,701,1421,26,1466,746,386,1826,1106,206,1646,926,566,2006,1286,116,1556,836,476,1916,1196,296,1736,1016,656,1376,71,1511,791,431,228,1668,948,588,1308,138,1578,858,498,1938,1218,318,1758,1038,678,1398,1395,1443,723,363,1803,1083,183,1623,903,543,1983,1263,93,1533,813,453,1893,1173,273,1713,993,633,1353,48,1488,768,408,1848,1128,985,625,1345,175,1615,895,535,1975,1255,355,1795,1075,715,1435,40,1480,760,400,1840,1120,220,1660,940,580,2020,1300,130,1570,850,490,1930,1210,310,1750,1030,670,1390,85,1525,805,445,1885,1165,265,1705,1323,153,1593,873,513,1953,1233,333,1773,1053,693,1413,18,1458,738,378,1818,1098,198,1638,918,558,1998,1278,108,1548,828,468,1908,1188,288,1728,1008,648,1368,63,1503,783,423,1863,1143,243,1683,963,603,1604,884,524,1964,1244,344,1784,1064,704,1424,29,1469,749,389,1829,1109,209,1649,929,569,2009,1289,119,1559,839,479,1919,1199,299,1739,1019,659,1379,74,1514,794,434,1874,1154,254,1694,974,614,1334,164,501,1941,1221,321,1761,1041,681,1401,6,1446,726,366,1806,1086,186,1626,906,546,1986,1266,96,1536,816,456,1896,1176,276,1716,996,636,1356,51,1491,771,411,1851,1131,231,1671,951,591,1311,141,1581,861,1249,349,1789,1069,709,1429,34,1474,754,394,1834,1114,214,1654,934,574,2014,1294,124,1564,844,484,1924,1204,304,1744,1024,664,1384,79,1519,799,439,1879,1159,259,1699,979,619,1339,169,1609,889,529,1969,1767,1047,687,1407,12,1452,732,372,1812,1092,192,1632,912,552,1992,1272,102,1542,822,462,1902,1182,282,1722,1002,642,1362,57,1497,777,417,1857,1137,237,1677,957,597,1317,147,1587,867,507,1947,1227,327,698,1418,23,1463,743,383,1823,1103,203,1643,923,563,2003,1283,113,1553,833,473,1913,1193,293,1733,1013,653,1373,68,1508,788,428,1868,1148,248,1688,968,608,1328,158,1598,878,518,1958,1238,338,1778,1058,0,1440,720,360,1800,1080,180,1620,900,540,1980,1260,90,1530,810,450,1890,1170,270,1710,990,630,1350,45,1485,765,405,1845,1125,225,1665,945,585,1305,135,1575,855,495,1935,1215,315,1755,1035,675,3],[2,398,1902,1150,210,1714,962,586,2090,1338,116,1620,868,492,1996,1244,304,1808,1056,680,2184,1432,69,1573,821,445,1949,1197,257,1761,1009,633,2137,1385,163,1667,915,539,2043,1291,351,1855,1103,727,1479,22,1,1914,1162,222,1726,974,598,2102,1350,128,1632,880,504,2008,1256,316,1820,1068,692,2196,1444,81,1585,833,457,1961,1209,269,1773,1021,645,2149,1397,175,1679,927,551,2055,1303,363,1867,1115,739,1491,34,1538,786,410,198,1702,950,574,2078,1326,104,1608,856,480,1984,1232,292,1796,1044,668,2172,1420,57,1561,809,433,1937,1185,245,1749,997,621,2125,1373,151,1655,903,527,2031,1279,339,1843,1091,715,1467,10,1514,762,386,1890,1138,980,604,2108,1356,134,1638,886,510,2014,1262,322,1826,1074,698,2202,1450,87,1591,839,463,1967,1215,275,1779,1027,651,2155,1403,181,1685,933,557,2061,1309,369,1873,1121,745,1497,40,1544,792,416,1920,1168,228,1732,2084,1332,110,1614,862,486,1990,1238,298,1802,1050,674,2178,1426,63,1567,815,439,1943,1191,251,1755,1003,627,2131,1379,157,1661,909,533,2037,1285,345,1849,1097,721,1473,16,1520,768,392,1896,1144,204,1708,956,580,122,1626,874,498,2002,1250,310,1814,1062,686,2190,1438,75,1579,827,451,1955,1203,263,1767,1015,639,2143,1391,169,1673,921,545,2049,1297,357,1861,1109,733,1485,28,1532,780,404,1908,1156,216,1720,968,592,2096,1344,850,474,1978,1226,286,1790,1038,662,2166,1414,51,1555,803,427,1931,1179,239,1743,991,615,2119,1367,145,1649,897,521,2025,1273,333,1837,1085,709,1461,4,1508,756,380,1884,1132,192,1696,944,568,2072,1320,98,1602,2017,1265,325,1829,1077,701,2205,1453,90,1594,842,466,1970,1218,278,1782,1030,654,2158,1406,184,1688,936,560,2064,1312,372,1876,1124,748,1500,43,1547,795,419,1923,1171,231,1735,983,607,2111,1359,137,1641,889,513,301,1805,1053,677,2181,1429,66,1570,818,442,1946,1194,254,1758,1006,630,2134,1382,160,1664,912,536,2040,1288,348,1852,1100,724,1476,19,1523,771,395,1899,1147,207,1711,959,583,2087,1335,113,1617,865,489,1993,1241,1065,689,2193,1441,78,1582,830,454,1958,1206,266,1770,1018,642,2146,1394,172,1676,924,548,2052,1300,360,1864,1112,736,1488,31,1535,783,407,1911,1159,219,1723,971,595,2099,1347,125,1629,877,501,2005,1253,313,1817,2169,1417,54,1558,806,430,1934,1182,242,1746,994,618,2122,1370,148,1652,900,524,2028,1276,336,1840,1088,712,1464,7,1511,759,383,1887,1135,195,1699,947,571,2075,1323,101,1605,853,477,1981,1229,289,1793,1041,665,84,1588,836,460,1964,1212,272,1776,1024,648,2152,1400,178,1682,930,554,2058,1306,366,1870,1118,742,1494,37,1541,789,413,1917,1165,225,1729,977,601,2105,1353,131,1635,883,507,2011,1259,319,1823,1071,695,2199,1447,812,436,1940,1188,248,1752,1e3,624,2128,1376,154,1658,906,530,2034,1282,342,1846,1094,718,1470,13,1517,765,389,1893,1141,201,1705,953,577,2081,1329,107,1611,859,483,1987,1235,295,1799,1047,671,2175,1423,60,1564,1952,1200,260,1764,1012,636,2140,1388,166,1670,918,542,2046,1294,354,1858,1106,730,1482,25,1529,777,401,1905,1153,213,1717,965,589,2093,1341,119,1623,871,495,1999,1247,307,1811,1059,683,2187,1435,72,1576,824,448,236,1740,988,612,2116,1364,142,1646,894,518,2022,1270,330,1834,1082,706,1458,1526,1505,753,377,1881,1129,189,1693,941,565,2069,1317,95,1599,847,471,1975,1223,283,1787,1035,659,2163,1411,48,1552,800,424,1928,1176,1033,657,2161,1409,187,1691,939,563,2067,1315,375,1879,1127,751,1503,46,1550,798,422,1926,1174,234,1738,986,610,2114,1362,140,1644,892,516,2020,1268,328,1832,1080,704,2208,1456,93,1597,845,469,1973,1221,281,1785,2138,1386,164,1668,916,540,2044,1292,352,1856,1104,728,1480,23,1527,775,399,1903,1151,211,1715,963,587,2091,1339,117,1621,869,493,1997,1245,305,1809,1057,681,2185,1433,70,1574,822,446,1950,1198,258,1762,1010,634,176,1680,928,552,2056,1304,364,1868,1116,740,1492,35,1539,787,411,1915,1163,223,1727,975,599,2103,1351,129,1633,881,505,2009,1257,317,1821,1069,693,2197,1445,82,1586,834,458,1962,1210,270,1774,1022,646,2150,1398,904,528,2032,1280,340,1844,1092,716,1468,11,1515,763,387,1891,1139,199,1703,951,575,2079,1327,105,1609,857,481,1985,1233,293,1797,1045,669,2173,1421,58,1562,810,434,1938,1186,246,1750,998,622,2126,1374,152,1656,2062,1310,370,1874,1122,746,1498,41,1545,793,417,1921,1169,229,1733,981,605,2109,1357,135,1639,887,511,2015,1263,323,1827,1075,699,2203,1451,88,1592,840,464,1968,1216,276,1780,1028,652,2156,1404,182,1686,934,558,346,1850,1098,722,1474,17,1521,769,393,1897,1145,205,1709,957,581,2085,1333,111,1615,863,487,1991,1239,299,1803,1051,675,2179,1427,64,1568,816,440,1944,1192,252,1756,1004,628,2132,1380,158,1662,910,534,2038,1286,1110,734,1486,29,1533,781,405,1909,1157,217,1721,969,593,2097,1345,123,1627,875,499,2003,1251,311,1815,1063,687,2191,1439,76,1580,828,452,1956,1204,264,1768,1016,640,2144,1392,170,1674,922,546,2050,1298,358,1862,1462,5,1509,757,381,1885,1133,193,1697,945,569,2073,1321,99,1603,851,475,1979,1227,287,1791,1039,663,2167,1415,52,1556,804,428,1932,1180,240,1744,992,616,2120,1368,146,1650,898,522,2026,1274,334,1838,1086,710,1548,796,420,1924,1172,232,1736,984,608,2112,1360,138,1642,890,514,2018,1266,326,1830,1078,702,2206,1454,91,1595,843,467,1971,1219,279,1783,1031,655,2159,1407,185,1689,937,561,2065,1313,373,1877,1125,749,1501,44,396,1900,1148,208,1712,960,584,2088,1336,114,1618,866,490,1994,1242,302,1806,1054,678,2182,1430,67,1571,819,443,1947,1195,255,1759,1007,631,2135,1383,161,1665,913,537,2041,1289,349,1853,1101,725,1477,20,1524,772,1160,220,1724,972,596,2100,1348,126,1630,878,502,2006,1254,314,1818,1066,690,2194,1442,79,1583,831,455,1959,1207,267,1771,1019,643,2147,1395,173,1677,925,549,2053,1301,361,1865,1113,737,1489,32,1536,784,408,1912,1700,948,572,2076,1324,102,1606,854,478,1982,1230,290,1794,1042,666,2170,1418,55,1559,807,431,1935,1183,243,1747,995,619,2123,1371,149,1653,901,525,2029,1277,337,1841,1089,713,1465,8,1512,760,384,1888,1136,196,602,2106,1354,132,1636,884,508,2012,1260,320,1824,1072,696,2200,1448,85,1589,837,461,1965,1213,273,1777,1025,649,2153,1401,179,1683,931,555,2059,1307,367,1871,1119,743,1495,38,1542,790,414,1918,1166,226,1730,978,1330,108,1612,860,484,1988,1236,296,1800,1048,672,2176,1424,61,1565,813,437,1941,1189,249,1753,1001,625,2129,1377,155,1659,907,531,2035,1283,343,1847,1095,719,1471,14,1518,766,390,1894,1142,202,1706,954,578,2082,1624,872,496,2e3,1248,308,1812,1060,684,2188,1436,73,1577,825,449,1953,1201,261,1765,1013,637,2141,1389,167,1671,919,543,2047,1295,355,1859,1107,731,1483,26,1530,778,402,1906,1154,214,1718,966,590,2094,1342,120,472,1976,1224,284,1788,1036,660,2164,1412,49,1553,801,425,1929,1177,237,1741,989,613,2117,1365,143,1647,895,519,2023,1271,331,1835,1083,707,1459,774,1506,754,378,1882,1130,190,1694,942,566,2070,1318,96,1600,848,1267,327,1831,1079,703,2207,1455,92,1596,844,468,1972,1220,280,1784,1032,656,2160,1408,186,1690,938,562,2066,1314,374,1878,1126,750,1502,45,1549,797,421,1925,1173,233,1737,985,609,2113,1361,139,1643,891,515,2019,1807,1055,679,2183,1431,68,1572,820,444,1948,1196,256,1760,1008,632,2136,1384,162,1666,914,538,2042,1290,350,1854,1102,726,1478,21,1525,773,397,1901,1149,209,1713,961,585,2089,1337,115,1619,867,491,1995,1243,303,691,2195,1443,80,1584,832,456,1960,1208,268,1772,1020,644,2148,1396,174,1678,926,550,2054,1302,362,1866,1114,738,1490,33,1537,785,409,1913,1161,221,1725,973,597,2101,1349,127,1631,879,503,2007,1255,315,1819,1067,1419,56,1560,808,432,1936,1184,244,1748,996,620,2124,1372,150,1654,902,526,2030,1278,338,1842,1090,714,1466,9,1513,761,385,1889,1137,197,1701,949,573,2077,1325,103,1607,855,479,1983,1231,291,1795,1043,667,2171,1590,838,462,1966,1214,274,1778,1026,650,2154,1402,180,1684,932,556,2060,1308,368,1872,1120,744,1496,39,1543,791,415,1919,1167,227,1731,979,603,2107,1355,133,1637,885,509,2013,1261,321,1825,1073,697,2201,1449,86,438,1942,1190,250,1754,1002,626,2130,1378,156,1660,908,532,2036,1284,344,1848,1096,720,1472,15,1519,767,391,1895,1143,203,1707,955,579,2083,1331,109,1613,861,485,1989,1237,297,1801,1049,673,2177,1425,62,1566,814,1202,262,1766,1014,638,2142,1390,168,1672,920,544,2048,1296,356,1860,1108,732,1484,27,1531,779,403,1907,1155,215,1719,967,591,2095,1343,121,1625,873,497,2001,1249,309,1813,1061,685,2189,1437,74,1578,826,450,1954,1742,990,614,2118,1366,144,1648,896,520,2024,1272,332,1836,1084,708,1460,1457,1507,755,379,1883,1131,191,1695,943,567,2071,1319,97,1601,849,473,1977,1225,285,1789,1037,661,2165,1413,50,1554,802,426,1930,1178,238,653,2157,1405,183,1687,935,559,2063,1311,371,1875,1123,747,1499,42,1546,794,418,1922,1170,230,1734,982,606,2110,1358,136,1640,888,512,2016,1264,324,1828,1076,700,2204,1452,89,1593,841,465,1969,1217,277,1781,1029,1381,159,1663,911,535,2039,1287,347,1851,1099,723,1475,18,1522,770,394,1898,1146,206,1710,958,582,2086,1334,112,1616,864,488,1992,1240,300,1804,1052,676,2180,1428,65,1569,817,441,1945,1193,253,1757,1005,629,2133,1675,923,547,2051,1299,359,1863,1111,735,1487,30,1534,782,406,1910,1158,218,1722,970,594,2098,1346,124,1628,876,500,2004,1252,312,1816,1064,688,2192,1440,77,1581,829,453,1957,1205,265,1769,1017,641,2145,1393,171,523,2027,1275,335,1839,1087,711,1463,6,1510,758,382,1886,1134,194,1698,946,570,2074,1322,100,1604,852,476,1980,1228,288,1792,1040,664,2168,1416,53,1557,805,429,1933,1181,241,1745,993,617,2121,1369,147,1651,899,1305,365,1869,1117,741,1493,36,1540,788,412,1916,1164,224,1728,976,600,2104,1352,130,1634,882,506,2010,1258,318,1822,1070,694,2198,1446,83,1587,835,459,1963,1211,271,1775,1023,647,2151,1399,177,1681,929,553,2057,1845,1093,717,1469,12,1516,764,388,1892,1140,200,1704,952,576,2080,1328,106,1610,858,482,1986,1234,294,1798,1046,670,2174,1422,59,1563,811,435,1939,1187,247,1751,999,623,2127,1375,153,1657,905,529,2033,1281,341,729,1481,24,1528,776,400,1904,1152,212,1716,964,588,2092,1340,118,1622,870,494,1998,1246,306,1810,1058,682,2186,1434,71,1575,823,447,1951,1199,259,1763,1011,635,2139,1387,165,1669,917,541,2045,1293,353,1857,1105,0,1504,752,376,1880,1128,188,1692,940,564,2068,1316,94,1598,846,470,1974,1222,282,1786,1034,658,2162,1410,47,1551,799,423,1927,1175,235,1739,987,611,2115,1363,141,1645,893,517,2021,1269,329,1833,1081,705,3]]
},k={ECC000:0,ECC050:1,ECC080:2,ECC100:3,ECC140:4};function u(a){var b=k[a];return{eccInfo:j.ECCInfos[b],headerBits:j.HeaderBits[b]}}l=["square9","square11","square13","square15","square17","square19","square21","square23","square25","square27","square29","square31","square33","square35","square37","square39","square41","square43","square45","square47","square49"];function v(a){var b=l.indexOf(a);if(b<0)throw new g.BadArgumentsException({ecc000_140SymbolSize:a});return j.SymbolCapacities[b]}function w(a){return l.indexOf(a)}function x(a,b){if("auto"==a){a="square9";for(var c=0;c<=l.length-1;++c)if(b<=j.SymbolCapacities[c]){a=l[c];break}}return a}function y(a){var b=l.indexOf(a);return j.ModuleMapping[b]}function z(a,b){var c,d,e,g,h=a[0];for(c=0;c<b;c++)(0,f.isEven)(c)?h[c]=1:h[c]=0;for(d=a[b-1],e=0;e<b;e++)d[e]=1;for(g=1;g<b-1;g++)a[g][0]=1,(0,f.isEven)(g)?a[g][b-1]=1:a[g][b-1]=0}function A(a,b,c){var d,e;for(b-=2,d=0;d<b;++d)for(e=0;e<b;++e)a[1+d][1+e]=+c.getBit(d*b+e)}b.default={chooseEncodationScheme:o,Constants:j,getECC:u,getProperSymbolSize:x,getSymbolSizeInfo:v,getSymbolSizeValue:w,getCodeWord:r,getModuleMapping:y,setBit:s,setFinder:z,setRegionData:A,createModules:e.default.createModules}},function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}();function f(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}e=function(){function a(b,c){f(this,a),this.aT=b,this.offset=c||0}return d(a,[{key:"fetchBit",value:function a(){return this.getBit(this.offset++)}},{key:"getBit",value:function a(b){return!(b>=this.bitLength)&&0!=(this.aT[~~(b/8)]&128>>b%8)}},{key:"setBit",value:function a(b,c){var d=~~(b/8),e=(128>>b%8)%256;this.aT[d]&=(this.aT[d]&~e)%256,c&&(this.aT[d]|=e)}},{key:"putBit",value:function a(b){this.setBit(this.offset,b),this.offset++}},{key:"putBitsMSF",value:function a(b,c,d){var e,f,g=!!d;for(g||(d=this.offset),e=2147483648,f=0;f<c;++f)this.setBit(d+f,0!=(b&e)),e/=2;g||(this.offset+=c)}},{key:"putBitsLSF",value:function a(b,c,d){var e,f,g=!!d;for(g||(d=this.offset),e=1,f=0;f<c;++f)this.setBit(d+f,0!=(b&e)),e<<=1;g||(this.offset+=c)}},{key:"bitLength",get:function a(){return 8*this.aT.length}},{key:"currentBit",get:function a(){return this.getBit(this.offset)}}]),a}(),b.default=e},function(a,b,c){"use strict";var d=c(2),e=j(d),f=c(46),g=j(f),h=c(47),i=j(h);function j(a){return a&&a.__esModule?a:{default:a}}e.default.registerEncoder("EAN8",g.default),e.default.registerEncoder("EAN13",i.default)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(12),f=l(e),g=c(9),h=l(g),i=c(1),j=c(0);function l(a){return a&&a.__esModule?a:{default:a}}function m(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function n(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function o(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}k=function(a){o(b,a);function b(a){var c,d,e;return m(this,b),c=n(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a)),d=c.config.text,d.length<8&&(d+=c.checksum(d,!0)),c.text=d,e=c.calculateData(),c.adjustDesiredSize(e),c.convertToShape(e),c}return d(b,[{key:"validate",value:function a(){var b,c=this.config.text,d=/^[0-9](\d{6}||\d{7})$/;if(!d.test(c))throw new i.InvalidTextException(c,"Text should be numbers. The length should be 7 or 8.");if(8===c.length&&(b=this.checksum(c.substr(0,7),!0),b!=c[7]))throw new i.InvalidTextException(c,"Check digit is invalid.")}},{key:"calculateData",value:function a(){var b=this,c=this.text,d=this.isTextGroup,e=c.substr(0,4),f=c.substr(4,4),g=[];return g.push({text:"<",role:"LEFT_QUIET_ZONE"}),g.push({binary:h.default.NORMAL_GUARD,role:"GUARD"}),d?(0,j.str2Array)(e).forEach(function(a,c){g.push({binary:b.encodeChar(a,h.default.ean8LeftStructure,c),text:a})}):g.push({binary:this.encode(e,h.default.ean8LeftStructure),text:e}),g.push({binary:h.default.CENTRE_GUARD,role:"GUARD"}),d?(0,j.str2Array)(f).forEach(function(a,c){g.push({binary:b.encodeChar(a,h.default.rightStructure,c),text:a})}):g.push({binary:this.encode(f,h.default.rightStructure),text:f}),g.push({binary:h.default.NORMAL_GUARD,role:"GUARD"}),g.push({text:">",role:"RIGHT_QUIET_ZONE"}),g}}]),b}(f.default),b.default=k},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(12),f=l(e),g=c(9),h=l(g),i=c(1),j=c(0);function l(a){return a&&a.__esModule?a:{default:a}}function m(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function n(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function o(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}k=function(a){o(b,a);function b(a){var c,d,e,f,g,h;return m(this,b),c=n(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a)),d=c.config,e=d.text,f=d.addOn,g=d.addOnHeight,e.length<13&&(e+=c.checksum(e)),c.text=e,(0,j.isDefined)(f)&&(c.addOn=""+f,"auto"!==g&&((0,j.isNumberLike)(g)?g=+g:(g=(0,j.convertUnit)((0,j.fixSize2PixelDefault)(g)),c.addOnHeightInPiexl=g,g/=c.style.unitValue)),c.addOnHeight=g),h=c.calculateData(),c.adjustDesiredSize(h),c.convertToShape(h),c}return d(b,[{key:"validate",value:function a(){var b,c=this.config.text,d=/^[1-9](\d{11}||\d{12})$/;if(!d.test(c))throw new i.InvalidTextException(c,"Text should be numbers. And it should not start with 0. The length should be 12 or 13.");if(13===c.length&&(b=this.checksum(c.substr(0,12)),b!=c[12]))throw new i.InvalidTextException(c,"Check digit is invalid.")}},{key:"calculateData",value:function a(){var b=this,c=this.text,d=this.addOn,e=this.isTextGroup,f=c.substr(1,6),g=c.substr(7,6),i=h.default.leftStructure[c[0]],k=[];return k.push({text:c[0],role:"LEFT_QUIET_ZONE"}),k.push({binary:h.default.NORMAL_GUARD,role:"GUARD"}),e?(0,j.str2Array)(f).forEach(function(a,c){k.push({binary:b.encodeChar(a,i,c),text:a})}):k.push({binary:this.encode(f,i),text:f}),k.push({binary:h.default.CENTRE_GUARD,role:"GUARD"}),e?(0,j.str2Array)(g).forEach(function(a,c){k.push({binary:b.encodeChar(a,h.default.rightStructure,c),text:a})}):k.push({binary:this.encode(g,h.default.rightStructure),text:g}),k.push({binary:h.default.NORMAL_GUARD,role:"GUARD"}),d?(k.push({role:"ADDON_QUIET_ZONE"}),this.addon(k)):k.push({text:">",role:"NO_ADDON_RIGHT_QUIET_ZONE"}),k}},{key:"addon",value:function a(b){var c,d,e,f,g,k=this,l=this.addOn,m=this.isTextGroup;if(2===l.length)c=void 0,d=void 0,e=void 0,c=h.default.get2DigitAddOnTable(l),d=this.encodeChar(l[0],c.leftStructure,0),e=this.encodeChar(l[1],c.rightStructure,0),m?(b.push({binary:h.default.ADD_ON_GUARD,role:"ADDON"}),b.push({binary:d,text:l[0],role:"ADDON"}),b.push({binary:h.default.ADD_ON_DELINEATOR,role:"ADDON"}),b.push({binary:e,text:l[1],role:"ADDON"})):b.push({binary:h.default.ADD_ON_GUARD+d+h.default.ADD_ON_DELINEATOR+e,text:l,role:"ADDON"});else{if(5!==l.length)throw new i.BadArgumentsException(l);f=h.default.get5DigitAddOnTable(l),m?(b.push({binary:h.default.ADD_ON_GUARD,role:"ADDON"}),(0,j.str2Array)(l).forEach(function(a,c){b.push({binary:k.encodeFor5DigitAddOn(a,c,f),text:a,role:"ADDON"}),c<l.length-1&&b.push({binary:h.default.ADD_ON_DELINEATOR,role:"ADDON"})})):(g=(0,j.str2Array)(l).reduce(function(a,b,c){return a+=k.encodeFor5DigitAddOn(b,c,f),c<l.length-1&&(a+=h.default.ADD_ON_DELINEATOR),a},""),b.push({binary:h.default.ADD_ON_GUARD+g,text:l,role:"ADDON"}))}b.push({text:">",role:"ADDON_RIGHT_QUIET_ZONE"})}},{key:"encodeFor5DigitAddOn",value:function a(b,c,d){var e=h.default.TABLE[d[c]];return e[b]}}]),b}(f.default),b.default=k},function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=c(2),e=i(d),f=c(11),g=i(f);function i(a){return a&&a.__esModule?a:{default:a}}function j(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function k(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function l(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}h=function(a){l(b,a);function b(a){return j(this,b),k(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,!0))}return b}(g.default),b.default=h,e.default.registerEncoder("GS1_128",h)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(7),f=r(e),g=c(2),h=r(g),i=c(50),j=r(i),k=c(0),l=c(51),m=r(l),n=c(52),o=r(n),p=c(1);function r(a){return a&&a.__esModule?a:{default:a}}function s(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function t(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function u(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}q=function(a){u(b,a);function b(a){var c,d,e,f,g,h,i,k,l;return s(this,b),c=t(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,j.default.defaultConfig)),d=c.config,e=d.text,f=d.errorCorrectionLevel,g=d.columns,h=d.rows,i=d.compact,c.text=e,c.ecl="auto"===f?f:+f,c.columns="auto"===g?g:+g,c.rows="auto"===h?h:+h,c.compact=!!i,k=j.default.compaction(e),c.encode(k),c.genEcc(),l=c.fillMatrix(),c.adjustDesiredSize(l),c.convertToShape(l),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.text,d=b.errorCorrectionLevel,e=b.rows,f=b.columns,g=/^[\x00-\xFF]+$/;if(!g.test(c))throw new p.InvalidTextException(c);if((0,k.isNumberLike)(d)&&(d<0||d>8))throw new p.BadArgumentsException({errorCorrectionLevel:d},"ErrorCorrectionLevel is from 0 - 8.");if((0,k.isNumberLike)(e)&&(e<3||e>90))throw new p.BadArgumentsException({rows:e},"Rows is from 3 - 90.");if((0,k.isNumberLike)(f)&&(f<1||f>30))throw new p.BadArgumentsException({columns:f},"Columns is from 1 - 30.")}},{key:"encodeNC",value:function a(b){var c=b.text,d=[];return(0,k.sliceString)(c,44,function(a){for(var b,c=new o.default("1"+a),e=[];;)if(b=c.modulo(900),c=c.dividedToIntegerBy((0,o.default)(900)),e.unshift(+(""+b)),c.lessThan(1))break;d=d.concat(e)}),d.unshift(j.default.MODE_NC),d}},{key:"encodeBC",value:function a(b){var c=b.text,d=[];return(0,k.sliceString)(c,6,function(a){var b,c,e,f;if(6===a.length){b=5,c=0,(0,k.sliceString)(a,1,function(a){c+=a.charCodeAt(0)*Math.pow(256,b--)}),e=0,f=Math.floor(c/Math.pow(900,e))%900;do d.unshift(f),f=Math.floor(c/Math.pow(900,++e))%900;while(f>0)}else(0,k.sliceString)(a,1,function(a){d.push(a.charCodeAt(0))})}),d.unshift(b.mode),d}},{key:"encodeTC",value:function a(b){var c,d=[],e="al";return b.subModes.forEach(function(a){e!==a.mode&&(d=d.concat(j.default.getTCSubModeValue(a.mode,e))),(0,k.sliceString)(a.text,1,function(b){d.push(j.default.getTCValue(b,a.mode))}),"ps"!==a.mode&&"as"!==a.mode&&(e=a.mode)}),c=[j.default.MODE_TC],(0,k.sliceArray)(d,2,function(a){2===a.length?c.push(30*a[0]+a[1]):c.push(30*a[0]+29)}),c}},{key:"encode",value:function a(b){var c=this,d=[];b.forEach(function(a){switch(a.mode){case j.default.MODE_TC:d=d.concat(c.encodeTC(a));break;case j.default.MODE_BC:case j.default.MODE_BC6:case j.default.MODE_BC_SHIFT:d=d.concat(c.encodeBC(a));break;case j.default.MODE_NC:d=d.concat(c.encodeNC(a))}}),d[0]===j.default.MODE_TC&&d.shift(),this.data=d}},{key:"genEcc",value:function a(){var b,c,d,e,f,g,h=this.data,i=this.ecl,k=this.columns,l=this.rows;if("auto"===i&&(this.ecl=i=j.default.getAutoECL(h)),b=Math.pow(2,i+1),c=h.length+1+b,"auto"===l&&"auto"===k)d=j.default.getAutoRowAndCol(c),e=d.col,f=d.row,this.columns=k=e,this.rows=l=f;else if("auto"===l){if(l=Math.ceil(c/k),l>90)throw new p.BadArgumentsException({columns:k},"Columns is not large enough");l=l<3?3:l,this.rows=l}else if("auto"===k){if(k=Math.ceil(c/l),k>30)throw new p.BadArgumentsException({rows:l},"Rows is not large enough");this.columns=k}if(g=k*l-b,h.unshift(g),h.length>j.default.MAX_DATA_NUM||h.length>g)throw new p.TextTooLargeException;for(;h.length<g;)h.push(j.default.PAD);this.ecc=(0,m.default)(h,this.ecl)}},{key:"fillMatrix",value:function a(){var b,c=this.data,d=this.ecc,e=this.columns,f=this.rows,g=this.ecl,h=this.compact,i=c.concat(d),k=j.default.createModules(f,e);return i.forEach(function(a,b){var c=Math.floor(b/e),d=b%e;k[c][d]=j.default.getPattern(c,a)}),b=[],k.forEach(function(a,c){var d=j.default.getPattern(c,j.default.getIndicator(c,f,g,e)),i=j.default.getPattern(c,j.default.getIndicator(c,f,g,e,!0)),k=[j.default.START,d.toString(2)];a.forEach(function(a){k.push(a.toString(2))}),h?k.push(j.default.COMPACT_END):(k.push(i.toString(2)),k.push(j.default.END)),b.push(k)}),b}},{key:"adjustDesiredSize",value:function a(b){var c,d,e,f=this.config.desiredSize,g=this.quietZone,h=this.fontHeight,i=this.containerWidth,j=this.containerHeight;f&&(c=b[0].reduce(function(a,b){return a+=b.length},0)+(0,k.getQuietZoneRelativeValue)(g.left)+(0,k.getQuietZoneRelativeValue)(g.right),i=i-(0,k.getQuietZonePixelValue)(g.left)-(0,k.getQuietZonePixelValue)(g.right),d=void 0,e=void 0,f.forceRounding?(d=~~(i/c),e=d<1?1:d):e=d=i/c,j=j-(0,k.getQuietZonePixelValue)(g.top)-(0,k.getQuietZonePixelValue)(g.bottom),this.style.unitValue=e,this.style.fontSizeInUnit=h/e,this.height=j/e-(0,k.getQuietZoneRelativeValue)(g.top)-(0,k.getQuietZoneRelativeValue)(g.bottom),Object.keys(g).forEach(function(a){g[a].originIsAbsoluteValue&&(g[a].relativeValue=g[a].pixelValue/e)}))}},{key:"convertToShape",value:function a(b){var c=this.height,d=this.quietZone,e=c/b.length,f=[],g=void 0,h=d.top.relativeValue;b.forEach(function(a){g=d.left.relativeValue,a.forEach(function(a){(0,k.combineTruthy)(a).forEach(function(a){0!==a?(f.push({type:"rect",x:g,y:h,width:a,height:e}),g+=a):g++})}),h+=e}),this.size={width:g+d.right.relativeValue,height:h+d.bottom.relativeValue},this.shapes=f}}]),b}(f.default),b.default=q,h.default.registerEncoder("PDF417",q)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y;Object.defineProperty(b,"__esModule",{value:!0}),d=c(0),e=[[120256,125680,128380,120032,125560,128318,108736,119920,108640,86080,108592,86048,110016,120560,125820,109792,120440,125758,88256,109680,88160,89536,110320,120700,89312,110200,120638,89200,110140,89840,110460,89720,110398,89980,128506,119520,125304,128190,107712,119408,125244,107616,119352,84032,107568,119324,84e3,107544,83984,108256,119672,125374,85184,108144,119612,85088,108088,119582,85040,108060,85728,108408,119742,85616,108348,85560,108318,85880,108478,85820,85790,107200,119152,125116,107104,119096,125086,83008,107056,119068,82976,107032,82960,82952,83648,107376,119228,83552,107320,119198,83504,107292,83480,83468,83824,107452,83768,107422,83740,83900,106848,118968,125022,82496,106800,118940,82464,106776,118926,82448,106764,82440,106758,82784,106936,119006,82736,106908,82712,106894,82700,82694,106974,82830,82240,106672,118876,82208,106648,118862,82192,106636,82184,106630,82180,82352,82328,82316,82080,118830,106572,106566,82050,117472,124280,127678,103616,117360,124220,103520,117304,124190,75840,103472,75808,104160,117624,124350,76992,104048,117564,76896,103992,76848,76824,77536,104312,117694,77424,104252,77368,77340,77688,104382,77628,77758,121536,126320,128700,121440,126264,128670,111680,121392,126236,111648,121368,126222,111632,121356,103104,117104,124092,112320,103008,117048,124062,112224,121656,126366,93248,74784,102936,117006,93216,112152,93200,75456,103280,117180,93888,75360,103224,117150,93792,112440,121758,93744,75288,93720,75632,103356,94064,75576,103326,94008,112542,93980,75708,94140,75678,94110,121184,126136,128606,111168,121136,126108,111136,121112,126094,111120,121100,111112,111108,102752,116920,123998,111456,102704,116892,91712,74272,121244,116878,91680,74256,102668,91664,111372,102662,74244,74592,102840,116958,92e3,74544,102812,91952,111516,102798,91928,74508,74502,74680,102878,92088,74652,92060,74638,92046,92126,110912,121008,126044,110880,120984,126030,110864,120972,110856,120966,110852,110850,74048,102576,116828,90944,74016,102552,116814,90912,111e3,121038,90896,73992,102534,90888,110982,90884,74160,102620,91056,74136,102606,91032,111054,91020,74118,91014,91100,91086,110752,120920,125998,110736,120908,110728,120902,110724,110722,73888,102488,116782,90528,73872,102476,90512,110796,102470,90504,73860,90500,73858,73944,90584,90572,90566,120876,120870,110658,102444,73800,90312,90308,90306,101056,116080,123580,100960,116024,70720,100912,115996,70688,100888,70672,70664,71360,101232,116156,71264,101176,116126,71216,101148,71192,71180,71536,101308,71480,101278,71452,71612,71582,118112,124600,127838,105024,118064,124572,104992,118040,124558,104976,118028,104968,118022,100704,115896,123486,105312,100656,115868,79424,70176,118172,115854,79392,105240,100620,79376,70152,79368,70496,100792,115934,79712,70448,118238,79664,105372,100750,79640,70412,79628,70584,100830,79800,70556,79772,70542,70622,79838,122176,126640,128860,122144,126616,128846,122128,126604,122120,126598,122116,104768,117936,124508,113472,104736,126684,124494,113440,122264,126670,113424,104712,117894,113416,122246,104706,69952,100528,115804,78656,69920,100504,115790,96064,78624,104856,117966,96032,113560,122318,100486,96016,78600,104838,96008,69890,70064,100572,78768,70040,100558,96176,78744,104910,96152,113614,70022,78726,70108,78812,70094,96220,78798,122016,126552,128814,122e3,126540,121992,126534,121988,121986,104608,117848,124462,113056,104592,126574,113040,122060,117830,113032,104580,113028,104578,113026,69792,100440,115758,78240,69776,100428,95136,78224,104652,100422,95120,113100,69764,95112,78212,69762,78210,69848,100462,78296,69836,95192,78284,69830,95180,78278,69870,95214,121936,126508,121928,126502,121924,121922,104528,117804,112848,104520,117798,112840,121958,112836,104514,112834,69712,100396,78032,69704,100390,94672,78024,104550,94664,112870,69698,94660,78018,94658,78060,94700,94694,126486,121890,117782,104484,104482,69672,77928,94440,69666,77922,99680,68160,99632,68128,99608,115342,68112,99596,68104,99590,68448,99768,115422,68400,99740,68376,99726,68364,68358,68536,99806,68508,68494,68574,101696,116400,123740,101664,116376,101648,116364,101640,116358,101636,67904,99504,115292,72512,67872,116444,115278,72480,101784,116430,72464,67848,99462,72456,101766,67842,68016,99548,72624,67992,99534,72600,101838,72588,67974,68060,72668,68046,72654,118432,124760,127918,118416,124748,118408,124742,118404,118402,101536,116312,105888,101520,116300,105872,118476,116294,105864,101508,105860,101506,105858,67744,99416,72096,67728,116334,80800,72080,101580,99398,80784,105932,67716,80776,72068,67714,72066,67800,99438,72152,67788,80856,72140,67782,80844,72134,67822,72174,80878,126800,128940,126792,128934,126788,126786,118352,124716,122576,126828,124710,122568,126822,122564,118338,122562,101456,116268,105680,101448,116262,114128,105672,118374,114120,122598,101442,114116,105666,114114,67664,99372,71888,67656,99366,80336,71880,101478,97232,80328,105702,67650,97224,114150,71874,97220,67692,71916,67686,80364,71910,97260,80358,97254,126760,128918,126756,126754,118312,124694,122472,126774,122468,118306,122466,101416,116246,105576,101412,113896,105572,101410,113892,105570,113890,67624,99350,71784,101430,80104,71780,67618,96744,80100,71778,96740,80098,96738,71798,96758,126738,122420,122418,105524,113780,113778,71732,79988,96500,96498,66880,66848,98968,66832,66824,66820,66992,66968,66956,66950,67036,67022,1e5,99984,115532,99976,115526,99972,99970,66720,98904,69024,100056,98892,69008,100044,69e3,100038,68996,66690,68994,66776,98926,69080,100078,69068,66758,69062,66798,69102,116560,116552,116548,116546,99920,102096,116588,115494,102088,116582,102084,99906,102082,66640,68816,66632,98854,73168,68808,66628,73160,68804,66626,73156,68802,66668,68844,66662,73196,68838,73190,124840,124836,124834,116520,118632,124854,118628,116514,118626,99880,115478,101992,116534,106216,101988,99874,106212,101986,106210,66600,98838,68712,99894,72936,68708,66594,81384,72932,68706,81380,72930,66614,68726,72950,81398,128980,128978,124820,126900,124818,126898,116500,118580,116498,122740,118578,122738,99860,101940,99858,106100,101938,114420],[128352,129720,125504,128304,129692,125472,128280,129678,125456,128268,125448,128262,125444,125792,128440,129758,120384,125744,128412,120352,125720,128398,120336,125708,120328,125702,120324,120672,125880,128478,110144,120624,125852,110112,120600,125838,110096,120588,110088,120582,110084,110432,120760,125918,89664,110384,120732,89632,110360,120718,89616,110348,89608,110342,89952,110520,120798,89904,110492,89880,110478,89868,90040,110558,90012,89998,125248,128176,129628,125216,128152,129614,125200,128140,125192,128134,125188,125186,119616,125360,128220,119584,125336,128206,119568,125324,119560,125318,119556,119554,108352,119728,125404,108320,119704,125390,108304,119692,108296,119686,108292,108290,85824,108464,119772,85792,108440,119758,85776,108428,85768,108422,85764,85936,108508,85912,108494,85900,85894,85980,85966,125088,128088,129582,125072,128076,125064,128070,125060,125058,119200,125144,128110,119184,125132,119176,125126,119172,119170,107424,119256,125166,107408,119244,107400,119238,107396,107394,83872,107480,119278,83856,107468,83848,107462,83844,83842,83928,107502,83916,83910,83950,125008,128044,125e3,128038,124996,124994,118992,125036,118984,125030,118980,118978,106960,119020,106952,119014,106948,106946,82896,106988,82888,106982,82884,82882,82924,82918,124968,128022,124964,124962,118888,124982,118884,118882,106728,118902,106724,106722,82408,106742,82404,82402,124948,124946,118836,118834,106612,106610,124224,127664,129372,124192,127640,129358,124176,127628,124168,127622,124164,124162,117568,124336,127708,117536,124312,127694,117520,124300,117512,124294,117508,117506,104256,117680,124380,104224,117656,124366,104208,117644,104200,117638,104196,104194,77632,104368,117724,77600,104344,117710,77584,104332,77576,104326,77572,77744,104412,77720,104398,77708,77702,77788,77774,128672,129880,93168,128656,129868,92664,128648,129862,92412,128644,128642,124064,127576,129326,126368,124048,129902,126352,128716,127558,126344,124036,126340,124034,126338,117152,124120,127598,121760,117136,124108,121744,126412,124102,121736,117124,121732,117122,121730,103328,117208,124142,112544,103312,117196,112528,121804,117190,112520,103300,112516,103298,112514,75680,103384,117230,94112,75664,103372,94096,112588,103366,94088,75652,94084,75650,75736,103406,94168,75724,94156,75718,94150,75758,128592,129836,91640,128584,129830,91388,128580,91262,128578,123984,127532,126160,123976,127526,126152,128614,126148,123970,126146,116944,124012,121296,116936,124006,121288,126182,121284,116930,121282,102864,116972,111568,102856,116966,111560,121318,111556,102850,111554,74704,102892,92112,74696,102886,92104,111590,92100,74690,92098,74732,92140,74726,92134,128552,129814,90876,128548,90750,128546,123944,127510,126056,128566,126052,123938,126050,116840,123958,121064,116836,121060,116834,121058,102632,116854,111080,121078,111076,102626,111074,74216,102646,91112,74212,91108,74210,91106,74230,91126,128532,90494,128530,123924,126004,123922,126002,116788,120948,116786,120946,102516,110836,102514,110834,73972,90612,73970,90610,128522,123914,125978,116762,120890,102458,110714,123552,127320,129198,123536,127308,123528,127302,123524,123522,116128,123608,127342,116112,123596,116104,123590,116100,116098,101280,116184,123630,101264,116172,101256,116166,101252,101250,71584,101336,116206,71568,101324,71560,101318,71556,71554,71640,101358,71628,71622,71662,127824,129452,79352,127816,129446,79100,127812,78974,127810,123472,127276,124624,123464,127270,124616,127846,124612,123458,124610,115920,123500,118224,115912,123494,118216,124646,118212,115906,118210,100816,115948,105424,100808,115942,105416,118246,105412,100802,105410,70608,100844,79824,70600,100838,79816,105446,79812,70594,79810,70636,79852,70630,79846,129960,95728,113404,129956,95480,113278,129954,95356,95294,127784,129430,78588,128872,129974,95996,78462,128868,127778,95870,128866,123432,127254,124520,123428,126696,128886,123426,126692,124514,126690,115816,123446,117992,115812,122344,117988,115810,122340,117986,122338,100584,115830,104936,100580,113640,104932,100578,113636,104930,113634,70120,100598,78824,70116,96232,78820,70114,96228,78818,96226,70134,78838,129940,94968,113022,129938,94844,94782,127764,78206,128820,127762,95102,128818,123412,124468,123410,126580,124466,126578,115764,117876,115762,122100,117874,122098,100468,104692,100466,113140,104690,113138,69876,78324,69874,95220,78322,95218,129930,94588,94526,127754,128794,123402,124442,126522,115738,117818,121978,100410,104570,112890,69754,78074,94714,94398,123216,127148,123208,127142,123204,123202,115408,123244,115400,123238,115396,115394,99792,115436,99784,115430,99780,99778,68560,99820,68552,99814,68548,68546,68588,68582,127400,129238,72444,127396,72318,127394,123176,127126,123752,123172,123748,123170,123746,115304,123190,116456,115300,116452,115298,116450,99560,115318,101864,99556,101860,99554,101858,68072,99574,72680,68068,72676,68066,72674,68086,72694,129492,80632,105854,129490,80508,80446,127380,72062,127924,127378,80766,127922,123156,123700,123154,124788,123698,124786,115252,116340,115250,118516,116338,118514,99444,101620,99442,105972,101618,105970,67828,72180,67826,80884,72178,80882,97008,114044,96888,113982,96828,96798,129482,80252,130010,97148,80190,97086,127370,127898,128954,123146,123674,124730,126842,115226,116282,118394,122618,99386,101498,105722,114170,67706,71930,80378,96632,113854,96572,96542,80062,96702,96444,96414,96350,123048,123044,123042,115048,123062,115044,115042,99048,115062,99044,99042,67048,99062,67044,67042,67062,127188,68990,127186,123028,123316,123026,123314,114996,115572,114994,115570,98932,100084,98930,100082,66804,69108,66802,69106,129258,73084,73022,127178,127450,123018,123290,123834,114970,115514,116602,98874,99962,102138,66682,68858,73210,81272,106174,81212,81182,72894,81342,97648,114364,97592,114334,97564,97550,81084,97724,81054,97694,97464,114270,97436,97422,80990,97502,97372,97358,97326,114868,114866,98676,98674,66292,66290,123098,114842,115130,98618,99194,66170,67322,69310,73404,73374,81592,106334,81564,81550,73310,81630,97968,114524,97944,114510,97932,97926,81500,98012,81486,97998,97880,114478,97868,97862,81454,97902,97836,97830,69470,73564,73550,81752,106414,81740,81734,73518,81774,81708,81702],[109536,120312,86976,109040,120060,86496,108792,119934,86256,108668,86136,129744,89056,110072,129736,88560,109820,129732,88312,109694,129730,88188,128464,129772,89592,128456,129766,89340,128452,89214,128450,125904,128492,125896,128486,125892,125890,120784,125932,120776,125926,120772,120770,110544,120812,110536,120806,110532,84928,108016,119548,84448,107768,119422,84208,107644,84088,107582,84028,129640,85488,108284,129636,85240,108158,129634,85116,85054,128232,129654,85756,128228,85630,128226,125416,128246,125412,125410,119784,125430,119780,119778,108520,119798,108516,108514,83424,107256,119166,83184,107132,83064,107070,83004,82974,129588,83704,107390,129586,83580,83518,128116,83838,128114,125172,125170,119284,119282,107508,107506,82672,106876,82552,106814,82492,82462,129562,82812,82750,128058,125050,119034,82296,106686,82236,82206,82366,82108,82078,76736,103920,117500,76256,103672,117374,76016,103548,75896,103486,75836,129384,77296,104188,129380,77048,104062,129378,76924,76862,127720,129398,77564,127716,77438,127714,124392,127734,124388,124386,117736,124406,117732,117730,104424,117750,104420,104418,112096,121592,126334,92608,111856,121468,92384,111736,121406,92272,111676,92216,111646,92188,75232,103160,117118,93664,74992,103036,93424,112252,102974,93304,74812,93244,74782,93214,129332,75512,103294,129908,129330,93944,75388,129906,93820,75326,93758,127604,75646,128756,127602,94078,128754,124148,126452,124146,126450,117236,121844,117234,121842,103412,103410,91584,111344,121212,91360,111224,121150,91248,111164,91192,111134,91164,91150,74480,102780,91888,74360,102718,91768,111422,91708,74270,91678,129306,74620,129850,92028,74558,91966,127546,128634,124026,126202,116986,121338,102906,90848,110968,121022,90736,110908,90680,110878,90652,90638,74104,102590,91e3,74044,90940,74014,90910,74174,91070,90480,110780,90424,110750,90396,90382,73916,90556,73886,90526,90296,110686,90268,90254,73822,90334,90204,90190,71136,101112,116094,70896,100988,70776,100926,70716,70686,129204,71416,101246,129202,71292,71230,127348,71550,127346,123636,123634,116212,116210,101364,101362,79296,105200,118140,79072,105080,118078,78960,105020,78904,104990,78876,78862,70384,100732,79600,70264,100670,79480,105278,79420,70174,79390,129178,70524,129466,79740,70462,79678,127290,127866,123514,124666,115962,118266,100858,113376,122232,126654,95424,113264,122172,95328,113208,122142,95280,113180,95256,113166,95244,78560,104824,117950,95968,78448,104764,95856,113468,104734,95800,78364,95772,78350,95758,70008,100542,78712,69948,96120,78652,69918,96060,78622,96030,70078,78782,96190,94912,113008,122044,94816,112952,122014,94768,112924,94744,112910,94732,94726,78192,104636,95088,78136,104606,95032,113054,95004,78094,94990,69820,78268,69790,95164,78238,95134,94560,112824,121950,94512,112796,94488,112782,94476,94470,78008,104542,94648,77980,94620,77966,94606,69726,78046,94686,94384,112732,94360,112718,94348,94342,77916,94428,77902,94414,94296,112686,94284,94278,77870,94318,94252,94246,68336,99708,68216,99646,68156,68126,68476,68414,127162,123258,115450,99834,72416,101752,116414,72304,101692,72248,101662,72220,72206,67960,99518,72568,67900,72508,67870,72478,68030,72638,80576,105840,118460,80480,105784,118430,80432,105756,80408,105742,80396,80390,72048,101564,80752,71992,101534,80696,71964,80668,71950,80654,67772,72124,67742,80828,72094,80798,114016,122552,126814,96832,113968,122524,96800,113944,122510,96784,113932,96776,113926,96772,80224,105656,118366,97120,80176,105628,97072,114076,105614,97048,80140,97036,80134,97030,71864,101470,80312,71836,97208,80284,71822,97180,80270,97166,67678,71902,80350,97246,96576,113840,122460,96544,113816,122446,96528,113804,96520,113798,96516,96514,80048,105564,96688,80024,105550,96664,113870,96652,80006,96646,71772,80092,71758,96732,80078,96718,96416,113752,122414,96400,113740,96392,113734,96388,96386,79960,105518,96472,79948,96460,79942,96454,71726,79982,96494,96336,113708,96328,113702,96324,96322,79916,96364,79910,96358,96296,113686,96292,96290,79894,96310,66936,99006,66876,66846,67006,68976,100028,68920,99998,68892,68878,66748,69052,66718,69022,73056,102072,116574,73008,102044,72984,102030,72972,72966,68792,99934,73144,68764,73116,68750,73102,66654,68830,73182,81216,106160,118620,81184,106136,118606,81168,106124,81160,106118,81156,81154,72880,101980,81328,72856,101966,81304,106190,81292,72838,81286,68700,72924,68686,81372,72910,81358,114336,122712,126894,114320,122700,114312,122694,114308,114306,81056,106072,118574,97696,81040,106060,97680,114380,106054,97672,81028,97668,81026,97666,72792,101934,81112,72780,97752,81100,72774,97740,81094,97734,68654,72814,81134,97774,114256,122668,114248,122662,114244,114242,80976,106028,97488,80968,106022,97480,114278,97476,80962,97474,72748,81004,72742,97516,80998,97510,114216,122646,114212,114210,80936,106006,97384,80932,97380,80930,97378,72726,80950,97398,114196,114194,80916,97332,80914,97330,66236,66206,67256,99166,67228,67214,66142,67294,69296,100188,69272,100174,69260,69254,67164,69340,67150,69326,73376,102232,116654,73360,102220,73352,102214,73348,73346,69208,100142,73432,102254,73420,69190,73414,67118,69230,73454,106320,118700,106312,118694,106308,106306,73296,102188,81616,106348,102182,81608,73284,81604,73282,81602,69164,73324,69158,81644,73318,81638,122792,126934,122788,122786,106280,118678,114536,106276,114532,106274,114530,73256,102166,81512,73252,98024,81508,73250,98020,81506,98018,69142,73270,81526,98038,122772,122770,106260,114484,106258,114482,73236,81460,73234,97908,81458,97906,122762,106250,114458,73226,81434,97850,66396,66382,67416,99246,67404,67398,66350,67438,69456,100268,69448,100262,69444,69442,67372,69484,67366,69478,102312,116694,102308,102306,69416,100246,73576,102326,73572,69410,73570,67350,69430,73590,118740,118738,102292,106420,102290,106418,69396,73524,69394,81780,73522,81778,118730,102282,106394,69386,73498,81722,66476,66470,67496,99286,67492,67490,66454,67510,100308,100306,67476,69556,67474,69554,116714]],
f="11111111010101000",g="111111101000101001",h="1",i=900,j="\u2000",k="\u2001",l="\u2002",m="\u2003",n="\u2004",o="\u2005",p=925,q={ll:j,ps:k,ml:l,al:m,pl:n,as:o},r=["ABCDEFGHIJKLMNOPQRSTUVWXYZ "+j+l+k,"abcdefghijklmnopqrstuvwxyz "+o+l+k,"0123456789&\r\t,:#-.$/+%*=^\u2004 "+j+m+k,";<>@[\\]_`~!\r\t,:\n-.$/\"|*()?{}'"+m],s={errorCorrectionLevel:"auto",columns:"auto",rows:"auto",compact:!1,height:60,quietZone:{top:2,left:2,right:2,bottom:2}},t=900,u=901,v=902,w=924,x=913;function z(a){return r[0].indexOf(a)>-1}function A(a){return r[1].indexOf(a)>-1}function B(a){return r[2].indexOf(a)>-1}function C(a){return r[3].indexOf(a)>-1}function D(a){return z(a)||A(a)||B(a)||C(a)}function E(a,b){for(var c=b,d=a.length,e=0;c<d&&(a[c]>="0"&&a[c]<="9"?e++:e=0,D(a[c]));){if(e>=13){c-=--e;break}c++}return a.substring(b,c)}function F(a,b){for(var c=b,d=a.length;c<d&&!(a[c]<"0"||a[c]>"9");)c++;return a.substring(b,c)}function G(a,b){for(var c=b,d=a.length,e=0,f=0;c<d;){if(a[c]<"0"||a[c]>"9"?e++:e=0,D(a[c])?f++:e=0,f>=5){c-=--f;break}if(e>=13){c-=--e;break}c++}return a.substring(b,c)}function H(a){for(var b,c,d,e=0,f=a.length,g={mode:t,text:""},h=[];e<f;)b=F(a,e),b.length>=13?(g={mode:v,text:b},h.push(g),e+=b.length):(c=E(a,e),c.length>=5?(g={mode:t,text:c},h.push(g),e+=c.length):(d=G(a,e),1===d.length&&g.mode==t?(g={mode:x,text:d},h.push(g)):(g=d.length%6===0?{mode:w,text:d}:{mode:u,text:d},h.push(g)),e+=d.length));return h.forEach(function(a){var b,c,d,e,f,g,h;if(a.mode==t)for(b={mode:"al",text:""},c=[b],a.subModes=c,d=0,e=a.text.length;d<e;d++)f=a.text[d],z(f)?0===d||"al"===b.mode?b.text+=f:(g=a.text[d+1],b=z(g)?{mode:"al",text:f}:"ll"===b.mode?{mode:"as",text:f}:{mode:"al",text:f},c.push(b)):A(f)?"ll"===b.mode?b.text+=f:(b={mode:"ll",text:f},c.push(b)):B(f)?"pl"===b.mode&&C(f)?b.text+=f:"ml"===b.mode?b.text+=f:(b={mode:"ml",text:f},c.push(b)):C(f)&&("pl"===b.mode?b.text+=f:(h=a.text[d+1],b=C(h)?{mode:"pl",text:f}:{mode:"ps",text:f},c.push(b)))}),h}function I(a,b,c,d,e){var f=~~(a/3),g=~~((b-1)/3),h=3*c+(b-1)%3,i=d-1,j=a%3*3,k=void 0;switch(j){case 0:k=e?i:g;break;case 3:k=e?g:h;break;case 6:k=e?h:i}return 30*f+k}function J(a,b){var c=e[a%3];return c[b]}function K(a,b){var c=void 0;switch(b){case"ll":c=r[1].indexOf(a);break;case"ml":c=r[2].indexOf(a);break;case"al":case"as":c=r[0].indexOf(a);break;case"pl":case"ps":c=r[3].indexOf(a)}return c}y={al:{ll:[27],ml:[28],pl:[28,25]},ll:{al:[28,28],ml:[28],pl:[28,25]},ml:{al:[28],ll:[27],pl:[25]},pl:{al:[29],ll:[29,27],ml:[29,28]}};function L(a,b){var c=K(q[a],b);return c>-1?[c]:y[b][a]}function M(a,b){var c,e=[];for(c=0;c<a;c++)e.push((0,d.fillArray)(Array(b),null));return e}function N(a){var b=a.length;return b<=40?2:b<=160?3:b<=320?4:5}function O(a){var b=31,c=void 0,d=void 0;do if(b--,c=Math.ceil(a/b),d=c/b,b<2)break;while(d<4);return c<3&&(c=3),{col:b,row:c}}b.default={defaultConfig:s,compaction:H,getIndicator:I,MODE_TC:t,MODE_BC:u,MODE_NC:v,MODE_BC6:w,MODE_BC_SHIFT:x,getTCValue:K,PAD:i,createModules:M,getPattern:J,START:f,END:g,getAutoECL:N,subModeMap:q,getTCSubModeValue:L,MAX_DATA_NUM:p,COMPACT_END:h,getAutoRowAndCol:O}},function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),b.default=f,d=c(0),e=[[27,917],[522,568,723,809],[237,308,436,284,646,653,428,379],[274,562,232,755,599,524,801,132,295,116,442,428,295,42,176,65],[361,575,922,525,176,586,640,321,536,742,677,742,687,284,193,517,273,494,263,147,593,800,571,320,803,133,231,390,685,330,63,410],[539,422,6,93,862,771,453,106,610,287,107,505,733,877,381,612,723,476,462,172,430,609,858,822,543,376,511,400,672,762,283,184,440,35,519,31,460,594,225,535,517,352,605,158,651,201,488,502,648,733,717,83,404,97,280,771,840,629,4,381,843,623,264,543],[521,310,864,547,858,580,296,379,53,779,897,444,400,925,749,415,822,93,217,208,928,244,583,620,246,148,447,631,292,908,490,704,516,258,457,907,594,723,674,292,272,96,684,432,686,606,860,569,193,219,129,186,236,287,192,775,278,173,40,379,712,463,646,776,171,491,297,763,156,732,95,270,447,90,507,48,228,821,808,898,784,663,627,378,382,262,380,602,754,336,89,614,87,432,670,616,157,374,242,726,600,269,375,898,845,454,354,130,814,587,804,34,211,330,539,297,827,865,37,517,834,315,550,86,801,4,108,539],[524,894,75,766,882,857,74,204,82,586,708,250,905,786,138,720,858,194,311,913,275,190,375,850,438,733,194,280,201,280,828,757,710,814,919,89,68,569,11,204,796,605,540,913,801,700,799,137,439,418,592,668,353,859,370,694,325,240,216,257,284,549,209,884,315,70,329,793,490,274,877,162,749,812,684,461,334,376,849,521,307,291,803,712,19,358,399,908,103,511,51,8,517,225,289,470,637,731,66,255,917,269,463,830,730,433,848,585,136,538,906,90,2,290,743,199,655,903,329,49,802,580,355,588,188,462,10,134,628,320,479,130,739,71,263,318,374,601,192,605,142,673,687,234,722,384,177,752,607,640,455,193,689,707,805,641,48,60,732,621,895,544,261,852,655,309,697,755,756,60,231,773,434,421,726,528,503,118,49,795,32,144,500,238,836,394,280,566,319,9,647,550,73,914,342,126,32,681,331,792,620,60,609,441,180,791,893,754,605,383,228,749,760,213,54,297,134,54,834,299,922,191,910,532,609,829,189,20,167,29,872,449,83,402,41,656,505,579,481,173,404,251,688,95,497,555,642,543,307,159,924,558,648,55,497,10],[352,77,373,504,35,599,428,207,409,574,118,498,285,380,350,492,197,265,920,155,914,299,229,643,294,871,306,88,87,193,352,781,846,75,327,520,435,543,203,666,249,346,781,621,640,268,794,534,539,781,408,390,644,102,476,499,290,632,545,37,858,916,552,41,542,289,122,272,383,800,485,98,752,472,761,107,784,860,658,741,290,204,681,407,855,85,99,62,482,180,20,297,451,593,913,142,808,684,287,536,561,76,653,899,729,567,744,390,513,192,516,258,240,518,794,395,768,848,51,610,384,168,190,826,328,596,786,303,570,381,415,641,156,237,151,429,531,207,676,710,89,168,304,402,40,708,575,162,864,229,65,861,841,512,164,477,221,92,358,785,288,357,850,836,827,736,707,94,8,494,114,521,2,499,851,543,152,729,771,95,248,361,578,323,856,797,289,51,684,466,533,820,669,45,902,452,167,342,244,173,35,463,651,51,699,591,452,578,37,124,298,332,552,43,427,119,662,777,475,850,764,364,578,911,283,711,472,420,245,288,594,394,511,327,589,777,699,688,43,408,842,383,721,521,560,644,714,559,62,145,873,663,713,159,672,729,624,59,193,417,158,209,563,564,343,693,109,608,563,365,181,772,677,310,248,353,708,410,579,870,617,841,632,860,289,536,35,777,618,586,424,833,77,597,346,269,757,632,695,751,331,247,184,45,787,680,18,66,407,369,54,492,228,613,830,922,437,519,644,905,789,420,305,441,207,300,892,827,141,537,381,662,513,56,252,341,242,797,838,837,720,224,307,631,61,87,560,310,756,665,397,808,851,309,473,795,378,31,647,915,459,806,590,731,425,216,548,249,321,881,699,535,673,782,210,815,905,303,843,922,281,73,469,791,660,162,498,308,155,422,907,817,187,62,16,425,535,336,286,437,375,273,610,296,183,923,116,667,751,353,62,366,691,379,687,842,37,357,720,742,330,5,39,923,311,424,242,749,321,54,669,316,342,299,534,105,667,488,640,672,576,540,316,486,721,610,46,656,447,171,616,464,190,531,297,321,762,752,533,175,134,14,381,433,717,45,111,20,596,284,736,138,646,411,877,669,141,919,45,780,407,164,332,899,165,726,600,325,498,655,357,752,768,223,849,647,63,310,863,251,366,304,282,738,675,410,389,244,31,121,303,263]];function f(a,b){var c=e[b],f=Math.pow(2,b+1),g=(0,d.fillArray)(Array(f),0);return a.forEach(function(a){var b,d=a+g[f-1],e=void 0,h=void 0;for(b=f-1;b>0;--b)e=d*c[b]%929,h=929-e,g[b]=(g[b-1]+h)%929;e=d*c[0]%929,h=929-e,g[0]=h%929}),g.forEach(function(a,b){0!=a&&(g[b]=929-a)}),g.reverse()}},function(a,b,c){var d;!function(e){"use strict";var f,g=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=Math.ceil,i=Math.floor,j=" not a boolean or binary digit",k="rounding mode",l="number type has more than 15 significant digits",m="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_",n=1e14,o=14,p=9007199254740991,q=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],r=1e7,s=1e9;function t(a){var b,c,d=0,e=P.prototype,f=new P(1),D=20,E=4,F=-7,G=21,H=-1e7,I=1e7,J=!0,K=T,L=!1,M=1,N=0,O={decimalSeparator:".",groupSeparator:",",groupSize:3,secondaryGroupSize:0,fractionGroupSeparator:"\xa0",fractionGroupSize:0};function P(a,b){var e,f,h,j,k,n,q=this;if(!(q instanceof P))return new P(a,b);if(null!=b&&K(b,2,64,d,"base")){if(b=0|b,n=a+"",10==b)return q=new P(a instanceof P?a:n),W(q,D+q.e+1,E);if((j="number"==typeof a)&&0*a!=0||!RegExp("^-?"+(e="["+m.slice(0,b)+"]+")+"(?:\\."+e+")?$",b<37?"i":"").test(n))return c(q,n,j,b);j?(q.s=1/a<0?(n=n.slice(1),-1):1,J&&n.replace(/^0\.0*|\./,"").length>15&&V(d,l,a),j=!1):q.s=45===n.charCodeAt(0)?(n=n.slice(1),-1):1,n=Q(n,10,b,q.s)}else{if(a instanceof P)return q.s=a.s,q.e=a.e,q.c=(a=a.c)?a.slice():a,void(d=0);if((j="number"==typeof a)&&0*a==0){if(q.s=1/a<0?(a=-a,-1):1,a===~~a){for(f=0,h=a;h>=10;h/=10,f++);return q.e=f,q.c=[a],void(d=0)}n=a+""}else{if(!g.test(n=a+""))return c(q,n,j);q.s=45===n.charCodeAt(0)?(n=n.slice(1),-1):1}}for((f=n.indexOf("."))>-1&&(n=n.replace(".","")),(h=n.search(/e/i))>0?(f<0&&(f=h),f+=+n.slice(h+1),n=n.substring(0,h)):f<0&&(f=n.length),h=0;48===n.charCodeAt(h);h++);for(k=n.length;48===n.charCodeAt(--k););if(n=n.slice(h,k+1))if(k=n.length,j&&J&&k>15&&(a>p||a!==i(a))&&V(d,l,q.s*a),f=f-h-1,f>I)q.c=q.e=null;else if(f<H)q.c=[q.e=0];else{if(q.e=f,q.c=[],h=(f+1)%o,f<0&&(h+=o),h<k){for(h&&q.c.push(+n.slice(0,h)),k-=o;h<k;)q.c.push(+n.slice(h,h+=o));n=n.slice(h),h=o-n.length}else h-=k;for(;h--;n+="0");q.c.push(+n)}else q.c=[q.e=0];d=0}P.another=t,P.ROUND_UP=0,P.ROUND_DOWN=1,P.ROUND_CEIL=2,P.ROUND_FLOOR=3,P.ROUND_HALF_UP=4,P.ROUND_HALF_DOWN=5,P.ROUND_HALF_EVEN=6,P.ROUND_HALF_CEIL=7,P.ROUND_HALF_FLOOR=8,P.EUCLID=9,P.config=P.set=function(){var a,b,c=0,e={},f=arguments,g=f[0],h=g&&"object"==typeof g?function(){if(g.hasOwnProperty(b))return null!=(a=g[b])}:function(){if(f.length>c)return null!=(a=f[c++])};return h(b="DECIMAL_PLACES")&&K(a,0,s,2,b)&&(D=0|a),e[b]=D,h(b="ROUNDING_MODE")&&K(a,0,8,2,b)&&(E=0|a),e[b]=E,h(b="EXPONENTIAL_AT")&&(y(a)?K(a[0],-s,0,2,b)&&K(a[1],0,s,2,b)&&(F=0|a[0],G=0|a[1]):K(a,-s,s,2,b)&&(F=-(G=0|(a<0?-a:a)))),e[b]=[F,G],h(b="RANGE")&&(y(a)?K(a[0],-s,-1,2,b)&&K(a[1],1,s,2,b)&&(H=0|a[0],I=0|a[1]):K(a,-s,s,2,b)&&(0|a?H=-(I=0|(a<0?-a:a)):J&&V(2,b+" cannot be zero",a))),e[b]=[H,I],h(b="ERRORS")&&(a===!!a||1===a||0===a?(d=0,K=(J=!!a)?T:x):J&&V(2,b+j,a)),e[b]=J,h(b="CRYPTO")&&(a===!0||a===!1||1===a||0===a?a?(a="undefined"==typeof crypto,!a&&crypto&&(crypto.getRandomValues||crypto.randomBytes)?L=!0:J?V(2,"crypto unavailable",a?void 0:crypto):L=!1):L=!1:J&&V(2,b+j,a)),e[b]=L,h(b="MODULO_MODE")&&K(a,0,9,2,b)&&(M=0|a),e[b]=M,h(b="POW_PRECISION")&&K(a,0,s,2,b)&&(N=0|a),e[b]=N,h(b="FORMAT")&&("object"==typeof a?O=a:J&&V(2,b+" not an object",a)),e[b]=O,e},P.max=function(){return S(arguments,e.lt)},P.min=function(){return S(arguments,e.gt)},P.random=function(){var a=9007199254740992,b=Math.random()*a&2097151?function(){return i(Math.random()*a)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)};return function(a){var c,d,e,g,j,k=0,l=[],m=new P(f);if(a=null!=a&&K(a,0,s,14)?0|a:D,g=h(a/o),L)if(crypto.getRandomValues){for(c=crypto.getRandomValues(new Uint32Array(g*=2));k<g;)j=131072*c[k]+(c[k+1]>>>11),j>=9e15?(d=crypto.getRandomValues(new Uint32Array(2)),c[k]=d[0],c[k+1]=d[1]):(l.push(j%1e14),k+=2);k=g/2}else if(crypto.randomBytes){for(c=crypto.randomBytes(g*=7);k<g;)j=281474976710656*(31&c[k])+1099511627776*c[k+1]+4294967296*c[k+2]+16777216*c[k+3]+(c[k+4]<<16)+(c[k+5]<<8)+c[k+6],j>=9e15?crypto.randomBytes(7).copy(c,k):(l.push(j%1e14),k+=7);k=g/7}else L=!1,J&&V(14,"crypto unavailable",crypto);if(!L)for(;k<g;)j=b(),j<9e15&&(l[k++]=j%1e14);for(g=l[--k],a%=o,g&&a&&(j=q[o-a],l[k]=i(g/j)*j);0===l[k];l.pop(),k--);if(k<0)l=[e=0];else{for(e=-1;0===l[0];l.splice(0,1),e-=o);for(k=1,j=l[0];j>=10;j/=10,k++);k<o&&(e-=o-k)}return m.e=e,m.c=l,m}}();function Q(a,c,d,e){var f,g,h,i,j,k,l,n=a.indexOf("."),o=D,p=E;for(d<37&&(a=a.toLowerCase()),n>=0&&(h=N,N=0,a=a.replace(".",""),l=new P(d),j=l.pow(a.length-n),N=h,l.c=z(B(v(j.c),j.e),10,c),l.e=l.c.length),k=z(a,d,c),g=h=k.length;0==k[--h];k.pop());if(!k[0])return"0";if(n<0?--g:(j.c=k,j.e=g,j.s=e,j=b(j,l,o,p,c),k=j.c,i=j.r,g=j.e),f=g+o+1,n=k[f],h=c/2,i=i||f<0||null!=k[f+1],i=p<4?(null!=n||i)&&(0==p||p==(j.s<0?3:2)):n>h||n==h&&(4==p||i||6==p&&1&k[f-1]||p==(j.s<0?8:7)),f<1||!k[0])a=i?B("1",-o):"0";else{if(k.length=f,i)for(--c;++k[--f]>c;)k[f]=0,f||(++g,k=[1].concat(k));for(h=k.length;!k[--h];);for(n=0,a="";n<=h;a+=m.charAt(k[n++]));a=B(a,g)}return a}b=function(){function a(a,b,c){var d,e,f,g,h=0,i=a.length,j=b%r,k=b/r|0;for(a=a.slice();i--;)f=a[i]%r,g=a[i]/r|0,d=k*f+g*j,e=j*f+d%r*r+h,h=(e/c|0)+(d/r|0)+k*g,a[i]=e%c;return h&&(a=[h].concat(a)),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c,d){for(var e=0;c--;)a[c]-=e,e=a[c]<b[c]?1:0,a[c]=e*d+a[c]-b[c];for(;!a[0]&&a.length>1;a.splice(0,1));}return function(d,e,f,g,h){var j,k,l,m,p,q,r,s,t,v,w,x,y,z,A,B,C,D=d.s==e.s?1:-1,E=d.c,F=e.c;if(!(E&&E[0]&&F&&F[0]))return new P(d.s&&e.s&&(E?!F||E[0]!=F[0]:F)?E&&0==E[0]||!F?0*D:D/0:NaN);for(s=new P(D),t=s.c=[],k=d.e-e.e,D=f+k+1,h||(h=n,k=u(d.e/o)-u(e.e/o),D=D/o|0),l=0;F[l]==(E[l]||0);l++);if(F[l]>(E[l]||0)&&k--,D<0)t.push(1),m=!0;else{for(z=E.length,B=F.length,l=0,D+=2,p=i(h/(F[0]+1)),p>1&&(F=a(F,p,h),E=a(E,p,h),B=F.length,z=E.length),y=B,v=E.slice(0,B),w=v.length;w<B;v[w++]=0);C=F.slice(),C=[0].concat(C),A=F[0],F[1]>=h/2&&A++;do{if(p=0,j=b(F,v,B,w),j<0){if(x=v[0],B!=w&&(x=x*h+(v[1]||0)),p=i(x/A),p>1)for(p>=h&&(p=h-1),q=a(F,p,h),r=q.length,w=v.length;1==b(q,v,r,w);)p--,c(q,B<r?C:F,r,h),r=q.length,j=1;else 0==p&&(j=p=1),q=F.slice(),r=q.length;if(r<w&&(q=[0].concat(q)),c(v,q,w,h),w=v.length,j==-1)for(;b(F,v,B,w)<1;)p++,c(v,B<w?C:F,w,h),w=v.length}else 0===j&&(p++,v=[0]);t[l++]=p,v[0]?v[w++]=E[y]||0:(v=[E[y]],w=1)}while((y++<z||null!=v[0])&&D--);m=null!=v[0],t[0]||t.splice(0,1)}if(h==n){for(l=1,D=t[0];D>=10;D/=10,l++);W(s,f+(s.e=l+k*o-1)+1,g,m)}else s.e=k,s.r=+m;return s}}();function R(a,b,c,d){var e,f,g,h,i;if(c=null!=c&&K(c,0,8,d,k)?0|c:E,!a.c)return""+a;if(e=a.c[0],g=a.e,null==b)i=v(a.c),i=19==d||24==d&&g<=F?A(i,g):B(i,g);else if(a=W(new P(a),b,c),f=a.e,i=v(a.c),h=i.length,19==d||24==d&&(b<=f||f<=F)){for(;h<b;i+="0",h++);i=A(i,f)}else if(b-=g,i=B(i,f),f+1>h){if(--b>0)for(i+=".";b--;i+="0");}else if(b+=f-h,b>0)for(f+1==h&&(i+=".");b--;i+="0");return a.s<0&&e?"-"+i:i}function S(a,b){var c,d,e=0;for(y(a[0])&&(a=a[0]),c=new P(a[0]);++e<a.length;){if(d=new P(a[e]),!d.s){c=d;break}b.call(c,d)&&(c=d)}return c}function T(a,b,c,d,e){return(a<b||a>c||a!=C(a))&&V(d,(e||"decimal places")+(a<b||a>c?" out of range":" not an integer"),a),!0}function U(a,b,c){for(var d=1,e=b.length;!b[--e];b.pop());for(e=b[0];e>=10;e/=10,d++);return(c=d+c*o-1)>I?a.c=a.e=null:c<H?a.c=[a.e=0]:(a.e=c,a.c=b),a}c=function(){var a=/^(-?)0([xbo])(?=\w[\w.]*$)/i,b=/^([^.]+)\.$/,c=/^\.([^.]+)$/,e=/^-?(Infinity|NaN)$/,f=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(g,h,i,j){var k,l=i?h:h.replace(f,"");if(e.test(l))g.s=isNaN(l)?null:l<0?-1:1;else{if(!i&&(l=l.replace(a,function(a,b,c){return k="x"==(c=c.toLowerCase())?16:"b"==c?2:8,j&&j!=k?a:b}),j&&(k=j,l=l.replace(b,"$1").replace(c,"0.$1")),h!=l))return new P(l,k);J&&V(d,"not a"+(j?" base "+j:"")+" number",h),g.s=null}g.c=g.e=null,d=0}}();function V(a,b,c){var e=Error(["new BigNumber","cmp","config","div","divToInt","eq","gt","gte","lt","lte","minus","mod","plus","precision","random","round","shift","times","toDigits","toExponential","toFixed","toFormat","toFraction","pow","toPrecision","toString","BigNumber"][a]+"() "+b+": "+c);throw e.name="BigNumber Error",d=0,e}function W(a,b,c,d){var e,f,g,j,k,l,m,p=a.c,r=q;if(p){a:{for(e=1,j=p[0];j>=10;j/=10,e++);if(f=b-e,f<0)f+=o,g=b,k=p[l=0],m=k/r[e-g-1]%10|0;else if(l=h((f+1)/o),l>=p.length){if(!d)break a;for(;p.length<=l;p.push(0));k=m=0,e=1,f%=o,g=f-o+1}else{for(k=j=p[l],e=1;j>=10;j/=10,e++);f%=o,g=f-o+e,m=g<0?0:k/r[e-g-1]%10|0}if(d=d||b<0||null!=p[l+1]||(g<0?k:k%r[e-g-1]),d=c<4?(m||d)&&(0==c||c==(a.s<0?3:2)):m>5||5==m&&(4==c||d||6==c&&(f>0?g>0?k/r[e-g]:0:p[l-1])%10&1||c==(a.s<0?8:7)),b<1||!p[0])return p.length=0,d?(b-=a.e+1,p[0]=r[(o-b%o)%o],a.e=-b||0):p[0]=a.e=0,a;if(0==f?(p.length=l,j=1,l--):(p.length=l+1,j=r[o-f],p[l]=g>0?i(k/r[e-g]%r[g])*j:0),d)for(;;){if(0==l){for(f=1,g=p[0];g>=10;g/=10,f++);for(g=p[0]+=j,j=1;g>=10;g/=10,j++);f!=j&&(a.e++,p[0]==n&&(p[0]=1));break}if(p[l]+=j,p[l]!=n)break;p[l--]=0,j=1}for(f=p.length;0===p[--f];p.pop());}a.e>I?a.c=a.e=null:a.e<H&&(a.c=[a.e=0])}return a}return e.absoluteValue=e.abs=function(){var a=new P(this);return a.s<0&&(a.s=1),a},e.ceil=function(){return W(new P(this),this.e+1,2)},e.comparedTo=e.cmp=function(a,b){return d=1,w(this,new P(a,b))},e.decimalPlaces=e.dp=function(){var a,b,c=this.c;if(!c)return null;if(a=((b=c.length-1)-u(this.e/o))*o,b=c[b])for(;b%10==0;b/=10,a--);return a<0&&(a=0),a},e.dividedBy=e.div=function(a,c){return d=3,b(this,new P(a,c),D,E)},e.dividedToIntegerBy=e.divToInt=function(a,c){return d=4,b(this,new P(a,c),0,1)},e.equals=e.eq=function(a,b){return d=5,0===w(this,new P(a,b))},e.floor=function(){return W(new P(this),this.e+1,3)},e.greaterThan=e.gt=function(a,b){return d=6,w(this,new P(a,b))>0},e.greaterThanOrEqualTo=e.gte=function(a,b){return d=7,1===(b=w(this,new P(a,b)))||0===b},e.isFinite=function(){return!!this.c},e.isInteger=e.isInt=function(){return!!this.c&&u(this.e/o)>this.c.length-2},e.isNaN=function(){return!this.s},e.isNegative=e.isNeg=function(){return this.s<0},e.isZero=function(){return!!this.c&&0==this.c[0]},e.lessThan=e.lt=function(a,b){return d=8,w(this,new P(a,b))<0},e.lessThanOrEqualTo=e.lte=function(a,b){return d=9,(b=w(this,new P(a,b)))===-1||0===b},e.minus=e.sub=function(a,b){var c,e,f,g,h,i,j,k,l=this,m=l.s;if(d=10,a=new P(a,b),b=a.s,!m||!b)return new P(NaN);if(m!=b)return a.s=-b,l.plus(a);if(h=l.e/o,i=a.e/o,j=l.c,k=a.c,!h||!i){if(!j||!k)return j?(a.s=-b,a):new P(k?l:NaN);if(!j[0]||!k[0])return k[0]?(a.s=-b,a):new P(j[0]?l:3==E?-0:0)}if(h=u(h),i=u(i),j=j.slice(),m=h-i){for((g=m<0)?(m=-m,f=j):(i=h,f=k),f.reverse(),b=m;b--;f.push(0));f.reverse()}else for(e=(g=(m=j.length)<(b=k.length))?m:b,m=b=0;b<e;b++)if(j[b]!=k[b]){g=j[b]<k[b];break}if(g&&(f=j,j=k,k=f,a.s=-a.s),b=(e=k.length)-(c=j.length),b>0)for(;b--;j[c++]=0);for(b=n-1;e>m;){if(j[--e]<k[e]){for(c=e;c&&!j[--c];j[c]=b);--j[c],j[e]+=n}j[e]-=k[e]}for(;0==j[0];j.splice(0,1),--i);return j[0]?U(a,j,i):(a.s=3==E?-1:1,a.c=[a.e=0],a)},e.modulo=e.mod=function(a,c){var e,f,g=this;return d=11,a=new P(a,c),!g.c||!a.s||a.c&&!a.c[0]?new P(NaN):!a.c||g.c&&!g.c[0]?new P(g):(9==M?(f=a.s,a.s=1,e=b(g,a,0,3),a.s=f,e.s*=f):e=b(g,a,0,M),g.minus(e.times(a)))},e.negated=e.neg=function(){var a=new P(this);return a.s=-a.s||null,a},e.plus=e.add=function(a,b){var c,e,f,g,h,i=this,j=i.s;if(d=12,a=new P(a,b),b=a.s,!j||!b)return new P(NaN);if(j!=b)return a.s=-b,i.minus(a);if(e=i.e/o,f=a.e/o,g=i.c,h=a.c,!e||!f){if(!g||!h)return new P(j/0);if(!g[0]||!h[0])return h[0]?a:new P(g[0]?i:0*j)}if(e=u(e),f=u(f),g=g.slice(),j=e-f){for(j>0?(f=e,c=h):(j=-j,c=g),c.reverse();j--;c.push(0));c.reverse()}for(j=g.length,b=h.length,j-b<0&&(c=h,h=g,g=c,b=j),j=0;b;)j=(g[--b]=g[b]+h[b]+j)/n|0,g[b]=n===g[b]?0:g[b]%n;return j&&(g=[j].concat(g),++f),U(a,g,f)},e.precision=e.sd=function(a){var b,c,d=this,e=d.c;if(null!=a&&a!==!!a&&1!==a&&0!==a&&(J&&V(13,"argument"+j,a),a!=!!a&&(a=null)),!e)return null;if(c=e.length-1,b=c*o+1,c=e[c]){for(;c%10==0;c/=10,b--);for(c=e[0];c>=10;c/=10,b++);}return a&&d.e+1>b&&(b=d.e+1),b},e.round=function(a,b){var c=new P(this);return(null==a||K(a,0,s,15))&&W(c,~~a+this.e+1,null!=b&&K(b,0,8,15,k)?0|b:E),c},e.shift=function(a){var b=this;return K(a,-p,p,16,"argument")?b.times("1e"+C(a)):new P(b.c&&b.c[0]&&(a<-p||a>p)?b.s*(a<0?0:1/0):b)},e.squareRoot=e.sqrt=function(){var a,c,d,e,f,g=this,h=g.c,i=g.s,j=g.e,k=D+4,l=new P("0.5");if(1!==i||!h||!h[0])return new P(!i||i<0&&(!h||h[0])?NaN:h?g:1/0);if(i=Math.sqrt(+g),0==i||i==1/0?(c=v(h),(c.length+j)%2==0&&(c+="0"),i=Math.sqrt(c),j=u((j+1)/2)-(j<0||j%2),i==1/0?c="1e"+j:(c=i.toExponential(),c=c.slice(0,c.indexOf("e")+1)+j),d=new P(c)):d=new P(i+""),d.c[0])for(j=d.e,i=j+k,i<3&&(i=0);;)if(f=d,d=l.times(f.plus(b(g,f,k,1))),v(f.c).slice(0,i)===(c=v(d.c)).slice(0,i)){if(d.e<j&&--i,c=c.slice(i-3,i+1),"9999"!=c&&(e||"4999"!=c)){+c&&(+c.slice(1)||"5"!=c.charAt(0))||(W(d,d.e+D+2,1),a=!d.times(d).eq(g));break}if(!e&&(W(f,f.e+D+2,0),f.times(f).eq(g))){d=f;break}k+=4,i+=4,e=1}return W(d,d.e+D+1,E,a)},e.times=e.mul=function(a,b){var c,e,f,g,h,i,j,k,l,m,p,q,s,t,v,w=this,x=w.c,y=(d=17,a=new P(a,b)).c;if(!(x&&y&&x[0]&&y[0]))return!w.s||!a.s||x&&!x[0]&&!y||y&&!y[0]&&!x?a.c=a.e=a.s=null:(a.s*=w.s,x&&y?(a.c=[0],a.e=0):a.c=a.e=null),a;for(e=u(w.e/o)+u(a.e/o),a.s*=w.s,j=x.length,m=y.length,j<m&&(s=x,x=y,y=s,f=j,j=m,m=f),f=j+m,s=[];f--;s.push(0));for(t=n,v=r,f=m;--f>=0;){for(c=0,p=y[f]%v,q=y[f]/v|0,h=j,g=f+h;g>f;)k=x[--h]%v,l=x[h]/v|0,i=q*k+l*p,k=p*k+i%v*v+s[g]+c,c=(k/t|0)+(i/v|0)+q*l,s[g--]=k%t;s[g]=c}return c?++e:s.splice(0,1),U(a,s,e)},e.toDigits=function(a,b){var c=new P(this);return a=null!=a&&K(a,1,s,18,"precision")?0|a:null,b=null!=b&&K(b,0,8,18,k)?0|b:E,a?W(c,a,b):c},e.toExponential=function(a,b){return R(this,null!=a&&K(a,0,s,19)?~~a+1:null,b,19)},e.toFixed=function(a,b){return R(this,null!=a&&K(a,0,s,20)?~~a+this.e+1:null,b,20)},e.toFormat=function(a,b){var c,d,e,f,g,h,i,j,k,l,m=R(this,null!=a&&K(a,0,s,21)?~~a+this.e+1:null,b,21);if(this.c){if(d=m.split("."),e=+O.groupSize,f=+O.secondaryGroupSize,g=O.groupSeparator,h=d[0],i=d[1],j=this.s<0,k=j?h.slice(1):h,l=k.length,f&&(c=e,e=f,f=c,l-=c),e>0&&l>0){for(c=l%e||e,h=k.substr(0,c);c<l;c+=e)h+=g+k.substr(c,e);f>0&&(h+=g+k.slice(c)),j&&(h="-"+h)}m=i?h+O.decimalSeparator+((f=+O.fractionGroupSize)?i.replace(RegExp("\\d{"+f+"}\\B","g"),"$&"+O.fractionGroupSeparator):i):h}return m},e.toFraction=function(a){var c,d,e,g,h,i,j,k,l,m=J,n=this,p=n.c,r=new P(f),s=d=new P(f),t=j=new P(f);if(null!=a&&(J=!1,i=new P(a),J=m,(m=i.isInt())&&!i.lt(f)||(J&&V(22,"max denominator "+(m?"out of range":"not an integer"),a),a=!m&&i.c&&W(i,i.e+1,1).gte(f)?i:null)),!p)return""+n;for(l=v(p),g=r.e=l.length-n.e-1,r.c[0]=q[(h=g%o)<0?o+h:h],a=!a||i.cmp(r)>0?g>0?r:s:i,h=I,I=1/0,i=new P(l),j.c[0]=0;k=b(i,r,0,1),e=d.plus(k.times(t)),1!=e.cmp(a);)d=t,t=e,s=j.plus(k.times(e=s)),j=e,r=i.minus(k.times(e=r)),i=e;return e=b(a.minus(d),t,0,1),j=j.plus(e.times(s)),d=d.plus(e.times(t)),j.s=s.s=n.s,g*=2,c=b(s,t,g,E).minus(n).abs().cmp(b(j,d,g,E).minus(n).abs())<1?[""+s,""+t]:[""+j,""+d],I=h,c},e.toNumber=function(){return+this},e.toPower=e.pow=function(a,b){var c,e,g,j=i(a<0?-a:+a),k=this;if(null!=b&&(d=23,b=new P(b)),!K(a,-p,p,23,"exponent")&&(!isFinite(a)||j>p&&(a/=0)||parseFloat(a)!=a&&!(a=NaN))||0==a)return c=Math.pow(+k,a),new P(b?c%b:c);for(b?a>1&&k.gt(f)&&k.isInt()&&b.gt(f)&&b.isInt()?k=k.mod(b):(g=b,b=null):N&&(c=h(N/o+2)),e=new P(f);;){if(j%2){if(e=e.times(k),!e.c)break;c?e.c.length>c&&(e.c.length=c):b&&(e=e.mod(b))}if(j=i(j/2),!j)break;k=k.times(k),c?k.c&&k.c.length>c&&(k.c.length=c):b&&(k=k.mod(b))}return b?e:(a<0&&(e=f.div(e)),g?e.mod(g):c?W(e,N,E):e)},e.toPrecision=function(a,b){return R(this,null!=a&&K(a,1,s,24,"precision")?0|a:null,b,24)},e.toString=function(a){var b,c=this,d=c.s,e=c.e;return null===e?d?(b="Infinity",d<0&&(b="-"+b)):b="NaN":(b=v(c.c),b=null!=a&&K(a,2,64,25,"base")?Q(B(b,e),0|a,10,d):e<=F||e>=G?A(b,e):B(b,e),d<0&&c.c[0]&&(b="-"+b)),b},e.truncated=e.trunc=function(){return W(new P(this),this.e+1,1)},e.valueOf=e.toJSON=function(){var a,b=this,c=b.e;return null===c?""+b:(a=v(b.c),a=c<=F||c>=G?A(a,c):B(a,c),b.s<0?"-"+a:a)},e.isBigNumber=!0,null!=a&&P.config(a),P}function u(a){var b=0|a;return a>0||a===b?b:b-1}function v(a){for(var b,c,d=1,e=a.length,f=a[0]+"";d<e;){for(b=a[d++]+"",c=o-b.length;c--;b="0"+b);f+=b}for(e=f.length;48===f.charCodeAt(--e););return f.slice(0,e+1||1)}function w(a,b){var c,d,e=a.c,f=b.c,g=a.s,h=b.s,i=a.e,j=b.e;if(!g||!h)return null;if(c=e&&!e[0],d=f&&!f[0],c||d)return c?d?0:-h:g;if(g!=h)return g;if(c=g<0,d=i==j,!e||!f)return d?0:!e^c?1:-1;if(!d)return i>j^c?1:-1;for(h=(i=e.length)<(j=f.length)?i:j,g=0;g<h;g++)if(e[g]!=f[g])return e[g]>f[g]^c?1:-1;return i==j?0:i>j^c?1:-1}function x(a,b,c){return(a=C(a))>=b&&a<=c}function y(a){return"[object Array]"==Object.prototype.toString.call(a)}function z(a,b,c){for(var d,e=[0],f,g=0,h=a.length;g<h;){for(f=e.length;f--;e[f]*=b);for(e[d=0]+=m.indexOf(a.charAt(g++));d<e.length;d++)e[d]>c-1&&(null==e[d+1]&&(e[d+1]=0),e[d+1]+=e[d]/c|0,e[d]%=c)}return e.reverse()}function A(a,b){return(a.length>1?a.charAt(0)+"."+a.slice(1):a)+(b<0?"e":"e+")+b}function B(a,b){var c,d;if(b<0){for(d="0.";++b;d+="0");a=d+a}else if(c=a.length,++b>c){for(d="0",b-=c;--b;d+="0");a+=d}else b<c&&(a=a.slice(0,b)+"."+a.slice(b));return a}function C(a){return a=parseFloat(a),a<0?h(a):i(a)}f=t(),f.default=f.BigNumber=f,d=function(){return f}.call(b,c,b,a),!(void 0!==d&&(a.exports=d))}(this)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(7),f=r(e),g=c(2),h=r(g),i=c(3),j=r(i),k=c(54),l=r(k),m=c(56),n=r(m),o=c(1),p=c(0);function r(a){return a&&a.__esModule?a:{default:a}}function s(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function t(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function u(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}q=function(a){u(b,a);function b(a){var c,d,e;return s(this,b),c=t(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,a,j.default.defaultConfig)),d=void 0,d="2"==c.config.model?new n.default(c.config):new l.default(c.config),e=d.getMatrix(),c.adjustDesiredSize(e),c.convertToShape(e),c}return d(b,[{key:"validate",value:function a(){var b=this.config,c=b.version,d=b.model,e=b.charset,f=b.text,g=b.charCode,h=b.connectionNo;if(!(f||g&&0!==g.length))throw new o.InvalidTextException(f);if("1"!=d&&"2"!=d)throw new o.BadArgumentsException({model:d});if("UTF-8"!=e&&"Shift_JIS"!=e)throw new o.BadArgumentsException({charset:e});if("1"==d&&(0,p.isNumberLike)(c)&&(c<1||c>14))throw new o.BadArgumentsException({version:c},"Model 1 only support version 1 - 14.");if("2"==d&&(0,p.isNumberLike)(c)&&(c<1||c>40))throw new o.BadArgumentsException({version:c},"Model 2 only support version 1 - 40.");if(h>15||h<0)throw new o.BadArgumentsException({connectionNo:h},"ConnectionNo is in range 0 - 15.")}}]),b}(f.default),b.default=q,h.default.registerEncoder("QRCode",q)},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(0),f=c(3),g=u(f),h=c(14),i=u(h),j=c(15),k=u(j),l=c(16),m=u(l),n=c(17),o=u(n),p=c(18),q=u(p),r=c(19),s=u(r);function u(a){return a&&a.__esModule?a:{default:a}}function v(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function w(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function x(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}t=function(a){x(b,a);function b(){return v(this,b),w(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}return d(b,[{key:"encodeData",value:function a(b,c){var d=this.charCountIndicatorBitsNumber,e=this.charCode,f=new s.default;return f.put(0,4),c&&(f.put(g.default.MODE_INDICATOR.StructuredAppend,4),f.put(c.connectionNo,4),f.put(c.connectionCnt-1,4),f.put(g.default.getParityData(e),8)),b.forEach(function(a){if(a.code){var b=void 0;switch(a.mode){case"Numeric":b=new m.default(a.code);break;case"Alphanumeric":b=new o.default(a.code);break;case"8BitByte":b=new k.default(a.code);break;case"Kanji":b=new q.default(a.code)}f.put(b.getMode(),4),f.put(b.getLength(),d[b.mode]),b.write(f)}}),f}},{key:"getFinalMessage",value:function a(b){var c=[],d=b.map(function(a){return a.data}),e=b.map(function(a){return a.ec});return d.forEach(function(a){return c=c.concat(a)}),e.forEach(function(a){return c=c.concat(a)}),c}},{key:"setModules",value:function a(b){var c=this.modulesCount;this.modules=g.default.createModules(c),this.addPositionDetectionPattern(),this.addExtensionPattern(),this.addTimingPattern(),this.maskModules(b)}},{key:"maskModules",value:function a(b){var c,d,e=this.modules,f=this.errorCorrectionLevel,h=this.model,i=this.config.mask;"auto"===i?this.autoMask(b):(c=g.default.getMaskFunc(i),this.maskPattern=i,d=g.default.addFormatInformation(e,i,f,h),this.modules=this.fillDataModules(d,b,c))}},{key:"autoMask",value:function a(b){var c=this,d=this.modules,e=this.errorCorrectionLevel,f=this.model,h=void 0,i=void 0,j=void 0;g.default.maskFuncs.forEach(function(a,k){var l=g.default.addFormatInformation(d,k,e,f),m=c.fillDataModules(l,b,a),n=g.default.getMaskScore(m);(!i||n<i)&&(h=m,i=n,j=k)}),this.modules=h,this.maskPattern=j}},{key:"addExtensionPattern",value:function a(){var b,c,d,f=this.modules,g=this.version,h=f.length;if(f[h-1][h-1]=1,f[h-2][h-1]=0,f[h-1][h-2]=0,f[h-2][h-2]=0,b=Math.floor(g/2),(0,e.isEven)(g))for(c=0;c<b;c++)this.addBaseExtension(13+8*c),this.addRightExtension(13+8*c);else for(d=0;d<b;d++)this.addBaseExtension(17+8*d),this.addRightExtension(17+8*d)}},{key:"addBaseExtension",value:function a(b){var c=this.modules,d=c.length,e=d-2,f=d-1;c[e][b]=0,c[e][b+1]=0,c[e][b+2]=0,c[e][b+3]=0,c[f][b]=1,c[f][b+1]=1,c[f][b+2]=1,c[f][b+3]=1}},{key:"addRightExtension",value:function a(b){var c=this.modules,d=c.length,e=d-2,f=d-1;c[b][e]=0,c[b+1][e]=0,c[b+2][e]=0,c[b+3][e]=0,c[b][f]=1,c[b+1][f]=1,c[b+2][f]=1,c[b+3][f]=1}},{key:"fillDataModules",value:function a(b,c,d){var e,f,g,h,i=b.length,j=void 0,k=void 0;for(j=k=i-1,e=2,f=new s.default(c),f.next(),f.next(),f.next(),f.next();j>=0;){if(j==i-5)e=4;else if(8==j)e=2;else if(6==j){j--;continue}for(;k>=0;)if(null===b[k][j]){for(g=0;g<e;g++)h=f.next(),d(k,j-g)&&(h=!h),b[k][j-g]=+h;k--}else k--;j-=e,k=i-1}return b}},{key:"getMatrix",value:function a(){var b,c,d=this.charCode,e=this.analysisData(d),f=this.encodeData(e),g=this.processConnection(f);return this.padBuffer(g),b=this.generateErrorCorrectionCode(g),c=this.getFinalMessage(b),this.setModules(c),this.modules}}]),b}(i.default),b.default=t},function(a,b,c){"use strict";var d,e,f,g,h,i;for(Object.defineProperty(b,"__esModule",{value:!0}),b.default=j,d=Array(256),e=0;e<8;e++)d[e]=1<<e;for(f=8;f<256;f++)d[f]=d[f-4]^d[f-5]^d[f-6]^d[f-8];for(g=Array(256),h=0;h<255;h+=1)g[d[h]]=h;i=[null,null,null,null,null,null,null,[0,87,229,146,149,238,102,21],null,null,[0,251,67,46,61,118,70,64,94,32,45],null,null,[0,74,152,176,100,86,100,106,104,130,218,206,140,78],null,[0,8,183,61,91,202,37,51,58,58,237,140,124,5,99,105],[0,120,104,107,109,102,161,76,3,91,191,147,169,182,194,225,120],[0,43,139,206,78,43,239,123,206,214,147,24,99,150,39,243,163,136],[0,215,234,158,94,184,97,118,170,79,187,152,148,252,179,5,98,96,153],null,[0,17,60,79,50,61,163,26,187,202,180,221,225,83,239,156,164,212,212,188,190],null,[0,210,171,247,242,93,230,14,109,221,53,200,74,8,172,98,80,219,134,160,105,165,231],null,[0,229,121,135,48,211,117,251,126,159,180,169,152,192,226,228,218,111,0,117,232,87,96,227,21],null,[0,173,125,158,2,103,182,118,17,145,201,111,28,165,53,161,21,245,142,13,102,48,227,153,145,218,70],null,[0,168,223,200,104,224,234,108,180,110,190,195,147,205,27,232,201,21,43,245,87,42,195,212,119,242,37,9,123],null,[0,41,173,145,152,216,31,179,182,50,48,110,86,239,96,222,125,42,173,226,193,224,130,156,37,251,216,238,40,192,180],null,[0,10,6,106,190,249,167,4,67,209,138,138,32,242,123,89,27,120,185,80,156,38,69,171,60,28,222,80,52,254,185,220,241],null,[0,111,77,146,94,26,21,108,19,105,94,113,193,86,140,163,125,58,158,229,239,218,103,56,70,114,61,183,129,167,13,98,62,129,51],null,[0,200,183,98,16,172,31,246,234,60,152,115,0,167,152,113,248,238,107,18,63,218,37,87,210,105,177,120,74,121,196,117,251,113,233,30,120],null,null,null,[0,59,116,79,161,252,98,128,205,128,161,247,57,163,56,235,106,53,26,187,174,226,104,170,7,175,35,181,114,88,41,47,163,125,134,72,20,232,53,35,15],null,[0,250,103,221,230,25,18,137,231,0,3,58,242,221,191,110,84,230,8,188,106,96,147,15,131,139,34,101,223,39,101,213,199,237,254,201,123,171,162,194,117,50,96],null,[0,190,7,61,121,71,246,69,55,168,188,89,243,191,25,72,123,9,145,14,247,1,238,44,78,143,62,224,126,118,114,68,163,52,194,217,147,204,169,37,130,113,102,73,181],null,[0,112,94,88,112,253,224,202,115,187,99,89,5,54,113,129,44,58,16,135,216,169,211,36,1,4,96,60,241,73,104,234,8,249,245,119,174,52,25,157,224,43,202,223,19,82,15],null,[0,228,25,196,130,211,146,60,24,251,90,39,102,240,61,178,63,46,123,115,18,221,111,135,160,182,205,107,206,95,150,120,184,91,21,247,156,140,238,191,11,94,227,84,50,163,39,34,108],null,[0,232,125,157,161,164,9,118,46,209,99,203,193,35,3,209,111,195,242,203,225,46,13,32,160,126,209,130,160,242,215,242,75,77,42,189,32,113,65,124,69,228,114,235,175,124,170,215,232,133,205],null,[0,116,50,86,186,50,220,251,89,192,46,86,127,124,19,184,233,151,215,22,14,59,145,37,242,203,134,254,89,190,94,59,65,124,113,100,233,235,121,22,76,86,97,39,242,200,220,101,33,239,254,116,51],null,[0,183,26,201,87,210,221,113,21,46,65,45,50,238,184,249,225,102,58,209,218,109,165,26,95,184,192,52,245,35,254,238,175,172,79,123,25,122,43,120,108,215,80,128,201,235,8,153,59,101,31,198,76,31,156],null,[0,106,120,107,157,164,216,112,116,2,91,248,163,36,201,202,229,6,144,254,155,135,208,170,209,12,139,127,142,182,249,177,174,190,28,10,85,239,184,101,124,152,206,96,23,163,61,27,196,247,151,154,202,207,20,61,10],null,[0,82,116,26,247,66,27,62,107,252,182,200,185,235,55,251,242,210,144,154,237,176,141,192,248,152,249,206,85,253,142,65,165,125,23,24,30,122,240,214,6,129,218,29,145,127,134,206,245,117,29,41,63,159,142,233,125,148,123],null,[0,107,140,26,12,9,141,243,197,226,197,219,45,211,101,219,120,28,181,127,6,100,247,2,205,198,57,115,219,101,109,160,82,37,38,238,49,160,209,121,86,11,124,30,181,84,25,194,87,65,102,190,220,70,27,209,16,89,7,33,240],null,[0,65,202,113,98,71,223,248,118,214,94,1,122,37,23,2,228,58,121,7,105,135,78,243,118,70,76,223,89,72,50,70,111,194,17,212,126,181,35,221,117,235,11,229,149,147,123,213,40,115,6,200,100,26,246,182,218,127,215,36,186,110,106],null,[0,45,51,175,9,7,158,159,49,68,119,92,123,177,204,187,254,200,78,141,149,119,26,127,53,160,93,199,212,29,24,145,156,208,150,218,209,4,216,91,47,184,146,47,140,195,195,125,242,238,63,99,108,140,230,242,31,204,11,178,243,217,156,213,231],null,[0,5,118,222,180,136,136,162,51,46,117,13,215,81,17,139,247,197,171,95,173,65,137,178,68,111,95,101,41,72,214,169,197,95,7,44,154,77,111,236,40,121,143,63,87,80,253,240,126,217,77,34,232,106,50,168,82,76,146,67,106,171,25,132,93,45,105],null,[0,247,159,223,33,224,93,77,70,90,160,32,254,43,150,84,101,190,205,133,52,60,202,165,220,203,151,93,84,15,84,253,173,160,89,227,52,199,97,95,231,52,177,41,125,137,241,166,225,118,2,54,32,82,215,175,198,43,238,235,27,101,184,127,3,5,8,163,238],null];
function j(a,b,c){var e,f,h,j,k,l,m=[],n=i[b],o=a.slice(0);for(e=0;e<c;e++){if(0!=o[0]){for(f=g[o[0]],h=0;h<=n.length-1;h++){for(j=n[h]+f;j>=255;)j-=255;m[h]=d[j]}for(k=0;k<=n.length-1;k++)o[k]=o[k]^m[k]}for(l=1;l<o.length;l++)o[l-1]=o[l];o[o.length-1]=0}return o.slice(0,b)}},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v;Object.defineProperty(b,"__esModule",{value:!0}),d=function(){function a(a,b){var c,d;for(c=0;c<b.length;c++)d=b[c],d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=c(0),f=c(3),g=w(f),h=c(13),i=w(h),j=c(14),k=w(j),l=c(15),m=w(l),n=c(16),o=w(n),p=c(17),q=w(p),r=c(18),s=w(r),t=c(19),u=w(t);function w(a){return a&&a.__esModule?a:{default:a}}function x(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function y(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function z(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}v=function(a){z(b,a);function b(){return x(this,b),y(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}return d(b,[{key:"encodeData",value:function a(b,c){var d=this.charCountIndicatorBitsNumber,e=this.charCode,f=new u.default;return c&&(f.put(g.default.MODE_INDICATOR.StructuredAppend,4),f.put(c.connectionNo,4),f.put(c.connectionCnt-1,4),f.put(g.default.getParityData(e),8)),b.forEach(function(a){if(a.code){var b=void 0;switch(a.mode){case"Numeric":b=new o.default(a.code);break;case"Alphanumeric":b=new q.default(a.code);break;case"8BitByte":b=new m.default(a.code);break;case"Kanji":b=new s.default(a.code)}f.put(b.getMode(),4),f.put(b.getLength(),d[b.mode]),b.write(f)}}),f}},{key:"getFinalMessage",value:function a(b){var c,d,f=[],g=b.map(function(a){return a.data}),h=b.map(function(a){return a.ec}),i=(0,e.getMaxValue)(g),j=(0,e.getMaxValue)(h),k=function a(b){g.forEach(function(a){(0,e.isDefined)(a[b])&&f.push(a[b])})};for(c=0;c<i;c++)k(c);for(d=function a(b){h.forEach(function(a){(0,e.isDefined)(a[b])&&f.push(a[b])})},c=0;c<j;c++)d(c);return f}},{key:"setModules",value:function a(b){var c=this.modulesCount,d=this.version;this.modules=g.default.createModules(c),this.addPositionDetectionPattern(),this.addAlignmentPattern(),this.addTimingPattern(),d>6&&this.addVersionInformation(),this.maskModules(b)}},{key:"maskModules",value:function a(b){var c,d,e=this.modules,f=this.errorCorrectionLevel,h=this.model,i=this.config.mask;"auto"===i?this.autoMask(b):(c=g.default.getMaskFunc(i),this.maskPattern=i,d=g.default.addFormatInformation(e,i,f,h),this.modules=this.fillDataModules(d,b,c))}},{key:"autoMask",value:function a(b){var c=this,d=this.modules,e=this.errorCorrectionLevel,f=this.model,h=void 0,i=void 0,j=void 0;g.default.maskFuncs.forEach(function(a,k){var l=g.default.addFormatInformation(d,k,e,f),m=c.fillDataModules(l,b,a),n=g.default.getMaskScore(m);(!i||n<i)&&(h=m,i=n,j=k)}),this.modules=h,this.maskPattern=j}},{key:"addAlignmentPattern",value:function a(){var b=this,c=this.modules,d=this.version,e=g.default.getAlignmentPattersPos(d);e.forEach(function(a){e.forEach(function(d){null===c[a][d]&&b.addPattern(d-2,a-2,5)})})}},{key:"addVersionInformation",value:function a(){var b,c,d=this.modulesCount,e=this.modules,f=this.version,g=i.default.getBCH18(f);for(b=0;b<18;b++)c=g>>b&1,e[Math.floor(b/3)][b%3+d-8-3]=c,e[b%3+d-8-3][Math.floor(b/3)]=c}},{key:"fillDataModules",value:function a(b,c,d){var e,f,g,h=b.length,i=-1,j=h-1,k=7,l=0;for(e=h-1;e>0;e-=2)for(6==e&&(e-=1);;){for(f=0;f<2;f+=1)null==b[j][e-f]&&(g=!1,l<c.length&&(g=1==(c[l]>>>k&1)),d(j,e-f)&&(g=!g),b[j][e-f]=+g,k-=1,k==-1&&(l+=1,k=7));if(j+=i,j<0||h<=j){j-=i,i=-i;break}}return b}},{key:"getMatrix",value:function a(){var b,c,d=this.charCode,e=this.analysisData(d),f=this.encodeData(e),g=this.processConnection(f);return this.padBuffer(g),b=this.generateErrorCorrectionCode(g),c=this.getFinalMessage(b),this.setModules(c),this.modules}}]),b}(k.default),b.default=v},function(a,b,c){"use strict";var d=c(2),e=f(d);function f(a){return a&&a.__esModule?a:{default:a}}function g(a){var b,c=document.createElement("div");return c.style.visibility="hidden",c.style.absolute="hidden",c.style.padding="0",c.style.border="0",c.style.width=a,document.body.appendChild(c),b=c.getBoundingClientRect(),document.body.removeChild(c),b.width}e.default.registerPlugin("convertUnit",g)},function(a,b,c){"use strict";var d=c(2),e=f(d);function f(a){return a&&a.__esModule?a:{default:a}}function g(a,b){var c,d=document.createElement("span");return d.style.visibility="hidden",d.style.absolute="hidden",d.style.lineHeight="1",d.textContent=a,Object.keys(b).forEach(function(a){d.style[a]=b[a]}),document.body.appendChild(d),c=d.getBoundingClientRect(),document.body.removeChild(d),c.height}e.default.registerPlugin("measureText",g)}]).default})},CalcEngine:function(b,c){b.exports=a.Spread.CalcEngine},Common:function(b,c){b.exports=a.Spread},Core:function(b,c){b.exports=a.Spread.Sheets},Sparkline:function(b,c){b.exports=a.Spread.Sheets.Sparklines}})});