<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 10:28:02
-->
<template>
  <div>
    <template v-if="skinStyle().includes('classic-style')">
      <a-drawer
        class="cardChooseDrawer"
        :visible="visible"
        :width="600"
        title="勾选指标"
        @close="close"
      >
        <a-spin :spinning="loadTree">
          <div style="min-height: 500px;">
            <a-tree
              class="treeRoleSelectClass"
              checkable
              v-model="orgCheckedKeysData"
              :auto-expand-parent="false"
              :tree-data="orgTreeData"
              @check="onCheck"
            />
            <a-empty
              style="margin-top: 100px;"
              v-if="orgTreeData.length === 0"
            />
          </div>
        </a-spin>
        <div class="drawer-footer">
          <a-button style="marginRight: 8px" @click="close">
            {{ showAlias("CANCEL", "取消") }}
          </a-button>
          <a-button type="primary" @click="handleOk" :loading="submitLoading">
            {{ showAlias("CONFIRM", "确定") }}
          </a-button>
        </div>
      </a-drawer>
    </template>
    <template v-else>
      <a-modal
        class="cardChooseModal"
        :class="[skinStyle()]"
        v-model="visible"
        title="添加指标卡片"
        @close="close"
        :footer="null"
      >
        <a-spin :spinning="loadTree">
          <div style="min-height: 500px;">
            <a-tree
              class="treeRoleSelectClass"
              checkable
              v-model="orgCheckedKeysData"
              :auto-expand-parent="false"
              :tree-data="orgTreeData"
              @check="onCheck"
            />
            <a-empty
              style="margin-top: 100px;"
              v-if="orgTreeData.length === 0"
            />
          </div>
        </a-spin>
        <div class="modal-footer">
          <a-button style="marginRight: 8px" @click="close">
            {{ showAlias("CANCEL", "取消") }}
          </a-button>
          <a-button type="primary" @click="handleOk" :loading="submitLoading">
            {{ showAlias("CONFIRM", "确定") }}
          </a-button>
        </div>
      </a-modal>
    </template>
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
import { showAlias } from "@/utils/intl.js";
import uniqBy from "lodash/uniqBy";
import cloneDeep from "lodash/cloneDeep";
export default {
  inject: ["skinStyle"],
  data() {
    return {
      showAlias,
      visible: false, // 打开关闭弹窗
      checkedKeys: [], // 树形组件选择的nodeId
      submitLoading: false,
      postTreeData: [], // 要提交的组件数据
      loadTree: false, // 加载树形组件loading
      SYS_NAME: window.system,
      orgInvalidCheckedKeysData: [], // 组织无效数据，历史订阅指标权限
      orgTreeData: [], // 公司下组织数据
      orgCheckedKeysData: [], // 公司下组织数据选中
      ppTreeDataList: [] // 平铺树数据
    };
  },
  props: {
    companyName: String,
    pageClass: String,
    signOrgId: String
  },
  methods: {
    async show() {
      this.visible = true;
      this.loadTree = true;
      await this.getRoleListByCompany(this.signOrgId);
      this.getSelectOrgKeysByCompany(this.signOrgId);
    },
    close() {
      this.visible = false;
      this.postTreeData = [];
      this.checkedKeys = [];
      this.treeData = [];
      this.orgTreeData = [];
      this.orgCheckedKeysData = [];
      this.orgInvalidCheckedKeysData = [];
      this.ppTreeDataList = [];
    },
    async handleOk() {
      await this.saveCurrentRoleList();
      this.$emit("savedData");
      this.close();
    },
    arrayToTree(items) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const id = item.key;
        const pid = item.parentKey || "x00001";

        if (!itemMap[id]) {
          itemMap[id] = {
            children: []
          };
        }

        itemMap[id] = {
          ...item,
          children: itemMap[id]["children"]
        };

        const treeItem = itemMap[id];
        if (pid === "x00001") {
          result.push(treeItem);
        } else {
          if (!itemMap[pid]) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    },
    /**
     * 树结构数组扁平化
     * @param {*} data 树结构的数组
     * @returns
     */
    treeToArray(data) {
      return data.reduce((pre, cur) => {
        const { children = [], ...item } = cur;
        return pre.concat([{ ...item }], this.treeToArray(children));
      }, []);
    },
    // 按公司获取指标树列表
    getRoleListByCompany(signOrgId) {
      return new Promise(resolve => {
        this.loadTree = true;
        request(`${"/api/smc2/newuserIndexRelation/search"}`, {
          method: "POST",
          body: {
            sign: `${this.companyName}${
              this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
            }`,
            signOrgId
          }
        }).then(res => {
          if (Array.isArray(res) && res.length) {
            console.time("orgInCompany");
            const orgTreeData = this.arrayToTree(res);
            // this.delFourthOrg(orgTreeData);
            this.orgTreeData = Object.freeze(orgTreeData);
            // this.treeToArray(cloneDeep(orgTreeData));
            this.ppTreeDataList = uniqBy(
              this.treeToArray(cloneDeep(orgTreeData)),
              "key"
            );
            console.timeEnd("orgInCompany");
          }
          resolve();
        });
      });
    },
    // 最高层级下探3层组织
    delFourthOrg(arr, index = 0) {
      index++;
      arr.forEach(item => {
        if (index > 4) {
          item.children = [];
        } else {
          this.delFourthOrg(item.children, index);
        }
      });
    },
    // 获取x公司底下的角色
    getSelectOrgKeysByCompany(signOrgId) {
      request(
        `/api/smc2/newuserIndexRelation/${
          this.pageClass === "indexGeneralViewPage2"
            ? "searchRole2"
            : "searchRoleHB"
        }`,
        {
          method: "POST",
          body: {
            sign: `${this.companyName}${
              this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
            }`,
            signOrgId
          }
        }
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          console.time("roleInCompany");
          this.orgCheckedKeysData = res;
          console.timeEnd("roleInCompany");
        }
        this.loadTree = false;
      });
    },
    // 保存当前角色选中的指标
    saveCurrentRoleList() {
      let postData = [];
      const checkedNodes = this.ppTreeDataList.filter(item => {
        return (
          this.orgCheckedKeysData.includes(item.key) && item.fullCode !== null
        );
      });
      postData = checkedNodes.map(item => {
        return {
          signOrgId: item.key.split("-")[0],
          orgId: item.key.split("-")[3],
          businessSegmentsId: item.key.split("-")[1],
          indexId: item.key.split("-")[2],
          fullCode: item.fullCode,
          roleId: this.roleCode
        };
      });
      this.submitLoading = true;
      return new Promise(resolve => {
        request("/api/smc2/newuserIndexRelation/insert", {
          method: "POST",
          body: {
            sign: `${this.companyName}${
              this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
            }`,
            listRelation: postData
          }
        })
          .then(() => {
            this.submitLoading = false;
            resolve();
          })
          .catch(() => {
            this.submitLoading = false;
          });
      });
    },
    // 树形控件改变
    onCheck(checkedKeys) {
      this.orgCheckedKeysData = checkedKeys;
    }
  }
};
</script>
<style lang="less">
.cardChooseDrawer {
  .ant-drawer-body {
    padding-bottom: 77px !important;
    .drawer-footer {
      position: absolute;
      bottom: 0;
      width: 100%;
      border-top: 1px solid #e8e8e8;
      padding: 10px 16px;
      text-align: right;
      left: 0;
      background: #fff;
      border-radius: 0 0 4px 4px;
    }
  }
  .treeRoleSelectClass {
    .ant-tree-checkbox-disabled {
      display: none !important;
    }
    .ant-tree-treenode-disabled {
      .ant-tree-node-content-wrapper {
        cursor: default;
        span {
          color: rgba(0, 0, 0, 0.65);
          cursor: default;
        }
      }
    }
  }
}
.cardChooseModal {
  &.dark {
    .ant-spin-nested-loading > div > .ant-spin {
      background-color: rgba(34, 36, 37, 1);
    }
    .ant-modal-header {
      border-bottom: none;
    }
    .ant-modal-content {
      background-color: #222425;
      .ant-tree li .ant-tree-node-content-wrapper {
        color: #e2e8ea;
      }
      .modal-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        border-top: 1px solid #a1a6ac;
        padding: 10px 16px;
        text-align: right;
        left: 0;
        background: #222425;
        border-radius: 0 0 4px 4px;
        .ant-btn {
          background-color: transparent;
          color: #fff;
          &.ant-btn-primary {
            background-color: #00aaa6;
          }
        }
      }
    }
  }
  .ant-modal-header {
    text-align: center;
    background-color: #00aaa6;
    .ant-modal-title {
      color: #ffffff;
    }
  }
  .ant-modal-close-x {
    color: #ffffff;
  }
  .ant-modal-body {
    .ant-tree-switcher {
      color: #c9cdd4;
      i {
        font-size: 20px !important;
        margin-top: 2px;
      }
    }
  }
}
</style>
