/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Filter=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/filter/filter.entry.js")}({"./dist/plugins/filter/filter-actions.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/filter/filter-ns.js"),e=c("Core"),f=c("Common"),g=c("./dist/plugins/filter/filter.js"),h=c("ConditionalFormatting"),i=f.Common.j.Fa,j=f.Common.j.Ba,k=e.Commands.ActionBase,l="sortFilter",m="filterTextSelectAction",n="filterByCondition",o="clearFilter",p="contextmenuFilterForSheet",q="contextmenuFilterForTable",r=e.Commands.h4,s=f.Common.k.ac,t=void 0,u=null,v=new f.Common.ResourceManager(d.SR),w=v.getResource.bind(v);function E(a,b){var c,d;return a?(d=b.tables.findByName(a),d&&(c=d.rowFilter())):c=b.rowFilter(),c}function F(a,b,c,d,e){return{sheet:a,sheetName:a.name(),table:b,tableCol:c,filterValues:d,conditionInfo:0===s(d)&&e?e.toJSON():t}}function G(a,b,c,d){return{sheet:a,sheetName:a.name(),col:b,filterValues:c,conditionInfo:0===s(c)&&d?d.toJSON():t}}function H(a,b,c,d,f){var g=c?a-c.range().col:-1;c?b.Wq(e.Events.TableFiltering,F(b,c,g,d,f)):b.Wq(e.Events.RangeFiltering,G(b,a,d,f))}function I(a,b,c){var d,f;c?(f=a-c.range().col,d={sheet:b,sheetName:b.name(),table:c,tableCol:f},b.Wq(e.Events.TableFilterClearing,d)):(d={sheet:b,sheetName:b.name(),col:a},b.Wq(e.Events.RangeFilterClearing,d))}function J(a,b,c){var d,f;c?(f=a-c.range().col,d={sheet:b,sheetName:b.name(),table:c,tableCol:f},b.Wq(e.Events.TableFilterCleared,d)):(d={sheet:b,sheetName:b.name(),col:a},b.Wq(e.Events.RangeFilterCleared,d))}function K(a,b,c,d,f,g){var h=d?b-d.range().col:-1;a.filter(b),d?c.Wq(e.Events.TableFiltered,F(c,d,h,f,g)):c.Wq(e.Events.RangeFiltered,G(c,b,f,g))}function L(a,b){a.suspendPaint();var c=e.Commands.bWa(a.name());return a.ITa.undo(b[c]),a.resumePaint(),!0}x=function(a){D(b,a);function b(b,c){var d=a.call(this,b,c)||this,e=d;return e.zZ=e.VQ.cmdOption,e.zZ.rowFilter=E(e.zZ.tableName,e.kj),d}return b.prototype.execute=function(){var a,b=this,c=b.zZ.rowFilter,d=b.zZ.colIndex,f=b.zZ.isAsc,g=b.zZ.color,h=b.zZ.isBackColor;b.kj.suspendPaint(),b.kj.ITa.startTransaction(),g!==t?c.sortColumnByColor(d,f,g,h):c.sortColumn(d,f),a=e.Commands.bWa(b.kj.name()),b.VQ[a]=b.kj.ITa.endTransaction(),b.kj.resumePaint()},b.prototype.undo=function(){return L(this.kj,this.VQ)},b}(k),e.Commands[l]={canUndo:!0,execute:function(a,b,c){return r(a,x,b,c)}},y=function(a){D(b,a);function b(b,c){var d=a.call(this,b,c)||this,e=d;return e.zZ=e.VQ.cmdOption,d}return b.prototype.execute=function(){var a,b,c=this,d=c.zZ.colIndex,f=c.VQ.cmdOption.tableName,g=E(f,c.kj);g&&(f&&(a=c.kj.tables.findByName(f)),c.kj.suspendPaint(),c.kj.ITa.startTransaction(),I(d,c.kj,a),g.removeFilterItems(d),J(d,c.kj,a),b=e.Commands.bWa(c.kj.name()),c.VQ[b]=c.kj.ITa.endTransaction(),c.kj.resumePaint())},b.prototype.undo=function(){return L(this.kj,this.VQ)},b}(k),e.Commands[o]={canUndo:!0,execute:function(a,b,c){return r(a,y,b,c)}},z=function(a){D(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.execute=function(){var a,b,c,d,f,g,i,k=this,l=k.VQ.cmdOption.colIndex,m=k.VQ.cmdOption.checkedValues,n=k.VQ.cmdOption.allValuesLength,o=s(m),p=w().Blanks,q=k.VQ.cmdOption.tableName,r=k.kj,t=E(q,r);if(t){if(q&&(a=r.tables.findByName(q)),r.suspendPaint(),r.ITa.startTransaction(),H(l,r,a,m,u),t.removeFilterItems(l),o!==n)for(b=0;b<o;b++)c=m[b],d=void 0,f=c.text,g=c.value,f===p&&(f=""),d=new h.Condition(2),d._ps.expected=f,d._ps.compareType=0,d._ps.useWildCards=!1,"date"===j(g)&&(d.jzb=!0),t.addFilterItem(l,d);K(t,l,r,a,m,u),i=e.Commands.bWa(r.name()),k.VQ[i]=r.ITa.endTransaction(),r.resumePaint()}},b.prototype.undo=function(){return L(this.kj,this.VQ)},b}(k),e.Commands[m]={canUndo:!0,execute:function(a,b,c){return r(a,z,b,c)}},A=function(a){D(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.execute=function(){var a,b,c=this,d=c.VQ.cmdOption.colIndex,f=c.VQ.cmdOption.condition,g=c.VQ.cmdOption.tableName,h=E(g,c.kj);h&&(g&&(a=c.kj.tables.findByName(g)),c.kj.suspendPaint(),c.kj.ITa.startTransaction(),H(d,c.kj,a,[],f),h.removeFilterItems(d),h.addFilterItem(d,f),K(h,d,c.kj,a,[],f),b=e.Commands.bWa(c.kj.name()),c.VQ[b]=c.kj.ITa.endTransaction(),c.kj.resumePaint())},b.prototype.undo=function(){return L(this.kj,this.VQ)},b}(k),e.Commands[n]={canUndo:!0,execute:function(a,b,c){return r(a,A,b,c)}},B=function(a){D(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.execute=function(){var a,b,c,d,f,j=this,k=j.kj,l=j.VQ.cmdOption,m=l.selection,n=l.activeRow,o=l.activeCol,p=l.expectedText;k.ITa.startTransaction(),k.rowFilter()?(a=void 0,b=k.rowFilter(),c=b.range,c.contains(n,o,1,1)?(H(o,k,t,[p],u),a=new h.Condition(h.ConditionType.textCondition,{compareType:0,expected:p}),b.addFilterItem(o,a),K(b,o,k,t,[p],u)):c.row-1!==n||i(b.qZ[o])?(k.rowFilter().unfilter(),k.rowFilter(new g.HideRowFilter(m))):(d=k.getText(c.row,o),H(o,k,t,[p],u),a=new h.Condition(h.ConditionType.textCondition,{compareType:0,expected:d}),b.addFilterItem(o,a),K(b,o,k,t,[p],u))):k.rowFilter(new g.HideRowFilter(m)),f=e.Commands.bWa(k.name()),this.VQ[f]=k.ITa.endTransaction()},b.prototype.undo=function(){return L(this.kj,this.VQ)},b}(k),e.Commands[p]={canUndo:!0,execute:function(a,b,c){return r(a,B,b,c)}},C=function(a){D(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.execute=function(){var a,b,c,d,f,g=this,j=g.kj,k=g.VQ.cmdOption,l=k.tableName,m=k.expectedText,n=k.activeCol,o=k.activeRow;j.ITa.startTransaction(),a=E(l,j),b=j.tables.findByName(l),a&&b&&(c=b.range(),b.showHeader()&&c.row===o&&!i(a.qZ[n-c.col])&&(m=j.getText(o+1,n)),H(n,j,t,[m],u),d=new h.Condition(h.ConditionType.textCondition,{compareType:0,expected:m}),a.removeFilterItems(n),a.addFilterItem(n,d),K(a,n,j,t,[m],u),f=e.Commands.bWa(j.name()),this.VQ[f]=j.ITa.endTransaction())},b.prototype.undo=function(){return L(this.kj,this.VQ)},b}(k),e.Commands[q]={canUndo:!0,execute:function(a,b,c){return r(a,C,b,c)}},e.Commands.fVa=function(a){a.register(l,e.Commands[l]),a.register(m,e.Commands[m]),a.register(n,e.Commands[n]),a.register(o,e.Commands[o]),a.register(p,e.Commands[p]),a.register(q,e.Commands[q])}},"./dist/plugins/filter/filter-dialog.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa,Ta,Ua,Va,Wa,Xa,Ya,Za,$a,_a,ab,bb,cb,db,eb,fb,gb,hb,ib,jb,kb,lb,mb,nb,ob,pb,qb,rb,sb,tb,ub,vb,wb,xb,yb,zb,Ab,Bb=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("ConditionalFormatting"),g=c("./dist/plugins/filter/filter-ui.js"),h=c("CellTypes"),i=c("./dist/plugins/filter/filter-ns.js"),j=e.Common.CultureManager,k=e.Common.l,l=e.Common.j.Ba,m=e.Common.u.Ob,n=e.Common.u.Pb,o=null,p=void 0,q=d.Ul.Nl,r=d.Ul,s=e.Common.k.ac,t=r.nl,u="left",v="top",w="float",x="relative",y="gc-ui-filter-subitems-container",z="gc-no-user-select",A="gc-filter-dialog-style",B="position",C="gc-ui-filter-subitem-item",D="gc-ui-filter-allDate-item",E="gc-filter-alldates-arrow",F="gc-filter-item-hover",G="ui-state-hover ",H=G+F,I="gc-filter-hover",J=I+" form-control well "+H,K="gc-filter-button-hover "+G,L="gc-filter-submenu-check",M="gc-filter-submenu-check-img",N="gc-filter-allDate-check-img",O="gc-filter-alldates-list",P="gc-filter-addDates-container",Q="gc-filter-subMenu-wrap",R="top10_dialog",S="custom_dialog",T="gc-sub-color-sort",U="gc-sub-color-filter",V="automatic",W="more_color",X="gc-filter-dialog-color-title",Y="gc-color-item",Z="color-checked-wrap",$="gc-color-item-list",_="gc-filter-sort",aa="gc-filter-color-dialog-background",ba="gc-filter-color-dialog-font",ca="gc-filter-color-dialog-style",da="TextFilter",ea="NumberFilter",fa="DateFilter",ga="#D3F0E0",ha="#86BFA0",ia="#000000",ja="#FFFFFF",ka="border-box",la="data-isCheck",ma="inline-block",na="content-box",oa="#top10-item",pa="#gc-color-dialog-selected",qa="#gc-color-dialog-noFill",ra="thin solid rgb(160,160,160)",sa="data-checked",ta="gc-filter-dialog-close",ua="1px solid #c2c2c2",va="gc-filter-top10-rank-illegal",wa="1px solid #e0e0e0",xa="data-isNeedDialog",ya="data-isCustom",za="data-conditionType",Aa="data-operatorType",Ba="data-colIndex",Ca="data-colorType",Da="data-color",Ea="<div class=",Fa="<span class=",Ga="<img class=",Ha="<li class=",Ia="</span><span>",Ja="</div></div>",Ka="display",La="box-sizing",Ma="none",Na="width",Oa="height",Pa="padding",Qa="border",Ra="click",Sa="mouseover",Ta="mouseout",Ua="resize",Va="inherit",Wa="#E81123",Xa="white",Ya="backgroundColor",Za="#f0f0f0",$a="margin",_a=$a+"Left",ab=$a+"Top",bb=$a+"Right",cb="font-size",db=[[1,"Equal","E",1,0],[1,"NotEqual","N",1,1],[1,"GreaterThan","G",1,2],[1,"GreaterOrEquals","O",1,3],[1,"LessThan","L",1,4],[1,"LessThanOrEquals","Q",1,5],[1,"Between","W",0,1],[1,"Top10","T",8,0],[0,"AboveAverage","A",10,0],[0,"BelowAverage","O",10,1],[1,"Custom","F",1,0]],eb=[[1,"Equal","E",2,0],[1,"NotEqual","N",2,1],[1,"Begin","I",2,2],[1,"End","T",2,4],[1,"Contain","A",2,6],[1,"NotContain","D",2,7],[1,"Custom","F",2,0]],fb=[[1,"Equal","E",5,0],[1,"Before","B",5,2],[1,"After","A",5,4],[1,"Between","W",0,1],[0,"Tomorrow","T",6,2],[0,"Today","O",6,0],[0,"Yesterday","D",6,1],[0,"NextWeek","K",6,9],[0,"ThisWeek","H",6,7],[0,"LastWeek","L",6,8],[0,"NextMonth","M",6,6],[0,"ThisMonth","S",6,4],[0,"LastMonth","N",6,5],[0,"NextQuarter","N",6,10],[0,"ThisQuarter","N",6,11],[0,"LastQuarter","N",6,12],[0,"NextYear","N",6,13],[0,"ThisYear","N",6,14],[0,"LastYear","N",6,15],[0,"YearToDate","A",6,0,6],[0,"AllDates","P",6,1],[1,"Custom","F",6,0]],gb="IsBeginWith-2-2",hb="IsEndWith-4-2",ib="NotEndWith-5-2",jb="IsContain-6-2",kb="NotContains-7-2",lb=[["Q1","1",6,0,2],["Q2","B",6,1,2],["Q3","A",6,2,2],["Q4","4",6,3,2],["Jan","J",6,0,3],["Feb","F",6,1,3],["Mar","M",6,2,3],["Apr","A",6,3,3],["May","Y",6,4,3],["Jun","U",6,5,3],["Jul","L",6,6,3],["Aug","T",6,7,3],["Sep","S",6,8,3],["Oct","O",6,9,3],["Nov","N",6,10,3],["Dec","D",6,11,3]],mb=["IsEquals-0-1","NotEquals-1-11","IsGreaterThan-2-1","IsGreaterOrEqual-3-1","IsLess-4-1","LessOrEqual-5-1",gb,"NotBeginWith-3-2",hb,ib,jb,kb],nb=["IsEquals-0-2","NotEquals-1-11","IsGreaterThan-2-1","IsGreaterOrEqual-3-1","IsLess-4-1","LessOrEqual-5-1",gb,"NotBeginWith-3-2",hb,ib,jb,kb],ob=["IsEquals-0-5","NotEquals-1-5","IsAfter-4-5","AfterOrEqual-5-5","IsBefore-2-5","BeforeOrEqual-3-5",gb,"NotBeginWith-4-2",hb,ib,jb,kb],pb=new e.Common.ResourceManager(i.SR),qb=pb.getResource.bind(pb);function Cb(a){return e.Common.pc.bc(e.Common.pc.ec(a))}function Db(a,b,c,d){var e,f;try{if(c.suspendPaint(),!d)return;e=d.sj?d.sj.name():p,f={tableName:e,colIndex:a,condition:b},c.wu().execute({cmd:"filterByCondition",sheetName:c.name(),cmdOption:f})}finally{c.resumePaint()}}rb=function(){function a(a,b,c,e,f,g){var h=this;h.Nc=p,h.EXa=g,h.FXa=a,h.kj=e,h.GXa=d.GC$(a),h.xo=b,h.P$a=!1,h.HXa=c,h.xr=c.rowFilter,h.IXa=f,h.JXa=o}return a.prototype.ad=function(){this.yl(this.KXa()),this.LXa()},a.prototype.zW=function(a){return g.gZ.zW(a)},a.prototype.KXa=function(){var a=d.GC$(q("div")).attr("id",y+"_"+this.EXa);return a.addClass(A+" "+z+" "+Q),a.css([B,Ka,La],["absolute",Ma,ka]),this.JXa=a,d.GC$(this.FXa).append(this.JXa),a},a.prototype.cXa=function(){return!("none"===this.JXa[0].style.display)},a.prototype.Ao=function(){d.GC$("."+Q).hide(),this.JXa.show(),this.MXa()},a.prototype.hT=function(){this.JXa.hide(),this.JXa.find("."+J).removeClass(J)},a.prototype.OWa=function(a){return this.Nc=a,this},a.prototype.MXa=function(a){var b,c,d,e,f=this.GXa.offset(),g=this.IXa.AWa.offset(),h=document.documentElement,i=g.left,j=g.top,k=h.clientWidth,l=h.clientHeight,m=this.JXa.width(),n=this.JXa.height(),o=this.GXa.parent().parent(),p=o.offset(),q=p.top,r=this.xo.offset();r.left+this.xo.width()+m-i>k?(b=-1*(m+2)+"px",d=!1):(b=this.xo.width()+"px",d=!0),e=f.top-r.top,c=q+e+n-j>l?j+l-n-p.top-6:e,c+="px",this.JXa.css([u,v],[b,c]),a&&a.call(this,d)},a.prototype.NXa=function(a,b){var c=this,d=c.kj,e=c.xr;Db(a,b,d,e),this.IXa.close()},a.prototype.oXa=function(){var a=d.GC$("."+F,this.JXa[0]);a.trigger(Ra)},a.prototype.WW=function(){var a,b=this,c=b.HXa,d=this.kj,e=c.col,f=c.rowFilter;f&&(a={rowFilter:f,colIndex:e},d.wu().execute({cmd:"clearFilter",sheetName:d.name(),cmdOption:a}))},a.prototype.tXa=function(){return this.hT(),o},a.prototype.uXa=function(){this.cXa()||(this.Ao(),this.mXa())},a.prototype.yl=function(a){},a.prototype.LXa=function(){},a.prototype.mXa=function(){},a.prototype.wXa=function(){},a.prototype.vXa=function(){},a}(),sb=function(a){Bb(b,a);function b(b,c,d,e,f,g){return a.call(this,b,c,d,e,f,g)||this}return b.prototype.yl=function(a){this.OXa()},b.prototype.LXa=function(){var a=this;a.JXa.bind(Sa,function(a){t(a)}).bind(Ta,function(a){t(a)}),d.GC$("."+C,a.JXa[0]).bind(Sa,function(a){d.GC$("."+C).removeClass(J),d.GC$(this).addClass(J),t(a)}).bind(Ta,function(a){d.GC$(this).removeClass(J),t(a)}).bind(Ra,function(b){d.GC$(this).attr("id")!==P&&a.PXa(d.GC$(this)),t(b)}),this.QXa&&this.QXa()},b.prototype.r$a=function(a){return a!==p&&"TRUE"===a||"FALSE"===a},b.prototype.RXa=function(){var a,b,c,e,f,g,h,i,j,k,l,m,n=this.xr.rZ[this.HXa.col],o=this.SXa,q=!1;if(n&&n.length>0)for(n=n[0],g=n.compareType(),h=n.expected(),e=n.expectTypeId(),f=n.conType(),10===f&&(g=n.type()),8===f&&(g=0),this.r$a(h)&&(f=11),0===f&&(i=n.item1().compareType(),j=n.item2().compareType(),5===Math.max(i,j)&&3===Math.min(i,j)&&(q=!0)),k=0,l=s(o);k<l;k++){if(b=0===f&&o[k][3]===f&&q||o[k][3]===f&&0!==f||11===f&&(1===o[k][3]||2===o[k][3]),b&&8===f){a=k;break}if(c=o[k][4]===g&&!q||6===o[k][3]&&h===o[k][4]||q,b&&c&&(isNaN(e)||0===e)){a=k;break}}isNaN(e)||6!==e||(a=19),isNaN(e)||2!==e&&3!==e||(a=20,this.TXa&&this.TXa(lb,h,e)),a!==p&&0!==a?m=d.GC$("."+C)[a]:!n||3===f||a!==p||2===f&&0===g?0===a&&5===f&&(m=d.GC$("."+C)[a]):m=d.GC$("."+C)[s(o)-1],d.GC$(m).attr(la,1),d.GC$(m).find("."+M).css([Ka],[ma])},b.prototype.mXa=function(){var a=d.GC$("."+C).removeClass(J)[0];d.GC$(a).addClass(J)},b.prototype.UXa=function(a){var b;if(0!==a)b=a;else switch(this.Nc){case ea:b=1;break;case da:b=2;break;case fa:b=5}return b},b.prototype.VXa=function(a,b,c,d){var e=this.kj,f;switch(a){case R:f=new yb(e.parent.xv(),e,this.HXa,R);break;case S:f=new Ab(e.parent.xv(),e,this.HXa,S,this.IXa.fY,d,b,c,this.UXa(b))}f&&f.OT()},b.prototype.PXa=function(a){var b,c,d=this.HXa.col,e=parseInt(a.attr(xa),10),g=parseInt(a.attr(za),10),h=parseInt(a.attr(Aa),10),i=parseInt(a.attr("data-expectTypeId"),10),j=parseInt(a.attr(ya),10),k=parseInt(a.attr(la),10);return isNaN(i)||(e=0,j=0),0===e&&1===k?(this.WW(),void this.IXa.close()):1===e?(this.IXa.close(),b=8===g&&0===h?R:S,void this.VXa(b,g,h,j)):(c=new f.Condition(g),10===g?c.type(h):c.compareType(h),6!==g||isNaN(i)?6===g&&(c=new f.Condition(g),c.expected(h)):(c=new f.Condition(g),c.expectTypeId(i),c.expected(h)),void this.NXa(d,c))},b.prototype.OXa=function(){},b.prototype.WXa=function(a){var b,c,e="";for(b=0,c=a.length;b<c;b++)e+=b===c-1?this.XXa(a[b],1):this.XXa(a[b],0);this.JXa.html(e),d.GC$(this.FXa).append(this.JXa),this.YXa(),this.RXa()},b.prototype.YXa=function(){var a=this.FXa.offsetHeight-2,b=this.FXa.offsetTop;d.GC$("."+C+",."+D).css([Pa,Oa,"line-height","white-space","clear",Qa,Na,Oa,cb,$a],["0 10px 0 0",a,a,"nowrap","both",Ma,"auto","auto","12px",0]),this.JXa.css([u,v],["100%",b]),d.GC$(".gc-filter-hotkey").css(["text-decoration"],["underline"]),d.GC$("."+L).css([Ka,Na,"textAlign"],[ma,"24px","center"]),d.GC$("."+L+" img").css([Ka,"verticalAlign"],[ma,"text-bottom"]),d.GC$("."+M+",."+N).hide(),d.GC$("#"+P).css([B],[x]),d.GC$("."+E).css([_a],["5px"]),d.GC$("#"+O).css([B,Ka,Na],["absolute","none","auto"])},b.prototype.XXa=function(a,b){var c="",d=a[0],e=qb()[a[1]],f=a[3],g=a[4];return c=""+Ea+'"'+C+'" '+ya+'="'+b+'" '+xa+' ="'+d+'" '+za+'="'+f+'" '+Aa+'="'+g+'">'+Ea+'"'+_+'">'+Fa+'"'+L+'">'+Ga+'"'+M+'" src="'+this.zW(11)+'">'+Ia+e+"</span>"+Ja},b.prototype.vXa=function(){var a=d.GC$("."+F,this.JXa[0]).removeClass(J),b=d.GC$("."+C);0===a.index()?d.GC$(b[b.length-1]).addClass(J):d.GC$(b[a.index()-1]).addClass(J)},b.prototype.wXa=function(){var a=d.GC$("."+F,this.JXa[0]);a.length>0&&a[0].nextSibling?(a.removeClass(J),d.GC$(a[0].nextSibling).addClass(J)):this.mXa()},b}(rb),tb=function(a){Bb(b,a);function b(b,c,d,e,f,g){var h=a.call(this,b,c,d,e,f,g)||this;return h.SXa=db,h}return b.prototype.OXa=function(){this.WXa(this.SXa)},b}(sb),b.WWa=tb,ub=function(a){Bb(b,a);function b(b,c,d,e,f,g){var h=a.call(this,b,c,d,e,f,g)||this;return h.SXa=eb,h}return b.prototype.OXa=function(){this.WXa(eb)},b}(sb),b.XWa=ub,vb=function(a){Bb(b,a);function b(b,c,d,e,f,g){var h=a.call(this,b,c,d,e,f,g)||this;return h.SXa=fb,h}return b.prototype.OXa=function(){this.WXa(fb)},b.prototype.XXa=function(a,b){var c="",d=a[0],e=qb()[a[1]],f=a[3],g=a[4];return c="AllDates"===a[1]?'<div id="'+P+'" class="'+C+'" '+ya+'="'+b+'" '+xa+' ="'+d+'" '+za+'="'+f+'" '+Aa+'="'+g+'">'+Ea+'"'+_+'">'+Fa+'"'+L+'">'+Ga+'"'+M+'" src="'+this.zW(11)+'">'+Ia+e+"</span>"+Fa+'"'+E+'"><img src="'+this.zW(10)+'"></span></div>'+this.ZXa()+"</div>":"YearToDate"===a[1]?""+Ea+'"'+C+'" data-expectTypeId="'+a[5]+'" '+ya+'="'+b+'" '+xa+' ="'+d+'" '+za+'="'+f+'" '+Aa+'="'+g+'">'+Ea+'"'+_+'">'+Fa+'"'+L+'">'+Ga+'"'+M+'" src="'+this.zW(11)+'">'+Ia+e+"</span>"+Ja:""+Ea+'"'+C+'" '+ya+'="'+b+'" '+xa+' ="'+d+'" '+za+'="'+f+'" '+Aa+'="'+g+'">'+Ea+'"'+_+'">'+Fa+'"'+L+'">'+Ga+'"'+M+'" src="'+this.zW(11)+'">'+Ia+e+"</span>"+Ja},b.prototype.Ao=function(){d.GC$("."+Q).hide(),this.JXa.show(),this.MXa(this.$Xa)},b.prototype.$Xa=function(a){var b,c,e,f,g,h,i,j,k,l;this.JXa.show(),b=d.GC$("#"+O),c=d.GC$("#"+P),e=this.JXa,h=document.documentElement,i=h.clientWidth,j=h.clientHeight,k=this.IXa.AWa.offset(),l=k.top,f=e.offset().left+e.width()+b.width()>i?-1*b.width()+"px":a?e.width()+"px":-1*b.width()+"px",g=c.offset().top+b.height()-l>j?l+j-b.height()-c.offset().top-5:0,g+="px",b.css([u,v],[f,g])},b.prototype.ZXa=function(){var a,b='<div id="'+O+'" class="'+A+'">';for(a=0;a<s(lb);a++)b+=""+Ea+'"'+D+'" data-expectTypeId="'+lb[a][4]+'" '+za+'="6"  '+Aa+'="'+lb[a][3]+'">'+Ea+'"'+_+'">'+Fa+'"'+L+'">'+Ga+'"'+N+'" src="'+this.zW(11)+'">'+Ia+qb()[lb[a][0]]+"</span>"+Ja;return b+="</div>"},b.prototype.mXa=function(){var a,b;this._Xa()?(a=d.GC$("."+D).removeClass(J),d.GC$(a[0]).addClass(J)):(b=d.GC$("."+C).removeClass(J)[0],d.GC$(b).addClass(J))},b.prototype._Xa=function(){var a=!1,b=d.GC$("#"+O);return a=0!==b.length&&b.css(Ka)!==Ma},b.prototype.aYa=function(){d.GC$("#"+O).hide(),d.GC$("."+D).removeClass(J)},b.prototype.QXa=function(){var a=this;d.GC$("."+D,a.JXa[0]).bind(Sa,function(){d.GC$(this).addClass(J),d.GC$("#"+P).addClass(J)}).bind(Ta,function(){d.GC$(this).removeClass(J)}).bind(Ra,function(b){a.PXa(d.GC$(this)),t(b)}),d.GC$("#"+P,a.JXa[0]).bind(Sa,function(){d.GC$("#"+O).show()}).bind(Ta,function(){a.aYa()}).bind(Ra,function(b){d.GC$("#"+O).show(),a.mXa(),t(b)})},b.prototype.bYa=function(){var a=!1,b=d.GC$("#"+P);return a=0!==b.length&&b.hasClass(J)},b.prototype.TXa=function(a,b,c){var e,f,g;for(f=0;f<s(a);f++)if(a[f][3]===b&&a[f][4]===c){e=f;break}g=d.GC$("."+D)[e],d.GC$(g).attr(la,1),d.GC$(g).find("."+N).css([Ka],[ma]).attr(la,1)},b.prototype.oXa=function(){var a;a=this._Xa()?d.GC$("."+F,d.GC$("#"+O)[0]):d.GC$("."+F,this.JXa[0]),a.trigger(Ra)},b.prototype.tXa=function(){var a=o;return this._Xa()?(this.aYa(),a=this):this.hT(),a},b.prototype.uXa=function(){var a=this.cXa(),b=this._Xa();a?!b&&this.bYa()&&(d.GC$("#"+O).show(),this.mXa()):(this.Ao(),this.mXa())},b.prototype.vXa=function(){var a,b,c,e;this._Xa()?(a=d.GC$("."+F,d.GC$("#"+O)[0]).removeClass(J),b=d.GC$("."+D),0===a.index()?d.GC$(b[b.length-1]).addClass(J):d.GC$(b[a.index()-1]).addClass(J)):(c=d.GC$("."+F,this.JXa[0]).removeClass(J),e=d.GC$("."+C),0===c.index()?d.GC$(e[e.length-1]).addClass(J):d.GC$(e[c.index()-1]).addClass(J))},b.prototype.wXa=function(){var a,b,c;this._Xa()?(a=d.GC$("."+F,d.GC$("#"+O)[0]).removeClass(J),b=d.GC$("."+D),a.index()===b.length-1?d.GC$(b[0]).addClass(J):d.GC$(b[a.index()+1]).addClass(J)):(c=d.GC$("."+F,this.JXa[0]),c.length>0&&c[0].nextSibling?(c.removeClass(J),d.GC$(c[0].nextSibling).addClass(J)):this.mXa())},b}(sb),b.YWa=vb,wb=function(a){Bb(b,a);function b(b,c,d,e,f,g){return a.call(this,b,c,d,e,f,g)||this}return b.prototype.yl=function(){this.cYa(this.IXa._Wa,this.IXa.aXa)},b.prototype.dYa=function(){var a,b,c,d,e,f=this.xr.zZ;if(f&&f.color!==p){if(b=f.isBackColor?0:1,c=f.color,d=this.IXa._Wa,e=this.IXa.aXa,""!==c&&(c=Cb(c)),""===c&&0===b&&d[V]===p)return p;if(""===c&&0!==b&&e[V]===p)return p;a={colorType:b,color:c}}return a},b.prototype.eYa=function(){var a=this.xr.rZ[this.HXa.col],b;return a&&a.length>0&&3===a[0].conType()&&(b={colorType:a[0].compareType(),color:a[0].expected()}),b},b.prototype.cYa=function(a,b){this.fYa(a,b),this.gYa()},b.prototype.hYa=function(a,b){this.IXa.DXa(a,b),this.IXa.close()},b.prototype.iYa=function(a){var b,c,d,e,f;this.IXa.close(),b=this,c=b.kj,e=a?this.IXa._Wa:this.IXa.aXa,this.d$a&&(d=this.d$a.color),f=new zb(c.parent.xv(),c,this.HXa,this.EXa,e,a,d),f.OT()},b.prototype.jYa=function(a,b){var c=new f.Condition(3),d=this.HXa.col,e=b?0:1,g=this.eYa();return g&&g.colorType===e&&g.color===a?(this.WW(),void this.IXa.close()):(c.compareType(e),c.expected(a),void this.NXa(d,c))},b.prototype.gYa=function(){d.GC$("."+X,this.JXa[0]).css([Oa,"line-height","textIndent",Ya,"color","whiteSpace"],["32px","32px","8px","rgb(235,235,235)","rgb(106,106,106)","nowrap"]),d.GC$("."+$,this.JXa[0]).css(["listStyle","padding",Oa,"lineHeight",Qa,"whiteSpace",Na,$a,cb,La],[Ma,"2px 0","23px","23px","1px solid transparent","nowrap","140px",0,"12px","content-box"]),d.GC$("."+Y,this.JXa[0]).css([Na,Qa,bb,Oa,La,"whiteSpace",w],["75px","1px solid rgb(130,130,130)","24px","22px",ka,"nowrap",u]),d.GC$("."+Z,this.JXa[0]).css([Ka,Na,Oa,"verticalAlign",$a,"textAlign",w],[ma,"22px","22px","middle","0 3px 0 2px","center",u]),d.GC$("."+Z+" img",this.JXa[0]).css([ab,Ka],["3px",Ma]),d.GC$(".gc-color-dialog-more-color",this.JXa[0]).css([Pa],["0 5px 0 0"]),d.GC$("."+Z+" img.checked",this.JXa[0]).css([ab,Ka],["3px","block"])},b.prototype.fYa=function(a,b){var c,e,f,g;this.EXa===T?(c=qb().SortCellTitle,e=qb().SortFontTitle,f=this.dYa()):this.EXa===U&&(c=qb().FilterCellTitle,e=qb().FilterFontTitle,f=this.eYa()),this.d$a=f,g=this.kYa(c,a[V],a.data,0,f),g+=this.kYa(e,b[V],b.data,1,f),""!==g?this.JXa.html(g):this.P$a=!0,d.GC$(this.FXa).append(this.JXa)},b.prototype.kYa=function(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,q=!1,r="checked";e&&d===e.colorType&&""!==e.color?g=Cb(e.color):e&&""===e.color&&d===e.colorType&&(g=e.color),f=0===d?aa:ba,h=""+Ea+"'"+X+"'>"+a+"</div><ul class='"+f+"' style='padding: 7px 0 0 0;margin:0'>",i=0;for(j in c)c.hasOwnProperty(j)&&i<5&&(k="",g!==p&&g===j&&(k=r,q=!0),h+=""+Ha+'"'+$+'" '+Ba+'="'+c[j]+'" '+Ca+'="'+d+'" '+Da+' = "'+j+'">'+Fa+'"'+Z+'">'+Ga+'"'+k+'" src="'+this.zW(11)+'"></span>'+Ea+'"'+Y+'" style="background-color: '+j+'" ></div></li>',i++);return 0===i&&g===p||b===p&&1===i?h="":(b!==p&&(l=0===d?qb().NoFill:qb().Automatic,m="",g!==p&&""===g&&(m=r,q=!0),h+=""+Ha+'"'+$+'" '+Ba+'="'+V+'" '+Ca+'="'+d+'" '+Da+' = "">'+Fa+'"'+Z+'"><img  class="'+m+'" src="'+this.zW(11)+'"></span>'+Ea+'"'+_+'">'+l+"</div></li>"),5===i&&(n="",q||g===p||(n=r),o=0===d?qb().CellColor:qb().FontColor,h+=""+Ha+'"'+$+'" '+Ba+'="'+W+'" '+Ca+'="'+d+'" >'+Fa+'"'+Z+'">'+Ga+'"'+n+'" src="'+this.zW(11)+'"></span>'+Ea+'"'+_+' gc-color-dialog-more-color">'+o+"</div></li>"),h+="</ul>")},b.prototype.mXa=function(){var a=d.GC$("."+$,this.JXa[0]).removeClass(J)[0];d.GC$(a).addClass(J)},b.prototype.LXa=function(){var a=this;a.JXa.bind(Sa,function(a){t(a)}).bind(Ta,function(a){t(a)}),d.GC$("."+$,a.JXa[0]).bind(Ra,function(){var b=d.GC$(this).attr(Da),c=d.GC$(this).attr(Ba),e=d.GC$(this).attr(Ca),f=!1;return e=parseInt(e,10),0===e&&(f=!0),"more_color"===c?void a.iYa(f):void(a.EXa===T?a.hYa(b,f):a.EXa===U&&a.jYa(b,f))}).bind(Sa,function(b){d.GC$("."+$,a.JXa[0]).removeClass(J),d.GC$(this).addClass(J),t(b)}).bind(Ta,function(a){d.GC$(this).removeClass(J),t(a)})},b.prototype.kzb=function(a){var b=0;return a.forEach(function(a,c){d.GC$(a).hasClass(F)&&(b=c)}),b},b.prototype.vXa=function(){var a,b=d.GC$("."+$,this.JXa[0]),c=this.kzb(b);0===c?d.GC$(b.removeClass(J)[b.length-1]).addClass(J):(a=b.removeClass(J)[c-1],d.GC$(a).addClass(J))},b.prototype.wXa=function(){var a,b=d.GC$("."+$,this.JXa[0]),c=this.kzb(b);c===b.length-1?this.mXa():(a=b.removeClass(J)[c+1],d.GC$(a).addClass(J))},b}(rb),b.ZWa=wb,xb=function(a){Bb(b,a);function b(b,c,d,e){var f,g=this,h=c.parent;return g=a.call(this,b,r.vl(h&&h.qo),!0)||this,f=g,f.kj=c,f.lYa=f.Cj+"_OK",f.mYa=f.Cj+"_Cancel",f.nYa=f.Cj+"_Close",f.HXa=d,f.xr=d.rowFilter,f.EXa=e,f.xo=f.yo(),g}return b.prototype.OT=function(){},b.prototype.Fva=function(){this.Ao(),this.yl(),this.oYa(),this.BPa()},b.prototype.yl=function(){},b.prototype.BPa=function(){var a=this,b=a.xo.width(),c=a.xo.height(),d=a.AWa.width(),e=a.AWa.height();this.xo.css([B,u,v],["fixed",(d-b)/2+"px",(e-c)/2+"px"])},b.prototype.oYa=function(){var a=this;d.GC$("#"+a.lYa).bind(Ra,function(){var b=a.pYa();b&&a.close()}),d.GC$("#"+a.mYa).bind(Ra,function(){a.qYa(),a.close()}),d.GC$("#"+a.nYa).bind(Ra,function(){a.qYa(),a.close()}).bind(Sa,function(){this.style.backgroundColor=Wa,this.style.color=Xa}).bind(Ta,function(){this.style.backgroundColor=Va,this.style.color=Va}),d.GC$("#"+a.lYa+", #"+a.mYa).bind(Sa,function(){d.GC$(this).addClass(K)}).bind(Ta,function(){d.GC$(this).removeClass(K)}),d.GC$(document).bind(Ua,function(){a.BPa()})},b.prototype.PXa=function(a){var b=this.HXa,c=b.col,d=this.kj,e=b.rowFilter;Db(c,a,d,e)},b.prototype.pYa=function(){return!0},b.prototype.qYa=function(){},b}(d.Go),b.rYa=xb,yb=function(a){Bb(b,a);function b(b,c,d,e){var f=a.call(this,b,c,d,e)||this,g=f;return g.Vo=230,g._v=140,g.HXa=d,g.xr=g.HXa.rowFilter,g.EXa=e,g.sYa=g.tYa(),g.Fva(),f}return b.prototype.tYa=function(){var a,b,c={rank:0,item:10},d=this,e=d.xr.rZ[d.HXa.col];return e&&e.length>0&&8===e[0].conType()&&(a=e[0].type(),b=e[0].expected(),c={rank:a,item:b}),c},b.prototype.yl=function(){var a=this.uYa();this.yo().html(a),this.setStyle(),this.LXa()},b.prototype.vYa=function(a){1===a.value.length?a.value=a.value.replace(/[^1-9]/g,""):a.value=a.value.replace(/\D/g,"")},b.prototype.LXa=function(){var a=this;d.GC$(oa).bind("keyup",function(){d.GC$(this).removeClass(va),a.vYa(this)}).bind("paste",function(){d.GC$(this).removeClass(va),a.vYa(this)})},b.prototype.pYa=function(){var a=this,b=parseInt(d.GC$("#top10-rank").val(),10),c=parseInt(d.GC$(oa).val(),10),e=new f.Condition(8),g=!1;return c?(g=!0,e.type(b),e.expected(c),a.PXa(e)):d.GC$(oa).addClass(va).focus(),g},b.prototype.uYa=function(){var a="",b="";return b=1===this.sYa.rank?'<select id="top10-rank"><option value="0">'+qb().top+'</option><option value="1" selected = "selected">'+qb().bottom+"</option></select>":'<select id="top10-rank"><option value="0" selected="selected">'+qb().top+'</option><option value="1">'+qb().bottom+"</option></select>",a+='<div class="gc-popup ui-widget '+A+'"><div class="ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix"><span class="gc-filter-dialog-title">'+qb().Top10Filter+'</span><span id="'+this.nYa+'" class="gc-filter-dialog-close">\xd7</span></div><div id="top10-desc"><div class="top10-show-text">'+qb().Show+'</div><div class="ui-dialog-hr"></div></div><div class="top10-wrap">'+b+'<input id="top10-item" class="gc-filter-top10-rank" type="text"  value="'+this.sYa.item+'" /></div><div class="top10-footer top10-wrap"><input type="button" id="'+this.lYa+'" value="'+qb().OK+'"><input id="'+this.mYa+'" type="button" class="gc-filter-dialog-close" value="'+qb().Cancel+'"/></div></div>'},b.prototype.setStyle=function(){d.GC$(".gc-popup",this.xo[0]).css([Ya,Na,Oa],[Za,this.Vo,this._v]),d.GC$(".ui-dialog-titlebar",this.xo[0]).css({height:"34px",background:"white",fontSize:"12px",fontWeight:"normal",color:"#222",border:"none",borderRadius:"0"}),d.GC$(".gc-filter-dialog-title",this.xo[0]).css({float:"left",height:"34px",lineHeight:"34px",display:ma,marginLeft:"15px"}),d.GC$(".top10-show-text",this.xo[0]).css({float:"left"}),d.GC$(".ui-dialog-hr",this.xo[0]).css({position:"absolute",left:"24px",right:"0",margin:"0 5px",height:"12px",borderBottom:wa,borderWidth:"thin"}),d.GC$("#top10-desc",this.xo[0]).css({height:"24px",lineHeight:"24px",marginLeft:"15px",position:x,fontSize:"10px"}),d.GC$("#"+this.nYa,this.xo[0]).css({float:"right",fontSize:"18px",width:"36px",height:"34px",lineHeight:"34px",textAlign:"center"}),d.GC$("#top10-rank",this.xo[0]).css({width:"120px",marginRight:"20px",height:"21px",textIndent:"5px",fontSize:"inherit",padding:"0",marginBottom:0}),d.GC$(oa,this.xo[0]).css({width:"60px",textIndent:"5px",padding:0,height:"21px",margin:0,fontSize:"inherit",display:ma}),d.GC$(".top10-wrap",this.xo[0]).css({padding:"0px 12px",textAlign:"right",color:"#9B9C9C",fontSize:"13px"}),d.GC$(".top10-footer",this.xo[0]).css({padding:"20px 12px 12px"}),d.GC$("#"+this.lYa+", #"+this.mYa).css({width:"75px",height:"23px",lineHeight:"21px",fontSize:"12px",backgroundColor:"#E1E1E1",border:ua,padding:0,display:ma}),d.GC$("#"+this.lYa,this.xo[0]).css({marginRight:"12px"})},b}(xb),zb=function(a){Bb(b,a);function b(b,c,d,e,f,g,h){var i=a.call(this,b,c,d,e)||this,j=i;return j.Vo=416,j._v=100,j.wYa=36,j.xYa=24,j.yYa=8,j.zYa=7,j.EXa=e,j.AYa=f,j.Xba=g?0:1,h?j.BYa=Cb(h):j.BYa=h,j.CYa=o,j.DYa=o,j.Fva(),i}return b.prototype.yl=function(){var a,b,c=this.EYa();this.xo.append(c),a=d.GC$("#color-spread-wrap",this.xo[0])[0],this.e$a=this.f$a(),b=this.FYa(a),this.GYa(a,b),this.LXa(this.CYa,this.DYa)},b.prototype.f$a=function(){return ga},b.prototype.FYa=function(a){var b=this.AYa.colorNumber,c,e,f=!1;return e=this.wYa*this.zYa+4,Math.ceil(b/this.zYa)>this.yYa?(c=this.xYa*this.zYa,f=!0,e+=20):c=this.xYa*Math.ceil(b/this.zYa),d.GC$(a).height(c),d.GC$(a).width(e),f},b.prototype.HYa=function(a,b,c){var d,e,f;a.suspendPaint(),d=a.options,e=b.options,d.showHorizontalScrollbar=!1,d.allowContextMenu=!1,d.showVerticalScrollbar=c,d.scrollbarMaxAlign=!0,d.tabStripVisible=!1,d.allowUserResize=!1,d.allowUserDragDrop=!1,d.allowUserDragFill=!1,
d.allowUserZoom=!1,d.grayAreaBackColor="#fff",e.colHeaderVisible=!1,e.rowHeaderVisible=!1,e.selectionBackColor="transparent",e.selectionBorderColor="transparent",e.gridline={showVerticalGridline:!1,showHorizontalGridline:!1},e.isProtected=!0,b.setColumnCount(this.zYa+2),f=this.AYa.colorNumber,b.setRowCount(Math.ceil(f/this.zYa)+1),b.defaults.rowHeight=21,b.defaults.colWidth=36,b.setColumnWidth(0,2),b.setColumnWidth(this.zYa+1,2),b.setRowHeight(0,2),a.resumePaint()},b.prototype.IYa=function(a,b){var c,d,e,f,g;b.suspendPaint(),c=(this.zYa-this.AYa.colorNumber)*this.wYa/2,b.setColumnWidth(0,c);for(g in this.AYa.data)this.AYa.data.hasOwnProperty(g)&&(f=this.JYa(d,e),d=f.row,e=f.col,this.Bba(d,e,g,b));b.resumePaint()},b.prototype.Bba=function(a,b,c,d){var e=new h.Button;e.buttonBackColor(c),e.marginLeft(3),e.marginTop(3),e.marginRight(3),e.marginBottom(3),d.setCellType(a,b,e,3),c===this.BYa&&this.g$a(d,a,b,this.e$a)},b.prototype.JYa=function(a,b){return a===p&&b===p?(a=1,b=1):b<this.zYa?b+=1:(a+=1,b=1),{row:a,col:b}},b.prototype.KYa=function(a,b){var c,d,e,f;b.suspendPaint(),c=p,d=p;for(f in this.AYa.data)this.AYa.data.hasOwnProperty(f)&&(e=this.JYa(c,d),c=e.row,d=e.col,this.Bba(c,d,f,b));b.resumePaint()},b.prototype.g$a=function(a,b,c,e){var f,g;a.suspendPaint(),this.h$a&&(f=a.getStyle(this.h$a.row,this.h$a.col,3),f.backColor="#ffffff",f.borderLeft=new d.LineBorder(ha,d.LineStyle.empty),f.borderTop=new d.LineBorder(ha,d.LineStyle.empty),f.borderRight=new d.LineBorder(ha,d.LineStyle.empty),f.borderBottom=new d.LineBorder(ha,d.LineStyle.empty),a.setStyle(this.h$a.row,this.h$a.col,f,3)),this.h$a={row:b,col:c},g=a.getStyle(b,c,3),g.backColor=e,g.borderLeft=new d.LineBorder(ha,d.LineStyle.thin),g.borderTop=new d.LineBorder(ha,d.LineStyle.thin),g.borderRight=new d.LineBorder(ha,d.LineStyle.thin),g.borderBottom=new d.LineBorder(ha,d.LineStyle.thin),a.setStyle(b,c,g,3),a.resumePaint()},b.prototype.GYa=function(a,b){var c=new d.Workbook(a),e=c.getActiveSheet();this.CYa=c,this.DYa=e,this.HYa(c,e,b),this.AYa.colorNumber<this.zYa?this.IYa(c,e):this.KYa(c,e)},b.prototype.B_a=function(){var a,b=p;for(a in this.AYa.data)if(this.AYa.data.hasOwnProperty(a)){b=a;break}return b},b.prototype.EYa=function(){var a,b,c,e,f,g,h=d.GC$(q("div")).addClass("gc-popup ui-widget "+ca+" "+A+" "+z);return h.css({backgroundColor:"#f0f0f0"}),a=this.LYa(),h.append(a),b=this.MYa(),h.append(b),c=d.GC$(q("div")).css([Pa],["0 12px"]),e=this.NYa(),e!==p&&c.append(e),f=this.OYa(),c.append(f),g=this.PYa(),c.append(g),h.append(c),h.append(this.QYa()),h},b.prototype.LYa=function(){var a=d.GC$(q("div")),b=d.GC$(q("span")),c=d.GC$(q("span")).attr("id",this.nYa);return a.css([Na,Oa,Ya,Pa,La],["100%","34px","#fff","0 0 0 12px",ka]),b.css(["float",Oa,"lineHeight",Ka],[u,"34px","34px",ma]).html(qb().ColorTitle),c.css(["float",Oa,"lineHeight",Ka,cb,Na,"textAlign"],["right","34px","34px",ma,"18px","36px","center"]).html("\xd7").addClass(ta),a.append(b),a.append(c),a},b.prototype.MYa=function(){var a=d.GC$(q("div")),b=d.GC$(q("div")),c;return 0===this.Xba&&this.EXa===T?c=qb().SortCell:0===this.Xba&&this.EXa===U?c=qb().FilterCell:1===this.Xba&&this.EXa===T?c=qb().SortFont:1===this.Xba&&this.EXa===U&&(c=qb().FilterFont),a.css(Pa,"0 12px"),b.css([Oa,"lineHeight"],["24px","24px"]).html(c),a.append(b),a},b.prototype.NYa=function(){var a,b,c,e=p;return this.AYa[V]!==p&&(a=""===this.BYa,b=1===this.Xba?qb().Automatic:qb().NoFill,c=d.GC$(q("div")).html(b),c.attr("id","gc-color-dialog-noFill"),a?(c.attr(sa,"checked"),c.css([Ya,Pa,Qa,"textAlign","margin"],["rgb(134,191,160)","0 12px",ra,"center","5px 0"])):c.css([Ya,Pa,Qa,"textAlign","margin"],["transparent","0 12px",ra,"center","5px 0"]),e=c),e},b.prototype.OYa=function(){var a=d.GC$(q("div")).attr("id","color-spread-wrap");return a.css([Ya,Pa,Qa,Na,"box-sizing"],["#fff","6px","1px solid rgb(160,160,160)","288px",na]),a},b.prototype.PYa=function(){var a,b=this,c=d.GC$(q("div")),e=d.GC$(q("div")).html(qb().Selected),f=d.GC$(q("div")).attr("id","gc-color-dialog-selected");return c.css([B,Pa,La,$a,Oa],[x,"0 12px",na,"12px 0","16px"]),e.css([w,Na,"text-align"],[u,"60px","center"]),a=1===b.Xba?ia:ja,b.BYa===p&&(b.BYa=b.B_a()),""!==b.BYa&&(a=b.BYa),f.css([B,u,"right",Oa,_a,Qa,Ya],["absolute","70px","0","16px","10px",ra,a]),c.append(e),c.append(f),c},b.prototype.pYa=function(){var a,b=d.GC$(qa);return a=1===b.length&&"checked"===b.attr(sa)?"":d.GC$(pa).css(Ya),this.RYa(a),!0},b.prototype.RYa=function(a){var b=0===this.Xba;this.EXa===T?this.SYa(a,b):this.EXa===U&&this.TYa(a,b)},b.prototype.SYa=function(a,b,c){var e,f,g=this,h=g.HXa,i=h.rowFilter,j=g.kj;i&&j&&j.hK()&&(""!==a&&(a=Cb(a)),c=c!==p&&c,e=h.col,f={sheet:j,sheetName:j.Cj,col:e,ascending:c,color:a,isBackColor:b,cancel:!1},j.Wq(d.Events.RangeSorting,f),f.cancel===!1&&(j.suspendPaint(),i.sortColumnByColor(e,c,a,b),j.resumePaint(),j.Wq(d.Events.RangeSorted,f)))},b.prototype.TYa=function(a,b){var c=new f.Condition(3),d=b?0:1;c.compareType(d),c.expected(a),this.PXa(c)},b.prototype.LXa=function(a,b){var c=this;d.GC$(qa).bind(Ra,function(){var a=1===c.Xba?ia:ja;d.GC$(pa).css(Ya,a),d.GC$(this).css(Ya,"rgb(134,191,160)"),d.GC$(this).attr(sa,"checked")}),a.bind(d.Events.ButtonClicked,function(a,e){var f,g=e.row,h=e.col,i=b.getCellType(g,h);c.g$a(b,g,h,c.e$a),f=i.buttonBackColor(),d.GC$(pa).css(Ya,f),d.GC$(qa).css(Ya,"transparent").attr(sa,"")}),a.bind(d.Events.TouchToolStripOpening,function(a,b){b.handled=!0})},b.prototype.QYa=function(){var a=d.GC$(q("div")),b=d.GC$(q("input")).attr({type:"button",value:qb().OK,id:this.lYa}),c=d.GC$(q("input")).attr({type:"button",value:qb().Cancel,id:this.mYa}).addClass(ta),e={width:"75px",height:"23px",lineHeight:"21px",fontSize:"12px",backgroundColor:"#E1E1E1",border:ua,padding:"0",display:ma,margin:"0"};return a.addClass("color-custom-footer").css({textAlign:"right",padding:"10px 12px 12px"}),b.css(e).css({marginRight:"12px"}),c.css(e),a.append(b[0]),a.append(c[0]),a},b}(xb),Ab=function(a){Bb(b,a);function b(b,c,d,e,f,g,h,i,j){var k=a.call(this,b,c,d,e)||this,l=k;return l.Vo=840,l._v=260,l.HXa=d,l.xr=l.HXa.rowFilter,l.EXa=e,l.UYa=g,l.fY=f,l.VYa=h,l.WYa=j,l.XYa=i,l.YYa(g),l.Fva(),k}return b.prototype.yl=function(){var a=this,b=a.ZYa(this.sYa);a.xo.append(b),d.GC$(".gc-popup",this.xo[0]).css([Ya,Na,Oa,cb],[Za,this.Vo,this._v,"12px"]),a.LXa()},b.prototype.LXa=function(){var a=this;d.GC$(".filter-custom-select-input").bind("change",function(){var a=d.GC$(this).find("option")[this.selectedIndex];d.GC$(this).parent().find("input").val(d.GC$(a).text()).attr("gc-data",d.GC$(this).val())}),d.GC$(".filter-custom-select-first-option").bind("click",function(a){a.stopPropagation(),a.preventDefault(),d.GC$(this).parent().trigger("change")}),d.GC$("#first-condition-expected input, #sec-condition-expected input").bind("change",function(){var b=d.GC$(this).val(),c=-1;a.fY.forEach(function(a,d){a.text===b&&(c=d)}),d.GC$(this).attr("gc-data",c)})},b.prototype.F_a=function(a,b,c){var d,e,f,g=a.val();return 0===c&&2===b?g:(e=a.attr("gc-data"),f=this.fY[e],d=f?f.value:11!==b||isNaN(+g)?g:parseFloat(g))},b.prototype.pYa=function(){var a=this,b=parseInt(d.GC$("#filter-logical-wrap input:checked").val(),10),c=a.$Ya(b),e=!1;return c?(a.PXa(c),e=!0):d.GC$("#first-condition-type").focus(),e},b.prototype.$V=function(a,b){var c,d,e,g,h=a.val().trim();return h?(d=parseInt(h.split("-")[0],10),c=parseInt(h.split("-")[1],10),e=this.F_a(b,c,d),g=new f.Condition(c),this.r$a(e)&&g.conType(2),g.compareType(d),"string"===l(e)&&(e=n(e)),g.expected(e),g.treatNullValueAsZero(!1),g.ignoreCase(!0),"string"===l(e)&&e.indexOf("*")===-1&&e.indexOf("?")===-1?g.useWildCards(!1):"string"!==l(e)||e.indexOf("*")===-1&&e.indexOf("?")===-1||(g.conType(2),g.useWildCards(!0)),g):o},b.prototype.$Ya=function(a){var b,c=this.$V(d.GC$("#first-condition-type"),d.GC$("#first-condition-expected input")),e=this.$V(d.GC$("#sec-condition-type"),d.GC$("#sec-condition-expected input"));return e?(b=new f.Condition(0),b.compareType(a),b.item1(c),b.item2(e)):b=c,b},b.prototype.b$a=function(){var a=this.HXa,b=a.rowFilter,c=this.kj.getText(a.row,a.col);return b&&b.range.row<=0&&(c=this.kj.getText(a.row,a.col,a.sheetArea)),c},b.prototype.ZYa=function(a){var b,c,e,f,g,h=this.b$a(),i=d.GC$(q("div")).addClass("gc-popup ui-widget "+A);return i.css({backgroundColor:"#f0f0f0"}),i.append(this._Ya()),i.append(this.aZa(h)),b=this.bZa("first-condition-type","first-condition-expected",a.compareType1||0,a.expected1,a.conType1,!0),i.append(b),i.append(this.cZa(a.logic)),c=this.bZa("sec-condition-type","sec-condition-expected",a.compareType2,a.expected2,a.conType2),i.append(c),c.css([B,Pa],[x,"0 0 12px 35px"]),e=d.GC$(q("div")),f=d.GC$(q("p")).html(qb().Explain1).css({margin:"0"}),g=d.GC$(q("p")).html(qb().Explain2).css({margin:"0"}),e.append(f),e.append(g),e.css({fontSize:"10px",textIndent:"12px"}),i.append(e),i.append(this.dZa()),i},b.prototype.bZa=function(a,b,c,e,f,g){var h=d.GC$(q("div"));return h.css([B,Pa],[x,"0 0 12px 35px"]),h.append(this.eZa(c,f,g).attr("id",a)),h.append(this.fZa(this.fY,e).attr("id",b)),h},b.prototype._Ya=function(){var a=d.GC$(q("div")),b=d.GC$(q("span")),c=d.GC$(q("span")).attr("id",this.nYa);return a.css([Na,Oa,Ya,Pa,La],["100%","34px","#fff","0 0 0 12px",ka]),b.css(["float",Oa,"lineHeight",Ka],[u,"34px","34px",ma]).html(qb().CustomTitle),c.css(["float",Oa,"lineHeight",Ka,cb,Na,"textAlign"],["right","34px","34px",ma,"18px","36px","center"]).html("\xd7").addClass(ta),a.append(b),a.append(c),a},b.prototype.cZa=function(a){var b=d.GC$(q("div")),c=d.GC$(q("input")).attr({name:"filter-logical",id:"filter-dialog-and",type:"radio"}).val(1).css([Na,Ka],["unset",ma]),e=d.GC$(q("input")).attr({name:"filter-logical",id:"filter-dialog-or",type:"radio"}).val(0).css([Na,Ka],["unset",ma]),f=d.GC$(q("label")).attr({for:"filter-dialog-and"}).html(qb().And).css([bb,"font-weight",cb,Ka,"margin-bottom",Pa],["10px","inherit","13px",ma,"0","0"]),g=d.GC$(q("label")).attr({for:"filter-dialog-or"}).html(qb().Or).css([bb,"font-weight",cb,Ka,"margin-bottom",Pa],["10px","inherit","13px",ma,"0","0"]);return b.attr("id","filter-logical-wrap"),b.css({padding:"0 0 0 40px",height:"30px"}),0===a?(e.prop("checked",!0),c.prop("checked",!1)):(e.prop("checked",!1),c.prop("checked",!0)),b.append(c),b.append(f),b.append(e),b.append(g),b},b.prototype.aZa=function(a){var b,c,e,f=d.GC$(q("div")),g=d.GC$(q("div")).html(qb().ShowRows);return f.css(Pa,"0 12px"),g.css([Oa,"lineHeight"],["24px","24px"]),f.append(g),b=d.GC$(q("div")),c=d.GC$(q("div")),a?(e=d.GC$(q("div")).html(m(a)),e.css([w,"maxWidth","textOverflow","whiteSpace","overflow"],[u,"300px","ellipsis","nowrap","hidden"]),b.css({height:"24px",lineHeight:"24px",marginLeft:"15px",fontSize:"10px"}),c.css({overflow:"hidden",margin:"0 5px",height:"12px",borderBottom:wa,borderWidth:"thin"}),b.append(e)):(b.css({height:"12px",lineHeight:"2px",marginLeft:"15px",fontSize:"10px"}),c.css({margin:"0 5px",height:"2px",borderBottom:wa,borderWidth:"thin"})),b.append(c),f.append(b),f},b.prototype.eZa=function(a,b,c){var e,f,g,h,i,j,k,l=[],m=!1;for(l=6===this.VYa||5===this.VYa?ob:1===this.VYa?mb:nb,e=d.GC$(q("select")),f={height:"25px",width:"230px",marginBottom:0,marginRight:"12px",fontSize:"inherit",padding:0,boxSizing:"border-box"},e.css(f),g="<option>&nbsp;</option>",j=!1,a===p&&(g='<option selected="selected" >&nbsp;</option>'),k=0;k<l.length;k++)h=l[k].split("-"),i=!1,h[2]&&(j=!1,(b===parseInt(h[2],10)||6===this.WYa&&5===parseInt(h[2],10)||11===parseInt(h[2],10))&&(j=!0),j&&a===parseInt(h[1],10)&&(i=!0)),i?(m=!0,g+='<option selected="selected" value="'+h[1]+"-"+h[2]+'">'+qb()[h[0]]+"</option>"):g+='<option value="'+h[1]+"-"+h[2]+'">'+qb()[h[0]]+"</option>";return e[0].innerHTML=g,d.GC$("option",e[0]).css({height:"20px"}),!m&&c&&d.GC$(d.GC$("option",e[0])[1]).attr("selected","selected"),e},b.prototype.I$a=function(a){var b=m(a);return b&&b.length>100&&(b=b.substr(0,100)+"..."),b},b.prototype.fZa=function(a,b){var c,e,f,g,h,i=-1,m=b,n=d.GC$(q("div")),p=d.GC$(q("select")).addClass("filter-custom-select-input"),r=d.GC$(q("input")),s=qb().Blanks,t={height:"25px",width:"540px",margin:0,padding:0};for(n.css([B,u,"top"],["absolute","278px","0"]),r.css([B,u,v,Na,"textIndent",Qa,Oa,La,"outline",Pa,$a],["absolute","2px","2px","518px","5px",0,"21px",ka,Ma,0,0]),p.css(t),e="",f=!0,g=0;g<a.length;g++)a[g].text===s&&a[g].value===o||(h=this.I$a(a[g].text),"date"===l(a[g].value)&&(c||(c=j.q4(j.culture()).DateTimeFormat),h=k.Vb(a[g].value,c.shortDatePattern)),a[g].value+""==b+""&&(i=g,m=h),f?(e+='<option class="filter-custom-select-first-option" value="'+g+'">'+h+"</option>",f=!f):e+='<option value="'+g+'">'+h+"</option>");return p[0].innerHTML=e,p[0].selectedIndex=-1,"date"===l(m)&&(c||(c=j.q4(j.culture()).DateTimeFormat),m=k.Vb(m,c.shortDatePattern)),r.attr("gc-data",i).val(m),n.append(p),n.append(r),n},b.prototype.dZa=function(){var a=d.GC$(q("div")),b=d.GC$(q("input")).attr({type:"button",value:qb().OK,id:this.lYa}),c=d.GC$(q("input")).attr({type:"button",value:qb().Cancel,id:this.mYa}).addClass(ta),e={width:"75px",height:"23px",lineHeight:"21px",fontSize:"12px",backgroundColor:"#E1E1E1",border:ua,padding:0,display:ma};return a.addClass("custom-footer").css({textAlign:"right",padding:"10px 12px 12px"}),b.css(e).css({marginRight:"12px"}),c.css(e),a.append(b[0]),a.append(c[0]),a},b.prototype.YYa=function(a){var b=this,c=b.xr.rZ[b.HXa.col],d={conType1:2,conType2:p,compareType1:0,compareType2:p,expected1:p,expected2:p,logic:1};c=c&&c.length?c[0]:o,b.sYa=b.gZa(d,a,c)},b.prototype._Za=function(a,b){var c,d,e=j.q4(j.culture()).DateTimeFormat,f={},g=a.expectTypeId();if(0===g)f=a.getExConditionDateScope(a.expected());else if(6===g)c=new Date,d=new Date,c.setMonth(0,1),c.setHours(0,0,0,0),d.setHours(23,59,59,59),f={from:c,to:d};else if(3===g)return b.conType1=this.WYa,b.compareType1=this.XYa,b.expected1=p,b;return b.conType1=5,b.conType2=5,b.compareType1=5,b.compareType2=2,b.expected1=k.Vb(f.from,e.shortDatePattern),b.expected2=k.Vb(f.to,e.shortDatePattern),b},b.prototype.gZa=function(a,b,c){var d,e,f,g,h,i=c&&(c.conType()===this.VYa||11===c.conType()&&(1===this.VYa||2===this.VYa)),j=c&&(c.compareType()===this.XYa||0===c.conType());return!c||1!==b||6!==c.conType()||0!==c.expectTypeId()&&6!==c.expectTypeId()&&3!==c.expectTypeId()?(d=!0,1===this.WYa&&c&&0===c.conType()&&(e=c.item1().compareType(),f=c.item2().compareType(),5===Math.max(e,f)&&3===Math.min(e,f)||(d=!1)),1===b||i&&j&&d?c&&0===c.conType()?(this.VYa=this.WYa,a.conType1=c.item1().conType(),a.conType2=c.item2().conType(),a.compareType1=c.item1().compareType(),a.compareType2=c.item2().compareType(),a.expected1=c.item1().expected(),a.expected2=c.item2().expected(),a.logic=c.compareType()):c&&0!==c.conType()&&3!==c.conType()&&(g=c.compareType(),h=c.conType(),a.conType1=h,a.expected1=c.expected(),a.compareType1=g):0===this.VYa?(this.VYa=this.WYa,1===this.WYa?(a.conType1=1,a.conType2=1,a.compareType1=3,a.compareType2=5):5===this.WYa&&(a.conType1=5,a.compareType1=5,a.conType2=5,a.compareType2=3)):1===this.VYa&&this.s$a(c)?(a.conType1=this.WYa,a.compareType1=this.XYa,a.expected1=c.expected()):(a.conType1=this.WYa,a.compareType1=this.XYa,a.expected1=p),a):this._Za(c,a)},b.prototype.r$a=function(a){return a!==p&&"TRUE"===a||"FALSE"===a},b.prototype.s$a=function(a){if(!a)return!1;var b=!1,c=a.expected();return 1===a.conType()&&this.r$a(c)&&(b=!0),b},b}(xb)},"./dist/plugins/filter/filter-ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/filter/filter.res.en.js");b.SR={en:d}},"./dist/plugins/filter/filter-ui.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa,Ta,Ua,Va,Wa,Xa,Ya,Za,$a,_a,ab,bb,cb=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("./dist/plugins/filter/filter-dialog.js"),g=c("./dist/plugins/filter/filter-ns.js"),h=d.Common.q,i=d.Formatter.lxb,j=e.Ul.Nl,k=e.Ul,l=k.nl,m=d.Common.k.ac,n=null,o=d.Common.j.Fa,p=parseFloat,q=e.GC$.inArray,r="gc-sortASC",s="gc-sortDES",t="gc-filterOK",u="gc-filterCancel",v="gc-filterSearch",w="gc-filterCheckAll",x="gc-filterUnCheckAll",y="gc-filter-item-container",z="gc-filter-hover",A="gc-filter-button",B="gc-filter-item-link ",C="gc-filter-sort",D="ui-state-hover ",E="gc-filter-button-hover "+D,F="gc-filter-item-hover",G=D+F,H=z+" form-control well "+G,I=" ui-corner-all ",J="ui-button-disabled",K=" ui-state-disabled",L="gc-filter-button-disable",M="gc-sub-color-filter",N="gc-sub-content-flter",O="gc-sub-color-sort",P="gc-no-user-select",Q=" gc-filter-check",R="gc-filter-check-outerDiv",S="gc-filter-button-active",T=".ui-filter-dialog",U="px",V="font-size",W="string",X="javascript:void(0)",Y="mouseout",Z="click",$="keydown",_="keyup",aa="search",ba="input",ca="span",da="div",ea="button",fa="DOMMouseScroll",ga="mousewheel",ha="mousedown",ia="mouseover",ja="mouseup",ka="margin",la=ka+"Left",ma=ka+"Top",na="padding",oa=na+"Top",pa="display",qa="none",ra="width",sa="height",ta="border",ua="left",va="top",wa="box-sizing",xa="content-box",ya="block",za=d.Common.j.Ba,Aa=d.Common.l.Xb,Ba="gc-sortASC-tr",Ca="gc-sortDEC-tr",Da="gc-sort-color-tr",Ea="clear_filter",Fa="gc-color-filter",Ga="gc-major_filter",Ha="gc-search-tr",Ia="gc-filter-function-tr",Ja=void 0,Ka="automatic",La="TextFilter",Ma="NumberFilter",Na="DateFilter",Oa="gc-filter-choose-area",Pa="filter-button-tr",Qa="gc-filter-dialog-style",Ra="gc-sortColor",Sa="gc-filter-color",Ta="gc-clearFilter",Ua="gc-majorFilter",Va="gc-filterDialog-rightArrow",Wa="gc-filter-disable-item",Xa=500,Ya="data:image/png;base64,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",function(a){a[a.true=1]="true",a[a.false=2]="false",a[a.undefined=2]="undefined",a[a.null=3]="null"}(Za||(Za={})),$a=new d.Common.ResourceManager(g.SR),_a=$a.getResource.bind($a),ab=function(){function a(a,b,c,d,e,f,g,h){var i=this;i.rowFilter=a,i.row=b,i.col=c,i.sheetArea=d,i.x=e,i.y=f,i.width=g,i.height=h}return a.prototype.ko=function(){var a=this,b=a.rowFilter,c=a.col,d=b.getSortState(c);return b?b.isFiltered(c)?[3,4,5][d]:d:0},a}(),b.eY=ab;function db(a,b){e.GC$(a).addClass(b)}function eb(a,b){return a=b.getColor(a),d.Common.pc.bc(d.Common.pc.ec(a))}function fb(a,b,c,d,e){if(a){var f=e[a];f===Ja&&(f=e[a]=eb(a,c)),b.data[f]===Ja&&b.colorNumber++,b.data[f]=d}else b[Ka]=Ka}function gb(a,b){e.GC$(a).removeClass(b)}function hb(a,b,c){a.css([ra,sa],[b+U,c+U])}function ib(a){e.GC$(a).css([ta,ka,na,"outline"],[0,0,0,qa])}function jb(a){return a.concat([])}function kb(a){return o(a)||""===a||"number"==typeof a&&isNaN(a)?"blank":za(a)}function lb(a){var b,c,d,e="",f=!1;for(b=0,c=a.length;b<c;b++)d=a[b],'"'===d&&(f=!f),"E"===d||f||(e+=d.toLowerCase());return e}function mb(a,b,c,d){var e,f,g,h,i,j;if(b&&a)return e=a.format(c),i=[],e&&(j=e.split("/"),f=j[0]||"",g=j[1]||"",h=j[2]||""),i.push({value:f,level:0,status:d}),i.push({value:g,level:1,status:d}),i.push({value:h,level:2,status:d}),{key:b,value:c,children:i,status:d}}function nb(a,b,c,d,e,f){if(b){var g=[];return g.push({value:b,level:a.outlineColumn&&a.outlineColumn.XQa(e)?a.getCell(d,e).textIndent():0,status:f}),{key:b,value:c,children:g,status:f}}}bb=function(a){cb(b,a);function b(b,c,d,e){var f,g=this,h=c.parent;return g=a.call(this,b,k.vl(h&&h.qo))||this,f=g,f.pxb={},f.fY=[],f.hY=[],f.gY=[],f.CWa=[],f.a$a=[],f.DWa={Number:0,Text:0,Date:0},f.EWa="",f.FWa=n,f.sheet=c,f.filterButtonInfo=d,f.lY=237,f.mY=410,f.U$a=26,f.nY=191,f.oY=150,f.lzb=1.2,f.rEscape=/[\-\[\]{}()+.,\\\^$|#\s]/g,f.elementList=[],f.activeItemIndex=n,f.xo=f.yo(),f.GWa=f.HWa(),f.IWa=f.JWa(),f.V$a=e,f.W$a=f.X$a(),f.wY(),f.xY(),g}return b.prototype.X$a=function(){var a=this,b=[],c=this.V$a,d=a.mY,e=a.U$a;return c.sortByValue?b=b.concat([1,1]):(b=b.concat([0,0]),d-=2*e),c.sortByColor?b=b.concat([1]):(b=b.concat([0]),d-=e),c.filterByValue===!0||c.filterByColor===!0?b=b.concat([1]):(b=b.concat([0]),d-=e),c.filterByColor===!0?b=b.concat([1]):(b=b.concat([0]),d-=e),c.filterByValue===!0?b=b.concat([1]):(b=b.concat([0]),d-=e),c.listFilterArea===!0?b=b.concat([1,1,1]):(b=b.concat([0,0,0]),d-=254),a.mY=d,b},b.prototype.OT=function(){var a,b=this,c=b.sheet.parent,d=b.filterButtonInfo,f=b.xo;b.yY=!!c&&c.options.useTouchLayout,b.zY(d.col),b.itemList&&b.itemList.length>0&&b.AY(b.dataSource),b.KWa(),b.LWa(),1===e.GC$("#"+Ga).length&&(b.MWa=b.NWa(e.GC$("#"+Ga)[0].children[2]),b.MWa.OWa(b.EWa).ad()),1===e.GC$("#"+Fa).length&&(b.PWa=b.QWa(e.GC$("#"+Fa)[0].children[2],M),b.PWa.ad()),1===e.GC$("#"+Da).length&&(b.RWa=b.QWa(e.GC$("#"+Da)[0].children[2],O),b.RWa.ad()),f.hide(),b.Ao(function(){b.elementList.length>0&&e.GC$(b.elementList[0]).focus()}),b.BY(b.yY),a=d.x+d.width-f.width()-6,a<5&&(a=5),f.css([ua,va],[a,d.y+d.height]),b.Do(),b.QQ(),b.MY()},b.prototype.BY=function(a){var b,c,d,f,g,h,i,j,k,l,m,n,o,q,r,s,u,w,y=this;a&&(b=y.lY*y.lzb,c=y.mY*y.lzb,d=b-y.lY,f=c-y.mY,hb(y.xo,b,c),g=e.GC$("."+C),h=p(g.css(oa))+f/30,i=e.GC$("."+A),j=p(i.css(ra))+d/4,k=p(i.css(sa))+f/8,l=e.GC$("#"+t),m=p(l.css(la))+d/2,n=e.GC$("#"+v),o=p(n.css(ra))+d,q=p(n.css(sa))+f/8,r=e.GC$("."+R),s=p(r.css(ma))+f/10,u=e.GC$("#"+x),w=p(u.css(la))+d,g.css(na,h+U+" 6px"),hb(i,j,k),l.css(la,m+U),hb(n,o,q),r.css(ma,s+U),u.css(la,w+U))},b.prototype.close=function(){var b=this,c=b.sheet;a.prototype.close.call(b),e.GC$(b.CY).remove(),e.GC$(b.DY).remove(),c.GZ=null,c&&c.zt()},b.prototype.Y$a=function(){1===e.GC$("#"+Ba).length&&this.Z$a(),1===e.GC$("#"+Da).length&&this.$$a()},b.prototype.Z$a=function(){var a=this,c=a.elementList,d=a.EY(r,_a().SortAscending),f=a.EY(s,_a().SortDescending),g=e.GC$("#"+Ba)[0],h=e.GC$("#"+Ca)[0];e.GC$(g.children[0]).append(e.GC$(j("img")).attr("src",b.zW(6))),e.GC$(h.children[0]).append(e.GC$(j("img")).attr("src",b.zW(7))),e.GC$(g.children[1]).append(d),e.GC$(h.children[1]).append(f),c.push(g),c.push(h)},b.prototype.$$a=function(){var a=this,c=a.elementList,d=a.EY(Ra,_a().SortColor),f=e.GC$("#"+Da)[0],g=a.SWa(),h=this.TWa(g),i=e.GC$(j("img")).attr("src",b.zW(10));i.css([la],["7px"]).addClass(Va),e.GC$(f.children[0]).append(h),e.GC$(f.children[1]).append(d),e.GC$(f.children[2]).append(i),c.push(f)},b.prototype._$a=function(a){var c,d=this,f=d.elementList,g=d.EY(Sa,_a().FilterColor),h=e.GC$("#"+Fa)[0],i=e.GC$(j("img")).attr("src",b.zW(10));i.css([la],["7px"]).addClass(Va),c=this.TWa(a.isShowColorFilterSelect),e.GC$(h.children[0]).append(c),e.GC$(h.children[1]).append(g),e.GC$(h.children[2]).append(i),f.push(h)},b.prototype.a_a=function(a){var c,d,f=this,g=f.sheet,h=f.filterButtonInfo.col,i=f.TWa(a.isShowMajorCheck),k=e.GC$("#"+Ga)[0],l=g.outlineColumn&&g.outlineColumn.XQa(h);l&&e.GC$(k).addClass(Wa+K),e.GC$(k.children[0]).append(i),c=f.EY(Ua,""),c.appendTo(k.children[1]),d=e.GC$(j("img")).attr("src",b.zW(10)),d.css([la],["7px"]).addClass(Va),e.GC$(k.children[2]).append(d),f.elementList.push(k)},b.prototype.b_a=function(){var a,b,c,d,f=1===e.GC$("#"+Fa).length,g=1===e.GC$("#"+Ga).length;(f||g)&&(a=this,b=a.elementList,c=e.GC$("#"+Ea)[0],a.UWa(c),b.push(c),d=a.VWa(),f&&a._$a(d),g&&a.a_a(d))},b.prototype.c_a=function(){var a=this;a.V$a.listFilterArea&&(a.d_a(),a.e_a(),a.f_a())},b.prototype.f_a=function(){var a=A+" gc-filter-button-default ui-button ui-state-default"+I+"btn btn-default",b=e.GC$("#"+Pa)[0],c=j(ea),d=j(ea),f=e.GC$(c).text(_a().OK).css(V,"1em").attr("id",t),g=e.GC$(d).text(_a().Cancel).css(V,"1em").attr("id",u);db(c,a),db(d,a),e.GC$(b.children[1]).append(f).append(g),this.elementList.push(c),this.elementList.push(d)},b.prototype.e_a=function(){var a,c=this,d=c.VWa(),f=e.GC$("#"+Oa)[0],g=e.GC$(j("img")).attr("src",b.zW(11));g.css(["verticalAlign","margin-top"],["top","4px"]),d.isShowSelectCheck||g.css([pa],[qa]),a=c.itemList=e.GC$(j(da)).css([ra,sa,wa],[c.nY,c.oY,xa]),db(a[0],y),e.GC$(f.children[0]).append(g).css([pa],["inline-block"]),e.GC$(f.children[1]).append(a)},b.prototype.d_a=function(){var a,b,c,d=this,f=e.GC$(j(da)).css([ka,na],[0,0]),g=e.GC$(j(da)).appendTo(f),h=e.GC$("#"+Ha)[0],i=e.GC$(j(ba)).attr({type:aa,placeholder:_a().Search,id:v}).appendTo(g);db(f[0],"gc-search-outer-div ui-widget-header"+I+"ui-multiselect-header ui-helper-clearfix"),d.elementList.push(i[0]),a=e.GC$(j(da)).appendTo(f),b=d.FY(w,_a().CheckAll,"gc-check-image ui-icon ui-icon-check"),c=d.FY(x,_a().UncheckAll,"gc-uncheck-image ui-icon ui-icon-closethick"),db(a[0],R),a.append(b).append(c),e.GC$(h.children[1]).append(f)},b.prototype.xY=function(){var a=this;a.Y$a(),a.b_a(),a.c_a()},b.prototype.NWa=function(a){var b=this,c=n;switch(this.EWa){case Ma:c=new f.WWa(a,b.xo,b.filterButtonInfo,b.sheet,b,N);break;case La:c=new f.XWa(a,b.xo,b.filterButtonInfo,b.sheet,b,N);break;case Na:c=new f.YWa(a,b.xo,b.filterButtonInfo,b.sheet,b,N)}return c},b.prototype.QWa=function(a,b){var c=this,d=n;return b===M?d=new f.ZWa(a,c.xo,c.filterButtonInfo,c.sheet,c,M):b===O&&(d=new f.ZWa(a,c.xo,c.filterButtonInfo,c.sheet,c,O)),d},b.prototype.SWa=function(){var a=this,b=a.filterButtonInfo,c=b.rowFilter,d=!1,e=c.zZ;return e!==n&&e.color!==Ja&&b.col===e.index&&(d=!0),d},b.prototype.TWa=function(a){var c=e.GC$(j("div")),d=e.GC$(j("img")).attr("src",b.zW(11));return a||d.css([pa],[qa]),c.append(d),c},b.prototype.HWa=function(){var a=this,b=a.filterButtonInfo,c=b.rowFilter,d=b.col,e=a.sheet.getText(a.filterButtonInfo.row,d,b.sheetArea);return c&&c.range.row<=0?e=a.sheet.getText(b.row,d,b.sheetArea):e||(e=a.sheet.getText(0,d,1)),""===e&&1===a.sheet.options.colHeaderAutoText?e="(Column "+(d+1)+")":""===e&&(e="(Column "+k.xq(d+1)+")"),e='"'+e},b.prototype.UWa=function(a){var c=this,d;c.IWa?(d=b.zW(12),e.GC$(a).addClass("gc-filter-item")):(d=b.zW(13),e.GC$(a).addClass(Wa+K)),e.GC$(a.children[0]).append(e.GC$(j("img")).attr("src",d).css([ra],["18px"])),c.v$a(Ta,c.GWa,e.GC$(a.children[1]))},b.prototype.i$a=function(a){var b=a.conType();return 0===b&&0===a.item1().compareType()&&0===a.item2().compareType()&&5===a.item1().conType()&&5===a.item2().conType()},b.prototype.VWa=function(){var a,b,c,d=this,e=!1,f=!1,g=!1,h=d.filterButtonInfo,i=h.col,j=h.rowFilter,k=j.rZ[i];return k&&k.length>0&&(k=k[0],a=k.conType(),b=k.compareType(),0===b&&3!==a&&d.V$a.listFilterArea?g=!0:0!==b||3===a||d.V$a.listFilterArea?0!==b&&3!==a?e=!0:3===a&&(f=!0):e=!0,c=this.i$a(k),(5===a&&0===b||c)&&(g=!1,e=!0)),{isShowColorFilterSelect:f,isShowMajorCheck:e,isShowSelectCheck:g}},b.prototype.JWa=function(){var a=this,b=!1,c=a.filterButtonInfo,d=c.col,e=c.rowFilter,f=e.rZ[d];return f&&f.length>0&&(b=!0),b},b.prototype.$Wa=function(){var a=Math.max(this.DWa.Date,this.DWa.Number,this.DWa.Text);return a===this.DWa.Text?La:a===this.DWa.Number?Ma:a===this.DWa.Date?Na:void 0},b.prototype.KWa=function(){var a,b,c=this.$Wa();this.EWa=c,a=_a()[c],b=e.GC$("#"+Ua),b.text(a)},b.prototype.WW=function(){var a,b,c=this,d=c.filterButtonInfo,e=this.sheet,f=d.col,g=d.rowFilter;g&&(a=g.sj?g.sj.name():Ja,b={tableName:a,colIndex:f},e.wu().execute({cmd:"clearFilter",sheetName:e.name(),cmdOption:b}),c.close())},b.prototype.LWa=function(){var a,b=!1,c=this._Wa,d=this.aXa,e=c.colorNumber,f=d.colorNumber;c.automatic&&e++,d.automatic&&f++,(e!==Ja&&e>1||f!==Ja&&f>1)&&(b=!0),a=this.VWa(),b||(!a.isShowColorFilterSelect&&this.bXa(Fa),!this.SWa()&&this.bXa(Da))},b.prototype.bXa=function(a){var c=e.GC$("#"+a).addClass(Wa+K);c.find("."+Va).attr("src",b.zW(14))},b.prototype.wY=function(){var a,b,c,d,f,g,h,i=this,k=i.xo,l=" gc-sort-container ui-menu-item",m=i.table=e.GC$(j("table")).css([na,pa],[0,"table"]).attr({cellspacing:0,cellpadding:0});for(db(m[0],"gc-layout-table"),c=[Ba,Ca,Da,Ea,Fa,Ga,Ha],d=i.W$a,a=0;a<9;a++)if(1===d[a])for(f=j("tr"),g=e.GC$(f).appendTo(m),a<6&&(e.GC$(f).attr("id",c[a]).css([pa,sa,V],["table-row","26px","9pt"]),e.GC$(f).addClass(Ia)),6===a&&e.GC$(f).attr("id",c[a]),7===a&&e.GC$(f).attr("tabindex",0).attr("id",Oa),8===a&&e.GC$(f).attr("id",Pa),ib(f),b=0;b<3;b++)h=j("td"),e.GC$(h).appendTo(g),ib(h),0===b?db(h,"gc-layout-table-first-column"):2===b?db(h,"gc-layout-table-last-column"):1===b&&(0===a?db(h,"gc-filter-sort-asc-container"+l+"gc-layout-table-middle-column"):1===a&&db(h,"gc-filter-sort-desc-container"+l+"gc-layout-table-middle-column"));
i.table.appendTo(k),k.css(["box-shadow",ra,sa,wa],["rgba(0, 0, 0, 0.15) 2px 4px 5px",i.lY+U,i.mY+U,xa]),db(k[0],Qa+" "+P),k.appendTo(i.zo())},b.prototype.QQ=function(){var a,b,c,d=this,f=!1,g=!1,h=e.GC$("."+A);e.GC$("#"+Ba).bind(ja,function(a){f&&(f=!1,d.JY(!0),d.close(),l(a))}).bind(ha,function(a){l(a),f=!0}),e.GC$("#"+Ca).bind(ja,function(a){g&&(g=!1,d.JY(!1),d.close(),l(a))}).bind(ha,function(a){l(a),g=!0}),e.GC$("."+Ia).bind(ia,function(){d.MWa&&e.GC$(this).attr("id")!==Ga&&d.MWa.cXa()&&(d.MWa.hT(),d.FWa=n,gb(e.GC$("#"+Ga)[0],H)),d.PWa&&e.GC$(this).attr("id")!==Fa&&d.PWa.cXa()&&(d.PWa.hT(),d.FWa=n,gb(e.GC$("#"+Fa)[0],H)),d.RWa&&e.GC$(this).attr("id")!==Da&&d.RWa.cXa()&&(d.RWa.hT(),d.FWa=n,gb(e.GC$("#"+Da)[0],H)),!e.GC$(this).hasClass(K.trim())&&e.GC$(this).index()<6&&db(this,H)}).bind(Y,function(){d.MWa&&e.GC$(this).attr("id")===Ga&&(clearTimeout(a),d.MWa.cXa())||d.PWa&&e.GC$(this).attr("id")===Fa&&(clearTimeout(b),d.PWa.cXa())||d.RWa&&e.GC$(this).attr("id")===Da&&(clearTimeout(c),d.RWa.cXa())||gb(this,H)}),e.GC$("#"+Ea).bind(Z,function(){d.IWa&&d.WW()}),e.GC$("#"+Ga).bind(ia,function(){e.GC$(this).hasClass(Wa)||(a=setTimeout(function(){d.FWa=d.MWa,d.FWa.Ao()},Xa))}),e.GC$("#"+Fa).bind(ia,function(){e.GC$(this).hasClass(Wa)||(b=setTimeout(function(){d.FWa=d.PWa,d.FWa.Ao()},Xa))}),e.GC$("#"+Da).bind(ia,function(){e.GC$(this).hasClass(Wa)||(c=setTimeout(function(){d.FWa=d.RWa,d.FWa.Ao()},Xa))}),e.GC$("#"+w).bind(ha,function(a){l(a)}).bind(ja,function(a){d.KY(a,!0)}),e.GC$("#"+x).bind(ha,function(a){l(a)}).bind(ja,function(a){d.KY(a,!1)}),e.GC$("#"+v).bind($,function(a){13===(a.which||a.keyCode)&&a.preventDefault()}).bind(_,function(a){q(a.keyCode,[9,37,38,39,40])<0&&(d.NY(),d.MY())}).bind(aa,function(a){d.NY(),d.MY()}),h.bind(ia,function(){db(this,E)}).bind(Y,function(){gb(this,E)}),h.bind(ha,function(){gb(this,E),db(this,S)}).bind(Y,function(){gb(this,S)}),e.GC$("#"+t).bind(Z,function(){d.dXa();var a=d.hY;d.sheet.suspendPaint(),d.PY(a),d.sheet.resumePaint(),d.close()}),e.GC$("#"+u).bind(Z,function(){d.close()}),d.xo.bind(ga,function(a){l(a)}).bind(fa,function(a){l(a)}).bind($+T,function(a){d.QY(a)}).bind(_+T,function(a){l(a)}),d.itemList&&d.itemList.length>0&&(d.hXa.bind(e.Events.TopRowChanged,function(a,b){d.fXa(b)}),d.hXa.bind(e.Events.OutlineColumnCheckStatusChanged,function(a,b){d.gXa({sheet:b.sheet,row:b.row,sheetArea:3,ignoreClick:!0})}),d.hXa.bind(e.Events.TouchToolStripOpening,function(a,b){b.handled=!0}))},b.prototype.fXa=function(a){var b,c,d,e,f,g,h,i,j;a.oldTopRow>a.newTopRow||(b=this,c=a.sheet,d=c.parent,e=a.newTopRow,f=c.jt(1).length,g=b.itemList.width(),h=b.iXa,i=c.getColumnWidth(h),d.options.showVerticalScrollbar&&(g-=22),j=Math.max(c.getColumnCount()*i,g),b.PZa(c,j,i,e,f))},b.prototype.PZa=function(a,b,c,d,f,g){var h,i,j,k,l=0,m=0,n=this,o=a.parent,p=n.iXa,q=d+f,r=a.outlineColumn.WZa(),s=Math.min(q,a.getRowCount());for(h=d;h<s;h++)i=g&&g[h].name||a.getText(h,p),j=r[h].cellContent.left,k=e.Vn.Xn(i),m=k+j-b,m>0&&m>l&&(l=m);l>0&&(a.addColumns(a.getColumnCount(),Math.ceil(l/c)),o.options.showHorizontalScrollbar=!0)},b.prototype.gXa=function(a){var b,c,d,f,g,h,i,k,l,n,o=this,p=a.sheet,q=a.row,r=a.sheetArea,s=o.iXa;o.activeItemIndex=q,p.suspendPaint(),!a.ignoreClick&&p.yt&&(b=p.yt.cellTypeHitInfo,c=b&&b.outlineColumnHitInfo,d=c&&c.indicator,d||(p.outlineColumn.getCheckStatus(q,s)===o.dataSource[q].status&&(f=p.outlineColumn,f.setCheckStatus(q,o.MZa(o.dataSource[q].status))),o.DZa())),p.CH&&p.m_a("transparent"),g=e.GC$("."+F,this.xo[0]),m(g)>0?gb(g,G):o.jXa(p),h=p.getStyle(q,s,r),i=h&&h.clone()||new e.Style,k=e.GC$(j(da)),db(k,H),this.xo.append(k),l=k.css("backgroundColor"),n=k.css("color"),k.remove(),i.backColor=l,i.foreColor=n,p.setStyle(q,-1,i),p.resumePaint(),o.dXa(),o.MY()},b.prototype.DZa=function(){for(var a=this,b=a.eXa,c=b.outlineColumn,d=c.getCheckStatus(),e=0;e<d.length;)a.dataSource[e].status=d[e],e++},b.prototype.MZa=function(a){return 1===a?2:1},b.prototype.jXa=function(a){var b,c,d=a.getRowCount(),e=this.iXa;for(b=0;b<d;b++)if(c=a.getActualStyle(b,e),c.backColor||c.foreColor){c.foreColor=c.backColor=void 0,a.setStyle(b,-1,c);break}},b.prototype.dXa=function(){var a,b,c,d,e,f,g,h=this,i=h.eXa.outlineColumn.getCheckStatus(),j=0,k=h.gY||h.hY,l=h.CWa,m=0;for(h.hY=[],b=0;b<l.length&&(a=l[b],!(m>=k.length));b++)if(a.key===k[m].text){for(m++,c=a.children,d=Ja,e=0;e<c.length;e++)f=c[e],g=i[j],f.show!==!1&&(j+=1),d=g||d,3!==g&&(f.status=g);2!==d&&h.hY.push({text:a.key,value:a.value})}},b.prototype.KY=function(a,b){var c=this,d=c.eXa,e=Za[b];d.suspendPaint(),d.outlineColumn.Wlb(e),d.resumePaint(),c.DZa(),c.hY=b?jb(c.gY):[],c.MY(),l(a)},b.prototype.kXa=function(a){switch(a){case Ga:this.FWa=this.MWa;break;case Fa:this.FWa=this.PWa;break;case Da:this.FWa=this.RWa}},b.prototype.lXa=function(a){var b=this;b.FWa=a,b.FWa.Ao(),b.FWa.mXa()},b.prototype.nXa=function(a){var b,c,d,f=m(a);0!==f&&(b=this,c=a.attr("id"),d=c===Ba,1===f?d||c===Ca?(b.JY(d),b.close()):c===u?b.close():c===Ga?b.FWa!==n?b.FWa.oXa():b.lXa(b.MWa):c===Fa?b.lXa(b.PWa):c===Da?b.lXa(b.RWa):c===Ea?b.WW():e.GC$("#"+t).trigger(Z):2===m(a)||3===m(a)?b.FWa&&b.FWa.oXa():e.GC$("#"+t).trigger(Z))},b.prototype.pXa=function(){var a,b,c,d,e=this,f=e.CWa,g=e.gY,h=0,i=[];for(a=0;a<f.length;a++)if(f[a].key===g[h].text)for(h++,b=f[a].children,c=0;c<b.length;c++)d=b[c],d.show!==!1&&i.push(d.value||f[a].key);return i},b.prototype.qXa=function(a,b,c){var d,e,f=this,g=f.elementList;return!m(b)&&o(f.activeItemIndex)?d=g[0]:(m(b)?gb(b,G):f.jXa(f.eXa),e=f.pXa(),d=4===a?f.rXa(e,b,c):f.sXa(e,b,a)),d},b.prototype.sXa=function(a,b,c){var d,e,f,g,h=this,i=h.elementList,j=b[0],k=i.length-2,l=jb(i),n=h.activeItemIndex||0,o=0;for(e=0;e<m(a);e++)l.splice(-2,0,a[e]);if(f=j?q(j,l):k+n,g=3===c,f>=0){for(d=g?l[f+1]:l[f-1],n=n||0,o=g?f+1:f-1,q(d,a)>=0&&m(b)>0&&(f=g?f+n:n+(i.length-2),d=g?l[f+1]:l[f],o=g?f+1:f);g&&f<m(l)&&h.RY(d);)o=f+1,f+=1,d=l[f+1];if(!g){for(;f>0&&h.RY(d);)o=f-1,f-=1,d=l[f-1];d||(d=l[m(l)-1])}d||(d=l[0])}return typeof d===W?o-k:d},b.prototype.rXa=function(a,b,c){var d,e,f=this,g=f.elementList,h=b[0],i=jb(g),j=f.activeItemIndex||0;if(i.splice(-2,0,a[j]),m(b)){if(e=q(h,i),e>=0){for(d=c?i[e-1]||i[m(i)-1]:i[e+1];c&&e>0&&f.RY(d);)e-=1,d=i[e-1];for(;!c&&f.RY(d)&&e<m(i);)e+=1,d=i[e+1]}}else d=c?g[g.length-3]:f.RY(g[g.length-2])?g[g.length-1]:g[g.length-2];return d||(d=i[0]),typeof d===W?j:d},b.prototype.QY=function(a){var b,c,d,f=this,g=e.GC$("."+F,f.xo[0]),h=a.which||a.keyCode,i=q(h,[27,13,38,40,9,37,39]);g.length>0&&(b=g.attr("id")),0===i?(f.close(),l(a)):1===i?this.nXa(g):i>4?(5===i?(f.FWa===n&&f.kXa(b),f.FWa!==n&&(f.FWa=f.FWa.tXa())):6===i&&(f.FWa===n&&f.kXa(b),f.FWa===n||f.FWa.P$a||f.FWa.uXa()),l(a)):i>1&&f.FWa!==n&&f.FWa.cXa()?(2===i||4===i&&a.shiftKey?f.FWa.vXa():(3===i||4===i&&!a.shiftKey)&&f.FWa.wXa(),l(a)):i>1&&(c=f.qXa(i,g,a.shiftKey),"number"==typeof c?(g=c,f.gXa({sheet:f.eXa,row:g,sheetArea:3,ignoreClick:!0}),f.eXa.Yq(g,f.iXa),e.GC$("#"+Oa).focus(),l(a)):(g=e.GC$(c),d=g.attr("id"),d!==v&&d!==t&&d!==u?g.find("a").focus():g.focus(),db(g,z+" "+G),l(a)))},b.prototype.RY=function(a){return typeof a!==W&&(a=e.GC$(a),0!==m(a)&&!!(a.hasClass(J)||a.hasClass(K)||a.hasClass(L)))},b.prototype.PY=function(a){var b,c,d,e,f,g=this,h=g.sheet;if(h)try{if(h.suspendPaint(),b=g.filterButtonInfo,c=b.rowFilter,!c)return;d=b.col,e=c.sj?c.sj.name():Ja,f={tableName:e,colIndex:d,checkedValues:a,allValuesLength:m(g.fY)},h.wu().execute({cmd:"filterTextSelectAction",sheetName:h.name(),cmdOption:f})}finally{h.resumePaint()}},b.prototype.NY=function(){var a,b,c,d,f,g,i,j,k,l,n,o=this,p=e.GC$.trim(e.GC$("#"+v).val().toLowerCase()),q=o.CWa,r=o.a$a,s={};if(p){for(b=h.ub(p),c=[],d=void 0,b||(d=RegExp(p.replace(o.rEscape,"\\$&"),"gi")),a=0;a<q.length;a++)for(f=q[a],g=f.children,i=0;i<g.length;i++)if(j=g[i],k=f.key,l=j.value||k,n=b&&h.sb(b).exec(l),n&&0===n.index||!b&&l.search(d)!==-1){c.push({text:k,value:f.value}),s[k]=!0;break}o.gY=jb(c),o.hY=jb(c),o.HY(s)}else if(m(r)){for(a=0;a<r.length;a++)1!==r[a].status&&(r[a].status=1);o.gY=jb(o.fY),o.hY=jb(o.fY),o.AXa(r),o.dataSource=r}else o.a$a=jb(o.dataSource)},b.prototype.EY=function(a,b){var c=e.GC$(j("a"));return c.attr({id:a,href:X}).text(b).css([pa,na,"cursor",ka,"text-decoration","text-align",V,"outline",sa],[ya,"0 6px","default","0px 0 1px 2px",qa,ua,"12px",qa,"inherit"]),db(c[0],B+C+I),c},b.prototype.v$a=function(a,b,c){var d,f,g,h=_a().Clear,i=e.GC$(j("a")).html(h.replace("{0}",'"'));i.attr({id:a,href:X}).css([pa,na,"cursor",ka,"text-decoration","text-align",V,"outline",sa,"white-space"],["inline","0 6px","default","0px 0 1px 2px",qa,ua,"12px",qa,"inherit","nowrap"]),c.append(i),d=c.width()-i[0].offsetWidth,g='<span class="filterName">'+b+'</span><span style="vertical-align: top">"</span>',f=h.replace("{0}",g),i.html(f),e.GC$(".filterName",i[0]).css(["max-width","overflow","text-overflow",pa,"vertical-align"],[d+"px","hidden","ellipsis","inline-block","text-bottom"]),db(i[0],B+C+I)},b.prototype.FY=function(a,b,c){var d=j(ca),f=e.GC$(j(ca)).text(b),g=j("a"),h=e.GC$(g);return db(d,"gc-check-uncheck-all "+c),h.attr({href:X,id:a}).css(["float",la,na,V,sa],[ua,"7px","0","12px","16px"]).append(e.GC$(d)).append(f),db(g,Q+Q+"-style"),h},b.prototype.xXa=function(a){var b=kb(a);return"date"===b?void this.DWa.Date++:"number"===b||"boolean"===b?void this.DWa.Number++:"string"===b?void this.DWa.Text++:void 0},b.prototype.yXa=function(){var a=this.DWa.Date,b=this.DWa.Number,c=this.DWa.Text,d=a+b+c;return d>a&&d>b&&d>c?"mix":d===a?"type":d===b?"number":d===c?"text":void 0},b.prototype.YZa=function(a){var b,c=!0,d=a.rZ[this.filterButtonInfo.col];return d&&d.length>0&&(d=d[0],b=this.i$a(d),(0!==d.compareType()||b||0===d.compareType()&&5===d.conType())&&(c=!1)),c},b.prototype.qxb=function(a){var b,c=this,d=c.pxb[a];return o(d)&&(b=a.replace(/(\[[^h^m^s]+\])|(General)/gi,""),d=i.Lc(b),c.pxb[a]=d),d},b.prototype.T0=function(a,b){return"number"===za(b)&&"string"===za(a)&&this.qxb(a)?Aa(b):b},b.prototype.Q$a=function(a,b,c,d,e,f){fb(a!==n&&a.backColor,c,d,e,f),fb(a!==n&&a.foreColor,b,d,e,f)},b.prototype.zY=function(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s=this,t=s.filterButtonInfo.rowFilter;if(t){if(b=s.sheet,c=b.Tq(t.range),d=c.col,e={},f=[],g={},i=b.currentTheme(),j={},k={data:{},colorNumber:0},l={data:{},colorNumber:0},m=b.outlineColumn&&b.outlineColumn.XQa(a),s.hasBlank=!1,a>=d&&a<d+c.colCount)for(n=0;n<c.rowCount;n++)o=c.row+n,h=b.getActualStyle(o,a,3,!1,!1,!0),p=b.getRowHeight(o)>0,q=p||!t.isFiltered()||t.ima(o,a),q&&this.Q$a(h,l,k,i,o,j),s.u$a(e,g,f,o,a);s._Wa=k,s.aXa=l,r={date:0,number:1,string:2,boolean:3,blank:4},m||f.sort(function(a,b){var c,d,e=a.value,f=b.value,g=kb(e),h=kb(f);if(g!==h)return r[g]<r[h]?-1:1;if("date"===g&&"date"===h){if(c=e.getFullYear(),d=f.getFullYear(),c<d)return 1;if(c>d)return-1}return e>f?1:e<f?-1:0}),s.fY=f,s.gY=jb(f),s.CWa=s.zXa(a,g,f),s.a$a=s.dataSource}},b.prototype.u$a=function(a,b,c,d,e){var f,g,h,i,j,k,l,m=this,n=m.sheet,o=m.filterButtonInfo.rowFilter,p=n.getText(d,e),q=n.getValue(d,e);p=p?p.trim():p,f="date"===kb(q),g=_a().Blanks,h=n.getRowHeight(d)>0,i=m.YZa(o),j=n.outlineColumn&&n.outlineColumn.XQa(e),k=n.getFormatter(d,e),k&&(q=m.T0(k,q)),m.xXa(q),1===m.DWa.Date&&(m.XZa=m.AZa(k)),l=i&&(h||!o.isFiltered(e)||j&&n.getRowVisible(d,3,!0)&&!o.WY(d,e)),""!==p||!h&&o.isFiltered()&&!o.ima(d,e)?a[p]&&!f||(p=f?o.mzb.format(q):p,n.getRowVisible(d,3,!0)&&n.Yr(d)&&(l&&(b[p]=!0,m.hY.push({text:p,value:q})),(h||!o.isFiltered()||o.ima(d,e))&&(c.push({text:p,value:q}),a[p]=!0))):m.hasBlank||(m.hasBlank=!0,c.push({text:g,value:q}),l&&(b[g]=!0,m.hY.push({text:g,value:q})))},b.prototype.zXa=function(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q=this,r=[],s=[],t=q.sheet,u=t.Tq(q.filterButtonInfo.rowFilter.range),v=t.outlineColumn;if(v&&v.XQa(a))for(j=0;j<c.length;j++)n=c[j].text,o=c[j].value,i=Za[!!b[n]],k=nb(t,n,o,u.row+j,a,i),r.push(k),m=k.children[0],m&&m.show!==!1&&s.push({name:m.value||k.key,level:m.level,status:m.status});else for(j=0;j<c.length;j++)switch(n=c[j].text,o=c[j].value,d=kb(o||n)){case"date":for(i=Za[!!b[n]],k=mb(q.XZa,n,o,i),l=k.children,e=l[0].value,f=l[1].value||l[0].value,l[0].show=l[1].show=!0,e===g&&(l[0].show=!1,f===h&&(l[1].show=!1)),g=e,h=f,r.push(k),m=k.children,p=0;p<3;p++)m[p]&&m[p].show!==!1&&s.push({name:m[p].value||k.key,level:m[p].level,status:m[p].status});break;case"number":case"string":case"boolean":case"object":i=Za[!!b[n]],k=nb(t,n,o,u.row+j,a,i),r.push(k),m=k.children[0],m&&m.show!==!1&&s.push({name:m.value||k.key,level:m.level,status:m.status});break;default:return}return q.dataSource=s,r},b.prototype.AZa=function(a){var b,c,e,f,g,h,i,j,k;if(o(a)&&(a=""),b=new d.Formatter.GeneralFormatter(a),c=b.formatters,c&&!(c.length<=0)&&(e=c[0]))return f=e.localeIDFormatPart,g=e.dbNumberFormatPart,h=f&&f.Wc,i=d.Common.CultureManager.LZa(h,lb(a)),j=f&&f.Kc||"",k=g&&g.Kc||"",new d.Formatter.GeneralFormatter(k+j+i)},b.prototype.AY=function(a){var b=this,c=0,d=b.itemList,f=new e.Workbook(d[0]);b.hXa=f,b.eXa=f.getActiveSheet(),b.iXa=c,b.AXa(a),b.DZa()},b.prototype.AXa=function(a){var b,c,d,f,g,h,i=this,j=i.hXa,k=i.itemList,l=k.width(),m=k.height(),n=20,o=10;i.hXa.sheets.length>0&&(i.hXa.removeSheet(0),i.eXa=Ja),j.addSheet(0,new e.Worksheet("sheet1")),b=i.eXa=j.sheets[0],j.suspendEvent(),b.suspendPaint(),c=j.options,d=b.options,c.allowContextMenu=!1,c.showHorizontalScrollbar=!1,c.showVerticalScrollbar=!1,c.scrollbarMaxAlign=!0,c.tabStripVisible=!1,c.allowUserResize=!1,c.allowUserDragDrop=!1,c.allowUserDragFill=!1,c.allowUserZoom=!1,c.grayAreaBackColor="white",d.colHeaderVisible=!1,d.rowHeaderVisible=!1,d.selectionBackColor="transparent",d.selectionBorderColor="transparent",d.gridline={showVerticalGridline:!1,showHorizontalGridline:!1},d.isProtected=!0,b.defaults.rowHeight=n,b.defaults.colWidth=o,b.setRowCount(a.length),f=new e.Style,f.font='12px "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text"',b.setDefaultStyle(f),b.outlineColumn.options({columnIndex:i.iXa,showIndicator:!0,showCheckBox:!0}),b.showRowOutline(!1),b.setColumnCount(1),i.BXa(b,a),n*a.length>m&&(j.options.showVerticalScrollbar=!0,l-=22),g=b.jt(1).length,i.PZa(b,l,o,0,g,a),!j.options.showVerticalScrollbar&&j.options.showHorizontalScrollbar&&(m-=22,n*a.length>m&&(j.options.showVerticalScrollbar=!0,l-=22)),h=Math.ceil(l/o),b.addColumns(b.getColumnCount(),h-1),b.outlineColumn.refresh(),b.resumePaint(),j.resumeEvent()},b.prototype.BXa=function(a,b){var c,d,e,f,g,h,i;if(0!==b.length){if(c=this,d=0,f=0,g=a.outlineColumn,h=c.iXa,a.suspendPaint(),a.autoGenerateColumns=!1,a.bindColumn(0,"name"),a.setDataSource(b),i=c.yXa(),"text"!==i&&"number"!==i||g&&g.XQa(c.filterButtonInfo.col))for(f=0;f<b.length;f++)d=b[f].level,a.getCell(f,h).textIndent(d);else a.getCell(-1,h).textIndent(d);for(g.refresh(),f=0;f<b.length;f++)e=b[f].status,g.CXa(f,e);a.resumePaint()}},b.prototype.HY=function(a){var b=this;b.zXa(b.filterButtonInfo.col,a,b.gY),b.AXa(b.dataSource)},b.prototype.JY=function(a){var b,c,d,f,g=this,h=g.filterButtonInfo,i=h.rowFilter,j=g.sheet;i&&j&&j.hK()&&(b=h.col,c={sheet:j,sheetName:j.name(),col:b,ascending:a,cancel:!1},j.Wq(e.Events.RangeSorting,c),c.cancel===!1&&(d=i.sj?i.sj.name():Ja,f={tableName:d,colIndex:b,isAsc:a},j.wu().execute({cmd:"sortFilter",sheetName:j.name(),cmdOption:f}),j.Wq(e.Events.RangeSorted,c)))},b.prototype.DXa=function(a,b,c){var d,f,g,h,i=this,j=i.filterButtonInfo,k=j.rowFilter,l=i.sheet;k&&l&&l.hK()&&(""!==a&&(a=eb(a,l.currentTheme())),c=c!==Ja&&c,d=j.col,f={sheet:l,sheetName:l.Cj,col:d,ascending:c,color:a,isBackColor:b,cancel:!1},l.Wq(e.Events.RangeSorting,f),f.cancel===!1&&(g=k.sj?k.sj.name():Ja,h={tableName:g,colIndex:d,isAsc:c,color:a,isBackColor:b},l.wu().execute({cmd:"sortFilter",sheetName:l.name(),cmdOption:h}),l.Wq(e.Events.RangeSorted,f)))},b.prototype.MY=function(){var a=e.GC$("#"+t),b=J+K+" "+L;m(this.hY)>0?(a.prop("disabled",!1),gb(a[0],b)):(a.prop("disabled",!0),db(a[0],b))},b.zW=function(a){return 0===a?"data:image/png;base64,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":1===a?"data:image/png;base64,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":2===a?Ya+"fvM55PfGm/K3O233vuO+638e9H5ko/ED+UPPR+mPHp9BP9z7nfP78L/eE8/sl0p8zAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAJKSURBVHjanJJfSJNhFMaf9/02R9MFCVZLUOgfpLMCmwrd7KIIm6X4fXoTY1oXltaN86oEi4xodtNNeNMHJZQ0gpDQmUKEyUAiyKCFW4PNtSFMoyKJSp4uputbJIUX5+K855zfe85zDkhiza4u2+HXn4M64dcBvw5BTotLtyDJ49KvI2fapK/Q/8qkwAjYiEHj4rrBG+ft4p+AB51f8pKazxxDY/djhLYj95bJLKG5fR+w5whUVxNUtR6qOiHUrmABNH3AkgcoqsBM11O56gvVUy2GV6CQNA/WjFdkMpnyhYV0NjZOAf6ksQMZCGQL3e526fUWm0kKrlAJRT7CUXeac+Gp3bRTaJpHXsReoLl3yGQAKPUtlaJh8yHxKI0s+C3xLHHXwjIqlbXtTKc/lKTScaRSFItMAWReB4WtdfdMJCVJTI18xihLNzFCyWXC4WxjMpksjiOK5BytjXIGUH0XCowjkFsESeF27BQLSy9F/2FK0ip6wa2VtV7OxxNF8XgciUQCKpMCZFoaRdQ6PXIHshvQDtACUJDARA/tVTVe6rHrSs97mMPh16j3vQECvpKc4iSLWzrGqXWMrahnx6h1jP5oPRf8Ho1FLA5nG/c7vayq8TIajdoikRi0nqCEdn9gDaCQLNVOXZOuk310nbhCV0Mvhx+OWlbhykGnh+Hou20sy2rUdLtbAJxUfs9PwW9PQM4qjuqjfDEd6ssX+VP56kdZ/+s80H9nZJcBYDxT6x8b+vspDw4Fbf+TuC7gpn7ZtoFCSdJE0vxrAN8Jcr/DWZzEAAAAAElFTkSuQmCC":3===a?Ya+"fvM55PfGm/K3O233vuO+638e9H5ko/ED+UPPR+mPHp9BP9z7nfP78L/eE8/sl0p8zAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMuSURBVHjaZJN/aNR1GMefz+f7/d6P3aZGDGVKERLDbXdMJAQhlr+ujWo62g5DpUBiEyJjpxi0ZRtyDSQvV0cDd2uw213b1EHiFjbGZq5bIbc5dTuyybRyK+ZCDrG0evWHdwfWHx8+PDwPz/vzeb2fR451DGvpQB2ODJvnP24zDkewD7Zct6h9qD1j4TzPWNgsip80isbCxuS7vzinGm5aZwcwbwVPGoAhgAIEsAAN2NKxSscmYKRvE1iRPrY3Vt7Ski4WQKaJOmbjyF3WS//3jfr4hVXGEA55J5aXc+ZMdF0yOZWbFtCAKtx5zCUQLQBk32svPFJ8eHN16u+fCn6fv+MErKWlRWdvPzr9QpUVnIroxravlYz0tTvWdi9XRFGA6ugKOMPh7qHu7u5Ue3s7ra2tBIMnaG4+Sl1dHTU1NZSXl7P42x39zRxKYl82mYD8Q0wA8bjFmv855Xqzdu/swsICDx78xfz8r0xMXKa+/iBer5fx8W+PDi22PjH43pKSoi2bzIS+reVlUYCVn18q11hl9vb2GIFAgFQqxczMDKFQCK/Xy8WLF4ozkN2e00o8O07YA1KpxCUCyKYSUX1fxDQgc3NzRjAY/DMej+Pz+ejpiTkzAAEpqfpEJHeP2PZJXO32VBsbKryydY3IPZB1a3IVIH6/n1AoREVFBWmQjzknK7aLHm4bWXs5cel48uq0ziQGolE9e/uG8vsPceDttygrK8s2cBfkZ9zQ8syGbeaVyQmp3v8VNXWDvFo3SPX+8/hqzxEPjqiD9X5eqqoiN8fFjzeu5wHidh9xZTjIgZr1BqAaWi6Zm3e8z/OVDZS90kBv74AdUH7/IbZu3oJps0h8N7N62IXpLt2VnVbZWfKc+jTx+aM/3bti85RuZ3RsvBjQxFgWO9VnTSebzB+SMyvhtMMTFsv9+l4r26Czq0sxcV9F+3eprnOJwumr14ozpMMfnLV/duQjPdeDmiTiaGrutLs3Nhjujc86sxD/uHs/DxCPjFrbXly0N7ZEbR9GcPb0jz4NS/q/1P/nwlOFlYpOrZ4UkeUiOt8nRk6xyGNzT3YXTGBZeqEcgP53ACxeTBjELwuFAAAAAElFTkSuQmCC":4===a?Ya+"fvM55PfGm/K3O233vuO+638e9H5ko/ED+UPPR+mPHp9BP9z7nfP78L/eE8/sl0p8zAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAMNSURBVHjalJNfaFt1FMfP796bm9hublCwa93AVd3W5d67ddisFFw1lovUl7r0jx2rWWW6prpCkzfrUwjVFx0LwSrYPtiO0YJPDsuwIOJKq1PL0q43iZ3OuUp04p8owmbl40PXMgaCPnzhcPjCOef7PV8BxD6WETrKjXcviUbjTlW870/ta9mqfiqv1kuRQePjk68pkj4FSfX66HRA8ujfDPapvveWN4n1xKAC5H9CB3SRo767m9uhvRxQUGXAEXOt3uCodViWpT4Yi+tC1Rrhj99/luV8QQ43unrkxWaNVSqvXSNgHSeQSqU2A4Zt2xUdHR3bp6amwp8uTNU91jRgij2PAWg1da+sTwmeGTv768TExN8jIyOk0xlOnTpNMpkiFovR3t6O67rcuPHDJkCtr6U7zVH/gffPKrwravLzC75EIkGxWOTWrVWKxR+Zn79EPB7HdV3m5uYaAK12/xfmxm0P1uwW62D36p77nzdXVr4Txwn5h4aGVkqlEp6XJ5PJ4LouMzMzDYAGiPXkGwGJLGIAyqmPYod6sEM9vziRF0xALMvS0+n0zdnZWTo7O5mcnNwHaNevi27bqN0v+w1pWyj5AOUEjygn1I3VeLzaund/xfj4uAASi8WWM5kMLS0tAAYgV69+K5cvvyq/zZ7TpG1hzZaqbZbsDR3DPnSi2n6ka0uhUJB8Pt+QSCTo7++nqamJ204YXV1PG5aF2nFxUgn7PlGAWIefVXYoihVufcBWorLZrOTz+YMDAwN/tba2UlZWRi6Xq7itmQbb9JnU25qMvvmRAKrmoafEqY/edOqf2bljV/iebDYrgPT29q6Ew2FM06RQKFQMDw+L4ziabaP3nXwpIBy9oACVnBblHOh52N4Vqaxr7jYAFQwGty4uLm72PE/3PK8yt+QZuawnjgSN0TOiDj36nCmP9364pjiY1hJ67RX8F5dMBUjtW7Xav+Xh3PRnIiK6CF/qd/66886YHvW3Sfh8lQHfy11Z2MCegBgioknbCZQTPr1l71di/IcUqjsgLfHzvn8GACNDKumTxWELAAAAAElFTkSuQmCC":5===a?Ya+"fvM55PfGm/K3O233vuO+638e9H5ko/ED+UPPR+mPHp9BP9z7nfP78L/eE8/sl0p8zAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAANBSURBVHjaXJNfSGR1HMXP73fvdcYdo7Vy1KRlZ9DdnOvv5uTODK0s1mxDyKIYSRYoUwQikkgzPUS0RuW6QRCEuIiM/XuxNdiH6m17WnZFCMSHtjuOszONf0ZnGgW1iLbw9DBl0sPhy3k5HDjfD0h6w/VvSJJC8a5sOPeUYQ1TIjAuPOc+kN6Kae2JthodGBCVrSEdpmXAd8pJUrb2julgiWgJ9MMXABIXYADApQA0ACLc8Jjg9bAkKbhbD5LimDTgNQn15sc6SZD1onyPZIAUSyQOSS124oLzDxL7hwRJjaTIFbYcUJ5xB0lYp29oZ8+OypZnPhfvkXiHdGz8nHHs7++6e+vede6UiiiVio69vR2v1+2S5slqQRJ4XvdXkBS7O+uuUv43bO79UvPZp4nfr8/N/Tk7O8urVz9cu3x5zH6x76Xvuru7f+zq6joIhUKFYrHYSBJA4yVBUm5sbWPm15nqH1bvOpRScnBwcL1QKPD+/b+4vV3k0tIy4/E4I5EIFxcXu0nKckDwWUlSV6peqGCUW/mvJUmoNr82MTHBg4MD2rbNqakpRiIR3r59a5ykzOc3QBJob5txkZSbuQ00h/q5ub7htZSJ9NYmlDLF5OQkFxYW2NfXx/n5+YskhWk2G0p9IV5BXgDBAZ1k5Z07tx6wAlHmcjlHjrUim13D8vIaYrEYr12bZmdnJ0k6ygvVHi0GOD2SpHGPhApGmclkKjKZ15HJpHHzJrV4PM6RkRF2dHQwm83WeTweMTw8cCwA1YKkUwWjVMEofaFXeS99w1CksKwWIxaLsaenh1VVVUyn0yf8fmBoKCpIoqeBAr3n50DywdXVlWorEGUqufp0imnpM8eEaZpaPB5nOBymrutMpVKuM2dGdQvjGh+icfFxCCA0YJB0tKC9Mpn8qTKZhLSS0BOJUWmaprBtu8K2bW1lZeVR27alUq3SwpDBh2t1fAUN/eepkYfCOvW26/grWxNB6XukvYJJwgKE+c1zUs270TQdkmq4WeKtTwS+hETiCgUJafrb/89CWTU9/8JT9k2UnP7HN1Ag+OQV8Ps6Yb78vigj/a08ahGCaGzql/zIrbmhy/9IzAmSztMvzJz8ewAvdf7cUVI5hQAAAABJRU5ErkJggg==":6===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAABy0lEQVR42pyST2gTQRTGv9nEpCnqTasHES8VLCioiFDIYYum+I/ebFXQm1BbsdSDhKgoXhS8tLQSqAhexIInETwJIqJiexJEZXupwRJNbRsy2Z3dzHxesjGEChs/eMx7A+837817giQAYCD3cZuScqG8Wtj19tG5n/iHhBAkKcLYCh3fdSd04HXCq5xHG2oAdOAdNIG3qAJ5tW1A/9hrm8ZsV+7vDLTe2nsmP9AWwATuXe27330lLwvLkloHY5EBR0dexkB0xxKp9Ieno8MbOjYP1ZRMHz77YE8kAMk35V/fkpWSc6L3dL7Tl6uPAaAm155EAYhwjFHVOsY4AEwPXXkG7W+0EqmCodlRW17oCkrO5Pjc55lon7i2mBqenc7ETTmLFWdvaenLpijJDUBcywsAYFRwu/jja1dMWNfWKf2WEOJ43T8ihLgHACAJkpgZvNQ3kTnJG/u6Z8O7ZgNwAACbzCb5dxNVLPnQW3aKyY7EKABM2XZPcwUk5wG8qIfvSL5qtJAfHLnvF+Z2GsPr2fefipPH0luq5aXsOi3frJ+5xhSm+k/1VErOxRWlqgkrfujO/t191ZqxLeB5azbJeSFELnz9v/agVX8GAIjG5r9I9yjkAAAAAElFTkSuQmCC":7===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAACXBIWXMAAAsSAAALEgHS3X78AAAAGXRFWHRTb2Z0d2FyZQBQYWludC5ORVQgVjMuNS41Tib51gAAAXlJREFUOE9jYACCqRH5a6eGZu6cHl00d2pUwa6J7r4Xe4w1U0ByWMB/DLHJnoHbQIKzI1PEp3r6vKjVU72HQzNIGNOA6W5uYiCZmUHxM+v0Vf836KuHkWQASPGciGznSe6+/+v01Vbh0YzdBeBwiC560GWi+aLVXEccxJ/i6KhNdBjMCM/u7be3+99hpJEK0jTJ01a0y1hjKVEGTHH31e4w1vxSrqP0tVZPbXazodqKSl2VV9W6KnOJMoCAf4mLRpAq/+pTEh4F+79aJSwGxwjJseBZdHCVa/b2/1YR00vJMsAtd+c957T1D42De17iMKARKO4NTUiuQLoLrs694IAT0PbvNrFzNYwDOv9bRc4IwGKIMVQzKCWCsBNcDVDzaafUdbfMwiZNMwnq/mIeNvkgDldsgWo+hqyZ2TVr+0egIRIgQcuomb7G/h3/zaOmaeFxBcJ2l6xtx8xDJ/6wDJ+cYhU2g8vYv/M9yABj//aLOFxRTUbU49YCALt6h216b0oVAAAAAElFTkSuQmCC":8===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAKBJREFUOE+l0cENwjAMBdCM0BEYhZG6AjdvwQhMUvXQI0KcKo6IY2/9v0qQnVilFoenJF+2q6RJRP7ihhFuGOGGO851Zg4/9PCGi8oOD+iAzQM8c7bRRTX9pStMmbnGd1Nhwz2vbOD+ATcwteagjPBSZuAVTmBqzUHhnTnkA2wkPmJT2wRKGbLk1avZHUAcwldv/n/hhhFuGOGGEW54nKQVV2qyDejeW7YAAAAASUVORK5CYII=":9===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAHpJREFUOE+lz8ENgDAMA8COxEiMkxWYgkmYhCGgllpkUz8aeJyQrVQJJSJ+sWWGLTNsmWHLaq22amkZX2T0MiuB7ASPOMusBIKNh9Evekh4weaTDOfDUDTYdBnTF/AjbOYssxJIf9Q39ouG35DwhS0zbJlhywxbzotyAztMtZGgPPrJAAAAAElFTkSuQmCC":10===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHCAYAAAAvZezQAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkU3MkFGN0FGNDFDMjExRTc5NDI5QzJBNEY3MjNCMjlDIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkU3MkFGN0IwNDFDMjExRTc5NDI5QzJBNEY3MjNCMjlDIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RTcyQUY3QUQ0MUMyMTFFNzk0MjlDMkE0RjcyM0IyOUMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RTcyQUY3QUU0MUMyMTFFNzk0MjlDMkE0RjcyM0IyOUMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5OU3N/AAAAT0lEQVR42kyLQQqAMBADk7j+yt4s6o8Fj35Ldt1WCg6EMIRgWbdIMMJSjwC68L5OWISjoclyAIUfpe4hyT7LPS+U+9M9m/1qNrfmuL0CDABNSCHTGW/2lwAAAABJRU5ErkJggg==":11===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjQ4RjIyODk5REFGMDExRTdBQjYzODEzMjUxNkQwMjNGIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjQ4RjIyODlBREFGMDExRTdBQjYzODEzMjUxNkQwMjNGIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NDhGMjI4OTdEQUYwMTFFN0FCNjM4MTMyNTE2RDAyM0YiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NDhGMjI4OThEQUYwMTFFN0FCNjM4MTMyNTE2RDAyM0YiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Qaz1BAAAAgklEQVR42syTQQvAIAiFkw7RNejg//+hbwhTWput6LAFEpLvU+pFAMLOos8BQQCzkKYOppsFaF1KSYqhkCWAbKUULE+g4hjjRXwDMDM8cRPhEVBrRdPFFbsAPZM4L8ryfrIRoO+InDO8F5EwIxFRDzGzDfzgAhRCL4b6wV/YBhwCDAB5AMCzqyCJUAAAAABJRU5ErkJggg==":12===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjk1MTQzMkI5REUzNjExRTc5NTJGRURCQjMxNUZBMkY1IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjk1MTQzMkJBREUzNjExRTc5NTJGRURCQjMxNUZBMkY1Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6OTUxNDMyQjdERTM2MTFFNzk1MkZFREJCMzE1RkEyRjUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6OTUxNDMyQjhERTM2MTFFNzk1MkZFREJCMzE1RkEyRjUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6ynYg+AAAAk0lEQVR42qSS4RGAIAiFhWuXNmkAd2iqdnCANmkaii47j15o+f7ooXzCQxKR0CNSQEpJ91VSjJFsjK9Vk6n2GApycehBXuFsLiGIWxmDyyWk1tYDkM36NYVgDBXkutWARqXAnLzNE/JFxmWluwJPB8CO+dwfAFyBM0ZBxnLoVCsgt0D2y/OHZNRO3cSmf9CjXYABADU0MwNjlomLAAAAAElFTkSuQmCC":13===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVGMDFDMjYxREUzNjExRTc4QkRCRjdBQjc3Q0NGRkNCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVGMDFDMjYyREUzNjExRTc4QkRCRjdBQjc3Q0NGRkNCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NUYwMUMyNUZERTM2MTFFNzhCREJGN0FCNzdDQ0ZGQ0IiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NUYwMUMyNjBERTM2MTFFNzhCREJGN0FCNzdDQ0ZGQ0IiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6yeKkNAAAAhElEQVR42qSSUQ7AIAhDLfGEHtQrspGMhSATnfxoVJ62FsxcTgoC6L3LPCW11uDX6BmlGdll0SKZzRnkE07uUASZvoyCwxaSyRoAatavXyjOUI5c91WjrxKgNt/zyJcXjixIJiOwL1SZdVWqCRpSE3dqFaAS4CNPG82RnNzEpRyc1CXAAE5UMwNlC3A6AAAAAElFTkSuQmCC":14===a?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHCAYAAAAvZezQAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MkM3QkZFRTJFNTVBMTFFN0E2RERENkM0NDRCMEZGODQiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MkM3QkZFRTFFNTVBMTFFN0E2RERENkM0NDRCMEZGODQiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RTcyQUY3QUY0MUMyMTFFNzk0MjlDMkE0RjcyM0IyOUMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RTcyQUY3QjA0MUMyMTFFNzk0MjlDMkE0RjcyM0IyOUMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5dkRuoAAAALElEQVR42mI8duzYfwYGBkYGKGCC0v/RBeCCyAJgQXQBRmQBRmQtcFsAAgwAYLMGYBu/8woAAAAASUVORK5CYII=":"";
},b}(e.Go),b.gZ=bb},"./dist/plugins/filter/filter.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/filter/filter-ns.js")),c("./dist/plugins/filter/filter-actions.js"),d(c("./dist/plugins/filter/filter-ui.js")),d(c("./dist/plugins/filter/filter.js"))},"./dist/plugins/filter/filter.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("ConditionalFormatting"),g=c("./dist/plugins/filter/filter-ui.js"),h=c("./dist/plugins/filter/filter-ns.js"),i=null,j=void 0,k=f.$V,l=d.kf,m=e.Common.j.Fa,n=parseInt,o=e.Common.k,p=o.Cb,q=o.Bb,r=o.Fb,s=o.ac,t="HideRowFilter",u=d.GC$,v=u.each,w=new e.Common.ResourceManager(h.SR),x=w.getResource.bind(w);function H(a,b,c,d){var e,f;for(f=0;f<s(a);f++)if(e=a[f],e&&e.row===b&&e.col===c&&e.sheetArea===d)return e;return i}y={rowFilter:function(a){var b=this,c=b.xr;return s(arguments)?(b.ITa.gVa(c,0),c&&c.reset(),c=b.xr=a,c&&(b.xr.kj=b),b.$p(),b):c},iZ:function(){var a,b,c,d,e,f,h,i,j,k,m=this,n=[],o=m.rowFilter(),p=o&&o.range,q=m.tables;if(p&&(d=p.row,e=p.col,f=void 0,h=e<0?K(m):e+p.colCount,b=e<0?0:e,d<1?(a=L(m,1)-1,f=1):(a=d-1,f=3),a>=0))for(;b<h;)o.filterButtonVisible(b)?(i=m.getSpans(l(a,b,1,1),f),j=void 0,c=new g.eY(o),c.sheetArea=f,c.row=a,s(i)?(j=i[0],c.row=j.row,c.col=j.col,b+=j.colCount):(c.col=b,b++),n.push(c)):b++;return q&&(k=q.all(),v(k,function(d,e){if(a=e.headerIndex(),e&&e.showHeader()&&a>=0){var f=e.range(),h=void 0;for(h=0;h<f.colCount;h++)b=f.col+h,e.filterButtonVisible(h)&&(c=new g.eY(e.rowFilter(),a,b,3),n.push(c))}})),n},jZ:function(){var a=this,b=a.kZ;return b||(b=a.kZ=a.iZ()),b},Ps:function(a,b){var c=this.filterRowsVisibleInfo;return m(b)&&(b=3),!(3!==b&&2!==b||!c||c.lZ(a))},Nu:function(){return this.filterRowsVisibleInfo.mZ()},oK:function(a,b){var c=this,d=c.zoom(),e=c.parent,f=d>1?1:d,g=3===b||b===j?c.defaults.rowHeight:c.defaults.colHeaderRowHeight,h=n(""+Math.min(20,g)*f);return e&&e.options.useTouchLayout&&(h=n(""+g*d)),{x:a.x+a.width-h,y:a.y+a.height-h,width:h,height:h}}},u.extend(d.Worksheet.prototype,y),function(a){a[a.filter=0]="filter",a[a.unfilter=1]="unfilter"}(z=b.FilterActionType||(b.FilterActionType={}));function I(a,b){var c,d=M(a),e=a.compareType();2===d||7===d?(c=a.jzb,p(b,"T")!==-1||c?p(b,"DT")===-1&&c&&b.push("DT"):b.push("T")):3===d?0===e&&p(b,"BC")===-1?b.push("BC"):1===e&&p(b,"FC")===-1&&b.push("FC"):p(b,"V")===-1&&b.push("V")}function J(a){var b,c,d,e,f=[];for(b=0;b<s(a);b++)c=a[b],0===M(c)?(d=c.item1(),e=c.item2(),d&&I(d,f),e&&I(e,f)):I(c,f);return f}function K(a){return a.getColumnCount()}function L(a,b){return a.getRowCount(b)}function M(a){return a.conType()}A={sortByValue:!0,sortByColor:!0,filterByColor:!0,filterByValue:!0,listFilterArea:!0},B=function(){function a(a){var b=this;b.range=a,b.reset(),b.typeName="",b.filterHandler=i}return a.prototype.filterButtonVisible=function(a,b){var c,d,e=this,f=e.kj,g=e.range,h=e.oZ(g),i=e.pZ(g,f),k=arguments,l=e.qZ,m=a>=h&&a<i,n=s(k),o=k[0],p=typeof o;if(0===n){for(c=h;c<i;c++)if(l[c]||l[c]===j)return!0;return!1}if(d=a,1===n){if("number"===p)return m&&l[d]===j&&(l[d]=!0),l[d];if("boolean"===p)for(f.ITa.gVa(e,3),c=h;c<i;c++)l[c]=o}else 2===n&&m&&(f.ITa.gVa(e,4),l[d]=b);return f&&f.$p(),e},a.prototype.oZ=function(a){return a.col<0?0:a.col},a.prototype.pZ=function(a,b){return a.col<0?K(b):a.col+a.colCount},a.prototype.addFilterItem=function(a,b){var c=this,d=c.kj,e=c.range,f=c.rZ,g=f[a];if(m(b))throw Error(x().Exp_FilterItemIsNull);if(a<-1||a>=K(d))throw Error(x().Exp_InvalidColumnIndex);if(e){if(d.ITa.gVa(c,5),e=d.Tq(e),a<e.col||a>=e.col+e.colCount)return;g||(g=[]),g.push(b),f[a]=g}},a.prototype.removeFilterItems=function(a,b){this.kj.ITa.gVa(this,6),this.NX(a),this.unfilter(a,b)},a.prototype.NX=function(a){var b=this,c=b.rZ;b.sZ(a),c[a]&&c.splice(a,1,i)},a.prototype.unfilter=function(a,b){var c,d,e,f=this,g=f.kj;if(g){if(g.ITa.gVa(f,2),c=[],d=f.Je,e=void 0,m(a))for(e=s(d)-1;e>=0;e--)c.push(d[e]),f.OX(d[e]);else c.push(a),f.OX(a),f.reFilter(b);b||f.tZ(g,1,c)}},a.prototype.uZ=function(a){var b,c,d,e,f,g,h=this,i=h.range;if(h.OX(a),h.rZ[a]&&i){if(b=i.col,b!==-1&&(a<b||a>=b+i.colCount))return;for(i=h.kj.Tq(i),c=i.row,d=i.rowCount,e=h.vZ(a,c,d),f=void 0,g=h.wZ(c,d,a,e),g.forEach(function(a){a.nxb()}),f=c;f<c+d;f++)h.isRowFilteredOut(f)||h.xZ(g,f,a,e);g.forEach(function(a){a.oxb()}),h.RX(a,!0)}},a.prototype.vZ=function(a,b,c){var d,e,f,g,h,i,j,k,l=this,m=l.kj,n=J(l.rZ[a]),o={},p=s(n);for(d=b;d<b+c;d++){for(h={},e=0;e<p;e++)f=n[e],"T"===f?(i=m.getText(d,a),h[f]=i?i.trim():i):"DT"===f?(j=m.getValue(d,a),k=l.mzb,h[f]=k.format(j)):"BC"===f?(g=m.getActualStyle(d,a,3,!1,!1,!0),g&&(h[f]=g.backColor)):"FC"===f?(g=m.getActualStyle(d,a,3,!1,!1,!0),g&&(h[f]=g.foreColor)):"V"===f&&(h[f]=m.getValue(d,a));o[d]=h}return o},a.prototype.filter=function(a,b){var c,d,e=this,f=e.kj,g=e.rZ;if(f){if(f.ITa.gVa(e,7),d=[],m(a))for(c=0;c<s(g);c++)g[c]&&(d.push(c),e.uZ(c));else{if(!g[a])return;d.push(a),e.uZ(a)}b||e.tZ(f,0,d)}},a.prototype.tZ=function(a,b,c){var d,e=this;a.AR&&a.AR(),d=e.SX(b,c),e.filterHandler&&e.filterHandler(d),e.onFilter(d),a.$p()},a.prototype.SX=function(a,b){var c,d,e,f=this,g=f.kj,h=f.range,i=[],j=[];if(h)for(c=g.Tq(h),d=c.row,e=void 0,e=d;e<d+c.rowCount;e++)f.isRowFilteredOut(e)?j.push(e):i.push(e);return{action:a,sheet:g,range:h,filteredRows:i,filteredOutRows:j,columns:b}},a.prototype.onFilter=function(a){},a.prototype.isFiltered=function(a){var b=this.Je;return arguments.length?q(b,a):b.length>0},a.prototype.isRowFilteredOut=function(a){var b,c,d=this,e=d.range;return!(!d.isFiltered()||!e)&&(b=e.row,c=d.LX[a],!(b!==-1&&(a<b||a>=b+e.rowCount)||s(c)>=s(d.Je)))},a.prototype.reset=function(){var a=this,b=a.kj;b&&b.ITa.gVa(a,8),a.unfilter(),a.rZ=[],a.Je=[],a.yZ=[],a.LX={},a.qZ={},a.zZ=i},a.prototype.Dl=function(a,b,c){var d,e,f,g=this,h=!1,i=g.kj,j=g.range;return j&&(j=i.Tq(j,c),d=j.row,e=j.col,f=b>=e&&b<e+j.colCount,1===c&&a===L(i,c)-1&&d-1<0&&f?h=!0:3===c&&a===d-1&&f&&(h=!0)),h},a.prototype.getFilterItems=function(a){var b=this.rZ[a];return b||[]},a.prototype.getFilteredItems=function(){return this.yZ},a.prototype.sortColumn=function(a,b){var c,d,e,f=this,g=f.kj;g&&(c=g.Tq(f.range),g.suspendPaint(),g.ITa.gVa(f,1),d={index:a,ascending:b},e=g.sortRange(c.row,c.col,c.rowCount,c.colCount,!0,[d]),e&&(f.zZ=d,f.isFiltered(a)&&f.reFilter()),g.resumePaint())},a.prototype.sortColumnByColor=function(a,b,c,d){var e,f,g,h=this,i=h.kj;i&&(e=i.Tq(h.range),i.suspendPaint(),i.ITa.gVa(h,1),f={index:a,ascending:b,color:c,isBackColor:d},g=i.sortRange(e.row,e.col,e.rowCount,e.colCount,!0,[f]),g&&(h.zZ=f,h.isFiltered(a)&&h.reFilter()),i.resumePaint())},a.prototype.getSortState=function(a){var b=this.zZ,c=0;return b&&b.index===a&&(c=b.ascending?1:2),c},a.prototype.reFilter=function(a){var b,c=this,d=c.Je,e=s(d);for(c.kj.ITa.gVa(c,13),c.LX={},c.yZ=[],c.Je=[],b=0;b<e;b++)c.filter(d[b],a)},a.prototype.openFilterDialog=function(){},a.prototype.QX=function(a,b){var c=this.LX,d=c[a];m(d)?d=[b]:(q(d,b)&&r(d,b),d.push(b)),c[a]=d},a.prototype.PX=function(a){var b=this.yZ;q(b,a)||b.push(a)},a.prototype.OX=function(a){var b,c,d=this,e=d.LX;if(d.range&&d.isFiltered(a)){d.RX(a,!1);for(c in e)c&&(b=e[c],b&&q(b,a)&&(r(b,a),s(b)||(e[c]=j)));d.sZ(a)}},a.prototype.RX=function(a,b){var c=this.Je;if(b){if(s(c)){if(c[s(c)-1]===a)return;r(c,a)}c.push(a)}else r(c,a)},a.prototype.sZ=function(a){var b,c,d=this,e=d.rZ,f=e.length,g=[],h=[];for(b=0;b<f;b++)b===a?g=g.concat(e[b]):h=h.concat(e[b]);for(c=g.length,b=0;b<c;b++)q(h,g[b])||r(d.yZ,g[b])},a.prototype.wZ=function(a,b,c,d){var e,f,g,h=this,i=h.rZ[c],k={},l=[];for(e=0;e<s(i);e++)f=i[e],2!==M(f)||0!==f.compareType()||f.forceValue2Text()||f.useWildCards()||f.ignoreCase()||f.ignoreBlank()?l.push(f):k[f.expected()]=f;for(e=a;e<a+b;e++)g=h.isRowFilteredOut(e),k[d[e].T]===j&&k[d[e].DT]===j||(h.QX(e,c),g||h.PX(f));return l},a.prototype.xZ=function(a,b,c,d){var e,f,g,h,i,j,k=this,l=k.range;for(e=0;e<s(a);e++)if(f=a[e],g=f.item1(),h=f.item2(),i=void 0,j=void 0,0===M(f)?(g&&(i=k.AZ(g,b,c,l,d)),h&&(j=k.AZ(h,b,c,l,d))):i=k.AZ(f,b,c,l,d),f.evaluate(k.kj,b,c,i,j)){k.QX(b,c),k.PX(f);break}},a.prototype.AZ=function(a,b,c,d,e){var f,g,h,j=this,k=i,m=M(a),n=a.compareType(),o=e[b];return 8!==m&&10!==m||(f=[],g=j.kj.Tq(d),h=g.col,h<=c&&c<h+g.colCount&&f.push(l(g.row,c,g.rowCount,1)),a.ranges(f)),2===m||7===m?k=o.T:3===m?0===n?k=o.BC:1===n&&(k=o.FC):k=o.V,k},a.prototype.E0a=function(a,b){var c,d,e,f,g=this,h=g.Je;return!!s(h)&&(d=g.LX[a],e=s(d),m(d)||e<0?c=!0:(f=p(d,b),c=f<0),c)},a.prototype.WY=function(a,b){var c,d,e,f,g=this,h=g.Je;return!!s(h)&&(c=-1,d=p(h,b),e=g.LX[a],f=s(e),d>0&&(c=h[d-1]),c>-1?!!f&&c===e[f-1]:!f)},a.prototype.ima=function(a,b){var c,d,e,f=this,g=f.Je;return!s(g)||(c=p(g,b),d=f.LX[a],e=s(d)||0,e>=s(g)||!(c<0||e<s(g)-1)&&(!d||p(d,b)<0))},a.prototype.rI=function(a,b,c){var d,e,f,g,h,i,k,m,n,o=this,p=o.range;if(p){if(d=L(o.kj)-b-1,e=p.row,f=p.col,g=p.rowCount,h=p.colCount,e>-1&&(d=e+g-1,a<e||c&&a===e&&0===a?o.BZ(l(e+b,f,g,h)):a<e+g&&o.BZ(l(e,f,g+b,h))),i=o.LX,o.isFiltered()&&i){for(k=[],m=void 0,m=0;m<s(i);m++)k.push(m);for(k.sort(),m=0;m<s(k);m++)n=k[m],n>=a&&n<=d&&(i[n+b]=i[n],i[n]=j)}o.reFilter()}},a.prototype.tI=function(a,b){var c,d,e,f,g,h,i,j,k,m,n,o=this,q=o.range;if(q){if(c=q.row,d=q.col,e=q.rowCount,f=q.colCount,g=0,h=-1,i=0,a>=0&&o.CZ()){for(j=0;j<b;j++)j+a<=o.DZ()&&g++;o.DZ(o.DZ()+g)}for(d>-1&&(a<=d?(h=d,i=f,o.BZ(l(c,d+b,e,f))):a<d+f&&(h=a,i=f-(a-d),o.BZ(l(c,d,e,f+b)))),h<0&&(h=0,i=K(o.kj)-b),j=h+i-1;j>=h;j--)j>=a&&(k=j+b,m=p(o.Je,j),m>=0&&(o.Je[m]=k),n=o.rZ[j],s(n)&&(r(o.rZ,j),o.rZ[k]=n));o.reFilter()}},a.prototype.GR=function(a,b){var c,d,e,f,g,h,k,m,n,o,p=this,q=p.range,r=p.kj;if(q){if(c=0,d=L(r)+b-1,e=q.row,f=q.col,g=q.rowCount,h=q.colCount,e>-1&&(c=e,d=e+g-1,a<=e?r.YBb?(p.BZ(l(a,f,g,h)),delete r.YBb):e===a+1||e+g<=a+b?p.BZ(i):e<a+b?p.BZ(l(a,f,e+g-(a+b),h)):p.BZ(l(e-b,f,g,h)):a<e+g&&p.BZ(l(e,f,g-Math.min(e+g-a,b),h))),k=p.LX,p.isFiltered()&&k)for(m=c;m<=d;m++)m>=a&&m<a+b?k[m]=j:m>=a+b&&(n=m,o=k[n],o&&(k[n-b]=o,k[n]=j));p.kj.filterRowsVisibleInfo.C_a(Math.min(e,a),g),p.reFilter()}},a.prototype.BZ=function(a){this.kj.ITa.gVa(this,14),this.range=a},a.prototype.EZ=function(a){var b,c,d,e,f,g,h,k,l,m,n,o,p=this,q=p.kj,r=p.range;if(r)if(a){if(!a.equals(r)){if(d=r.col,e=r.colCount,f=a.col,g=a.colCount,h=K(q),p.isFiltered()&&!(d<0&&f<0))for(d<0&&(d=0,e=h),f<0&&(f=0,g=h),b=0;b<e;b++)c=d+b,(f>c||c>=f+g)&&p.removeFilterItems(c);if(k=r.row,l=r.rowCount,m=a.row,n=a.rowCount,o=L(q),p.isFiltered()&&p.LX&&!(k<0&&m<0))for(k<0&&(k=0,l=o),m<0&&(m=0,n=o),b=0;b<l;b++)c=k+b,(m>c||c>=m+n)&&(p.LX[c]=j)}}else p.reset();!p.CZ()||a&&a.contains(-1,p.DZ())||(p.zZ=i),p.BZ(a)},a.prototype.HR=function(a,b){var c,d,e,f,g,h,j,k,m=this,n=m.range,o=m.DZ(),q=a+b;if(n){for(a>=0&&m.CZ()&&(o>=a&&o<q?m.DZ(-1):o>=q&&m.DZ(o-b)),c=n.row,d=n.col,e=n.rowCount,d<0?(d=0,f=K(m.kj)+b):f=n.colCount,g=d;g<d+f;g++)g>=a&&(g<q?m.removeFilterItems(g):(h=g-b,j=p(m.Je,g),k=m.rZ[g],j>=0&&(m.Je[j]=h),s(k)&&(r(m.rZ,g),m.rZ[h]=k)));d>=0&&(a<d?q<=d?m.BZ(l(c,d-b,e,f)):q<=d+f?m.BZ(l(c,a,e,d+f-q)):m.BZ(i):a<d+f&&(q<=d+f?m.BZ(l(c,d,e,f-b)):m.BZ(l(c,d,e,a-d)))),m.reFilter()}},a.prototype.CZ=function(){var a=this,b=a.zZ;return!!b&&(b.index>-1&&0!==a.getSortState(b.index))},a.prototype.DZ=function(a){var b=this,c=b.zZ,d;return s(arguments)?(c?c.index=a:b.zZ={index:a,ascending:!1},d=b):d=c?c.index:-1,d},a.prototype.$b=function(a,b,c,d){var e,f,g,h=this,i=h.range;i&&(e=l(a,b,c,d),h.filterButtonVisible()?(f=i.row-1,g=i.rowCount+1,f<0&&(f=-1,g=-1),e.containsRange(l(f,i.col,g,i.colCount))&&h.unfilter()):e.containsRange(i)&&h.unfilter())},a.prototype.fromJSON=function(a,b){var c,d,e,f,g,h,i,n,o,p,q,r,t,v,w,x,y;if(a){for(c=this,d=a.range,e=a.filterItemMap,f=c.rZ,g=a.sortInfo,h=a.showFilterButton,i=a.filterButtonVisibleInfo,d&&(c.range=l(d.row,d.col,d.rowCount,d.colCount)),a.dialogVisibleInfo&&(c.g_a=u.extend({},A,a.dialogVisibleInfo)),n=0;n<s(e);n++)for(q=e[n],r=q.conditions,o=0;o<s(r);o++)t=r[o],t&&(v=k(),w=q.index,x=f[w],v.fromJSON(t,b),x||(x=f[w]=[]),x.push(v));for(p=a.filteredColumns||a.filteredColMap,n=0;n<s(p);n++)m(p[n])||c.Je.push(p[n]);if(g&&g.color===j?c.zZ={index:g.index,ascending:g.ascending}:g&&g.color!==j&&(c.zZ={index:g.index,ascending:g.ascending,color:g.color,isBackColor:g.isBackColor}),h!==j)if(y=c.range,i)c.qZ=i;else if(y)for(n=c.oZ(y);n<c.pZ(y,c.kj);n++)c.qZ[n]=h}},a.prototype.toJSON=function(){var a,b,c,d,e,f,g,h,j,k,l=this,m={},n=l.range,o=l.qZ,p=l.rZ,q=l.Je,r=l.zZ,t=[],v=0,w=l.typeName;if(n&&(m.range=n),w&&(m.typeName=w),l.g_a){b={};for(c in l.g_a)l.g_a.hasOwnProperty(c)&&l.g_a[c]===!1&&(b[c]=!1);m.dialogVisibleInfo=b}for(a=0;a<s(p);a++)if(d=p[a]){for(e=[],f=void 0,f=0;f<s(d);f++)e.push(d[f]?d[f].toJSON():i);t[v]={index:a,conditions:e},v++}if(s(t)&&(m.filterItemMap=t),s(q)&&(m.filteredColumns=q),r&&(m.sortInfo=r),o){m.filterButtonVisibleInfo=u.extend({},o),g=!1;for(a in o)if(o[a]){g=!0;break}m.showFilterButton=g}for(h=[],k=l.kj.getRowCount(),j=0;j<k;j++)l.isRowFilteredOut(j)&&h.push(j);return h.length>0&&(m.filteredOutRows=h),m},a.prototype.TX=function(a){var b=a.options;return!b.isProtected||b.protectionOptions.allowFilter},a.prototype.hitTest=function(a,b,c){var d,e,f,h,j,k,l,m,n,o,p,q,r,s,t,u,v=this.kj;if(v){if(d=a.rowViewportIndex,e=a.colViewportIndex,f=a.hitTestType,h=v.it(e),1===f?(j=v.ut().findY(c),k=h.findX(b),l=1):3===f&&(j=v.jt(d).findY(c),k=h.findX(b),l=3),!j||!k)return i;if(m=j.row,n=k.col,o=v.getSpan(m,n,l),p=v.jZ(),o){if(q=o.row,r=o.col,m!==q+o.rowCount-1||n!==r+o.colCount-1)return i;m=q,n=r}return p&&(s=H(p,m,n,l),s&&(t=v.getCellRect(m,n,d,e),u=v.oK(t,l),u.x<=b&&b<=u.x+u.width&&u.y<=c&&c<=u.y+u.height))?new g.eY(s.rowFilter,s.row,s.col,s.sheetArea,u.x,u.y,u.width,u.height):i}},a}(),b.RowFilterBase=B,C=function(a){G(b,a);function b(b){var c=a.call(this,b)||this;return c.typeName=t,c.g_a=u.extend({},A),c.mzb=new e.Formatter.GeneralFormatter("yyyy/mm/dd"),c}return b.prototype.onFilter=function(a){var b=this,c=b.kj;c&&(b.FZ(c,a.filteredRows,a.filteredOutRows),c.au())},b.prototype.filterDialogVisibleInfo=function(a){if(0===arguments.length)return this.g_a||(this.g_a=u.extend({},A)),this.g_a;if(a){var b={};return a.sortByValue!==j&&(b.sortByValue=!!a.sortByValue),a.sortByColor!==j&&(b.sortByColor=!!a.sortByColor),a.filterByColor!==j&&(b.filterByColor=!!a.filterByColor),a.filterByValue!==j&&(b.filterByValue=!!a.filterByValue),a.listFilterArea!==j&&(b.listFilterArea=!!a.listFilterArea),u.extend(this.g_a,b),this}},b.prototype.FZ=function(a,b,c){var d,e=a.filterRowsVisibleInfo;e&&(d=e.D4(b,c),a.recalcRows&&a.recalcRows(d))},b.prototype.openFilterDialog=function(a){var b,c=this,d=c.kj;c.TX(d)&&(b=new g.gZ(d.parent.xv(),d,a,c.filterDialogVisibleInfo()),d.GZ=b,b.OT())},b}(B),b.HideRowFilter=C,D=function(){function a(){this.hZ()}return a.prototype.lZ=function(a){var b=this.rowsVisibleInfo[a];return!!m(b)||b},a.prototype.D4=function(a,b){var c,d,e,f,g,h,k,l,m=[],n=this.rowsVisibleInfo;for(c=0,d=a.length;c<d;c++)e=a[c],f=n[e],f!==j&&f!==i&&m.push(e),delete n[e];for(g=0,h=b.length;g<h;g++)k=b[g],l=n[k],l!==j&&l!==i||m.push(k),n[k]=!1;return this.empty=!1,m},a.prototype.hZ=function(){this.rowsVisibleInfo={},this.empty=!0},a.prototype.C_a=function(a,b){var c,d,e=this.rowsVisibleInfo;for(d=a;d<b;d++)c=e[d+a],m(c)||delete e[d+a]},a.prototype.mZ=function(){return!this.empty},a.prototype.clone=function(){var b=new a;return b.rowsVisibleInfo=u.extend({},this.rowsVisibleInfo),b},a}();function N(a,b,c){var e=a.typeName,f,g;e&&e!==t?(f=d.getTypeFromString(e),f&&(g=new f)):g=new C,g&&(b.xr=g,g.kj=b,g.fromJSON(a,c),g.reFilter())}E={init:function(){var a=this;a.xr=i,a.filterRowsVisibleInfo=new D,a.Wr.push({Xr:function(b){return a.filterRowsVisibleInfo.lZ(b)}})},processMouseDown:function(a){var b=a.hitInfo.filterButtonHitInfo;b&&(b.rowFilter.openFilterDialog(b),a.r=!0)},onLayoutChanged:function(a){var b,c=this,d=a.changeType,e=a.row,f=a.rowCount,g=a.col,h=a.colCount,j=c.xr;return"invalidateLayout"===d?void(c.kZ=i):void(j&&(b=c.ITa,"addRows"===d?b.hVa(e,f,a.rowExpand):"deleteRows"===d?b.iVa(e,f):"addColumns"===d?b.jVa(g,h):"deleteColumns"===d?b.kVa(g,h):"clear"===d&&j.range&&b.lVa(e,g,f,h)))},dispose:function(){var a=this,b=a._filterDialiog;b&&b.close()},toJson:function(a,b){var c=this.xr,d=b&&b.ignoreStyle;c&&!d&&(a.rowFilter=c.toJSON())},fromJson:function(a,b,c){var d=a.rowFilter,e=c&&c.ignoreStyle;d&&!e&&N(d,this,b)},paintCell:function(a){var b,c,e,f,h,i,j,k,l=this,m=a.cell,n=m.width,o=m.height,p=m.cellLayout,r=l.vu(),s=l.jZ(),t=[],u=a.ctx;if((!p||!q(t,p))&&s&&n>0&&o>0&&(b=H(s,m.row,m.col,a.sheetArea))){c=new g.eY(b.rowFilter,b.row,b.col,b.sheetArea,m.x,m.y,n,o),e=l.oK(new d.Rect(c.x,c.y,c.width,c.height),c.sheetArea),f=e.width,h=e.height,i=e.x,j=e.y,k=g.gZ.zW(c.ko()),u.save(),u.rect(c.x,c.y,c.width,c.height),u.clip(),u.beginPath(),u.lineWidth=1,u.fillStyle="#FFFFFF",u.strokeStyle="#CCCCCC",u.fillRect(i+1,j+1,f-3,h-3),u.strokeRect(i+1-.5,j+2-.5,f-3,h-4);try{r.ko(k)?u.drawImage(r.lo(k),i,j+1,f-3,h-3):r.fo(k)}catch(a){}u.beginPath(),u.restore()}}},d.Worksheet.$n("filter",E),F={init:function(){d.Commands.fVa(this.commandManager())}},d.Workbook.$n("filter",F),u.extend(d.lUa.prototype,{gVa:function(a,b){var c,d,e,f,g=this,h=g.zTa;if(h){switch(h.mVa||(h.mVa=[]),c={type:b,rowFilter:a},b){case 0:d=void 0,d=a?{range:a.range,typeName:a.typeName,Je:a.Je.concat([]),yZ:a.yZ.concat([]),rZ:a.rZ.concat([]),LX:u.extend({},a.LX),qZ:u.extend({},a.qZ),zZ:u.extend({},a.zZ)}:i,e=g.kj.filterRowsVisibleInfo.clone(),c.HUa={rowFilter:d,filterRowsVisibleInfo:e};break;case 2:case 7:c.HUa={Je:a.Je.concat([]),filterRowsVisibleInfo:g.kj.filterRowsVisibleInfo.clone()};break;case 1:c.HUa=a.zZ;break;case 3:c.HUa=u.extend({},a.qZ);break;case 4:f=arguments[2],c.HUa={col:f,val:a.qZ[f]};break;case 5:case 6:c.HUa={rZ:a.rZ.concat([]),filterRowsVisibleInfo:g.kj.filterRowsVisibleInfo.clone(),Je:a.Je.concat([])};break;case 8:c.HUa={range:a.range,Je:a.Je.concat([]),yZ:a.yZ.concat([]),LX:u.extend({},a.LX),qZ:u.extend({},a.qZ),zZ:u.extend({},a.zZ),filterRowsVisibleInfo:g.kj.filterRowsVisibleInfo.clone()};break;case 9:case 10:c.HUa={range:a.range,LX:u.extend({},a.LX),Je:a.Je.concat([]),filterRowsVisibleInfo:g.kj.filterRowsVisibleInfo.clone()};break;case 11:case 12:c.HUa={range:a.range,zZ:u.extend({},a.zZ),rZ:a.rZ.concat([]),Je:a.Je.concat([]),filterRowsVisibleInfo:g.kj.filterRowsVisibleInfo.clone()};break;case 13:c.HUa={filteredInRowsWithColIndexs:u.extend({},a.LX),filteredItems:a.yZ.concat([]),filteredColumns:a.Je.concat([])};break;case 14:c.HUa={range:a.range}}h.mVa.push(c)}},nVa:function(a){var b=a.rowFilter,c=a.HUa;if(b||0===a.type)switch(a.type){case 0:c.rowFilter?this.kj.xr=u.extend(b,c.rowFilter):this.kj.xr=c.rowFilter,this.kj.filterRowsVisibleInfo=c.filterRowsVisibleInfo;break;case 1:b.zZ=c;break;case 3:b.qZ=c;break;case 4:b.qZ[c.col]=c.val;break;case 5:case 6:b.rZ=c.rZ,b.Je=c.Je,this.kj.filterRowsVisibleInfo=c.filterRowsVisibleInfo;break;case 2:case 7:b.Je=c.Je,this.kj.filterRowsVisibleInfo=c.filterRowsVisibleInfo;break;case 8:u.extend(b,c),this.kj.filterRowsVisibleInfo=c.filterRowsVisibleInfo;break;case 9:case 10:b.range=c.range,b.LX=c.LX,b.Je=c.Je,this.kj.filterRowsVisibleInfo=c.filterRowsVisibleInfo;break;case 11:case 12:b.range=c.range,b.zZ=c.zZ,b.rZ=c.rZ,b.Je=c.Je,this.kj.filterRowsVisibleInfo=c.filterRowsVisibleInfo;break;case 13:b.LX=c.filteredInRowsWithColIndexs,b.yZ=c.filteredItems,b.Je=c.filteredColumns;break;case 14:b.range=c.range}},oVa:function(a){var b,c=s(a);for(b=c-1;b>=0;b--)this.nVa(a[b])},hVa:function(a,b,c){var d=this.kj.xr;d&&(this.gVa(d,9),d.rI(a,b,c))},iVa:function(a,b){var c=this.kj.xr;c&&(this.gVa(c,10),c.GR(a,b))},jVa:function(a,b){var c=this.kj.xr;c&&(this.gVa(c,11),c.tI(a,b))},kVa:function(a,b){var c=this.kj.xr;c&&(this.gVa(c,12),c.HR(a,b))},lVa:function(a,b,c,d){var e=this.kj.xr;e&&e.$b(a,b,c,d)}}),d.lUa.$n("filter",{undo:function(a){var b=a.mVa;s(b)&&this.oVa(b)}})},"./dist/plugins/filter/filter.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidColumnIndex="Invalid column index.",b.SortAscending="Sort Ascending",b.SortDescending="Sort Descending",b.OK="OK",b.Cancel="Cancel",b.Search="Search",b.CheckAll="Check all",b.UncheckAll="Uncheck all",b.Blanks="(Blanks)",b.Exp_FilterItemIsNull="FilterItem is null.",b.Show="Show",b.ShowRows="Show rows where:",b.And="And",b.Or="Or",b.SortColor="Sort By Color",b.FilterColor="Filter By Color",b.FilterCellTitle="Filter By Cell Color",b.FilterFontTitle="Filter By Font Color",b.SortCellTitle="Sort By Cell Color",b.SortFontTitle="Sort By Font Color",b.FontColor="More Font Colors...",b.CellColor="More Cell Colors...",b.NoFill="No Fill",b.Automatic="Automatic",b.Clear="Clear Filter From {0}",b.TextFilter="Text Filters",b.DateFilter="Date Filters",b.NumberFilter="Number Filters",b.Custom="Custom Filter...",b.Equal="Equals...",b.NotEqual="Does Not Equal...",b.GreaterThan="Greater Than...",b.GreaterOrEquals="Greater Than Or Equal To...",b.LessThan="Less Than...",b.LessThanOrEquals="Less Than Or Equal To...",b.Between="Between...",b.Top10="Top 10...",b.AboveAverage="Above Average",b.BelowAverage="Below Average",b.Begin="Begins With...",b.End="Ends With...",b.Contain="Contains...",b.NotContain="Does Not Contain...",b.Before="Before...",b.After="After...",b.Tomorrow="Tomorrow",b.Today="Today",b.Yesterday="Yesterday",b.NextWeek="Next Week",b.ThisWeek="This Week",b.LastWeek="Last Week",b.NextMonth="Next Month",b.ThisMonth="This Month",b.LastMonth="Last Month",b.NextQuarter="Next Quarter",b.ThisQuarter="This Quarter",b.LastQuarter="Last Quarter",b.NextYear="Next Year",b.ThisYear="This Year",b.LastYear="Last Year",b.YearToDate="Year To Date",b.AllDates="All Dates in Period",b.Top10Filter="Top 10 Auto Filter",b.CustomTitle="Custom AutoFilter",b.ColorTitle="Available Cell Colors",b.top="top",b.bottom="bottom",b.SortCell="Select a cell color to sort by:",b.SortFont="Select a font color to sort by:",b.FilterCell="Select a cell color to filter by:",b.FilterFont="Select a font color to filter by:",b.Selected="Selected:",b.IsEquals="equals",b.NotEquals="does not equal",b.IsGreaterThan="is greater than",b.IsGreaterOrEqual="is greater than or equal to",b.IsLess="is less than",b.LessOrEqual="is less than or equal to",b.IsBeginWith="begins with",b.NotBeginWith="does not begin with",b.IsEndWith="ends with",b.NotEndWith="does not end with",b.IsContain="contains",b.NotContains="does not contain",b.IsAfter="is after",b.AfterOrEqual="is after or equal to",b.IsBefore="is before",b.BeforeOrEqual="is before or equal to",b.Q1="Quarter 1",b.Q2="Quarter 2",b.Q3="Quarter 3",b.Q4="Quarter 4",b.Jan="January",b.Feb="February",b.Mar="March",b.Apr="April",b.May="May",b.Jun="June",b.Jul="July",b.Aug="August",b.Sep="September",b.Oct="October",b.Nov="November",b.Dec="December",b.Explain1="Use ? to represent any single character",b.Explain2="Use * to represent any series of characters",b.Year="",b.Day=""},CellTypes:function(a,b){a.exports=GC.Spread.Sheets.CellTypes},Common:function(a,b){a.exports=GC.Spread},ConditionalFormatting:function(a,b){a.exports=GC.Spread.Sheets.ConditionalFormatting},Core:function(a,b){a.exports=GC.Spread.Sheets}});