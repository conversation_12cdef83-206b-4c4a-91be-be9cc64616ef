/*
 * @Description: 组件配置描述json
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-07 15:58:35
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-08-11 08:56:50
 */
const comJson = {
  json: [
    {
      type: "input",
      col: "companyName",
      label: "公司",
      props: {}
    },
    {
      type: "input",
      col: "baseLabelName",
      label: "基地下拉框名称",
      props: {}
    },
    {
      type: "input",
      col: "indexCardDetailInfoJSUrl",
      label: "指标卡片详情组件jsUrl",
      props: {}
    }
  ],
  props: {
    companyName: "空调",
    baseLabelName: "基地",
    indexCardDetailInfoJSUrl: "/smcbucket/IndexCardDetailInfo.umd.min.1.0.js"
  },
  buttonList: []
};
export default comJson;
