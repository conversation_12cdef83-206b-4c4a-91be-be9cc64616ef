<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-11-13 16:30:40
-->
<template>
  <div style="display: inline-block;">
    <a-button  :size="size" @click="btClick">
      下载模板
    </a-button>
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import { pureAxios } from "@/utils/requestHttp";
export default {
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      pureAxios({
        url: "/smc2/indexPerfect/export",
        method: "post",
        data: {},
        responseType: "blob"
      })
        .then(response => {
          if (!response.data) {
            return;
          }
          const fileName = `注册表导入模板-${new Date().getTime()}.xlsx`;
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message
          });
        });
    }
  }
};
</script>
