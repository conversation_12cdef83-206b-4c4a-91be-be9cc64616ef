<!--
 * @Description: 自定义按钮组件实例
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-07 15:58:35
 * @LastEditors: g<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-05-31 17:41:57
-->
<template>
  <div>
    <a-button
      @click="methodsObj.handleClick"
      :type="type"
      :shape="shape"
      :size="size"
      :icon="icon"
      :block="block"
    >
      {{ descript ? $t(descript) : "按钮表述" }}
    </a-button>
  </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import request from "@/utils/requestHttp";
import ComFunMixins from "pangea-com/lib/ComFunMixins";
export default {
  name: "ButtonDemo",
  mixins: [ComFunMixins],
  props: {
    comKey: [String, Number],
    data: Object,
    json: Object
  },
  data() {
    return {
      methodsObj: {
        handleClick: async () => {}
      }
    };
  },
  computed: {
    // 按钮形状
    shape() {
      const { shape } = this.data.props;
      return shape === "default" ? null : shape;
    },
    // 按钮文本
    descript() {
      return this.data.props.descript;
    },
    // 按钮类型
    type() {
      return this.data.props.btntype;
    },
    // 按钮尺寸
    size() {
      return this.data.props.size;
    },
    // 按钮图标
    icon() {
      return this.data.props.icon;
    },
    // 是否占满容器全部宽度
    block() {
      const { block } = this.data.props;
      return block === "true";
    }
  }
};
</script>
