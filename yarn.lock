# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@achrinza/node-ipc@9.2.2":
  "integrity" "sha512-b90U39dx0cU6emsOvy5hxU4ApNXnE3+Tuo8XQZfiKTGelDwpMwBVgBP7QX6dGTcJgu/miyJuNJ/2naFBliNWEw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@achrinza/node-ipc/-/node-ipc-9.2.2.tgz"
  "version" "9.2.2"
  dependencies:
    "@node-ipc/js-queue" "2.0.3"
    "event-pubsub" "4.3.0"
    "js-message" "1.0.7"

"@ampproject/remapping@^2.1.0":
  "integrity" "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@ampproject/remapping/-/remapping-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@ant-design/colors@^3.1.0":
  "integrity" "sha512-YKgNbG2dlzqMhA9NtI3/pbY16m3Yl/EeWBRa+lB1X1YaYxHrxNexiQYCLTWO/uDvAjLFMEDU+zR901waBtMtjQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@ant-design/colors/-/colors-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "tinycolor2" "^1.4.1"

"@ant-design/icons-vue@^2.0.0":
  "integrity" "sha512-2c0QQE5hL4N48k5NkPG5sdpMl9YnvyNhf0U7YkdZYDlLnspoRU7vIA0UK9eHBs6OpFLcJB6o8eJrIl2ajBskPg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@ant-design/icons-vue/-/icons-vue-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@ant-design/colors" "^3.1.0"
    "babel-runtime" "^6.26.0"

"@ant-design/icons@^2.0.0", "@ant-design/icons@^2.1.1":
  "integrity" "sha512-jCH+k2Vjlno4YWl6g535nHR09PwCEmTBKAG6VqF+rhkrSPRLfgpU2maagwbZPLjaHuU5Jd1DFQ2KJpQuI6uG8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@ant-design/icons/-/icons-2.1.1.tgz"
  "version" "2.1.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6":
  "integrity" "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/code-frame/-/code-frame-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.20.0", "@babel/compat-data@^7.20.1":
  "integrity" "sha512-EWZ4mE2diW3QALKvDMiXnbZpRvlj+nayZ112nK93SnhqOtpdsbVD4W+2tEoT3YNBAG9RBR0ISY758ZkOgsn6pQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/compat-data/-/compat-data-7.20.1.tgz"
  "version" "7.20.1"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.11.0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.4.0-0":
  "integrity" "sha512-w7DbG8DtMrJcFOi4VrLm+8QM4az8Mo+PuLBKLp2zrYRCow8W/f9xiXm5sN53C8HksCyDQwCKha9JiDoIyPjT2g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/core/-/core-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.2"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-module-transforms" "^7.20.2"
    "@babel/helpers" "^7.20.1"
    "@babel/parser" "^7.20.2"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.2"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.1"
    "semver" "^6.3.0"

"@babel/generator@^7.20.1", "@babel/generator@^7.20.2":
  "integrity" "sha512-luCf7yk/cm7yab6CAW1aiFnmEfBJplb/JojV56MYEK7ziWfGmFlTfmL9Ehwfy4gFhbjBfWO1wj7/TuSbVNEEtA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/generator/-/generator-7.20.4.tgz"
  "version" "7.20.4"
  dependencies:
    "@babel/types" "^7.20.2"
    "@jridgewell/gen-mapping" "^0.3.2"
    "jsesc" "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  "integrity" "sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  "integrity" "sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.0", "@babel/helper-compilation-targets@^7.9.6":
  "integrity" "sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/compat-data" "^7.20.0"
    "@babel/helper-validator-option" "^7.18.6"
    "browserslist" "^4.21.3"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.20.2":
  "integrity" "sha512-k22GoYRAHPYr9I+Gvy2ZQlAe5mGy8BqWst2wRt8cwIufWTxrsVshhIBvYNqC80N0GSFWTsqRVexOtfzlgOEDvA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.19.1"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.19.0":
  "integrity" "sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "regexpu-core" "^5.1.0"

"@babel/helper-define-polyfill-provider@^0.3.3":
  "integrity" "sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  "integrity" "sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz"
  "version" "7.18.9"

"@babel/helper-explode-assignable-expression@^7.18.6":
  "integrity" "sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0":
  "integrity" "sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/types" "^7.19.0"

"@babel/helper-hoist-variables@^7.18.6":
  "integrity" "sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.18.9":
  "integrity" "sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.8.3":
  "integrity" "sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.18.6", "@babel/helper-module-transforms@^7.19.6", "@babel/helper-module-transforms@^7.20.2":
  "integrity" "sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.2"

"@babel/helper-optimise-call-expression@^7.18.6":
  "integrity" "sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz"
  "version" "7.20.2"

"@babel/helper-remap-async-to-generator@^7.18.6", "@babel/helper-remap-async-to-generator@^7.18.9":
  "integrity" "sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-wrap-function" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.19.1":
  "integrity" "sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/traverse" "^7.19.1"
    "@babel/types" "^7.19.0"

"@babel/helper-simple-access@^7.19.4", "@babel/helper-simple-access@^7.20.2":
  "integrity" "sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-skip-transparent-expression-wrappers@^7.18.9":
  "integrity" "sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/types" "^7.20.0"

"@babel/helper-split-export-declaration@^7.18.6":
  "integrity" "sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.25.9":
  "integrity" "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  "version" "7.25.9"

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1", "@babel/helper-validator-identifier@^7.25.9":
  "integrity" "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  "version" "7.25.9"

"@babel/helper-validator-option@^7.18.6":
  "integrity" "sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz"
  "version" "7.18.6"

"@babel/helper-wrap-function@^7.18.9":
  "integrity" "sha512-txX8aN8CZyYGTwcLhlk87KRqncAzhh5TpQamZUa0/u3an36NtDpUP6bQgBCBcLeBs09R/OwQu3OjK0k/HwfNDg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helper-wrap-function/-/helper-wrap-function-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-function-name" "^7.19.0"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.0"
    "@babel/types" "^7.19.0"

"@babel/helpers@^7.20.1":
  "integrity" "sha512-J77mUVaDTUJFZ5BpP6mMn6OIl3rEWymk2ZxDBQJUG3P+PbmyMcF3bYWvz0ma69Af1oobDqT/iAsvzhB58xhQUg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/helpers/-/helpers-7.20.1.tgz"
  "version" "7.20.1"
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.0"

"@babel/highlight@^7.18.6":
  "integrity" "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/highlight/-/highlight-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.18.10", "@babel/parser@^7.18.4", "@babel/parser@^7.20.1", "@babel/parser@^7.20.2", "@babel/parser@^7.25.3", "@babel/parser@^7.7.0":
  "integrity" "sha512-WJ/CvmY8Mea8iDXo6a7RK2wbmJITT5fN3BEkRuFlxVyNx8jOKIIhmC4fSkTcPcf8JyavbBwIe6OpiCOBXt/IcA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/parser/-/parser-7.26.3.tgz"
  "version" "7.26.3"
  dependencies:
    "@babel/types" "^7.26.3"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.18.6":
  "integrity" "sha512-Dgxsyg54Fx1d4Nge8UnvTrED63vrwOdPmyvPzlNN/boaliRP54pm3pGzZD1SJUwrBA+Cs/xdG8kXX6Mn/RfISQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.18.9":
  "integrity" "sha512-AHrP9jadvH7qlOj6PINbgSuphjQUAK7AOT7DPjBo9EHoLhQTnnK5u45e1Hd4DbSQEO9nqPWtQ89r+XEOWFScKg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"

"@babel/plugin-proposal-async-generator-functions@^7.20.1":
  "integrity" "sha512-Gh5rchzSwE4kC+o/6T8waD0WHEQIsDmjltY8WnWRXHUdH8axZhuH86Ov9M72YhJfDrZseQwuuWaaIT/TmePp3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.1.tgz"
  "version" "7.20.1"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.18.6", "@babel/plugin-proposal-class-properties@^7.8.3":
  "integrity" "sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-static-block@^7.18.6":
  "integrity" "sha512-+I3oIiNxrCpup3Gi8n5IGMwj0gOCAjcJUSQEcotNnCCPMEnixawOQ+KeJPlgfjzx+FKQ1QSyZOWe7wmoJp7vhw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.8.3":
  "integrity" "sha512-nkBH96IBmgKnbHQ5gXFrcmez+Z9S2EIDKDQGp005ROqBigc88Tky4rzCnlP/lnlj245dCEQl4/YyV0V1kYh5dw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.20.2"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.19.1"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/plugin-syntax-decorators" "^7.19.0"

"@babel/plugin-proposal-dynamic-import@^7.18.6":
  "integrity" "sha512-1auuwmK+Rz13SJj36R+jqFPMJWyKEDd7lLSdOj4oJK0UTgGueSAtkrCvz9ewmgyU/P941Rv2fQwZJN8s6QruXw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.18.9":
  "integrity" "sha512-k1NtHyOMvlDDFeb9G5PhUXuGj8m/wiwojgQVEhJ/fsVsMCpLyOP4h0uGEjYJKrRI+EVPlb5Jk+Gt9P97lOGwtA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.18.6":
  "integrity" "sha512-lr1peyn9kOdbYc0xr0OdHTZ5FMqS6Di+H0Fz2I/JwMzGmzJETNeOFq2pBySw6X/KFL5EWDjlJuMsUGRFb8fQgQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.18.9":
  "integrity" "sha512-128YbMpjCrP35IOExw2Fq+x55LMP42DzhOhX2aNNIdI9avSWl2PI0yuBWarr3RYpZBSPtabfadkH2yeRiMD61Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.18.6":
  "integrity" "sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.18.6":
  "integrity" "sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.20.2":
  "integrity" "sha512-Ks6uej9WFK+fvIMesSqbAto5dD8Dz4VuuFvGJFKgIGSkJuRGcrwGECPA1fDgQK3/DbExBJpEkTeYeB8geIFCSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/compat-data" "^7.20.1"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.1"

"@babel/plugin-proposal-optional-catch-binding@^7.18.6":
  "integrity" "sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.18.9":
  "integrity" "sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.18.6":
  "integrity" "sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@^7.18.6":
  "integrity" "sha512-9Rysx7FOctvT5ouj5JODjAFAkgGoudQuLPamZb0v1TGLpapdNaftzifU8NTWQm0IRjqoYypdrSmyWgkocDQ8Dw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.18.6", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.19.0":
  "integrity" "sha512-xaBZUEDntt4faL1yN8oIFlhfXeQAWJW7CLKYsHTUqriCUbj8xOra8bfxxKGi/UwExPFBuPdH4XfHc9rGQhrVkQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.20.0":
  "integrity" "sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  "integrity" "sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.18.6":
  "integrity" "sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-async-to-generator@^7.18.6":
  "integrity" "sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-remap-async-to-generator" "^7.18.6"

"@babel/plugin-transform-block-scoped-functions@^7.18.6":
  "integrity" "sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.20.2":
  "integrity" "sha512-y5V15+04ry69OV2wULmwhEA6jwSWXO1TwAtIwiPXcvHcoOQUqpyMVd2bDsQJMW8AurjulIyUV8kDqtjSwHy1uQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-classes@^7.20.2":
  "integrity" "sha512-9rbPp0lCVVoagvtEyQKSo5L8oo0nQS/iif+lwlAz29MccX2642vWDlSZK+2T2buxbopotId2ld7zZAzRfz9j1g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-classes/-/plugin-transform-classes-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.19.1"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.18.9":
  "integrity" "sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-destructuring@^7.20.2":
  "integrity" "sha512-mENM+ZHrvEgxLTBXUiQ621rRXZes3KWUv6NdQlrnr1TkWVw+hUjQBZuP2X32qKlrlG2BzgR95gkuCRSkJl8vIw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-dotall-regex@^7.18.6", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha512-6S3jpun1eEbAxq7TdjLotAsl4WpQI9DxfkycRcKrjhQYzU87qpXdknpBg/e+TdcMehqGnLFi7tnFUBR02Vq6wg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-duplicate-keys@^7.18.9":
  "integrity" "sha512-d2bmXCtZXYc59/0SanQKbiWINadaJXqtvIQIzd4+hNwkWBgyCd5F/2t1kXoUdvPMrxzPvhK6EMQRROxsue+mfw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-exponentiation-operator@^7.18.6":
  "integrity" "sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-for-of@^7.18.8":
  "integrity" "sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.8.tgz"
  "version" "7.18.8"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-function-name@^7.18.9":
  "integrity" "sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.18.9":
  "integrity" "sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.18.6":
  "integrity" "sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-amd@^7.19.6":
  "integrity" "sha512-uG3od2mXvAtIFQIh0xrpLH6r5fpSQN04gIVovl+ODLdUMANokxQLZnPBHcjmv3GxRjnqwLuHvppjjcelqUFZvg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.19.6.tgz"
  "version" "7.19.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.19.6"
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-modules-commonjs@^7.19.6":
  "integrity" "sha512-8PIa1ym4XRTKuSsOUXqDG0YaOlEuTVvHMe5JCfgBMOtHvJKw/4NGovEGN33viISshG/rZNVrACiBmPQLvWN8xQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.19.6.tgz"
  "version" "7.19.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.19.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-simple-access" "^7.19.4"

"@babel/plugin-transform-modules-systemjs@^7.19.6":
  "integrity" "sha512-fqGLBepcc3kErfR9R3DnVpURmckXP7gj7bAlrTQyBxrigFqszZCkFkcoxzCp2v32XmwXLvbw+8Yq9/b+QqksjQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.19.6.tgz"
  "version" "7.19.6"
  dependencies:
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-module-transforms" "^7.19.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-validator-identifier" "^7.19.1"

"@babel/plugin-transform-modules-umd@^7.18.6":
  "integrity" "sha512-dcegErExVeXcRqNtkRU/z8WlBLnvD4MRnHgNs3MytRO1Mn1sHRyhbcpYbVMGclAqOjdW+9cfkdZno9dFdfKLfQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-module-transforms" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-named-capturing-groups-regex@^7.19.1":
  "integrity" "sha512-oWk9l9WItWBQYS4FgXD4Uyy5kq898lvkXpXQxoJEY1RnvPk4R/Dvu2ebXU9q8lP+rlMwUQTFf2Ok6d78ODa0kw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.19.1.tgz"
  "version" "7.19.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.19.0"
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-new-target@^7.18.6":
  "integrity" "sha512-DjwFA/9Iu3Z+vrAn+8pBUGcjhxKguSMlsFqeCKbhb9BAV756v0krzVK04CRDi/4aqmk8BsHb4a/gFcaA5joXRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.18.6":
  "integrity" "sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.20.1":
  "integrity" "sha512-oZg/Fpx0YDrj13KsLyO8I/CX3Zdw7z0O9qOd95SqcoIzuqy/WTGWvePeHAnZCN54SfdyjHcb1S30gc8zlzlHcA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.3.tgz"
  "version" "7.20.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-property-literals@^7.18.6":
  "integrity" "sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-regenerator@^7.18.6":
  "integrity" "sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "regenerator-transform" "^0.15.0"

"@babel/plugin-transform-reserved-words@^7.18.6":
  "integrity" "sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-runtime@^7.11.0":
  "integrity" "sha512-PRH37lz4JU156lYFW1p8OxE5i7d6Sl/zV58ooyr+q1J1lnQPyg5tIiXlIwNVhJaY4W3TmOtdc8jqdXQcB1v5Yw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.19.6.tgz"
  "version" "7.19.6"
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.18.6":
  "integrity" "sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.19.0":
  "integrity" "sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-spread/-/plugin-transform-spread-7.19.0.tgz"
  "version" "7.19.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"

"@babel/plugin-transform-sticky-regex@^7.18.6":
  "integrity" "sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.18.9":
  "integrity" "sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typeof-symbol@^7.18.9":
  "integrity" "sha512-SRfwTtF11G2aemAZWivL7PD+C9z52v9EvMqH9BuYbabyPuKUvSWks3oCg6041pT925L4zVFqaVBeECwsmlguEw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-escapes@^7.18.10":
  "integrity" "sha512-kKAdAI+YzPgGY/ftStBFXTI1LZFju38rYThnfMykS+IXy8BVx+res7s2fxf1l8I35DV2T97ezo6+SGrXz6B3iQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-unicode-regex@^7.18.6":
  "integrity" "sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-env@^7.11.0":
  "integrity" "sha512-1G0efQEWR1EHkKvKHqbG+IN/QdgwfByUpM5V5QroDzGV2t3S/WXNQd693cHiHTlCFMpr9B6FkPFXDA2lQcKoDg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/preset-env/-/preset-env-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/compat-data" "^7.20.1"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-validator-option" "^7.18.6"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.18.6"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-async-generator-functions" "^7.20.1"
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-proposal-class-static-block" "^7.18.6"
    "@babel/plugin-proposal-dynamic-import" "^7.18.6"
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/plugin-proposal-json-strings" "^7.18.6"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.18.9"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.6"
    "@babel/plugin-proposal-numeric-separator" "^7.18.6"
    "@babel/plugin-proposal-object-rest-spread" "^7.20.2"
    "@babel/plugin-proposal-optional-catch-binding" "^7.18.6"
    "@babel/plugin-proposal-optional-chaining" "^7.18.9"
    "@babel/plugin-proposal-private-methods" "^7.18.6"
    "@babel/plugin-proposal-private-property-in-object" "^7.18.6"
    "@babel/plugin-proposal-unicode-property-regex" "^7.18.6"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.20.0"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.18.6"
    "@babel/plugin-transform-async-to-generator" "^7.18.6"
    "@babel/plugin-transform-block-scoped-functions" "^7.18.6"
    "@babel/plugin-transform-block-scoping" "^7.20.2"
    "@babel/plugin-transform-classes" "^7.20.2"
    "@babel/plugin-transform-computed-properties" "^7.18.9"
    "@babel/plugin-transform-destructuring" "^7.20.2"
    "@babel/plugin-transform-dotall-regex" "^7.18.6"
    "@babel/plugin-transform-duplicate-keys" "^7.18.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.18.6"
    "@babel/plugin-transform-for-of" "^7.18.8"
    "@babel/plugin-transform-function-name" "^7.18.9"
    "@babel/plugin-transform-literals" "^7.18.9"
    "@babel/plugin-transform-member-expression-literals" "^7.18.6"
    "@babel/plugin-transform-modules-amd" "^7.19.6"
    "@babel/plugin-transform-modules-commonjs" "^7.19.6"
    "@babel/plugin-transform-modules-systemjs" "^7.19.6"
    "@babel/plugin-transform-modules-umd" "^7.18.6"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.19.1"
    "@babel/plugin-transform-new-target" "^7.18.6"
    "@babel/plugin-transform-object-super" "^7.18.6"
    "@babel/plugin-transform-parameters" "^7.20.1"
    "@babel/plugin-transform-property-literals" "^7.18.6"
    "@babel/plugin-transform-regenerator" "^7.18.6"
    "@babel/plugin-transform-reserved-words" "^7.18.6"
    "@babel/plugin-transform-shorthand-properties" "^7.18.6"
    "@babel/plugin-transform-spread" "^7.19.0"
    "@babel/plugin-transform-sticky-regex" "^7.18.6"
    "@babel/plugin-transform-template-literals" "^7.18.9"
    "@babel/plugin-transform-typeof-symbol" "^7.18.9"
    "@babel/plugin-transform-unicode-escapes" "^7.18.10"
    "@babel/plugin-transform-unicode-regex" "^7.18.6"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.20.2"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "core-js-compat" "^3.25.1"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/runtime@^7.11.0", "@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.14.0", "@babel/runtime@^7.8.4":
  "integrity" "sha512-mrzLkl6U9YLF8qpqI7TB82PESyEGjm/0Ly91jG575eVxMMlb8fYfOXFZIJ8XfLrJZQbm7dlKry2bJmXBUEkdFg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/runtime/-/runtime-7.20.1.tgz"
  "version" "7.20.1"
  dependencies:
    "regenerator-runtime" "^0.13.10"

"@babel/template@^7.0.0", "@babel/template@^7.18.10":
  "integrity" "sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/template/-/template-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.19.0", "@babel/traverse@^7.19.1", "@babel/traverse@^7.20.1", "@babel/traverse@^7.7.0":
  "integrity" "sha512-d3tN8fkVJwFLkHkBN479SOsw4DMZnz8cdbL/gvuDuzy3TS6Nfw80HuQqhw1pITbIruHyh7d1fMA47kWzmcUEGA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/traverse/-/traverse-7.20.1.tgz"
  "version" "7.20.1"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.1"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.20.1"
    "@babel/types" "^7.20.0"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.10", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.19.0", "@babel/types@^7.20.0", "@babel/types@^7.20.2", "@babel/types@^7.26.3", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  "integrity" "sha512-vN5p+1kl59GVKMvTHt55NzzmYVxprfJD+ql7U9NFIfKCBkYE55LYtS+WtPlaYOyzydrKI8Nezd+aZextrd+FMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@babel/types/-/types-7.26.3.tgz"
  "version" "7.26.3"
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@hapi/address@2.x.x":
  "integrity" "sha512-QD1PhQk+s31P1ixsX0H0Suoupp3VMXzIVMSwobR3F3MSUO2YCV0B7xqLcUw/Bh8yuvd3LhpyqLQWTNcRmp6IdQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@hapi/address/-/address-2.1.4.tgz"
  "version" "2.1.4"

"@hapi/bourne@1.x.x":
  "integrity" "sha512-1dVNHT76Uu5N3eJNTYcvxee+jzX4Z9lfciqRRHCU27ihbUcYi+iSc2iml5Ke1LXe1SyJCLA0+14Jh4tXJgOppA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@hapi/bourne/-/bourne-1.3.2.tgz"
  "version" "1.3.2"

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  "integrity" "sha512-yN7kbciD87WzLGc5539Tn0sApjyiGHAJgKvG9W8C7O+6c7qmoQMfVs0W4bX17eqz6C78QJqqFrtgdK5EWf6Qow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@hapi/hoek/-/hoek-8.5.1.tgz"
  "version" "8.5.1"

"@hapi/joi@^15.0.1":
  "integrity" "sha512-entf8ZMOK8sc+8YfeOlM8pCfg3b5+WZIKBfUaaJT8UsjAAPjartzxIYm3TIbjvA4u+u++KbcXD38k682nVHDAQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@hapi/joi/-/joi-15.1.1.tgz"
  "version" "15.1.1"
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  "integrity" "sha512-tAag0jEcjwH+P2quUfipd7liWCNX2F8NvYjQp2wtInsZxnMlypdw0FtAOLxtvvkO+GSRRbmNi8m/5y42PQJYCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@hapi/topo/-/topo-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  "integrity" "sha512-zN69TnSr0viRSU6cEDIcuPcP67QcpQ6uHACg58FiN9PDrU6SLyGW3MR4tiISbYxy1kDWAVPwD+XwQTWE5cigAA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@intervolga/optimize-cssnano-plugin/-/optimize-cssnano-plugin-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "cssnano" "^4.0.0"
    "cssnano-preset-default" "^4.0.0"
    "postcss" "^7.0.0"

"@jridgewell/gen-mapping@^0.1.0":
  "integrity" "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.2":
  "integrity" "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  "integrity" "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
  "version" "3.1.0"

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  "integrity" "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  "version" "1.1.2"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@1.4.14":
  "integrity" "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  "version" "1.4.14"

"@jridgewell/sourcemap-codec@^1.5.0":
  "integrity" "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  "version" "1.5.0"

"@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz"
  "version" "0.3.17"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@microsoft/fetch-event-source@^2.0.1":
  "integrity" "sha512-W6CLUJ2eBMw3Rec70qrsEW0jOm/3twwJv21mrmj2yORiaVmVYGS4sSS5yUwvQc1ZlDLYGPnClVWmUUMagKNsfA=="
  "resolved" "https://registry.npmjs.org/@microsoft/fetch-event-source/-/fetch-event-source-2.0.1.tgz"
  "version" "2.0.1"

"@mrmlnc/readdir-enhanced@^2.2.1":
  "integrity" "sha512-bPHp6Ji8b41szTOcaP63VlnbbO5Ny6dwAATtY6JTjh5N2OLrb5Qk/Th5cRkRQhkWCt+EJsYrNB0MiL+Gpn6e3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "call-me-maybe" "^1.0.1"
    "glob-to-regexp" "^0.3.0"

"@node-ipc/js-queue@2.0.3":
  "integrity" "sha512-fL1wpr8hhD5gT2dA1qifeVaoDFlQR5es8tFuKqjHX+kdOtdNHnxkVZbtIrR2rxnMFvehkjaZRNV2H/gPXlb0hw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@node-ipc/js-queue/-/js-queue-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "easy-stack" "1.0.1"

"@nodelib/fs.stat@^1.1.2":
  "integrity" "sha512-shAmDyaQC4H92APFoIaVDHCx5bStIocgvbwQyxPRrbUY20V1EYTbSDchWbuwlMG3V17cprZhA6+78JfB+3DTPw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@nodelib/fs.stat/-/fs.stat-1.1.3.tgz"
  "version" "1.1.3"

"@simonwep/pickr@~1.7.0":
  "integrity" "sha512-fq7jgKJT21uWGC1mARBHvvd1JYlEf93o7SuVOB4Lr0x/2UPuNC9Oe9n/GzVeg4oVtqMDfh1wIEJpsdOJEZb+3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@simonwep/pickr/-/pickr-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "core-js" "^3.6.5"
    "nanopop" "^2.1.0"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  "integrity" "sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "chalk" "^3.0.0"
    "error-stack-parser" "^2.0.6"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"@soda/get-current-script@^1.0.0":
  "integrity" "sha512-T7VNNlYVM1SgQ+VsMYhnDkcGmWhQdL0bDyGm5TlQ3GBXnJscEClUUOKduWTmm2zCnvNLC1hc3JpuXjs/nFOc5w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@soda/get-current-script/-/get-current-script-1.0.2.tgz"
  "version" "1.0.2"

"@transloadit/prettier-bytes@0.0.7":
  "integrity" "sha512-VeJbUb0wEKbcwaSlj5n+LscBl9IPgLPkHVGBkh00cztv6X4L/TJXK58LzFuBKX7/GAfiGhIwH67YTLTlzvIzBA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@transloadit/prettier-bytes/-/prettier-bytes-0.0.7.tgz"
  "version" "0.0.7"

"@types/body-parser@*":
  "integrity" "sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/body-parser/-/body-parser-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  "integrity" "sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/connect/-/connect-3.4.35.tgz"
  "version" "3.4.35"
  dependencies:
    "@types/node" "*"

"@types/event-emitter@^0.3.3":
  "integrity" "sha512-UfnOK1pIxO7P+EgPRZXD9jMpimd8QEFcEZ5R67R1UhGbv4zghU5+NE7U8M8G9H5Jc8FI51rqDWQs6FtUfq2e/Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/event-emitter/-/event-emitter-0.3.3.tgz"
  "version" "0.3.3"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  "integrity" "sha512-DxMhY+NAsTwMMFHBTtJFNp5qiHKJ7TeqOo23zVEM9alT1Ml27Q3xcTH0xwxn7Q0BbMcVEJOs/7aQtUWupUQN3Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/express-serve-static-core/-/express-serve-static-core-4.17.31.tgz"
  "version" "4.17.31"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  "integrity" "sha512-TEbt+vaPFQ+xpxFLFssxUDXj5cWCxZJjIcB7Yg0k0GMHGtgtQgpvx/MUQUeAkNbA9AAGrwkAsoeItdTgS7FMyg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/express/-/express-4.17.14.tgz"
  "version" "4.17.14"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  "integrity" "sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/glob/-/glob-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/http-proxy@^1.17.5":
  "integrity" "sha512-QsbSjA/fSk7xB+UXlCT3wHBy5ai9wOcNDWwZAtud+jXhwOM3l+EYZh8Lng4+/6n8uar0J7xILzqftJdJ/Wdfkw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/http-proxy/-/http-proxy-1.17.9.tgz"
  "version" "1.17.9"
  dependencies:
    "@types/node" "*"

"@types/json-schema@^7.0.5":
  "integrity" "sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/json-schema/-/json-schema-7.0.11.tgz"
  "version" "7.0.11"

"@types/mime@*":
  "integrity" "sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/mime/-/mime-3.0.1.tgz"
  "version" "3.0.1"

"@types/minimatch@*":
  "integrity" "sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/minimatch/-/minimatch-5.1.2.tgz"
  "version" "5.1.2"

"@types/minimist@^1.2.0":
  "integrity" "sha512-jhuKLIRrhvCPLqwPcx6INqmKeiA5EWrsCOPhrlFSrbrmU4ZMPjj5Ul/oLCMDO98XRUIwVm78xICz4EPCektzeQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/minimist/-/minimist-1.2.2.tgz"
  "version" "1.2.2"

"@types/node@*":
  "integrity" "sha512-CRpX21/kGdzjOpFsZSkcrXMGIBWMGNIHXXBVFSH+ggkftxg+XYP20TESbh+zFvFj3EQOl5byk0HTRn1IL6hbqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/node/-/node-18.11.9.tgz"
  "version" "18.11.9"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/q@^1.5.1":
  "integrity" "sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/q/-/q-1.5.5.tgz"
  "version" "1.5.5"

"@types/qs@*":
  "integrity" "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/qs/-/qs-6.9.7.tgz"
  "version" "6.9.7"

"@types/raf@^3.4.0":
  "integrity" "sha512-taW5/WYqo36N7V39oYyHP9Ipfd5pNFvGTIQsNGj86xV88YQ7GnI30/yMfKDF7Zgin0m3e+ikX88FvImnK4RjGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/raf/-/raf-3.4.0.tgz"
  "version" "3.4.0"

"@types/range-parser@*":
  "integrity" "sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/range-parser/-/range-parser-1.2.4.tgz"
  "version" "1.2.4"

"@types/serve-static@*":
  "integrity" "sha512-z5xyF6uh8CbjAu9760KDKsH2FcDxZ2tFCsA4HIMWE6IkiYMXfVoa+4f9KX+FN0ZLsaMw1WNG2ETLA6N+/YA+cg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/serve-static/-/serve-static-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/source-list-map@*":
  "integrity" "sha512-K5K+yml8LTo9bWJI/rECfIPrGgxdpeNbj+d53lwN4QjW1MCwlkhUms+gtdzigTeUyBr09+u8BwOIY3MXvHdcsA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/source-list-map/-/source-list-map-0.1.2.tgz"
  "version" "0.1.2"

"@types/tapable@^1":
  "integrity" "sha512-ipixuVrh2OdNmauvtT51o3d8z12p6LtFW9in7U79der/kwejjdNchQC5UMn5u/KxNoM7VHHOs/l8KS8uHxhODQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/tapable/-/tapable-1.0.8.tgz"
  "version" "1.0.8"

"@types/uglify-js@*":
  "integrity" "sha512-GkewRA4i5oXacU/n4MA9+bLgt5/L3F1mKrYvFGm7r2ouLXhRKjuWwo9XHNnbx6WF3vlGW21S3fCvgqxvxXXc5g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/uglify-js/-/uglify-js-3.17.1.tgz"
  "version" "3.17.1"
  dependencies:
    "source-map" "^0.6.1"

"@types/webpack-dev-server@^3.11.0":
  "integrity" "sha512-XCph0RiiqFGetukCTC3KVnY1jwLcZ84illFRMbyFzCcWl90B/76ew0tSqF46oBhnLC4obNDG7dMO0JfTN0MgMQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/webpack-dev-server/-/webpack-dev-server-3.11.6.tgz"
  "version" "3.11.6"
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-static" "*"
    "@types/webpack" "^4"
    "http-proxy-middleware" "^1.0.0"

"@types/webpack-sources@*":
  "integrity" "sha512-Ft7YH3lEVRQ6ls8k4Ff1oB4jN6oy/XmU6tQISKdhfh+1mR+viZFphS6WL0IrtDOzvefmJg5a0s7ZQoRXwqTEFg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/webpack-sources/-/webpack-sources-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    "source-map" "^0.7.3"

"@types/webpack@^4", "@types/webpack@^4.0.0":
  "integrity" "sha512-PPajH64Ft2vWevkerISMtnZ8rTs4YmRbs+23c402J0INmxDKCrhZNvwZYtzx96gY2wAtXdrK1BS2fiC8MlLr3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@types/webpack/-/webpack-4.41.33.tgz"
  "version" "4.41.33"
  dependencies:
    "@types/node" "*"
    "@types/tapable" "^1"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    "anymatch" "^3.0.0"
    "source-map" "^0.6.0"

"@uppy/companion-client@^2.2.2":
  "integrity" "sha512-5mTp2iq97/mYSisMaBtFRry6PTgZA6SIL7LePteOV5x0/DxKfrZW3DEiQERJmYpHzy7k8johpm2gHnEKto56Og=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@uppy/companion-client/-/companion-client-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "@uppy/utils" "^4.1.2"
    "namespace-emitter" "^2.0.1"

"@uppy/core@^2.0.3", "@uppy/core@^2.1.1", "@uppy/core@^2.1.4", "@uppy/core@^2.3.3":
  "integrity" "sha512-iWAqppC8FD8mMVqewavCz+TNaet6HPXitmGXpGGREGrakZ4FeuWytVdrelydzTdXx6vVKkOmI2FLztGg73sENQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@uppy/core/-/core-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.3"
    "lodash.throttle" "^4.1.1"
    "mime-match" "^1.0.2"
    "namespace-emitter" "^2.0.1"
    "nanoid" "^3.1.25"
    "preact" "^10.5.13"

"@uppy/store-default@^2.1.1":
  "integrity" "sha512-xnpTxvot2SeAwGwbvmJ899ASk5tYXhmZzD/aCFsXePh/v8rNvR2pKlcQUH7cF/y4baUGq3FHO/daKCok/mpKqQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@uppy/store-default/-/store-default-2.1.1.tgz"
  "version" "2.1.1"

"@uppy/utils@^4.1.2", "@uppy/utils@^4.1.3":
  "integrity" "sha512-nTuMvwWYobnJcytDO3t+D6IkVq/Qs4Xv3vyoEZ+Iaf8gegZP+rEyoaFT2CK5XLRMienPyqRqNbIfRuFaOWSIFw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@uppy/utils/-/utils-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "lodash.throttle" "^4.1.1"

"@uppy/xhr-upload@^2.0.3", "@uppy/xhr-upload@^2.0.7":
  "integrity" "sha512-YWOQ6myBVPs+mhNjfdWsQyMRWUlrDLMoaG7nvf/G6Y3GKZf8AyjFDjvvJ49XWQ+DaZOftGkHmF1uh/DBeGivJQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@uppy/xhr-upload/-/xhr-upload-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    "nanoid" "^3.1.25"

"@vue/babel-helper-vue-jsx-merge-props@^1.4.0":
  "integrity" "sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz"
  "version" "1.4.0"

"@vue/babel-helper-vue-transform-on@^1.0.2":
  "integrity" "sha512-hz4R8tS5jMn8lDq6iD+yWL6XNB699pGIVLk7WSJnn1dbpjaazsjZQkieJoRX6gW5zpYSCFqQ7jUquPNY65tQYA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.0.2.tgz"
  "version" "1.0.2"

"@vue/babel-plugin-jsx@^1.0.3":
  "integrity" "sha512-j2uVfZjnB5+zkcbc/zsOc0fSNGCMMjaEXP52wdwdIfn0qjFfEYpYZBFKFg+HHnQeJCVrjOeO0YxgaL7DMrym9w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    "camelcase" "^6.0.0"
    "html-tags" "^3.1.0"
    "svg-tags" "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.4.0":
  "integrity" "sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^4.5.19":
  "integrity" "sha512-VCNRiAt2P/bLo09rYt3DLe6xXUMlhJwrvU18Ddd/lYJgC7s8+wvhgYs+MTx4OiAXdu58drGwSBO9SPx7C6J82Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-preset-app/-/babel-preset-app-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@babel/core" "^7.11.0"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.11.0"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.0"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.2.4"
    "babel-plugin-dynamic-import-node" "^2.3.3"
    "core-js" "^3.6.5"
    "core-js-compat" "^3.6.5"
    "semver" "^6.1.0"

"@vue/babel-preset-jsx@^1.2.4":
  "integrity" "sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h" "^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance" "^1.4.0"
    "@vue/babel-sugar-functional-vue" "^1.4.0"
    "@vue/babel-sugar-inject-h" "^1.4.0"
    "@vue/babel-sugar-v-model" "^1.4.0"
    "@vue/babel-sugar-v-on" "^1.4.0"

"@vue/babel-sugar-composition-api-inject-h@^1.4.0":
  "integrity" "sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.4.0":
  "integrity" "sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.4.0":
  "integrity" "sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.4.0":
  "integrity" "sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.4.0":
  "integrity" "sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.4.0":
  "integrity" "sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^4.5.19":
  "integrity" "sha512-GdxvNSmOw7NHIazCO8gTK+xZbaOmScTtxj6eHVeMbYpDYVPJ+th3VMLWNpw/b6uOjwzzcyKlA5dRQ1DAb+gF/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-overlay/-/cli-overlay-4.5.19.tgz"
  "version" "4.5.19"

"@vue/cli-plugin-babel@^4.5.0":
  "integrity" "sha512-8ebXzaMW9KNTMAN6+DzkhFsjty1ieqT7hIW5Lbk4v30Qhfjkms7lBWyXPGkoq+wAikXFa1Gnam2xmWOBqDDvWg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-plugin-babel/-/cli-plugin-babel-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@babel/core" "^7.11.0"
    "@vue/babel-preset-app" "^4.5.19"
    "@vue/cli-shared-utils" "^4.5.19"
    "babel-loader" "^8.1.0"
    "cache-loader" "^4.1.0"
    "thread-loader" "^2.1.3"
    "webpack" "^4.0.0"

"@vue/cli-plugin-eslint@^4.5.0":
  "integrity" "sha512-53sa4Pu9j5KajesFlj494CcO8vVo3e3nnZ1CCKjGGnrF90id1rUeepcFfz5XjwfEtbJZp2x/NoX/EZE6zCzSFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-plugin-eslint/-/cli-plugin-eslint-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"
    "eslint-loader" "^2.2.1"
    "globby" "^9.2.0"
    "inquirer" "^7.1.0"
    "webpack" "^4.0.0"
    "yorkie" "^2.0.0"

"@vue/cli-plugin-router@^4.5.19":
  "integrity" "sha512-3icGzH1IbVYmMMsOwYa0lal/gtvZLebFXdE5hcQJo2mnTwngXGMTyYAzL56EgHBPjbMmRpyj6Iw9k4aVInVX6A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-plugin-router/-/cli-plugin-router-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@vue/cli-shared-utils" "^4.5.19"

"@vue/cli-plugin-vuex@^4.5.19":
  "integrity" "sha512-DUmfdkG3pCdkP7Iznd87RfE9Qm42mgp2hcrNcYQYSru1W1gX2dG/JcW8bxmeGSa06lsxi9LEIc/QD1yPajSCZw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-plugin-vuex/-/cli-plugin-vuex-4.5.19.tgz"
  "version" "4.5.19"

"@vue/cli-service@^3.0.0 || ^4.0.0-0", "@vue/cli-service@^4.5.0":
  "integrity" "sha512-+Wpvj8fMTCt9ZPOLu5YaLkFCQmB4MrZ26aRmhhKiCQ/4PMoL6mLezfqdt6c+m2htM+1WV5RunRo+0WHl2DfwZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-service/-/cli-service-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@types/minimist" "^1.2.0"
    "@types/webpack" "^4.0.0"
    "@types/webpack-dev-server" "^3.11.0"
    "@vue/cli-overlay" "^4.5.19"
    "@vue/cli-plugin-router" "^4.5.19"
    "@vue/cli-plugin-vuex" "^4.5.19"
    "@vue/cli-shared-utils" "^4.5.19"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    "acorn" "^7.4.0"
    "acorn-walk" "^7.1.1"
    "address" "^1.1.2"
    "autoprefixer" "^9.8.6"
    "browserslist" "^4.12.0"
    "cache-loader" "^4.1.0"
    "case-sensitive-paths-webpack-plugin" "^2.3.0"
    "cli-highlight" "^2.1.4"
    "clipboardy" "^2.3.0"
    "cliui" "^6.0.0"
    "copy-webpack-plugin" "^5.1.1"
    "css-loader" "^3.5.3"
    "cssnano" "^4.1.10"
    "debug" "^4.1.1"
    "default-gateway" "^5.0.5"
    "dotenv" "^8.2.0"
    "dotenv-expand" "^5.1.0"
    "file-loader" "^4.2.0"
    "fs-extra" "^7.0.1"
    "globby" "^9.2.0"
    "hash-sum" "^2.0.0"
    "html-webpack-plugin" "^3.2.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "lodash.transform" "^4.6.0"
    "mini-css-extract-plugin" "^0.9.0"
    "minimist" "^1.2.5"
    "pnp-webpack-plugin" "^1.6.4"
    "portfinder" "^1.0.26"
    "postcss-loader" "^3.0.0"
    "ssri" "^8.0.1"
    "terser-webpack-plugin" "^1.4.4"
    "thread-loader" "^2.1.3"
    "url-loader" "^2.2.0"
    "vue-loader" "^15.9.2"
    "vue-style-loader" "^4.1.2"
    "webpack" "^4.0.0"
    "webpack-bundle-analyzer" "^3.8.0"
    "webpack-chain" "^6.4.0"
    "webpack-dev-server" "^3.11.0"
    "webpack-merge" "^4.2.2"
  optionalDependencies:
    "vue-loader-v16" "npm:vue-loader@^16.1.0"

"@vue/cli-shared-utils@^4.5.19":
  "integrity" "sha512-JYpdsrC/d9elerKxbEUtmSSU6QRM60rirVubOewECHkBHj+tLNznWq/EhCjswywtePyLaMUK25eTqnTSZlEE+g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/cli-shared-utils/-/cli-shared-utils-4.5.19.tgz"
  "version" "4.5.19"
  dependencies:
    "@achrinza/node-ipc" "9.2.2"
    "@hapi/joi" "^15.0.1"
    "chalk" "^2.4.2"
    "execa" "^1.0.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^5.1.1"
    "open" "^6.3.0"
    "ora" "^3.4.0"
    "read-pkg" "^5.1.1"
    "request" "^2.88.2"
    "semver" "^6.1.0"
    "strip-ansi" "^6.0.0"

"@vue/compiler-core@3.5.13":
  "integrity" "sha512-oOdAkwqUfW1WqpwSYJce06wvt6HljgY3fGeM9NcVA1HaYOij3mZG9Rkysn0OHuyUAGMbEbARIpsG+LPVlBJ5/Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/compiler-core/-/compiler-core-3.5.13.tgz"
  "version" "3.5.13"
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/shared" "3.5.13"
    "entities" "^4.5.0"
    "estree-walker" "^2.0.2"
    "source-map-js" "^1.2.0"

"@vue/compiler-dom@3.5.13":
  "integrity" "sha512-ZOJ46sMOKUjO3e94wPdCzQ6P1Lx/vhp2RSvfaab88Ajexs0AHeV0uasYhi99WPaogmBlRHNRuly8xV75cNTMDA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/compiler-dom/-/compiler-dom-3.5.13.tgz"
  "version" "3.5.13"
  dependencies:
    "@vue/compiler-core" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/compiler-sfc@^3.0.0-beta.14":
  "integrity" "sha512-6VdaljMpD82w6c2749Zhf5T9u5uLBWKnVue6XWxprDobftnletJ8+oel7sexFfM3qIxNmVE7LSFGTpv6obNyaQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/compiler-sfc/-/compiler-sfc-3.5.13.tgz"
  "version" "3.5.13"
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/compiler-core" "3.5.13"
    "@vue/compiler-dom" "3.5.13"
    "@vue/compiler-ssr" "3.5.13"
    "@vue/shared" "3.5.13"
    "estree-walker" "^2.0.2"
    "magic-string" "^0.30.11"
    "postcss" "^8.4.48"
    "source-map-js" "^1.2.0"

"@vue/compiler-sfc@2.7.14":
  "integrity" "sha512-aNmNHyLPsw+sVvlQFQ2/8sjNuLtK54TC6cuKnVzAY93ks4ZBrvwQSnkkIh7bsbNhum5hJBS00wSDipQ937f5DA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/compiler-sfc/-/compiler-sfc-2.7.14.tgz"
  "version" "2.7.14"
  dependencies:
    "@babel/parser" "^7.18.4"
    "postcss" "^8.4.14"
    "source-map" "^0.6.1"

"@vue/compiler-ssr@3.5.13":
  "integrity" "sha512-wMH6vrYHxQl/IybKJagqbquvxpWCuVYpoUJfCqFZwa/JY1GdATAQ+TgVtgrwwMZ0D07QhA99rs/EAAWfvG6KpA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/compiler-ssr/-/compiler-ssr-3.5.13.tgz"
  "version" "3.5.13"
  dependencies:
    "@vue/compiler-dom" "3.5.13"
    "@vue/shared" "3.5.13"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  "integrity" "sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.36"
    "postcss-selector-parser" "^6.0.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"
  optionalDependencies:
    "prettier" "^1.18.2 || ^2.0.0"

"@vue/eslint-config-prettier@^6.0.0":
  "integrity" "sha512-wFQmv45c3ige5EA+ngijq40YpVcIkAy0Lihupnsnd1Dao5CBbPyfCzqtejFLZX1EwH/kCJdpz3t6s+5wd3+KxQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/eslint-config-prettier/-/eslint-config-prettier-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "eslint-config-prettier" "^6.0.0"

"@vue/preload-webpack-plugin@^1.1.0":
  "integrity" "sha512-LIZMuJk38pk9U9Ur4YzHjlIyMuxPlACdBIHH9/nGYVTsaGKOSnSuELiE8vS9wa+dJpIYspYUOqk+L1Q4pgHQHQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/preload-webpack-plugin/-/preload-webpack-plugin-1.1.2.tgz"
  "version" "1.1.2"

"@vue/shared@3.5.13":
  "integrity" "sha512-/hnE/qP5ZoGpol0a5mDi45bOd7t3tjYJBjsgCsivow7D48cJeV5l05RD82lPqi7gRiphZM37rnhW1l6ZoCNNnQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/shared/-/shared-3.5.13.tgz"
  "version" "3.5.13"

"@vue/web-component-wrapper@^1.2.0":
  "integrity" "sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz"
  "version" "1.3.0"

"@wangeditor/basic-modules@^1.1.7", "@wangeditor/basic-modules@1.x":
  "integrity" "sha512-cY9CPkLJaqF05STqfpZKWG4LpxTMeGSIIF1fHvfm/mz+JXatCagjdkbxdikOuKYlxDdeqvOeBmsUBItufDLXZg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/basic-modules/-/basic-modules-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "is-url" "^1.2.4"

"@wangeditor/code-highlight@^1.0.3":
  "integrity" "sha512-iazHwO14XpCuIWJNTQTikqUhGKyqj+dUNWJ9288Oym9M2xMVHvnsOmDU2sgUDWVy+pOLojReMPgXCsvvNlOOhw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/code-highlight/-/code-highlight-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "prismjs" "^1.23.0"

"@wangeditor/core@^1.1.19", "@wangeditor/core@1.x":
  "integrity" "sha512-KevkB47+7GhVszyYF2pKGKtCSj/YzmClsD03C3zTt+9SR2XWT5T0e3yQqg8baZpcMvkjs1D8Dv4fk8ok/UaS2Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/core/-/core-1.1.19.tgz"
  "version" "1.1.19"
  dependencies:
    "@types/event-emitter" "^0.3.3"
    "event-emitter" "^0.3.5"
    "html-void-elements" "^2.0.0"
    "i18next" "^20.4.0"
    "scroll-into-view-if-needed" "^2.2.28"
    "slate-history" "^0.66.0"

"@wangeditor/editor-for-vue@^1.0.2":
  "integrity" "sha512-BOENvAXJVtVXlE2X50AAvjV82YlCUeu5cbeR0cvEQHQjYtiVnJtq7HSoj85r2kTgGouI5OrpJG9BBEjSjUSPyA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/editor-for-vue/-/editor-for-vue-1.0.2.tgz"
  "version" "1.0.2"

"@wangeditor/editor@^5.1.23", "@wangeditor/editor@>=5.1.0":
  "integrity" "sha512-0RxfeVTuK1tktUaPROnCoFfaHVJpRAIE2zdS0mpP+vq1axVQpLjM8+fCvKzqYIkH0Pg+C+44hJpe3VVroSkEuQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/editor/-/editor-5.1.23.tgz"
  "version" "5.1.23"
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.7"
    "@wangeditor/code-highlight" "^1.0.3"
    "@wangeditor/core" "^1.1.19"
    "@wangeditor/list-module" "^1.0.5"
    "@wangeditor/table-module" "^1.1.4"
    "@wangeditor/upload-image-module" "^1.0.2"
    "@wangeditor/video-module" "^1.1.4"
    "dom7" "^3.0.0"
    "is-hotkey" "^0.2.0"
    "lodash.camelcase" "^4.3.0"
    "lodash.clonedeep" "^4.5.0"
    "lodash.debounce" "^4.0.8"
    "lodash.foreach" "^4.5.0"
    "lodash.isequal" "^4.5.0"
    "lodash.throttle" "^4.1.1"
    "lodash.toarray" "^4.4.0"
    "nanoid" "^3.2.0"
    "slate" "^0.72.0"
    "snabbdom" "^3.1.0"

"@wangeditor/list-module@^1.0.5":
  "integrity" "sha512-uDuYTP6DVhcYf7mF1pTlmNn5jOb4QtcVhYwSSAkyg09zqxI1qBqsfUnveeDeDqIuptSJhkh81cyxi+MF8sEPOQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/list-module/-/list-module-1.0.5.tgz"
  "version" "1.0.5"

"@wangeditor/table-module@^1.1.4":
  "integrity" "sha512-5saanU9xuEocxaemGdNi9t8MCDSucnykEC6jtuiT72kt+/Hhh4nERYx1J20OPsTCCdVr7hIyQenFD1iSRkIQ6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/table-module/-/table-module-1.1.4.tgz"
  "version" "1.1.4"

"@wangeditor/upload-image-module@^1.0.2":
  "integrity" "sha512-z81lk/v71OwPDYeQDxj6cVr81aDP90aFuywb8nPD6eQeECtOymrqRODjpO6VGvCVxVck8nUxBHtbxKtjgcwyiA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/upload-image-module/-/upload-image-module-1.0.2.tgz"
  "version" "1.0.2"

"@wangeditor/video-module@^1.1.4":
  "integrity" "sha512-ZdodDPqKQrgx3IwWu4ZiQmXI8EXZ3hm2/fM6E3t5dB8tCaIGWQZhmqd6P5knfkRAd3z2+YRSRbxOGfoRSp/rLg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@wangeditor/video-module/-/video-module-1.1.4.tgz"
  "version" "1.1.4"

"@webassemblyjs/ast@1.9.0":
  "integrity" "sha512-C6wW5L+b7ogSDVqymbkkvuW9kruN//YisMED04xzeBBqjHa2FYnmvOlS6Xj68xWQRgWvI9cIglsjFowH/RJyEA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/ast/-/ast-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  "integrity" "sha512-TG5qcFsS8QB4g4MhrxK5TqfdNe7Ey/7YL/xN+36rRjl/BlGE/NcBvJcqsRgCP6Z92mRE+7N50pRIi8SmKUbcQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-api-error@1.9.0":
  "integrity" "sha512-NcMLjoFMXpsASZFxJ5h2HZRcEhDkvnNFOAKneP5RbKRzaWJN36NC4jqQHKwStIhGXu5mUWlUUk7ygdtrO8lbmw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-buffer@1.9.0":
  "integrity" "sha512-qZol43oqhq6yBPx7YM3m9Bv7WMV9Eevj6kMi6InKOuZxhw+q9hOkvq5e/PpKSiLfyetpaBnogSbNCfBwyB00CA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-code-frame@1.9.0":
  "integrity" "sha512-ERCYdJBkD9Vu4vtjUYe8LZruWuNIToYq/ME22igL+2vj2dQ2OOujIZr3MEFvfEaqKoVqpsFKAGsRdBSBjrIvZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  "integrity" "sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-module-context@1.9.0":
  "integrity" "sha512-MJCW8iGC08tMk2enck1aPW+BE5Cw8/7ph/VGZxwyvGbJwjktKkDK7vy7gAmMDx88D7mhDTCNKAW5tED+gZ0W8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  "integrity" "sha512-R7FStIzyNcd7xKxCZH5lE0Bqy+hGTwS3LJjuv1ZVxd9O7eHCedSdrId/hMOd20I+v8wDXEn+bjfKDLzTepoaUw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/helper-wasm-section@1.9.0":
  "integrity" "sha512-XnMB8l3ek4tvrKUUku+IVaXNHz2YsJyOOmz+MMkZvh8h1uSJpSen6vYnw3IoQ7WwEuAhL8Efjms1ZWjqh2agvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  "integrity" "sha512-dcX8JuYU/gvymzIHc9DgxTzUUTLexWwt8uCTWP3otys596io0L5aW02Gb1RjYpx2+0Jus1h4ZFqjla7umFniTg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  "integrity" "sha512-ENVzM5VwV1ojs9jam6vPys97B/S65YQtv/aanqnU7D8aSoHFX8GyhGg0CMfyKNIHBuAVjy3tlzd5QMMINa7wpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/leb128/-/leb128-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  "integrity" "sha512-GZbQlWtopBTP0u7cHrEx+73yZKrQoBMpwkGEIqlacljhXCkVM1kMQge/Mf+csMJAjEdSwhOyLAS0AoR3AG5P8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/utf8/-/utf8-1.9.0.tgz"
  "version" "1.9.0"

"@webassemblyjs/wasm-edit@1.9.0":
  "integrity" "sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  "integrity" "sha512-cPE3o44YzOOHvlsb4+E9qSqjc9Qf9Na1OO/BHFy4OI91XDE14MjFN4lTMezzaIWdPqHnsTodGGNP+iRSYfGkjA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  "integrity" "sha512-Qkjgm6Anhm+OMbIL0iokO7meajkzQD71ioelnfPEj6r4eOFuqm4YC3VBPqXjFyyNwowzbMD+hizmprP/Fwkl2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  "integrity" "sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  "integrity" "sha512-qsqSAP3QQ3LyZjNC/0jBJ/ToSxfYJ8kYyuiGvtn/8MK89VrNEfwj7BPQzJVHi0jGTRK2dGdJ5PRqhtjzoww+bw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  "integrity" "sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/@xtuc/long/-/long-4.2.2.tgz"
  "version" "4.2.2"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.8":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-jsx@^5.2.0":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^7.1.1":
  "integrity" "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^7.1.1", "acorn@^7.4.0":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^6.4.1":
  "integrity" "sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/acorn/-/acorn-6.4.2.tgz"
  "version" "6.4.2"

"add-dom-event-listener@^1.0.2":
  "integrity" "sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "object-assign" "4.x"

"address@^1.1.2":
  "integrity" "sha512-B+6bi5D34+fDYENiH5qOlA0cV2rAGKuWZ9LeyUUehbXy8e0VS9e498yO0Jeeh+iM+6KbfudHTFjXw2MmJD4QRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/address/-/address-1.2.1.tgz"
  "version" "1.2.1"

"adler-32@~1.2.0":
  "integrity" "sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/adler-32/-/adler-32-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "exit-on-epipe" "~1.0.1"
    "printj" "~1.1.0"

"adler-32@~1.3.0":
  "integrity" "sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/adler-32/-/adler-32-1.3.1.tgz"
  "version" "1.3.1"

"aes-decrypter@1.0.3":
  "integrity" "sha512-rsx8pfx7wJsn+ziYbpJ8XA5c93hKAtBCrfydxJqJCMT+qfjipd/B5wC2xHtBcoxyvlqJcpeAo3K55t0lXOn9yQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/aes-decrypter/-/aes-decrypter-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "pkcs7" "^0.2.3"

"ajv-errors@^1.0.0":
  "integrity" "sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ajv-errors/-/ajv-errors-1.0.1.tgz"
  "version" "1.0.1"

"ajv-keywords@^3.1.0", "ajv-keywords@^3.4.1", "ajv-keywords@^3.5.2":
  "integrity" "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv@^6.1.0", "ajv@^6.10.0", "ajv@^6.10.2", "ajv@^6.12.3", "ajv@^6.12.4", "ajv@^6.9.1", "ajv@>=5.0.0":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.0":
  "integrity" "sha512-0FcBfdcmaumGPQ0qPn7Q5qTgz/ooXgIyp1rf8ik5bGX8mpE2YHjC0P/eyQvxu1GURYQgq9ozf2mteQ5ZD9YiyQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/alphanum-sort/-/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-colors@^3.0.0":
  "integrity" "sha512-hHUXGagefjN2iRrID63xckIvotOXOojhQKWIPUZ4mNUZ9nLZW+7FMNoE1lOkEhNWYsx/7ysGIuJYCiMAA9FnrA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-colors/-/ansi-colors-3.2.4.tgz"
  "version" "3.2.4"

"ansi-escapes@^4.2.1":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html-community@0.0.8":
  "integrity" "sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  "version" "0.0.8"

"ansi-regex@^2.0.0":
  "integrity" "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-regex/-/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^4.1.0":
  "integrity" "sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-regex/-/ansi-regex-4.1.1.tgz"
  "version" "4.1.1"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
  "integrity" "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-styles/-/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ant-design-vue@^1.7.8":
  "integrity" "sha512-F1hmiS9vwbyfuFvlamdW5l9bHKqRlj9wHaGDIE41NZMWXyWy8qL0UFa/+I0Wl8gQWZCqODW5pN6Yfoyn85At3A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ant-design-vue/-/ant-design-vue-1.7.8.tgz"
  "version" "1.7.8"
  dependencies:
    "@ant-design/icons" "^2.1.1"
    "@ant-design/icons-vue" "^2.0.0"
    "@simonwep/pickr" "~1.7.0"
    "add-dom-event-listener" "^1.0.2"
    "array-tree-filter" "^2.1.0"
    "async-validator" "^3.0.3"
    "babel-helper-vue-jsx-merge-props" "^2.0.3"
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "component-classes" "^1.2.6"
    "dom-align" "^1.10.4"
    "dom-closest" "^0.2.0"
    "dom-scroll-into-view" "^2.0.0"
    "enquire.js" "^2.1.6"
    "intersperse" "^1.0.0"
    "is-mobile" "^2.2.1"
    "is-negative-zero" "^2.0.0"
    "ismobilejs" "^1.0.0"
    "json2mq" "^0.2.0"
    "lodash" "^4.17.5"
    "moment" "^2.21.0"
    "mutationobserver-shim" "^0.3.2"
    "node-emoji" "^1.10.0"
    "omit.js" "^1.0.0"
    "raf" "^3.4.0"
    "resize-observer-polyfill" "^1.5.1"
    "shallow-equal" "^1.0.0"
    "shallowequal" "^1.0.2"
    "vue-ref" "^2.0.0"
    "warning" "^4.0.0"

"ant-design-vue@1.6.5":
  "integrity" "sha512-FzLrK+JuJUq+g4vd+UjFFi13ZdSsb11EZFapisFXilJxpc1LxqyunZu5AP4nr9vFLs3J4UX2A6q+Rp/Fi6JrLg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ant-design-vue/-/ant-design-vue-1.6.5.tgz"
  "version" "1.6.5"
  dependencies:
    "@ant-design/icons" "^2.1.1"
    "@ant-design/icons-vue" "^2.0.0"
    "@simonwep/pickr" "~1.7.0"
    "add-dom-event-listener" "^1.0.2"
    "array-tree-filter" "^2.1.0"
    "async-validator" "^3.0.3"
    "babel-helper-vue-jsx-merge-props" "^2.0.3"
    "babel-runtime" "6.x"
    "classnames" "^2.2.5"
    "component-classes" "^1.2.6"
    "dom-align" "^1.10.4"
    "dom-closest" "^0.2.0"
    "dom-scroll-into-view" "^2.0.0"
    "enquire.js" "^2.1.6"
    "intersperse" "^1.0.0"
    "is-mobile" "^2.2.1"
    "is-negative-zero" "^2.0.0"
    "ismobilejs" "^1.0.0"
    "json2mq" "^0.2.0"
    "lodash" "^4.17.5"
    "moment" "^2.21.0"
    "mutationobserver-shim" "^0.3.2"
    "node-emoji" "^1.10.0"
    "omit.js" "^1.0.0"
    "raf" "^3.4.0"
    "resize-observer-polyfill" "^1.5.1"
    "shallow-equal" "^1.0.0"
    "shallowequal" "^1.0.2"
    "vue-ref" "^2.0.0"
    "warning" "^4.0.0"

"any-promise@^1.0.0":
  "integrity" "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/any-promise/-/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@^2.0.0":
  "integrity" "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/anymatch/-/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.0", "anymatch@~3.1.2":
  "integrity" "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/anymatch/-/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aproba@^1.1.1":
  "integrity" "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/aproba/-/aproba-1.2.0.tgz"
  "version" "1.2.0"

"arch@^2.1.1":
  "integrity" "sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/arch/-/arch-2.2.0.tgz"
  "version" "2.2.0"

"argparse@^1.0.7":
  "integrity" "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/argparse/-/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/arr-diff/-/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/arr-flatten/-/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/arr-union/-/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-flatten@^2.1.0":
  "integrity" "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array-flatten/-/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-tree-filter@^2.1.0":
  "integrity" "sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  "version" "2.1.0"

"array-union@^1.0.1", "array-union@^1.0.2":
  "integrity" "sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array-union/-/array-union-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "array-uniq" "^1.0.1"

"array-uniq@^1.0.1":
  "integrity" "sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array-uniq/-/array-uniq-1.0.3.tgz"
  "version" "1.0.3"

"array-unique@^0.3.2":
  "integrity" "sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array-unique/-/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.reduce@^1.0.5":
  "integrity" "sha512-kDdugMl7id9COE8R7MHF5jWk7Dqt/fs4Pv+JXoICnYwqpjjjbUurz6w5fT5IG6brLdJhv6/VoHB0H7oyIBXd+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/array.prototype.reduce/-/array.prototype.reduce-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"
    "es-array-method-boxes-properly" "^1.0.0"
    "is-string" "^1.0.7"

"asn1.js@^5.2.0":
  "integrity" "sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/asn1.js/-/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"asn1@~0.2.3":
  "integrity" "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/asn1/-/asn1-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"assert@^1.1.1":
  "integrity" "sha512-EDsgawzwoun2CZkCgtxJbv392v4nbk9XDD06zI+kQYoBM/3RBWLlEyJARDOmhAAosBjWACEkKL6S+lIZtcAubA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/assert/-/assert-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "object-assign" "^4.1.1"
    "util" "0.10.3"

"assign-symbols@^1.0.0":
  "integrity" "sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/assign-symbols/-/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^1.0.0":
  "integrity" "sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/astral-regex/-/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"async-each@^1.0.1":
  "integrity" "sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/async-each/-/async-each-1.0.3.tgz"
  "version" "1.0.3"

"async-limiter@~1.0.0":
  "integrity" "sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/async-limiter/-/async-limiter-1.0.1.tgz"
  "version" "1.0.1"

"async-validator@^3.0.3":
  "integrity" "sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/async-validator/-/async-validator-3.5.2.tgz"
  "version" "3.5.2"

"async@^2.6.4":
  "integrity" "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/async/-/async-2.6.4.tgz"
  "version" "2.6.4"
  dependencies:
    "lodash" "^4.17.14"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^9.8.6":
  "integrity" "sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/autoprefixer/-/autoprefixer-9.8.8.tgz"
  "version" "9.8.8"
  dependencies:
    "browserslist" "^4.12.0"
    "caniuse-lite" "^1.0.30001109"
    "normalize-range" "^0.1.2"
    "num2fraction" "^1.2.2"
    "picocolors" "^0.2.1"
    "postcss" "^7.0.32"
    "postcss-value-parser" "^4.1.0"

"aws-sign2@~0.7.0":
  "integrity" "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/aws4/-/aws4-1.11.0.tgz"
  "version" "1.11.0"

"axios@^0.20.0":
  "integrity" "sha512-ANA4rr2BDcmmAQLOKft2fufrtuvlqR+cXNNinUmvfeSNCOF98PZL+7M/v1zIdGo7OLjEA9J2gXJL+j4zGsl0bA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/axios/-/axios-0.20.0.tgz"
  "version" "0.20.0"
  dependencies:
    "follow-redirects" "^1.10.0"

"babel-eslint@^10.1.0":
  "integrity" "sha512-ifWaTHQ0ce+448CYop8AdrQiBsGrnC+bMgfyKFdi6EsPLTAWG+QfyDeM6OH+FmWnKvEq5NnBMLvlBUPKQZoDSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-eslint/-/babel-eslint-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    "eslint-visitor-keys" "^1.0.0"
    "resolve" "^1.12.0"

"babel-helper-vue-jsx-merge-props@^2.0.3":
  "integrity" "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz"
  "version" "2.0.3"

"babel-loader@^8.1.0":
  "integrity" "sha512-H8SvsMF+m9t15HNLMipppzkC+Y2Yq+v3SonZyU70RBL/h1gxPkH08Ot8pEE9Z4Kd+czyWJClmFS8qzIP9OZ04Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-loader/-/babel-loader-8.3.0.tgz"
  "version" "8.3.0"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^2.0.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-polyfill-corejs2@^0.3.3":
  "integrity" "sha512-8hOdmFYFSZhqg2C/JgLUQ+t52o5nirNwaWM2B9LWteozwIvM14VSwdsCAUET10qT+kmySAlseadmfeeSWFCy+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.6.0":
  "integrity" "sha512-+eHqR6OPcBhJOGgsIar7xoAB1GcSwVUA3XjAd7HJNzOXT4wv6/H7KIdA/Nc60cvUlDbKApmqNvD1B1bzOt4nyA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "core-js-compat" "^3.25.1"

"babel-plugin-polyfill-regenerator@^0.4.1":
  "integrity" "sha512-NtQGmyQDXjQqQ+IzRkBVwEOz9lQ4zxAQZgoAYEtU9dJjnl1Oc98qnN7jcp+bE7O7aYzVpavXE3/VKXNzUbh7aw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

"babel-runtime@^6.23.0", "babel-runtime@^6.26.0", "babel-runtime@^6.9.2", "babel-runtime@6.x":
  "integrity" "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/babel-runtime/-/babel-runtime-6.26.0.tgz"
  "version" "6.26.0"
  dependencies:
    "core-js" "^2.4.0"
    "regenerator-runtime" "^0.11.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/base/-/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-arraybuffer@^1.0.2":
  "integrity" "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  "version" "1.0.2"

"base64-js@^1.0.2":
  "integrity" "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/base64-js/-/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch@0.6.1":
  "integrity" "sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/batch/-/batch-0.6.1.tgz"
  "version" "0.6.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"bfj@^6.1.1":
  "integrity" "sha512-BmBJa4Lip6BPRINSZ0BPEIfB1wUY/9rwbwvIHQA1KjX9om29B6id0wnWXq7m3bn5JrUVjeOTnVuhPT1FiHwPGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bfj/-/bfj-6.1.2.tgz"
  "version" "6.1.2"
  dependencies:
    "bluebird" "^3.5.5"
    "check-types" "^8.0.3"
    "hoopy" "^0.1.4"
    "tryer" "^1.0.1"

"big.js@^3.1.3":
  "integrity" "sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/big.js/-/big.js-3.2.0.tgz"
  "version" "3.2.0"

"big.js@^5.2.2":
  "integrity" "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/big.js/-/big.js-5.2.2.tgz"
  "version" "5.2.2"

"binary-extensions@^1.0.0":
  "integrity" "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/binary-extensions/-/binary-extensions-1.13.1.tgz"
  "version" "1.13.1"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"blob.js@^1.0.1":
  "integrity" "sha512-TkPuWPeCHBbN+LWFg7BlXdSh6stRxwmAbuirKfiiHTMmo/uQfKFQMx2jrxVUkueKRiG+Tc7Os1Zn618Yc0MZpg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/blob.js/-/blob.js-1.0.1.tgz"
  "version" "1.0.1"

"bluebird@^3.1.1", "bluebird@^3.5.5":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bn.js@^4.0.0":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.1.0":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^4.11.9":
  "integrity" "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bn.js/-/bn.js-4.12.0.tgz"
  "version" "4.12.0"

"bn.js@^5.0.0", "bn.js@^5.1.1":
  "integrity" "sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bn.js/-/bn.js-5.2.1.tgz"
  "version" "5.2.1"

"body-parser@1.20.1":
  "integrity" "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/body-parser/-/body-parser-1.20.1.tgz"
  "version" "1.20.1"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "on-finished" "2.4.1"
    "qs" "6.11.0"
    "raw-body" "2.5.1"
    "type-is" "~1.6.18"
    "unpipe" "1.0.0"

"bonjour@^3.5.0":
  "integrity" "sha512-RaVTblr+OnEli0r/ud8InrU7D+G0y6aJhlxaLa6Pwty4+xoxboF1BsUI45tujvRpbj9dQVoglChqonGAsjEBYg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bonjour/-/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^2.3.1", "braces@^2.3.2":
  "integrity" "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/braces/-/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"brorand@^1.0.1", "brorand@^1.1.0":
  "integrity" "sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/brorand/-/brorand-1.1.0.tgz"
  "version" "1.1.0"

"browserify-aes@^1.0.0", "browserify-aes@^1.0.4":
  "integrity" "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserify-aes/-/browserify-aes-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-xor" "^1.0.3"
    "cipher-base" "^1.0.0"
    "create-hash" "^1.1.0"
    "evp_bytestokey" "^1.0.3"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"browserify-cipher@^1.0.0":
  "integrity" "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserify-cipher/-/browserify-cipher-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "browserify-aes" "^1.0.4"
    "browserify-des" "^1.0.0"
    "evp_bytestokey" "^1.0.0"

"browserify-des@^1.0.0":
  "integrity" "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserify-des/-/browserify-des-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "cipher-base" "^1.0.1"
    "des.js" "^1.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"browserify-rsa@^4.0.0", "browserify-rsa@^4.0.1":
  "integrity" "sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserify-rsa/-/browserify-rsa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "bn.js" "^5.0.0"
    "randombytes" "^2.0.1"

"browserify-sign@^4.0.0":
  "integrity" "sha512-/vrA5fguVAKKAVTNJjgSm1tRQDHUU6DbwO9IROu/0WAzC8PKhucDSh18J0RMvVeHAn5puMd+QHC2erPRNf8lmg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserify-sign/-/browserify-sign-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "bn.js" "^5.1.1"
    "browserify-rsa" "^4.0.1"
    "create-hash" "^1.2.0"
    "create-hmac" "^1.1.7"
    "elliptic" "^6.5.3"
    "inherits" "^2.0.4"
    "parse-asn1" "^5.1.5"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"browserify-zlib@^0.2.0":
  "integrity" "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "pako" "~1.0.5"

"browserslist@^4.0.0", "browserslist@^4.12.0", "browserslist@^4.21.3", "browserslist@^4.21.4", "browserslist@>= 4.21.0":
  "integrity" "sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/browserslist/-/browserslist-4.21.4.tgz"
  "version" "4.21.4"
  dependencies:
    "caniuse-lite" "^1.0.30001400"
    "electron-to-chromium" "^1.4.251"
    "node-releases" "^2.0.6"
    "update-browserslist-db" "^1.0.9"

"btoa@^1.2.1":
  "integrity" "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/btoa/-/btoa-1.2.1.tgz"
  "version" "1.2.1"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer-indexof@^1.0.0":
  "integrity" "sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-json@^2.0.0":
  "integrity" "sha512-+jjPFVqyfF1esi9fvfUs3NqM0pH1ziZ36VP4hmA/y/Ssfo/5w5xHKfTw9BwQjoJ1w/oVtpLomqwUHKdefGyuHw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/buffer-json/-/buffer-json-2.0.0.tgz"
  "version" "2.0.0"

"buffer-xor@^1.0.3":
  "integrity" "sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/buffer-xor/-/buffer-xor-1.0.3.tgz"
  "version" "1.0.3"

"buffer@^4.3.0":
  "integrity" "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/buffer/-/buffer-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "base64-js" "^1.0.2"
    "ieee754" "^1.1.4"
    "isarray" "^1.0.0"

"builtin-status-codes@^3.0.0":
  "integrity" "sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.0.0":
  "integrity" "sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bytes/-/bytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"cacache@^12.0.2", "cacache@^12.0.3":
  "integrity" "sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cacache/-/cacache-12.0.4.tgz"
  "version" "12.0.4"
  dependencies:
    "bluebird" "^3.5.5"
    "chownr" "^1.1.1"
    "figgy-pudding" "^3.5.1"
    "glob" "^7.1.4"
    "graceful-fs" "^4.1.15"
    "infer-owner" "^1.0.3"
    "lru-cache" "^5.1.1"
    "mississippi" "^3.0.0"
    "mkdirp" "^0.5.1"
    "move-concurrently" "^1.0.1"
    "promise-inflight" "^1.0.1"
    "rimraf" "^2.6.3"
    "ssri" "^6.0.1"
    "unique-filename" "^1.1.1"
    "y18n" "^4.0.0"

"cache-base@^1.0.1":
  "integrity" "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cache-base/-/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cache-loader@^4.1.0":
  "integrity" "sha512-ftOayxve0PwKzBF/GLsZNC9fJBXl8lkZE3TOsjkboHfVHVkL39iUEs1FO07A33mizmci5Dudt38UZrrYXDtbhw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cache-loader/-/cache-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer-json" "^2.0.0"
    "find-cache-dir" "^3.0.0"
    "loader-utils" "^1.2.3"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.1"
    "schema-utils" "^2.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/call-bind/-/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"call-me-maybe@^1.0.1":
  "integrity" "sha512-HpX65o1Hnr9HH25ojC1YGs7HCQLq0GCOibSaWER0eNpgJ/Z1MZv2mTc7+xh6WOPxbRVcmgbv4hGU+uSQ/2xFZQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/call-me-maybe/-/call-me-maybe-1.0.2.tgz"
  "version" "1.0.2"

"caller-callsite@^2.0.0":
  "integrity" "sha512-JuG3qI4QOftFsZyOn1qq87fq5grLIyk1JYd5lJmdA+fG7aQ9pA/i3JIJGcO3q0MrRcHlOt1U+ZeHW8Dq9axALQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/caller-callsite/-/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/caller-path/-/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/callsites/-/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@3.0.x":
  "integrity" "sha512-+MbKztAYHXPr1jNTSKQF52VpcFjwY5RkR7fxksV8Doo4KAYc5Fl4UJRgthBbTmEx8C54DqahhbLJkDwjI3PI/w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/camel-case/-/camel-case-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "no-case" "^2.2.0"
    "upper-case" "^1.1.1"

"camelcase@^5.0.0":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^5.3.1":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/camelcase/-/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"caniuse-api@^3.0.0":
  "integrity" "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/caniuse-api/-/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001109", "caniuse-lite@^1.0.30001400":
  "integrity" "sha512-zBUoFU0ZcxpvSt9IU66dXVT/3ctO1cy4y9cscs1szkPlcWb6pasYM144GqrUygUbT+k7cmUCW61cvskjcv0enQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001431.tgz"
  "version" "1.0.30001431"

"canvg@^3.0.6":
  "integrity" "sha512-qwR2FRNO9NlzTeKIPIKpnTY6fqwuYSequ8Ru8c0YkYU7U0oW+hLUvWadLvAu1Rl72OMNiFhoLu4f8eUjQ7l/+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/canvg/-/canvg-3.0.10.tgz"
  "version" "3.0.10"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    "core-js" "^3.8.3"
    "raf" "^3.4.1"
    "regenerator-runtime" "^0.13.7"
    "rgbcolor" "^1.0.1"
    "stackblur-canvas" "^2.0.0"
    "svg-pathdata" "^6.0.3"

"case-sensitive-paths-webpack-plugin@^2.3.0":
  "integrity" "sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  "version" "2.4.0"

"caseless@~0.12.0":
  "integrity" "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"cfb@^1.1.4":
  "integrity" "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cfb/-/cfb-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "adler-32" "~1.3.0"
    "crc-32" "~1.2.0"

"chalk@^2.0.0", "chalk@^2.0.1", "chalk@^2.1.0", "chalk@^2.4.1", "chalk@^2.4.2":
  "integrity" "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chalk/-/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chalk/-/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chardet@^0.7.0":
  "integrity" "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chardet/-/chardet-0.7.0.tgz"
  "version" "0.7.0"

"check-types@^8.0.3":
  "integrity" "sha512-YpeKZngUmG65rLudJ4taU7VLkOCTMhNl/u4ctNC56LQS/zJTyNH0Lrtwm1tfTsbLlwvlfsA2d1c8vCf/Kh2KwQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/check-types/-/check-types-8.0.3.tgz"
  "version" "8.0.3"

"chokidar@^2.1.8":
  "integrity" "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chokidar/-/chokidar-2.1.8.tgz"
  "version" "2.1.8"
  dependencies:
    "anymatch" "^2.0.0"
    "async-each" "^1.0.1"
    "braces" "^2.3.2"
    "glob-parent" "^3.1.0"
    "inherits" "^2.0.3"
    "is-binary-path" "^1.0.0"
    "is-glob" "^4.0.0"
    "normalize-path" "^3.0.0"
    "path-is-absolute" "^1.0.0"
    "readdirp" "^2.2.1"
    "upath" "^1.1.1"
  optionalDependencies:
    "fsevents" "^1.2.7"

"chokidar@^3.4.1":
  "integrity" "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^1.1.1":
  "integrity" "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chownr/-/chownr-1.1.4.tgz"
  "version" "1.1.4"

"chrome-trace-event@^1.0.2":
  "integrity" "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"ci-info@^1.5.0":
  "integrity" "sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ci-info/-/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"cipher-base@^1.0.0", "cipher-base@^1.0.1", "cipher-base@^1.0.3":
  "integrity" "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cipher-base/-/cipher-base-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"class-utils@^0.3.5":
  "integrity" "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/class-utils/-/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"classnames@^2.2.5":
  "integrity" "sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/classnames/-/classnames-2.3.2.tgz"
  "version" "2.3.2"

"clean-css@4.2.x":
  "integrity" "sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/clean-css/-/clean-css-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "source-map" "~0.6.0"

"cli-cursor@^2.1.0":
  "integrity" "sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cli-cursor/-/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cli-cursor/-/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-highlight@^2.1.4":
  "integrity" "sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cli-highlight/-/cli-highlight-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "chalk" "^4.0.0"
    "highlight.js" "^10.7.1"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^6.0.0"
    "yargs" "^16.0.0"

"cli-spinners@^2.0.0":
  "integrity" "sha512-qu3pN8Y3qHNgE2AFweciB1IfMnmZ/fsNTEE+NOFjmGB2F/7rLhnhzppvpCnN4FovtP26k8lHyy9ptEbNwWFLzw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cli-spinners/-/cli-spinners-2.7.0.tgz"
  "version" "2.7.0"

"cli-width@^3.0.0":
  "integrity" "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cli-width/-/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"clipboardy@^2.3.0":
  "integrity" "sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/clipboardy/-/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^5.0.0":
  "integrity" "sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cliui/-/cliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2":
  "integrity" "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cliui/-/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone@^1.0.2":
  "integrity" "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/clone/-/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/clone/-/clone-2.1.2.tgz"
  "version" "2.1.2"

"coa@^2.0.2":
  "integrity" "sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/coa/-/coa-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    "chalk" "^2.4.1"
    "q" "^1.1.2"

"codepage@~1.14.0":
  "integrity" "sha512-iz3zJLhlrg37/gYRWgEPkaFTtzmnEv1h+r7NgZum2lFElYQPi0/5bnmuDfODHxfp0INEfnRqyfyeIJDbb7ahRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/codepage/-/codepage-1.14.0.tgz"
  "version" "1.14.0"
  dependencies:
    "commander" "~2.14.1"
    "exit-on-epipe" "~1.0.1"

"collection-visit@^1.0.0":
  "integrity" "sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/collection-visit/-/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0", "color-convert@^1.9.3":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-string@^1.6.0":
  "integrity" "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/color-string/-/color-string-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color@^3.0.0":
  "integrity" "sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/color/-/color-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.3"
    "color-string" "^1.6.0"

"combined-stream@^1.0.6", "combined-stream@~1.0.6":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"commander@^2.18.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^2.20.0":
  "integrity" "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/commander/-/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@~2.14.1":
  "integrity" "sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/commander/-/commander-2.14.1.tgz"
  "version" "2.14.1"

"commander@~2.17.1", "commander@2.17.x":
  "integrity" "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/commander/-/commander-2.17.1.tgz"
  "version" "2.17.1"

"commander@~2.19.0":
  "integrity" "sha512-6tvAOO+D6OENvRAh524Dh9jcfKTYDQAqvqezbCW82xj5X0pSrcpxtvRKHLG0yBY6SD7PSDrJaj+0AiOcKVd1Xg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/commander/-/commander-2.19.0.tgz"
  "version" "2.19.0"

"commondir@^1.0.1":
  "integrity" "sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/commondir/-/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-classes@^1.2.6":
  "integrity" "sha512-hPFGULxdwugu1QWW3SvVOCUHLzO34+a2J6Wqy0c5ASQkfi9/8nZcBB0ZohaEbXOQlCflMAEMmEWk7u7BVs4koA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/component-classes/-/component-classes-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "component-indexof" "0.0.3"

"component-emitter@^1.2.1":
  "integrity" "sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/component-emitter/-/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"component-indexof@0.0.3":
  "integrity" "sha512-puDQKvx/64HZXb4hBwIcvQLaLgux8o1CbWl39s41hrIIZDl1lJiD5jc22gj3RBeGK0ovxALDYpIbyjqDUUl0rw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/component-indexof/-/component-indexof-0.0.3.tgz"
  "version" "0.0.3"

"compressible@~2.0.16":
  "integrity" "sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/compressible/-/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "integrity" "sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/compression/-/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"compute-scroll-into-view@^1.0.17":
  "integrity" "sha512-j4dx+Fb0URmzbwwMUrhqWM2BEWHdFGx+qZ9qqASHRPqvTYdqvWnHg0H1hIbcyLnvgnoNAVMlwkepyqM3DaIFUg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/compute-scroll-into-view/-/compute-scroll-into-view-1.0.17.tgz"
  "version" "1.0.17"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.5.0":
  "integrity" "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/concat-stream/-/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"console-browserify@^1.1.0":
  "integrity" "sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/console-browserify/-/console-browserify-1.2.0.tgz"
  "version" "1.2.0"

"consolidate@^0.15.1":
  "integrity" "sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/consolidate/-/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"constants-browserify@^1.0.0":
  "integrity" "sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/constants-browserify/-/constants-browserify-1.0.0.tgz"
  "version" "1.0.0"

"content-disposition@0.5.4":
  "integrity" "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4":
  "integrity" "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/content-type/-/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.7.0":
  "integrity" "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/convert-source-map/-/convert-source-map-1.9.0.tgz"
  "version" "1.9.0"

"cookie-signature@1.0.6":
  "integrity" "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.5.0":
  "integrity" "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cookie/-/cookie-0.5.0.tgz"
  "version" "0.5.0"

"copy-anything@^2.0.1":
  "integrity" "sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/copy-anything/-/copy-anything-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "is-what" "^3.14.1"

"copy-concurrently@^1.0.0":
  "integrity" "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/copy-concurrently/-/copy-concurrently-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "aproba" "^1.1.1"
    "fs-write-stream-atomic" "^1.0.8"
    "iferr" "^0.1.5"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/copy-descriptor/-/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-webpack-plugin@^5.1.1":
  "integrity" "sha512-Uh7crJAco3AjBvgAy9Z75CjK8IG+gxaErro71THQ+vv/bl4HaQcpkexAY8KVW/T6D2W2IRr+couF/knIRkZMIQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/copy-webpack-plugin/-/copy-webpack-plugin-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "cacache" "^12.0.3"
    "find-cache-dir" "^2.1.0"
    "glob-parent" "^3.1.0"
    "globby" "^7.1.1"
    "is-glob" "^4.0.1"
    "loader-utils" "^1.2.3"
    "minimatch" "^3.0.4"
    "normalize-path" "^3.0.0"
    "p-limit" "^2.2.1"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "webpack-log" "^2.0.0"

"core-js-compat@^3.25.1", "core-js-compat@^3.6.5":
  "integrity" "sha512-622/KzTudvXCDLRw70iHW4KKs1aGpcRcowGWyYJr2DEBfRrd6hNJybxSWJFuZYD4ma86xhrwDDHxmDaIq4EA8A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/core-js-compat/-/core-js-compat-3.26.1.tgz"
  "version" "3.26.1"
  dependencies:
    "browserslist" "^4.21.4"

"core-js@^2.4.0":
  "integrity" "sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/core-js/-/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-js@^3.6.0", "core-js@^3.6.5", "core-js@^3.8.3":
  "integrity" "sha512-21491RRQVzUn0GGM9Z1Jrpr6PNPxPi+Za8OM9q4tksTSnlbXXGKK1nXNg/QvwFYettXvSX6zWKCtHHfjN4puyA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/core-js/-/core-js-3.26.1.tgz"
  "version" "3.26.1"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"core-util-is@1.0.2":
  "integrity" "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/core-util-is/-/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cosmiconfig@^5.0.0":
  "integrity" "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cosmiconfig/-/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"crc-32@~1.2.0":
  "integrity" "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/crc-32/-/crc-32-1.2.2.tgz"
  "version" "1.2.2"

"create-ecdh@^4.0.0":
  "integrity" "sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/create-ecdh/-/create-ecdh-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "bn.js" "^4.1.0"
    "elliptic" "^6.5.3"

"create-hash@^1.1.0", "create-hash@^1.1.2", "create-hash@^1.2.0":
  "integrity" "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/create-hash/-/create-hash-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cipher-base" "^1.0.1"
    "inherits" "^2.0.1"
    "md5.js" "^1.3.4"
    "ripemd160" "^2.0.1"
    "sha.js" "^2.4.0"

"create-hmac@^1.1.0", "create-hmac@^1.1.4", "create-hmac@^1.1.7":
  "integrity" "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/create-hmac/-/create-hmac-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "cipher-base" "^1.0.3"
    "create-hash" "^1.1.0"
    "inherits" "^2.0.1"
    "ripemd160" "^2.0.0"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"cross-spawn@^5.0.1":
  "integrity" "sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cross-spawn/-/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0", "cross-spawn@^6.0.5":
  "integrity" "sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cross-spawn/-/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-browserify@^3.11.0":
  "integrity" "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/crypto-browserify/-/crypto-browserify-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "browserify-cipher" "^1.0.0"
    "browserify-sign" "^4.0.0"
    "create-ecdh" "^4.0.0"
    "create-hash" "^1.1.0"
    "create-hmac" "^1.1.0"
    "diffie-hellman" "^5.0.0"
    "inherits" "^2.0.1"
    "pbkdf2" "^3.0.3"
    "public-encrypt" "^4.0.0"
    "randombytes" "^2.0.0"
    "randomfill" "^1.0.3"

"crypto-js@^3.1.9-1":
  "integrity" "sha512-DIT51nX0dCfKltpRiXV+/TVZq+Qq2NgF4644+K7Ttnla7zEzqc+kjJyiB96BHNyUTBxyjzRcZYpUdZa+QAqi6Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/crypto-js/-/crypto-js-3.3.0.tgz"
  "version" "3.3.0"

"css-color-names@^0.0.4", "css-color-names@0.0.4":
  "integrity" "sha512-zj5D7X1U2h2zsXOAM8EyUREBnnts6H+Jm+d1M2DbiQQcUtnqgQsMrdo8JW9R80YFUmIdBZeMu5wvYM7hcgWP/Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-color-names/-/css-color-names-0.0.4.tgz"
  "version" "0.0.4"

"css-declaration-sorter@^4.0.1":
  "integrity" "sha512-BcxQSKTSEEQUftYpBVnsH4SF05NTuBokb19/sBt6asXGKZ/6VP7PLG1CBCkFDYOnhXhPh0jMhO6xZ71oYHXHBA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-declaration-sorter/-/css-declaration-sorter-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.1"
    "timsort" "^0.3.0"

"css-line-break@^2.1.0":
  "integrity" "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-line-break/-/css-line-break-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "utrie" "^1.0.2"

"css-loader@*", "css-loader@^3.5.3":
  "integrity" "sha512-M5lSukoWi1If8dhQAUCvj4H8vUt3vOnwbQBH9DdTm/s4Ym2B/3dPMtYZeJmq7Q3S3Pa+I94DcZ7pc9bP14cWIQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-loader/-/css-loader-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "camelcase" "^5.3.1"
    "cssesc" "^3.0.0"
    "icss-utils" "^4.1.1"
    "loader-utils" "^1.2.3"
    "normalize-path" "^3.0.0"
    "postcss" "^7.0.32"
    "postcss-modules-extract-imports" "^2.0.0"
    "postcss-modules-local-by-default" "^3.0.2"
    "postcss-modules-scope" "^2.2.0"
    "postcss-modules-values" "^3.0.0"
    "postcss-value-parser" "^4.1.0"
    "schema-utils" "^2.7.0"
    "semver" "^6.3.0"

"css-select-base-adapter@^0.1.1":
  "integrity" "sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  "version" "0.1.1"

"css-select@^2.0.0":
  "integrity" "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-select/-/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-select@^4.1.3":
  "integrity" "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-select/-/css-select-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^6.0.1"
    "domhandler" "^4.3.1"
    "domutils" "^2.8.0"
    "nth-check" "^2.0.1"

"css-tree@^1.1.2":
  "integrity" "sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-tree/-/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-tree@1.0.0-alpha.37":
  "integrity" "sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  "version" "1.0.0-alpha.37"
  dependencies:
    "mdn-data" "2.0.4"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-what/-/css-what-3.4.2.tgz"
  "version" "3.4.2"

"css-what@^6.0.1":
  "integrity" "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/css-what/-/css-what-6.1.0.tgz"
  "version" "6.1.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^4.0.0", "cssnano-preset-default@^4.0.8":
  "integrity" "sha512-LdAyHuq+VRyeVREFmuxUZR1TXjQm8QQU/ktoo/x7bz+SdOge1YKc5eMN6pRW7YWBmyq59CqYba1dJ5cUukEjLQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssnano-preset-default/-/cssnano-preset-default-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "css-declaration-sorter" "^4.0.1"
    "cssnano-util-raw-cache" "^4.0.1"
    "postcss" "^7.0.0"
    "postcss-calc" "^7.0.1"
    "postcss-colormin" "^4.0.3"
    "postcss-convert-values" "^4.0.1"
    "postcss-discard-comments" "^4.0.2"
    "postcss-discard-duplicates" "^4.0.2"
    "postcss-discard-empty" "^4.0.1"
    "postcss-discard-overridden" "^4.0.1"
    "postcss-merge-longhand" "^4.0.11"
    "postcss-merge-rules" "^4.0.3"
    "postcss-minify-font-values" "^4.0.2"
    "postcss-minify-gradients" "^4.0.2"
    "postcss-minify-params" "^4.0.2"
    "postcss-minify-selectors" "^4.0.2"
    "postcss-normalize-charset" "^4.0.1"
    "postcss-normalize-display-values" "^4.0.2"
    "postcss-normalize-positions" "^4.0.2"
    "postcss-normalize-repeat-style" "^4.0.2"
    "postcss-normalize-string" "^4.0.2"
    "postcss-normalize-timing-functions" "^4.0.2"
    "postcss-normalize-unicode" "^4.0.1"
    "postcss-normalize-url" "^4.0.1"
    "postcss-normalize-whitespace" "^4.0.2"
    "postcss-ordered-values" "^4.1.2"
    "postcss-reduce-initial" "^4.0.3"
    "postcss-reduce-transforms" "^4.0.2"
    "postcss-svgo" "^4.0.3"
    "postcss-unique-selectors" "^4.0.1"

"cssnano-util-get-arguments@^4.0.0":
  "integrity" "sha512-6RIcwmV3/cBMG8Aj5gucQRsJb4vv4I4rn6YjPbVWd5+Pn/fuG+YseGvXGk00XLkoZkaj31QOD7vMUpNPC4FIuw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-get-match@^4.0.0":
  "integrity" "sha512-JPMZ1TSMRUPVIqEalIBNoBtAYbi8okvcFns4O0YIhcdGebeYZK7dMyHJiQ6GqNBA9kE0Hym4Aqym5rPdsV/4Cw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz"
  "version" "4.0.0"

"cssnano-util-raw-cache@^4.0.1":
  "integrity" "sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"cssnano-util-same-parent@^4.0.0":
  "integrity" "sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz"
  "version" "4.0.1"

"cssnano@^4.0.0", "cssnano@^4.1.10":
  "integrity" "sha512-6gZm2htn7xIPJOHY824ERgj8cNPgPxyCSnkXc4v7YvNW+TdVfzgngHcEhy/8D11kUWRUMbke+tC+AUcUsnMz2g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cssnano/-/cssnano-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "cssnano-preset-default" "^4.0.8"
    "is-resolvable" "^1.0.0"
    "postcss" "^7.0.0"

"csso@^4.0.2":
  "integrity" "sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/csso/-/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"csstype@^3.1.0":
  "integrity" "sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/csstype/-/csstype-3.1.1.tgz"
  "version" "3.1.1"

"cyclist@^1.0.1":
  "integrity" "sha512-NJGVKPS81XejHcLhaLJS7plab0fK3slPh11mESeeDq2W4ZI5kUKK/LRRdVDvjJseojbPB7ZwjnyOybg3Igea/A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/cyclist/-/cyclist-1.0.1.tgz"
  "version" "1.0.1"

"d@^1.0.1", "d@1":
  "integrity" "sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/d/-/d-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "es5-ext" "^0.10.50"
    "type" "^1.0.1"

"dashdash@^1.12.0":
  "integrity" "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"de-indent@^1.0.2":
  "integrity" "sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/de-indent/-/de-indent-1.0.2.tgz"
  "version" "1.0.2"

"debug@^2.2.0":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.2.7":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.2.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decimal.js@^10.4.3":
  "integrity" "sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/decimal.js/-/decimal.js-10.4.3.tgz"
  "version" "10.4.3"

"decode-uri-component@^0.2.0":
  "integrity" "sha512-hjf+xovcEn31w/EUYdTXQh/8smFL/dzYjohQGEIgjyNavaJfBY2p5F527Bo1VPATxv0VYTUC2bOcXvqFwk78Og=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/decode-uri-component/-/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/deep-equal/-/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@~0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^1.5.2":
  "integrity" "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/deepmerge/-/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"default-gateway@^4.2.0":
  "integrity" "sha512-h6sMrVB1VMWVrW13mSc6ia/DwYYw5MN6+exNu1OaJeFac5aSAvwM7lZ0NVfTABuSkQelr4h5oebg3KB1XPdjgA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/default-gateway/-/default-gateway-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "execa" "^1.0.0"
    "ip-regex" "^2.1.0"

"default-gateway@^5.0.5":
  "integrity" "sha512-z2RnruVmj8hVMmAnEJMTIJNijhKCDiGjbLP+BHJFOT7ld3Bo5qcIBpVYDniqhbMIIf+jZDlkP2MkPXiQy/DBLA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/default-gateway/-/default-gateway-5.0.5.tgz"
  "version" "5.0.5"
  dependencies:
    "execa" "^3.3.0"

"defaults@^1.0.3":
  "integrity" "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/defaults/-/defaults-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "clone" "^1.0.2"

"define-properties@^1.1.2", "define-properties@^1.1.3", "define-properties@^1.1.4":
  "integrity" "sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/define-properties/-/define-properties-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"define-property@^0.2.5":
  "integrity" "sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/define-property/-/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/define-property/-/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/define-property/-/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^4.1.1":
  "integrity" "sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/del/-/del-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "@types/glob" "^7.1.1"
    "globby" "^6.1.0"
    "is-path-cwd" "^2.0.0"
    "is-path-in-cwd" "^2.0.0"
    "p-map" "^2.0.0"
    "pify" "^4.0.1"
    "rimraf" "^2.6.3"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@~1.1.2":
  "integrity" "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/depd/-/depd-1.1.2.tgz"
  "version" "1.1.2"

"depd@2.0.0":
  "integrity" "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"des.js@^1.0.0":
  "integrity" "sha512-Q0I4pfFrv2VPd34/vfLrFOoRmlYj3OV50i7fskps1jZWK1kApMWWT9G6RRUeYedLcBDIhnSDaUvJMb3AhUlaEA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/des.js/-/des.js-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"

"destroy@1.2.0":
  "integrity" "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/destroy/-/destroy-1.2.0.tgz"
  "version" "1.2.0"

"detect-node@^2.0.4":
  "integrity" "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/detect-node/-/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"diffie-hellman@^5.0.0":
  "integrity" "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/diffie-hellman/-/diffie-hellman-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "miller-rabin" "^4.0.0"
    "randombytes" "^2.0.0"

"dir-glob@^2.0.0", "dir-glob@^2.2.2":
  "integrity" "sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dir-glob/-/dir-glob-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "path-type" "^3.0.0"

"dns-equal@^1.0.0":
  "integrity" "sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dns-equal/-/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dns-packet/-/dns-packet-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha512-Ix5PrWjphuSoUXV/Zv5gaFHjnaJtb02F2+Si3Ht9dyJ87+Z/lMmy+dpNHtTGraNK958ndXq2i+GLkWsWHcKaBQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dns-txt/-/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-align@^1.10.4":
  "integrity" "sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-align/-/dom-align-1.12.4.tgz"
  "version" "1.12.4"

"dom-closest@^0.2.0":
  "integrity" "sha512-6neTn1BtJlTSt+XSISXpnOsF1uni1CHsP/tmzZMGWxasYFHsBOqrHPnzmneqEgKhpagnfnfSfbvRRW0xFsBHAA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-closest/-/dom-closest-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "dom-matches" ">=1.0.1"

"dom-converter@^0.2.0":
  "integrity" "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-converter/-/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-matches@>=1.0.1":
  "integrity" "sha512-2VI856xEDCLXi19W+4BechR5/oIS6bKCKqcf16GR8Pg7dGLJ/eBOWVbCmQx2ISvYH6wTNx5Ef7JTOw1dRGRx6A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-matches/-/dom-matches-2.0.0.tgz"
  "version" "2.0.0"

"dom-scroll-into-view@^2.0.0":
  "integrity" "sha512-bvVTQe1lfaUr1oFzZX80ce9KLDlZ3iU+XGNE/bz9HnGdklTieqsbmsLHe+rT2XWqopvL0PckkYqN7ksmm5pe3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-scroll-into-view/-/dom-scroll-into-view-2.0.1.tgz"
  "version" "2.0.1"

"dom-serializer@^1.0.1":
  "integrity" "sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-serializer/-/dom-serializer-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-serializer/-/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"dom-walk@^0.1.0":
  "integrity" "sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom-walk/-/dom-walk-0.1.2.tgz"
  "version" "0.1.2"

"dom7@^3.0.0":
  "integrity" "sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dom7/-/dom7-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ssr-window" "^3.0.0-alpha.1"

"domain-browser@^1.1.1":
  "integrity" "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/domain-browser/-/domain-browser-1.2.0.tgz"
  "version" "1.2.0"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/domelementtype/-/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domelementtype@1":
  "integrity" "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/domelementtype/-/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domhandler@^4.0.0", "domhandler@^4.2.0", "domhandler@^4.3.1":
  "integrity" "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/domhandler/-/domhandler-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "domelementtype" "^2.2.0"

"dompurify@^2.2.0":
  "integrity" "sha512-1e2SpqHiRx4DPvmRuXU5J0di3iQACwJM+mFGE2HAkkK7Tbnfk9WcghcAmyWc9CRrjyRRUpmuhPUH6LphQQR3EQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dompurify/-/dompurify-2.4.4.tgz"
  "version" "2.4.4"

"domutils@^1.7.0":
  "integrity" "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/domutils/-/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.5.2", "domutils@^2.8.0":
  "integrity" "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/domutils/-/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-prop@^5.2.0":
  "integrity" "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dot-prop/-/dot-prop-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "is-obj" "^2.0.0"

"dotenv-expand@^5.1.0":
  "integrity" "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^8.2.0":
  "integrity" "sha512-IrPdXQsk2BbzvCBGBOTmmSH5SodmqZNt4ERAZDmW4CT+tL8VtvinqywuANaFu4bOMWki16nqf0e4oC0QIaDr/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/dotenv/-/dotenv-8.6.0.tgz"
  "version" "8.6.0"

"duplexer@^0.1.1":
  "integrity" "sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/duplexer/-/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"duplexify@^3.4.2", "duplexify@^3.6.0":
  "integrity" "sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/duplexify/-/duplexify-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "end-of-stream" "^1.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"
    "stream-shift" "^1.0.0"

"easy-stack@1.0.1":
  "integrity" "sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/easy-stack/-/easy-stack-1.0.1.tgz"
  "version" "1.0.1"

"ecc-jsbn@~0.1.1":
  "integrity" "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"echarts@^5.1.2":
  "integrity" "sha512-uPsO9VRUIKAdFOoH3B0aNg7NRVdN7aM39/OjovjO9MwmWsAkfGyeXJhK+dbRi51iDrQWliXV60/XwLA7kg3z0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/echarts/-/echarts-5.4.0.tgz"
  "version" "5.4.0"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.4.0"

"ee-first@1.1.1":
  "integrity" "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^2.6.1":
  "integrity" "sha512-7vmuyh5+kuUyJKePhQfRQBhXV5Ce+RnaeeQArKu1EAMpL3WbgMt5WG6uQZpEVvYSSsxMXRKOewtDk9RaTKXRlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ejs/-/ejs-2.7.4.tgz"
  "version" "2.7.4"

"electron-to-chromium@^1.4.251":
  "integrity" "sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz"
  "version" "1.4.284"

"elliptic@^6.5.3":
  "integrity" "sha512-iLhC6ULemrljPZb+QutR5TQGB+pdW6KGD5RSegS+8sorOZT+rdQFbsQFJgvN3eRqNALqJer4oQ16YvJHlU8hzQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/elliptic/-/elliptic-6.5.4.tgz"
  "version" "6.5.4"
  dependencies:
    "bn.js" "^4.11.9"
    "brorand" "^1.1.0"
    "hash.js" "^1.0.0"
    "hmac-drbg" "^1.0.1"
    "inherits" "^2.0.4"
    "minimalistic-assert" "^1.0.1"
    "minimalistic-crypto-utils" "^1.0.1"

"emoji-regex@^7.0.1":
  "integrity" "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/emoji-regex/-/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^2.0.0":
  "integrity" "sha512-knHEZMgs8BB+MInokmNTg/OyPlAddghe1YBgNwJBc5zsJi/uyIcXoSDsL/W9ymOsBoBGdPIHXYJ9+qKFwRwDng=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/emojis-list/-/emojis-list-2.1.0.tgz"
  "version" "2.1.0"

"emojis-list@^3.0.0":
  "integrity" "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/emojis-list/-/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encodeurl@~1.0.2":
  "integrity" "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^4.5.0":
  "integrity" "sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "memory-fs" "^0.5.0"
    "tapable" "^1.0.0"

"enquire.js@^2.1.6":
  "integrity" "sha512-/KujNpO+PT63F7Hlpu4h3pE3TokKRHN26JYmQpPyjkRD/N57R7bPDNojMXdi7uveAKjYB7yQnartCxZnFWr0Xw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/enquire.js/-/enquire.js-2.1.6.tgz"
  "version" "2.1.6"

"entities@^2.0.0":
  "integrity" "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/entities/-/entities-2.2.0.tgz"
  "version" "2.2.0"

"entities@^4.5.0":
  "integrity" "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/entities/-/entities-4.5.0.tgz"
  "version" "4.5.0"

"errno@^0.1.1", "errno@^0.1.3", "errno@~0.1.7":
  "integrity" "sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/errno/-/errno-0.1.8.tgz"
  "version" "0.1.8"
  dependencies:
    "prr" "~1.0.1"

"error-ex@^1.3.1":
  "integrity" "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/error-ex/-/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "stackframe" "^1.3.4"

"es-abstract@^1.19.0", "es-abstract@^1.20.4":
  "integrity" "sha512-0UtvRN79eMe2L+UNEF1BwRe364sj/DXhQ/k5FmivgoSdpM90b8Jc0mDzKMGo7QS0BVbOP/bTwBKNnDc9rNzaPA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es-abstract/-/es-abstract-1.20.4.tgz"
  "version" "1.20.4"
  dependencies:
    "call-bind" "^1.0.2"
    "es-to-primitive" "^1.2.1"
    "function-bind" "^1.1.1"
    "function.prototype.name" "^1.1.5"
    "get-intrinsic" "^1.1.3"
    "get-symbol-description" "^1.0.0"
    "has" "^1.0.3"
    "has-property-descriptors" "^1.0.0"
    "has-symbols" "^1.0.3"
    "internal-slot" "^1.0.3"
    "is-callable" "^1.2.7"
    "is-negative-zero" "^2.0.2"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.2"
    "is-string" "^1.0.7"
    "is-weakref" "^1.0.2"
    "object-inspect" "^1.12.2"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.4"
    "regexp.prototype.flags" "^1.4.3"
    "safe-regex-test" "^1.0.0"
    "string.prototype.trimend" "^1.0.5"
    "string.prototype.trimstart" "^1.0.5"
    "unbox-primitive" "^1.0.2"

"es-array-method-boxes-properly@^1.0.0":
  "integrity" "sha512-wd6JXUmyHmt8T5a2xreUwKcGPq6f1f+WwIJkijUqiGcJz1qqnZgP6XIK+QyIWU5lT7imeNxUll48bziG+TSYcA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz"
  "version" "1.0.0"

"es-to-primitive@^1.2.1":
  "integrity" "sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"es5-ext@^0.10.35", "es5-ext@^0.10.50", "es5-ext@~0.10.14":
  "integrity" "sha512-BHLqn0klhEpnOKSrzn/Xsz2UIW8j+cGmo9JLzr8BiUapV8hPL9+FliFqjwr9ngW7jWdnxv6eO+/LqyhJVqgrjA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es5-ext/-/es5-ext-0.10.62.tgz"
  "version" "0.10.62"
  dependencies:
    "es6-iterator" "^2.0.3"
    "es6-symbol" "^3.1.3"
    "next-tick" "^1.1.0"

"es5-shim@^4.5.1":
  "integrity" "sha512-jg21/dmlrNQI7JyyA2w7n+yifSxBng0ZralnSfVZjoCawgNTCnS+yBCyVM9DL5itm7SUnDGgv7hcq2XCZX4iRQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es5-shim/-/es5-shim-4.6.7.tgz"
  "version" "4.6.7"

"es6-iterator@^2.0.3":
  "integrity" "sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es6-iterator/-/es6-iterator-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "d" "1"
    "es5-ext" "^0.10.35"
    "es6-symbol" "^3.1.1"

"es6-symbol@^3.1.1", "es6-symbol@^3.1.3":
  "integrity" "sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/es6-symbol/-/es6-symbol-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "d" "^1.0.1"
    "ext" "^1.1.2"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.5":
  "integrity" "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"eslint-config-prettier@^6.0.0":
  "integrity" "sha512-a1+kOYLR8wMGustcgAjdydMsQ2A/2ipRPwRKUmfYaSxc9ZPcrku080Ctl6zrZzZNs/U82MjSv+qKREkoq3bJaw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-config-prettier/-/eslint-config-prettier-6.15.0.tgz"
  "version" "6.15.0"
  dependencies:
    "get-stdin" "^6.0.0"

"eslint-loader@^2.2.1":
  "integrity" "sha512-RLgV9hoCVsMLvOxCuNjdqOrUqIj9oJg8hF44vzJaYqsAHuY9G2YAeN3joQ9nxP0p5Th9iFSIpKo+SD8KISxXRg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-loader/-/eslint-loader-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "loader-fs-cache" "^1.0.0"
    "loader-utils" "^1.0.2"
    "object-assign" "^4.0.1"
    "object-hash" "^1.1.4"
    "rimraf" "^2.6.1"

"eslint-plugin-prettier@^3.1.0", "eslint-plugin-prettier@^3.1.3":
  "integrity" "sha512-htg25EUYUeIhKHXjOinK4BgCcDwtLHjqaxCDsMy5nbnUMkKFvIhMVCp+5GFUXQ4Nr8lBsPqtGAqBenbpFqAA2g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-plugin-prettier/-/eslint-plugin-prettier-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-vue@^6.2.2":
  "integrity" "sha512-Nhc+oVAHm0uz/PkJAWscwIT4ijTrK5fqNqz9QB1D35SbbuMG1uB6Yr5AJpvPSWg+WOw7nYNswerYh0kOk64gqQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-plugin-vue/-/eslint-plugin-vue-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "natural-compare" "^1.4.0"
    "semver" "^5.6.0"
    "vue-eslint-parser" "^7.0.0"

"eslint-scope@^4.0.3":
  "integrity" "sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-scope/-/eslint-scope-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "esrecurse" "^4.1.0"
    "estraverse" "^4.1.1"

"eslint-scope@^5.0.0", "eslint-scope@^5.1.1":
  "integrity" "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-scope/-/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^1.4.3":
  "integrity" "sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-utils/-/eslint-utils-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-visitor-keys@^1.0.0", "eslint-visitor-keys@^1.1.0":
  "integrity" "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint@^5.0.0 || ^6.0.0", "eslint@^6.7.2", "eslint@>= 1.6.0 < 7.0.0", "eslint@>= 4.12.1", "eslint@>= 5.0.0", "eslint@>=1.6.0 <7.0.0", "eslint@>=3.14.1", "eslint@>=5.0.0":
  "integrity" "sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eslint/-/eslint-6.8.0.tgz"
  "version" "6.8.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "ajv" "^6.10.0"
    "chalk" "^2.1.0"
    "cross-spawn" "^6.0.5"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "eslint-scope" "^5.0.0"
    "eslint-utils" "^1.4.3"
    "eslint-visitor-keys" "^1.1.0"
    "espree" "^6.1.2"
    "esquery" "^1.0.1"
    "esutils" "^2.0.2"
    "file-entry-cache" "^5.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.0.0"
    "globals" "^12.1.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "inquirer" "^7.0.0"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.3.0"
    "lodash" "^4.17.14"
    "minimatch" "^3.0.4"
    "mkdirp" "^0.5.1"
    "natural-compare" "^1.4.0"
    "optionator" "^0.8.3"
    "progress" "^2.0.0"
    "regexpp" "^2.0.1"
    "semver" "^6.1.2"
    "strip-ansi" "^5.2.0"
    "strip-json-comments" "^3.0.1"
    "table" "^5.2.3"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^6.1.2", "espree@^6.2.1":
  "integrity" "sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/espree/-/espree-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-jsx" "^5.2.0"
    "eslint-visitor-keys" "^1.1.0"

"esprima@^4.0.0":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.0.1", "esquery@^1.4.0":
  "integrity" "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/esquery/-/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.1.0", "esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/estraverse/-/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^2.0.2":
  "integrity" "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/estree-walker/-/estree-walker-2.0.2.tgz"
  "version" "2.0.2"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-emitter@^0.3.5":
  "integrity" "sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/event-emitter/-/event-emitter-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "d" "1"
    "es5-ext" "~0.10.14"

"event-pubsub@4.3.0":
  "integrity" "sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/event-pubsub/-/event-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^4.0.0":
  "integrity" "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eventemitter3/-/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.0.0":
  "integrity" "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/events/-/events-3.3.0.tgz"
  "version" "3.3.0"

"eventsource@^2.0.2":
  "integrity" "sha512-IzUmBGPR3+oUG9dUeXynyNmf91/3zUSJg1lCktzKw47OXuhco54U3r9B7O4XX+Rb1Itm9OZ2b0RkTs10bICOxA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/eventsource/-/eventsource-2.0.2.tgz"
  "version" "2.0.2"

"evp_bytestokey@^1.0.0", "evp_bytestokey@^1.0.3":
  "integrity" "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "md5.js" "^1.3.4"
    "safe-buffer" "^5.1.1"

"execa@^0.8.0":
  "integrity" "sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/execa/-/execa-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/execa/-/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^3.3.0":
  "integrity" "sha512-r9vdGQk4bmCuK1yKQu1KTwcT2zwfWdbdaXfCtAh+5nU/4fSX+JAb7vZGvI5naJrQlvONrEB20jeruESI69530g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/execa/-/execa-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "p-finally" "^2.0.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"exit-on-epipe@~1.0.1":
  "integrity" "sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz"
  "version" "1.0.1"

"expand-brackets@^2.1.4":
  "integrity" "sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/expand-brackets/-/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"express@^4.16.3", "express@^4.17.1":
  "integrity" "sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/express/-/express-4.18.2.tgz"
  "version" "4.18.2"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.20.1"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.5.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.2.0"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.7"
    "qs" "6.11.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.18.0"
    "serve-static" "1.15.0"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"ext@^1.1.2":
  "integrity" "sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ext/-/ext-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "type" "^2.7.2"

"extend-shallow@^2.0.1":
  "integrity" "sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/extend-shallow/-/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0", "extend-shallow@^3.0.2":
  "integrity" "sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/extend-shallow/-/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend@~3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"external-editor@^3.0.3":
  "integrity" "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/external-editor/-/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^2.0.4":
  "integrity" "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/extglob/-/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^3.1.1":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fast-diff/-/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-glob@^2.2.6":
  "integrity" "sha512-g1KuQwHOZAmOZMuBtHdxDtju+T2RT8jgCC9aANsbpdiDDTSnjgfuVsIBNKbUeJI3oKMRExcfNDtJl4OhbffMsw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fast-glob/-/fast-glob-2.2.7.tgz"
  "version" "2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    "glob-parent" "^3.1.0"
    "is-glob" "^4.0.0"
    "merge2" "^1.2.3"
    "micromatch" "^3.1.10"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@~2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"faye-websocket@^0.11.3", "faye-websocket@^0.11.4":
  "integrity" "sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/faye-websocket/-/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"fflate@^0.4.8":
  "integrity" "sha512-FJqqoDBR00Mdj9ppamLa/Y7vxm+PRmNWA67N846RvsoYVMKB4q3y/de5PA7gUmRMYK/8CMz2GDZQmCRN1wBcWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fflate/-/fflate-0.4.8.tgz"
  "version" "0.4.8"

"figgy-pudding@^3.5.1":
  "integrity" "sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/figgy-pudding/-/figgy-pudding-3.5.2.tgz"
  "version" "3.5.2"

"figures@^3.0.0":
  "integrity" "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/figures/-/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^5.0.1":
  "integrity" "sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/file-entry-cache/-/file-entry-cache-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "flat-cache" "^2.0.1"

"file-loader@*", "file-loader@^4.2.0":
  "integrity" "sha512-aKrYPYjF1yG3oX0kWRrqrSMfgftm7oJW5M+m4owoldH5C51C0RkIwB++JbRvEW3IU6/ZG5n8UvEcdgwOt2UOWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/file-loader/-/file-loader-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "schema-utils" "^2.5.0"

"file-saver@^1.3.3":
  "integrity" "sha512-spKHSBQIxxS81N/O21WmuXA2F6wppUCsutpzenOeZzOCCJ5gEfcbqJP983IrpLXzYmXnMUa6J03SubcNPdKrlg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/file-saver/-/file-saver-1.3.8.tgz"
  "version" "1.3.8"

"file-uri-to-path@1.0.0":
  "integrity" "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filesize@^3.6.1":
  "integrity" "sha512-7KjR1vv6qnicaPMi1iiTcI85CyYwRO/PSFCu6SvqL8jN2Wjt/NIYQTFtFs7fSDCYOstUkEWIQGFUg5YZQfjlcg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/filesize/-/filesize-3.6.1.tgz"
  "version" "3.6.1"

"fill-range@^4.0.0":
  "integrity" "sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fill-range/-/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.2.0":
  "integrity" "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/finalhandler/-/finalhandler-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "statuses" "2.0.1"
    "unpipe" "~1.0.0"

"find-cache-dir@^0.1.1":
  "integrity" "sha512-Z9XSBoNE7xQiV6MSgPuCfyMokH2K7JdpRkOYE1+mu3d4BFJtx3GW+f6Bo4q8IX6rlf5MYbLBKW0pjl2cWdkm2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/find-cache-dir/-/find-cache-dir-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "commondir" "^1.0.1"
    "mkdirp" "^0.5.1"
    "pkg-dir" "^1.0.0"

"find-cache-dir@^2.1.0":
  "integrity" "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/find-cache-dir/-/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-cache-dir@^3.0.0", "find-cache-dir@^3.3.1":
  "integrity" "sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-up@^1.0.0":
  "integrity" "sha512-jvElSjyuo4EMQGoTwo1uJU5pQMwTW5lS1x05zzfJuTIyLR3zwO27LYrxNg+dlvKpGOuGy/MzBdXh80g0ve5+HA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/find-up/-/find-up-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "path-exists" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"find-up@^3.0.0":
  "integrity" "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/find-up/-/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^2.0.1":
  "integrity" "sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/flat-cache/-/flat-cache-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "flatted" "^2.0.0"
    "rimraf" "2.6.3"
    "write" "1.0.3"

"flatted@^2.0.0":
  "integrity" "sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/flatted/-/flatted-2.0.2.tgz"
  "version" "2.0.2"

"flush-write-stream@^1.0.0":
  "integrity" "sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/flush-write-stream/-/flush-write-stream-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "inherits" "^2.0.3"
    "readable-stream" "^2.3.6"

"follow-redirects@^1.0.0", "follow-redirects@^1.10.0":
  "integrity" "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/follow-redirects/-/follow-redirects-1.15.2.tgz"
  "version" "1.15.2"

"for-in@^1.0.2":
  "integrity" "sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/for-in/-/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forever-agent@~0.6.1":
  "integrity" "sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@~2.3.2":
  "integrity" "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"frac@~1.1.2":
  "integrity" "sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/frac/-/frac-1.1.2.tgz"
  "version" "1.1.2"

"fragment-cache@^0.2.1":
  "integrity" "sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fragment-cache/-/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@^2.1.0":
  "integrity" "sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/from2/-/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-extra@^7.0.1":
  "integrity" "sha512-YJDaCJZEnBmcbw13fvdAM9AwNOJwOzrE4pqMqBq5nFiEqXUqHwlK4B+3pUw6JNvfSPtX05xFHtYy/1ni01eGCw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fs-extra/-/fs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-write-stream-atomic@^1.0.8":
  "integrity" "sha512-gehEzmPn2nAwr39eay+x3X34Ra+M2QlVUTLhkXPjWdeO8RF9kszk116avgBJM3ZyNHgHXBNx+VmPaFC36k0PzA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "graceful-fs" "^4.1.2"
    "iferr" "^0.1.5"
    "imurmurhash" "^0.1.4"
    "readable-stream" "1 || 2"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"function.prototype.name@^1.1.5":
  "integrity" "sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/function.prototype.name/-/function.prototype.name-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"
    "functions-have-names" "^1.2.2"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"functions-have-names@^1.2.2":
  "integrity" "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.0", "get-intrinsic@^1.1.1", "get-intrinsic@^1.1.3":
  "integrity" "sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-intrinsic/-/get-intrinsic-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.3"

"get-stdin@^6.0.0":
  "integrity" "sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-stdin/-/get-stdin-6.0.0.tgz"
  "version" "6.0.0"

"get-stream@^3.0.0":
  "integrity" "sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-stream/-/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-stream/-/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0":
  "integrity" "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-stream/-/get-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-symbol-description@^1.0.0":
  "integrity" "sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/get-value/-/get-value-2.0.6.tgz"
  "version" "2.0.6"

"getpass@^0.1.1":
  "integrity" "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-parent@^3.1.0":
  "integrity" "sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/glob-parent/-/glob-parent-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-glob" "^3.1.0"
    "path-dirname" "^1.0.0"

"glob-parent@^5.0.0":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-to-regexp@^0.3.0":
  "integrity" "sha512-Iozmtbqv0noj0uDDqoL0zNq0VBEfK2YFoMAZoxJe4cwphvLR+JskfF30QhXHOR4m3KrE6NLRYw+U9MRXvifyig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/glob-to-regexp/-/glob-to-regexp-0.3.0.tgz"
  "version" "0.3.0"

"glob@^7.0.3", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"global@^4.3.0", "global@^4.3.1", "global@~4.3.0", "global@4.3.2":
  "integrity" "sha512-/4AybdwIDU4HkCUbJkZdWpe4P6vuw/CUtu+0I1YlLIPe7OlUO7KNJ+q/rO70CW2/NW6Jc6I62++Hzsf5Alu6rQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/global/-/global-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "min-document" "^2.19.0"
    "process" "~0.5.1"

"global@^4.4.0":
  "integrity" "sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/global/-/global-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "min-document" "^2.19.0"
    "process" "^0.11.10"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^12.1.0":
  "integrity" "sha512-BWICuzzDvDoH54NHKCseDanAhE3CeDorgDL5MT6LMXXj2WCnd9UC2szdk4AWLfjdgNBCXLUanXYcpBBKOSWGwg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/globals/-/globals-12.4.0.tgz"
  "version" "12.4.0"
  dependencies:
    "type-fest" "^0.8.1"

"globby@^6.1.0":
  "integrity" "sha512-KVbFv2TQtbzCoxAnfD6JcHZTYCzyliEaaeM/gH8qQdkKr5s0OP9scEgvdcngyk7AVdY6YVW/TJHd+lQ/Df3Daw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/globby/-/globby-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "array-union" "^1.0.1"
    "glob" "^7.0.3"
    "object-assign" "^4.0.1"
    "pify" "^2.0.0"
    "pinkie-promise" "^2.0.0"

"globby@^7.1.1":
  "integrity" "sha512-yANWAN2DUcBtuus5Cpd+SKROzXHs2iVXFZt/Ykrfz6SAXqacLX25NZpltE+39ceMexYF4TtEadjuSTw8+3wX4g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/globby/-/globby-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "array-union" "^1.0.1"
    "dir-glob" "^2.0.0"
    "glob" "^7.1.2"
    "ignore" "^3.3.5"
    "pify" "^3.0.0"
    "slash" "^1.0.0"

"globby@^9.2.0":
  "integrity" "sha512-ollPHROa5mcxDEkwg6bPt3QbEf4pDQSNtd6JPL1YvOvAo/7/0VAm9TccUeoTmarjPw4pfUthSCqcyfNB1I3ZSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/globby/-/globby-9.2.0.tgz"
  "version" "9.2.0"
  dependencies:
    "@types/glob" "^7.1.1"
    "array-union" "^1.0.2"
    "dir-glob" "^2.2.2"
    "fast-glob" "^2.2.6"
    "glob" "^7.1.3"
    "ignore" "^4.0.3"
    "pify" "^4.0.1"
    "slash" "^2.0.0"

"graceful-fs@^4.1.11", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.1.6":
  "integrity" "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/graceful-fs/-/graceful-fs-4.2.10.tgz"
  "version" "4.2.10"

"gzip-size@^5.0.0":
  "integrity" "sha512-FNHi6mmoHvs1mxZAds4PpdCS6QG8B4C1krxJsMutgxl5t3+GlRTzzI3NEkifXx2pVsOvJdOGSmIgDhQ55FwdPA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/gzip-size/-/gzip-size-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "duplexer" "^0.1.1"
    "pify" "^4.0.1"

"handle-thing@^2.0.0":
  "integrity" "sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/handle-thing/-/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"har-schema@^2.0.0":
  "integrity" "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/har-validator/-/har-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-bigints@^1.0.1", "has-bigints@^1.0.2":
  "integrity" "sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-bigints/-/has-bigints-1.0.2.tgz"
  "version" "1.0.2"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-property-descriptors@^1.0.0":
  "integrity" "sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.1.1"

"has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-symbols/-/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-tostringtag@^1.0.0":
  "integrity" "sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-value@^0.3.1":
  "integrity" "sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-value/-/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-value/-/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-values/-/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has-values/-/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.0", "has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-base@^3.0.0":
  "integrity" "sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hash-base/-/hash-base-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "inherits" "^2.0.4"
    "readable-stream" "^3.6.0"
    "safe-buffer" "^5.2.0"

"hash-sum@^1.0.2":
  "integrity" "sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hash-sum/-/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash-sum@^2.0.0":
  "integrity" "sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hash-sum/-/hash-sum-2.0.0.tgz"
  "version" "2.0.0"

"hash.js@^1.0.0", "hash.js@^1.0.3":
  "integrity" "sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hash.js/-/hash.js-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "inherits" "^2.0.3"
    "minimalistic-assert" "^1.0.1"

"he@^1.2.0", "he@1.2.x":
  "integrity" "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/he/-/he-1.2.0.tgz"
  "version" "1.2.0"

"hex-color-regex@^1.1.0":
  "integrity" "sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hex-color-regex/-/hex-color-regex-1.1.0.tgz"
  "version" "1.1.0"

"highlight.js@^10.7.1":
  "integrity" "sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/highlight.js/-/highlight.js-10.7.3.tgz"
  "version" "10.7.3"

"hmac-drbg@^1.0.1":
  "integrity" "sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hmac-drbg/-/hmac-drbg-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "hash.js" "^1.0.3"
    "minimalistic-assert" "^1.0.0"
    "minimalistic-crypto-utils" "^1.0.1"

"hoopy@^0.1.4":
  "integrity" "sha512-HRcs+2mr52W0K+x8RzcLzuPPmVIKMSv97RGHy0Ea9y/mpcaK+xTrjICA04KAHi4GRzxliNqNJEFYWHghy3rSfQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hoopy/-/hoopy-0.1.4.tgz"
  "version" "0.1.4"

"hosted-git-info@^2.1.4":
  "integrity" "sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hpack.js/-/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"hsl-regex@^1.0.0":
  "integrity" "sha512-M5ezZw4LzXbBKMruP+BNANf0k+19hDQMgpzBIYnya//Al+fjNct9Wf3b1WedLqdEs2hKBvxq/jh+DsHJLj0F9A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hsl-regex/-/hsl-regex-1.0.0.tgz"
  "version" "1.0.0"

"hsla-regex@^1.0.0":
  "integrity" "sha512-7Wn5GMLuHBjZCb2bTmnDOycho0p/7UVaAeqXZGbHrBCl6Yd/xDhQJAXe6Ga9AXJH2I5zY1dEdYw2u1UptnSBJA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/hsla-regex/-/hsla-regex-1.0.0.tgz"
  "version" "1.0.0"

"html-entities@^1.3.1":
  "integrity" "sha512-8nxjcBcd8wovbeKx7h3wTji4e6+rhaVuPNpMqwWgnHh+N9ToqsCs6XztWRBPQ+UtzsoMAdKZtUENoVzU/EMtZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html-entities/-/html-entities-1.4.0.tgz"
  "version" "1.4.0"

"html-minifier@^3.2.3":
  "integrity" "sha512-LKUKwuJDhxNa3uf/LPR/KVjm/l3rBqtYeCOAekvG8F1vItxMUpueGd94i/asDDr8/1u7InxzFA5EeGjhhG5mMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html-minifier/-/html-minifier-3.5.21.tgz"
  "version" "3.5.21"
  dependencies:
    "camel-case" "3.0.x"
    "clean-css" "4.2.x"
    "commander" "2.17.x"
    "he" "1.2.x"
    "param-case" "2.1.x"
    "relateurl" "0.2.x"
    "uglify-js" "3.4.x"

"html-tags@^2.0.0":
  "integrity" "sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html-tags/-/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-tags@^3.1.0":
  "integrity" "sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html-tags/-/html-tags-3.2.0.tgz"
  "version" "3.2.0"

"html-void-elements@^2.0.0":
  "integrity" "sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html-void-elements/-/html-void-elements-2.0.1.tgz"
  "version" "2.0.1"

"html-webpack-plugin@^3.2.0", "html-webpack-plugin@>=2.26.0":
  "integrity" "sha512-Br4ifmjQojUP4EmHnRBoUIYcZ9J7M4bTMcm7u6xoIAIuq2Nte4TzXX0533owvkQKQD1WeMTTTyD4Ni4QKxS0Bg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "html-minifier" "^3.2.3"
    "loader-utils" "^0.2.16"
    "lodash" "^4.17.3"
    "pretty-error" "^2.0.2"
    "tapable" "^1.0.0"
    "toposort" "^1.0.0"
    "util.promisify" "1.0.0"

"html2canvas@^1.0.0-rc.5", "html2canvas@^1.4.1":
  "integrity" "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/html2canvas/-/html2canvas-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "css-line-break" "^2.1.0"
    "text-segmentation" "^1.0.3"

"htmlparser2@^6.1.0":
  "integrity" "sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/htmlparser2/-/htmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-deceiver/-/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-errors/-/http-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@2.0.0":
  "integrity" "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-parser-js@>=0.5.1":
  "integrity" "sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-parser-js/-/http-parser-js-0.5.8.tgz"
  "version" "0.5.8"

"http-proxy-middleware@^1.0.0":
  "integrity" "sha512-13eVVDYS4z79w7f1+NPllJtOQFx/FdUW4btIvVRMaRlUY9VGstAbo5MOhLEuUgZFRHn3x50ufn25zkj/boZnEg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-proxy-middleware/-/http-proxy-middleware-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@types/http-proxy" "^1.17.5"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy-middleware@0.19.1":
  "integrity" "sha512-yHYTgWMQO8VvwNS22eLLloAkvungsKdKTLO8AJlftYIKNfJr3GK3zK0ZCfzDDGUBttdGc8xFy1mCitvNKQtC3Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "http-proxy" "^1.17.0"
    "is-glob" "^4.0.0"
    "lodash" "^4.17.11"
    "micromatch" "^3.1.10"

"http-proxy@^1.17.0", "http-proxy@^1.18.1":
  "integrity" "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-proxy/-/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"http-signature@~1.2.0":
  "integrity" "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"https-browserify@^1.0.0":
  "integrity" "sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/https-browserify/-/https-browserify-1.0.0.tgz"
  "version" "1.0.0"

"human-signals@^1.1.1":
  "integrity" "sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/human-signals/-/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"i18next@^20.4.0":
  "integrity" "sha512-yCMYTMEJ9ihCwEQQ3phLo7I/Pwycf8uAx+sRHwwk5U9Aui/IZYgQRyMqXafQOw5QQ7DM1Z+WyEXWIqSuJHhG2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/i18next/-/i18next-20.6.1.tgz"
  "version" "20.6.1"
  dependencies:
    "@babel/runtime" "^7.12.0"

"iconv-lite@^0.4.24", "iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^4.0.0", "icss-utils@^4.1.1":
  "integrity" "sha512-4aFq7wvWyMHKgxsH8QQtGpvbASCf+eM3wPRLI6R+MgAnTCZ6STYsRvttLvRWK0Nfif5piF394St3HeJDaljGPA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/icss-utils/-/icss-utils-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "postcss" "^7.0.14"

"ieee754@^1.1.4":
  "integrity" "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ieee754/-/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"iferr@^0.1.5":
  "integrity" "sha512-DUNFN5j7Tln0D+TxzloUjKB+CtVu6myn0JEFak6dG18mNt9YkQ6lzGCdafwofISZ1lLF3xRHJ98VKy9ynkcFaA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/iferr/-/iferr-0.1.5.tgz"
  "version" "0.1.5"

"ignore@^3.3.5":
  "integrity" "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ignore/-/ignore-3.3.10.tgz"
  "version" "3.3.10"

"ignore@^4.0.3", "ignore@^4.0.6":
  "integrity" "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ignore/-/ignore-4.0.6.tgz"
  "version" "4.0.6"

"image-size@~0.5.0":
  "integrity" "sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/image-size/-/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immer@^9.0.6":
  "integrity" "sha512-qenGE7CstVm1NrHQbMh8YaSzTZTFNP3zPqr3YU0S0UY441j4bJTg4A2Hh5KAhwgaiU6ZZ1Ar6y/2f4TblnMReQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/immer/-/immer-9.0.16.tgz"
  "version" "9.0.16"

"import-cwd@^2.0.0":
  "integrity" "sha512-Ew5AZzJQFqrOV5BTW3EIoHAnoie1LojZLXKcCQ/yTRyVZosBhK1x1ViYjHGf5pAFOq8ZyChZp6m/fSN7pJyZtg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/import-cwd/-/import-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "import-from" "^2.1.0"

"import-fresh@^2.0.0":
  "integrity" "sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/import-fresh/-/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-from@^2.1.0":
  "integrity" "sha512-0vdnLL2wSGnhlRmzHJAg5JHjt1l2vYhzJ7tNLGbeVg0fse56tpGaH0uzH+r9Slej+BSXXEHvBKDEnVSLLE9/+w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/import-from/-/import-from-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "resolve-from" "^3.0.0"

"import-local@^2.0.0":
  "integrity" "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/import-local/-/import-local-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "pkg-dir" "^3.0.0"
    "resolve-cwd" "^2.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indexes-of@^1.0.1":
  "integrity" "sha512-bup+4tap3Hympa+JBJUG7XuOsdNQ6fxt0MHyXMKuLBKn0OqsTfvUxkUrroEX1+B2VsSHvCjiIcZVxRtYa4nllA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/indexes-of/-/indexes-of-1.0.1.tgz"
  "version" "1.0.1"

"individual@^2.0.0":
  "integrity" "sha512-pWt8hBCqJsUWI/HtcfWod7+N9SgAqyPEaF7JQjwzjn5vGrpg6aQ5qeAFQ7dx//UH4J1O+7xqew+gCeeFt6xN/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/individual/-/individual-2.0.0.tgz"
  "version" "2.0.0"

"infer-owner@^1.0.3":
  "integrity" "sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/infer-owner/-/infer-owner-1.0.4.tgz"
  "version" "1.0.4"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.1", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.1":
  "integrity" "sha512-8nWq2nLTAwd02jTqJExUYFSD/fKq6VH9Y/oG2accc/kdI0V98Bag8d5a4gi3XHz73rDWa2PvTtvcWYquKqSENA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/inherits/-/inherits-2.0.1.tgz"
  "version" "2.0.1"

"inherits@2.0.3":
  "integrity" "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/inherits/-/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inquirer@^7.0.0", "inquirer@^7.1.0":
  "integrity" "sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/inquirer/-/inquirer-7.3.3.tgz"
  "version" "7.3.3"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.19"
    "mute-stream" "0.0.8"
    "run-async" "^2.4.0"
    "rxjs" "^6.6.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"

"internal-ip@^4.3.0":
  "integrity" "sha512-S1zBo1D6zcsyuC6PMmY5+55YMILQ9av8lotMx447Bq6SAgo/sDK6y6uUKmuYhW7eacnIhFfsPmCNYdDzsnnDCg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/internal-ip/-/internal-ip-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "default-gateway" "^4.2.0"
    "ipaddr.js" "^1.9.0"

"internal-slot@^1.0.3":
  "integrity" "sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/internal-slot/-/internal-slot-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "get-intrinsic" "^1.1.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"intersperse@^1.0.0":
  "integrity" "sha512-LGcfug7OTeWkaQ8PEq8XbTy9Jl6uCNg8DrPnQUmwxSY8UETj1Y+LLmpdD0qHdEj6KVchuH3BE3ZzIXQ1t3oFUw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/intersperse/-/intersperse-1.0.0.tgz"
  "version" "1.0.0"

"ip-regex@^2.1.0":
  "integrity" "sha512-58yWmlHpp7VYfcdTwMTvwMmqx/Elfxjd9RXTDyMsbL7lLWmhMylLEqiYVLKuLzOZqVgiWXD9MfR62Vv89VRxkw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ip-regex/-/ip-regex-2.1.0.tgz"
  "version" "2.1.0"

"ip@^1.1.0", "ip@^1.1.5":
  "integrity" "sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ip/-/ip-1.1.8.tgz"
  "version" "1.1.8"

"ipaddr.js@^1.9.0", "ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^2.0.0":
  "integrity" "sha512-vOx7VprsKyllwjSkLV79NIhpyLfr3jAp7VaTCMXOJHu4m0Ew1CZ2fcjASwmV1jI3BWuWHB013M48eyeldk9gYg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-absolute-url/-/is-absolute-url-2.1.0.tgz"
  "version" "2.1.0"

"is-absolute-url@^3.0.3":
  "integrity" "sha512-opmNIX7uFnS96NtPmhWQgQx6/NYFgsUXYMllcfzwWKUMwfo8kku1TvE6hkNcH+Q1ts5cMVrsY7j0bxXQDciu9Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-absolute-url/-/is-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha512-e1BM1qnDbMRG3ll2U9dSK0UMHuWOs3pY3AtcFsmvwPtKL3MML/Q86i+GilLfvqEs4GW+ExB91tQ3Ig9noDIZ+A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-arguments/-/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-arrayish/-/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-arrayish/-/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-bigint/-/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-binary-path@^1.0.0":
  "integrity" "sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-binary-path/-/is-binary-path-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "binary-extensions" "^1.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.1.0":
  "integrity" "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-buffer/-/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.4", "is-callable@^1.2.7":
  "integrity" "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-callable/-/is-callable-1.2.7.tgz"
  "version" "1.2.7"

"is-ci@^1.0.10":
  "integrity" "sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-ci/-/is-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-color-stop@^1.0.0":
  "integrity" "sha512-H1U8Vz0cfXNujrJzEcvvwMDW9Ra+biSYA3ThdQvAnMLJkEHQXn6bWzLkxHtVYJ+Sdbx0b6finn3jZiaVe7MAHA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-color-stop/-/is-color-stop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "css-color-names" "^0.0.4"
    "hex-color-regex" "^1.1.0"
    "hsl-regex" "^1.0.0"
    "hsla-regex" "^1.0.0"
    "rgb-regex" "^1.0.1"
    "rgba-regex" "^1.0.0"

"is-core-module@^2.9.0":
  "integrity" "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-core-module/-/is-core-module-2.11.0.tgz"
  "version" "2.11.0"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha512-+w9D5ulSoBNlmw9OHn3U2v51SyoCd0he+bB3xMl62oijhrspxowjU+AIcDY0N3iEJbUEkB15IlMASQsxYigvXg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-date-object/-/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-descriptor/-/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
  "integrity" "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-descriptor/-/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-directory/-/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-docker@^2.0.0":
  "integrity" "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-docker/-/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^0.1.1":
  "integrity" "sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-extendable/-/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-extendable/-/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.0", "is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-function@^1.0.1":
  "integrity" "sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-function/-/is-function-1.0.2.tgz"
  "version" "1.0.2"

"is-glob@^3.1.0":
  "integrity" "sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-glob/-/is-glob-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "is-extglob" "^2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-hotkey@^0.2.0":
  "integrity" "sha512-UknnZK4RakDmTgz4PI1wIph5yxSs/mvChWs9ifnlXsKuXgWmOkY/hAE0H/k2MIqH0RlRye0i1oC07MCRSD28Mw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-hotkey/-/is-hotkey-0.2.0.tgz"
  "version" "0.2.0"

"is-mobile@^2.2.1":
  "integrity" "sha512-wW/SXnYJkTjs++tVK5b6kVITZpAZPtUrt9SF80vvxGiF/Oywal+COk1jlRkiVq15RFNEQKQY31TkV24/1T5cVg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-mobile/-/is-mobile-2.2.2.tgz"
  "version" "2.2.2"

"is-negative-zero@^2.0.0", "is-negative-zero@^2.0.2":
  "integrity" "sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  "version" "2.0.2"

"is-number-object@^1.0.4":
  "integrity" "sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-number-object/-/is-number-object-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^3.0.0":
  "integrity" "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-number/-/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-obj@^2.0.0":
  "integrity" "sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-obj/-/is-obj-2.0.0.tgz"
  "version" "2.0.0"

"is-path-cwd@^2.0.0":
  "integrity" "sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-in-cwd@^2.0.0":
  "integrity" "sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-path-inside" "^2.1.0"

"is-path-inside@^2.1.0":
  "integrity" "sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-path-inside/-/is-path-inside-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "path-is-inside" "^1.0.2"

"is-plain-obj@^1.0.0":
  "integrity" "sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-plain-obj/-/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-obj@^3.0.0":
  "integrity" "sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-plain-object/-/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-plain-object@^5.0.0":
  "integrity" "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-plain-object/-/is-plain-object-5.0.0.tgz"
  "version" "5.0.0"

"is-regex@^1.0.4", "is-regex@^1.1.4":
  "integrity" "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-regex/-/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-resolvable@^1.0.0":
  "integrity" "sha512-qgDYXFSR5WvEfuS5dMj6oTMEbrrSaM0CrFk2Yiq/gXnBvD9pMa2jGXxyhGLfvhZpuMZe18CJpFxAt3CRs42NMg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-resolvable/-/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-shared-array-buffer@^1.0.2":
  "integrity" "sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-stream@^1.1.0":
  "integrity" "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-stream/-/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-stream/-/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-string/-/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-symbol/-/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typedarray@~1.0.0":
  "integrity" "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-url@^1.2.4":
  "integrity" "sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-url/-/is-url-1.2.4.tgz"
  "version" "1.2.4"

"is-weakref@^1.0.2":
  "integrity" "sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-weakref/-/is-weakref-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-what@^3.14.1":
  "integrity" "sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-what/-/is-what-3.14.1.tgz"
  "version" "3.14.1"

"is-windows@^1.0.2":
  "integrity" "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-windows/-/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-wsl/-/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"is-wsl@^2.1.1":
  "integrity" "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/is-wsl/-/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@^1.0.0", "isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"ismobilejs@^1.0.0":
  "integrity" "sha512-VaFW53yt8QO61k2WJui0dHf4SlL8lxBofUuUmwBo0ljPk0Drz2TiuDW4jo3wDcv41qy/SxrJ+VAzJ/qYqsmzRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ismobilejs/-/ismobilejs-1.1.1.tgz"
  "version" "1.1.1"

"isobject@^2.0.0":
  "integrity" "sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/isobject/-/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/isobject/-/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isstream@~0.1.2":
  "integrity" "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"javascript-stringify@^2.0.1":
  "integrity" "sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/javascript-stringify/-/javascript-stringify-2.1.0.tgz"
  "version" "2.1.0"

"js-cookie@^2.2.1":
  "integrity" "sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/js-cookie/-/js-cookie-2.2.1.tgz"
  "version" "2.2.1"

"js-export-excel@^1.1.4":
  "integrity" "sha512-19m7e3Gnn4CRfHXoFrLYj4fFfJ/KpvI7HRRn25p4GXYD+AlTV+1oU24NH6S904Ksi44tSx7futxhouOPAQ22oQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/js-export-excel/-/js-export-excel-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "blob.js" "^1.0.1"
    "file-saver" "^1.3.3"
    "script-loader" "0.7.2"
    "xlsx" "0.16.3"

"js-message@1.0.7":
  "integrity" "sha512-efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/js-message/-/js-message-1.0.7.tgz"
  "version" "1.0.7"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/js-yaml/-/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbn@~0.1.0":
  "integrity" "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsesc@^2.5.1":
  "integrity" "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/jsesc/-/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/jsesc/-/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.1", "json-parse-better-errors@^1.0.2":
  "integrity" "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.4.0":
  "integrity" "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json-schema/-/json-schema-0.4.0.tgz"
  "version" "0.4.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stringify-safe@~5.0.1":
  "integrity" "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"json2mq@^0.2.0":
  "integrity" "sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json2mq/-/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "^0.2.0"

"json5@^0.5.0":
  "integrity" "sha512-4xrs1aW+6N5DalkqSVA8fxh458CXvR99WU8WLKmq4v8eWAL86Xo3BVqyd3SkA9wEVjCMqyvvRRkshAdOnBp5rw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json5/-/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.1":
  "integrity" "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json5/-/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2", "json5@^2.2.1":
  "integrity" "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/json5/-/json5-2.2.1.tgz"
  "version" "2.2.1"

"jsonfile@^4.0.0":
  "integrity" "sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/jsonfile/-/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jspdf@^2.5.1":
  "integrity" "sha512-hXObxz7ZqoyhxET78+XR34Xu2qFGrJJ2I2bE5w4SM8eFaFEkW2xcGRVUss360fYelwRSid/jT078kbNvmoW0QA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/jspdf/-/jspdf-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "@babel/runtime" "^7.14.0"
    "atob" "^2.1.2"
    "btoa" "^1.2.1"
    "fflate" "^0.4.8"
  optionalDependencies:
    "canvg" "^3.0.6"
    "core-js" "^3.6.0"
    "dompurify" "^2.2.0"
    "html2canvas" "^1.0.0-rc.5"

"jsprim@^1.2.2":
  "integrity" "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/jsprim/-/jsprim-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.4.0"
    "verror" "1.10.0"

"killable@^1.0.1":
  "integrity" "sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/killable/-/killable-1.0.1.tgz"
  "version" "1.0.1"

"kind-of@^3.0.2", "kind-of@^3.0.3":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/kind-of/-/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/kind-of/-/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/kind-of/-/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/kind-of/-/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha512-K2yxgljj5TdCeRN1lBtO3/J26+AIDDDw+04y6VAiZbWcTdBwsYN6RrZBnW5DN/QiSIdKNjKdATLUUluWWFYTIA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/launch-editor-middleware/-/launch-editor-middleware-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "launch-editor" "^2.6.0"

"launch-editor@^2.2.1", "launch-editor@^2.6.0":
  "integrity" "sha512-JpDCcQnyAAzZZaZ7vEiSqL690w7dAEyLao+KC96zBplnYbJS7TYNjvM3M7y3dGz+v7aIsJk3hllWuc0kWAjyRQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/launch-editor/-/launch-editor-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "picocolors" "^1.0.0"
    "shell-quote" "^1.7.3"

"less-loader@^5.0.0":
  "integrity" "sha512-bquCU89mO/yWLaUq0Clk7qCsKhsF/TZpJUzETRvJa9KSVEL9SO3ovCvdEHISBhrC81OwC8QSVX7E0bzElZj9cg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/less-loader/-/less-loader-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "clone" "^2.1.1"
    "loader-utils" "^1.1.0"
    "pify" "^4.0.1"

"less@^2.3.1 || ^3.0.0", "less@^3.0.4":
  "integrity" "sha512-SwA1aQXGUvp+P5XdZslUOhhLnClSLIjWvJhmd+Vgib5BFIr9lMNlQwmwUNOjXThF/A0x+MCYYPeWEfeWiLRnTw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/less/-/less-3.13.1.tgz"
  "version" "3.13.1"
  dependencies:
    "copy-anything" "^2.0.1"
    "tslib" "^1.10.0"
  optionalDependencies:
    "errno" "^0.1.1"
    "graceful-fs" "^4.1.2"
    "image-size" "~0.5.0"
    "make-dir" "^2.1.0"
    "mime" "^1.4.1"
    "native-request" "^1.0.5"
    "source-map" "~0.6.0"

"levn@^0.3.0", "levn@~0.3.0":
  "integrity" "sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/levn/-/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"loader-fs-cache@^1.0.0":
  "integrity" "sha512-ldcgZpjNJj71n+2Mf6yetz+c9bM4xpKtNds4LbqXzU/PTdeAX0g3ytnU1AJMEcTk2Lex4Smpe3Q/eCTsvUBxbA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-fs-cache/-/loader-fs-cache-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "find-cache-dir" "^0.1.1"
    "mkdirp" "^0.5.1"

"loader-runner@^2.3.1", "loader-runner@^2.4.0":
  "integrity" "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-runner/-/loader-runner-2.4.0.tgz"
  "version" "2.4.0"

"loader-utils@^0.2.16":
  "integrity" "sha512-tiv66G0SmiOx+pLWMtGEkfSEejxvb6N6uRrQjfWJIT79W9GMpgKeCAmm9aVBKtd4WEgntciI8CsGqjpDoCWJug=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-utils/-/loader-utils-0.2.17.tgz"
  "version" "0.2.17"
  dependencies:
    "big.js" "^3.1.3"
    "emojis-list" "^2.0.0"
    "json5" "^0.5.0"
    "object-assign" "^4.0.1"

"loader-utils@^1.0.2":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^1.1.0":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^1.2.3":
  "integrity" "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-utils/-/loader-utils-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loader-utils/-/loader-utils-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"locate-path@^3.0.0":
  "integrity" "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/locate-path/-/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash.camelcase@^4.3.0":
  "integrity" "sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  "version" "4.3.0"

"lodash.clonedeep@^4.5.0":
  "integrity" "sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  "version" "4.5.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.foreach@^4.5.0":
  "integrity" "sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.foreach/-/lodash.foreach-4.5.0.tgz"
  "version" "4.5.0"

"lodash.isequal@^4.5.0":
  "integrity" "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
  "version" "4.5.0"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.throttle@^4.1.1":
  "integrity" "sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.toarray@^4.4.0":
  "integrity" "sha512-QyffEA3i5dma5q2490+SgCvDN0pXLmRGSyAANuVi0HQ01Pkfr9fuoKQW8wm1wGBnJITs/mS7wQvS6VshUEBFCw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.toarray/-/lodash.toarray-4.4.0.tgz"
  "version" "4.4.0"

"lodash.transform@^4.6.0":
  "integrity" "sha512-LO37ZnhmBVx0GvOU/caQuipEh4GN82TcWv3yHlebGDgOxbxiwwzW5Pcx2AcvpIv2WmvmSMoC492yQFNhy/l/UQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.transform/-/lodash.transform-4.6.0.tgz"
  "version" "4.6.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.11", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.19", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.17.3", "lodash@^4.17.5":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^2.2.0":
  "integrity" "sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/log-symbols/-/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"loglevel@^1.6.8":
  "integrity" "sha512-tCRIJM51SHjAayKwC+QAg8hT8vg6z7GSgLJKGvzuPb1Wc+hLzqtuVLxp6/HzSPOozuK+8ErAhy7U/sVzw8Dgfg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loglevel/-/loglevel-1.8.1.tgz"
  "version" "1.8.1"

"loose-envify@^1.0.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lower-case@^1.1.1":
  "integrity" "sha512-2Fgx1Ycm599x+WGpIYwJOvsjmXFzTSc34IwDWALRA/8AopUKAVPwfJ+h5+f85BCp0PWmmJcWzEpxOpoXycMpdA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lower-case/-/lower-case-1.1.4.tgz"
  "version" "1.1.4"

"lru-cache@^4.0.1":
  "integrity" "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.2":
  "integrity" "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lru-cache/-/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"m3u8-parser@2.1.0":
  "integrity" "sha512-WbEpQ2FUaNGbJ0YanSeyj9D9ruu4FUvz+ZvebIzI2bSME+PUwcPXO1kKXZkjcPUAFruDikoOI5fWQNIA6JCCOQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/m3u8-parser/-/m3u8-parser-2.1.0.tgz"
  "version" "2.1.0"

"magic-string@^0.30.11":
  "integrity" "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/magic-string/-/magic-string-0.30.17.tgz"
  "version" "0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

"make-dir@^2.0.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^2.1.0":
  "integrity" "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/make-dir/-/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/make-dir/-/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"map-cache@^0.2.2":
  "integrity" "sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/map-cache/-/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/map-visit/-/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"marked@^4.3.0":
  "integrity" "sha512-PRsaiG84bK+AMvxziE/lCFss8juXjNaWzVbN5tXAm4XjeaS9NAHhop+PjQxz2A9h8Q4M/xGmzP8vqNwy6JeK0A=="
  "resolved" "https://registry.npmjs.org/marked/-/marked-4.3.0.tgz"
  "version" "4.3.0"

"md5.js@^1.3.4":
  "integrity" "sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/md5.js/-/md5.js-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"
    "safe-buffer" "^5.1.2"

"mdn-data@2.0.14":
  "integrity" "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mdn-data/-/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdn-data@2.0.4":
  "integrity" "sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mdn-data/-/mdn-data-2.0.4.tgz"
  "version" "2.0.4"

"media-typer@0.3.0":
  "integrity" "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memory-fs@^0.4.1":
  "integrity" "sha512-cda4JKCxReDXFXRqOHPQscuIYg1PvxbE2S2GP45rnwfEK+vZaXC8C1OFvdHIbgw0DLzowXGVoxLaAmlgRy14GQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/memory-fs/-/memory-fs-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"memory-fs@^0.5.0":
  "integrity" "sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/memory-fs/-/memory-fs-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "errno" "^0.1.3"
    "readable-stream" "^2.0.1"

"merge-descriptors@1.0.1":
  "integrity" "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-source-map@^1.1.0":
  "integrity" "sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/merge-source-map/-/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^2.0.0":
  "integrity" "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/merge-stream/-/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.2.3":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^3.1.10", "micromatch@^3.1.4":
  "integrity" "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/micromatch/-/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"miller-rabin@^4.0.0":
  "integrity" "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/miller-rabin/-/miller-rabin-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "bn.js" "^4.0.0"
    "brorand" "^1.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-match@^1.0.2":
  "integrity" "sha512-VXp/ugGDVh3eCLOBCiHZMYWQaTNUHv2IJrut+yXA6+JbLPXHglHwfS/5A5L0ll+jkCY7fIzRJcH6OIunF+c6Cg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mime-match/-/mime-match-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "wildcard" "^1.1.0"

"mime-types@^2.1.12", "mime-types@~2.1.17", "mime-types@~2.1.19", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.4.1", "mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"mime@^2.4.4":
  "integrity" "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mime/-/mime-2.6.0.tgz"
  "version" "2.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mimic-fn/-/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mimic-fn/-/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"min-document@^2.19.0":
  "integrity" "sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/min-document/-/min-document-2.19.0.tgz"
  "version" "2.19.0"
  dependencies:
    "dom-walk" "^0.1.0"

"mini-css-extract-plugin@^0.9.0":
  "integrity" "sha512-lp3GeY7ygcgAmVIcRPBVhIkf8Us7FZjA+ILpal44qLdSu11wmjKQ3d9k15lfD7pO4esu9eUIAW7qiYIBppv40A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mini-css-extract-plugin/-/mini-css-extract-plugin-0.9.0.tgz"
  "version" "0.9.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "normalize-url" "1.9.1"
    "schema-utils" "^1.0.0"
    "webpack-sources" "^1.1.0"

"minimalistic-assert@^1.0.0", "minimalistic-assert@^1.0.1":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimalistic-crypto-utils@^1.0.1":
  "integrity" "sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@^3.1.1":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.2.0", "minimist@^1.2.5", "minimist@^1.2.6":
  "integrity" "sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/minimist/-/minimist-1.2.7.tgz"
  "version" "1.2.7"

"minipass@^3.1.1":
  "integrity" "sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/minipass/-/minipass-3.3.4.tgz"
  "version" "3.3.4"
  dependencies:
    "yallist" "^4.0.0"

"mississippi@^3.0.0":
  "integrity" "sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mississippi/-/mississippi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "concat-stream" "^1.5.0"
    "duplexify" "^3.4.2"
    "end-of-stream" "^1.1.0"
    "flush-write-stream" "^1.0.0"
    "from2" "^2.1.0"
    "parallel-transform" "^1.1.0"
    "pump" "^3.0.0"
    "pumpify" "^1.3.3"
    "stream-each" "^1.1.0"
    "through2" "^2.0.0"

"mixin-deep@^1.2.0":
  "integrity" "sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mixin-deep/-/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1", "mkdirp@^0.5.3", "mkdirp@^0.5.6", "mkdirp@~0.5.1":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"moment@^2.21.0", "moment@^2.30.1":
  "integrity" "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/moment/-/moment-2.30.1.tgz"
  "version" "2.30.1"

"move-concurrently@^1.0.1":
  "integrity" "sha512-hdrFxZOycD/g6A6SoI2bB5NA/5NEqD0569+S47WZhPvm46sD50ZHdYaFmnua5lndde9rCHGjmfK7Z8BuCt/PcQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/move-concurrently/-/move-concurrently-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "aproba" "^1.1.1"
    "copy-concurrently" "^1.0.0"
    "fs-write-stream-atomic" "^1.0.8"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"
    "run-queue" "^1.0.3"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha512-cnAsSVxIDsYt0v7HmC0hWZFwwXSh+E6PgCrREDuN/EsjgLwA5XRmlMHhSiDPrt6HxY1gTivEa/Zh7GtODoLevQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/multicast-dns/-/multicast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mutationobserver-shim@^0.3.2":
  "integrity" "sha512-oRIDTyZQU96nAiz2AQyngwx1e89iApl2hN5AOYwyxLUB47UYsU3Wv9lJWqH5y/QdiYkc5HQLi23ZNB3fELdHcQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mutationobserver-shim/-/mutationobserver-shim-0.3.7.tgz"
  "version" "0.3.7"

"mute-stream@0.0.8":
  "integrity" "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mute-stream/-/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"mux.js@4.3.2":
  "integrity" "sha512-g0q6DPdvb3yYcoK7ElBGobdSSrhY/RjPt19U7uUc733aqvc5bCS/aCvL9z+448y+IoCZnYDwyZfQBBXMSmGOaQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mux.js/-/mux.js-4.3.2.tgz"
  "version" "4.3.2"

"mz@^2.4.0":
  "integrity" "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/mz/-/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"namespace-emitter@^2.0.1":
  "integrity" "sha512-N/sMKHniSDJBjfrkbS/tpkPj4RAbvW3mr8UAzvlMHyun93XEm83IAvhWtJVHo+RHn/oO8Job5YN4b+wRjSVp5g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/namespace-emitter/-/namespace-emitter-2.0.1.tgz"
  "version" "2.0.1"

"nanoid@^3.1.25", "nanoid@^3.2.0", "nanoid@^3.3.8":
  "integrity" "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/nanoid/-/nanoid-3.3.8.tgz"
  "version" "3.3.8"

"nanomatch@^1.2.9":
  "integrity" "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/nanomatch/-/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"nanopop@^2.1.0":
  "integrity" "sha512-E9JaHcxh3ere8/BEZHAcnuD10RluTSPyTToBvoFWS9/7DcCx6gyKjbn7M7Bx7E1veCxCuY1iO6h4+gdAf1j73Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/nanopop/-/nanopop-2.2.0.tgz"
  "version" "2.2.0"

"native-request@^1.0.5":
  "integrity" "sha512-uZ5rQaeRn15XmpgE0xoPL8YWqcX90VtCFglYwAgkvKM5e8fog+vePLAhHxuuv/gRkrQxIeh5U3q9sMNUrENqWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/native-request/-/native-request-1.1.0.tgz"
  "version" "1.1.0"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"neo-async@^2.5.0", "neo-async@^2.6.0", "neo-async@^2.6.1":
  "integrity" "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/neo-async/-/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"next-tick@^1.1.0":
  "integrity" "sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/next-tick/-/next-tick-1.1.0.tgz"
  "version" "1.1.0"

"nice-try@^1.0.4":
  "integrity" "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/nice-try/-/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^2.2.0":
  "integrity" "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/no-case/-/no-case-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "lower-case" "^1.1.1"

"node-emoji@^1.10.0":
  "integrity" "sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/node-emoji/-/node-emoji-1.11.0.tgz"
  "version" "1.11.0"
  dependencies:
    "lodash" "^4.17.21"

"node-forge@^0.10.0":
  "integrity" "sha512-PPmu8eEeG9saEUvI97fm4OYxXVB6bFvyNTyiUOBichBpFG8A1Ljw3bY62+5oOjDEMHRnd0Y7HQ+x7uzxOzC6JA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/node-forge/-/node-forge-0.10.0.tgz"
  "version" "0.10.0"

"node-libs-browser@^2.2.1":
  "integrity" "sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/node-libs-browser/-/node-libs-browser-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "assert" "^1.1.1"
    "browserify-zlib" "^0.2.0"
    "buffer" "^4.3.0"
    "console-browserify" "^1.1.0"
    "constants-browserify" "^1.0.0"
    "crypto-browserify" "^3.11.0"
    "domain-browser" "^1.1.1"
    "events" "^3.0.0"
    "https-browserify" "^1.0.0"
    "os-browserify" "^0.3.0"
    "path-browserify" "0.0.1"
    "process" "^0.11.10"
    "punycode" "^1.2.4"
    "querystring-es3" "^0.2.0"
    "readable-stream" "^2.3.3"
    "stream-browserify" "^2.0.1"
    "stream-http" "^2.7.2"
    "string_decoder" "^1.0.0"
    "timers-browserify" "^2.0.4"
    "tty-browserify" "0.0.0"
    "url" "^0.11.0"
    "util" "^0.11.0"
    "vm-browserify" "^1.0.1"

"node-releases@^2.0.6":
  "integrity" "sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/node-releases/-/node-releases-2.0.6.tgz"
  "version" "2.0.6"

"normalize-package-data@^2.5.0":
  "integrity" "sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-path/-/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^2.1.1":
  "integrity" "sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-path/-/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^3.0.0":
  "integrity" "sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-url/-/normalize-url-3.3.0.tgz"
  "version" "3.3.0"

"normalize-url@1.9.1":
  "integrity" "sha512-A48My/mtCklowHBlI8Fq2jFWK4tX4lJ5E6ytFsSOq1fzpvT0SQSgKhSg7lN5c2uYFOrUAOQp6zhhJnpp1eMloQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/normalize-url/-/normalize-url-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "object-assign" "^4.0.1"
    "prepend-http" "^1.0.0"
    "query-string" "^4.1.0"
    "sort-keys" "^1.0.0"

"npm-run-path@^2.0.0":
  "integrity" "sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/npm-run-path/-/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.0":
  "integrity" "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/npm-run-path/-/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nth-check@^1.0.2":
  "integrity" "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/nth-check/-/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"nth-check@^2.0.1":
  "integrity" "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/nth-check/-/nth-check-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "boolbase" "^1.0.0"

"num2fraction@^1.2.2":
  "integrity" "sha512-Y1wZESM7VUThYY+4W+X4ySH2maqcA+p7UR+w8VWNWVAd6lwuXXWz/w/Cz43J/dI2I+PS6wD5N+bJUF+gjWvIqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/num2fraction/-/num2fraction-1.2.2.tgz"
  "version" "1.2.2"

"oauth-sign@~0.9.0":
  "integrity" "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1", "object-assign@4.x":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-copy/-/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^1.1.4":
  "integrity" "sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-hash/-/object-hash-1.3.1.tgz"
  "version" "1.3.1"

"object-inspect@^1.12.2", "object-inspect@^1.9.0":
  "integrity" "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-inspect/-/object-inspect-1.12.2.tgz"
  "version" "1.12.2"

"object-is@^1.0.1":
  "integrity" "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-is/-/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object-visit/-/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0", "object.assign@^4.1.4":
  "integrity" "sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object.assign/-/object.assign-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "has-symbols" "^1.0.3"
    "object-keys" "^1.1.1"

"object.getownpropertydescriptors@^2.0.3":
  "integrity" "sha512-yDNzckpM6ntyQiGTik1fKV1DcVDRS+w8bvpWNCBanvH5LfRX9O8WTHqQzG4RZwRAM4I0oU7TV11Lj5v0g20ibw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "array.prototype.reduce" "^1.0.5"
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"object.pick@^1.3.0":
  "integrity" "sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object.pick/-/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.0":
  "integrity" "sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/object.values/-/object.values-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/obuf/-/obuf-1.1.2.tgz"
  "version" "1.1.2"

"omit.js@^1.0.0":
  "integrity" "sha512-/QPc6G2NS+8d4L/cQhbk6Yit1WTB6Us2g84A7A/1+w9d/eRGHyEqC5kkQtHVoHZ5NFWGG7tUGgrhVZwgZanKrQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/omit.js/-/omit.js-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "babel-runtime" "^6.23.0"

"on-finished@2.4.1":
  "integrity" "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/on-finished/-/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/onetime/-/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0":
  "integrity" "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/onetime/-/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^6.3.0":
  "integrity" "sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/open/-/open-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"opencollective-postinstall@^2.0.2":
  "integrity" "sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/opencollective-postinstall/-/opencollective-postinstall-2.0.3.tgz"
  "version" "2.0.3"

"opener@^1.5.1":
  "integrity" "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/opener/-/opener-1.5.2.tgz"
  "version" "1.5.2"

"opn@^5.5.0":
  "integrity" "sha512-PqHpggC9bLV0VeWcdKhkpxY+3JTzetLSqTCWL/z/tFIbI6G8JCjondXklT1JinczLz2Xib62sSp0T/gKT4KksA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/opn/-/opn-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "is-wsl" "^1.1.0"

"optionator@^0.8.3":
  "integrity" "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/optionator/-/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"ora@^3.4.0":
  "integrity" "sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ora/-/ora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"os-browserify@^0.3.0":
  "integrity" "sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/os-browserify/-/os-browserify-0.3.0.tgz"
  "version" "0.3.0"

"os-tmpdir@~1.0.2":
  "integrity" "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-finally@^1.0.0":
  "integrity" "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-finally/-/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-finally@^2.0.0":
  "integrity" "sha512-vpm09aKwq6H9phqRQzecoDpD8TmVyGw70qmWlyq5onxY7tqyTTFVvxMykxQSQKILBSFlbXpypIw2T1Ml7+DDtw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-finally/-/p-finally-2.0.1.tgz"
  "version" "2.0.1"

"p-limit@^2.0.0", "p-limit@^2.2.0", "p-limit@^2.2.1":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^3.0.0":
  "integrity" "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-locate/-/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^2.0.0":
  "integrity" "sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-map/-/p-map-2.1.0.tgz"
  "version" "2.1.0"

"p-retry@^3.0.1":
  "integrity" "sha512-XE6G4+YTTkT2a0UWb2kjZe8xNwf8bIbnqpc/IS/idOBVhyves0mK5OJgeocjx7q5pvX/6m23xuzVPYT1uGM73w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-retry/-/p-retry-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "retry" "^0.12.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.5":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"pangea-com@0.1.5-beta.6":
  "integrity" "sha512-Ij6k3xaE9wWVBF+y4TZpx16fMUPZM5nZ1Ck5bVl0ao3gZl2vnZ7RLc1K+bsvypCT3Z/LNe0Rp2420BmA72Gb3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pangea-com/-/pangea-com-0.1.5-beta.6.tgz"
  "version" "0.1.5-beta.6"
  dependencies:
    "ant-design-vue" "1.6.5"
    "core-js" "^3.6.5"
    "js-cookie" "^2.2.1"
    "vue" "^2.6.11"

"parallel-transform@^1.1.0":
  "integrity" "sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parallel-transform/-/parallel-transform-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "cyclist" "^1.0.1"
    "inherits" "^2.0.3"
    "readable-stream" "^2.1.5"

"param-case@2.1.x":
  "integrity" "sha512-eQE845L6ot89sk2N8liD8HAuH4ca6Vvr7VWAWwt7+kvvG5aBcPmmphQ68JsEG2qa9n1TykS2DLeMt363AAH8/w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/param-case/-/param-case-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "no-case" "^2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-asn1@^5.0.0", "parse-asn1@^5.1.5":
  "integrity" "sha512-RnZRo1EPU6JBnra2vGHj0yhp6ebyjBZpmUCLHWiFhxlzvBCCpAuZ7elsBp1PVAbQN0/04VD/19rfzlBSwLstMw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse-asn1/-/parse-asn1-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "asn1.js" "^5.2.0"
    "browserify-aes" "^1.0.0"
    "evp_bytestokey" "^1.0.0"
    "pbkdf2" "^3.0.3"
    "safe-buffer" "^5.1.1"

"parse-headers@^2.0.0":
  "integrity" "sha512-ft3iAoLOB/MlwbNXgzy43SWGP6sQki2jQvAyBg/zDFAgr9bfNWZIUj42Kw2eJIl8kEi4PbgE6U1Zau/HwI75HA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse-headers/-/parse-headers-2.0.5.tgz"
  "version" "2.0.5"

"parse-json@^4.0.0":
  "integrity" "sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse-json/-/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse-json/-/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^6.0.0":
  "integrity" "sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5@^5.1.1":
  "integrity" "sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse5/-/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@^6.0.1":
  "integrity" "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parse5/-/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pascalcase/-/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@0.0.1":
  "integrity" "sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-browserify/-/path-browserify-0.0.1.tgz"
  "version" "0.0.1"

"path-dirname@^1.0.0":
  "integrity" "sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-dirname/-/path-dirname-1.0.2.tgz"
  "version" "1.0.2"

"path-exists@^2.0.0":
  "integrity" "sha512-yTltuKuhtNeFJKa1PiRzfLAU5182q1y4Eb4XCJ3PBqyzEDkAZRzBrKKBct682ls9reBVHf9udYLN5Nd+K1B9BQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-exists/-/path-exists-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pinkie-promise" "^2.0.0"

"path-exists@^3.0.0":
  "integrity" "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-exists/-/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-is-inside@^1.0.2":
  "integrity" "sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-is-inside/-/path-is-inside-1.0.2.tgz"
  "version" "1.0.2"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-key/-/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@0.1.7":
  "integrity" "sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^3.0.0":
  "integrity" "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/path-type/-/path-type-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "pify" "^3.0.0"

"pbkdf2@^3.0.3":
  "integrity" "sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pbkdf2/-/pbkdf2-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "create-hash" "^1.1.2"
    "create-hmac" "^1.1.4"
    "ripemd160" "^2.0.1"
    "safe-buffer" "^5.0.1"
    "sha.js" "^2.4.8"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^0.2.1":
  "integrity" "sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/picocolors/-/picocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.0.0":
  "integrity" "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pify/-/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pify/-/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pinkie/-/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pkcs7@^0.2.3":
  "integrity" "sha512-kJRwmADEQUg+qJyRgWLtpEL9q9cFjZschejTEK3GRjKvnsU9G5WWoe/wKqRgbBoqWdVSeTUKP6vIA3Y72M3rWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pkcs7/-/pkcs7-0.2.3.tgz"
  "version" "0.2.3"

"pkg-dir@^1.0.0":
  "integrity" "sha512-c6pv3OE78mcZ92ckebVDqg0aWSoKhOTbwCV6qbCWMk546mAL9pZln0+QsN/yQ7fkucd4+yJPLrCBXNt8Ruk+Eg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pkg-dir/-/pkg-dir-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "find-up" "^1.0.0"

"pkg-dir@^3.0.0":
  "integrity" "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pkg-dir/-/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.1.0":
  "integrity" "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pkg-dir/-/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pnp-webpack-plugin@^1.6.4":
  "integrity" "sha512-2Rb3vm+EXble/sMXNSu6eoBx8e79gKqhNq9F5ZWW6ERNCTE/Q0wQNne5541tE5vKjfM8hpNCYL+LGc1YTfI0dg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pnp-webpack-plugin/-/pnp-webpack-plugin-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "ts-pnp" "^1.1.6"

"portfinder@^1.0.26":
  "integrity" "sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/portfinder/-/portfinder-1.0.32.tgz"
  "version" "1.0.32"
  dependencies:
    "async" "^2.6.4"
    "debug" "^3.2.7"
    "mkdirp" "^0.5.6"

"posix-character-classes@^0.1.0":
  "integrity" "sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/posix-character-classes/-/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^7.0.1":
  "integrity" "sha512-1tKHutbGtLtEZF6PT4JSihCHfIVldU72mZ8SdZHIYriIZ9fh9k9aWSppaT8rHsyI3dX+KSR+W+Ix9BMY3AODrg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-calc/-/postcss-calc-7.0.5.tgz"
  "version" "7.0.5"
  dependencies:
    "postcss" "^7.0.27"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^4.0.3":
  "integrity" "sha512-WyQFAdDZpExQh32j0U0feWisZ0dmOtPl44qYmJKkq9xFWY3p+4qnRzCHeNrkeRhwPHz9bQ3mo0/yVkaply0MNw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-colormin/-/postcss-colormin-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "color" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-convert-values@^4.0.1":
  "integrity" "sha512-Kisdo1y77KUC0Jmn0OXU/COOJbzM8cImvw1ZFsBgBgMgb1iL23Zs/LXRe3r+EZqM3vGYKdQ2YJVQ5VkJI+zEJQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-convert-values/-/postcss-convert-values-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-discard-comments@^4.0.2":
  "integrity" "sha512-RJutN259iuRf3IW7GZyLM5Sw4GLTOH8FmsXBnv8Ab/Tc2k4SR4qbV4DNbyyY4+Sjo362SyDmW2DQ7lBSChrpkg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-discard-comments/-/postcss-discard-comments-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-duplicates@^4.0.2":
  "integrity" "sha512-ZNQfR1gPNAiXZhgENFfEglF93pciw0WxMkJeVmw8eF+JZBbMD7jp6C67GqJAXVZP2BWbOztKfbsdmMp/k8c6oQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-empty@^4.0.1":
  "integrity" "sha512-B9miTzbznhDjTfjvipfHoqbWKwd0Mj+/fL5s1QOz06wufguil+Xheo4XpOnc4NqKYBCNqqEzgPv2aPBIJLox0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-discard-empty/-/postcss-discard-empty-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-discard-overridden@^4.0.1":
  "integrity" "sha512-IYY2bEDD7g1XM1IDEsUT4//iEYCxAmP5oDSFMVU/JVvT7gh+l4fmjciLqGgwjdWpQIdb0Che2VX00QObS5+cTg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-discard-overridden/-/postcss-discard-overridden-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-load-config@^2.0.0":
  "integrity" "sha512-/rDeGV6vMUo3mwJZmeHfEDvwnTKKqQ0S7OHUi/kJvvtx3aWtyWG2/0ZWnzCt2keEclwN6Tf0DST2v9kITdOKYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-load-config/-/postcss-load-config-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "cosmiconfig" "^5.0.0"
    "import-cwd" "^2.0.0"

"postcss-loader@^3.0.0":
  "integrity" "sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-loader/-/postcss-loader-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "loader-utils" "^1.1.0"
    "postcss" "^7.0.0"
    "postcss-load-config" "^2.0.0"
    "schema-utils" "^1.0.0"

"postcss-merge-longhand@^4.0.11":
  "integrity" "sha512-alx/zmoeXvJjp7L4mxEMjh8lxVlDFX1gqWHzaaQewwMZiVhLo42TEClKaeHbRf6J7j82ZOdTJ808RtN0ZOZwvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-merge-longhand/-/postcss-merge-longhand-4.0.11.tgz"
  "version" "4.0.11"
  dependencies:
    "css-color-names" "0.0.4"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "stylehacks" "^4.0.0"

"postcss-merge-rules@^4.0.3":
  "integrity" "sha512-U7e3r1SbvYzO0Jr3UT/zKBVgYYyhAz0aitvGIYOYK5CPmkNih+WDSsS5tvPrJ8YMQYlEMvsZIiqmn7HdFUaeEQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-merge-rules/-/postcss-merge-rules-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "cssnano-util-same-parent" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"
    "vendors" "^1.0.0"

"postcss-minify-font-values@^4.0.2":
  "integrity" "sha512-j85oO6OnRU9zPf04+PZv1LYIYOprWm6IA6zkXkrJXyRveDEuQggG6tvoy8ir8ZwjLxLuGfNkCZEQG7zan+Hbtg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-minify-font-values/-/postcss-minify-font-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-gradients@^4.0.2":
  "integrity" "sha512-qKPfwlONdcf/AndP1U8SJ/uzIJtowHlMaSioKzebAXSG4iJthlWC9iSWznQcX4f66gIWX44RSA841HTHj3wK+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-minify-gradients/-/postcss-minify-gradients-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "is-color-stop" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-minify-params@^4.0.2":
  "integrity" "sha512-G7eWyzEx0xL4/wiBBJxJOz48zAKV2WG3iZOqVhPet/9geefm/Px5uo1fzlHu+DOjT+m0Mmiz3jkQzVHe6wxAWg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-minify-params/-/postcss-minify-params-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "browserslist" "^4.0.0"
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "uniqs" "^2.0.0"

"postcss-minify-selectors@^4.0.2":
  "integrity" "sha512-D5S1iViljXBj9kflQo4YutWnJmwm8VvIsU1GeXJGiG9j8CIg9zs4voPMdQDUmIxetUOh60VilsNzCiAFTOqu3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-minify-selectors/-/postcss-minify-selectors-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"postcss-modules-extract-imports@^2.0.0":
  "integrity" "sha512-LaYLDNS4SG8Q5WAWqIJgdHPJrDDr/Lv775rMBFUbgjTz6j34lUznACHcdRWroPvXANP2Vj7yNK57vp9eFqzLWQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-modules-extract-imports/-/postcss-modules-extract-imports-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "postcss" "^7.0.5"

"postcss-modules-local-by-default@^3.0.2":
  "integrity" "sha512-e3xDq+LotiGesympRlKNgaJ0PCzoUIdpH0dj47iWAui/kyTgh3CiAr1qP54uodmJhl6p9rN6BoNcdEDVJx9RDw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-modules-local-by-default/-/postcss-modules-local-by-default-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "icss-utils" "^4.1.1"
    "postcss" "^7.0.32"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^2.2.0":
  "integrity" "sha512-YyEgsTMRpNd+HmyC7H/mh3y+MeFWevy7V1evVhJWewmMbjDHIbZbOXICC2y+m1xI1UVfIT1HMW/O04Hxyu9oXQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-modules-scope/-/postcss-modules-scope-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "postcss" "^7.0.6"
    "postcss-selector-parser" "^6.0.0"

"postcss-modules-values@^3.0.0":
  "integrity" "sha512-1//E5jCBrZ9DmRX+zCtmQtRSV6PV42Ix7Bzj9GbwJceduuf7IqP8MgeTXuRDHOWj2m0VzZD5+roFWDuU8RQjcg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-modules-values/-/postcss-modules-values-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "icss-utils" "^4.0.0"
    "postcss" "^7.0.6"

"postcss-normalize-charset@^4.0.1":
  "integrity" "sha512-gMXCrrlWh6G27U0hF3vNvR3w8I1s2wOBILvA87iNXaPvSNo5uZAMYsZG7XjCUf1eVxuPfyL4TJ7++SGZLc9A3g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-charset/-/postcss-normalize-charset-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "^7.0.0"

"postcss-normalize-display-values@^4.0.2":
  "integrity" "sha512-3F2jcsaMW7+VtRMAqf/3m4cPFhPD3EFRgNs18u+k3lTJJlVe7d0YPO+bnwqo2xg8YiRpDXJI2u8A0wqJxMsQuQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-positions@^4.0.2":
  "integrity" "sha512-Dlf3/9AxpxE+NF1fJxYDeggi5WwV35MXGFnnoccP/9qDtFrTArZ0D0R+iKcg5WsUd8nUYMIl8yXDCtcrT8JrdA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-positions/-/postcss-normalize-positions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-repeat-style@^4.0.2":
  "integrity" "sha512-qvigdYYMpSuoFs3Is/f5nHdRLJN/ITA7huIoCyqqENJe9PvPmLhNLMu7QTjPdtnVf6OcYYO5SHonx4+fbJE1+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-string@^4.0.2":
  "integrity" "sha512-RrERod97Dnwqq49WNz8qo66ps0swYZDSb6rM57kN2J+aoyEAJfZ6bMx0sx/F9TIEX0xthPGCmeyiam/jXif0eA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-string/-/postcss-normalize-string-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-timing-functions@^4.0.2":
  "integrity" "sha512-acwJY95edP762e++00Ehq9L4sZCEcOPyaHwoaFOhIwWCDfik6YvqsYNxckee65JHLKzuNSSmAdxwD2Cud1Z54A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-unicode@^4.0.1":
  "integrity" "sha512-od18Uq2wCYn+vZ/qCOeutvHjB5jm57ToxRaMeNuf0nWVHaP9Hua56QyMF6fs/4FSUnVIw0CBPsU0K4LnBPwYwg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-url@^4.0.1":
  "integrity" "sha512-p5oVaF4+IHwu7VpMan/SSpmpYxcJMtkGppYf0VbdH5B6hN8YNmVyJLuY9FmLQTzY3fag5ESUUHDqM+heid0UVA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-url/-/postcss-normalize-url-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-absolute-url" "^2.0.0"
    "normalize-url" "^3.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-normalize-whitespace@^4.0.2":
  "integrity" "sha512-tO8QIgrsI3p95r8fyqKV+ufKlSHh9hMJqACqbv2XknufqEDhDvbguXGBBqxw9nsQoXWf0qOqppziKJKHMD4GtA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-ordered-values@^4.1.2":
  "integrity" "sha512-2fCObh5UanxvSxeXrtLtlwVThBvHn6MQcu4ksNT2tsaV2Fg76R2CV98W7wNSlX+5/pFwEyaDwKLLoEV7uRybAw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-ordered-values/-/postcss-ordered-values-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "cssnano-util-get-arguments" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-reduce-initial@^4.0.3":
  "integrity" "sha512-gKWmR5aUulSjbzOfD9AlJiHCGH6AEVLaM0AV+aSioxUDd16qXP1PCh8d1/BGVvpdWn8k/HiK7n6TjeoXN1F7DA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-reduce-initial/-/postcss-reduce-initial-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-api" "^3.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"

"postcss-reduce-transforms@^4.0.2":
  "integrity" "sha512-EEVig1Q2QJ4ELpJXMZR8Vt5DQx8/mo+dGWSR7vWXqcob2gQLyQGsionYcGKATXvQzMPn6DSN1vTN7yFximdIAg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "cssnano-util-get-match" "^4.0.0"
    "has" "^1.0.0"
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"

"postcss-selector-parser@^3.0.0":
  "integrity" "sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "dot-prop" "^5.2.0"
    "indexes-of" "^1.0.1"
    "uniq" "^1.0.1"

"postcss-selector-parser@^6.0.0", "postcss-selector-parser@^6.0.2":
  "integrity" "sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  "version" "6.0.10"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^4.0.3":
  "integrity" "sha512-NoRbrcMWTtUghzuKSoIm6XV+sJdvZ7GZSc3wdBN0W19FTtp2ko8NqLsgoh/m9CzNhU3KLPvQmjIwtaNFkaFTvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-svgo/-/postcss-svgo-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "postcss" "^7.0.0"
    "postcss-value-parser" "^3.0.0"
    "svgo" "^1.0.0"

"postcss-unique-selectors@^4.0.1":
  "integrity" "sha512-+JanVaryLo9QwZjKrmJgkI4Fn8SBgRO6WXQBJi7KiAVPlmxikB5Jzc4EvXMT2H0/m0RjrVVm9rGNhZddm/8Spg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-unique-selectors/-/postcss-unique-selectors-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "alphanum-sort" "^1.0.0"
    "postcss" "^7.0.0"
    "uniqs" "^2.0.0"

"postcss-value-parser@^3.0.0":
  "integrity" "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  "version" "3.3.1"

"postcss-value-parser@^4.0.2", "postcss-value-parser@^4.1.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^7.0.0", "postcss@^7.0.1", "postcss@^7.0.14", "postcss@^7.0.27", "postcss@^7.0.32", "postcss@^7.0.36", "postcss@^7.0.5", "postcss@^7.0.6":
  "integrity" "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss/-/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"postcss@^8.4.14":
  "integrity" "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss/-/postcss-8.5.3.tgz"
  "version" "8.5.3"
  dependencies:
    "nanoid" "^3.3.8"
    "picocolors" "^1.1.1"
    "source-map-js" "^1.2.1"

"postcss@^8.4.48":
  "integrity" "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/postcss/-/postcss-8.5.3.tgz"
  "version" "8.5.3"
  dependencies:
    "nanoid" "^3.3.8"
    "picocolors" "^1.1.1"
    "source-map-js" "^1.2.1"

"preact@^10.5.13":
  "integrity" "sha512-eY93IVpod/zG3uMF22Unl8h9KkrcKIRs2EGar8hwLZZDU1lkjph303V9HZBwufh2s736U6VXuhD109LYqPoffg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/preact/-/preact-10.11.3.tgz"
  "version" "10.11.3"

"prelude-ls@~1.1.2":
  "integrity" "sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/prelude-ls/-/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^1.0.0":
  "integrity" "sha512-PhmXi5XmoyKw1Un4E+opM2KcsJInDvKyuOumcjjw3waw86ZNjHwVUOOWLc4bCzLdcKNaWBH9e99sbWzDQsVaYg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/prepend-http/-/prepend-http-1.0.4.tgz"
  "version" "1.0.4"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^1.18.2 || ^2.0.0", "prettier@^1.19.1", "prettier@>= 1.13.0", "prettier@>=1.13.0":
  "integrity" "sha512-s7PoyDv/II1ObgQunCbB9PdLmUcBZcnWOcxDh7O0N/UwDEsHyqkW+Qh28jW+mVuCdx7gLB0BotYI1Y6uI9iyew=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/prettier/-/prettier-1.19.1.tgz"
  "version" "1.19.1"

"pretty-error@^2.0.2":
  "integrity" "sha512-EY5oDzmsX5wvuynAByrmY0P0hcp+QpnAKbJng2A2MPjVKXCxrDSUkzghVJ4ZGPIv+JC4gX8fPUWscC0RtjsWGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pretty-error/-/pretty-error-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^2.0.4"

"printj@~1.1.0":
  "integrity" "sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/printj/-/printj-1.1.2.tgz"
  "version" "1.1.2"

"prismjs@^1.23.0":
  "integrity" "sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/prismjs/-/prismjs-1.29.0.tgz"
  "version" "1.29.0"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"process@^0.11.10":
  "integrity" "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/process/-/process-0.11.10.tgz"
  "version" "0.11.10"

"process@~0.5.1":
  "integrity" "sha512-oNpcutj+nYX2FjdEW7PGltWhXulAnFlM0My/k48L90hARCOJtvBbQXc/6itV2jDvU5xAAtonP+r6wmQgCcbAUA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/process/-/process-0.5.2.tgz"
  "version" "0.5.2"

"progress@^2.0.0":
  "integrity" "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/progress/-/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise-inflight@^1.0.1":
  "integrity" "sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/promise-inflight/-/promise-inflight-1.0.1.tgz"
  "version" "1.0.1"

"proxy-addr@~2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"prr@~1.0.1":
  "integrity" "sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/prr/-/prr-1.0.1.tgz"
  "version" "1.0.1"

"pseudomap@^1.0.2":
  "integrity" "sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pseudomap/-/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.28":
  "integrity" "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/psl/-/psl-1.9.0.tgz"
  "version" "1.9.0"

"public-encrypt@^4.0.0":
  "integrity" "sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/public-encrypt/-/public-encrypt-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "bn.js" "^4.1.0"
    "browserify-rsa" "^4.0.0"
    "create-hash" "^1.1.0"
    "parse-asn1" "^5.0.0"
    "randombytes" "^2.0.1"
    "safe-buffer" "^5.1.2"

"pump@^2.0.0":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pump@^3.0.0":
  "integrity" "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pump/-/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"pumpify@^1.3.3":
  "integrity" "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/pumpify/-/pumpify-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "duplexify" "^3.6.0"
    "inherits" "^2.0.3"
    "pump" "^2.0.0"

"punycode@^1.2.4":
  "integrity" "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/punycode/-/punycode-1.4.1.tgz"
  "version" "1.4.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/punycode/-/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/punycode/-/punycode-1.3.2.tgz"
  "version" "1.3.2"

"q@^1.1.2":
  "integrity" "sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@^6.10.1", "qs@6.11.0":
  "integrity" "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/qs/-/qs-6.11.0.tgz"
  "version" "6.11.0"
  dependencies:
    "side-channel" "^1.0.4"

"qs@~6.5.2":
  "integrity" "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/qs/-/qs-6.5.3.tgz"
  "version" "6.5.3"

"query-string@^4.1.0":
  "integrity" "sha512-O2XLNDBIg1DnTOa+2XrIwSiXEV8h2KImXUnjhhn2+UsvZ+Es2uyd5CCRTNQlDGbzUQOW3aYCBx9rVA6dzsiY7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/query-string/-/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring-es3@^0.2.0":
  "integrity" "sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/querystring-es3/-/querystring-es3-0.2.1.tgz"
  "version" "0.2.1"

"querystring@0.2.0":
  "integrity" "sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/querystring/-/querystring-0.2.0.tgz"
  "version" "0.2.0"

"querystringify@^2.1.1":
  "integrity" "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/querystringify/-/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"raf@^3.4.0", "raf@^3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"randombytes@^2.0.0", "randombytes@^2.0.1", "randombytes@^2.0.5", "randombytes@^2.1.0":
  "integrity" "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/randombytes/-/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"randomfill@^1.0.3":
  "integrity" "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/randomfill/-/randomfill-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "randombytes" "^2.0.5"
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.5.1":
  "integrity" "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/raw-body/-/raw-body-2.5.1.tgz"
  "version" "2.5.1"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"raw-loader@~0.5.1":
  "integrity" "sha512-sf7oGoLuaYAScB4VGr0tzetsYlS8EJH6qnTCfQ/WVEa89hALQ4RQfCKt5xCyPQKPDUbVUAIP1QsxAwfAjlDp7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/raw-loader/-/raw-loader-0.5.1.tgz"
  "version" "0.5.1"

"read-pkg@^5.1.1":
  "integrity" "sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/read-pkg/-/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.2", "readable-stream@^2.1.5", "readable-stream@^2.2.2", "readable-stream@^2.3.3", "readable-stream@^2.3.6", "readable-stream@~2.3.6", "readable-stream@1 || 2":
  "integrity" "sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/readable-stream/-/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/readable-stream/-/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@^2.2.1":
  "integrity" "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/readdirp/-/readdirp-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "graceful-fs" "^4.1.11"
    "micromatch" "^3.1.10"
    "readable-stream" "^2.0.2"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerate-unicode-properties@^10.1.0":
  "integrity" "sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regenerate/-/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.11.0":
  "integrity" "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"
  "version" "0.11.1"

"regenerator-runtime@^0.13.10", "regenerator-runtime@^0.13.7":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-transform@^0.15.0":
  "integrity" "sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regenerator-transform/-/regenerator-transform-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regex-not/-/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0", "regexp.prototype.flags@^1.4.3":
  "integrity" "sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "functions-have-names" "^1.2.2"

"regexpp@^2.0.1":
  "integrity" "sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regexpp/-/regexpp-2.0.1.tgz"
  "version" "2.0.1"

"regexpu-core@^5.1.0":
  "integrity" "sha512-T0+1Zp2wjF/juXMrMxHxidqGYn8U4R+zleSJhX9tQ1PUsS8a9UtYfbsF9LdiVgNX3kiX8RNaKM42nfSgvFJjmw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regexpu-core/-/regexpu-core-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^10.1.0"
    "regjsgen" "^0.7.1"
    "regjsparser" "^0.9.1"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.1.0"

"regjsgen@^0.7.1":
  "integrity" "sha512-RAt+8H2ZEzHeYWxZ3H2z6tF18zyyOnlcdaafLrm21Bguj7uZy6ULibiAFdXEtKQY4Sy7wDTwDiOazasMLc4KPA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regjsgen/-/regjsgen-0.7.1.tgz"
  "version" "0.7.1"

"regjsparser@^0.9.1":
  "integrity" "sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/regjsparser/-/regjsparser-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@0.2.x":
  "integrity" "sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/relateurl/-/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"renderkid@^2.0.4":
  "integrity" "sha512-oCcFyxaMrKsKcTY59qnCAtmDVSLfPbrv6A3tVbPdFMMrv5jaK10V6m40cKsoPNhAqN6rmHW9sswW4o3ruSrwUQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/renderkid/-/renderkid-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^3.0.1"

"repeat-element@^1.1.2":
  "integrity" "sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/repeat-element/-/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/repeat-string/-/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"request@^2.88.2":
  "integrity" "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/request/-/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-cwd@^2.0.0":
  "integrity" "sha512-ccu8zQTrzVr954472aUVPLEcB3YpKSYR3cg/3lo1okzobPBM+1INXBbBZlDbnI/hbEocnf8j0QVo43hQKrbchg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/resolve-cwd/-/resolve-cwd-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "resolve-from" "^3.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/resolve-from/-/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/resolve-url/-/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.12.0", "resolve@^1.14.2":
  "integrity" "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/resolve/-/resolve-1.22.1.tgz"
  "version" "1.22.1"
  dependencies:
    "is-core-module" "^2.9.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/restore-cursor/-/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/restore-cursor/-/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ret/-/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/retry/-/retry-0.12.0.tgz"
  "version" "0.12.0"

"rgb-regex@^1.0.1":
  "integrity" "sha512-gDK5mkALDFER2YLqH6imYvK6g02gpNGM4ILDZ472EwWfXZnC2ZEpoB2ECXTyOVUKuk/bPJZMzwQPBYICzP+D3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rgb-regex/-/rgb-regex-1.0.1.tgz"
  "version" "1.0.1"

"rgba-regex@^1.0.0":
  "integrity" "sha512-zgn5OjNQXLUTdq8m17KdaicF6w89TZs8ZU8y0AYENIU6wG8GG6LLm0yLSiPY8DmaYmHdgRW8rnApjoT0fQRfMg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rgba-regex/-/rgba-regex-1.0.0.tgz"
  "version" "1.0.0"

"rgbcolor@^1.0.1":
  "integrity" "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rgbcolor/-/rgbcolor-1.0.1.tgz"
  "version" "1.0.1"

"rimraf@^2.5.4", "rimraf@^2.6.1", "rimraf@^2.6.3":
  "integrity" "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rimraf/-/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@2.6.3":
  "integrity" "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rimraf/-/rimraf-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "glob" "^7.1.3"

"ripemd160@^2.0.0", "ripemd160@^2.0.1":
  "integrity" "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ripemd160/-/ripemd160-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "hash-base" "^3.0.0"
    "inherits" "^2.0.1"

"run-async@^2.4.0":
  "integrity" "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/run-async/-/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-queue@^1.0.0", "run-queue@^1.0.3":
  "integrity" "sha512-ntymy489o0/QQplUDnpYAYUsO50K9SBrIVaKCWDOJzYJts0f9WH9RFJkyagebkw5+y1oi00R7ynNW/d12GBumg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/run-queue/-/run-queue-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "aproba" "^1.1.1"

"rust-result@^1.0.0":
  "integrity" "sha512-6cJzSBU+J/RJCF063onnQf0cDUOHs9uZI1oroSGnHOph+CQTIJ5Pp2hK5kEQq1+7yE/EEWfulSNXAQ2jikPthA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rust-result/-/rust-result-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "individual" "^2.0.0"

"rxjs@^6.6.0":
  "integrity" "sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/rxjs/-/rxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@^5.2.0":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-json-parse@4.0.0":
  "integrity" "sha512-RjZPPHugjK0TOzFrLZ8inw44s9bKox99/0AZW9o/BEQVrJfhI+fIHMErnPyRa89/yRXUUr93q+tiN6zhoVV4wQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safe-json-parse/-/safe-json-parse-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "rust-result" "^1.0.0"

"safe-regex-test@^1.0.0":
  "integrity" "sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safe-regex-test/-/safe-regex-test-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.3"
    "is-regex" "^1.1.4"

"safe-regex@^1.1.0":
  "integrity" "sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safe-regex/-/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sax@~1.2.4":
  "integrity" "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sax/-/sax-1.2.4.tgz"
  "version" "1.2.4"

"schema-utils@^1.0.0":
  "integrity" "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/schema-utils/-/schema-utils-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "ajv" "^6.1.0"
    "ajv-errors" "^1.0.0"
    "ajv-keywords" "^3.1.0"

"schema-utils@^2.0.0", "schema-utils@^2.5.0", "schema-utils@^2.6.5", "schema-utils@^2.7.0":
  "integrity" "sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/schema-utils/-/schema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"script-loader@0.7.2":
  "integrity" "sha512-UMNLEvgOAQuzK8ji8qIscM3GIrRCWN6MmMXGD4SD5l6cSycgGsCo0tX5xRnfQcoghqct0tjHjcykgI1PyBE2aA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/script-loader/-/script-loader-0.7.2.tgz"
  "version" "0.7.2"
  dependencies:
    "raw-loader" "~0.5.1"

"scroll-into-view-if-needed@^2.2.28":
  "integrity" "sha512-hxpAR6AN+Gh53AdAimHM6C8oTN1ppwVZITihix+WqalywBeFcQ6LdQP5ABNl26nX8GTEL7VT+b8lKpdqq65wXg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.29.tgz"
  "version" "2.2.29"
  dependencies:
    "compute-scroll-into-view" "^1.0.17"

"select-hose@^2.0.0":
  "integrity" "sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/select-hose/-/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@^1.10.8":
  "integrity" "sha512-lkjaiAye+wBZDCBsu5BGi0XiLRxeUlsGod5ZP924CRSEoGuZAw/f7y9RKu28rwTfiHVhdavhB0qH0INV6P1lEA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/selfsigned/-/selfsigned-1.10.14.tgz"
  "version" "1.10.14"
  dependencies:
    "node-forge" "^0.10.0"

"semver@^5.5.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.1.0", "semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0":
  "integrity" "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/semver/-/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/semver/-/semver-5.7.1.tgz"
  "version" "5.7.1"

"send@0.18.0":
  "integrity" "sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/send/-/send-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serialize-javascript@^4.0.0":
  "integrity" "sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/serialize-javascript/-/serialize-javascript-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/serve-index/-/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.15.0":
  "integrity" "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/serve-static/-/serve-static-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.18.0"

"set-blocking@^2.0.0":
  "integrity" "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/set-value/-/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.4":
  "integrity" "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.1.0":
  "integrity" "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/setprototypeof/-/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"sha.js@^2.4.0", "sha.js@^2.4.8":
  "integrity" "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sha.js/-/sha.js-2.4.11.tgz"
  "version" "2.4.11"
  dependencies:
    "inherits" "^2.0.1"
    "safe-buffer" "^5.0.1"

"shallow-equal@^1.0.0":
  "integrity" "sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shallow-equal/-/shallow-equal-1.2.1.tgz"
  "version" "1.2.1"

"shallowequal@^1.0.2":
  "integrity" "sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shallowequal/-/shallowequal-1.1.0.tgz"
  "version" "1.1.0"

"shebang-command@^1.2.0":
  "integrity" "sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shebang-command/-/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shebang-regex/-/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.7.3":
  "integrity" "sha512-8o/QEhSSRb1a5i7TFR0iM4G16Z0vYB2OQVs4G3aAFXjn3T6yEx8AZxy1PgDF7I00LZHYA3WxaSYIf5e5sAX8Rw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/shell-quote/-/shell-quote-1.7.4.tgz"
  "version" "1.7.4"

"side-channel@^1.0.4":
  "integrity" "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/side-channel/-/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2":
  "integrity" "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/signal-exit/-/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"simple-swizzle@^0.2.2":
  "integrity" "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"slash@^1.0.0":
  "integrity" "sha512-3TYDR7xWt4dIqV2JauJr+EJeW356RXijHeUlO+8djJ+uBXPn8/2dpzBc8yQhh583sVvc9CvFAeQVgijsH+PNNg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/slash/-/slash-1.0.0.tgz"
  "version" "1.0.0"

"slash@^2.0.0":
  "integrity" "sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/slash/-/slash-2.0.0.tgz"
  "version" "2.0.0"

"slate-history@^0.66.0":
  "integrity" "sha512-6MWpxGQZiMvSINlCbMW43E2YBSVMCMCIwQfBzGssjWw4kb0qfvj0pIdblWNRQZD0hR6WHP+dHHgGSeVdMWzfng=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/slate-history/-/slate-history-0.66.0.tgz"
  "version" "0.66.0"
  dependencies:
    "is-plain-object" "^5.0.0"

"slate@^0.72.0", "slate@>=0.65.3":
  "integrity" "sha512-/nJwTswQgnRurpK+bGJFH1oM7naD5qDmHd89JyiKNT2oOKD8marW0QSBtuFnwEbL5aGCS8AmrhXQgNOsn4osAw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/slate/-/slate-0.72.8.tgz"
  "version" "0.72.8"
  dependencies:
    "immer" "^9.0.6"
    "is-plain-object" "^5.0.0"
    "tiny-warning" "^1.0.3"

"slice-ansi@^2.1.0":
  "integrity" "sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/slice-ansi/-/slice-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "astral-regex" "^1.0.0"
    "is-fullwidth-code-point" "^2.0.0"

"snabbdom@^3.1.0":
  "integrity" "sha512-wHMNIOjkm/YNE5EM3RCbr/+DVgPg6AqQAX1eOxO46zYNvCXjKP5Y865tqQj3EXnaMBjkxmQA5jFuDpDK/dbfiA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/snabbdom/-/snabbdom-3.5.1.tgz"
  "version" "3.5.1"

"snapdragon-node@^2.0.1":
  "integrity" "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/snapdragon-node/-/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/snapdragon-util/-/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/snapdragon/-/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sockjs-client@^1.5.0":
  "integrity" "sha512-2g0tjOR+fRs0amxENLi/q5TiJTqY+WXFOzb5UwXndlK6TO3U/mirZznpx6w34HVMoc3g7cY24yC/ZMIYnDlfkw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sockjs-client/-/sockjs-client-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "debug" "^3.2.7"
    "eventsource" "^2.0.2"
    "faye-websocket" "^0.11.4"
    "inherits" "^2.0.4"
    "url-parse" "^1.5.10"

"sockjs@^0.3.21":
  "integrity" "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sockjs/-/sockjs-0.3.24.tgz"
  "version" "0.3.24"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^8.3.2"
    "websocket-driver" "^0.7.4"

"sort-keys@^1.0.0":
  "integrity" "sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sort-keys/-/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-list-map@^2.0.0":
  "integrity" "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-list-map/-/source-list-map-2.0.1.tgz"
  "version" "2.0.1"

"source-map-js@^1.2.0", "source-map-js@^1.2.1":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"source-map-resolve@^0.5.0":
  "integrity" "sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map-resolve/-/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@~0.5.12":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map-support/-/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map-url/-/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.6":
  "integrity" "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map/-/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0", "source-map@^0.6.1", "source-map@~0.6.0", "source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/source-map/-/source-map-0.7.4.tgz"
  "version" "0.7.4"

"spdx-correct@^3.0.0":
  "integrity" "sha512-cOYcUWwhCuHCXi49RhFRCyJEK3iPj1Ziz9DpViV3tbZOwXD49QzIN3MpOLJNxh2qwq2lJJZaKMVw9qNi4jTC0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/spdx-correct/-/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-rr+VVSXtRhO4OHbXUiAF7xW3Bo9DuuF6C5jH+q/x15j2jniycgKbxU09Hr0WqlSLUs4i4ltHGXqTe7VHclYWyA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/spdx-license-ids/-/spdx-license-ids-3.0.12.tgz"
  "version" "3.0.12"

"spdy-transport@^3.0.0":
  "integrity" "sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/spdy-transport/-/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/spdy/-/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/split-string/-/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sprintf-js/-/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"ssf@~0.11.2":
  "integrity" "sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ssf/-/ssf-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "frac" "~1.1.2"

"sshpk@^1.7.0":
  "integrity" "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/sshpk/-/sshpk-1.17.0.tgz"
  "version" "1.17.0"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"ssr-window@^3.0.0-alpha.1":
  "integrity" "sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ssr-window/-/ssr-window-3.0.0.tgz"
  "version" "3.0.0"

"ssri@^6.0.1":
  "integrity" "sha512-cepbSq/neFK7xB6A50KHN0xHDotYzq58wWCa5LeWqnPrHG8GzfEjO/4O8kpmcGW+oaxkvhEJCWgbgNk4/ZV93Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ssri/-/ssri-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "figgy-pudding" "^3.5.1"

"ssri@^8.0.1":
  "integrity" "sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ssri/-/ssri-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stable/-/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackblur-canvas@^2.0.0":
  "integrity" "sha512-EeNzTVfj+1In7aSLPKDD03F/ly4RxEuF/EX0YcOG0cKoPXs+SLZxDawQbexQDBzwROs4VKLWTOaZQlZkGBFEIQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stackblur-canvas/-/stackblur-canvas-2.5.0.tgz"
  "version" "2.5.0"

"stackframe@^1.3.4":
  "integrity" "sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stackframe/-/stackframe-1.3.4.tgz"
  "version" "1.3.4"

"static-extend@^0.1.1":
  "integrity" "sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/static-extend/-/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2":
  "integrity" "sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/statuses/-/statuses-1.5.0.tgz"
  "version" "1.5.0"

"statuses@2.0.1":
  "integrity" "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"stream-browserify@^2.0.1":
  "integrity" "sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stream-browserify/-/stream-browserify-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "inherits" "~2.0.1"
    "readable-stream" "^2.0.2"

"stream-each@^1.1.0":
  "integrity" "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stream-each/-/stream-each-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "end-of-stream" "^1.1.0"
    "stream-shift" "^1.0.0"

"stream-http@^2.7.2":
  "integrity" "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stream-http/-/stream-http-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "builtin-status-codes" "^3.0.0"
    "inherits" "^2.0.1"
    "readable-stream" "^2.3.6"
    "to-arraybuffer" "^1.0.0"
    "xtend" "^4.0.0"

"stream-shift@^1.0.0":
  "integrity" "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stream-shift/-/stream-shift-1.0.1.tgz"
  "version" "1.0.1"

"strict-uri-encode@^1.0.0":
  "integrity" "sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strict-uri-encode/-/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.0.0", "string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-convert@^0.2.0":
  "integrity" "sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/string-convert/-/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"string-width@^3.0.0", "string-width@^3.1.0":
  "integrity" "sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/string-width/-/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string.prototype.trimend@^1.0.5":
  "integrity" "sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"string.prototype.trimstart@^1.0.5":
  "integrity" "sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/string.prototype.trimstart/-/string.prototype.trimstart-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"strip-ansi@^3.0.1":
  "integrity" "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-ansi/-/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^5.0.0":
  "integrity" "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.1.0":
  "integrity" "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^5.2.0":
  "integrity" "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-ansi/-/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-eof@^1.0.0":
  "integrity" "sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-eof/-/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-indent/-/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.0.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"stylehacks@^4.0.0":
  "integrity" "sha512-7GlLk9JwlElY4Y6a/rmbH2MhVlTyVmiJd1PfTCqFaIBEGMYNsrO/v3SeGTdhBThLg4Z+NbOk/qFMwCa+J+3p/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/stylehacks/-/stylehacks-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "browserslist" "^4.0.0"
    "postcss" "^7.0.0"
    "postcss-selector-parser" "^3.0.0"

"supports-color@^5.3.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/supports-color/-/supports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-pathdata@^6.0.3":
  "integrity" "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  "version" "6.0.3"

"svg-tags@^1.0.0":
  "integrity" "sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/svg-tags/-/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^1.0.0":
  "integrity" "sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/svgo/-/svgo-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "chalk" "^2.4.1"
    "coa" "^2.0.2"
    "css-select" "^2.0.0"
    "css-select-base-adapter" "^0.1.1"
    "css-tree" "1.0.0-alpha.37"
    "csso" "^4.0.2"
    "js-yaml" "^3.13.1"
    "mkdirp" "~0.5.1"
    "object.values" "^1.1.0"
    "sax" "~1.2.4"
    "stable" "^0.1.8"
    "unquote" "~1.1.1"
    "util.promisify" "~1.0.0"

"table@^5.2.3":
  "integrity" "sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/table/-/table-5.4.6.tgz"
  "version" "5.4.6"
  dependencies:
    "ajv" "^6.10.2"
    "lodash" "^4.17.14"
    "slice-ansi" "^2.1.0"
    "string-width" "^3.0.0"

"tapable@^1.0.0", "tapable@^1.1.3":
  "integrity" "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tapable/-/tapable-1.1.3.tgz"
  "version" "1.1.3"

"terser-webpack-plugin@^1.4.3", "terser-webpack-plugin@^1.4.4":
  "integrity" "sha512-04Rfe496lN8EYruwi6oPQkG0vo8C+HT49X687FZnpPF0qMAIHONI6HEXYPKDOE8e5HjXTyKfqRd/agHtH0kOtw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz"
  "version" "1.4.5"
  dependencies:
    "cacache" "^12.0.2"
    "find-cache-dir" "^2.1.0"
    "is-wsl" "^1.1.0"
    "schema-utils" "^1.0.0"
    "serialize-javascript" "^4.0.0"
    "source-map" "^0.6.1"
    "terser" "^4.1.2"
    "webpack-sources" "^1.4.0"
    "worker-farm" "^1.7.0"

"terser@^4.1.2":
  "integrity" "sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/terser/-/terser-4.8.1.tgz"
  "version" "4.8.1"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.6.1"
    "source-map-support" "~0.5.12"

"text-segmentation@^1.0.3":
  "integrity" "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/text-segmentation/-/text-segmentation-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "utrie" "^1.0.2"

"text-table@^0.2.0":
  "integrity" "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"thenify-all@^1.0.0":
  "integrity" "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/thenify-all/-/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/thenify/-/thenify-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^2.1.3":
  "integrity" "sha512-wNrVKH2Lcf8ZrWxDF/khdlLlsTMczdcwPA9VEK4c2exlEPynYWxi9op3nPTo5lAnDIkE0rQEB3VBP+4Zncc9Hg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/thread-loader/-/thread-loader-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "loader-runner" "^2.3.1"
    "loader-utils" "^1.1.0"
    "neo-async" "^2.6.0"

"through@^2.3.6":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0":
  "integrity" "sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/through2/-/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"thunky@^1.0.2":
  "integrity" "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/thunky/-/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timers-browserify@^2.0.4":
  "integrity" "sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/timers-browserify/-/timers-browserify-2.0.12.tgz"
  "version" "2.0.12"
  dependencies:
    "setimmediate" "^1.0.4"

"timsort@^0.3.0":
  "integrity" "sha512-qsdtZH+vMoCARQtyod4imc2nIJwg9Cc7lPRrw9CzF8ZKR0khdr8+2nX80PBhET3tcyTtJDxAffGh2rXH4tyU8A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/timsort/-/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tiny-warning@^1.0.3":
  "integrity" "sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tiny-warning/-/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tinycolor2@^1.4.1":
  "integrity" "sha512-vJhccZPs965sV/L2sU4oRQVAos0pQXwsvTLkWYdqJ+a8Q5kPFzJTuOFwy7UniPli44NKQGAglksjvOcpo95aZA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tinycolor2/-/tinycolor2-1.4.2.tgz"
  "version" "1.4.2"

"tmp@^0.0.33":
  "integrity" "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"to-arraybuffer@^1.0.0":
  "integrity" "sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz"
  "version" "1.0.1"

"to-object-path@^0.3.0":
  "integrity" "sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/to-object-path/-/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/to-regex-range/-/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/to-regex/-/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"toposort@^1.0.0":
  "integrity" "sha512-FclLrw8b9bMWf4QlCJuHBEVhSRsqDj6u3nIjAzPeJvgl//1hBlffdlk0MALceL14+koWEdU4ofRAXofbODxQzg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/toposort/-/toposort-1.0.7.tgz"
  "version" "1.0.7"

"tough-cookie@~2.5.0":
  "integrity" "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tough-cookie/-/tough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tryer@^1.0.1":
  "integrity" "sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tryer/-/tryer-1.0.1.tgz"
  "version" "1.0.1"

"ts-pnp@^1.1.6":
  "integrity" "sha512-csd+vJOb/gkzvcCHgTGSChYpy5f1/XKNsmvBGO4JXS+z1v2HobugDz4s1IeFXM3wZB44uczs+eazB5Q/ccdhQw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ts-pnp/-/ts-pnp-1.2.0.tgz"
  "version" "1.2.0"

"tslib@^1.10.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^1.9.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@2.3.0":
  "integrity" "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tslib/-/tslib-2.3.0.tgz"
  "version" "2.3.0"

"tsml@1.0.1":
  "integrity" "sha512-3KmepnH9SUsoOVtg013CRrL7c+AK7ECaquAsJdvu4288EDJuraqBlP4PDXT/rLEJ9YDn4jqLAzRJsnFPx+V6lg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tsml/-/tsml-1.0.1.tgz"
  "version" "1.0.1"

"tty-browserify@0.0.0":
  "integrity" "sha512-JVa5ijo+j/sOoHGjw0sxw734b1LhBkQ3bvUGNdxnVXDCX81Yx7TFgnZygxrIIWn23hbfTaMYLwRmAxFyDuFmIw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tty-browserify/-/tty-browserify-0.0.0.tgz"
  "version" "0.0.0"

"tunnel-agent@^0.6.0":
  "integrity" "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-check@~0.3.2":
  "integrity" "sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type-check/-/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type-fest/-/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type-fest/-/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type-is@~1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"type@^1.0.1":
  "integrity" "sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type/-/type-1.2.0.tgz"
  "version" "1.2.0"

"type@^2.7.2":
  "integrity" "sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/type/-/type-2.7.2.tgz"
  "version" "2.7.2"

"typedarray@^0.0.6":
  "integrity" "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uglify-js@3.4.x":
  "integrity" "sha512-Y2VsbPVs0FIshJztycsO2SfPk7/KAF/T72qzv9u5EpQ4kB2hQoHlhNQTsNyy6ul7lQtqJN/AoWeS23OzEiEFxw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/uglify-js/-/uglify-js-3.4.10.tgz"
  "version" "3.4.10"
  dependencies:
    "commander" "~2.19.0"
    "source-map" "~0.6.1"

"unbox-primitive@^1.0.2":
  "integrity" "sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-bigints" "^1.0.2"
    "has-symbols" "^1.0.3"
    "which-boxed-primitive" "^1.0.2"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.1.0":
  "integrity" "sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"union-value@^1.0.0":
  "integrity" "sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/union-value/-/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"uniq@^1.0.1":
  "integrity" "sha512-Gw+zz50YNKPDKXs+9d+aKAjVwpjNwqzvNpLigIruT4HA9lMZNdMqs9x07kKHB/L9WRzqp4+DlTU5s4wG2esdoA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/uniq/-/uniq-1.0.1.tgz"
  "version" "1.0.1"

"uniqs@^2.0.0":
  "integrity" "sha512-mZdDpf3vBV5Efh29kMw5tXoup/buMgxLzOt/XKFKcVmi+15ManNQWr6HfZ2aiZTYlYixbdNJ0KFmIZIv52tHSQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/uniqs/-/uniqs-2.0.0.tgz"
  "version" "2.0.0"

"unique-filename@^1.1.1":
  "integrity" "sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unique-filename/-/unique-filename-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "unique-slug" "^2.0.0"

"unique-slug@^2.0.0":
  "integrity" "sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unique-slug/-/unique-slug-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "imurmurhash" "^0.1.4"

"universalify@^0.1.0":
  "integrity" "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/universalify/-/universalify-0.1.2.tgz"
  "version" "0.1.2"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unquote@~1.1.1":
  "integrity" "sha512-vRCqFv6UhXpWxZPyGDh/F3ZpNv8/qo7w6iufLpQg9aKnQ71qM4B5KiI7Mia9COcjEhrO9LueHpMYjYzsWH3OIg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unquote/-/unquote-1.1.1.tgz"
  "version" "1.1.1"

"unset-value@^1.0.0":
  "integrity" "sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/unset-value/-/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"upath@^1.1.1":
  "integrity" "sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/upath/-/upath-1.2.0.tgz"
  "version" "1.2.0"

"update-browserslist-db@^1.0.9":
  "integrity" "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"upper-case@^1.1.1":
  "integrity" "sha512-WRbjgmYzgXkCV7zNVpy5YgrHgbBv126rMALQQMrmzOVC4GM2waQ9x7xtm8VU+1yF2kWyPzI9zbZ48n4vSxwfSA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/upper-case/-/upper-case-1.1.3.tgz"
  "version" "1.1.3"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/urix/-/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-loader@^2.2.0":
  "integrity" "sha512-goSdg8VY+7nPZKUEChZSEtW5gjbS66USIGCeSJ1OVOJ7Yfuh/36YxCwMi5HVEJh6mqUYOoy3NJ0vlOMrWsSHog=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/url-loader/-/url-loader-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "loader-utils" "^1.2.3"
    "mime" "^2.4.4"
    "schema-utils" "^2.5.0"

"url-parse@^1.5.10":
  "integrity" "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/url-parse/-/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url-toolkit@^2.1.3":
  "integrity" "sha512-mtN6xk+Nac+oyJ/PrI7tzfmomRVNFIWKUbG8jdYFt52hxbiReFAXIjYskvu64/dvuW71IcB7lV8l0HvZMac6Jg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/url-toolkit/-/url-toolkit-2.2.5.tgz"
  "version" "2.2.5"

"url@^0.11.0":
  "integrity" "sha512-kbailJa29QrtXnxgq+DdCEGlbTeYM2eJUxsz6vjZavrCYPMIFHMKQmSKYAIuUK2i7hgPm28a8piX5NTUtM/LKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/url/-/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/use/-/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"util.promisify@~1.0.0", "util.promisify@1.0.0":
  "integrity" "sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/util.promisify/-/util.promisify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "define-properties" "^1.1.2"
    "object.getownpropertydescriptors" "^2.0.3"

"util@^0.11.0":
  "integrity" "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/util/-/util-0.11.1.tgz"
  "version" "0.11.1"
  dependencies:
    "inherits" "2.0.3"

"util@0.10.3":
  "integrity" "sha512-5KiHfsmkqacuKjkRkdV7SsfDJ2EGiPsK92s2MhNSY0craxjTdKTtqKsJaCWp4LW33ZZ0OPUv1WO/TFvNQRiQxQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/util/-/util-0.10.3.tgz"
  "version" "0.10.3"
  dependencies:
    "inherits" "2.0.1"

"utila@~0.4":
  "integrity" "sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/utila/-/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"utrie@^1.0.2":
  "integrity" "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/utrie/-/utrie-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "base64-arraybuffer" "^1.0.2"

"uuid@^3.3.2":
  "integrity" "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.3.2":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@^2.0.3":
  "integrity" "sha512-l8lCEmLcLYZh4nbunNZvQCJc5pv7+RCwa8q/LdUx8u7lsWvPDKmpodJAJNwkAhJC//dFY48KuIEmjtd4RViDrA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"vendors@^1.0.0":
  "integrity" "sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vendors/-/vendors-1.0.4.tgz"
  "version" "1.0.4"

"verror@1.10.0":
  "integrity" "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"video.js@^5.17.0 || ^6.2.0", "video.js@^5.19.1 || ^6.2.0", "video.js@^6 || ^7", "video.js@^6.6.0":
  "integrity" "sha512-36/JR/GhPQSZj0o+GNbhcEYv/b0SkV9SQsjlodAnzMQYN0TA7VhmqrKPYMCi1NGRYu7S9W3OaFCFoUxkYfSVlg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/video.js/-/video.js-6.13.0.tgz"
  "version" "6.13.0"
  dependencies:
    "babel-runtime" "^6.9.2"
    "global" "4.3.2"
    "safe-json-parse" "4.0.0"
    "tsml" "1.0.1"
    "videojs-font" "2.1.0"
    "videojs-ie8" "1.1.2"
    "videojs-vtt.js" "0.12.6"
    "xhr" "2.4.0"

"videojs-contrib-hls@^5.12.2":
  "integrity" "sha512-18zbMYZ0XRBKTPEayA9bFTWWrqhT9b4G8+zf0czJLD7Epe5PcK1I/3dflTHQeQ5rwlWir+/XnFU3sMg/B2MMcw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-contrib-hls/-/videojs-contrib-hls-5.15.0.tgz"
  "version" "5.15.0"
  dependencies:
    "aes-decrypter" "1.0.3"
    "global" "^4.3.0"
    "m3u8-parser" "2.1.0"
    "mux.js" "4.3.2"
    "url-toolkit" "^2.1.3"
    "video.js" "^5.19.1 || ^6.2.0"
    "videojs-contrib-media-sources" "4.7.2"
    "webwackify" "0.1.6"

"videojs-contrib-media-sources@4.7.2":
  "integrity" "sha512-e6iCHWBFuV05EGo7v+pS9iepObXnJ9joms467gzi8ZjpKVb3ifha9M0Ja24Rd8JfvYpzjltsgDVtGFDvIg4hQQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-contrib-media-sources/-/videojs-contrib-media-sources-4.7.2.tgz"
  "version" "4.7.2"
  dependencies:
    "global" "^4.3.0"
    "mux.js" "4.3.2"
    "video.js" "^5.17.0 || ^6.2.0"
    "webwackify" "0.1.6"

"videojs-flash@^2.1.0":
  "integrity" "sha512-mHu6TD12EKkxMvr8tg4AcfV/DuVLff427nneoZom3N9Dd2bv0sJOWwdLPQH1v5BCuAuXAVuAOba56ovTl+G3tQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-flash/-/videojs-flash-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "global" "^4.4.0"
    "video.js" "^6 || ^7"
    "videojs-swf" "5.4.2"

"videojs-font@2.1.0":
  "integrity" "sha512-zFqWpLrXf1q8NtYx5qtZhMC6SLUFScDmR6j+UGPogobxR21lvXShhnzcNNMdOxJUuFLiToJ/BPpFUQwX4xhpvA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-font/-/videojs-font-2.1.0.tgz"
  "version" "2.1.0"

"videojs-hotkeys@^0.2.20":
  "integrity" "sha512-M8rlD5OSB3EDRdbS4MRNlGKFpA2sSIStmUPvy5zfl/NigzWaN6r4wnb32rEN0v97GiQwmUfXSmqrPNrXhiFQmQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-hotkeys/-/videojs-hotkeys-0.2.28.tgz"
  "version" "0.2.28"

"videojs-ie8@1.1.2":
  "integrity" "sha512-0Zb2T4MLkpfZbeGMK/Z93b8Lrepr+rLFoHgQV1CoDeFqXvH7b+Vsd/VHoILGxQrgCSHFQ7mAODR6oyMjuiD4/g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-ie8/-/videojs-ie8-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "es5-shim" "^4.5.1"

"videojs-swf@5.4.2":
  "integrity" "sha512-FGg+Csioa8/A/EacvFefBdb9Z0rSiMlheHDunZnN3xXfUF43jvjawcWFQnZvrv1Cs1nE1LBrHyUZjF7j2mKOLw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-swf/-/videojs-swf-5.4.2.tgz"
  "version" "5.4.2"

"videojs-vtt.js@0.12.6":
  "integrity" "sha512-XFXeGBQiljnElMhwCcZst0RDbZn2n8LU7ZScXryd3a00OaZsHAjdZu/7/RdSr7Z1jHphd45FnOvOQkGK4YrWCQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/videojs-vtt.js/-/videojs-vtt.js-0.12.6.tgz"
  "version" "0.12.6"
  dependencies:
    "global" "^4.3.1"

"vm-browserify@^1.0.1":
  "integrity" "sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vm-browserify/-/vm-browserify-1.1.2.tgz"
  "version" "1.1.2"

"vue-eslint-parser@^7.0.0":
  "integrity" "sha512-qh3VhDLeh773wjgNTl7ss0VejY9bMMa0GoDG2fQVyDzRFdiU3L7fw74tWZDHNQXdZqxO3EveQroa9ct39D2nqg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-eslint-parser/-/vue-eslint-parser-7.11.0.tgz"
  "version" "7.11.0"
  dependencies:
    "debug" "^4.1.1"
    "eslint-scope" "^5.1.1"
    "eslint-visitor-keys" "^1.1.0"
    "espree" "^6.2.1"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^6.3.0"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-i18n@^8.14.0":
  "integrity" "sha512-C5GZjs1tYlAqjwymaaCPDjCyGo10ajUphiwA922jKt9n7KPpqR7oM1PCwYzhB/E7+nT3wfdG3oRre5raIT1rKA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-i18n/-/vue-i18n-8.28.2.tgz"
  "version" "8.28.2"

"vue-loader-v16@npm:vue-loader@^16.1.0":
  "integrity" "sha512-7vKN45IxsKxe5GcVCbc2qFU5aWzyiLrYJyUuMz4BQLKctCj/fmCa0w6fGiiQ2cLFetNcek1ppGJQDCup0c1hpA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-loader/-/vue-loader-16.8.3.tgz"
  "version" "16.8.3"
  dependencies:
    "chalk" "^4.1.0"
    "hash-sum" "^2.0.0"
    "loader-utils" "^2.0.0"

"vue-loader@^15.9.2":
  "integrity" "sha512-SaPHK1A01VrNthlix6h1hq4uJu7S/z0kdLUb6klubo738NeQoLbS6V9/d8Pv19tU0XdQKju3D1HSKuI8wJ5wMA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-loader/-/vue-loader-15.10.1.tgz"
  "version" "15.10.1"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"vue-ls@^3.2.1":
  "integrity" "sha512-xros9Zheckv+8x9PerHvWe5SMYud0+ZlPAMrKWKNtDN/usMOKRoluj6kBZyQo6BxwpmiBL8/EjKMYjxmCNXOMg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-ls/-/vue-ls-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "opencollective-postinstall" "^2.0.2"

"vue-ref@^2.0.0":
  "integrity" "sha512-uKNKpFOVeWNqS2mrBZqnpLyXJo5Q+vnkex6JvpENvhXHFNBW/SJTP8vJywLuVT3DpxwXcF9N0dyIiZ4/NpTexQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-ref/-/vue-ref-2.0.0.tgz"
  "version" "2.0.0"

"vue-router@^3.4.3":
  "integrity" "sha512-VYXZQLtjuvKxxcshuRAwjHnciqZVoXAjTjcqBTz4rKc8qih9g9pI3hbDjmqXaHdgL3v8pV6P8Z335XvHzESxLQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-router/-/vue-router-3.6.5.tgz"
  "version" "3.6.5"

"vue-style-loader@^4.1.0", "vue-style-loader@^4.1.2":
  "integrity" "sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-style-loader/-/vue-style-loader-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-compiler@^2.0.0", "vue-template-compiler@^2.6.0", "vue-template-compiler@^2.6.11", "vue-template-compiler@>=2.5.0", "vue-template-compiler@>=2.6.0":
  "integrity" "sha512-zyA5Y3ArvVG0NacJDkkzJuPQDF8RFeRlzV2vLeSnhSpieO6LK2OVbdLPi5MPPs09Ii+gMO8nY4S3iKQxBxDmWQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-template-compiler/-/vue-template-compiler-2.7.14.tgz"
  "version" "2.7.14"
  dependencies:
    "de-indent" "^1.0.2"
    "he" "^1.2.0"

"vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue-video-player@^5.0.2":
  "integrity" "sha512-IZXeRGGSX4YIp54G0Q5cB7iqh6Ok6Dpa2jRkjdyvMWw7MShJuh54/d5QNb1CZ+CvZUzX/TH7osnpir7mBNcFvQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue-video-player/-/vue-video-player-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "object-assign" "^4.1.1"
    "video.js" "^6.6.0"
    "videojs-contrib-hls" "^5.12.2"
    "videojs-flash" "^2.1.0"
    "videojs-hotkeys" "^0.2.20"

"vue@*", "vue@^2 || ^3.0.0-0", "vue@^2.0.0", "vue@^2.5.4", "vue@^2.6.0", "vue@^2.6.11", "vue@^2.6.14", "vue@>=2.5.0", "vue@>=2.6.0":
  "integrity" "sha512-b2qkFyOM0kwqWFuQmgd4o+uHGU7T+2z3T+WQp8UBjADfEv2n4FEMffzBmCKNP0IGzOEEfYjvtcC62xaSKeQDrQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue/-/vue-2.7.14.tgz"
  "version" "2.7.14"
  dependencies:
    "@vue/compiler-sfc" "2.7.14"
    "csstype" "^3.1.0"

"vue2-org-tree@^1.3.6":
  "integrity" "sha512-ExkQWmHcS3s1tjPDdaruE2p4JnfwOTc4MBqqMY7rsSM9pgzD53t6hbjyNOq9csnyugzREdCnX5V/ZHQMREDYZQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vue2-org-tree/-/vue2-org-tree-1.3.6.tgz"
  "version" "1.3.6"

"vuex@^3.6.0":
  "integrity" "sha512-ETW44IqCgBpVomy520DT5jf8n0zoCac+sxWnn+hMe/CzaSejb/eVw2YToiXYX+Ex/AuHHia28vWTq4goAexFbw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/vuex/-/vuex-3.6.2.tgz"
  "version" "3.6.2"

"warning@^4.0.0":
  "integrity" "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/warning/-/warning-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "loose-envify" "^1.0.0"

"watchpack-chokidar2@^2.0.1":
  "integrity" "sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "chokidar" "^2.1.8"

"watchpack@^1.7.4":
  "integrity" "sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/watchpack/-/watchpack-1.7.5.tgz"
  "version" "1.7.5"
  dependencies:
    "graceful-fs" "^4.1.2"
    "neo-async" "^2.5.0"
  optionalDependencies:
    "chokidar" "^3.4.1"
    "watchpack-chokidar2" "^2.0.1"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wbuf/-/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wcwidth/-/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webpack-bundle-analyzer@^3.8.0":
  "integrity" "sha512-Ob8amZfCm3rMB1ScjQVlbYYUEJyEjdEtQ92jqiFUYt5VkEeO2v5UMbv49P/gnmCZm3A6yaFQzCBvpZqN4MUsdA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-bundle-analyzer/-/webpack-bundle-analyzer-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"
    "bfj" "^6.1.1"
    "chalk" "^2.4.1"
    "commander" "^2.18.0"
    "ejs" "^2.6.1"
    "express" "^4.16.3"
    "filesize" "^3.6.1"
    "gzip-size" "^5.0.0"
    "lodash" "^4.17.19"
    "mkdirp" "^0.5.1"
    "opener" "^1.5.1"
    "ws" "^6.0.0"

"webpack-chain@^6.4.0":
  "integrity" "sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-chain/-/webpack-chain-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"webpack-dev-middleware@^3.7.2":
  "integrity" "sha512-djelc/zGiz9nZj/U7PTBi2ViorGJXEWo/3ltkPbDyxCXhhEXkW0ce99falaok4TPj+AsxLiXJR0EBOb0zh9fKQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-dev-middleware/-/webpack-dev-middleware-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "memory-fs" "^0.4.1"
    "mime" "^2.4.4"
    "mkdirp" "^0.5.1"
    "range-parser" "^1.2.1"
    "webpack-log" "^2.0.0"

"webpack-dev-server@^3.11.0":
  "integrity" "sha512-3x31rjbEQWKMNzacUZRE6wXvUFuGpH7vr0lIEbYpMAG9BOxi0928QU1BBswOAP3kg3H1O4hiS+sq4YyAn6ANnA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-dev-server/-/webpack-dev-server-3.11.3.tgz"
  "version" "3.11.3"
  dependencies:
    "ansi-html-community" "0.0.8"
    "bonjour" "^3.5.0"
    "chokidar" "^2.1.8"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "debug" "^4.1.1"
    "del" "^4.1.1"
    "express" "^4.17.1"
    "html-entities" "^1.3.1"
    "http-proxy-middleware" "0.19.1"
    "import-local" "^2.0.0"
    "internal-ip" "^4.3.0"
    "ip" "^1.1.5"
    "is-absolute-url" "^3.0.3"
    "killable" "^1.0.1"
    "loglevel" "^1.6.8"
    "opn" "^5.5.0"
    "p-retry" "^3.0.1"
    "portfinder" "^1.0.26"
    "schema-utils" "^1.0.0"
    "selfsigned" "^1.10.8"
    "semver" "^6.3.0"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.21"
    "sockjs-client" "^1.5.0"
    "spdy" "^4.0.2"
    "strip-ansi" "^3.0.1"
    "supports-color" "^6.1.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^3.7.2"
    "webpack-log" "^2.0.0"
    "ws" "^6.2.1"
    "yargs" "^13.3.2"

"webpack-log@^2.0.0":
  "integrity" "sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-log/-/webpack-log-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-colors" "^3.0.0"
    "uuid" "^3.3.2"

"webpack-merge@^4.2.2":
  "integrity" "sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-merge/-/webpack-merge-4.2.2.tgz"
  "version" "4.2.2"
  dependencies:
    "lodash" "^4.17.15"

"webpack-sources@^1.1.0", "webpack-sources@^1.4.0", "webpack-sources@^1.4.1":
  "integrity" "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack-sources/-/webpack-sources-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "source-list-map" "^2.0.0"
    "source-map" "~0.6.1"

"webpack@^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", "webpack@^4.0.0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.1.0 || ^5.0.0-0", "webpack@^4.4.0", "webpack@>=2", "webpack@>=2.0.0 <5.0.0", "webpack@>=4.0.0":
  "integrity" "sha512-6jJuJjg8znb/xRItk7bkT0+Q7AHCYjjFnvKIWQPkNIOyRqoCGvkOs0ipeQzrqz4l5FtN5ZI/ukEHroeX/o1/5Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webpack/-/webpack-4.46.0.tgz"
  "version" "4.46.0"
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "acorn" "^6.4.1"
    "ajv" "^6.10.2"
    "ajv-keywords" "^3.4.1"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^4.5.0"
    "eslint-scope" "^4.0.3"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^2.4.0"
    "loader-utils" "^1.2.3"
    "memory-fs" "^0.4.1"
    "micromatch" "^3.1.10"
    "mkdirp" "^0.5.3"
    "neo-async" "^2.6.1"
    "node-libs-browser" "^2.2.1"
    "schema-utils" "^1.0.0"
    "tapable" "^1.1.3"
    "terser-webpack-plugin" "^1.4.3"
    "watchpack" "^1.7.4"
    "webpack-sources" "^1.4.1"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/websocket-driver/-/websocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"webwackify@0.1.6":
  "integrity" "sha512-pGcw1T3HpNnM/UTRQqqRkkkzythSLts05mB+7Gr00B+0VbL0m39dFL5g20rSIEUt9Wrpw+/8k+snxRlUFHhcqA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/webwackify/-/webwackify-0.1.6.tgz"
  "version" "0.1.6"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^2.0.0":
  "integrity" "sha512-B+enWhmw6cjfVC7kS8Pj9pCrKSc5txArRyaYGe088shv/FGWH+0Rjx/xPgtsWfsUtS27FkP697E4DDhgrgoc0Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9":
  "integrity" "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/which/-/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wildcard@^1.1.0":
  "integrity" "sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wildcard/-/wildcard-1.1.2.tgz"
  "version" "1.1.2"

"wmf@~1.0.1":
  "integrity" "sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wmf/-/wmf-1.0.2.tgz"
  "version" "1.0.2"

"word-wrap@~1.2.3":
  "integrity" "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/word-wrap/-/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"word@~0.3.0":
  "integrity" "sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/word/-/word-0.3.0.tgz"
  "version" "0.3.0"

"worker-farm@^1.7.0":
  "integrity" "sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/worker-farm/-/worker-farm-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "errno" "~0.1.7"

"wrap-ansi@^5.1.0":
  "integrity" "sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wrap-ansi/-/wrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write@1.0.3":
  "integrity" "sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/write/-/write-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "mkdirp" "^0.5.1"

"ws@^6.0.0", "ws@^6.2.1":
  "integrity" "sha512-zmhltoSR8u1cnDsD43TX59mzoMZsLKqUweyYBAIvTngR3shc0W6aOZylZmq/7hqyVxPdi+5Ud2QInblgyE72fw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/ws/-/ws-6.2.2.tgz"
  "version" "6.2.2"
  dependencies:
    "async-limiter" "~1.0.0"

"xhr@2.4.0":
  "integrity" "sha512-TUbBsdAuJbX8olk9hsDwGK8P1ri1XlV+PdEWkYw+HQQbpkiBR8PLgD1F3kQDPBs9l4Px34hP9rCYAZOCCAENbw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/xhr/-/xhr-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "global" "~4.3.0"
    "is-function" "^1.0.1"
    "parse-headers" "^2.0.0"
    "xtend" "^4.0.0"

"xlsx@0.16.3":
  "integrity" "sha512-LInZ1OK6vpe+Em8XDZ5gDH3WixARwxI7UWc+3chLeafI6gUwECEgL43k4Tjbs1uRfkxpM7wQFy5DLE0hFBRqRw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/xlsx/-/xlsx-0.16.3.tgz"
  "version" "0.16.3"
  dependencies:
    "adler-32" "~1.2.0"
    "cfb" "^1.1.4"
    "codepage" "~1.14.0"
    "commander" "~2.17.1"
    "crc-32" "~1.2.0"
    "exit-on-epipe" "~1.0.1"
    "ssf" "~0.11.2"
    "wmf" "~1.0.1"
    "word" "~0.3.0"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/y18n/-/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yallist/-/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yargs-parser@^13.1.2":
  "integrity" "sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yargs-parser/-/yargs-parser-13.1.2.tgz"
  "version" "13.1.2"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yargs-parser/-/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^13.3.2":
  "integrity" "sha512-AX3Zw5iPruN5ie6xGRIDgqkT+ZhnRlZMLMHAs8tg7nRruy2Nb+i5o9bwghAogtM08q1dpr2LVoS8KSTMYpWXUw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yargs/-/yargs-13.3.2.tgz"
  "version" "13.3.2"
  dependencies:
    "cliui" "^5.0.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^13.1.2"

"yargs@^16.0.0":
  "integrity" "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yargs/-/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yorkie@^2.0.0":
  "integrity" "sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/yorkie/-/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"

"zrender@5.4.0":
  "integrity" "sha512-rOS09Z2HSVGFs2dn/TuYk5BlCaZcVe8UDLLjj1ySYF828LATKKdxuakSZMvrDz54yiKPDYVfjdKqcX8Jky3BIA=="
  "resolved" "http://nexus.hisense.com/repository/npm-public/zrender/-/zrender-5.4.0.tgz"
  "version" "5.4.0"
  dependencies:
    "tslib" "2.3.0"
