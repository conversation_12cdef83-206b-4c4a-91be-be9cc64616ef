/**
 * 车间站立会议暗黑模式样式
 * 本文件定义了车间站立会议页面在暗黑主题下的所有样式
 * 样式按照组件类型和功能进行分组
 */

/*==================================
  1. 基础样式
==================================*/
/* 全局暗色主题背景和文字颜色 */
body.has-dark-theme .workshop-meeting.dark-theme,
.workshop-meeting.dark-theme {
  background-color: #01141e !important; /* 深蓝色背景 */
  color: #fff !important; /* 白色文字 */
}

/*==================================
  2. 卡片组件样式
==================================*/
/* 卡片容器基本样式 */
.workshop-meeting.dark-theme .card-container {
  background-color: #001d2c !important; /* 深色背景 */
}

/* 卡片内容区样式 */
.workshop-meeting.dark-theme .card-container .card-content {
  color: #ffffff !important; /* 白色文字 */
}

/* 卡片标题栏样式 */
.workshop-meeting.dark-theme .card-container .card-title {
  background: linear-gradient(
    90deg,
    rgba(5, 82, 99, 0.4) 0%,
    rgba(10, 216, 244, 0) 89%
  ) !important;
}

.workshop-meeting.dark-theme .card-container .card-title .card-title-text {
  font-weight: bold;
  letter-spacing: normal;
  color: #ffffff;
  text-shadow: 0px 0px 7.5px rgba(0, 170, 166, 0.8);
}

/*==================================
  3. 新建事件按钮样式
==================================*/
/* 确保新建事件按钮在暗黑主题下的样式正确应用 */
body.has-dark-theme .card-container .card-title .addExceptionBtn,
.workshop-meeting.dark-theme .card-container .card-title .addExceptionBtn {
  background: rgba(0, 170, 166, 0.8) !important;
  border: 1px solid #00fbfb !important;
  color: #ffffff !important;
  padding: 0 12px !important;
  height: 32px !important;
  font-weight: 500 !important;
}

/* 按钮悬停效果 */
body.has-dark-theme .card-container .card-title .addExceptionBtn:hover,
.workshop-meeting.dark-theme
  .card-container
  .card-title
  .addExceptionBtn:hover {
  background: rgba(0, 251, 251, 0.4) !important;
}

/* 按钮文本样式 */
body.has-dark-theme .card-container .card-title .addExceptionBtnText,
.workshop-meeting.dark-theme .card-container .card-title .addExceptionBtnText {
  color: #ffffff !important;
}

/*==================================
  4. 筛选器组件样式
==================================*/
/* 筛选器框样式 */
.workshop-meeting.dark-theme
  .filter-item-container
  .ant-select
  .ant-select-selection,
.workshop-meeting.dark-theme .filter-item-container .ant-calendar-picker-input,
.workshop-meeting.dark-theme .filter-item-container .ant-input {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

/* 筛选器占位符文本样式 */
.workshop-meeting.dark-theme
  .filter-item-container
  .ant-select-selection__placeholder,
.workshop-meeting.dark-theme
  .filter-item-container
  .ant-calendar-picker-input::placeholder,
.workshop-meeting.dark-theme .filter-item-container .ant-input::placeholder,
.workshop-meeting.dark-theme
  .filter-item-container
  .ant-cascader-picker-label {
  color: rgba(255, 255, 255, 0.7) !important; /* 半透明白色 */
}

/* 筛选器和按钮组容器背景 */
.workshop-meeting.dark-theme .filter-wrapper.with-bg,
.workshop-meeting.dark-theme .button-group.with-bg {
  background-color: #001d2c !important; /* 深色背景 */
}

/*==================================
  5. 数据指标样式
==================================*/
/* 质量指标数值样式 */
.workshop-meeting.dark-theme
  .card-container
  .card-content
  .quality-item
  .item-value {
  color: #ffffff !important; /* 白色文字 */
}

/* 质量指标标签样式 */
.workshop-meeting.dark-theme
  .card-container
  .card-content
  .quality-item
  .item-label {
  color: #aaa !important; /* 灰色文字 */
}

/* 异常问题指标数值样式 */
.workshop-meeting.dark-theme
  .card-container
  .card-content
  .issue-item
  .item-value {
  color: #ffffff !important; /* 白色文字 */
}

/* 异常问题指标标签样式 */
.workshop-meeting.dark-theme
  .card-container
  .card-content
  .issue-item
  .item-label {
  color: #aaa !important; /* 灰色文字 */
}

/*==================================
  6. 图表样式
==================================*/
/* 图表背景透明 */
.workshop-meeting.dark-theme .card-container .card-content .chart {
  background-color: transparent !important; /* 透明背景 */
}

/*==================================
  7. 表格样式
==================================*/
/* 表格容器样式 */
.workshop-meeting.dark-theme .card-container.table-container {
  background-color: #001d2c !important; /* 深色背景 */
}

/* 表格组件基本样式 */
.workshop-meeting.dark-theme .ant-table-wrapper .ant-table,
.workshop-meeting.dark-theme .table-container .ant-table {
  background-color: #001d2c !important; /* 深色背景 */
}

/* 表头样式 */
.workshop-meeting.dark-theme .ant-table-wrapper .ant-table-thead > tr > th,
.workshop-meeting.dark-theme .table-container .ant-table-thead > tr > th {
  background-color: #015d52 !important; /* 稍浅的深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-bottom: 1px solid #00fbfb !important; /* 青色底边框 */
}

/* 表格单元格基本样式 */
.workshop-meeting.dark-theme .ant-table-wrapper .ant-table-tbody > tr > td,
.workshop-meeting.dark-theme .table-container .ant-table-tbody > tr > td,
.workshop-meeting.dark-theme .ant-table-wrapper tr.table-row td,
.workshop-meeting.dark-theme .table-container tr.table-row td {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-bottom: 1px solid #00fbfb !important; /* 青色底边框 */
}

/* 表格偶数行样式 */
.workshop-meeting.dark-theme
  .ant-table-wrapper
  .ant-table-tbody
  > tr:nth-child(even)
  > td,
.workshop-meeting.dark-theme
  .table-container
  .ant-table-tbody
  > tr:nth-child(even)
  > td,
.workshop-meeting.dark-theme .ant-table-wrapper tr.table-row:nth-child(even) td,
.workshop-meeting.dark-theme .table-container tr.table-row:nth-child(even) td {
  background-color: #00464c !important; /* 稍浅的深色背景 */
}

/* 表格悬停行样式 */
.workshop-meeting.dark-theme
  .ant-table-wrapper
  .ant-table-tbody
  > tr:hover
  > td,
.workshop-meeting.dark-theme .table-container .ant-table-tbody > tr:hover > td,
.workshop-meeting.dark-theme
  .ant-table-wrapper
  .ant-table-tbody
  > tr.ant-table-row:hover
  > td,
.workshop-meeting.dark-theme .ant-table-wrapper tr.table-row:hover td,
.workshop-meeting.dark-theme .table-container tr.table-row:hover td {
  background-color: #008590 !important; /* 青色背景 */
}

/*==================================
  8. Ant Design组件样式覆盖
==================================*/
/* 下拉选择框样式 */
.workshop-meeting.dark-theme .ant-select {
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-select .ant-select-selection {
  background-color: #001d2c !important; /* 深色背景 */
  border: 1px solid #00fbfb !important; /* 青色边框 */
}

/* 级联选择器样式 */
.workshop-meeting.dark-theme .ant-cascader-picker {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-cascader-picker-label {
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-cascader-picker-arrow {
  color: #00fbfb !important; /* 青色图标 */
}

.workshop-meeting.dark-theme .ant-cascader-picker:hover {
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 输入框样式 */
.workshop-meeting.dark-theme .ant-input,
.workshop-meeting.dark-theme .ant-calendar-picker-input {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 日期选择器样式 */
.workshop-meeting.dark-theme .ant-calendar-picker .ant-calendar-picker-input {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

/* 图标样式 */
.workshop-meeting.dark-theme .ant-select-arrow,
.workshop-meeting.dark-theme .ant-calendar-picker-icon {
  color: #00fbfb !important; /* 青色图标 */
}

/* 按钮样式 */
.workshop-meeting.dark-theme .ant-btn:not(.ant-btn-primary) {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-btn:not(.ant-btn-primary):hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/*==================================
  9. 弹出组件样式
==================================*/
/* 模态框标题样式 */
.workshop-meeting.dark-theme .ant-modal-wrap .ant-modal .modal-title,
body.has-dark-theme .ant-modal-wrap .ant-modal .modal-title,
.dark-theme .modal-title {
  background: linear-gradient(180deg, #00141a 0%, #00282b 128%) !important;
}

.workshop-meeting.dark-theme .ant-modal-wrap .ant-modal .modal-title::before,
body.has-dark-theme .ant-modal-wrap .ant-modal .modal-title::before,
.dark-theme .modal-title::before {
  background: none !important;
  background-image: none !important;
  opacity: 0 !important;
  display: none !important;
}

.workshop-meeting.dark-theme
  .ant-modal-wrap
  .ant-modal
  .modal-title
  .title-text,
body.has-dark-theme .ant-modal-wrap .ant-modal .modal-title .title-text,
.dark-theme .modal-title .title-text {
  color: #ffffff !important;
  font-weight: bold !important;
}

.workshop-meeting.dark-theme
  .ant-modal-wrap
  .ant-modal
  .modal-title
  .title-icon,
body.has-dark-theme .ant-modal-wrap .ant-modal .modal-title .title-icon,
.dark-theme .modal-title .title-icon {
  background-image: url("~@/assets/images/workShop_standing_meeting/dialog_banner_title_icon.png") !important;
  /* filter: brightness(10) !important; */
}

/* 下拉菜单样式 */
.workshop-meeting.dark-theme .ant-select-dropdown,
body.has-dark-theme .ant-select-dropdown {
  background-color: #001d2c !important; /* 深色背景 */
  border: 1px solid #00fbfb !important; /* 青色边框 */
  box-shadow: 0 2px 8px rgba(0, 251, 251, 0.2) !important; /* 青色阴影 */
}

.workshop-meeting.dark-theme .ant-select-dropdown-content,
.workshop-meeting.dark-theme .ant-select-dropdown-menu,
body.has-dark-theme .ant-select-dropdown-menu {
  background-color: #001d2c !important; /* 深色背景 */
}

/* 下拉菜单选项样式 */
.workshop-meeting.dark-theme
  .ant-select-dropdown
  .ant-select-dropdown-menu-item,
body.has-dark-theme .ant-select-dropdown-menu-item {
  color: #ffffff !important; /* 白色文字 */
  background-color: #001d2c !important; /* 深色背景 */
}

/* 级联选择器下拉菜单样式 */
.workshop-meeting.dark-theme .ant-cascader-menus,
body.has-dark-theme .ant-cascader-menus {
  background-color: #001d2c !important; /* 深色背景 */
  border: 1px solid #00fbfb !important; /* 青色边框 */
  box-shadow: 0 2px 8px rgba(0, 251, 251, 0.2) !important; /* 青色阴影 */
}

.workshop-meeting.dark-theme .ant-cascader-menu,
body.has-dark-theme .ant-cascader-menu {
  background-color: #001d2c !important; /* 深色背景 */
  border-right-color: #00fbfb !important; /* 青色边框 */
}

.workshop-meeting.dark-theme .ant-cascader-menu-item,
body.has-dark-theme .ant-cascader-menu-item {
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-cascader-menu-item:hover,
body.has-dark-theme .ant-cascader-menu-item:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

.workshop-meeting.dark-theme .ant-cascader-menu-item-active,
body.has-dark-theme .ant-cascader-menu-item-active {
  background-color: rgba(0, 251, 251, 0.4) !important; /* 较深的半透明青色背景 */
  font-weight: bold;
}

.workshop-meeting.dark-theme .ant-cascader-menu-item-expand-icon,
body.has-dark-theme .ant-cascader-menu-item-expand-icon {
  color: #00fbfb !important; /* 青色图标 */
}

.workshop-meeting.dark-theme
  .ant-select-dropdown
  .ant-select-dropdown-menu-item:hover,
body.has-dark-theme .ant-select-dropdown-menu-item:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/* 下拉菜单已选项样式 */
.workshop-meeting.dark-theme
  .ant-select-dropdown
  .ant-select-dropdown-menu-item-selected,
.workshop-meeting.dark-theme
  .ant-select-dropdown
  .ant-select-dropdown-menu-item-selected:hover,
body.has-dark-theme .ant-select-dropdown-menu-item-selected {
  background-color: rgba(
    0,
    251,
    251,
    0.4
  ) !important; /* 较深的半透明青色背景 */
  color: #ffffff !important; /* 白色文字 */
}

/* 下拉选择项样式 */
.workshop-meeting.dark-theme .ant-select-selection__choice {
  background-color: #00464c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-select-selection__choice__content {
  color: #ffffff !important; /* 白色文字 */
}

/* 日历弹出框样式 */
.workshop-meeting.dark-theme .ant-calendar,
body.has-dark-theme .ant-calendar {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

/* 日历日期样式 */
.workshop-meeting.dark-theme .ant-calendar .ant-calendar-date,
body.has-dark-theme .ant-calendar .ant-calendar-date {
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-calendar .ant-calendar-date:hover,
body.has-dark-theme .ant-calendar .ant-calendar-date:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/* 日历已选日期样式 */
.workshop-meeting.dark-theme
  .ant-calendar
  .ant-calendar-selected-day
  .ant-calendar-date,
body.has-dark-theme
  .ant-calendar
  > div
  .ant-calendar-selected-day
  .ant-calendar-date {
  background-color: rgba(
    0,
    251,
    251,
    0.4
  ) !important; /* 较深的半透明青色背景 */
  color: #ffffff !important; /* 白色文字 */
}

/* 日历列头和日历头部样式 */
.workshop-meeting.dark-theme .ant-calendar .ant-calendar-column-header,
.workshop-meeting.dark-theme .ant-calendar .ant-calendar-header,
body.has-dark-theme .ant-calendar .ant-calendar-column-header,
body.has-dark-theme .ant-calendar .ant-calendar-header,
.workshop-meeting.dark-theme .ant-calendar-my-select,
body.has-dark-theme .ant-calendar-my-select .ant-calendar-month-select,
body.has-dark-theme .ant-calendar-my-select .ant-calendar-year-select,
body.has-dark-theme .ant-calendar-header .ant-calendar-century-select,
body.has-dark-theme .ant-calendar-header .ant-calendar-decade-select,
body.has-dark-theme .ant-calendar-header .ant-calendar-month-select,
body.has-dark-theme .ant-calendar-header .ant-calendar-year-select {
  color: #ffffff !important; /* 白色文字 */
}

/* 确保悬停状态颜色 */
body.has-dark-theme .ant-calendar-header .ant-calendar-century-select:hover,
body.has-dark-theme .ant-calendar-header .ant-calendar-decade-select:hover,
body.has-dark-theme .ant-calendar-header .ant-calendar-month-select:hover,
body.has-dark-theme .ant-calendar-header .ant-calendar-year-select:hover {
  color: #00fbfb !important;
}

/* 模态框样式 */
.workshop-meeting.dark-theme .ant-modal-content,
body.has-dark-theme .ant-modal-content {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-modal-content .ant-modal-body,
body.has-dark-theme .ant-modal-content .ant-modal-body {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-modal-content .ant-modal-close-x,
body.has-dark-theme .ant-modal-content .ant-modal-close-x {
  color: #ffffff !important; /* 白色图标 */
}

/*==================================
  10. 表单元素暗黑模式样式
==================================*/
/* 表单标签颜色 */
.workshop-meeting.dark-theme .form-label,
body.has-dark-theme .form-label {
  color: #ffffff !important; /* 白色文字 */
}

/* 表单输入框样式 */
.workshop-meeting.dark-theme .form-input .ant-input,
.workshop-meeting.dark-theme .form-input.ant-input,
body.has-dark-theme .form-input .ant-input,
body.has-dark-theme .form-input.ant-input {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 表单下拉选择框样式 */
.workshop-meeting.dark-theme .form-input.ant-select .ant-select-selection,
body.has-dark-theme .form-input.ant-select .ant-select-selection {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 表单下拉选择框文本颜色 */
.workshop-meeting.dark-theme
  .form-input.ant-select
  .ant-select-selection-selected-value,
body.has-dark-theme
  .form-input.ant-select
  .ant-select-selection-selected-value {
  color: #ffffff !important; /* 白色文字 */
}

/* 表单下拉选择框占位符颜色 */
.workshop-meeting.dark-theme
  .form-input.ant-select
  .ant-select-selection__placeholder,
body.has-dark-theme .form-input.ant-select .ant-select-selection__placeholder {
  color: rgba(255, 255, 255, 0.7) !important; /* 半透明白色 */
}

/* 表单容器样式 */
.workshop-meeting.dark-theme .modal-content,
body.has-dark-theme .modal-content {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
}

/* 表单行样式 */
.workshop-meeting.dark-theme .form-row,
body.has-dark-theme .form-row {
  border-color: rgba(0, 251, 251, 0.3) !important; /* 半透明青色边框 */
}

/* 表单项样式 */
.workshop-meeting.dark-theme .form-item,
body.has-dark-theme .form-item {
  color: #ffffff !important; /* 白色文字 */
}

/* 用户图标样式 */
.workshop-meeting.dark-theme .user-icon,
body.has-dark-theme .user-icon {
  filter: brightness(10) !important; /* 使图标变亮，适应暗色背景 */
}

/*==================================
  11. 更多Ant Design组件暗黑模式样式
==================================*/
/* 日期选择器样式 */
.workshop-meeting.dark-theme .ant-date-picker-input,
.workshop-meeting.dark-theme .ant-calendar-my-select,
body.has-dark-theme .ant-date-picker-input,
.workshop-meeting.dark-theme .ant-date-picker,
body.has-dark-theme .ant-date-picker {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 时间选择面板样式 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-panel,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-panel,
body.has-dark-theme .ant-calendar-time-picker-panel.ant-calendar-time-picker-panel,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 时间选择面板标题 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-panel-header,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-panel-header,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-panel-header,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-panel-header {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  border-bottom-color: #00fbfb !important; /* 青色边框 */
}

/* 时间选择面板列表容器 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-panel-inner,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-panel-inner,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-panel-inner,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-panel-inner {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  box-shadow: 0 2px 8px rgba(0, 251, 251, 0.2) !important; /* 青色阴影 */
}

/* 时间选择面板列表 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-combobox,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-combobox,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-combobox,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-combobox {
  background-color: #001d2c !important; /* 深色背景 */
}

/* 时间选择面板选项列表 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-select,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-select,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-select,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select {
  background-color: #001d2c !important; /* 深色背景 */
  border-right-color: #00fbfb !important; /* 青色边框 */
}

/* 时间选择面板选项 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-select li,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-select li,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li {
  color: #ffffff !important; /* 白色文字 */
  background-color: #001d2c !important; /* 深色背景 */
}

/* 时间选择面板选项悬停状态 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-select li:hover,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-select li:hover,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li:hover,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/* 时间选择面板选中选项 - 增加权重 */
html body.has-dark-theme .ant-calendar-time-picker-select li.ant-calendar-time-picker-select-option-selected,
html .workshop-meeting.dark-theme .ant-calendar-time-picker-select li.ant-calendar-time-picker-select-option-selected,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li.ant-calendar-time-picker-select-option-selected,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li.ant-calendar-time-picker-select-option-selected {
  background-color: rgba(0, 251, 251, 0.4) !important; /* 较深的半透明青色背景 */
  color: #ffffff !important; /* 白色文字 */
}

/* 时间选择面板底部按钮区域 - 增加权重 */
html body.has-dark-theme .ant-calendar-footer,
html .workshop-meeting.dark-theme .ant-calendar-footer,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-footer,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-footer {
  background-color: #001d2c !important; /* 深色背景 */
  border-top-color: #00fbfb !important; /* 青色边框 */
}

/* 时间选择面板底部按钮 - 增加权重 */
html body.has-dark-theme .ant-calendar-footer .ant-calendar-time-picker-btn,
html body.has-dark-theme .ant-calendar-footer .ant-calendar-ok-btn,
html .workshop-meeting.dark-theme .ant-calendar-footer .ant-calendar-time-picker-btn,
html .workshop-meeting.dark-theme .ant-calendar-footer .ant-calendar-ok-btn,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-time-picker-btn,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-ok-btn,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-time-picker-btn,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-ok-btn {
  color: #ffffff !important; /* 白色文字 */
}

/* 时间选择面板底部按钮悬停状态 - 增加权重 */
html body.has-dark-theme .ant-calendar-footer .ant-calendar-time-picker-btn:hover,
html body.has-dark-theme .ant-calendar-footer .ant-calendar-ok-btn:hover,
html .workshop-meeting.dark-theme .ant-calendar-footer .ant-calendar-time-picker-btn:hover,
html .workshop-meeting.dark-theme .ant-calendar-footer .ant-calendar-ok-btn:hover,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-time-picker-btn:hover,
body.has-dark-theme .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-ok-btn:hover,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-time-picker-btn:hover,
.workshop-meeting.dark-theme + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-ok-btn:hover {
  color: #00fbfb !important; /* 青色文字 */
}

/* 确保在非暗色主题下，时间选择器面板恢复默认样式 */
html body:not(.has-dark-theme) .ant-calendar-time-picker-panel,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel {
  background-color: #fff !important;
  color: rgba(0, 0, 0, 0.65) !important;
  border-color: #e8e8e8 !important;
}

html body:not(.has-dark-theme) .ant-calendar-time-picker-panel-inner,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-time-picker-panel-inner {
  background-color: #fff !important;
  color: rgba(0, 0, 0, 0.65) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

html body:not(.has-dark-theme) .ant-calendar-time-picker-panel-header,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-time-picker-panel-header {
  background-color: #fff !important;
  color: rgba(0, 0, 0, 0.65) !important;
  border-bottom-color: #e8e8e8 !important;
}

html body:not(.has-dark-theme) .ant-calendar-time-picker-select,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select {
  background-color: #fff !important;
  border-right-color: #e8e8e8 !important;
}

html body:not(.has-dark-theme) .ant-calendar-time-picker-select li,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li {
  color: rgba(0, 0, 0, 0.65) !important;
  background-color: #fff !important;
}

html body:not(.has-dark-theme) .ant-calendar-time-picker-select li:hover,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li:hover {
  background-color: #e6f7ff !important;
}

html body:not(.has-dark-theme) .ant-calendar-time-picker-select li.ant-calendar-time-picker-select-option-selected,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-time-picker-select li.ant-calendar-time-picker-select-option-selected {
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

html body:not(.has-dark-theme) .ant-calendar-footer,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-footer {
  background-color: #fff !important;
  border-top-color: #e8e8e8 !important;
}

html body:not(.has-dark-theme) .ant-calendar-footer .ant-calendar-time-picker-btn,
html body:not(.has-dark-theme) .ant-calendar-footer .ant-calendar-ok-btn,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-time-picker-btn,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-ok-btn {
  color: rgba(0, 0, 0, 0.65) !important;
}

html body:not(.has-dark-theme) .ant-calendar-footer .ant-calendar-time-picker-btn:hover,
html body:not(.has-dark-theme) .ant-calendar-footer .ant-calendar-ok-btn:hover,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-time-picker-btn:hover,
html .workshop-meeting:not(.dark-theme) + .ant-calendar-time-picker-panel .ant-calendar-footer .ant-calendar-ok-btn:hover {
  color: #1890ff !important;
}

/* 图标样式 */
.workshop-meeting.dark-theme .ant-icon,
body.has-dark-theme .ant-icon {
  color: #00fbfb !important; /* 青色图标 */
}

/* 弹出框样式 */
.workshop-meeting.dark-theme .ant-popover .ant-popover-inner,
body.has-dark-theme .ant-popover .ant-popover-inner {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

.workshop-meeting.dark-theme .ant-popover .ant-popover-inner-content,
body.has-dark-theme .ant-popover .ant-popover-inner-content {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-popover .ant-popover-arrow,
body.has-dark-theme .ant-popover .ant-popover-arrow {
  border-color: #00fbfb !important; /* 青色边框 */
  background-color: #001d2c !important; /* 深色背景 */
}

/* 开关样式 */
.workshop-meeting.dark-theme .ant-switch,
body.has-dark-theme .ant-switch {
  background-color: #ff4d4f !important; /* 鲜明的红色背景，表示关闭状态 */
  border: 2px solid #ff7875 !important; /* 红色边框 */
  box-shadow: 0 0 8px rgba(255, 77, 79, 0.4) !important; /* 红色发光效果 */
}

.workshop-meeting.dark-theme .ant-switch-checked,
body.has-dark-theme .ant-switch-checked {
  background-color: #52c41a !important; /* 鲜明的绿色背景，表示开启状态 */
  border: 2px solid #73d13d !important; /* 绿色边框 */
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.4) !important; /* 绿色发光效果 */
}

.workshop-meeting.dark-theme .ant-switch-handle,
body.has-dark-theme .ant-switch-handle {
  background-color: #ffffff !important; /* 白色开关柄 */
  border: 1px solid rgba(0, 0, 0, 0.1) !important; /* 轻微边框增强立体感 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important; /* 阴影增强立体感 */
}

/* 开关悬停效果 */
.workshop-meeting.dark-theme .ant-switch:hover,
body.has-dark-theme .ant-switch:hover {
  background-color: #ff7875 !important; /* 悬停时稍亮的红色 */
  box-shadow: 0 0 12px rgba(255, 77, 79, 0.6) !important; /* 更强的发光效果 */
}

.workshop-meeting.dark-theme .ant-switch-checked:hover,
body.has-dark-theme .ant-switch-checked:hover {
  background-color: #73d13d !important; /* 悬停时稍亮的绿色 */
  box-shadow: 0 0 12px rgba(82, 196, 26, 0.6) !important; /* 更强的发光效果 */
}

/* 确认按钮样式 */
.workshop-meeting.dark-theme .ant-btn-primary,
body.has-dark-theme .ant-btn-primary {
  /* background-color: rgba(0, 251, 251, 0.8) !important; 青色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/* 更多按钮样式 */
.workshop-meeting.dark-theme .ai_btn,
.workshop-meeting.dark-theme .QRCode_btn,
.workshop-meeting.dark-theme .moreOperator_btn,
body.has-dark-theme .ai_btn,
body.has-dark-theme .QRCode_btn,
body.has-dark-theme .moreOperator_btn {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ai_btn:hover,
.workshop-meeting.dark-theme .QRCode_btn:hover,
.workshop-meeting.dark-theme .moreOperator_btn:hover,
body.has-dark-theme .ai_btn:hover,
body.has-dark-theme .QRCode_btn:hover,
body.has-dark-theme .moreOperator_btn:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/* 取消按钮样式 */
.workshop-meeting.dark-theme .cancel-btn,
body.has-dark-theme .cancel-btn {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .cancel-btn:hover,
body.has-dark-theme .cancel-btn:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/* 确认按钮样式 */
.workshop-meeting.dark-theme .confirm-btn,
body.has-dark-theme .confirm-btn {
  background-color: rgba(0, 251, 251, 0.8) !important; /* 青色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

/*==================================
  12. 回到顶部按钮样式
==================================*/
/* 暗色主题下的回到顶部按钮样式 */
.workshop-meeting.dark-theme .back-to-top,
body.has-dark-theme .back-to-top {
  background-color: rgba(0, 170, 166, 0.8) !important; /* 半透明青色背景 */
  color: #ffffff !important; /* 白色文字 */
  border: 1px solid #00fbfb !important; /* 青色边框 */
  box-shadow: 0 2px 10px rgba(0, 251, 251, 0.3) !important; /* 青色阴影 */
  z-index: 999 !important; /* 确保在最上层 */
}

.workshop-meeting.dark-theme .back-to-top:hover,
body.has-dark-theme .back-to-top:hover {
  background-color: rgba(0, 251, 251, 0.6) !important; /* 较亮的半透明青色背景 */
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 251, 251, 0.5) !important; /* 更明显的青色阴影 */
}

/*==================================
  13. 分页组件样式
==================================*/
/* 分页容器样式 */
.workshop-meeting.dark-theme .ant-pagination,
body.has-dark-theme .ant-pagination {
  color: #ffffff !important; /* 白色文字 */
}

/* 分页数字样式 */
.workshop-meeting.dark-theme .ant-pagination-item,
body.has-dark-theme .ant-pagination-item {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

.workshop-meeting.dark-theme .ant-pagination-item a,
body.has-dark-theme .ant-pagination-item a {
  color: #ffffff !important; /* 白色文字 */
}

/* 分页当前页样式 */
.workshop-meeting.dark-theme .ant-pagination-item-active,
body.has-dark-theme .ant-pagination-item-active {
  background-color: rgba(0, 251, 251, 0.4) !important; /* 半透明青色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

.workshop-meeting.dark-theme .ant-pagination-item-active a,
body.has-dark-theme .ant-pagination-item-active a {
  color: #ffffff !important; /* 白色文字 */
}

/* 分页按钮样式 */
.workshop-meeting.dark-theme .ant-pagination-prev .ant-pagination-item-link,
.workshop-meeting.dark-theme .ant-pagination-next .ant-pagination-item-link,
body.has-dark-theme .ant-pagination-prev .ant-pagination-item-link,
body.has-dark-theme .ant-pagination-next .ant-pagination-item-link {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

/* 分页按钮图标样式 */
.workshop-meeting.dark-theme .ant-pagination-prev .ant-pagination-item-link .ant-pagination-item-container .ant-pagination-item-link-icon,
.workshop-meeting.dark-theme .ant-pagination-next .ant-pagination-item-link .ant-pagination-item-container .ant-pagination-item-link-icon,
body.has-dark-theme .ant-pagination-prev .ant-pagination-item-link .ant-pagination-item-container .ant-pagination-item-link-icon,
body.has-dark-theme .ant-pagination-next .ant-pagination-item-link .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: #00fbfb !important; /* 青色图标 */
}

/* 分页跳转样式 */
.workshop-meeting.dark-theme .ant-pagination-options,
body.has-dark-theme .ant-pagination-options {
  color: #ffffff !important; /* 白色文字 */
}

/* 分页跳转输入框样式 */
.workshop-meeting.dark-theme .ant-pagination-options .ant-pagination-options-quick-jumper input,
body.has-dark-theme .ant-pagination-options .ant-pagination-options-quick-jumper input {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

/* 分页每页条数选择器样式 */
.workshop-meeting.dark-theme .ant-pagination-options .ant-select .ant-select-selection,
body.has-dark-theme .ant-pagination-options .ant-select .ant-select-selection {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
  color: #ffffff !important; /* 白色文字 */
}

.workshop-meeting.dark-theme .ant-pagination-options .ant-select .ant-select-selection-selected-value,
body.has-dark-theme .ant-pagination-options .ant-select .ant-select-selection-selected-value {
  color: #ffffff !important; /* 白色文字 */
}

/* 分页总数样式 */
.workshop-meeting.dark-theme .ant-pagination-total-text,
body.has-dark-theme .ant-pagination-total-text {
  color: #ffffff !important; /* 白色文字 */
}

/* 表格内分页组件样式 */
.workshop-meeting.dark-theme .ant-table-wrapper .ant-pagination,
.workshop-meeting.dark-theme .table-container .ant-pagination,
body.has-dark-theme .ant-table-wrapper .ant-pagination,
body.has-dark-theme .table-container .ant-pagination {
  background-color: #001d2c !important; /* 深色背景 */
  color: #ffffff !important; /* 白色文字 */
  padding: 16px 0 !important; /* 增加内边距 */
}

/* 表格内分页组件中的图标 */
.workshop-meeting.dark-theme .ant-table-wrapper .ant-pagination .ant-pagination-item-link .anticon,
.workshop-meeting.dark-theme .table-container .ant-pagination .ant-pagination-item-link .anticon,
body.has-dark-theme .ant-table-wrapper .ant-pagination .ant-pagination-item-link .anticon,
body.has-dark-theme .table-container .ant-pagination .ant-pagination-item-link .anticon {
  color: #00fbfb !important; /* 青色图标 */
}

/* 分页组件中的跳转文本 */
.workshop-meeting.dark-theme .ant-pagination-options-quick-jumper,
body.has-dark-theme .ant-pagination-options-quick-jumper {
  color: #ffffff !important; /* 白色文字 */
}

/* 分页组件中的下拉选择器 */
.workshop-meeting.dark-theme .ant-pagination-options .ant-select-dropdown,
body.has-dark-theme .ant-pagination-options .ant-select-dropdown {
  background-color: #001d2c !important; /* 深色背景 */
  border-color: #00fbfb !important; /* 青色边框 */
}

.workshop-meeting.dark-theme .ant-pagination-options .ant-select-dropdown-menu-item,
body.has-dark-theme .ant-pagination-options .ant-select-dropdown-menu-item {
  color: #ffffff !important; /* 白色文字 */
  background-color: #001d2c !important; /* 深色背景 */
}

.workshop-meeting.dark-theme .ant-pagination-options .ant-select-dropdown-menu-item:hover,
body.has-dark-theme .ant-pagination-options .ant-select-dropdown-menu-item:hover {
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

.workshop-meeting.dark-theme .ant-pagination-options .ant-select-dropdown-menu-item-selected,
body.has-dark-theme .ant-pagination-options .ant-select-dropdown-menu-item-selected {
  background-color: rgba(0, 251, 251, 0.4) !important; /* 较深的半透明青色背景 */
  color: #ffffff !important; /* 白色文字 */
}

/*==================================
  14. 修复表格下拉选择项文字透明问题
==================================*/
/* 修复Ant Design下拉菜单选中项文字消失的问题 */
.workshop-meeting.dark-theme .ant-dropdown-menu-item-selected,
.workshop-meeting.dark-theme .ant-dropdown-menu-item-selected > a,
.workshop-meeting.dark-theme .ant-dropdown-menu-submenu-title-selected,
.workshop-meeting.dark-theme .ant-dropdown-menu-submenu-title-selected > a,
body.has-dark-theme .ant-dropdown-menu-item-selected,
body.has-dark-theme .ant-dropdown-menu-item-selected > a,
body.has-dark-theme .ant-dropdown-menu-submenu-title-selected,
body.has-dark-theme .ant-dropdown-menu-submenu-title-selected > a {
  color: #ffffff !important; /* 强制设置白色文字，覆盖透明色 */
  background-color: rgba(0, 251, 251, 0.4) !important; /* 半透明青色背景 */
}

/* 修复表格筛选下拉菜单选中项文字消失的问题 */
.workshop-meeting.dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item-selected,
.workshop-meeting.dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item-selected > a,
body.has-dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item-selected,
body.has-dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item-selected > a {
  color: #ffffff !important; /* 强制设置白色文字，覆盖透明色 */
  background-color: rgba(0, 251, 251, 0.4) !important; /* 半透明青色背景 */
}

/* 修复所有下拉菜单选中项的文字颜色问题 */
.workshop-meeting.dark-theme .ant-dropdown-menu-item-selected .ant-dropdown-menu-title-content,
.workshop-meeting.dark-theme .ant-dropdown-menu-submenu-title-selected .ant-dropdown-menu-title-content,
body.has-dark-theme .ant-dropdown-menu-item-selected .ant-dropdown-menu-title-content,
body.has-dark-theme .ant-dropdown-menu-submenu-title-selected .ant-dropdown-menu-title-content {
  color: #ffffff !important; /* 强制设置白色文字 */
}

/* 确保下拉菜单项的基本样式 */
.workshop-meeting.dark-theme .ant-dropdown-menu-item,
.workshop-meeting.dark-theme .ant-dropdown-menu-submenu-title,
body.has-dark-theme .ant-dropdown-menu-item,
body.has-dark-theme .ant-dropdown-menu-submenu-title {
  color: #ffffff !important; /* 白色文字 */
  background-color: #001d2c !important; /* 深色背景 */
}

/* 下拉菜单项悬停状态 */
.workshop-meeting.dark-theme .ant-dropdown-menu-item:hover,
.workshop-meeting.dark-theme .ant-dropdown-menu-submenu-title:hover,
body.has-dark-theme .ant-dropdown-menu-item:hover,
body.has-dark-theme .ant-dropdown-menu-submenu-title:hover {
  color: #ffffff !important; /* 白色文字 */
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}

/* 下拉菜单容器样式 */
.workshop-meeting.dark-theme .ant-dropdown-menu,
body.has-dark-theme .ant-dropdown-menu {
  background-color: #001d2c !important; /* 深色背景 */
  border: 1px solid #00fbfb !important; /* 青色边框 */
  box-shadow: 0 2px 8px rgba(0, 251, 251, 0.2) !important; /* 青色阴影 */
}

/* 表格筛选下拉菜单容器样式 */
.workshop-meeting.dark-theme .ant-table-filter-dropdown,
body.has-dark-theme .ant-table-filter-dropdown {
  background-color: #001d2c !important; /* 深色背景 */
  border: 1px solid #00fbfb !important; /* 青色边框 */
  box-shadow: 0 2px 8px rgba(0, 251, 251, 0.2) !important; /* 青色阴影 */
}

/* 表格筛选下拉菜单项样式 */
.workshop-meeting.dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item,
body.has-dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item {
  color: #ffffff !important; /* 白色文字 */
  background-color: #001d2c !important; /* 深色背景 */
}

/* 表格筛选下拉菜单项悬停状态 */
.workshop-meeting.dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item:hover,
body.has-dark-theme .ant-table-filter-dropdown .ant-dropdown-menu-item:hover {
  color: #ffffff !important; /* 白色文字 */
  background-color: rgba(0, 251, 251, 0.2) !important; /* 半透明青色背景 */
}
