<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-12-24 10:51:10
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-table
      :columns="columns"
      :data-source="targetData"
      bordered
      :scroll="{ x: 1000 }"
    >
      <template
        v-for="(zitem, zindex) in columns"
        :slot="zitem.key"
        slot-scope="text, record"
      >
        <!-- :disabled=" Number(record['year']) < new Date().getFullYear() ||
(Number(record['year']) === new Date().getFullYear() && Number(zitem.num) < new
Date().getMonth() + 1) " -->

        <a-input-number
          v-model="record[zitem.key]"
          :key="zindex"
          v-if="!['year'].includes(zitem.key)"
        />
        <span :key="zindex" v-else>{{ record[zitem.key] }}</span>
      </template>
      <template slot="title">
        <h3>{{ indexName }} {{ `(单位：${indexUnit})` }}</h3>
        <div>目标值填报时，如果是百分数，则填写实际值。</div>
        <div>
          示例：如果一个指标目标值为“50%”，则目标值填报为“0.5”；如果目标值为“300%”，则目标值填报为“3”
        </div>
      </template>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      isEdit: false, // 是否编辑状态
      indexName: "某某某指标",
      indexUnit: "%",
      columns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left"
        },
        {
          title: "一月",
          dataIndex: "Jan",
          key: "Jan",
          num: "01",
          scopedSlots: { customRender: "Jan" },
          width: 120
        },
        {
          title: "二月",
          dataIndex: "Feb",
          key: "Feb",
          num: "02",
          scopedSlots: { customRender: "Feb" },
          width: 120
        },
        {
          title: "三月",
          key: "Mar",
          dataIndex: "Mar",
          num: "03",
          scopedSlots: { customRender: "Mar" },
          width: 120
        },
        {
          title: "四月",
          key: "Apr",
          dataIndex: "Apr",
          num: "04",
          scopedSlots: { customRender: "Apr" },
          width: 120
        },
        {
          title: "五月",
          key: "May",
          dataIndex: "May",
          num: "05",
          scopedSlots: { customRender: "May" },
          width: 120
        },
        {
          title: "六月",
          key: "Jun",
          dataIndex: "Jun",
          num: "06",
          scopedSlots: { customRender: "Jun" },
          width: 120
        },
        {
          title: "七月",
          key: "Jul",
          dataIndex: "Jul",
          num: "07",
          scopedSlots: { customRender: "Jul" },
          width: 120
        },
        {
          title: "八月",
          key: "Aug",
          dataIndex: "Aug",
          num: "08",
          scopedSlots: { customRender: "Aug" },
          width: 120
        },
        {
          title: "九月",
          key: "Sept",
          dataIndex: "Sept",
          num: "09",
          scopedSlots: { customRender: "Sept" },
          width: 120
        },
        {
          title: "十月",
          key: "Oct",
          dataIndex: "Oct",
          scopedSlots: { customRender: "Oct" },
          num: "10",
          width: 120
        },
        {
          title: "十一月",
          key: "Nov",
          dataIndex: "Nov",
          num: "11",
          scopedSlots: { customRender: "Nov" },
          width: 120
        },
        {
          title: "十二月",
          key: "Dec",
          dataIndex: "Dec",
          num: "12",
          scopedSlots: { customRender: "Dec" },
          width: 120
        }
      ], // 表格列
      targetData: [],
      submitData: []
    };
  },
  methods: {
    show({ indexName, indexUnit, attributesId }) {
      this.visible = true;
      this.isEdit = true;
      this.indexName = indexName;
      this.indexUnit = indexUnit;
      this.generateCoulmns();
      this.$nextTick(() => {
        this.getIndexTarget(attributesId);
      });
    },
    generateCoulmns() {
      const year = new Date().getFullYear();
      const common = {
        Jan: "",
        Feb: "",
        Mar: "",
        Apr: "",
        May: "",
        Jun: "",
        Jul: "",
        Aug: "",
        Sept: "",
        Oct: "",
        Nov: "",
        Dec: ""
      };
      this.targetData.push({
        year: year - 1,
        ...common
      });
      this.targetData.push({
        year,
        ...common
      });
      this.targetData.push({
        year: year + 1,
        ...common
      });
    },
    getIndexTarget(attributesId) {
      request(
        `/api/smc/target/getListByIndex?attributesId=${attributesId}`
      ).then(res => {
        if (res && res.length) {
          const validArr = res.filter(item => item.target);
          validArr.forEach(element => {
            const arr = element.indexDate.split("-");
            this.updateTargetData(arr[0], arr[1], element.target);
          });
          this.submitData = res;
        }
      });
    },
    updateTargetData(year, month, target) {
      this.targetData.forEach(element => {
        if (year === String(element.year)) {
          const enKey = this.columns.filter(item => item.num === month)[0]
            .dataIndex;
          element[enKey] = target;
        }
      });
    },
    close() {
      this.targetData = [];
      this.indexName = "";
      this.indexUnit = "";
      this.submitData = [];
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 保存卡片信息
    saveCard() {
      this.targetData.forEach(yearElement => {
        for (const key in yearElement) {
          if (Object.hasOwnProperty.call(yearElement, key)) {
            const element = yearElement[key];
            if (key !== "year") {
              const numKey = this.columns.filter(
                item => item.dataIndex === key
              )[0]?.num;
              if (numKey) {
                this.submitData.forEach(submitElement => {
                  if (
                    submitElement.indexDate ===
                    `${yearElement["year"]}-${numKey}-01`
                  ) {
                    submitElement.target = element;
                  }
                });
              }
            }
          }
        }
      });
      request("/api/smc/target/batchUpdate", {
        method: "PUT",
        body: this.submitData
      }).then(() => {
        this.close();
        this.$emit("fetchData");
      });
    }
  }
};
</script>
