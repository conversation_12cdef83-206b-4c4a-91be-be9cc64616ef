<template>
  <a-modal v-model="visible" :title="isEdit ? '编辑' : '新增'" @ok="close">
    <a-form-model :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol" ref="form">
      <template v-if="!isEdit">
        <a-form-model-item label="上级组织名称">
          <a-input v-model="form.parentName" disabled /> </a-form-model-item></template>
      <a-form-model-item label="组织名称" prop="name">
        <a-input v-model="form.name" />
      </a-form-model-item>
      <template v-if="isEdit">
        <a-form-model-item label="组织编码">
          <a-input v-model="form.code" disabled />
        </a-form-model-item>
        <a-form-model-item label="有效开始时间">
          <a-date-picker v-model="form.beginDate" valueFormat="YYYY-MM-DD" placeholder="选择开始时间" />
        </a-form-model-item>
        <a-form-model-item label="结束时间">
          <a-date-picker v-model="form.endDate" valueFormat="YYYY-MM-DD" placeholder="选择结束时间" />
        </a-form-model-item>
      </template>
      <a-form-model-item required label="层级描述" prop="levelName">
        <a-select v-model="form.levelName" :dropdownMatchSelectWidth="false" style="width: 100%"
          @change="handleLevelChange">
          <a-select-option :value="zitem.key" v-for="zitem in dict['mom-smc2Level']" :key="zitem.key">
            {{ zitem.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="sap编码" prop="sapCode" >
        <a-input v-model="form.sapCode" />
      </a-form-model-item>
      <a-form-model-item label="dhr编码" prop="dhrCode" v-if="form.levelName !== '工段'">
        <a-input v-model="form.dhrCode" />
      </a-form-model-item>
      <a-form-model-item label="视角" prop="angleOfView">
        <a-select v-model="form.angleOfView" multiple mode="multiple" placeholder="请选择视角">
          <a-select-option value="管理视角">管理视角</a-select-option>
          <a-select-option value="制造视角">制造视角</a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
    <template slot="footer">
      <a-button key="back" @click="close">
        取消
      </a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="submit">
        提交
      </a-button>
    </template>
  </a-modal>
</template>
<script>
import { _ } from "core-js";
import request from "../../../utils/requestHttp";
export default {
  props: {
    activeOrgTreeId: Number
  },
  data() {
    return {
      visible: false, // 弹框控制
      isEdit: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      dict: {},
      form: {
        code: "",
        name: "",
        parentName: "",
        invalid: "",
        beginDate: "",
        endDate: "",
        levelName: "",
        sapCode: undefined,
        dhrCode: undefined,
        angleOfView: undefined
      },
      rules: {
        name: [{ required: true, trigger: "blur", message: "请输入组织名称" }],
        levelName: [{ required: true, trigger: "blur", message: "请选择层级描述" }],
        // sapCode: [{ required: true, trigger: "blur", message: "请输入sap编码" }],
        // dhrCode: [{ required: true, trigger: "blur", message: "请输入dhr编码" }],
      },
      loading: false,
      changeFlag: false
    };
  },
  methods: {
    show(data, isEdit = false) {
      this.visible = true;
      this.isEdit = isEdit;
      this.form = { ...data };
      this.form.angleOfView = data.angleOfView ? data.angleOfView.split(',') : undefined;
      this.form.id = data.id
      this.getDICT();
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=mom-smc2Level&languageCode=zh_CN"
        )
      ).then(res => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    close() {
      this.form = {
        code: "",
        name: "",
        parentName: "",
        invalid: "",
        beginDate: "",
        endDate: "",
        levelName: "",
        levelCode: "",
        sapCode: "",
        dhrCode: "",
        angleOfView: "",
        fullName: "",
        parentFullName: "",
        id: ""
      };
      this.visible = false;
      this.$refs.form && this.$refs.form.clearValidate();
    },
    getLevelCode(levelName) {
      const levelMap = {
        '集团': 1,
        '公司': 2,
        '中心': 3,
        '基地': 4,
        '工厂': 5,
        '部门': 6,
        '科室': 7,
        '车间': 8,
        '二级车间': 9,
        '线体': 10,
        '二级线体': 11,
        '班组': 12,
        '大班组': 13,
        '小班组': 14,
        '班次': 15,
        '工段': 16,
        '分厂': 17
      };
      return levelMap[levelName] || 0;
    },
    handleLevelChange(value) {
      if (value !== '线体') {
        this.form.sapCode = undefined;
      }
    },
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            // 编辑
            const _this = this;
            this.$confirm({
              title: "是否保存修改？",
              onOk() {
                return new Promise(resolve => {
                  // const arr = _this.form.fullCode.split("-");
                  // const postFullCode = arr.slice(0, arr.length - 1).join("-");
                  request(`/api/smc2/treeOrg/updateCmTree`, {
                    method: "POST",
                    body: {
                      code: _this.form.code,
                      fullCode: _this.form.fullCode,
                      name: _this.form.name,
                      levelName: _this.form.levelName,
                      levelCode: _this.getLevelCode(_this.form.levelName),
                      beginDate: _this.form.beginDate,
                      endDate: _this.form.endDate,
                      sapCode: _this.form.sapCode,
                      dhrCode: _this.form.dhrCode,
                      angleOfView: _this.form.angleOfView ? _this.form.angleOfView.join(',') : undefined,
                      fullName: _this.form.fullName,
                      parentFullName: _this.form.parentFullName,
                      hid:_this.form.hid,
                      id:_this.form.id
                    }
                  }).then(res => {
                    if (res) {
                      _this.$message.error(res.result);
                    } else {
                      _this.close();
                      _this.$emit("ok", "edit");
                    }
                    resolve();
                  });
                });
              },
              onCancel() { }
            });
          } else {
            // 新增
            request(`/api/smc2/treeOrg/saveTree1`, {
              method: "POST",
              body: {
                list: [
                  {
                    code: "",
                    superiorCode: this.form.parentDhrCode,
                    name: this.form.name,
                    levelName: this.form.levelName,
                    beginDate: this.form.beginDate,
                    endDate: this.form.endDate,
                    levelCode: this.getLevelCode(this.form.levelName),
                    sapCode: this.form.sapCode,
                    dhrCode: this.form.dhrCode,
                    angleOfView: this.form.angleOfView ? this.form.angleOfView.join(',') : undefined
                  }
                ],
                id: this.form.id
              }
            }).then(res => {
              if (res) {
                this.$message.error('新增失败');
              } else {
                this.close();
                this.$emit("ok", "add");
              }
            }).catch(err => {
              this.$message.error('新增失败');
            })
          }
        }
      });
    },
  },
};
</script>
