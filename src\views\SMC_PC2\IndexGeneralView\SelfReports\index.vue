<template>
  <div
    class="indexGeneralView2-SelfReports"
    :class="[skinStyle()]"
    @keydown.27="close"
    @click="close"
    v-show="visible"
  >
    <div @click.stop="visible = true">
      <div class="empt-div"></div>
      <div class="fill-div">
        <div class="box-title">
          您的专属快报
          <template v-if="skinStyle().includes('classic-style')">
            <a-icon
              type="close-circle"
              style="font-size: 24px; margin-left: 20px;"
              @click.stop="close"
            />
          </template>
          <template v-else>
            <div class="close" @click.stop="close">×</div>
          </template>
        </div>
        <div class="report">
          <div class="_l">
            <div class="_t">
              <span>KPI快报</span>
              <template v-if="skinStyle().includes('classic-style')">
                <a-radio-group
                  v-model="frequency"
                  @change="getReportLeftDetail"
                  button-style="solid"
                >
                  <a-radio-button
                    v-for="item in timeTypeOptions"
                    :value="item.key"
                    :key="item.key"
                  >
                    {{ item.value }}
                  </a-radio-button>
                </a-radio-group>
              </template>
              <template v-else>
                <div class="timetyep-radio-group _flex">
                  <div
                    :value="item.key"
                    v-for="item in timeTypeOptions"
                    :key="item.key"
                    @click="
                      frequency = item.key;
                      getReportLeftDetail();
                    "
                    :class="frequency === item.key ? 'active' : ''"
                  >
                    {{ item.value }}
                  </div>
                </div>
              </template>
            </div>
            <div class="_b">
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton
                    active
                    :paragraph="{
                      rows: skinStyle().includes('classic-style') ? 3 : 2
                    }"
                  />
                </template>
                <template v-else>
                  <div class="title">您可订阅KPI数量</div>
                  <div class="num">{{ reportInfo["可订阅KPI数量"] || 0 }}</div>
                </template>
              </div>
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton
                    active
                    :paragraph="{
                      rows: skinStyle().includes('classic-style') ? 3 : 2
                    }"
                  />
                </template>
                <template v-else>
                  <div class="title">您已订阅KPI数量</div>
                  <div class="num">{{ reportInfo["已订阅KPI数量"] || 0 }}</div>
                </template>
              </div>
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton
                    active
                    :paragraph="{
                      rows: skinStyle().includes('classic-style') ? 3 : 2
                    }"
                  />
                </template>
                <template v-else>
                  <div class="title">订阅KPI未达标数量</div>
                  <div class="num special">
                    {{ reportInfo["订阅KPI未达标数量"] || 0 }}
                  </div>
                  <a-button
                    type="link"
                    size="small"
                    @click.stop="getSpeicalCardList('0')"
                    >查看</a-button
                  >
                </template>
              </div>
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton
                    active
                    :paragraph="{
                      rows: skinStyle().includes('classic-style') ? 3 : 2
                    }"
                  />
                </template>
                <template v-else>
                  <div class="title">未达标同环比恶化</div>
                  <div class="num special">
                    {{ reportInfo["未达标同环比恶化"] || 0 }}
                  </div>
                  <a-button
                    type="link"
                    size="small"
                    @click.stop="getSpeicalCardList('1')"
                    >查看</a-button
                  >
                </template>
              </div>
            </div>
          </div>
          <div class="_r">
            <div class="_t">
              <span>使用快报</span>
              <template v-if="skinStyle().includes('hisense-style')">
                <div class="time">截止{{ nowTime }}日</div>
              </template>
            </div>
            <div class="_b">
              <template v-if="skinStyle().includes('classic-style')">
                <div class="_top">截止{{ nowTime }}日：</div>
              </template>
              <div class="list">
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    本<span>月</span>登录云图平台<span>{{
                      reportInfo["月登陆"] || 0
                    }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    本<span>周</span>登录云图平台<span>{{
                      reportInfo["周登陆"] || 0
                    }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    主动订阅<span>预警</span>邮件<span>{{
                      reportInfo["预警订阅"] || 0
                    }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    共收到<span>预警邮件{{ reportInfo["预警邮件"] || 0 }}</span
                    >封
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    发起一键沟通<span>{{ reportInfo["一键沟通"] || 0 }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    发起督办<span>{{ reportInfo["督办"] || 0 }}</span
                    >次，<span>{{ reportInfo["督办未关闭"] || 0 }}</span
                    >次未关闭
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span class="yt-suggest"
          ><span>云图建议：</span>{{ reportInfo["云图建议"] }}</span
        >
      </div>
    </div>
  </div>
</template>
<script>
import ArrowRight from "@/assets/images/icon-arrow-right.png";
import { covertDate } from "@/utils/utils.js";
import request from "../../../../utils/requestHttp";
export default {
  props: {
    companyName: String,
    signOrgId: String
  },
  inject: ["timeMap", "skinStyle"],
  data() {
    return {
      visible: false,
      frequency: "month",
      ArrowRight,
      leftDataLodaing: false,
      rightDataLodaing: false,
      reportInfo: {},
      timeTypeOptionInDict: [
        { key: "D", value: "日" },
        { key: "Q", value: "季" },
        { key: "HF", value: "半年" },
        { key: "Y", value: "年" },
        { key: "M", value: "月" },
        { key: "W", value: "周" }
      ],
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: "month",
          value: "月"
        },
        {
          key: "week",
          value: "周"
        },
        {
          key: "day",
          value: "日"
        }
      ]
    };
  },
  computed: {
    nowTime() {
      return covertDate(new Date().getTime(), 2);
    }
  },
  methods: {
    async show() {
      this.visible = true;
      this.getReportLeftDetail();
      this.getReportRightDetail();
    },
    close() {
      this.visible = false;
      localStorage.setItem(
        `${this.companyName}IndexGeneralSeltReports`,
        `${covertDate(new Date().getTime(), 0)}`
      );
    },
    // 获取指标详情
    getReportLeftDetail() {
      this.leftDataLodaing = true;
      request(`/api/smc2/exclusiveReport/searchExclusiveExpress`, {
        method: "POST",
        body: {
          sign: `${this.companyName}概览`,
          signOrgId: this.signOrgId,
          indexFrequencyId: this.timeTypeOptionInDict.filter(
            item => item.value === this.timeMap[this.frequency]
          )[0].key
        }
      }).then(res => {
        this.reportInfo = { ...this.reportInfo, ...(res || {}) };
        this.leftDataLodaing = false;
      });
    },
    getReportRightDetail() {
      this.rightDataLodaing = true;
      request(`/api/smc2/exclusiveReport/searchExclusiveExpress1`, {
        method: "POST",
        body: {
          sign: `${this.companyName}概览`,
          signOrgId: this.signOrgId,
          indexFrequencyId: this.timeTypeOptionInDict.filter(
            item => item.value === this.timeMap[this.frequency]
          )[0].key
        }
      }).then(res => {
        this.reportInfo = { ...this.reportInfo, ...(res || {}) };
        this.rightDataLodaing = false;
      });
    },
    // 筛选过滤卡片
    getSpeicalCardList(type) {
      this.$emit("change", {
        frequency: this.frequency,
        type
      });
      this.close();
    }
  }
};
</script>
<style lang="less">
.indexGeneralView2-SelfReports {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.35);
  z-index: 55;
  display: flex;
  align-items: center;
  justify-content: center;
  &.hisense-style {
    &.dark {
      & > div {
        background-color: #222425;
        .report {
          ._l {
            ._t {
              .timetyep-radio-group {
                border-color: #a1a6ac;
              }
            }
            ._b {
              & > div {
                border-color: #a1a6ac;
                .num {
                  color: #e2e8ea;
                }
                .ant-btn {
                  color: #e2e8ea;
                }
              }
            }
          }
          ._r {
            ._b {
              .list {
                border-radius: 16px;
                background-color: #313335;
                & > div {
                  color: #e2e8ea;
                }
              }
            }
          }
        }
        .yt-suggest {
          color: #a1a6ac;
        }
      }
    }
    & > div {
      width: 960px;
      height: 560px;
      transform: scale(0.9);
      background-color: #fff;
      .box-title {
        height: 50px;
        line-height: 50px;
        text-align: center;
        color: #fff;
        background: #00aaa6;
        font-size: 18px;
        position: relative;
        .close {
          position: absolute;
          width: 50px;
          height: 50px;
          right: 0;
          text-align: center;
          top: 50%;
          transform: translateY(-25px);
          line-height: 50px;
          font-size: 32px;
          cursor: pointer;
        }
      }
      .report {
        margin-bottom: 24px;
        padding: 32px 30px 0 30px;
        ._l {
          margin-bottom: 34px;
          ._t {
            .timetyep-radio-group {
              margin-right: 16px;
              padding: 6px;
              height: 32px;
              color: #a9aeb8;
              font-size: 14px;
              box-sizing: border-box;
              border-radius: 3px;
              border: 1px solid #e5e6eb;
              & > div {
                height: 24px;
                padding: 0 10px;
                line-height: 24px;
                border-radius: 3px;
                cursor: pointer;
                &.active {
                  background-color: #00aaa6;
                  color: #fff;
                }
              }
            }
          }
          ._b {
            margin-left: 90px;
            display: flex;
            align-items: center;
            & > div {
              &:not(:last-child) {
                margin-right: 24px;
              }
              width: 180px;
              height: 120px;
              border-radius: 4px;
              border: 1px solid #e5e6eb;
              display: flex;
              flex-direction: column-reverse;
              align-items: center;
              justify-content: center;
              position: relative;
              .num {
                font-weight: 500;
                height: 20px;
                font-size: 26px;
                line-height: 20px;
                color: #00aaa6;
                margin-bottom: 16px;
              }
              .title {
                height: 20px;
                font-size: 14px;
                line-height: 20px;
                color: #6b7785;
              }
              .ant-btn {
                position: absolute;
                right: 0;
                bottom: 0;
              }
            }
          }
        }
        ._r,
        ._l {
          ._t {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            & > span {
              width: 90px;
              display: block;
              font-size: 16px;
              color: #00aaa6;
            }
          }
        }
        ._r {
          ._t {
            .time {
              color: #a9aeb8;
              font-size: 14px;
            }
          }
          ._b {
            margin-left: 90px;
            .list {
              display: flex;
              width: 796px;
              height: 96px;
              background: #f7f8fa;
              border-radius: 4px;
              flex-wrap: wrap;
              & > div {
                box-sizing: border-box;
                padding-left: 32px;
                width: calc(100% / 3);
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #1d2129;
                img {
                  display: none;
                }
                span {
                  color: #00aaa6;
                  margin: 0 5px;
                }
              }
            }
          }
        }
      }
      .yt-suggest {
        padding-left: 120px;
        font-size: 14px;
        color: #6e7785;
        span {
          color: #f83a3a;
        }
      }
    }
  }
  &.classic-style {
    & > div {
      background-color: #fff;
      border-radius: 3px;
      overflow: hidden;
      // height: 80%;
      width: 70%;
      min-height: 550px;
      position: relative;
      transform: scale(0.8);
      .empt-div {
        border: 20px solid gainsboro;
      }
      .empt-div,
      .fill-div {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
      .fill-div {
        padding: 20px;
        z-index: 2;
        .box-title {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 0;
          font-size: 24px;
          background-color: #fff;
          z-index: 9;
          padding: 5px 70px;
          font-weight: bold;
        }
        .report {
          padding-top: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          & > div {
            width: 50%;
            min-height: 350px;
            &._l {
              border-right: 1px solid rgba(0, 0, 0, 0.35);
              ._t {
                & > span {
                  margin-right: 120px;
                }
              }
              ._b {
                display: flex;
                flex-wrap: wrap;
                & > div {
                  height: 130px;
                  overflow: hidden;
                  margin-right: 20px;
                  width: calc(50% - 20px);
                  border: none;
                  background: #f4f5f7;
                  border-radius: 12px;
                  box-sizing: border-box;
                  padding: 20px;
                  margin-bottom: 20px;
                  position: relative;
                  .title {
                    text-align: center;
                    margin-bottom: 15px;
                  }
                  .num {
                    text-align: center;
                    font-size: 36px;
                    &.special {
                      color: #00aaa6;
                    }
                  }
                  button {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                  }
                  .ant-skeleton-content .ant-skeleton-title {
                    margin-top: 0;
                  }
                  .ant-skeleton-content
                    .ant-skeleton-title
                    + .ant-skeleton-paragraph {
                    margin-top: 12px;
                  }
                  .ant-skeleton-content .ant-skeleton-paragraph > li + li {
                    margin-top: 12px;
                  }
                }
              }
            }
            &._l,
            &._r {
              box-sizing: border-box;
              padding: 0 40px;
              ._t {
                padding-top: 20px;
                margin-bottom: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                & > span {
                  font-size: 18px;
                  font-weight: bold;
                }
              }
            }
            &._r {
              ._b {
                ._top {
                  font-weight: bold;
                  margin-bottom: 20px;
                  font-size: 14px;
                  margin-bottom: 20px;
                }
                .list {
                  & > div {
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    img {
                      display: block;
                      width: 30px;
                      height: 30px;
                      margin-right: 15px;
                    }
                    div {
                      span {
                        color: red;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .yt-suggest {
        font-size: 16px;
        display: block;
        text-align: center;
        padding: 20px;
        text-shadow: 0.1rem 0.1rem 0.2rem rgba(0, 0, 0, 0.15);
      }
    }
  }
}
</style>
