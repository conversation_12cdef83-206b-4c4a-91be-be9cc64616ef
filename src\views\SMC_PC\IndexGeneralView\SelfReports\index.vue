<template>
  <div
    class="indexGeneralView-SelfReports"
    @keydown.27="close"
    @click="close"
    v-show="visible"
  >
    <div @click.stop="visible = true" style="transform: scale(0.8);">
      <div class="empt-div"></div>
      <div class="fill-div">
        <div class="box-title">
          您的专属快报<a-icon
            type="close-circle"
            style="font-size: 24px; margin-left: 20px;"
            @click.stop="close"
          />
        </div>
        <div class="report">
          <div class="_l">
            <div class="_t">
              <span style="margin-right: 120px;">KPI快报</span>
              <a-radio-group
                v-model="frequency"
                @change="getReportDetail"
                button-style="solid"
              >
                <a-radio-button value="month">
                  月
                </a-radio-button>
                <a-radio-button value="week">
                  周
                </a-radio-button>
                <a-radio-button value="day">
                  日
                </a-radio-button>
              </a-radio-group>
            </div>
            <div class="_b">
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton active />
                </template>
                <template v-else>
                  <div class="title">您可订阅KPI数量</div>
                  <div class="num">{{ reportInfo.sumIndex || 0 }}</div>
                </template>
              </div>
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton active />
                </template>
                <template v-else>
                  <div class="title">您已订阅KPI数量</div>
                  <div class="num">{{ reportInfo.alreadyIndex || 0 }}</div>
                </template>
              </div>
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton active />
                </template>
                <template v-else>
                  <div class="title">订阅KPI未达标数量</div>
                  <div class="num special">
                    {{ reportInfo.notComplete || 0 }}
                  </div>
                  <a-button
                    type="link"
                    size="small"
                    @click.stop="getSpeicalCardList('0')"
                    >查看</a-button
                  >
                </template>
              </div>
              <div>
                <template v-if="leftDataLodaing">
                  <a-skeleton active />
                </template>
                <template v-else>
                  <div class="title">未达标同环比恶化</div>
                  <div class="num special">
                    {{ reportInfo.deteriorate || 0 }}
                  </div>
                  <a-button
                    type="link"
                    size="small"
                    @click.stop="getSpeicalCardList('1')"
                    >查看</a-button
                  >
                </template>
              </div>
            </div>
          </div>
          <div class="_r">
            <div class="_t">
              <span>使用快报</span>
            </div>
            <div class="_b">
              <div class="_top">截止{{ nowTime }}日：</div>
              <div class="list">
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    本<span>月</span>登录云图平台<span>{{
                      reportInfo.loginMonth || 0
                    }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    本<span>周</span>登录云图平台<span>{{
                      reportInfo.loginWeek || 0
                    }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    主动订阅<span>预警</span>邮件<span>{{
                      reportInfo.alreadtMail || 0
                    }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    共收到<span>预警邮件{{ reportInfo.receiveMail || 0 }}</span
                    >封
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    发起一键沟通<span>{{ reportInfo.talk || 0 }}</span
                    >次
                  </div>
                </div>
                <div>
                  <img :src="ArrowRight" alt="" srcset="" />
                  <div>
                    发起督办<span>{{ reportInfo.startDb || 0 }}</span
                    >次，<span>{{ reportInfo.notEndDb || 0 }}</span
                    >次未关闭
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span
          style="font-size: 16px; display:block; text-align:center;padding: 20px;text-shadow: 0.1rem 0.1rem 0.2rem rgb(0 0 0 / 15%);
"
          >云图建议：{{ reportInfo.proposal }}</span
        >
      </div>
    </div>
  </div>
</template>
<script>
import ArrowRight from "@/assets/images/icon-arrow-right.png";
import { covertDate } from "@/utils/utils.js";
import request from "../../../../utils/requestHttp";
export default {
  props: {
    companyName: String
  },
  data() {
    return {
      visible: false,
      frequency: "month",
      ArrowRight,
      leftDataLodaing: false,
      rightDataLodaing: false,
      reportInfo: {}
    };
  },
  computed: {
    nowTime() {
      return covertDate(new Date().getTime(), 2);
    }
  },
  methods: {
    show() {
      this.visible = true;
      this.getReportDetail();
    },
    close() {
      this.visible = false;
      localStorage.setItem(
        `${this.companyName}IndexGeneralSeltReports`,
        `${covertDate(new Date().getTime(), 0)}`
      );
    },
    // 获取指标详情
    getReportDetail(e) {
      if (!e) {
        this.rightDataLodaing = true;
      }
      this.leftDataLodaing = true;
      request(
        `/api/smc/exclusiveReport/getExclusiveReport?company=${
          this.companyName
        }&frequency=${
          this.frequency === "month"
            ? "月"
            : this.frequency === "week"
            ? "周"
            : "日"
        }`
      ).then(res => {
        this.reportInfo = res || {};
        this.leftDataLodaing = false;
        this.rightDataLodaing = false;
      });
    },
    // 筛选过滤卡片
    getSpeicalCardList(type) {
      this.$emit("change", {
        frequency: this.frequency,
        type
      });
      this.close();
    }
  }
};
</script>
<style lang="less">
.indexGeneralView-SelfReports {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.35);
  z-index: 55;
  display: flex;
  align-items: center;
  justify-content: center;
  & > div {
    background-color: #fff;
    border-radius: 3px;
    overflow: hidden;
    // height: 80%;
    width: 70%;
    min-height: 550px;
    position: relative;
    .empt-div {
      border: 20px solid gainsboro;
    }
    .empt-div,
    .fill-div {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }
    .fill-div {
      padding: 20px;
      z-index: 2;
      .box-title {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 0;
        font-size: 24px;
        background-color: #fff;
        z-index: 9;
        padding: 5px 70px;
        font-weight: bold;
      }
      .report {
        padding-top: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        & > div {
          width: 50%;
          min-height: 350px;
          &._l {
            border-right: 1px solid rgba(0, 0, 0, 0.35);
            ._b {
              display: flex;
              flex-wrap: wrap;
              & > div {
                height: 130px;
                overflow: hidden;
                margin-right: 20px;
                width: calc(50% - 20px);
                border: none;
                background: #f4f5f7;
                border-radius: 12px;
                box-sizing: border-box;
                padding: 20px;
                margin-bottom: 20px;
                position: relative;
                .title {
                  text-align: center;
                  margin-bottom: 15px;
                }
                .num {
                  text-align: center;
                  font-size: 36px;
                  &.special {
                    color: #00aaa6;
                  }
                }
                button {
                  position: absolute;
                  right: 0;
                  bottom: 0;
                }
                .ant-skeleton-content .ant-skeleton-title {
                  margin-top: 0;
                }
                .ant-skeleton-content
                  .ant-skeleton-title
                  + .ant-skeleton-paragraph {
                  margin-top: 12px;
                }
                .ant-skeleton-content .ant-skeleton-paragraph > li + li {
                  margin-top: 12px;
                }
              }
            }
          }
          &._l,
          &._r {
            box-sizing: border-box;
            padding: 0 40px;
            ._t {
              padding-top: 20px;
              margin-bottom: 30px;
              display: flex;
              align-items: center;
              justify-content: center;
              & > span {
                font-size: 18px;
                font-weight: bold;
              }
            }
          }
          &._r {
            ._b {
              ._top {
                font-weight: bold;
                margin-bottom: 20px;
                font-size: 14px;
                margin-bottom: 20px;
              }
              .list {
                & > div {
                  display: flex;
                  align-items: center;
                  margin-bottom: 12px;
                  img {
                    display: block;
                    width: 30px;
                    height: 30px;
                    margin-right: 15px;
                  }
                  div {
                    span {
                      color: red;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
