/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Outlines=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/group/group.entry.js")}({"./dist/plugins/group/group-actions.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),b.Commands=e.Commands,f=e.Commands.ActionBase,g=e.GC$,h="outlineColumn",i="removeColumnOutline",j="expandColumnOutline",k="expandColumnOutlineForLevel",l="outlineRow",m="removeRowOutline",n="expandRowOutline",o="expandRowOutlineForLevel",p=d.Common.j.Fa;function E(a){var b=a.kj;return a.ML?b.rowOutlines:b.columnOutlines}function F(a){var b,c,d,e,f,g=this,h=!1;return(a?g.canExecute():g.canUndo())&&(b=g.kj,c=g.VQ.index,d=g.VQ.count,e=E(g),b&&e&&!p(c)&&!p(d)&&(f=a?e.group:e.ungroupRange,g.Lz(b,!0),f.call(e,c,d),g.Mz(b,!0),h=!0)),h}q=function(a){D(b,a);function b(b,c,d){var e=a.call(this,b,c)||this;return e.ML=d,e}return b.prototype.execute=function(){return F.call(this,!0)},b.prototype.undo=function(){return F.call(this,!1)},b}(f),r=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!1)||this}return b}(q),s=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!0)||this}return b}(q),t=function(a){D(b,a);function b(b,c,d){var e=a.call(this,b,c)||this;return e.ML=d,e}return b.prototype.execute=function(){var a,b,c,d,f,g=this,h=g.kj,i=h.ITa,j=!1;return g.canExecute()&&(a=g.VQ,b=a.index,c=a.count,d=E(g),h&&d&&!p(b)&&!p(c)&&(i.startTransaction(),g.Lz(h,!0),d.ungroupRange(b,c),g.Mz(h,!0),f=e.Commands.bWa(h.name()),a[f]=i.endTransaction(),j=!0)),j},b.prototype.undo=function(){var a,b,c=this,d=!1,f=c.kj;return c.canUndo()&&(a=e.Commands.bWa(f.name()),b=c.VQ[a],f&&b&&(c.Lz(f,!0),f.ITa.undo(b),c.Mz(f,!0),d=!0)),d},b}(f),u=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!1)||this}return b}(t),v=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!0)||this}return b}(t);function G(a,b,c,d,e){var f=0,g;for(g=b;g<=c&&g<d;g++)f+=e.call(a,g)*a.zoom();return f}function H(a,b,c,d,e){var f=0,g;for(g=b;g<d&&f<c;g++)b++,f+=e.call(a,g)*a.zoom();return b>=d?d-1:b}function I(a,b,c,d,e){if(b<d)return d;if(b>e)return e;for(var f=b;f<=e;f++)if(c.call(a,f))return f;return-1}function J(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q=this,r=q.kj,s=c.index,t=b?r.getRowCount():r.getColumnCount(),u=void 0;s<0||s>=t||(d=E(q),e=1===d.direction(),f=b?r.frozenRowCount():r.frozenColumnCount(),g=b?r.getRowHeight:r.getColumnWidth,h=b?r.getViewportTopRow:r.getViewportLeftColumn,i=b?r.getViewportHeight:r.getViewportWidth,j=h.call(r,1),n=d.find(e?s-1:s+1,c.level),n&&(o=e?n.start:s,p=e?s:n.end,e?(a?o=s:o<f&&(o=f),o<j&&(j=o),k=i.call(r,1),l=G(r,j,p,t,g),l>k&&(j=H(r,j,l-k,t,g)),m=I(r,j,g,f,t-1)):(a?p=s:p>=t&&(p=t-1),o<j?m=I(r,o,g,f,t-1):(k=i.call(r,1),l=G(r,j,p,t,g),l>k&&(m=I(r,o,g,f,t-1)))),b&&m!==u?(r.lq=m,r.os()):b||m===u||(r.qq=m,r.qs())))}function K(a){var b,c,d,e,f,g,h,i=this,j=!1;return(a?i.canExecute():i.canUndo())&&(b=i.kj,c=i.VQ,d=E(i),e=c.collapsed,f=c.index,g=c.level,!b||!d||p(e)||p(f)||p(g)||(i.Lz(b,!0),a||(e=!e),h=1===d.direction()?d.find(f-1,g):d.find(f+1,g),h&&d.setCollapsed(f,e),J.call(i,e,i.ML,c),i.Mz(b,!0),j=!0)),j}w=function(a){D(b,a);function b(b,c,d){var e=a.call(this,b,c)||this;return e.ML=d,e}return b.prototype.execute=function(){return K.call(this,!0)},b.prototype.undo=function(){return K.call(this,!1)},b}(f),x=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!1)||this}return b}(w),y=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!0)||this}return b}(w),z=function(a){D(b,a);function b(b,c,d){var e=a.call(this,b,c)||this;return e.ML=d,e}return b.prototype.execute=function(){var a,b,c,d,f=this,g=f.kj,h=!1;if(f.canExecute()&&(a=void 0,b=f.VQ.level,c=E(f),g&&c&&!p(b))){for(g.ITa.startTransaction(),f.Lz(g,!0),a=0;a<b;a++)c.expand(a,!0);c.expand(b,!1),f.Mz(g,!0),d=e.Commands.bWa(g.name()),f.VQ[d]=g.ITa.endTransaction(),h=!0}return h},b.prototype.undo=function(){var a,b,c,d=this,f=!1;return d.canUndo()&&(a=d.kj,b=e.Commands.bWa(a.name()),c=d.VQ[b],a&&c&&(d.Lz(a,!0),a.ITa.undo(c),f=!0,d.Mz(a,!0))),f},b}(f),A=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!1)||this}return b}(z),B=function(a){D(b,a);function b(b,c){return a.call(this,b,c,!0)||this}return b}(z),C=e.Commands.h4,e.Commands[h]={canUndo:!0,execute:function(a,b,c){return C(a,r,b,c)}},e.Commands[l]={canUndo:!0,execute:function(a,b,c){return C(a,s,b,c)}},e.Commands[i]={canUndo:!0,execute:function(a,b,c){return C(a,u,b,c)}},e.Commands[m]={canUndo:!0,execute:function(a,b,c){return C(a,v,b,c)}},e.Commands[j]={canUndo:!0,execute:function(a,b,c){return C(a,x,b,c)}},e.Commands[n]={canUndo:!0,execute:function(a,b,c){return C(a,y,b,c)}},e.Commands[k]={canUndo:!0,execute:function(a,b,c){return C(a,A,b,c)}},e.Commands[o]={canUndo:!0,execute:function(a,b,c){return C(a,B,b,c)}},e.Commands.SL=function(a){a.register(h,e.Commands[h]),a.register(l,e.Commands[l]),a.register(i,e.Commands[i]),a.register(m,e.Commands[m]),a.register(j,e.Commands[j]),a.register(n,e.Commands[n]),a.register(k,e.Commands[k]),a.register(o,e.Commands[o])}},"./dist/plugins/group/group.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/group/group-actions.js")),d(c("./dist/plugins/group/group.js")),d(c("./dist/plugins/group/group.ns.js"))},"./dist/plugins/group/group.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("./dist/plugins/group/group-actions.js"),g=c("./dist/plugins/group/group.ns.js"),h={},i=null,j=void 0,k=Math.max,l=Math.min,m=Math.round,n=Math.ceil,o=e.GC$,p=o.each,q=e.Im.Lm,r=d.Common.k,s=r._b,t=r.$b,u=r.ac,v=8.25,w=17,x=4,y=e.Ul.Ol,z=d.Common.j.Fa,A=e.Ul.Pl,B=e.Ul.fp,C="rangegroup",D="gc-group",E=D+"-box",F=E+"-expand",G=E+"-collapsed",H=D+"-line",I=D+"-dot",J="rgh",K="cgh",L="rg",M="cg",N=new d.Common.ResourceManager(g.SR),O=N.getResource.bind(N),P={Gt:function(){var a,b,c,d,e,f,g,h=this,i=h.TL;return i?i:(a=h.options,b=a.sheetAreaOffset,c={x:b.left,y:b.top,width:0,height:0,rowMaxLevel:-1,colMaxLevel:-1},d=h.rowOutlines,e=h.columnOutlines,f=w*h.zoom(),h.showRowOutline()&&d&&!d.iT()&&(g=d.getMaxLevel(),g>=0&&(c.width=f*(g+2)+x,c.rowMaxLevel=g)),h.showColumnOutline()&&e&&!e.iT()&&(g=e.getMaxLevel(),g>=0&&(c.height=f*(g+2)+x,c.colMaxLevel=g)),h.TL=c,c)},showRowOutline:A("showRowOutline",!0,function(){this.$p()}),showColumnOutline:A("showColumnOutline",!0,function(){this.$p()})},o.extend(e.Worksheet.prototype,P);function Z(a,b){a.Wq(e.Events.RangeGroupStateChanging,b)}function $(a,b){a.Wq(e.Events.RangeGroupStateChanged,b)}function _(a,b){var c,d,e,f;a.isEditing&&a.isEditing()||(c=b.info.index,d=b.what===J,e=d?"expandRowOutlineForLevel":"expandColumnOutlineForLevel",f=ba(a,d,-1,c,!0),Z(a,f),f&&f.cancel===!1&&(a.wu().execute({cmd:e,sheetName:a.name(),level:c}),$(a,ba(a,d,-1,c,!1))))}function aa(a,b){var c,d,e,f,g,h,i;a.isEditing&&a.isEditing()||(c=b.what===L,ga(c,a)&&(d=b.info,e=d.index,f=e,g=d.level,h=void 0,i=c?"expandRowOutline":"expandColumnOutline",1===d.lineDirection?e--:e++,h=ba(a,c,e,g,!0),Z(a,h),h&&h.cancel===!1&&(a.wu().execute({cmd:i,sheetName:a.name(),index:f,level:g,collapsed:d.isExpanded}),$(a,ba(a,c,e,g,!1)))))}function ba(a,b,c,d,e){var f={sheet:a,sheetName:a.name(),isRowGroup:b,index:c,level:d};return e&&(f.cancel=!1),f}function ca(a,b){var c,d,f,g,h=this,j=h.Gt(),k=j.width,l=j.height;return k||l?(c=h.am(),d=new e.Rect(j.x,j.y,k,c.height),f=new e.Rect(j.x,j.y,c.width,l),d.contains(a,b)||f.contains(a,b)?(g=da(h,a,b,j.rowMaxLevel,!0),g||(g=da(h,a,b,j.colMaxLevel,!1)),g||{what:"empty",info:i}):i):i}Q=function(){function a(a,b,c){var d=this;d.PADDING=2,d.kj=a,d.ML=b,d.ZL=y(c)?c:ga(b,a).getMaxLevel()}return a.prototype._L=function(a){var b,c,d,e,f,g,h,i,j,l,n,o,p,r,s,t,u,w,x,y,z,A,C,F,G,H,I,J=this,K=J.kj,L=J.ZL;if(!(L<0)&&(b=J.ML,c=K.Gt(),d=fa.call(J,c,b),d)){if(e=L+2,f=J.PADDING,g=K.options,h=c.width,i=c.height,j=c.x,l=c.y,n=K.am(),o=n.width,p=n.height,r=n.Cr,s=n.Br,t=n.zr,u=n.Ar,a.save(),a.fillStyle=q(D).backgroundColor,a.strokeStyle=q(D).borderTopColor,a.lineWidth=1,a.fillRect(j,l,b?h:o,b?p:i),a.strokeRect(j-.5,l-.5,b?h:o+1,b?p+1:i),b?a.strokeRect(j-.5,u-.5,h,r):a.strokeRect(t-.5,l-.5,s,i),w=(b?r:s)-d,y=k(0,(b?u-g.sheetAreaOffset.top:t-g.sheetAreaOffset.left)+w/2),z=f,b&&(C=y,y=z,z=C),a.restore(),x=b?g.colHeaderVisible:g.rowHeaderVisible,a.save(),x&&w>=0)for(A=0;A<e;A++)F=d-1,G=void 0,H=j+y,I=l+z,b?H=m(H)+.5:I+=.5,a.fillStyle=q(E).backgroundColor,a.fillRect(H,I,F,F),a.font=v*K.zoom()+"pt Arial",a.strokeStyle=q(E).borderTopColor,a.strokeRect(H,I,F,F),a.fillStyle=q(E).color,G=B(a.font),J.pM(a,H,I,F,d,A+1,G),b?y+=d:z+=d;a.restore()}},a.prototype.pM=function(a,b,c,d,e,f,g){var h=""+f,i=a.measureText(h).width;i<=d&&g<=e&&(a.textBaseline="middle",a.fillText(h,b+(d-i)/2,c+d/2))},a.prototype.WL=function(a,b,c){var d,e,f,g,h=this,j=h.kj,k=h.ZL;return k<0?i:(d=k+2,e=j.am(),f=j.options,g=f.sheetAreaOffset,c?h.qM(j,a,b,c,f.colHeaderVisible,e.Cr,e.Ar-g.top,d):h.qM(j,a,b,c,f.rowHeaderVisible,e.Br,e.zr-g.left,d))},a.prototype.qM=function(a,b,c,d,f,g,h,j){var l,m,n,o,p,q=this,r=a.Gt(),s=fa.call(q,r,d),t=g-s;if(s&&f&&t>=0)for(l=k(0,h+t/2),m=q.PADDING,d&&(p=l,l=m,m=p),o=0;o<j;o++){if(n=new e.Rect(r.x+l,r.y+m,s,s),n.contains(b,c))return{index:o};d?l+=s:m+=s}return i},a}(),R=function(){function a(a,b,c,d){var e=this;e.PADDING=2,e.LINE_SIZE=2,e.START_LINE_SIZE=6,e.bM=[],e.cM=[],e.dM=[],e.kj=a,e.ML=b,e.eM=c,e.ZL=y(d)?d:ga(b,a).getMaxLevel()}return a.prototype.YL=function(){var a,b=this;b.ZL!==-1&&(a=b.fM(0,b.ML),p(a,function(a,c){b.gM(c)}))},a.prototype.gM=function(a){var b,c,d,e,f,g,h,j,m,n,o,q,r,s,t,v,w,x,y=this,z=y.kj,A=y.ML,B=ga(A,z).direction(),C=1===B,D=0===B,E=y.hM(!0,A),F=y.hM(!1,A),G=a.start,H=a.end,I=a.level;if(C?b=H+1:D&&(b=G-1),0===a.state()){if(c=!0,d=a.parent,d&&(D&&G===d.start||C&&H===d.end)&&(c=!1),c&&y.iM(!0,I,b,E,F,B),G<=F&&H>=E){for(e=k(E,G),f=l(F,H),g=void 0,h=void 0,c&&(g={start:e,end:f,level:I,startLine:i},(C&&e===G||D&&f===H)&&(g.startLine=!0),y.bM.push(g)),m=[],n=a.children,h=e;h<=f;h++)m.push(!1);for(p(n,function(a,b){var c=b.start,d=b.end,f;if(1===b.state())for(j=c;j<=d;j++)m[j-e]=!0;f=C?d+1:d-1,m[f-e]=!0,y.gM(b)}),o=!0,h=0;h<u(n);h++){if(q=n[h],r=q.start,s=q.end,t=r===G,v=s===H,t&&v){o=!1;break}if(C&&v||D&&t)for(j=r;j<=s;j++)m[j-e]=!0}if(o)for(w=e;w<=f;w++)m[w-e]||(x={index:w,level:I+1},y.cM.push(x))}}else 1===a.state()&&y.iM(!1,I,b,E,F,B)},a.prototype.iM=function(a,b,c,d,e,f){var g={isExpanded:a,level:b};a&&(g.paintLine=!0),c>=d&&c<=e&&(g.index=c,g.lineDirection=f,this.dM.push(g))},a.prototype.jM=function(a){var b=this,c=b.kj,d=b.eM;return b.ML?c.Gr(d).findRow(a):c.Hr(d).findCol(a)},a.prototype.aM=function(a){var b,c,d,e,f,g,h,i=this;i.ZL!==-1&&(b=i.kj.Gt(),c=i.ML,d=fa.call(i,b,c),d&&(e=parseInt(q(H).borderTopWidth,10),e=isNaN(e)?2:e,f=m(d/3),g=i.PADDING,h=(d-e)/2+g,a.save(),a.fillStyle=q(D).color,i.kM(a,i.cM,c,b,d,h,e),c?i.D_a(a,i.bM,c,b,d,h,e,f):i.E_a(a,i.bM,c,b,d,h,e,f),i.mM(a,i.dM,c,b,d,h,e,g),a.beginPath(),a.restore()))},a.prototype.kM=function(a,b,c,d,e,f,g){var h,i,j,l=this,m=2,n=l.kj,o=n.options,r=o.sheetAreaOffset,s=r.left,t=r.top;a.save(),a.fillStyle=q(I).color,j=l.bM,p(b,function(b,n){var o,p,q,r,u,v,w=n.index,x=n.level;l.C0a(j,w,x)||(o=l.jM(w),o&&(p=c?o.y:o.x,q=(c?o.height:o.width)-g,q>=0&&(h=p+k(0,q/2),i=n.level*e+f,c?(r=h,h=i+.5,i=r,i-=t):(i+=.5,h-=s),a.strokeStyle="transparent",a.lineWidth=0,u=d.x+h,v=d.y+i,u%1!==0&&(u+=.5),v%1!==0&&(v+=.5),a.strokeRect(u,v,m,m),a.fillRect(u,v,m,m))))}),a.restore()},a.prototype.C0a=function(a,b,c){var d,e=!1,f=u(a);for(d=0;d<f;d++)if(a[d].level===c&&a[d].start<=b&&a[d].end>=b){e=!0;break}return e},a.prototype.D_a=function(a,b,c,d,e,f,g,h){var i,j,l,m,o,p,r,s,t,v,w,x,y,z,A,B,C;for(a.save(),a.strokeStyle=q(H).borderTopColor,a.fillStyle=q(H).borderTopColor,a.lineWidth=g,i=this,j=i.kj,l=ga(c,j).direction(),o=j.options,p=o.sheetAreaOffset,r=p.top,m=0;m<u(b);m++){s=b[m],t=s.start,v=s.end,w=void 0,x=void 0;do w=i.jM(t),t++;while(!w&&t<=v);do x=i.jM(v),v--;while(!x&&v>=t);(w||x)&&(w?x||(x=w):w=x,y=w.y,z=s.level*e+f,A=g,B=k(0,x.y+x.height-w.y),1===l?y+=1:y-=1,y-=r,a.beginPath(),C=d.x+z+A/2,C=n(C),a.moveTo(C,d.y+y),a.lineTo(C,d.y+y+B),a.stroke(),s.startLine&&e/2-2*g>0&&(0===l&&(y=y+B-g-r),y>=w.y-r&&y<x.y+x.height-r&&a.fillRect(n(d.x+z),d.y+y,h,g)))}a.restore()},a.prototype.E_a=function(a,b,c,d,e,f,g,h){var i,j,l,m,o,p,r,s,t,v,w,x,y,z,A,B;for(a.save(),a.strokeStyle=q(H).borderTopColor,a.fillStyle=q(H).borderTopColor,a.lineWidth=g,i=this,j=i.kj,l=ga(c,j).direction(),o=j.options,p=o.sheetAreaOffset,r=p.left,m=0;m<u(b);m++){s=b[m],t=s.start,v=s.end,w=void 0,x=void 0;do w=i.jM(t),t++;while(!w&&t<=v);do x=i.jM(v),v--;while(!x&&v>=t);(w||x)&&(w?x||(x=w):w=x,y=w.x,z=s.level*e+f,A=k(0,x.x+x.width-w.x),1===l&&(y+=1),y-=r,B=d.y+z+g/2,B=n(B),a.beginPath(),a.moveTo(d.x+y,B),a.lineTo(d.x+y+A,B),a.stroke(),s.startLine&&e/2-2*g>0&&(0===l&&(y=y+A-g-r),y>=w.x-r&&y<x.x+x.width-r&&a.fillRect(d.x+y,n(d.y+z),g,h)))}a.restore()},a.prototype.r0a=function(a){var b=q(a).backgroundImage;return"none"===b&&(b=""),b&&(b=b.split(",")[0],b=b.replace(/url\(['"]?([^'"\)]+)['"]?\)/,"$1"),b=b.trim()),b},a.prototype.mM=function(a,b,c,d,e,f,g,h){var i,j,m,o,p,r,s,t,v,w,x,y,z,A,B,C,D,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z=this,$=Z.kj,_=$.options,aa=_.sheetAreaOffset.left,ba=_.sheetAreaOffset.top,ca=q(E).borderTopColor,da=q(E).backgroundColor,ea=q(E).color,fa=q(H).borderTopColor,ga=1,ha=$.vu(),ia=Z.r0a(F),ja=Z.r0a(G);for(ia&&!ha.ko(ia)&&ha.fo(ia),ja&&!ha.ko(ja)&&ha.fo(ja),j=ja&&ha.lo(ja),m=ia&&ha.lo(ia),o=q(F),p=q(G),r=m?o.backgroundColor:"",s=j?p.backgroundColor:"",t=m?o.borderTopColor:"",v=j?p.borderTopColor:"",i=0;i<u(b);i++)a.save(),A=b[i],B=Z.jM(A.index),B?(C=c?B.height:B.width,C<=0?a.restore():(D=k(0,(C-e)/2),I=void 0,J=void 0,K=(c?B.y-ba:B.x-aa)+D,L=A.level*e+h,M=l(e,C),N=e,O=void 0,P=void 0,Q=C-M,c?(R=K,K=L,L=R,S=M,M=N,N=S,O=.5,P=0):(O=0,P=.5),y=A.isExpanded?t:v,a.strokeStyle=y?y:ca,a.lineWidth=ga,x=A.isExpanded?r:s,x?a.fillStyle=x:a.fillStyle=da,a.fillRect(d.x+K,d.y+L,M,N),I=d.x+K+O,J=d.y+L+P,a.strokeRect(I,J,M,N),a.fillStyle=ea,w=A.isExpanded?ia:ja,Z.nM(a,A.isExpanded,w,I,J,M,N),a.restore(),A.paintLine&&Q>0&&(a.save(),a.strokeStyle=fa,K=c?B.y-ba:B.x-aa,L=A.level*e+f,T=0===A.lineDirection,U=c?g:D,V=c?D:g,c?(W=K,K=L,L=W,T&&(L+=D+N,V=Q-D)):T&&(K+=D+M,U=Q-D),a.beginPath(),a.lineWidth=g,X=n(d.x+K+g/2),Y=n(d.y+L+g/2),c?(a.moveTo(X,d.y+L),a.lineTo(X,d.y+L+V-ga)):(z=0,j&&!A.isExpanded&&(z=1),a.moveTo(d.x+K,Y),a.lineTo(d.x+K+O+U+z,Y)),a.stroke(),a.restore()))):a.restore()},a.prototype.nM=function(a,b,c,d,e,f,g){var h,i,j,k=this,l=this.kj,n=l.vu();if(c&&n.ko(c)){h=n.lo(c),i=h.width,j=h.height;try{a.drawImage(h,0,0,i,j,m(d),m(e),f-1,g-1)}catch(a){}}else k.u0a(a,b,d,e,f,g)},a.prototype.u0a=function(a,b,c,d,e,f){var g,h,i=m(e/4),j=m(e/2);b||(g=m(f/4),h=m(f/2),a.fillRect(c+e/2-1,d+g,2,f-h)),a.fillRect(c+i,d+f/2-1,j,2)},a.prototype.fM=function(a,b){var c,d,e,f=this,g=f.kj,h=[],i=-1,j=-1,m=ga(b,g),n=b?g.Gr:g.Hr,o=n.call(g,f.eM),p=u(o);for(p&&(d=o[0],e=o[p-1],i=k(0,(b?d.row:d.col)-1),j=b?l(g.getRowCount(),e.row+2):l(g.getColumnCount(),e.col+2));i<j;)c=m.find(i,a),c?(i=c.end+1,h.push(c)):i++;return h},a.prototype.hM=function(a,b){var c=-1,d=this,e=d.kj,f=d.eM,g=e.frozenRowCount(),h=e.frozenColumnCount(),i=e.frozenTrailingRowCount(),j=e.frozenTrailingColumnCount(),k=e.getRowCount(),l=e.getColumnCount();return 0===f?c=a?0:(b?g:h)-1:1===f?c=a?b?g:h:(b?k-i:l-j)-1:2===f&&(c=a?b?k-i:l-j:(b?k:l)-1),c},a.prototype.oM=function(a){a&&this.kj.CH&&(a.x-=2,a.y-=2,a.width+=4,a.height+=4)},a.prototype.XL=function(a,b,c){var d,f,g,h,j,m,n,o,p,q,r,s,t,v,w,x,y=this,z=y.ZL,A=y.kj,B=A.options,C=B.sheetAreaOffset.left,D=B.sheetAreaOffset.top;if(z<0)return i;if(d=A.Gt(),f=fa.call(y,d,c),!f)return i;for(g=y.dM,m=0;m<u(g);m++)if(n=g[m],o=n.index,p=n.level*f+y.PADDING,q=y.jM(o),q&&(h=c?q.height:q.width,!(h<=0)&&(r=l(f,h),s=f,t=(c?q.y-D:q.x-C)+k(0,(h-f)/2),v=p,c&&(w=r,r=s,s=w,x=t,t=v,v=x),j=new e.Rect(d.x+t+.5,d.y+v+.5,r,s),y.oM(j),j.contains(a,b))))return n;return i},a}();function da(a,b,c,d,e){var f,g,h=new Q(a,e,d),j=h.WL(b,c,e);if(j)return{what:e?J:K,info:j};for(f=ea(a,e,d),g=0;g<=2;g++)if(j=f[g].XL(b,c,e))return{what:e?L:M,info:j};return i}function ea(a,b,c){var d=[new R(a,b,0,c),new R(a,b,1,c),new R(a,b,2,c)],e;for(e=0;e<=2;e++)d[e].YL();return d}function fa(a,b){var c=b?a.width:a.height;return k(0,(c-2*this.PADDING)/(this.ZL+2))}function ga(a,b){return a?b.rowOutlines:b.columnOutlines}!function(a){a[a.backward=0]="backward",a[a.forward=1]="forward"}(S=b.OutlineDirection||(b.OutlineDirection={})),h.OutlineDirection=S,function(a){a[a.expanded=0]="expanded",a[a.collapsed=1]="collapsed"}(T=b.OutlineState||(b.OutlineState={})),h.OutlineState=T,U=function(){function a(a,b,c,d){var e=this;e.children=[],e.parent=i,e.model=a,e.start=b,e.end=c,e.level=d}return a.prototype.state=function(a){var b=this,c=b.model;return arguments.length?void(c&&c.expandGroup(b,0===a)):c?c.getState(b):0},a.prototype.contains=function(a){return this.start<=a&&a<=this.end},a.prototype.addChild=function(a){a&&(this.children.push(a),a.parent=this)},a}(),b.OutlineInfo=U,h.OutlineInfo=U,V=function(){function a(a){this.level=a?a.level:0,this.collapsed=!!a&&a.collapsed,this.viewCollapsed=!!a&&a.viewCollapsed}return a}();function ha(a,b,c){var d,e,f,g;if(c>0){for(d=a.items,e=void 0,f=void 0,g=[],e=0;e<c;e++)g.push(i);if(a.items=d=d.slice(0,b).concat(g,d.slice(b)),a.V3<=0&&b>0&&(f=d[b-1]))for(e=0;e<c;e++)d[b+e]=new V(f);na(a)}}function ia(a,b,c){c>0&&(a.items.splice(b,c),na(a))}function ja(a,b,c){for(var d,e,f,g,h=i,j=a.items,k=b,l=u(j);k<l&&(d=j[k],!d||d.level<c||(h||(h=new U(a,k,k,c)),d.level>c&&(e=ja(a,k,c+1),k=e.index,h.addChild(e.g)),k>h.end&&(h.end=k),!a.isGroupEnd(k,c)));k++);if(h)for(f=h.end+1,g=h.start;g<f;g++)j[g].groupInfo||(j[g].groupInfo=h);return{g:h,index:k}}function ka(a,b){return b>=-1&&b<u(a.items)}function la(a,b,c){var d,e,f=a&&a.items;if(f)for(d=f[b]&&f[b].groupInfo;d;){if(e=d.level,e===c)return d;if(!(e>c))break;d=d.parent}return i}function ma(a,b){switch(a){case"head":case"tail":return b===i;case"direction":return 1===b;case"itemsData":return 0===u(b);default:return!1}}function na(a){a.Zka=i,a.refresh()}function oa(a,b){!b&&a.Wka||(a.Zka=a.createRangeGroup(),qa(a))}function pa(a){a.Wka&&!a.Zka&&(a.Zka=a.createRangeGroup())}function qa(a,b){a.groupChangeHandler&&a.groupChangeHandler(b)}function ra(a,b,c){if(sa(a,b),!ka(a,b+c-1))throw Error(O().Exp_InvalidCount)}function sa(a,b){if(!ka(a,b))throw Error(O().Exp_InvalidIndex)}function ta(a,b){for(var c,d=a.parent;d&&(c=b?a.end===d.end:a.start===d.start);)a=d,d=d.parent;return a}W=function(){function a(a,b,c){var d=this;d.head=i,d.tail=i,d.Zka=i,d.items=Array(a),d.V3=0,d.wz=!0,d.kj=b,d.ML=c}return a.prototype.group=function(a,b){var c=this,d,e;for(ra(c,a,b),c.kj.ITa.vUa(c.ML),d=0;d<b;d++)e=a+d,c.items[e]?c.items[e].level++:c.items[e]=new V;c.Wka&&c.Zka&&(c.Zka=i),oa(c),c.wz=!1},a.prototype.Lia=function(){this.Wka=!0,this.Zka=i},a.prototype.Mia=function(){var a=this;a.Wka=!1,a.refresh()},a.prototype.ungroupRange=function(a,b){var c=this,d,e;for(ra(c,a,b),c.kj.ITa.vUa(c.ML),d=0;d<b;d++)e=c.items[a+d],e&&e.level>-1&&e.level--;oa(c)},a.prototype.ungroup=function(){var a=this,b=u(a.items);a.kj.ITa.vUa(a.ML),a.items=Array(b),oa(a),a.wz=!0},a.prototype.expand=function(a,b){var c,d,e,f,g,h;if(a<-1)throw Error(O().Exp_InvalidLevel);for(c=this,d=c.items,c.kj.ITa.vUa(c.ML),pa(c),f=c.kj.Cf(),f.suspend(),g=0,h=d.length;g<h;g++)d[g]&&(e=c.find(g,a),e&&c.expandGroup(e,b));f.resume(!1)},a.prototype.expandGroup=function(a,b){if(!a)throw Error(O().Exp_GroupInfoIsNull);var c=this,d=c.direction(),e=-1;c.kj.ITa.vUa(c.ML),0===d?e=a.start-1:1===d&&(e=a.end+1),c.setCollapsed(e,!b)},a.prototype.Xr=function(a){return!ka(this,a)||!this.isCollapsed(a)},a.prototype.isCollapsed=function(a){var b=this.items[a];return!!(b&&b.viewCollapsed&&b.level>-1)},a.prototype.find=function(a,b){var c=this,d;return pa(c),b===-1?d=c.Zka:(sa(c,a),d=la(c,a,b)),d},a.prototype.getLevel=function(a){var b=this.items[a];return b?b.level:-1},a.prototype.getCollapsed=function(a){var b=this.items[a];return!!b&&!!b.collapsed},a.prototype.B4=function(a,b,c,d){var e,f;for(e=b;e<=c;e++)f=a[e],f||(f=a[e]=new V,f.level=-1),f.viewCollapsed=d},a.prototype.setCollapsed=function(a,b){var c,d,e,f,g,h,i=this,j=!1,k=i.items;if(i.kj.ITa.vUa(i.ML),pa(i),a<0?(c=i.head,c||(c=i.head=new V)):a<u(k)?(c=k[a],c||(c=k[a]=new V,c.level=-1)):(c=i.tail,c||(c=i.tail=new V)),d=1===i.direction(),c.collapsed!==b&&(c.collapsed=b,j=!0,i.E4(a,b)),j&&(a+=d?-1:1,f=[],g=i.find(a,i.getLevel(a)))){for(h=g.start;h<=g.end;h++)f.push(h);e={indexes:f}}qa(i,e)},a.prototype.dPa=function(a,b){var c,d=this.items;d[a]?d[a].collapsed!==b&&(d[a].collapsed=b):(c=d[a]=new V,c.level=-1,c.collapsed=b)},a.prototype.E4=function(a,b){var c,d,e,f,g,h,i,j,k,l=this,m=l.items,n=1===l.direction(),o=n?-1:1,p=a+o,q=la(l,p,l.getLevel(p));if(q)if(c=void 0,d=q.parent,b)c=n?q.end+1:q.start-1,l.dPa(c,!0),q=ta(q,n),l.B4(m,q.start,q.end,!0);else{for(e=!1,f=void 0;d;){if(f=n?d.end+1:d.start-1,m[f]&&m[f].collapsed&&m[f].level>-1){e=!0;break}d=d.parent}if(!e)for(c=n?q.end+1:q.start-1,l.dPa(c,!1),q=ta(q,n),l.B4(m,q.start,q.end,!1),g=[q],h=0;h>=0;)if(i=g[h],h--,f=n?i.end+1:i.start-1,m[f]&&m[f].collapsed&&m[f].level!==-1)l.B4(m,i.start,i.end,!0);else for(j=i.children,k=0;j&&k<j.length;k++)++h,g[h]=j[k]}},a.prototype.getMaxLevel=function(){var a=-1,b=this.items,c;return p(b,function(b,d){d&&(c=d.level,c>a&&(a=c))}),a},a.prototype.cB=function(a,b,c,d){var e,f,g,h,i;if(!(c<=0||a===b)){for(e=this,e.kj.ITa.vUa(e.ML),a<0&&(a=0),b<0&&(b=0),f=[],g=e.items,h=s(g,a-1);h>=0&&h<a+c;)i=new V(g[h]),f.push({index:h-a,value:i}),h=s(g,h);d&&t(g,a,c),t(g,b,c),p(f,function(a,c){g[b+c.index]=c.value}),na(e)}},a.prototype.bB=function(a,b,c,d,e){var f,g,h=this;if(h.kj.ITa.vUa(h.ML),b<0&&(b=0),c<0&&(c=0),f=[],a)for(g=s(a.items,b-1);g>=0&&g<b+d;)f[g-b]=new V(a.items[g]),g=s(a.items,g);t(h.items,c,d),f.length>0&&f.forEach(function(a,b){h.items[c+b]=a}),e&&t(a.items,b,d)},a.prototype.refresh=function(){this.wz||oa(this)},a.prototype.VL=function(a){var b,c,d=this,e=d.items,f=u(e),g=a-f;if(g<0)e.splice(a,-g);else if(b=e[f-1],c=void 0,b)for(c=0;c<g;c++)e.push(new V(b));else d.items=e.concat(Array(g));na(d)},a.prototype.getState=function(a){var b=this,c=b.items,d,e=b.direction(),f=-1;return 0===e?f=a.start-1:1===e&&(f=a.end+1),d=f<0?b.head:f<u(c)?c[f]:b.tail,d&&d.collapsed?1:0},a.prototype.Ez=function(a,b){var c=this,d=c.items;a<0?(c.head||(c.head=new V),c.head.level=b):a<u(d)?(d[a]||(d[a]=new V),d[a].level=b):(c.tail||(c.tail=new V),c.tail.level=b),na(c)},a.prototype.suspendAdding=function(){this.V3++},a.prototype.resumeAdding=function(){this.V3--},a.prototype.createRangeGroup=function(){for(var a,b,c=this,d=c.items,e=u(c.items),f=new U(c,0,e-1,(-1)),g=0;g<e;)d[g]&&(d[g].groupInfo=i),g++;for(g=0;g<e;)d[g]?(a=ja(c,g,0),b=a.g,g=a.index+1,b&&b.level>-1&&f.addChild(b)):g++;return f},a.prototype.isGroupEnd=function(a,b){var c,d,e,f,g,h,i=this,j=i.items,k=a+1;return!ka(i,k)||(!(c=j[k])||(d=c.level,e=j[a].level,f=!1,d<e&&(g=e-d,h=e-b,h>=0&&h<g&&(f=!0)),f))},a.prototype.hitTest=function(a,b,c){return ca.call(a,b,c)},a.prototype.jp=function(a,b,c,d){var e,f,g,h,i,j,k,l=a.Gt(),m=a.am(),n=a.ss,o=l.width,p=l.height;if(o||p){if(e=d?o:m.width,f=d?m.height:p,g=d?l.rowMaxLevel:l.colMaxLevel,h=d?n.bn:n.dn,b.save(),b.beginPath(),!c||c.intersect(l.x,l.y,e,f))for(i=new Q(a,d,g),j=void 0,k=void 0,i._L(b),j=h.call(a.ss,function(){return ea(a,d,g)}),k=0;k<=2;k++)j[k].aM(b);b.beginPath(),b.restore()}},a.prototype.UL=function(a,b){if(b){var c=b.what;c===J||c===K?_(a,b):c!==L&&c!==M||aa(a,b)}},a.prototype.iT=function(){return this.wz},a.prototype.s0a=function(){var a=parseInt(q(H).borderTopWidth,10);return isNaN(a)?2:a},a.prototype.fromJSON=function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n=b&&b.ignoreStyle;if(a&&!n)for(c=this,d=c.items,e=a.itemsData,f=a.direction,g=a.head,h=a.tail,l=[],p(e,function(a,b){if(i=b.index,j=b.info,i>=0&&j){for(k=0;k<b.count&&!(i+k>=u(d));k++)b.info.collapsed&&l.push(b),d[i+k]=new V(j);c.wz=!1}}),z(f)||c.direction(f),g&&(c.head=g),h&&(c.tail=h),oa(c,!0),m=0;m<l.length;m++)c.E4(l[m].index,!0)},a.prototype.toJSON=function(a){var b,c,d,e,f,g,h,i,k,l=a&&a.ignoreStyle;if(!l){for(b=this,c=[],d=b.items,f=-1,e=0;e<u(d);e++)g=c[f],h=d[e],h&&(f>=0&&e===g.count+g.index&&h.level===g.info.level&&h.collapsed===g.info.collapsed?c[f].count++:(f++,c[f]={index:e,count:1,info:{level:h.level,collapsed:h.collapsed}}));return i={itemsData:c,direction:b.direction(),head:b.head,tail:b.tail},k={},p(i,function(a,b){ma(a,b)||(k[a]=b)}),o.isEmptyObject(k)?j:k}},a}(),b.Outline=W,W.prototype.direction=A("direction",1),h.Outline=W;function ua(a){return a&&new V(a)}function va(a){return{items:a.items.map(ua),head:ua(a.head),tail:ua(a.tail),wz:a.wz,direction:a.direction()}}function wa(a,b){a.items=b.items.map(ua),a.head=ua(b.head),a.tail=ua(b.tail),a.wz=b.wz,a.direction(b.direction),oa(a,!0)}o.extend(e.lUa.prototype,{vUa:function(a){var b=this,c=b.zTa;c&&(c.wUa||(c.wUa=[]),a&&!c.wUa[0]?c.wUa[0]=va(b.xUa):a||c.wUa[1]||(c.wUa[1]=va(b.yUa)))},zUa:function(a){var b,c;a&&(b=this,c=a[0],c&&wa(b.xUa,c),c=a[1],c&&wa(b.yUa,c))}}),e.lUa.$n(C,{init:function(){var a=this,b=a.kj;a.yUa=new W(a.getColumnCount(),b,(!1)),a.xUa=new W(a.getRowCount(),b,(!0))},undo:function(a){var b=a.wUa;b&&(this.zUa(b),this.kj.$p())}}),X={init:function(){var a=this;a.rowOutlines=a.ITa.xUa,a.columnOutlines=a.ITa.yUa,a.rowOutlines.groupChangeHandler=function(b){var c,d,f,g=b&&b.indexes;g&&a.recalcRows&&a.recalcRows(g),c=a.parent,c&&a!==c.getActiveSheet()||a.au(),g?(d=g[0],f=g[g.length-1]):(d=0,f=a.getRowCount()-1),e.Worksheet.ao(a,"onGroupChanged",{start:d,end:f,isRow:!0})},a.columnOutlines.groupChangeHandler=function(b){var c,d,f,g=a.parent;g&&a!==g.getActiveSheet()||a.bu(),c=b&&b.indexes,c?(d=c[0],f=c[c.length-1]):(d=0,f=a.getColumnCount()-1),e.Worksheet.ao(a,"onGroupChanged",{start:d,end:f,isRow:!1})},a.Wr.push(a.rowOutlines),a.Zr.push(a.columnOutlines)},dispose:function(){var a=this;o(a.Ws()).unbind("mousedown.group")},setHost:function(a){if(a){var b=this;a.bind("mousedown.group",function(a){var c,d=b.Vs(),e=ca.call(b,a.pageX-d.left,a.pageY-d.top);e&&(c=e.what,c===L||c===J?b.rowOutlines.UL(b,e):c!==M&&c!==K||b.columnOutlines.UL(b,e))})}},onPaintSuspend:function(a){var b=this;a.suspend?(b.rowOutlines.Lia(),b.columnOutlines.Lia()):(b.rowOutlines.Mia(),b.columnOutlines.Mia())},onLayoutChanged:function(a){var b=this,c=a.changeType,d=a.row,e=a.rowCount,f=a.col,g=a.colCount,h=a.sheetArea,j=b.rowOutlines,k=b.columnOutlines,l=this.ITa;"addRows"===c?(l.vUa(!0),ha(j,d,e)):"deleteRows"===c?(l.vUa(!0),ia(j,d,e)):"addColumns"===c?(l.vUa(!1),ha(k,f,g)):"deleteColumns"===c?(l.vUa(!1),ia(k,f,g)):"invalidateLayout"===c?b.TL=i:"setColumnCount"===c?(l.vUa(!1),3!==h&&1!==h||k.VL(g)):"setRowCount"!==c||3!==h&&2!==h||(l.vUa(!0),j.VL(e))},paint:function(a){var b=a.ctx,c=a.clipRect,d=this,e=d.rowOutlines,f=d.columnOutlines;e&&e.jp(d,b,c,!0),f&&f.jp(d,b,c,!1)},fromJson:function(a,b,c){if(a){var d=this,e=void 0,f=a.showRowRangeGroup,g=a.showColumnRangeGroup,h=y(f)?f:a.showRowOutline,i=y(g)?g:a.showColumnOutline;z(h)||d.showRowOutline(h),z(i)||d.showColumnOutline(i),e=a.rowRangeGroup||a.rowOutlines,e&&d.rowOutlines.fromJSON(e,c),e=a.colRangeGroup||a.columnOutlines,e&&d.columnOutlines.fromJSON(e,c)}},toJson:function(a,b){var c,d=this,e=d.rowOutlines,f=d.columnOutlines,g=d.showRowOutline();g!==!0&&(a.showRowOutline=g),c=d.showColumnOutline(),c!==!0&&(a.showColumnOutline=c),a.rowOutlines=e?e.toJSON(b):j,a.columnOutlines=f?f.toJSON(b):j}},e.Worksheet.$n(C,X),Y={init:function(){f.Commands.SL(this.commandManager())}},e.Workbook.$n(C,Y)},"./dist/plugins/group/group.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/group/group.res.en.js");b.SR={en:d}},"./dist/plugins/group/group.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidIndex="Invalid index",b.Exp_InvalidCount="Invalid count",b.Exp_InvalidLevel="Invalid level",b.Exp_GroupInfoIsNull="groupInfo is null"},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});