<template>
  <a-modal
    v-model="visible"
    title="选择报表打开"
    width="800px"
    :footer="null"
    @cancle="close"
  >
    <a-table
      :columns="columns"
      size="small"
      :pagination="false"
      :data-source="indexReports"
    >
      <template slot="operation" slot-scope="text, record">
        <span>
          <a @click="() => openReport(record)">打开报表</a>
        </span>
      </template>
    </a-table>
  </a-modal>
</template>
<script>
import { openReport } from "../utils";
export default {
  data() {
    return {
      visible: false,
      indexReports: [],
      cardItem: {},
      columns: [
        {
          title: "报表名称",
          dataIndex: "reportName",
          key: "reportName"
        },
        {
          title: "报表类型",
          dataIndex: "reportType",
          key: "reportType",
          width: 100
        },
        {
          title: "报表开发人员",
          dataIndex: "reportDeveloper",
          key: "reportDeveloper",
          wdith: 120
        },
        {
          title: "操作",
          dataIndex: "operation",
          scopedSlots: { customRender: "operation" },
          width: 90
        }
      ]
    };
  },
  methods: {
    show({ reports, cardItem }) {
      this.visible = true;
      this.indexReports = reports;
      this.cardItem = cardItem;
    },
    close() {
      this.indexReports = [];
      this.cardItem = {};
      this.visible = false;
    },
    openReport(item) {
      window.vm.$message.success("正在打开报表....");
      let params = "";
      params = ["cmimId", "indexDt", "indexFrequency", "signOrgId"]
        .map(filed => `${filed}=${item[filed]}`)
        .join("&");
      openReport(item.id, params);
      this.close();
    }
  }
};
</script>
