<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuyong<PERSON>e
 * @LastEditTime: 2021-07-23 09:39:58
-->
<template>
  <div>
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal ref="modal" />
  </div>
</template>
<script>
import Modal from "./modal.vue";
import { showAlias } from "@/utils/intl.js";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      this.$refs.modal.show({
        roleCode: this.record.roleCode
      });
    }
  }
};
</script>
