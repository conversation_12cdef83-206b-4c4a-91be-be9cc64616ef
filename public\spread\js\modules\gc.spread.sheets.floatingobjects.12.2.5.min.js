/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.FloatingObjects=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/floatingObject/floatingobject.entry.js")}({"./dist/plugins/floatingObject/drawing/StatefulChartElementBase.js":function(a,b,c){"use strict";var d,e,f,g,h=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/stateful.js"),e=c("./dist/plugins/floatingObject/drawing/common.js"),f=null,g=function(a){h(b,a);function b(b){return a.call(this,b)||this}return Object.defineProperty(b.prototype,"ChartFormat",{get:function(){var a=this;return e.UnitHelper.isNullOrUndefined(a.Kb)&&(a.Kb=a.GetDefaultFormat()),a.Kb},set:function(a){var b,c=this;e.UnitHelper.isNullOrUndefined(c.Kb)||c.Kb.SetParentForChildren(a),b=c.Kb,c.Kb=a,e.UnitHelper.isNullOrUndefined(b)||(c.Kb.ParentStateful=b.ParentStateful)},enumerable:!0,configurable:!0}),b.prototype.CreateFormat=function(){return f},b.prototype.GetDefaultFormat=function(){return this.CreateFormat()},b.prototype.GetFormatInternal=function(){return this.Kb},b.prototype.FromShapeProperties=function(a){var b=this;e.UnitHelper.isNullOrUndefined(a)?b.ClearFormat():(e.UnitHelper.isNullOrUndefined(b.Kb)&&(b.Kb=b.CreateFormat()),b.Kb.FromOOModel(a))},b.prototype.ToShapeProperties=function(){return e.UnitHelper.isNullOrUndefined(this.Kb)?f:this.Kb.ToOOModel()},b.prototype.Delete=function(){e.UnitHelper.isNullOrUndefined(this.Kb)||(this.Kb.ParentStateful=f)},b.prototype.ClearFormat=function(){var a=this;e.UnitHelper.isNullOrUndefined(a.Kb)||(a.Kb.SetParentForChildren(f),a.Kb.ParentStateful=f,a.Kb=f)},b}(d.StatefullBase),b.StatefulChartElementBase=g},"./dist/plugins/floatingObject/drawing/chart.ns.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/floatingObject/drawing/drawingInterface.js")),d(c("./dist/plugins/floatingObject/drawing/stateful.js")),d(c("./dist/plugins/floatingObject/drawing/common.js")),d(c("./dist/plugins/floatingObject/drawing/color.js")),d(c("./dist/plugins/floatingObject/drawing/colorData.js")),d(c("./dist/plugins/floatingObject/drawing/colorFormat.js")),d(c("./dist/plugins/floatingObject/drawing/effects.js")),d(c("./dist/plugins/floatingObject/drawing/fillFormat.js")),d(c("./dist/plugins/floatingObject/drawing/fontData.js")),d(c("./dist/plugins/floatingObject/drawing/fontFormat.js")),d(c("./dist/plugins/floatingObject/drawing/gradient.js")),d(c("./dist/plugins/floatingObject/drawing/lineFormat.js")),d(c("./dist/plugins/floatingObject/drawing/picture.js")),d(c("./dist/plugins/floatingObject/drawing/threeDFormat.js")),d(c("./dist/plugins/floatingObject/drawing/StatefulChartElementBase.js"))},"./dist/plugins/floatingObject/drawing/color.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./dist/plugins/floatingObject/drawing/common.js"),f=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),g=d.Common.pc,h=e.UnitHelper.isNullOrUndefined,i=null,j=function(){function a(a,b,c,d){var e=this;e.value=a,e.state=b,e.name=c,e.knownColor=d}return a.FromKnownColor=function(b){return new a(0,a.StateKnownColorValid,i,b)},a.FromIndexedColor=function(b,c){var d=a.IndexedColors;return d||(d=a.IndexedColors=[4278190080,4294967295,4294901760,4278255360,4278190335,4294967040,4294902015,4278255615,4278190080,4294967295,4294901760,4278255360,4278190335,4294967040,4294902015,4278255615,4286578688,4278222848,4278190208,4286611456,4286578816,4278222976,4290822336,4286611584,4288256511,4288230246,4294967244,4291624959,4284874854,4294934656,4278216396,4291611903,4278190208,4294902015,4294967040,4278255615,4286578816,4286578688,4278222976,4278190335,4278242559,4291624959,4291624908,4294967193,4288269567,4294941132,4291598847,4294954137,4281558783,4281584844,4288269312,4294953984,4294940928,4294927872,4284901017,4288059030,4278203238,4281571686,4278203136,4281545472,4288230144,4288230246,4281545625,4281545523,a.FromKnownColor(k.WindowText).ToArgb(),a.FromKnownColor(k.Window).ToArgb()]),a.FromArgb(d[b]).GetTintColor(c)},a.FromArgb=function(b){return new a(b,a.StateARGBValueValid,i,0)},a.FromArgbs=function(b,c,d,e){return a.CheckByte(b,"alpha"),a.CheckByte(c,"red"),a.CheckByte(d,"green"),a.CheckByte(e,"blue"),new a(a.MakeArgb(b,c,d,e),a.StateARGBValueValid,i,0)},a.FromBaseColor=function(b,c){return a.CheckByte(b,"alpha"),new a(a.MakeArgb(b,c.R,c.G,c.B),a.StateARGBValueValid,i,0)},a.FromRgb=function(b,c,d){return a.FromArgbs(255,b,c,d)},a.CheckByte=function(a,b){if(a<0||a>255)throw Error("InvalidEx2BoundArgument")},a.MakeArgb=function(a,b,c,d){var e=Math.pow(2,24);return e*a+(b<<16|c<<8|d)},Object.defineProperty(a.prototype,"Transparent",{get:function(){return a.FromKnownColor(k.Transparent)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"R",{get:function(){return this.Value>>16&255},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"G",{get:function(){return this.Value>>8&255},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"B",{get:function(){return 255&this.Value},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"A",{get:function(){return this.Value>>24&255},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsKnownColor",{get:function(){return 0!==(this.state&a.StateKnownColorValid)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsEmpty",{get:function(){return 0===this.state},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsNamedColor",{get:function(){return 0!==(this.state&a.StateNameValid)||this.IsKnownColor},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsSystemColor",{get:function(){return!!this.IsKnownColor&&(!(this.knownColor>26)||this.knownColor>167)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Name",{get:function(){var b,c=this;return 0!==(c.state&a.StateNameValid)?c.name:c.IsKnownColor?(b=m.KnownColorToName(c.knownColor),h(b)?"":b):c.value.toString(16)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Value",{get:function(){var b=this;return 0!==(b.state&a.StateValueMask)?b.value:b.IsKnownColor?m.KnownColorToArgb(b.knownColor):a.NotDefinedValue},enumerable:!0,configurable:!0}),a.prototype.Equals=function(b){return b instanceof a&&this.Value===b.Value},a.prototype.GetTintColor=function(b){var c,d=this;return 0===b?d:(c=g.jc({r:d.R,g:d.G,b:d.B},b),a.FromArgbs(c.a,c.r,c.g,c.b))},a.prototype.GetBrightness=function(){var a=this.R/255,b=this.G/255,c=this.B/255,d=a,e=a;return b>d&&(d=b),c>d&&(d=c),b<e&&(e=b),c<e&&(e=c),(d+e)/2},a.prototype.GetHue=function(){var a,b,c,d,e,f,g,h=this;return h.R===h.G&&h.G===h.B?0:(a=h.R/255,b=h.G/255,c=h.B/255,d=0,e=a,f=a,b>e&&(e=b),c>e&&(e=c),b<f&&(f=b),c<f&&(f=c),g=e-f,a===e?d=(b-c)/g:b===e?d=2+(c-a)/g:c===e&&(d=4+(a-b)/g),d*=60,d<0&&(d+=360),d)},a.prototype.GetSaturation=function(){var a,b=this.R/255,c=this.G/255,d=this.B/255,e=0,f=b,g=b;return c>f&&(f=c),d>f&&(f=d),c<g&&(g=c),d<g&&(g=d),f===g?e:(a=(f+g)/2,a<=.5?(f-g)/(f+g):(f-g)/(2-f-g))},a.prototype.ToArgb=function(){return this.Value},a.prototype.ToKnownColor=function(){return this.knownColor},a.prototype.ToString=function(){var b=this,c="ARGBColor";return c+=" [",0!==(b.state&a.StateNameValid)?c+=b.Name:0!==(b.state&a.StateKnownColorValid)?c+=b.Name:0!==(b.state&a.StateValueMask)?(c+="A=",c+=b.A,c+=", R=",c+=b.R,c+=", G=",c+=b.G,c+=", B=",c+=b.B):c+="Empty",c+="]"},a.StateKnownColorValid=1,a.StateARGBValueValid=2,a.StateValueMask=2,a.StateNameValid=8,a.NotDefinedValue=0,a}(),b.ARGBColor=j,e.ChartUtility.Kqa=function(a,b){var c,d,h=b.ColorType;return h===f.ColorType.Index?j.FromIndexedColor(b.Value,b.Tint):h===f.ColorType.Theme?(c=e.ChartUtility.toThemeColorString(b.Value,b.Tint),d=g.ec(a.getColor(c)),b.Transparency&&(d.a=parseInt(255*(1-b.Transparency)+"",10)),j.FromArgbs(d.a,d.r,d.g,d.b)):h===f.ColorType.Auto?j.FromKnownColor(k.WindowText):h===f.ColorType.None?new j:j.FromArgb(b.Value).GetTintColor(b.Tint)},function(a){a[a.ActiveBorder=1]="ActiveBorder",a[a.ActiveCaption=2]="ActiveCaption",a[a.ActiveCaptionText=3]="ActiveCaptionText",a[a.AliceBlue=28]="AliceBlue",a[a.AntiqueWhite=29]="AntiqueWhite",a[a.AppWorkspace=4]="AppWorkspace",a[a.Aqua=30]="Aqua",a[a.Aquamarine=31]="Aquamarine",a[a.Azure=32]="Azure",a[a.Beige=33]="Beige",a[a.Bisque=34]="Bisque",a[a.Black=35]="Black",a[a.BlanchedAlmond=36]="BlanchedAlmond",a[a.Blue=37]="Blue",a[a.BlueViolet=38]="BlueViolet",a[a.Brown=39]="Brown",a[a.BurlyWood=40]="BurlyWood",a[a.ButtonFace=168]="ButtonFace",a[a.ButtonHighlight=169]="ButtonHighlight",a[a.ButtonShadow=170]="ButtonShadow",a[a.CadetBlue=41]="CadetBlue",a[a.Chartreuse=42]="Chartreuse",a[a.Chocolate=43]="Chocolate",a[a.Control=5]="Control",a[a.ControlDark=6]="ControlDark",a[a.ControlDarkDark=7]="ControlDarkDark",a[a.ControlLight=8]="ControlLight",a[a.ControlLightLight=9]="ControlLightLight",a[a.ControlText=10]="ControlText",a[a.Coral=44]="Coral",a[a.CornflowerBlue=45]="CornflowerBlue",a[a.Cornsilk=46]="Cornsilk",a[a.Crimson=47]="Crimson",a[a.Cyan=48]="Cyan",a[a.DarkBlue=49]="DarkBlue",a[a.DarkCyan=50]="DarkCyan",a[a.DarkGoldenrod=51]="DarkGoldenrod",a[a.DarkGray=52]="DarkGray",a[a.DarkGreen=53]="DarkGreen",a[a.DarkKhaki=54]="DarkKhaki",a[a.DarkMagenta=55]="DarkMagenta",a[a.DarkOliveGreen=56]="DarkOliveGreen",a[a.DarkOrange=57]="DarkOrange",a[a.DarkOrchid=58]="DarkOrchid",a[a.DarkRed=59]="DarkRed",a[a.DarkSalmon=60]="DarkSalmon",a[a.DarkSeaGreen=61]="DarkSeaGreen",a[a.DarkSlateBlue=62]="DarkSlateBlue",a[a.DarkSlateGray=63]="DarkSlateGray",a[a.DarkTurquoise=64]="DarkTurquoise",a[a.DarkViolet=65]="DarkViolet",a[a.DeepPink=66]="DeepPink",a[a.DeepSkyBlue=67]="DeepSkyBlue",a[a.Desktop=11]="Desktop",a[a.DimGray=68]="DimGray",a[a.DodgerBlue=69]="DodgerBlue",a[a.Firebrick=70]="Firebrick",a[a.FloralWhite=71]="FloralWhite",a[a.ForestGreen=72]="ForestGreen",a[a.Fuchsia=73]="Fuchsia",a[a.Gainsboro=74]="Gainsboro",a[a.GhostWhite=75]="GhostWhite",a[a.Gold=76]="Gold",a[a.Goldenrod=77]="Goldenrod",a[a.GradientActiveCaption=171]="GradientActiveCaption",a[a.GradientInactiveCaption=172]="GradientInactiveCaption",a[a.Gray=78]="Gray",a[a.GrayText=12]="GrayText",a[a.Green=79]="Green",a[a.GreenYellow=80]="GreenYellow",a[a.Highlight=13]="Highlight",a[a.HighlightText=14]="HighlightText",a[a.Honeydew=81]="Honeydew",a[a.HotPink=82]="HotPink",a[a.HotTrack=15]="HotTrack",a[a.InactiveBorder=16]="InactiveBorder",a[a.InactiveCaption=17]="InactiveCaption",a[a.InactiveCaptionText=18]="InactiveCaptionText",a[a.IndianRed=83]="IndianRed",a[a.Indigo=84]="Indigo",a[a.Info=19]="Info",a[a.InfoText=20]="InfoText",a[a.Ivory=85]="Ivory",a[a.Khaki=86]="Khaki",a[a.Lavender=87]="Lavender",a[a.LavenderBlush=88]="LavenderBlush",a[a.LawnGreen=89]="LawnGreen",a[a.LemonChiffon=90]="LemonChiffon",a[a.LightBlue=91]="LightBlue",a[a.LightCoral=92]="LightCoral",a[a.LightCyan=93]="LightCyan",a[a.LightGoldenrodYellow=94]="LightGoldenrodYellow",a[a.LightGray=95]="LightGray",a[a.LightGreen=96]="LightGreen",a[a.LightPink=97]="LightPink",a[a.LightSalmon=98]="LightSalmon",a[a.LightSeaGreen=99]="LightSeaGreen",a[a.LightSkyBlue=100]="LightSkyBlue",a[a.LightSlateGray=101]="LightSlateGray",a[a.LightSteelBlue=102]="LightSteelBlue",a[a.LightYellow=103]="LightYellow",a[a.Lime=104]="Lime",a[a.LimeGreen=105]="LimeGreen",a[a.Linen=106]="Linen",a[a.Magenta=107]="Magenta",a[a.Maroon=108]="Maroon",a[a.MediumAquamarine=109]="MediumAquamarine",a[a.MediumBlue=110]="MediumBlue",a[a.MediumOrchid=111]="MediumOrchid",a[a.MediumPurple=112]="MediumPurple",a[a.MediumSeaGreen=113]="MediumSeaGreen",a[a.MediumSlateBlue=114]="MediumSlateBlue",a[a.MediumSpringGreen=115]="MediumSpringGreen",a[a.MediumTurquoise=116]="MediumTurquoise",a[a.MediumVioletRed=117]="MediumVioletRed",a[a.Menu=21]="Menu",a[a.MenuBar=173]="MenuBar",a[a.MenuHighlight=174]="MenuHighlight",a[a.MenuText=22]="MenuText",a[a.MidnightBlue=118]="MidnightBlue",a[a.MintCream=119]="MintCream",a[a.MistyRose=120]="MistyRose",a[a.Moccasin=121]="Moccasin",a[a.NavajoWhite=122]="NavajoWhite",a[a.Navy=123]="Navy",a[a.OldLace=124]="OldLace",a[a.Olive=125]="Olive",a[a.OliveDrab=126]="OliveDrab",a[a.Orange=127]="Orange",a[a.OrangeRed=128]="OrangeRed",a[a.Orchid=129]="Orchid",a[a.PaleGoldenrod=130]="PaleGoldenrod",a[a.PaleGreen=131]="PaleGreen",a[a.PaleTurquoise=132]="PaleTurquoise",a[a.PaleVioletRed=133]="PaleVioletRed",a[a.PapayaWhip=134]="PapayaWhip",a[a.PeachPuff=135]="PeachPuff",a[a.Peru=136]="Peru",a[a.Pink=137]="Pink",a[a.Plum=138]="Plum",a[a.PowderBlue=139]="PowderBlue",a[a.Purple=140]="Purple",a[a.Red=141]="Red",a[a.RosyBrown=142]="RosyBrown",a[a.RoyalBlue=143]="RoyalBlue",a[a.SaddleBrown=144]="SaddleBrown",a[a.Salmon=145]="Salmon",a[a.SandyBrown=146]="SandyBrown",a[a.ScrollBar=23]="ScrollBar",a[a.SeaGreen=147]="SeaGreen",a[a.SeaShell=148]="SeaShell",a[a.Sienna=149]="Sienna",a[a.Silver=150]="Silver",a[a.SkyBlue=151]="SkyBlue",a[a.SlateBlue=152]="SlateBlue",a[a.SlateGray=153]="SlateGray",a[a.Snow=154]="Snow",a[a.SpringGreen=155]="SpringGreen",a[a.SteelBlue=156]="SteelBlue",a[a.Tan=157]="Tan",a[a.Teal=158]="Teal",a[a.Thistle=159]="Thistle",a[a.Tomato=160]="Tomato",a[a.Transparent=27]="Transparent",a[a.Turquoise=161]="Turquoise",a[a.Violet=162]="Violet",a[a.Wheat=163]="Wheat",a[a.White=164]="White",a[a.WhiteSmoke=165]="WhiteSmoke",a[a.Window=24]="Window",a[a.WindowFrame=25]="WindowFrame",a[a.WindowText=26]="WindowText",a[a.Yellow=166]="Yellow",a[a.YellowGreen=167]="YellowGreen"}(k=b.KnownColor||(b.KnownColor={})),l=function(a){a[1]=4290032820,a[2]=4288263377,a[3]=4278190080,a[4]=4289440683,a[5]=4293980400,a[6]=4288716960,a[7]=4285098345,a[8]=4293125091,a[9]=4294967295,a[10]=4278190080,a[11]=4278190080,a[12]=4285361517,a[13]=4281571839,a[14]=4294967295,a[15]=4278216396,a[16]=4294244348,a[17]=4290760155,a[18]=4278190080,a[19]=4294967265,a[20]=4278190080,a[21]=4293980400,a[22]=4278190080,a[23]=4291348680,a[24]=4294967295,a[25]=4284769380,a[26]=4278190080,a[168]=4293980400,a[169]=4294967295,a[170]=4288716960,a[171]=4290367978,a[172]=4292338930,a[173]=4293980400,a[174]=4281571839},m=function(){function a(){}return a.ArgbToKnownColor=function(b){var c,d,e;for(this.EnsureColorTable(),c=0;c<a.colorTable.length;c++)if(d=a.colorTable[c],d===b&&(e=j.FromArgb(c),!e.IsSystemColor))return e;return j.FromArgb(b)},a.Encode=function(a,b,c,d){return b<<16|c<<8|d|a<<24},a.EnsureColorNameTable=function(){h(a.colorNameTable)&&a.InitColorNameTable()},a.EnsureColorTable=function(){h(a.colorTable)&&this.InitColorTable()},a.InitColorNameTable=function(){var b,c=[];for(b in k)k.hasOwnProperty(b)&&(c[k[b]]=b);a.colorNameTable=c},a.InitColorTable=function(){var b=[];l(b),b[27]=16777215,b[28]=-984833,b[29]=-332841,b[30]=-16711681,b[31]=-8388652,b[32]=-983041,b[33]=-657956,b[34]=-6972,b[35]=-16777216,b[36]=-5171,b[37]=-16776961,b[38]=-7722014,b[39]=-5952982,b[40]=-2180985,b[41]=-10510688,b[42]=-8388864,b[43]=-2987746,b[44]=-32944,b[45]=-10185235,b[46]=-1828,b[47]=-2354116,b[48]=-16711681,b[49]=-16777077,b[50]=-16741493,b[51]=-4684277,b[52]=-5658199,b[53]=-16751616,b[54]=-4343957,b[55]=-7667573,b[56]=-11179217,b[57]=-29696,b[58]=-6737204,b[59]=-7667712,b[60]=-1468806,b[61]=-7357301,b[62]=-12042869,b[63]=-13676721,b[64]=-16724271,b[65]=-7077677,b[66]=-60269,b[67]=-16728065,b[68]=-9868951,b[69]=-14774017,b[70]=-5103070,b[71]=-1296,b[72]=-14513374,b[73]=-65281,b[74]=-2302756,b[75]=-460545,b[76]=-10496,b[77]=-2448096,b[78]=-8355712,b[79]=-16744448,b[80]=-5374161,b[81]=-983056,b[82]=-38476,b[83]=-3318692,b[84]=-11861886,b[85]=-16,b[86]=-989556,b[87]=-1644806,b[88]=-3851,b[89]=-8586240,b[90]=-1331,b[91]=-5383962,b[92]=-1015680,b[93]=-2031617,b[94]=-329006,b[95]=-2894893,b[96]=-7278960,b[97]=-18751,b[98]=-24454,b[99]=-14634326,b[100]=-7876870,b[101]=-8943463,b[102]=-5192482,b[103]=-32,b[104]=-16711936,b[105]=-13447886,b[106]=-331546,b[107]=-65281,b[108]=-8388608,b[109]=-10039894,b[110]=-16777011,b[111]=-4565549,b[112]=-7114533,b[113]=-12799119,b[114]=-8689426,b[115]=-16713062,b[116]=-12004916,b[117]=-3730043,b[118]=-15132304,b[119]=-655366,b[120]=-6943,b[121]=-6987,b[122]=-8531,b[123]=-16777088,b[124]=-133658,b[125]=-8355840,b[126]=-9728477,b[127]=-23296,b[128]=-47872,b[129]=-2461482,b[130]=-1120086,b[131]=-6751336,b[132]=-5247250,b[133]=-2396013,b[134]=-4139,b[135]=-9543,b[136]=-3308225,b[137]=-16181,b[138]=-2252579,b[139]=-5185306,b[140]=-8388480,b[141]=-65536,b[142]=-4419697,b[143]=-12490271,b[144]=-7650029,b[145]=-360334,b[146]=-744352,b[147]=-13726889,b[148]=-2578,b[149]=-6270419,b[150]=-4144960,b[151]=-7876885,b[152]=-9807155,b[153]=-9404272,b[154]=-1286,b[155]=-16711809,b[156]=-12156236,b[157]=-2968436,b[158]=-16744320,b[159]=-2572328,b[160]=-40121,b[161]=-12525360,b[162]=-1146130,b[163]=-663885,b[164]=-1,b[165]=-657931,b[166]=-256,b[167]=-6632142,a.colorTable=b},a.KnownColorToArgb=function(a){return this.EnsureColorTable(),a<=k.MenuHighlight?this.colorTable[a]:0},a.KnownColorToName=function(b){return this.EnsureColorNameTable(),b<=k.MenuHighlight?a.colorNameTable[b]:i},a.IsKnowColor=function(b){return a.EnsureColorTable(),a.colorTable.indexOf(b)!==-1},a}(),b.KnownColorTable=m},"./dist/plugins/floatingObject/drawing/colorData.js":function(a,b,c){"use strict";var d,e,f;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),function(a){a[a.None=0]="None",a[a.ColorType=1]="ColorType",a[a.Value=2]="Value",a[a.Tint=4]="Tint",a[a.All=7]="All"}(e=b.ColorDataFlag||(b.ColorDataFlag={})),f=function(){function a(){}return a.ConvertHexStringToInt=function(a){return parseInt(a,16)},a.prototype.Equals=function(a){return this.ColorType===a.ColorType&&this.Value===a.Value&&this.Tint===a.Tint},a.prototype.Clone=function(){return this},a.prototype.Compose=function(a,b){void 0===b&&(b=!0),a.Flag!==e.None&&(b?this.ConflictCompose(a):this.NonConflictCompose(a))},a.prototype.ConflictCompose=function(a){var b=this;(a.Flag&e.ColorType)===e.ColorType&&b.ColorType!==a.ColorType&&(b.ColorType=a.ColorType,b.Value=0,b.Tint=0),(a.Flag&e.Value)===e.Value&&(b.Value=a.Value),(a.Flag&e.Tint)===e.Tint&&(b.Tint=a.Tint),b.Flag|=a.Flag},a.prototype.NonConflictCompose=function(a){var b=this;(b.Flag&e.ColorType)!==e.ColorType&&(a.Flag&e.ColorType)===e.ColorType&&(b.ColorType=a.ColorType,b.Value=a.Value,b.Tint=a.Tint,b.Flag=a.Flag)},a.prototype.RemoveDuplicateStyle=function(a){return this.Compare(a,!1)},a.prototype.RemoveDifferentStyle=function(a){return this.Compare(a,!0)},a.prototype.Compare=function(a,b){var c,d,f,g=this,h=!1;return(g.Flag&e.ColorType)===e.ColorType&&(f=(a.Flag&e.ColorType)!==e.ColorType||g.ColorType!==a.ColorType,b?f?(g.Flag=e.None,h=!0):((g.Flag&e.Value)===e.Value&&(c=(a.Flag&e.Value)!==e.Value||g.Value!==a.Value,c&&(g.Flag=e.None,h=!0)),(g.Flag&e.Tint)===e.Tint&&(d=(a.Flag&e.Tint)!==e.Tint||g.Tint!==a.Tint,d&&(g.Flag=e.None,h=!0))):f||((g.Flag&e.Value)===e.Value&&(c=(a.Flag&e.Value)!==e.Value||g.Value!==a.Value,c||(g.Flag&=~e.Value,h=!0)),(g.Flag&e.Tint)===e.Tint&&(d=(a.Flag&e.Tint)!==e.Tint||g.Tint!==a.Tint,d||(g.Flag&=~e.Tint,h=!0)),g.Flag===e.ColorType&&(g.Flag=e.None,h=!0))),h},a.prototype.UpdateDefaultValueFlag=function(){this.ColorType===d.ColorType.None?this.Flag=e.None:this.Flag=e.All},a.prototype.UpdateFlagFromBottom=function(){},a.prototype.ClearFlag=function(){this.Flag=e.None},a.prototype.SetFullFlag=function(){this.Flag=e.All},a.prototype.IsFullFlag=function(){return this.Flag===e.All},a}(),b.ColorData=f},"./dist/plugins/floatingObject/drawing/colorFormat.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./dist/plugins/floatingObject/drawing/colorData.js"),f=c("./dist/plugins/floatingObject/drawing/color.js"),g=c("./dist/plugins/floatingObject/drawing/common.js"),h=c("./dist/plugins/floatingObject/drawing/stateful.js"),i=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),j=d.Common.pc,k=g.UnitHelper.isNullOrUndefined,l=g.ShapeConstants.DefaultTransparent,m=g.ShapeConstants.PositionConver,n=null,o=Math.abs,p=Math.floor;function s(a,b){return a.getColor(b)!==b}b.createColorFormatFromRGB=function(a,b,c){var d=new q(a);return d.ColorType=b,d.RGB=c,d},b.createColorFormat=function(a,b,c,d){var e=new q(a);return e.ColorType=b,e.ObjectThemeColor=c,d&&(e.TintAndShade=d),e},q=function(a){r(b,a);function b(b,c){var d,e,f=a.call(this,c)||this;return void 0===c&&(c=n),d=f,d.Hqa=i.ColorSchemeIndex.None,d.Iqa=n,d.Yba=0,d.Jqa=0,d.Gqa=l,d.xc=b,d.hqa=c,e=d.xc.DrawingType,e===i.DrawingType.Shape?d.Xba=i.SolidColorType.Theme:e===i.DrawingType.Chart?d.Xba=i.SolidColorType.Automatic:e===i.DrawingType.Picture&&(d.Xba=i.SolidColorType.None),d}return b.prototype.setColor=function(a,b){var c,d,e=this;a&&s(a.currentTheme(),b)?(c=g.ChartUtility.fromThemeColorString(b),c&&(e.ObjectThemeColor=c.index,void 0!==c.tint&&(e.TintAndShade=c.tint))):(d=j.ec(b),e.RGB=f.ARGBColor.FromArgbs(d.a,d.r,d.g,d.b))},b.prototype.GetColorData=function(){var a,b,c,d,g=this,h=new e.ColorData;if(h.ColorType=i.ColorType.RGB,a=g.Xba,a===i.SolidColorType.None)h.ColorType=i.ColorType.None;else if(a===i.SolidColorType.Automatic){if(h.ColorType=i.ColorType.Auto,b=g.GetAutoColorFormat(),!k(b))return b.GetColorData()}else a===i.SolidColorType.RGB?k(g.Iqa)||(h.ColorType=i.ColorType.RGB,c=this.Iqa,d=1-g.Gqa,1!==d&&(c=f.ARGBColor.FromArgbs(parseInt(255*d,10),c.R,c.G,c.B)),h.Value=c.ToArgb(),h.Tint=g.Yba||g.Jqa):a===i.SolidColorType.Theme&&(h.ColorType=i.ColorType.Theme,h.Value=g.Hqa,h.Tint=g.Yba||g.Jqa,h.Transparency=g.Gqa);return h},b.prototype.HasOwnColor=function(){return this.GetState(i.ColorFormatStates.ObjectThemeColor)||this.GetState(i.ColorFormatStates.RGB)||this.GetAutoColorFormat()!==n},b.prototype.GetAutoColorFormat=function(){if(this.ColorType===i.SolidColorType.Automatic&&!k(this.AutoColorFormat)){var a=this.AutoColorFormat();if(!k(a)&&a.ColorType!==i.SolidColorType.Automatic)return a}return n},b.prototype.OnParentChanged=function(a){this.hqa=a},b.prototype.ClearModel=function(){var a,b,c;for(this.Lqa=n,a=0,b=this.Children;a<b.length;a++)c=b[a],c.ClearModel()},b.prototype.CalcBrightness=function(a,b){return a&&a.length>0&&b&&b.length>0?b[0]/m:a&&a.length>0?a[0]/m-1:0},b.prototype.CalcLumModeOff=function(a,b){var c=this;c.Brightness>0?(a.push(p((1-c.Brightness)*m)),b.push(p(c.Brightness*m))):c.Brightness<0&&a.push(p((1+c.Brightness)*m))},b.prototype.FromCT_ColorProperties=function(a){var b,c,d,e,h,j,l,n=this;n.Lqa=a,k(a)?n.ColorType=i.SolidColorType.None:k(a.schemeClr)?k(a.srgbClr)||k(a.srgbClr.val)||3!==a.srgbClr.val.length?k(a.scrgbClr)?k(a.sysClr)?k(a.prstClr)||(l=a.prstClr,n.RGB=f.ARGBColor.FromArgb(g.ChartUtility.GetPresetColorRGB(l.val)),n.Brightness=n.CalcBrightness(l.lumMod,l.lumOff),!k(l.alpha)&&l.alpha.length>0&&(n.Transparency=l.alpha[0]/m)):(h=a.sysClr,j=h.val,k(j)?k(h.lastClr)||3!==h.lastClr.length||(n.RGB=f.ARGBColor.FromRgb(h.lastClr[0],h.lastClr[1],h.lastClr[2])):n.RGB=f.ARGBColor.FromKnownColor(j),n.Brightness=n.CalcBrightness(h.lumMod,h.lumOff),!k(h.alpha)&&h.alpha.length>0&&n.FromTransparency(h.alpha[0])):(e=a.scrgbClr,n.RGB=f.ARGBColor.FromRgb(e.r,e.g,e.b)):(d=a.srgbClr,n.RGB=f.ARGBColor.FromRgb(d.val[0],d.val[1],d.val[2]),n.Brightness=n.CalcBrightness(d.lumMod,d.lumOff),!k(d.alpha)&&d.alpha.length>0&&n.FromTransparency(d.alpha[0]),!k(d.tint)&&d.tint.length>0&&n.FromTint(d.tint[0])):(b=a.schemeClr,n.ObjectThemeColor=b.val,n.Brightness=n.CalcBrightness(b.lumMod,b.lumOff),n.Yba=0,!k(b.tint)&&b.tint.length>0&&n.FromTint(b.tint[0]),!k(b.alpha)&&b.alpha.length>0&&n.FromTransparency(b.alpha[0]),c=b.shade,c&&c.length&&n.FromShade(c[0]))},b.prototype.ToCT_ColorProperties=function(a){var b,c,d,e,f=this,h=f.Lqa||a,j=[],m=[],o=h.schemeClr;return o&&g.ChartUtility.removeEmptyArrayProperties(o),f.ColorType===i.SolidColorType.Theme?(t(h),o||(o=h.schemeClr={}),o.val=f.ObjectThemeColor,f.CalcLumModeOff(j,m),u(o,j,m),f.TintAndShade>0?o.tint=[f.ToTint()]:f.TintAndShade<0&&(o.shade=[f.ToShade()]),f.Transparency!==l&&(o.alpha=[f.ToTransparency()])):f.ColorType===i.SolidColorType.RGB&&(t(h),delete h.schemeClr,b=n,f.Iqa&&f.Iqa.IsKnownColor&&(b=f.Iqa.ToKnownColor()),k(b)?(d=h.srgbClr={},d.val=[f.RGB.R,f.RGB.G,f.RGB.B],f.CalcLumModeOff(j,m),u(d,j,m),f.Transparency!==l&&(d.alpha=[f.ToTransparency()]),f.TintAndShade>0?d.tint=[f.ToTint()]:f.TintAndShade<0&&(d.shade=[f.ToShade()])):(c=h.sysClr={},c.val=b,c.lastClr=[f.RGB.R,f.RGB.G,f.RGB.B],f.CalcLumModeOff(j,m),u(c,j,m),f.Transparency!==l&&(c.alpha=[f.ToTransparency()]))),e=a&&a.colorFillType,k(e)||(h.colorFillType=e),h},b.prototype.FromTint=function(a){this.TintAndShade=1-a/m},b.prototype.FromShade=function(a){this.TintAndShade=a/m-1},b.prototype.ToTint=function(){return p(o((1-this.TintAndShade)*m))},b.prototype.ToShade=function(){return p(o((1+this.TintAndShade)*m))},b.prototype.FromTransparency=function(a){this.Transparency=1-a/m},b.prototype.ToTransparency=function(){return p((1-this.Transparency)*m)},b.prototype.Clone=function(){var a=this,c=new b(a.xc);return c.Jqa=a.Jqa,c.Xba=a.Xba,c.xc=a.xc,c.Iqa=a.Iqa,c.Hqa=a.Hqa,c.Yba=a.Yba,c.AutoColorFormat=a.AutoColorFormat,c},b.prototype.FromOOModel=function(a){this.FromCT_ColorProperties(a)},b.prototype.ToOOModel=function(){var a=this.ColorType;return a===i.SolidColorType.Automatic?{}:a===i.SolidColorType.None?{colorFillType:i.ColorFillType.NoFillProperties}:this.ToCT_ColorProperties({colorFillType:0})},b.prototype.Color_FromOOModel=function(a){this.FromCT_ColorProperties(a)},b.prototype.Color_ToOOModel=function(){return this.ToCT_ColorProperties({})},b.prototype.GradientStop_FromOOModel=function(a){this.FromCT_ColorProperties(a)},b.prototype.GradientStop_ToOOModel=function(){return this.ToCT_ColorProperties({})},b.prototype.ColorData_FromOOModel=function(a){a.ColorType===i.ColorType.Index?this.RGB=f.ARGBColor.FromArgb(a.Value):a.ColorType===i.ColorType.Theme&&(this.ObjectThemeColor=a.Value,this.TintAndShade=a.Tint)},b.prototype.ColorData_ToOOModel=function(){var a=this,b=new e.ColorData;return a.ColorType===i.SolidColorType.RGB?(b.Value=a.RGB.Value,b.ColorType=i.ColorType.RGB):a.ColorType===i.SolidColorType.Theme&&(b.Value=a.ObjectThemeColor,b.Tint=a.TintAndShade,b.ColorType=i.ColorType.Theme),b},Object.defineProperty(b.prototype,"Brightness",{get:function(){return this.GetState(i.ColorFormatStates.Brightness)||k(this.hqa)?this.Jqa:this.hqa.Brightness},set:function(a){if(a<-1||a>1)throw Error();this.Jqa=a,this.Dirty(i.ColorFormatStates.Brightness)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ObjectThemeColor",{get:function(){return this.GetState(i.ColorFormatStates.ObjectThemeColor)||k(this.hqa)?this.Hqa:this.hqa.ObjectThemeColor},set:function(a){var b=this,c={13:1,14:0,15:3,16:2};k(c[a])||(a=c[a]),b.Hqa=a,b.Dirty(i.ColorFormatStates.ObjectThemeColor),b.ColorType=i.SolidColorType.Theme,b.Iqa=n,b.UnDirty(i.ColorFormatStates.RGB),b.Jqa=0,b.Dirty(i.ColorFormatStates.Brightness)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RGB",{get:function(){var a=this;return a.HasOwnColor()||k(a.hqa)?a.xc.Kqa(a.GetColorData()):a.hqa.RGB},set:function(a){var b=this;b.Iqa=a,b.Transparency=1-a.A/255,b.Dirty(i.ColorFormatStates.RGB),b.ColorType=i.SolidColorType.RGB,b.Hqa=i.ColorSchemeIndex.None,b.Dirty(i.ColorFormatStates.ObjectThemeColor),b.Jqa=0,b.Yba=0,b.Dirty(i.ColorFormatStates.Brightness),b.Dirty(i.ColorFormatStates.TintAndShade)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TintAndShade",{get:function(){var a=this;return a.GetState(i.ColorFormatStates.TintAndShade)||k(a.hqa)?a.Yba:a.hqa.TintAndShade},set:function(a){this.Yba=a,this.Dirty(i.ColorFormatStates.TintAndShade)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Transparency",{get:function(){var a=this;return a.GetState(i.ColorFormatStates.Transparency)||k(a.hqa)?a.Gqa:a.hqa.Transparency},set:function(a){a=Math.max(0,Math.min(1,a)),this.Gqa=a,this.Dirty(i.ColorFormatStates.Transparency)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ColorType",{get:function(){var a=this;return a.GetState(i.ColorFormatStates.ColorType)||k(a.hqa)?a.Xba:a.hqa.ColorType},set:function(a){this.Xba=a,this.Dirty(i.ColorFormatStates.ColorType)},enumerable:!0,configurable:!0}),b}(h.StatefullBase),b.ColorFormat=q;function t(a){delete a.hslClr,delete a.prstClr,delete a.scrgbClr,delete a.srgbClr,delete a.sysClr}function u(a,b,c){b.length>0&&(a.lumMod=b),c.length>0&&(a.lumOff=c)}},"./dist/plugins/floatingObject/drawing/common.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),f=c("SheetsCalc"),g=d.Common.l,h=null,i=void 0,j=Math.max,k=Math.min,l=Math.floor,m=Math.log,n=Math.pow,o=Math.ceil,p=Math.abs,q=1048576,r=16384;function G(a){return"number"==typeof a||a instanceof Date}function H(a,b,c){c.configurable=!0,c.enumerable=!0,Object.defineProperty(a,b,c)}function I(a){var b,c,d,e,f,g,i,j;if(!a||0===a.length)return!1;if(a=a.toUpperCase().trim(),"["===a[0]){if(b=a.indexOf("]"),b===-1||b===a.length-1)return!1;a=a.substr(b+1)}for(c=-1,d=0,e=!1,f=h,g=0;g<a.length;g++){if(j=a[g],"["===j)do j=a[g],g++;while(g<a.length&&"]"!==j);if("Y"===j||"D"===j||"M"===j||"H"===j||"S"===j)e?d++:0!==g&&"\\"===a[g-1]||(e=!0,c=g,d=1);else{if(e=!1," "===j&&f)return!0;0!==d&&(i=a.substr(c,d),c=g,d=0,f=u.isNullOrUndefined(f)?J(i):f&&J(i))}}return 0!==d&&g===a.length&&(i=a.substr(c,d),f=u.isNullOrUndefined(f)?J(i):f&&J(i)),f}function J(a){var b,c,d,e,f,g={Y:4,D:4,M:5,H:2,S:2};if(!a)return!1;for(b={},c=0;c<a.length;c++)d=a[c],b[d]?b[d]=b[d]+1:b[d]=1;for(e in b)if(b.hasOwnProperty(e)){if(f=g[e],void 0===f)return!1;if(b[e]>f)return!1}return!0}s=function(){function a(){}return a.FromOADate=g.Xb,a.ToOADate=g.Ra,a.OADateMinAsDouble=-657435,a.OADateMaxAsDouble=2958466,a}(),b.DateTimeExtension=s,t=function(){function a(){}return a.log=function(a,b){return m(a)/m(b)},a.round=function(a,b){var c=n(10,b);return Math.round(a*c)/c},a.isNaNOrInfinite=function(a){return isNaN(a)||!isFinite(a)},a.GetTriangleAngle=function(a,b,c){var d,e;return 0===a&&0===b?0:(d=(a*a+c*c-b*b)/p(2*a*c),e=0,a<0&&b>0?e=90:a<0&&b<0?e=180:a>0&&b<0&&(e=270),180*Math.acos(d)/Math.PI+e)},
a.INT32_MAX_VALUE=2147483647,a.DOUBLE_MAX_VALUE=Number.MAX_VALUE,a.DOUBLE_MIN_VALUE=-Number.MAX_VALUE,a}(),b.NumberExtension=t;function K(a,b){var c=Math.pow(10,b);return Math.round(a*c)/c}u=function(){function a(){}return a.pointToPixel=function(a){return K(96*a/72,2)},a.IsNullOrEmpty=function(b){return a.isNullOrUndefined(b)||""===b},a.IsNegativeInfinity=function(a){return a===Number.NEGATIVE_INFINITY},a.IsPositiveInfinity=function(a){return a===Number.POSITIVE_INFINITY},a.isNullOrUndefined=function(a){return a===i||a===h},a}(),b.UnitHelper=u,v=function(){function a(a,b,c,d){var e=this;e.Row=a,e.Column=b,e.RowCount=c,e.ColumnCount=d}return Object.defineProperty(a.prototype,"Left",{get:function(){return this.Column},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Top",{get:function(){return this.Row},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Right",{get:function(){return this.Column+this.ColumnCount},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Bottom",{get:function(){return this.Row+this.RowCount},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsFullColumn",{get:function(){return 0===this.Row&&this.RowCount===t.INT32_MAX_VALUE},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsFullRow",{get:function(){return 0===this.Column&&this.ColumnCount===t.INT32_MAX_VALUE},enumerable:!0,configurable:!0}),a.prototype.Intersect=function(a){var b=this,c=a,d=j(b.Column,c.Column),e=j(b.Row,c.Row),f=k(b.Right,c.Right),g=k(b.Bottom,c.Bottom);d>=f||e>=g?(b.Column=0,b.Row=0,b.ColumnCount=0,b.RowCount=0):(b.Column=d,b.Row=e,b.ColumnCount=f-d,b.RowCount=g-e)},a.prototype.IntersectsWith=function(a){var b=this,c=a;return!(b.Left>=c.Right||b.Right<=c.Left||b.Top>=c.Bottom||b.Bottom<=c.Top)},a}(),b.CellRect=v,w=function(){function a(a,b){this.Worksheet=a,this.tpa=b,this.UpdateRects()}return a.prototype.UpdateRects=function(){var a,b,c,d,e,f,g,h,i,j,l,m=this;for(m.upa=[],d=m.Worksheet,e=d.getRowCount(),f=d.getColumnCount(),g=0;g<m.tpa.length;g++){if(c=m.tpa[g],h=c.Row,i=c.Column,a=c.RowCount,b=c.ColumnCount,(h<0||a<0||i<0||b<0)&&((h<0||a<0)&&(h=0,a=t.INT32_MAX_VALUE),(i<0||b<0)&&(i=0,b=t.INT32_MAX_VALUE),m.tpa[g]=new v(h,i,a,b)),(0!==h||a!==t.INT32_MAX_VALUE)&&(h<0||a<=0||h+a>q))throw Error();if((0!==i||b!==t.INT32_MAX_VALUE)&&(i<0||b<=0||i+b>r))throw Error()}for(j=0,l=m.tpa;j<l.length;j++)c=l[j],a=k(c.Bottom,e+1)-c.Top,b=k(c.Right,f+1)-c.Left,m.upa.push(new v(c.Row,c.Column,a,b))},Object.defineProperty(a.prototype,"Row",{get:function(){var a=this.upa[0].Row;return a<0?0:a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Column",{get:function(){var a=this.upa[0].Column;return a<0?0:a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"RowCount",{get:function(){var a,b,c=this,d=c.upa[0].RowCount,e=c.upa[0].Row;return d<0||e<0?(a=c.Worksheet,b=a.getRowCount()):d},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ColumnCount",{get:function(){var a=this,b=this.upa[0].ColumnCount,c=this.upa[0].Column,d=a.Worksheet,e=d.getColumnCount();return b<0||c<0?e:b},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"EntireRow",{get:function(){var b,c,d=[];for(b=0;b<this.upa.length;b++)c=this.upa[b],c.Column=-1,c.ColumnCount=-1,d.push(c);return new a(this.Worksheet,d)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"EntireColumn",{get:function(){var b,c,d=[];for(b=0;b<this.upa.length;b++)c=this.upa[b],c.Row=-1,c.RowCount=-1,d.push(c);return new a(this.Worksheet,d)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Text",{get:function(){var a=this;return a.Worksheet.vpa(a.Row,a.Column,a.RowCount,a.ColumnCount)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Areas",{get:function(){var b,c,d,e=this.upa,f=[];for(b=0,c=e;b<c.length;b++)d=c[b],f.push(new a(this.Worksheet,[d]));return f},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Hidden",{get:function(){return this.Worksheet.wpa(this.tpa)},enumerable:!0,configurable:!0}),a}(),b.Range=w,function(a){a[a.Row=1]="Row",a[a.Column=2]="Column",a[a.RowIsRelative=4]="RowIsRelative",a[a.ColumnIsRelative=8]="ColumnIsRelative",a[a.LastRowIsRelative=16]="LastRowIsRelative",a[a.LastColumnIsRelative=32]="LastColumnIsRelative",a[a.Range=64]="Range",a[a.Error=128]="Error"}(x||(x={})),y=function(){function a(a,b,c,d,e){var f=this;f.WorksheetName=a,f.Row=b,f.Column=c,void 0!==d&&(f.LastRow=d),void 0!==e&&(f.LastColumn=e)}return Object.defineProperty(a.prototype,"Row",{get:function(){return(this.Kind&x.Row)===x.Row?this.cj:0},set:function(a){this.cj=a,this.Kind|=x.Row},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Column",{get:function(){return(this.Kind&x.Column)===x.Column?this.Lk:0},set:function(a){this.Lk=a,this.Kind|=x.Column},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"RowCount",{get:function(){var a,b,c=this;return c.IsRange?(c.Kind&x.Row)===x.Row?c.RowIsRelative&&c.LastRowIsRelative||!c.RowIsRelative&&!c.LastRowIsRelative?c.LastRow-c.Row+1:-1:(a=c.Worksheet,b=a.getRowCount()):1},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ColumnCount",{get:function(){var a=this;return a.IsRange?(a.Kind&x.Column)===x.Column?a.ColumnIsRelative&&a.LastColumnIsRelative||!a.ColumnIsRelative&&!a.LastColumnIsRelative?a.LastColumn-a.Column+1:-1:a.Worksheet.getColumnCount():1},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"LastColumn",{get:function(){var a,b,c=this;return c.IsRange?(c.Kind&x.Column)===x.Column?c.xpa:(a=c.Worksheet,b=a.getColumnCount(),b-1):c.Column},set:function(a){this.xpa=a,this.Kind|=x.Column|x.Range},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"LastRow",{get:function(){var a,b,c=this;return c.IsRange?(c.Kind&x.Row)===x.Row?c.ypa:(a=c.Worksheet,b=a.getRowCount(),b-1):c.Row},set:function(a){this.ypa=a,this.Kind|=x.Row|x.Range},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"RowIsRelative",{get:function(){return(this.Kind&(x.Row|x.RowIsRelative))===(x.Row|x.RowIsRelative)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ColumnIsRelative",{get:function(){return(this.Kind&(x.Column|x.ColumnIsRelative))===(x.Column|x.ColumnIsRelative)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"LastRowIsRelative",{get:function(){return this.IsRange?(this.Kind&(x.Row|x.LastRowIsRelative))===(x.Row|x.LastRowIsRelative):this.RowIsRelative},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"LastColumnIsRelative",{get:function(){return this.IsRange?(this.Kind&(x.Column|x.LastColumnIsRelative))===(x.Column|x.LastColumnIsRelative):this.ColumnIsRelative},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsRange",{get:function(){return(this.Kind&x.Range)===x.Range},set:function(a){a?this.Kind|=x.Range:this.Kind&=~x.Range},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsError",{get:function(){return"#REF"===this.WorksheetName},enumerable:!0,configurable:!0}),a.prototype.GetSourceRange=function(a,b){var c=this,d=c.Row,e=c.Column,f=c.LastRow,g=c.LastColumn;return new v(d,e,f-d+1,g-e+1)},a.prototype.ToR1C1Text=function(a){var b,c,d,e,f;return a===i&&(a=h),b=this,(b.Kind&x.Error)===x.Error?b.GetPrevText(a)+"#REF!":(c="",(b.Kind&x.Row)===x.Row&&(d=b.Row,c+=b.RowIsRelative?0===d?"R":"R["+d+"]":"R"+(d+1)),(b.Kind&x.Column)===x.Column&&(e=b.Column,c+=b.ColumnIsRelative?0===e?"C":"C["+e+"]":"C"+(e+1)),b.IsRange&&(f=!1,(b.Kind&(x.Row|x.Column))!==(x.Row|x.Column)&&((b.Kind&x.Row)===x.Row?b.RowIsRelative===b.LastRowIsRelative&&b.Row===b.LastRow&&(f=!0):b.ColumnIsRelative===b.LastColumnIsRelative&&b.Column===b.LastColumn&&(f=!0)),f||(c+=":",(b.Kind&x.Row)===x.Row&&(d=b.LastRow,c+=b.LastRowIsRelative?0===d?"R":"R["+d+"]":"R"+(d+1)),(b.Kind&x.Column)===x.Column&&(e=b.LastColumn,c+=b.LastColumnIsRelative?0===e?"C":"C["+e+"]":"C"+(e+1)))),b.GetPrevText(a)+c)},a.prototype.ToA1Text=function(a,b,c){var d,e,f,g;return c===i&&(c=h),d=this,(d.Kind&x.Error)===x.Error?d.GetPrevText(c)+"#REF!":(e="",(d.Kind&x.Column)===x.Column&&(g=d.Column,d.ColumnIsRelative?g+=b:e+="$",e+=d.GetColumnIndexInA1Letter(g)),(d.Kind&x.Row)===x.Row&&(f=d.Row,d.RowIsRelative?f+=a:e+="$",e+=""+(f+1)),d.IsRange&&(e+=":",(d.Kind&x.Column)===x.Column&&(g=d.LastColumn,d.LastColumnIsRelative?g+=b:e+="$",e+=d.GetColumnIndexInA1Letter(g)),(d.Kind&x.Row)===x.Row&&(f=d.LastRow,d.LastRowIsRelative?f+=a:e+="$",e+=""+(f+1))),d.GetPrevText(c)+e)},a.prototype.GetPrevText=function(a){var b,c=this,d="";return c.Workbook>0&&(d="["+c.Workbook+"]"),b=c.WorksheetName,u.isNullOrUndefined(b)&&(b=a),b&&(d+=u.isNullOrUndefined(c.LastWorksheetName)?b:b+":"+c.LastWorksheetName),d?((c.IsSpecialSheetName(b)||c.IsSpecialSheetName(c.LastWorksheetName))&&(d="'"+d.replace("'","''")+"'"),d+="!"):""===b&&(d="!"),d},a.prototype.IsSpecialSheetName=function(b){var c,d;if(!u.isNullOrUndefined(b)){if(b===a.ErrorWorksheetName)return!1;for(c=0;c<b.length;c++)if(d=b[c],!(d>="A"&&d<="Z"||d>="a"&&d<="z"||d>="0"&&d<="9"||"_"===d))return!0}return!1},a.prototype.GetColumnIndexInA1Letter=function(a){for(var b="ABCDEFGHIJKLMNOPQRSTUVWXYZ",c=[],d=0;;){if(d=a%26,c.unshift(b[d]),a=parseInt(a/26+"",10),0===a)break;a--}return c.join("")},a.ErrorWorksheetName="#REF",a}(),b.Reference=y,b.ChartConstants={XValuesMinMax:"XValuesMinMax",ValuesMinMax:"ValuesMinMax",PrimaryValuesMinMax:"PrimaryValuesMinMax",SecondaryValuesMinMax:"SecondaryValuesMinMax",PrimaryXValuesMinMax:"PrimaryXValuesMinMax",SecondaryXValuesMinMax:"SecondaryXValuesMinMax",Collection:"Collection",Restore:"Restore",ChartType:"ChartType",AxisGroup:"AxisGroup",AreAllXValuesDateTime:"AreAllXValuesDateTime",XValuesFormula:"XValuesFormula",XValues:"XValues",CategoryNames:"CategoryNames",CategoryType:"CategoryType",SecondaryPlot:"SecondaryPlot",SplitType:"SplitType",SplitValue:"SplitValue",IsMultiLevelXValues:"IsMultiLevelXValues",PlotOrder:"PlotOrder",FIELD_TYPE_CELLRANGE:"CELLRANGE",FIELD_TYPE_SERIESNAME:"SERIESNAME",FIELD_TYPE_CATEGORYNAME:"CATEGORYNAME",FIELD_TYPE_VALUE:"VALUE",FIELD_TYPE_XVALUE:"XVALUE",FIELD_TYPE_YVALUE:"YVALUE",FIELD_TYPE_BUBBLESIZE:"BUBBLESIZE",FIELD_TYPE_PERCENTAGE:"PERCENTAGE",FIELD_TYPE_TXLINK:"TxLink",FIELD_TEXT_CELLRANGE:"[CELLRANGE]",FIELD_TEXT_SERIESNAME:"[SERIES NAME]",FIELD_TEXT_CATEGORYNAME:"[CATEGORY NAME]",FIELD_TEXT_VALUE:"[VALUE]",FIELD_TEXT_XVALUE:"[XVALUE]",FIELD_TEXT_YVALUE:"[YVALUE]",FIELD_TEXT_BUBBLESIZE:"[BUBBLE SIZE]",CHART_LATIN_HEAD_FONT:"+mj-lt",CHART_LATIN_BODY_FONT:"+mn-lt",CHART_EA_HEAD_FONT:"+mj-ea",CHART_EA_BODY_FONT:"+mn-ea",CHART_CS_HEAD_FONT:"+mj-cs",CHART_CS_BODY_FONT:"+mn-cs",DefaultLineColorBrightness:.85,DefaultMarkerSize:7},b.ShapeConstants={ShapeTypePrefix:"Shape",PositiveFixedAngleConvert:6e4,PositionConver:1e5,CropPositionConver:1e3,ShadeConver:2e5,DefaultBrightness:.5,RelativeRectConver:1e3,DefaultTransparent:0,PositiveFixedPercentageConvert:1e5,DefaultOffset:2.07995176},function(a){a[a["background 1"]=0]="background 1",a[a["background 2"]=2]="background 2",a[a["text 1"]=1]="text 1",a[a["text 2"]=3]="text 2",a[a["accent 1"]=4]="accent 1",a[a["accent 2"]=5]="accent 2",a[a["accent 3"]=6]="accent 3",a[a["accent 4"]=7]="accent 4",a[a["accent 5"]=8]="accent 5",a[a["accent 6"]=9]="accent 6",a[a.hyperlink=10]="hyperlink",a[a.followedhyperlink=11]="followedhyperlink"}(z||(z={}));function L(a){if(null===a||void 0===a)return!0;if(Array.isArray(a))return 0===a.length;if(a instanceof Date)return!1;if("object"!=typeof a)return!1;for(var b in a)return!1;return!0}function M(a,b){var c,d;if(L(a))return null;for(c in a)b.indexOf(c)>=0||(d=a[c],Array.isArray(d)?0!==d.length&&null!==N(d,b)||delete a[c]:"object"==typeof d&&(d=M(d,b),null===d&&delete a[c]));return L(a)?null:a}function N(a,b){var c=[],d=a.length;return a.forEach(function(a){var d=M(a,b);null!==d&&c.push(d)}),c.length<d&&[].splice.apply(a,[].concat(0,d,c)),c.length?a:null}function O(a){delete a.solidFill,delete a.pattFill,delete a.blipFill,delete a.gradFill,delete a.noFill,delete a.grpFill,delete a.uFill,delete a.uFillTx}A=function(){function a(){}return a.Kqa=function(a,b){},a.Yra=function(a,b){return b===e.ThemeFont.Major?a.getFont("Headings"):b===e.ThemeFont.Minor?a.getFont("Body"):""},a.colorFormatToString=function(b,c,d,f){var g,h,i=b&&b.Color;if(i&&i.ColorType!==e.SolidColorType.None){if(c&&(g=i.GetColorData(),g.ColorType===e.ColorType.Theme))return a.toThemeColorString(g.Value,g.Tint);if(h=i.RGB)return d?"rgba("+h.R+","+h.G+","+h.B+","+h.A/255+")":"rgb("+h.R+","+h.G+","+h.B+")"}else if(i&&i.ColorType===e.SolidColorType.None&&f)return"";return"transparent"},a.getTransparencyFromColorFormat=function(a){var b,c=a&&a.Color;if(c&&c.ColorType!==e.SolidColorType.None){if(!u.isNullOrUndefined(c.Transparency))return parseFloat(c.Transparency.toFixed(2));if(b=c.RGB)return parseFloat((b.A/255).toFixed(2))}return 0},a.toThemeColorString=function(a,b){return z[a]+" "+parseInt(100*b+"",10)},a.fromThemeColorString=function(a){var b,c=a.split(" "),d=c.length,e=c[0].toLowerCase(),f=c[1],g=c[2];if("hyperlink"===e||"followedhyperlink"===e){if(1===d)return{index:z[e],tint:0};if(2===d)return{index:z[e],tint:parseInt(f,10)/100}}if(d>=2&&(b=z[e+" "+f],b!==i)){if(2===d)return{index:b,tint:0};if(3===d)return{index:b,tint:parseInt(g,10)/100}}},a.InitPresetColors=function(){var b=a.presetColors;b[e.ST_PresetColorVal.aliceBlue]=16766374,b[e.ST_PresetColorVal.antiqueWhite]=10079474,b[e.ST_PresetColorVal.aqua]=14276864,b[e.ST_PresetColorVal.aquamarine]=12713798,b[e.ST_PresetColorVal.azure]=16777126,b[e.ST_PresetColorVal.beige]=10938086,b[e.ST_PresetColorVal.bisque]=8439295,b[e.ST_PresetColorVal.black]=0,b[e.ST_PresetColorVal.blanchedAlmond]=8966143,b[e.ST_PresetColorVal.blue]=14221312,b[e.ST_PresetColorVal.blueViolet]=13180022,b[e.ST_PresetColorVal.brown]=2368652,b[e.ST_PresetColorVal.burlyWood]=6135762,b[e.ST_PresetColorVal.cadetBlue]=8947281,b[e.ST_PresetColorVal.chartreuse]=55660,b[e.ST_PresetColorVal.chocolate]=1661363,b[e.ST_PresetColorVal.coral]=1989375,b[e.ST_PresetColorVal.cornflowerBlue]=15234615,b[e.ST_PresetColorVal.cornsilk]=9825023,b[e.ST_PresetColorVal.crimson]=3346875,b[e.ST_PresetColorVal.cyan]=14276864,b[e.ST_PresetColorVal.dkBlue]=7733248,b[e.ST_PresetColorVal.dkCyan]=7763456,b[e.ST_PresetColorVal.dkGoldenrod]=619164,b[e.ST_PresetColorVal.dkGray]=9474192,b[e.ST_PresetColorVal.dkGreen]=21760,b[e.ST_PresetColorVal.dkKhaki]=5154734,b[e.ST_PresetColorVal.dkMagenta]=7733366,b[e.ST_PresetColorVal.dkOliveGreen]=2644808,b[e.ST_PresetColorVal.dkOrange]=30681,b[e.ST_PresetColorVal.dkOrchid]=11348866,b[e.ST_PresetColorVal.dkRed]=118,b[e.ST_PresetColorVal.dkSalmon]=5010145,b[e.ST_PresetColorVal.dkSeaGreen]=7383408,b[e.ST_PresetColorVal.dkSlateBlue]=7746621,b[e.ST_PresetColorVal.dkSlateGray]=4408104,b[e.ST_PresetColorVal.dkTurquoise]=11710208,b[e.ST_PresetColorVal.dkViolet]=11731070,b[e.ST_PresetColorVal.deepPink]=8257770,b[e.ST_PresetColorVal.deepSkyBlue]=14262784,b[e.ST_PresetColorVal.dimGray]=5855577,b[e.ST_PresetColorVal.dodgerBlue]=15891200,b[e.ST_PresetColorVal.firebrick]=1908119,b[e.ST_PresetColorVal.floralWhite]=10936831,b[e.ST_PresetColorVal.forestGreen]=1930781,b[e.ST_PresetColorVal.fuchsia]=14221529,b[e.ST_PresetColorVal.gainsboro]=12303291,b[e.ST_PresetColorVal.ghostWhite]=16756141,b[e.ST_PresetColorVal.gold]=47065,b[e.ST_PresetColorVal.goldenrod]=1805497,b[e.ST_PresetColorVal.gray]=7171437,b[e.ST_PresetColorVal.green]=27904,b[e.ST_PresetColorVal.greenYellow]=196507,b[e.ST_PresetColorVal.honeydew]=10944422,b[e.ST_PresetColorVal.hotPink]=10040319,b[e.ST_PresetColorVal.indianRed]=3881921,b[e.ST_PresetColorVal.indigo]=7274560,b[e.ST_PresetColorVal.ivory]=10944511,b[e.ST_PresetColorVal.khaki]=5954537,b[e.ST_PresetColorVal.lavender]=15641258,b[e.ST_PresetColorVal.lavenderBlush]=12822271,b[e.ST_PresetColorVal.lawnGreen]=54889,b[e.ST_PresetColorVal.lemonChiffon]=8975359,b[e.ST_PresetColorVal.ltBlue]=14205567,b[e.ST_PresetColorVal.ltCoral]=5197802,b[e.ST_PresetColorVal.ltCyan]=16777112,b[e.ST_PresetColorVal.ltGoldenrodYellow]=9761779,b[e.ST_PresetColorVal.ltGray]=11776947,b[e.ST_PresetColorVal.ltGreen]=6219358,b[e.ST_PresetColorVal.ltPink]=9008383,b[e.ST_PresetColorVal.ltSalmon]=4290815,b[e.ST_PresetColorVal.ltSeaGreen]=9541403,b[e.ST_PresetColorVal.ltSkyBlue]=16299856,b[e.ST_PresetColorVal.ltSlateGray]=8680547,b[e.ST_PresetColorVal.ltSteelBlue]=13411718,b[e.ST_PresetColorVal.ltYellow]=10027007,b[e.ST_PresetColorVal.lime]=55552,b[e.ST_PresetColorVal.limeGreen]=2797098,b[e.ST_PresetColorVal.linen]=11193582,b[e.ST_PresetColorVal.magenta]=14221529,b[e.ST_PresetColorVal.maroon]=109,b[e.ST_PresetColorVal.medAquamarine]=9945667,b[e.ST_PresetColorVal.medBlue]=11403264,b[e.ST_PresetColorVal.medOrchid]=13120682,b[e.ST_PresetColorVal.medPurple]=13715573,b[e.ST_PresetColorVal.medSeaGreen]=6330419,b[e.ST_PresetColorVal.medSlateBlue]=15284819,b[e.ST_PresetColorVal.medSpringGreen]=8639488,b[e.ST_PresetColorVal.medTurquoise]=12238640,b[e.ST_PresetColorVal.medVioletRed]=7410345,b[e.ST_PresetColorVal.midnightBlue]=6231317,b[e.ST_PresetColorVal.mintCream]=13959082,b[e.ST_PresetColorVal.mistyRose]=10068991,b[e.ST_PresetColorVal.moccasin]=7654655,b[e.ST_PresetColorVal.navajoWhite]=7193855,b[e.ST_PresetColorVal.navy]=7143424,b[e.ST_PresetColorVal.oldLace]=10738424,b[e.ST_PresetColorVal.olive]=28013,b[e.ST_PresetColorVal.oliveDrab]=1997147,b[e.ST_PresetColorVal.orange]=36057,b[e.ST_PresetColorVal.orangeRed]=15321,b[e.ST_PresetColorVal.orchid]=13322704,b[e.ST_PresetColorVal.paleGoldenrod]=7854820,b[e.ST_PresetColorVal.paleGreen]=6224222,b[e.ST_PresetColorVal.paleTurquoise]=14934908,b[e.ST_PresetColorVal.paleVioletRed]=7686353,b[e.ST_PresetColorVal.papayaWhip]=9426175,b[e.ST_PresetColorVal.peachPuff]=7845887,b[e.ST_PresetColorVal.peru]=3109301,b[e.ST_PresetColorVal.pink]=9731583,b[e.ST_PresetColorVal.plum]=13530830,b[e.ST_PresetColorVal.powderBlue]=14143106,b[e.ST_PresetColorVal.purple]=7143533,b[e.ST_PresetColorVal.red]=217,b[e.ST_PresetColorVal.rosyBrown]=7368873,b[e.ST_PresetColorVal.royalBlue]=13979170,b[e.ST_PresetColorVal.saddleBrown]=1063798,b[e.ST_PresetColorVal.salmon]=4018680,b[e.ST_PresetColorVal.sandyBrown]=3181041,b[e.ST_PresetColorVal.seaGreen]=4879911,b[e.ST_PresetColorVal.seaShell]=10799871,b[e.ST_PresetColorVal.sienna]=2508424,b[e.ST_PresetColorVal.silver]=10724259,b[e.ST_PresetColorVal.skyBlue]=14924631,b[e.ST_PresetColorVal.slateBlue]=12597837,b[e.ST_PresetColorVal.slateGray]=8088927,b[e.ST_PresetColorVal.snow]=11448063,b[e.ST_PresetColorVal.springGreen]=7133440,b[e.ST_PresetColorVal.steelBlue]=10055484,b[e.ST_PresetColorVal.tan]=6724547,b[e.ST_PresetColorVal.teal]=7171328,b[e.ST_PresetColorVal.thistle]=12687809,b[e.ST_PresetColorVal.tomato]=1456895,b[e.ST_PresetColorVal.turquoise]=12702498,b[e.ST_PresetColorVal.violet]=15159783,b[e.ST_PresetColorVal.wheat]=8111854,b[e.ST_PresetColorVal.white]=14277081,b[e.ST_PresetColorVal.whiteSmoke]=13684944,b[e.ST_PresetColorVal.yellow]=55769,b[e.ST_PresetColorVal.yellowGreen]=2797187},a.GetPresetColorRGB=function(b){return a.presetColors[b]},a.GetDimensioin=function(a){var b={0:-1,1:2,33:2,34:2,35:2,36:2,11:3,48:3,57:2,58:2};return b[a]||1},a.AreValuesAllNum=function(a,b){var c,d,e;if(u.isNullOrUndefined(b))return null;for(c=a.getArray(b.row,b.col,b.rowCount,b.colCount),d=!0,e=0;e<c.length;e++)if(!this.AreAllNumbers(c[e])){d=!1;break}return d},a.AreValuesDateTime=function(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,v;if(u.isNullOrUndefined(b)||0===b.length)return!1;for(d=!0,f=this.GetStylesFromCellRefers(a,b,c),g=this.GetValuesFromCellRefers(a,b,c),h=0;h<f.length;h++){for(i=f[h],j=i.length,k=i[0]&&i[0].length,l=0;l<k;l++){for(m=0;m<j;m++)if(n=i[m][l],e=g[h][m][l],!u.isNullOrUndefined(e)&&!(e instanceof Date)&&(u.isNullOrUndefined(n)||!I(n.FormatCode)||!G(e))){d=!1;break}if(!d)break}if(!d)break}if(d){for(o=0,p=g;o<p.length;o++)for(q=p[o],r=0,s=q;r<s.length;r++)if(t=s[r],!u.isNullOrUndefined(t))for(v=0;v<t.length;v++)if(e=t[v],!u.isNullOrUndefined(e))return!0;return!1}return!1},a.AreAllNumbers=function(a,b){var c,d,e,f,g,h;if(!a)return!1;for(c=!0,d=0,e=a;d<e.length;d++)if(f=e[d],g=G(f),h=b&&(null===f||f&&f._error),!g&&!h){c=!1;break}return c},a.AreAllNull=function(a){var b,c,d,e=!0;for(b=0,c=a;b<c.length;b++)if(d=c[b],null!==d&&void 0!==d){e=!1;break}return e},a.AreAllDate=function(a){var b,c,d;for(b=0,c=a;b<c.length;b++)if(d=c[b],!(d instanceof Date))return!1;return!0},a.TryAllToNumbers=function(a,b,c){var d,e,f,g,h,i;if(u.isNullOrUndefined(a)||0===a.length)return!1;for(d=!0,e=[],f=a.length,g=0;g<f;g++)h=a[g],u.isNullOrUndefined(h)?c||e.push(h):(i={value:void 0},this.TryToDouble(h,i)?e.push(i.value):(d=!1,e.push(0)));return Array.prototype.push.apply(b,e),d},a.TryToDouble=function(a,b,c){var d,e,f,g,i,j,k={JAN:1,FEB:2,MAR:3,APR:4,MAY:5,JUN:6,JUL:7,AUG:8,SEP:9,OCT:10,NOV:11,DEC:12},l=h;if(!a)return b.value=0,!0;d=typeof a;try{if("number"===d)l=a;else if("string"===d&&c){if(a=a.trim(),0===a.length)return b.value=0,!0;if(e=".",f=",","."!==e&&"."!==f&&a.indexOf(".")>=0||","!==e&&","!==f&&a.indexOf(",")>=0)return!1;if(g=!1,"%"===a.charAt(a.length-1)&&(g=!0,a=a.substr(0,a.length-1)),"$"===a[0]||"$"===a[a.length-1])return!1;if(a.indexOf(e)!==a.lastIndexOf(e))return!1;if(a.length>=2&&"0"===a[0]&&"x"===a[1])return!1;if(l=+a,t.isNaNOrInfinite(l)){if(i=a.charCodeAt(0),i|=32,a.length>4&&i>=96&&i<=122&&(j=a[0]+a[1]+a[2],j=j.toUpperCase(),!k[j]))return!1;if("/"===a[0]||"/"===a[a.length-1])return!1;if("#"===a[0]||"#"===a[a.length-1])return!1;if(l=new Date(a),t.isNaNOrInfinite(l.valueOf()))return!1;l=s.ToOADate(l)}g&&(l/=100)}else if("boolean"===d)l=a?1:0;else{if(!(a instanceof Date))return!1;l=s.ToOADate(a)}}catch(a){return!1}return b.value=l,!0},a.IsBarChart=function(a){return a===e.ChartType.barClustered||a===e.ChartType.barClustered3D||a===e.ChartType.barStacked||a===e.ChartType.barStacked100||a===e.ChartType.barStacked1003D||a===e.ChartType.barStacked3D},a.zpa=function(b){var c,d=a.chartTypeDict;if(!d){d=a.chartTypeDict={};for(c in e.ChartType)d[e.ChartType[c]]=c.toLowerCase()}return d[b]},a.IsColumnChart=function(b){return a.zpa(b).indexOf("column")>=0},a.IsAnyPieChart=function(a){return this.IsPieChart(a)||this.IsDoughnutChart(a)||this.IsOfPieChart(a)},a.IsPieChart=function(b){return a.zpa(b).indexOf("pie")>=0},a.IsPieOrDoughnutChart=function(a){return this.IsPieChart(a)||this.IsDoughnutChart(a)},a.IsRadarChart=function(a){return a===e.ChartType.radar||a===e.ChartType.radarFilled||a===e.ChartType.radarMarkers},a.IsOfPieChart=function(a){return a===e.ChartType.pieOfPie||a===e.ChartType.barOfPie},a.IsStackedChart=function(b){return a.zpa(b).indexOf("stacked")>=0},a.IsStacked100Chart=function(b){return a.zpa(b).indexOf("stacked100")>=0},a.IsLineChart=function(a){return a===e.ChartType.line3D||a===e.ChartType.line||a===e.ChartType.lineMarkers||a===e.ChartType.lineMarkersStacked||a===e.ChartType.lineMarkersStacked100||a===e.ChartType.lineStacked||a===e.ChartType.lineStacked100},a.IsLineMarkerChart=function(a){return a===e.ChartType.lineMarkers||a===e.ChartType.lineMarkersStacked||a===e.ChartType.lineMarkersStacked100},a.IsLineSeries=function(a){return a===e.ChartType.line||a===e.ChartType.lineMarkers||a===e.ChartType.lineMarkersStacked||a===e.ChartType.lineMarkersStacked100||a===e.ChartType.lineStacked||a===e.ChartType.lineStacked100||a===e.ChartType.xyScatterLines||a===e.ChartType.xyScatterLinesNoMarkers||a===e.ChartType.xyScatterSmooth||a===e.ChartType.xyScatterSmoothNoMarkers||a===e.ChartType.radar||a===e.ChartType.radarMarkers},a.IsMarkerSeries=function(a){return a===e.ChartType.lineMarkers||a===e.ChartType.lineMarkersStacked||a===e.ChartType.lineMarkersStacked100||a===e.ChartType.radarMarkers||a===e.ChartType.xyScatter||a===e.ChartType.xyScatterLines||a===e.ChartType.xyScatterSmooth},a.IsFillSeries=function(a){return!this.IsLineSeries(a)&&a!==e.ChartType.xyScatter},a.IsAreaChart=function(b){return a.zpa(b).indexOf("area")>=0},a.IsScatterChart=function(b){return a.zpa(b).indexOf("scatter")>=0},a.IsSurfaceChart=function(b){return a.zpa(b).indexOf("surface")>=0},a.IsBubbleChart=function(b){return a.zpa(b).indexOf("bubble")>=0},a.IsSmoothLine=function(b){return a.zpa(b).indexOf("smooth")>=0},a.Is3DChart=function(b){var c=a.zpa(b);return c.indexOf("3d")>=0&&b!==e.ChartType.bubble3DEffect||this.IsSurfaceChart(b)||c.indexOf("cylinder")>=0||c.indexOf("cone")>=0||c.indexOf("pyramid")>=0},a.IsSurface3DChart=function(a){return a===e.ChartType.surface||a===e.ChartType.surfaceWireframe},a.IsDoughnutChart=function(a){return a===e.ChartType.doughnut||a===e.ChartType.doughnutExploded},a.IsExplodedChart=function(a){return a===e.ChartType.pieExploded3D||a===e.ChartType.doughnutExploded||a===e.ChartType.pieExploded},a.HasSeriesAx=function(a){return a===e.ChartType.column3D||a===e.ChartType.area3D||a===e.ChartType.line3D||this.IsSurfaceChart(a)},a.IsStockChart=function(b){return a.zpa(b).indexOf("stock")>=0},a.IsTreeMap=function(a){return a===e.ChartType.treemap},a.IsSunburstChart=function(a){return a===e.ChartType.sunburst},a.IsSunburstOrTreemapChart=function(b){return a.IsTreeMap(b)||a.IsSunburstChart(b)},a.IsStockHLC=function(a){return a===e.ChartType.stockHLC},a.IsBuiltInComboChart=function(a){return this.IsStockChart(a)},a.ConvertToTextUnderlineType=function(a){var b=e.TextUnderlineType.None;return a===e.UnderlineType.Single?b=e.TextUnderlineType.Single:a===e.UnderlineType.Double&&(b=e.TextUnderlineType.Double),b},a.ToUnderlineType=function(a){var b=e.UnderlineType.None;return a===e.TextUnderlineType.Single?b=e.UnderlineType.Single:a===e.TextUnderlineType.Double&&(b=e.UnderlineType.Double),b},a.MapToGroupType=function(a){var b={12:9,13:9,14:9,15:3,16:3,17:3,6:3,18:8,19:8,20:8,21:2,22:2,23:2,9:11,24:11,25:11,26:11,27:11,28:11,5:4,10:12,29:12,30:12,32:12,31:5,4:5,1:14,33:14,34:14,35:14,36:14,8:7,37:7,38:7,39:1,40:1,7:1,3:10,41:10,2:13,42:13,43:13,44:6,45:6,46:6,47:6,11:14,48:14,49:11,50:11,51:11,52:11};return b[a]||F.UnKnown},a.HasSeriesLines=function(a){return this.IsOfPieChart(a)},a.HasHiLoLines=function(a){return this.IsStockChart(a)},a.HasUpDownBars=function(a){return a===e.ChartType.stockOHLC||a===e.ChartType.stockVOHLC},a.RangeToReferences=function(b){var c,d,e,f=[];for(c=0,d=b.Areas;c<d.length;c++)e=d[c],f.push(a.CreateReference(e.Worksheet.name(),e.Row,e.Column,e.RowCount,e.ColumnCount));return f},a.CreateReference=function(a,b,c,d,e){return 1===d&&1===e?new y(a,b,c):new y(a,b,c,b+d-1,c+e-1)},a.ReferencesToFormula=function(a,b){var c,d;if(b===i&&(b=!1),u.isNullOrUndefined(a)||0===a.length)return h;for(c=[],d=0;d<a.length;d++)d>0&&c.push(","),b?c.push(a[d].ToR1C1Text()):c.push(a[d].ToA1Text(0,0));return c.join("")},a.getRangeInfoByFormula=function(a,b){var c,d,e,g;return b&&(c=f.formulaToRanges(a,b,0,0),d=c[0],d&&(e=a.parent,g=e.getSheetFromName(d.sheetName)))?{sheet:g,range:d.ranges&&d.ranges[0]}:{range:{}}},a.isContinuousRange=function(a,b,c){if(a.sheet!==b.sheet)return!1;var d,e,f,g,h,i,j,k,l,m;if(c){if(d=a.range,f=d.rowCount,g=d.col,h=d.colCount,i=b.range,k=i.rowCount,l=i.col,f===k&&g+h===l)return!0}else if(d=a.range,f=d.rowCount,e=d.row,h=d.colCount,i=b.range,m=i.colCount,j=d.row,h===m&&e+f===j)return!0},a.GetValuesFromCellRefers=function(a,b,c){var d,e,f,g,h=[];if(u.isNullOrUndefined(b)||0===b.length||b.some(function(a){return a.IsError}))return h;for(d=0,e=b;d<e.length;d++)f=e[d],g=this.GetValuesFromCellRefer(a,f,c),u.isNullOrUndefined(g)||h.push(g);return h},a.GetValuesFromCellRefer=function(a,b,c){var d,e=a.Apa(b.WorksheetName);return u.isNullOrUndefined(e)?h:d=c?e.Bpa(b.Row,b.Column,b.RowCount,b.ColumnCount,!1):e.Cpa(b.Row,b.Column,b.RowCount,b.ColumnCount,!1)},a.GetStylesFromCellRefers=function(a,b,c){var d,e,f,g,h=[];if(u.isNullOrUndefined(b)||0===b.length||b.some(function(a){return a.IsError}))return h;for(d=0,e=b;d<e.length;d++)f=e[d],g=this.GetStylesFromCellRefer(a,f,c),u.isNullOrUndefined(g)||h.push(g);return h},a.GetStylesFromCellRefer=function(a,b,c){var d,e=a.Apa(b.WorksheetName);return u.isNullOrUndefined(e)?h:d=c?e.Dpa(b.Row,b.Column,b.RowCount,b.ColumnCount):e.Epa(b.Row,b.Column,b.RowCount,b.ColumnCount)},a.GetPrimaryAxises=function(a){var b,c=[];return a.axes&&a.axes.length>0&&(b=a.axes[0],c.push(b.axId),c.push(b.crossAx),c.axisGroup=b.AxisGroup),c},a.GetAxises=function(b,c){var d,f,g,h,i,j=a.GetPrimaryAxises(b);if(c===e.AxisGroup.primary)return j;if(d=[],!u.isNullOrUndefined(b.axes))for(f=b.axes.filter(function(a){return j.indexOf(a.axId)<0}),g=0,h=f;g<h.length;g++)i=h[g],d.push(i.axId);return d},a.GetCategoryAxises=function(a){var b,c,d,e=[];for(b=0,c=a.chartGroups;b<c.length;b++)d=c[b],!u.isNullOrUndefined(d.axId)&&d.axId.length>0&&e.push(d.axId[0]);return e},a.GetIs2016ChartByPlotArea=function(b){var c=a.Get2016ChartType(b);return!!c},a.GetIs2016ChartByChartType=function(a){return 53===a||54===a||55===a||56===a||57===a||58===a||59===a||60===a},a.Get2016ChartType=function(a){var b,c=a&&a.plotAreaRegion&&a.plotAreaRegion.series;return c&&c.length>0&&(b=c[0].layoutId),b},a.GetChartType=function(b){var c,d,f,g,h,i,j=a.Get2016ChartType(b);if(j)return a.GetChartTypeBy2016InnerChartType(j);if(c=this.GetStockChartType(b),!u.isNullOrUndefined(c))return c;for(d=[],f=0,g=b.chartGroups;f<g.length;f++)h=g[f],i=a.GetChartTypeByChartBase(h),d.indexOf(i)<0&&d.push(i);return 0===d.length?e.ChartType.columnClustered:d.length>1?e.ChartType.combo:d[0]},a.GetStockChartType=function(b){var c=a.GetStockChart(b);if(0===c.length)return h;if(!u.isNullOrUndefined(b.chartGroups)&&b.chartGroups.length>1){if(3===c[0].ser.length)return e.ChartType.stockVHLC;if(4===c[0].ser.length)return e.ChartType.stockVOHLC}else{if(3===c[0].ser.length)return e.ChartType.stockHLC;if(4===c[0].ser.length)return e.ChartType.stockOHLC}return h},a.GetChartTypeByChartBase=function(b,c){return c===i&&(c=h),b.chartType===e.CT_ChartType.CT_BarChart||b.chartType===e.CT_ChartType.CT_Bar3DChart?a.GetChartTypeByBarChart(b):b.chartType===e.CT_ChartType.CT_AreaChart||b.chartType===e.CT_ChartType.CT_Area3DChart?a.GetChartTypeByAreaChart(b):b.chartType===e.CT_ChartType.CT_LineChart||b.chartType===e.CT_ChartType.CT_Line3DChart?a.GetChartTypeByLineChart(b,c):[e.CT_ChartType.CT_PieChart,e.CT_ChartType.CT_Pie3DChart,e.CT_ChartType.CT_DoughnutChart,e.CT_ChartType.CT_OfPieChart].indexOf(b.chartType)>=0?a.GetChartTypeByPieChart(b):b.chartType===e.CT_ChartType.CT_RadarChart?a.GetChartTypeByRadarChart(b,c):b.chartType===e.CT_ChartType.CT_ScatterChart?a.GetChartTypeByScatterChart(b,c):b.chartType===e.CT_ChartType.CT_BubbleChart?a.GetChartTypeByBubbleChart(b):b.chartType===e.CT_ChartType.CT_SurfaceChart||b.chartType===e.CT_ChartType.CT_Surface3DChart?a.GetChartTypeBySurfaceChart(b):e.ChartType.columnClustered},a.GetChartTypeBy2016InnerChartType=function(a){if(!u.isNullOrUndefined(a))switch(a){case e.CT_ChartType.CT_BoxWhisker:return e.ChartType.boxWhisker;case e.CT_ChartType.CT_Funnel:return e.ChartType.funnel;case e.CT_ChartType.CT_ParetoLine:return e.ChartType.paretoLine;case e.CT_ChartType.CT_RegionMap:return e.ChartType.regionMap;case e.CT_ChartType.CT_Sunburst:return e.ChartType.sunburst;case e.CT_ChartType.CT_Treemap:return e.ChartType.treemap;case e.CT_ChartType.CT_Waterfall:return e.ChartType.waterfall;case e.CT_ChartType.CT_ClusteredColumn:return e.ChartType.clusteredColumn}return e.CT_ChartType.CT_Treemap},a.Get2016InnerChartTypeByChartType=function(a){if(!u.isNullOrUndefined(a))switch(a){case e.ChartType.boxWhisker:return e.CT_ChartType.CT_BoxWhisker;case e.ChartType.funnel:return e.CT_ChartType.CT_Funnel;case e.ChartType.paretoLine:return e.CT_ChartType.CT_ParetoLine;case e.ChartType.regionMap:return e.CT_ChartType.CT_RegionMap;case e.ChartType.sunburst:return e.CT_ChartType.CT_Sunburst;case e.ChartType.treemap:return e.CT_ChartType.CT_Treemap;case e.ChartType.waterfall:return e.CT_ChartType.CT_Waterfall;case e.ChartType.clusteredColumn:return e.CT_ChartType.CT_ClusteredColumn}return e.CT_ChartType.CT_Treemap},a.GetChartTypeByBarChart=function(a){
var b=a.chartType===e.CT_ChartType.CT_Bar3DChart;if(u.isNullOrUndefined(a.grouping))return b?a.barDir===e.ST_BarDir.bar?e.ChartType.barClustered3D:e.ChartType.columnClustered3D:a.barDir===e.ST_BarDir.bar?e.ChartType.barClustered:e.ChartType.columnClustered;switch(a.grouping){case e.ST_BarGrouping.percentStacked:return b?a.barDir===e.ST_BarDir.bar?e.ChartType.barStacked1003D:e.ChartType.columnStacked1003D:a.barDir===e.ST_BarDir.bar?e.ChartType.barStacked100:e.ChartType.columnStacked100;case e.ST_BarGrouping.clustered:return b?a.barDir===e.ST_BarDir.bar?e.ChartType.barClustered3D:e.ChartType.columnClustered3D:a.barDir===e.ST_BarDir.bar?e.ChartType.barClustered:e.ChartType.columnClustered;case e.ST_BarGrouping.standard:return b?a.barDir===e.ST_BarDir.bar?e.ChartType.barClustered3D:e.ChartType.column3D:a.barDir===e.ST_BarDir.bar?e.ChartType.barClustered:e.ChartType.columnClustered;case e.ST_BarGrouping.stacked:return b?a.barDir===e.ST_BarDir.bar?e.ChartType.barStacked3D:e.ChartType.columnStacked3D:a.barDir===e.ST_BarDir.bar?e.ChartType.barStacked:e.ChartType.columnStacked}return e.ChartType.columnClustered},a.GetChartTypeByAreaChart=function(a){var b=a.chartType===e.CT_ChartType.CT_Area3DChart;if(u.isNullOrUndefined(a.grouping))return b?e.ChartType.area3D:e.ChartType.area;switch(a.grouping){case e.ST_Grouping.percentStacked:return b?e.ChartType.areaStacked1003D:e.ChartType.areaStacked100;case e.ST_Grouping.standard:return b?e.ChartType.area3D:e.ChartType.area;case e.ST_Grouping.stacked:return b?e.ChartType.areaStacked3D:e.ChartType.areaStacked}return e.ChartType.area},a.GetChartTypeByLineChart=function(a,b){var c=a.chartType===e.CT_ChartType.CT_Line3DChart,d=a.marker;if(d&&b&&b.marker&&b.marker.symbol===e.MarkerStyle.MarkerStyleNone&&(d=!1),u.isNullOrUndefined(a.grouping)){if(c)return e.ChartType.line3D}else switch(a.grouping){case e.ST_Grouping.percentStacked:return c?e.ChartType.line3D:d?e.ChartType.lineMarkersStacked100:e.ChartType.lineStacked100;case e.ST_Grouping.standard:return c?e.ChartType.line3D:d?e.ChartType.lineMarkers:e.ChartType.line;case e.ST_Grouping.stacked:return c?e.ChartType.line3D:d?e.ChartType.lineMarkersStacked:e.ChartType.lineStacked;default:return e.ChartType.area}return d?e.ChartType.lineMarkers:e.ChartType.line},a.GetChartTypeByPieChart=function(a){if(a.chartType===e.CT_ChartType.CT_PieChart)return e.ChartType.pie;if(a.chartType===e.CT_ChartType.CT_Pie3DChart)return e.ChartType.pie3D;if(a.chartType===e.CT_ChartType.CT_OfPieChart){var b=a;return b.ofPieType===e.ST_OfPieType.bar?e.ChartType.barOfPie:e.ChartType.pieOfPie}return a.chartType===e.CT_ChartType.CT_DoughnutChart?e.ChartType.doughnut:e.ChartType.pie},a.GetChartTypeByRadarChart=function(a,b){var c=b&&b.marker&&b.marker.symbol&&b.marker.symbol===e.MarkerStyle.MarkerStyleNone;if(!u.isNullOrUndefined(a.radarStyle))switch(a.radarStyle){case e.ST_RadarStyle.standard:case e.ST_RadarStyle.marker:return c?e.ChartType.radar:e.ChartType.radarMarkers;case e.ST_RadarStyle.filled:return e.ChartType.radarFilled}return 2},a.GetChartTypeByScatterChart=function(a,b){var c,d,f,g,h,i,j,k=a.scatterStyle;return u.isNullOrUndefined(k)?e.ChartType.xyScatter:(c={0:1,1:36,2:35,3:e.ChartType.xyScatter,4:34,5:33},d=k,f=b||a.ser[0],g=f&&f.marker&&f.marker.symbol,h=4===g,i=f&&f.spPr&&f.spPr.ln,j=i&&i.noFill,2===d?j?d=3:h&&(d=1):5===d&&h&&(d=4),c[d])},a.GetChartTypeByBubbleChart=function(a){return a.bubble3D?e.ChartType.bubble3DEffect:e.ChartType.bubble},a.GetChartTypeBySurfaceChart=function(a){var b=a.chartType===e.CT_ChartType.CT_Surface3DChart;return b?a.wireframe?e.ChartType.surfaceWireframe:e.ChartType.surface:a.wireframe?e.ChartType.surfaceTopViewWireframe:e.ChartType.surfaceTopView},a.GetTextFieldTypeString=function(a){var c={0:b.ChartConstants.FIELD_TYPE_CELLRANGE,1:b.ChartConstants.FIELD_TYPE_SERIESNAME,2:b.ChartConstants.FIELD_TYPE_CATEGORYNAME,3:b.ChartConstants.FIELD_TYPE_VALUE,4:b.ChartConstants.FIELD_TYPE_XVALUE,5:b.ChartConstants.FIELD_TYPE_YVALUE,6:b.ChartConstants.FIELD_TYPE_BUBBLESIZE,7:b.ChartConstants.FIELD_TYPE_PERCENTAGE,8:b.ChartConstants.FIELD_TYPE_TXLINK};return c[a]||""},a.GetTextFieldTypeText=function(a){var c={0:b.ChartConstants.FIELD_TEXT_CELLRANGE,1:b.ChartConstants.FIELD_TEXT_SERIESNAME,2:b.ChartConstants.FIELD_TEXT_CATEGORYNAME,3:b.ChartConstants.FIELD_TEXT_VALUE,4:b.ChartConstants.FIELD_TEXT_XVALUE,5:b.ChartConstants.FIELD_TEXT_YVALUE,6:b.ChartConstants.FIELD_TEXT_BUBBLESIZE};return c[a]||""},a.GetTextFieldType=function(a){var c=e.TextFieldType.CellRange;switch(a){case b.ChartConstants.FIELD_TYPE_CELLRANGE:c=e.TextFieldType.CellRange;break;case b.ChartConstants.FIELD_TYPE_SERIESNAME:c=e.TextFieldType.SeriesName;break;case b.ChartConstants.FIELD_TYPE_CATEGORYNAME:c=e.TextFieldType.CategoryName;break;case b.ChartConstants.FIELD_TYPE_VALUE:c=e.TextFieldType.Value;break;case b.ChartConstants.FIELD_TYPE_XVALUE:c=e.TextFieldType.XValue;break;case b.ChartConstants.FIELD_TYPE_YVALUE:c=e.TextFieldType.YValue;break;case b.ChartConstants.FIELD_TYPE_BUBBLESIZE:c=e.TextFieldType.BubbleSize;break;case b.ChartConstants.FIELD_TYPE_PERCENTAGE:c=e.TextFieldType.Percentage;break;case b.ChartConstants.FIELD_TYPE_TXLINK:c=e.TextFieldType.TxLink}return c},a.GetPrimaryPieChart=function(a){return a[0]},a.GetPlotAreaCharts=function(a,b){return a.chartGroups?a.chartGroups.filter(function(a){return a.chartType===b}):[]},a.GetAreaChart=function(b){return a.GetPlotAreaCharts(b,4)},a.GetArea3DChart=function(b){var c=a.GetPlotAreaCharts(b,5);return c.length>0?c[0]:h},a.GetBarChart=function(b){return a.GetPlotAreaCharts(b,6)},a.GetBar3DChart=function(b){var c=a.GetPlotAreaCharts(b,7);return c.length>0?c[0]:h},a.GetLineChart=function(b){return a.GetPlotAreaCharts(b,8)},a.GetLine3DChart=function(b){var c=a.GetPlotAreaCharts(b,9);return c.length>0?c[0]:h},a.GetStockChart=function(b){return a.GetPlotAreaCharts(b,0)},a.GetRadarChart=function(b){return a.GetPlotAreaCharts(b,2)},a.GetScatterChart=function(b){return a.GetPlotAreaCharts(b,1)},a.GetPieCharts=function(a){var b=[10,11,12,13];return a.chartGroups?a.chartGroups.filter(function(a){return b.indexOf(a.chartType)>=0}):[]},a.GetPieChart=function(b){return a.GetPlotAreaCharts(b,10)},a.GetPie3DChart=function(b){var c=a.GetPlotAreaCharts(b,11);return c.length>0?c[0]:h},a.GetDoughnutChart=function(b){return a.GetPlotAreaCharts(b,12)},a.GetOfPieChart=function(b){return a.GetPlotAreaCharts(b,13)},a.GetSurfaceChart=function(b){return a.GetSurface3DChartOrChart(b,14)},a.GetSurface3DChart=function(b){return a.GetSurface3DChartOrChart(b,15)},a.GetSurface3DChartOrChart=function(b,c){var d=a.GetPlotAreaCharts(b,c);return d.length>0?d[0]:h},a.GetBubbleChart=function(b){return a.GetPlotAreaCharts(b,3)},a.GetAxes=function(a,b){return a.axes.filter(function(a){return a.axisType===b})},a.removeEmptyArrayProperties=function(a){var b,c;for(b in a)a.hasOwnProperty(b)&&(c=a[b],Array.isArray(c)&&0===c.length&&delete a[b])},a.allSeriesIsScatterOrBubble=function(b){var c,d,e,f,g=b.ChartType;if(a.IsBubbleChart(g)||a.IsScatterChart(g))return!0;if(0===g&&(c=b.SeriesCollection.AllSers,d=c.length,d>0)){if(e=c[0].ChartType,f=void 0,a.IsBubbleChart(e)){for(f=1;f<d;f++)if(!a.IsBubbleChart(c[f].ChartType))return!1;return!0}if(a.IsScatterChart(e)){for(f=1;f<d;f++)if(!a.IsScatterChart(c[f].ChartType))return!1;return!0}}return!1},a.getLineFormatInfo=function(b,c,d,e){var f,g,i,j=b&&b.Format&&b.Format.Line;return j?(f=j.Weight,g={},f>=0&&(g.width=f),i=a.colorFormatToString(j,c,d,e),d||(g.transparency=a.getTransparencyFromColorFormat(j)),(i||""===i&&e)&&(g.color=i),g):h},a.presetColors={},a.defineProperty=H,a.simpleJSONObject=M,a.umb=O,a}(),b.ChartUtility=A,A.InitPresetColors(),B=function(){function a(){}return a.InitGradientStopPresetColor=function(a,b){},a.InitGradientStopTwoColor=function(a,b){},a.InitGradientTwoColorCornerStops=function(a,b){},a.InitGradientTwoColorCenterStops=function(a,b){},a.InitGradientOneColorCornerStops=function(a,b){},a.InitGradientOneColorCenterStops=function(a,b){},a.InitGradientStopOneColor=function(a,b){},a.InitGradientStops=function(a,b,c){},a.GetGradientAngle=function(a,b){var c=0;switch(a){case e.GradientStyle.GradientHorizontal:1===b?c=90:2===b||4===b?c=270:3===b&&(c=90);break;case e.GradientStyle.GradientVertical:1===b||3===b?c=0:2!==b&&4!==b||(c=180);break;case e.GradientStyle.GradientDiagonalUp:1===b||3===b?c=45:2!==b&&4!==b||(c=225);break;case e.GradientStyle.GradientDiagonalDown:1===b||3===b?c=135:2!==b&&4!==b||(c=315);break;case e.GradientStyle.GradientFromCorner:case e.GradientStyle.GradientFromCenter:c=135}return c},a.GetTintAndShadeByDegree=function(a){return 2*(a-.5)},a.ToLum=function(a){return 1e5-l(2e5*(1-a))},a.FromLum=function(a){return 1-(1e5-a)/2e5},a}(),b.ShapeUtility=B;function P(a){return 8===a||!A.Is3DChart(a)&&(A.IsBarChart(a)||A.IsColumnChart(a)||A.IsLineChart(a))}function Q(a){var b,c,d,e=a.ChartType;if(0!==e)return P(e);for(b=a.SeriesCollection.AllSers,c=0,d=b.length;c<d;c++)if(P(b[c].ChartType))return!0;return!1}C=function(){function a(){}return a.CalculateValidMinimum=function(a,b,c,d,e,f){return c?a===t.DOUBLE_MAX_VALUE||a<=0?b===t.DOUBLE_MIN_VALUE||b<=0?a=1:b<1?a=b/d:1===b?a=1/d:1<b&&(a=1):b<=a&&(b===t.DOUBLE_MIN_VALUE||b<=0||(b<1&&e&&!f?a=b/d:1===b&&e&&!f?a=1/d:1<b&&(e||!f)&&(a=1))):a===t.DOUBLE_MAX_VALUE?b===t.DOUBLE_MIN_VALUE?a=0:b<0?a=2*b:0===b?a=-1:0<b&&(a=0):b<=a&&(b===t.DOUBLE_MIN_VALUE||(b<0&&e&&!f?b!==a&&(a=2*b):0===b&&e&&!f?a=-1:0<b&&(e||!f)&&(a=0))),a},a.CalculateValidMaximum=function(a,b,c,d){return c?b<=a&&(a<1?b=1:1===a?b=d:1<a&&(b=a*d)):b<=a&&(a<0?b=0:0===a?b=1:0<a&&b!==a&&(b=2*a)),b},a.calcMinMax=function(a,b,c,d){var e,f,g=b-a,h=.05*g,i=d.ChartType;return c?(e=a,f=b,b<1&&(e=f=1)):a>=0?(f=b,Q(d)&&(f+=h),6*g>b?e=0:(e=a-g/2,(A.IsScatterChart(i)||A.IsBubbleChart(i))&&(e=a))):b<=0?(e=a,Q(d)&&(e-=h),6*g+a>0?f=0:(f=b+g/2,(A.IsScatterChart(i)||A.IsBubbleChart(i))&&(f=b))):(e=a-h,f=b+h),{Min:e,Max:f}},a.CalculateMinimum2=function(a,b,c,d,e){return a=d?n(e,l(t.log(a,e))):l(a/c)*c},a.Gpa={},a.CalculateMaximum2=function(a,b,c,d,e){return b=d?n(e,o(t.log(b,e))):o(b/c)*c},a.CalculateMajorUnit=function(a,b,c,d,e,f){var g,h=p(b-a);return e?g=f:(g=n(10,l(t.log(h,10))),h/g<=1.6?g/=5:h/g<=4?g/=2:h/g>8&&(g*=2),c||(g=j(g,d))),g},a.CalculateMinorUnit=function(a,b,c,d){var e;return e=d?c:c/5},a.GenerateAxisId=function(){for(var a=0;;)if(a=l(9e7*Math.random())+1e7,!this.Gpa[a]){this.Gpa[a]=!0;break}return a},a}(),b.AxisUtility=C,D=function(){function a(){}return Object.defineProperty(a.prototype,"ChartFormat",{get:function(){return u.isNullOrUndefined(this.Kb)&&(this.Kb=this.GetDefaultFormat()),this.Kb},enumerable:!0,configurable:!0}),a.prototype.CreateFormat=function(){return h},a.prototype.GetDefaultFormat=function(){return this.CreateFormat()},a.prototype.GetFormatInternal=function(){return this.Kb},a.prototype.FromShapeProperties=function(a){var b=this;u.isNullOrUndefined(a)?b.ClearFormat():(u.isNullOrUndefined(b.Kb)&&(b.Kb=b.CreateFormat()),b.Kb.FromOOModel(a))},a.prototype.ToShapeProperties=function(){return u.isNullOrUndefined(this.Kb)?h:this.Kb.ToOOModel()},a.prototype.Delete=function(){u.isNullOrUndefined(this.Kb)||(this.Kb.ParentStateful=h)},a.prototype.ClearFormat=function(){var a=this;u.isNullOrUndefined(a.Kb)||(a.Kb.SetParentForChildren(h),a.Kb.ParentStateful=h,a.Kb=h)},a}(),b.ChartElementBase=D,function(a){a[a.BevelNone=0]="BevelNone",a[a.BevelRelaxedInset=1]="BevelRelaxedInset",a[a.BevelCircle=2]="BevelCircle",a[a.BevelSlope=3]="BevelSlope",a[a.BevelCross=4]="BevelCross",a[a.BevelAngle=5]="BevelAngle",a[a.BevelSoftRound=6]="BevelSoftRound",a[a.BevelConvex=7]="BevelConvex",a[a.BevelCoolSlant=8]="BevelCoolSlant",a[a.BevelDivot=9]="BevelDivot",a[a.BevelRiblet=10]="BevelRiblet",a[a.BevelHardEdge=11]="BevelHardEdge",a[a.BevelArtDeco=12]="BevelArtDeco"}(E=b.BevelType||(b.BevelType={})),function(a){a[a.UnKnown=0]="UnKnown",a[a.Area3DGroup=1]="Area3DGroup",a[a.Bar3DGroup=2]="Bar3DGroup",a[a.Column3DGroup=3]="Column3DGroup",a[a.Line3DGroup=4]="Line3DGroup",a[a.Pie3DGroup=5]="Pie3DGroup",a[a.SurfaceGroup=6]="SurfaceGroup",a[a.AreaGroup=7]="AreaGroup",a[a.BarGroup=8]="BarGroup",a[a.ColumnGroup=9]="ColumnGroup",a[a.DoughnutGroup=10]="DoughnutGroup",a[a.LineGroup=11]="LineGroup",a[a.PieGroup=12]="PieGroup",a[a.RadarGroup=13]="RadarGroup",a[a.XYGroup=14]="XYGroup"}(F=b.ChartGroupType||(b.ChartGroupType={}))},"./dist/plugins/floatingObject/drawing/drawingInterface.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja;Object.defineProperty(b,"__esModule",{value:!0}),function(a){a[a.noStrike=0]="noStrike",a[a.sngStrike=1]="sngStrike",a[a.dblStrike=2]="dblStrike"}(d=b.ST_TextStrikeType||(b.ST_TextStrikeType={})),function(a){a[a.rnd=0]="rnd",a[a.sq=1]="sq",a[a.flat=2]="flat"}(e=b.ST_LineCap||(b.ST_LineCap={})),function(a){a[a.email=0]="email",a[a.screen=1]="screen",a[a.print=2]="print",a[a.hqprint=3]="hqprint",a[a.none=4]="none"}(f=b.ST_BlipCompression||(b.ST_BlipCompression={})),function(a){a[a.shape=0]="shape",a[a.circle=1]="circle",a[a.rect=2]="rect"}(g=b.ST_PathShadeType||(b.ST_PathShadeType={})),function(a){a[a.legacyMatte=0]="legacyMatte",a[a.legacyPlastic=1]="legacyPlastic",a[a.legacyMetal=2]="legacyMetal",a[a.legacyWireframe=3]="legacyWireframe",a[a.matte=4]="matte",a[a.plastic=5]="plastic",a[a.metal=6]="metal",a[a.warmMatte=7]="warmMatte",a[a.translucentPowder=8]="translucentPowder",a[a.powder=9]="powder",a[a.dkEdge=10]="dkEdge",a[a.softEdge=11]="softEdge",a[a.clear=12]="clear",a[a.flat=13]="flat",a[a.softmetal=14]="softmetal"}(h=b.ST_PresetMaterialType||(b.ST_PresetMaterialType={})),function(a){a[a.maxMin=0]="maxMin",a[a.minMax=1]="minMax"}(i=b.ST_Orientation||(b.ST_Orientation={})),function(a){a[a.between=0]="between",a[a.midCat=1]="midCat"}(j=b.ST_CrossBetween||(b.ST_CrossBetween={})),function(a){a[a.TwoCellAnchor=0]="TwoCellAnchor",a[a.OneCellAnchor=1]="OneCellAnchor",a[a.AbsoluteAnchor=2]="AbsoluteAnchor",a[a.RelSizeAnchor=3]="RelSizeAnchor",a[a.AbsSizeAnchor=4]="AbsSizeAnchor"}(k=b.AnchorType||(b.AnchorType={})),function(a){a[a.SolidColorFillProperties=0]="SolidColorFillProperties",a[a.PatternFillProperties=1]="PatternFillProperties",a[a.GradientFillProperties=2]="GradientFillProperties",a[a.BlipFillProperties=3]="BlipFillProperties",a[a.GroupFillProperties=4]="GroupFillProperties",a[a.NoFillProperties=5]="NoFillProperties"}(l=b.ColorFillType||(b.ColorFillType={})),function(a){a[a.CT_StockChart=0]="CT_StockChart",a[a.CT_ScatterChart=1]="CT_ScatterChart",a[a.CT_RadarChart=2]="CT_RadarChart",a[a.CT_BubbleChart=3]="CT_BubbleChart",a[a.CT_AreaChart=4]="CT_AreaChart",a[a.CT_Area3DChart=5]="CT_Area3DChart",a[a.CT_BarChart=6]="CT_BarChart",a[a.CT_Bar3DChart=7]="CT_Bar3DChart",a[a.CT_LineChart=8]="CT_LineChart",a[a.CT_Line3DChart=9]="CT_Line3DChart",a[a.CT_PieChart=10]="CT_PieChart",a[a.CT_Pie3DChart=11]="CT_Pie3DChart",a[a.CT_DoughnutChart=12]="CT_DoughnutChart",a[a.CT_OfPieChart=13]="CT_OfPieChart",a[a.CT_SurfaceChart=14]="CT_SurfaceChart",a[a.CT_Surface3DChart=15]="CT_Surface3DChart",a[a.CT_BoxWhisker=16]="CT_BoxWhisker",a[a.CT_Funnel=17]="CT_Funnel",a[a.CT_ParetoLine=18]="CT_ParetoLine",a[a.CT_RegionMap=19]="CT_RegionMap",a[a.CT_Sunburst=20]="CT_Sunburst",a[a.CT_Treemap=21]="CT_Treemap",a[a.CT_Waterfall=22]="CT_Waterfall",a[a.CT_ClusteredColumn=23]="CT_ClusteredColumn"}(m=b.CT_ChartType||(b.CT_ChartType={})),function(a){a[a.CT_CatAx=0]="CT_CatAx",a[a.CT_DateAx=1]="CT_DateAx",a[a.CT_SerAx=2]="CT_SerAx",a[a.CT_ValAx=3]="CT_ValAx"}(n=b.CT_AxisType||(b.CT_AxisType={})),function(a){a[a.CT_HiddenScene3dExtension=0]="CT_HiddenScene3dExtension",a[a.CT_HiddenFillPropertiesExtension=1]="CT_HiddenFillPropertiesExtension",a[a.CT_HiddenLinePropertiesBaseExtension=2]="CT_HiddenLinePropertiesBaseExtension",a[a.CT_HiddenShape3dExtension=3]="CT_HiddenShape3dExtension",a[a.CT_OfficeArtExtension=4]="CT_OfficeArtExtension"}(o=b.CT_HiddenExtensionType||(b.CT_HiddenExtensionType={})),function(a){a[a.major=0]="major",a[a.minor=1]="minor",a[a.none=2]="none"}(p=b.ST_FontCollectionIndex||(b.ST_FontCollectionIndex={})),function(a){a[a.aliceBlue=0]="aliceBlue",a[a.antiqueWhite=1]="antiqueWhite",a[a.aqua=2]="aqua",a[a.aquamarine=3]="aquamarine",a[a.azure=4]="azure",a[a.beige=5]="beige",a[a.bisque=6]="bisque",a[a.black=7]="black",a[a.blanchedAlmond=8]="blanchedAlmond",a[a.blue=9]="blue",a[a.blueViolet=10]="blueViolet",a[a.brown=11]="brown",a[a.burlyWood=12]="burlyWood",a[a.cadetBlue=13]="cadetBlue",a[a.chartreuse=14]="chartreuse",a[a.chocolate=15]="chocolate",a[a.coral=16]="coral",a[a.cornflowerBlue=17]="cornflowerBlue",a[a.cornsilk=18]="cornsilk",a[a.crimson=19]="crimson",a[a.cyan=20]="cyan",a[a.dkBlue=21]="dkBlue",a[a.dkCyan=22]="dkCyan",a[a.dkGoldenrod=23]="dkGoldenrod",a[a.dkGray=24]="dkGray",a[a.dkGreen=25]="dkGreen",a[a.dkKhaki=26]="dkKhaki",a[a.dkMagenta=27]="dkMagenta",a[a.dkOliveGreen=28]="dkOliveGreen",a[a.dkOrange=29]="dkOrange",a[a.dkOrchid=30]="dkOrchid",a[a.dkRed=31]="dkRed",a[a.dkSalmon=32]="dkSalmon",a[a.dkSeaGreen=33]="dkSeaGreen",a[a.dkSlateBlue=34]="dkSlateBlue",a[a.dkSlateGray=35]="dkSlateGray",a[a.dkTurquoise=36]="dkTurquoise",a[a.dkViolet=37]="dkViolet",a[a.deepPink=38]="deepPink",a[a.deepSkyBlue=39]="deepSkyBlue",a[a.dimGray=40]="dimGray",a[a.dodgerBlue=41]="dodgerBlue",a[a.firebrick=42]="firebrick",a[a.floralWhite=43]="floralWhite",a[a.forestGreen=44]="forestGreen",a[a.fuchsia=45]="fuchsia",a[a.gainsboro=46]="gainsboro",a[a.ghostWhite=47]="ghostWhite",a[a.gold=48]="gold",a[a.goldenrod=49]="goldenrod",a[a.gray=50]="gray",a[a.green=51]="green",a[a.greenYellow=52]="greenYellow",a[a.honeydew=53]="honeydew",a[a.hotPink=54]="hotPink",a[a.indianRed=55]="indianRed",a[a.indigo=56]="indigo",a[a.ivory=57]="ivory",a[a.khaki=58]="khaki",a[a.lavender=59]="lavender",a[a.lavenderBlush=60]="lavenderBlush",a[a.lawnGreen=61]="lawnGreen",a[a.lemonChiffon=62]="lemonChiffon",a[a.ltBlue=63]="ltBlue",a[a.ltCoral=64]="ltCoral",a[a.ltCyan=65]="ltCyan",a[a.ltGoldenrodYellow=66]="ltGoldenrodYellow",a[a.ltGray=67]="ltGray",a[a.ltGreen=68]="ltGreen",a[a.ltPink=69]="ltPink",a[a.ltSalmon=70]="ltSalmon",a[a.ltSeaGreen=71]="ltSeaGreen",a[a.ltSkyBlue=72]="ltSkyBlue",a[a.ltSlateGray=73]="ltSlateGray",a[a.ltSteelBlue=74]="ltSteelBlue",a[a.ltYellow=75]="ltYellow",a[a.lime=76]="lime",a[a.limeGreen=77]="limeGreen",a[a.linen=78]="linen",a[a.magenta=79]="magenta",a[a.maroon=80]="maroon",a[a.medAquamarine=81]="medAquamarine",a[a.medBlue=82]="medBlue",a[a.medOrchid=83]="medOrchid",a[a.medPurple=84]="medPurple",a[a.medSeaGreen=85]="medSeaGreen",a[a.medSlateBlue=86]="medSlateBlue",a[a.medSpringGreen=87]="medSpringGreen",a[a.medTurquoise=88]="medTurquoise",a[a.medVioletRed=89]="medVioletRed",a[a.midnightBlue=90]="midnightBlue",a[a.mintCream=91]="mintCream",a[a.mistyRose=92]="mistyRose",a[a.moccasin=93]="moccasin",a[a.navajoWhite=94]="navajoWhite",a[a.navy=95]="navy",a[a.oldLace=96]="oldLace",a[a.olive=97]="olive",a[a.oliveDrab=98]="oliveDrab",a[a.orange=99]="orange",a[a.orangeRed=100]="orangeRed",a[a.orchid=101]="orchid",a[a.paleGoldenrod=102]="paleGoldenrod",a[a.paleGreen=103]="paleGreen",a[a.paleTurquoise=104]="paleTurquoise",a[a.paleVioletRed=105]="paleVioletRed",a[a.papayaWhip=106]="papayaWhip",a[a.peachPuff=107]="peachPuff",a[a.peru=108]="peru",a[a.pink=109]="pink",a[a.plum=110]="plum",a[a.powderBlue=111]="powderBlue",a[a.purple=112]="purple",a[a.red=113]="red",a[a.rosyBrown=114]="rosyBrown",a[a.royalBlue=115]="royalBlue",a[a.saddleBrown=116]="saddleBrown",a[a.salmon=117]="salmon",a[a.sandyBrown=118]="sandyBrown",a[a.seaGreen=119]="seaGreen",a[a.seaShell=120]="seaShell",a[a.sienna=121]="sienna",a[a.silver=122]="silver",a[a.skyBlue=123]="skyBlue",a[a.slateBlue=124]="slateBlue",a[a.slateGray=125]="slateGray",a[a.snow=126]="snow",a[a.springGreen=127]="springGreen",a[a.steelBlue=128]="steelBlue",a[a.tan=129]="tan",a[a.teal=130]="teal",a[a.thistle=131]="thistle",a[a.tomato=132]="tomato",a[a.turquoise=133]="turquoise",a[a.violet=134]="violet",a[a.wheat=135]="wheat",a[a.white=136]="white",a[a.whiteSmoke=137]="whiteSmoke",a[a.yellow=138]="yellow",a[a.yellowGreen=139]="yellowGreen"}(q=b.ST_PresetColorVal||(b.ST_PresetColorVal={})),function(a){a[a.percentStacked=0]="percentStacked",a[a.standard=1]="standard",a[a.stacked=2]="stacked"}(r=b.ST_Grouping||(b.ST_Grouping={})),function(a){a[a.none=0]="none",a[a.line=1]="line",a[a.lineMarker=2]="lineMarker",a[a.marker=3]="marker",a[a.smooth=4]="smooth",a[a.smoothMarker=5]="smoothMarker"}(s=b.ST_ScatterStyle||(b.ST_ScatterStyle={})),function(a){a[a.standard=0]="standard",a[a.marker=1]="marker",a[a.filled=2]="filled"}(t=b.ST_RadarStyle||(b.ST_RadarStyle={})),function(a){a[a.percentStacked=0]="percentStacked",a[a.clustered=1]="clustered",a[a.standard=2]="standard",a[a.stacked=3]="stacked"}(u=b.ST_BarGrouping||(b.ST_BarGrouping={})),function(a){a[a.bar=0]="bar",a[a.col=1]="col"}(v=b.ST_BarDir||(b.ST_BarDir={})),function(a){a[a.pie=0]="pie",a[a.bar=1]="bar"}(w=b.ST_OfPieType||(b.ST_OfPieType={})),function(a){a[a.b=0]="b",a[a.l=1]="l",a[a.r=2]="r",a[a.t=3]="t"}(x=b.ST_AxPos||(b.ST_AxPos={})),function(a){a[a.OuterShadowEffect=0]="OuterShadowEffect",a[a.InnerShadowEffect=1]="InnerShadowEffect",a[a.PresetShadowEffect=2]="PresetShadowEffect"}(y=b.ShadowEffectType||(b.ShadowEffectType={})),function(a){a[a.BarSer=0]="BarSer",a[a.AreaSer=1]="AreaSer",a[a.LineSer=2]="LineSer",a[a.PieSer=3]="PieSer",a[a.RadarSer=4]="RadarSer",a[a.ScatterSer=5]="ScatterSer",a[a.BubbleSer=6]="BubbleSer",a[a.SurfaceSer=7]="SurfaceSer",a[a.boxWhisker=16]="boxWhisker",a[a.funnel=17]="funnel",a[a.paretoLine=18]="paretoLine",a[a.regionMap=19]="regionMap",a[a.sunburst=20]="sunburst",a[a.treemap=21]="treemap",a[a.waterfall=22]="waterfall",a[a.clusteredColumn=23]="clusteredColumn"}(z=b.SeriesType||(b.SeriesType={})),function(a){a[a.InvertSolidFillFmt=0]="InvertSolidFillFmt",a[a.SeriesDataLabelsRange=1]="SeriesDataLabelsRange"}(A=b.ExtDataType||(b.ExtDataType={})),function(a){a[a.RegularTextRun=0]="RegularTextRun",a[a.TextLineBreak=1]="TextLineBreak",a[a.TextField=2]="TextField"}(B=b.TextParagraphElementType||(b.TextParagraphElementType={})),function(a){a[a.None=0]="None",a[a.Font=1]="Font",a[a.Fill=2]="Fill",a[a.Border=4]="Border",a[a.Alignment=8]="Alignment",a[a.Protection=16]="Protection",a[a.FormatCode=32]="FormatCode",a[a.All=63]="All"}(C=b.StyleDataFlag||(b.StyleDataFlag={})),function(a){a[a.None=-4142]="None",a[a.LT1=0]="LT1",a[a.DK1=1]="DK1",a[a.LT2=2]="LT2",a[a.DK2=3]="DK2",a[a.Accent1=4]="Accent1",a[a.Accent2=5]="Accent2",a[a.Accent3=6]="Accent3",a[a.Accent4=7]="Accent4",a[a.Accent5=8]="Accent5",a[a.Accent6=9]="Accent6",a[a.Hlink=10]="Hlink",a[a.FolHlink=11]="FolHlink"}(D=b.ColorSchemeIndex||(b.ColorSchemeIndex={})),function(a){a[a.None=0]="None",a[a.Major=1]="Major",a[a.Minor=2]="Minor"}(E=b.ThemeFont||(b.ThemeFont={})),function(a){a[a.Interpolated=0]="Interpolated",a[a.NotPlotted=1]="NotPlotted",a[a.Zero=2]="Zero"}(F=b.DisplayBlanksAs||(b.DisplayBlanksAs={})),function(a){a[a.Empty=0]="Empty",a[a.Row=1]="Row",a[a.Column=2]="Column",a[a.RowIsRelative=4]="RowIsRelative",a[a.ColumnIsRelative=8]="ColumnIsRelative",a[a.LastRowIsRelative=16]="LastRowIsRelative",a[a.LastColumnIsRelative=32]="LastColumnIsRelative",a[a.Range=64]="Range",a[a.Error=128]="Error"}(G=b.ReferenceKind||(b.ReferenceKind={})),function(a){a[a.Shape=0]="Shape",a[a.Chart=1]="Chart",a[a.Picture=2]="Picture",a[a.Connector=3]="Connector",a[a.GroupShape=4]="GroupShape"}(H=b.DrawingType||(b.DrawingType={})),function(a){a[a.TextureTopLeft=0]="TextureTopLeft",a[a.TextureTop=1]="TextureTop",a[a.TextureTopRight=2]="TextureTopRight",a[a.TextureLeft=3]="TextureLeft",a[a.TextureCenter=4]="TextureCenter",a[a.TextureRight=5]="TextureRight",a[a.TextureBottomLeft=6]="TextureBottomLeft",a[a.TextureBottom=7]="TextureBottom",a[a.TextureBottomRight=8]="TextureBottomRight"}(I=b.TextureAlignment||(b.TextureAlignment={})),function(a){a[a.TextureTypeNone=0]="TextureTypeNone",a[a.TexturePreset=1]="TexturePreset",a[a.TextureUserDefined=2]="TextureUserDefined"}(J=b.TextureType||(b.TextureType={})),function(a){a[a.Solid=0]="Solid",a[a.Patterned=1]="Patterned",a[a.Gradient=2]="Gradient",a[a.Textured=3]="Textured",a[a.Background=4]="Background",a[a.Picture=5]="Picture",a[a.Group=6]="Group"}(K=b.FillType||(b.FillType={})),function(a){a[a.GradientEarlySunset=0]="GradientEarlySunset",a[a.GradientLateSunset=1]="GradientLateSunset",a[a.GradientNightfall=2]="GradientNightfall",a[a.GradientDaybreak=3]="GradientDaybreak",a[a.GradientHorizon=4]="GradientHorizon",a[a.GradientDesert=5]="GradientDesert",a[a.GradientOcean=6]="GradientOcean",a[a.GradientCalmWater=7]="GradientCalmWater",a[a.GradientFire=8]="GradientFire",a[a.GradientFog=9]="GradientFog",a[a.GradientMoss=10]="GradientMoss",a[a.GradientPeacock=11]="GradientPeacock",a[a.GradientWheat=12]="GradientWheat",a[a.GradientParchment=13]="GradientParchment",a[a.GradientMahogany=14]="GradientMahogany",a[a.GradientRainbow=15]="GradientRainbow",a[a.GradientRainbowII=16]="GradientRainbowII",a[a.GradientGold=17]="GradientGold",a[a.GradientGoldII=18]="GradientGoldII",a[a.GradientBrass=19]="GradientBrass",a[a.GradientChrome=20]="GradientChrome",a[a.GradientChromeII=21]="GradientChromeII",a[a.GradientSilver=22]="GradientSilver",a[a.GradientSapphire=23]="GradientSapphire"}(L=b.PresetGradientType||(b.PresetGradientType={})),function(a){a[a.TexturePapyrus=0]="TexturePapyrus",a[a.TextureCanvas=1]="TextureCanvas",a[a.TextureDenim=2]="TextureDenim",a[a.TextureWovenMat=3]="TextureWovenMat",a[a.TextureWaterDroplets=4]="TextureWaterDroplets",a[a.TexturePaperBag=5]="TexturePaperBag",a[a.TextureFishFossil=6]="TextureFishFossil",a[a.TextureSand=7]="TextureSand",a[a.TextureGreenMarble=8]="TextureGreenMarble",a[a.TextureWhiteMarble=9]="TextureWhiteMarble",a[a.TextureBrownMarble=10]="TextureBrownMarble",a[a.TextureGranite=11]="TextureGranite",a[a.TextureNewsprint=12]="TextureNewsprint",a[a.TextureRecycledPaper=13]="TextureRecycledPaper",a[a.TextureParchment=14]="TextureParchment",a[a.TextureStationery=15]="TextureStationery",a[a.TextureBlueTissuePaper=16]="TextureBlueTissuePaper",a[a.TexturePinkTissuePaper=17]="TexturePinkTissuePaper",a[a.TexturePurpleMesh=18]="TexturePurpleMesh",a[a.TextureBouquet=19]="TextureBouquet",a[a.TextureCork=20]="TextureCork",a[a.TextureWalnut=21]="TextureWalnut",a[a.TextureOak=22]="TextureOak",a[a.TextureMediumWood=23]="TextureMediumWood"}(M=b.PresetTexture||(b.PresetTexture={})),function(a){a[a.GradientColorNone=0]="GradientColorNone",a[a.GradientOneColor=1]="GradientOneColor",a[a.GradientTwoColors=2]="GradientTwoColors",a[a.GradientPresetColors=3]="GradientPresetColors",a[a.GradientMultiColor=4]="GradientMultiColor"}(N=b.GradientColorType||(b.GradientColorType={})),function(a){a[a.PatternNone=0]="PatternNone",a[a.Pattern5Percent=1]="Pattern5Percent",a[a.Pattern10Percent=2]="Pattern10Percent",a[a.Pattern20Percent=3]="Pattern20Percent",a[a.Pattern25Percent=4]="Pattern25Percent",a[a.Pattern30Percent=5]="Pattern30Percent",a[a.Pattern40Percent=6]="Pattern40Percent",a[a.Pattern50Percent=7]="Pattern50Percent",a[a.Pattern60Percent=8]="Pattern60Percent",a[a.Pattern70Percent=9]="Pattern70Percent",a[a.Pattern75Percent=10]="Pattern75Percent",a[a.Pattern80Percent=11]="Pattern80Percent",a[a.Pattern90Percent=12]="Pattern90Percent",a[a.PatternDarkHorizontal=13]="PatternDarkHorizontal",a[a.PatternDarkVertical=14]="PatternDarkVertical",a[a.PatternDarkDownwardDiagonal=15]="PatternDarkDownwardDiagonal",a[a.PatternDarkUpwardDiagonal=16]="PatternDarkUpwardDiagonal",a[a.PatternSmallCheckerBoard=17]="PatternSmallCheckerBoard",a[a.PatternTrellis=18]="PatternTrellis",a[a.PatternLightHorizontal=19]="PatternLightHorizontal",a[a.PatternLightVertical=20]="PatternLightVertical",a[a.PatternLightDownwardDiagonal=21]="PatternLightDownwardDiagonal",a[a.PatternLightUpwardDiagonal=22]="PatternLightUpwardDiagonal",a[a.PatternSmallGrid=23]="PatternSmallGrid",a[a.PatternDottedDiamond=24]="PatternDottedDiamond",a[a.PatternWideDownwardDiagonal=25]="PatternWideDownwardDiagonal",a[a.PatternWideUpwardDiagonal=26]="PatternWideUpwardDiagonal",a[a.PatternDashedUpwardDiagonal=27]="PatternDashedUpwardDiagonal",a[a.PatternDashedDownwardDiagonal=28]="PatternDashedDownwardDiagonal",a[a.PatternNarrowVertical=29]="PatternNarrowVertical",a[a.PatternNarrowHorizontal=30]="PatternNarrowHorizontal",a[a.PatternDashedVertical=31]="PatternDashedVertical",a[a.PatternDashedHorizontal=32]="PatternDashedHorizontal",a[a.PatternLargeConfetti=33]="PatternLargeConfetti",a[a.PatternLargeGrid=34]="PatternLargeGrid",a[a.PatternHorizontalBrick=35]="PatternHorizontalBrick",a[a.PatternLargeCheckerBoard=36]="PatternLargeCheckerBoard",a[a.PatternSmallConfetti=37]="PatternSmallConfetti",a[a.PatternZigZag=38]="PatternZigZag",a[a.PatternSolidDiamond=39]="PatternSolidDiamond",a[a.PatternDiagonalBrick=40]="PatternDiagonalBrick",a[a.PatternOutlinedDiamond=41]="PatternOutlinedDiamond",a[a.PatternPlaid=42]="PatternPlaid",a[a.PatternSphere=43]="PatternSphere",a[a.PatternWeave=44]="PatternWeave",a[a.PatternDottedGrid=45]="PatternDottedGrid",a[a.PatternDivot=46]="PatternDivot",a[a.PatternShingle=47]="PatternShingle",a[a.PatternWave=48]="PatternWave",a[a.PatternHorizontal=49]="PatternHorizontal",a[a.PatternVertical=50]="PatternVertical",a[a.PatternCross=51]="PatternCross",a[a.PatternDownwardDiagonal=52]="PatternDownwardDiagonal",a[a.PatternUpwardDiagonal=53]="PatternUpwardDiagonal",a[a.PatternDiagonalCross=54]="PatternDiagonalCross"}(O=b.PatternType||(b.PatternType={})),function(a){a[a.GradientHorizontal=0]="GradientHorizontal",a[a.GradientVertical=1]="GradientVertical",a[a.GradientDiagonalUp=2]="GradientDiagonalUp",a[a.GradientDiagonalDown=3]="GradientDiagonalDown",a[a.GradientFromCorner=4]="GradientFromCorner",a[a.GradientFromTitle=5]="GradientFromTitle",a[a.GradientFromCenter=6]="GradientFromCenter"}(P=b.GradientStyle||(b.GradientStyle={})),function(a){a[a.short=0]="short",a[a.medium=1]="medium",a[a.long=2]="long"}(Q=b.ArrowheadLength||(b.ArrowheadLength={})),function(a){a[a.none=0]="none",a[a.triangle=1]="triangle",a[a.stealth=2]="stealth",a[a.diamond=3]="diamond",a[a.oval=4]="oval",a[a.open=5]="open"}(R=b.ArrowheadStyle||(b.ArrowheadStyle={})),function(a){a[a.narrow=0]="narrow",a[a.medium=1]="medium",a[a.wide=2]="wide"}(S=b.ArrowheadWidth||(b.ArrowheadWidth={})),function(a){a[a.solid=0]="solid",a[a.squareDot=1]="squareDot",a[a.dash=2]="dash",a[a.longDash=3]="longDash",a[a.dashDot=4]="dashDot",a[a.longDashDot=5]="longDashDot",a[a.longDashDotDot=6]="longDashDotDot",a[a.sysDash=7]="sysDash",a[a.sysDot=8]="sysDot",a[a.sysDashDot=9]="sysDashDot",a[a.dashDotDot=10]="dashDotDot",a[a.roundDot=11]="roundDot"}(T=b.LineDashStyle||(b.LineDashStyle={})),function(a){a[a.flat=2]="flat",a[a.square=1]="square",a[a.round=0]="round"}(U=b.LineCapStyle||(b.LineCapStyle={})),function(a){a[a.round=0]="round",a[a.miter=1]="miter",a[a.bevel=2]="bevel"}(V=b.LineJoinStyle||(b.LineJoinStyle={})),function(a){a[a.LineSingle=0]="LineSingle",a[a.LineThinThin=1]="LineThinThin",a[a.LineThinThick=2]="LineThinThick",a[a.LineThickThin=3]="LineThickThin",a[a.LineThickBetweenThin=4]="LineThickBetweenThin"}(W=b.LineStyle||(b.LineStyle={})),function(a){a[a.PictureAutomatic=0]="PictureAutomatic",a[a.PictureGrayscale=1]="PictureGrayscale",a[a.PictureBlackAndWhite=2]="PictureBlackAndWhite",a[a.PictureWatermark=3]="PictureWatermark"}(X=b.PictureColorType||(b.PictureColorType={})),function(a){a[a.Shadow1=0]="Shadow1",a[a.Shadow2=1]="Shadow2",a[a.Shadow3=2]="Shadow3",a[a.Shadow4=3]="Shadow4",a[a.Shadow5=4]="Shadow5",a[a.Shadow6=5]="Shadow6",a[a.Shadow7=6]="Shadow7",a[a.Shadow8=7]="Shadow8",a[a.Shadow9=8]="Shadow9",a[a.Shadow10=9]="Shadow10",a[a.Shadow11=10]="Shadow11",a[a.Shadow12=11]="Shadow12",a[a.Shadow13=12]="Shadow13",a[a.Shadow14=13]="Shadow14",a[a.Shadow15=14]="Shadow15",a[a.Shadow16=15]="Shadow16",a[a.Shadow17=16]="Shadow17",a[a.Shadow18=17]="Shadow18",a[a.Shadow19=18]="Shadow19",
a[a.Shadow20=19]="Shadow20",a[a.Shadow21=20]="Shadow21",a[a.Shadow22=21]="Shadow22",a[a.Shadow23=22]="Shadow23",a[a.Shadow24=23]="Shadow24",a[a.Shadow25=24]="Shadow25",a[a.Shadow26=25]="Shadow26",a[a.Shadow27=26]="Shadow27",a[a.Shadow28=27]="Shadow28",a[a.Shadow29=28]="Shadow29",a[a.Shadow30=29]="Shadow30",a[a.Shadow31=30]="Shadow31",a[a.Shadow32=31]="Shadow32",a[a.Shadow33=32]="Shadow33",a[a.Shadow34=33]="Shadow34",a[a.Shadow35=34]="Shadow35",a[a.Shadow36=35]="Shadow36",a[a.Shadow37=36]="Shadow37",a[a.Shadow38=37]="Shadow38",a[a.Shadow39=38]="Shadow39",a[a.Shadow40=39]="Shadow40",a[a.Shadow41=40]="Shadow41",a[a.Shadow42=41]="Shadow42",a[a.Shadow43=42]="Shadow43"}(Y=b.ShadowType||(b.ShadowType={})),function(a){a[a.ShadowStyleInnerShadow=0]="ShadowStyleInnerShadow",a[a.ShadowStyleOuterShadow=1]="ShadowStyleOuterShadow"}(Z=b.ShadowStyle||(b.ShadowStyle={})),function(a){a[a.SoftEdgeTypeNone=0]="SoftEdgeTypeNone",a[a.SoftEdgeType1=1]="SoftEdgeType1",a[a.SoftEdgeType2=2]="SoftEdgeType2",a[a.SoftEdgeType3=3]="SoftEdgeType3",a[a.SoftEdgeType4=4]="SoftEdgeType4",a[a.SoftEdgeType5=5]="SoftEdgeType5",a[a.SoftEdgeType6=6]="SoftEdgeType6"}($=b.SoftEdgeType||(b.SoftEdgeType={})),function(a){a[a.CameraLegacyObliqueTopLeft=0]="CameraLegacyObliqueTopLeft",a[a.CameraLegacyObliqueTop=1]="CameraLegacyObliqueTop",a[a.CameraLegacyObliqueTopRight=2]="CameraLegacyObliqueTopRight",a[a.CameraLegacyObliqueLeft=3]="CameraLegacyObliqueLeft",a[a.CameraLegacyObliqueFront=4]="CameraLegacyObliqueFront",a[a.CameraLegacyObliqueRight=5]="CameraLegacyObliqueRight",a[a.CameraLegacyObliqueBottomLeft=6]="CameraLegacyObliqueBottomLeft",a[a.CameraLegacyObliqueBottom=7]="CameraLegacyObliqueBottom",a[a.CameraLegacyObliqueBottomRight=8]="CameraLegacyObliqueBottomRight",a[a.CameraLegacyPerspectiveTopLeft=9]="CameraLegacyPerspectiveTopLeft",a[a.CameraLegacyPerspectiveTop=10]="CameraLegacyPerspectiveTop",a[a.CameraLegacyPerspectiveTopRight=11]="CameraLegacyPerspectiveTopRight",a[a.CameraLegacyPerspectiveLeft=12]="CameraLegacyPerspectiveLeft",a[a.CameraLegacyPerspectiveFront=13]="CameraLegacyPerspectiveFront",a[a.CameraLegacyPerspectiveRight=14]="CameraLegacyPerspectiveRight",a[a.CameraLegacyPerspectiveBottomLeft=15]="CameraLegacyPerspectiveBottomLeft",a[a.CameraLegacyPerspectiveBottom=16]="CameraLegacyPerspectiveBottom",a[a.CameraLegacyPerspectiveBottomRight=17]="CameraLegacyPerspectiveBottomRight",a[a.CameraOrthographicFront=18]="CameraOrthographicFront",a[a.CameraIsometricTopUp=19]="CameraIsometricTopUp",a[a.CameraIsometricTopDown=20]="CameraIsometricTopDown",a[a.CameraIsometricBottomUp=21]="CameraIsometricBottomUp",a[a.CameraIsometricBottomDown=22]="CameraIsometricBottomDown",a[a.CameraIsometricLeftUp=23]="CameraIsometricLeftUp",a[a.CameraIsometricLeftDown=24]="CameraIsometricLeftDown",a[a.CameraIsometricRightUp=25]="CameraIsometricRightUp",a[a.CameraIsometricRightDown=26]="CameraIsometricRightDown",a[a.CameraIsometricOffAxis1Left=27]="CameraIsometricOffAxis1Left",a[a.CameraIsometricOffAxis1Right=28]="CameraIsometricOffAxis1Right",a[a.CameraIsometricOffAxis1Top=29]="CameraIsometricOffAxis1Top",a[a.CameraIsometricOffAxis2Left=30]="CameraIsometricOffAxis2Left",a[a.CameraIsometricOffAxis2Right=31]="CameraIsometricOffAxis2Right",a[a.CameraIsometricOffAxis2Top=32]="CameraIsometricOffAxis2Top",a[a.CameraIsometricOffAxis3Left=33]="CameraIsometricOffAxis3Left",a[a.CameraIsometricOffAxis3Right=34]="CameraIsometricOffAxis3Right",a[a.CameraIsometricOffAxis3Bottom=35]="CameraIsometricOffAxis3Bottom",a[a.CameraIsometricOffAxis4Left=36]="CameraIsometricOffAxis4Left",a[a.CameraIsometricOffAxis4Right=37]="CameraIsometricOffAxis4Right",a[a.CameraIsometricOffAxis4Bottom=38]="CameraIsometricOffAxis4Bottom",a[a.CameraObliqueTopLeft=39]="CameraObliqueTopLeft",a[a.CameraObliqueTop=40]="CameraObliqueTop",a[a.CameraObliqueTopRight=41]="CameraObliqueTopRight",a[a.CameraObliqueLeft=42]="CameraObliqueLeft",a[a.CameraObliqueRight=43]="CameraObliqueRight",a[a.CameraObliqueBottomLeft=44]="CameraObliqueBottomLeft",a[a.CameraObliqueBottom=45]="CameraObliqueBottom",a[a.CameraObliqueBottomRight=46]="CameraObliqueBottomRight",a[a.CameraPerspectiveFront=47]="CameraPerspectiveFront",a[a.CameraPerspectiveLeft=48]="CameraPerspectiveLeft",a[a.CameraPerspectiveRight=49]="CameraPerspectiveRight",a[a.CameraPerspectiveAbove=50]="CameraPerspectiveAbove",a[a.CameraPerspectiveBelow=51]="CameraPerspectiveBelow",a[a.CameraPerspectiveAboveLeftFacing=52]="CameraPerspectiveAboveLeftFacing",a[a.CameraPerspectiveAboveRightFacing=53]="CameraPerspectiveAboveRightFacing",a[a.CameraPerspectiveContrastingLeftFacing=54]="CameraPerspectiveContrastingLeftFacing",a[a.CameraPerspectiveContrastingRightFacing=55]="CameraPerspectiveContrastingRightFacing",a[a.CameraPerspectiveHeroicLeftFacing=56]="CameraPerspectiveHeroicLeftFacing",a[a.CameraPerspectiveHeroicRightFacing=57]="CameraPerspectiveHeroicRightFacing",a[a.CameraPerspectiveHeroicExtremeLeftFacing=58]="CameraPerspectiveHeroicExtremeLeftFacing",a[a.CameraPerspectiveHeroicExtremeRightFacing=59]="CameraPerspectiveHeroicExtremeRightFacing",a[a.CameraPerspectiveRelaxed=60]="CameraPerspectiveRelaxed",a[a.CameraPerspectiveRelaxedModerately=61]="CameraPerspectiveRelaxedModerately",a[a.PresetCameraNone=62]="PresetCameraNone"}(_=b.PresetCamera||(b.PresetCamera={})),function(a){a[a.LightingTopLeft=0]="LightingTopLeft",a[a.LightingTop=1]="LightingTop",a[a.LightingTopRight=2]="LightingTopRight",a[a.LightingLeft=3]="LightingLeft",a[a.LightingNone=4]="LightingNone",a[a.LightingRight=5]="LightingRight",a[a.LightingBottomLeft=6]="LightingBottomLeft",a[a.LightingBottom=7]="LightingBottom",a[a.LightingBottomRight=8]="LightingBottomRight"}(aa=b.PresetLightingDirection||(b.PresetLightingDirection={})),function(a){a[a.LightRigLegacyFlat1=0]="LightRigLegacyFlat1",a[a.LightRigLegacyFlat2=1]="LightRigLegacyFlat2",a[a.LightRigLegacyFlat3=2]="LightRigLegacyFlat3",a[a.LightRigLegacyFlat4=3]="LightRigLegacyFlat4",a[a.LightRigLegacyNormal1=4]="LightRigLegacyNormal1",a[a.LightRigLegacyNormal2=5]="LightRigLegacyNormal2",a[a.LightRigLegacyNormal3=6]="LightRigLegacyNormal3",a[a.LightRigLegacyNormal4=7]="LightRigLegacyNormal4",a[a.LightRigLegacyHarsh1=8]="LightRigLegacyHarsh1",a[a.LightRigLegacyHarsh2=9]="LightRigLegacyHarsh2",a[a.LightRigLegacyHarsh3=10]="LightRigLegacyHarsh3",a[a.LightRigLegacyHarsh4=11]="LightRigLegacyHarsh4",a[a.LightRigThreePoint=12]="LightRigThreePoint",a[a.LightRigBalanced=13]="LightRigBalanced",a[a.LightRigSoft=14]="LightRigSoft",a[a.LightRigHarsh=15]="LightRigHarsh",a[a.LightRigFlood=16]="LightRigFlood",a[a.LightRigContrasting=17]="LightRigContrasting",a[a.LightRigMorning=18]="LightRigMorning",a[a.LightRigSunrise=19]="LightRigSunrise",a[a.LightRigSunset=20]="LightRigSunset",a[a.LightRigChilly=21]="LightRigChilly",a[a.LightRigFreezing=22]="LightRigFreezing",a[a.LightRigFlat=23]="LightRigFlat",a[a.LightRigTwoPoint=24]="LightRigTwoPoint",a[a.LightRigGlow=25]="LightRigGlow",a[a.LightRigBrightRoom=26]="LightRigBrightRoom"}(ba=b.LightRigType||(b.LightRigType={})),function(a){a[a.Category=0]="Category",a[a.Value=1]="Value",a[a.SeriesAxis=2]="SeriesAxis"}(ca=b.AxisType||(b.AxisType={})),function(a){a[a.high=0]="high",a[a.low=1]="low",a[a.nextToAxis=2]="nextToAxis",a[a.none=3]="none"}(da=b.TickLabelPosition||(b.TickLabelPosition={})),function(a){a[a.ScaleLogarithmic=0]="ScaleLogarithmic",a[a.ScaleLinear=1]="ScaleLinear"}(ea=b.ScaleType||(b.ScaleType={})),function(a){a[a.Days=0]="Days",a[a.Months=1]="Months",a[a.Years=2]="Years"}(fa=b.TimeUnit||(b.TimeUnit={})),function(a){a[a.cross=0]="cross",a[a.inside=1]="inside",a[a.none=2]="none",a[a.outside=3]="outside"}(ga=b.TickMark||(b.TickMark={})),function(a){a[a.Hundreds=0]="Hundreds",a[a.Thousands=1]="Thousands",a[a.TenThousands=2]="TenThousands",a[a.HundredThousands=3]="HundredThousands",a[a.Millions=4]="Millions",a[a.TenMillions=5]="TenMillions",a[a.HundredMillions=6]="HundredMillions",a[a.ThousandMillions=7]="ThousandMillions",a[a.MillionMillions=8]="MillionMillions",a[a.None=9]="None",a[a.Custom=10]="Custom"}(ha=b.DisplayUnit||(b.DisplayUnit={})),function(a){a[a.AxisCrossesCustom=0]="AxisCrossesCustom",a[a.AxisCrossesAutomatic=1]="AxisCrossesAutomatic",a[a.AxisCrossesMaximum=2]="AxisCrossesMaximum",a[a.AxisCrossesMinimum=3]="AxisCrossesMinimum"}(ia=b.AxisCrosses||(b.AxisCrosses={})),function(a){a[a.AutomaticScale=0]="AutomaticScale",a[a.CategoryScale=1]="CategoryScale",a[a.TimeScale=2]="TimeScale",a[a.ValueScale=3]="ValueScale"}(ja=b.CategoryType||(b.CategoryType={})),function(a){a[a.primary=0]="primary",a[a.secondary=1]="secondary"}(ka=b.AxisGroup||(b.AxisGroup={})),function(a){a[a.ConeToPoint=0]="ConeToPoint",a[a.ConeToMax=1]="ConeToMax",a[a.Box=2]="Box",a[a.Cylinder=3]="Cylinder",a[a.PyramidToPoint=4]="PyramidToPoint",a[a.PyramidToMax=5]="PyramidToMax"}(la=b.BarShape||(b.BarShape={})),function(a){a[a.combo=0]="combo",a[a.xyScatter=1]="xyScatter",a[a.radar=2]="radar",a[a.doughnut=3]="doughnut",a[a.pie3D=4]="pie3D",a[a.line3D=5]="line3D",a[a.column3D=6]="column3D",a[a.area3D=7]="area3D",a[a.area=8]="area",a[a.line=9]="line",a[a.pie=10]="pie",a[a.bubble=11]="bubble",a[a.columnClustered=12]="columnClustered",a[a.columnStacked=13]="columnStacked",a[a.columnStacked100=14]="columnStacked100",a[a.columnClustered3D=15]="columnClustered3D",a[a.columnStacked3D=16]="columnStacked3D",a[a.columnStacked1003D=17]="columnStacked1003D",a[a.barClustered=18]="barClustered",a[a.barStacked=19]="barStacked",a[a.barStacked100=20]="barStacked100",a[a.barClustered3D=21]="barClustered3D",a[a.barStacked3D=22]="barStacked3D",a[a.barStacked1003D=23]="barStacked1003D",a[a.lineStacked=24]="lineStacked",a[a.lineStacked100=25]="lineStacked100",a[a.lineMarkers=26]="lineMarkers",a[a.lineMarkersStacked=27]="lineMarkersStacked",a[a.lineMarkersStacked100=28]="lineMarkersStacked100",a[a.pieOfPie=29]="pieOfPie",a[a.pieExploded=30]="pieExploded",a[a.pieExploded3D=31]="pieExploded3D",a[a.barOfPie=32]="barOfPie",a[a.xyScatterSmooth=33]="xyScatterSmooth",a[a.xyScatterSmoothNoMarkers=34]="xyScatterSmoothNoMarkers",a[a.xyScatterLines=35]="xyScatterLines",a[a.xyScatterLinesNoMarkers=36]="xyScatterLinesNoMarkers",a[a.areaStacked=37]="areaStacked",a[a.areaStacked100=38]="areaStacked100",a[a.areaStacked3D=39]="areaStacked3D",a[a.areaStacked1003D=40]="areaStacked1003D",a[a.doughnutExploded=41]="doughnutExploded",a[a.radarMarkers=42]="radarMarkers",a[a.radarFilled=43]="radarFilled",a[a.surface=44]="surface",a[a.surfaceWireframe=45]="surfaceWireframe",a[a.surfaceTopView=46]="surfaceTopView",a[a.surfaceTopViewWireframe=47]="surfaceTopViewWireframe",a[a.bubble3DEffect=48]="bubble3DEffect",a[a.stockHLC=49]="stockHLC",a[a.stockOHLC=50]="stockOHLC",a[a.stockVHLC=51]="stockVHLC",a[a.stockVOHLC=52]="stockVOHLC",a[a.boxWhisker=53]="boxWhisker",a[a.funnel=54]="funnel",a[a.paretoLine=55]="paretoLine",a[a.regionMap=56]="regionMap",a[a.sunburst=57]="sunburst",a[a.treemap=58]="treemap",a[a.waterfall=59]="waterfall",a[a.clusteredColumn=60]="clusteredColumn"}(ma=b.ChartType||(b.ChartType={})),function(a){a[a.Exponential=0]="Exponential",a[a.Linear=1]="Linear",a[a.Logarithmic=2]="Logarithmic",a[a.MovingAvg=3]="MovingAvg",a[a.Polynomial=4]="Polynomial",a[a.Power=5]="Power"}(na=b.TrendlineType||(b.TrendlineType={})),function(a){a[a.bestFit=0]="bestFit",a[a.below=1]="below",a[a.center=2]="center",a[a.insideBase=3]="insideBase",a[a.insideEnd=4]="insideEnd",a[a.left=5]="left",a[a.outsideEnd=6]="outsideEnd",a[a.right=7]="right",a[a.above=8]="above"}(oa=b.DataLabelPosition||(b.DataLabelPosition={})),function(a){a[a.MarkerStyleCircle=0]="MarkerStyleCircle",a[a.MarkerStyleDash=1]="MarkerStyleDash",a[a.MarkerStyleDiamond=2]="MarkerStyleDiamond",a[a.MarkerStyleDot=3]="MarkerStyleDot",a[a.MarkerStyleNone=4]="MarkerStyleNone",a[a.MarkerStylePicture=5]="MarkerStylePicture",a[a.MarkerStylePlus=6]="MarkerStylePlus",a[a.MarkerStyleSquare=7]="MarkerStyleSquare",a[a.MarkerStyleStar=8]="MarkerStyleStar",a[a.MarkerStyleTriangle=9]="MarkerStyleTriangle",a[a.MarkerStyleX=10]="MarkerStyleX",a[a.MarkerStyleAutomatic=11]="MarkerStyleAutomatic"}(pa=b.MarkerStyle||(b.MarkerStyle={})),function(a){a[a.Stretch=0]="Stretch",a[a.Stack=1]="Stack",a[a.StackScale=2]="StackScale"}(qa=b.DrawingPictureType||(b.DrawingPictureType={})),function(a){a[a.SizeIsArea=0]="SizeIsArea",a[a.SizeIsWidth=1]="SizeIsWidth"}(ra=b.SizeRepresents||(b.SizeRepresents={})),function(a){a[a.SplitByPosition=0]="SplitByPosition",a[a.SplitByValue=1]="SplitByValue",a[a.SplitByPercentValue=2]="SplitByPercentValue",a[a.SplitByCustomSplit=3]="SplitByCustomSplit"}(sa=b.ChartSplitType||(b.ChartSplitType={})),function(a){a[a.custom=0]="custom",a[a.top=1]="top",a[a.right=2]="right",a[a.left=3]="left",a[a.bottom=4]="bottom",a[a.corner=5]="corner"}(ta=b.LegendPosition||(b.LegendPosition={})),function(a){a[a.rows=0]="rows",a[a.columns=1]="columns"}(ua=b.RowCol||(b.RowCol={})),function(a){a[a.circle=0]="circle",a[a.dash=1]="dash",a[a.diamond=2]="diamond",a[a.dot=3]="dot",a[a.none=4]="none",a[a.picture=5]="picture",a[a.plus=6]="plus",a[a.square=7]="square",a[a.star=8]="star",a[a.triangle=9]="triangle",a[a.x=10]="x"}(va=b.SymbolShape||(b.SymbolShape={})),function(a){a[a.solid=0]="solid",a[a.dot=1]="dot",a[a.dash=2]="dash",a[a.lgDash=3]="lgDash",a[a.dashDot=4]="dashDot",a[a.lgDashDot=5]="lgDashDot",a[a.lgDashDotDot=6]="lgDashDotDot",a[a.sysDash=7]="sysDash",a[a.sysDot=8]="sysDot",a[a.sysDashDot=9]="sysDashDot",a[a.sysDashDotDot=10]="sysDashDotDot"}(wa=b.LineType||(b.LineType={})),function(a){a[a.Baseline=0]="Baseline",a[a.Superscript=1]="Superscript",a[a.Subscript=2]="Subscript"}(xa=b.VertAlignType||(b.VertAlignType={})),function(a){a[a.MoveAndSize=0]="MoveAndSize",a[a.Move=1]="Move",a[a.FreeFloating=2]="FreeFloating"}(ya=b.Placement||(b.Placement={})),function(a){a[a.All=0]="All",a[a.Chart=1]="Chart",a[a.PlotArea=2]="PlotArea",a[a.Series=3]="Series",a[a.SeriesCollection=4]="SeriesCollection",a[a.Point=5]="Point",a[a.DataLabel=6]="DataLabel",a[a.DataMarker=7]="DataMarker",a[a.CategoryAxis=8]="CategoryAxis",a[a.ValueAxis=9]="ValueAxis",a[a.SeriesAxis=10]="SeriesAxis",a[a.Lengend=11]="Lengend",a[a.FloorWall=12]="FloorWall",a[a.SideWall=13]="SideWall",a[a.BackWall=14]="BackWall",a[a.ChartTitle=15]="ChartTitle",a[a.AxisTitle=16]="AxisTitle",a[a.ChartGroup=17]="ChartGroup"}(za=b.ChartElements||(b.ChartElements={})),function(a){a[a.PNG=0]="PNG",a[a.JPG=1]="JPG",a[a.JPEG=2]="JPEG",a[a.EMF=3]="EMF"}(Aa=b.ImageType||(b.ImageType={})),function(a){a[a.None=0]="None",a[a.Automatic=1]="Automatic",a[a.RGB=2]="RGB",a[a.Theme=3]="Theme"}(Ba=b.SolidColorType||(b.SolidColorType={})),function(a){a[a.None=0]="None",a[a.Words=1]="Words",a[a.Single=2]="Single",a[a.Double=3]="Double",a[a.Heavy=4]="Heavy",a[a.Dotted=5]="Dotted",a[a.DottedHeavy=6]="DottedHeavy",a[a.Dash=7]="Dash",a[a.DashHeavy=8]="DashHeavy",a[a.DashLong=9]="DashLong",a[a.DashLongHeavy=10]="DashLongHeavy",a[a.DotDash=11]="DotDash",a[a.DotDashHeavy=12]="DotDashHeavy",a[a.DotDotDash=13]="DotDotDash",a[a.DotDotDashHeavy=14]="DotDotDashHeavy",a[a.Wavy=15]="Wavy",a[a.WavyHeavy=16]="WavyHeavy",a[a.WavyDouble=17]="WavyDouble"}(Ca=b.TextUnderlineType||(b.TextUnderlineType={})),function(a){a[a.ReflectionTypeNone=0]="ReflectionTypeNone",a[a.ReflectionType1=1]="ReflectionType1",a[a.ReflectionType2=2]="ReflectionType2",a[a.ReflectionType3=3]="ReflectionType3",a[a.ReflectionType4=4]="ReflectionType4",a[a.ReflectionType5=5]="ReflectionType5",a[a.ReflectionType6=6]="ReflectionType6",a[a.ReflectionType7=7]="ReflectionType7",a[a.ReflectionType8=8]="ReflectionType8",a[a.ReflectionType9=9]="ReflectionType9"}(Da=b.ReflectionType||(b.ReflectionType={})),function(a){a[a.CellRange=0]="CellRange",a[a.SeriesName=1]="SeriesName",a[a.CategoryName=2]="CategoryName",a[a.Value=3]="Value",a[a.XValue=4]="XValue",a[a.YValue=5]="YValue",a[a.BubbleSize=6]="BubbleSize",a[a.Percentage=7]="Percentage",a[a.TxLink=8]="TxLink"}(Ea=b.TextFieldType||(b.TextFieldType={})),function(a){a[a.None=0]="None",a[a.Single=1]="Single",a[a.Double=2]="Double",a[a.SingleAccounting=3]="SingleAccounting",a[a.DoubleAccounting=4]="DoubleAccounting"}(Fa=b.UnderlineType||(b.UnderlineType={})),function(a){a[a.None=0]="None",a[a.Auto=1]="Auto",a[a.RGB=2]="RGB",a[a.Index=3]="Index",a[a.Theme=4]="Theme"}(Ga=b.ColorType||(b.ColorType={})),function(a){a[a.Color=1]="Color",a[a.PatternColor=2]="PatternColor",a[a.BeginArrowheadLength=4]="BeginArrowheadLength",a[a.BeginArrowheadStyle=8]="BeginArrowheadStyle",a[a.BeginArrowheadWidth=16]="BeginArrowheadWidth",a[a.DashStyle=32]="DashStyle",a[a.EndArrowheadLength=64]="EndArrowheadLength",a[a.EndArrowheadStyle=128]="EndArrowheadStyle",a[a.EndArrowheadWidth=256]="EndArrowheadWidth",a[a.InsetPen=512]="InsetPen",a[a.Pattern=1024]="Pattern",a[a.Style=2048]="Style",a[a.Transparency=4096]="Transparency",a[a.Visible=8192]="Visible",a[a.Weight=16384]="Weight",a[a.CapStyle=32768]="CapStyle",a[a.JoinStyle=65536]="JoinStyle"}(Ha=b.LineFormatStates||(b.LineFormatStates={})),function(a){a[a.Brightness=1]="Brightness",a[a.ObjectThemeColor=2]="ObjectThemeColor",a[a.RGB=4]="RGB",a[a.TintAndShade=8]="TintAndShade",a[a.ColorType=16]="ColorType",a[a.Transparency=32]="Transparency"}(Ia=b.ColorFormatStates||(b.ColorFormatStates={})),function(a){a[a.PictureShape=0]="PictureShape",a[a.PictureFill=1]="PictureFill",a[a.TextureFill=2]="TextureFill"}(Ja=b.PictureFormatType||(b.PictureFormatType={}))},"./dist/plugins/floatingObject/drawing/effects.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/stateful.js"),e=c("./dist/plugins/floatingObject/drawing/common.js"),f=c("./dist/plugins/floatingObject/drawing/colorFormat.js"),g=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),function(a){a[a.Blur=1]="Blur",a[a.Offset=2]="Offset",a[a.Size=4]="Size",a[a.Transparency=8]="Transparency",a[a.Type=16]="Type"}(h||(h={})),i=e.UnitHelper.isNullOrUndefined,j=Math.floor,k=Math.sqrt,function(a){a[a.Blur=1]="Blur",a[a.Obscured=2]="Obscured",a[a.OffsetX=4]="OffsetX",a[a.OffsetY=8]="OffsetY",a[a.RotateWithShape=16]="RotateWithShape",a[a.Size=32]="Size",a[a.Style=64]="Style",a[a.Transparency=128]="Transparency",a[a.Type=256]="Type",a[a.Visible=512]="Visible"}(l||(l={})),m=function(a){r(b,a);function b(b,c){var d=a.call(this,c)||this,f=d;return f.Ira=5,f.Jra=!1,f.Kra=e.ShapeConstants.DefaultOffset,f.Lra=e.ShapeConstants.DefaultOffset,f.Mra=!1,f.Oz=100,f.Gqa=0,f.bz=!1,f.presetShadowOffset=-6,f.xc=b,f.hqa=c,d}return b.prototype.OnStyleChange=function(){var a=this;a.Ira=0,a.Kra=0,a.Lra=0,a.Oz=100,a.Gqa=0,a.UnDirty(l.Type),a.Style===g.ShadowStyle.ShadowStyleInnerShadow?a.Mra=!1:a.Style===g.ShadowStyle.ShadowStyleOuterShadow&&(a.Mra=!0)},b.prototype.OnTypeChange=function(){var a=this;a.Ira=5,a.Kra=a.presetShadowOffset,a.Lra=a.presetShadowOffset,a.Oz=100,a.Gqa=.5,a.UnDirty(l.Style)},b.prototype.GetDefaultColor=function(){var a=new f.ColorFormat(this.xc,this.hqa&&this.hqa.Nra);return a.ColorType=g.SolidColorType.None,a},b.prototype.IncrementOffsetX=function(a){this.OffsetX+=a},b.prototype.IncrementOffsetY=function(a){this.OffsetY+=a},b.prototype.FromOOModel=function(a){this.Lqa=a},b.prototype.ToOOModel=function(){return this.Lqa},b.prototype.ToCT_PresetShadowEffect=function(){var a=this,b=a.OffsetX,c=a.OffsetY,d=k(b*b+c*c),f={dist:0,dir:0};return f.dist=d,f.dir=j(e.NumberExtension.GetTriangleAngle(b,c,d)*e.ShapeConstants.PositiveFixedAngleConvert),f.prst=a.Type,a.ToCT_Color(f),f},b.prototype.ToCT_InnerShadowEffect=function(){var a=this,b=a.OffsetX,c=a.OffsetY,d=k(b*b+c*c),f={blurRad:0,dist:0,dir:0};return f.blurRad=a.Blur,f.dist=d,0===b&&0===c||(f.dir=j(e.NumberExtension.GetTriangleAngle(b,c,d)*e.ShapeConstants.PositiveFixedAngleConvert)),f.scrgbClr=a.ToCT_ScRgbColor(),f},b.prototype.ToCT_OuterShadowEffect=function(){var a=this,b=a.OffsetX,c=a.OffsetY,d=k(b*b+c*c),f={blurRad:0,dist:0,dir:0,sx:1e5,sy:1e5,kx:0,ky:0,algn:g.TextureAlignment.TextureBottom,rotWithShape:!0};return f.blurRad=a.Blur,f.dist=d,0===b&&0===c||(f.dir=j(e.NumberExtension.GetTriangleAngle(b,c,d)*e.ShapeConstants.PositiveFixedAngleConvert)),f.rotWithShape=a.RotateWithShape===!0,f.sx=j(a.Size*e.ShapeConstants.RelativeRectConver),f.sy=j(a.Size*e.ShapeConstants.RelativeRectConver),f.scrgbClr=a.ToCT_ScRgbColor(),f},b.prototype.ToCT_Color=function(a){var b,c,d,f,h,k=this;i(k.Nra)||(k.Nra.ColorType===g.SolidColorType.Theme?(f={},f.val=k.Nra.ObjectThemeColor,0!==k.Nra.Brightness&&(b=j(1e5*k.Nra.Brightness),f.lumMod=[b],c=j(1e5*(1-k.Nra.Brightness)),f.lumOff=[c]),k.Nra.TintAndShade>0&&k.Nra.TintAndShade<1&&(d=j(k.Nra.TintAndShade*e.ShapeConstants.ShadeConver),f.shade=[d]),a.schemeClr=f):k.Nra.ColorType===g.SolidColorType.RGB&&(h={},h.val=[k.Nra.RGB.R,k.Nra.RGB.G,k.Nra.RGB.B],0!==k.Nra.Brightness&&(b=j(1e5*k.Nra.Brightness),h.lumMod=[b],c=j(1e5*(1-k.Nra.Brightness)),h.lumOff=[c]),0!==k.Transparency&&(d=j(k.Transparency*e.ShapeConstants.PositiveFixedPercentageConvert),h.alpha=[d]),a.srgbClr=h))},b.prototype.ToCT_ScRgbColor=function(){var a,b={};return 0!==this.Transparency&&(a=j(this.Transparency*e.ShapeConstants.PositiveFixedPercentageConvert),b.alpha=[a]),b},b.prototype.SetState=function(b,c){a.prototype.SetState.call(this,b,c),b!==l.Blur&&b!==l.OffsetX&&b!==l.OffsetY&&b!==l.RotateWithShape&&b!==l.Size&&b!==l.Transparency||this.GetState(l.Style)||(this.Wqa=g.ShadowStyle.ShadowStyleOuterShadow)},Object.defineProperty(b.prototype,"ForeColor",{get:function(){var a=this;return i(a.Nra)&&(a.Nra=a.GetDefaultColor()),a.Nra},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Obscured",{get:function(){var a=this;return a.GetState(l.Obscured)||i(a.hqa)?a.Jra:a.hqa.Obscured},set:function(a){this.Jra=a,this.SetState(l.Obscured,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Blur",{get:function(){var a=this;return a.GetState(l.Blur)||i(a.hqa)?a.Ira:a.hqa.Blur},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Blue",{set:function(a){this.Ira=a,this.SetState(l.Blur,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"OffsetX",{get:function(){var a=this;return a.GetState(l.OffsetX)||i(a.hqa)?a.Kra:a.hqa.OffsetX},set:function(a){this.Kra=a,this.SetState(l.OffsetX,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"OffsetY",{get:function(){var a=this;return a.GetState(l.OffsetY)||i(a.hqa)?a.Lra:a.hqa.OffsetY},set:function(a){this.Lra=a,this.SetState(l.OffsetY,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RotateWithShape",{get:function(){var a=this;return a.GetState(l.RotateWithShape)||i(a.hqa)?a.Mra:a.hqa.RotateWithShape},set:function(a){var b=this;b.Style===g.ShadowStyle.ShadowStyleInnerShadow||b.GetState(l.Type)||(b.Mra=a,b.SetState(l.RotateWithShape,!0))},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Size",{get:function(){var a=this;return a.GetState(l.Size)||i(a.hqa)?a.Oz:a.hqa.Size},set:function(a){this.Oz=a,this.SetState(l.Size,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Style",{get:function(){var a=this;return a.GetState(l.Style)||i(a.hqa)?a.Wqa:a.hqa.Style},set:function(a){var b=this;b.Style!==a&&b.OnStyleChange(),b.Wqa=a,b.SetState(l.Style,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Transparency",{get:function(){var a=this;return a.GetState(l.Transparency)||i(a.hqa)?a.Gqa:a.hqa.Transparency},set:function(a){this.Gqa=a,this.SetState(l.Transparency,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Type",{get:function(){var a=this;return a.GetState(l.Type)||i(a.hqa)?a.Nc:a.hqa.Type},set:function(a){this.Nc=a,this.SetState(l.Type,!0),this.OnTypeChange()},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Visible",{get:function(){var a=this;return a.GetState(l.Visible)||i(a.hqa)?a.bz:a.hqa.Visible},set:function(a){var b=this;b.bz=a,b.SetState(l.Visible,!0),a===!1||b.GetState(l.Style)||(b.Wqa=g.ShadowStyle.ShadowStyleOuterShadow)},enumerable:!0,configurable:!0}),b}(d.StatefullBase),b.ShadowFormat=m,n=function(a){r(b,a);function b(b){return a.call(this,b)||this}return b.prototype.FromOOModel=function(a){this.Lqa=a},b.prototype.ToOOModel=function(){return this.Lqa},b}(d.StatefullBase),b.SoftEdgeFormat=n,o=function(a){r(b,a);function b(b,c){var d=a.call(this,c)||this,e=d;return e.Oz=100,e.Nc=g.ReflectionType.ReflectionType1,e.presetShadowOffset=-6,e.xc=b,e.hqa=c,d}return Object.defineProperty(b.prototype,"Blur",{get:function(){var a=this;return a.GetState(h.Blur)||i(a.hqa)?a.Ira:a.hqa.Blur},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Blue",{set:function(a){this.Ira=a,this.SetState(h.Blur,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Offset",{get:function(){var a=this;return a.GetState(h.Offset)||i(a.hqa)?a.UP:a.hqa.Offset},set:function(a){this.UP=a,this.SetState(h.Offset,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Size",{get:function(){var a=this;return a.GetState(h.Size)||i(a.hqa)?a.Oz:a.hqa.Size},set:function(a){this.Oz=a,this.SetState(h.Size,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Transparency",{get:function(){var a=this;return a.GetState(h.Transparency)||i(a.hqa)?a.Gqa:a.hqa.Transparency},set:function(a){this.Gqa=a,this.SetState(h.Transparency,!0)},enumerable:!0,configurable:!0}),b.prototype.FromOOModel=function(a){this.Lqa=a},b.prototype.ToOOModel=function(){return this.Lqa},b.prototype.OnTypeChange=function(){var a=this;a.Ira=5,a.UP=a.presetShadowOffset,a.Oz=100,a.Gqa=.5},b}(d.StatefullBase),b.ReflectionFormat=o,function(a){a[a.Color=1]="Color",a[a.Radius=2]="Radius",a[a.Transparency=4]="Transparency"}(p||(p={})),q=function(a){r(b,a);function b(b,c){var d=a.call(this,c)||this;return d.xc=b,d.hqa=c,d}return Object.defineProperty(b.prototype,"Color",{get:function(){var a=this;return i(a.tE)&&(a.tE=a.GetDefaultColor()),a.tE},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Radius",{get:function(){var a=this;return a.GetState(p.Radius)||i(a.hqa)?a.radius:a.hqa.Radius},set:function(a){this.radius=a,this.Dirty(p.Radius)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Transparency",{get:function(){var a=this;return a.GetState(p.Transparency)||i(a.hqa)?a.Gqa:a.hqa.Transparency},set:function(a){this.Gqa=a,this.Dirty(p.Transparency)},enumerable:!0,configurable:!0}),b.prototype.FromOOModel=function(a){this.Lqa=a},b.prototype.ToOOModel=function(){return this.Lqa},b.prototype.GetDefaultColor=function(){var a=new f.ColorFormat(this.xc,this.hqa&&this.hqa.Color);return a.ColorType=g.SolidColorType.None,a},b.prototype.SetState=function(b,c){var d=this;a.prototype.SetState.call(d,b,c),b!==p.Radius&&b!==p.Transparency||d.tE&&d.tE.ColorType===g.SolidColorType.None&&(d.tE.ColorType=g.SolidColorType.RGB)},b.prototype.ToCT_Color=function(a){var b,c,d,f,h,k,l,m=this,n=m.tE;i(n)||(f=n.ColorType,h=n.Brightness,f===g.SolidColorType.Theme?(k={},k.val=n.ObjectThemeColor,0!==h&&(b=j(1e5*h),k.lumMod=[b],c=j(1e5*(1-h)),k.lumOff=[c]),n.TintAndShade>0&&n.TintAndShade<1&&(d=j(n.TintAndShade*e.ShapeConstants.ShadeConver),k.shade=[d]),a.schemeClr=k):f===g.SolidColorType.RGB&&(l={},l.val=[n.RGB.R,n.RGB.G,n.RGB.B],0!==h&&(b=j(1e5*h),l.lumMod=[b],c=j(1e5*(1-h)),l.lumOff=[c]),0!==m.Transparency&&(d=j(m.Transparency*e.ShapeConstants.PositiveFixedPercentageConvert),l.alpha=[d]),a.srgbClr=l))},b}(d.StatefullBase),b.GlowFormat=q},"./dist/plugins/floatingObject/drawing/fillFormat.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/stateful.js"),e=c("./dist/plugins/floatingObject/drawing/common.js"),f=c("./dist/plugins/floatingObject/drawing/colorFormat.js"),g=c("./dist/plugins/floatingObject/drawing/gradient.js"),h=c("./dist/plugins/floatingObject/drawing/picture.js"),i=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),j=e.ShapeConstants,k=j.PositiveFixedPercentageConvert,l=j.PositiveFixedAngleConvert,m=j.PositionConver,n=e.ShapeUtility,o=e.UnitHelper,p=o.isNullOrUndefined,q=null,r=void 0,s=Math.floor,t="The specified value is out of range",function(a){a[a.GradientStops=1]="GradientStops",a[a.GradientStyle=2]="GradientStyle",a[a.GradientColorType=4]="GradientColorType",a[a.Pattern=8]="Pattern",a[a.PresetTexture=16]="PresetTexture",a[a.RotateWithObject=32]="RotateWithObject",a[a.TextureAlignment=64]="TextureAlignment",a[a.TextureHorizontalScale=128]="TextureHorizontalScale",a[a.TextureName=256]="TextureName",a[a.TextureOffsetX=512]="TextureOffsetX",a[a.TextureOffsetY=1024]="TextureOffsetY",a[a.TextureTile=2048]="TextureTile",a[a.TextureType=4096]="TextureType",a[a.TextureVerticalScale=8192]="TextureVerticalScale",a[a.Transparency=16384]="Transparency",a[a.Type=32768]="Type",a[a.Visible=65536]="Visible",a[a.Color=131072]="Color",a[a.PatternColor=262144]="PatternColor",a[a.PictureFormat=524288]="PictureFormat",a[a.GradientDegree=1048576]="GradientDegree",a[a.GradientAngle=2097152]="GradientAngle",a[a.GradientVariant=4194304]="GradientVariant",a[a.PresetGradientType=8388608]="PresetGradientType"}(u||(u={})),v=function(a){w(b,a);function b(b,c,d,e){var f=a.call(this,c)||this,j=f;return j.dqa=!0,j.eqa=i.ST_PathShadeType.rect,j.wE=i.PatternType.PatternNone,j.fqa=!0,j.bz=!0,j.gqa=q,j.xc=b,j.Nc=i.FillType.Solid,j.hqa=c,j.iqa=d,j.xo=e,j.jqa=new h.PictureFormat(i.PictureFormatType.PictureFill,c&&c.PictureFormat),j.jqa.Container=e,j.kqa=1,j.lqa=1,j.mqa=new g.GradientStops(j.xc),f}return Object.defineProperty(b.prototype,"Parent",{get:function(){return this.hqa},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PictureFormat",{get:function(){return this.jqa},set:function(a){var b,c=this;p(c.jqa)||c.jqa.SetParentForChildren(a),b=c.jqa,c.jqa=a,p(b)||(c.jqa.ParentStateful=b.ParentStateful)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PatternColor",{get:function(){return this.PatternColorInternal},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Color",{get:function(){return this.ColorInternal},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientAngle",{get:function(){var a=this;if(!a.IsDirty(u.GradientAngle)&&!p(a.hqa))return a.hqa.GradientAngle;if(a.GradientStyle===i.GradientStyle.GradientFromCenter||a.GradientStyle===i.GradientStyle.GradientFromCorner)throw Error();return a.nqa},set:function(a){this.nqa=a,this.Dirty(u.GradientAngle)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientColorType",{get:function(){return this.GetGradientColorType()},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientDegree",{get:function(){var a=this;return a.IsDirty(u.GradientDegree)||p(a.hqa)?a.oqa:a.hqa.GradientDegree},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientStops",{get:function(){var a=this;return a.IsDirty(u.GradientStops)||p(a.hqa)?a.mqa:a.hqa.GradientStops},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientStyle",{get:function(){var a=this;return a.IsDirty(u.GradientStyle)||p(a.hqa)?a.qqa:a.hqa.GradientStyle},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientVariant",{get:function(){var a=this;
return a.IsDirty(u.GradientVariant)||p(a.hqa)?a.sqa:a.hqa.GradientVariant},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Pattern",{get:function(){var a=this;return a.IsDirty(u.Pattern)||p(a.hqa)?a.wE:a.hqa.Pattern},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PresetGradientType",{get:function(){var a=this;return a.IsDirty(u.PresetGradientType)||p(a.hqa)?a.wqa:a.hqa.PresetGradientType},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RotateWithObject",{get:function(){var a=this;return a.IsDirty(u.RotateWithObject)||p(a.hqa)?a.fqa:a.hqa.RotateWithObject},set:function(a){this.fqa=a,this.Dirty(u.RotateWithObject)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TextureAlignment",{get:function(){var a=this;return a.IsDirty(u.TextureAlignment)||p(a.hqa)?a.yqa:a.hqa.TextureAlignment},set:function(a){this.yqa=a,this.Dirty(u.TextureAlignment)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TextureHorizontalScale",{get:function(){var a=this;return a.IsDirty(u.TextureHorizontalScale)||p(a.hqa)?a.kqa:a.hqa.TextureHorizontalScale},set:function(a){this.kqa=a,this.Dirty(u.TextureHorizontalScale)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TextureOffsetX",{get:function(){var a=this;return a.IsDirty(u.TextureOffsetX)||p(a.hqa)?a.zqa:a.hqa.TextureOffsetX},set:function(a){this.zqa=a,this.Dirty(u.TextureOffsetX)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TextureOffsetY",{get:function(){var a=this;return a.IsDirty(u.TextureOffsetY)||p(a.hqa)?a.Aqa:a.hqa.TextureOffsetY},set:function(a){this.Aqa=a,this.Dirty(u.TextureOffsetY)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TextureVerticalScale",{get:function(){var a=this;return a.IsDirty(u.TextureVerticalScale)||p(a.hqa)?a.lqa:a.hqa.TextureVerticalScale},set:function(a){this.lqa=a,this.Dirty(u.TextureVerticalScale)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Transparency",{get:function(){var a=this;return a.IsDirty(u.Transparency)||p(a.hqa)?a.ColorInternal.Transparency:a.hqa.Transparency},set:function(a){this.ColorInternal.Transparency=a,this.Dirty(u.Transparency)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Type",{get:function(){var a=this;return a.IsDirty(u.Type)||p(a.hqa)?a.Nc:a.hqa.Type},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Visible",{get:function(){var a=this;return a.IsDirty(u.Visible)||p(a.hqa)?a.bz:a.hqa.Visible},set:function(a){this.bz=a,this.Dirty(u.Visible)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ColorInternal",{get:function(){var a=this;return p(a.tE)&&(a.tE=new f.ColorFormat(a.xc,p(a.hqa)?q:a.hqa.Color),a.tE.AutoColorFormat=a.iqa),a.tE},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PatternColorInternal",{get:function(){var a=this;return p(a.Bqa)&&(a.Bqa=new f.ColorFormat(a.xc,p(a.hqa)?q:a.hqa.PatternColor)),a.Bqa},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PresetTexture",{get:function(){var a=this;return a.IsDirty(u.PresetTexture)||p(a.hqa)?a.Cqa:a.hqa.PresetTexture},set:function(a){this.Cqa=a,this.Dirty(u.PresetTexture)},enumerable:!0,configurable:!0}),b.prototype.GetGradientColorType=function(){var a,b,c,d,e=[];for(a=0,b=this.mqa.GradientStopList;a<b.length;a++)c=b[a],this.FindInColorStops(c.Color,e)||e.push(c.Color);return d=e.length,0===d?i.GradientColorType.GradientColorNone:1===d?i.GradientColorType.GradientOneColor:2===d?i.GradientColorType.GradientTwoColors:i.GradientColorType.GradientMultiColor},b.prototype.FindInColorStops=function(a,b){var c,d,e,f;for(c=0,d=b;c<d.length;c++)if(e=d[c],f=e.ColorType,f===a.ColorType)if(f===i.SolidColorType.Theme){if(e.ObjectThemeColor===a.ObjectThemeColor&&e.TintAndShade===a.TintAndShade)return!0}else if(f===i.SolidColorType.RGB&&e.RGB.Equals(a.RGB)&&e.TintAndShade===a.TintAndShade)return!0;return!1},b.prototype.pqa=function(a){this.oqa=a,this.Dirty(u.GradientDegree)},b.prototype.Fpa=function(a){this.mqa=a,this.Dirty(u.GradientStops)},b.prototype.rqa=function(a){this.qqa=a,this.Dirty(u.GradientStyle)},b.prototype.tqa=function(a){this.sqa=a,this.Dirty(u.GradientVariant)},b.prototype.uqa=function(a){if(a===i.PatternType.PatternNone)throw Error(t);var b=this;b.Pattern===i.PatternType.PatternNone&&b.vqa(i.FillType.Patterned),b.wE=a,b.Dirty(u.Pattern)},b.prototype.xqa=function(a){this.wqa=a,this.Dirty(u.PresetGradientType)},b.prototype.vqa=function(a){this.Nc=a,this.Dirty(u.Type)},b.prototype.InitGradientInfo=function(a,b,c){if(a===i.GradientStyle.GradientFromTitle)throw Error(t);var d=this;d.rqa(a),d.tqa(b),d.vqa(i.FillType.Gradient),d.xqa(c)},b.prototype.OneColorGradient=function(a,b,c){if(c<0||c>1)throw Error(t);var d=this;d.InitGradientInfo(a,b,i.PresetGradientType.GradientEarlySunset),d.pqa(c),n.InitGradientStops(d.xc,d,i.GradientColorType.GradientOneColor)},b.prototype.TwoColorGradient=function(a,b){this.InitGradientInfo(a,b,i.PresetGradientType.GradientEarlySunset),n.InitGradientStops(this.xc,this,i.GradientColorType.GradientTwoColors)},b.prototype.PresetGradient=function(a,b,c){this.InitGradientInfo(a,b,c),n.InitGradientStops(this.xc,this,i.GradientColorType.GradientPresetColors)},b.prototype.Patterned=function(a){this.SetPatternColor(),this.vqa(i.FillType.Patterned),this.uqa(a)},b.prototype.SetPatternColor=function(){var a=this;a.Color.ObjectThemeColor=i.ColorSchemeIndex.LT1,a.PatternColor.ObjectThemeColor=i.ColorSchemeIndex.Accent1,a.Color.ClearModel(),a.PatternColor.ClearModel()},b.prototype.Solid=function(){this.vqa(i.FillType.Solid),p(this.tE)||this.tE.ClearModel()},b.prototype.ClearColor=function(){this.tE=q},b.prototype.GetState=function(b,c){var d=this;return c===r&&(c=!1),b!==u.Color||p(d.tE)?b!==u.PatternColor||p(d.Bqa)?a.prototype.GetState.call(d,b,c):d.Bqa.IsDirtyIncludingParent(c):d.tE.IsDirtyIncludingParent(c)},b.prototype.IsDirtyIncludingParent=function(b){void 0===b&&(b=!1);var c=this;return!(p(c.Bqa)||!c.Bqa.IsDirtyIncludingParent())||(!(p(c.tE)||!c.tE.IsDirtyIncludingParent())||(!p(c.mqa)&&c.mqa.Count>0||(!(p(c.jqa)||!c.jqa.IsDirtyIncludingParent())||a.prototype.IsDirtyIncludingParent.call(c,b))))},b.prototype.OnParentChanged=function(a){var b=this;b.hqa=a,p(b.hqa)?(p(b.tE)||(b.tE.ParentStateful=q),p(b.Bqa)||(b.Bqa.ParentStateful=q)):(p(b.tE)||(b.tE.ParentStateful=b.hqa.Color),p(b.Bqa)||(b.Bqa.ParentStateful=b.hqa.PatternColor))},b.prototype.FromOOModel=function(a,b){if(p(b))throw Error("colorFillType not provided!");var c=this;c.gqa=a,b===i.ColorFillType.SolidColorFillProperties?(c.ColorInternal.FromOOModel(c.gqa),c.Nc=i.FillType.Solid,c.SetState(u.Type,!0)):b===i.ColorFillType.PatternFillProperties?(c.FromCT_PatternFillProperties(a),c.Nc=i.FillType.Patterned,c.SetState(u.Type,!0)):b===i.ColorFillType.GradientFillProperties?(c.FromCT_GradientFillProperties(a),c.Nc=i.FillType.Gradient,c.SetState(u.Type,!0)):b===i.ColorFillType.BlipFillProperties?(c.jqa.FromOOModel(a),c.Nc=i.FillType.Picture,c.SetState(u.Type,!0)):b===i.ColorFillType.GroupFillProperties?(c.Nc=i.FillType.Group,c.SetState(u.Type,!0)):b===i.ColorFillType.NoFillProperties&&(c.Nc=i.FillType.Solid,c.SetState(u.Type,!0))},b.prototype.ToOOModel=function(a){var b;if(this.Visible)switch(this.Type){case i.FillType.Solid:b=this.ToCT_SolidColorFillProperties();break;case i.FillType.Patterned:b=this.ToCT_PatternFillProperties();break;case i.FillType.Gradient:b=this.ToCT_GradientFillProperties();break;case i.FillType.Textured:b=this.ToCT_TileFillProperties();break;case i.FillType.Background:break;case i.FillType.Picture:b=this.ToCT_PictureFillProperties();break;case i.FillType.Group:b={colorFillType:i.ColorFillType.GroupFillProperties}}else b={colorFillType:i.ColorFillType.NoFillProperties};return b},b.prototype.FromCT_PatternFillProperties=function(a){this.uqa(a.prst),this.ColorInternal.FromOOModel(a.bgClr),this.PatternColorInternal.FromOOModel(a.fgClr)},b.prototype.ToCT_PatternFillProperties=function(){var a,b=this;return b.xc.DrawingType===i.DrawingType.GroupShape?q:(a={colorFillType:i.ColorFillType.PatternFillProperties},a.prst=b.Pattern,b.PatternColorInternal.ColorType!==i.SolidColorType.None&&(a.fgClr=b.PatternColorInternal.Color_ToOOModel()),b.Color.ColorType!==i.SolidColorType.None&&(a.bgClr=b.Color.Color_ToOOModel()),a)},b.prototype.FromCT_GradientFillProperties=function(a){var b=this;b.Fpa(new g.GradientStops(b.xc)),b.mqa.FromOOModel(a.gsLst),b.RotateWithObject=a.rotWithShape,p(a.path)?p(a.lin)||(b.FromCT_LinearShadeProperties(a.lin),b.FromTitleCT_RelativeRect(a.tileRect)):b.FromCT_PathShadeProperties(a.path)},b.prototype.ToCT_GradientFillProperties=function(){var a=this,b={colorFillType:i.ColorFillType.GradientFillProperties};return b.rotWithShape=a.RotateWithObject,!p(a.GradientStops)&&a.GradientStops.Count>0&&(b.gsLst=a.GradientStops.ToOOModel()),a.GradientStyle===i.GradientStyle.GradientFromCenter||a.GradientStyle===i.GradientStyle.GradientFromCorner?b.path=a.ToCT_PathShadeProperties():b.lin=a.ToCT_LinearShadeProperties(),b.tileRect=a.ToTitleCT_RelativeRect(),b},b.prototype.ToCT_PictureFillProperties=function(){return this.jqa.ToOOModel()},b.prototype.FromTitleCT_RelativeRect=function(a){var b=this;p(a)||(a.l===-k&&a.t===-k?b.tqa(1):a.t===-k&&a.r===-k?b.tqa(2):a.l===-k&&a.b===-k?b.tqa(3):a.r===-k&&a.b===-k&&b.tqa(4))},b.prototype.ToTitleCT_RelativeRect=function(){var a={l:0,r:0,t:0,b:0},b=this.GradientVariant;return this.GradientStyle===i.GradientStyle.GradientFromCorner&&(1===b?(a.l=-k,a.t=-k):2===b?(a.t=-k,a.r=-k):3===b?(a.l=-k,a.b=-k):4===b&&(a.r=-k,a.b=-k)),a},b.prototype.FromCT_LinearShadeProperties=function(a){this.GradientAngle=a.ang/l,this.Vkb=!0,this.dqa=a.scaled},b.prototype.ToCT_LinearShadeProperties=function(){return{ang:s(this.GradientAngle*l),scaled:this.dqa}},b.prototype.FromCT_PathShadeProperties=function(a){p(a)||(this.eqa=a.path,this.FromCT_RelativeRect(a.fillToRect))},b.prototype.ToCT_PathShadeProperties=function(){return{path:this.eqa,fillToRect:this.ToCT_RelativeRect()}},b.prototype.FromCT_RelativeRect=function(a){var b,c;p(a)||(b=.5*k,c=this,a.l===b&&a.t===b&&a.r===b&&a.b===b?c.rqa(i.GradientStyle.GradientFromCenter):a.r===k&&a.b===k?(c.rqa(i.GradientStyle.GradientFromCorner),c.tqa(1)):a.l===k&&a.b===k?(c.rqa(i.GradientStyle.GradientFromCorner),c.tqa(2)):a.r===k&&a.t===k?(c.rqa(i.GradientStyle.GradientFromCorner),c.tqa(3)):a.l===k&&a.t===k&&(c.rqa(i.GradientStyle.GradientFromCorner),c.tqa(4)))},b.prototype.ToCT_RelativeRect=function(){var a,b={l:0,r:0,t:0,b:0},c=.5*k;return this.GradientStyle===i.GradientStyle.GradientFromCenter?(b.l=c,b.t=c,b.r=c,b.b=c):this.GradientStyle===i.GradientStyle.GradientFromCorner&&(a=this.GradientVariant,1===a?(b.r=k,b.b=k):2===a?(b.l=k,b.b=k):3===a?(b.t=k,b.r=k):4===a&&(b.l=k,b.t=k)),b},b.prototype.ToCT_SolidColorFillProperties=function(){return this.ColorInternal.ToOOModel()},b.prototype.ToCT_TileFillProperties=function(){var a=this.jqa.ToOOModel();return a.tile=this.ToCT_TileInfoProperties(),a},b.prototype.ToCT_TileInfoProperties=function(){var a=this;return{algn:a.TextureAlignment,tx:a.TextureOffsetX,ty:a.TextureOffsetY,sx:s(a.TextureHorizontalScale*m),sy:s(a.TextureVerticalScale*m)}},b.prototype.Clone=function(){var a=this,c=new b(a.xc,a.hqa,a.iqa,a.xo);return p(a.Bqa)||(c.Bqa=a.Bqa.Clone()),p(a.tE)||(c.tE=a.tE.Clone()),c.oqa=a.oqa,c.dqa=a.dqa,c.mqa=a.mqa,c.sqa=a.sqa,c.wE=a.wE,c.wqa=a.wqa,c.Cqa=a.Cqa,c.Dqa=a.Dqa,c.Eqa=a.Eqa,c.Nc=a.Nc,c.nqa=a.nqa,c.fqa=a.fqa,c.yqa=a.yqa,c.kqa=a.kqa,c.zqa=a.zqa,c.Aqa=a.Aqa,c.Fqa=a.Fqa,c.lqa=a.lqa,c.Gqa=a.Gqa,c.bz=a.bz,c.rpa=a.rpa,c.hqa=a.hqa,c},b}(d.StatefullBase),b.FillFormat=v},"./dist/plugins/floatingObject/drawing/fontData.js":function(a,b,c){"use strict";var d,e,f,g,h,i;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/common.js"),e=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),f=c("./dist/plugins/floatingObject/drawing/colorData.js"),g=d.UnitHelper.isNullOrUndefined,function(a){a[a.None=0]="None",a[a.Color=1]="Color",a[a.Size=2]="Size",a[a.Name=4]="Name",a[a.Family=8]="Family",a[a.CharSet=16]="CharSet",a[a.Scheme=32]="Scheme",a[a.Bold=64]="Bold",a[a.Italic=128]="Italic",a[a.Underline=256]="Underline",a[a.Strike=512]="Strike",a[a.VertAlign=1024]="VertAlign",a[a.Shadow=2048]="Shadow",a[a.Outline=4096]="Outline",a[a.Condense=8192]="Condense",a[a.Extend=16384]="Extend",a[a.All=32767]="All"}(h||(h={})),i=function(){function a(){var a=this;a.Flag=h.None,a.Size=0,a.Family=0,a.CharSet=0,a.Scheme=e.ThemeFont.None,a.Underline=e.UnderlineType.None,a.VertAlign=e.VertAlignType.Baseline}return a.prototype.Equals=function(a){if(g(a))return!1;var b=this;return b.Size===a.Size&&b.Color.Equals(a.Color)&&b.Name===a.Name&&b.Family===a.Family&&b.CharSet===a.CharSet&&b.Scheme===a.Scheme&&b.Bold===a.Bold&&b.Italic===a.Italic&&b.Underline===a.Underline&&b.Strike===a.Strike&&b.VertAlign===a.VertAlign&&b.Shadow===a.Shadow&&b.Outline===a.Outline&&b.Condense===a.Condense&&b.Extend===a.Extend&&b.NotUsed===a.NotUsed},a.prototype.Clone=function(){var b=new a,c=this;return b.Color=c.Color,b.Size=c.Size,b.Name=c.Name,b.Family=c.Family,b.CharSet=c.CharSet,b.Scheme=c.Scheme,b.Bold=c.Bold,b.Italic=c.Italic,b.Underline=c.Underline,b.Strike=c.Strike,b.VertAlign=c.VertAlign,b.Shadow=c.Shadow,b.Outline=c.Outline,b.Condense=c.Condense,b.Extend=c.Extend,b.Flag=c.Flag,b.NotUsed=c.NotUsed,b},a.prototype.UpdateDefaultValueFlag=function(){var a=this,b=h.None;a.Color.UpdateDefaultValueFlag(),a.Color.Flag!==f.ColorDataFlag.None&&(b|=h.Color),0!==a.Size&&(b|=h.Size),g(a.Name)||(b|=h.Name),0!==a.Family&&(b|=h.Family),0!==a.CharSet&&(b|=h.CharSet),a.Scheme!==e.ThemeFont.None&&(b|=h.Scheme),a.Bold&&(b|=h.Bold),a.Italic&&(b|=h.Italic),a.Underline!==e.UnderlineType.None&&(b|=h.Underline),a.Strike&&(b|=h.Strike),a.VertAlign!==e.VertAlignType.Baseline&&(b|=h.VertAlign),a.Shadow&&(b|=h.Shadow),a.Outline&&(b|=h.Outline),a.Condense&&(b|=h.Condense),a.Extend&&(b|=h.Extend),a.Flag=b},a.prototype.UpdateFlagFromBottom=function(){var a=this;a.Color.UpdateFlagFromBottom(),a.Color.Flag===f.ColorDataFlag.None?a.Flag&=~h.Color:a.Flag|=h.Color},a.prototype.UpdateDefaultValueFlagForDxf=function(){var a=this,b=a.Flag,c=h.None;(b&h.Bold)===h.Bold&&(c|=h.Bold),(b&h.Italic)===h.Italic&&(c|=h.Italic),a.Color.UpdateDefaultValueFlag(),a.Color.Flag!==f.ColorDataFlag.None&&(c|=h.Color),0!==a.Size&&(c|=h.Size),g(a.Name)||(c|=h.Name),0!==a.Family&&(c|=h.Family),0!==a.CharSet&&(c|=h.CharSet),a.Scheme!==e.ThemeFont.None&&(c|=h.Scheme),a.Underline!==e.UnderlineType.None&&(c|=h.Underline),a.Strike&&(c|=h.Strike),a.VertAlign!==e.VertAlignType.Baseline&&(c|=h.VertAlign),a.Shadow&&(c|=h.Shadow),a.Outline&&(c|=h.Outline),a.Condense&&(c|=h.Condense),a.Extend&&(c|=h.Extend),a.Flag=c},a.prototype.Compose=function(a,b){void 0===b&&(b=!0),a.Flag!==h.None&&(b?this.ConflictCompose(a):this.NonConflictCompose(a))},a.prototype.ConflictCompose=function(a){var b=this,c=a.Flag;(c&h.Color)===h.Color&&b.Color.Compose(a.Color),(c&h.Size)===h.Size&&(b.Size=a.Size),(c&h.Name)===h.Name&&(b.Name=a.Name),(c&h.Family)===h.Family&&(b.Family=a.Family),(c&h.CharSet)===h.CharSet&&(b.CharSet=a.CharSet),(c&h.Scheme)===h.Scheme?b.Scheme=a.Scheme:(c&h.Name)===h.Name&&(b.Scheme=e.ThemeFont.None,c&=~h.Scheme),(c&h.Bold)===h.Bold&&(b.Bold=a.Bold),(c&h.Italic)===h.Italic&&(b.Italic=a.Italic),(c&h.Underline)===h.Underline&&(b.Underline=a.Underline),(c&h.Strike)===h.Strike&&(b.Strike=a.Strike),(c&h.VertAlign)===h.VertAlign&&(b.VertAlign=a.VertAlign),(c&h.Shadow)===h.Shadow&&(b.Shadow=a.Shadow),(c&h.Outline)===h.Outline&&(b.Outline=a.Outline),(c&h.Condense)===h.Condense&&(b.Condense=a.Condense),(c&h.Extend)===h.Extend&&(b.Extend=a.Extend),b.Flag|=c},a.prototype.NonConflictCompose=function(a){var b=this,c=b.Flag;b.Color.Compose(a.Color,!1),(c&h.Size)!==h.Size&&(b.Size=a.Size),(c&h.Name)!==h.Name&&(b.Name=a.Name),(c&h.Family)!==h.Family&&(b.Family=a.Family),(c&h.CharSet)!==h.CharSet&&(b.CharSet=a.CharSet),(c&h.Scheme)!==h.Scheme&&(b.Scheme=a.Scheme),(c&h.Bold)!==h.Bold&&(b.Bold=a.Bold),(c&h.Italic)!==h.Italic&&(b.Italic=a.Italic),(c&h.Underline)!==h.Underline&&(b.Underline=a.Underline),(c&h.Strike)!==h.Strike&&(b.Strike=a.Strike),(c&h.VertAlign)!==h.VertAlign&&(b.VertAlign=a.VertAlign),(c&h.Shadow)!==h.Shadow&&(b.Shadow=a.Shadow),(c&h.Outline)!==h.Outline&&(b.Outline=a.Outline),(c&h.Condense)!==h.Condense&&(b.Condense=a.Condense),(c&h.Extend)!==h.Extend&&(b.Extend=a.Extend),b.Flag|=a.Flag},a.prototype.RemoveDuplicateStyle=function(a){return this.Compare(a,!1)},a.prototype.RemoveDifferentStyle=function(a){return this.Compare(a,!0)},a.prototype.Compare=function(a,b){var c,d,e,g,i=this,j=i.Flag,k=a.Flag,l=[],m=!1;if((j&h.Color)===h.Color&&(c=(k&h.Color)!==h.Color,c||(b?(m=i.Color.RemoveDifferentStyle(a.Color),c=i.Color.Flag===f.ColorDataFlag.None):(m=i.Color.RemoveDuplicateStyle(a.Color),c=i.Color.Flag!==f.ColorDataFlag.None)),d=b?c:!c,d&&l.push(h.Color)),(j&h.Size)===h.Size&&(c=(k&h.Size)!==h.Size||i.Size!==a.Size,d=b?c:!c,d&&l.push(h.Size)),(j&h.Name)===h.Name&&(c=(k&h.Name)!==h.Name||i.Name!==a.Name,d=b?c:!c,d&&l.push(h.Name)),(j&h.Family)===h.Family&&(c=(k&h.Family)!==h.Family||i.Family!==a.Family,d=b?c:!c,d&&l.push(h.Family)),(j&h.CharSet)===h.CharSet&&(c=(k&h.CharSet)!==h.CharSet||i.CharSet!==a.CharSet,d=b?c:!c,d&&l.push(h.CharSet)),(j&h.Scheme)===h.Scheme&&(c=(k&h.Scheme)!==h.Scheme||i.Scheme!==a.Scheme,d=b?c:!c,d&&l.push(h.Scheme)),(j&h.Bold)===h.Bold&&(c=(k&h.Bold)!==h.Bold||i.Bold!==a.Bold,d=b?c:!c,d&&l.push(h.Bold)),(j&h.Italic)===h.Italic&&(c=(k&h.Italic)!==h.Italic||i.Italic!==a.Italic,d=b?c:!c,d&&l.push(h.Italic)),(j&h.Underline)===h.Underline&&(c=(k&h.Underline)!==h.Underline||i.Underline!==a.Underline,d=b?c:!c,d&&l.push(h.Underline)),(j&h.Strike)===h.Strike&&(c=(k&h.Strike)!==h.Strike||i.Strike!==a.Strike,d=b?c:!c,d&&l.push(h.Strike)),(j&h.VertAlign)===h.VertAlign&&(c=(k&h.VertAlign)!==h.VertAlign||i.VertAlign!==a.VertAlign,d=b?c:!c,d&&l.push(h.VertAlign)),(j&h.Shadow)===h.Shadow&&(c=(k&h.Shadow)!==h.Shadow||i.Shadow!==a.Shadow,d=b?c:!c,d&&l.push(~h.Shadow)),(j&h.Outline)===h.Outline&&(c=(k&h.Outline)!==h.Outline||i.Outline!==a.Outline,d=b?c:!c,d&&l.push(h.Outline)),(j&h.Condense)===h.Condense&&(c=(k&h.Condense)!==h.Condense||i.Condense!==a.Condense,d=b?c:!c,d&&l.push(h.Condense)),(j&h.Extend)===h.Extend&&(c=(k&h.Extend)!==h.Extend||i.Extend!==a.Extend,d=b?c:!c,d&&l.push(h.Extend)),e=l.length,e>0)for(m=!0,g=0;g<e;g++)j&=~l[g];return i.Flag=j,m},a.prototype.ClearFlag=function(){this.Color.ClearFlag(),this.Flag=h.None},a.prototype.SetFullFlag=function(){this.Color.SetFullFlag(),this.Flag=h.All},a.prototype.IsFullFlag=function(){return this.Flag===h.All&&this.Color.IsFullFlag()},a}(),b.FontData=i},"./dist/plugins/floatingObject/drawing/fontFormat.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./dist/plugins/floatingObject/drawing/stateful.js"),f=c("./dist/plugins/floatingObject/drawing/common.js"),g=c("./dist/plugins/floatingObject/drawing/fillFormat.js"),h=c("./dist/plugins/floatingObject/drawing/fontData.js"),i=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),j=d.Common.j,k=j.za,l=f.ChartUtility,m=l.umb,n=f.ChartConstants,o=f.UnitHelper,p=o.isNullOrUndefined,q=null,r=void 0,s=function(){function a(a){this.Hpa=a}return Object.defineProperty(a.prototype,"Formula",{get:function(){return this.GetFormula(!1)},set:function(a){a!==this.Formula&&this.UpdateTextByFormulas(a)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"FormulaR1C1",{get:function(){return this.GetFormula(!0)},set:function(a){a!==this.Formula&&this.UpdateTextByFormulas(a,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Parent",{get:function(){return this.Hpa},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ReferingText",{get:function(){return this.GetReferingText()},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Worksheet",{get:function(){return this.Hpa.sheet()},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"TextRefer",{get:function(){return this.Ipa},set:function(a){this.SetTextRefer(a)},enumerable:!0,configurable:!0}),a.prototype.SetTextRefer=function(a,b){b===r&&(b=!0),this.SetTextReferInternal(a,b),this.OnSetTextRefer(a)},a.prototype.OnSetTextRefer=function(a){},a.prototype.GetReferingText=function(){var a,b,c,d="";if(!p(this.Ipa))for(a=this.GetTextList(this.Ipa),b=0,c=a;b<c.length;b++)d+=c[b];return d},a.prototype.GetTextList=function(a){var b,c,d,e,f=[],g=this.Worksheet;for(p(a.WorksheetName)&&(a.WorksheetName=g.name()),b=g.Apa(a.WorksheetName),c=a.Row;c<=a.LastRow;c++)for(d=a.Column;d<=a.LastColumn;d++)e=b.Jpa(c,d).Text,f.push(p(e)?"":e);return f},a.prototype.SetTextReferInternal=function(a,b){this.Ipa=a},a.prototype.GetFormula=function(a){var b,c;return a===r&&(a=!1),(b=this.Ipa)?(c=this.Worksheet.name(),a?b.ToR1C1Text(c):b.ToA1Text(0,0,c)):""},a.prototype.UpdateTextByFormulas=function(a,b){var c,d;b===r&&(b=!1),c=this.Worksheet.Kpa(a,0,0,b),d=c.references,this.Ipa=d?d[0]:q},a}(),b.ReferenceText=s,t=function(a){E(b,a);function b(b,c,d,e){var f,g=a.call(this,b)||this;return c===r&&(c=q),d===r&&(d=q),e===r&&(e=q),f=g,f.Lpa=q,f.Mpa=q,f.Npa=d||"",f.Opa=e,f.InitRichTextFields(c),g}return b.prototype.InitRichTextFields=function(a){var b=this,c=new B(b.Hpa,b.Opa);p(b.Lpa)||b.Lpa.Font.SetParentForChildren(c.Font),b.Lpa=c,b.Lpa.Font.ParentStateful=a,p(b.Npa)||b.Lpa.AddParagraph(b.Npa)},Object.defineProperty(b.prototype,"TextBody",{get:function(){return this.Lpa},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Font",{get:function(){return this.Lpa.Font},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Text",{get:function(){return p(this.TextRefer)?this.Lpa.Text:this.ReferingText},set:function(a){this.SetTextInternal(a)},enumerable:!0,configurable:!0}),b.prototype.SetTextInternal=function(a){this.SetTextReferInternal(q),this.UpdateTextBody(a)},b.prototype.UpdateTextBody=function(a){this.Lpa.Clear();var b=p(a)?this.Npa:a;p(b)||this.Lpa.AddParagraph(b)},b.prototype.OnSetTextRefer=function(b){a.prototype.OnSetTextRefer.call(this,b),this.UpdateTextBody(q)},b.prototype.FromOOModel=function(a){var b=this;b.Mpa=a,b.InitRichTextFields(b.Lpa.Font.ParentStateful),p(a.rich)||b.Lpa.FromOOModel(a.rich),!p(a.strRef)&&a.strRef.f&&b.UpdateTextByFormulas(a.strRef.f)},b.prototype.ToOOModel=function(){var a=this,b=p(a.Mpa)?{}:a.Mpa;return p(a.Ipa)?b.rich=this.Lpa.ToOOModel():(b.strRef={f:a.Formula},b.rich=a.Lpa.ToOOModel()),b},b.prototype.SetParagraphSplitFlag=function(a){var b,c,d=this.Lpa;for(b=0,c=d.Count;b<c;b++)d.Get(b).SplitSpaceFlag=a},b.prototype.UpdateFiledElementTextLink=function(a){var b,c,d,e,f,g,h=this.Lpa;for(b=0,c=h.Count;b<c;b++)for(d=h.Get(b),e=0,f=d.Count;e<f;e++)if(g=d.Get(e),!p(g)&&g.Type===i.TextFieldType.TxLink)return void g.UpdateTextByFormulas(a)},b}(s),b.DrawingText=t,u={Bold:1,Color:2,Italic:4,Name:8,OutlineFont:16,Shadow:32,Size:64,Strikethrough:128,Subscript:256,Superscript:512,ThemeFont:1024,Underline:2048},v=function(a){E(b,a);function b(b,c,d){var e,f=this;return c===r&&(c=q),d===r&&(d=q),f=a.call(this,c)||this,e=f,e.Ora=i.ThemeFont.None,e.Pra=q,e.Qra=q,e.Rra=q,e.xc=b,e.Zpa=new g.FillFormat(b,c&&c.FillInternal),e.hqa=c,d&&(p(d.Size)||(e.Oz=d.Size),p(d.Bold)||(e.Sra=d.Bold)),e}return Object.defineProperty(b.prototype,"Bold",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Bold)?a.Bold:this.Sra},set:function(a){this.Sra=a,this.SetState(u.Bold,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Color",{get:function(){return this.FillInternal.Color},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Italic",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Italic)?a.Italic:this.Tra},set:function(a){this.Tra=a,this.SetState(u.Italic,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Name",{get:function(){var a=this.hqa;return a&&!this.HasOwnFont()?a.Name:this.GetOwnFont()},set:function(a){this.SetFont(a),this.ClearOOFontNames()},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Size",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Size)?a.Size:this.Oz},set:function(a){this.Oz=a,this.SetState(u.Size,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Strikethrough",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Strikethrough)?a.Strikethrough:this.Ura},set:function(a){this.Ura=a,this.SetState(u.Strikethrough,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Subscript",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Subscript)?a.Subscript:this.Vra},set:function(a){this.Vra=a,this.SetState(u.Subscript,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Superscript",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Superscript)?a.Superscript:this.Wra},set:function(a){this.Wra=a,this.SetState(u.Superscript,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Underline",{get:function(){var a=this.hqa;return a&&!this.GetState(u.Underline)?a.Underline:this.Xra},set:function(a){this.Xra=a,this.SetState(u.Underline,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ThemeFont",{get:function(){var a=this.hqa;return a&&!this.GetState(u.ThemeFont)?a.ThemeFont:this.Ora},set:function(a){this.SetThemeFont(a),this.ClearOOFontNames()},enumerable:!0,configurable:!0}),b.prototype.IsDirtyIncludingParent=function(b){return!(!this.Zpa||!this.Zpa.IsDirtyIncludingParent(b))||a.prototype.IsDirtyIncludingParent.call(this,b)},b.prototype.HasOwnFont=function(){return this.IsDirty(u.Name)||this.IsDirty(u.ThemeFont)},b.prototype.OnParentChanged=function(a){this.hqa=a,this.Zpa&&(this.Zpa.ParentStateful=a&&a.FillInternal)},b.prototype.GetOwnFont=function(){return this.Ora===i.ThemeFont.None?this.Cj:this.xc.Yra(this.Ora)},b.prototype.SetFont=function(a){var b=this;a===n.CHART_CS_HEAD_FONT||a===n.CHART_EA_HEAD_FONT||a===n.CHART_LATIN_HEAD_FONT?b.SetThemeFont(i.ThemeFont.Major):a===n.CHART_CS_BODY_FONT||a===n.CHART_EA_BODY_FONT||a===n.CHART_LATIN_BODY_FONT?b.SetThemeFont(i.ThemeFont.Minor):(b.Cj=a,b.SetState(u.Name,!0),b.Ora=i.ThemeFont.None,b.SetState(u.ThemeFont,!1))},b.prototype.SetThemeFont=function(a){var b=this;b.Ora=a,b.SetState(u.ThemeFont,!0),b.Cj=q,b.SetState(u.Name,!1)},b.prototype.ClearOOFontNames=function(){this.Qra=q,this.Rra=q,this.Pra=q},b.prototype.GetActualFormat=function(){var a,c,d=this,e=new b(d.xc);for(a in u)if(c=u[a],this.IsDirty(c))switch(c){case u.Bold:e.Bold=this.Bold;break;case u.Italic:e.Italic=this.Italic;break;case u.Name:e.Name=this.Name;break;case u.Size:e.Size=this.Size;break;case u.Strikethrough:e.Strikethrough=this.Strikethrough;break;case u.Subscript:e.Superscript=this.Superscript;break;case u.Superscript:e.Subscript=this.Subscript;break;case u.ThemeFont:e.ThemeFont=this.ThemeFont;break;case u.Underline:e.Underline=this.Underline}return e},Object.defineProperty(b.prototype,"FillInternal",{get:function(){var a=this;return a.Zpa||(a.Zpa=new g.FillFormat(a.xc,a.hqa&&a.hqa.FillInternal)),a.Zpa},enumerable:!0,configurable:!0}),b.prototype.TextCharacterProperties_FromOOModel=function(a){var b,c=this;c.SuspendClearChildrenState(),c.Zra=a,p(a.latin)||(c.Qra=a.latin.typeface),p(a.ea)||(c.Pra=a.ea.typeface),p(a.cs)||(c.Rra=a.cs.typeface),c.Qra?c.SetFont(c.Qra):c.Pra?c.SetFont(c.Pra):c.Rra&&c.SetFont(c.Rra),p(a.sz)||(b=a.sz,c.Oz!==b&&(c.Size=b)),p(a.b)||(c.Bold=a.b),p(a.i)||(c.Italic=a.i),p(a.strike)||(c.Strikethrough=a.strike===i.ST_TextStrikeType.sngStrike||a.strike===i.ST_TextStrikeType.dblStrike),p(a.u)||(c.Underline=a.u),p(a.baseline)||0===a.baseline?c.Subscript=!1:c.Subscript=!0,p(a.solidFill)?p(a.noFill)?p(a.blipFill)?p(a.gradFill)?p(a.pattFill)?c.SetFillField(q):c.FillInternal.FromOOModel(a.pattFill,i.ColorFillType.PatternFillProperties):c.FillInternal.FromOOModel(a.gradFill,i.ColorFillType.GradientFillProperties):c.FillInternal.FromOOModel(a.blipFill,i.ColorFillType.BlipFillProperties):c.FillInternal.Color.ColorType=i.SolidColorType.None:c.FillInternal.FromOOModel(a.solidFill,i.ColorFillType.SolidColorFillProperties),c.ResumeClearChilrenState()},b.prototype.AdjustStateful=function(a,b){p(a)||(a.SetParentForChildren(b),p(b)||(b.ParentStateful=a.ParentStateful),a.ParentStateful=q)},b.prototype.SetFillField=function(a){this.AdjustStateful(this.Zpa,a),this.Zpa=a},b.prototype.TextCharacterProperties_ToOOModel=function(){var a,b,c,d,e=this;return e.IsDirtyIncludingParent(!0)?(a=e.Zra||{},e.IsPropDirtyIncludingParent(u.Name,!0)?(b=e.Name,p(a.latin)&&(a.latin={}),a.latin.typeface=b):e.IsPropDirtyIncludingParent(u.ThemeFont,!0)&&e.ThemeFont!==i.ThemeFont.None?(p(a.latin)&&(a.latin={}),a.latin.typeface=e.ThemeFont===i.ThemeFont.Major?n.CHART_LATIN_HEAD_FONT:n.CHART_LATIN_BODY_FONT):(e.Qra||e.Pra||e.Rra)&&(e.Qra&&(p(a.latin)&&(a.latin={}),a.latin.typeface=e.Qra),e.Pra&&(p(a.ea)&&(a.ea={}),a.ea.typeface=e.Pra),e.Rra&&(p(a.cs)&&(a.cs={}),a.cs.typeface=e.Rra)),e.IsPropDirtyIncludingParent(u.Size,!0)&&e.Size>1?a.sz=e.Size:delete a.sz,e.IsPropDirtyIncludingParent(u.Bold,!0)&&(a.b=e.Bold),e.IsPropDirtyIncludingParent(u.Italic,!0)&&(a.i=e.Italic),e.IsPropDirtyIncludingParent(u.Strikethrough,!0)&&(a.strike=e.Strikethrough?i.ST_TextStrikeType.sngStrike:i.ST_TextStrikeType.noStrike),e.IsPropDirtyIncludingParent(u.Underline,!0)&&(a.u=e.Underline),e.Subscript&&p(a.baseline)?a.baseline=-25e3:e.Superscript&&p(a.baseline)?a.baseline=3e4:e.Subscript||e.Superscript||delete a.baseline,m(a),!p(e.Zpa)&&e.Zpa.IsDirtyIncludingParent(!0)&&(c=e.Zpa.ToOOModel(),d=c.colorFillType,d===i.ColorFillType.SolidColorFillProperties?a.solidFill=c:d===i.ColorFillType.PatternFillProperties?a.pattFill=c:d===i.ColorFillType.GradientFillProperties?a.gradFill=c:d===i.ColorFillType.BlipFillProperties?a.blipFill=c:d===i.ColorFillType.NoFillProperties&&(a.noFill=!0),delete c.colorFillType),a):{}},b.prototype.FromOOModel=function(a){var b=this;b.Bold=a.Bold,b.Italic=a.Italic,b.Name=a.Name,a.Scheme!==i.ThemeFont.None&&(b.ThemeFont=a.Scheme),b.Size=a.Size,b.Underline=l.ConvertToTextUnderlineType(a.Underline),b.Strikethrough=a.Strike,b.Subscript=(a.VertAlign&i.VertAlignType.Subscript)===i.VertAlignType.Subscript,b.Superscript=(a.VertAlign&i.VertAlignType.Superscript)===i.VertAlignType.Superscript,b.FillInternal.ColorInternal.ColorData_FromOOModel(a.Color)},b.prototype.ToOOModel=function(){var a,b=this,c=new h.FontData;return c.Bold=b.Bold,c.Italic=b.Italic,c.Name=b.Name,c.Scheme=b.ThemeFont,c.Size=b.Size,c.Underline=l.ToUnderlineType(b.Underline),c.Strike=b.Strikethrough,b.Subscript?c.VertAlign=i.VertAlignType.Subscript:b.Superscript&&(c.VertAlign=i.VertAlignType.Superscript),a=b.Zpa,a&&a.ColorInternal.IsDirtyIncludingParent(!0)&&a.ColorInternal.ColorType!==i.SolidColorType.None&&(c.Color=a.ColorInternal.ColorData_ToOOModel()),c},b}(e.StatefullBase),b.FontFormat=v,w=function(){function a(a,b){var c=this;c.xc=a,c.$ra=b,c._font=new v(a),c._font.ParentStateful=b.Font}return Object.defineProperty(a.prototype,"Font",{get:function(){return this._font},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Text",{get:function(){return""},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsCustomText",{get:function(){return!1},enumerable:!0,configurable:!0}),a.prototype.Delete=function(){this.$ra.DeleteElement(this),this.$ra=q},a.prototype.FromOOModel=function(a){},a.prototype.ToOOModel=function(){},a}(),b.DrawingTextElement=w,
x=function(a){E(b,a);function b(b,c,d){var e=a.call(this,b,d)||this,f=e;return f._ra=q,f._text=c,f.xc=b,f.elementType=i.TextParagraphElementType.RegularTextRun,e}return Object.defineProperty(b.prototype,"Text",{get:function(){return this._text},set:function(a){this._text=a},enumerable:!0,configurable:!0}),b.prototype.FromOOModel=function(a){var b=this;b._ra=a,b._text=a.t,p(a.rPr)?b._font=new v(b.xc,b._font.ParentStateful):b._font.TextCharacterProperties_FromOOModel(a.rPr)},b.prototype.ToOOModel=function(){var a=this._ra||{elementType:i.TextParagraphElementType.RegularTextRun};return a.t=this.Text,a.rPr=this._font.TextCharacterProperties_ToOOModel(),a},b}(w),b.DrawingTextRun=x,y=function(a){E(b,a);function b(b,c,d){var e=a.call(this,b,d)||this,f=e;return f.Nc=c,f.Hpa=b,f.asa=new s(f.Hpa),f.elementType=i.TextParagraphElementType.TextField,e}return Object.defineProperty(b.prototype,"Type",{get:function(){return this.Nc},set:function(a){this.Nc=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Text",{get:function(){return this.Type===i.TextFieldType.TxLink?this.asa.ReferingText:l.GetTextFieldTypeText(this.Type)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Worksheet",{get:function(){return this.Hpa&&this.Hpa.Parent},enumerable:!0,configurable:!0}),b.prototype.FromOOModel=function(a){var b=this;b.Lqa=a,b.Type=l.GetTextFieldType(a.type),p(a.rPr)?b._font=new v(b.xc,b._font.ParentStateful):b._font.TextCharacterProperties_FromOOModel(a.rPr)},b.prototype.ToOOModel=function(){var a=this,c=a.Lqa||{elementType:i.TextParagraphElementType.TextField};return p(c.id)&&(c.id="{"+b.guid+"}",b.guid+=1),c.type=l.GetTextFieldTypeString(a.Type),p(c.rPr)&&(c.rPr={}),c.rPr=a._font.TextCharacterProperties_ToOOModel(),c.t=a.Text,c},b.prototype.UpdateTextByFormulas=function(a,b){this.asa.UpdateTextByFormulas(a,b)},b.guid=1,b}(w),b.DrawingTextField=y,z=function(a){E(b,a);function b(b,c){var d=a.call(this,b,c)||this;return d.elementType=i.TextParagraphElementType.TextLineBreak,d}return Object.defineProperty(b.prototype,"Text",{get:function(){return"\r\n"},enumerable:!0,configurable:!0}),b.prototype.FromOOModel=function(a){this.Lqa=a},b.prototype.ToOOModel=function(){return this.Lqa||{elementType:i.TextParagraphElementType.TextLineBreak}},b}(w),b.DrawingLineBreak=z,A=function(){function a(a,b){var c=this;c.bsa=q,c.csa=" ",c.xc=a,c.Lpa=b,c._font=new v(a),c._font.ParentStateful=c.Lpa.Font,c.rM=[]}return Object.defineProperty(a.prototype,"SplitSpaceFlag",{get:function(){return this.csa},set:function(a){this.csa=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Text",{get:function(){var a,b,c,d="";for(a=0,b=this.rM;a<b.length;a++)c=b[a],p(c.Text)||(d+=c.Text);return d},set:function(a){this.Clear(),this.AddRun(a)},enumerable:!0,configurable:!0}),a.prototype.Get=function(a){return this.rM[a]},Object.defineProperty(a.prototype,"Count",{get:function(){return this.rM.length},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Font",{get:function(){return this._font},enumerable:!0,configurable:!0}),a.prototype.AddRun=function(a,b){return this.AddRunInternal(a,b)},a.prototype.AddField=function(a){return this.AddFieldInternal(a)},a.prototype.Clear=function(){var a,b,c;for(a=0,b=this.rM;a<b.length;a++)c=b[a],c.Font.ParentStateful=q;this.rM.length=0},a.prototype.FromOOModel=function(a){var b,c,d,e,f,g,h,j=this;if(j.bsa=a,a.pPr&&a.pPr.defRPr&&j._font.TextCharacterProperties_FromOOModel(a.pPr.defRPr),a.elements)for(b=a.elements.length,c=j.rM=[],d=j.xc,e=0;e<b;e++)f=a.elements[e],g=f.elementType,h=void 0,g===i.TextParagraphElementType.RegularTextRun?h=new x(d,f.t,j):g===i.TextParagraphElementType.TextLineBreak?h=new z(d,j):g===i.TextParagraphElementType.TextField&&(h=new y(d,l.GetTextFieldType(f.type),j)),h&&(h.FromOOModel(f),c.push(h))},a.prototype.AddRunInternal=function(a,b){var c,d,e=this;return b===r&&(b=-1),c=new x(e.xc,a,e),d=this.rM,b<0||b>=d.length?d.push(c):d.splice(b,0,c),c},a.prototype.AddFieldInternal=function(a){var b=new y(this.xc,a,this);return this.rM.push(b),b},a.prototype.ToOOModel=function(){var a,b,c,d,e,f=this,g=f.bsa||{elements:[]};for(g.pPr||(g.pPr={}),a=g.pPr,p(f._font)||(a.defRPr=f._font.TextCharacterProperties_ToOOModel()),b=g.elements=[],c=0,d=f.rM,e=d.length;c<e;c++)b.push(d[c].ToOOModel());return(f.xc.DrawingType===i.DrawingType.Chart||p(f.bsa))&&p(g.endParaRPr)&&(g.endParaRPr={}),g},a.prototype.Delete=function(){var a,b=this;for(a=b.rM.length-1;a>=0;a--)b.DeleteElement(b.rM[a]);b.Lpa.DeleteParagraph(b),b.Lpa=q},a.prototype.DeleteElement=function(a){a.Font.ParentStateful=q;var b=this.rM.indexOf(a);b>=0&&this.rM.splice(b,1)},a}(),b.DrawingTextParagraph=A,B=function(){function a(a,b){this.dsa=[],this.esa=q,this.xc=a,this.Opa=b,this._font=new v(a,q,b),this.kO=void 0}return Object.defineProperty(a.prototype,"Text",{get:function(){var a,b,c,d="";for(a=0,b=this.dsa;a<b.length;a++)c=b[a],""!==d&&(d+="\r\n"),d+=c.Text;return d},set:function(a){this.Clear(),this.AddParagraph(a)},enumerable:!0,configurable:!0}),a.prototype.Get=function(a){return this.dsa[a]},Object.defineProperty(a.prototype,"Count",{get:function(){return this.dsa.length},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Font",{get:function(){return this._font},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Rotation",{get:function(){return this.kO},set:function(a){this.kO=a},enumerable:!0,configurable:!0}),a.prototype.setFontColor=function(a,b){var c,d=this,e=d.dsa.length;for(d._font&&d._font.Color.setColor(a,b),c=0;c<e;c++)d.dsa[c].Font.Color.setColor(a,b)},a.prototype.setFontTransparency=function(a){var b,c=this,d=c.dsa.length;for(c._font&&(c._font.Color.Transparency=a),b=0;b<d;b++)c.dsa[b].Font.Color.Transparency=a},a.prototype.updateFont=function(a,b,c,d){var e=this,f=[e._font];e.dsa.forEach(function(a){f.push(a.Font)}),f.forEach(function(e){e&&(e.Name=a,e.Size=b,e.Bold=c,e.Italic=d)})},a.prototype.AddParagraph=function(a,b){var c,d;return b===r&&(b=-1),c=new A(this.xc,this),p(a)||c.AddRun(a),d=this.dsa,b<0||b>=d.length?d.push(c):d.splice(b,0,c),c},a.prototype.Clear=function(){var a,b,c;for(a=0,b=this.dsa;a<b.length;a++)c=b[a],c.Font.ParentStateful=q;this.dsa.length=0},a.prototype.ToOOModel=function(){var a,b,c,d,e,f,g=this,h=g.esa||{p:[]},i=this.dsa,j=i.length;if(0===j&&!this._font.IsDirtyIncludingParent())return q;if(a=h.p=[],j>0)for(b=0;b<j;b++)c=i[b],a.push(c.ToOOModel());else d=new A(g.xc,g),e=d.ToOOModel(),e.pPr.defRPr=g._font.TextCharacterProperties_ToOOModel(),a.push(e);return p(h.bodyPr)&&(h.bodyPr={}),f=this.Rotation,p(f)||(h.bodyPr.rot=f),p(h.lstStyle)&&(h.lstStyle={}),h},a.prototype.FromOOModel=function(a){var b,c,d,e,f,g,h,i,j,l,m,n,o,r,s=this;for(s.esa=a,b=0,c=s.dsa;b<c.length;b++)d=c[b],d.Clear();if(s.dsa.length=0,e=new v(s.xc,q,s.Opa),f=s._font,f&&(f.SetParentForChildren(e),e.ParentStateful=f.ParentStateful,f.ParentStateful=q),s._font=e,a.p&&a.p.length)for(g=a.p[0].pPr,h=a.p[0].endParaRPr,i=void 0,g&&g.defRPr&&!k(g.defRPr)?i=g.defRPr:h&&!k(h)&&(i=h),i&&s._font.TextCharacterProperties_FromOOModel(i),j=0,l=a.p;j<l.length;j++)m=l[j],n=new A(s.xc,s),n.FromOOModel(m),s.dsa.push(n);o=a.bodyPr,o&&(r=o.rot,p(r)||(this.kO=r))},a.prototype.Delete=function(){for(var a=this.dsa,b=a.length-1;b>=0;b--)this.DeleteParagraph(a[b])},a.prototype.DeleteParagraph=function(a){a.Font.ParentStateful=q;var b=this.dsa.indexOf(a);b>=0&&this.dsa.splice(b,1)},a}(),b.DrawingTextBody=B,C=function(a){E(b,a);function b(b,c){var d=a.call(this)||this,e=d;return e.Format1=b,e.Format2=c,d}return Object.defineProperty(b.prototype,"Stateful1",{get:function(){return this.Format1},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Stateful2",{get:function(){return this.Format2},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Brightness",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(i.ColorFormatStates.Brightness,!1)?a.Format2.Brightness:a.Format1.Brightness},set:function(a){this.Format1.Brightness=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ColorType",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(i.ColorFormatStates.ColorType,!1)?a.Format2.ColorType:a.Format1.ColorType},set:function(a){this.Format1.ColorType=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ObjectThemeColor",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(i.ColorFormatStates.ObjectThemeColor,!1)?a.Format2.ObjectThemeColor:a.Format1.ObjectThemeColor},set:function(a){this.Format1.ObjectThemeColor=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RGB",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(i.ColorFormatStates.RGB,!1)?a.Format2.RGB:a.Format1.RGB},set:function(a){this.Format1.RGB=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TintAndShade",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(i.ColorFormatStates.TintAndShade,!1)?a.Format2.TintAndShade:a.Format1.TintAndShade},set:function(a){this.Format1.TintAndShade=a},enumerable:!0,configurable:!0}),b.prototype.GetState=function(){},b.prototype.SetState=function(){},b.prototype.IsDirtyIncludingParent=function(){},b.prototype.IsPropDirtyIncludingParent=function(a,b){return this.Stateful1.IsPropDirtyIncludingParent(a,b)||this.Stateful2.IsPropDirtyIncludingParent(a,b)},b.prototype.Dirty=function(){},b.prototype.UnDirty=function(){},b.prototype.SuspendClearChildrenState=function(){},b.prototype.ResumeClearChilrenState=function(){},b.prototype.AddChildInternal=function(){},b.prototype.RemoveChildInternal=function(){},b}(e.StatefullBase),b.ComboColorFormat=C,D=function(a){E(b,a);function b(b,c){var d=a.call(this)||this,e=d;return e.Format1=b,e.Format2=c,d}return Object.defineProperty(b.prototype,"Stateful1",{get:function(){return this.Format1},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Stateful2",{get:function(){return this.Format2},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Bold",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Bold,!1)?a.Format2.Bold:a.Format1.Bold},set:function(a){this.Format1.Bold=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Color",{get:function(){return new C(this.Format1.Color,this.Format2.Color)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Italic",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Italic,!1)?a.Format2.Bold:a.Format1.Bold},set:function(a){this.Format1.Bold=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Name",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Name,!1)?a.Format2.Name:a.Format1.Name},set:function(a){this.Format1.Name=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Size",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Size,!1)?a.Format2.Size:a.Format1.Size},set:function(a){this.Format1.Size=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Strikethrough",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Strikethrough,!1)?a.Format2.Strikethrough:a.Format1.Strikethrough},set:function(a){this.Format1.Strikethrough=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Subscript",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Subscript,!1)?a.Format2.Subscript:a.Format1.Subscript},set:function(a){this.Format1.Subscript=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Superscript",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Superscript,!1)?a.Format2.Superscript:a.Format1.Superscript},set:function(a){this.Format1.Superscript=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ThemeFont",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.ThemeFont,!1)?a.Format2.ThemeFont:a.Format1.ThemeFont},set:function(a){this.Format1.ThemeFont=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Underline",{get:function(){var a=this;return!p(a.Format2)&&a.Stateful2.IsPropDirtyIncludingParent(u.Underline,!1)?a.Format2.Underline:a.Format1.Underline},set:function(a){this.Format1.Underline=a},enumerable:!0,configurable:!0}),b.prototype.GetState=function(){},b.prototype.SetState=function(){},b.prototype.IsDirtyIncludingParent=function(){},b.prototype.IsPropDirtyIncludingParent=function(a,b){return this.Stateful1.IsPropDirtyIncludingParent(a,b)||this.Stateful2.IsPropDirtyIncludingParent(a,b)},b.prototype.Dirty=function(){},b.prototype.UnDirty=function(){},b.prototype.SuspendClearChildrenState=function(){},b.prototype.ResumeClearChilrenState=function(){},b.prototype.AddChildInternal=function(){},b.prototype.RemoveChildInternal=function(){},b}(e.StatefullBase),b.ComboFontFormat=D},"./dist/plugins/floatingObject/drawing/gradient.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/common.js"),e=c("./dist/plugins/floatingObject/drawing/color.js"),f=c("./dist/plugins/floatingObject/drawing/colorFormat.js"),g=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),h=d.ShapeConstants.PositionConver,i=d.ShapeUtility,j=void 0,k=function(){function a(a){this.nzb=a,this.tE=new f.ColorFormat(this.nzb)}return Object.defineProperty(a.prototype,"Color",{get:function(){return this.tE},set:function(a){this.tE=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Position",{get:function(){return this.SH},set:function(a){this.SH=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Transparency",{get:function(){return this.tE.Transparency},set:function(a){this.tE.Transparency=a},enumerable:!0,configurable:!0}),a.prototype.FromOOModel=function(a){this.Position=a.pos/h,this.tE.FromOOModel(a)},a.prototype.ToOOModel=function(){var a=this.tE.ToOOModel();return a.pos=Math.round(this.Position*h),a},a}(),b.GradientStop=k,l=function(){function a(a){this.Nqa=a,this.Oqa=[]}return a.prototype.Item=function(a){return this.Oqa[a]},Object.defineProperty(a.prototype,"Count",{get:function(){return this.Oqa.length},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"GradientStopList",{get:function(){return this.Oqa},enumerable:!0,configurable:!0}),a.prototype.Delete=function(a){a===-1&&(a=this.Count-1),this.Oqa.splice(a,1)},a.prototype.Insert=function(a,b,c,d,f){c===j&&(c=0),d===j&&(d=-1),f===j&&(f=0);var g=new k(this.Nqa);g.Color.RGB=e.ARGBColor.FromArgb(a),g.Position=b,g.Transparency=c,g.Color.Brightness=f,d<0?this.Oqa.push(g):this.Oqa.splice(d,0,g)},a.prototype.FromOOModel=function(a){var b,c,d,e;for(b=0,c=a.gs;b<c.length;b++)d=c[b],e=new k(this.Nqa),e.FromOOModel(d),this.GradientStopList.push(e)},a.prototype.ToOOModel=function(){var a,b,c,d={gs:[]};for(a=0,b=this.Oqa;a<b.length;a++)c=b[a],d.gs.push(c.ToOOModel());return d},a}(),b.GradientStops=l;function m(a,b,c){var d=new k(a);return d.Position=b,d.Color=c,d}function n(a){return e.ARGBColor.FromArgb(a)}i.InitGradientStopPresetColor=function(a,b){var c,d,e,h,i,j,k,l,o,p,q,r,s,t=b.GradientStops.GradientStopList;switch(b.PresetGradientType){case g.PresetGradientType.GradientEarlySunset:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(130))),d=m(a,.3,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(6684815))),e=m(a,.64999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12189798))),h=m(a,.89999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16711680))),i=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16744960))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i);break;case g.PresetGradientType.GradientLateSunset:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(0))),d=m(a,.2,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(64))),e=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(4194368))),h=m(a,.75,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(9371712))),i=m(a,.89999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15889152))),j=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16760576))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j);break;case g.PresetGradientType.GradientNightfall:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(0))),d=m(a,.39999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(660108))),e=m(a,.7,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(1580231))),h=m(a,.88,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(7341524))),i=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(9190801))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i);break;case g.PresetGradientType.GradientDaybreak:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(6201087))),d=m(a,.39999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8766207))),e=m(a,.7,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12900075))),h=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16772090))),t.push(c),t.push(d),t.push(e),t.push(h);break;case g.PresetGradientType.GradientHorizon:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(14478325))),d=m(a,.08,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8628163))),e=m(a,.13,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(7770041))),h=m(a,.21001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8628163))),i=m(a,.52,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))),j=m(a,.56,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10249571))),k=m(a,.58,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8400941))),l=m(a,.71001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12603982))),o=m(a,.94,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15456980))),p=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(5580316))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l),t.push(o),t.push(p);break;case g.PresetGradientType.GradientDesert:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16555979))),d=m(a,.87,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16298057))),e=m(a,.78999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16298057))),h=m(a,.37,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16705522))),i=m(a,.33,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16339616))),j=m(a,.31,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12912713))),k=m(a,.17999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(11812485))),l=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16298057))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l);break;case g.PresetGradientType.GradientOcean:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(251048))),d=m(a,.25,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(2217696))),e=m(a,.75,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(34790))),h=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(23743))),t.push(c),t.push(d),t.push(e),t.push(h);break;case g.PresetGradientType.GradientCalmWater:c=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13421823))),d=m(a,.41001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10079487))),e=m(a,.32,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10053375))),h=m(a,.195,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13408767))),i=m(a,.8999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10079487))),j=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13421823))),k=m(a,.59,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10079487))),l=m(a,.68,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10053375))),o=m(a,.805,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13408767))),p=m(a,.91001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10079487))),q=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13421823))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l),t.push(o),t.push(p),t.push(q);break;case g.PresetGradientType.GradientFire:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16773632))),d=m(a,.45,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16742912))),e=m(a,.7,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16712448))),h=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(5048328))),t.push(c),t.push(d),t.push(e),t.push(h);break;case g.PresetGradientType.GradientFog:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8685764))),d=m(a,.53,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13950719))),e=m(a,.83,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13950719))),h=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(9874324))),t.push(c),t.push(d),t.push(e),t.push(h);break;case g.PresetGradientType.GradientMoss:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(14543823))),d=m(a,.25,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10270830))),e=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(1403667))),h=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(14543823))),i=m(a,.75,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10270830))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i);break;case g.PresetGradientType.GradientPeacock:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(3381759))),d=m(a,.16,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(52428))),e=m(a,.47,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10066431))),h=m(a,.60001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(3041170))),i=m(a,.71001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(3355596))),j=m(a,.81,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(1143039))),k=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(26265))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k);break;case g.PresetGradientType.GradientWheat:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16509639))),d=m(a,.17999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16705522))),e=m(a,.36,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16435069))),h=m(a,.61,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16492925))),i=m(a,.82001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16503964))),j=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16705522))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j);break;case g.PresetGradientType.GradientParchment:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16773073))),d=m(a,.64999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15789013))),e=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13747103))),t.push(c),t.push(d),t.push(e);break;case g.PresetGradientType.GradientMahogany:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(14070172))),d=m(a,.15,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13934188))),e=m(a,.35,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10900776))),h=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(6696978))),i=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(14070172))),j=m(a,.85,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13934188))),k=m(a,.65,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10900776))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k);break;case g.PresetGradientType.GradientRainbow:c=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10879915))),d=m(a,.395,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(530939))),e=m(a,.32499,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(1740104))),h=m(a,.24,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16776960))),i=m(a,.135,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15613719))),j=m(a,.6,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15210342))),k=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10879915))),l=m(a,.60501,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(530939))),o=m(a,.67501,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(1740104))),p=m(a,.76,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16776960))),q=m(a,.865,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15613719))),r=m(a,.94,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15210342))),s=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10879915))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l),t.push(o),t.push(p),t.push(q),t.push(r),t.push(s);break;case g.PresetGradientType.GradientRainbowII:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16724889))),d=m(a,.25,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16737843))),e=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16776960))),h=m(a,.75,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(108431))),i=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(3368703))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i);break;case g.PresetGradientType.GradientGold:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15129772))),d=m(a,.12,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15128458))),e=m(a,.3,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13085772))),h=m(a,.45,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15128458))),i=m(a,.77,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13085772))),j=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15129772))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j);break;case g.PresetGradientType.GradientGoldII:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16508078))),d=m(a,.13,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12423722))),e=m(a,.21001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12423722))),h=m(a,.63,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16508078))),i=m(a,.67,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(12423722))),j=m(a,.69,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8609303))),k=m(a,.82001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(10651977))),l=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16442295))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l);break;case g.PresetGradientType.GradientBrass:c=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8541696))),d=m(a,.87,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16754688))),e=m(a,.72,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8541696))),h=m(a,.57001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16754688))),i=m(a,.42,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8541696))),j=m(a,.28,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16754688))),k=m(a,.13,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8541696))),l=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16754688))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l);break;case g.PresetGradientType.GradientChrome:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))),d=m(a,.16,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(2039583))),e=m(a,.17999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))),h=m(a,.42,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(6513507))),i=m(a,.53,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13619151))),j=m(a,.66,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13619151))),k=m(a,.75999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(2039583))),l=m(a,.78999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))),o=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8355711))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l),t.push(o);break;case g.PresetGradientType.GradientChromeII:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(13355979))),d=m(a,.13,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(6250335))),e=m(a,.21001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(6250335))),h=m(a,.63,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))),i=m(a,.67,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(11711154))),j=m(a,.69,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(2697513))),k=m(a,.82001,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(7829367))),l=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15395562))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l);break;case g.PresetGradientType.GradientSilver:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))),d=m(a,.03501,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15132390))),e=m(a,.16,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8225942))),h=m(a,.235,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15132390))),i=m(a,.42501,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8225942))),j=m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15132390))),k=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15132390))),l=m(a,.965,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15132390))),o=m(a,.84,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8225942))),p=m(a,.765,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(15132390))),q=m(a,.575,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(8225942))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l),t.push(o),t.push(p),t.push(q);break;case g.PresetGradientType.GradientSapphire:c=m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(130))),d=m(a,.13,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(18431))),e=m(a,.28,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(130))),h=m(a,.42999,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(18431))),i=m(a,.58,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(130))),j=m(a,.72,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(18431))),k=m(a,.87,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(130))),l=m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(18431))),t.push(c),t.push(d),t.push(e),t.push(h),t.push(i),t.push(j),t.push(k),t.push(l)}},i.InitGradientStopTwoColor=function(a,b){var c,d,e=b.GradientStyle;return e===g.GradientStyle.GradientFromCenter?void this.InitGradientTwoColorCenterStops(a,b):e===g.GradientStyle.GradientFromCorner?void this.InitGradientTwoColorCornerStops(a,b):(c=b.GradientStops.GradientStopList,d=b.GradientVariant,void(1===d||2===d?(c.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))))):3===d?(c.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))),c.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1)))):4===d&&(c.push(m(a,.5,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))),c.push(m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))))))},i.InitGradientTwoColorCornerStops=function(a,b){var c=b.GradientStops.GradientStopList;c.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))))},i.InitGradientTwoColorCenterStops=function(a,b){var c=b.GradientStops.GradientStopList,d=b.GradientVariant;1===d?(c.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215))))):2===d?(c.push(m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))),
c.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1)))):3===d?(c.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,.5,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))),c.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1)))):4===d&&(c.push(m(a,0,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))),c.push(m(a,.5,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),c.push(m(a,1,f.createColorFormatFromRGB(a,g.SolidColorType.RGB,n(16777215)))))},i.InitGradientOneColorCornerStops=function(a,b){var c=f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1,this.GetTintAndShadeByDegree(b.GradientDegree)),d=b.GradientStops.GradientStopList;d.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,1,c))},i.InitGradientOneColorCenterStops=function(a,b){var c=f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1,this.GetTintAndShadeByDegree(b.GradientDegree)),d=b.GradientStops.GradientStopList,e=b.GradientVariant;1===e?(d.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,1,c))):2===e?(d.push(m(a,0,c)),d.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1)))):3===e?(d.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,.5,c)),d.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1)))):4===e&&(d.push(m(a,0,c)),d.push(m(a,.5,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,1,c)))},i.InitGradientStopOneColor=function(a,b){var c,d,e,h=b.GradientStyle;return h===g.GradientStyle.GradientFromCenter?void this.InitGradientOneColorCenterStops(a,b):h===g.GradientStyle.GradientFromCorner?void this.InitGradientOneColorCornerStops(a,b):(c=this.GetTintAndShadeByDegree(b.GradientDegree),d=b.GradientStops.GradientStopList,e=b.GradientVariant,void(1===e||2===e?(d.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1,c)))):3===e?(d.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,.5,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1,c))),d.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1)))):4===e&&(d.push(m(a,.5,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,0,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1))),d.push(m(a,1,f.createColorFormat(a,g.SolidColorType.Theme,g.ColorSchemeIndex.Accent1,c))))))},i.InitGradientStops=function(a,b,c){var d=this;if(b.Fpa(new l(a)),c===g.GradientColorType.GradientOneColor)d.InitGradientStopOneColor(a,b);else if(c===g.GradientColorType.GradientTwoColors)d.InitGradientStopTwoColor(a,b);else{if(c!==g.GradientColorType.GradientPresetColors)throw Error();d.InitGradientStopPresetColor(a,b)}b.GradientAngle=d.GetGradientAngle(b.GradientStyle,b.GradientVariant)}},"./dist/plugins/floatingObject/drawing/lineFormat.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/stateful.js"),e=c("./dist/plugins/floatingObject/drawing/common.js"),f=c("./dist/plugins/floatingObject/drawing/fillFormat.js"),g=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),h=e.ChartUtility.umb,i=e.UnitHelper.isNullOrUndefined,j=null;function m(a,b,c){return a>=b&&a<=c}k=function(a){l(b,a);function b(b,c,d){var e=a.call(this,c)||this,h=e;return h.Pqa=g.ArrowheadLength.medium,h.Qqa=g.ArrowheadStyle.none,h.Rqa=g.ArrowheadWidth.medium,h.Sqa=g.LineDashStyle.solid,h.Tqa=g.ArrowheadLength.medium,h.Uqa=g.ArrowheadStyle.none,h.Vqa=g.ArrowheadWidth.medium,h.Wqa=g.LineStyle.LineSingle,h.bz=!0,h.Xqa=-1,h.Yqa=j,h.xc=b,h.hqa=c,h.iqa=d,h.vmb=g.LineCapStyle.flat,h.wmb=g.LineJoinStyle.round,h.Zpa=new f.FillFormat(b,c&&c.FillInternal,d),e}return Object.defineProperty(b.prototype,"PatternColor",{get:function(){return this.FillInternal.PatternColorInternal},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Color",{get:function(){return this.FillInternal.ColorInternal},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"BeginArrowheadLength",{get:function(){return this.IsDirty(g.LineFormatStates.BeginArrowheadLength)||i(this.hqa)?this.Pqa:this.hqa.BeginArrowheadLength},set:function(a){this.Pqa=a,this.Dirty(g.LineFormatStates.BeginArrowheadLength)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"BeginArrowheadStyle",{get:function(){return this.IsDirty(g.LineFormatStates.BeginArrowheadStyle)||i(this.hqa)?this.Qqa:this.hqa.BeginArrowheadStyle},set:function(a){this.Qqa=a,this.Dirty(g.LineFormatStates.BeginArrowheadStyle)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"BeginArrowheadWidth",{get:function(){return this.IsDirty(g.LineFormatStates.BeginArrowheadWidth)||i(this.hqa)?this.Rqa:this.hqa.BeginArrowheadWidth},set:function(a){this.Rqa=a,this.Dirty(g.LineFormatStates.BeginArrowheadWidth)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"DashStyle",{get:function(){return this.IsDirty(g.LineFormatStates.DashStyle)||i(this.hqa)?this.Sqa:this.hqa.DashStyle},set:function(a){m(a,0,11)&&(this.Sqa=a,this.Dirty(g.LineFormatStates.DashStyle))},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"CapStyle",{get:function(){return this.IsDirty(g.LineFormatStates.CapStyle)||i(this.hqa)?this.vmb:this.hqa.CapStyle},set:function(a){m(a,0,2)&&(this.vmb=a,this.Dirty(g.LineFormatStates.CapStyle))},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"JoinStyle",{get:function(){return this.IsDirty(g.LineFormatStates.JoinStyle)||i(this.hqa)?this.wmb:this.hqa.JoinStyle},set:function(a){m(a,0,2)&&(this.wmb=a,this.Dirty(g.LineFormatStates.JoinStyle))},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"EndArrowheadLength",{get:function(){return this.IsDirty(g.LineFormatStates.EndArrowheadLength)||i(this.hqa)?this.Tqa:this.hqa.EndArrowheadLength},set:function(a){this.Tqa=a,this.Dirty(g.LineFormatStates.EndArrowheadLength)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"EndArrowheadStyle",{get:function(){return this.IsDirty(g.LineFormatStates.EndArrowheadStyle)||i(this.hqa)?this.Uqa:this.hqa.EndArrowheadStyle},set:function(a){this.Uqa=a,this.Dirty(g.LineFormatStates.EndArrowheadStyle)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"EndArrowheadWidth",{get:function(){return this.IsDirty(g.LineFormatStates.EndArrowheadWidth)||i(this.hqa)?this.Vqa:this.hqa.EndArrowheadWidth},set:function(a){this.Vqa=a,this.Dirty(g.LineFormatStates.EndArrowheadWidth)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"InsetPen",{get:function(){return this.IsDirty(g.LineFormatStates.InsetPen)||i(this.hqa)?this.Zqa:this.hqa.InsetPen},set:function(a){this.Zqa=a,this.Dirty(g.LineFormatStates.InsetPen)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Pattern",{get:function(){return this.FillInternal.Pattern},set:function(a){this.FillInternal.uqa(a)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PresetGradientType",{get:function(){return this.FillInternal.PresetGradientType},enumerable:!0,configurable:!0}),b.prototype.xqa=function(a){this.FillInternal.xqa(a)},Object.defineProperty(b.prototype,"GradientAngle",{get:function(){return this.FillInternal.GradientAngle},set:function(a){this.FillInternal.GradientAngle=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientColorType",{get:function(){return this.FillInternal.GradientColorType},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientDegree",{get:function(){return this.FillInternal.GradientDegree},enumerable:!0,configurable:!0}),b.prototype.pqa=function(a){this.FillInternal.pqa(a)},Object.defineProperty(b.prototype,"GradientStops",{get:function(){return this.FillInternal.GradientStops},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"GradientStyle",{get:function(){return this.FillInternal.GradientStyle},enumerable:!0,configurable:!0}),b.prototype.rqa=function(a){this.FillInternal.rqa(a)},Object.defineProperty(b.prototype,"GradientVariant",{get:function(){return this.FillInternal.GradientVariant},enumerable:!0,configurable:!0}),b.prototype.tqa=function(a){this.FillInternal.tqa(a)},Object.defineProperty(b.prototype,"Style",{get:function(){return this.IsDirty(g.LineFormatStates.Style)||i(this.hqa)?this.Wqa:this.hqa.Style},set:function(a){this.Wqa=a,this.Dirty(g.LineFormatStates.Style)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Transparency",{get:function(){return this.FillInternal.Transparency},set:function(a){this.FillInternal.Transparency=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Visible",{get:function(){return this.IsDirty(g.LineFormatStates.Visible)||i(this.hqa)?this.bz:this.hqa.Visible},set:function(a){this.bz=a,this.Dirty(g.LineFormatStates.Visible)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Weight",{get:function(){return this.IsDirty(g.LineFormatStates.Weight)||i(this.hqa)?this.Xqa:this.hqa.Weight},set:function(a){this.Xqa=a,this.Dirty(g.LineFormatStates.Weight)},enumerable:!0,configurable:!0}),b.prototype.OneColorGradient=function(a,b,c){this.FillInternal.OneColorGradient(a,b,c)},b.prototype.TwoColorGradient=function(a,b){this.FillInternal.TwoColorGradient(a,b)},b.prototype.PresetGradient=function(a,b,c){this.FillInternal.PresetGradient(a,b,c)},b.prototype.Solid=function(){this.FillInternal.Solid()},b.prototype.Patterned=function(a){this.FillInternal.Patterned(a)},Object.defineProperty(b.prototype,"FillInternal",{get:function(){var a=this;return a.Zpa||(a.Zpa=new f.FillFormat(a.xc,a.hqa&&a.hqa.FillInternal,a.iqa)),a.Zpa},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Type",{get:function(){return this.FillInternal.Type},enumerable:!0,configurable:!0}),b.prototype.GetState=function(b,c){var d=this.Zpa;if(d){if(b===g.LineFormatStates.Color&&d.Color)return d.Color.IsDirtyIncludingParent(c);if(b===g.LineFormatStates.PatternColor&&d.PatternColor)return d.PatternColor.IsDirtyIncludingParent(c)}return a.prototype.GetState.call(this,b,c)},b.prototype.IsDirtyIncludingParent=function(b){var c=this.Zpa;return!(!c||!c.IsDirtyIncludingParent(b))||a.prototype.IsDirtyIncludingParent.call(this,b)},b.prototype.OnParentChanged=function(a){this.hqa=a;var b=this.Zpa;b&&a&&(b.ParentStateful=a.FillInternal)},b.prototype.FromOOModel=function(a){this.Yqa=a,this.FromCT_LineProperties(a)},b.prototype.ToOOModel=function(){return this.ToCT_LineProperties()},b.prototype.FromCT_LineProperties=function(a){var b=this;i(a.solidFill)?i(a.pattFill)?i(a.gradFill)?i(a.noFill)||(b.FillInternal.ColorInternal.ColorType=g.SolidColorType.None):b.FillInternal.FromOOModel(a.gradFill,g.ColorFillType.GradientFillProperties):b.FillInternal.FromOOModel(a.pattFill,g.ColorFillType.PatternFillProperties):b.FillInternal.FromOOModel(a.solidFill,g.ColorFillType.SolidColorFillProperties),i(a.headEnd)||b.FromCT_LineEndProperties(a.headEnd),i(a.tailEnd)||b.FromCT_TailLineEndProperties(a.tailEnd),!i(a.w)&&a.w>=0?b.Weight=a.w:(b.Xqa=-1,b.UnDirty(g.LineFormatStates.Weight)),i(a.cmpd)||(b.Style=a.cmpd),b.Round=!!a.round,i(a.prstDash)?b.UnDirty(g.LineFormatStates.DashStyle):b.DashStyle=a.prstDash,i(a.cap)||(b.CapStyle=a.cap),a.bevel?b.JoinStyle=g.LineJoinStyle.bevel:a.round?b.JoinStyle=g.LineJoinStyle.round:a.miter&&(b.JoinStyle=g.LineJoinStyle.miter)},b.prototype.ToCT_LineProperties=function(){var a,b,c,d,e=this,f=e.Yqa;return e.IsDirtyIncludingParent()||i(f)?(a=f||{},h(a),e.bz?i(e.Zpa)||(b=e.Zpa.ToOOModel(),b.colorFillType===g.ColorFillType.SolidColorFillProperties?a.solidFill=b:b.colorFillType===g.ColorFillType.PatternFillProperties?a.pattFill=b:b.colorFillType===g.ColorFillType.GradientFillProperties?a.gradFill=b:b.colorFillType===g.ColorFillType.NoFillProperties&&(a.noFill=!0),delete b.colorFillType):a.noFill=!0,c=e.ToCT_HeadLineEndProperties(),c&&(a.headEnd=c),c=e.ToCT_TailLineEndProperties(),c&&(a.tailEnd=c),e.Weight>=0&&(a.w=e.Weight),e.GetState(g.LineFormatStates.Style,!0)&&(a.cmpd=e.Style),e.GetState(g.LineFormatStates.DashStyle,!0)&&(a.prstDash=e.DashStyle),e.Round&&(a.round=!0),e.GetState(g.LineFormatStates.CapStyle,!0)&&(a.cap=e.CapStyle),delete a.bevel,delete a.round,delete a.miter,e.GetState(g.LineFormatStates.JoinStyle,!0)&&(d=e.JoinStyle,d===g.LineJoinStyle.bevel?a.bevel={}:d===g.LineJoinStyle.round?a.round=!0:d===g.LineJoinStyle.miter&&(a.miter={lim:0})),a):f},b.prototype.FromCT_LineEndProperties=function(a){var b=this;b.Qqa=a.type,b.Rqa=a.w,b.Pqa=a.len,b.SetState(g.LineFormatStates.BeginArrowheadStyle,!0),b.SetState(g.LineFormatStates.BeginArrowheadWidth,!0),b.SetState(g.LineFormatStates.BeginArrowheadLength,!0)},b.prototype.ToCT_HeadLineEndProperties=function(){var a={w:g.ArrowheadWidth.medium,len:g.ArrowheadLength.medium},b=this,c=!1;return b.GetState(g.LineFormatStates.BeginArrowheadStyle,!0)&&(a.type=b.BeginArrowheadStyle,c=!0),b.GetState(g.LineFormatStates.BeginArrowheadWidth,!0)&&(a.w=b.BeginArrowheadWidth,c=!0),b.GetState(g.LineFormatStates.BeginArrowheadLength,!0)&&(a.len=b.BeginArrowheadLength,c=!0),c?a:j},b.prototype.FromCT_TailLineEndProperties=function(a){if(!i(a)){var b=this;b.Uqa=a.type,b.Vqa=a.w,b.Tqa=a.len,b.SetState(g.LineFormatStates.EndArrowheadStyle,!0),b.SetState(g.LineFormatStates.EndArrowheadWidth,!0),b.SetState(g.LineFormatStates.EndArrowheadLength,!0)}},b.prototype.ToCT_TailLineEndProperties=function(){var a={w:g.ArrowheadWidth.medium,len:g.ArrowheadLength.medium},b=!1,c=this;return c.GetState(g.LineFormatStates.EndArrowheadStyle,!0)&&(a.type=c.EndArrowheadStyle,b=!0),c.GetState(g.LineFormatStates.EndArrowheadWidth,!0)&&(a.w=c.EndArrowheadWidth,b=!0),c.GetState(g.LineFormatStates.EndArrowheadLength,!0)&&(a.len=c.EndArrowheadLength,b=!0),b?a:j},b.prototype.Clone=function(){var a=this,c=new b(a.xc);return c.Zpa=a.Zpa&&a.Zpa.Clone(),c.Pqa=a.Pqa,c.Qqa=a.Qqa,c.Rqa=a.Rqa,c.Sqa=a.Sqa,c.Tqa=a.Tqa,c.Uqa=a.Uqa,c.Vqa=a.Vqa,c.Zqa=a.Zqa,c.bz=a.bz,c.Xqa=a.Xqa,c.rpa=a.rpa,c.hqa=a.hqa,c},b}(d.StatefullBase),b.LineFormat=k},"./dist/plugins/floatingObject/drawing/picture.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/stateful.js"),e=c("./dist/plugins/floatingObject/drawing/common.js"),f=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),g=e.ShapeConstants.PositionConver,h=e.ShapeUtility.FromLum,i=e.ShapeUtility.ToLum,j=e.UnitHelper.isNullOrUndefined,k=null,l=void 0,m=function(){function a(a){var b=this;b.qva=0,b.rva=0,b.sva=0,b.tva=0,b.xo=a}return Object.defineProperty(a.prototype,"PictureHeight",{get:function(){return this.tva},set:function(a){this.tva=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"PictureOffsetX",{get:function(){return this.qva},set:function(a){this.qva=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"PictureOffsetY",{get:function(){return this.rva},set:function(a){this.rva=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"PictureWidth",{get:function(){return this.sva},set:function(a){this.sva=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ShapeHeight",{get:function(){return this.xo.Height},set:function(a){this.xo.Height=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ShapeLeft",{get:function(){return this.xo.Left},set:function(a){this.xo.Left=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ShapeTop",{get:function(){return this.xo.Top},set:function(a){this.xo.Top=a},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"ShapeWidth",{get:function(){return this.xo.Width},set:function(a){this.xo.Width=a},enumerable:!0,configurable:!0}),a.prototype.Clone=function(){var b=this,c=new a(b.xo);return c.tva=b.tva,c.qva=b.qva,c.rva=b.rva,c.sva=b.sva,c},a}(),b.Crop=m,n=function(a){o(b,a);function b(b,c){var d,g=this;return b===l&&(b=f.PictureFormatType.PictureShape),c===l&&(c=k),g=a.call(this,c)||this,d=g,d.Jqa=e.ShapeConstants.DefaultBrightness,d.Xba=f.PictureColorType.PictureAutomatic,d.uva=e.ShapeConstants.DefaultBrightness,d.vva=e.ShapeConstants.DefaultTransparent,d.wva=b,d.hqa=c,d.xva=d.wva===f.PictureFormatType.PictureShape,d}return Object.defineProperty(b.prototype,"Brightness",{get:function(){return this.IsDirty(1)||j(this.hqa)?this.Jqa:this.hqa.Brightness},set:function(a){if(a<0||a>1)throw Error();this.Jqa=a,this.Dirty(1)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"ColorType",{get:function(){return this.IsDirty(2)||j(this.hqa)?this.Xba:this.hqa.ColorType},set:function(a){a===f.PictureColorType.PictureAutomatic?(this.Jqa=e.ShapeConstants.DefaultBrightness,this.uva=e.ShapeConstants.DefaultBrightness):a===f.PictureColorType.PictureWatermark&&(this.Jqa=.85,this.uva=.15),this.Xba=a,this.Dirty(2)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Contrast",{get:function(){return this.IsDirty(4)||j(this.hqa)?this.uva:this.hqa.Contrast},set:function(a){if(a<0||a>1)throw Error();this.uva=a,this.Dirty(4)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Crop",{get:function(){var a=this;return a.yva||(a.yva=new m(a.zva)),a.yva},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"CropBottom",{get:function(){return this.Crop.ShapeTop+this.Crop.ShapeHeight},set:function(a){this.Crop.ShapeHeight=a-this.Crop.ShapeTop},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"CropLeft",{get:function(){return this.Crop.ShapeLeft},set:function(a){this.Crop.ShapeLeft=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"CropRight",{get:function(){return this.Crop.ShapeLeft+this.Crop.ShapeWidth},set:function(a){this.Crop.ShapeWidth=a-this.Crop.ShapeLeft},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"CropTop",{get:function(){return this.Crop.ShapeTop},set:function(a){this.Crop.ShapeTop=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TransparencyColor",{get:function(){return this.IsDirty(8)||j(this.hqa)?this.vva:this.hqa.TransparencyColor},set:function(a){this.vva=a,this.Dirty(8)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"TransparentBackground",{get:function(){return this.IsDirty(16)||j(this.hqa)?this.xva:this.hqa.TransparentBackground},set:function(a){this.xva=a;var b=this.zva.Fill;a&&(b.Transparency=1),b.Solid(),this.Dirty(16)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Container",{get:function(){return this.zva},set:function(a){if(this.zva=a,a){var b=this.Crop;b.PictureWidth=a.Width,b.PictureHeight=a.Height}},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PicFill",{get:function(){return this.IsDirty(32)||j(this.hqa)?this.Ava:this.hqa.PicFill},set:function(a){this.Ava=a,this.Dirty(32)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PicType",{get:function(){return this.IsDirty(64)||j(this.hqa)?this.Bva:this.hqa.PicType},set:function(a){this.Bva=a,this.Dirty(64)},enumerable:!0,configurable:!0}),b.prototype.ClearModel=function(){var a,b,c;for(this.Tpa=k,a=0,b=this.Children;a<b.length;a++)c=b[a],c.ClearModel()},Object.defineProperty(b.prototype,"PictureFormatType",{get:function(){return this.wva},set:function(a){this.wva=a},enumerable:!0,configurable:!0}),b.prototype.HasOwnPicture=function(){return this.IsDirty(1)||this.IsDirty(2)||this.IsDirty(4)||this.IsDirty(8)||this.IsDirty(16)},b.prototype.SetPictureInfo=function(a,b,c){this.PicFill=a,this.PicType=b,this.PictureFormatType=c},b.prototype.IncrementBrightness=function(a){this.Brightness+=a},b.prototype.IncrementContrast=function(a){this.Contrast+=a},b.prototype.FromOOModel=function(a){var b,c=this;c.Tpa=a,j(a)||(c.FromCT_Blip(a.blip),b=c.zva,j(b)||(j(a.tile)||(c.PictureFormatType=f.PictureFormatType.TextureFill),c.Crop.PictureWidth=b.Width,c.Crop.PictureHeight=b.Height,c.PictureFormatType===f.PictureFormatType.PictureShape?c.FromCT_RelativeRect(a.srcRect):c.PictureFormatType===f.PictureFormatType.PictureFill&&c.FromCT_RelativeRect(a.stretch.fillRect)))},b.prototype.ToOOModel=function(){var a,b,c=this,d=c.Tpa||{};return d.colorFillType=f.ColorFillType.BlipFillProperties,d.blip=c.ToCT_Blip(),a=c.zva,j(a)?d:(b=c.PictureFormatType,b===f.PictureFormatType.PictureShape?(d.srcRect=c.ToCT_RelativeRect(),d.stretch={},d.stretch.fillRect={l:0,r:0,t:0,b:0}):b===f.PictureFormatType.PictureFill?(d.stretch={},d.stretch.fillRect=c.ToCT_RelativeRect(),d.srcRect={l:0,r:0,t:0,b:0}):b===f.PictureFormatType.TextureFill&&(d.stretch=k,d.srcRect=k),d)},b.prototype.FromCT_Blip=function(a){if(!j(a)){var b=this;b.FromCT_ColorChangeEffect(a.clrChange),b.FromCT_LuminanceEffect(a.lum),j(a.grayscl)?j(a.biLevel)||(b.ColorType=f.PictureColorType.PictureBlackAndWhite):b.ColorType=f.PictureColorType.PictureGrayscale,b.FromCT_BlipBlob(a.blipBlob)}},b.prototype.ToCT_Blip=function(){var a=this,b=a.Tpa&&a.Tpa.blip||{cstate:f.ST_BlipCompression.none};return a.TransparencyColor!==e.ShapeConstants.DefaultTransparent&&(b.clrChange=a.ToCT_ColorChangeEffect()),a.Brightness===e.ShapeConstants.DefaultBrightness&&a.Contrast===e.ShapeConstants.DefaultBrightness||(b.lum=a.ToCT_LuminanceEffect()),a.ColorType===f.PictureColorType.PictureGrayscale?b.grayscl={}:a.ColorType===f.PictureColorType.PictureBlackAndWhite&&(b.biLevel={thresh:5e4}),b.blipBlob=a.ToCT_BlipBlob(),b},b.prototype.FromCT_ColorChangeEffect=function(a){var b=a&&a.clrFrom&&a.clrFrom.srgbClr;b&&this.FromColorByteArr(a.val)},b.prototype.ToCT_ColorChangeEffect=function(){var a=this.ToColorByteArr(this.vva);return{useA:!0,clrFrom:{srgbClr:{val:a}},clrTo:{srgbClr:{val:a,alpha:[0]}}}},b.prototype.FromColorByteArr=function(a){this.vva=0|a[2]<<16|a[1]<<8|a[0]},b.prototype.ToColorByteArr=function(a){var b=[];return b[3]=Math.floor(a/(1<<24)),a%=1<<24,b[2]=Math.floor(a/65536),a%=65536,b[1]=Math.floor(a/256),a%=256,b[0]=Math.floor(a),b},b.prototype.FromCT_LuminanceEffect=function(a){a&&(this.Brightness=h(a.bright),this.Contrast=h(a.contrast))},b.prototype.ToCT_LuminanceEffect=function(){return{bright:i(this.Brightness),contrast:i(this.Contrast)}},b.prototype.FromCT_BlipBlob=function(a){if(a){var b=this;b.Ava=a.blob,b.Dirty(32),b.Bva=a.type,b.Dirty(64)}},b.prototype.ToCT_BlipBlob=function(){var a=this,b=a.Tpa&&a.Tpa.blip&&a.Tpa.blip.blipBlob||{type:f.ImageType.PNG};return b.blob=a.PicFill,b.type=a.PicType,b},b.prototype.FromCT_RelativeRect=function(a){var b,c,d,e,f,h,i,k=this.zva;j(a)||j(k)||(b=a.l/g,c=a.r/g,d=a.t/g,e=a.b/g,f=this.Crop,h=f.PictureWidth=k.Width/(1-b-c),i=f.PictureHeight=k.Height/(1-d-e),f.PictureOffsetX=(h-k.Width)/2-b*h,f.PictureOffsetY=(i-k.Height)/2-d*i)},b.prototype.ToCT_RelativeRect=function(){var a,b,c,d,e,f,h,i,k=this.zva,l=this.Crop,m=l.ShapeHeight,n=l.ShapeWidth;return j(k)||0===m||0===n?{l:0,r:0,t:0,b:0}:(a=l.PictureWidth,b=l.PictureHeight,c=l.PictureOffsetX,d=l.PictureOffsetY,e=((a-n)/2-c)/a,f=((a-n)/2+c)/a,h=((b-m)/2-d)/b,i=((b-m)/2+d)/b,{l:e*g,r:f*g,t:h*g,b:i*g})},b.prototype.Clone=function(){var a=this,c=new b;return c.Jqa=a.Jqa,c.uva=a.uva,c.Xba=a.Xba,a.yva&&(c.yva=a.yva.Clone()),c.Ava=a.Ava,c.wva=a.wva,c.Bva=a.Bva,c.zva=a.zva,c.vva=a.vva,c.xva=a.xva,c},b.prototype.OnParentChanged=function(a){this.hqa=a},b}(d.StatefullBase),b.PictureFormat=n},"./dist/plugins/floatingObject/drawing/stateful.js":function(a,b,c){"use strict";var d,e,f,g;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=d.Common.j,f=e.Fa,g=function(){function a(a){var b=this;b.oi=[],b.qpa=0,b.rpa=0,b.spa=a,b.spa&&b.spa.AddChildInternal(b)}return a.prototype.SetState=function(a,b){var c=this,d=a;b?c.rpa|=d:c.rpa&=~d,c.IsClearChildrenStateSuspended||c.oi.forEach(function(b){b.SetState(a,!1)})},a.prototype.GetState=function(a,b){var c=this,d=a,e=(d&c.rpa)===d;return e||!b||f(c.spa)||(e=c.spa.GetState(a,!0)),e},a.prototype.OnStateChanged=function(a){},a.prototype.Dirty=function(a){this.SetState(a,!0)},a.prototype.UnDirty=function(a){this.SetState(a,!1)},a.prototype.UnDirtyAll=function(){this.rpa=0},a.prototype.IsDirty=function(a){return this.GetState(a)},a.prototype.IsPropDirtyIncludingParent=function(a,b){var c=this,d=c.GetState(a);return!!d||!(!b||f(c.spa))&&c.spa.IsPropDirtyIncludingParent(a,!0)},a.prototype.IsDirtyIncludingParent=function(a){var b=this;return b.rpa>0||!(!a||f(b.spa))&&b.spa.IsDirtyIncludingParent(!0)},a.prototype.AddChildInternal=function(a){var b=this.oi;b.indexOf(a)===-1&&b.push(a)},a.prototype.RemoveChildInternal=function(a){var b=this.oi,c=b.indexOf(a);c!==-1&&b.splice(c,1)},a.prototype.SetParentForChildren=function(a){var b=this.oi.slice(0);b.forEach(function(b){b.ParentStateful=a})},a.prototype.OnParentChanged=function(a){},a.prototype.SuspendClearChildrenState=function(){this.qpa++},a.prototype.ResumeClearChilrenState=function(){this.qpa--},Object.defineProperty(a.prototype,"ParentStateful",{get:function(){return this.spa},set:function(a){var b=this;a!==b.spa&&(b.spa&&b.spa.RemoveChildInternal(this),b.spa=a,b.spa&&b.spa.AddChildInternal(this),b.OnParentChanged(a))},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"Children",{get:function(){return this.oi},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"IsClearChildrenStateSuspended",{get:function(){return this.qpa>0},enumerable:!0,configurable:!0}),a}(),b.StatefullBase=g},"./dist/plugins/floatingObject/drawing/threeDFormat.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/floatingObject/drawing/common.js"),e=c("./dist/plugins/floatingObject/drawing/stateful.js"),f=c("./dist/plugins/floatingObject/drawing/drawingInterface.js"),g=d.ShapeConstants.PositiveFixedAngleConvert,h=d.UnitHelper.isNullOrUndefined,function(a){a[a.BevelBottomDepth=1]="BevelBottomDepth",a[a.BevelBottomInset=2]="BevelBottomInset",a[a.BevelBottomType=4]="BevelBottomType",a[a.BevelTopDepth=8]="BevelTopDepth",a[a.BevelTopInset=16]="BevelTopInset",a[a.BevelTopType=32]="BevelTopType",a[a.ContourColor=64]="ContourColor",a[a.ContourWidth=128]="ContourWidth",a[a.Depth=256]="Depth",a[a.ExtrusionColor=512]="ExtrusionColor",a[a.ExtrusionColorType=1024]="ExtrusionColorType",a[a.FieldOfView=2048]="FieldOfView",a[a.LightAngle=4096]="LightAngle",a[a.Perspective=8192]="Perspective",a[a.PresetCamera=16384]="PresetCamera",a[a.PresetExtrusionDirection=32768]="PresetExtrusionDirection",a[a.PresetLighting=65536]="PresetLighting",a[a.PresetLightingDirection=131072]="PresetLightingDirection",a[a.PresetLightingSoftness=262144]="PresetLightingSoftness",a[a.PresetMaterial=524288]="PresetMaterial",a[a.PresetThreeDFormat=1048576]="PresetThreeDFormat",a[a.ProjectText=2097152]="ProjectText",a[a.RotationX=4194304]="RotationX",a[a.RotationY=8388608]="RotationY",a[a.RotationZ=16777216]="RotationZ",a[a.Visible=33554432]="Visible",a[a.Z=67108864]="Z",a[a.AutoScale=134217728]="AutoScale",a[a.DepthPercent=268435456]="DepthPercent",a[a.HeightPercent=536870912]="HeightPercent",a[a.RightAngleAxes=1073741824]="RightAngleAxes"}(i=b.ThreeDFormatStates||(b.ThreeDFormatStates={})),function(a){a[a.ExtrusionColorAutomatic=0]="ExtrusionColorAutomatic",a[a.ExtrusionColorCustom=1]="ExtrusionColorCustom"}(j||(j={})),k=null,l=function(a){m(b,a);function b(b,c){var e=a.call(this,c)||this,g=e;return g.$qa=d.BevelType.BevelNone,g._qa=d.BevelType.BevelNone,g.ara=j.ExtrusionColorAutomatic,g.bra=45,g.bz=!0,g.cra=k,g.dra=!0,g.era=100,g.fra=100,g.gra=!0,g.xc=b,g.hra=30,g.hqa=c,g.ira(f.PresetCamera.PresetCameraNone),e}return b.prototype.ira=function(a){var b=this;b.jra=a,b.Dirty(i.PresetCamera)},b.prototype.InitDicCameraType=function(){var a=this.cra=[];a[f.PresetCamera.CameraLegacyObliqueTopLeft]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueTop]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueTopRight]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueLeft]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueFront]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueRight]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueBottomLeft]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueBottom]=[0,0,0],a[f.PresetCamera.CameraLegacyObliqueBottomRight]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveTopLeft]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveTop]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveTopRight]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveLeft]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveFront]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveRight]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveBottomLeft]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveBottom]=[0,0,0],a[f.PresetCamera.CameraLegacyPerspectiveBottomRight]=[0,0,0],a[f.PresetCamera.CameraOrthographicFront]=[0,0,0],a[f.PresetCamera.CameraIsometricTopUp]=[314.7191,324.6037,60.16242],a[f.PresetCamera.CameraIsometricTopDown]=[45.28088,324.6037,299.8376],a[f.PresetCamera.CameraIsometricBottomUp]=[45.28088,35.39627,60.16242],a[f.PresetCamera.CameraIsometricBottomDown]=[314.7191,35.39627,299.8376],a[f.PresetCamera.CameraIsometricLeftUp]=[45,325,0],a[f.PresetCamera.CameraIsometricLeftDown]=[45,35,0],a[f.PresetCamera.CameraIsometricRightUp]=[315,35,0],a[f.PresetCamera.CameraIsometricRightDown]=[315,325,0],a[f.PresetCamera.CameraIsometricOffAxis1Left]=[64,18,0],a[f.PresetCamera.CameraIsometricOffAxis1Right]=[334,18,0],a[f.PresetCamera.CameraIsometricOffAxis1Top]=[306.5457,301.2619,57.6425],a[f.PresetCamera.CameraIsometricOffAxis2Left]=[26,18,0],a[f.PresetCamera.CameraIsometricOffAxis2Right]=[296,18,0],a[f.PresetCamera.CameraIsometricOffAxis2Top]=[53.45424,301.2619,302.3575],a[f.PresetCamera.CameraIsometricOffAxis3Left]=[64,342,0],a[f.PresetCamera.CameraIsometricOffAxis3Right]=[334,342,0],a[f.PresetCamera.CameraIsometricOffAxis3Bottom]=[306.5457,58.73808,302.3575],a[f.PresetCamera.CameraIsometricOffAxis4Left]=[26,342,0],a[f.PresetCamera.CameraIsometricOffAxis4Right]=[296,342,0],a[f.PresetCamera.CameraIsometricOffAxis4Bottom]=[53.45424,58.73808,57.64252],a[f.PresetCamera.CameraObliqueTopLeft]=[0,0,0],a[f.PresetCamera.CameraObliqueTop]=[0,0,0],a[f.PresetCamera.CameraObliqueTopRight]=[0,0,0],a[f.PresetCamera.CameraObliqueLeft]=[0,0,0],a[f.PresetCamera.CameraObliqueRight]=[0,0,0],a[f.PresetCamera.CameraObliqueBottomLeft]=[0,0,0],a[f.PresetCamera.CameraObliqueBottom]=[0,0,0],a[f.PresetCamera.CameraObliqueBottomRight]=[0,0,0],a[f.PresetCamera.CameraPerspectiveFront]=[0,0,0],a[f.PresetCamera.CameraPerspectiveLeft]=[20,0,0],a[f.PresetCamera.CameraPerspectiveRight]=[340,0,0],a[f.PresetCamera.CameraPerspectiveAbove]=[0,340,0],
a[f.PresetCamera.CameraPerspectiveBelow]=[0,20,0],a[f.PresetCamera.CameraPerspectiveAboveLeftFacing]=[14.33758,39.30647,341.1238],a[f.PresetCamera.CameraPerspectiveAboveRightFacing]=[345.6624,39.30647,18.87615],a[f.PresetCamera.CameraPerspectiveContrastingLeftFacing]=[43.93887,10.39642,356.4465],a[f.PresetCamera.CameraPerspectiveContrastingRightFacing]=[316.0611,10.39642,3.553517],a[f.PresetCamera.CameraPerspectiveHeroicLeftFacing]=[14.29302,348.9837,2.626817],a[f.PresetCamera.CameraPerspectiveHeroicRightFacing]=[345.7069,348.9837,357.3732],a[f.PresetCamera.CameraPerspectiveHeroicExtremeLeftFacing]=[34.46068,8.12245,357.0914],a[f.PresetCamera.CameraPerspectiveHeroicExtremeRightFacing]=[325.5393,8.12245,2.9086],a[f.PresetCamera.CameraPerspectiveRelaxed]=[0,309.5601,0],a[f.PresetCamera.CameraPerspectiveRelaxedModerately]=[0,324.844,0]},b.prototype.SetPresetCamera=function(a){var b=this;b.jra=a,b.Dirty(i.PresetCamera)},b.prototype.UpdatePresetCamera=function(){var a=this;a.jra===f.PresetCamera.PresetCameraNone&&a.SetPresetCamera(f.PresetCamera.CameraOrthographicFront)},b.prototype.IncrementRotationX=function(a){this.RotationX+=a},b.prototype.IncrementRotationY=function(a){this.RotationY+=a},b.prototype.IncrementRotationZ=function(a){this.RotationZ+=a},b.prototype.ResetRotation=function(){var a=this;a.RotationX=0,a.RotationY=0,a.RotationZ=0},b.prototype.Clone=function(){var a=this,c=new b(a.xc);return c.pra=a.pra,c.qra=a.qra,c.$qa=a.$qa,c.rra=a.rra,c.sra=a.sra,c._qa=a._qa,h(a.tra)||(c.tra=a.tra.Clone()),c.ura=a.ura,c.ora=a.ora,h(a.vra)||(c.vra=a.vra.Clone()),c.ara=a.ara,c.bra=a.bra,c.wra=a.wra,c.hra=a.hra,c.jra=a.jra,c.xra=a.xra,c.yra=a.yra,c.zra=a.zra,c.Ara=a.Ara,c.Bra=a.Bra,c.Cra=a.Cra,c.Dra=a.Dra,c.kra=a.kra,c.lra=a.lra,c.mra=a.mra,c.bz=a.bz,c.nra=a.nra,c.rpa=a.rpa,c.hqa=a.hqa,c},b.prototype.IsDirtyIncludingParent=function(b){var c=this;return!(!c.tra||!c.tra.IsDirtyIncludingParent(b))||(!(!c.vra||!c.vra.IsDirtyIncludingParent(b))||a.prototype.IsDirtyIncludingParent.call(this,b))},b.prototype.OnParentChanged=function(a){var b=this;b.hqa=a,h(b.hqa)&&(b.tra&&(b.tra.ParentStateful=k),b.vra&&(b.vra.ParentStateful=k))},b.prototype.FromOOModel=function(a){var b=this;b.Lqa=a,b.FromCT_Scene3D(b.Lqa.Scene3D),b.FromCT_View3D(b.Lqa.View3D),b.FromCT_Shape3D(b.Lqa.Shape3D)},b.prototype.ToOOModel=function(a){var b=this,c=b.Lqa||{};return c.View3D=b.ToCT_View3D(),b.PresetCamera!==f.PresetCamera.PresetCameraNone&&(c.Scene3D=b.ToCT_Scene3D()),(b.IsPropDirtyIncludingParent(i.Depth,!0)||b.IsPropDirtyIncludingParent(i.Z,!0))&&(c.Shape3D=b.ToCT_Shape3D()),c},b.prototype.FromCT_View3D=function(a){var b=this;h(a)||(h(a.hPercent)||(b._HeightPercent=a.hPercent),h(a.depthPercent)||(b._DepthPercent=a.depthPercent),h(a.rotX)||(b.RotationY=a.rotX),h(a.rotY)||(b.RotationX=a.rotY),h(a.rAngAx)?b._RightAngleAxes=!1:b._RightAngleAxes=a.rAngAx,h(a.perspective)||(b.Perspective=a.perspective/2))},b.prototype.ToCT_View3D=function(){var a=this,b=a.Lqa&&a.Lqa.View3D||{};return!a._AutoScale&&a.IsDirty(i.HeightPercent)&&(b.hPercent=a._HeightPercent),a.IsDirty(i.DepthPercent)&&(b.depthPercent=a._DepthPercent),a.IsDirty(i.RotationY)&&(b.rotX=a.RotationY),a.IsDirty(i.RotationX)&&(b.rotY=a.RotationX),a.IsDirty(i.RightAngleAxes)&&(b.rAngAx=a._RightAngleAxes),!a._RightAngleAxes&&a.IsDirty(i.Perspective)&&(b.perspective=2*a.Perspective),b},b.prototype.FromCT_Scene3D=function(a){h(a)||this.FromCT_Camera(a.camera)},b.prototype.ToCT_Scene3D=function(){var a=this,b=a.Lqa,c=b&&b.Scene3D||{};return c.camera=a.ToCT_Camera(),!h(c.camera)&&h(c.lightRig)&&(c.lightRig=a.ToDefaultCT_LightRig()),h(c.camera)&&h(c.backdrop)&&h(c.lightRig)?k:c},b.prototype.GetActualCT_Scene3D=function(){var a=this;return h(a.Lqa)||h(a.Lqa.Scene3D)?h(a.hqa)?k:a.GetActualCT_Scene3D():a.Lqa.Scene3D},b.prototype.FromCT_Shape3D=function(a){var b=this;h(a)||(0!==a.extrusionH&&(b.Depth=a.extrusionH,b.Dirty(i.Depth)),0!==a.z&&(b.Z=a.z,b.Dirty(i.Z)))},b.prototype.ToCT_Shape3D=function(){var a=this,b=a.Lqa,c=b&&b.Shape3D||{z:0,extrusionH:0,contourW:0,prstMaterial:f.ST_PresetMaterialType.warmMatte};return c.extrusionH=a.Depth,c.z=a.Z,c},b.prototype.ToDefaultCT_LightRig=function(){return{rig:f.LightRigType.LightRigThreePoint,dir:f.PresetLightingDirection.LightingTopLeft}},b.prototype.FromCT_Camera=function(a){h(a)||(this.ira(a.prst),h(a.rot)||this.FromCameraCT_SphereCoords(a.rot))},b.prototype.ToCT_Camera=function(){var a,b,c=this;return c.xc.DrawingType===f.DrawingType.Shape||c.xc.DrawingType===f.DrawingType.GroupShape?(a={zoom:1e5},a.prst=c.PresetCamera,a.rot=c.ToCameraCT_SphereCoords(),a):(b=c.Lqa,b&&b.Scene3D&&b.Scene3D.camera||k)},b.prototype.FromCameraCT_SphereCoords=function(a){var b=this;h(a)||(b.RotationX=a.lon/g,b.RotationY=a.lat/g,b.RotationZ=a.rev/g)},b.prototype.ToCameraCT_SphereCoords=function(){var a,b,c,d,e=this;return 0===e.RotationX&&0===e.RotationY&&0===e.RotationZ?k:(a={},b=e.RotationX,c=e.RotationY,d=e.RotationZ,a.lon=b*g,a.lat=c*g,a.rev=d*g,a)},Object.defineProperty(b.prototype,"Perspective",{get:function(){var a=this;return a.Dirty(i.Perspective),h(a.hqa)?a.hra:a.hqa.Perspective},set:function(a){var b=this;b.hra=a,b.Dirty(i.Perspective)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"PresetCamera",{get:function(){var a=this;return a.Dirty(i.PresetCamera),h(a.hqa)?a.jra:a.hqa.PresetCamera},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RotationX",{get:function(){var a=this;return a.Dirty(i.RotationX),h(a.hqa)?a.Visible?a.kra:0:a.hqa.RotationX},set:function(a){var b=this;a!==b.kra&&(b.kra=a,b.Dirty(i.RotationX),b.UpdatePresetCamera())},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RotationY",{get:function(){var a=this;return a.Dirty(i.RotationY),h(a.hqa)?a.Visible?a.lra:0:a.hqa.RotationY},set:function(a){var b=this;if(b.xc.DrawingType===f.DrawingType.Chart){if(a<-90&&a>=180)return}else if(a<0&&a>=360)return;b.lra=a,b.Dirty(i.RotationY),b.UpdatePresetCamera()},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"RotationZ",{get:function(){var a=this;return a.Dirty(i.RotationZ),h(a.hqa)?a.Visible?a.mra:0:a.hqa.RotationZ},set:function(a){var b=this;a!==b.mra&&(b.mra=a,b.Dirty(i.RotationZ),b.UpdatePresetCamera())},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Visible",{get:function(){var a=this;return a.Dirty(i.Visible),h(a.hqa)?a.bz:a.hqa.Visible},set:function(a){var b=this;b.bz=a,b.Dirty(i.Visible)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Z",{get:function(){var a=this;return a.Dirty(i.Z),h(a.hqa)?a.Visible?a.nra:0:a.hqa.Z},set:function(a){var b=this;b.nra=a,b.Dirty(i.Z),b.UpdatePresetCamera()},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"Depth",{get:function(){var a=this;return a.Dirty(i.Depth),h(a.hqa)?a.Visible?a.ora:0:a.hqa.Depth},set:function(a){var b=this;b.ora=a,b.Dirty(i.Depth),b.UpdatePresetCamera()},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"DicCameraType",{get:function(){var a=this;return h(a.cra)&&a.InitDicCameraType(),a.cra},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"_InternalVisible",{get:function(){return this.bz},set:function(a){this.bz=a},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"_AutoScale",{get:function(){return this.dra},set:function(a){var b=this;b.dra=a,b.Dirty(i.AutoScale)},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"_DepthPercent",{get:function(){return this.era},set:function(a){var b=this;a>=20&&a<=2e3&&(b.era=a,b.Dirty(i.DepthPercent))},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"_HeightPercent",{get:function(){return this.fra},set:function(a){var b=this;a>=5&&a<=500&&(b.fra=a,b.Dirty(i.HeightPercent))},enumerable:!0,configurable:!0}),Object.defineProperty(b.prototype,"_RightAngleAxes",{get:function(){return this.gra},set:function(a){var b=this;b.gra=a,b.Dirty(i.RightAngleAxes)},enumerable:!0,configurable:!0}),b}(e.StatefullBase),b.ThreeDFormat=l},"./dist/plugins/floatingObject/floatingobject-actions.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("./dist/plugins/floatingObject/floatingobject.js"),g=e.GC$,h=g.isArray,i=d.Common.k.ac,j=null,k=Math.min,l=d.Common.j.Fa,m="unSelectAllFloatingObjects",n="deleteFloatingObjects",o="navigationNextFloatingObject",p="navigationPreviousFloatingObject",q="cutFloatingObjects",r="copyFloatingObjects",s="pasteFloatingObjects",t="dragCopyFloatingObjects",u="selectAllFloatingObjects",v="moveFloatingObjects",w="moveFloatingObjectsUp",x="moveFloatingObjectsDown",y="moveFloatingObjectsLeft",z="moveFloatingObjectsRight",A="resizeFloatingObjects",B="isSelected",C="name",D=e.Commands.h4;function N(a){var b=a.wr,c=!1;return b&&b.zR().forEach(function(a){a&&a[B]()&&(c=!0)}),c}function O(a,b){var c=i(b),d,e;if(i(b)>0)for(d=0;d<c;d++)e=a.wr.NR(b[d]),e&&e.isSelected(!0)}E=function(a){M(b,a);function b(){return null!==a&&a.apply(this,arguments)||this}return b.prototype.canExecute=function(){return!!N(this.kj)},b.prototype.canUndo=function(){var a=e.Commands.bWa(this.kj.name()),b=this.VQ[a],c=b&&b.CUa;return!!(c&&i(c)>0)||(c=b&&b.EUa,!!(c&&i(c)>0))},b}(e.Commands.ActionBase),b.FloatingObjectUndoActionBase=E,F=function(a){M(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.execute=function(){var a,b,c,d,f,g,j=this,k=!1,l=j.kj,m=j.VQ,n=m.floatingObjects;if(j.canExecute()&&h(n)){for(l.ITa.startTransaction(),j.Lz(l,!0),a=l.wr,b=0,c=i(n);b<c;b++)d=a.NR(n[b]),d&&(f=l.slicers,j.zS(d)&&f&&f.vV(d[C]()),a.Fb(n[b],!0),d[B](!1));l.GJ(),j.Mz(l,!0),g=e.Commands.bWa(l.name()),m[g]=l.ITa.endTransaction(),k=i(n)>0}return k},b.prototype.undo=function(){var a,b=this,c=b.VQ,d=b.kj;return!!b.canUndo()&&(b.Lz(d,!0),a=e.Commands.bWa(d.name()),d.ITa.undo(c[a]),d.EJ(),b.Mz(d,!0),!0)},b.prototype.zS=function(a){return a&&"Slicer"===a.typeName},b}(E),b.DeleteFloatingObjectUndoAction=F;function P(a){var b,c=a,d=c.kj;return!!c.canUndo()&&(c.Lz(d,!0),b=e.Commands.bWa(d.name()),d.ITa.undo(c.VQ[b]),c.Mz(d,!0),!0)}G=function(a){M(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!0},b.prototype.execute=function(){var a,b,c,d,f,g,j=this,k=!1,l=j.VQ,m=l.floatingObjects,n=j.kj;if(j.canExecute()&&h(m)){for(n.ITa.startTransaction(),j.Lz(n,!0),a=n.wr,b=0,c=i(m);b<c;b++)d=a.NR(m[b]),d.allowMove()&&(f=d.position(),d.position(new e.Point(f.x+l.offsetX,f.y+l.offsetY)));j.Mz(n,!0),g=e.Commands.bWa(n.name()),l[g]=n.ITa.endTransaction(),k=i(m)>0}return k},b.prototype.undo=function(){return P(this)},b}(E),b.MovingFloatingObjectUndoAction=G,H=function(a){M(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.execute=function(){var a,b,c,d,f,g,j=this,k=!1,l=j.VQ,m=l.floatingObjects,n=j.kj;if(O(n,m),j.canExecute()&&h(m)){for(n.ITa.startTransaction(),j.Lz(n,!0),a=n.wr,b=0,c=i(m);b<c;b++)d=a.NR(m[b]),f=d.position(),d.width(d.width()+l.offsetWidth),d.height(d.height()+l.offsetHeight),d.position(new e.Point(f.x+l.offsetX,f.y+l.offsetY));j.Mz(n,!0),g=e.Commands.bWa(n.name()),l[g]=n.ITa.endTransaction(),k=i(m)>0}return k},b.prototype.undo=function(){return P(this)},b}(E),b.ResizingFloatingObjectUndoAction=H,I=function(a){M(b,a);function b(){var b=a.call(this)||this;return b.kj=j,b}return b.prototype.canExecute=function(){var a=this,b=a.VQ.floatingObjects;return!!(i(b)>0&&a.ES(b))},b.prototype.canUndo=function(){var a=e.Commands.bWa(this.kj.name()),b=this.VQ[a],c=b&&b.CUa;return!!(c&&i(c)>0)},b.prototype.ES=function(a){var b,c,d,e=this,f=e.kj,g=f.wr;for(b=0,c=i(a);b<c;b++)if(d=g.NR(a[b]),d&&!f.ER(d))return!1;return!0},b.prototype.zS=function(a){return a&&"Slicer"===a.typeName},b}(e.Commands.ActionBase),b.FloatingObjectCopyPasteUndoAction=I,J=function(a){M(b,a);function b(b,c){var d=a.call(this)||this,e=d;return e.OFFSET=15,e.kj=b,e.VQ=c,l(c.clipboardFloatingObjectData)&&(c.clipboardFloatingObjectData=c.fromSheet.CR),d}return b.prototype.execute=function(){var a,b,c,d,f,g,h,j,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,D,E,F,G=this,H=G.VQ,I=H.floatingObjects;if(G.canExecute()){for(a=G.kj,b=H.fromSheet,a.ITa.startTransaction(),G.Lz(a,!0),c=[],d=a.wr,f=H.clipboardFloatingObjectData,void 0===H.Jl&&(N(a)&&d.zR().forEach(function(a){a&&a[B]()&&c.push(a)}),H.Jl=G.kj.getActiveRowIndex(),H.GS=G.kj.getActiveColumnIndex()),N(a)&&d.zR().forEach(function(a){a&&a[B]()&&a[B](!1)}),g=[],h=void 0,j=void 0,l=Number.MAX_VALUE,m=l,n=l,o=[],j=i(I),h=0;h<j;h++)p=f.find(I[h]).position(),m=k(m,p.x),n=k(n,p.y),o.push(new e.Point(p.x-m,p.y-n));if(q=0,r=0,c.length)s=l,t=l,c.forEach(function(a){if(a){var b=a.position();s=k(s,b.x),t=k(t,b.y)}}),q=s<l?s+G.OFFSET:0,r=t<l?t+G.OFFSET:0;else{for(u=0;u<H.Jl;u++)r+=a.Yr(u,3);for(v=0;v<H.GS;v++)q+=a.$r(v,3)}for(j=i(I),h=0;h<j;h++)w=o[h],g.push(new e.Point(q+w.x,r+w.y));for(x=H.isCutting,y=b.wr,j=i(I),h=0;h<j;h++)z=void 0,A=void 0,D=!1,E=f.find(I[h]),z=E.clone(a),z.sheet(a),!x||y.NR(I[h])||d.NR(I[h])?(D=z.name()===E.name(),D&&(A=G.zS(z)?a.slicers.HS(z[C]()):d.FS())):A=E.name(),D&&z[C](A),G.zS(z)&&(a.slicers.Cz(z),z.width(E.width()),z.height(E.height())),z.isVisible(!0),z.position(g[h]),z[B](!0),d.Sb(z);return G.Mz(a,!0),F=e.Commands.bWa(a.name()),H[F]=a.ITa.endTransaction(),!0}return!1},b.prototype.undo=function(){var a,b,c=this;return!!c.canUndo()&&(a=c.kj,c.Lz(a,!0),b=e.Commands.bWa(a.name()),a.ITa.undo(c.VQ[b]),c.Mz(a,!0),!0)},b}(I),b.ClipboardPasteFloatingObjectUndoAction=J,K=function(a){M(b,a);function b(b,c){var d=a.call(this)||this,e=d;return e.kj=b,e.VQ=c,d}return b.prototype.execute=function(){var a,b,c,d,f,g,h,j,k,l=this,m=!1,n=l.VQ,o=n.floatingObjects,p=l.kj;if(l.canExecute()){for(p.ITa.startTransaction(),l.Lz(p,!0),a=p.wr,b=i(o),c=0;c<b;c++)d=a.NR(o[c]),d&&(f=d.clone(p),f.sheet(p),g=d.position(),f.position(new e.Point(g.x+n.offsetX,g.y+n.offsetY)),f[C](a.FS()),f[B](!0),d[B](!1),l.zS(f)&&p.slicers&&(h=f,j=p.slicers.HS(h.columnName()),h[C](j),p.slicers.Cz(h)),a.Sb(f));l.Mz(p,!0),k=e.Commands.bWa(p.name()),n[k]=p.ITa.endTransaction(),m=!0}return m},b.prototype.undo=function(){var a,b=this,c=b.kj;return!!b.canUndo()&&(b.Lz(c,!0),a=e.Commands.bWa(c.name()),c.ITa.undo(b.VQ[a]),b.Mz(c,!0),!0)},b}(I),b.DragCopyFloatingObjectUndoAction=K;function Q(a){var b=[];return a.wr.zR().forEach(function(a){a[B]()&&b.push(a[C]())}),b}function R(a,b,c){var d,e,g,h,k=a.BR(),l=k.fromSheet;for(k.fromSheet=a,k.isCutting=c,d=new f.yR,e=0,g=0;g<i(b);g++)h=a.wr.NR(b[g]),h&&(d.push(h),e++);return l&&(l.CR=j),a.CR=d,e>0}e.Commands[m]={canUndo:!1,execute:function(a,b){var c=e.Commands.bT(a,b);return!!N(c)&&(c.suspendPaint(),c.uQ(),c.GJ(),c.resumePaint(),!0)}},e.Commands[u]={canUndo:!1,execute:function(a,b){var c=e.Commands.bT(a,b);c.suspendPaint(),c.wr.zR().forEach(function(a){a[B](!0)}),c.resumePaint()}},e.Commands[n]={canUndo:!0,execute:function(a,b,c){var d,f=e.Commands.bT(a,b);return c||(b.cmd||(b.cmd=n),b.floatingObjects||(d=Q(f),b.floatingObjects=d)),i(b.floatingObjects)>0&&D(a,F,b,c)}},e.Commands[o]={canUndo:!1,execute:function(a,b){var c,d,f,g,h,i,j,k=e.Commands.bT(a,b);if(N(k)){for(k.suspendPaint(),c=void 0,d=void 0,f=void 0,g=void 0,h=k.wr.zR(),i=h.length,g=0;g<i;g++){if(j=h[g],d||(d=j),c){f=j;break}j&&j[B]()&&(c=j)}return k.uQ(),f||(f=d),f&&f[B](!0),k.resumePaint(),!0}return!1}},e.Commands[p]={canUndo:!1,execute:function(a,b){var c,d,f,g,h,i,j=e.Commands.bT(a,b);if(N(j)){for(j.suspendPaint(),c=void 0,d=void 0,f=void 0,g=j.wr.zR(),h=g.length,f=0;f<h;f++)if(i=g[f],c||(c=i),c[B]())d=i;else if(i[B]()||(d=i),i[B]())break;return d&&(j.uQ(),d[B](!0)),j.resumePaint(),!0}return!1}};function S(a){var b=[];return a.wr.zR().forEach(function(a){a[B]()&&b.push(a[C]())}),b}e.Commands[q]={canUndo:!1,execute:function(a,b){var c,d,f=e.Commands.bT(a,b),g=S(f);return i(g)>0&&(c=R(f,g,!0),c&&(f.Sob&&f.Sob(),e.Commands.Yxb(f.parent)),d={cmd:n,sheetName:f.name(),floatingObjects:g},f.wu().execute(d))}},e.Commands[r]={canUndo:!1,execute:function(a,b){var c=e.Commands.bT(a,b),d=S(c),f=R(c,d,!1);return f&&(c.Sob&&c.Sob(),e.Commands.Yxb(c.parent)),f}},e.Commands[s]={canUndo:!0,execute:function(a,b,c){var d,f,g,h,j=e.Commands.bT(a,b);return!j.isEditing()&&(d=j.BR(),f=d.fromSheet,!!(g=f&&f.CR)&&(h=[],g.each(function(a){h.push(a[C]())}),0!==i(h)&&(!j.Js&&(c||(b.cmd||(b.cmd=s),b.floatingObjects||(b.floatingObjects=h,b.fromSheet=f,b.isCutting=d.isCutting)),i(b.floatingObjects)>0&&D(a,J,b,c)))))}},e.Commands[t]={canUndo:!0,execute:function(a,b,c){return D(a,K,b,c)}},function(a){a[a.left=0]="left",a[a.up=1]="up",a[a.right=2]="right",a[a.down=3]="down"}(L||(L={}));function T(a){return function(b,c){var d,f,g=e.Commands.bT(b,c),h=0;return a===L.left?h=-1:a===L.right&&(h=1),d=0,a===L.up?d=-1:a===L.down&&(d=1),f=Q(g),i(f)>0&&g.wu().execute({cmd:v,sheetName:g.name(),floatingObjects:f,offsetX:h,offsetY:d})}}e.Commands[y]=T(L.left),e.Commands[w]=T(L.up),e.Commands[z]=T(L.right),e.Commands[x]=T(L.down),e.Commands[v]={canUndo:!0,execute:function(a,b,c){return(0!==b.offsetX||0!==b.offsetY)&&D(a,G,b,c)}},e.Commands[A]={canUndo:!0,execute:function(a,b,c){return D(a,H,b,c)}},e.Commands.JR=function(a){var b=e.Ul.sl(),c=!b,d=b;a.register(m,e.Commands[m],27,!1,!1,!1,!1),a.register(u,e.Commands[u],65,c,!1,!1,d),a.register(n,e.Commands[n],b?8:46,!1,!1,!1,!1),a.register(o,e.Commands[o],9,!1,!1,!1,!1),a.register(p,e.Commands[p],9,!1,!0,!1,!1),a.register(q,e.Commands[q],88,c,!1,!1,d),a.register(r,e.Commands[r],67,c,!1,!1,d),a.register(t,e.Commands[t]),a.register(s,e.Commands[s],86,c,!1,!1,d),a.register(v,e.Commands[v]),a.register(w,e.Commands[w],38,!1,!1,!1,!1),a.register(x,e.Commands[x],40,!1,!1,!1,!1),a.register(y,e.Commands[y],37,!1,!1,!1,!1),a.register(z,e.Commands[z],39,!1,!1,!1,!1),a.register(A,e.Commands[A])}},"./dist/plugins/floatingObject/floatingobject-touch.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Touch"),e=c("Core"),f=c("./dist/plugins/floatingObject/floatingobject.js"),g=e.Ul.nl,h=null,i=function(){function a(a,b,c){var e,f,g=this;g.yM=new d.jR(g),g.DQ=new d.kR(a,"FL_"+b.name,g.yM,2,200),g.BQ=c,g.CQ=new j(a,b),e=g.CQ,f=g.DQ,f._M=function(){return b.YR.isSelected()},f.bN=function(){return!0},f.dN=function(a){return e.jQ(a)},f.eN=function(a){return e.lQ(a)},f.fN=function(a){return e.pQ(a)},f.hN=function(a){return e.qQ(a)},f.iN=function(a){return e.sQ(a)}}return a.prototype.EQ=function(){var a=this,b=a.BQ;b&&b.xN(a.DQ,!0)},a.prototype.FQ=function(){var a=this,b=a.BQ;b&&b.xN(a.DQ,!1)},a.prototype.GQ=function(a){return this.yM.GQ()},a.prototype.HQ=function(a){return this.yM.HQ()},a.prototype.IQ=function(a){return this.yM.IQ()},a}(),j=function(){function a(a,b){var c=this;c.MS=b,c.YR=b.YR,c.NS=a,c.hQ=new d.TP(b.kj)}return a.prototype.OS=function(a){var b,c=new d.oP(a.X,a.Y),f=e.GC$(this.NS).offset();return f&&(b=document.body,c.X+=f.left+b.clientLeft||0,c.Y+=f.top+b.clientTop||0),c},a.prototype.jQ=function(a){a.DO=3},a.prototype.lQ=function(a,b){var c=this,d=b?a.rN:c.OS(a.rN);c.MS.AD({target:a.GP,isTouch:!0,button:0,pageX:d.X,pageY:d.Y,stopPropagation:function(){}}),b||c.hQ.OP()},a.prototype.pQ=function(a,b){var c=this,d=b?a.rN:c.OS(a.rN),e=a.dP.rO,f=c.MS.kj,g=f.parent;1!==e&&g&&g.options.allowUserZoom&&!b?(f.mm.O3=!1,c.MS.rS(),c.hQ.PP(e)):c.MS.BD({isTouch:!0,button:0,pageX:d.X,pageY:d.Y,stopPropagation:function(){}})},a.prototype.qQ=function(a,b){var c=this,d=b?a.rN:c.OS(a.rN),e=a.dP.rO,f=c.MS.kj,g=f.parent;1!==e&&g&&g.options.allowUserZoom&&!b?c.hQ.SP(e):this.MS.CD({isTouch:!0,button:0,pageX:d.X,pageY:d.Y,stopPropagation:function(){}})},a.prototype.sQ=function(b,c){var d,f=this,g=c?b.rN:f.OS(b.rN),i={e:{rN:g},r:h};if(a.ao(f,"preProcessTapped",i),!i.r)try{d=f.MS.kj,d.suspendPaint(),f.YR.isSelected()||(d.uQ(),f.YR.isSelected(!0),d.Wq(e.Events.FloatingElementSelected,{type:"floatingObject"}),e.Vl.Zl(d)),d.clearSelection()}finally{d.resumePaint()}},a}(),b.FloatingObjectTouchEventHandler=j,e.Zn(j),e.Rxb.touch&&f.FloatingObjectRender.$n("touch",{init:function(a){var b=this,c=b.wR=new i(a,b,b.kj.parent.BQ);c.EQ()},dispose:function(){var a=this.wR;a&&a.FQ()},preProcessMouseDown:function(a){var b=a.e,c=this.wR;c&&!b.isTouch&&c.GQ(b)&&(g(b),a.r=!0)},preProcessMouseMove:function(a){var b=a.e,c=this.wR;c&&!b.isTouch&&c.IQ(b)&&(g(b),a.r=!0)},preProcessMouseUp:function(a){var b=a.e,c=this.wR;c&&!b.isTouch&&c.HQ(b)&&(g(b),a.r=!0)}})},"./dist/plugins/floatingObject/floatingobject.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/floatingObject/picture.js")),d(c("./dist/plugins/floatingObject/floatingobject.js")),d(c("./dist/plugins/floatingObject/floatingobject-actions.js")),d(c("./dist/plugins/floatingObject/floatingobject-touch.js")),d(c("./dist/plugins/floatingObject/floatingobject.ns.js"));var e=c("./dist/plugins/floatingObject/drawing/chart.ns.js");b.Drawing=e},"./dist/plugins/floatingObject/floatingobject.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa,Ta,Ua,Va,Wa,Xa,Ya,Za,$a,_a,ab,bb,cb,db,eb,fb,gb,hb,ib,jb,kb,lb,mb,nb,ob,pb,qb,rb,sb,tb,ub,vb,wb,xb,yb,zb,Ab,Bb,Cb;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("./dist/plugins/floatingObject/floatingobject.ns.js"),g=function(){function a(){this.rM={}}return a.prototype.push=function(a){this.rM[Db(a)]=a},a.prototype.remove=function(a){delete this.rM[a]},a.prototype.find=function(a){return this.rM[a]},a.prototype.empty=function(){delete this.rM,this.rM={}},a.prototype.each=function(a){var b,c=this.rM;for(b in c)c.hasOwnProperty(b)&&a(c[b])},a.prototype.isEmpty=function(){var a,b=this.rM;for(a in b)if(b.hasOwnProperty(a))return!1;return!0},a}(),b.yR=g,h=new e.Common.ResourceManager(f.SR),i=h.getResource.bind(h),j=e.Common.j,k=e.Common.D,l=j.Fa,m=j.Ma,n=d.Ul.Pl,o=d.Ul.Ol,p=d.Ul.Nl,q=d.Ul.Rl,r=g,s=document,t=k,u=null,v=void 0,w=Math.ceil,x=Math.floor,y=Math.max,z=Math.min,A=Math.abs,B="div",C="on",D="z-index",E="move",F="unselectable",G=1,H="border",I=9007199254740992,J="floatingObjects",K=".gcFloatingObject",L="name",M="x",N="y",O="width",P="height",Q="start",R=Q+"Row",S=Q+"RowOffset",T=Q+"Column",U=Q+"ColumnOffset",V="end",W=V+"Row",X=V+"RowOffset",Y=V+"Column",Z=V+"ColumnOffset",$="isSelected",_="isLocked",aa="isVisible",ba="canPrint",ca="dynamicSize",da="dynamicMove",ea="fixedPosition",fa="allowResize",ga="allowMove",ha=700,ia=701,ja="position",ka="absolute",la="background",ma="gc-no-user-select",na="gc-floatingobject-selected",oa="gc-floatingobject-unselected",pa="gc-floatingobject-resize-indicator-select",qa="gc-floatingobject-resize-indicator-unSelect",ra="gc-floatingobject-content-container",sa="gc-floatingobject-container",ta="gc-floatingobject-moving-container",ua="gc-floatingobject-moving-div",va=".gc-floatingobject-resize-indicator",wa="gc-floatingobject-resize-indicator",xa="gc-floatingobject-absolute",ya="gc-floatingobject-top",za="gc-floatingobject-middle",Aa="gcfloatingobject-bottom",Ba="gc-floatingobject-left",Ca="gc-floatingobject-center",Da="gc-floatingobject-right",Ea="div."+wa,Fa=Ea+"."+ya+"."+Ba,Ga=Ea+"."+za+"."+Ba,Ha=Ea+"."+Aa+"."+Ba,Ia=Ea+"."+ya+"."+Ca,Ja=Ea+"."+Aa+"."+Ca,Ka=Ea+"."+ya+"."+Da,La=Ea+"."+za+"."+Da,Ma=Ea+"."+Aa+"."+Da,Na="content",Oa="gc-no-user-select",Pa="100%",Qa="gc-floatingobject-background-cover",Ra=".fos",Sa=Q+"X",Ta=Q+"Y",Ua=Q+"Width",Va=Q+"Height",Wa=V+"X",Xa=V+"Y",Ya=Q+"TopRow",Za=Q+"LeftColumn",$a="left",_a="center",ab="right",bb="top",cb=bb+$a,db=bb+_a,eb=bb+ab,fb="middle",gb=fb+$a,hb=fb+ab,ib="bottom",jb=ib+$a,kb=ib+_a,lb=ib+ab,mb="-resize",nb="cursor",ob="mousemove",pb="mouseup";function Db(a){return"string"==typeof a[L]?a[L]:a[L]()}qb=function(){var a=this;a.uQ=function(){var a=this.wr;a&&a.zR().forEach(function(a){a.isSelected(!1)})},a.AR=function(){var a=this,b=a.wr;b&&(b.isNeedToUpdateLayout=!0)},a.BR=function(){var a=this,b=a.parent,c=a.tv,d=b&&b.tv;return c||(c=a.tv=d?d:{fromSheet:u,isCutting:!1}),c},a.ex=function(){var a=this,b=a.BR(),c=b&&b.fromSheet;c&&(c.CR=u),b.fromSheet=u,b.isCutting=!1},a.DR=function(a){var b=this.options;return!b.isProtected||!a.isLocked()||b.protectionOptions.allowEditObjects},a.ER=function(a){var b=this.options;return!b.isProtected||!a.isLocked()||b.protectionOptions.allowEditObjects},a.isPasteFloatingObject=function(){var a=this,b=a.BR().fromSheet,c=this.CR;return!c&&b&&(c=b.CR),c&&!c.isEmpty()}},qb.call(d.Worksheet.prototype);function Eb(a,b){var c,d,e,f,g,h,i,j=a.FR;if(j){for(c=void 0,d=void 0,e=void 0,f=void 0,e=j.length,c=0;c<e;c++)if(g=j[c]){for(f=g.length,d=0;d<f;d++)h=g[d],h&&h.no();g.length=0}j.length=0,a.FR=u}i=a.wr,i&&b!==!1&&i.no()}function Fb(a,b,c){a.zR().forEach(function(a){a.rI(b,c)})}function Gb(a,b,c){var d,e,f=[];for(a.zR().forEach(function(d){Jb(b,-1,b+c-1,a.sheet().getColumnCount(),d)&&(d.dynamicMove()||d.dynamicSize())?f.push(d):d.GR(b,c)}),e=f.length,d=0;d<e;d++)a.Fb(f[d].name())}function Hb(a,b,c){a.zR().forEach(function(a){a.tI(b,c)})}function Ib(a,b,c){var d,e,f=[];for(a.zR().forEach(function(d){Jb(-1,b,a.sheet().getRowCount(),b+c-1,d)&&(d.dynamicMove()||d.dynamicSize())?f.push(d):d.HR(b,c)}),e=f.length,d=0;d<e;d++)a.Fb(f[d].name())}function Jb(a,b,c,d,e){var f=e[R](),g=e[T](),h=e[W](),i=e[Y]();return a<=f&&b<=g&&c>=h&&d>=i}function Kb(a,b){var c=this;return b||(b={}),d.GC$.each(a,function(a,d){var e=c[d],f=e.call(c);e.isDefault(f)||(b[d]=f)}),b}b.toJsonFn=Kb;function Lb(a,b){if(b){var c=this;d.GC$.each(a,function(a,d){b[d]!==v&&c[d].call(c,b[d],!1)})}}b.fromJsonFn=Lb;function Mb(a){var b=a;return function(a,c){var d=this,e=d.sheet();b===Na&&(d.KR=!0),e&&e.$p(),d.onPropertyChanged(b,a,c)}}b.propertyRefreshCallback=Mb;function Nb(a){var b=a;return function(a,c){var d=this,e=d.sheet();e&&(Pb(d),e.$p()),d.onPropertyChanged(b,a,c)}}function Ob(a){var b=a;return function(a,c){var d=this,e=d.sheet();e&&(cc(d),e.$p()),d.onPropertyChanged(b,a,c)}}function Pb(a){var b,c,e,f,g=a.sheet();function h(b,c,d,e){var f=0,h=b.call(a);return f=c.call(a,g,0,h),Qb.call(a,b,d,e),f+=d.call(a)}g&&(b=h(a[R],Rb,a[S],g.getRowHeight),c=h(a[T],Sb,a[U],g.getColumnWidth),e=a.x(),f=a.y(),e===c&&f===b||(a.x(c,!1),a.y(b,!1),a.onPropertyChanged("position",new d.Point(c,b),new d.Point(e,f))))}function Qb(a,b,c){var d=this,e=b.call(d),f=c.call(d.sheet(),a.call(d),3);f>0&&f--,e>f&&b.call(d,f,!1)}function Rb(a,b,c,d){var e,f=0;for(e=b;e<c&&(f+=a.getRowHeight(e,3),!(f>d));e++);return f}function Sb(a,b,c,d){var e,f=0;for(e=b;e<c&&(f+=a.getColumnWidth(e,3),!(f>d));e++);return f}function Tb(a){var b=a.sheet();function c(b,c,d,e,f){var g=Vb(a,b,c),h=g.index;e.call(a,g.offset,!1),g.offset===-1&&(h+=1,e.call(a,0),h===f&&(h=f-1)),d.call(a,h,!1)}b&&(c(!0,a.y(),a[R],a[S],b.getRowCount()),c(!1,a.x(),a[T],a[U],b.getColumnCount()))}function Ub(a){var b=a.sheet();function c(b,c,d,e){var f=Vb(a,b,c);d.call(a,f.index,!1),e.call(a,f.offset,!1)}b&&(c(!0,a.y()+a[P](),a[W],a[X]),c(!1,a.x()+a[O](),a[Y],a[Z]))}function Vb(a,b,c){var d,e,f=0,g=0,h=a.sheet(),i=b?h.getRowCount():h.getColumnCount();for(d=0;d<i;d++){if(e=b?h.getRowHeight(d,3):h.getColumnWidth(d,3),f+=e,g=f-c,g>0){g=g>0?e-g:-1;break}if(0===g){g=0,d+=1;break}}return d>=i&&(d=i-1,g=b?h.getRowHeight(d,3):h.getColumnWidth(d,3)),{offset:g,index:d}}function Wb(a,b){Xb(b,Yb(a))}function Xb(a,b){a&&(a.style.zIndex=b+"")}function Yb(a){var b=a.PR;return l(b)&&(b=a.Cka),b}function Zb(a,b){a.PR=b,a.qo.forEach(function(a){for(var c=a.parentElement;c.className.indexOf(sa)===-1;)c=c.parentElement;Xb(c,b)})}function $b(a,b){l(a.PR)&&(a.Cka+=b)}rb=function(){function a(a,b){this.kj=a,this.g3=b}return a.prototype.add=function(a,b,c,d,e,f,g,h){var i=this,j;return"1"===i.g3?j=xb.ozb(a,b,c,d,e,f):"0"===i.g3&&(j=a),i.kj.wr.Sb(j),j},a.prototype.get=function(a){return this.kj.wr.NR(a)},a.prototype.remove=function(a){this.kj.wr.Fb(a)},a.prototype.clear=function(){return this.kj.wr.$b(this.g3)},a.prototype.all=function(){return this.kj.wr.zR(this.g3)},a.prototype.zIndex=function(a,b){return this.kj.wr.PR(a,b)},a.prototype.tTa=function(a,b,c){this.all().forEach(function(d){var e=c?d.startRow():d.startColumn(),f=c?d.endRow():d.endColumn();b<e||a>f||!d.dynamicSize()||cc(d),Pb(d)})},a}(),b.FloatingObjectCollection=rb;function _b(a){var b=a.sheet();b&&(d.GC$(s.body).bind("scroll"+K,function(){var a=b.parent.getActiveSheet();a.wr&&b.QP(a.Dr())}),b.Fu(d.Events.ColumnChanged+K,function(b,c){var d=c.propertyName;d!==O&&d!==aa||ac(a)}),b.Fu(d.Events.RowChanged+K,function(b,c){var d=c.propertyName;d!==P&&d!==aa||ac(a)}),b.Fu(d.Events.ColumnWidthChanged+K,function(){ac(a)}),b.Fu(d.Events.RowHeightChanged+K,function(){ac(a)}))}function ac(a){var b=a.sheet();a.isNeedToUpdateLayout=!0,b.QP&&b.QP(b.Dr())}function bc(a,b,c){return c&&c.width>0&&c.height>0&&(c.x===a||c.y===b||c.contains(a,b))}sb=function(){function a(a){var b=this;b.sheet(a),_b(b),b.QR={}}return a.prototype.Dka=function(a){this.zR().forEach(function(b){$b(b,a)})},a.prototype.Sb=function(a,b){var c,d=this,e=d.sheet();if(e.suspendPaint(),c=a.name(),!c)throw Error(i().Exp_FloatingObjectNameEmptyError);if(d.QR[c]){if(!b)throw Error(i().Exp_FloatingObjectHasSameNameError);c=d.FS(c+"__"),a.name(c)}e.ITa.AUa(),d.Dka(-1),a.sheet(e),d.QR[c]=a,e.Zoa(a),e.resumePaint()},a.prototype.NR=function(a){return this.QR[a]},a.prototype.Fb=function(a,b){var c,e,f,g=this;return!!a&&(c=g.sheet(),e=g.NR(a),c.ITa.AUa(),c.suspendPaint(),b?(f={sheet:c,sheetName:c.name(),floatingObject:e,cancel:!1},c.Wq(d.Events.FloatingObjectRemoving,f),f.cancel===!1&&(delete g.QR[a],c.Wq(d.Events.FloatingObjectRemoved,{sheet:c,sheetName:c.name(),floatingObject:e}))):delete g.QR[a],c.resumePaint(),!0)},a.prototype.U3=function(a,b){var c,d=this,e=d.sheet();if(e.suspendPaint(),!b)throw Error(i().Exp_FloatingObjectNameEmptyError);if(d.QR[b])throw Error(i().Exp_FloatingObjectHasSameNameError);c=d.QR[a],delete d.QR[a],d.QR[b]=c,c.onNameChanged&&c.onNameChanged(a,b),e.resumePaint()},a.prototype.$b=function(a){var b=this,c=b.sheet();c.suspendPaint(),d.GC$.each(this.QR,function(c,d){a&&a!==d.g3||b.Fb(d.name())}),c.resumePaint()},a.prototype.zR=function(a){var b=[];return d.GC$.each(this.QR,function(c,d){a&&a!==d.g3||b.push(d)}),b},a.prototype.PR=function(a,b){var c=isNaN(b),d=this.NR(a);if(d){if(c)return Yb(d);Zb(d,b)}if(c)return-1},a.prototype.toJSON=function(){var a=[];return this.zR().forEach(function(b){var c,d;b instanceof xb&&b.hga()&&(c=b.toJSON(),d=b.PR,isNaN(d)||(c.zIndex=d),a.push(c))}),a},a.prototype.fromJSON=function(a,b){var c,e,f,g,h,i,j,k=this;if(a&&0!==a.length)for(c=0;c<a.length;c++)e=a[c],f=void 0,e&&(g=void 0,h=e.floatingObjectType,i=d.Ul.Ol(h)?h+"":e.typeName,"0"===i?f=new xb:"1"===i?f=xb.ozb():(g=d.getTypeFromString(i),g&&(f=new g)),f&&(f.fromJSON(e,b),k.Sb(f),j=e.zIndex,l(j)||(f.PR=j)))},a.prototype.no=function(){d.GC$(s.body).unbind("scroll"+K);var a=this.sheet();a&&(a.Gu(d.Events.ColumnChanged+K),a.Gu(d.Events.RowChanged+K),a.Gu(d.Events.ColumnWidthChanged+K),a.Gu(d.Events.RowHeightChanged+K))},a.prototype.hitTest=function(a,b,c){var d,e,f,g,h=this.sheet(),i=h.am(),j=u;if(i)for(d=0;d<=2;d++)for(e=0;e<=2;e++)if(f=i.Ft(d,e),f&&f.contains(a,b)&&(g=h.IR&&h.IR(d,e),g&&g.jS.each(function(d){var e=d.YR;e&&e.isVisible()&&(h.DR(e)||c)&&bc(a,b,d.Tka)&&(j?Yb(j.floatingObject)<Yb(e)&&(j.floatingobject=e):j={x:a,y:b,floatingObject:e,yl:d})}),j))return j;return j},a.prototype.FS=function(a){var b,c;for(a||(a="FloatingObject"),b=1,c=a+(""+b);this.NR(c);)b++,
c=a+(""+b);return c},a}(),b.FloatingObjectModel=sb,tb={sheet:n("sheet",v)},d.GC$.extend(sb.prototype,tb);function cc(a){var b,c,d,e,f=a.sheet();function g(b,c,d,e,g,h){var i,j=0,k=b.call(a),l=c.call(a);return j=d.call(a,f,k,l),k===l?(Qb.call(a,c,h,e),j=h.call(a)-g.call(a)):(i=e.call(f,k,3),i>0&&(j-=g.call(a)),Qb.call(a,c,h,e),j+=h.call(a)),j}f&&(b=g(a[R],a[W],Rb,f.getRowHeight,a[S],a[X]),c=g(a[T],a[Y],Sb,f.getColumnWidth,a[U],a[Z]),c<0&&(c=0),b<0&&(b=0),d=a[O](),d!==c&&(a[O](c,!1),a.onPropertyChanged(O,c,d)),e=a[P](),e!==b&&(a[P](b,!1),a.onPropertyChanged(P,b,e)))}ub=["dotted","dashed","solid","double","groove","ridge","inset","outset"];function dc(a){return ub.indexOf(a)>=0}function ec(a){var b,c,d;if(o(a)&&("string"!=typeof a||""===a))throw Error(i().Exp_FloatingObjectNameEmptyError);return b=this,a!==b.name()&&(c=b.sheet(),!c||(d=c.wr.NR(a),!d))}vb=[L,M,N,O,P,R,S,T,U,W,X,Y,Z,ba,$,_,aa,da,ca,ea,fa,ga],wb=[["sheet",u,function(){lc(this)}],[L,v,function(a,b){var c,d,e=this,f=e.sheet();f&&(c=f.wr,d=c.NR(b),d===e&&c.U3(b,a)),e.onPropertyChanged("name",a,b)},ec],[M,0,gc(M,!1)],[N,0,gc(N,!1)],[P,0,gc(P,!0)],[O,0,gc(O,!0)],[R,v,Nb(R)],[S,0,Nb(S)],[T,v,Nb(T)],[U,0,Nb(U)],[W,v,Ob(W)],[X,0,Ob(X)],[Y,v,Ob(Y)],[Z,0,Ob(Z)],[$,!1,Mb($)],[_,!0,fc(_)],[ba,!0,fc(ba)],[aa,!0,Mb(aa)],[da,!0,function(a,b){fc(da).call(this,a,b)},function(a){return"boolean"==typeof a&&(a||this.dynamicSize(!1,!1),!this.fixedPosition())}],[ca,!0,function(a,b){fc(ca).call(this,a,b)},function(a){return"boolean"==typeof a&&this.dynamicMove()&&!this.fixedPosition()}],[ea,!1,function(a,b){var c=this;a&&(c.dynamicMove(!1,!1),c.dynamicSize(!1,!1)),c.onPropertyChanged(ea,a,b)}],[fa,!0,Mb(fa)],[ga,!0,Mb(ga)]];function fc(a){return function(b,c){this.onPropertyChanged(a,b,c)}}function gc(a,b){return function(c,d){var e=this,f=e.sheet();f&&(b&&jc(e),kc(e),oc(e),f.$p()),e.onPropertyChanged(a,c,d)}}function hc(a,b,c,d){var e=this,f=a.call(e),g=b.call(e);c<=f?e.dynamicMove()&&(a.call(e,f+d),b.call(e,g+d)):c<g&&e.dynamicSize()&&b.call(e,g+d)}function ic(a,b,c,d,e,f,g){var h,i,j=this,k=a.call(j),l=c.call(j);e<=k?g<k?j.dynamicMove()&&(a.call(j,k-f),c.call(j,l-f)):g<l&&(h=g-k+1,i=l-k+1-h,j.dynamicMove()&&(a.call(j,e),b.call(j,0)),j.dynamicSize()&&c.call(j,e+i-1)):e<=l&&(g<l?j.dynamicSize()&&c.call(j,l-f):j.dynamicSize()&&(c.call(j,e),d.call(j,0)))}function jc(a){var b=mc(a,a[P]()),c=nc(a,a[O]());a[O]()>c&&a[O](c,!1),a[P]()>b&&a[P](b,!1)}function kc(a){var b,c,d=a.x()+a[O](),e=nc(a,d);d>e&&a.x(y(0,e-a[O]()),!1),a.x()<0&&a.x(0,!1),b=a.y()+a[P](),c=mc(a,b),b>c&&a.y(y(0,c-a[P]()),!1),a.y()<0&&a.y(0,!1)}function lc(a){var b,c,d,e;jc(a),kc(a),b=a[R](),c=a[T](),l(b)||l(c)?Tb(a):Pb(a),d=a[W](),e=a[Y](),l(d)||l(e)?Ub(a):cc(a)}function mc(a,b){var c,d=a.sheet();return d?(c=d.getRowCount(),Rb(d,0,c,b)):I}function nc(a,b){var c,d=a.sheet();return d?(c=d.getColumnCount(),Sb(d,0,c,b)):I}function oc(a){Tb(a),Ub(a)}function pc(a,b,c,d,e,f,g){var h,i=0,j=g?f.getRowHeight:f.getColumnWidth;for(h=a;h<b;h++)i+=x(j.call(f,h)*e);return i-=x((c||0)*e),i+=x((d||0)*e)}function qc(a,b,c,d){var e,f=0;for(e=0;e<a;e++)f+=d?c.getRowHeight(e,3):c.getColumnWidth(e,3);return f+=b}xb=function(){function a(a,b,c,d,e){var f=this;f.LR={startRowOffset:v,startColumnOffset:v,endRowOffset:v,endColumnOffset:v},f.typeName="0",f.g3="0",f.name(a),f.x(b?b:0,!1),f.y(c?c:0,!1),f[O](d?d:0,!1),f[P](e?e:0,!1),f.qo=[],f.KR=!1,f.Cka=ha}return a.prototype.position=function(a){var b,c,e=this,f=e.sheet();return 0===arguments.length?new d.Point(e.x(),e.y()):(b=e.x(),c=e.y(),!m(a,d.Point)||b===a.x&&c===a.y||(e.x(a.x,!1),e.y(a.y,!1),kc(e),oc(e),f&&f.$p(),e.onPropertyChanged("position",a,new d.Point(b,c))),e)},a.prototype.cloneContent=function(){var a,b,c=this.content();return c?(a=void 0,c.cloneNode?a=c.cloneNode(!0):(b=p(B),b.innerHTML=c.outerHTML,a=b.firstChild),d.GC$(a).removeAttr("id")[0]):u},a.prototype.refreshContent=function(a){var b,c,e,f,g,h,i=this;i.KR&&a&&(b=d.GC$(a),b.empty(),c=i.cloneContent(),e=Pa,f=Pa,g="",h="",c&&c.style&&(g=c.style.width,h=c.style.height),""!==g&&(e=g),""!==h&&(f=h),c!==u?(d.GC$(c).css({width:e,height:f}).addClass(Qa).addClass(ma).attr(F,C).appendTo(a),b.css(la,"")):b.css(la,""),i.KR=!1)},a.prototype.toJSON=function(){var a,b=this,c=Kb.call(b,vb),e=b.LR;for(a in e)l(e[a])||(c[a]=e[a]);return c.typeName=b.typeName,c[Na]=d.GC$(p(B)).append(b.cloneContent()).html(),c},a.prototype.fromJSON=function(a,b){var c,e;a&&(c=this,c.TR=!0,Lb.call(this,vb,a,b),l(a[R])&&oc(c),a.content&&(e=p(B),e.innerHTML=a.content,c.content(d.GC$(e.firstChild)[0])),c.TR=v)},a.prototype.clone=function(b){var c=this,e=d.getTypeFromString(c.typeName),f=e?new e:new a,g=JSON.stringify(c.toJSON());return f.fromJSON(JSON.parse(g)),f.content(c.cloneContent()),f},a.prototype.onPropertyChanged=function(a,b,c){var d,e=this;e.TR||(d=e.sheet(),d&&(d.ITa.BUa(e,a,c),e.Wq({sheet:d,sheetName:d.name(),floatingObject:e,propertyName:a})))},a.prototype.Wq=function(a){var b,c=this.sheet();c&&(c.Wq(d.Events.FloatingObjectChanged,a),a.propertyName===$&&(b={sheet:a.sheet,sheetName:a.sheetName,floatingObject:a.floatingObject},c.Wq(d.Events.FloatingObjectSelectionChanged,b)))},a.prototype.rI=function(a,b){var c=this;a=w(a),b=w(b),hc.call(c,c[R],c[W],a,b)},a.prototype.GR=function(a,b){var c=this,d=a+b-1;ic.call(c,c[R],c[S],c[W],c[X],a,b,d)},a.prototype.tI=function(a,b){var c=this;a=w(a),b=w(b),hc.call(c,c[T],c[Y],a,b)},a.prototype.HR=function(a,b){var c,d=this;a=w(a),b=w(b),c=a+b-1,ic.call(d,d[T],d[U],d[Y],d[Z],a,b,c)},a.prototype.getHost=function(a,b){var c,d,e=this.qo.concat();if(!l(a)&&!l(b)){for(c=0;c<e.length;c++)if(d=e[c],d.rowViewportIndex===a&&d.columnViewportIndex===b)return d;return u}return e},a.prototype.UR=function(a,b,c){var d=this.qo;a&&d.indexOf(a)<0&&(a.rowViewportIndex=b,a.columnViewportIndex=c,d.push(a))},a.prototype.VR=function(a){var b=this.qo,c=b.indexOf(a);c>=0&&b.splice(c,1)},a.prototype.WR=function(a,b){var c,e,f,g,h,i,j=p(B);return d.GC$(j).addClass(Oa).addClass(ra).attr(F,C).css(ja,ka),c=this.cloneContent(),e=Pa,f=Pa,g="",h="",i=c&&c.style,i&&(g=i.width,h=i.height),""!==g&&(e=g),""!==h&&(f=h),c!==u&&d.GC$(c).css({width:e,height:f}).addClass(Qa).addClass(Oa).attr(F,C).appendTo(j),this.UR(j,a,b),j},a.prototype.hga=function(){return!0},a.prototype.no=function(a){},a.prototype.Toa=function(){Pb(this),cc(this)},a.prototype.wxb=function(a){var b,c,d=this;return 1===a?x(d.width()):(b=qc(d.startColumn(),d.startColumnOffset(),d.sheet(),!1),c=Vb(d,!1,b+d[O]()),pc(d.startColumn(),c.index,d.startColumnOffset(),c.offset,a,d.sheet(),!1))},a.prototype.xxb=function(a){var b,c,d=this;return 1===a?x(d.height()):(b=qc(d.startRow(),d.startRowOffset(),d.sheet(),!0),c=Vb(d,!0,b+d[P]()),pc(d.startRow(),c.index,d.startRowOffset(),c.offset,a,d.sheet(),!0))},a}(),b.FloatingObject=xb,yb={content:n(Na,u,Mb(Na))},d.GC$.each(wb,function(a,b){yb[b[0]]=n(b[0],b[1],b[2],b[3])}),d.GC$.extend(xb.prototype,yb);function rc(a,b,c){var e=a.$R,f,g,h,i;switch(b){case cb:f=z(e[Sa]+e[Ua],c.x),g=z(e[Ta]+e[Va],c.y),h=A(c.x-e[Sa]-e[Ua]),i=A(c.y-e[Ta]-e[Va]);break;case db:f=e[Sa],g=z(e[Ta]+e[Va],c.y),h=e[Ua],i=A(c.y-e[Ta]-e[Va]);break;case eb:f=z(e[Sa],c.x),g=z(e[Ta]+e[Va],c.y),h=A(c.x-e[Sa]),i=A(c.y-e[Ta]-e[Va]);break;case gb:f=z(e[Sa]+e[Ua],c.x),g=e[Ta],h=A(c.x-e[Sa]-e[Ua]),i=e[Va];break;case hb:f=z(e[Sa],c.x),g=e[Ta],h=A(c.x-e[Sa]),i=e[Va];break;case jb:f=z(e[Sa]+e[Ua],c.x),g=z(e[Ta],c.y),h=A(c.x-e[Sa]-e[Ua]),i=A(c.y-e[Ta]);break;case kb:f=e[Sa],g=z(e[Ta],c.y),h=e[Ua],i=A(c.y-e[Ta]);break;case lb:f=z(e[Sa],c.x),g=z(e[Ta],c.y),h=A(c.x-e[Sa]),i=A(c.y-e[Ta])}return new d.Rect(f,g,h,i)}function sc(a){var b=d.GC$(a),c=b.position();return new d.Rect(c.left,c.top,b[O](),b[P]())}function tc(a,b,c,e,f,g){d.GC$(a).css([bb,$a,O,P],[[e-1],[c-1],f,g]).addClass(ua).appendTo(b)}function uc(a,b){if(!a._R)return u;var c={inMoving:!1};return b||(c.inMoving=!0),c}function vc(a){var b,c,d,e,f,g=a,h=g.kj,i=g.WE,j=g.aS,k=h.frozenTrailingRowCount(),l=h.frozenTrailingColumnCount(),m=h.am(),n=m.Ft(i,j),o=g.YR,p=o[R](),q=o[T](),r=o[W](),s=o[Y](),t=h.frozenRowCount(),u=h.frozenColumnCount(),v=h.getRowCount(3)-k-1,w=h.getColumnCount(3)-l-1;return i===G?(t>0&&(b=h.getViewportTopRow(i),(p<t||b===t)&&(n.y-=m.fs,n[P]+=m.fs)),k>0&&(c=h.getViewportBottomRow(i),r>v&&c===v&&(n[P]+=m.gs))):i===G-1?(b=h.getViewportTopRow(i+1),(r>=b||b===t)&&(f=m.Ft(i+1,j),n[P]+=f[P])):i===G+1&&(c=h.getViewportBottomRow(i-1),(p<=c||c===v)&&(f=m.Ft(i-1,j),n.y=f.y,n[P]+=f[P])),j===G?(u>0&&(d=h.getViewportLeftColumn(j),(q<u||d===u)&&(n.x-=m.hs,n[O]+=m.hs)),h.frozenTrailingColumnCount()>0&&(e=h.getViewportRightColumn(j),(s>w||e===w)&&(n[O]+=m.js))):j===G-1?(d=h.getViewportLeftColumn(j+1),(s>=d||d===u)&&(f=m.Ft(i,j+1),n[O]+=f[O])):j===G+1&&(e=h.getViewportRightColumn(j-1),(q<=e||e===w)&&(f=m.Ft(i,j-1),n.x=f.x,n[O]+=f[O])),n}function wc(a,b,c,e,f){var g=sc(a);d.GC$(a).css([bb,$a,O,P],[g.y+c,g.x+b,g[O]+e,g[P]+f])}function xc(a){var b,c,e,f,g,h,i,j,k,l,m,n,o,p,q=a;if(q.bS){b=q.$F,c=vc(q),e=q.cS,f=q.$R,g=f.direction,h=void 0,b=new d.Point(b.x-c.x,b.y-c.y),h=rc(q,g,b),f.endX=h.x,f.endY=h.y,f.endWidth=h[O],f.endHeight=h[P],i=sc(e),j=h.y-i.y,k=h.x-i.x,l=h[O]-i[O],m=h[P]-i[P],wc(e,k,j,l,m),n=q.dS;for(o in n)t(n,o)&&(p=n[o],wc(p.moveResizeDiv,k,j,l,m))}}function yc(a,b){var c,e=a.kj;e&&(c=e.RF(b),c===!1&&d.Ul.nl(b))}function zc(a){var b,c,d,e,f,g,h,i=a,j=i.kj,k=i.eS;if(k){if(b=q(j,k[Ya],j.getViewportTopRow(i.WE),!0),c=q(j,k[Za],j.getViewportLeftColumn(i.aS),!1),d=k[Wa]-k[Sa]+(k[Wa]>k[Sa]?c:-1*c),e=k[Xa]-k[Ta]+(k[Xa]>k[Ta]?b:-1*b),0===d&&0===e)return;f=[],f.push(i.YR.name()),g=i.dS;for(h in g)t(g,h)&&f.push(h);j.wu().execute({cmd:"dragCopyFloatingObjects",sheetName:j.name(),floatingObjects:f,offsetX:d,offsetY:e})}}function Ac(a){var b,c,d,e,f,g,h,i=a,j=i.kj,k=i.eS,l=j.zoom();if(k){b=q(j,k[Ya],j.getViewportTopRow(i.WE),!0),c=q(j,k[Za],j.getViewportLeftColumn(i.aS),!1),d=k[Wa]-k[Sa]+(k[Wa]>k[Sa]?c:-1*c),e=k[Xa]-k[Ta]+(k[Xa]>k[Ta]?b:-1*b),d/=l,e/=l,f=[],f.push(i.YR.name()),g=i.dS;for(h in g)t(g,h)&&f.push(h);j.wu().execute({cmd:"moveFloatingObjects",sheetName:j.name(),floatingObjects:f,offsetX:d,offsetY:e})}}function Bc(a){var b,c,d,e,f,g,h,i,j,k=a,l=k.kj,m=l.zoom(),n=k.YR,o=k.$R;if(o){b=q(l,o[Ya],l.getViewportTopRow(k.WE),!0),c=q(l,o[Za],l.getViewportLeftColumn(k.aS),!1),d=o[Wa]-o[Sa]+(o[Wa]>o[Sa]?c:-1*c),d/=m,e=o[Xa]-o[Ta]+(o[Xa]>o[Ta]?b:-1*b),e/=m,f=(o.endWidth+c)/m-n[O](),g=(o.endHeight+b)/m-n[P](),h=[],h.push(n.name()),i=k.dS;for(j in i)t(i,j)&&h.push(j);l.wu().execute({cmd:"resizeFloatingObjects",sheetName:l.name(),floatingObjects:h,offsetX:d,offsetY:e,offsetWidth:f,offsetHeight:g})}}function Cc(a,b,c){var d,e,f,g,h,i=a,j=i.cS;if(j){d=c.x-b.x,e=c.y-b.y,Dc(j,d,e),f=i.dS;for(g in f)t(f,g)&&(h=f[g],Dc(h.moveResizeDiv,d,e))}}function Dc(a,b,c){var e=d.GC$(a),f=d.GC$(a).position();e.css([bb,$a],[f[bb]+c,f[$a]+b])}function Ec(a){var b,c,d,e,f,g,h,i,j,k,l,m=a,n=m.eS;n&&(b=m.YR,c=m.kj,d=c.am(),e=d[O]-d.Br-d.zr,f=d[P]-d.Cr-d.Ar,g=n.startX,h=n.startY,i=b.position().x,j=b.position().y,k=b[O](),l=b[P](),i+n.endX-g<0&&(n.endX=g-i),j+n.endY-h<0&&(n.endY=h-j),i+k+n.endX-g>e&&(n.endX=e+g-i-k),j+l+n.endY-h>f&&(n.endY=f+h-j-l),n.startTopRow=c.getViewportTopRow(m.WE),n.startLeftColumn=c.getViewportLeftColumn(m.aS))}function Fc(a){var b,c,e,f,g,h=a,i=h.$R;i&&(b=h.kj,c=b.am(),e=c[O]-c.Br-c.zr,f=c[P]-c.Cr-c.Ar,g=new d.Rect(i.endX,i.endY,i.endWidth,i.endHeight),g=g.getIntersect(0,0,e,f),i.endX=g.x,i.endY=g.y,i.endWidth=g[O],i.endHeight=g[P],i.startTopRow=b.getViewportTopRow(h.WE),i.startLeftColumn=b.getViewportLeftColumn(h.aS))}function Gc(a){var b,c,e,f,g,h,i,j,k=p(B),l=d.GC$(k),m=a;return l.addClass(sa).addClass(ma).attr(F,C).css(D,ha).bind("mousedown",function(a){m.AD(a)}).bind(ob,function(a){m.BD(a)}).bind(pb,function(a){m.CD(a)}),k.addEventListener("mousewheel",function(a){yc(m,a)},!1),k.addEventListener("DOMMouseScroll",function(a){yc(m,a)},!1),b=d.GC$(p(B)).addClass(wa+" "+ya+" "+Ba+" "+xa),c=d.GC$(p(B)).addClass(wa+" "+ya+" "+Ca+" "+xa),e=d.GC$(p(B)).addClass(wa+" "+ya+" "+Da+" "+xa),f=d.GC$(p(B)).addClass(wa+" "+za+" "+Ba+" "+xa),g=d.GC$(p(B)).addClass(wa+" "+za+" "+Da+" "+xa),h=d.GC$(p(B)).addClass(wa+" "+Aa+" "+Ba+" "+xa),i=d.GC$(p(B)).addClass(wa+" "+Aa+" "+Ca+" "+xa),j=d.GC$(p(B)).addClass(wa+" "+Aa+" "+Da+" "+xa),l.append(b).append(c).append(e).append(f).append(g).append(h).append(i).append(j),zb.ao(m,"init",k),k}function Hc(a){var b,c,e,f,g,h,i,j,k=a,l=k.hS&&k._R;if(l&&!k.cS){b=k.iS=p(B),k.cS=p(B),k.dS={},k.gS.jS.each(function(a){var b,c=a.YR;c&&c.name()!==k.YR.name()&&c.isSelected()&&!k.dS[c.name()]&&(b=a._R,k.dS[c.name()]={offsetX:b.x-l.x,offsetY:b.y-l.y,height:b[P],width:b[O],moveResizeDiv:p(B)})}),c=vc(k),e=d.GC$(b),e.css([D,bb,$a,O,P,la],[ia,c.y,c.x,c[O],c[P],"rgba(255,255,255,0.01)"]).addClass(ta).bind(ob,function(a){k.BD(a)}).bind(pb,function(a){k.CD(a)}),f=l.y-c.y,g=l.x-c.x,tc(k.cS,b,g,f,l[O],l[P]),h=k.dS;for(i in h)t(h,i)&&(j=h[i],tc(j.moveResizeDiv,k.iS,g+j.offsetX,f+j.offsetY,j[O],j[P]));k.Ks&&d.GC$(k.Ks).append(k.iS)}}function Ic(a){var b=a;b.RB||(d.GC$(s).bind(ob+K,function(a){b.BD(a)}).bind(pb+K,function(a){b.CD(a)}),b.RB=!0)}function Jc(a){var b=a;b.RB&&(b.RB=!1,d.GC$(s).unbind(ob+K).unbind(pb+K))}function Kc(a,b){var c,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t=d.GC$(va,b);t.removeClass(qa),c=a,e=sc(c.hS),f=c.kS,g=c.YR,c.lS&&(f=c.lS),h=zb.mS,i=sc(f),j=i.x,k=i.y,l=e[P]-i.y-i[P],m=e[O]-i.x-i[O],n=0,o=h+2,p=x(h/2+1),"1"===g.typeName&&(q=g.borderWidth(),r=g.borderStyle(),dc(r)||(q=0),q>=p&&(n=q-p),l=e[P]-k-i[P]-q,m=e[O]-j-i[O]-q,j+=q,k+=q),s=[],j>0?(Lc(c,k>0,Fa,n,n,v,v,"nw"+mb,cb,s),Lc(c,e[P]>0,Ga,n,(i[P]-o)/2+k,v,v,"w"+mb,gb,s),Lc(c,l>0,Ha,n,v,v,n,"ne"+mb,jb,s)):(Mc(c,Fa),Mc(c,Ga),Mc(c,Ha)),Lc(c,k>0,Ia,(i[O]-o)/2+j,n,v,v,"n"+mb,db,s),Lc(c,l>0,Ja,(i[O]-o)/2+j,v,v,n,"n"+mb,kb,s),m>0?(Lc(c,k>0,Ka,v,n,n,v,"sw"+mb,eb,s),Lc(c,e[P]>0,La,v,(i[P]-o)/2+k,n,v,"w"+mb,hb,s),Lc(c,l>0,Ma,v,v,n,n,"se"+mb,lb,s)):(Mc(c,Ka),Mc(c,La),Mc(c,Ma)),c.ZR=!0,c.pmb=s}function Lc(a,b,c,e,f,g,h,i,j,k){var m,n,o,p,q=a,r=d.GC$(c,q.hS),s=zb.mS;b?r.css([$a,bb,ab,ib,O,P,nb],[e,f,g,h,s,s,i]).addClass(pa).attr("resizeDirection",j):Mc(q,c),k&&(m=d.GC$(q.hS),n=0,o=0,l(e)||(n=e),l(f)||(o=f),l(g)||(n=m.width()-g-s),l(h)||(o=m.height()-h-s),p=new d.Rect(n,o,s,s),k.push({rect:p,cursor:i,direction:j}))}function Mc(a,b){d.GC$(b,a.hS).removeClass(pa).addClass(qa)}function Nc(a,b){d.GC$(va,b).removeClass(pa).addClass(qa),a.ZR=!1}function Oc(a){var b,c,e,f,g,h,i,j,k,l,m,n=a,o=n.YR,p=o.qmb||o.src(),q=o.pictureStretch(),r=d.Rm.Om(o.sheet(),o.backColor()),s=n.kS;p&&s&&(b=d.GC$(s),n.XR!==p&&(n.XR=p,b.css([la+"-image",la+"-repeat"],["url('"+p+"')","no-repeat"])),b.css(la+"-color",r),q!==u&&q!==v&&o.oS&&(c=o.getOriginalWidth(),e=o.getOriginalHeight(),f=b[O](),g=b[P](),h=o.rmb,i=void 0,h&&(j=f/h.width,k=g/h.height,l=j*h.left,m=k*h.top,i={position:-l+"px "+-m+"px",size:j+"px "+k+"px"}),d.Ul.ql(s,f,g,c,e,q,i)))}function Pc(a,b,c){var e,f,g,h,i=a.lS,j=a.YR,k=j.borderWidth(),l=j.borderStyle(),m=d.Rm.Om(j.sheet(),j.borderColor()),n=j.borderRadius(),o=0,p=x(zb.mS/2+1);dc(l)&&!j.noFill()||(k=0),k<p&&(o=p-k),e=o,f=o,g=o,h=o,p=y(k,p),b.x<0?e+=b.x-p:(e+=b.x<p?b.x-p:0,h=c[O]-e-b[O]-2-2*k),b.y<0?f+=b.y-p:(f+=b.y<p?b.y-p:0,g=c[P]-f-b[P]-2-2*k),d.GC$(i).css([ja,$a,bb,ib,ab,H+"-width",H+"-style",H+"-color",H+"-radius"],[ka,e,f,g,h,k,l,m,n])}zb=function(){function a(a,b){this.XR="";var c=this;c.YR=a,c.name=a?a.name():"",c.kj=b,c.RB=!1,c.ZR=!1}return a.prototype.gV=function(a){var b,c,e,f,g,h,i=d.GC$(a.target).attr("resizeDirection");if(i)return i;if(b=d.GC$(this.hS).offset(),c=a.pageX-b.left,e=a.pageY-b.top,f=this.pmb)for(g=0;g<f.length;g++)if(h=f[g],h.rect.contains(c,e))return h.direction},a.prototype.smb=function(a){var b,c,e,f,g,h,i=a.target,j=d.GC$(i).attr("resizeDirection");if(j)return i.style.cursor;if(b=d.GC$(this.hS).offset(),c=a.pageX-b.left,e=a.pageY-b.top,f=this.pmb)for(g=0;g<f.length;g++)if(h=f[g],h.rect.contains(c,e))return h.cursor},a.prototype.AD=function(b){var c,e,f,g,h,i,j,k,l,m,n,o=this,p=o.kj,q=o.YR,r=p.ITa.Ky,s=o.WE,t=o.aS;if(p.ER(q)&&p.endEdit()&&(p.zt(!0),c={e:b,r:u},a.ao(o,"preProcessMouseDown",c),!c.r)){if(0===b.button||2===b.button){if(e=q.isSelected(),b.ctrlKey||b.shiftKey?q.isSelected(!e):e||(p.uQ(),q.isSelected(!0)),e||d.Vl.Zl(p),r&&r.vQ(),p.EJ(),p.hm&&p.hm(),2===b.button)return;if(f=p.mm,g=p.Vs(),h=o.gV(b),i=new d.Point(b.pageX-g.left,b.pageY-g.top),j=uc(o,h),!q.allowMove()&&(!q.allowResize()||j.inMoving))return;o.$F=i,o.pS=!0,o.bS=j,j.inMoving?(k={},k.startTopRow=p.getViewportTopRow(s),k.startLeftColumn=p.getViewportLeftColumn(t),k.startX=i.x,k.startY=i.y,o.eS=k):(Hc(o),l={},m=sc(o.cS),l.startX=m.x,l.startY=m.y,l.startWidth=m[O],l.startHeight=m[P],l.startTopRow=p.getViewportTopRow(s),l.startLeftColumn=p.getViewportLeftColumn(t),l.cursor=o.smb(b),l.direction=h,o.$R=l),Ic(o),n=p.hitTest(i.x,i.y),f.rG={KG:n.rowViewportIndex,MG:n.colViewportIndex,sG:n.hitTestType},f.$F=i,f.qG(),f.O3=!0,p.Wq(d.Events.FloatingElementSelected,{type:"floatingObject"})}return!1}},a.prototype.BD=function(b){var c,e,f,g,h,i,j,k=this,l=k.kj,m=k.hS,n=k.iS;if(l.ER(k.YR))return c={e:b,r:u},a.ao(k,"preProcessMouseMove",c),c.r?(e=c.cursor,void(e&&d.GC$(m).css(nb,e))):(f=k.YR,g=f.isLocked()&&l.options.isProtected,d.GC$(m).css(nb,g||!f.allowMove()?"default":E),l.dG?(m&&d.GC$(m).css(nb,"default"),!0):(h=l.mm,i=l.Vs(),j=new d.Point(b.pageX-i.left,b.pageY-i.top),k.pS&&(k.bS.inMoving?(k.qS=!0,Hc(k),Cc(k,k.$F,j),d.GC$(n).css(nb,E)):k.YR.allowResize()&&(k.kv=!0,xc(k),d.GC$(n).css(nb,k.$R[nb])),(k.qS||k.kv)&&(k.$F=j,h.$F=j,h.NG(),d.GC$(s.body).addClass(ma).attr(F,C))),!1))},a.prototype.CD=function(b){var c,e,f,g,h,i=this,j=i.kj,k=j.mm,l=i.YR,m=i.hS,n=i.bS;if(j.ER(l)&&(c={e:b,r:u},a.ao(i,"preProcessMouseUp",c),!c.r))return j.dG?(d.GC$(m).css(nb,E),!0):(k.O3=!1,k.RG(),e=j.Vs(),f=new d.Point(b.pageX-e.left,b.pageY-e.top),g=i.eS,g&&(g.endX=f.x,g.endY=f.y),n&&(h=l.fixedPosition(),n.inMoving&&i.qS?(h&&Ec(i),b.ctrlKey?zc(i):Ac(i)):i.kv&&(h&&Fc(i),Bc(i)),d.GC$(s.body).removeClass(ma)),i.rS(),!1)},a.prototype.rS=function(){var a=this;a.$R=u,a.eS=u,a.dS=u,d.GC$(a.iS).remove(),a.cS=u,a.pS=!1,Jc(a),a.qS=!1,a.kv=!1},a.prototype.yl=function(a,b){var c,e,f,g,h,i,j,k=this,l=k.YR,m=k.kj;if(l){if(c=!1,k.sS=a,e=k.hS,!e){if(a.width<=0||a.height<=0)return;e=k.hS=Gc(k),Wb(l,e),f=m.parent,f&&(g=k.Ks=f.xv(),g&&d.GC$(g).append(e))}h=k.kS,h||(h=k.kS=l.WR(k.WE,k.aS),"1"===l.typeName?(k.lS=p(B),d.GC$(k.lS).addClass(ma+" "+ra).attr(F,C),e.appendChild(k.lS),k.lS.appendChild(h)):(e.appendChild(h),c=!0)),k.lS&&(Pc(k,b,a),b.x=0,b.y=0),i=d.GC$(h),d.GC$(e).css([bb,$a,O,P],[a.y,a.x,a[O],a[P]]),i.css([bb,$a,O,P],[b.y,b.x,b[O],b[P]]),k.Tka=a,l.refreshContent&&l.refreshContent(h),k.lS&&Oc(k),l.isSelected()?(i.removeClass(oa).addClass(na),l.allowResize()?Kc(k,e):k.ZR&&Nc(k,e)):(i.removeClass(na).addClass(oa),Nc(k,e)),c&&(j=h.firstChild,m.Wq(d.Events.FloatingObjectLoaded,{sheet:m,sheetName:m.name(),floatingObject:l,element:j}))}},a.prototype.no=function(){var b=this,c=b.YR,e=b.hS,f=b.iS,g=b.kS;a.ao(b,"dispose"),g&&c.VR&&(c.VR(g),c.no(!1)),e&&(d.GC$(e).remove(),b.hS=u),f&&(d.GC$(f).remove(),b.iS=u)},a.mS=7,a}(),b.FloatingObjectRender=zb,d.Zn(zb);function Qc(a){a.zR().forEach(function(a){a.dynamicMove()?Pb(a):Tb(a),a.dynamicSize()?cc(a):Ub(a)})}function Rc(a,b,c){var e,f,g,h,i,j,k,l=a.kj,m=l.it(c),n=0,o=0,p=0,q=0;return m&&m.length>0&&(e=m[0],f=m[m.length-1],n=e.x,p=f.x+f[O]-n),g=l.jt(b),g&&g.length>0&&(h=g[0],i=g[g.length-1],o=h.y,q=i.y+i[P]-o),j=l.am(),k=j.Ft(b,c),k.getIntersect(n,o,p,q)||new d.Rect(0,0,0,0)}function Sc(a,b,c,d){var e,f,g,h,i,j,k,l,m=a,n=m.kj,o=new r,p=n.wr;return p.isNeedToUpdateLayout&&(Qc(p),p.isNeedToUpdateLayout=!1),e=n.am(),f=n.ss,g=n.it(c),h=n.jt(b),g.length<=0||h.length<=0?o:(i=g[0],j=g[g.length-1],k=h[0],l=h[h.length-1],p.zR().forEach(function(a){var b,c,m,p,q,r,s,t,u,v,w,x,y,z,A;if(a&&a.isVisible()){if(b=void 0,c=void 0,m=a[R](),p=a[T](),q=a[W](),r=a[Y](),a.fixedPosition())s=a.position(),b=s.x*d+e.kt,c=s.y*d+e.nt;else{if(t=g.findCol(p))b=t.x;else if(p<i.col)for(b=i.x,u=i.col-1;u>=p;u--)b-=f._m(u);else for(b=j.x+j[O],v=j.col+1;v<=p;v++)b+=f._m(v);if(b+=a[U]()*d,w=h.findRow(m))c=w.y;else if(m<k.row)for(c=k.y,x=k.row-1;x>=m;x--)c-=f.Sl(x);else for(c=l.y+l[P],y=l.row+1;y<=m;y++)c+=f.Sl(y);c+=a[S]()*d}z=void 0,A=void 0,z=a.wxb(d),A=a.xxb(d),Tc(n,m,p,q,r)||o.push({name:a.name(),x:b,y:c,width:z,height:A})}}),o)}function Tc(a,b,c,d,e){var f,g,h;if(a.rowOutlines)for(f=!0,h=b;h<=d;h++)if(!a.rowOutlines.isCollapsed(h)){f=!1;break}if(a.columnOutlines)for(g=!0,h=c;h<=e;h++)if(!a.columnOutlines.isCollapsed(h)){g=!1;break}return f||g}Ab=function(){function a(a){this.kj=a,this.jS=new r}return a.prototype.no=function(){var a=this.jS;a.each(function(a){a.no()}),a.empty()},a.prototype.yl=function(a,b,c){var e,f,g,h,i,j,k,l,m,n;if(!(a<0||b<0)){for(e=this,f=e.kj,g=e.jS,h=f.wr,i=Sc(e,a,b,c),j=[],g.each(function(a){i.find(a.name)||j.push(a)}),l=j.length,k=0;k<l;k++)m=j[k],m.gS=u,g.remove(m.name),m.no();n=f.parent&&f.parent.options.useTouchLayout,zb.mS=n?11:7,i.each(function(c){var i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,z,A,B=h.NR(c.name),C=x(zb.mS/2+1);B&&"1"===B.typeName&&(i=B.borderWidth(),j=B.borderStyle(),dc(j)||(i=0),i>C&&(C=i)),k=Rc(e,a,b),l=1,m=c.x-C-l,n=c.y-C-l,o=c[O]+2*C+2*l,p=c[P]+2*C+2*l,q=y(k.y,n),r=y(k.x,m),s=p,t=o,u=C,v=C,w=-1*C-l,u=n-q+u,v=m-r+v,u<=0?(s+=u,s+=w):u<C&&u>0&&(s-=C-u),v<=0?(t+=v,t+=w):v<C&&v>0&&(t-=C-v),z=g.find(c.name),z||(z=new zb(h.NR(c.name),f),z.gS=e,g.push(z)),z._R=c,z.WE=a,z.aS=b,A=new d.Rect(k.x,k.y,k[O],k[P]),z.yl(new d.Rect(r,q,t,s).getIntersectRect(A)||new d.Rect(r,q,0,0),new d.Rect(v,u,c[O],c[P]))})}},a}(),Bb={init:function(){var a=this,b=a.$oa=[];a.Zoa=function(a){b.forEach(function(b){b.Wva&&b.Wva(a)})},a.wr=a.ITa.wr,a.pictures=new rb(a,"1"),a.floatingObjects=new rb(a,"0")},dispose:function(a){var b=this;Eb(b,a.clearCache),d.GC$.each(b.wr.zR(),function(b,c){c.no(a.clearCache)}),d.GC$(b.Ws()).unbind("mousedown.fos"),b.unbind(d.Events.TableFiltered+Ra),b.unbind(d.Events.RangeFiltered+Ra),b.unbind(d.Events.FloatingElementSelected+Ra)},setHost:function(a){if(a){var b=this;a.bind("mousedown"+Ra,function(){b.uQ()}),b.bind(d.Events.TableFiltered+Ra,function(){b.AR()}),b.bind(d.Events.RangeFiltered+Ra,function(){b.AR()}),b.bind(d.Events.FloatingElementSelected+Ra,function(a,c){"floatingObject"!==c.type&&b.uQ()})}},onLayoutChanged:function(a){var b=a.changeType,c=a.row,d=a.rowCount,e=a.col,f=a.colCount,g=this.wr;"addRows"===b?Fb(g,c,d):"deleteRows"===b?Gb(g,c,d):"addColumns"===b?Hb(g,e,f):"deleteColumns"===b&&Ib(g,e,f)},paint:function(a){var b=this,c=a.clipRect;b.QP||(b.IR=function(a,c){var d,e;return b.FR||(b.FR=[]),d=b.FR[a],d||(b.FR[a]=[]),e=b.FR[a][c],e||(b.FR[a][c]=new Ab(b)),e=b.FR[a][c]},b.QP=function(a,b){var c,d,e,f,g,h=this;if(!h.fI&&h.wr)for(c=h.am(),d=void 0,b!==u&&void 0!==b||(b=h.zoom()),e=0;e<=2;e++)for(f=0;f<=2;f++)d=c.Ft(e,f),d&&(g=h.IR(e,f),g&&g.yl(e,f,b))}),b.QP(c)},lastNonNullRowAndCol:function(){var a=0,b=0,c=this.wr;return c&&c.zR().forEach(function(c){c&&(c[W]()>a&&(a=c[W]()),c[Y]()>b&&(b=c[Y]()))}),{lastNonNullRow:a,lastNonNullCol:b}},toJson:function(a,b){var c,d=b&&b.ignoreStyle;d||(c=this.wr.toJSON(),c.length>0&&(a.floatingObjects=c))},fromJson:function(a,b,c){var d,e,f,g=c&&c.ignoreStyle;g||(d=this,d.wr=new sb(d),d.ITa.wr=d.wr,e=a&&a.floatingObjectArray,f=b?e&&e.floatingObjects:a.floatingObjects,f&&d.wr.fromJSON(f,b))},preProcessMouseDown:function(){this.uQ()},processKeyDown:function(a){var b,c,d,e,f=this.wr;if(f)for(b=f.zR(),c=void 0,d=b.length,c=0;c<d;c++)if(e=b[c],e.isSelected())return void(a.r=!0)},onGroupChanged:function(a){this.floatingObjects.tTa(a.start,a.end,a.isRow),this.pictures.tTa(a.start,a.end,a.isRow)}},d.Worksheet.$n(J,Bb),Cb={init:function(){d.Commands.JR(this.commandManager())}},d.Workbook.$n(J,Cb),d.GC$.extend(d.lUa.prototype,{BUa:function(a,b,c){var d,e=this.zTa;e&&(d=e.CUa,d||(d=e.CUa=[]),d.push({type:"property",floatingObject:a,name:b,value:c}))},AUa:function(){var a,b,c,e=this.zTa;e&&(a=e.CUa,a||(a=e.CUa=[]),b={},c=this.wr,d.GC$.each(c.QR,function(a,c){b[a]=c}),a.push({type:"collection",floatingObjects:b}))},DUa:function(a){var b,c,d,e,f,g,h;if(a)for(b=this.wr,c=a.length-1;c>=0;c--)d=a[c],"collection"===d.type?b.QR=d.floatingObjects:"property"===d.type&&(e=["startRow","startRowOffset","startColumn","startColumnOffset","endRow","endRowOffset","endColumn","endColumnOffset"],f=d.floatingObject,g=d.name,h=d.value,f[g](h),e.indexOf(g)>=0&&(Pb(f),cc(f),f.sheet().$p()))}}),d.lUa.$n(J,{init:function(){this.wr=new sb(this.kj)},undo:function(a){var b=a.CUa;b&&this.DUa(b)}})},"./dist/plugins/floatingObject/floatingobject.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/floatingObject/floatingobject.res.en.js");b.SR={en:d}},"./dist/plugins/floatingObject/floatingobject.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_FloatingObjectHasSameNameError="The current worksheet already has a floating object with the same name.",b.Exp_FloatingObjectNameEmptyError="Floating object must have name"},"./dist/plugins/floatingObject/picture.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("./dist/plugins/floatingObject/floatingobject.js"),f=d.Ul.Nl,g=d.Ul.Pl,h=d.GC$,i=null,j=void 0,k="src",l="backColor",m="pictureStretch",n="borderRadius",o="borderWidth",p="borderStyle",q="borderColor",r="noFill",s=[k,l,n,o,p,r,q,m];function x(a){return"number"==typeof a&&!isNaN(a)}e.FloatingObject.ozb=function(a,b,c,d,e,f){return new t(a,b,c,d,e,f)},t=function(a){w(b,a);function b(b,c,d,e,f,g){var h=a.call(this,b,d,e,f,g)||this,j=h;return j.typeName="1",j.g3="1",j.bt=i,j.oS=!1,j.JS=!1,"string"==typeof c&&(j.JS=!f||!g,j.src(c)),h}return b.prototype.onPropertyChanged=function(a,b,c){var d,e=this;e.TR||(d=e.sheet(),d&&(d.ITa.BUa(e,a,c),this.Wq({sheet:d,sheetName:d.name(),picture:e,propertyName:a})))},b.prototype.toJSON=function(){var b,c,d,f=this,g=a.prototype.toJSON.call(this);return e.toJsonFn.call(f,s,g),f.tmb&&(g.rot=f.tmb),b=f.rmb,b&&(c=b.left,d=b.top,g.srcRect={l:c,t:d,r:1-c-b.width,b:1-d-b.height}),delete g.content,g},b.prototype.fromJSON=function(b,c){var d,f,g,h;b&&(d=this,d.TR=!0,a.prototype.fromJSON.call(this,b,c),e.fromJsonFn.call(d,s,b,c),b.rot&&(d.tmb=b.rot),d.oS=!1,f=b.srcRect,f&&(g=f.l,h=f.t,d.rmb={left:g,top:h,width:1-g-f.r,height:1-h-f.b}),y(d),d.TR=j)},b.prototype.clone=function(){var a=new b,c=JSON.stringify(this.toJSON());return a.fromJSON(JSON.parse(c)),a},b.prototype.getOriginalWidth=function(){return this.KS},b.prototype.getOriginalHeight=function(){return this.LS},b.prototype.nS=function(a){var b=["dotted","dashed","solid","double","groove","ridge","inset","outset"];return b.indexOf(a)>=0},b.prototype.WR=function(a,b){var c=f("div");return h(c).addClass("gc-floatingobject-content-container").addClass("gc-no-user-select").attr("unselectable","on").css("position","absolute"),this.UR(c,a,b),c},b.prototype.Wq=function(a){var b,c=this.sheet();c&&(c.Wq(d.Events.PictureChanged,a),"isSelected"===a.propertyName&&(b={sheet:a.sheet,sheetName:a.sheetName,picture:a.picture},c.Wq(d.Events.PictureSelectionChanged,b)))},b.prototype.no=function(a){var b=this;b.bt&&a!==!1&&(b.bt.no(),b.bt=i)},b}(e.FloatingObject),b.Picture=t;function y(a){var b,c,e=a,f=e.src(),g=e.sheet();e.bt||(e.bt=new d.oo(function(){y(e)})),b=e.bt;try{b.ko(f)?(e.oS=!0,c=b.lo(f),e.KS=c.width,e.LS=c.height,e.tmb&&e.tmb%90===0&&(e.qmb=z(c,c.width,c.height,e.tmb)),e.JS&&(e.width(c.width,!0),e.height(c.height,!0),e.JS=!1),e.isVisible()&&g&&g.repaint()):b.fo(f)}catch(a){}}function z(a,b,c,d){var e,f,g=b,h=c;return 90!==d&&270!==d||(g=c,h=b),e=document.createElement("canvas"),e.width=g,e.height=h,f=e.getContext("2d"),f.translate(g/2,h/2),f.rotate(d/180*Math.PI),f.translate(-g/2,-h/2),f.drawImage(a,0,0,b,c,(g-b)/2,(h-c)/2,b,c),f.setTransform(1,0,0,1,0,0),e.toDataURL()}u=[[k,j,function(a,b){var c=this;c.oS=!1,y(c),c.srccallback||(c.srccallback=e.propertyRefreshCallback(k)),c.srccallback.call(c,a,b)}],[l,i,e.propertyRefreshCallback(l)],[n,-1,e.propertyRefreshCallback(n),x],[o,1,e.propertyRefreshCallback(o),x],[p,"none",function(a,b){var c=this;c.nS(a)||c.borderWidth(0),c.bscallback||(c.bscallback=e.propertyRefreshCallback(p)),c.bscallback.call(c,a,b)}],[r,j],[q,i,e.propertyRefreshCallback(q)],[m,0,e.propertyRefreshCallback(m)]],v=t.prototype,h.each(u,function(a,b){v[b[0]]=g(b[0],b[1],b[2],b[3])})},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets},SheetsCalc:function(a,b){a.exports=GC.Spread.Sheets.CalcEngine},Touch:function(a,b){a.exports=GC.Spread.Sheets.Touch}});