<!--
 * @Description: 预警管理-操作栏新增按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-15 17:04:10
-->
<template>
  <div>
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" size="small" @click="btClick">人员查看</a-button>
    <!-- 弹窗 -->
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal-person.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      const record = this.record;
      // const record = {"createdBy":null,"createdDate":null,"modifiedBy":null,"modifiedDate":null,"remark":null,"id":"48854","sendType":"1","indexName":"不良率","businessSegments":"质量","triggers":"1","role":"视像科技=视像科技=A2车间","receive":"122 122 122 122 122  liupingli yangman2 wangshengzhen.ex ","triggersName":"最近3小时（滚动）内，同一机型同一故障现象达3例","classKeyindexName":"visibleindexName16913894424491684210831000","classKeytriggersName":"visibletriggersName16913894424521684210831000","classKeybusinessSegments":"visiblebusinessSegments16913894424531684210831000","classKeysendType":"visiblesendType16913894424571684210831000","classKeyrole":"visiblerole16913894424591684210831000","classKeyreceive":"visiblereceive16913894424621684210831000"};
      // console.log(JSON.stringify(record));
      this.$refs["modal"].show(record);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {}
      });
    }
  }
};
</script>
