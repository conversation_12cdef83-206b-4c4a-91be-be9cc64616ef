<template>
  <!-- 方案一 -->
  <!-- <a-tree-select
    v-model="value"
    :tree-data="ytOrgList"
    @select="ytTreeSelect"
  /> -->
  <!-- 方案二 -->
  <div>
    <!-- <a-input></a-input> -->
    <div
      :title="orgText ? orgText : '点击选择组织'"
      style="line-height: 40px;display: flex;align-items: center;height: 40px;"
    >
      <div
        class="input"
        :class="[disabled ? 'disabled' : '']"
        @click="divClick"
      >
        {{ orgText }}
      </div>
    </div>
    <a-modal title="组织选择" :visible="show" @cancel="show = false" @ok="onOk">
      <div class="date-picker">
        <a-date-picker
          :value="searchTime"
          placeholder="选择时间"
          style="flex: 1;"
          valueFormat="YYYY-MM-DD"
          @change="onDateChange"
          format="YYYY-MM-DD"
          :disabled-date="disabledDate"
        />
      </div>
      <div style="min-height: 400px;">
        <a-tree
          class="org-list-tree"
          blockNode
          :expanded-keys="ytExpandedKeys"
          :selected-keys="ytSelectedKeys"
          :tree-data="ytOrgList"
          @select="ytTreeSelect"
          @expand="onYTExpand"
        />
      </div>
    </a-modal>
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
import moment from "moment";
import uniqBy from "lodash/uniqBy";
import cloneDeep from "lodash.clonedeep";
const YTRquestUrl1 = "/api/smc2/treeOrg/searchTopLevelCM"; // 组织树顶级查询(右侧云图)
const YTRquestUrl2 = "/api/smc2/treeOrg/searchNextLevelCM"; // 组织树逐级查询 (右侧云图)
export default {
  props: {
    activeOrgTreeId: {
      require: true,
    },
    disabled: Boolean,
  },
  data() {
    return {
      show: false,
      // 云图组织层级相关数据
      value: "",
      searchTime: moment().format("YYYY-MM-DD"),
      orgText: "",
      ytDataLoading: false,
      ytExpandedKeys: [],
      ytSelectedKeys: [],
      ytOrgList: [],
      ytOrgPPList: [],
      ytOriginDataList: [],
      setTopOriginData: [],
    };
  },
  methods: {
    disabledDate(current) {
      // Can not select days before today and today
      return current && current > moment().endOf("day");
    },
    // 时间改变
    onDateChange(date) {
      // 清空数据
      this.searchTime = date;
      this.ytExpandedKeys = [];
      this.ytSelectedKeys = [];
      this.ytOrgList = [];
      this.ytOrgPPList = [];
      this.ytOriginDataList = [];
      this.$nextTick(() => {
        this.setTopOrgList(this.setTopOriginData);
      });
    },
    // // YT组织树选择 方案一
    // ytTreeSelect(value, node, extra) {
    //   this.value = extra.selectedNodes[0].data.key;
    //   console.log(this.value, extra);
    //   this.getYTOrgList(this.value);
    // },
    // YT组织树选择
    ytTreeSelect(selectedKeys) {
      this.ytSelectedKeys = selectedKeys;
      selectedKeys.length && this.getYTOrgList(selectedKeys[0]);
      console.log("this.ytSelectedKeys----->", this.ytSelectedKeys);
    },
    onYTExpand(expandedKeys) {
      this.ytExpandedKeys = expandedKeys;
    },
    divClick() {
      if (!this.disabled) {
        this.$emit("divClick");
      }
    },
    setOrgText(text) {
      this.orgText = text;
    },
    showDialog() {
      this.show = true;
    },
    clearData() {
      this.searchTime = moment().format("YYYY-MM-DD");
      this.orgText = "";
      this.ytExpandedKeys = [];
      this.ytSelectedKeys = [];
      this.ytOrgList = [];
      this.ytOrgPPList = [];
      this.ytOriginDataList = [];
    },
    onOk() {
      this.orgText = this.ytOrgPPList.filter(
        (item) => item.key === this.ytSelectedKeys[0]
      )[0].title;
      const fullCode = this.ytOrgPPList.filter(
        (item) => item.key === this.ytSelectedKeys[0]
      )[0].fullCode;
      this.$emit("choose", { dhrCode: this.ytSelectedKeys[0], fullCode });
      this.show = false;
    },
    // 设置第一层组织机构信息
    setTopOrgList(arr) {
      this.setTopOriginData = cloneDeep(arr); // 保存一下父级组件传值过来的数据，在时间选择框改变的时候使用
      let treeData = arr.map((item) => {
        return {
          title: `${item.name}(${item.code})`,
          fullCode: item.fullCode,
          value: item.code,
          key: item.code,
          children: [],
        };
      });
      this.ytOrgPPList = [...treeData, ...this.ytOrgPPList];
      this.ytOriginDataList = uniqBy(
        [...arr, ...this.ytOriginDataList],
        "code"
      );
      this.ytOrgList = treeData;
      treeData.length && this.getYTOrgList(treeData[0].key);
    },
    // 获取DHR组织结构列表 selfUse 需要和 dhrCode一块使用 selfUse 表示是否外部组件调用
    getYTOrgList(parentOrgCode, selfUse = true, dhrCode = "") {
      console.log("----->", parentOrgCode);
      // parentOrgCode 接收的是带版本号的code或者空 定义一个realCode来查找到数据的真实dhrCode不带版本号的
      let realCode = "";
      if (parentOrgCode && selfUse) {
        const ytOriginData = this.ytOriginDataList.filter(
          (item) => item.code === parentOrgCode
        )[0];
        console.log(ytOriginData);
        realCode = ytOriginData.code;
      }
      this.ytDataLoading = true;
      request(`${parentOrgCode ? YTRquestUrl2 : YTRquestUrl1}`, {
        method: "POST",
        body: parentOrgCode
          ? {
              superiorCode: selfUse ? realCode : dhrCode,
              fullCode: selfUse
                ? this.ytOrgPPList.filter(
                    (item) => item.key === parentOrgCode
                  )[0].fullCode
                : parentOrgCode,
              treeType: this.activeOrgTreeId,
              endDate: this.searchTime || "",
            }
          : {
              treeType: this.activeOrgTreeId,
              endDate: this.searchTime || "",
            },
      }).then((res) => {
        let treeData = res.map((item) => {
          return {
            title: `${item.name}(${item.code})`,
            fullCode: item.fullCode,
            value: item.code,
            key: item.code,
            children: [],
          };
        });
        this.ytOrgPPList = [...treeData, ...this.ytOrgPPList];
        this.ytOriginDataList = uniqBy(
          [...res, ...this.ytOriginDataList],
          "code"
        );
        console.log("this.ytOriginDataList---->", this.ytOriginDataList);
        if (!parentOrgCode) {
          this.ytOrgList = treeData;
          treeData.length && this.getYTOrgList(treeData[0].key);
        } else {
          if (this.ytOrgList.length) {
            // 展开节点
            this.treeDataDeepFillArr(this.ytOrgList, parentOrgCode, treeData);
            if (treeData.length) {
              this.ytExpandedKeys = [...this.ytExpandedKeys, parentOrgCode];
            }
          } else {
            this.ytOrgList = treeData;
          }
        }
        console.log("this.ytOrgList------>", this.ytOrgList);
        this.ytDataLoading = false;
      });
    },
    // 树形组件深层次查找并赋值
    treeDataDeepFillArr(treeData, orgCode, children) {
      for (let i = 0; i < treeData.length; i++) {
        const element = treeData[i];
        if (element.key === orgCode) {
          treeData[i].children = children;
          break;
        } else {
          this.treeDataDeepFillArr(element.children, orgCode, children);
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.input {
  cursor: pointer;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: "tnum";
  position: relative;
  display: inline-block;
  width: 100%;
  height: 32px;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  &.disabled {
    color: rgba(0, 0, 0, 0.25);
    background: #f5f5f5;
    cursor: not-allowed;
  }
}
</style>
