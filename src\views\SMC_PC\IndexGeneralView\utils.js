/*
 * @Description: 工具
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 16:15:03
 * @LastEditors: y<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-09-10 15:10:47
 */
import Decimal from "decimal.js";

/**
 * @description: 千位分割
 * @param {Number} num 要分割的数字
 * @return {String}
 */
export function thousandSplit(num) {
  var reg = /\d{1,3}(?=(\d{3})+$)/g;
  if (String(num).includes(".")) {
    const arr = String(num).split(".");
    return (arr[0] + "").replace(reg, "$&,") + "." + arr[1];
  } else {
    return (num + "").replace(reg, "$&,");
  }
}

/**
 * @description: 处理千位展示的数据
 * @param {Number} data 要处理的数据
 * @param {String} indexUnit 指标单位
 * @return {String} displayData 要展示的值
 */

export function dealThousandData(
  data,
  indexUnit,
  precisions,
  openThousandSplit = true
) {
  let fixedNum = precisions === 0 ? 0 : precisions || 2;
  if (typeof precisions === "string") {
    fixedNum = +precisions;
  }
  if ([null, undefined, "null", "undefined"].includes(data)) {
    return `-`;
  } else {
    let value = new Decimal(data).mul(
      new Decimal(indexUnit === "%" ? 100 : indexUnit === "PPM" ? 1000000 : 1)
    );
    value = value.toFixed(fixedNum, Decimal.ROUND_HALF_UP);
    return openThousandSplit ? thousandSplit(value) : value;
  }
}

// 渲染到页面的style内联样式
export function styleObject(data, cardItem) {
  const indexType = cardItem.dwppCmTfIndexLibrary?.indexType;
  let style = {
    width: 0,
    height: 0,
    "border-right": "5px solid transparent",
    "border-left": "5px solid transparent",
    position: "relative",
    "margin-right": "4px"
  };
  if (data) {
    if (indexType === "正向") {
      if (data.includes("-")) {
        style["border-top"] = "5px solid #6495f9";
        style["top"] = "10px";
      } else {
        style["border-bottom"] = "5px solid #f75050";
        style["top"] = "-10px";
      }
    } else {
      if (data.includes("-")) {
        style["border-bottom"] = "5px solid #f75050";
        style["top"] = "-10px";
      } else {
        style["border-top"] = "5px solid #6495f9";
        style["top"] = "10px";
      }
    }
    return style;
  } else {
    return {};
  }
}

// 获取当前第几周
export function getYearWeek(dateTime = new Date()) {
  // 获取从1970年到现在的时间毫秒数
  var temp_ms = dateTime.getTime();
  let temptTime = new Date(temp_ms);
  // 今天周几，如果是周日，则设为7
  let weekday = temptTime.getDay() & 7;
  // 周1+5天=周六，得到本周6的日期,之所以以每周末的日期为基准，不能用每周日的日期为基准来计算
  // 当前日期的周六的日期
  temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
  // 每年的第一天，年/1/1，参数之中，0代表月份，介于0(1月) ~11(12月)之间的整数，getDay获取星期几同理
  // 第一天的日期
  let firstDay = new Date(temptTime.getFullYear(), 0, 1);
  let dayOfWeek = firstDay.getDay();
  let spendDay = 1;
  // 如果第一天不是星期日，那么就找到下一个星期日作为开始
  if (dayOfWeek != 0) {
    spendDay = 7 - dayOfWeek + 1;
  }
  let yearOfW = temptTime.getFullYear();
  firstDay = new Date(yearOfW, 0, 1 + spendDay);
  /*
    1.Math.ceil 取大于等于所给值的最小整数
    2.86400000是换算到天的基数，js的时间差值为时间戳，即毫秒数
      1000毫秒 * 60秒 * 60分钟* 24小时 = 86400000
    3.temptTime是当前日期，firstDay是当年第一天，周数计算公式就是（当前日期-第一天天数）/7 就是本年的第几周
    4.d是差距天数，res是周数
  */
  let d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
  let res = Math.ceil(d / 7) + 1;
  return res;
}
