<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2022-07-29 17:39:56
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    title="批量更新"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="mainForm"
      :model="mainForm"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row>
        <a-col :span="8" v-for="(item, index) in mainFormArray" :key="index">
          <a-form-model-item
            :label="subitem.label"
            v-for="subitem in item"
            :key="subitem.key"
            :prop="subitem.key"
          >
            <a-select
              v-model="mainForm[subitem.key]"
              :placeholder="`请选择${subitem.label}`"
              allowClear
              :dropdownMatchSelectWidth="false"
            >
              <template v-if="subitem.isPangeaDICT">
                <a-select-option
                  :value="item.key"
                  v-for="item in dict[subitem.dict] || []"
                  :key="item.key"
                >
                  {{ item.value }}
                </a-select-option>
              </template>
              <template v-else>
                <a-select-option
                  :value="item.id"
                  v-for="item in dict[subitem.dict]"
                  :key="item.val"
                >
                  {{ item.val }}
                </a-select-option>
              </template>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="责任人" prop="fillInPerson">
            <a-select
              show-search
              :value="mainForm.fillInPerson"
              :dropdownMatchSelectWidth="false"
              placeholder="请输入责任人名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleAccountSearch($event, 'fillInPerson')"
              @change="handleAccountChange($event, 'fillInPerson')"
            >
              <a-select-option
                v-for="d in fillInPersonList"
                :key="`${d.name}-${d.account}`"
                :value="`${d.name}-${d.account}`"
              >
                {{ d.name }}({{ d.account }}) {{ d.o }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="分管领导" prop="fillInPersonLeader">
            <a-select
              show-search
              :value="mainForm.fillInPersonLeader"
              :dropdownMatchSelectWidth="false"
              placeholder="请输入分管领导名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleAccountSearch($event, 'fillInPersonLeader')"
              @change="handleAccountChange($event, 'fillInPersonLeader')"
            >
              <a-select-option
                v-for="d in fillInPersonLeaderList"
                :key="`${d.name}-${d.account}`"
                :value="`${d.name}-${d.account}`"
              >
                {{ d.name }}({{ d.account }}) {{ d.o }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
import cloneDeep from "lodash.clonedeep";
export default {
  data() {
    this.handleAccountSearch = debounce(this.handleAccountSearch, 800);
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      mainForm: {
        classCompareDesc: "",
        isDelete: "",
        isManually: null,
        fillInPerson: "",
        fillInPersonLeader: ""
      },
      mainFormArray: [
        [
          {
            key: "isPartSum",
            label: "是否参与累加",
            dict: "smc-Y/N",
            isPangeaDICT: true
          },
          {
            key: "isFromSum",
            label: "是否由累加计算",
            dict: "smc-Y/N",
            isPangeaDICT: true
          },
          {
            key: "formulaId",
            label: "实际值计算公式",
            dict: "10",
            isPangeaDICT: false
          },
          {
            key: "contemFormulaId",
            label: "同期值计算公式",
            dict: "18",
            isPangeaDICT: false
          },
          {
            key: "previousFormulaId",
            label: "上期值计算公式",
            dict: "19",
            isPangeaDICT: false
          },
          {
            key: "sumTargetFormulaId",
            label: "累计目标完成率计算公式",
            dict: "25",
            isPangeaDICT: false
          },
          {
            key: "isDelete",
            label: "是否冻结",
            dict: "smc-Y/N",
            isPangeaDICT: true
          },
          {
            key: "indexUnitId",
            label: "指标单位",
            dict: "2",
            isPangeaDICT: false
          }
        ],
        [
          {
            key: "contemRateFormulaId",
            label: "同比变化率计算公式",
            dict: "16",
            isPangeaDICT: false
          },
          {
            key: "previousRateFormulaId",
            label: "环比变化率计算公式",
            dict: "17",
            isPangeaDICT: false
          },
          {
            key: "sumValueFormulaId",
            label: "累计实际值计算公式",
            dict: "15",
            isPangeaDICT: false
          },
          {
            key: "sumContemFormulaId",
            label: "累计同期值计算公式",
            dict: "20",
            isPangeaDICT: false
          },
          {
            key: "sumPreviousFormulaId",
            label: "累计上期值计算公式",
            dict: "21",
            isPangeaDICT: false
          },
          {
            key: "sumTargetValueWayId",
            label: "累计目标值获取方式",
            dict: "13",
            isPangeaDICT: false
          },
          {
            key: "classCompareDesc",
            label: "对应层级描述",
            dict: "9",
            isPangeaDICT: false
          }
        ],
        [
          {
            key: "sumRatioFormulaId",
            label: "累计占比计算公式",
            dict: "22",
            isPangeaDICT: false
          },
          {
            key: "sumContemRateFormulaId",
            label: "累计同比变化率计算公式",
            dict: "23",
            isPangeaDICT: false
          },
          {
            key: "sumPreviousRateFormulaId",
            label: "累计环比变化率计算公式",
            dict: "24",
            isPangeaDICT: false
          },
          {
            key: "targetValueWayId",
            label: "目标值获取方式",
            dict: "12",
            isPangeaDICT: false
          },
          {
            key: "targetFormulaId",
            label: "目标完成率计算公式",
            dict: "11",
            isPangeaDICT: false
          },
          {
            key: "isManually",
            label: "是否手工填报",
            dict: "smc-Y/N",
            isPangeaDICT: true
          },
          {
            key: "isCalculate",
            label: "是否展示",
            dict: "smc-Y/N",
            isPangeaDICT: true
          }
        ]
      ],
      isEdit: false, // 是否编辑状态
      dict: {}, // 字典
      fillInPersonList: [],
      fillInPersonLeaderList: [],
      ids: ""
    };
  },
  methods: {
    handleAccountSearch(value, key) {
      this.getAcountList(value).then(res => {
        this[`${key}List`] = res;
      });
    },
    handleAccountChange(value, key) {
      this.mainForm[key] = value;
    },
    getAcountList(account) {
      return new Promise(resolve => {
        request(`/api/smc2/ldap/searchLdapUser`, {
          method: "POST",
          body: {
            account
          }
        }).then(res => {
          resolve(res || []);
        });
      });
    },
    // 获取码值表数据
    getAllDict() {
      request("/api/smc2/codeValue/searchAllValue").then(res => {
        if (res[2]) {
          this.$set(this.dict, 2, res[2]);
        }
        for (let i = 9; i <= 25; i++) {
          for (const key in res) {
            if (Object.hasOwnProperty.call(res, key)) {
              const element = res[key];
              if (i.toString() === key) {
                // this.dict[key] = element;
                this.$set(this.dict, key, element);
              }
            }
          }
        }
      });
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-Y%2FN&languageCode=zh_CN"
        )
      ).then(res => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    show(selectedRowKeys) {
      this.ids = (selectedRowKeys || []).join(",");
      this.visible = true;
      this.isEdit = false;
      this.getDICT();
      this.getAllDict();
    },
    close() {
      this.mainForm = {
        classCompareDesc: "",
        isDelete: "",
        isManually: null,
        fillInPerson: "",
        fillInPersonLeader: ""
      };
      this.ids = "";
      this.fillInPersonList = [];
      this.fillInPersonLeaderList = [];
      this.$refs.mainForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    // 提交
    submit() {
      this.$refs.mainForm.validate(async valid => {
        if (valid) {
          let postData = cloneDeep({ ...this.mainForm });
          postData["ids"] = this.ids;
          request(`/api/smc2/attributes/batchUpdate`, {
            method: "POST",
            body: postData
          }).then(() => {
            // if (res.result === "success") {
            this.close();
            this.$emit("fetchData");
            // } else {
            //   this.$message.error(res.result);
            // }
          });
        }
      });
    }
  }
};
</script>
