/* * @Description: 研发大屏弹窗 * @Author: y<PERSON><PERSON><PERSON><PERSON> * @Date: 2022-05-07
15:58:35 * @LastEditors: yuyongjie * @LastEditTime: 2022-06-10 17:28:04 */
<template>
  <a-modal
    width="100%"
    v-model="visible"
    class="RD-bigScreen-modal"
    :footer="null"
    @cancel="close"
    :closable="false"
  >
    <slot name="title"></slot>
    <a-spin :spinning="reasonLoading">
      <a-button type="primary" block @click="close" class="closeBtn">
        <a-icon type="left-circle" />
        返回上层
      </a-button>
      <!-- 卡片区域 -->
      <div class="charts-area">
        <!-- chart1 -->
        <div
          :class="[item.companyName === activeCompanyName ? 'active' : '']"
          v-for="item in topCardList"
          :key="item.companyName"
          @click="cardClick(item.companyName)"
        >
          <div class="_top">
            <div class="_l">
              <span class="title">{{ item.companyName }}</span>
              <div class="data-box">
                <div class="num">{{ item.currentMonthValue }}</div>
                <div class="dw">{{ item.salesUnit }}</div>
              </div>
              <div class="target">
                <span>目标值</span>
                <span>{{ item.planValue }}{{ item.salesUnit }}</span>
              </div>
            </div>
            <div class="chart" ref="chart1">
              <a-progress type="circle" :percent="item.completionRate">
                <template #format="percent">
                  <div>
                    <span>{{ item.completionRateDesc }}</span>
                    <span>完成率</span>
                  </div>
                </template>
              </a-progress>
            </div>
          </div>
          <div class="THB">
            <div>
              <span class="_title">同比</span>
              <template v-if="item.whetherYearOnYear">
                <span
                  :style="styleObject(item.yearOnYearRateDesc, item.indexType)"
                ></span>
                <span
                  :style="{
                    color: item.yearOnYearRate
                      ? item.indexType === '正向'
                        ? item.yearOnYearRateDesc.includes('-')
                          ? '#6495F9'
                          : '#f75050'
                        : item.yearOnYearRateDesc.includes('-')
                        ? '#f75050'
                        : '#6495F9'
                      : 'rgba(0, 0, 0, 0.8);'
                  }"
                >
                  {{ item.yearOnYearRateDesc }}
                </span>
              </template>
              <span
                v-if="!item.whetherYearOnYear"
                style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.1, 0.1, 0.1);font-size: 0.1rem;"
              >
                不对比
              </span>
            </div>
            <div>
              <span class="_title">环比</span>
              <template v-if="item.whetherChainRatioOnYear">
                <span
                  :style="styleObject(item.chainRatioRateDesc, item.indexType)"
                ></span>
                <span
                  :style="{
                    color: item.chainRatioRate
                      ? item.indexType === '正向'
                        ? item.chainRatioRateDesc.includes('-')
                          ? '#6495F9'
                          : '#f75050'
                        : item.chainRatioRateDesc.includes('-')
                        ? '#f75050'
                        : '#6495F9'
                      : 'rgba(0, 0, 0, 0.8);'
                  }"
                >
                  {{ item.chainRatioRateDesc }}
                </span>
              </template>
              <span
                v-if="!item.whetherChainRatioOnYear"
                style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.1, 0.1, 0.1);font-size: 0.1rem;"
              >
                不对比
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 图表区域 -->
      <div class="echart-area" v-if="completion.data.length">
        <div class="box">
          <div
            v-for="(item, index) in completion.data"
            :key="`${activeCompanyName}-${index}`"
          >
            <!-- 单平台/单型号平均销量两个指标 和 其他指标的completion.data第0组数据都用以下渲染模板 -->
            <template
              v-if="
                ['单平台平均销量', '单型号平均销量'].includes(indexName) ||
                  (indexName === 'TOP机型' && index === 0) ||
                  (indexName === '设计里程碑质量评审无条件通过率' &&
                    index === 0)
              "
            >
              <div class="title">
                {{ activeCompanyName }}
                <span style="color: #f75050;">{{
                  item[0] ? item[0].indexName : ""
                }}</span>
                内外销完成情况
              </div>
              <div
                class="chart"
                :ref="
                  `${activeCompanyName}-${
                    item[0] ? item[0].indexName : ''
                  }-${index}`
                "
              ></div>
            </template>
            <!-- TOP机型 并且 是第1和第2组数据用以下模板 -->
            <template
              v-else-if="indexName === 'TOP机型' && [1, 2].includes(index)"
            >
              <div class="title">
                {{ activeCompanyName }}
                <span style="color: #f75050;">{{
                  index === 1 ? "线上" : "线下"
                }}</span>
                TOP机型未达标机型（金额:万元）
              </div>
              <div class="table">
                <a-table
                  size="small"
                  :columns="topJXColumns"
                  :data-source="index === 1 ? topJXLeftData : topJXRightData"
                  :pagination="false"
                  :scroll="{ x: 480 }"
                />
              </div>
            </template>
          </div>
        </div>
        <div class="reasonAnalysis" v-html="completion.reasonAnalysis"></div>
      </div>
      <!-- 表格区域 -->
      <div
        class="table-area"
        v-if="['单平台平均销量', '单型号平均销量'].includes(indexName)"
      >
        <div
          class="container"
          v-for="(item, index) in distribution"
          :key="`${item.data[0].companyName}-${index}`"
        >
          <div class="box">
            <div>
              <div class="title">
                {{ item.titleSet[0] }}
              </div>
              <div class="table">
                <a-table
                  size="small"
                  :columns="leftCloumns"
                  :data-source="leftTableObject[`Data${index}`]"
                  :pagination="false"
                  :scroll="{ x: 560, y: 210 }"
                />
              </div>
            </div>
            <div>
              <div class="title">
                {{ item.titleSet[1] }}
              </div>
              <div
                class="bottomChart"
                :ref="
                  `${activeCompanyName}-${item.data[1][0].companyName}-${index}-bottom`
                "
              ></div>
            </div>
            <div>
              <div class="title">
                {{ item.titleSet[2] }}
                <img
                  src="./images/download.png"
                  style="display: inline-block;width: 22px;height: 22px;cursor: pointer;"
                  alt=""
                  title="导出数据"
                  srcset=""
                  @click="
                    genExcel(
                      {
                        data: leftTableObject[`Data${index}`],
                        title: item.titleSet[0]
                      },
                      {
                        data: rightTableObject[`Data${index}`],
                        title: item.titleSet[2]
                      }
                    )
                  "
                />
              </div>
              <div class="table">
                <a-table
                  size="small"
                  :columns="rightCloumns"
                  :data-source="rightTableObject[`Data${index}`]"
                  :pagination="false"
                  :scroll="{ x: 520, y: 210 }"
                />
              </div>
            </div>
          </div>
          <div class="reasonAnalysis" v-html="item.reasonAnalysis"></div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import * as echarts from "echarts";
import moment from "moment";
import request from "@/utils/requestHttp";
import { styleObject } from "./util";
export default {
  data() {
    return {
      visible: false,
      activeCompanyName: "",
      topCardList: [],
      indexName: "",
      time: "",
      completion: {
        data: [],
        reasonAnalysis: ""
      },
      distribution: [],
      styleObject,
      chartObject: {},
      chartOption: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["实际", "目标", "同期", "完成率"],
          top: 6,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisPointer: {
              type: "shadow"
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: "{value}"
            },
            // min: function(value) {
            //   return value.min;
            // },
            max: function(value) {
              return value.max;
            }
          },
          {
            type: "value",
            show: false,
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: "{value} %"
            },
            // min: function(value) {
            //   return value.min;
            // },
            max: function(value) {
              return value.max;
            }
          }
        ],
        series: [
          {
            name: "实际",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(35, 181, 177 , 0.85)"
            },
            // label: {
            //   show: true,
            //   position: "top",
            //   color: "rgba(89, 89, 89, 1)"
            // },
            barMaxWidth: 33
          },
          {
            name: "目标",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(245, 34, 45, 0.85)"
            },
            // label: {
            //   show: true,
            //   position: "top",
            //   color: "rgba(89, 89, 89, 1)"
            // },
            barMaxWidth: 33
          },
          {
            name: "同期",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(181, 181, 181, 0.85)"
            },
            // label: {
            //   show: true,
            //   position: "top",
            //   color: "rgba(89, 89, 89, 1)"
            // },
            barMaxWidth: 33
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: 1,
            data: [],
            // symbol: "none",
            label: {
              show: true,
              position: "right",
              distance: 20,
              color: "rgba(0, 0, 0, 1)",
              formatter: "完成率\n{c}%"
              // backgroundColor: "inherit",
              // borderRadius: [2, 2, 2, 2]
            },
            itemStyle: {
              color: "rgba(242, 156, 150, 1)"
            }
          }
        ]
      },
      leftCloumns: [],
      leftTableObject: {},
      rightCloumns: [],
      rightTableObject: {},
      bottomChartOption: {
        tooltip: {
          trigger: "axis"
        },
        calculable: false,
        grid: {
          top: "12%",
          left: "1%",
          right: "1%",
          bottom: "45",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: []
          }
        ],
        yAxis: [
          {
            type: "value",
            name: "贡献度",
            axisLabel: {
              formatter: "{value}%"
            }
          }
        ],
        dataZoom: [
          {
            type: "slider",
            show: true,
            start: 0,
            end: 40,
            height: 16
          }
        ],
        series: [
          {
            name: "贡献度",
            type: "bar",
            data: [
              {
                value: 100,
                itemStyle: {
                  color: "#D84D3F"
                }
              },
              {
                value: 80,
                itemStyle: {
                  color: "#008888"
                }
              },
              {
                value: 50,
                itemStyle: {
                  color: "#FF978C"
                }
              },
              {
                value: 40,
                itemStyle: {
                  color: "#57C7C7"
                }
              },
              {
                value: 10,
                itemStyle: {
                  color: "#57C7C7"
                }
              }
            ]
          }
        ]
      },
      reasonLoading: true,
      topJXColumns: [
        {
          title: "TOP类别",
          dataIndex: "topCategory",
          key: "topCategory",
          width: 80
        },
        {
          title: "机型",
          dataIndex: "modelName",
          key: "modelName",
          align: "right",
          width: 80
        },
        {
          title: "排名",
          dataIndex: "rank",
          key: "rank",
          align: "right",
          width: 50
        },
        {
          title: "实际销额",
          key: "actualSales",
          dataIndex: "actualSales",
          align: "right",
          width: 80
        },
        {
          title: "目标销额",
          key: "targetSales",
          dataIndex: "targetSales",
          align: "right",
          width: 80
        },
        {
          title: "销额差距",
          key: "salesGap",
          dataIndex: "salesGap",
          align: "right",
          width: 80
        }
      ], // TOP机型未达标机型表格列项
      topJXLeftData: [], // TOP机型未达标机型表格左侧数据
      topJXRightData: [] // TOP机型未达标机型表格右侧数据
    };
  },
  // mounted() {
  //   this.show({ indexName: "单平台平均销量", time: "2022-05" });
  // },
  methods: {
    cardClick(companyName) {
      for (const key in this.leftTableObject) {
        if (Object.hasOwnProperty.call(this.leftTableObject, key)) {
          this.leftTableObject[key] = [];
          delete this.leftTableObject[key];
        }
      }
      for (const key in this.rightTableObject) {
        if (Object.hasOwnProperty.call(this.rightTableObject, key)) {
          this.rightTableObject[key] = [];
          delete this.rightTableObject[key];
        }
      }
      this.reasonLoading = true;
      this.completion = {
        data: [],
        reasonAnalysis: ""
      };
      this.distribution = [];
      // 清空数据
      for (const key in this.chartObject) {
        if (Object.hasOwnProperty.call(this.chartObject, key)) {
          const element = this.chartObject[key];
          if (element) {
            element.dispose();
          }
          this.chartObject[key] = null;
          delete this.chartObject[key];
        }
      }
      this.topJXLeftData = [];
      this.topJXRightData = [];
      this.activeCompanyName = companyName;
      this.getReasonData();
    },
    // 获取顶部4个卡片
    getTopCardList() {
      request(
        `/api/smc/statistics/detail/header?yearAndMonth=${moment(
          this.time
        ).format("YYYY-MM")}&indexName=${this.indexName}`
      ).then(res => {
        this.$emit("cancelLoading", res.length);
        if (res.length) {
          this.visible = true;
        }
        // this.pageLoading = false;
        this.topCardList = res.map(item => {
          return {
            ...item,
            currentMonthValue:
              (item.currentMonthValue === 0 ? 0 : item.currentMonthValue) ||
              "-",
            planValue: (item.planValue === 0 ? 0 : item.planValue) || "-"
          };
        });
        // 获取四个卡片中最低完成率的指标进行数据分析
        if (this.topCardList.length) {
          this.cardClick(
            this.topCardList.filter(card => {
              return (
                (card.completionRate ? card.completionRate : 0) ===
                Math.min(
                  ...this.topCardList.map(item => item.completionRate || 0)
                )
              );
            })[0].companyName
          );
        }
      });
    },
    // 获取数据分析
    getReasonData() {
      request(
        `/api/smc/statistics/detail/body?yearAndMonth=${moment(
          this.time
        ).format("YYYY-MM")}&indexName=${this.indexName}&companyName=${
          this.activeCompanyName
        }`
      )
        .then(res => {
          this.reasonLoading = false;
          this.completion = res.completion;
          this.distribution = res.distribution || [];
          this.$nextTick(() => {
            // 中间图表渲染
            this.completion.data.forEach((item, index) => {
              if (
                ["单平台平均销量", "单型号平均销量"].includes(this.indexName) ||
                (this.indexName === "TOP机型" && index === 0) ||
                (this.indexName === "设计里程碑质量评审无条件通过率" &&
                  index === 0)
              ) {
                this.chartObject[
                  `${this.activeCompanyName}-${item[0].indexName}-${index}`
                ] = echarts.init(
                  this.$refs[
                    `${this.activeCompanyName}-${item[0].indexName}-${index}`
                  ][0]
                );
                // 初始化
                const option = Object.assign({}, this.chartOption);
                option.xAxis[0].data = item.map(it => {
                  return it.dimensionName;
                });
                option.series[0].data = item.map(it => {
                  return it.currentMonthValue;
                });
                option.series[1].data = item.map(it => {
                  return it.targetValue;
                });
                option.series[2].data = item.map(it => {
                  return it.yearOnYearValue;
                });
                if (["单平台平均销量", "单型号平均销量"].includes(this.indexName)) {
                  option.series[3].data = item.map(it => {
                    return it.completionRate;
                  });
                } else {
                  option.legend.data = ["实际", "目标", "同期"];
                  option.yAxis = [option.yAxis[0]];
                  option.series = option.series.slice(0, 3);
                }
                option.yAxis[0].axisLabel.formatter = `{value} ${
                  [null, undefined].includes(item[0].salesUnit)
                    ? ""
                    : item[0].salesUnit
                }`;
                this.chartObject[
                  `${this.activeCompanyName}-${item[0].indexName}-${index}`
                ].setOption(option);
              } else if (
                this.indexName === "TOP机型" &&
                [1, 2].includes(index)
              ) {
                if (index === 1) {
                  this.topJXLeftData = item;
                } else {
                  this.topJXRightData = item;
                }
              }
            });

            this.distribution.forEach((item, index) => {
              // 底部图表中间渲染
              this.chartObject[
                `${this.activeCompanyName}-${item.data[1][0].companyName}-${index}-bottom`
              ] = echarts.init(
                this.$refs[
                  `${this.activeCompanyName}-${item.data[1][0].companyName}-${index}-bottom`
                ][0]
              );

              const chartData = item.data[1];
              const bottomOption = Object.assign({}, this.bottomChartOption);
              bottomOption.xAxis[0].data = chartData.map(
                zitem => zitem.categoryName
              );
              bottomOption.series[0].data = chartData.map(zitem => {
                const { categoryValue } = zitem;
                return {
                  value: categoryValue,
                  itemStyle: {
                    color:
                      categoryValue > 1
                        ? "#D84D3F"
                        : categoryValue > 0.5
                        ? "#008888"
                        : categoryValue > 0.1
                        ? "#FF978C"
                        : "#57C7C7"
                  }
                };
              });
              this.chartObject[
                `${this.activeCompanyName}-${item.data[1][0].companyName}-${index}-bottom`
              ].setOption(bottomOption);

              // 底部左边右边表格渲染
              if (["单型号平均销量", "单平台平均销量"].includes(this.indexName)) {
                this.$set(this.leftTableObject, `Data${index}`, item.data[0]);
                this.$set(this.rightTableObject, `Data${index}`, item.data[2]);
              }
            });
          });
        })
        .catch(() => {
          this.reasonLoading = false;
        });
    },
    genExcel(leftData, rightData) {
      if (!rightData.data.length && !leftData.data.length) {
        return;
      }
      const ExportJsonExcel = require("js-export-excel");
      // const columnNames = this.columns.map(item => item.name);
      const option = {};
      option.fileName = `${moment(this.time).format("YYYY-MM")}${
        rightData.data[0].splitKey
      }数据分析`;
      option.datas = [
        {
          //第一个sheet（第一个excel表格）
          sheetData: leftData.data, //数据
          sheetName: leftData.title,
          sheetFilter: this.leftCloumns.map(item => item.dataIndex), //表头数据对应的sheetData数据
          sheetHeader: this.leftCloumns.map(item => item.title) //表头数据
        },
        {
          //第一个sheet（第一个excel表格）
          sheetData: rightData.data, //数据
          sheetName: rightData.title,
          sheetFilter: this.rightCloumns.map(item => item.dataIndex), //表头数据对应的sheetData数据
          sheetHeader: this.rightCloumns.map(item => item.title) //表头数据
        }
      ];
      const toExcel = new ExportJsonExcel(option);
      toExcel.saveExcel();
    },
    show(data) {
      this.indexName = data.indexName;
      this.time = data.time;
      // 底部左边右边表格渲染
      if (this.indexName === "单型号平均销量") {
        this.leftCloumns = [
          {
            title: "目标完成率",
            dataIndex: "targetCompletionRateDesc",
            key: "targetCompletionRateDesc",
            width: 80
            // scopedSlots: { customRender: "targetCompletionRateDesc" }
          },
          {
            title: "型号数",
            dataIndex: "modelNum",
            key: "modelNum",
            align: "right",
            width: 80
          },
          {
            title: "型号数占比",
            dataIndex: "modelNumProportionDesc",
            key: "modelNumProportionDesc",
            align: "right",
            width: 80
          },
          {
            title: "实际销量",
            key: "actualSales",
            dataIndex: "actualSales",
            align: "right",
            width: 80
          },
          {
            title: "销量占比",
            key: "salesRatio",
            dataIndex: "salesRatio",
            align: "right",
            width: 80
          },
          {
            title: "目标销量",
            key: "targetSales",
            dataIndex: "targetSales",
            align: "right",
            width: 80
          },
          {
            title: "目标-实际",
            key: "diffValue",
            dataIndex: "diffValue",
            align: "right",
            width: 80
          }
        ];
        this.bottomChartOption.dataZoom[0].end = 40;
      } else if (this.indexName === "单平台平均销量") {
        this.leftCloumns = [
          {
            title: "销量占比",
            dataIndex: "targetCompletionRateDesc",
            key: "targetCompletionRateDesc"
            // scopedSlots: { customRender: "targetCompletionRateDesc" }
          },
          {
            title: "平台数",
            dataIndex: "platformNum",
            key: "platformNum",
            align: "right"
          },
          {
            title: "平台数占比",
            dataIndex: "platformNumRatioDesc",
            key: "platformNumRatioDesc",
            align: "right"
          },
          {
            title: "销量占比",
            key: "salesRatioDesc",
            dataIndex: "salesRatioDesc",
            align: "right"
          },
          {
            title: "同期平台数",
            key: "platformNumInSamePeriod",
            dataIndex: "platformNumInSamePeriod",
            align: "right"
          },
          {
            title: "同期销量占比",
            key: "salesRatioInSamePeriodDesc",
            dataIndex: "salesRatioInSamePeriodDesc",
            align: "right"
          }
        ];
        this.bottomChartOption.dataZoom[0].end = 100;
      }
      this.rightCloumns = [
        {
          title: this.indexName === "单型号平均销量" ? "型号" : "平台",
          dataIndex: "modelName",
          key: "modelName",
          width: 80
        }
      ];
      if (this.indexName === "单型号平均销量") {
        this.rightCloumns = [
          ...this.rightCloumns,
          {
            title: "本月目标销量",
            dataIndex: "targetSales",
            key: "targetSales",
            align: "right",
            width: 80
          },
          {
            title: "上市日期",
            dataIndex: "listingDate",
            key: "listingDate",
            align: "right",
            width: 80
          }
        ];
      }
      this.genRightColumns(
        new Date(
          new Date().getFullYear(),
          new Date(moment(this.time).format("YYYY-MM")).getMonth() + 1,
          1
        ),
        4
      );
      this.getTopCardList();
    },
    genRightColumns(time, times) {
      times--;
      if (times >= 0) {
        let month =
          time.getMonth() <= 0 ? 12 + time.getMonth() : time.getMonth();
        month = month < 10 ? `0${month}` : month;
        let year =
          time.getMonth() <= 0 ? time.getFullYear() - 1 : time.getFullYear();
        this.rightCloumns.push({
          title: `${year}${month}`,
          dataIndex: `${year}-${month}`,
          key: `${year}-${month}`,
          align: "right",
          width: this.indexName === "单型号平均销量" ? 60 : 80
        });
        this.genRightColumns(
          new Date(time.getFullYear(), time.getMonth(), 0),
          times
        );
      }
    },
    close() {
      this.indexName = "";
      this.time = "";
      this.visible = false;
      this.topCardList = [];
      this.completion = {
        data: [],
        reasonAnalysis: ""
      };
      this.distribution = [];
      for (const key in this.leftTableObject) {
        if (Object.hasOwnProperty.call(this.leftTableObject, key)) {
          delete this.leftTableObject[key];
        }
      }
      for (const key in this.rightTableObject) {
        if (Object.hasOwnProperty.call(this.rightTableObject, key)) {
          delete this.rightTableObject[key];
        }
      }
      for (const key in this.chartObject) {
        if (Object.hasOwnProperty.call(this.chartObject, key)) {
          const element = this.chartObject[key];
          if (element) {
            element.dispose();
          }
          this.chartObject[key] = null;
          delete this.chartObject[key];
        }
      }
    }
  }
};
</script>
<style lang="less">
.RD-bigScreen-modal {
  *::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  *::-webkit-scrollbar-button {
    width: 0px;
    height: 0px;
    display: none;
  }
  *::-webkit-scrollbar-corner {
    background-color: transparent;
  }
  *::-webkit-scrollbar-thumb {
    border: 4px solid rgba(0, 0, 0, 0);
    height: 6px;
    border-radius: 25px;
    background-clip: padding-box;
    background-color: rgba(0, 0, 0, 0.3);
  }
  .ant-modal {
    .ant-modal-close {
      right: calc(100% - 0.5rem);
    }
    .closeBtn {
      height: 0.52rem;
      line-height: 0.52rem;
      text-align: left;
      // position: absolute;
      // left: 0.1rem;
      // top: 0.1rem;
      // cursor: pointer;
      // font-size: 0.4rem;
    }
    // .charts-area {
    //   margin-top: 0.5rem !important;
    // }
    top: 0;
    padding-bottom: 0;
    .ant-modal-content {
      height: 100vh;
      min-width: 1280px;
      .ant-modal-body {
        padding: 0;
        height: 100%;
        min-width: 1280px;
        overflow: auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        background-color: #f2f3f5;
        .ant-spin-nested-loading {
          flex: 1;
          overflow-y: auto;
        }
        .echart-area,
        .table-area .container {
          box-sizing: border-box;
          margin: 0 0.4rem 0.24rem 0.4rem;
          background-color: #fff;
          padding: 0.4rem 0;
          border-radius: 0.16rem;
          & > .box {
            display: flex;
            & > div {
              flex: 1;
              overflow: hidden;
              .title {
                font-size: 0.24rem;
                font-weight: bold;
                color: #000000;
                text-align: center;
              }
              .chart {
                margin-top: 0.17rem;
                width: 100%;
                height: 3.3rem;
              }
              .table {
                margin-top: 0.17rem;
                width: 94%;
              }
            }
          }
          .reasonAnalysis {
            padding: 0 0.4rem;
            text-shadow: 0.1rem 0.1rem 0.2rem rgba(0, 0, 0, 0.15);
          }
        }
        .table-area {
          .container > .box {
            & > div {
              .title {
                margin-bottom: 0.24rem;
              }
              .table {
                padding: 0 0.24rem;
                .ant-table-thead > tr > th {
                  padding: 0;
                  font-size: 0.14rem;
                  & > span {
                    height: 0.5rem;
                    line-height: 0.5rem;
                    display: block;
                  }
                }
                .ant-table-thead > tr > th,
                .ant-table-tbody > tr > td {
                  font-size: 10px;
                }
              }
              .bottomChart {
                margin-top: 0.17rem;
                width: 100%;
                height: 3.4rem;
              }
            }
          }
        }
      }
    }
  }
}
</style>
