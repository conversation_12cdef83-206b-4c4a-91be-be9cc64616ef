/*
 * @Description: 组件配置描述json
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-07 15:58:35
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-12-14 14:48:02
 */
const comJson = {
  json: [
    {
      type: "input",
      col: "companyName",
      label: "公司"
    },
    {
      type: "input",
      col: "baseLabelName",
      label: "基地下拉框名称",
      props: {}
    },
    {
      type: "input",
      col: "operationGuideJSUrl",
      label: "操作引导组件jsUrl",
      props: {}
    },
    {
      type: "input",
      col: "indexCardDetailInfoJSUrl",
      label: "指标卡片详情组件jsUrl",
      props: {}
    },
    {
      type: "input",
      col: "selfReportsJSUrl",
      label: "个人专属报告jsUrl",
      props: {}
    }
  ],
  props: {
    companyName: "空调",
    baseLabelName: "基地",
    operationGuideJSUrl: "/smcbucket/VideoCom.umd.min.1.0.js",
    indexCardDetailInfoJSUrl: "/smcbucket/IndexCardDetailInfo.umd.min.1.0.js",
    selfReportsJSUrl: "/smcbucket/SelfReports.umd.min.1.0.js"
  },
  buttonList: []
};
export default comJson;
