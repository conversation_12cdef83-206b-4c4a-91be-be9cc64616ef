/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.CalcEngine=GC.Spread.CalcEngine||{},GC.Spread.CalcEngine.BasicFunctions=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="",c(c.s="./node_modules_local/@grapecity/js-calc-basicfunctions/index.js")}({"./node_modules_local/@grapecity/js-calc-basicfunctions/dist/gc.spread.calcEngine.basicfunctions.js":function(a,b,c){var d="object"==typeof d?d:{};d.Spread=d.Spread||{},d.Spread.CalcEngine=d.Spread.CalcEngine||{},d.Spread.CalcEngine.BasicFunctions=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/basicFunctions.entry.ts")}({"./src/basicFunctions.entry.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("CalcEngine");b.Functions=d.Functions,c("./src/functions-basic.ts")},"./src/functions-basic.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=d.Common.q,g=d.Common.u,h=d.Common.l,i=h.Ra,j=h.Qa,k=d.Common.k.ac,l=d.Common.j,m=l.Ea,n=l.Fa,o=void 0,p=isNaN,q=parseFloat,r=parseInt,s=Math.abs,t=Math.sqrt,u=Math.max,v=Math.min,w=Math.floor,x=Math.exp,y=Math.log,z=Math.PI,A=Math.random,B=Math.pow,C=e.Errors.Null,D=e.Errors.DivideByZero,E=e.Errors.Value,F=e.Errors.Reference,G=e.Errors.Name,H=e.Errors.NotAvailable,I=e.Errors.Number,J=e.Convert.CalcConvertedError,K=e.Convert.vf,L=e.Convert.Fh,M=e.Convert.Ca,N=e.Convert.Uh,O=e.Convert.Rh,P=e.Convert.Pa,Q=e.Convert.bc,R=e.Convert.Na,S=e.Convert.Ph,T=e.Convert.Nh,U=e.Zh.tryExtractToSingleValue,V=e.Functions.ik,W=e.Functions.jk,X=e.Functions.kk,Y=e.Functions.UAb,Z=e.Functions.VAb,$=e.Functions.WAb,_=e.Functions.nk,aa=e.Functions.tk,ba=e.Functions.rk,ca=e.Functions.sk,da=e.Functions.MathHelper,ea=da.zk,fa=da.wk,ga=e.Functions.ak,ha=e.Convert.Th,ia=e.Convert.Uh,ja=e.Convert.jja;function Da(a,b){for(;0!==b;){var c=a%b;a=b,b=c}return a}function Ea(a,b,c,d){return 0===b||0===a?0:0!==c&&a<0?d===!0?fa(a/Math.abs(b))*Math.abs(b):ea(a/Math.abs(b))*Math.abs(b):d===!0?ea(a/Math.abs(b))*Math.abs(b):fa(a/Math.abs(b))*Math.abs(b)}function Fa(a,b){var c,d,e,f,g,h,i=a[0],j=k(i),l=i.rowCount,m=i.colCount,n=[],o=[],p=[];for(f=1;f<k(a);f+=2){if(h=S(a[f],0,!0,!1,!1),h.rowCount!==l||h.colCount!==m)return E;g=da.xk(a[f+1]),n.push(h),o.push(g)}for(e=0;e<j;e++){for(d=!0,f=0;f<k(n)&&(g=o[f],c=n[f][e],d=g&&g(c),d);f++);d&&(c=i[e],c!==J&&p.push(c))}return b(p)}function Ga(a,b){return b=b<0?w(b):Math.ceil(b),(a&&b%2===0||!a&&b%2!==0)&&(b+=b<0?-1:1),b}function Ha(a,b,c){return b>0&&c<0?I:b<0&&c>0?(c=-c,a?fa(b/c)*c:ea(b/c)*c):a?ea(b/c)*c:fa(b/c)*c}function Ia(a,b,c){var d,e,f,g=da.yk(s(c));return b=c<0?b/g:b*g,d=ea(b),e=fa(b),a&&(f=d,d=e,e=f),b=b<0?d:e,b=c<0?b*g:b/g,T(b)}function Ja(a,b,c,d){var e=b?-1:1,f=0,g,h,i;if(k(c)!==k(d))return H;for(i=0;i<k(c);i++)g=c[i],h=d[i],g!==J&&h!==J&&(f+=a?(g-h)*(g-h):g*g+e*h*h);return T(f)}function Ka(a){return T(s(a))}function La(a){return fa(a)}function Ma(a,b){var c=1,d;if(a<b)return I;for(b=v(a-b,b),d=1;d<=b;d++)c*=a-d+1,c/=d;return T(c)}function Na(a,b){var c=1,d;for(d=1;d<=b;d++)c*=a+d-1;for(d=2;d<=b;d++)c/=d;return T(c)}function Oa(a,b){var c,d,e,f,g,h,i;if(!a||a.length>255)return 0;if(c=a.length,0===c)return 0;if("-"===a[0])return I;for(d=0;d<c&&" "===a[d];)d++;if(e=0,d===c)return 0;for(f=1,g=c-1;g>=d;g--){if(h=a.charCodeAt(g)|" ".charCodeAt(0),i=h-"a".charCodeAt(0)>=0?h-87:h-48,i<0||i>=b)return I;e+=i*f,f*=b}return e}function Pa(a){return 180*a/z}function Qa(a){return Ga(!0,a)}function Ra(a){return Ga(!1,a)}function Sa(a){return aa(a)}function Ta(a){var b=1,c;for(c=a;c>1;c-=2)b*=c;return b}function Ua(a){return T(y(a))}function Va(a,b){return a-b*w(a/b)}function Wa(){return z}function Xa(a,b){return B(a,b)}function Ya(){var a,b,c,d,e=arguments,f=0;for(a=0;a<k(e);a++){if(b=e[a],c=void 0,d=void 0,K(b))return b;if(L(b)||M(b)){if(c=S(b,1,!0,!0,!1),c.isError)return c[0]}else c=S(b,1,!0,!0,!0);for(d=0;d<k(c);d++)c[d]!==J&&(f+=c[d])}return f}function Za(a){return 0===a?0:-1}function $a(){var a,b,c,d,e=arguments,f=0;for(a=0;a<k(e);a++)for(c=e[a],b=0;b<k(c);b++)if(d=c[b],d!==J){if(d<0)return I;f=Da(f,O(d))}return f}function _a(){var a,b,c,d,e=arguments,f=1,g=[];for(a=0;a<k(e);a++){for(c=e[a],b=0;b<k(c);b++)if(d=c[b],d!==J){if(d<0)return I;if(0===d)return 0;g.push(O(d))}for(b=0;b<k(g);b++)d=g[b],f/=Da(f,d),f*=d}return f}function ab(){return ba(arguments,!0,6)}function bb(a){return t(a)}function cb(a,b){return r(""+a/b)}function db(a){var b,c=arguments,d=[],e=E;for(b=1;b<k(c);b++)d[b-1]=c[b];return m(a,[1,101,2,102,3,103,4,104,5,105,6,106,9,109])>=0?e=ba(d,!1,a):m(a,[7,107,8,108,10,110,11,111])>=0&&(e=ca(d,!1,a)),e}function eb(a,b){return Ha(!0,a,b)}function fb(a,b){return Ha(!1,a,b)}function gb(a,b,c){return Ea(a,b,c,!0)}function hb(a,b,c){return Ea(a,b,c,!1)}function ib(a,b,c){var d=""+a.toString(b);return c&&d.length<c?Array.apply(null,{length:c-d.length+1}).join(0)+d:d}function jb(a,b){return a<0&&b>0||b<0&&a>0?I:fa(a/b+.5)*b}function kb(a,b){return da.Ak(a,b)}function lb(a,b){return Ia(!1,a,b)}function mb(a,b){return Ia(!0,a,b)}function nb(a){return T(x(a))}function ob(a,b){return ea(a/s(b))*s(b)}function pb(a,b){return fa(a/s(b))*s(b)}function qb(a,b){return 1===b?D:T(da.Ek(a,b))}function rb(a){return T(da.Ek(a,10))}function sb(a,b,c){var d,e,f,g,h,i,j,l,m,p,q,r,s,t,u,v,w,x,y;if(c=c!==o?c:a,n(b)&&(b=0),d=0,f=da.xk(b),g=M(a),h=M(c),i=a.toArray(0,!0,!1),j=c.toArray(1,!0,!1),(g?1:a.getRangeCount())!==(h?1:c.getRangeCount()))return E;if(i.isError)return i[0];if(j.isError)return j[0];if(c===a||h)for(e=0;e<k(i);e++)f&&f(i[e])&&j[e]!==J&&(d+=j[e]);else for(l=a.getRow(0),m=a.getColumn(0),p=c.getRow(0),q=c.getColumn(0),r=a.getRowCount(0),s=a.getColumnCount(0),t=a.getSource(),u=c.getSource(),v=0;v<r;v++)for(w=0;w<s;w++)x=u.getValue(p+v,q+w),y=t.getValue(l+v,m+w),f&&f(y)&&x!==J&&Sd(x)&&(d+=x);return T(d)}function tb(){return Fa(arguments,function(a){var b,c,d=0;for(b=0;b<a.length;b++){if(c=a[b],K(c))return c;d+=c}return d})}function ub(){return Fa(arguments,function(a){var b=null;return a.length>0&&(b=Math.max.apply(null,a)),b})}function vb(){return Fa(arguments,function(a){var b=null;return a.length>0&&(b=Math.min.apply(null,a)),b})}function wb(a){for(var b=[],c=0,d=[];c<a;c++)d[c]=0;for(c=0;c<a;c++)b[c]=d.slice(0),b[c][c]=1;return new e.CalcArray(b)}function xb(){var a,b,c,d,e,f,g,h=arguments,i=0,j=[],l=0;for(a=0;a<k(h);a++){if(d=S(h[a],1,!0,!0,!1,!0),d.isError)return d[0];if(d.isConvertError)return E;if(0===a)b=d.rowCount,c=d.colCount,l=k(d);else if(d.rowCount!==b||d.colCount!==c)return E;j.push(d)}for(a=0;a<l;a++){for(e=1,f=0;f<k(h);f++){if(g=j[f][a],g===J){e=0;break}e*=g}i+=e}return T(i)}function yb(){var a,b,c,d,e=arguments,f=0;for(b=0;b<k(e);b++)for(d=e[b],c=0;c<k(d);c++)a=d[c],a!==J&&(f+=a*a);return T(f)}function zb(a,b){return Ja(!1,!0,a,b)}function Ab(a,b){return Ja(!1,!1,a,b)}function Bb(a,b){return Ja(!0,!1,a,b)}function Cb(a,b,c,d){var e,f,g=0;for(e=0;e<k(d);e++){if(f=P(d[e]),p(f))return E;g+=f*B(a,b+e*c)}return T(g)}function Db(a){return T(t(a*z))}function Eb(a){return z*a/180}function Fb(a){return T(Math.sin(a))}function Gb(a){var b=Fb(a);return 0===b?D:1/b}function Hb(a){return T(Math.cos(a))}function Ib(a){var b=Hb(a);return 0===b?D:1/b}function Jb(a){return T(Math.tan(a))}function Kb(a){return 0===a?D:T(1/Math.tan(a))}function Lb(a){return T(Math.asin(a))}function Mb(a){return T(Math.acos(a))}function Nb(a){return T(da.Dk(a,!0))}function Ob(a){if(a>709||a<-709)return 0;var b=Nb(a);return 0===b?D:1/b}function Pb(a){return T(da.Dk(a,!1))}function Qb(a){if(a>709||a<-709)return 0;var b=Pb(a);return 0===b?D:1/b}function Rb(a){return T(y(a+t(a*a+1)))}function Sb(a){return T(y(a+t(a*a-1)))}function Tb(a){return T(Math.atan(a))}function Ub(a){return T(a<0?Math.PI+Math.atan(1/a):Math.atan(1/a))}function Vb(a,b){return 0===a&&0===b?D:T(Math.atan2(b,a))}function Wb(a){var b=x(a),c=x(-a);return T(b-c)/(b+c)}function Xb(a){if(0===a)return D;if(a>10)return 1;if(a<-10)return-1;var b=Wb(a);return 1/b}function Yb(a){return T(y((1+a)/(1-a))/2)}function Zb(a){return Yb(1/a)}function $b(a){var b=a.rowCount,c=1,d,e,f,g,h,i,j,k;if(a.rangeCount>1||b!==a.colCount)return E;for(d=0;d<b-1;d++){if(0===a[d][d]){for(i=!1,e=d+1;!i&&e<b;e++)if(0!==a[e][d]){for(g=d;g<b;g++)j=a[d][g],a[d][g]=a[e][g],a[e][g]=j;c*=-1,i=!0}if(!i)return 0}for(f=d+1;f<b;f++)if(0!==a[f][d])for(k=a[f][d]/a[d][d],h=d;h<b;h++)a[f][h]-=k*a[d][h]}for(d=0;d<b;d++)c*=a[d][d];return c}function _b(a){var b=a.rowCount,c=[],d,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;if(a.rangeCount>1||b!==a.colCount)return E;for(d=0;d<b;d++)for(c[d]=[b],h=0;h<b;h++)c[d][h]=d===h?1:0;for(f=0;f<b;f++){if(0===a[f][f]){for(t=!1,i=f+1;!t&&i<b;i++)if(0!==a[i][f]){for(k=f;k<b;k++)r=a[f][k],a[f][k]=a[i][k],a[i][k]=r;for(l=1;l<b;l++)s=c[f][l],c[f][l]=c[i][l],c[i][l]=s;t=!0}if(!t)return I}for(j=0;j<b;j++)if(j!==f&&0!==a[j][f]){for(p=a[j][f]/a[f][f],m=f;m<b;m++)a[j][m]-=p*a[f][m];for(n=0;n<b;n++)c[j][n]-=p*c[f][n]}}for(g=0;g<b;g++)for(q=a[g][g],o=0;o<b;o++)c[g][o]/=q;return new e.CalcArray(c)}function ac(a,b){var c,d,f,g,h,i,j=a.rowCount,k=a.colCount,l=b.rowCount,m=b.colCount,n=[];if(k!==l)return E;for(f=0;f<j;f++)for(n[f]=c=[],d=a[f],g=0;g<m;g++){for(i=0,h=0;h<l;h++)i+=d[h]*b[h][g];c[g]=i}return new e.CalcArray(n)}function bc(){var a,b,c,d,e=arguments,f=0,g=1;for(b=0;b<k(e);b++)for(a=e[b],c=0;c<k(a);c++){if(d=O(a[c]),d<0||170<d)return I;f+=d,g*=aa(d)}return f<0||170<f?I:aa(f)/g}function cc(){return s(2*A()-1)}function dc(a,b){return b<a?I:O(a+A()*(b-a+1))}function ec(a,b){var c,d,e,f,g,h,i,j,k=["M","D","C","L","X","V","I"],l=[1e3,500,100,50,10,5,1],m=7,n=[];for(c=V(b)?b?0:4:b,d=0;d<m;d+=2){if(2<=d&&l[d-2]-l[d]<=a){for(e=d,f=d-2,g=void 0,g=0;g<c&&e+1<m&&l[f]-l[e+1]<=a;g++)e++;n.push(k[e]),n.push(k[f]),a+=l[e],a-=l[f]}if(1<=d&&l[d-1]<=a&&(n.push(k[d-1]),a-=l[d-1]),1<=d&&l[d-1]-l[d]<=a){for(h=d,i=d-1,j=void 0,j=0;j<c&&h+1<m&&l[i]-l[h+1]<=a;j++)h++;n.push(k[h]),n.push(k[i]),a+=l[h],a-=l[i]}for(;l[d]<=a;)n.push(k[d]),a-=l[d]}return n.join("")}function fc(a){var b,c,d,e,f,g,h,i,j,k;if(!a.length)return 0;if(a.length>255)return E;for(a=a.toLowerCase(),b=0,c=a.length-1,e=0,h=-1;c>=0&&" "===a[c];)c--;for(i=0;i<=c&&" "===a[i];)i++;j=!1,i<=c&&"-"===a[i]&&(j=!0,i++);do{for(d=c,k=a[d],--c;c>=i&&(a[c].charCodeAt(0)|" ".charCodeAt(0))===k.charCodeAt(0);)--c;switch(k){case"i":f=d-c,g=1;break;case"v":f=5*(d-c),g=5;break;case"x":f=10*(d-c),g=10;break;case"l":f=50*(d-c),g=50;break;case"c":f=100*(d-c),g=100;break;case"d":f=500*(d-c),g=500;break;case"m":f=1e3*(d-c),g=1e3;break;default:return E}g>=h?(b+=f-e,h=g,e=0):e+=f}while(c>=i);return 0!==e&&(b-=e),j&&(b=-b),b}function gc(a,b){var c,d,e;for(c=0;c<k(b);c++)for(e=b[c],d=0;d<k(e);d++)if(a){if(!e[d])return!1}else if(e[d]&&e[d]!==J)return!0;return!!a}function hc(){return gc(!0,arguments)}function ic(){return gc(!1,arguments)}function jc(a){var b={};return e.Convert.Uh(a,b),!b.value}function kc(a,b,c){var d,e,f=a,g=b,h=U(f);return d=c!==o?c:2!==k(arguments)&&0,e={},N(h.value,e),e.value?g:d}function lc(a,b){return K(a)?n(b)?0:b:n(a)?0:a}function mc(a,b){var c,d,e,f;if(!K(a)&&L(a)){if(a.getRangeCount()>1)return E;if(c=0,d=0,e=this.y0a,e&&(c=e.row,d=e.col,f=a.yf&&a.yf[0],f&&(c+1>f.rowCount||d+1>f.colCount)))return b;a=a.getValue(0,c,d)}return lc(a,b)}function nc(){return!0}function oc(){return!1}function pc(a,b){return ja(a)?b:a}function qc(){var a,b,c;for(a=0,b=arguments.length;a<b;a++){if(c={value:!1},K(arguments[a]))return arguments[a];if(!N(arguments[a++],c))return E;if(c.value)return arguments[a]}return H}function rc(){var a,b,c;if(arguments.length<3)return E;if(K(arguments[0]))return arguments[0];for(a=1,b=arguments.length-1;a<b;a++){if(c=arguments[a++],K(c))return c;if("string"==typeof arguments[0]&&"string"==typeof c){if(arguments[0].toUpperCase()===c.toUpperCase())return arguments[a]}else if(arguments[0]===c)return arguments[a]}return arguments.length%2===0?arguments[arguments.length-1]:H}function sc(){var a,b,c,d=0;for(a=0;a<k(arguments);a++)for(c=arguments[a],b=0;b<k(c);b++)c[b]&&d++;return!!(d%2)}function tc(a){var b,c=a.getFullYear(),d=a.getMonth(),e=a.getDate(),f=e;for(b=0;b<d;b++)f+=_(c,b);return f}function uc(a){if(W(a)){if(7!==k(a)||p(r(a)))return E}else if(!p(a)){if(w(a)<1||w(a)>17)return I;a=""+a}var b=["1","2","3","4","5","6","7"],c=[0,0,0,0,0,0,0],d;switch(k(a)){case 1:if(m(a,b)>=0){d=r(a),c[(d+4)%7]=1,c[(d+5)%7]=1;break}return I;case 2:if("1"===a[0]&&m(a[1],b)>=0){d=r(a[1]),c[(d+5)%7]=1;break}return I;case 7:for(d=0;d<7;d++)c[d]=r(a[d]);break;default:return E}return c}function vc(a,b,c,d,e){var f,g=Mc(b,3);if(K(g))return g;for(f=O(g);c<0;++c)b.setDate(b.getDate()-1),f=0===f?6:--f,(a&&(5===f||6===f)||!a&&e[f]||d.indexOf(i(b))>=0)&&c--;for(;c>0;--c)b.setDate(b.getDate()+1),f=6===f?0:++f,(a&&(5===f||6===f)||!a&&e[f]||d.indexOf(i(b))>=0)&&c++;return i(b)}function wc(a,b,c,d,e,f){var g,i,j,l,m,n,p,q,r;if(f&&(a=a!==o?a:"0000011",i=uc(a),K(i)))return i;if(b!==o&&(g=S(b,4,!0,!0,!1)),!g)return e;if(g.isError)return g[0];for(j=k(g),l=[],m=0;m<j;m++)n=g[m],p=h.Xb(n),q=p.getDay(),r=k(i)?i[Mc(p,3)]:6===q||0===q,l.indexOf(n)===-1&&!r&&n>=c&&n<=d&&l.push(n);return e-=k(l)}function xc(a,b,c,d,e,f){var g,h,j,k=O(i(b)),l=O(i(c)),m=!1;return k>l&&(g=b,b=c,c=g,h=k,k=l,l=h,m=!0),j=l-k+1,j=f(b,c,j,d),a&&K(j)?j:j<=0?0:(j=wc(d,e,k,l,j,a),j=m?-j:j)}function yc(a,b,c){var d,f;return b=e.Convert.Wh(b),d=new Date(b.getFullYear(),b.getMonth(),1),d.setMonth(d.getMonth()+c),f=_(d.getFullYear(),d.getMonth()),d.setDate(a?f:v(b.getDate(),f)),d}function zc(a,b,c){a<=1899&&(a+=1900);var d=new Date(a,b-1,c);return d<new Date(1899,11,30)?I:d}function Ac(a,b,c){var d=h.Xb(0);return d.setHours(a),d.setMinutes(b),d.setSeconds(c),d.setMilliseconds(0),d.getHours()<0&&d.getMinutes()<0&&d.getSeconds()<0?I:d}function Bc(a){var b,c,d,e;if(W(a)&&(b=j(a),b||(b=new Date(a),b&&!p(b.valueOf())||(b=null))),!b)return E;if(d=b.getFullYear(),e=tc(b),d<1900)return E;for(c=1900;c<d;c++)e+=365,X(c)&&e++;return e}function Cc(a){var b;return W(a)&&(b=j(a)),b?(60*(60*b.getHours()+b.getMinutes())+b.getSeconds())/86400:E}function Dc(){return new Date}function Ec(){var a=new Date;return a.setHours(0,0,0,0),a}function Fc(a){return a.getHours()}function Gc(a){return a.getMinutes()}function Hc(a){return a.getSeconds()}function Ic(a){return a.getDate()}function Jc(a){return a.getMonth()+1}function Kc(a){return a.getFullYear()}function Lc(a,b){var c,d,e;return 1===b||2===b?(c=tc(a),d=new Date(a.getFullYear(),0,1).getDay(),2===b&&(d-=1,d<0&&(d=6)),e=c-1-(6-d),e<0&&(e=0),1+O(e/7)+(e%7!==0?1:0)):I}function Mc(a,b){var c=a.getDay();switch(b){case 1:return c+1;case 2:return 0===c?7:c;case 3:return 0===c?6:c-1;default:return I}}function Nc(a,b){return yc(!1,a,b)}function Oc(a,b){return yc(!0,a,b)}function Pc(a,b,c){return vc(!0,a,b,c)}function Qc(a,b,c,d){var e=uc(c);return K(e)?e:"1111111"===e.join("")?E:vc(!1,a,b,d,e)}function Rc(a,b,c){function d(a,b,c){var d=O(i(b)-i(a))%7,e=O(Mc(a,2)+d),f=0===a.getDay()?1:2;return d=e>5?e-5:0,d=d>f?f:d,c-=d,c-=2*w(c/7)}return xc(!1,a,b,o,c,d)}function Sc(a,b,c,d){var e,f,g,h,i,j,l;if(d=d!==o?d:"0000011",e=uc(d),f=0,g=0,K(e))return e;for(h=0;h<k(e);h++)1===e[h]&&f++;if(c<30){for(h=0;h<c;h++)e[(O(Mc(a,3))+h)%7]&&g++;return c-=g}for(i=c,j=0,l=0,h=0;h<i&&O(Mc(a,3)+h)%7!==0;h++)j++;for(h=0;h<j;h++)e[(O(Mc(a,3))+h)%7]&&g++;for(h=0;h<i&&(O(Mc(b,3))+7-h)%7!==6;h++)l++;for(h=0;h<l;h++)e[(O(Mc(b,3))+7-h)%7]&&g++;return i=i-j-l,c-=O(i/7)*f,c-=g}function Tc(a,b,c,d){return xc(!0,a,b,c,d,Sc)}function Uc(a,b,c){var d,f,g;function h(){var a=[];return a[0]=function(a,b){return b.getFullYear()-a.getFullYear()+(b.getMonth()<a.getMonth()||b.getMonth()===a.getMonth()&&b.getDate()<a.getDate()?-1:0)},a[1]=function(a,b){return 12*(b.getFullYear()-a.getFullYear())+(b.getMonth()-a.getMonth())+(b.getDate()<a.getDate()?-1:0)},a[2]=function(a,b){return(b.getTime()-a.getTime())/864e5},a[3]=function(a,b){var c=new Date(b.getFullYear(),b.getMonth()+(b.getDate()<a.getDate()?-1:0),a.getDate());return(b.getTime()-c.getTime())/864e5},a[4]=function(a,b){return b.getMonth()-a.getMonth()+(b.getMonth()<a.getMonth()||b.getMonth()===a.getMonth()&&b.getDate()<a.getDate()?12:0)+(b.getDate()<a.getDate()?-1:0)},a[5]=function(a,b){var c,d=b.getFullYear()+(b.getMonth()<a.getMonth()||b.getMonth()===a.getMonth()&&b.getDate()<a.getDate()?-1:0),e=a.getMonth(),f=a.getDate(),g=X(a.getFullYear());return 1!==e||29!==f||X(d)||f--,!g&&1===e&&28===f&&X(d)&&f++,c=new Date(d,e,f),(b-c)/864e5},a}if(a.setHours(0,0,0,0),b.setHours(0,0,0,0),c=c.toLocaleUpperCase(),d=h(),f=m(c,["Y","M","D","MD","YM","YD"]),b<a)return I;if(f<0)throw e.sR().Exp_NotSupported;return(g=d[f])(a,b)}function Vc(a,b){var c,d,e={value:0};if(!ha(a,e))return E;if(c={value:0},!ha(b,c))return E;d=2958465;function f(a){return a>=0&&a<d+1}return f(e.value)&&f(c.value)?e.value-c.value:I}function Wc(a){var b,c,d;if(isNaN(a))return I;function e(a){var b=new Date(a,0,4),c=b.getDay();return 0===c&&(c=7),b.setDate(b.getDate()+1-c),b}return b=new Date(1900,1,28),a.getTime()<=b.getTime()&&a.setDate(a.getDate()-1),1900===(c=a.getFullYear())?c+=400:9999===c&&(c-=400),a.setYear(c),a.getTime()<new Date(c,11,29).getTime()&&c--,d=e(c+1),a.getTime()<d.getTime()&&(d=e(c)),r(""+(a.getTime()-d.getTime())/864e5/7)+1}function Xc(a){var b,c,d,e=""+a,f="",g=[];if(a<0&&(e=e.substr(1),f="-"),c=e.split("."),d=k(c),d<1||d>2)return E;for(2===d&&(g.push(c[1]),g.push(".")),e=c[0],b=k(e)-3;b>=0;b-=3)g.push(e.substr(b,3)),b>0&&g.push(",");return g.push(e.substring(0,b+3)),g.reverse(),f+g.join("")}function Yc(a,b){var c,d,e,f,g=[],h=k(a);for(c=0;c<h;c++){if(e=a[c],f=k(e),0===f&&"concat"===b)return C;for(d=0;d<f;d++)n(e[d])||g.push(Q(e[d]))}return g.join("")}function Zc(a,b,c){var d,e,f=[],g=0;for(d=0;d<b.length;d++)if(e=Y(b[d]),"fourByte"===e){if(0===g){g++;continue}g=0,f.push(b[d-1]+b[d])}else f.push(b[d]);return c>=f.length?b:f.splice(a?0:f.length-c,c).join("")}function $c(a){var b=[],c,d;for(c=0;c<k(a);c++)d=a.charCodeAt(c),0<=d&&d<=31||127===d||128<=d&&d<=159||b.push(a[c]);return b.join("")}function _c(a){var b,c,d,e,f;for(a=a.trim(),b=[],c=!0,d=0;d<k(a);d++)e=a.charAt(d),f=" "===e||"\t"===e||"\n"===e,f&&!c||b.push(e),c=!f;return b.join("")}function ad(a,b){function c(a){var b=Xc(a),c=[];return a<0&&(b=b.substr(1)),c.push("$"),c.push(b),a<0&&(c.push(")"),c.unshift("(")),c.join("")}return a=P(kb(a,b)),c(a)}function bd(a,b,c){var d,e,f,g,h,i=0;if(b<0&&(i=O(B(10,s(b))),a/=i),a=P(kb(a,u(0,b))),b<0&&(a*=i),d=c?""+a:Xc(a),b>0)if(e=d.indexOf("."),e===-1)for(d+=".",f=0;f<b;f++)d+="0";else for(g=d.length-(e+1),h=g;h<b;h++)d+="0";return d}function cd(a,b){var c,e;if(""===b)return Q(a);n(a)&&(a=0);try{return c=d.Formatter&&d.Formatter.GeneralFormatter,c?(e=new c(b),e.format(a)):""+a}catch(a){return E}}function dd(a){if(!a)return 0;var b=j(a),c;return n(b)?ed(a):(c=i(b),K(c)?Cc(b):c)}function ed(a){var b,c,f,h,i,j,k,l,m,n,o=d.Common.CultureManager.q4().NumberFormat;if("$"!==a[0]&&a[0]!==o.currencySymbol||(a=a.substring(1)),b=q(a),c=e.isDigit(a[0].charCodeAt(0)),f=45===a[0].charCodeAt(0)&&e.isDigit(a[1].charCodeAt(0)),h=!a||p(b)||!c&&"."!==a[0],i=!a||p(b)||!f&&"-"!==a[0]&&"."!==a[1],h&&i)return E;for(("."===a[0]||"-"===a[0]&&"."===a[1])&&("-"===a[0]?a.replace(a[1],"0."):a="0"+a),j=a.length,k=0,m=0,n=0;k<j;k++){for(l=a[k];k<j&&e.isDigit(l.charCodeAt(0));)k++,l=a[k];if(k===j){if(m&&k-m<=3)return E;break}if(l===o.numberGroupSeparator){if(n||m&&k-m<=3)return E;m=k}else if(l===o.numberDecimalSeparator){if(n||m&&k-m<=3)return E;n=k}}return a=g.Gb(a,",",""),q(a)}function fd(a){return a.toLowerCase()}function gd(a){return a.toUpperCase()}function hd(a){var b,c,d,f=k(a),g=[];if(f>0)for(b=a.toUpperCase(),c=a.toLowerCase(),d=1,g[0]=b[0];d<f;)g[d]=e.ei(a[d-1])?c[d]:b[d],d++;return g.join("")}function id(a){return String.fromCharCode(a)}function jd(a){return a[0].charCodeAt(0)}function kd(a,b,c,d){var e,f,g=k(a);return b=v(b,g+1),c=v(c||0,g-b+1),e=a.substring(0,b-1),f=a.substr(b-1+c),e.concat(d).concat(f)}function ld(a,b,c,d){var f,g,h,i,j,k,l,m;if(Bd()||e.usedbcs){for(f=[],g=void 0,h=0,i=0;i<a.length;i++)g=a[i],j=Y(a[i]),"doubleByte"===j&&(h++,g=a[i]),f[i+h]=g;return k=f.length,b=v(b,k+1),c=v(c||0,k-b+1),n(f[b-1])?(f[b-1]=" ",f[b]=" "):n(f[b-2])&&b>=2&&(f[b-2]=" ",f[b-1]=" "),l=f.slice(0,b-1).join(""),m=f.slice(b-1+c).join(""),l.concat(d).concat(m)}return kd(a,b,c,d)}function md(a,b,c,d){var e,f,h,i,j,l,m=k(b);if(k(arguments)>3){if(f=O(d),h=0,i=void 0,f<1)return E;for(i=0;i<f;i++){if(h=a.indexOf(b,h),h===-1)return a;h+=m}j=a.substring(0,h-m),l=a.substr(h),e=j.concat(c).concat(l)}else e=g.yb(a,b,c);return e}function nd(){return Yc(arguments,"concatenate")}function od(){return Yc(arguments,"concat")}function pd(a,b){var c,d,e,f,g,h,i;if(a.isReference&&0===a.length)return C;if(c={value:!1},d=!!n(b[0])||b[0],!ia(d,c))return E;for(d=c.value,e=arguments,f=[],g=[],h=k(e),i=2;i<h;i++)qd(f,e[i],d);return qd(g,e[0],!1),rd(f,g)}function qd(a,b,c){var d,e=k(b);if(0===e)return C;for(d=0;d<e;d++)n(b[d])||""===b[d]?c||a.push(""):a.push(Q(b[d]))}function rd(a,b){if(0===a.length)return"";if(1===b.length)return a.join(b[0]);var c=a[0],d,e;for(d=1,e=0;d<a.length;d++,e++)e===b.length&&(e=0),c+=b[e]+a[d];return c}function sd(a,b){return Zc(!0,a,b)}function td(a,b){return Zc(!1,a,b)}function ud(a,b,c){var d,f,g,h,i,j,k,l,m;if(Bd()||e.usedbcs){for(d=[],f=void 0,g=0,h=void 0,i=0,h=0;h<b.length;h++)if(f=b[h],j=Y(b[h]),"fourByte"===j){if(g%2===0){g++;continue}g++,f=b[h-1]+b[h],d[d.length]=f}else"doubleByte"===j&&(i++,f=b[h]),k=g>0?g/2:0,d[h-k+i]=f;return a||(d=$(d)),l=d.length,c>=l?b:(m=void 0,a?(m=d.slice(0,c-1<0?0:c),m.length>0&&n(m[m.length-1])&&(m[m.length-1]="\u200b")):(m=d.slice(l-c),m.length>0&&n(m[0])&&(m[0]="\u200b")),m.join(""))}return Zc(a,b,c)}function vd(a,b){return ud(!0,a,b)}function wd(a,b){return ud(!1,a,b)}function xd(a,b,c){b--;var d=k(a);return b>=d?"":d<b+c?a.substr(b):a.substr(b,c)}function yd(a,b,c){var d,f,g;return Bd()||e.usedbcs?(d=Z(a),f=d.length,b--,b<0?E:b>=f?"":f<b+c?d.slice(b).join(""):(g=d.slice(b,b+c),"singleByte"!==Y(g[g.length-1])&&(g[g.length-1]="\u200b",n(g[0])&&(g[g.length]="\u200b")),g.join(""))):xd(a,b,c)}function zd(a,b){var c=[],d;if(b<0||b*k(a)>32767)return E;for(d=0;d<b;d++)c.push(a);return c.join("")}function Ad(a){return a?a.length:0}function Bd(){var a=d.Common.CultureManager.q4();return!!a.isJCKCulture}function Cd(a){var b,c,f,g;if(!a)return 0;if(Bd()||e.usedbcs){for(b=0,c=0;c<a.length;c++)if(8203!==a[c].charCodeAt(0)&&(f=Y(a[c]),g=d.Common.CultureManager.q4(),"doubleByte"===f&&9390!==a[c].charCodeAt(0))){if("ja-jp"===(g&&g.name().toLocaleLowerCase())&&(711===a[c].charCodeAt(0)||9356===a[c].charCodeAt(0)||8212===a[c].charCodeAt(0)))continue;b++}return a.length+b}return a.length}function Dd(a,b,c){if(c<1||k(b)<c)return E;var d=b.indexOf(a,c-1);return d===-1?E:d+1}function Ed(a,b,c){var d,f,g,h,i,j;return Bd()||e.usedbcs?(d=Z(b),f=Z(a),g=d.length,c<1||g<c?E:(h=Gd(d),i=Gd(f),j=h.indexOf(i,c-1),j===-1?E:j+1)):Dd(a,b,c)}function Fd(a,b,c){var d=-1,e,g;return e=f.ub(a),e?(g=f.sb(e).exec(b),d=n(g)?-1:g.index):d=b.toLowerCase().indexOf(a.toLowerCase(),--c),d===-1?E:d+1}function Gd(a){for(var b=0;b<a.length;b++)n(a[b])&&a[b-1]&&2!==a[b-1].length&&(a[b]="\u200b");return a.join("")}function Hd(a){for(var b=0;b<a.length;b++)n(a[b])&&(a[b]="\u200b");return a}function Id(a,b,c){var d,f,g,h;return Bd()||e.usedbcs?(d=Z(b),f=Z(a),g=d.length,c<1||g<c?E:(h=Fd(Gd(f),Gd(d),c),h===E?E:h)):Fd(a,b,c)}function Jd(a,b){return a===b}function Kd(a){return W(a)?a:""}function Ld(a){if(a){var b=a;return b<=0||b>1114111?E:b>=55296&&b<=57343?H:b<1048576?String.fromCharCode(b):(b-=1048576,String.fromCharCode((b>>10)+55296,(1023&b)+56320))}return E}function Md(a){var b,c,d=Q(a);return""===d||null===d?E:(b=d.charCodeAt(0))>=55296&&b<=56319?1===d.length?E:(c=void 0,(c=d.charCodeAt(1))>=56320&&c<=57343?b|c<<8:1):b>=56320&&b<=57343?b-53248+53248:b}function Nd(a){var b,c;function d(a){var b,c,d,e,f=["\u0e25\u0e49\u0e32\u0e19","\u0e2a\u0e34\u0e1a","\u0e23\u0e49\u0e2d\u0e22","\u0e1e\u0e31\u0e19","\u0e2b\u0e21\u0e37\u0e48\u0e19","\u0e41\u0e2a\u0e19",""],g=["\u0e28\u0e39\u0e19\u0e22\u0e4c","\u0e2b\u0e19\u0e36\u0e48\u0e07","\u0e2a\u0e2d\u0e07","\u0e2a\u0e32\u0e21","\u0e2a\u0e35\u0e48","\u0e2b\u0e49\u0e32","\u0e2b\u0e01","\u0e40\u0e08\u0e47\u0e14","\u0e41\u0e1b\u0e14","\u0e40\u0e01\u0e49\u0e32"],h=["\u0e25\u0e1a","\u0e1a\u0e32\u0e17","\u0e16\u0e49\u0e27\u0e19","\u0e2a\u0e15\u0e32\u0e07\u0e04\u0e4c","\u0e22\u0e35\u0e48","\u0e40\u0e2d\u0e47\u0e14",","," ","\u0e3f"],i="",j=a+"",k=j.length;for(b=k;b>0;--b){if(c=r(j[k-b]),d=g[c],e=1<b?(b-1)%6:6,1===e&&2===c&&(d=h[4]),1===c)switch(e){case 0:case 6:i+=b<k?h[5]:d;break;case 1:break;default:i+=d}else{if(0===c){0===e&&(i+=f[e]);continue}i+=d}i+=f[e]}return i}return a?(b=Math.abs(r(a)),c=parseFloat((Math.abs(a)-b).toFixed(2)),0!==b?0===c?(a<0?"\u0e25\u0e1a":"")+d(b)+"\u0e1a\u0e32\u0e17\u0e16\u0e49\u0e27\u0e19":(a<0?"\u0e25\u0e1a":"")+d(b)+"\u0e1a\u0e32\u0e17"+d(100*c)+"\u0e2a\u0e15\u0e32\u0e07\u0e04\u0e4c":0!==c?(a<0?"\u0e25\u0e1a":"")+d(100*c)+"\u0e2a\u0e15\u0e32\u0e07\u0e04\u0e4c":"\u0e28\u0e39\u0e19\u0e22\u0e4c\u0e1a\u0e32\u0e17\u0e16\u0e49\u0e27\u0e19"):E}function Od(a){return K(a)}function Pd(a){return!!K(a)&&a._code!==H._code}function Qd(a){return!!K(a)&&a._code===H._code}function Rd(a){if(!n(a)&&K(a)){var b=m(a._code,[C._code,D._code,E._code,F._code,G._code,I._code,H._code]);if(b>=0)return b+1}return H}function Sd(a){return R(a)}function Td(a){return fa(s(a))%2===0}function Ud(a){return fa(s(a))%2!==0}function Vd(a){return R(a)?P(a):V(a)?a?1:0:K(a)?a:0}function Wd(a){return n(a)}function Xd(a){return V(a)}function Yd(a){return W(a)}function Zd(a){return!W(a)}function $d(a){return L(a)}function _d(a){return V(a)?4:R(a)?1:W(a)?2:K(a)?16:M(a)?64:E}function ae(){return H}function be(a,b){var c,d,f=0,g=a.source.kj;if(M(b))return H;if(b instanceof e.CalcReference||n(b))return c=b,d=c&&(c.xf||c.references[0].xf)||a.source,g.parent.getSheetIndex(d.kj.name())+1;if(R(b)||W(b))for(f;f<g.parent.sheets.length;f++)if(b.toUpperCase()===g.parent.sheets[f].name().toUpperCase())return f+1;return H}function ce(a,b){var c,d,f,g=a.source.kj;return n(b)?g.parent.sheets.length:M(b)||R(b)?H:W(b)?E:b instanceof e.CalcReference?(c=b,(d=c.references)?d.length:(f=c.xf||a.source,g.parent.getSheetIndex(f.kj.name())+1)):H}function de(){var a=new e.Functions.AsyncFunction("REFRESH",1,3);a.evaluate=ee,e.Functions.bi.REFRESH=a}function ee(a,b){return b}function fe(a){return a>=55296&&a<=57343}function ge(a,b){return!(a<55296||a>56319)&&(b>=56320&&b<=57343)}function he(a){var b,c,d,e,f,g="";if((b=a.length-1)<0)return"";for(c=0;c<=b;c++)if(d=a.charCodeAt(c),d>="A".charCodeAt(0)&&d<="Z".charCodeAt(0)||d>="0".charCodeAt(0)&&d<="9".charCodeAt(0)||d>="a".charCodeAt(0)&&d<="z".charCodeAt(0)||d==="-".charCodeAt(0)||d==="_".charCodeAt(0)||d===".".charCodeAt(0))g+=a[c];else{if(e=void 0,fe(d)){if(b===c||!ge(d,a.charCodeAt(c+1)))return null;e=[d,a.charCodeAt(c+1)],c++}else e=[d];for(f=0;f<e.length;f++)g+="%",g+=e[f].toString(16).toUpperCase()}return g}ka={Hi:0},la={Hi:0,aj:"= 0",bj:D},ma={Hi:0,aj:"= 0",bj:0},na={_i:1,Hi:0,aj:"= 0",bj:0},oa={Hi:2,aj:"< 0"},pa={Hi:0,aj:["> 134217728","< -134217728"]},qa={Hi:1},ra={Hi:1,aj:"< 0"},sa={Hi:2},ta={_i:1,Hi:2},ua={_i:1,Hi:2,aj:"< 0",bj:E},va={Hi:5},wa={Hi:6},xa={Hi:2,aj:"< 0",bj:E},ya={_i:[],Hi:4,Ii:4,Ji:!0,Ki:!0,Li:!0},za={Hi:4,Ii:1,Ji:!0,Ki:!0},Aa={Hi:4,Ii:1,Ki:!0,Li:!0},Ba={Hi:4,Ii:1,Ji:!0,Ki:!0,Xi:!0},Ca={Hi:4,Ii:3,Ji:!0,Ki:!0,Xi:!0},ga("ABS",Ka,1,1,ka),ga("ACOS",Mb,1,1,{Hi:0,aj:["> 1","< -1"]}),ga("ACOT",Ub,1,1,ka),ga("ASIN",Lb,1,1,{Hi:0,aj:["> 1","< -1"]}),ga("ATAN",Tb,1,1,ka),ga("ATAN2",Vb,2,2,[ka,ka]),ga("COS",Hb,1,1,ka),ga("SEC",Ib,1,1,pa),ga("CEILING",eb,2,2,[ma,ma]),ga("ODD",Qa,1,1,qa),ga("EVEN",Ra,1,1,qa),ga("FLOOR",fb,2,2,[ma,ma]),ga("LN",Ua,1,1,{Hi:1,aj:"<= 0"}),ga("SQRT",bb,1,1,{Hi:1,aj:"< 0"}),ga("SIN",Fb,1,1,ka),ga("CSC",Gb,1,1,pa),ga("TAN",Jb,1,1,ka),ga("COT",Kb,1,1,pa),ga("SIGN",Za,1,1,{Hi:1,aj:"> 0",bj:1}),ga("GCD",$a,1,o,Ba,-1,-1),ga("LCM",_a,1,o,Ba,-1,-1),ga("PRODUCT",ab,1,o,o,-1,-1),ga("POWER",Xa,2,2,[qa,qa]),ga("MOD",Va,2,2,[qa,{Hi:1,aj:"= 0",bj:D}]),ga("QUOTIENT",cb,2,2,[ka,la],o,o),ga("SUBTOTAL",db,2,o,sa,"!= 0"),ga("INT",La,1,1,qa),ga("MROUND",jb,2,2,[ma,ma]),ga("ROUND",kb,2,2,[ka,sa]),ga("ROUNDDOWN",lb,2,2,[ka,sa]),ga("ROUNDUP",mb,2,2,[ka,sa]),ga("TRUNC",lb,1,2,[ka,{_i:0,Hi:2}],o,o,{bk:1}),ga("EXP",nb,1,1,ka),ga("LOG",qb,1,2,[{Hi:0,aj:"<= 0"},{_i:10,Hi:0,aj:"<= 0"}],o,o,{bk:1}),ga("LOG10",rb,1,1,{Hi:0,aj:"<= 0"}),ga("SUM",Ya,1,o,o,-1,-1),ga("SUMIF",sb,2,3,o,[0,2],[0,2],{bk:2}),ga("SUMIFS",tb,3,o,{Hi:4,Ii:1,Ji:!0},[0,"%= 1"],[0,"%= 1"]),ga("SUMPRODUCT",xb,1,o,o,-1,-1,{fk:1}),ga("SUMSQ",yb,1,o,Ba,-1,-1),ga("SUMX2MY2",zb,2,2,Ba,-1,-1),ga("SUMX2PY2",Ab,2,2,Ba,-1,-1),ga("SUMXMY2",Bb,2,2,Ba,-1,-1),ga("SERIESSUM",Cb,4,4,[ka,sa,sa,{Hi:4,Ii:1,Ji:!0,Ki:!0,Li:!0}],3,3),ga("PI",Wa,0,0),ga("SQRTPI",Db,1,1,{Hi:0,aj:"< 0"}),ga("DEGREES",Pa,1,1,qa),ga("RADIANS",Eb,1,1,ka),ga("COSH",Pb,1,1,ka),ga("SECH",Qb,1,1,ka),ga("ACOSH",Sb,1,1,{Hi:0,aj:"< 0"}),ga("SINH",Nb,1,1,ka),ga("CSCH",Ob,1,1,ka),ga("ASINH",Rb,1,1,ka),ga("TANH",Wb,1,1,ka),ga("COTH",Xb,1,1,{Hi:0}),ga("ATANH",Yb,1,1,{Hi:0,aj:["<= -1",">= 1"]}),ga("ACOTH",Zb,1,1,{Hi:0}),ga("MDETERM",$b,1,1,Aa,-1,-1),ga("MINVERSE",_b,1,1,Aa,-1,-1),ga("MMULT",ac,2,2,[Aa,Aa],-1,-1),ga("FACT",Sa,1,1,{Hi:3,aj:["< 0","> 170"]}),ga("FACTDOUBLE",Ta,1,1,{Hi:3,aj:["< 0","> 300"]}),ga("MULTINOMIAL",bc,1,o,{Hi:4,Ii:0,Ji:!0,Ki:!0,Xi:!0},-1,-1),ga("RAND",cc,0,0,o,o,o,{ck:!0}),ga("RANDBETWEEN",dc,2,2,[sa,sa],o,o,{ck:!0}),ga("COMBIN",Ma,2,2,[ra,ra]),ga("COMBINA",Na,2,2,[oa,oa]),ga("DECIMAL",Oa,2,2,[{_i:"0",Hi:5},{Hi:0,aj:["> 36","< 2"]}]),ga("CEILING.MATH",gb,1,3,[ka,{Hi:0,_i:1},{Hi:0,_i:0}]),ga("FLOOR.MATH",hb,1,3,[ka,{Hi:0,_i:1},{Hi:0,_i:0}]),ga("BASE",ib,2,3,[{Hi:0,aj:["< 0",">= 9007199254740992"]},{Hi:3,aj:["> 36","< 2"]},{Hi:3,_i:0,aj:["< 0",">= 256"]}]),ga("ROMAN",ec,1,2,[{Hi:2,aj:["< 0","> 3999"],bj:E},{_i:0,Hi:2,aj:["< 0","> 4"],bj:E}],o,o,{bk:1}),ga("ARABIC",fc,1,1,va),ga("CEILING.PRECISE",ob,1,2,[ma,na]),ga("ISO.CEILING",ob,1,2,[ma,na]),ga("FLOOR.PRECISE",pb,1,2,[ma,na]),ga("MAXIFS",ub,3,o,za,[0,"%= 1"],[0,"%= 1"]),ga("MINIFS",vb,3,o,za,[0,"%= 1"],[0,"%= 1"]),
ga("MUNIT",wb,1,1,ka),ga("AND",hc,1,o,Ca,-1,-1),ga("OR",ic,1,o,Ca,-1,-1),ga("NOT",jc,1,1),ga("IF",kc,2,3,o,[1,2],[1,2],{gk:[1,2],bk:2,isBranch:!0,findTestArgument:0,findBranchArgument:function(a){if(e.Convert.vf(a))return-1;var b={value:!1};return N(a,b),b.value?1:2}}),ga("IFERROR",mc,2,2,o,0,o,{gk:-1}),ga("TRUE",nc,0,0),ga("FALSE",oc,0,0),ga("IFNA",pc,2,2,o,0,o,{gk:-1}),ga("IFS",qc,2,254,o,0,o,{gk:-1}),ga("SWITCH",rc,3,253,o,1,o,{gk:-1}),ga("XOR",sc,1,o,Ca,-1,-1),ga("DATE",zc,3,3,[{Hi:2,aj:["< 0","> 9999"]},sa,sa]),ga("TIME",Ac,3,3,[sa,sa,sa]),ga("DATEVALUE",Bc,1,1,{Ni:!0}),ga("TIMEVALUE",Cc,1,1,{Ni:!0}),ga("NOW",Dc,0,0,o,o,o,{ck:!0}),ga("TODAY",Ec,0,0,o,o,o,{ck:!0}),ga("HOUR",Fc,1,1,wa),ga("MINUTE",Gc,1,1,wa),ga("SECOND",Hc,1,1,wa),ga("DAY",Ic,1,1,wa),ga("MONTH",Jc,1,1,wa),ga("YEAR",Kc,1,1,wa),ga("WEEKNUM",Lc,1,2,[wa,ta],o,o,{bk:1}),ga("WEEKDAY",Mc,1,2,[wa,ta],o,o,{bk:1}),ga("EDATE",Nc,2,2,[wa,sa]),ga("EOMONTH",Oc,2,2,[wa,sa]),ga("WORKDAY",Pc,2,3,[wa,sa,ya],2,2,{bk:2}),ga("WORKDAY.INTL",Qc,2,4,[wa,sa,{_i:"0000011"},ya],[2,3],[2,3],{bk:[2,3]}),ga("DAYS360",e.Functions.uk,2,3,[wa,wa,{_i:!1,Hi:7}],o,o,{bk:2}),ga("NETWORKDAYS",Rc,2,3,[wa,wa],2,2,{bk:2}),ga("NETWORKDAYS.INTL",Tc,2,4,[wa,wa],[2,3],[2,3],{bk:[2,3]}),ga("YEARFRAC",e.Functions.vk,2,3,[wa,wa,{_i:0,Hi:2}],o,o,{bk:2}),ga("DATEDIF",Uc,3,3,[wa,wa,va]),ga("DAYS",Vc,2,2),ga("ISOWEEKNUM",Wc,1,1,wa),ga("CLEAN",$c,1,1,va),ga("TRIM",_c,1,1,va),ga("DOLLAR",ad,1,2,[ka,{_i:2,Hi:2,aj:"> 99",bj:E}],o,o,{bk:1}),ga("FIXED",bd,1,3,[ka,{_i:2,Hi:2},{_i:!1,Hi:7}],o,o,{bk:[1,2]}),ga("TEXT",cd,2,2,[{},va]),ga("VALUE",dd,1,1,va),ga("LOWER",fd,1,1,va),ga("UPPER",gd,1,1,va),ga("PROPER",hd,1,1,va),ga("CHAR",id,1,1,{Hi:2,aj:["> 255","< 1"],bj:E}),ga("CODE",jd,1,1,{Hi:5,Ni:!0}),ga("REPLACE",kd,4,4,[va,{Hi:2,aj:"< 1",bj:E},xa,va],o,o,{bk:2}),ga("SUBSTITUTE",md,3,4,[va,{Hi:5,Ni:!0},va],o,o,{bk:3}),ga("CONCATENATE",nd,1,o,{Hi:4,Ii:0,Ji:!0,Xi:!0},o,-1),ga("LEFT",sd,1,2,[va,ua],o,o,{bk:1}),ga("MID",xd,3,3,[va,{Hi:2,aj:"<= 0",bj:E},xa]),ga("RIGHT",td,1,2,[va,ua],o,o,{bk:1}),ga("REPT",zd,2,2,[va,sa]),ga("LEN",Ad,1,1,va),ga("FIND",Dd,2,3,[va,va,ta],o,o,{bk:2}),ga("SEARCH",Fd,2,3,[va,va,ua],o,o,{bk:2}),ga("EXACT",Jd,2,2,[va,va]),ga("T",Kd,1,1),ga("UNICHAR",Ld,1,1,[{Hi:2,bj:E}]),ga("UNICODE",Md,1,1),ga("BAHTTEXT",Nd,1,1,[{Hi:0,bj:E}]),ga("CONCAT",od,1,o,{Hi:4,Ji:!0,Ki:!0,Xi:!0},-1,-1),ga("TEXTJOIN",pd,3,o,{Hi:4,Ji:!0,Ki:!0,Xi:!0},-1,-1),ga("FINDB",Ed,2,3,[va,va,ta],o,o,{bk:2}),ga("LEFTB",vd,1,2,[va,ua],o,o,{bk:1}),ga("RIGHTB",wd,1,2,[va,ua],o,o,{bk:1}),ga("MIDB",yd,3,3,[va,xa,xa]),ga("LENB",Cd,1,1,va),ga("REPLACEB",ld,4,4,[va,{Hi:2,aj:"< 1",bj:E},xa,va],o,o,{bk:2}),ga("SEARCHB",Id,2,3,[va,va,ua],o,o,{bk:2}),ga("ENCODEURL",he,0,1,va),ga("ISERROR",Od,1,1,o,o,o,{gk:-1}),ga("ISERR",Pd,1,1,o,o,o,{gk:-1}),ga("ISNA",Qd,1,1,o,o,o,{gk:-1}),ga("ERROR.TYPE",Rd,1,1,o,o,o,{gk:-1}),ga("ISNUMBER",Sd,1,1,o,o,o,{gk:-1}),ga("ISEVEN",Td,1,1,ka,o,o),ga("ISODD",Ud,1,1,ka,o,o),ga("N",Vd,1,1,o,o,o),ga("ISBLANK",Wd,1,1,o,o,o,{gk:-1}),ga("ISLOGICAL",Xd,1,1,o,o,o,{gk:-1}),ga("ISTEXT",Yd,1,1,o,o,o,{gk:-1}),ga("ISNONTEXT",Zd,1,1,o,o,o,{gk:-1}),ga("ISREF",$d,1,1,o,-1,o,{gk:-1}),ga("TYPE",_d,1,1,o,o,-1,{gk:-1}),ga("NA",ae,0,0),ga("SHEET",be,0,1,o,-1,o,{dk:!0}),ga("SHEETS",ce,0,1,o,-1,o,{dk:!0}),de()},CalcEngine:function(a,b){a.exports=c("@grapecity/js-calc")},Common:function(a,b){a.exports=c("@grapecity/js-sheets-common")}}),a.exports=d.Spread.CalcEngine.BasicFunctions},"./node_modules_local/@grapecity/js-calc-basicfunctions/index.js":function(a,b,c){a.exports=c("./node_modules_local/@grapecity/js-calc-basicfunctions/dist/gc.spread.calcEngine.basicfunctions.js")},"@grapecity/js-calc":function(a,b){a.exports=GC.Spread.CalcEngine},"@grapecity/js-sheets-common":function(a,b){a.exports=GC.Spread}});