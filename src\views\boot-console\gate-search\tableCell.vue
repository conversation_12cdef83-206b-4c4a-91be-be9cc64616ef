<template>
  <div>
    {{ tableCell }}
  </div>
</template>
<script>
export default {
  props: {
    record: Object, // 当前操作行
    text: String,
    name: String // 当前字段名
  },
  data() {
    return {};
  },
  computed: {
    tableCell() {
      const arr = ["gs", "jd", "cj", "xt"];
      const data = [];
      arr.forEach(item => {
        data.push(this.record[item]);
      });
      return data.filter(item => item).join("-");
    }
  }
};
</script>
