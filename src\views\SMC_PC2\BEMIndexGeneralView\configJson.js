/*
 * @Description: 组件配置描述json
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-07 15:58:35
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-12-14 14:48:02
 */
const comJson = {
  json: [
    {
      type: "input",
      col: "companyName",
      label: "注册公司名称"
    },
    {
      type: "input",
      col: "signOrgId",
      label: "注册公司编码"
    },
    {
      type: "input",
      col: "orgLabelName",
      label: "组织下拉框名称",
      props: {}
    },
    {
      type: "input",
      col: "operationGuideJSUrl",
      label: "操作引导组件jsUrl",
      props: {}
    },
    {
      type: "input",
      col: "indexCardDetailInfoJSUrl",
      label: "指标卡片详情组件jsUrl",
      props: {}
    },
    {
      type: "input",
      col: "indexDetectionInfoJSUrl",
      label: "指标卡片组织下探组件jsUrl",
      props: {}
    },
    {
      type: "input",
      col: "indexMDYSFXInfoJSUrl",
      label: "指标末端因素分析组件jsUrl",
      props: {}
    },
    {
      type: "input",
      col: "selfReportsJSUrl",
      label: "个人专属报告jsUrl",
      props: {}
    }
  ],
  props: {
    companyName: "空调",
    signOrgId: "H0203",
    orgLabelName: "组织",
    operationGuideJSUrl: "/smcbucket/VideoCom.umd.min.1.0.js",
    indexCardDetailInfoJSUrl: "/smcbucket/IndexCardDetailInfo2.umd.min.1.0.js",
    selfReportsJSUrl: "/smcbucket/SelfReports2.umd.min.1.0.js",
    indexDetectionInfoJSUrl: "/smcbucket/IndexDetectionInfo2.umd.min.1.0.js",
    indexMDYSFXInfoJSUrl: "/smcbucket/IndexMMYSFXInfo.umd.min.1.0.js"
  },
  buttonList: []
};
export default comJson;
