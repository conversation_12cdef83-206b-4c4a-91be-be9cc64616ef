<!--
 * @Description: 指标卡片共用html显示组件
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-01-17 15:36:50
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 09:42:50
-->
<template>
  <!-- 自动生成的内容 -->
  <div style="padding: 5px 0;">
    <a-divider>指标信息</a-divider>
    <div ref="autoContent" style="padding: 10px;line-height: 20px;">
      <div>指标信息展示(系统自动呈现)：</div>
      <br />
      <div>
        <div>
          {{ cardItem.businessSegments }}-{{
            cardItem.wdInCardName ? "(" + cardItem.wdInCardName + ")" : ""
          }}{{ cardItem.displayIndexName }}（{{ cardItem.companyName
          }}{{ cardItem.org }}-{{
            cardItem.indexDt + "" + cardItem.indexFrequency
          }}）
        </div>
        <div>
          目标值：{{ cardItem.targetValue }} {{ cardItem.indexUnitId }}；
          实际值：{{ cardItem.actualValue }} {{ cardItem.indexUnitId }}；
          {{
            cardItem.isContemRate === "Y"
              ? showText(
                  "contemRate",
                  cardItem.contemChangeRate,
                  cardItem.indexType
                )
              : ""
          }}
          {{
            cardItem.isPreviousRate === "Y"
              ? showText(
                  "previousRate",
                  cardItem.previousChangeRate,
                  cardItem.indexType
                )
              : ""
          }}
        </div>
        <br />
        <div class="card" ref="cardItem">
          <div
            style="
				display: inline-block;
				background: #fff;
        border: 1px solid rgba(0, 0, 0, 0.1);
				border-radius: 12px;
				padding: 20px;
				box-sizing: border-box;
				min-width: 227px;
			"
          >
            <!-- 组织、标题 -->
            <div style="display: flex; align-items: center; margin-bottom: 8px">
              <span
                style="
						height: 20px;
						border-radius: 2px;
						line-height: 20px;
						padding: 0 4px;
						font-size: 12px;
						margin-right: 8px;
					"
                :style="{
                  backgroundColor: this.businessSegmentsColorMap()[
                    cardItem.businessSegments
                  ].bgColor,
                  color: this.businessSegmentsColorMap()[
                    cardItem.businessSegments
                  ].color
                }"
              >
                {{ cardItem.businessSegments }}
              </span>
              <span
                style="
                height: 20px;
                border-radius: 2px;
                line-height: 20px;
                padding: 0 4px;
                font-size: 12px;
                margin-right: 8px;
                color: #53667a;
                background-color: #e0e3ea;
                "
                v-for="(tag, tagIdx) in cardItem.wdInCardTag"
                :key="`${tag}${tagIdx}`"
              >
                {{ tag }}
              </span>
            </div>
            <div
              style="
              height: 22px;
              font-size: 14px;
              line-height: 22px;
              color: rgb(78, 89, 105);
              margin-right: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 3px;"
            >
              {{ cardItem.wdInCardName ? cardItem.wdInCardName + " - " : "" }}
              {{ cardItem.displayIndexName }}
            </div>
            <!-- 实际值 -->
            <div
              style="
					font-size: 24px;
					color: rgba(0, 0, 0, 0.85);
					line-height: 32px;
					margin-bottom: 8px;
				"
            >
              {{ cardItem.actualValue }}
              <span style="font-size: 16px;line-height: 32px;">{{
                cardItem.indexUnitId
              }}</span>
            </div>
            <!-- 目标值 -->
            <div
              style="
					font-size: 12px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 18px;
					margin-bottom: 8px;
				"
            >
              <span style="margin-right: 8px">目标值</span>
              {{ cardItem.targetValue }}
              {{ cardItem.indexUnitId }}
            </div>
            <!-- 完成率 -->
            <div
              style="
					display: flex;
					align-items: center;
					font-size: 12px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 18px;
					padding-bottom: 11px;
					border-bottom: 1px solid rgba(0, 0, 0, 0.1);
					margin-bottom: 12px;
				"
            >
              <span style="margin-right: 8px">完成率</span>
              <div
                style="
						width: 100px;
						height: 8px;
						background: #f0f0f0;
						position: relative;
						margin-right: 16px;
					"
              >
                <span
                  style="
							position: absolute;
							left: 0;
							top: 0;
							height: 8px;
						"
                  :style="{
                    width: `${
                      cardItem.targetCompletionRate
                        ? parseFloat(cardItem.targetCompletionRate) * 100 > 100
                          ? 100
                          : parseFloat(cardItem.targetCompletionRate) * 100
                        : 0
                    }%`,
                    'background-color': cardItem.targetCompletionRate
                      ? parseFloat(cardItem.targetCompletionRate) < 100
                        ? '#6495f9'
                        : '#f75050'
                      : '#f0f0f0'
                  }"
                ></span>
              </div>
              <span>
                {{
                  `${
                    cardItem.targetCompletionRate
                      ? cardItem.targetCompletionRate
                      : "-%"
                  }`
                }}
              </span>
            </div>
            <!-- 同环比 -->
            <div
              style="
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 12px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 18px;
				"
            >
              <div style="flex: 1">
                <span style="margin-right: 8px">同比</span>
                <template v-if="cardItem.isContemRate === 'Y'">
                  <template v-if="cardItem.contemChangeRate">
                    <span
                      :style="styleObject(cardItem.contemChangeRate)"
                    ></span>
                  </template>
                  <span
                    :style="{
                      color: cardItem.contemChangeRate
                        ? cardItem.indexType === '正向'
                          ? cardItem.contemChangeRate.includes('-')
                            ? '#6495f9'
                            : '#f75050'
                          : cardItem.contemChangeRate.includes('-')
                          ? '#f75050'
                          : '#6495f9'
                        : 'rgba(0, 0, 0, 0.8);'
                    }"
                  >
                    {{
                      cardItem.contemChangeRate
                        ? Math.abs(
                            Decimal(cardItem.contemChangeRate)
                              .mul(Decimal(100))
                              .toFixed(2, Decimal.ROUND_HALF_UP)
                          ) + "%"
                        : "-"
                    }}
                  </span>
                </template>
                <span
                  v-if="cardItem.isContemRate !== 'Y'"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                >
                  不对比
                </span>
              </div>
              <div style="flex: 1">
                <span style="margin-right: 8px">环比</span>
                <template v-if="cardItem.isPreviousRate === 'Y'">
                  <template v-if="cardItem.previousChangeRate">
                    <span
                      :style="styleObject(cardItem.previousChangeRate)"
                    ></span>
                  </template>
                  <span
                    :style="{
                      color: cardItem.previousChangeRate
                        ? cardItem.indexType === '正向'
                          ? cardItem.previousChangeRate.includes('-')
                            ? '#6495f9'
                            : '#f75050'
                          : cardItem.previousChangeRate.includes('-')
                          ? '#f75050'
                          : '#6495f9'
                        : 'rgba(0, 0, 0, 0.8);'
                    }"
                  >
                    {{
                      cardItem.previousChangeRate
                        ? Math.abs(
                            Decimal(cardItem.previousChangeRate)
                              .mul(Decimal(100))
                              .toFixed(2, Decimal.ROUND_HALF_UP)
                          ) + "%"
                        : "-"
                    }}
                  </span>
                </template>
                <span
                  v-if="cardItem.isPreviousRate !== 'Y'"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                >
                  不对比
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Decimal from "decimal.js";
export default {
  props: {
    cardItem: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  inject: ["businessSegmentsColorMap"],
  data() {
    return {
      Decimal
    };
  },
  methods: {
    getCardItemHtmlContent() {
      return this.$refs["autoContent"].innerHTML;
    },
    // 渲染到页面的style内联样式
    styleObject(data) {
      const indexType = this.cardItem.indexType;
      let style = {
        width: 0,
        height: 0,
        "border-right": "5px solid transparent",
        "border-left": "5px solid transparent",
        position: "relative",
        "margin-right": "4px"
      };
      if (data) {
        if (indexType === "正向") {
          if (data.includes("-")) {
            style["border-top"] = "5px solid #6495f9";
            style["top"] = "10px";
          } else {
            style["border-bottom"] = "5px solid #f75050";
            style["top"] = "-10px";
          }
        } else {
          if (data.includes("-")) {
            style["border-bottom"] = "5px solid #f75050";
            style["top"] = "-10px";
          } else {
            style["border-top"] = "5px solid #6495f9";
            style["top"] = "10px";
          }
        }
        return style;
      } else {
        return {};
      }
    },
    // 展示到页面的文本
    showText(type, val, indexType) {
      let str = type === "contemRate" ? "同比" : "环比";
      if (val) {
        if (
          (val.includes("-") && indexType === "反向") ||
          (!val.includes("-") && indexType === "正向")
        ) {
          str += `上涨：`;
        } else {
          str += `下降：`;
        }
        str += `${Math.abs(
          Decimal(val)
            .mul(Decimal(100))
            .toFixed(2, Decimal.ROUND_HALF_UP)
        )}`;
      } else {
        str += `-`;
      }
      str += "%;";
      return str;
    }
  }
};
</script>
