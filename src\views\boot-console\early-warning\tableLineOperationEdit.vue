<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: yuy<PERSON><PERSON>e
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 14:18:58
-->
<template>
  <div style="display: inline-block;">
    <a @click="btClick">编辑</a>
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal-new.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      const record = this.record;
      // const record =  {"createdBy":null,"createdDate":null,"modifiedBy":"于永杰.ex","modifiedDate":"2023-05-06 15:15:55","remark":null,"id":345,"indexId":"ZBY00583","signOrgId":"H0104","businessSegmentsId":"SEG_1300","orgId":"CM_TREE002282","indexFrequencyId":"M","indexPushFrequency":"月","warnStatus":"1","warnReceiver":"<EMAIL>","indexTypeId":"TYPE_100","settings":null,"name":"智动精工公司-人资-合同工流失率","delFlag":0,"indexName":"合同工流失率","signOrg":"智动精工公司","businessSegments":"人资","org":"平度基地","indexFrequency":"月","indexType":"反向","rolelist":null,"completionRate":"<3","tb":"恶化>4","hb":"恶化<undefined","actualValueDeterioration":null,"noUpStandard":null,"executionTime":1,"symbol":null,"symbol1":null,"val":null,"indexDt":null,"classKeysignOrg":"visiblesignOrg1683357326028100001","classKeyorg":"visibleorg1683357326029100001","classKeybusinessSegments":"visiblebusinessSegments1683357326029100001","classKeyindexName":"visibleindexName1683357326030100001","classKeyindexFrequency":"visibleindexFrequency1683357326031100001","classKeycompletionRate":"visiblecompletionRate1683357326031100001","classKeyhb":"visiblehb1683357326032100001","classKeytb":"visibletb1683357326032100001","classKeyactualValueDeterioration":"visibleactualValueDeterioration1683357326033100001","classKeynoUpStandard":"visiblenoUpStandard1683357326033100001","classKeyindexPushFrequency":"visibleindexPushFrequency1683357326034100001","classKeyindexType":"visibleindexType1683357326034100001","classKeywarnStatus":"visiblewarnStatus1683357326035100001","classKeymodifiedDate":"visiblemodifiedDate1683357326035100001","classKeywarnReceiver":"visiblewarnReceiver1683357326036100001"}
      // console.log("record---->", JSON.stringify(record));
      this.$refs["modal"].show(record);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {}
      });
    }
  }
};
</script>
