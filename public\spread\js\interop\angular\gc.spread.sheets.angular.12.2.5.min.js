"use strict";var core_1,common_1,<PERSON>um<PERSON><PERSON><PERSON>ponent,WorksheetComponent,SpreadSheetsComponent,SpreadSheetsModule,__decorate=this&&this.__decorate||function(a,b,c,d){var e,f,g=arguments.length,h=g<3?b:null===d?d=Object.getOwnPropertyDescriptor(b,c):d;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)h=Reflect.decorate(a,b,c,d);else for(f=a.length-1;f>=0;f--)(e=a[f])&&(h=(g<3?e(h):g>3?e(b,c,h):e(b,c))||h);return g>3&&h&&Object.defineProperty(b,c,h),h},__metadata=this&&this.__metadata||function(a,b){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(a,b)},__param=this&&this.__param||function(a,b){return function(c,d){b(c,d,a)}};Object.defineProperty(exports,"__esModule",{value:!0}),core_1=require("@angular/core"),common_1=require("@angular/common"),ColumnComponent=function(){function a(){this.changes={}}return a.prototype.onAttached=function(a,b){this.sheet=a,this.index=b,this.onColumnChanged()},a.prototype.onColumnChanged=function(){var a,b,c,d;if(this.sheet){a=this.sheet,a.suspendPaint(),a.suspendEvent(),b=this.changes;for(c in b)if(d=b[c].currentValue,null!==d&&void 0!==d)switch(c){case"width":d=parseInt(d,10),a.setColumnWidth(this.index,d);break;case"visible":a.setColumnVisible(this.index,d);break;case"resizable":a.setColumnResizable(this.index,d);break;case"autoFit":d&&a.autoFitColumn(this.index);break;case"style":a.setStyle(-1,this.index,d);break;case"headerStyle":a.setStyle(-1,this.index,d,GC.Spread.Sheets.SheetArea.colHeader);break;case"cellType":a.setCellType(-1,this.index,d);break;case"formatter":a.setFormatter(-1,this.index,d,GC.Spread.Sheets.SheetArea.viewport)}a.resumeEvent(),a.resumePaint()}},a.prototype.ngOnChanges=function(a){var b,c;this.changes={},b=this.changes;for(c in a)b[c]=a[c];this.onColumnChanged()},a}(),__decorate([core_1.Input(),__metadata("design:type",Number)],ColumnComponent.prototype,"width",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],ColumnComponent.prototype,"dataField",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],ColumnComponent.prototype,"headerText",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],ColumnComponent.prototype,"visible",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],ColumnComponent.prototype,"resizable",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],ColumnComponent.prototype,"autoFit",void 0),__decorate([core_1.Input(),__metadata("design:type",GC.Spread.Sheets.Style)],ColumnComponent.prototype,"style",void 0),__decorate([core_1.Input(),__metadata("design:type",GC.Spread.Sheets.CellTypes.Base)],ColumnComponent.prototype,"cellType",void 0),__decorate([core_1.Input(),__metadata("design:type",GC.Spread.Sheets.Style)],ColumnComponent.prototype,"headerStyle",void 0),__decorate([core_1.Input(),__metadata("design:type",Object)],ColumnComponent.prototype,"formatter",void 0),ColumnComponent=__decorate([core_1.Component({selector:"gc-column",template:"\n        <ng-content></ng-content>\n    "})],ColumnComponent),exports.ColumnComponent=ColumnComponent,WorksheetComponent=function(){function a(){this.sheet=new GC.Spread.Sheets.Worksheet("")}return a.prototype.onAttached=function(){var a=this,b=this.sheet,c=this.columns;b.suspendPaint(),b.suspendEvent(),this.dataSource&&(b.setDataSource(this.dataSource),c.forEach(function(a,c){a.dataField&&b.bindColumn(c,{name:a.dataField,displayName:a.headerText})})),c.length>0&&(b.setColumnCount(c.length),c.forEach(function(b,c){b.onAttached(a.sheet,c)})),b.resumeEvent(),b.resumePaint()},a.prototype.getSheet=function(){return this.sheet},a.prototype.ngOnChanges=function(a){var b,c,d=this.sheet;d.suspendPaint(),d.suspendEvent();for(b in a)if(c=a[b].currentValue,null!==c&&void 0!==c)switch(b){case"rowCount":d.setRowCount(c);break;case"colCount":d.setColumnCount(c);break;case"name":d.name(c);break;case"frozenColumnCount":d.frozenColumnCount(c);break;case"frozenRowCount":d.frozenRowCount(c);break;case"frozenTrailingRowCount":d.frozenTrailingRowCount(c);break;case"frozenTrailingColumnCount":d.frozenTrailingColumnCount(c);break;case"selectionPolicy":d.selectionPolicy(c);break;case"selectionUnit":d.selectionUnit(c);break;case"zoom":d.zoom(c);break;case"currentTheme":d.currentTheme(c);break;case"defaultStyle":d.setDefaultStyle(c);break;case"rowOutlineInfo":c.forEach(function(a){d.rowOutlines.group(a.index,a.count)}),d.repaint();break;case"columnOutlineInfo":c.forEach(function(a){d.columnOutlines.group(a.index,a.count)}),d.repaint();break;case"showRowOutline":d.showRowOutline(c);break;case"showColumnOutline":d.showColumnOutline(c);break;case"dataSource":d.setDataSource(c);break;case"autoGenerateColumns":d[b]=c;default:d.options[b]=c}d.resumeEvent(),d.resumePaint()},a.prototype.ngAfterViewInit=function(){var a=this;this.columns.changes.subscribe(function(){a.onAttached()})},a.prototype.ngOnDestroy=function(){var a,b=this.sheet,c=b?b.getParent():null;c&&(a=c.getSheetIndex(b.name()),void 0!==a&&c.removeSheet(a))},a}(),__decorate([core_1.ContentChildren(ColumnComponent),__metadata("design:type",core_1.QueryList)],WorksheetComponent.prototype,"columns",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"rowCount",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"colCount",void 0),__decorate([core_1.Input(),__metadata("design:type",Object)],WorksheetComponent.prototype,"dataSource",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],WorksheetComponent.prototype,"name",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"frozenColumnCount",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"frozenRowCount",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"frozenTrailingRowCount",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"frozenTrailingColumnCount",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"allowCellOverflow",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],WorksheetComponent.prototype,"frozenlineColor",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],WorksheetComponent.prototype,"sheetTabColor",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"selectionPolicy",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"selectionUnit",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"zoom",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],WorksheetComponent.prototype,"currentTheme",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"clipBoardOptions",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"rowHeaderVisible",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"colHeaderVisible",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"rowHeaderAutoText",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"colHeaderAutoText",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"rowHeaderAutoTextIndex",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],WorksheetComponent.prototype,"colHeaderAutoTextIndex",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"isProtected",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"showRowOutline",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"showColumnOutline",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],WorksheetComponent.prototype,"selectionBackColor",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],WorksheetComponent.prototype,"selectionBorderColor",void 0),__decorate([core_1.Input(),__metadata("design:type",GC.Spread.Sheets.Style)],WorksheetComponent.prototype,"defaultStyle",void 0),__decorate([core_1.Input(),__metadata("design:type",Array)],WorksheetComponent.prototype,"rowOutlineInfo",void 0),__decorate([core_1.Input(),__metadata("design:type",Array)],WorksheetComponent.prototype,"columnOutlineInfo",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],WorksheetComponent.prototype,"autoGenerateColumns",void 0),WorksheetComponent=__decorate([core_1.Component({selector:"gc-worksheet",template:"\n        <ng-content></ng-content>\n    "}),__metadata("design:paramtypes",[])],WorksheetComponent),exports.WorksheetComponent=WorksheetComponent,SpreadSheetsComponent=function(){function a(a){this.style={width:"800px",height:"600px"},this.workbookInitialized=new core_1.EventEmitter,this.validationError=new core_1.EventEmitter,this.cellClick=new core_1.EventEmitter,this.cellDoubleClick=new core_1.EventEmitter,this.enterCell=new core_1.EventEmitter,this.leaveCell=new core_1.EventEmitter,this.valueChanged=new core_1.EventEmitter,this.topRowChanged=new core_1.EventEmitter,this.leftColumnChanged=new core_1.EventEmitter,this.invalidOperation=new core_1.EventEmitter,this.rangeFiltering=new core_1.EventEmitter,this.rangeFiltered=new core_1.EventEmitter,this.tableFiltering=new core_1.EventEmitter,this.tableFiltered=new core_1.EventEmitter,this.rangeSorting=new core_1.EventEmitter,this.rangeSorted=new core_1.EventEmitter,this.clipboardChanging=new core_1.EventEmitter,this.clipboardChanged=new core_1.EventEmitter,this.clipboardPasting=new core_1.EventEmitter,this.clipboardPasted=new core_1.EventEmitter,this.columnWidthChanging=new core_1.EventEmitter,this.columnWidthChanged=new core_1.EventEmitter,this.rowHeightChanging=new core_1.EventEmitter,this.rowHeightChanged=new core_1.EventEmitter,this.dragDropBlock=new core_1.EventEmitter,this.dragDropBlockCompleted=new core_1.EventEmitter,this.dragFillBlock=new core_1.EventEmitter,this.dragFillBlockCompleted=new core_1.EventEmitter,this.editStarting=new core_1.EventEmitter,this.editChange=new core_1.EventEmitter,this.editEnding=new core_1.EventEmitter,this.editEnd=new core_1.EventEmitter,this.editEnded=new core_1.EventEmitter,this.rangeGroupStateChanging=new core_1.EventEmitter,this.rangeGroupStateChanged=new core_1.EventEmitter,this.selectionChanging=new core_1.EventEmitter,this.selectionChanged=new core_1.EventEmitter,this.sheetTabClick=new core_1.EventEmitter,this.sheetTabDoubleClick=new core_1.EventEmitter,this.sheetNameChanging=new core_1.EventEmitter,this.sheetNameChanged=new core_1.EventEmitter,this.userZooming=new core_1.EventEmitter,this.userFormulaEntered=new core_1.EventEmitter,this.cellChanged=new core_1.EventEmitter,this.columnChanged=new core_1.EventEmitter,this.rowChanged=new core_1.EventEmitter,this.activeSheetChanging=new core_1.EventEmitter,this.activeSheetChanged=new core_1.EventEmitter,this.sparklineChanged=new core_1.EventEmitter,this.rangeChanged=new core_1.EventEmitter,this.buttonClicked=new core_1.EventEmitter,this.editorStatusChanged=new core_1.EventEmitter,this.floatingObjectChanged=new core_1.EventEmitter,this.floatingObjectSelectionChanged=new core_1.EventEmitter,this.pictureChanged=new core_1.EventEmitter,this.floatingObjectRemoving=new core_1.EventEmitter,this.floatingObjectRemoved=new core_1.EventEmitter,this.pictureSelectionChanged=new core_1.EventEmitter,this.floatingObjectLoaded=new core_1.EventEmitter,this.touchToolStripOpening=new core_1.EventEmitter,this.commentChanged=new core_1.EventEmitter,this.commentRemoving=new core_1.EventEmitter,this.commentRemoved=new core_1.EventEmitter,this.slicerChanged=new core_1.EventEmitter,this.elRef=a}return a.prototype.ngAfterViewInit=function(){var a=this,b=this.elRef,c=b.nativeElement,d=c.querySelector("div");this.spread=new GC.Spread.Sheets.Workbook(d,{sheetCount:0}),this.setSpreadOptions(),this.initSheets(),this.sheets.changes.subscribe(function(b){a.onSheetsChanged(b)}),this.bindCustomEvent(this.spread),this.workbookInitialized.emit({spread:this.spread})},a.prototype.onSheetsChanged=function(a){var b=this.spread;b.suspendPaint(),a&&a.forEach(function(a,c){var d=a.getSheet();d&&!d.getParent()&&(b.addSheet(c,a.getSheet()),a.onAttached())}),b.resumePaint()},a.prototype.initSheets=function(){var a=this.sheets,b=this.spread;b.clearSheets(),a.forEach(function(a,c){b.addSheet(c,a.getSheet()),a.onAttached()}),0===a.length&&b.addSheet(0,new GC.Spread.Sheets.Worksheet(""))},a.prototype.bindCustomEvent=function(a){var b=this,c=".ng",d=["ValidationError","CellClick","CellDoubleClick","EnterCell","LeaveCell","ValueChanged","TopRowChanged","LeftColumnChanged","InvalidOperation","RangeFiltering","RangeFiltered","TableFiltering","TableFiltered","RangeSorting","RangeSorted","ClipboardChanging","ClipboardChanged","ClipboardPasting","ClipboardPasted","ColumnWidthChanging","ColumnWidthChanged","RowHeightChanging","RowHeightChanged","DragDropBlock","DragDropBlockCompleted","DragFillBlock","DragFillBlockCompleted","EditStarting","EditChange","EditEnding","EditEnd","EditEnded","RangeGroupStateChanging","RangeGroupStateChanged","SelectionChanging","SelectionChanged","SheetTabClick","SheetTabDoubleClick","SheetNameChanging","SheetNameChanged","UserZooming","UserFormulaEntered","CellChanged","ColumnChanged","RowChanged","ActiveSheetChanging","ActiveSheetChanged","SparklineChanged","RangeChanged","ButtonClicked","EditorStatusChanged","FloatingObjectChanged","FloatingObjectSelectionChanged","PictureChanged","FloatingObjectRemoving","FloatingObjectRemoved","PictureSelectionChanged","FloatingObjectLoaded","TouchToolStripOpening","CommentChanged","CommentRemoving","CommentRemoved","SlicerChanged"];d.forEach(function(d){a.bind(d+c,function(a,c){var d=a.type,e=d[0].toLowerCase()+d.substr(1);b[e].emit(c)})})},a.prototype.setSpreadOptions=function(){var a,b=this.spread;this.spread&&(b.suspendEvent(),b.suspendPaint(),a=this.spreadOptions,a&&a.forEach(function(a){"name"===a.name?b.name=a.value:b.options[a.name]=a.value}),b.resumePaint(),b.resumeEvent())},a.prototype.ngOnChanges=function(a){var b,c,d=[];for(b in a)if(c=a[b].currentValue,null!==c&&void 0!==c)switch(b){case"hostStyle":this.style=c;break;case"hostClass":break;default:d.push({name:b,value:c})}this.spreadOptions=d,this.setSpreadOptions()},a.prototype.ngOnDestroy=function(){this.spread.destroy()},a}(),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserResize",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserZoom",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserEditFormula",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserDragFill",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserDragDrop",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserDragMerge",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUndo",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowSheetReorder",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowContextMenu",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowUserDeselect",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowCopyPasteExcelStyle",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"allowExtendPasteRange",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"cutCopyIndicatorVisible",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],SpreadSheetsComponent.prototype,"cutCopyIndicatorBorderColor",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"copyPasteHeaderOptions",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"defaultDragFillType",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"enableFormulaTextbox",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"highlightInvalidData",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"newTabVisible",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"tabStripVisible",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"tabEditable",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"tabStripRatio",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"tabNavigationVisible",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"autoFitType",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"referenceStyle",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],SpreadSheetsComponent.prototype,"backColor",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],SpreadSheetsComponent.prototype,"grayAreaBackColor",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"resizeZeroIndicator",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"showVerticalScrollbar",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"showHorizontalScrollbar",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"scrollbarMaxAlign",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"scrollIgnoreHidden",void 0),__decorate([core_1.Input(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"hostStyle",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],SpreadSheetsComponent.prototype,"hostClass",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"hideSelection",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],SpreadSheetsComponent.prototype,"name",void 0),__decorate([core_1.Input(),__metadata("design:type",String)],SpreadSheetsComponent.prototype,"backgroundImage",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"backgroundImageLayout",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"showScrollTip",void 0),__decorate([core_1.Input(),__metadata("design:type",Number)],SpreadSheetsComponent.prototype,"showResizeTip",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"showDragDropTip",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"showDragFillTip",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"showDragFillSmartTag",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"scrollbarShowMax",void 0),__decorate([core_1.Input(),__metadata("design:type",Boolean)],SpreadSheetsComponent.prototype,"useTouchLayout",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"workbookInitialized",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"validationError",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"cellClick",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"cellDoubleClick",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"enterCell",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"leaveCell",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"valueChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"topRowChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"leftColumnChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"invalidOperation",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeFiltering",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeFiltered",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"tableFiltering",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"tableFiltered",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeSorting",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeSorted",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"clipboardChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"clipboardChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"clipboardPasting",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"clipboardPasted",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"columnWidthChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"columnWidthChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rowHeightChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rowHeightChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"dragDropBlock",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"dragDropBlockCompleted",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"dragFillBlock",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"dragFillBlockCompleted",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"editStarting",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"editChange",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"editEnding",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"editEnd",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"editEnded",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeGroupStateChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeGroupStateChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"selectionChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"selectionChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"sheetTabClick",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"sheetTabDoubleClick",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"sheetNameChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"sheetNameChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"userZooming",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"userFormulaEntered",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"cellChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"columnChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rowChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"activeSheetChanging",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"activeSheetChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"sparklineChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"rangeChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"buttonClicked",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"editorStatusChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"floatingObjectChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"floatingObjectSelectionChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"pictureChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"floatingObjectRemoving",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"floatingObjectRemoved",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"pictureSelectionChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"floatingObjectLoaded",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"touchToolStripOpening",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"commentChanged",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"commentRemoving",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"commentRemoved",void 0),__decorate([core_1.Output(),__metadata("design:type",Object)],SpreadSheetsComponent.prototype,"slicerChanged",void 0),__decorate([core_1.ContentChildren(WorksheetComponent),__metadata("design:type",core_1.QueryList)],SpreadSheetsComponent.prototype,"sheets",void 0),SpreadSheetsComponent=__decorate([core_1.Component({selector:"gc-spread-sheets",template:'\n        <div [ngStyle]="style" [ngClass]="hostClass">\n            <ng-content></ng-content>\n        </div>\n    '}),__param(0,core_1.Inject(core_1.ElementRef)),__metadata("design:paramtypes",[core_1.ElementRef])],SpreadSheetsComponent),exports.SpreadSheetsComponent=SpreadSheetsComponent,SpreadSheetsModule=function(){function a(){}return a}(),SpreadSheetsModule=__decorate([core_1.NgModule({imports:[common_1.CommonModule],declarations:[SpreadSheetsComponent,WorksheetComponent,ColumnComponent],exports:[SpreadSheetsComponent,WorksheetComponent,ColumnComponent]})],SpreadSheetsModule),exports.SpreadSheetsModule=SpreadSheetsModule;