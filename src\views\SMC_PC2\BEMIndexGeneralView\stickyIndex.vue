<!--
 * @Description: 置顶模块
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:42:00
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 09:16:22
-->
<template>
  <div class="_stickyIndex" :class="[topCardList.length > 0 ? 'hasData' : '']">
    <!-- 骨架屏 -->
    <a-skeleton
      active
      :loading="cardListLoading && enterPageTimes === 1"
      :paragraph="{ rows: 1 }"
    >
      <!-- 置顶卡片 -->
      <a-row
        :gutter="{
          xs: 16,
          sm: skinStyle().includes('classic-style') ? 24 : 16,
          md: skinStyle().includes('classic-style') ? 32 : 16,
          lg: skinStyle().includes('classic-style') ? 64 : 16
        }"
      >
        <a-col :span="4" v-for="(item, index) in topCardList" :key="index">
          <!-- 卡片 -->
          <div class="_card">
            <!-- 标题 -->
            <div class="_title">
              <!-- 提示信息 -->
              <a-tooltip placement="top">
                <template slot="title">
                  <span>{{
                    (item.wdInCardName ? item.wdInCardName + " - " : "") +
                      item.displayIndexName
                  }}</span>
                </template>
                <!-- <a-icon type="question-circle" /> -->
                <span>{{
                  (item.wdInCardName ? item.wdInCardName + " - " : "") +
                    item.displayIndexName
                }}</span>
              </a-tooltip>
            </div>
            <!-- 数据 -->
            <div class="_data">
              <span class="num" :ref="`data_num${index}`">
                {{ item.actualValue }}
                <span>{{ item.indexUnitId }}</span>
              </span>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-skeleton>
  </div>
</template>
<script>
import { thousandSplit } from "../utils";
export default {
  inject: ["skinStyle"],
  props: {
    cardListLoading: Boolean,
    topCardList: Array
  },
  data() {
    return {
      thousandSplit,
      list: [],
      enterPageTimes: 0 // 进入页面次数，用来控制顶部置顶骨架屏显示的
    };
  },
  watch: {
    cardListLoading: {
      handler(val) {
        val && this.enterPageTimes++;
      },
      immediate: true
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage2 {
  &.classic-style {
    ._stickyIndex {
      &.hasData {
        padding-top: 16px;
      }
      .ant-skeleton-content .ant-skeleton-title,
      .ant-skeleton-content .ant-skeleton-title + .ant-skeleton-paragraph {
        margin-top: 0;
      }
      .ant-row {
        .ant-col {
          border-right: 1px solid #f0f0f0;
          &:last-child {
            border: none;
          }
          ._title {
            height: 22px;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bold;
            line-height: 22px;
            margin-bottom: 4px;
          }
          ._data {
            height: 36px;
            font-family: PingFangSC-Medium;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 36px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            span:not(.num) {
              font-size: 16px;
              line-height: 36px;
            }
          }
        }
      }
    }
  }
  &.hisense-style {
    &.dark {
      ._stickyIndex {
        &.hasData {
          ._card {
            border: 1px solid #4e5969;
            background-color: #222325;
            ._title {
              color: #ffffff;
            }
            ._data {
              color: rgba(255, 255, 255, 0.85);
            }
          }
        }
      }
    }
    ._stickyIndex {
      &.hasData {
        padding-top: 18px;
        ._card {
          height: 100px;
          border: 1px solid #e5e6eb;
          border-radius: 3px;
          padding: 11px 16px 14px 16px;
          ._title {
            height: 20px;
            font-size: 14px;
            line-height: 20px;
            color: #1d2129;
            margin-bottom: 10px;
          }
          ._data {
            span {
              display: block;
            }
            & > span {
              display: flex;
              align-items: flex-end;
            }
            height: 39px;
            font-weight: 500;
            font-size: 32px;
            line-height: 39px;
            span {
              span {
                margin-left: 16px;
                font-size: 14px;
                height: 20px;
                line-height: 20px;
                margin-bottom: 3px;
              }
            }
          }
        }
      }
      padding: 0 8px;
    }
  }
  ._stickyIndex {
    .ant-row {
      .ant-col {
        ._card {
          ._title {
            display: flex;
            align-items: center;
            .anticon {
              cursor: pointer;
            }
            span:not(.anticon) {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}
</style>
