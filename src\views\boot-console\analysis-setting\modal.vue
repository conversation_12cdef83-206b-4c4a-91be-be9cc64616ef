<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-11-10 15:47:51
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="600"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="ruleForm"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="公司" prop="signOrgId">
        <template v-if="this.isEdit">
          <a-input v-model="form.signOrg" disabled />
        </template>
        <template v-else>
          <a-select
            v-model="form.signOrgId"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
            @change="companyChange"
          >
            <a-select-option
              :value="item.key"
              v-for="item in companyList"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
      <a-form-model-item label="版块" prop="businessSegmentsId">
        <template v-if="this.isEdit">
          <a-input v-model="form.businessSegments" disabled />
        </template>
        <template v-else>
          <a-select
            v-model="form.businessSegmentsId"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
          >
            <a-select-option
              :value="item.key"
              v-for="item in plateList"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
      <a-form-model-item label="报表名称" prop="reportName">
        <a-input v-model="form.reportName" />
      </a-form-model-item>
      <a-form-model-item label="报表链接" prop="reportUrl">
        <a-input v-model="form.reportUrl" />
      </a-form-model-item>
      <a-form-model-item label="报表类型" prop="reportType">
        <a-select
          v-model="form.reportType"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
        >
          <a-select-option
            :value="item.key"
            v-for="item in dict['smc-reportType'] || []"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="链接参数" prop="reportParam">
        <a-input v-model="form.reportParam" />
      </a-form-model-item>
      <a-form-model-item label="版块排序" prop="topSort">
        <a-input-number v-model="form.topSort" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";

export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        reportName: "",
        reportType: "",
        businessSegmentsId: "",
        businessSegments: "",
        signOrgId: "",
        signOrg: "",
        reportUrl: "",
        reportParam: "",
        topSort: ""
      },
      rules: {
        reportName: [
          {
            required: true,
            message: "请填写报表名称",
            trigger: "blur"
          }
        ],
        reportUrl: [
          {
            required: true,
            message: "请填写报表链接",
            trigger: "blur"
          }
        ],
        topSort: [
          {
            required: true,
            message: "请填写版块排序",
            trigger: "blur"
          }
        ],
        reportType: [
          {
            required: true,
            message: "请选择报表类型",
            trigger: "change"
          }
        ],
        signOrgId: [
          {
            required: true,
            message: "请选择公司",
            trigger: "change"
          }
        ],
        businessSegmentsId: [
          {
            required: true,
            message: "请选择版块",
            trigger: "change"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      dict: {}, // 字典
      companyList: [], // 公司列表
      plateList: [] // 版块列表
    };
  },
  methods: {
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      this.getCompany();
      this.getDICT();
      if (formValue) {
        const {
          id,
          reportName,
          reportType,
          businessSegmentsId,
          businessSegments,
          signOrgId,
          signOrg,
          reportUrl,
          topSort,
          reportParam
        } = formValue;
        this.isEdit = true;
        this.form = {
          id,
          reportName,
          reportType,
          businessSegmentsId,
          businessSegments,
          signOrgId,
          signOrg,
          reportUrl,
          topSort,
          reportParam
        };
        this.rules["signOrgId"][0].required = false;
        this.rules["businessSegmentsId"][0].required = false;
      }
    },
    close() {
      this.form = {
        reportName: "",
        reportType: "",
        businessSegmentsId: "",
        businessSegments: "",
        signOrgId: "",
        signOrg: "",
        reportUrl: "",
        reportParam: "",
        topSort: ""
      };
      this.$refs.ruleForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.saveData();
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-Y%2FN%2Csmc-reportType&languageCode=zh_CN"
        )
      ).then(res => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    // 获取公司
    getCompany(callback) {
      request("/api/smc2/codeValue/getRelation1").then(res => {
        this.companyList = res || [];
        typeof callback === "function" && callback();
      });
    },
    // 公司下拉框修改
    companyChange(value) {
      this.getPlate(value);
    },
    getPlate(companyId) {
      request(`/api/smc2/codeValue/getRelation2?companyId=${companyId}`).then(
        res => {
          this.plateList = res || [];
        }
      );
    },
    // 保存专项分析
    saveData() {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          const postData = { ...this.form };
          if (!this.isEdit) {
            delete postData.businessSegments;
            delete postData.signOrg;
            postData["type"] = "1";
          }
          request(
            `/api/smc2/cardInfo/${
              this.isEdit ? "updateReportUrl" : "insertCardInfo"
            }`,
            {
              method: "POST",
              body: this.isEdit ? postData : [postData]
            }
          ).then(res => {
            if (res?.code === "5501") {
              return;
            }
            this.close();
            this.$emit("fetchData");
          });
        }
      });
    }
  }
};
</script>
