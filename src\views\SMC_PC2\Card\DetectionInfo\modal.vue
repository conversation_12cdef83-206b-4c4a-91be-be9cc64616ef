<template>
  <div>
    <a-modal
      title="查看"
      :visible="visible"
      @cancel="handleCancel"
      :footer="null"
      width="100%"
    >
      <div style="display: flex;justify-content: flex-end;">
        <a-button type="primary" size="small" @click="exportData">
          导出数据
        </a-button>
      </div>
      <a-table
        :scroll="{ x: 2100 }"
        style="background: #fff;padding: 10px 5px;"
        :pagination="false"
        size="small"
        :columns="columns"
        @change="tableChange"
        :data-source="tableData"
      >
        <!-- <a-table-column
          key="index"
          title="序号"
          data-index="index"
          width="80"
        />
        <a-table-column
          key="postEndIndexName"
          title="指标名称"
          data-index="postEndIndexName"
          width="150"
        />
        <a-table-column
          key="businessSegments"
          title="业务版块"
          data-index="businessSegments"
          width="100"
        />
        <a-table-column
          key="signOrg"
          title="公司"
          data-index="signOrg"
          width="80"
        />
        <a-table-column
          key="org"
          title="组织"
          data-index="org"
          :filtered-value="filteredOrgs"
          @filter="(value, record) => record.org.includes(value)"
          :filterDropdownVisible.sync="orgFilterDropdownVisible"
        >
          <template slot="filterDropdown">
            <div style="background-color: white; border: solid;">
              <a-checkbox
                v-for="tag in ['nice', 'cool', 'loser']"
                :key="tag"
                :checked="filteredOrgs.includes(tag)"
                @change="
                  filteredOrgs = toggleSelectedTags(
                    filteredOrgs,
                    tag,
                    $event.target.checked
                  )
                "
              >
                {{ tag }}
              </a-checkbox>
            </div>
          </template>
          <template slot-scope="org">
            <span>
              {{ org }}
            </span>
          </template>
        </a-table-column>
        <a-table-column
          key="actualValue"
          title="实际值"
          data-index="actualValue"
          width="100"
        />
        <a-table-column
          key="actualMolecule"
          title="实际分子"
          data-index="actualMolecule"
          width="120"
        />
        <a-table-column
          key="actualDenominator"
          title="实际分母"
          data-index="actualDenominator"
          width="120"
        />
        <a-table-column
          key="indexUnitId"
          title="单位"
          data-index="indexUnitId"
          width="80"
        />
        <a-table-column
          key="targetValue"
          title="目标值"
          data-index="targetValue"
          width="90"
        />
        <a-table-column
          key="targetCompletionRate"
          title="完成率"
          data-index="targetCompletionRate"
          width="90"
        />
        <a-table-column
          key="contemValue"
          title="同期值"
          data-index="contemValue"
          width="90"
        />
        <a-table-column
          key="contemChangeRate"
          title="同比"
          data-index="contemChangeRate"
          width="90"
        />
        <a-table-column
          key="previousValue"
          title="上期值"
          data-index="previousValue"
          width="90"
        />
        <a-table-column
          key="previousChangeRate"
          title="环比"
          data-index="previousChangeRate"
          width="90"
        />
        <a-table-column
          key="indexFrequency"
          title="频次"
          data-index="indexFrequency"
          width="90"
        />
        <a-table-column
          key="indexDt"
          title="指标时间"
          data-index="indexDt"
          width="110"
        />
        <a-table-column
          key="indexType"
          title="指标类型"
          data-index="indexType"
          width="100"
        /> -->
      </a-table>
    </a-modal>
  </div>
</template>
<script>
export default {
  props: {
    tableData: Array,
    companyName: String
  },
  data() {
    return {
      visible: false,
      // 表格列展示
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          width: 80
        },
        {
          title: "指标名称",
          dataIndex: "postEndIndexName",
          key: "postEndIndexName",
          width: 150,
          ellipsis: true
        },
        {
          title: "业务版块",
          dataIndex: "businessSegments",
          key: "businessSegments",
          width: 100
        },
        {
          title: "公司",
          key: "signOrg",
          dataIndex: "signOrg",
          width: 120,
          ellipsis: true
        },
        {
          title: "组织",
          dataIndex: "org",
          key: "org",
          width: 120,
          filters: [],
          ellipsis: true,
          onFilter: (value, record) =>
            record.org && record.org.indexOf(value) === 0
        },
        {
          title: "层级描述",
          key: "levelName",
          dataIndex: "levelName",
          width: 80,
          filters: [],
          ellipsis: true,
          onFilter: (value, record) =>
            record.levelName && record.levelName.indexOf(value) === 0
        },
        {
          title: "父级组织",
          key: "parentName",
          dataIndex: "parentName",
          width: 120,
          filters: [],
          ellipsis: true,
          onFilter: (value, record) =>
            record.parentName && record.parentName.indexOf(value) === 0
        },
        {
          title: "实际值",
          dataIndex: "actualValue",
          key: "actualValue",
          width: 100
        },
        {
          title: "实际分子",
          dataIndex: "actualMolecule",
          key: "actualMolecule",
          width: 120
        },
        {
          title: "实际分母",
          dataIndex: "actualDenominator",
          key: "actualDenominator",
          width: 120
        },
        {
          title: "单位",
          dataIndex: "indexUnitId",
          key: "indexUnitId",
          width: 80
        },
        {
          title: "目标值",
          dataIndex: "targetValue",
          key: "targetValue",
          width: 90
        },
        {
          title: "完成率",
          dataIndex: "targetCompletionRate",
          key: "targetCompletionRate",
          width: 90
        },
        {
          title: "同期值",
          dataIndex: "contemValue",
          key: "contemValue",
          width: 90
        },
        {
          title: "同比",
          dataIndex: "contemChangeRate",
          key: "contemChangeRate",
          width: 90
        },
        {
          title: "上期值",
          dataIndex: "previousValue",
          key: "previousValue",
          width: 90
        },
        {
          title: "环比",
          dataIndex: "previousChangeRate",
          key: "previousChangeRate",
          width: 90
        },
        {
          title: "频次",
          dataIndex: "indexFrequency",
          key: "indexFrequency",
          width: 90
        },
        {
          title: "指标时间",
          dataIndex: "indexDt",
          key: "indexDt",
          width: 110
        },
        {
          title: "指标类型",
          dataIndex: "indexType",
          key: "indexType",
          width: 100
        }
      ],
      filteredOrgs: [],
      filterTableData: [],
      orgFilterDropdownVisible: false
    };
  },
  watch: {
    tableData: {
      handler(val) {
        this.filterTableData = this.tableData;
        const orgArr = [
          ...new Set(val.map(item => item.org).filter(item => item))
        ];
        const levelNameArr = [
          ...new Set(val.map(item => item.levelName).filter(item => item))
        ];
        const parentNameArr = [
          ...new Set(val.map(item => item.parentName).filter(item => item))
        ];
        this.columns[4].filters = orgArr.map(item => {
          return {
            text: item,
            value: item
          };
        });
        this.columns[5].filters = levelNameArr.map(item => {
          return {
            text: item,
            value: item
          };
        });
        this.columns[6].filters = parentNameArr.map(item => {
          return {
            text: item,
            value: item
          };
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    toggleSelectedTags(oldTags, tag, checked) {
      let newTags = [...oldTags];
      if (checked) {
        newTags.push(tag);
      } else {
        newTags = newTags.filter(t => t != tag);
      }
      return newTags;
    },
    tableChange(pagination, filters, sorter, { currentDataSource }) {
      this.filterTableData = currentDataSource;
    },
    exportData() {
      if (this.filterTableData.length === 0) {
        this.$message.warning("请选择数据");
        return;
      }
      const ExportJsonExcel = require("js-export-excel");
      // const columnNames = this.columns.map(item => item.name);
      const option = {};
      option.fileName = `${this.companyName}数据导出${new Date().getTime()}`;
      option.datas = [
        {
          //第一个sheet（第一个excel表格）
          sheetData: this.filterTableData, //数据
          sheetName: "Sheet1",
          sheetFilter: this.columns.map(item => item.dataIndex), //表头数据对应的sheetData数据
          sheetHeader: this.columns.map(item => item.title) //表头数据
        }
      ];
      const toExcel = new ExportJsonExcel(option);
      toExcel.saveExcel();
    },
    showModal() {
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
    }
  }
};
</script>
