<!--
 * @Description: 卡片详情
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 10:45:41
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-11-22 15:28:29
-->
<template>
  <a-drawer
    placement="right"
    width="640px"
    :visible="visible"
    @close="close"
    class="CardDetailDrawerWrap"
  >
    <!-- 线上运行 -->
    <component
      :is="compName"
      v-bind="$attrs"
      :dataItem="dataItem"
      @close="close"
    ></component>
    <!-- 本地调试 -->
    <!-- <CardDetailInfo v-bind="$attrs" :dataItem="dataItem" @close="close" /> -->
  </a-drawer>
</template>
<script>
import { publicPath } from "@/utils/utils.js";
// import CardDetailInfo from "./cardDetailInfo.vue"; // 本地调试
export default {
  // components: { CardDetailInfo }, // 本地调试
  inheritAttrs: true,
  props: {
    indexCardDetailInfoJSUrl: String
  },
  data() {
    return {
      visible: false,
      dataItem: {},
      compName: ""
    };
  },
  mounted() {
    if (!window["IndexCardDetailInfo"]) {
      const script = document.createElement("script");
      let fileUrl = this.indexCardDetailInfoJSUrl;
      if (this.indexCardDetailInfoJSUrl.indexOf("http") === -1) {
        fileUrl = `${publicPath}/minio${this.indexCardDetailInfoJSUrl}`;
      }
      script.src = fileUrl + `?t=${Date.now()}`;
      script.onload = () => {
        const exportCom = window["IndexCardDetailInfo"].default;
        this.compName = exportCom.myCom;
      };
      document.body.appendChild(script);
    } else {
      const exportCom = window["IndexCardDetailInfo"].default;
      this.compName = exportCom.myCom;
    }
  },
  methods: {
    /**
     * @description: 打开抽屉
     * @param {Object} item 指标详情
     */
    show(item) {
      this.dataItem = item;
      this.visible = true;
    },
    // 关闭抽屉
    close() {
      this.dataItem = {};
      this.visible = false;
    }
  }
};
</script>
<style lang="less">
.CardDetailDrawerWrap {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  .ant-drawer-content {
    height: 100vh !important;
    overflow: hidden !important;
    .ant-drawer-body {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 0 !important;
      .__center {
        flex: 1;
        overflow-y: auto;
        padding: 0 24px;
        margin-bottom: 10px;
      }
    }
  }
  .ant-drawer-body {
    height: 100%;
    overflow-y: auto;
    position: relative;
  }
}
</style>
