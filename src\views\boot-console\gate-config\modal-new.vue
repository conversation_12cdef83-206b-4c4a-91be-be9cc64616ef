<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 14:18:36
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1200"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <!-- 基础表单 -->
    <a-form-model
      ref="baseForm"
      :model="baseForm"
      :rules="baseFormRules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <!-- 选择指标 -->
      <a-form-model-item label="指标" prop="indexName">
        <a-select
          v-model="baseForm.indexName"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          allowClear
          @change="indexChange"
        >
          <a-select-option :value="item" v-for="item in indexList" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 领域 -->
      <a-form-model-item label="领域" prop="businessSegments">
        <a-select
          v-model="baseForm.businessSegments"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          allowClear
          @change="businessSegmentsChange"
        >
          <a-select-option
            :value="item"
            v-for="item in businessSegmentsList"
            :key="item"
          >
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 公司 -->
      <a-form-model-item label="公司" prop="gs">
        <a-select
          v-model="baseForm.gs"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          allowClear
          @change="gsChange"
        >
          <a-select-option :value="item" v-for="item in GSList" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 基地 -->
      <a-form-model-item label="基地" prop="jd">
        <a-select
          v-model="baseForm.jd"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          allowClear
          @change="jdChange"
        >
          <a-select-option :value="item" v-for="item in JDList" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 车间 -->
      <a-form-model-item label="车间" prop="cj">
        <a-select
          v-model="baseForm.cj"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          allowClear
          @change="cjChange"
        >
          <a-select-option :value="item" v-for="item in CJList" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 公司 -->
      <a-form-model-item label="线体" prop="xt">
        <a-select
          v-model="baseForm.xt"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
          allowClear
        >
          <a-select-option :value="item" v-for="item in XTList" :key="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
    <a-divider></a-divider>
    <!-- 预警条件 -->
    <a-button
      type="primary"
      v-if="!isEdit"
      style="margin-bottom: 20px;"
      @click="addOneCondition"
      >新增预警渠道人员配置</a-button
    >
    <a-table
      :pagination="false"
      :columns="
        !isEdit
          ? [
              ...columns,
              {
                title: '操作',
                key: 'action',
                scopedSlots: { customRender: 'action' }
              }
            ]
          : columns
      "
      :data-source="conditionArr"
    >
      <!-- 序号 -->
      <template slot="index" slot-scope="text, record, index">
        <span>{{ index + 1 }}</span>
      </template>
      <!-- 触发条件 -->
      <template slot="triggers" slot-scope="text, record">
        <a-select
          v-model="record[`triggers`]"
          :dropdownMatchSelectWidth="false"
          style="width: 300px;"
        >
          <a-select-option
            :value="zitem.val"
            v-for="zitem in conditionList"
            :key="zitem.val"
          >
            {{ zitem.name }}
          </a-select-option>
        </a-select>
      </template>
      <!-- 预警渠道 -->
      <template slot="sendType" slot-scope="text, record">
        <a-select
          mode="multiple"
          v-model="record[`sendType`]"
          :dropdownMatchSelectWidth="false"
          style="width: 300px"
        >
          <a-select-option
            :value="zitem.key"
            v-for="zitem in dict['smc-gateEarlyType']"
            :key="zitem.key"
          >
            {{ zitem.value }}
          </a-select-option>
        </a-select>
      </template>
      <!-- 接收人员 -->
      <template slot="receive" slot-scope="text, record, index">
        <a-select
          mode="multiple"
          :value="record[`receive`]"
          placeholder="接收人员"
          style="width: 300px;"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUser"
          @change="handleChange(index, $event)"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option v-for="d in userData" :key="`${d.uid}`">
            {{ d.cn }}
            <template v-if="d.uid"> ({{ d.uid }}) </template>
            <template v-if="d.o">
              <div>{{ d.o }}</div>
            </template>
          </a-select-option>
        </a-select>
      </template>
      <!-- 操作栏 -->
      <span slot="action" slot-scope="text, record, index">
        <a-tooltip placement="top">
          <template slot="title">
            <span>删除</span>
          </template>
          <a-icon @click="removeCondition(index)" type="delete" />
        </a-tooltip>
      </span>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
export default {
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      userData: [],
      fetching: false,
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      baseForm: {
        businessSegments: "",
        indexName: "",
        gs: "",
        jd: "",
        cj: "",
        xt: ""
      },
      indexList: [],
      businessSegmentsList: [],
      GSList: [],
      JDList: [],
      CJList: [],
      XTList: [],
      conditionList: [],
      dict: {},
      conditionArr: [
        {
          triggers: "",
          sendType: [],
          receive: []
        }
      ],
      baseFormRules: {
        gs: [
          {
            required: true,
            message: "请选择公司",
            trigger: "change"
          }
        ],
        businessSegments: [
          {
            required: true,
            message: "请选择领域",
            trigger: "change"
          }
        ],
        indexName: [
          {
            required: true,
            message: "请选择指标",
            trigger: "change"
          }
        ],
        jd: [
          {
            required: true,
            message: "请选择基地",
            trigger: "change"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      equalList: [
        {
          key: "<",
          value: "<"
        },
        {
          key: "<=",
          value: "<="
        },
        {
          key: "=",
          value: "="
        },
        {
          key: ">",
          value: ">"
        },
        {
          key: ">=",
          value: ">="
        }
      ],
      columns: [
        {
          title: "序号",
          key: "index",
          scopedSlots: { customRender: "index" },
          width: 80
        },
        {
          title: "触发条件",
          scopedSlots: { customRender: "triggers" },
          width: 300
        },
        {
          title: "预警渠道",
          scopedSlots: { customRender: "sendType" },
          width: 300
        },
        {
          title: "接收人员",
          scopedSlots: { customRender: "receive" },
          width: 350
        }
      ], // 表格列
      warnId: null
    };
  },
  methods: {
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      this.publicFun("index", "指标");
      this.publicFun("condition", "触发条件");
      this.getDICT();
      if (formValue) {
        console.log("formValue---->", formValue);
      }
    },
    // 保存卡片信息
    saveCard() {
      this.$refs.baseForm.validate(async valid => {
        if (valid) {
          let hasError = false;
          for (let i = 0; i < this.conditionArr.length; i++) {
            const element = this.conditionArr[i];
            if (
              !element.triggers ||
              !element.sendType.join(",") ||
              !element.receive.join(" ")
            ) {
              hasError = true;
              break;
            }
          }
          if (hasError) {
            this.$message.error("请完善预警人员配置信息！");
            return;
          }
          const { indexName, businessSegments, gs, jd, cj, xt } = this.baseForm;
          const role = [gs, jd, cj, xt];
          const postData = this.conditionArr.map(item => {
            return {
              indexName,
              businessSegments,
              role: role.filter(item => item).join("="),
              triggers: item.triggers,
              sendType: item.sendType.join(","),
              receive: item.receive.join(" ")
            };
          });
          request("/api/smc2/gate/gateConfig", {
            method: "POST",
            body: postData
          }).then(res => {
            if (res && res.result === "success") {
              this.close();
              this.$emit("fetchData");
            } else if (res && res.result !== "success") {
              this.$message.error(res.result);
            }
          });
        }
      });
    },
    handleChange(index, value) {
      this.$set(this.conditionArr[index], "receive", value);
      this.fetching = false;
      // this.userData = [];
    },
    close() {
      this.baseForm = {
        businessSegments: "",
        indexName: "",
        gs: "",
        jd: "",
        cj: "",
        xt: ""
      };
      this.indexList = [];
      this.businessSegmentsList = [];
      this.GSList = [];
      this.JDList = [];
      this.CJList = [];
      this.XTList = [];
      this.conditionList = [];
      this.conditionArr = [
        {
          triggers: "",
          sendType: [],
          receive: []
        }
      ];
      this.$refs.baseForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 增加一行条件
    addOneCondition() {
      this.conditionArr.push({
        triggers: "",
        sendType: [],
        receive: []
      });
    },
    // 删除条件
    removeCondition(index) {
      this.conditionArr.splice(index, 1);
    },
    // 查找用户
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      request(`/api/system/user/getLdapByName?name=${value}`).then(res => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        this.userData = res;
        // this.userData = res.filter(item => item.employeeNumber);
        this.fetching = false;
      });
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-gateEarlyType&languageCode=zh_CN"
        )
      ).then(res => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    // 指标修改
    indexChange() {
      this.baseForm.businessSegments = "";
      this.businessSegmentsList = [];
      this.baseForm.gs = "";
      this.GSList = [];
      this.baseForm.jd = "";
      this.JDList = [];
      this.baseForm.cj = "";
      this.CJList = [];
      this.baseForm.xt = "";
      this.XTList = [];
      this.publicFun("businessSegments", this.baseForm.indexName);
    },
    // 领域修改
    businessSegmentsChange() {
      this.baseForm.gs = "";
      this.GSList = [];
      this.baseForm.jd = "";
      this.JDList = [];
      this.baseForm.cj = "";
      this.CJList = [];
      this.baseForm.xt = "";
      this.XTList = [];
      const { indexName, businessSegments } = this.baseForm;
      this.publicFun("gs", `${indexName}${businessSegments}`);
    },
    // 公司修改
    gsChange() {
      this.baseForm.jd = "";
      this.JDList = [];
      this.baseForm.cj = "";
      this.CJList = [];
      this.baseForm.xt = "";
      this.XTList = [];
      const { indexName, businessSegments, gs } = this.baseForm;
      this.publicFun("jd", `${indexName}${businessSegments}${gs}`);
    },
    // 基地修改
    jdChange() {
      const { indexName, businessSegments, gs, jd } = this.baseForm;
      this.baseForm.cj = "";
      this.CJList = [];
      this.baseForm.xt = "";
      this.XTList = [];
      this.publicFun("cj", `${indexName}${businessSegments}${gs}${jd}`);
    },
    // 车间修改
    cjChange() {
      const { indexName, businessSegments, gs, jd, cj } = this.baseForm;
      this.baseForm.xt = "";
      this.XTList = [];
      this.publicFun("xt", `${indexName}${businessSegments}${gs}${jd}${cj}`);
    },
    // 公共查询方法
    publicFun(type, searchdata) {
      request(`/api/smc2/gate/searchSelect?val=${searchdata}`).then(res => {
        if (Array.isArray(res) && res.length) {
          if (type === "index") {
            this.indexList = res;
          } else if (type === "businessSegments") {
            this.businessSegmentsList = res;
          } else if (type === "gs") {
            this.GSList = res;
          } else if (type === "jd") {
            this.JDList = res;
          } else if (type === "cj") {
            this.CJList = res;
          } else if (type === "xt") {
            this.XTList = res;
          } else if (type === "condition") {
            this.conditionList = res;
          }
        }
      });
    }
  }
};
</script>
<style lang="less">
.conditionForm {
  .ant-form-item {
    margin-bottom: 0;
  }
}
</style>
