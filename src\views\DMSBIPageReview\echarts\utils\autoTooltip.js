/*
 * @Description: echarts tooltip自动滚动展示工具函数
 */

/**
 * 为echarts图表添加tooltip自动滚动展示功能
 * @param {echarts.ECharts} chart - echarts实例
 * @param {Object} options - 配置选项
 * @param {number} options.interval - 滚动间隔时间(毫秒)，默认2000ms
 * @param {boolean} options.loop - 是否循环滚动，默认true
 * @param {boolean} options.debug - 是否启用调试模式，默认false
 * @returns {Object} - 返回控制对象，包含start、stop方法
 */
export const createAutoTooltip = (chart, options = {}) => {
  if (!chart) {
    return null;
  }

  // 默认配置
  const defaultOptions = {
    interval: 2000, // 滚动间隔，默认2秒
    loop: true, // 是否循环滚动
    debug: false, // 是否启用调试模式
  };

  // 合并配置
  const config = { ...defaultOptions, ...options };

  // 调试日志函数
  const log = (...args) => {
    if (config.debug) {
      // 调试模式下才输出日志
    }
  };

  // 当前索引
  let currentIndex = -1;
  // 定时器ID
  let timer = null;
  // 数据系列数量
  let seriesCount = 0;
  // 数据点数量
  let dataCount = 0;
  // 是否已暂停
  let isPaused = false;
  // 是否有数据
  let hasData = false;

  // 更新数据点数量
  const updateDataInfo = () => {
    try {
      // 确保图表实例存在
      if (!chart) {
        hasData = false;
        return;
      }

      // 获取图表配置
      const option = chart.getOption();

      // 检查是否有系列数据
      if (!option || !option.series || !option.series.length) {
        hasData = false;
        return;
      }

      // 更新系列数量
      seriesCount = option.series.length;

      // 严格检查是否有有效数据
      let validDataCount = 0;
      let maxDataLength = 0;

      // 检查所有系列的数据
      for (let i = 0; i < seriesCount; i++) {
        const series = option.series[i];

        // 检查该系列是否有数据
        if (series.data && series.data.length) {
          // 更新最大数据长度
          maxDataLength = Math.max(maxDataLength, series.data.length);

          // 检查是否有有效数据（非null、非undefined、非0）
          const validData = series.data.filter(item => {
            if (item === null || item === undefined) return false;
            if (typeof item === 'number') return item !== 0;
            if (typeof item === 'object' && 'value' in item) return item.value !== 0;
            return true;
          });

          validDataCount += validData.length;
        }
      }

      // 更新数据点数量为最大数据长度
      dataCount = maxDataLength;

      // 检查是否有有效数据
      if (validDataCount <= 0 || dataCount <= 0) {
        hasData = false;

        // 确保隐藏tooltip
        try {
          chart.dispatchAction({
            type: 'hideTip'
          });
        } catch (e) {
          // 忽略错误
        }

        return;
      }

      // 标记有数据
      hasData = true;

      // 重置当前索引，确保从头开始
      currentIndex = -1;
    } catch (error) {
      hasData = false;

      // 确保隐藏tooltip
      try {
        chart.dispatchAction({
          type: 'hideTip'
        });
      } catch (e) {
        // 忽略错误
      }
    }
  };

  // 显示提示框
  const showTooltip = (index) => {
    log('尝试显示提示框，索引:', index);

    if (!hasData || !chart || isPaused) {
      return;
    }

    // 更新当前索引
    currentIndex = index >= dataCount ? (config.loop ? 0 : -1) : index;

    if (currentIndex === -1) {
      // 如果索引无效且不循环，则停止
      stop();
      return;
    }

    try {
      // 获取当前图表配置
      const option = chart.getOption();

      // 检查是否有tooltip配置
      if (!option.tooltip) {
        chart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          }
        });
      }

      // 严格检查是否有数据
      let hasValidData = false;

      if (option.series && option.series.length) {
        for (let i = 0; i < option.series.length; i++) {
          const series = option.series[i];
          // 检查该系列是否有数据
          if (series.data && series.data.length && series.data.some(item => item !== null && item !== undefined && item !== 0)) {
            hasValidData = true;
            break;
          }
        }
      }

      if (!hasValidData) {
        // 确保隐藏tooltip
        chart.dispatchAction({
          type: 'hideTip'
        });
        return;
      }

      // 只使用一种方式触发tooltip，避免位置抖动
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: currentIndex
      });
    } catch (error) {
      // 忽略错误
    }
  };

  // 开始自动滚动
  const start = () => {
    // 清除已存在的定时器
    if (timer) {
      clearInterval(timer);
      timer = null;
    }

    // 更新图表数据信息
    updateDataInfo();

    // 检查是否有数据
    if (!hasData) {
      // 确保隐藏tooltip
      try {
        chart.dispatchAction({
          type: 'hideTip'
        });
      } catch (e) {
        // 忽略错误
      }
      return;
    }

    // 重置状态
    isPaused = false;
    currentIndex = -1;

    // 立即显示第一个提示
    showTooltip(0);

    // 设置定时器，定期切换提示位置
    timer = setInterval(() => {
      // 再次检查是否有数据，防止数据在滚动过程中被清空
      if (!hasData) {
        stop();
        return;
      }

      // 如果暂停了，则不显示提示
      if (isPaused) {
        return;
      }

      // 显示下一个提示
      showTooltip(currentIndex + 1);
    }, config.interval);

    return { stop, pause, resume };
  };

  // 停止自动滚动
  const stop = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }

    // 隐藏提示框
    try {
      // 确保隐藏tooltip
      chart.dispatchAction({
        type: 'hideTip'
      });

      // 取消所有高亮状态
      chart.dispatchAction({
        type: 'downplay'
      });
    } catch (error) {
      // 忽略错误
    }

    // 重置状态
    currentIndex = -1;
    isPaused = false;
  };

  // 暂停自动滚动
  const pause = () => {
    isPaused = true;
  };

  // 恢复自动滚动
  const resume = () => {
    isPaused = false;
  };

  // 返回控制对象
  return {
    start,
    stop,
    pause,
    resume,
    updateDataInfo
  };
};

export default createAutoTooltip;
