<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-09-22 17:40:50
-->
<template>
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">编辑</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      // const record = {"createdBy":null,"createdDate":null,"modifiedBy":null,"modifiedDate":null,"remark":null,"id":32343474,"ids":null,"loadDt":null,"dmId":"ZBP01058","cmimId":"ZBP01058-H0104214-W-0000-0000-DIM_958-0000-0000-DIM_231212005-0000-0000-0000-0000-0000","businessSegmentsId":"SEG_84","indexId":"ZBP01058","indexNameInd":null,"signOrgId":"H0104","orgId":"H0104214","indexUnitId":"UN_89","precisions":"2","codeValueId":"TYPE_101","indexFrequencyId":"W","isFactoryweek":"Y","isPartSum":"Y","isFromSum":"Y","formulaId":"FOR_509","contemFormulaId":"CFOR_479","previousFormulaId":"PFOR_482","contemRateFormulaId":"CRF_477","previousRateFormulaId":"PRF_478","sumValueFormulaId":null,"sumContemFormulaId":null,"sumPreviousFormulaId":null,"sumRatioFormulaId":null,"sumContemRateFormulaId":null,"sumPreviousRateFormulaId":null,"targetValueWayId":"TVW_516","targetFormulaId":"TFOR_512","sumTargetValueWayId":null,"sumTargetFormulaId":null,"classCompareDesc":null,"fillInPerson":"冯艳清-fengyanqing","fillInPersonLeader":"刁斌-diaobin","isDelete":"N","productTypeId":null,"brandId":null,"productAtt1Id":"DIM_958-卡片标签","productAtt2Id":null,"productAtt3Id":null,"productAtt4Id":"DIM_231212005","productAtt5Id":null,"productAtt6Id":null,"productAtt7Id":null,"productAtt8Id":null,"productAtt9Id":null,"p1":null,"p2":null,"p3":null,"p4":null,"p5":null,"p6":null,"p7":null,"perfectId":"6006","hid":null,"fullCode":null,"treeType":null,"productAttList":null,"companyId":null,"company":null,"customIndexName":null,"isManually":"N","isDel":null,"dimension":null,"businessSegments":"交付","signOrg":"智动精工公司","indexName":"产能利用率","org":"贵阳板卡部","indexUnit":"%","codeValue":"正向","indexFrequency":"周","formula":"分子比分母","contemFormula":"同期分子比同期分母","previousFormula":"上期分子比上期分母","contemRateFormula":"(当期-同期)/|同期|","previousRateFormula":"(当期-上期)/|上期|","sumValueFormula":null,"sumContemFormula":null,"sumPreviousFormula":null,"sumRatioFormula":null,"sumContemRateFormula":null,"sumPreviousRateFormula":null,"targetValueWay":"日周同于月度","targetFormula":"实际值比目标值","sumTargetValueWay":null,"sumTargetFormula":null,"productType":null,"brand":null,"productAtt1Dimtp":"SMT","productAtt2Dimtp":null,"productAtt3Dimtp":null,"productAtt4Dimtp":"是遥控器","productAtt5Dimtp":null,"productAtt6Dimtp":null,"productAtt7Dimtp":null,"productAtt8Dimtp":null,"productAtt9Dimtp":null,"groupId":null,"role":null,"orgId1":null,"signOrgId1":null,"list":null,"listDis":null,"listOrgId":null,"indexFrequencyList":null,"jsonLocation":null,"classKeybusinessSegments":"visiblebusinessSegments17024589839911636418798000","classKeyindexName":"visibleindexName17024589839921636418798000","classKeysignOrg":"visiblesignOrg17024589839931636418798000","classKeyorg":"visibleorg17024589839931636418798000","classKeydmId":"visibledmId17024589839941636418798000","classKeyfillInPerson":"visiblefillInPerson17024589839941636418798000","classKeyindexNameInd":"visibleindexNameInd17024589839951636418798000","classKeyindexUnit":"visibleindexUnit17024589839951636418798000","classKeycodeValue":"visiblecodeValue17024589839961636418798000","classKeyindexFrequency":"visibleindexFrequency17024589839961636418798000","classKeyprecisions":"visibleprecisions17024589839971636418798000","classKeyisManually":"visibleisManually17024589839971636418798000","classKeyisFactoryweek":"visibleisFactoryweek17024589839981636418798000","classKeyisDelete":"visibleisDelete17024589839981636418798000","classKeyclassCompareDesc":"visibleclassCompareDesc17024589839981636418798000","classKeyisPartSum":"visibleisPartSum17024589839991636418798000","classKeyisFromSum":"visibleisFromSum17024589839991636418798000","classKeyformula":"visibleformula17024589840001636418798000","classKeycontemFormula":"visiblecontemFormula17024589840001636418798000","classKeypreviousFormula":"visiblepreviousFormula17024589840011636418798000","classKeycontemRateFormula":"visiblecontemRateFormula17024589840011636418798000","classKeypreviousRateFormula":"visiblepreviousRateFormula17024589840021636418798000","classKeysumValueFormula":"visiblesumValueFormula17024589840021636418798000","classKeysumContemFormula":"visiblesumContemFormula17024589840031636418798000","classKeysumPreviousFormula":"visiblesumPreviousFormula17024589840031636418798000","classKeysumRatioFormula":"visiblesumRatioFormula17024589840041636418798000","classKeysumContemRateFormula":"visiblesumContemRateFormula17024589840051636418798000","classKeysumPreviousRateFormula":"visiblesumPreviousRateFormula17024589840051636418798000","classKeytargetValueWay":"visibletargetValueWay17024589840061636418798000","classKeytargetFormula":"visibletargetFormula17024589840071636418798000","classKeysumTargetValueWay":"visiblesumTargetValueWay17024589840071636418798000","classKeysumTargetFormula":"visiblesumTargetFormula17024589840081636418798000","classKeyproductAtt1Dimtp":"visibleproductAtt1Dimtp17024589840091636418798000","classKeyproductAtt2Dimtp":"visibleproductAtt2Dimtp17024589840101636418798000","classKeyproductAtt3Dimtp":"visibleproductAtt3Dimtp17024589840101636418798000","classKeyproductAtt4Dimtp":"visibleproductAtt4Dimtp17024589840111636418798000","classKeyproductAtt5Dimtp":"visibleproductAtt5Dimtp17024589840121636418798000","classKeyproductAtt6Dimtp":"visibleproductAtt6Dimtp17024589840121636418798000","classKeyproductAtt7Dimtp":"visibleproductAtt7Dimtp17024589840131636418798000"};
      const record = this.record;
      // console.log(JSON.stringify(this.record));
      this.$refs["modal"].show(record);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex
        }
      });
    }
  }
};
</script>
