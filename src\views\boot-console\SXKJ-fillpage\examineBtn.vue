<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2024-10-17 13:51:07
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-18 09:58:38
 * @FilePath: \localdev\pangea-component\src\views\boot-console\SXKJ-fillpage\examineBtn.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="display: inline-block;">
    <a-button
      v-if="isShow"
      :disabled="!selectedRowsLength"
      :type="data?.type || 'primary'"
      :size="size"
      @click="btClick"
      >{{ showAlias(data?.showName, "审批") }}</a-button
    >
    <Modal ref="modal" :selectedRows="this.selectedRows" @fetchData="fetchData"/>
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import request from "@/utils/requestHttp";
import Modal from "./examineBtnModal.vue"
export default {
  data() {
    return {
      isShow: false,
      showAlias,
    };
  },
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
    selectedRows:Array
  },
  methods: {
    btClick() {
      this.$refs["modal"].show()
    },
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex
        }
      });
    }
  },
  computed:{
    selectedRowsLength(){
        return this.selectedRows.length
    }
  },
  mounted() {
    request("/api/smc2/gate/isButton").then((res) => {
      this.isShow = res;
    });
  },
};
</script>
