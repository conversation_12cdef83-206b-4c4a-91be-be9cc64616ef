<template>
  <a-spin style="height: 100%;" :spinning="dataLoading">
    <div class="mdys-modal-container">
      <div class="container-top">
        <div class="_left">
          <div class="title">组织层级末端因素分析</div>
          <template v-show="!isMoneyIndex">
            <a-radio-group
              size="small"
              button-style="solid"
              v-model="typeRadioValue"
              @change="typeRadioChange"
            >
              <a-tooltip v-for="item in typeRadioList">
                <template slot="title" v-if="item.toolTip">
                  <p>
                    计算依据：<br /><span>{{ item.toolTip.accordingTo }}</span>
                  </p>
                  <p>
                    计算公式：<br /><span>{{ item.toolTip.formula1 }}</span
                    ><br />
                    <span>{{ item.toolTip.formula2 }}</span
                    ><br />
                    <span>{{ item.toolTip.formula3 }}</span>
                  </p>
                </template>
                <a-radio-button :value="item.key" :key="item.key">
                  {{ item.value }}
                </a-radio-button>
              </a-tooltip>
            </a-radio-group>
          </template>
        </div>
        <div class="_right">
          <div class="zrr">
            <div v-for="item in indexContactUser" :key="item.phonenumber">
              <a-tag class="zw-tag" style="margin-right: 8px;">
                {{ item.password }}
              </a-tag>
              <div class="name">{{ item.userName }}</div>
              <div class="contact">
                <!-- 信鸿 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>联系信鸿</span>
                  </template>
                  <div class="contactType xinhong" @click="chat(item)">
                    <template
                      v-if="skinStyle && skinStyle().includes('classic-style')"
                    >
                      <img
                        :src="require('@/assets/images/icon-xinhong.png')"
                        alt=""
                        srcset=""
                      />
                    </template>
                    <template v-else>
                      <img
                        :src="require('@/assets/images/<EMAIL>')"
                        alt=""
                        srcset=""
                      />
                    </template>
                  </div>
                </a-tooltip>
                <!-- 邮件 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>发送邮件</span>
                  </template>
                  <div class="contactType email" @click="sendMail(item)">
                    <template
                      v-if="skinStyle && skinStyle().includes('classic-style')"
                    >
                      <img
                        :src="require('@/assets/images/icon-email.png')"
                        alt=""
                        srcset=""
                      />
                    </template>
                    <template v-else>
                      <img
                        :src="require('@/assets/images/<EMAIL>')"
                        alt=""
                        srcset=""
                      />
                    </template>
                  </div>
                </a-tooltip>
                <!-- 督办 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>闭环</span>
                  </template>
                  <div
                    class="contactType duban"
                    :style="{
                      background: ['noTask', 'complete', undefined].includes(
                        item.taskStatus
                      )
                        ? '#e7e9ee'
                        : item.taskStatus === 'processing'
                        ? '#52c41a'
                        : '#f5222d',
                    }"
                    @click="dubanClick(item.loginName)"
                  >
                    <span
                      style="font-size: 12px;"
                      :style="{
                        color: ['noTask', 'complete', undefined].includes(
                          item.taskStatus
                        )
                          ? 'rgba(0, 0, 0, 0.65)'
                          : '#fff',
                      }"
                      >闭环</span
                    >
                  </div>
                </a-tooltip>
              </div>
            </div>
          </div>
          <a-divider type="vertical" />
          <div
            style="font-size: 14px;color: rgb(134, 144, 156);display: flex;align-items: center;"
          >
            预警
            <a-switch
              :checked="warnFlag"
              checked-children="ON"
              un-checked-children="OFF"
              @change="switchChange"
              :loading="warnFlagLoading"
              style="margin-left: 8px;"
            />
          </div>
        </div>
      </div>
      <div class="reason" v-show="reason">
        <a-icon type="unordered-list" />
        <div v-html="reason"></div>
      </div>
      <div class="reason" v-show="reason">
        <a-icon type="unordered-list" />
        <div>
          您可直接点击上方信鸿或者邮件沟通指标主人进行闭环管理，也可点击右上角订阅预警。
        </div>
      </div>
      <template v-if="Object.keys(datas).length">
        <vue2-org-tree
          style="width: 100%;height: 300px;overflow: auto;"
          ref="leftpartdiv"
          :data="datas"
          :horizontal="horizontal"
          :collapsable="collapsable"
          :label-class-name="labelClassName"
          :render-content="renderContent"
          @on-expand="onExpand"
          name="organ"
        />
      </template>
      <template v-else>
        <a-spin :spinning="dataLoading">
          <div style="height: calc(100% - 137px);width: 100%;"></div>
        </a-spin>
      </template>
    </div>
    <Mail ref="mail" :pageClass="pageClass" />
    <DuBanModal ref="dubanModal" />
  </a-spin>
</template>
<script>
import request from "@/utils/requestHttp";
import axios from "axios";
import Decimal from "decimal.js";
import Mail from "../CardInfo/mail.vue";
import DuBanModal from "../CardInfo/duBanModal.vue";
import Vue2OrgTree from "vue2-org-tree";

export default {
  inject: ["skinStyle"],
  components: { Mail, DuBanModal, Vue2OrgTree },
  data() {
    return {
      cardItem: {
        businessSegments: "",
      },
      typeRadioValue: "", // 单选组件选中值
      warnFlag: false, // 是否预警
      warnFlagLoading: false,
      dataLoading: false,
      indexContactUser: [], // 指标负责人列表
      reason: "", // 原因
      datas: {},
      horizontal: true, //横版 竖版
      collapsable: true,
      expandAll: true, //是否全部展开
      labelClassName: "白色", // 默认颜色
    };
  },
  props: {
    pageClass: String,
    companyName: String,
    signOrgId: String,
  },
  computed: {
    // 是否元、万元单位指标
    isMoneyIndex() {
      // console.log(["元", "万元"].includes(this.cardItem.indexUnitId),'===')
      // return ["元", "万元"].includes(this.cardItem.indexUnitId);
      return this.typeRadioValue.includes("贡献度");
    },
    typeRadioList() {
      const list = [
        {
          value: "与计划比",
          key: "与计划比",
        },
      ];
      return this.cardItem.businessSegments === "效率"
        ? [
            ...list,
            {
              value: "同比",
              key: "同比",
            },
            {
              value: "贡献度",
              key: "贡献度",
              toolTip: {
                accordingTo: "销售收入影响度根据销售收入占比变化计算",
                formula1: "正向影响度=正向KL散度/总体正向KL散度和",
                formula2: "负向响度=负向KL散度/总体负向KL散度和",
                formula3:
                  "KL散度=当前销售收入占比*log(当期销售收入占比/对比期销售收入占比)",
              },
            },
          ]
        : [
            ...list,
            {
              value: "贡献度",
              key: "贡献度",
              toolTip: {
                accordingTo: "销售收入影响度根据销售收入占比变化计算",
                formula1: "正向影响度=正向KL散度/总体正向KL散度和",
                formula2: "负向响度=负向KL散度/总体负向KL散度和",
                formula3:
                  "KL散度=当前销售收入占比*log(当期销售收入占比/对比期销售收入占比)",
              },
            },
          ];
    },
  },
  methods: {
    // 树图渲染
    renderContent(h, data) {
      const _this = this;
      return (
        <div>
          {data.isRoot ? (
            <div class="org-item-intree">
              <div class="title">{_this.cardItem.org}</div>
              <div class="value">
                {_this.isMoneyIndex ? "完成率" : _this.typeRadioValue}：
                {_this.cardItem.targetCompletionRate}
              </div>
            </div>
          ) : (
            <div class="org-item-intree">
              <div class="title">{data.title}</div>
              {data.showValue ? (
                <div
                  class={[
                    "value",
                    data.type === "正向" ? "forward" : "reverse",
                  ]}
                >
                  {_this.isMoneyIndex ? "贡献度" : "影响度"}：{data.value}
                </div>
              ) : (
                ""
              )}
            </div>
          )}
        </div>
      );
    },
    onExpand(e, data) {
      if ("expand" in data) {
        data.expand = !data.expand;
        if (!data.expand && data.children) {
          this.collapse(data.children);
        }
      } else {
        this.$set(data, "expand", true);
      }
    },
    // 初始化
    init(cardItem) {
      this.dataLoading = true;
      this.cardItem = cardItem;
      this.typeRadioValue = this.isMoneyIndex ? "" : "与计划比";
      // this.getCardInfo(cardItem.indexId);
      this.getDatas();
      const hichat_AccessToken = window.vm.$store
        ? window.vm.$store.state.hichat_AccessToken
        : "";
      // 如果没有信鸿Token则获取
      if (!hichat_AccessToken) {
        this.getHichatAccessToken();
      }
      // this.warnUserSearch(); // 预警查询
      // console.log("cardItem----->", cardItem);
    },
    // 关闭
    close() {
      this.cardItem = {
        businessSegments: "",
      };
      this.typeRadioValue = "";
      this.warnFlag = false;
      this.indexContactUser = [];
      this.reason = "";
      this.datas = {};
    },
    // 单选按钮改变
    typeRadioChange() {
      this.getDatas();
    },
    // 获取数据
    getDatas() {
      this.dataLoading = true;
      const postData = {
        indexDt: this.cardItem.indexDt,
        cmimId: this.cardItem.cmimId,
        indexName: this.cardItem.indexName,
        org: this.cardItem.org,
        targetCompletionRate: this.cardItem.targetCompletionRate,
      };
      if (this.isMoneyIndex) {
        postData["bak"] = 1;
      } else {
        postData["compareTypes"] = this.typeRadioValue;
      }
      request(
        "/api/smc2/newIndexLibrary/endPointFactorAnalysis?BI=1",
        {
          method: "POST",
          body: postData,
        },
        {}
      ).then((res) => {
        this.dataLoading = false;
        let reason = res["内容"] || "";
        if (reason) {
          let arr = reason.split(
            reason.includes("（完成率：") ? "（完成率：" : "(完成率："
          );
          arr = arr.map((item, index) =>
            index !== arr.length - 1
              ? `${item}（<span style="font-weight: bold;">完成率：</span>`
              : item
          );
          let finalArr = [];
          arr.forEach((item) => {
            if (item.includes("%）")) {
              let itemArr = item.split("%）");
              // let nums = itemArr[0];
              // nums = `${Decimal(parseFloat(nums))
              //   .mul(Decimal(100))
              //   .toFixed(2, Decimal.ROUND_HALF_UP)}%`;
              if (itemArr[0].includes("-") || parseFloat(itemArr[0]) < 100) {
                finalArr.push(
                  `<span style="color: rgb(52, 145, 250);">${itemArr[0]}%</span>）${itemArr[1]}`
                );
              } else {
                finalArr.push(
                  `<span style="color: rgb(247, 80, 80);">${itemArr[0]}%</span>）${itemArr[1]}`
                );
              }
            } else {
              finalArr.push(item);
            }
          });
          this.reason = finalArr.join("");
        } else {
          this.reason = "";
        }
        if (res["正向影响"] || res["负向影响"]) {
          // const data = this.loopData(res);
          const children = [];
          if (res["正向影响"]) {
            children.push({
              expand: true,
              showValue: false,
              title: "正向影响",
              // value:
              //   this.typeRadioValue === "与计划比"
              //     ? (res["正向影响"] || {}).targetEffect
              //     : (res["正向影响"] || {}).sumEffect,
              children: this.loopData(res["正向影响"].list || [], 0, "正向"),
            });
          }
          if (res["负向影响"]) {
            children.push({
              expand: true,
              showValue: false,
              title: "负向影响",
              // value:
              //   this.typeRadioValue === "与计划比"
              //     ? (res["负向影响"] || {}).targetEffect
              //     : (res["负向影响"] || {}).sumEffect,
              children: this.loopData(res["负向影响"].list || [], 0, "负向"),
            });
          }
          this.datas = {
            isRoot: true,
            children,
            expand: true,
          };
        } else {
          this.datas = {
            isRoot: true,
            children: [],
            expand: true,
          };
        }
      });
    },
    loopData(arr, index = 0, type = "正向") {
      index++;
      return arr.map((item) => {
        const { org, targetEffect, sumEffect, contribution } = item;
        return {
          showValue: true,
          title: org,
          value: this.isMoneyIndex
            ? this.dealValue(contribution)
            : this.typeRadioValue === "与计划比"
            ? this.dealValue(targetEffect)
            : this.dealValue(sumEffect),
          type,
          children: this.loopData(item.list || [], index, type),
          expand: false,
        };
      });
    },
    // 处理值
    dealValue(value) {
      return typeof value === "number" && !isNaN(value)
        ? `${Decimal(value)
            .mul(Decimal(100))
            .toFixed(2, Decimal.ROUND_HALF_UP)}%`
        : "";
    },
    // 获取信鸿token
    getHichatAccessToken() {
      request(`/api/smc/hichatx/getAccessToken`)
        .then((res) => {
          window.vm.$store.commit("rootSave", {
            hichat_AccessToken: res.accessToken,
          });
        })
        .catch(() => {});
    },
    // 获取卡片指标定义
    getCardInfo(indexCode) {
      request(
        `/api/smc/indexInfo/getIndexDetailByCode?BI=1`,
        {
          method: "POST",
          body: {
            indexCode,
            indexName: this.cardItem.indexName,
            menu_name: `${this.companyName}核心KPI${
              this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
            }${
              this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""
            }`,
            version: "2.0",
            signOrgId:
              this.signOrgId === "H" ? this.cardItem.signOrgId : this.signOrgId,
            businessSegmentsId: this.cardItem.businessSegmentsId,
            orgId: this.cardItem.orgId,
            fullCode: this.cardItem.fullCode,
            cmimId: this.cardItem.cmimId,
          },
        },
        {}
      ).then((res) => {
        if (Array.isArray(res.user)) {
          this.indexContactUser = res.user.filter(
            (item) => item.password === "数据主人"
          );
        }
        // this.dataLoading = false;
      });
    },
    // 查询预警
    // warnUserSearch() {
    //   this.warnFlagLoading = true;
    //   request(`/api/smc2/warnUser/search?cmimId=${this.cardItem.cmimId}`).then(
    //     (res) => {
    //       if (res && res.result) {
    //         this.warnFlag = res.result;
    //       }
    //       this.warnFlagLoading = false;
    //     }
    //   );
    // },
    // 预警开关保存
    switchChange(checked) {
      this.warnFlagLoading = true;
      if (checked) {
        request(`/api/smc2/warnUser/insertWarnUser`, {
          method: "POST",
          body: {
            cmimId: this.cardItem.cmimId,
          },
        })
          .then(() => {
            this.warnFlagLoading = false;
            this.warnFlag = checked;
          })
          .catch(() => {
            this.warnFlagLoading = false;
          });
      } else {
        request(`/api/smc2/warnUser/delWarnUser?cmimId=${this.cardItem.cmimId}`)
          .then(() => {
            this.warnFlagLoading = false;
            this.warnFlag = checked;
          })
          .catch(() => {
            this.warnFlagLoading = false;
          });
      }
    },
    // 信鸿聊天
    chat(item) {
      const data = encodeURIComponent(`{array:['${item.loginName}']}`);
      axios({
        url: `https://hichatx.hisense.com/gateway/openimport/open/person/getInfoByJobNo?accessToken=${window.vm.$store.state.hichat_AccessToken}`,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        method: "post",
        data: `eid=101&data=${data}`,
      }).then((res) => {
        if (res.data?.success) {
          const defaultMessage = `${this.cardItem.businessSegments}-${
            this.cardItem.wdInCardName
              ? "(" + this.cardItem.wdInCardName + ")"
              : ""
          }${this.cardItem.displayIndexName}（${this.companyName}-${
            this.cardItem.org
          }-${this.cardItem.indexDt}${this.cardItem.indexFrequency}）
目标值：${this.cardItem.targetValue} ${this.cardItem.indexUnitId}；实际值：${
            this.cardItem.actualValue
          } ${this.cardItem.indexUnitId}；完成率：${
            this.cardItem.targetCompletionRate
              ? this.cardItem.targetCompletionRate
              : "-%"
          }；
${
  this.cardItem.isContemRate === "Y"
    ? "同比" +
      (this.cardItem.contemChangeRate
        ? (this.cardItem.contemChangeRate.includes("-") &&
            this.cardItem.indexType === "反向") ||
          (!this.cardItem.contemChangeRate.includes("-") &&
            this.cardItem.indexType === "正向")
          ? "上涨"
          : "下降"
        : "") +
      "：" +
      (this.cardItem.contemChangeRate
        ? Math.abs(
            Decimal(this.cardItem.contemChangeRate)
              .mul(Decimal(100))
              .toFixed(2, Decimal.ROUND_HALF_UP)
          )
        : "-") +
      "%；"
    : ""
} ${
            this.cardItem.isPreviousRate === "Y"
              ? "环比" +
                (this.cardItem.previousChangeRate
                  ? (this.cardItem.previousChangeRate.includes("-") &&
                      this.cardItem.indexType === "反向") ||
                    (!this.cardItem.previousChangeRate.includes("-") &&
                      this.cardItem.indexType === "正向")
                    ? "上涨"
                    : "下降"
                  : "") +
                "：" +
                (this.cardItem.previousChangeRate
                  ? Math.abs(
                      Decimal(this.cardItem.previousChangeRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)
                    )
                  : "-") +
                "%；"
              : ""
          }`;
          if (window.self !== window.top) {
            window.parent.postMessage(
              {
                sourceType: "smc",
                msgType: "hiChat",
                msgContent: JSON.stringify({
                  openId: res.data.data[0],
                  defaultMessage,
                }),
              },
              "*"
            );
          } else {
            this.openHiChatWindow({
              openId: res.data.data[0],
              defaultMessage,
            });
          }
        }
      });
    },
    // 初始化信鸿api
    initQing(msgContent) {
      const script = document.createElement("script");
      script.src = "https://hichatx.hisense.com/public/js/qing/latest/qing.js";
      script.onload = () => {
        window.qing.use("desktop-remote", {
          timeout: 30000, // 选填，默认10000毫秒
          saas: true, // 必填 云之家是否为私有部署
        });
        setTimeout(() => {
          this.openHiChatWindow(msgContent);
        }, 1000); // 要延时一秒执行，否则信鸿无响应
      };
      document.body.appendChild(script);
    },
    openHiChatWindow(msgContent) {
      if (!window.qing) {
        this.initQing(msgContent);
        return;
      }
      // 打开聊天窗口
      window.qing.call("chat", {
        openId: msgContent.openId,
        draft: msgContent.defaultMessage,
      });
    },
    // 发送邮件
    sendMail(user) {
      this.$refs["mail"].show({ ...this.cardItem, email: user.email });
    },
  },
};
</script>
<style lang="less">
.mdys-modal-container {
  .container-top {
    padding: 10px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    ._left {
      display: flex;
      align-items: center;
      .title {
        font-size: 16px;
        height: 24px;
        font-weight: bold;
        margin-right: 15px;
      }
    }
    ._right {
      display: flex;
      align-items: center;
      .zrr {
        & > div {
          display: flex;
          align-items: center;
          font-weight: bold;
          .contact {
            display: flex;
            margin-left: 10px;
            .contactType {
              width: 24px;
              height: 24px;
              background: #e7e9ee;
              border-radius: 50%;
              cursor: pointer;
              &:not(:last-child) {
                margin-right: 4px;
              }
              transition: background ease-in-out 0.3s;
              &:hover {
                background: #fff;
              }
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 18px;
                height: 18px;
                display: block;
              }
            }
          }
        }
      }
    }
  }
  .reason {
    padding: 0 16px;
    display: flex;
    line-height: 18px;
    .anticon-unordered-list {
      margin-top: 3px;
      margin-right: 10px;
    }
    ._blue {
      color: rgb(52, 145, 250);
    }
    ._red {
      color: rgb(247, 80, 80);
    }
  }
  .org-item-intree {
    .value {
      &.forward {
        color: rgb(247, 80, 80);
      }
      &.reverse {
        color: rgb(52, 145, 250);
      }
    }
  }
}
</style>
