/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-20 10:19:22
 * @LastEditors: yuy<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-08-30 10:58:25
 */

import request from "@/utils/requestHttp";
import Vue from "vue";
import * as Cookies from "js-cookie";
const system = "smc";
import CryptoJS from "crypto-js/crypto-js";

export function evil(fn) {
  // // 等同于eval()方法
  // const Fn = Function; // 一个变量指向Function，防止有些前端编译工具报错
  return eval(fn);
}

export const adminUserUrlPrefix = {
  zcx: "/api/smc",
  lxp: "/api/smc"
};

// 根据用户名模糊查询督办用户信息
export const getDuBanUserByName = (value, callback) => {
  request(`/api/smc/duban/getUserByName?info=${value}`)
    .then(res => {
      typeof callback === "function" && callback(res);
    })
    .catch(() => {
      typeof callback === "function" && callback(false);
    });
};

// 督办配置
export const dubanConfig = {
  defaultParams: {
    type: "云图督办",
    typeCode: "YTDB",
    typeId: 15081100
  },
  adminUserCode:
    window.location.origin === "https://cmdt.hisense.com" ||
    window.location.origin.includes("pangea")
      ? "90090919"
      : "wangpengjun.ex", // 从督办系统里获取的管理员的userCode，用在了所有督办任务列表查询的userCode参数上， 如不生效可向督办系统重新要一个userCode
  origin:
    window.location.origin === "https://cmdt.hisense.com" ||
    window.location.origin.includes("pangea")
      ? "https://duban.hisense.com/"
      : "https://172.16.96.61:8091/" // 督办系统的域名
};

// 获取该卡片当前基地的报表链接
export const getCardReportList = cardItem => {
  return new Promise(resolve => {
    request(
      `${adminUserUrlPrefix["lxp"]}/indexCardUrl/list?cardId=${cardItem.id}`
    ).then(res => {
      if (Array.isArray(res) && res.length) {
        const arr = res.filter(item => {
          return cardItem.companyName === "集团"
            ? item.baseName === "整体"
            : item.baseName === cardItem.baseName;
        });
        if (arr.length === 1) {
          resolve(arr[0]);
        } else {
          resolve("");
        }
      } else {
        resolve("");
      }
    });
  });
};

// 打开报表
export const openReport = (cardItem, currentReportSetting) => {
  return new Promise(resolve => {
    request(`/api/smc/sysOperLog/saveLog`, {
      method: "POST",
      body: {
        menu: `${cardItem.companyName}核心KPI横比${
          window.system === "industrialInternet" ? "-工业互联网平台" : ""
        }`,
        title: "查看报表",
        operation: `查看报表[${cardItem.companyName}-${cardItem.baseName}-${currentReportSetting.reportName}]`
      }
    });
    if (window.self !== window.top) {
      window.parent.postMessage(
        {
          reportId: currentReportSetting.id,
          sourceType: "smc"
        },
        "*"
      );
      resolve();
    } else {
      request(
        `/api/smc/dt/getDtReportUrl?reportId=${currentReportSetting.id}`
      ).then(res => {
        if (res.url) {
          window.vm.$router.push({
            path: "/smc_pc/reportPage",
            query: {
              reportUrl: decodeURIComponent(res.url)
            }
          });
          resolve();
        }
      });
    }
  });
};

// url
export const publicPath =
  window.location.origin ||
  window.location.protocol +
    "//" +
    window.location.hostname +
    (window.location.port ? ":" + window.location.port : "");

// 获取cookie
function getCookie(key) {
  return Cookies.get(key);
}
const ACCESS_TOKEN = "Access-Token";
// 文件/图片上传请求headers
export function httpHeaders() {
  const token = Vue.ls.get(ACCESS_TOKEN);
  const menuCode = Vue.ls.get("turnToMenuCode");
  const currentId = getCookie("current-id");
  const obj = {
    systemName: system
  };
  if (token) {
    obj.token = token;
    obj.Authorization = `Bearer ${token}`;
  }
  if (menuCode) {
    obj.menuCode = menuCode;
  }
  if (currentId) {
    obj["current-id"] = currentId;
  }
  return obj;
}
/**
 * @description: 获取minio地址，当参数不包含http时需拼接全路径
 * @param {*} url 需处理的地址
 * @return {*} minio url地址
 */
export function genMinioUrl(url) {
  let fileUrl = url;
  if (url.indexOf("http") === -1) {
    fileUrl = `${publicPath}/minio${url}`;
  }
  return fileUrl;
}

// 人员部门展示
export function changeLdapFullName(name) {
  let newNameList = name.split("\\");
  let totalArr =
    newNameList.length > 2 ? "../" + newNameList.slice(-2).join("\\") : name;
  return totalArr;
}

// 时间戳转换时间
export function covertDate(value, type = 0) {
  var time = new Date(value);
  var year = time.getFullYear();
  var month = time.getMonth() + 1;
  var date = time.getDate();
  var hour = time.getHours();
  var minute = time.getMinutes();
  var second = time.getSeconds();
  month = month < 10 ? "0" + month : month;
  date = date < 10 ? "0" + date : date;
  hour = hour < 10 ? "0" + hour : hour;
  minute = minute < 10 ? "0" + minute : minute;
  second = second < 10 ? "0" + second : second;
  var arr = [
    year + "-" + month + "-" + date,
    year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second,
    year + "年" + month + "月" + date,
    year +
      "年" +
      month +
      "月" +
      date +
      " " +
      hour +
      ":" +
      minute +
      ":" +
      second,
    hour + ":" + minute + ":" + second
  ];
  return arr[type];
}

// 获取url中的参数 getUrlParam
export function getUrlParam(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) {
    return decodeURI(r[2]);
  } else {
    return null;
  }
}

/* ** des加密方法   ****/
export function encodeDes(data, noBase64, cryKey) {
  if (!cryKey) {
    cryKey = "loginKey"; // 长度必须为8位,否则后台会报错
  }
  const key = CryptoJS.enc.Utf8.parse(cryKey); // 加密秘钥
  const iv = CryptoJS.enc.Utf8.parse(cryKey); //  矢量偏移
  const options = {
    //  DES加密
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7 // 后台用的是pad.Pkcs5,前端对应为Pkcs7
  };
  const encryptResult = CryptoJS.DES.encrypt(data, key, options);
  if (noBase64) {
    // 不进行base64编码
    return encryptResult.ciphertext.toString().toUpperCase();
  }
  return encodeURIComponent(
    CryptoJS.enc.Base64.stringify(encryptResult.ciphertext)
  ); // Base64加密再 encode;
}

// // 文件/图片上传请求headers
// export function httpHeaders () {
//   let token = sessionStorage.getItem(ACCESS_TOKEN);
//   if (!token) {
//     token = JSON.parse(localStorage.getItem(`pro__${ACCESS_TOKEN}`)).value;
//   }
//   let menuCode = localStorage.getItem("pro__turnToMenuCode");
//   if (menuCode) {
//     menuCode = JSON.parse(menuCode).value
//   }
//   return {
//     token,
//     Authorization: `Bearer ${token}`,
//     "current-id": getCookie("current-id"),
//     systemName: window.system,
//     menuCode,
//     "default_language": getCookie("hi-lang")
//   }
// }