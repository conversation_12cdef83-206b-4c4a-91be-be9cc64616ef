<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2024-10-17 15:59:56
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2024-12-09 09:00:19
 * @FilePath: \localdev\pangea-component\src\views\boot-console\SXKJ-fillpage\modal3.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <a-modal v-model="visible" :destroyOnClose="true" :width="560" title="审核">
    <div class="dialogInner" v-if="selectOption.length">
      <a-form-model
        :form="formData"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="审批通过">
          <a-select
            style="width: 120px"
            :options="selectOption"
            v-model="formData.selectVal"
          >
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          label="驳回原因"
          v-if="this.formData.selectVal === '0'"
        >
          <a-textarea
            placeholder="请输入驳回原因"
            :auto-size="{ minRows: 3 }"
            v-model="formData.reason"
          />
        </a-form-model-item>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button key="back" @click="close">
        取消
      </a-button>
      <a-button key="submit" type="primary" @click="handleOk">
        提交
      </a-button>
    </template>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
export default {
  props: {
    selectedRows: Array,
  },
  data() {
    return {
      visible: false,
      selectOption: [
        {
          label: "是",
          value: "1",
        },
        {
          label: "否",
          value: "0",
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      formData: {
        selectVal: undefined,
        reason: null,
      },
    };
  },
  methods: {
    show() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    handleOk() {
      //   console.log(this.selectedRows, "====",this.formData);
      if (this.selectedRows.length) {
        let selectRowsNotMaintenanceList = this.selectedRows.filter(
          (item) => item.state === "待审核"
        );
        let list = selectRowsNotMaintenanceList.map((item) => item.id);
        let receivers = [
          ...new Set(
            selectRowsNotMaintenanceList.map((item) => item.personResponsible)
          ),
        ];
        if (list.length) {
          request("/api/smc2/gate/process", {
            method: "post",
            body: {
              list,
              receivers,
              state: this.formData.selectVal === "1" ? "审核通过" : "驳回",
              table: "t_index_gate_his1",
              reason: this.formData.reason,
              sign: "隔离率", //"隔离率""OQC抽检不良率"
            },
          }).then((res) => {
            this.visible = false;
            this.$emit("fetchData");
          });
        } else {
          this.$message.warning("请选择待审核的数据进行审核");
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.dialogInner {
  width: 100%;
  height: 100%;
  padding-left: 80px;
}
</style>
