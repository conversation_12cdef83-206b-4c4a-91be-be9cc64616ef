<template>
  <div class="CardDetailInfoBox">
    <div class="drawerTitle">
      <span style="margin-right: 20px;">指标介绍</span>
      <a-switch
        v-if="pageClass === 'indexGeneralViewPage'"
        :checked="warnFlag === '0' ? false : true"
        checked-children="接收预警"
        un-checked-children="不接收预警"
        @change="switchChange"
        :loading="warnFlagLoading"
      />
    </div>
    <div class="__center">
      <!-- 顶部按钮 -->
      <div class="_top _flex" style="padding-top: 20px;">
        <span>{{ cardItem.indexName }}</span>
        <a-button type="primary" v-if="currentReportSetting" @click="toReport">
          前往指标详情{{
            currentReportSetting.developer
              ? "(报表开发人员：" + currentReportSetting.developer + ")"
              : ""
          }}
          <a-icon type="right" style="font-size: 12px;" />
        </a-button>
      </div>
      <!-- 指标说明 -->
      <div class="_indexIntro">
        <div class="_title">
          <span>指标说明</span>
        </div>
        <div class="_info" :class="[showAll ? 'showall' : '']">
          <div class="item _flex">
            <span class="_title">指标定义：</span>
            <span class="_txt" v-html="cardInfo.description"></span>
          </div>
          <div class="item _flex">
            <span class="_title">计算公式：</span>
            <span class="_txt">{{ cardInfo.func }}</span>
          </div>
          <!-- <div class="item _flex">
          <span class="_title">完成率：</span>
          <span class="_txt">实际值/目标值*100%</span>
        </div> -->
          <div class="item _flex">
            <span class="_title">数据来源：</span>
            <span class="_txt" v-html="cardInfo.logic"></span>
          </div>
          <div class="item _flex">
            <span class="_title">更新频率：</span>
            <span class="_txt">{{ cardInfo.frequency }}</span>
          </div>
        </div>
        <!-- 展开/折叠 -->
        <div class="expand _flex">
          <span @click="showAll = !showAll">{{
            showAll ? "折叠收起" : "展开全部"
          }}</span>
          <a-icon :type="showAll ? 'up' : 'down'" />
        </div>
      </div>
      <!-- 联系责任人 -->
      <div class="_responsible">
        <div class="_title">
          <span>联系责任人</span>
        </div>
        <div class="_list _flex">
          <div
            class="item _flex"
            v-for="(item, index) in indexContactUser"
            :key="index"
          >
            <div class="_flex">
              <!-- 部门 -->
              <a-tooltip placement="top">
                <template slot="title">
                  <div>{{ item.ldapFullPath }}</div>
                </template>
                <div class="depart">{{ item.ldapFullPath }}</div>
              </a-tooltip>
              <!-- 联系 -->
              <div class="_contact _flex">
                <!-- 信鸿 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>联系信鸿</span>
                  </template>
                  <div class="contactType xinhong" @click="chat(item)">
                    <img
                      :src="require('@/assets/images/icon-xinhong.png')"
                      alt=""
                      srcset=""
                    />
                  </div>
                </a-tooltip>
                <!-- 邮件 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>发送邮件</span>
                  </template>
                  <div class="contactType email" @click="sendMail(item)">
                    <img
                      :src="require('@/assets/images/icon-email.png')"
                      alt=""
                      srcset=""
                    />
                  </div>
                </a-tooltip>
                <!-- 督办 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>闭环</span>
                  </template>
                  <div
                    class="contactType duban"
                    :style="{
                      background: ['noTask', 'complete', undefined].includes(
                        item.taskStatus
                      )
                        ? '#e7e9ee'
                        : item.taskStatus === 'processing'
                        ? '#52c41a'
                        : '#f5222d'
                    }"
                    @click="dubanClick(item.loginName)"
                  >
                    <span
                      style="font-size: 12px;"
                      :style="{
                        color: ['noTask', 'complete', undefined].includes(
                          item.taskStatus
                        )
                          ? 'rgba(0, 0, 0, 0.65)'
                          : '#fff'
                      }"
                      >闭环</span
                    >
                  </div>
                </a-tooltip>
              </div>
            </div>

            <div class="_flex">
              <!-- 职位 -->
              <a-tag color="#00aaa6" style="margin-right: 8px;">
                {{ item.password }}
              </a-tag>
              <!-- 联系人 -->
              <div class="name">{{ item.userName }} {{ item.phonenumber }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 原因分析 -->
      <div class="_comment">
        <div class="_title">
          <div class="_l">
            <span>原因分析</span>
            <span>{{ commentListInSearch.length }}</span>
          </div>
          <a-select
            show-search
            :value="searchComment"
            placeholder="请选择日期"
            style="width: 200px"
            :filter-option="filterOption"
            @change="value => (searchComment = value)"
            :allowClear="true"
          >
            <a-select-option v-for="d in commentSearchList" :key="d">
              {{ d }}
            </a-select-option>
          </a-select>
        </div>
        <!-- 列表 -->
        <a-skeleton avatar :paragraph="{ rows: 2 }" :loading="commentLoading">
          <div
            class="list"
            v-for="(item, index) in commentListInSearch"
            :key="index"
          >
            <div class="item">
              <!-- 用户信息及时间 -->
              <div class="top _flex">
                <!-- <img
                :src="require('@/assets/images/icon-duban.png')"
                alt=""
                class="avatar"
              /> -->
                <div>
                  <span class="name">{{ item.commentPeopleName }}</span>
                  <span class="date">{{ item.createdDate }}</span>
                </div>
                <span class="intro">{{
                  `${cardItem.baseName}-${cardItem.indexName}${
                    item.indexDt ? "-" + item.indexDt : ""
                  }${item.frequency || ""}`
                }}</span>
              </div>
              <!-- 原因分析内容 -->
              <div
                class="content"
                :class="[nowLoginUserAccount === item.createdBy ? 'canOp' : '']"
              >
                <a-textarea
                  v-if="item.isEdit"
                  v-model="item.commentContent"
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                />
                <span v-if="!item.isEdit">{{ item.commentContent }}</span>
                <span
                  class="_op"
                  :class="[item.isEdit ? 'isEdit' : '']"
                  v-if="nowLoginUserAccount === item.createdBy"
                >
                  <!-- 修改 -->
                  <a-icon
                    title="修改"
                    type="edit"
                    v-if="!item.isEdit"
                    @click="editComment(index)"
                  />
                  <!-- 提交 -->
                  <a-icon
                    title="提交"
                    v-if="item.isEdit"
                    type="check"
                    @click="confirmUpdate(item, index)"
                  />
                  <!-- 删除 -->
                  <a-icon
                    title="删除"
                    type="delete"
                    @click="deleteComment(item)"
                  />
                </span>
              </div>
            </div>
          </div>
        </a-skeleton>
        <a-empty
          description="暂无原因分析"
          style="margin-top: 30px;"
          v-if="commentList.length === 0 && !commentLoading"
        />
      </div>
    </div>
    <!-- 原因分析输入框等 -->
    <div class="comment-box _flex">
      <a-input
        v-model="commentValue"
        placeholder="请输入你想要发表的原因分析内容"
        class="comment-input"
      />
      <a-button
        type="primary"
        @click="publishComment"
        :disabled="!commentValue"
      >
        发布原因分析
      </a-button>
    </div>
    <Mail ref="mail" :pageClass="pageClass" />
    <DuBanModal ref="dubanModal" />
  </div>
</template>
<script>
import Mail from "./mail.vue";
import request from "@/utils/requestHttp";
import {
  adminUserUrlPrefix,
  openReport,
  dubanConfig,
  getDuBanUserByName
} from "@/utils/utils";
import axios from "axios";
import cloneDeep from "lodash/cloneDeep";
import DuBanModal from "./duBanModal.vue";
import Decimal from "decimal.js";
export default {
  name: "IndexCardDetailInfo",
  components: { Mail, DuBanModal },
  props: {
    pageClass: String,
    frequency: String,
    indexDt: String,
    dataItem: Object
  },
  data() {
    return {
      showAll: false, // 展开/折叠
      cardItem: {},
      commentValue: "",
      commentLoading: true,
      commentList: [], // 原因分析列表
      reportSettingList: [],
      warnFlag: "0", // 是否预警
      warnFlagLoading: false,
      cardInfo: {
        description: "",
        func: "",
        logic: "",
        frequency: ""
      }, // 卡片指标定义
      indexContactUser: [], // 指标负责人
      SYS_NAME: window.system,
      searchComment: undefined // 评论过滤日期
    };
  },
  watch: {
    dataItem: {
      handler(val) {
        if (val.hasOwnProperty("indexId")) {
          this.show(val);
        } else {
          // 重置数据
          this.cardItem = {};
          this.commentValue = "";
          this.commentList = [];
          this.reportSettingList = [];
          this.indexContactUser = [];
          this.cardInfo = {
            description: "",
            func: "",
            logic: "",
            frequency: ""
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    currentReportSetting() {
      const arr = this.reportSettingList.filter(item => {
        return this.cardItem.companyName === "集团"
          ? item.baseName === "整体"
          : item.baseName === this.cardItem.baseName;
      });
      if (arr.length === 1) {
        return arr[0];
      } else {
        return "";
      }
    },
    // 当前登陆用户的账号
    nowLoginUserAccount() {
      return window.vm.$store
        ? window.vm.$store.state.user.info?.loginName
        : "";
    },
    // 原因分析下拉框
    commentSearchList() {
      return Array.from(
        new Set(
          this.commentList
            .filter(item => item.indexDt)
            .map(item => `${item.indexDt}${item.frequency || ""}`)
        )
      );
    },
    // 筛选后的原因分析列表
    commentListInSearch() {
      return this.searchComment
        ? this.commentList.filter(item => {
            return (
              `${item.indexDt ? item.indexDt : ""}${item.frequency || ""}` ===
              this.searchComment
            );
          })
        : this.commentList;
    }
  },
  methods: {
    // bdbName 被督办人的ladp账号
    dubanClick(bdbName) {
      if (
        (window.vm.$store
          ? window.vm.$store.state.user.info.loginName
          : "yuyongjie.ex"
        ).endsWith(".ex")
      ) {
        // 督办人ladp账号后缀.ex的不能新建
        return;
      }
      this.$refs["dubanModal"].show(this.cardItem, bdbName);
    },
    // 获取信鸿token
    getHichatAccessToken() {
      request(`${adminUserUrlPrefix["lxp"]}/hichatx/getAccessToken`)
        .then(res => {
          window.vm.$store.commit("rootSave", {
            hichat_AccessToken: res.accessToken
          });
        })
        .catch(() => {});
    },
    /**
     * @description: 打开抽屉
     * @param {Object} item 指标详情
     */
    show(item) {
      this.cardItem = item;
      this.getComment();
      item.id && this.getCardReportList(item.id);
      this.getCardInfo(item?.indexCode);
      this.pageClass === "indexGeneralViewPage" && this.getCardWarnInfo(item);
      const hichat_AccessToken = window.vm.$store
        ? window.vm.$store.state.hichat_AccessToken
        : "";
      // 如果没有信鸿Token则获取
      if (!hichat_AccessToken) {
        this.getHichatAccessToken();
      }
      this.visible = true;
    },
    // 编辑某条评论
    editComment(index) {
      const commentList = cloneDeep(this.commentList);
      commentList[index].isEdit = true;
      this.$set(this, "commentList", commentList);
    },
    // 提交修改
    confirmUpdate(item, index) {
      request(`${adminUserUrlPrefix["lxp"]}/indexComment/update`, {
        method: "PUT",
        body: {
          id: item.id,
          commentContent: item.commentContent,
          menu_name: `${this.cardItem.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
          indexId: `${this.cardItem.indexId}==${this.cardItem.baseName}`
        }
      }).then(() => {
        const commentList = cloneDeep(this.commentList);
        commentList[index].isEdit = false;
        this.$set(this, "commentList", commentList);
        this.getComment();
      });
    },
    // 删除评论
    deleteComment(item) {
      request(`${adminUserUrlPrefix["lxp"]}/indexComment/remove`, {
        method: "DELETE",
        body: {
          ids: [item.id],
          indexId: `${this.cardItem.indexId}==${this.cardItem.baseName}`,
          menu_name: `${this.cardItem.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`
        }
      }).then(() => {
        this.getComment();
      });
    },
    // 获取卡片指标定义
    getCardInfo(indexCode) {
      request(`${adminUserUrlPrefix["lxp"]}/indexInfo/getIndexDetailByCode`, {
        method: "POST",
        body: {
          indexCode,
          indexName: this.cardItem.indexName,
          menu_name: `${this.cardItem.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
          base: this.cardItem.baseName
        }
      }).then(res => {
        if (res && res.detail) {
          this.cardInfo["func"] = res.detail["func"]
            ? res.detail["func"].replaceAll("\n", "<br />")
            : "";
          this.cardInfo["description"] = res.detail["description"]
            ? res.detail["description"].replaceAll("\n", "<br />")
            : "";
          this.cardInfo["logic"] = res.detail["logic"]
            ? res.detail["logic"].replaceAll("\n", "<br />")
            : "";
          this.cardInfo["frequency"] = res.detail["frequency"];
        }
        if (Array.isArray(res.user)) {
          res.user.forEach((item, index) => {
            item["dubanListStatus"] = "noTask";
            this.getDuBanList(item.loginName, index);
          });
          this.indexContactUser = res.user;
        }
      });
    },
    // 获取督办个人任务列表
    getDuBanList(userName, index) {
      getDuBanUserByName(userName, res => {
        if (res && Array.isArray(res) && res.length > 0) {
          request(`/api/smc/duban/search`, {
            methods: "GET",
            params: {
              userCode: dubanConfig.adminUserCode,
              format: "0",
              pcOrMobileFlag: "pc",
              range: "all",
              code: "",
              type: dubanConfig.defaultParams.type,
              content: "",
              source: `${this.cardItem.companyName}-智造云图`,
              supervisor: "",
              undertaker: res[0].userCode,
              keyWords: `${this.cardItem.baseName}-${this.cardItem.indexName}`,
              status: "",
              publishDate: "",
              page: 1,
              size: 99999
            }
          }).then(res => {
            let dubanListStatus = "noTask";
            let completeNum = 0;
            if (res.list.length > 0) {
              res.list.forEach(item => {
                const { requireDate, progress, requireFirstDate } = item;
                if (progress === 100) {
                  completeNum++;
                } else {
                  const nowTime = new Date(
                    `${new Date().getFullYear()}-${new Date().getMonth() +
                      1}-${new Date().getDate()} 23:59:59`
                  ).getTime();
                  if (requireDate) {
                    if (requireDate < nowTime) {
                      dubanListStatus = "processing";
                    } else {
                      if (dubanListStatus !== "processing") {
                        dubanListStatus = "exceed";
                      }
                    }
                  } else {
                    if (requireFirstDate < nowTime) {
                      dubanListStatus = "exceed";
                    } else {
                      if (dubanListStatus !== "exceed") {
                        dubanListStatus = "processing";
                      }
                    }
                  }
                }
              });
            }
            if (completeNum === res.list.length) {
              dubanListStatus = "complete";
            }
            this.$set(
              this.indexContactUser[index],
              "taskStatus",
              dubanListStatus
            );
          });
        }
      });
    },
    // 获取卡片报警信息
    getCardWarnInfo(item) {
      request(`${adminUserUrlPrefix["lxp"]}/indexWarnUser/getIndexWarnFlag`, {
        method: "POST",
        body: {
          indexDate: item.indexDt,
          indexFrequency: item.dataFrequency,
          indexId: `${item.indexId}==${item.baseName}`
        }
      }).then(res => {
        if (res && res.hasOwnProperty("warnFlag")) {
          this.warnFlag = res.warnFlag;
        }
      });
    },
    // 预警开关保存
    switchChange(checked) {
      this.warnFlagLoading = true;
      const item = this.cardItem;
      request(
        `${adminUserUrlPrefix["lxp"]}/indexWarnUser/${
          checked ? "save" : "remove"
        }`,
        {
          method: checked ? "POST" : "DELETE",
          body: {
            indexDate: item.indexDt,
            indexFrequency: item.dataFrequency,
            indexId: `${item.indexId}==${item.baseName}`
          }
        }
      )
        .then(() => {
          this.warnFlagLoading = false;
          this.warnFlag = checked ? "1" : "0";
        })
        .catch(() => {
          this.warnFlagLoading = false;
        });
    },
    // 信鸿聊天
    chat(item) {
      // 日志埋点
      request(`${adminUserUrlPrefix["zcx"]}/SysAnalyseInfo/getXinHongInfo`, {
        method: "POST",
        body: {
          indexId: `${this.cardItem.indexId}==${this.cardItem.baseName}`,
          menu_name: `${this.cardItem.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
          communicatePeople: item.userName
        }
      });
      const data = encodeURIComponent(`{array:['${item.loginName}']}`);
      axios({
        url: `https://hichatx.hisense.com/gateway/openimport/open/person/getInfoByJobNo?accessToken=${window.vm.$store.state.hichat_AccessToken}`,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        },
        method: "post",
        data: `eid=101&data=${data}`
      }).then(res => {
        if (res.data?.success) {
          const defaultMessage = `${this.cardItem.plateName}-${
            this.cardItem.indexName
          }（${this.cardItem.companyName}-${this.cardItem.baseName}-${
            this.cardItem.dwppCmTfIndexLibrary.indexDt
          }${this.cardItem.dataFrequency}）
目标值：${this.cardItem.realTargetValue} ${this.cardItem.unit}；实际值：${
            this.cardItem.realBaseActual
          } ${this.cardItem.unit}；完成率：${
            this.cardItem.dwppCmTfIndexLibrary.completionRate
              ? Decimal(this.cardItem.dwppCmTfIndexLibrary.completionRate)
                  .mul(Decimal(100))
                  .toFixed(2, Decimal.ROUND_HALF_UP)
              : "-"
          }%；
${
  this.cardItem.isContemRate === "Y"
    ? "同比" +
      (this.cardItem.dwppCmTfIndexLibrary.contemRate
        ? (this.cardItem.dwppCmTfIndexLibrary.contemRate.includes("-") &&
            this.cardItem.dwppCmTfIndexLibrary.indexType === "反向") ||
          (!this.cardItem.dwppCmTfIndexLibrary.contemRate.includes("-") &&
            this.cardItem.dwppCmTfIndexLibrary.indexType === "正向")
          ? "上涨"
          : "下降"
        : "") +
      "：" +
      (this.cardItem.dwppCmTfIndexLibrary.contemRate
        ? Math.abs(
            Decimal(this.cardItem.dwppCmTfIndexLibrary.contemRate)
              .mul(Decimal(100))
              .toFixed(2, Decimal.ROUND_HALF_UP)
          )
        : "-") +
      "%；"
    : ""
} ${
            this.cardItem.isPreviousRate === "Y"
              ? "环比" +
                (this.cardItem.dwppCmTfIndexLibrary.previousRate
                  ? (this.cardItem.dwppCmTfIndexLibrary.previousRate.includes(
                      "-"
                    ) &&
                      this.cardItem.dwppCmTfIndexLibrary.indexType ===
                        "反向") ||
                    (!this.cardItem.dwppCmTfIndexLibrary.previousRate.includes(
                      "-"
                    ) &&
                      this.cardItem.dwppCmTfIndexLibrary.indexType === "正向")
                    ? "上涨"
                    : "下降"
                  : "") +
                "：" +
                (this.cardItem.dwppCmTfIndexLibrary.previousRate
                  ? Math.abs(
                      Decimal(this.cardItem.dwppCmTfIndexLibrary.previousRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)
                    )
                  : "-") +
                "%；"
              : ""
          }`;
          if (window.self !== window.top) {
            window.parent.postMessage(
              {
                sourceType: "smc",
                msgType: "hiChat",
                msgContent: JSON.stringify({
                  openId: res.data.data[0],
                  defaultMessage
                })
              },
              "*"
            );
            console.log("智造云图指标一键沟通 postMassage Log----------->", {
              sourceType: "smc",
              msgType: "hiChat",
              msgContent: JSON.stringify({
                openId: res.data.data[0],
                defaultMessage
              })
            });
          } else {
            this.openHiChatWindow({
              openId: res.data.data[0],
              defaultMessage
            });
          }
        }
      });
    },
    // 初始化信鸿api
    initQing(msgContent) {
      const script = document.createElement("script");
      script.src = "https://hichatx.hisense.com/public/js/qing/latest/qing.js";
      script.onload = () => {
        window.qing.use("desktop-remote", {
          timeout: 30000, // 选填，默认10000毫秒
          saas: true // 必填 云之家是否为私有部署
        });
        setTimeout(() => {
          this.openHiChatWindow(msgContent);
        }, 1000); // 要延时一秒执行，否则信鸿无响应
      };
      document.body.appendChild(script);
    },
    openHiChatWindow(msgContent) {
      if (!window.qing) {
        this.initQing(msgContent);
        return;
      }
      // 打开聊天窗口
      window.qing.call("chat", {
        openId: msgContent.openId,
        draft: msgContent.defaultMessage
      });
    },
    // 根据卡片ID获取当前卡片上绑定的报表信息
    getCardReportList(id) {
      request(
        `${adminUserUrlPrefix["lxp"]}/indexCardUrl/list?cardId=${id}`
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          this.reportSettingList = res;
        }
      });
    },
    // 关闭抽屉
    close() {
      this.cardItem = {};
      this.commentValue = "";
      this.commentList = [];
      this.searchComment = undefined;
      this.commentLoading = true;
      this.visible = false;
      this.warnFlag = "0";
      this.reportSettingList = [];
      this.cardInfo = {
        description: "",
        func: "",
        logic: "",
        frequency: ""
      };
      this.indexContactUser = [];
      this.$emit("close");
    },
    // 下拉框搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 原因分析列表
    getComment() {
      request(
        `${adminUserUrlPrefix["lxp"]}/indexComment/list?pageNum=1&pageSize=99999`,
        {
          method: "POST",
          body: {
            indexId: `${this.cardItem.indexId}==${this.cardItem.baseName}`
          }
        }
      ).then(res => {
        this.commentLoading = false;
        if (res && res.hasOwnProperty("rows") && Array.isArray(res.rows)) {
          this.commentList = res.rows;
          this.commentList.forEach(item => {
            item["isEdit"] = false;
          });
        }
      });
    },
    // 发布原因分析
    publishComment() {
      request(`${adminUserUrlPrefix["lxp"]}/indexComment/save`, {
        method: "POST",
        body: {
          commentContent: this.commentValue,
          frequency: this.frequency,
          indexDt: this.indexDt,
          indexId: `${this.cardItem.indexId}==${this.cardItem.baseName}`,
          menu_name: `${this.cardItem.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`
        }
      }).then(() => {
        this.getComment();
        this.commentValue = "";
      });
    },
    // 发送邮件
    sendMail(user) {
      this.$refs["mail"].show({ ...this.cardItem, email: user.email });
    },
    // 打开报表
    toReport() {
      openReport(this.cardItem, this.currentReportSetting).then(() => {
        this.close();
      });
    }
  }
};
</script>
<style lang="less">
.CardDetailInfoBox {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  height: 100%;
  display: flex;
  flex-direction: column;
  .drawerTitle {
    position: relative;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  ._flex {
    display: flex;
    align-items: center;
  }
  ._top {
    justify-content: space-between;
    margin-bottom: 32px;
    & > span {
      font-size: 28px;
      font-weight: 500;
    }
  }
  ._indexIntro,
  ._responsible,
  ._comment {
    & > ._title {
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 7px;
      ._l {
        & > span:first-child {
          font-weight: 600;
        }
      }
    }
    .list {
      .item {
        padding: 15px 0;
        border-bottom: 1px solid #e8e8e8;
        .top {
          margin-bottom: 8px;
          justify-content: space-between;
          .avatar {
            display: block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
          }
          .name {
            margin-right: 16px;
          }
          .date {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.35);
          }
          .intro {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.35);
          }
        }
        .content {
          line-height: 22px;
          padding-right: 50px;
          box-sizing: border-box;
          position: relative;
          &.canOp:hover {
            ._op {
              display: flex;
            }
          }
          ._op {
            display: none;
            align-items: center;
            position: absolute;
            top: 5px;
            right: 0;
            color: rgba(0, 0, 0, 0.35);
            .anticon {
              cursor: pointer;
            }
            .anticon-edit,
            .anticon-check {
              margin-right: 10px;
            }
            &.isEdit {
              display: flex !important;
            }
          }
        }
      }
    }
  }
  ._indexIntro {
    margin-bottom: 32px;
    ._info {
      height: 72px;
      margin-top: -8px;
      overflow: hidden;
      position: relative;
      &:not(.showall)::after {
        content: "...";
        background-color: #fff;
        display: block;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        right: 10px;
        bottom: 0;
        position: absolute;
        z-index: 22;
      }
      &:not(.showall)::before {
        content: "";
        display: block;
        width: 100%;
        height: 30px;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 11;
        background: linear-gradient(rgba(0, 0, 0, 0), #fff);
      }
      &.showall {
        height: auto;
      }
      .item {
        align-items: flex-start;
        span {
          display: block;
          color: rgba(0, 0, 0, 0.65);
          line-height: 22px;
        }
        margin-top: 8px;
        ._title {
          height: 22px;
          width: 75px;
          font-weight: 600;
        }
        ._txt {
          flex: 1;
        }
      }
    }
    .expand {
      justify-content: center;
      color: #00aaa6;
      line-height: 24px;
      span {
        cursor: pointer;
        margin-right: 5px;
      }
      .anticon {
        font-size: 12px;
      }
    }
  }
  ._responsible {
    margin-bottom: 32px;
    ._list {
      justify-content: space-between;
      & > .item {
        flex: 1;
        max-width: 50%;
        &:nth-child(2n-1) {
          margin-right: 16px;
        }
        background: #f4f5f7;
        border-radius: 8px;
        padding: 16px;
        flex-direction: column;
        & > div {
          width: 100%;
          align-items: center;
          &:first-child {
            justify-content: space-between;
            margin-bottom: 8px;
          }
        }
        // justify-content: space-between;
        .depart {
          color: rgba(0, 0, 0, 0.85);
          font-weight: 600;
          width: 126px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .name {
          color: rgba(0, 0, 0, 0.5);
        }
        ._contact {
          .contactType {
            width: 32px;
            height: 32px;
            background: #e7e9ee;
            border-radius: 50%;
            cursor: pointer;
            &:not(:last-child) {
              margin-right: 8px;
            }
            transition: background ease-in-out 0.3s;
            &:hover {
              background: #fff;
            }
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 20px;
              height: 20px;
              display: block;
            }
          }
        }
      }
    }
  }
  ._comment {
    & > ._title {
      margin-bottom: 6px;
      span:last-child {
        color: rgba(0, 0, 0, 0.35);
        margin-left: 8px;
      }
    }
  }
  .comment-box {
    background-color: #fff;
    width: 100%;
    box-sizing: border-box;
    padding: 13px 24px;
    justify-content: space-between;
    box-shadow: 0 -1px 12px 0 rgba(0, 0, 0, 0.1);
    .comment-input {
      flex: 1;
      margin-right: 16px;
    }
  }
}
</style>
