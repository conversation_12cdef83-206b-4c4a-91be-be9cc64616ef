<template>
  <div class="CardDetailInfoBox2" :class="[skinStyle()]">
    <div class="drawerTitle">
      <span style="margin-right: 20px;">指标介绍</span>
      <div
        style="font-size: 14px;color: rgb(134, 144, 156);display: flex;align-items: center;"
      >
        预警
        <a-switch
          v-if="pageClass === 'indexGeneralViewPage2'"
          :checked="warnFlag"
          checked-children="ON"
          un-checked-children="OFF"
          @change="switchChange"
          :loading="warnFlagLoading"
          style="margin-left: 8px;margin-right: 24px;"
        />
        <!-- 指标订阅
        <a-switch
          v-if="pageClass === 'indexGeneralViewPage2'"
          :checked="isSubscribe"
          checked-children="ON"
          un-checked-children="OFF"
          @change="subscribeChange"
          :loading="subscribeLoading"
          style="margin-left: 8px;"
        /> -->
      </div>
    </div>
    <div class="__center">
      <!-- 顶部按钮 -->
      <div class="_top _flex" style="padding-top: 20px;">
        <span>{{
          (cardItem.wdInCardName ? cardItem.wdInCardName + " - " : "") +
            cardItem.displayIndexName
        }}</span>
        <a-button
          type="primary"
          v-if="currentReportSetting.length"
          @click="toReport"
        >
          <template v-if="currentReportSetting.length === 1">
            前往指标详情{{
              currentReportSetting[0].reportDeveloper
                ? "(报表开发人员：" +
                  currentReportSetting[0].reportDeveloper +
                  ")"
                : ""
            }}
          </template>
          <template v-else>
            查看指标详情
          </template>
          <a-icon type="right" style="font-size: 12px;" />
        </a-button>
      </div>
      <!-- 指标说明 -->
      <div class="_indexIntro">
        <div class="_title">
          <span>指标说明</span>
        </div>
        <div class="_info" :class="[showAll ? 'showall' : '']">
          <div class="item _flex">
            <span class="_title">指标定义：</span>
            <span class="_txt" v-html="cardInfo.description"></span>
          </div>
          <div class="item _flex">
            <span class="_title">计算公式：</span>
            <span class="_txt" v-html="cardInfo.func"></span>
          </div>
          <!-- <div class="item _flex">
          <span class="_title">完成率：</span>
          <span class="_txt">实际值/目标值*100%</span>
        </div> -->
          <div class="item _flex">
            <span class="_title">数据来源：</span>
            <span class="_txt">
              <a-table
                size="small"
                :bordered="true"
                :pagination="false"
                :columns="sjlyColumns"
                :data-source="cardInfo.dataSource"
              />
            </span>
          </div>
          <div class="item _flex">
            <span class="_title">更新频率：</span>
            <span class="_txt" v-html="cardInfo.frequency"></span>
          </div>
        </div>
        <!-- 展开/折叠 -->
        <div class="expand _flex">
          <span @click="showAll = !showAll">{{
            showAll ? "折叠收起" : "展开全部"
          }}</span>
          <a-icon :type="showAll ? 'up' : 'down'" />
        </div>
      </div>
      <!-- 联系责任人 -->
      <div class="_responsible">
        <div class="_title">
          <span>联系责任人</span>
        </div>
        <div class="_list _flex">
          <div
            class="item _flex"
            v-for="(item, index) in indexContactUser"
            :key="index"
          >
            <div class="_flex _top">
              <!-- 部门 -->
              <a-tooltip placement="top">
                <template slot="title">
                  <div>{{ item.ldapFullPath }}</div>
                </template>
                <div class="depart">{{ item.ldapFullPath }}</div>
              </a-tooltip>
              <!-- 联系 -->
              <div class="_contact _flex">
                <!-- 信鸿 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>联系信鸿</span>
                  </template>
                  <div class="contactType xinhong" @click="chat(item)">
                    <template v-if="skinStyle().includes('classic-style')">
                      <img
                        :src="require('@/assets/images/icon-xinhong.png')"
                        alt=""
                        srcset=""
                      />
                    </template>
                    <template v-else>
                      <img
                        :src="require('@/assets/images/<EMAIL>')"
                        alt=""
                        srcset=""
                      />
                    </template>
                  </div>
                </a-tooltip>
                <!-- 邮件 -->
                <a-tooltip placement="top">
                  <template slot="title">
                    <span>发送邮件</span>
                  </template>
                  <div class="contactType email" @click="sendMail(item)">
                    <template v-if="skinStyle().includes('classic-style')">
                      <img
                        :src="require('@/assets/images/icon-email.png')"
                        alt=""
                        srcset=""
                      />
                    </template>
                    <template v-else>
                      <img
                        :src="require('@/assets/images/<EMAIL>')"
                        alt=""
                        srcset=""
                      />
                    </template>
                  </div>
                </a-tooltip>
                <!-- 督办 -->
                <!-- <a-tooltip placement="top">
                  <template slot="title">
                    <span>闭环</span>
                  </template>
                  <div
                    class="contactType duban"
                    :style="{
                      background: ['noTask', 'complete', undefined].includes(
                        item.taskStatus
                      )
                        ? '#e7e9ee'
                        : item.taskStatus === 'processing'
                        ? '#52c41a'
                        : '#f5222d',
                    }"
                    @click="dubanClick(item.loginName)"
                  >
                    <span
                      style="font-size: 12px;"
                      :style="{
                        color: ['noTask', 'complete', undefined].includes(
                          item.taskStatus
                        )
                          ? 'rgba(0, 0, 0, 0.65)'
                          : '#fff',
                      }"
                      >闭环</span
                    >
                  </div>
                </a-tooltip> -->
              </div>
            </div>

            <div class="_flex _bottom">
              <!-- 职位 -->
              <a-tag class="zw-tag" style="margin-right: 8px;">
                {{ item.password }}
              </a-tag>
              <!-- 联系人 -->
              <div class="name">{{ item.userName }} {{ item.phonenumber }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 原因分析 -->
      <div class="_comment">
        <div class="_title">
          <div class="_l">
            <span>原因分析</span>
            <span>{{ commentListInSearch.length }}</span>
          </div>
          <a-select
            show-search
            :value="searchComment"
            placeholder="请选择日期"
            style="width: 200px"
            :filter-option="filterOption"
            @change="(value) => (searchComment = value)"
            :allowClear="true"
          >
            <a-select-option v-for="d in commentSearchList" :key="d">
              {{ d }}
            </a-select-option>
          </a-select>
        </div>
        <!-- 列表 -->
        <a-skeleton avatar :paragraph="{ rows: 2 }" :loading="commentLoading">
          <div
            class="list"
            v-for="(item, index) in commentListInSearch"
            :key="index"
          >
            <div class="item">
              <!-- 用户信息及时间 -->
              <div class="top _flex">
                <!-- <img
                :src="require('@/assets/images/icon-duban.png')"
                alt=""
                class="avatar"
              /> -->
                <div>
                  <span class="name">{{ item.commentPeopleName }}</span>
                  <span class="date">{{ item.createdDate }}</span>
                </div>
                <span class="intro">{{
                  `${cardItem.org}-${cardItem.displayIndexName}${
                    item.indexDt ? "-" + item.indexDt : ""
                  }${item.frequency || ""}`
                }}</span>
              </div>
              <!-- 原因分析内容 -->
              <div
                class="content canOp"
                :class="[nowLoginUserAccount === item.createdBy ? 'canOp' : '']"
              >
                <span v-if="item.replyHtml" v-html="item.replyHtml"></span>
                <a-textarea
                  class="textarea"
                  v-if="item.isEdit"
                  v-model="item.commentContent"
                  :auto-size="{ minRows: 3, maxRows: 5 }"
                />
                <span v-if="!item.isEdit">
                  {{ item.commentContent }}
                </span>
                <span class="_op" :class="item.isEdit ? 'isEdit' : ''">
                  <a-icon
                    title="回复"
                    type="form"
                    @click="replyComment(index)"
                  />
                  <template v-if="nowLoginUserAccount === item.createdBy">
                    <!-- 修改 -->
                    <a-icon
                      title="修改"
                      type="edit"
                      v-if="!item.isEdit"
                      @click="editComment(index)"
                    />
                    <!-- 提交 -->
                    <a-icon
                      title="提交"
                      v-if="item.isEdit"
                      type="check"
                      @click="confirmUpdate(item, index)"
                    />
                    <!-- 删除 -->
                    <a-icon
                      title="删除"
                      type="delete"
                      @click="deleteComment(item)"
                    />
                  </template>
                </span>
              </div>
            </div>
          </div>
        </a-skeleton>
        <a-empty
          description="暂无原因分析"
          style="margin-top: 30px;"
          v-if="commentList.length === 0 && !commentLoading"
        />
      </div>
    </div>
    <!-- 原因分析输入框等 -->
    <div class="comment-box _flex">
      <span v-if="beRepliedComment" style="margin-right: 10px;">
        回复
        <span style="color: #00aaa6;margin-left: 3px;">{{
          beRepliedComment.commentPeopleName
        }}</span>
        :
      </span>
      <a-input
        v-model="commentValue"
        placeholder="请输入你想要发表的原因分析内容"
        class="comment-input"
      />
      <a-button
        type="primary"
        @click="publishComment"
        :disabled="!commentValue"
      >
        发布
      </a-button>
    </div>
    <Mail ref="mail" :pageClass="pageClass" />
    <DuBanModal ref="dubanModal" />
    <ReportsChooseModalVue ref="reportsChooseModalVue" />
  </div>
</template>
<script>
import Mail from "./mail.vue";
import request from "@/utils/requestHttp";
import { dubanConfig, getDuBanUserByName } from "@/utils/utils";
import axios from "axios";
import cloneDeep from "lodash/cloneDeep";
import DuBanModal from "./duBanModal.vue";
import { getCardReportList, openReport } from "../../utils";
import Decimal from "decimal.js";
import ReportsChooseModalVue from "../reportsChooseModal.vue";
export default {
  name: "IndexCardDetailInfo",
  inject: ["skinStyle"],
  components: { Mail, DuBanModal, ReportsChooseModalVue },
  props: {
    pageClass: String,
    dataItem: Object,
    companyName: String,
    signOrgId: String,
  },
  data() {
    return {
      isSubscribe: true,
      showAll: false, // 展开/折叠
      cardItem: {},
      commentValue: "",
      commentLoading: true,
      commentList: [], // 原因分析列表
      currentReportSetting: [],
      warnFlag: false, // 是否预警
      warnFlagLoading: false,
      subscribeLoading: false,
      cardInfo: {
        description: "",
        func: "",
        logic: "",
        dataSource: [],
        frequency: "",
      }, // 卡片指标定义
      indexContactUser: [], // 指标负责人
      SYS_NAME: window.system,
      searchComment: undefined, // 评论过滤日期
      sjlyColumns: [
        {
          title: "分子分母",
          dataIndex: "numeratorOrDenominator",
          key: "numeratorOrDenominator",
          width: 100,
        },
        {
          title: "数据名称",
          dataIndex: "dataName",
          key: "dataName",
          width: 100,
        },
        {
          title: "数据来源系统",
          dataIndex: "dataSource",
          key: "dataSource",
          width: 120,
        },
        {
          title: "数据口径",
          dataIndex: "dataCaliber",
          key: "dataCaliber",
        },
      ],
    };
  },
  watch: {
    dataItem: {
      handler(val) {
        // eslint-disable-next-line no-prototype-builtins
        if (val.hasOwnProperty("indexId")) {
          this.show(val);
        } else {
          // 重置数据
          this.cardItem = {};
          this.commentValue = "";
          this.commentList = [];
          this.currentReportSetting = [];
          this.indexContactUser = [];
          this.cardInfo = {
            description: "",
            func: "",
            logic: "",
            dataSource: [],
            frequency: "",
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    // 当前登陆用户的账号
    nowLoginUserAccount() {
      return window.vm.$store
        ? window.vm.$store.state.user.info?.loginName
        : "yuyongjie.ex";
    },
    // 原因分析下拉框
    commentSearchList() {
      return Array.from(
        new Set(
          this.commentList
            .filter((item) => item.indexDt)
            .map((item) => `${item.indexDt}${item.frequency || ""}`)
        )
      );
    },
    // 筛选后的原因分析列表
    commentListInSearch() {
      return this.searchComment
        ? this.commentList.filter((item) => {
            return (
              `${item.indexDt ? item.indexDt : ""}${item.frequency || ""}` ===
              this.searchComment
            );
          })
        : this.commentList;
    },
    // 被回复评论
    beRepliedComment() {
      return this.commentList.filter((item) => item.beReplied)[0];
    },
  },
  methods: {
    // bdbName 被督办人的ladp账号
    dubanClick(bdbName) {
      if (
        (window.vm.$store
          ? window.vm.$store.state.user.info.loginName
          : "yuyongjie.ex"
        ).endsWith(".ex")
      ) {
        // 督办人ladp账号后缀.ex的不能新建
        return;
      }
      this.$refs["dubanModal"].show(this.cardItem, bdbName);
    },
    // 获取信鸿token
    getHichatAccessToken() {
      request(`/api/smc/hichatx/getAccessToken`)
        .then((res) => {
          window.vm.$store.commit("rootSave", {
            hichat_AccessToken: res.accessToken,
          });
        })
        .catch(() => {});
    },
    /**
     * @description: 打开抽屉
     * @param {Object} item 指标详情
     */
    show(item) {
      this.cardItem = item;
      // 获取指标分析
      this.getComment();
      // 获取当前指标报表链接
      this.getCardReportList();
      this.getCardInfo(item.indexId);
      const hichat_AccessToken = window.vm.$store
        ? window.vm.$store.state.hichat_AccessToken
        : "";
      // 如果没有信鸿Token则获取
      if (!hichat_AccessToken) {
        this.getHichatAccessToken();
      }
      this.warnUserSearch(); // 预警查询
      this.visible = true;
    },
    // 查询预警
    warnUserSearch() {
      this.warnFlagLoading = true;
      request(`/api/smc2/warnUser/search?cmimId=${this.cardItem.cmimId}`).then(
        (res) => {
          if (res && res.result) {
            this.warnFlag = res.result;
          }
          this.warnFlagLoading = false;
        }
      );
    },
    // 原因分析列表
    getComment() {
      request(`/api/smc/indexComment/list?pageNum=1&pageSize=99999`, {
        method: "POST",
        body: {
          indexId: `${this.cardItem.cmimId}`,
        },
      }).then((res) => {
        this.commentLoading = false;
        // eslint-disable-next-line no-prototype-builtins
        if (res && res.hasOwnProperty("rows") && Array.isArray(res.rows)) {
          res.rows.forEach((item) => {
            item["replyHtml"] = "";
            if (item.commentContent.includes("~&~")) {
              const arr = item.commentContent.split("~&~");
              item["replyHtml"] = arr[0];
              item.commentContent = arr[1];
            }
            item["isEdit"] = false;
            item["beReplied"] = false;
          });
          this.commentList = res.rows;
        }
      });
    },
    // 发布原因分析
    publishComment() {
      const commentContent = this.beRepliedComment
        ? `<span class="reply-span">回复<span>${this.beRepliedComment.commentPeopleName}</span>:</span>~&~${this.commentValue}`
        : this.commentValue;
      request(`/api/smc/indexComment/save`, {
        method: "POST",
        body: {
          commentContent,
          org: this.cardItem.org,
          indexDt: this.cardItem.indexDt,
          frequency: this.cardItem.indexFrequency,
          indexId: `${this.cardItem.cmimId}`,
          menu_name: `${this.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
        },
      }).then(() => {
        this.getComment();
        this.commentValue = "";
      });
    },
    // 编辑某条评论
    editComment(index) {
      const commentList = cloneDeep(this.commentList);
      commentList.forEach((item) => {
        item.isEdit = false;
      });
      commentList[index].isEdit = true;
      this.$set(this, "commentList", commentList);
    },
    // 评论某条评论
    replyComment(index) {
      const commentList = cloneDeep(this.commentList);
      commentList.forEach((item) => {
        item.beReplied = false;
      });
      commentList[index].beReplied = true;
      this.$set(this, "commentList", commentList);
    },
    // 提交修改
    confirmUpdate(item, index) {
      const commentContent = item.replyHtml
        ? `${item.replyHtml}~&~${item.commentContent}`
        : item.commentContent;
      request(`/api/smc/indexComment/update`, {
        method: "PUT",
        body: {
          id: item.id,
          commentContent,
          menu_name: `${this.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
          indexId: `${this.cardItem.cmimId}`,
        },
      }).then(() => {
        const commentList = cloneDeep(this.commentList);
        commentList[index].isEdit = false;
        this.$set(this, "commentList", commentList);
        this.getComment();
      });
    },
    // 删除评论
    deleteComment(item) {
      request(`/api/smc/indexComment/remove`, {
        method: "DELETE",
        body: {
          ids: [item.id],
          indexId: `${this.cardItem.cmimId}`,
          menu_name: `${this.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
        },
      }).then(() => {
        this.getComment();
      });
    },
    // 获取卡片指标定义
    getCardInfo(indexCode) {
      request(`/api/smc/indexInfo/getIndexDetailByCode`, {
        method: "POST",
        body: {
          indexCode,
          indexName: this.cardItem.indexName,
          menu_name: `${this.companyName}核心KPI${
            this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
          }${this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""}`,
          version: "2.0",
          signOrgId:
            this.signOrgId === "H" ? this.cardItem.signOrgId : this.signOrgId,
          businessSegmentsId: this.cardItem.businessSegmentsId,
          orgId: this.cardItem.orgId,
          fullCode: this.cardItem.fullCode,
          cmimId: this.cardItem.cmimId,
        },
      }).then((res) => {
        if (res && res.detail) {
          this.cardInfo["func"] = res.detail["func"]
            ? res.detail["func"].replaceAll("\n", "<br />")
            : "";
          this.cardInfo["description"] = res.detail["description"]
            ? res.detail["description"].replaceAll("\n", "<br />")
            : "";
          this.cardInfo["logic"] = res.detail["logic"]
            ? res.detail["logic"].replaceAll("\n", "<br />")
            : "";
          this.cardInfo["dataSource"] =
            Array.isArray(res.detail["dataSource"]) &&
            res.detail["dataSource"].length
              ? res.detail["dataSource"]
              : [];
          this.cardInfo["frequency"] = res.detail["frequency"];
        }
        if (Array.isArray(res.user)) {
          res.user.forEach((item, index) => {
            item["dubanListStatus"] = "noTask";
            this.getDuBanList(item.loginName, index);
          });
          this.indexContactUser = res.user;
        }
      });
    },
    // 获取督办个人任务列表
    getDuBanList(userName, index) {
      getDuBanUserByName(userName, (res) => {
        if (res && Array.isArray(res) && res.length > 0) {
          request(`/api/smc/duban/search`, {
            methods: "GET",
            params: {
              userCode: dubanConfig.adminUserCode,
              format: "0",
              pcOrMobileFlag: "pc",
              range: "all",
              code: "",
              type: dubanConfig.defaultParams.type,
              content: "",
              source: `${this.companyName}-智造云图`,
              supervisor: "",
              undertaker: res[0].userCode,
              keyWords: `${this.cardItem.org}-${this.cardItem.displayIndexName}`,
              status: "",
              publishDate: "",
              page: 1,
              size: 99999,
            },
          }).then((res) => {
            let dubanListStatus = "noTask";
            let completeNum = 0;
            if (res.list.length > 0) {
              res.list.forEach((item) => {
                const { requireDate, progress, requireFirstDate } = item;
                if (progress === 100) {
                  completeNum++;
                } else {
                  const nowTime = new Date(
                    `${new Date().getFullYear()}-${new Date().getMonth() +
                      1}-${new Date().getDate()} 23:59:59`
                  ).getTime();
                  if (requireDate) {
                    if (requireDate < nowTime) {
                      dubanListStatus = "processing";
                    } else {
                      if (dubanListStatus !== "processing") {
                        dubanListStatus = "exceed";
                      }
                    }
                  } else {
                    if (requireFirstDate < nowTime) {
                      dubanListStatus = "exceed";
                    } else {
                      if (dubanListStatus !== "exceed") {
                        dubanListStatus = "processing";
                      }
                    }
                  }
                }
              });
            }
            if (completeNum === res.list.length) {
              dubanListStatus = "complete";
            }
            this.$set(
              this.indexContactUser[index],
              "taskStatus",
              dubanListStatus
            );
          });
        }
      });
    },
    // 预警开关保存
    switchChange(checked) {
      this.warnFlagLoading = true;
      if (checked) {
        request(`/api/smc2/warnUser/insertWarnUser`, {
          method: "POST",
          body: {
            cmimId: this.cardItem.cmimId,
          },
        })
          .then(() => {
            this.warnFlagLoading = false;
            this.warnFlag = checked;
          })
          .catch(() => {
            this.warnFlagLoading = false;
          });
      } else {
        request(`/api/smc2/warnUser/delWarnUser?cmimId=${this.cardItem.cmimId}`)
          .then(() => {
            this.warnFlagLoading = false;
            this.warnFlag = checked;
          })
          .catch(() => {
            this.warnFlagLoading = false;
          });
      }
    },
    // 是否订阅开关改变
    subscribeChange(checked) {
      this.subscribeLoading = true;
      const {
        signOrgId,
        businessSegmentsId,
        indexId,
        fullCode,
      } = this.cardItem;
      const postData = {
        signOrgId,
        businessSegmentsId,
        indexId,
        fullCode,
        sign: `${this.companyName}概览`,
      };
      if (checked) {
        postData["type"] = this.pageClass === "indexGeneralView" ? "0" : "1";
      }
      request(
        `${
          checked
            ? "/api/smc2/newuserIndexRelation/insertByRecommend"
            : "/api/smc2/newuserIndexRelation/delCard"
        }`,
        {
          method: "POST",
          body: postData,
        }
      ).then((res) => {
        this.subscribeLoading = false;
        if (res && res.result === "success") {
          this.isSubscribe = checked;
          this.$emit("subscribe");
        }
      });
    },
    // 信鸿聊天
    chat(item) {
      const data = encodeURIComponent(`{array:['${item.loginName}']}`);
      axios({
        url: `https://hichatx.hisense.com/gateway/openimport/open/person/getInfoByJobNo?accessToken=${window.vm.$store.state.hichat_AccessToken}`,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        method: "post",
        data: `eid=101&data=${data}`,
      }).then((res) => {
        if (res.data?.success) {
          const defaultMessage = `${this.cardItem.businessSegments}-${
            this.cardItem.wdInCardName
              ? "(" + this.cardItem.wdInCardName + ")"
              : ""
          }${this.cardItem.displayIndexName}（${this.companyName}-${
            this.cardItem.org
          }-${this.cardItem.indexDt}${this.cardItem.indexFrequency}）
目标值：${this.cardItem.targetValue} ${this.cardItem.indexUnitId}；实际值：${
            this.cardItem.actualValue
          } ${this.cardItem.indexUnitId}；完成率：${
            this.cardItem.targetCompletionRate
              ? this.cardItem.targetCompletionRate
              : "-%"
          }；
${
  this.cardItem.isContemRate === "Y"
    ? "同比" +
      (this.cardItem.contemChangeRate
        ? (this.cardItem.contemChangeRate.includes("-") &&
            this.cardItem.indexType === "反向") ||
          (!this.cardItem.contemChangeRate.includes("-") &&
            this.cardItem.indexType === "正向")
          ? "上涨"
          : "下降"
        : "") +
      "：" +
      (this.cardItem.contemChangeRate
        ? Math.abs(
            Decimal(this.cardItem.contemChangeRate)
              .mul(Decimal(100))
              .toFixed(2, Decimal.ROUND_HALF_UP)
          )
        : "-") +
      "%；"
    : ""
} ${
            this.cardItem.isPreviousRate === "Y"
              ? "环比" +
                (this.cardItem.previousChangeRate
                  ? (this.cardItem.previousChangeRate.includes("-") &&
                      this.cardItem.indexType === "反向") ||
                    (!this.cardItem.previousChangeRate.includes("-") &&
                      this.cardItem.indexType === "正向")
                    ? "上涨"
                    : "下降"
                  : "") +
                "：" +
                (this.cardItem.previousChangeRate
                  ? Math.abs(
                      Decimal(this.cardItem.previousChangeRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)
                    )
                  : "-") +
                "%；"
              : ""
          }`;
          if (window.self !== window.top) {
            window.parent.postMessage(
              {
                sourceType: "smc",
                msgType: "hiChat",
                msgContent: JSON.stringify({
                  openId: res.data.data[0],
                  defaultMessage,
                }),
              },
              "*"
            );
          } else {
            this.openHiChatWindow({
              openId: res.data.data[0],
              defaultMessage,
            });
          }
        }
      });
    },
    // 初始化信鸿api
    initQing(msgContent) {
      const script = document.createElement("script");
      script.src = "https://hichatx.hisense.com/public/js/qing/latest/qing.js";
      script.onload = () => {
        window.qing.use("desktop-remote", {
          timeout: 30000, // 选填，默认10000毫秒
          saas: true, // 必填 云之家是否为私有部署
        });
        setTimeout(() => {
          this.openHiChatWindow(msgContent);
        }, 1000); // 要延时一秒执行，否则信鸿无响应
      };
      document.body.appendChild(script);
    },
    openHiChatWindow(msgContent) {
      if (!window.qing) {
        this.initQing(msgContent);
        return;
      }
      // 打开聊天窗口
      window.qing.call("chat", {
        openId: msgContent.openId,
        draft: msgContent.defaultMessage,
      });
    },
    // 获取当前卡片上绑定的报表信息
    getCardReportList() {
      getCardReportList(this.cardItem).then((res) => {
        const reports = res.filter((reportItem) =>
          this.cardItem.showYC
            ? reportItem.isPrediction === "1"
            : reportItem.isPrediction !== "1"
        );
        this.currentReportSetting = reports;
      });
    },
    // 关闭抽屉
    close(needEmit = true) {
      this.cardItem = {};
      this.commentValue = "";
      this.commentList = [];
      this.searchComment = undefined;
      this.commentLoading = true;
      this.visible = false;
      this.warnFlag = false;
      this.currentReportSetting = [];
      this.cardInfo = {
        description: "",
        func: "",
        logic: "",
        dataSource: [],
        frequency: "",
      };
      this.isSubscribe = true;
      this.indexContactUser = [];
      needEmit && this.$emit("close");
    },
    // 下拉框搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 发送邮件
    sendMail(user) {
      this.$refs["mail"].show({ ...this.cardItem, email: user.email });
    },
    // 打开报表
    toReport() {
      if (this.currentReportSetting.length === 1) {
        let params = "";
        params = ["cmimId", "indexDt", "indexFrequency", "signOrgId"]
          .map((filed) => `${filed}=${this.cardItem[filed]}`)
          .join("&");
        window.vm.$message.success("正在打开报表....");
        openReport(this.currentReportSetting[0].id, params).then(() => {
          this.close();
        });
      } else if (this.currentReportSetting.length >= 2) {
        window.vm.$message.info("请选择要打开的报表....");
        this.$refs["reportsChooseModalVue"].show({
          reports: this.currentReportSetting,
          cardItem: this.cardItem,
        });
        this.close();
      } else {
        window.vm.$message.warning("当前卡片没有配置报表");
      }
    },
    observeTableStyles() {
      const observer = new MutationObserver(() => {
        const tableHeaders = document.querySelectorAll(
          ".CardDetailInfoBox2 .ant-table-thead > tr"
        );
        tableHeaders.forEach((header) => {
          header.style.backgroundColor = "#00aaa6";
          const spans = header.querySelectorAll("th span");
          spans.forEach((span) => {
            span.style.color = "black";
          });
        });
      });
      // 处理表格body的margin
      const tableBody = document.querySelectorAll(
        ".CardDetailInfoBox2 .ant-table-small > .ant-table-content > .ant-table-body"
      );
      tableBody.forEach((body) => {
        body.style.margin = "0";
      });
      // 监听整个组件的 DOM 变化
      observer.observe(this.$el, {
        childList: true,
        subtree: true,
      });

      // 组件销毁时断开观察
      this.$once("hook:beforeDestroy", () => {
        observer.disconnect();
      });
    },
  },
  mounted() {
    this.observeTableStyles();
  },
  updated() {
    this.observeTableStyles();
  },
};
</script>
<style lang="less" scoped>
.CardDetailInfoBox2 {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  height: 100%;
  display: flex;
  flex-direction: column;
  .ant-table-small > .ant-table-content > .ant-table-body {
    margin: 0;
  }
  .ant-table-small {
    > .ant-table-content {
      > .ant-table-body {
        > table {
          > .ant-table-thead {
            > tr {
              background-color: #00aaa6 !important;
              th {
                span {
                  color: #fff !important;
                }
              }
            }
          }
        }
      }
    }
  }
  &.hisense-style {
    &.dark {
      background-color: #171717;
      .drawerTitle {
        background-color: #171717;
        color: #f2f2f2;
        border-bottom: 1px solid #252525;
      }
      .ant-table-small
        > .ant-table-content
        > .ant-table-body
        > table
        > .ant-table-tbody
        > tr
        > td {
        color: black !important;
      }
      .ant-table-tbody
        > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected)
        > td {
        background-color: transparent;
      }
      .__center {
        ._indexIntro,
        ._responsible,
        ._comment {
          & > ._title {
            color: #e2e8ea;
          }
        }
        ._indexIntro {
          ._info {
            &:not(.showall)::before {
              background: linear-gradient(
                rgba(0, 0, 0, 0),
                rgba(23, 23, 23, 1)
              );
            }
            &:not(.showall)::after {
              background-color: rgba(23, 23, 23, 1);
              color: #e2e8ea;
            }
            .item ._txt {
              color: #e2e8ea;
            }
          }
          .expand {
            color: #e2e8ea;
          }
        }
        ._responsible {
          ._list {
            .item {
              background-color: #222325;
              ._top {
                .depart {
                  color: #7b8189;
                }
                ._contact {
                  & > div {
                    &.duban {
                      span {
                        color: #d8d8d8 !important;
                      }
                      background-color: #353535 !important;
                    }
                  }
                }
              }
              ._bottom {
                .zw-tag {
                  background-color: #555555;
                }
                .name {
                  color: #e2e8ea;
                }
              }
            }
          }
        }
        ._comment {
          .list {
            .item {
              border-bottom: 1px solid #252525;
              .name,
              .content,
              ._op,
              .textarea {
                color: #e2e8ea;
              }
              .date,
              .intro {
                color: #7b8189;
              }
            }
          }
          .ant-empty-description {
            color: #e2e8ea;
          }
        }
      }
      .comment-box {
        background-color: #171717;
        .ant-input.comment-input {
          background-color: #565b60 !important;
          color: #e2e8ea !important;
        }
      }
    }
    .__center {
      ._top {
        margin-bottom: 24px;
        & > span {
          color: #00aaa6;
          font-size: 18px;
        }
      }
      ._indexIntro,
      ._responsible,
      ._comment {
        & > ._title {
          font-size: 16px;
          color: #4e5969;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          ._l {
            & > span:first-child {
              font-weight: 600;
            }
          }
        }
      }
      ._indexIntro {
        margin-bottom: 18px;
        display: flex;
        flex-direction: column;
        ._title {
          width: 100%;
        }
        ._info {
          .item ._title {
            font-weight: normal;
            font-size: 14px;
            color: #6b7785;
          }
          .item ._txt {
            color: #1d2129;
          }
        }
        .expand {
          color: #4e5969;
          font-size: 12px;
          padding: 0 15px;
          height: 22px;
          line-height: 22px;
          border: 1px solid #7b8189;
          border-radius: 25px;
          display: flex;
          margin: 0 auto;
          margin-top: 16px;
        }
      }
      ._responsible {
        ._list {
          align-items: flex-start;
          .item {
            flex-direction: column-reverse;
            ._top {
              margin-bottom: 0;
              display: block;
              .depart {
                width: 100%;
                text-overflow: inherit;
                white-space: inherit;
                margin-bottom: 8px;
              }
              ._contact {
                width: 100%;
                justify-content: flex-end;
                & > div {
                  background-color: transparent;
                  width: 24px;
                  height: 24px;
                  img {
                    display: block;
                    width: 100%;
                    height: 100%;
                  }
                  &.duban {
                    width: 29px;
                    height: 14px;
                    line-height: 14px;
                    font-size: 10px;
                    border-radius: 4px;
                    background-color: #e5e6eb;
                    color: #6b7785;
                  }
                }
              }
            }
            ._bottom {
              display: block;
              .zw-tag {
                background-color: #e7f8f7;
                color: #00aaa6;
                padding: 0 4px;
                height: 24px;
                line-height: 24px;
                margin-bottom: 16px;
              }
              .name {
                color: #1d2129;
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }
    .comment-box {
      .ant-input.comment-input {
        background-color: rgb(242, 243, 245) !important;
        border-color: transparent !important;
      }
    }
  }
  .drawerTitle {
    padding: 16px 24px;
    padding-right: 48px !important;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  ._flex {
    display: flex;
    align-items: center;
  }
  ._top {
    justify-content: space-between;
    margin-bottom: 32px;
    & > span {
      font-size: 28px;
      font-weight: 500;
    }
  }
  ._indexIntro,
  ._responsible,
  ._comment {
    & > ._title {
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 7px;
      ._l {
        & > span:first-child {
          font-weight: 600;
        }
      }
    }
    .list {
      .item {
        padding: 15px 0;
        border-bottom: 1px solid #e8e8e8;
        .top {
          margin-bottom: 8px;
          justify-content: space-between;
          .avatar {
            display: block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
          }
          .name {
            margin-right: 16px;
          }
          .date {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.35);
          }
          .intro {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.35);
          }
        }
        .content {
          line-height: 22px;
          padding-right: 80px;
          box-sizing: border-box;
          position: relative;
          display: flex;
          .reply-span {
            white-space: nowrap;
            color: #353535;
            margin-right: 5px;
            span {
              color: #00aaa6;
              margin: 0 3px;
            }
          }
          &.canOp:hover {
            ._op {
              .anticon-form,
              .anticon-edit,
              .anticon-delete {
                display: block;
              }
            }
          }
          ._op {
            display: flex;
            align-items: center;
            position: absolute;
            top: 5px;
            right: 0;
            color: rgba(0, 0, 0, 0.35);
            .anticon {
              cursor: pointer;
            }
            .anticon-form,
            .anticon-edit,
            .anticon-check,
            .anticon-delete {
              display: none;
              margin-right: 10px;
            }
            &.isEdit {
              display: flex !important;
              .anticon-form {
                display: none !important;
              }
              .anticon-delete,
              .anticon-check {
                display: block;
              }
            }
          }
        }
      }
    }
  }
  ._indexIntro {
    margin-bottom: 32px;
    ._info {
      height: 72px;
      margin-top: -8px;
      overflow: hidden;
      position: relative;
      &:not(.showall)::after {
        content: "...";
        background-color: #fff;
        display: block;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        right: 10px;
        bottom: 0;
        position: absolute;
        z-index: 22;
      }
      &:not(.showall)::before {
        content: "";
        display: block;
        width: 100%;
        height: 30px;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 11;
        background: linear-gradient(rgba(0, 0, 0, 0), #fff);
      }
      &.showall {
        height: auto;
      }
      .item {
        align-items: flex-start;
        span {
          display: block;
          color: rgba(0, 0, 0, 0.65);
          line-height: 22px;
        }
        margin-top: 8px;
        ._title {
          height: 22px;
          width: 75px;
          font-weight: 600;
        }
        ._txt {
          flex: 1;
        }
      }
    }
    .expand {
      justify-content: center;
      color: #00aaa6;
      line-height: 24px;
      span {
        cursor: pointer;
        margin-right: 5px;
      }
      .anticon {
        font-size: 12px;
      }
    }
  }
  ._responsible {
    margin-bottom: 32px;
    ._list {
      justify-content: space-between;
      & > .item {
        flex: 1;
        max-width: 50%;
        &:nth-child(2n-1) {
          margin-right: 16px;
        }
        background: #f4f5f7;
        border-radius: 8px;
        padding: 16px;
        flex-direction: column;
        & > div {
          width: 100%;
          align-items: center;
          &:first-child {
            justify-content: space-between;
            margin-bottom: 8px;
          }
        }
        // justify-content: space-between;
        .depart {
          color: rgba(0, 0, 0, 0.85);
          font-weight: 600;
          width: 126px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .name {
          color: rgba(0, 0, 0, 0.5);
        }
        .zw-tag {
          background-color: #00aaa6;
          border: none;
          color: #fff;
        }
        ._contact {
          .contactType {
            width: 32px;
            height: 32px;
            background: #e7e9ee;
            border-radius: 50%;
            cursor: pointer;
            &:not(:last-child) {
              margin-right: 8px;
            }
            transition: background ease-in-out 0.3s;
            &:hover {
              background: #fff;
            }
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 20px;
              height: 20px;
              display: block;
            }
          }
        }
      }
    }
  }
  ._comment {
    & > ._title {
      margin-bottom: 6px;
      span:last-child {
        color: rgba(0, 0, 0, 0.35);
        margin-left: 8px;
      }
    }
  }
  .comment-box {
    background-color: #fff;
    width: 100%;
    box-sizing: border-box;
    padding: 13px 24px;
    justify-content: space-between;
    box-shadow: 0 -1px 12px 0 rgba(0, 0, 0, 0.1);
    .comment-input {
      flex: 1;
      margin-right: 16px;
    }
  }
}
</style>
