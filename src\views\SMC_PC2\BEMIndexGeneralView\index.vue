<!--
 * @Description: 视像核心KPI概览图
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 14:26:03
 * @LastEditors: Jhin.Yu <EMAIL>
 * @LastEditTime: 2023-02-17 09:40:07
-->
<template>
  <!-- 给这个div绑定公司名称的class是为了拖拽时候找到html元素 -->
  <div
    class="indexGeneralViewPage2"
    :class="[companyName, form.skinStyle]"
    :ref="`${companyName}${signOrgId}indexGeneralViewPage2`"
  >
    <template
      v-if="
        form.skinStyle.includes('hisense-style') &&
          !form.skinStyle.includes('dark')
      "
    >
      <div class="empty-bg-div">
        <div class="div"></div>
      </div>
    </template>
    <div class="_top" ref="top-ref">
      <!-- 顶部 -->
      <PageHeader
        :companyName="companyName"
        @pageHeaderChange="searchConditionChange"
        :orgLabelName="attribute.orgLabelName"
        :orgTreeList="orgTreeList"
        :signOrgId="signOrgId"
        :list="cardList"
        ref="pageHeader"
        @reportModalChange="
          e => {
            this.$refs['plate'].setStatus(e);
          }
        "
        @indexSearchChanged="
          e => {
            this.$refs['cardList'].setFilterIndex(e);
            this.filterIndexArr = e;
          }
        "
      />
      <!-- 置顶模块 -->
      <StickyIndex
        v-if="searchForm.mode === 'chart' && topCardList.length > 0"
        :cardListLoading="cardListLoading"
        :topCardList="topCardList"
      />
      <template v-if="!form.skinStyle.includes('classic-style')">
        <div class="empty-fill-color-div"></div>
      </template>
    </div>
    <!-- 底部表格区域 -->
    <div class="_bottom table" v-show="searchForm.mode === 'table'">
      <template v-if="cardListLoading">
        <a-spin :spinning="cardListLoading">
          <div style="height: 100%"></div>
        </a-spin>
      </template>
      <template v-else>
        <div
          class="_flex"
          style="justify-content: flex-end; margin-bottom: 12px;"
        >
          <a-button type="primary" @click="download">
            数据导出
          </a-button>
        </div>
        <div class="table-area">
          <a-table
            :scroll="{ x: 1950 }"
            style="background: #fff;padding: 10px 5px;"
            :pagination="false"
            rowKey="pj"
            size="small"
            :columns="columns"
            :data-source="newTableData"
          >
          </a-table>
        </div>
      </template>
    </div>
    <!-- 底部卡片区域 -->
    <div class="_bottom" ref="bottom-ref" v-show="searchForm.mode === 'chart'">
      <CardList
        class="card-list"
        ref="cardList"
        :activePlate="activePlate"
        :list="newCardList"
        :cardListLoading="cardListLoading"
        @savedData="savedData"
        @refreshData="searchConditionChange"
        :companyName="companyName"
        :signOrgId="signOrgId"
        @changeRecommend="changeRecommend"
        @cancelDrag="getCardList"
        pageClass="indexGeneralViewPage2"
        @addToCardList="addToCardList"
        :timeTypeOptionInDict="timeTypeOptionInDict"
      />
    </div>
  </div>
</template>
<script>
import PageHeader from "./pageHeader.vue";
import StickyIndex from "./stickyIndex.vue";
import CardList from "./Card/cardList.vue";
import request from "@/utils/requestHttp";
import { pureAxios } from "@/utils/requestHttp";
import moment from "moment";
import sortBy from "lodash/sortBy";
import { dealThousandData, findNode, getDateOfISOWeek } from "../utils";
import { showAlias } from "@/utils/intl.js";
import { getUrlParam } from "@/utils/utils.js";
import Decimal from "decimal.js";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "IndexGeneralView2",
  components: { PageHeader, StickyIndex, CardList },
  props: {
    data: Object,
    // 是否设计器里
    isDesign: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  provide() {
    return {
      businessSegmentsColorMap: () => this.businessSegmentsColorMap,
      isDesign: this.isDesign,
      timeMap: this.timeMap,
      skinStyle: () => this.form.skinStyle
    };
  },
  computed: {
    // 当前组件属性props对象
    attribute() {
      if (this.data && this.data.props) {
        return this.data.props;
      } else {
        return {};
      }
    },
    // 公司名称
    companyName() {
      return this.attribute.companyName || "海信集团";
    },
    // 注册公司编码
    signOrgId() {
      return this.attribute.signOrgId || "H";
    },
    // 组合处理完的卡片列表
    newCardList() {
      // 有推荐就把推荐的第n个放到列表里进行展示
      return this.recommendList.length
        ? [this.recommendList[this.activeRecommendIndex], ...this.cardList]
        : this.cardList;
    },
    // 表格数据
    newTableData() {
      let arr = [];
      if (this.filterIndexArr.length) {
        arr = cloneDeep(this.tableData)
          .filter(item => this.filterIndexArr.includes(item.pj))
          .map((item, index) => {
            return {
              ...item,
              index: index + 1
            };
          });
      } else {
        arr = cloneDeep(this.tableData);
      }
      return arr;
    }
  },
  data() {
    return {
      systemSign: getUrlParam("systemSign"),
      businessSegmentsColorMap: {}, // 定义版块颜色
      form: {
        skinStyle: "classic-style"
      },
      orgTreeList: [], // 组织列表
      searchForm: {
        mode: "chart"
      },
      activePlate: "全部", // 当前激活的版块
      cardList: [], // 卡片列表
      topCardList: [], // 顶部置顶卡片
      recommendList: [], // 推荐列表
      cardListLoading: true, // 卡片列表Loading
      activeRecommendIndex: 0, // 激活的推荐卡片下标
      // 表格列展示
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          width: 80
        },
        {
          title: "指标名称",
          dataIndex: "displayIndexName",
          key: "displayIndexName"
        },
        {
          title: "业务版块",
          dataIndex: "businessSegments",
          key: "businessSegments",
          width: 100
        },
        {
          title: "公司",
          key: "signOrg",
          dataIndex: "signOrg",
          width: 80
        },
        {
          title: "组织",
          dataIndex: "org",
          key: "org",
          width: 120
        },
        {
          title: "实际值",
          dataIndex: "actualValue",
          key: "actualValue",
          width: 100
        },
        {
          title: "实际分子",
          dataIndex: "actualMolecule",
          key: "actualMolecule",
          width: 120
        },
        {
          title: "实际分母",
          dataIndex: "actualDenominator",
          key: "actualDenominator",
          width: 120
        },
        {
          title: "单位",
          dataIndex: "indexUnitId",
          key: "indexUnitId",
          width: 80
        },
        {
          title: "目标值",
          dataIndex: "targetValue",
          key: "targetValue",
          width: 90
        },
        {
          title: "完成率",
          dataIndex: "targetCompletionRate",
          key: "targetCompletionRate",
          width: 90
        },
        {
          title: "同期值",
          dataIndex: "contemValue",
          key: "contemValue",
          width: 90
        },
        {
          title: "同比",
          dataIndex: "contemChangeRate",
          key: "contemChangeRate",
          width: 90
        },
        {
          title: "上期值",
          dataIndex: "previousValue",
          key: "previousValue",
          width: 90
        },
        {
          title: "环比",
          dataIndex: "previousChangeRate",
          key: "previousChangeRate",
          width: 90
        },
        {
          title: "频次",
          dataIndex: "indexFrequency",
          key: "indexFrequency",
          width: 90
        },
        {
          title: "指标时间",
          dataIndex: "indexDt",
          key: "indexDt",
          width: 110
        },
        {
          title: "指标类型",
          dataIndex: "indexType",
          key: "indexType",
          width: 100
        },
        {
          title: "是否预测",
          dataIndex: "isYC",
          key: "isYC",
          width: 100
        }
      ],
      tableData: [], // 表格数据
      SYS_NAME: window.system, // 系统名称
      timeMap: {
        month: "月",
        week: "周",
        day: "日"
      },
      timeTypeOptionInDict: [
        { key: "D", value: "日" },
        { key: "Q", value: "季" },
        { key: "HF", value: "半年" },
        { key: "Y", value: "年" },
        { key: "M", value: "月" },
        { key: "W", value: "周" }
      ], // 码值表中的月周日 id列表
      filterIndexArr: "", // 过滤指标名
      getCardListTimes: 0 // 获取卡片列表次数
    };
  },
  created() {
    this.getBussinessColosMap();
    console.log("start--->");
    console.time("fetchOrg");
    this.getOrgList().then(res => {
      console.timeEnd("fetchOrg");
      console.time("transmit");
      this.delDeepOrg(res.org || []);
      this.orgTreeList = res.org;
    });
    // 设置页面高度
    this.$nextTick(() => {
      this.getAndSetWindowHeight();
    });
    window.addEventListener("resize", this.getAndSetWindowHeight);
  },
  mounted() {
    // 设置皮肤
    this.form.skinStyle =
      getUrlParam("skinStyle") === "dark"
        ? "hisense-style dark"
        : "classic-style";
    console.log(getUrlParam("skinStyle") === "dark");
    this.activePlate = getUrlParam("plate") || "全部";
  },
  destroyed() {
    window.removeEventListener("resize", this.getAndSetWindowHeight);
  },
  watch: {
    // 监听置顶卡片变动修改底部高度
    topCardList: {
      handler() {
        this.$nextTick(() => {
          this.setBottomStyle();
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 修改底部_bottom减去顶部高度
    setBottomStyle() {
      if (this.searchForm.mode === "chart") {
        this.$refs["bottom-ref"].style.setProperty(
          "--topHeight",
          this.$refs["top-ref"].offsetHeight +
            (this.form.skinStyle.includes("classic-style") ? 16 : 0) +
            "px"
        );
      }
    },
    // 获取盘古字典中维护的版块颜色
    getBussinessColosMap() {
      request(
        `${decodeURIComponent(
          "/api/system/dict/type/query?types=smc-bkColorMap&languageCode=zh_CN"
        )}`
      ).then(res => {
        const bkColorMap = {};
        res["smc-bkColorMap"].forEach(element => {
          bkColorMap[element.key] = {
            bgColor: element.value.split("~")[0],
            color: element.value.split("~")[1]
          };
        });
        this.businessSegmentsColorMap = bkColorMap;
      });
    },
    // 设置页面高度
    getAndSetWindowHeight() {
      const viewWidth = this.$refs[
        `${this.companyName}${this.signOrgId}indexGeneralViewPage2`
      ]?.offsetWidth; // 概览页面宽度，如果等于1240，高度去掉滚动条高度
      let height = "";
      if (window.self === window.top) {
        // 在盘古内部使用
        height = document.getElementsByClassName("100heightDiv")[0]
          ?.offsetHeight;
        height = height
          ? `${height - (viewWidth && viewWidth === 1240 ? 18 : 0)}px`
          : "100vh";
      } else {
        // 在信数内使用
        height =
          window.innerHeight -
          (viewWidth && viewWidth === 1240 ? 18 : 0) +
          "px";
      }
      this.$refs[
        `${this.companyName}${this.signOrgId}indexGeneralViewPage2`
      ].style.setProperty("--realHeight", height);
      this.setBottomStyle();
    },
    // 最高层级下探3层组织
    delDeepOrg(arr, index = 0) {
      index++;
      arr.forEach(item => {
        if (index > 2) {
          item.list = [];
        } else {
          this.delDeepOrg(item.list, index);
        }
      });
    },
    // 指标卡订阅保存后需要重新请求下组织列表
    savedData() {
      console.time("fetchOrg");
      this.getOrgList().then(res => {
        console.timeEnd("fetchOrg");
        console.time("transmit");
        this.delDeepOrg(res.org || []);
        this.orgTreeList = res.org;
      });
      this.searchConditionChange();
    },
    // 根据公司获取组织列表
    getOrgList() {
      return new Promise(resolve => {
        request(`/api/smc2/newIndexLibrary/searchDimension`, {
          method: "POST",
          body: {
            sign: `${this.companyName}概览`,
            signOrgId: this.signOrgId
          }
        }).then(res => {
          if (Array.isArray(res["组织机构"]) && res["组织机构"].length === 0) {
            this.cardListLoading = false;
            // this.$message.warning("请先维护数据！");
          }
          resolve({
            org: res["组织机构"] || []
          });
        });
      });
    },
    // 根据公司获取置顶卡片列表
    getTopCardList() {
      return new Promise(resolve => {
        request(`/api/smc2/newIndexLibrary/getTop`, {
          method: "POST",
          body: {
            indexDt: this.searchForm.time,
            signOrgId: this.signOrgId,
            orgId: this.searchForm.orgSign.split("-")[
              this.searchForm.orgSign.split("-").length - 1
            ],
            indexFrequencyId: this.timeTypeOptionInDict.filter(
              item => item.value === this.timeMap[this.searchForm.timeType]
            )[0].key
          }
        }).then(res => {
          if (Array.isArray(res) && res.length) {
            const { searchTimer, nowTimer } = this.searchForm;
            const list = res.map(item => {
              const {
                id,
                dmId,
                indexId,
                indexName,
                indexDt,
                fullCode,
                indexFrequency,
                indexFrequencyId,
                org,
                orgId,
                businessSegments,
                businessSegmentsId,
                signOrgId,
                signOrg,
                actualValue,
                actualMolecule,
                actualDenominator,
                targetValue,
                targetCompletionRate,
                previousChangeRate,
                contemChangeRate,
                indexUnitId,
                indexSort,
                productAtt1,
                productAtt2,
                productAtt3,
                productAtt4,
                previousValue,
                contemValue,
                precisions,
                indexTypeId,
                indexNameInd,
                label,
                cmimId,
                pj // 用于拖拽排序标记
              } = item;
              const normalWDList = [
                productAtt1,
                productAtt2,
                productAtt3,
                productAtt4
              ].filter(item => item && !item.includes("指标卡"));
              let wdInCardName = normalWDList
                .filter(item => item.includes("卡片名称"))
                .map(item => item.split("-")[2]);
              wdInCardName = wdInCardName.join("-");
              const wdInCardTag = normalWDList
                .filter(item => item.includes("卡片标签"))
                .map(item => item.split("-")[2]);
              return {
                sign: `${this.companyName}概览`,
                dmId,
                indexId,
                indexName,
                indexDt,
                fullCode,
                indexFrequency:
                  indexFrequency || this.timeMap[this.searchForm.timeType],
                indexFrequencyId,
                org,
                orgId,
                businessSegments,
                businessSegmentsId,
                signOrgId,
                signOrg,
                actualValue: dealThousandData(
                  actualValue,
                  item.indexUnitId,
                  precisions
                ),
                actualMolecule: actualMolecule
                  ? new Decimal(actualMolecule).toFixed(
                      2,
                      Decimal.ROUND_HALF_UP
                    )
                  : "-",
                actualDenominator: actualDenominator
                  ? new Decimal(actualDenominator).toFixed(
                      2,
                      Decimal.ROUND_HALF_UP
                    )
                  : "-",
                targetValue: dealThousandData(
                  targetValue,
                  item.indexUnitId,
                  precisions
                ),
                targetCompletionRate: targetCompletionRate
                  ? `${Decimal(targetCompletionRate)
                      .mul(Decimal(100))
                      .toFixed(2, Decimal.ROUND_HALF_UP)}%`
                  : "",
                previousChangeRate,
                isPreviousRate: "Y",
                isContemRate: "Y",
                contemChangeRate,
                indexUnitId,
                indexType: indexTypeId,
                indexNameInd,
                displayIndexName: indexNameInd || indexName,
                indexSort,
                normalWDList,
                wdInCardName,
                wdInCardTag,
                previousValue: previousValue
                  ? new Decimal(previousValue).toFixed(2, Decimal.ROUND_HALF_UP)
                  : "",
                contemValue: contemValue
                  ? new Decimal(contemValue).toFixed(2, Decimal.ROUND_HALF_UP)
                  : "",
                cmimId,
                companyName: this.companyName,
                label,
                pj,
                id,
                show: searchTimer <= nowTimer, // 日月周三种时间，搜索条件时间大于当前时间所有指标都隐藏
                recommend: false
              };
            });
            this.topCardList = [...list].filter(item => item.show);
          } else {
            this.topCardList = [];
          }
          resolve(res);
        });
      });
    },
    // 切换推荐
    changeRecommend() {
      if (this.activeRecommendIndex + 1 < this.recommendList.length) {
        this.activeRecommendIndex++;
      } else {
        this.activeRecommendIndex = 0;
      }
    },
    // 推荐卡片添加到自己的卡片列表中
    addToCardList() {
      // 根据版块过滤后的数组排序
      let currRow = this.recommendList.splice(this.activeRecommendIndex, 1)[0];
      currRow.recommend = false;
      this.changeRecommend();
      request(`/api/smc2/newuserIndexRelation/insertByRecommend`, {
        method: "POST",
        body: {
          signOrgId: currRow.signOrgId,
          businessSegmentsId: currRow.businessSegmentsId,
          indexId: currRow.indexId,
          fullCode: currRow.fullCode,
          sign: `${this.companyName}概览`,
          type: "0",
          indexName: currRow.indexNameInd,
          org: currRow.org,
          businessSegments: currRow.businessSegments
        }
      }).then(() => {
        this.$nextTick(() => {
          // 重新获取组织
          this.getOrgList().then(res => {
            this.delDeepOrg(res.org || []);
            this.orgTreeList = res.org;
            // 是本地存储且树数据中查找不到该节点，则默认选中组织列表第一个
            const data = findNode(this.orgTreeList, node => {
              return node.fullCode === this.searchForm.orgSign;
            });
            if (data) {
              this.$refs["cardList"].refreshData();
            }
          });
        });
      });
    },
    // 获取卡片列表
    getCardList() {
      console.time("fetchIndexData");
      return new Promise(resolve => {
        request(`/api/smc2/newIndexLibrary/searchGL`, {
          method: "POST",
          body: {
            zsbg:
              this.searchForm.indexStatus === "0"
                ? "未达标"
                : this.searchForm.indexStatus === "1"
                ? "未达标同环比恶化"
                : "",
            indexDt: this.searchForm.time,
            sign: `${this.companyName}概览`,
            orgId: this.searchForm.orgSign,
            indexFrequencyId: this.timeTypeOptionInDict.filter(
              item => item.value === this.timeMap[this.searchForm.timeType]
            )[0].key
          }
        })
          .then(res => {
            console.timeEnd("fetchIndexData");
            if (res && Array.isArray(res)) {
              console.time("dealIndexData");
              const { searchTimer, nowTimer } = this.searchForm;
              const list = res.map(item => {
                const {
                  dmId,
                  indexId,
                  indexName,
                  indexDt,
                  fullCode,
                  indexFrequency,
                  indexFrequencyId,
                  org,
                  orgId,
                  businessSegments,
                  businessSegmentsId,
                  signOrgId,
                  signOrg,
                  actualValue,
                  targetValue,
                  targetCompletionRate,
                  previousChangeRate,
                  contemChangeRate,
                  indexUnitId,
                  indexSort,
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4,
                  productAtt1Id,
                  productAtt2Id,
                  productAtt3Id,
                  productAtt4Id,
                  productAtt5Id,
                  productAtt6Id,
                  productAtt7Id,
                  previousValue,
                  contemValue,
                  precisions,
                  indexTypeId,
                  indexNameInd,
                  actualMolecule,
                  actualDenominator,
                  label,
                  cmimId,
                  id,
                  pj // 用于拖拽排序标记
                } = item;
                const normalWDList = [
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4
                ].filter(item => item && !item.includes("指标卡"));
                let wdInCardName = normalWDList
                  .filter(item => item.includes("卡片名称"))
                  .map(item => item.split("-")[2]);
                wdInCardName = wdInCardName.join("-");
                const wdInCardTag = normalWDList
                  .filter(item => item.includes("卡片标签"))
                  .map(item => item.split("-")[2]);
                const displayIndexName = indexNameInd || indexName;
                return {
                  sign: `${this.companyName}概览`,
                  postEndIndexName:
                    (wdInCardName ? wdInCardName + " - " : "") +
                    displayIndexName +
                    (wdInCardTag.length ? " - " + wdInCardTag.join("-") : ""),
                  dmId,
                  id,
                  indexId,
                  indexName,
                  indexDt,
                  fullCode,
                  indexFrequency:
                    indexFrequency || this.timeMap[this.searchForm.timeType],
                  indexFrequencyId,
                  org,
                  orgId,
                  businessSegments,
                  businessSegmentsId,
                  signOrgId,
                  signOrg,
                  actualValue: dealThousandData(
                    actualValue,
                    item.indexUnitId,
                    precisions
                  ),
                  actualMolecule: actualMolecule
                    ? new Decimal(actualMolecule).toFixed(
                        2,
                        Decimal.ROUND_HALF_UP
                      )
                    : "-",
                  actualDenominator: actualDenominator
                    ? new Decimal(actualDenominator).toFixed(
                        2,
                        Decimal.ROUND_HALF_UP
                      )
                    : "-",
                  targetValue: dealThousandData(
                    targetValue,
                    item.indexUnitId,
                    precisions
                  ),
                  targetCompletionRate: targetCompletionRate
                    ? `${Decimal(targetCompletionRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)}%`
                    : "",
                  previousChangeRate,
                  isPreviousRate: "Y",
                  isContemRate: "Y",
                  contemChangeRate,
                  indexUnitId,
                  indexType: indexTypeId,
                  indexNameInd,
                  displayIndexName,
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4,
                  productAtt1Id,
                  productAtt2Id,
                  productAtt3Id,
                  productAtt4Id,
                  productAtt5Id,
                  productAtt6Id,
                  productAtt7Id,
                  previousValue: previousValue
                    ? new Decimal(previousValue).toFixed(
                        2,
                        Decimal.ROUND_HALF_UP
                      )
                    : "",
                  contemValue: contemValue
                    ? new Decimal(contemValue).toFixed(2, Decimal.ROUND_HALF_UP)
                    : "",
                  indexSort,
                  normalWDList,
                  wdInCardName,
                  wdInCardTag,
                  cmimId,
                  companyName: this.companyName,
                  label,
                  pj,
                  recommend: false,
                  canDragSort: searchTimer <= nowTimer, // 是否可以进行排序
                  show: searchTimer <= nowTimer, // 日月周三种时间，搜索条件时间大于当前时间所有指标都隐藏
                  showYC: searchTimer > nowTimer // 搜索条件时间年月大于当前时间年月，认为所有指标都是预测
                };
              });
              console.timeEnd("dealIndexData");

              // 单独为冰箱公司做处理
              if (this.signOrgId === "H0201") {
                // 查询条件月类型且当前月的1-19号以及当前月的往后月份，以下指标名称添加“预测”两字
                if (this.searchForm.timeType === "month") {
                  const nowYear = new Date().getFullYear();
                  const nowMonth = new Date().getMonth() + 1;
                  const nowDay = new Date().getDate();
                  // const searchYear = parseInt(
                  //   this.searchForm.time.split("-")[0]
                  // );
                  // const searchMonth = parseInt(
                  //   this.searchForm.time.split("-")[1]
                  // );
                  /**
                   *  1、日月周三种时间，搜索条件时间大于当前时间所有指标都隐藏
                   *  2、月搜索条件下，“AFR”和“90天”指标需要根据不同情况恢复展示。“AFR”预测未来一个月数据，“90天”预测未来三个月数据。
                   *  3、月搜索条件时间年月等于当前时间年月，本月20号之前“AFR”和“90天”指标需要展示预测标识，本月20号之后取消预测标识展示。
                   *  4、搜索条件时间年月大于当前时间年月，认为所有指标都是预测，结合1和2来实现最终效果
                   */
                  // part.1 “AFR”和“90天”指标  当月且20号之前添加预测标识
                  if (
                    `${nowYear}-${(nowMonth + "").padStart(2, 0)}` ===
                      this.searchForm.time &&
                    nowDay < 20
                  ) {
                    list.forEach(item => {
                      if (
                        item.displayIndexName.includes("90天") ||
                        item.displayIndexName.includes("AFR")
                      ) {
                        item["showYC"] = true;
                      }
                    });
                  }
                  // part.2 “AFR”一个月内、“90天”三个月内时间显示该指标
                  const days = (searchTimer - nowTimer) / (24 * 60 * 60 * 1000);
                  const criticalDaysAFR = this.getCriticalDays(1);
                  const criticalDays90 = this.getCriticalDays(3);
                  console.log("days------>", days);
                  console.log("预测一个月天数临界值---->", criticalDaysAFR);
                  console.log("预测三个月天数临界值---->", criticalDays90);
                  list.forEach(item => {
                    if (
                      (item.displayIndexName.includes("AFR") &&
                        days <= criticalDaysAFR) ||
                      (item.displayIndexName.includes("90天") &&
                        days <= criticalDays90)
                    ) {
                      item["show"] = true;
                    }
                  });
                }
              }
              console.time("transmitIndexData");
              const cardList = [...list].filter(item => item.show);
              this.cardList = sortBy(cardList, function(item) {
                return item.indexSort;
              });
            } else {
              this.cardList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.cardList = [];
            // this.topCardList = [];
            resolve([]);
          });
      });
    },
    // 获取后推月数临界值 predicMonths = 预测月数
    getCriticalDays(predicMonths) {
      let criticalDays = 0; // 三个月内时间的临界值，初始化当月总天数减去20天
      let dealMonths = 0; // 需要后推月数
      // const nowDay = new Date().getDate();
      // if (nowDay < 20) {
      //   dealMonths = predicMonths - 1 < 0 ? 0 : predicMonths - 1;
      // } else {
      //   dealMonths = predicMonths - 1;
      // }
      dealMonths = predicMonths - 1;
      for (let index = 0; index <= dealMonths; index++) {
        let year =
          new Date().getMonth() + 1 === 12
            ? new Date().getFullYear() + 1
            : new Date().getFullYear();
        let month =
          (new Date().getMonth() + 1 === 12 ? 0 : new Date().getMonth() + 1) +
          index;
        if (index !== 0) {
          criticalDays += moment(
            `${year}-${(month + "").padStart(2, "0")}`,
            "YYYY-MM"
          ).daysInMonth();
        } else {
          criticalDays = moment().daysInMonth() - 20;
        }
      }
      return criticalDays;
    },
    // 获取推荐列表
    getRecommendList() {
      return new Promise(resolve => {
        this.activeRecommendIndex = 0;
        request(`/api/smc2/newIndexLibrary/searchSubscribeable`, {
          method: "POST",
          body: {
            signOrgId: this.signOrgId,
            sign: `${this.companyName}概览`,
            indexDt: this.searchForm.time,
            orgId: this.searchForm.orgSign
              ? this.searchForm.orgSign.split("-")[
                  this.searchForm.orgSign.split("-").length - 1
                ]
              : "",
            indexFrequencyId: this.timeTypeOptionInDict.filter(
              item => item.value === this.timeMap[this.searchForm.timeType]
            )[0]?.key
          }
        })
          .then(res => {
            if (res && Array.isArray(res)) {
              const list = res.map(item => {
                const {
                  indexId,
                  indexName,
                  indexDt,
                  indexFrequency,
                  indexFrequencyId,
                  org,
                  orgId,
                  businessSegments,
                  fullCode,
                  businessSegmentsId,
                  signOrg,
                  signOrgId,
                  actualValue,
                  targetValue,
                  targetCompletionRate,
                  indexUnitId,
                  indexSort,
                  precisions,
                  indexNameInd,
                  label,
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4,
                  cmimId,
                  pj // 用于拖拽排序标记
                } = item;
                const normalWDList = [
                  productAtt1,
                  productAtt2,
                  productAtt3,
                  productAtt4
                ].filter(item => item && !item.includes("指标卡"));
                let wdInCardName = normalWDList
                  .filter(item => item.includes("卡片名称"))
                  .map(item => item.split("-")[2]);
                wdInCardName = wdInCardName.join("-");
                const wdInCardTag = normalWDList
                  .filter(item => item.includes("卡片标签"))
                  .map(item => item.split("-")[2]);
                return {
                  sign: `${this.companyName}概览`,
                  indexId,
                  indexName,
                  indexDt,
                  indexFrequency: indexFrequency || this.searchForm.timeType,
                  indexFrequencyId,
                  org,
                  orgId,
                  businessSegments,
                  businessSegmentsId,
                  signOrg,
                  actualValue: dealThousandData(
                    actualValue,
                    item.indexUnitId,
                    precisions
                  ),
                  targetValue: dealThousandData(
                    targetValue,
                    item.indexUnitId,
                    precisions
                  ),
                  targetCompletionRate: targetCompletionRate
                    ? `${Decimal(targetCompletionRate)
                        .mul(Decimal(100))
                        .toFixed(2, Decimal.ROUND_HALF_UP)}%`
                    : "",
                  indexUnitId,
                  indexNameInd,
                  indexSort,
                  displayIndexName: indexNameInd || indexName,
                  pj,
                  fullCode,
                  signOrgId,
                  wdInCardName,
                  wdInCardTag,
                  label,
                  cmimId,
                  companyName: this.companyName,
                  recommend: true
                };
              });
              this.recommendList = sortBy(list, function(item) {
                return item.indexSort;
              });
            } else {
              this.recommendList = [];
            }
            resolve(res);
          })
          .catch(() => {
            this.recommendList = [];
            resolve([]);
          });
      });
    },
    // 下载数据
    download() {
      // const { searchTimer, nowTimer } = this.searchForm;
      let postData = {
        zsbg:
          this.searchForm.indexStatus === "0"
            ? "未达标"
            : this.searchForm.indexStatus === "1"
            ? "未达标同环比恶化"
            : "",
        indexDt: this.searchForm.time,
        sign: `${this.companyName}概览`,
        orgId: this.searchForm.orgSign,
        indexFrequencyId: this.timeTypeOptionInDict.filter(
          item => item.value === this.timeMap[this.searchForm.timeType]
        )[0].key
      };
      if (this.filterIndexArr.length) {
        // 如果有搜索指标
        postData["idList"] = cloneDeep(this.tableData)
          .filter(item => this.filterIndexArr.includes(item.pj))
          .map(item => item.id);
        postData["idList1"] = cloneDeep(this.tableData)
          .filter(item => this.filterIndexArr.includes(item.pj))
          .map(item => `${item.id}-${item.showYC ? "是" : "否"}`);
      } else {
        // // 没有搜索指标查看的是未来时间的指标 或 正常情况下，告诉后端哪些指标是预测指标
        // (this.filterIndexArr.length === 0 && searchTimer > nowTimer) ||
        //   searchTimer > nowTimer;
        postData["idList"] = cloneDeep(this.tableData).map(item => item.id);
        postData["idList1"] = cloneDeep(this.tableData).map(
          item => `${item.id}-${item.showYC ? "是" : "否"}`
        );
      }
      pureAxios({
        url: "/smc2/newIndexLibrary/exportGLList",
        method: "post",
        data: postData,
        responseType: "blob"
      })
        .then(response => {
          if (!response.data) {
            return;
          }
          const fileName = `${this.companyName}核心KPI概览数据表.xlsx`;
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message
          });
        });
    },
    // 查询条件更改
    searchConditionChange(data) {
      this.getCardListTimes++;
      this.searchForm = { ...this.searchForm, ...data };
      const timeType = this.searchForm.timeType || "day";
      if (timeType !== "week") {
        this.searchForm.time = moment(this.searchForm.time).format(
          timeType === "day" ? "YYYY-MM-DD" : "YYYY-MM"
        );
      }
      if (!this.searchForm.orgSign) {
        console.error("检查组织");
        return;
      }
      const searchTimer =
        this.searchForm.timeType === "week"
          ? new Date(
              getDateOfISOWeek(
                Number(this.searchForm.time.split("-")[1]),
                Number(this.searchForm.time.split("-")[0])
              )
            ).getTime()
          : new Date(this.searchForm.time).getTime(); // search time  周类型要把YYYY-MM时间转换成YYYY-MM-DD 月类型YYYY-MM转换成YYYY-MM-01 日类型YYYY-MM-DD
      const nowTimer = new Date().getTime();
      this.searchForm = { ...this.searchForm, searchTimer, nowTimer }; // 添加两个Timer
      this.cardListLoading = true;
      Promise.all([this.getTopCardList(), this.getCardList()]).then(() => {
        this.cardListLoading = false;
        if (this.searchForm.mode === "table") {
          const tableData = [...this.cardList];
          for (let i = 0; i < tableData.length; i++) {
            // 补充后台没返回的字段
            tableData[i]["displayIndexName"] =
              (tableData[i].wdInCardName
                ? tableData[i].wdInCardName + " - "
                : "") + tableData[i].displayIndexName;
            tableData[i]["isYC"] = tableData[i]["showYC"] ? "是" : "否";
            tableData[i]["index"] = i + 1;
            const deal100ParamsArr = ["contemChangeRate", "previousChangeRate"];
            tableData[i]["signOrg"] =
              tableData[i]["signOrg"] || this.companyName;
            deal100ParamsArr.forEach(item => {
              const dataItem = tableData[i];
              tableData[i][item] = dataItem[item]
                ? Decimal(dataItem[item])
                    .mul(Decimal(100))
                    .toFixed(2, Decimal.ROUND_HALF_UP) + "%"
                : "";
            });
          }
          this.tableData = tableData;
        } else {
          this.tableData = [];
        }
      });
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage2 {
  white-space: nowrap;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  color: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  min-width: 1240px;
  display: flex;
  flex-direction: column;
  background-color: rgb(238, 239, 243);
  box-sizing: border-box;
  height: var(--realHeight);
  position: relative;
  ._flex {
    display: flex;
    align-items: center;
  }
  & > ._top {
    background-color: #fff;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    margin-bottom: 16px;
    min-width: 1200px;
  }
  &.classic-style {
    padding: 16px 20px;
    & > ._top {
      padding: 22px 20px 0 20px;
    }
    & > ._bottom {
      background-color: transparent;
      box-sizing: border-box;
      overflow-y: auto;
    }
  }
  &.hisense-style {
    &.dark {
      background-color: #000;
      & > ._top,
      & > ._bottom {
        background-color: #313335;
      }
      & > ._top .empty-fill-color-div {
        background-color: #000;
      }
    }
    padding: 16px;
    & > ._top {
      padding: 20px 16px 0 16px;
      margin-bottom: 0;
      .empty-fill-color-div {
        width: calc(100% + 32px);
        margin: 0 -16px;
        margin-top: 20px;
        height: 16px;
        background-color: rgb(238, 239, 243);
      }
    }
    &:not(.dark) {
      .empty-bg-div {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        height: 100%;
        width: 100%;
        z-index: 0;
        overflow: hidden;
        &::before {
          content: "";
          display: block;
          width: calc(100% - 32px);
          height: calc(100% - 32px);
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          background: #fff;
          z-index: 1;
        }
        .div {
          width: 667px;
          height: 1007px;
          position: absolute;
          right: 16px;
          top: 16px;
          background-position: right top;
          background-repeat: no-repeat;
          background-image: url("~@/assets/images/Group 2413.png");
          background-size: 667px 1007px;
          opacity: 0.8;
          z-index: 2;
        }
      }
      & > ._top {
        background-color: transparent;
      }
      & > ._bottom {
        ._plate {
          z-index: 1;
        }
      }
    }
    & > ._bottom {
      background-color: #fff;
      box-sizing: border-box;
      overflow-y: hidden;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding: 16px 16px 0 16px;
      .card-list {
        flex: 1;
        overflow-y: auto;
      }
      .table-area {
        flex: 1;
        overflow-y: auto;
      }
    }
  }
  & > ._bottom {
    min-width: 1200px;
    overflow-x: hidden;
    height: calc(100% - var(--topHeight));
    &.table {
      flex: 1;
    }
  }
  .change-skin-btn {
    position: fixed;
    right: 0;
    bottom: 60px;
  }
}

.index-comparison-container {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow-x: auto;
  z-index: 999;
}
</style>
