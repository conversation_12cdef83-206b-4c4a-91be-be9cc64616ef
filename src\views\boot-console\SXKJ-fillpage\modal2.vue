<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2024-12-05 17:25:13
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="800"
    title="责任人维护"
    @ok="handleOk"
    @cancel="close"
  >
    <a-table
      size="small"
      :pagination="false"
      :columns="dataColumns"
      :data-source="dataList"
    >
      <div slot="userName" slot-scope="text, record, index">
        <template
          v-if="
            [
              'litingting21',
              'wangshengzhen.ex',
              'yueshengqi.ex',
              'niuwendan',
              record.signUserLdap,
            ].includes(nowLoginUserAccount) || record.isEdit
          "
        >
          <a-select
            style="width: 300px"
            show-search
            :value="record.userLdap"
            :dropdownMatchSelectWidth="false"
            placeholder="请输入责任人名称"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            @search="handleAccountSearch($event, 'user', index)"
            @change="handleAccountChange($event, 'user', index)"
          >
            <a-select-option
              v-for="d in userMapList[`user${index}`] || []"
              :key="`${d.name}-${d.account}`"
              :value="`${d.name}-${d.account}`"
            >
              {{ d.name }}({{ d.account }}) {{ d.o }}
            </a-select-option>
          </a-select>
        </template>
        <template v-else>
          {{ text }}
        </template>
      </div>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
export default {
  data() {
    this.handleAccountSearch = debounce(this.handleAccountSearch, 800);
    return {
      visible: false, // 打开关闭弹窗
      dataColumns: [
        {
          title: "车间",
          dataIndex: "des",
          key: "des",
        },
        {
          title: "责任人",
          dataIndex: "userName",
          key: "userName",
          width: 300,
          scopedSlots: { customRender: "userName" },
        },
      ],
      dataList: [],
      userMapList: {},
    };
  },
  computed: {
    // 当前登陆用户的账号
    nowLoginUserAccount() {
      return window.vm.$store
        ? window.vm.$store.state.user.info?.loginName
        : "yuyongjie.ex";
    },
  },
  methods: {
    // type=0 问题 type=1 车间
    getList() {
      request(`/api/smc2/gate/searchNotifyPerson?type=1`).then((res) => {
        this.dataList = res.map((item) => {
          return {
            ...item,
            userLdap: ["litingting21", "yangman2", item.userLdap].includes(
              this.nowLoginUserAccount
            )
              ? `${item.userName}-${item.userLdap}`
              : item.userLdap,
            signUserLdap: item.userLdap,
            isEdit: false,
          };
        });
      });
    },
    handleAccountSearch(value, key, index) {
      this.getAcountList(value).then((res) => {
        console.log(`${key}MapList`, `${key}${index}`, res);
        this.$set(this[`${key}MapList`], [`${key}${index}`], res);
      });
    },
    handleAccountChange(value, key, index) {
      const data = {
        ...this.dataList[index],
      };
      data[`${key}Ldap`] = value;
      data["isEdit"] = true;
      this.$set(this.dataList, index, data);
    },
    getAcountList(account) {
      return new Promise((resolve) => {
        request(`/api/smc2/ldap/searchLdapUser`, {
          method: "POST",
          body: {
            account,
          },
        }).then((res) => {
          resolve(res || []);
        });
      });
    },
    show() {
      this.visible = true;
      this.getList();
    },
    close() {
      this.visible = false;
      this.dataList = [];
      this.userMapList = {};
    },
    handleOk() {
      this.saveCard();
    },
    // 保存卡片信息
    saveCard() {
      const list = this.dataList
        .filter((item) => item.isEdit)
        .map((item) => {
          const userLdap = item.userLdap.includes("-")
            ? item.userLdap.split("-")[1]
            : item.userLdap;
          return {
            des: item.des,
            userLdap,
          };
        });
      if (list.length === 0) {
        this.$message.warning("请修改数据后提交");
        return;
      }
      request(`/api/smc2/gate/batchInsetPerson`, {
        method: "POST",
        body: {
          type: 1,
          list,
        },
      }).then((res) => {
        console.log("res----->", res);
        this.close();
      });
    },
  },
};
</script>
