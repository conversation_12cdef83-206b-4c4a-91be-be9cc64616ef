<!--
 * @Description: 
 * @Author: y<PERSON><PERSON>gqi.ex
 * @Date: 2025-03-06 10:07:39
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2025-03-06 10:56:38
-->
<template>
  <div>
    {{
      this.record.signOrg === "视像科技公司"
        ? this.record.warnType === 0
          ? "通知"
          : "工单"
        : this.record.warnType === 0
        ? "异常"
        : "预警"
    }}
  </div>
</template>
<script>
export default {
  props: {
    record: Object, // 当前操作行
    text: String,
    name: String, // 当前字段名
  },
  data() {
    return {};
  },
  mounted() {},
};
</script>
