<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-25 08:48:00
-->
<template>
  <!-- 累加目标值修改 -->
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal
      ref="modal"
      @fetchData="fetchData"
      :record="this.record"
      :target="this.target"
      :targetCIM="this.targetCIM"
      :targetCBG="this.targetCBG"
    />
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
  },
  data() {
    return {
      showAlias,
      target: true,
      targetCIM: true,
      targetCBG: false,
    };
  },
  methods: {
    btClick() {
      request("/api/smc2/newTarget/isModify").then((res) => {
        this.target = res.target;
        this.targetCIM = res.targetCIM;
        // this.targetCBG = res.targetCBG;
        this.targetCBG = false;
        this.$refs["modal"].show({
          indexName: this.record.indexName,
          indexUnit: this.record.unit,
          attributesId: this.record.attributesId,
          cmimId: this.record.cmimId,
          isLJ: true,
        });
      });
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex,
        },
      });
    },
  },
};
</script>
