<!--
 * @Description: 卡片列表
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-08 09:45:21
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-11-23 08:55:44
-->
<template>
  <div class="card_list">
    <div class="own-row">
      <div class="drag_box">
        <!-- 骨架屏 -->
        <template v-if="cardListLoading">
          <div
            v-for="item in pageClass === 'indexGeneralViewPage' ? 5 : 3"
            :key="item"
            class="own-col"
          >
            <a-card class="card spin">
              <a-skeleton active />
            </a-card>
          </div>
        </template>
        <template v-if="!cardListLoading">
          <!-- 指标 -->
          <div
            v-for="(item, index) in filterList"
            :key="
              `${item.indexId}==${
                item.baseName
              }==${index}=${new Date().getTime()}`
            "
            class="own-col"
            :data-dragDisabled="item.recommend"
          >
            <CardItem
              :pageClass="pageClass"
              :companyName="companyName"
              :base="base"
              @refreshData="refreshData"
              :item="item"
              :index="index"
              @showDetail="showDetail"
              @trendShow="trendShow"
              v-on="$listeners"
              :inHiData="inHiData"
            />
          </div>
          <!-- 新增指标 -->
          <div
            class="own-col"
            v-if="pageClass === 'indexGeneralViewPage' && !inHiData"
            data-dragDisabled="true"
          >
            <a-card class="card addCard" @click="addCard">
              <a-icon type="plus" />
              <span>添加指标卡片</span>
            </a-card>
          </div>
        </template>
      </div>
    </div>
    <!-- 卡片详情 -->
    <CardDetailDrawer
      :frequency="frequency"
      :indexDt="indexDt"
      :pageClass="pageClass"
      :indexCardDetailInfoJSUrl="indexCardDetailInfoJSUrl"
      ref="cardDetailDrawer"
    />
    <!-- 添加卡片 -->
    <Modal
      @refreshData="refreshData"
      :base="base"
      :companyName="companyName"
      ref="addCardModal"
    />
    <IndexTrendModel ref="IndexTrendModel" />
  </div>
</template>
<script>
import CardItem from "./cardItem.vue";
import cloneDeep from "lodash/cloneDeep";
import CardDetailDrawer from "./CardInfo/cardDetailDrawer.vue";
import Modal from "./modal.vue";
import request from "@/utils/requestHttp";
import { adminUserUrlPrefix } from "@/utils/utils";
import IndexTrendModel from "./indexTrendModal.vue";

export default {
  components: { CardItem, CardDetailDrawer, Modal, IndexTrendModel },
  props: {
    activePlate: String, // 当前激活的版块
    pageClass: String, // 当前是哪个页面（概览/横比）使用的cardList
    base: String, // 基地
    companyName: String, // 公司名称
    list: Array, // 卡片list
    cardListLoading: Boolean, // 卡片list Loading
    frequency: String, // 频率
    indexDt: String, // 时间
    indexCardDetailInfoJSUrl: String, // 指标详情组件jsUrl
    sortLoading: {
      // 拖拽排序后保存按钮的loading
      type: Boolean,
      default() {
        return false;
      }
    },
    // 搜索的指标名称
    searchIndexName: {
      type: String,
      default() {
        return "";
      }
    },
    // 信数速览内使用，仅展示卡片，不需要卡片详情，拖拽，删除，订阅卡片功能
    inHiData: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  data() {
    return {
      filterList: [], // 根据版块筛选后的数据，用于版块筛选后拖拽排序后可以正常保存页面卡片顺序
      pageShow: true // 页面当前是否展示 （生命周期）
    };
  },
  inject: ["isDesign"],
  watch: {
    // 监听卡片列表数据源
    list(val) {
      if (this.activePlate === "全部" && this.searchIndexName === "") {
        // 如果当前的版块是全部或者搜索的指标为空则，完全复制filterList
        this.filterList = cloneDeep(val);
      } else {
        // 处理filterList
        this.dealFilterList(this.activePlate, this.searchIndexName, val);
      }
    },
    activePlate: {
      handler(val) {
        if (val === "全部" && this.searchIndexName === "") {
          this.filterList = cloneDeep(this.list);
        } else {
          this.dealFilterList(val, this.searchIndexName, this.list);
        }
      }
    },
    searchIndexName: {
      handler(val) {
        if (val === "" && this.activePlate === "全部") {
          this.filterList = cloneDeep(this.list);
        } else {
          this.dealFilterList(this.activePlate, val, this.list);
        }
      }
    }
  },
  mounted() {
    // 概览页面
    if (this.pageClass === "indexGeneralViewPage") {
      // 定义拖动
      this.$nextTick(() => {
        this.initSortable();
      });
    }
  },
  methods: {
    // 处理filterList
    dealFilterList(
      activePlate = this.activePlate,
      searchIndexName = this.searchIndexName,
      list = this.list
    ) {
      if (activePlate === "全部" && searchIndexName !== "") {
        this.filterList = cloneDeep(list).filter(
          item => item.indexName === searchIndexName
        );
      } else if (activePlate !== "全部" && searchIndexName === "") {
        this.filterList = cloneDeep(list).filter(
          item => item.plateName === activePlate
        );
      } else {
        this.filterList = cloneDeep(list).filter(
          item =>
            item.plateName === activePlate && item.indexName === searchIndexName
        );
      }
    },
    // 初始化拖动
    initSortable() {
      const _this = this;
      var el = document.querySelector(
        `.${this.pageClass}.${this.companyName} .card_list .drag_box`
      );
      Sortable.create(el, {
        animation: 400,
        ghostClass: "blue-background-class",
        easing: "cubic-bezier(1, 0, 0, 1)",
        handle: "._drag",
        onMove(evt) {
          if (evt.related.getAttribute("data-dragDisabled") === "true") {
            return false;
          }
        },
        onEnd(data) {
          const { newIndex, oldIndex } = data;
          const oldIndexId = _this.filterList[oldIndex].indexId;
          const newIndexId = _this.filterList[newIndex].indexId;
          // 根据版块过滤后的数组排序
          const currRow = _this.filterList.splice(oldIndex, 1)[0];
          _this.filterList.splice(newIndex, 0, currRow);
          // 全部的数组排序
          let oldIndexInAll = 0;
          let newIndexInAll = 0;
          _this.list.forEach((item, index) => {
            if (item.indexId === oldIndexId) {
              oldIndexInAll = index;
            }
            if (item.indexId === newIndexId) {
              newIndexInAll = index;
            }
          });
          const currRowInAll = _this.list.splice(oldIndexInAll, 1)[0];
          _this.list.splice(newIndexInAll, 0, currRowInAll);
          _this.$emit("onDraged", true);
        }
      });
      document.body.ondrop = function(event) {
        event.preventDefault();
        event.stopPropagation();
      };
    },
    // 刷新数据
    refreshData() {
      this.$emit("refreshData");
    },
    // 保存卡片顺序
    saveCardSort() {
      if (this.list.length === 0 || this.isDesign) {
        return;
      }
      this.$emit("update:sortLoading", true);
      let postData = {
        base: this.base,
        company: this.companyName,
        list: []
      };
      this.list.forEach(item => {
        if (!item.recommend) {
          postData.list.push(`${item.indexId}==${this.base}`);
        }
      });
      postData.list = Array.from(new Set(postData.list));
      request(`${adminUserUrlPrefix["lxp"]}/userIndexRelation/saveSort`, {
        method: "POST",
        body: postData
      })
        .then(() => {
          this.$emit("update:sortLoading", false);
          this.refreshData();
        })
        .catch(() => {
          this.$emit("update:sortLoading", false);
        });
    },
    // 展示详情
    showDetail(item) {
      if (this.inHiData) {
        return;
      }
      this.$refs.cardDetailDrawer.show(item);
    },
    // 新增卡片
    addCard() {
      this.$refs.addCardModal.show();
    },
    // 展示指标走势
    trendShow(item) {
      this.$refs["IndexTrendModel"].show(item);
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage,
.indexGeneralViewPage-inHidata,
.indexComparisonPage {
  .card_list {
    .own-row {
      .drag_box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }
      .own-col {
        width: calc(20% - 16px);
        margin: 0 8px;
        margin-bottom: 16px;
        @media screen and (max-width: 1023px) {
          width: calc(25% - 16px);
        }
        @media screen and (min-width: 2000px) {
          width: calc(16% - 16px);
        }
        .card {
          cursor: pointer;
          border: none;
          background: #f4f5f7;
          border-radius: 12px;
          box-sizing: border-box;
          transition: all ease-in-out 0.3s;
          padding: 20px;
          &:hover {
            background: #fff;
            box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
            .dragDel {
              display: flex !important;
            }
          }
          &.recommend,
          &.addCard,
          &.spin {
            background: #ffffff;
            box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
          }
          .ant-card-body {
            padding: 0;
            position: relative;
          }
          .__top {
            margin-bottom: 8px;
            position: relative;
            ._title {
              height: 20px;
              line-height: 20px;
              margin-right: 4px;
              width: 50%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .plate,
            .YC {
              height: 20px;
              font-size: 12px;
              color: #53667a;
              line-height: 20px;
              border-radius: 2px;
              padding: 0 4px;
              background-color: #e0e3ea;
              margin-right: 8px;
            }
            .YC {
              color: #ee734f;
              background-color: #fdeeea;
            }
            .dragDel {
              right: 0;
              top: 0;
              position: absolute;
              display: none;
              ._report {
                font-size: 20px;
              }
              ._zs,
              ._report,
              ._drag {
                margin-right: 8px;
              }
              .anticon {
                color: rgba(0, 0, 0, 0.25);
                transition: color ease-in-out 0.3s;
                &:hover {
                  color: #00aaa6;
                }
              }
            }
          }
          ._data {
            height: 32px;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 32px;
            margin-bottom: 8px;
            span {
              font-size: 16px;
              line-height: 32px;
            }
          }
          .c_completRate,
          ._target,
          .completRate {
            font-size: 12px;
            line-height: 18px;
            margin-bottom: 8px;
            &._target {
              margin-bottom: 4px;
            }
            &.completRate {
              margin-bottom: 11px;
            }
          }
          .sign {
            color: #6495f9;
            font-size: 12px;
            margin-bottom: 16px;
            & > span {
              display: inline-block;
              height: 20px;
              line-height: 20px;
              padding: 0 4px;
              border-radius: 2px;
              background: #e0e3ea;
              &:not(:last-child) {
                margin-right: 4px;
              }
            }
          }
          ._op {
            justify-content: space-between;
            ._another {
              height: 20px;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.35);
              line-height: 20px;
              cursor: pointer;
              transition: all ease-in-out 0.3s;
              &:hover {
                color: #00aaa6;
              }
            }
            ._addtolist {
              font-size: 12px;
              height: 20px;
              padding: 0 9.5px;
            }
          }
          .recommend_bg {
            width: 48px;
            height: 48px;
            position: absolute;
            right: -21px;
            top: -20px;
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
            background-image: url("~@/assets/images/recommend.png");
          }
          .completRate {
            & > span {
              display: block;
              &:nth-child(1) {
                margin-right: 8px;
              }
              &.process {
                position: relative;
                width: 44%;
                height: 8px;
                background: #f0f0f0;
                margin-right: 12px;
                & > span {
                  height: 8px;
                  display: block;
                  &.b {
                    background: #6495f9;
                  }
                  &.r {
                    background: #f75050;
                  }
                }
              }
            }
          }
          .compare {
            font-size: 12px;
            ._c > span {
              color: rgba(0, 0, 0, 0.65);
              display: block;
              margin-right: 2px;
              white-space: nowrap;
            }
            ._c > div {
              .anticon {
                position: relative;
                top: 1px;
              }
              white-space: nowrap;
              span {
                white-space: nowrap;
              }
            }
          }
          &.addCard {
            cursor: pointer;
            padding: 64.5px 0;
            background: #fff;
            color: rgba(0, 0, 0, 0.25);
            transition: all ease-in-out 0.3s;
            &:hover {
              background: #f4f5f7;
            }
            .ant-card-body {
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              .anticon {
                width: 40px;
                height: 40px;
                font-size: 40px;
                margin-bottom: 24px;
              }
              span {
                height: 20px;
                font-size: 14px;
                line-height: 20px;
              }
            }
          }
        }
      }
    }
  }
}
.indexComparisonPage {
  .own-col {
    width: calc(25% - 16px) !important;
    @media screen and (max-width: 1023px) {
      width: calc(30% - 16px) !important;
    }
    @media screen and (min-width: 2000px) {
      width: calc(20% - 16px) !important;
    }
  }
}
</style>
