<!--
 * @Description: 组件打包入口示例文件
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2020-08-28 14:23:09
 * @LastEditors: gao<PERSON><PERSON>
 * @LastEditTime: 2021-05-31 17:42:35
-->
<template>
  <div
    :style="{
      width: width + 'px',
      height: '100px',
      background: 'red'
    }"
  >
    {{ $t("CANCEL") }}
  </div>
</template>

<script>
import ComFunMixins from "pangea-com/lib/ComFunMixins";
export default {
  name: "DemoCom",
  mixins: [ComFunMixins],
  props: {
    comKey: [String, Number], // 当前组件唯一标识 key
    data: Object, // 当前组件json
    json: Object // 当前组件所在页面的整个json
  },
  data() {
    return {
      // 当组件需要对外暴漏事件，供其他组件进行调用时，需配置EventOnList参数，在fun中进行回调方法处理
      EventOnList: [
        {
          key: "handleMethod",
          fun: params => {
            // 在 handleMethod 方法中进行回调函数编写
            this.handleMethod();
            typeof params.callback === "function" && params.callback();
          }
        }
      ]
    };
  },
  computed: {
    // 当前组件属性props对象
    attribute() {
      if (this.data && this.data.props) {
        return this.data.props;
      } else {
        return {};
      }
    },
    width() {
      if (this.attribute && this.attribute.width) {
        return this.attribute.width;
      } else {
        return 100;
      }
    }
  },
  methods: {
    handleMethod() {
      console.log("这里是回调方法");
    }
  }
};
</script>
