/*
 * @Description: 图表tooltip自动滚动管理相关方法
 */
import { createAutoTooltip } from "./utils/autoTooltip";

/**
 * 初始化tooltip自动滚动
 * @param {Object} charts - 包含所有图表实例的对象 {deliveryChart, qualityChart, timeChart}
 * @param {Object} options - 配置选项
 * @returns {Object} - 返回tooltip控制器对象
 */
export const initAutoTooltip = (charts, options = {}) => {
  const { deliveryChart, qualityChart, timeChart } = charts;

  // 确保图表实例存在
  if (!deliveryChart || !qualityChart || !timeChart) {
    console.warn('图表实例不存在，无法初始化tooltip自动滚动');
    return null;
  }

  // 创建各图表的tooltip自动滚动控制器
  const tooltipControllers = {
    deliveryTooltip: createAutoTooltip(deliveryChart, {
      interval: options.interval || 2000, // 默认2秒切换一次
      loop: options.loop !== undefined ? options.loop : true,
      debug: options.debug || false
    }),

    qualityTooltip: createAutoTooltip(qualityChart, {
      interval: options.interval || 2000,
      loop: options.loop !== undefined ? options.loop : true,
      debug: options.debug || false
    }),

    timeTooltip: createAutoTooltip(timeChart, {
      interval: options.interval || 2000,
      loop: options.loop !== undefined ? options.loop : true,
      debug: options.debug || false
    })
  };

  return tooltipControllers;
};

/**
 * 启动所有图表的tooltip自动滚动
 * @param {Object} tooltipControllers - tooltip控制器对象
 * @param {Object} charts - 包含所有图表实例的对象
 * @param {Function} checkChartHasData - 检查图表是否有数据的函数
 * @returns {void}
 */
export const startAutoTooltip = (tooltipControllers, charts, checkChartHasData) => {
  const { deliveryTooltip, qualityTooltip, timeTooltip } = tooltipControllers;
  const { deliveryChart, qualityChart, timeChart } = charts;

  // 确保各个控制器更新数据信息
  if (deliveryTooltip) {
    deliveryTooltip.updateDataInfo();
  }

  if (qualityTooltip) {
    qualityTooltip.updateDataInfo();
  }

  if (timeTooltip) {
    timeTooltip.updateDataInfo();
  }

  // 检查图表是否有数据
  const hasDeliveryData = checkChartHasData(deliveryChart);
  const hasQualityData = checkChartHasData(qualityChart);
  const hasTimeData = checkChartHasData(timeChart);

  // 只在有数据的图表上启动自动滚动
  if (deliveryTooltip && deliveryChart && hasDeliveryData) {
    // 确保先停止，再重新启动
    deliveryTooltip.stop();
    deliveryTooltip.start();
  } else {
    // 确保停止并隐藏tooltip
    if (deliveryTooltip) {
      deliveryTooltip.stop();
    }
  }

  if (qualityTooltip && qualityChart && hasQualityData) {
    // 确保先停止，再重新启动
    qualityTooltip.stop();
    qualityTooltip.start();
  } else {
    // 确保停止并隐藏tooltip
    if (qualityTooltip) {
      qualityTooltip.stop();
    }
  }

  if (timeTooltip && timeChart && hasTimeData) {
    // 确保先停止，再重新启动
    timeTooltip.stop();
    timeTooltip.start();
  } else {
    // 确保停止并隐藏tooltip
    if (timeTooltip) {
      timeTooltip.stop();
    }
  }
};

/**
 * 停止所有图表的tooltip自动滚动
 * @param {Object} tooltipControllers - tooltip控制器对象
 * @returns {void}
 */
export const stopAutoTooltip = (tooltipControllers) => {
  const { deliveryTooltip, qualityTooltip, timeTooltip } = tooltipControllers;

  // 停止各图表的tooltip自动滚动
  if (deliveryTooltip) {
    deliveryTooltip.stop();
  }

  if (qualityTooltip) {
    qualityTooltip.stop();
  }

  if (timeTooltip) {
    timeTooltip.stop();
  }
};

/**
 * 暂停所有图表的tooltip自动滚动
 * @param {Object} tooltipControllers - tooltip控制器对象
 * @returns {void}
 */
export const pauseAutoTooltip = (tooltipControllers) => {
  const { deliveryTooltip, qualityTooltip, timeTooltip } = tooltipControllers;

  if (deliveryTooltip) {
    deliveryTooltip.pause();
  }

  if (qualityTooltip) {
    qualityTooltip.pause();
  }

  if (timeTooltip) {
    timeTooltip.pause();
  }
};

/**
 * 恢复所有图表的tooltip自动滚动
 * @param {Object} tooltipControllers - tooltip控制器对象
 * @returns {void}
 */
export const resumeAutoTooltip = (tooltipControllers) => {
  const { deliveryTooltip, qualityTooltip, timeTooltip } = tooltipControllers;

  if (deliveryTooltip) {
    deliveryTooltip.resume();
  }

  if (qualityTooltip) {
    qualityTooltip.resume();
  }

  if (timeTooltip) {
    timeTooltip.resume();
  }
};

/**
 * 重置所有图表的tooltip自动滚动
 * @param {Object} tooltipControllers - tooltip控制器对象
 * @param {Object} charts - 包含所有图表实例的对象
 * @param {Function} checkChartHasData - 检查图表是否有数据的函数
 * @param {boolean} enabled - 是否启用自动滚动
 * @returns {void}
 */
export const resetAutoTooltip = (tooltipControllers, charts, checkChartHasData, enabled) => {
  // 停止当前的自动滚动
  stopAutoTooltip(tooltipControllers);

  // 给图表更多时间完成渲染和数据加载
  setTimeout(() => {
    // 确保各个控制器更新数据信息
    const { deliveryTooltip, qualityTooltip, timeTooltip } = tooltipControllers;

    if (deliveryTooltip) {
      deliveryTooltip.updateDataInfo();
    }

    if (qualityTooltip) {
      qualityTooltip.updateDataInfo();
    }

    if (timeTooltip) {
      timeTooltip.updateDataInfo();
    }

    // 如果启用了自动滚动，则重新启动
    if (enabled) {
      // 再次延迟启动，确保图表完全渲染
      setTimeout(() => {
        startAutoTooltip(tooltipControllers, charts, checkChartHasData);
      }, 1000);
    }
  }, 1500); // 增加延迟时间到1.5秒，确保API数据已加载完成
};
