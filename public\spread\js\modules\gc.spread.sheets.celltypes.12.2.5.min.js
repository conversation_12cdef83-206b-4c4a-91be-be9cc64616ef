/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.CellTypes=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/celltype/celltypes.entry.js")}({"./dist/plugins/celltype/buttoncelltype.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=e.Events.SelectionChanged,g=d.Common.j.Fa,h=e.Ul.nl,i=e.CellTypes.Context,j=e.CellTypes.Text,k=e.CellTypes.Base,l=e.GC$,m=l.each,n=null,o=void 0,p="_isFirstMouseUp",q="_mouseupToken";function x(a,b,c,d){var e,f=a.ITa;f&&(f.do("setValueForKey",b,c,p,o,d),e=f.getValueForKey(b,c,q,d),e&&(clearTimeout(e),f.do("setValueForKey",b,c,q,o,d)))}r="#707070",s="#34B4E3",t=".buttonCellType",u={marginTop:2,marginRight:2,marginBottom:2,marginLeft:2,text:"",buttonBackColor:n,buttonState:0},v=function(a){w(b,a);function b(){var b,c=a.call(this)||this;return c.typeName="6",b=c,m(u,function(a,c){b["_"+a]=c}),c}return b.prototype.paintValue=function(a,b,c,d,e,f,g,h){var i,k,l,m,n,o,p,q,t,u,v,w,x;a&&(i=this,k=i._marginLeft,l=i._marginTop,m=c+k,n=d+l,o=e-k-i._marginRight,p=f-l-i._marginBottom,q=m+o>c&&m<c+e&&n+p>d&&n<d+f,o-2>0&&p-2>0&&q&&(a.save(),(m<c||m+o>c+e||n<d||n+p>d+f)&&(a.rect(c,d,e,f),a.clip()),a.beginPath(),t=r,t&&a.strokeStyle!==t&&(a.strokeStyle=t),a.strokeRect(m+.5,n+.5,o-1,p-1),u=void 0,v=i._buttonState,w=i._text,2===v?u=s:(u=i._buttonBackColor,u||(x=a.createLinearGradient(c+e/2,d,c+e/2,d+f),x.addColorStop(.125,"#F6FAFB"),x.addColorStop(1,"#D2DBEB"),u=x)),a.fillStyle!==u&&(a.fillStyle=u),a.fillRect(m+1,n+1,o-2,p-2),a.restore(),w&&j.prototype.paintValue.call(i,a,w,m,n,o,p,g,h)))},b.prototype.getText=function(a,b){return a},b.prototype.tw=function(a,b,c,d){var e=a.parent;e&&e.tw(a,b,c,d)},b.prototype.getHitInfo=function(a,b,c,d,e){var f,h,i,j,k,l,m=this;return e&&(f=e.sheetArea,(g(f)||3===f)&&d)?(h=d.x+m._marginLeft,i=d.x+d.width-m._marginRight,j=d.y+m._marginTop,k=d.y+d.height-m._marginBottom,l={x:a,y:b,row:e.row,col:e.col,cellRect:d,sheetArea:f,sheet:e.sheet},h<=a&&a<=i&&j<=b&&b<=k&&(l.isReservedLocation=!0),l):n},b.prototype.processMouseDown=function(a){var b=this,c=a&&a.sheet;return!(!c||!a.isReservedLocation||b.uK)&&(b.uK=!0,b._buttonState=2,c.repaint(a.cellRect),!0)},b.prototype.processMouseUp=function(a){var b,c,d,e,f,g=this,h=a&&a.sheet;if(g.uK&&h&&a.isReservedLocation){if(g._buttonState=0,h.repaint(a.cellRect),g.uK=!1,b=a.row,c=a.col,d=a.sheetArea,e=h.ITa.getValueForKey(b,c,p,d),!e)return g.tw(h,b,c,d),h.ITa.do("setValueForKey",b,c,p,!0,d),f=setTimeout(function(){x(h,b,c,d)},250),h.ITa.do("setValueForKey",b,c,q,f,d),!0;x(h,b,c,d)}return!1},b.prototype.processMouseLeave=function(a){var b=this,c=a&&a.sheet;c&&b.uK&&(b._buttonState=0,c.repaint(a.cellRect),b.uK=!1)},b.prototype.processKeyDown=function(a,b){var c,d=b.sheet,e=this;return!(!d||e.vK)&&(c=d.getCellRect(b.row,b.col),e._buttonState=2,d.repaint(c),d.Fu(f+t,function(){d.Gu(f+t),e.vK=!1,e._buttonState=0,d.repaint(c)}),e.vK=!0,!0)},b.prototype.processKeyUp=function(a,b){var c,d,e,g=b.sheet,h=this;return!(!g||!h.vK)&&(c=b.row,d=b.col,e=g.getCellRect(c,d),h._buttonState=0,g.repaint(e),g.Gu(f+t),h.tw(g,c,d,b.sheetArea),h.vK=!1,!0)},b.prototype.isReservedKey=function(a,b){return 32===a.keyCode&&!a.ctrlKey&&!a.shiftKey&&!a.altKey},b.prototype.getAutoFitWidth=function(a,b,c,d,e){var f=this,g=i.cp(a,f._text,c,d,e);return g+f._marginLeft+f._marginRight},b.prototype.getAutoFitHeight=function(a,b,c,d,e){var f=this,g=i.ep(a,f._text,c,d,e);return g+f._marginTop+f._marginBottom},b.prototype.JH=function(a){this.isReservedKey(a)&&h(a)},b.prototype.isImeAware=function(a){return!1},b.prototype.toJSON=function(){var a,b=this,c={typeName:b.typeName};return m(u,function(d,e){"buttonState"!==d&&(a=b["_"+d],a!==e&&(c[d]=a))}),c},b.prototype.fromJSON=function(a){var b=this;m(u,function(c){var d=a[c];g(d)||(b["_"+c]=d)})},b.prototype.a5=function(a){},b.prototype.gQa=function(){},b}(k),b.Button=v,m(u,function(a){v.prototype[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}}),e.CellTypes._o[6]=v},"./dist/plugins/celltype/celltypes.entry.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;function l(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=d.CellTypes.Base,f=d.CellTypes.Text,g=d.CellTypes.Context,h=d.CellTypes.Corner,i=d.CellTypes.RowHeader,j=d.CellTypes.EditorType,k=d.CellTypes.ColumnHeader,b.Base=e,b.Text=f,b.Context=g,b.Corner=h,b.RowHeader=i,b.EditorType=j,b.ColumnHeader=k,l(c("./dist/plugins/celltype/checkboxcelltype.js")),l(c("./dist/plugins/celltype/buttoncelltype.js")),l(c("./dist/plugins/celltype/hyperlinkcelltype.js")),l(c("./dist/plugins/celltype/comboboxcelltype.js"))},"./dist/plugins/celltype/checkboxcelltype.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=e.Ul.Nl,g=d.Common.j.Fa,h=e.Ul.fp,i=e.Ul.nl,j=e.CellTypes.Context,k=e.CellTypes.Base,l=e.GC$,m=l.each,n=e.Ul.hZa,o=null,p=void 0,q=Math.max,r=parseInt,s="position",t="absolute",u="font",v="left",w="right",x="top",y="alphabetic",z=".gcEditingInput",A="background-color",B="white",C="green",D="div",E="_isFirstMouseUp",F="_mouseupToken";function L(a,b,c,d){var e,f=a.ITa;f&&(f.do("setValueForKey",b,c,E,p,d),e=f.getValueForKey(b,c,F,d),e&&(clearTimeout(e),f.do("setValueForKey",b,c,F,p,d)))}!function(a){a[a.top=0]="top",a[a.bottom=1]="bottom",a[a.left=2]="left",a[a.right=3]="right"}(G=b.CheckBoxTextAlign||(b.CheckBoxTextAlign={})),H=12,I={caption:"",textTrue:"",textIndeterminate:"",textFalse:"",textAlign:3,isThreeState:!1},J=function(a){K(b,a);function b(){var b,c=a.call(this)||this;return c.typeName="5",b=c,m(I,function(a,c){b["_"+a]=c}),c}return b.prototype.paintValue=function(a,b,c,d,f,i,j,k){var l,m,n,o,p,q,s,t,u,x,z,A,D,E,F,G,I,J,K,L,M;a&&(a.save(),a.rect(c,d,f,i),a.clip(),a.beginPath(),l=j.hAlign,m=j.font,n=j.foreColor,o=this,p=o._textAlign,q=o.getText(b,k),s=0,t=0,u=k.sheet,u&&(s=u.dp(q,m),t=h(m),q&&(x=q.split(/\r\n|\r|\n/),t*=x.length)),z=u.zoom(),++c,++d,--f,--i,A=H/2,D=new e.Rect(c,d,f,i),E=r(""+(c+N(p,j,D,s,z))),F=r(""+(d+O(p,j,D,t,z))),G=v,I=E+1,1===l?(G="center",I+=A):2===l&&(G=w,I+=2*A),m&&a.font!==m&&e.Ul.lZa(a,m),n&&(a.fillStyle=n),J=j.textDecoration,K=parseInt(k.fontInfo.fontSize,10),L=K>8?Math.floor((K-8)/5+2):1,M=t/2-K/2+L-1,a.textBaseline!==y&&(a.textBaseline=y),2===p?(a.textAlign=w,a.fillText(q,E+1-2,F+A+t/2-M),J&&o.ip(a,J,E+1-2,F+A+t/2-M,s,K,L)):0===p&&(a.textAlign=G,a.fillText(q,I,F-2-M),J&&o.ip(a,J,I,F-2-M,s,t)),a.save(),a.strokeStyle="black",a.strokeRect(E+.5,F+.5,2*A+.05,2*A+.05),a.fillStyle=B,a.fillRect(E+1,F+1,2*A-1,2*A-1),o._isThreeState&&g(b)?(a.beginPath(),a.fillStyle=C,a.rect(E+3,F+3,2*(A-2.5),2*(A-2.5)),a.fill()):!!b==!0&&(a.beginPath(),a.lineWidth=2.5,a.moveTo(E+3,F+A),a.lineTo(E+A,F+2*A-3.5),a.lineTo(E+2*A-1.5,F+3),a.stroke()),a.restore(),n&&(a.fillStyle=n),3===p?(a.textAlign=v,a.fillText(q,E+1+2*A+2,F+A+t/2-M),J&&o.ip(a,J,E+1+2*A+2,F+A+t/2-M,s,K,L)):1===p&&(a.textAlign=G,a.fillText(q,I,F+2*A+2+t-M),J&&o.ip(a,J,I,F+2*A+2+t-M,s,K,L)),a.restore())},b.prototype.getText=function(a,b){return this.sK(a)},b.prototype.focus=function(a,b){a&&a.parentNode.focus()},b.prototype.createEditorElement=function(a,b){var c,d,g,h,i=a&&a.sheet&&a.sheet.parent&&a.sheet.parent.qo,j=e.Ul.vl(i)+1e3;return l(b).css("z-index",j).attr("tabindex",1).attr("gcUIElement","gcEditingInput"),c=l(b.firstChild),c.css("overflow","hidden"),d=l(f("input")),d.attr("type","checkbox"),d.attr("class","gc-checkbox-cell-type-input"),c.append(d),g=l(f("span")),g.css(s,t).css(u,n("normal 11pt calibri")).css("cursor","default").css("white-space","nowrap"),c.append(g),h=l(f(D)),h.css(s,t).css(A,C).css("width",2*(H/2-2.5)).css("height",2*(H/2-2.5)),c.append(h),o},b.prototype.rK=function(a){var b;return b=this._isThreeState?!g(a)&&(!!a!=!0||o):!a},b.prototype.setEditorValue=function(a,b,c){var d,e,f,h,i,j,k,m,n;a&&(d=a.parentNode.children,e=d[0],f=d[1],h=d[2],e&&f&&h&&(i=this,j=c&&c.sheet,k=j&&j.HF,m=void 0,n=void 0,k||(n=i.rK(b)),i._isThreeState?g(n)?(m=!1,l(h).show()):(m=!!n,l(h).hide()):m=!!n,e.checked=m,l(f).text(i.sK(n))))},b.prototype.getEditorValue=function(a,b){if(a){var c=a.parentNode.children,d=c[0],e=c[1],f=c[2];if(d&&e&&f&&(!this._isThreeState||!l(f).isVisible()))return d.checked}return o},b.prototype.tw=function(a,b,c,d){var e=a.parent;e&&e.tw(a,b,c,d)},b.prototype.activateEditor=function(a,b,c,d){var e,f,g=d&&d.sheet;a&&g&&(e=l(a.parentNode.parentNode),f=this,e.bind("mousedown"+z,function(a){i(a)}),e.bind("mouseup"+z,function(){Q(f,a,b,c,d)}),e.bind("keydown"+z,function(a){var b=a.keyCode;if(!a.ctrlKey&&!a.shiftKey&&!a.altKey){if(32===b)return f.tK=!0,i(a),!1;8===b&&i(a)}}),e.bind("keyup"+z,function(e){!f.tK||32!==e.keyCode||e.ctrlKey||e.shiftKey||e.altKey||(f.tK=!1,Q(f,a,b,c,d))}),l(a).bind("click",function(a){i(a)}))},b.prototype.updateEditor=function(a,b,c,d){var e,f,g,i,j,k,m,o,p,q,r,s,t,w,y,z,A,B,C,D,E,F,G,I,J,K,L,M,P,Q=d&&d.sheet;a&&Q&&(e=Q.yl,f=a.parentNode,g=f.children,i=g[0],j=g[1],k=g[2],i&&j&&k&&(l(f).width(c.width).height(c.height),m=l(j),o=m.text(),p=b.foreColor,q=b.font,r=b.hAlign,s=b.textDecoration,p&&m.css("color",p),t=void 0,t=q?q:e.Bl(),w=Q.zoom(),w>1&&(t=e.Cl(t)),m.css(u,n(t)),y=Q.rt(o,t),z=h(t),o&&(A=o.split(/\r\n|\r|\n/),z*=A.length),B=this,C=B._textAlign,D=N(C,b,c,y,w),E=O(C,b,c,z,w),l(i).css("margin-left",D).css("margin-top",E),F=i.offsetLeft,G=i.offsetTop,I=i.offsetWidth,J=i.offsetHeight,K=0,L=0,0===C?(K=F,1===r?K=F+H/2-y/2:2===r&&(K=F+H-y),L=G-z):1===C?(K=F,1===r?K=F+H/2-y/2:2===r&&(K=F+H-y),L=G+J):2===C?(K=F-y-2,L=G+H/2-z/2):(K=F+I+2,L=G+H/2-z/2),m.css(v,K).css(x,L),B._isThreeState?(M=F+(I-k.offsetWidth)/2,P=G+(J-k.offsetHeight)/2,l(k).css(v,M).css(x,P).toggle().toggle()):l(k).hide(),s&&B.lp(m,s)))},b.prototype.sK=function(a){var b=this,c=b._caption;return b._isThreeState&&g(a)?b._textIndeterminate||c:!!a==!0?b._textTrue||c:b._textFalse||c},b.prototype.getHitInfo=function(a,b,c,d,e){if(e){var f=e.sheetArea;if(g(f)||3===f)return{x:a,y:b,row:e.row,col:e.col,cellRect:d,sheetArea:3,isReservedLocation:!0,sheet:e.sheet}}return o},b.prototype.processMouseDown=function(a){return!!a&&void(a.isReservedLocation&&(this.uK=!0))},b.prototype.processMouseUp=function(a){var b,c,d,e,f,g=this,h=a&&a.sheet;if(h&&g.uK&&a.isReservedLocation){if(g.uK=!1,b=a.row,c=a.col,d=a.sheetArea,e=h.ITa.getValueForKey(b,c,E,d),!e)return P(g,h,b,c,d),h.ITa.do("setValueForKey",b,c,E,!0,d),f=setTimeout(function(){L(h,b,c,d)},250),h.ITa.do("setValueForKey",b,c,F,f,d),!0;L(h,b,c,d)}return!1},b.prototype.processMouseLeave=function(a){return!!a&&void(this.uK=!1)},b.prototype.isReservedKey=function(a,b){return 32===a.keyCode&&!a.ctrlKey&&!a.shiftKey&&!a.altKey},b.prototype.processKeyUp=function(a,b){return P(this,b.sheet,b.row,b.col,b.sheetArea),!0},b.prototype.getAutoFitWidth=function(a,b,c,d,e){var f=this,g=f._textAlign,h=j.cp(a,f.sK(a),c,d,e);return 0===g||1===g?h=q(h,H):h+=H,h+5+2},b.prototype.getAutoFitHeight=function(a,b,c,d,e){var f=this,g=f._textAlign,h=j.ep(a,f.sK(a),c,d,e);return 0===g||1===g?h+=H:h=q(h,H),h+5},b.prototype.JH=function(a){this.isReservedKey(a)&&i(a)},b.prototype.isImeAware=function(a){return!1},b.prototype.gQa=function(){},b.prototype.toJSON=function(){var a,b=this,c={typeName:b.typeName};return m(I,function(d,e){a=b["_"+d],a!==e&&(c[d]=a)}),c},b.prototype.fromJSON=function(a){var b=this;m(I,function(c){var d=a[c];g(d)||(b["_"+c]=d)})},b}(k),b.CheckBox=J;function M(a){return 5*a}function N(a,b,c,d,e){var f=M(e),g=b.hAlign,h=c.x-1,i=c.width+1,j=0,k=0;return 0===a||1===a?(k=h+f,1===g?k=h+i/2-H/2:2===g&&(k=h+i-f-H)):2===a?(j=H+d,k=h+f+d,1===g?k=h+i/2-j/2+d:2===g&&(k=h+i-f-j+d)):(j=H+d,k=h+f,1===g?k=h+i/2-j/2:2===g&&(k=h+i-f-j)),k-h}function O(a,b,c,d,e){var f=M(e),g=b.vAlign,h=c.y-1,i=c.height+1,j=0,k=0;return 0===a?(j=H+d,k=h+f+d,1===g?k=h+i/2-j/2+d:2===g&&(k=h+i-f-j+d)):1===a?(j=H+d,k=h+f,1===g?k=h+i/2-j/2:2===g&&(k=h+i-f-j)):(k=h+f,1===g?k=h+i/2-H/2:2===g&&(k=h+i-f-H)),k-h}function P(a,b,c,d,e){var f=b.getValue(c,d,e),g=a.rK(f),h={cmd:"editCell",sheetName:b.name(),row:c,col:d,newValue:g,autoFormat:!0};b.wu().execute(h),a.tw(b,c,d,e)}function Q(a,b,c,d,e){var f=e&&e.sheet,g=a.getEditorValue(b,e);a.setEditorValue(b,g,e),a.updateEditor(b,c,d,e),a.tw(f,f.Jl,f.Kl,e.sheetArea)}m(I,function(a){J.prototype[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}}),e.CellTypes._o[5]=J},"./dist/plugins/celltype/comboboxcelltype.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=e.CellTypes.Base,g=e.Ul.Nl,h=d.Common.j.Fa,i=e.Ul,j=i.nl,k=e.GC$,l=k.each,m=i.hZa,n=i.Fxb,o=document,p=null,q=void 0,r=Math.max,s=parseInt,t="click",u="keydown",v="mouseover",w="mouseout",x="position",y="absolute",z="font",A="left",B="top",C="gcUIElement",D="tabindex",E="none",F="width",G="height",H="outline",I="box-sizing",J="color",K="background-color",L="z-index",M="display",N="content-box",O="white",P="black",Q="title",R="div",S="span",T="text",U="value",V="border",W=i.K_a;function ka(a,b){a.appendChild(b)}function la(a,b){a.removeChild(b)}function ma(a){return a&&a.parentNode.parentNode.comboBox}!function(a){a[a.text=0]="text",a[a.index=1]="index",a[a.value=2]="value"}(X=b.EditorValueType||(b.EditorValueType={})),Y=17,Z=20,$=0,_=1,aa=1,ba=_,ca="EndEdit.gcEditingInput",da=18,ea="lightgrey",fa="#1E90FF",ga={editorValueType:0,items:p,itemHeight:22,editable:!1,maxDropDownItems:Z},ha=function(){function a(a,b,c,d,f,i){var j,l,m,n,o,p,s,z,J=this;J.KK=a,j=a.firstChild,k(a).bind(u,function(a){J.LK(a)}).css(L,i||0).css(A,b).css(B,c).css(F,d).css(G,f).css("overflow","visible").attr(C,"gcComboBox"),l=r(0,d-Y),J._editable=!1,J.MK=m=g(R),J.NK=n=g("textarea"),J.PK=m,ka(j,m),na(m),na(n),k(n).bind("keyup",function(){var a,b=J.PK.value;J._text=b,a=J.QK(b),J.editorValue(a),J.RK(b),J.SK()}).bind(v,function(){J.TK()}).bind(w,function(){J.SK()}),k(m).bind(t,function(){J.IK?J.Wq(ca,{isMouse:!0}):J.UK()}).bind(v,function(){J.TK()}).bind(w,function(){J.SK()}),J.VK(0,0,l,f),o=J.WK=g(R),ka(j,o),k(o).css(V,E).css(x,y).css(K,O).css(I,N).attr(C,"gcDropDownButton").bind(t,function(){var a,b;!h(J.JK)&&(a=J.JK,J.JK=q,b=(new Date).valueOf(),b-a<100)||(J.IK?J.Wq(ca,{isMouse:!0}):J.UK())}),p=g("canvas"),J.Xs=p,J.XK(l,0,d-l,f),ka(o,p),s=J.YK=g(R),ka(j,s),J.ZK=!1,k(s).css(x,y).css(V,"1px solid").css(K,O).css(L,i||0).css(H,E).css(M,E).css("cursor","default").css(I,N).attr(C,"gcDropDownWindow").attr(D,-1).css(F,d-2*$).css(G,f),J.$K(0,f-2*$),J._K=0,z=J.aL=g(R),ka(s,z),J.bL=new e.IB(s,z),J._itemHeight=22,J.cL=Z,J.dL=-1,J._editorValueType=0,J._items=[]}return a.prototype.getComboBox=function(){return this.KK},a.prototype.dispose=function(){var a=this;k(a.KK).unbind(u),k(a.NK).unbind("keyup").unbind(v).unbind(w),k(a.MK).unbind(t).unbind(v).unbind(w),k(a.WK).unbind(t),k(a.aL).find("div").forEach(function(a){k(a).unbind(v).unbind(w).unbind(t)}),a.bL.dispose()},a.prototype.updateLocationAndSize=function(a,b,c,d){var e=this,f=r(0,c-Y);e.VK(0,0,f,d),e.XK(f,0,c-f,d),e.$K(0,d-2*$),e.listWidth(c-2*$),e.eL()},a.prototype.updateStyle=function(a,b,c){var d=this;c=m(c),a=a?a:q,k(d.PK).css(K,a).css(J,b).css(z,c),k(d.WK).css(K,a).css(J,b).css(z,c),k(d.YK).css(K,a).css(J,b).css(z,c),k(d.KK).css(K,"transparent")},a.prototype.updateImeMode=function(a){k(this.PK).css("ime-mode",a)},a.prototype.editorValueType=function(a){return 0===arguments.length?this._editorValueType:(this._editorValueType=a,this)},a.prototype.editorValue=function(a){var b,c=this;return 0===arguments.length?c.fL:(a!==c.fL&&(c.fL=a,b=c.gL(a),c.text(b),c.RK(b,!0)),c)},a.prototype.gL=function(a){var b,c=this,d=c._items,e=d.length,f=c._editorValueType,g,i,j;if(c.HK=!0,1===f)g=d[a],b=g&&oa(g,T)?g.text:g,b===q&&(b=a,c.HK=!1);else if(0===f){for(b=a,j=0;j<e&&(g=d[j],!(g&&oa(g,T)&&g.text===a||g===a));j++);j>=e&&(c.HK=!1)}else if(2===f){for(i=0;i<e;i++)if(g=d[i],g&&oa(g,U)&&g.value===a){b=g.text;break}i>=e&&(b=a,c.HK=!1)}return h(a)&&(b=""),b},a.prototype.QK=function(a){var b,c,d,e=this,f=e._items,g=f.length,h=e._editorValueType,i=a;if(1===h)for(c=0;c<g;c++)b=f[c],(b&&oa(b,T)&&b.text===a||b===a)&&(i=c);else if(2===h)for(d=0;d<g;d++)b=f[d],b&&oa(b,T)&&b.text===a?i=b.value:b===a&&(i=q);return i},a.prototype.RK=function(a,b){var c,d,e,f,g;if(a){for(d=this._items,e=d.length,c=0;c<e&&(f=d[c],g=void 0,g=oa(f,T)?f.text:f,(b?g:(""+g).substr(0,a.length))!==a);c++);c>=e&&(c=-1)}else c=-1;this.hL(c)},a.prototype.SK=function(){k(this.PK).removeAttr(Q)},a.prototype.TK=function(){var a=this,b=a.Xn(a._text),c=k(a.PK),d=c.width();b>d+_?c.attr(Q,a._text):c.removeAttr(Q)},a.prototype.VK=function(a,b,c,d){c-=$,d-=2*$,k(this.PK).css(A,a).css(B,b).css(F,c-_).css(G,d-aa)},a.prototype.editable=function(a){var b,c,d,e,f,g=this;return 0===arguments.length?g._editable:g._editable!==a?(g._editable=a,b=g.KK.firstChild,c=g.PK,d=k(c),e={left:s(d.css(A)),top:s(d.css(B)),width:s(d.css(F)),height:s(d.css(G))},f={backColor:d.css(K),foreColor:d.css(J),font:c.style.font},la(b,c),g.PK=a?g.NK:g.MK,ka(b,g.PK),k(g.PK).css(K,f.backColor).css(J,f.foreColor).css(z,f.font),g.VK(e.left,e.top,e.width,e.height),g):void 0},a.prototype.text=function(a){var b=this,c=b.PK;return 0===arguments.length?b._text:void(a!==b._text&&(b._text=a,b._editable?c.value=a:c.textContent=a))},a.prototype.focus=function(){var a=this,b=a.PK;b.focus(),a._editable&&(b.selectionStart=b.value.length)},a.prototype.selectAll=function(){var a=this;a._editable&&a.PK.select()},a.prototype.XK=function(a,b,c,d){var e,f,g,h,i;c-=$,d-=2*$,e=this,f=e.Xs,k(e.WK).css(A,a).css(B,b).css(F,c).css(G,d),k(f).attr(F,c).attr(G,d),g=f.width,h=f.height,i=f.getContext("2d"),i.beginPath(),i.lineWidth=2,i.fillStyle=P,i.moveTo(g-Y+4,(h-2)/2-2.5),i.lineTo(g-Y+7,(h-2)/2****),i.lineTo(g-Y+10,(h-2)/2-2.5),i.fill()},a.prototype.showDropDownList=function(){var a=this,b=a.bL;a.ZK=!0,k(a.YK).bind(u,function(b){a.LK(b)}).show(),a.iL=!1,a.jL(),a.eL(),a.iL&&(b.lB(k(a.aL.children).height()),b.ew(!1)),a.kL(fa),a.lL(a.dL),a.focus()},a.prototype.closeDropDownList=function(){var a=this;a.ZK=!1,k(a.YK).unbind(u).hide(),a.kL(""),a.focus()},a.prototype.UK=function(){this.ZK?this.closeDropDownList():this.showDropDownList()},a.prototype.listWidth=function(a){var b=this,c=k(b.YK);return 0===arguments.length?c.width():void(a>0&&(c.width(a),b.bL.ew(!1)))},a.prototype.listHeight=function(a){var b=this,c=k(b.YK);return 0===arguments.length?c.height():void(a>0&&(c.height(a),b.bL.ew(!1)))},a.prototype.$K=function(a,b){k(this.YK).css(A,a).css(B,b)},a.prototype.items=function(a){var b,c,d,e,f,g=this;if(0===arguments.length)return g._items;if(a){for(g._items=a,b=g.aL,c=void 0,k(b).find("div").forEach(function(a){k(a).unbind(v).unbind(w).unbind(t)}),c=b.firstChild;c;)la(b,c),c=b.firstChild;for(g._K=0,d=0,e=a.length;d<e;d++)f=a[d],g.mL(oa(f,T)?f.text:f);return g}},a.prototype.mL=function(a){var b=this,c=g(R),d=g(S);d.textContent=""+a,d.style.paddingLeft=ba+"px",ka(c,d),ka(b.aL,c),b._K++,k(c).bind(t,function(){var a=k(c).index();b.selectedIndex(a),b.closeDropDownList(),b.Wq(ca,{isMouse:!0})}).bind(v,function(){var a=k(c);c.oldBackColor=a.css(K),a.css(K,ea)}).bind(w,function(){k(c).css(K,c.oldBackColor||"")})},a.prototype.nL=function(){var a=this;a.Po=g(S),k(a.Po).css(M,E).css(z,a.YK.style.font).appendTo(o.body)},a.prototype.oL=function(){la(o.body,this.Po)},a.prototype.pL=function(a){return this.Po.textContent=a,k(this.Po).width()+2+ba},a.prototype.kL=function(a){var b=this,c=b.dL;0<=c&&c<b._K&&k(b.aL.children[c]).css(K,a)},a.prototype.LK=function(a){var b,c,d,e=this,f=a.keyCode,g=a.which;a.ctrlKey||a.shiftKey||a.altKey||a.metaKey||(37===g||39===g)&&e.editable()||(b=e._K,c=e.dL,d=0,38===g||40===g?(c>=0&&c<b&&(d=c+(38===g?-1:1)),d>=0&&d<b&&e.selectedIndex(d),e.ZK&&e.lL(d),j(a)):13===g||37===g||39===g||9===g?(e.selectedIndex(e.dL),e.closeDropDownList(),e.Wq(ca,{keyCode:f}),j(a)):27===g&&(e.closeDropDownList(),e.Wq(ca,{keyCode:f}),j(a)))},a.prototype.getSelectedValue=function(a,b,c){var d,e=b[a];return h(e)||(1===c?d=a:0===c?d=oa(e,T)?e.text:e:2===c&&oa(e,U)&&(d=e.value)),d},a.prototype.hL=function(a){var b=this;b.kL(""),b.dL=a,b.kL(fa),b.lL(a)},a.prototype.selectedIndex=function(a){var b,c=this;return 0===arguments.length?c.dL:void(0<=a&&a<c._K&&(c.hL(a),b=c.getSelectedValue(c.dL,c._items,c._editorValueType),h(b)||c.editorValue(b)))},a.prototype.itemCountPerPage=function(a){return 0===arguments.length?this.cL:void(a>0&&(this.cL=a))},a.prototype.Xn=function(a){this.nL();var b=this.pL(a);return this.oL(),b},a.prototype.itemHeight=function(a){return 0===arguments.length?this._itemHeight:void(a>0&&(this._itemHeight=a))},a.prototype.jL=function(){var a,b,c,d,e,f=this;f.nL(),a=f._items[0],b=r(f._itemHeight,k(f.Po).text(h(a)?"":a).height()),k(f.aL.children).css(G,b),d=f._K,e=f.cL,d<=e?c=d*b:(c=e*b,f.iL=!0),f.listHeight(c),f.oL()},a.prototype.eL=function(){var a,b,c,d,e,f,g=this;if(g.nL(),a=0,c=g._items)for(d=0,e=c.length;d<e;d++)f=c[d],b=g.pL(oa(f,T)?f.text:f),a<b&&(a=b);g.oL(),g.listWidth()<a+da&&g.listWidth(a+da)},a.prototype.lL=function(a){if(a>=0&&a<this._K){var b=this.aL.children[a];this.bL.BB(b)}},a.prototype.bind=function(a,b,c){k(this.KK).bind(a,b,c)},a.prototype.unbind=function(a,b){k(this.KK).unbind(a,b)},a.prototype.Wq=function(a,b){k(this.KK).trigger(a,b)},a}();function na(a){k(a).css("margin",0).css("overflow","hidden").css("resize",E).css(x,y).css("padding",aa+"px 0px 0px "+_+"px").css(H,E).css(K,O).css("white-space","nowrap").css(I,N).css(V,E).attr(C,"gcComboBoxEditor").attr(D,-1)}ia=function(a){ja(b,a);function b(){var b,c=a.call(this)||this;return c.typeName="7",c.DF=!1,c.GK=!1,b=c,l(ga,function(a,c){"items"===a&&(c=[]),b["_"+a]=c}),c}return b.prototype.isReservedKey=function(a,b){return this.GK},b.prototype.paintValue=function(a,b,c,d,f,g,h,i){var j,k,l,m,o,p,q,s,t=this,u=i.sheet,v=u.zoom?u.zoom():1,w=Y,x=r(0,f-w-1);3===h.hAlign&&(h.hAlign=0),h.wordWrap&&(h.wordWrap=!1),x>0&&g>0&&(j={},k=void 0,i.quotePrefix=h.quotePrefix,l=h.formatter&&"General"!==h.formatter,m=h._autoFormatter&&h._autoFormatter.formatCached&&"General"!==h._autoFormatter.formatCached,o=void 0,p=this.getItemValue(b),o=l?h.formatter:m?h._autoFormatter:e.CellTypes.Context.Ixb(a,p,x+1,h,v),k=this.format(W(b),o,j,i),k&&(q=h.isVerticalText,n(p)&&!q&&h.shrinkToFit!==!0&&(s=e.CellTypes.Context.Lxb(a,k,x+1,h,v),k!==s&&(delete j.content,h.textIndent=0),k=s),this.paintText(a,t.getText(b,i),c,d,x,g,h,i,k,j))),a.save(),(w>f||w>g)&&(a.rect(c,d,f,g),a.clip()),a.beginPath(),a.lineWidth=2,a.fillStyle=P,a.moveTo(c+f-w+3,d+(g-2)/2-2.5),a.lineTo(c+f-w+6,d+(g-2)/2****),a.lineTo(c+f-w+9,d+(g-2)/2-2.5),a.fill(),a.restore()},b.prototype.getText=function(a,b){return a},b.prototype.createEditorElement=function(a,b){var c,d=a&&a.sheet,e=d&&d.parent,f=e&&e.qo,g=i.vl(f)+1e3,h=d.defaults,j=this,k=new ha(b,0,0,h.colWidth,h.rowHeight,g);return k.editorValueType(j._editorValueType),k.items(j._items),k.itemHeight(j._itemHeight),k.itemCountPerPage(j._maxDropDownItems),k.editable(j._editable),c=k.getComboBox(),Object.defineProperty(c,"comboBox",{get:function(){return this.ixb},set:function(a){this.ixb!==a&&(this.ixb&&this.ixb.dispose(),this.ixb=a)}}),c.comboBox=k,p},b.prototype.getEditorValue=function(a,b){var c=ma(a);return c&&(this.DF=!c.HK),c&&c.editorValue()},b.prototype.setEditorValue=function(a,b,c){var d=ma(a);d&&d.editorValue(b)},b.prototype.focus=function(a,b){var c=ma(a);c&&c.focus()},b.prototype.selectAll=function(a,b){var c=ma(a);c&&c.selectAll()},b.prototype.activateEditor=function(a,b,c,d){var e=this,f=d.sheet,g=ma(a);g&&(g.editorValueType(e._editorValueType),g.items(e._items),g.itemHeight(e._itemHeight),g.itemCountPerPage(e._maxDropDownItems),g.editable(e._editable),g.bind(ca,function(a,b){var c,d,e,h,i,j=b.keyCode;if(j){if(c=f.wu(),d=c.getShortcutKey(j,!1,!1,!1,!1),e=c.getCommands(d))for(h=0,i=e.length;h<i;h++)if("navigationLeft"!==e[h].Cj&&"navigationRight"!==e[h].Cj||(f.Px=1),e[h].execute(f.parent,{sheetName:f.name()}))return!0}else b.isMouse&&g.IK&&f.endEdit()})),e.GK=!0},b.prototype.deactivateEditor=function(a,b){if(a){var c=ma(a);c&&(c.unbind(ca),c.closeDropDownList(),c.IK=!1)}this.GK=!1},b.prototype.updateImeMode=function(a,b){if(this.isImeAware()){var c=ma(a);c&&c.updateImeMode(b)}},b.prototype.updateEditor=function(a,b,c,d){var e,f,g=a&&d&&d.sheet;g&&(e=ma(a),b&&e&&(f=g.yl,e.updateStyle(b.backColor,b.foreColor,f.Cl(b.font||f.Bl()))),c&&e&&e.updateLocationAndSize(c.x,c.y,c.width,c.height))},b.prototype.format=function(a,b,c,d){var e,g;return h(a)?"":(e=this,g=e.getItemValue(a),f.prototype.format.call(e,g||a,b,c))},b.prototype.getItemValue=function(a){var b,c,d,e,f,g=this,h=g._editorValueType,i=g._items;if(i)if(d=i.length,1===h){if(e=s(a),0<=e&&e<d&&(b=i[e],b!==q&&b!==p))return c=oa(b,T)?b.text:b}else if(2===h)for(f=0;f<d;f++)if(b=i[f],b&&oa(b,U)&&b.value===a)return c=b.text},b.prototype.parse=function(a,b,c){var d,e,g,h=this,i=h._editorValueType,j=h._items,k=f.prototype.parse.call(h,a,b);if(j){if(g=j.length,0===i)return k;if(1===i){for(e=0;e<g;e++)if(d=j[e],d&&oa(d,T)&&d.text===k||d===k)return e}else if(2===i)for(e=0;e<g;e++)if(d=j[e],d&&oa(d,T)&&d.text===k)return d.value}return k},b.prototype.getHitInfo=function(a,b,c,d,e){var f,g,i,j;return e?(f=e.sheetArea,g=e.sheet,(h(f)||3===f)&&d&&(i=d.x+d.width,j={x:a,y:b,row:e.row,col:e.col,cellStyle:c,cellRect:d,sheetArea:f,sheet:g},i-Y<=a&&a<i)?(j.isReservedLocation=!0,j):p):p},b.prototype.processMouseDown=function(a){var b,c=a.sheet,d=a.sheetArea;(h(d)||3===d)&&a.isReservedLocation&&c&&(c.startEdit(),b=ma(c.BF),b&&(i.Ml.mozilla&&(b.JK=(new Date).valueOf()),b.showDropDownList(),b.IK=!0))},b.prototype.getAutoFitWidth=function(a,b,c,d,f){c&&c.wordWrap&&(c.wordWrap=!1);var g=e.CellTypes.Context.cp(a,b,c,d,f);return g+Y},b.prototype.getAutoFitHeight=function(a,b,c,d,f){return c&&c.wordWrap&&(c.wordWrap=!1),e.CellTypes.Context.ep(a,b,c,d,f)},b.prototype.isImeAware=function(a){return!0},b.prototype.gQa=function(){},b.prototype.toJSON=function(){var a,b=this,c={typeName:b.typeName};return l(ga,function(d,e){a=b["_"+d];var f="items"===d?a&&a.length>0:a!==e;f&&(c[d]=a)}),c},b.prototype.fromJSON=function(a){var b=this;l(ga,function(c){var d=a[c];h(d)||(b["_"+c]=d)})},b}(f),b.ComboBox=ia;function oa(a,b){return a.hasOwnProperty(b)}l(ga,function(a){ia.prototype[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}}),e.CellTypes._o[7]=ia},"./dist/plugins/celltype/hyperlinkcelltype.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=e.CellTypes.Base,g=e.Ul.Nl,h=d.Common.j.Fa,i=e.Ul.fp,j=e.Ul.Ml,k=j.mozilla,l=e.GC$,m=l.each,n=null,o=Math.max,p=Math.min,q=Math.floor,r=window.open,s="position",t="absolute",u="margin",v="font",w="left",x="top",y="padding",z="border",A="box-sizing",B="background-color",C="content-box",D="default",E="white",F="div",G="hyperlinkInfo";function L(a,b){a.removeChild(b)}!function(a){a[a.blank=0]="blank",a[a.self=1]="self",a[a.parent=2]="parent",a[a.top=3]="top"}(H=b.HyperLinkTargetType||(b.HyperLinkTargetType={})),I={linkColor:"#0066cc",visitedLinkColor:"#3399ff",text:"",linkToolTip:"",target:0,activeOnClick:!0},J=function(a){K(b,a);function b(){var b,c=a.call(this)||this;return c.typeName="8",b=c,c.yn=c.wK(),m(I,function(a,c){b["_"+a]=c}),c._onAction=function(a){var b=a&&a.sheet,c=a.row,d=a.col,e=a.sheetArea,f=b.getValue(c,d,e),g=M(this._target);f&&r(f,g)},c}return b.prototype.wK=function(){var a,b;return h(this.yn)&&(this.yn="0"),a=parseInt(this.yn,10),b="id_"+a,a++,this.yn=a+"",b},b.prototype.Eka=function(a,b,c,d,e,f,g,h,i,l){var m,n,o,p,r,s,t,u,v,x=1,y=w,z=g.hAlign,A=g.vAlign,B=h.lineHeight,C=i.length;for(x+=b,1===z?(x=e/2,y="center"):2===z&&(x=e-1,x-=b,y="right"),a.textAlign!==y&&(a.textAlign=y),m=1,n="alphabetic",o=parseInt(h.fontInfo.fontSize,10),p=o>8?q((o-8)/5+2):1,r=B/2-o/2+p-1,m+=B-r,1===A?B<f&&(m=k?(f-l)/2+1:j.msie?(f-l)/2+.5:(f-l)/2,q(m)!==m&&(m+=.5),m+=B/2-r):2===A&&(m=f-l-2.5-r),a.textBaseline!==n&&(a.textBaseline=n),s=d+m,t=0;t<C;t++)a.fillText(i[t],c+x,s),u=a.measureText(i[t]).width,v=g.textDecoration,v&&this.ip(a,v,c+x,s,u,o,p),s+=B},b.prototype.Fka=function(a,b,c,d,f,g,h,i,j){var k,l,m,n,p,r,s,t=h.hAlign,u=h.vAlign,v=i.lineHeight,w=j.length,x=0,y=0,z=0,A=0;for(2!==t&&(z=1),2===h.vAlign&&(A=-2.5),A-=o(0,Math.round(v/9)-1),a.strokeStyle!==a.fillStyle&&(a.strokeStyle=a.fillStyle),p=i.fontInfo.fontSize,r=q((p-12)/21+1),a.lineWidth=r,a.beginPath(),s=0;s<w;s++)k=a.measureText(j[s]).width,n=N(new e.Rect(c,d,f,g),k,v*w,t,u,b),l=z+c+n.x,m=l+k,x=A+d+n.y+v,q(x)===x&&(x+=.5),y=x,a.moveTo(l,x),a.lineTo(m,y),a.stroke(),A+=v},b.prototype.paintValue=function(a,b,c,d,f,g,i,j){var k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C=this;if(a&&(k=C.getText(b,j),!h(k))){for(k+="",l=k.split(/\r\n|\r|\n/),m=0;m<l.length;)l[m]=l[m].replace(/\s+/g," "),m++;k=l.join("\r\n"),n=!1,o=j.sheet.ITa,p=j.row,q=j.col,r=j.sheetArea,s=o.getValueForKey(p,q,G,r),s&&(C.yn===s.id?n=s.visited:o.do("setValueForKey",p,q,G,void 0,r)),a.save(),a.beginPath(),t=n?C._visitedLinkColor:C._linkColor,t&&a.fillStyle!==t&&(a.fillStyle=t),u=i.font,u&&a.font!==u&&e.Ul.lZa(a,u),v=S(i),j.sheet.outlineColumn&&j.sheet.outlineColumn.XQa(j.col)&&(v=0),w=i.wordWrap,x=[],y=0,z=0,w?(A=f-3-v,A-=1,x=e.Vn.Wn(k,A,u),y=x.length,y>1&&0!==i.vAlign&&(B=j.lineHeight,z=(y-1)*B)):x.push(k),a.rect(c,d,f,g),a.clip(),a.beginPath(),C.Eka(a,v,c,d,f,g,i,j,x,z),C.Fka(a,v,c,d,f,g,i,j,x),
a.restore()}},b.prototype.getText=function(a,b){return this._text||a},b.prototype.tw=function(a,b,c,d){if(!a.HF){var e=a.parent;e&&e.tw(a,b,c,d)}},b.prototype.getHitInfo=function(a,b,c,d,e){var f,g=this;return e&&(f=e.sheetArea,(h(f)||3===f)&&c&&d)?{x:a,y:b,row:e.row,col:e.col,cellStyle:c,cellRect:d,sheetArea:f,isFocusAware:!0,sheet:e.sheet,isReservedLocation:R(a,b,c,d,e,g._text)}:n},b.prototype.processMouseDown=function(a){var b=a&&a.sheet,c=this;b&&!b.isEditing()&&a.isReservedLocation&&(c.AK=!0)},b.prototype.processMouseUp=function(a){var b,c=a&&a.sheet;c&&!c.isEditing()&&(b=this,a.isReservedLocation&&b.AK&&b.Eia(a),b.AK=!1)},b.prototype.processMouseMove=function(a){var b,c=a.row,d=a.col,e=a.sheet,f=this;!e||e.isEditing()&&e.getActiveRowIndex()===c&&e.getActiveColumnIndex()===d||(b=e.Ws(),a.isReservedLocation?b&&(f.BK(e,a),b.style.cursor="pointer"):(f.CK(e),b&&(b.style.cursor=D)))},b.prototype.processMouseLeave=function(a){var b,c=a.sheet,d=this;d.AK=!1,d.CK(c),c&&(b=c.Ws(),b&&(b.style.cursor=D))},b.prototype.BK=function(a,b){var c,d,e,f,g,h,i=this;i._linkToolTip&&(c=i.DK(),d=l(c),d.text(i._linkToolTip),e=a.mm.bG(),f=e.left+b.x,g=e.top+b.y+20,0===d.parent().length&&(h=a&&a.zo(),h&&h.insertBefore(c,n),d.css(x,g).css(w,f)))},b.prototype.CK=function(a){var b,c=this;c.EK&&(b=a&&a.zo(),b&&c.EK.parentElement===b&&L(b,c.EK),c.EK=n)},b.prototype.DK=function(){var a,b=this;return b.EK||(a=g(F),l(a).css(s,t).css(u,0).css(y,2).css(z,"1px #c0c0c0 solid").css("box-shadow","1px 2px 5px rgba(0,0,0,0.4)").css(A,C).css(B,E).css(v,"9pt Arial"),b.EK=a),b.EK},b.prototype.isReservedKey=function(a,b){return 32===a.keyCode&&!a.ctrlKey&&!a.shiftKey&&!a.altKey},b.prototype.processKeyUp=function(a,b){var c=b.sheet,d=this;return!!c&&(d.Eia(b),!0)},b.prototype.getAutoFitWidth=function(a,b,c,d,e){return T(this._text||a,c,d,e,!0)},b.prototype.getAutoFitHeight=function(a,b,c,d,e){return T(this._text||a,c,d,e)},b.prototype.isImeAware=function(a){return!1},b.prototype.Eia=function(a){var b=a&&a.sheet,c=this,d=a.row,e=a.col,f=a.sheetArea,g=c.onClickAction();g.call(this,a),b.ITa.do("setValueForKey",d,e,G,{id:c.yn,visited:!0},f),b.repaint(b.getCellRect(d,e)),c.tw(b,d,e,f)},b.prototype.onClickAction=function(a){return 0===arguments.length?this._onAction:(a&&(this._onAction=a),this)},b.prototype.toJSON=function(){var a,b=this,c={typeName:b.typeName};return m(I,function(d,e){a=b["_"+d],a!==e&&(c[d]=a)}),c},b.prototype.fromJSON=function(a){var b=this;m(I,function(c){var d=a[c];h(d)||(b["_"+c]=d)})},b.prototype.a5=function(a){},b.prototype.gQa=function(){var a,c=this,d=new b;return m(I,function(b,e){a=c["_"+b],a!==e&&(d["_"+b]=a)}),d._onAction=c._onAction,d},b}(f),b.HyperLink=J;function M(a){return["_blank","_self","_parent","_top"][a]}function N(a,b,c,d,e,f){var g=0,h=0,i=a.width,j=a.height;return g=f=f||0,1===d?g=(i-b)/2:2===d&&(g=i-b-f),1===e?h=(j-c)/2:2===e&&(h=j-c),{x:g,y:h}}function O(a,b,c,d){var e=Q(a,c),f=e.width;return f+=(1!==b&&!h(d))==!0?d:0,e.width=f,e}function P(a,b,c,d,e,f){var g=Q(a,d),h=N(e,g.width,g.height,b,c,f);return g.x=h.x,g.y=h.y,g}function Q(a,b){var c=i(b),d=Math.max(0,e.Vn.Xn(a,b,!0)),f=a.split(/\r\n|\r|\n/).length*c;return{x:0,y:0,width:d,height:f}}function R(a,b,c,d,f,g){var i,j,k,l,m,n,o,q,r,s,t,u=f.sheet,v=u.yl;if(h(c))return!1;if(g||(i=u.getValue(f.row,f.col,f.sheetArea),h(i)||(g=i+"")),!g)return!1;for(g=g.replace(/\s+/g," "),j=c&&c.font?c.font:v.Bl(),u.zoom()>1&&(j=v.Cl(j)),k=[g],l=S(c),c.wordWrap&&(k=e.Vn.Wn(g,d.width-3-l,j),g=k.join("\r\n")),m=P(g,c.hAlign,c.vAlign,j,d,l),n=d.y+m.y,o=0;o<k.length;o++)if(q=P(k[o],c.hAlign,c.vAlign,j,d,l),r=d.x+q.x,s=p(r+q.width,d.x+d.width),t=n,n=p(t+q.height,d.y+d.height),r<=a&&a<s&&t<=b&&b<n)return!0;return!1}function S(a){return 8*(a.textIndent||0)}function T(a,b,c,d,f){var g,h,i,j,k,l,m,n,o,p,q,r,s,t=d&&d.sheet;if(t){if(g=t.yl,h=b.font||g.Bl(),c>1&&(h=g.Cl(h)),i=S(b),b.wordWrap||f){if(b.wordWrap){if(j=d.row,k=d.col,l=d.sheetArea,m=0,n=t.ITa.findSpan(j,k,l)){if(n.row>=j&&n.rowCount<=1&&n.col>=k&&k===n.col&&(m=t.Tl(k),n.colCount>1))for(o=k+1;o<k+n.colCount;o++)m+=t.Tl(o)}else m=t.Tl(k);if(p=e.Vn.Wn(a,m-3-i,h),f&&a.split(/\r\n|\r|\n/).length>1){for(q=0,r=0;r<p.length;r++)q=Math.max(q,O(p[r],b.hAlign,h,S(b)).width);return q}f||(a=p.join("\r\n"))}}else a=a.replace(/\s+/g," ");return s=O(a,b.hAlign,h,S(b)),f?s.width:s.height}return 0}m(I,function(a){J.prototype[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}}),e.CellTypes._o[8]=J},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});