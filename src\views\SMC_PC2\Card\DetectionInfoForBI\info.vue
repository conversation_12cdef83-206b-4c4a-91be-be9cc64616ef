<template>
  <div
    class="CardDetectionInfo"
    :class="[this.skinStyle ? this.skinStyle() : '']"
  >
    <div class="drawerTitle" slot="title">
      <div class="_left">
        <div class="index-info _flex" v-if="true">
          <div class="title">
            {{ cardItem.wdInCardName ? cardItem.wdInCardName + " - " : ""
            }}{{ cardItem.indexName }}
          </div>
          <div
            class="plate tag"
            :style="{
              backgroundColor: this.businessSegmentsColorMap
                ? this.businessSegmentsColorMap()[cardItem.businessSegments]
                    ?.bgColor
                : '',
              color: this.businessSegmentsColorMap
                ? this.businessSegmentsColorMap()[cardItem.businessSegments]
                    ?.color
                : '',
            }"
          >
            {{ cardItem.businessSegments }}
          </div>
          <div class="wd tag" v-for="item in cardItem.wdInCardTag" :key="item">
            {{ item }}
          </div>
        </div>
        <!-- <a-button
          type="primary"
          size="small"
          style="margin-left: 10px;"
          @click="close"
        >
          <a-icon type="left" />返回
        </a-button> -->
      </div>
      <!-- 时间select -->
      <div class="time _flex">
        <template>
          <a-radio-group
            class="timetyep-radio-group"
            v-model="searchForm.timeType"
            button-style="solid"
            @change="timeTypeChange"
          >
            <a-radio-button
              :value="item.key"
              v-for="item in timeTypeOptions.filter(
                (item) =>
                  item.key.substring(0, 1).toUpperCase() ===
                  this.cmimId.split('-')[2]
              )"
              :key="item.key"
            >
              {{ item.value }}
            </a-radio-button>
          </a-radio-group>
        </template>

        <!-- 月选择器 -->
        <a-month-picker
          :allowClear="false"
          v-model="searchForm.time"
          v-if="searchForm.timeType === 'month'"
          :format="monthFormat"
        />
        <!-- 周选择器 -->
        <a-select
          show-search
          placeholder="选择年"
          style="width: 90px;margin-right: 4px;"
          :filter-option="filterOption"
          v-model="selectedYearWeek[0]"
          v-if="searchForm.timeType === 'week'"
          @change="yearWeekChange"
        >
          <a-select-option
            :value="item"
            v-for="item in yearSelectOptions"
            :key="item"
          >
            {{ item }}年
          </a-select-option>
        </a-select>
        <a-select
          show-search
          placeholder="选择周"
          style="width: 90px"
          :filter-option="filterOption"
          v-model="selectedYearWeek[1]"
          v-if="searchForm.timeType === 'week'"
          @change="yearWeekChange"
        >
          <a-select-option
            :value="item"
            v-for="item in weekSelectOptions"
            :key="item"
          >
            {{ item }}周
          </a-select-option>
        </a-select>
        <!-- 日选择器 -->
        <a-date-picker
          :allowClear="false"
          v-model="searchForm.time"
          v-if="searchForm.timeType === 'day'"
          :format="dateFormat"
        />
      </div>
    </div>

    <div class="bottom-container" id="bottom-container">
      <!-- 左边部分 -->
      <div class="left-part" :class="chartShowControl ? 'open' : 'close'">
        <a-button
          type="primary"
          class="switchTable-btn"
          size="small"
          @click="$refs['modal'].showModal()"
        >
          <a-icon type="swap" />切换表格
        </a-button>
        <div class="__c" :style="{ zoom: zoom }" :ref="cardItem.pj || 'x00001'">
          <template v-if="Object.keys(datas).length">
            <vue2-org-tree
              ref="leftpartdiv"
              :data="datas"
              :horizontal="horizontal"
              :collapsable="collapsable"
              :label-class-name="labelClassName"
              :render-content="renderContent"
              @on-expand="onExpand"
              name="organ"
            />
          </template>
          <template v-else>
            <a-spin :spinning="dataLoading">
              <div style="height: calc(100% - 137px);width: 100%;"></div>
            </a-spin>
          </template>
        </div>
        <div class="operation-area">
          {{ (zoom * 100).toFixed(0) }}%
          <a-tooltip placement="left">
            <template slot="title">
              <span>放大</span>
            </template>
            <a-button icon="plus" @click="zoomOut"></a-button>
          </a-tooltip>
          <a-tooltip placement="left">
            <template slot="title">
              <span>缩小</span>
            </template>
            <a-button icon="minus" @click="zoomIn"></a-button>
          </a-tooltip>
          <a-tooltip placement="left">
            <template slot="title">
              <span>还原</span>
            </template>
            <a-button icon="undo" @click="zoom = 1"></a-button>
          </a-tooltip>
        </div>
        <!-- <template v-if="activeIndexId">
          <div class="tip-area">
            <div>
              <div class="color active"></div>
              已选中
            </div>
            <div>
              <div class="color parent"></div>
              父级
            </div>
            <div>
              <div class="color slide"></div>
              同层级
            </div>
            <div>
              <div class="color child"></div>
              子级
            </div>
          </div>
        </template> -->
      </div>
      <!-- 右边部分 -->
      <div class="right-part" :class="chartShowControl ? 'open' : 'close'">
        <div class="control" @click="controlClick">
          <a-icon type="right" />
        </div>
        <div class="container">
          <!-- 本层级完成情况 -->
          <div
            style="width: 100%;height: auto;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;position: relative;"
          >
            <MDYSModal
              :signOrgId="signOrgId"
              :pageClass="pageClass"
              :companyName="companyName"
              ref="MDYSModal"
            />
          </div>
          <div
            style="width: 100%;height: 300px;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;position: relative;"
          >
            <a-spin :spinning="complateChartLoading">
              <div ref="complateDiv" style="width: 100%;height: 300px;"></div>
            </a-spin>
            <a-select
              @change="complateChartSelectChange"
              v-model="complateChartSelect"
              :maxTagCount="2"
              :maxTagTextLength="5"
              mode="multiple"
              style="position: absolute;right: 200px;top: 15px;width: 300px;"
            >
              <a-select-option
                :value="item"
                v-for="item in complateChartSelectOptions"
                :key="item"
                :title="item"
                >{{ item }}</a-select-option
              >
            </a-select>
          </div>
          <a-spin :spinning="compareChartLoading">
            <div
              ref="compareDiv"
              style="width: 100%;height: 300px;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;"
            ></div>
          </a-spin>
          <div
            style="width: 100%;height: 300px;padding: 10px 20px;box-sizing: border-box;border-bottom: 1px solid #efefef;position: relative;"
          >
            <a-spin :spinning="wdChartLoading">
              <div ref="wdDiv" style="width: 100%;height: 300px;"></div>
            </a-spin>
            <a-select
              @change="wdChartSelectChange"
              v-model="wdChartSelect"
              style="position: absolute;right: 20px;top: 8px;width: 130px;"
            >
              <a-select-option
                :value="item"
                v-for="item in activeIndexItem.goDown"
                :key="item"
                :title="item"
                >{{ item }}</a-select-option
              >
            </a-select>
          </div>
        </div>
      </div>
    </div>
    <!-- 维度下探 -->
    <a-drawer
      title="维度下探"
      placement="right"
      :closable="true"
      :visible="visible"
      width="315px"
      @close="visible = false"
    >
      <!-- <template v-for="item in wdXTList">
        {{ renderContent(h, item) }}
      </template> -->
      <div
        class="card-in-detection-block"
        v-for="item in wdXTList"
        :key="item.indexId"
        style="margin-bottom: 10px"
      >
        <div class="card">
          <div class="top">
            <div class="_t">{{ item.wd }}</div>
          </div>
          <template v-if="item.isDelete === null || item.isDelete === 'Y'">
            <div
              class="sjz"
              :style="item.isDelete ? 'color: #5f8cc0;' : 'color: #a5a5a5;'"
            >
              不计算
            </div>
          </template>
          <template v-else>
            <div
              class="sjz"
              :style="{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                color: item.targetCompletionRate
                  ? parseFloat(item.targetCompletionRate) < 100
                    ? 'rgba(52, 145, 250, 1)'
                    : 'rgb(245, 63, 63)'
                  : '',
              }"
            >
              {{ item.actualValue }}
              {{ item.indexUnitId }}
            </div>
          </template>
          <div class="intro">
            <div>目标值：{{ item.targetValue }} {{ item.indexUnitId }}</div>
            <div>完成率：{{ item.targetCompletionRate }}</div>
          </div>
          <div class="compare _flex">
            <a-row style="width: 100%;">
              <a-col :span="12">
                <div class="_flex _c">
                  <span>同比</span>
                  <template v-if="item.isContemRate === 'Y'">
                    <div
                      :style="{
                        color: item.contemChangeRate
                          ? item.indexType === '正向'
                            ? item.contemChangeRate.includes('-')
                              ? '#6495F9'
                              : '#f75050'
                            : item.contemChangeRate.includes('-')
                            ? '#f75050'
                            : '#6495F9'
                          : 'rgba(0, 0, 0, 0.8)',
                      }"
                      class="_flex"
                    >
                      <a-icon
                        v-if="item.contemChangeRate"
                        :type="
                          item.indexType === '正向'
                            ? item.contemChangeRate.includes('-')
                              ? 'caret-down'
                              : 'caret-up'
                            : item.contemChangeRate.includes('-')
                            ? 'caret-up'
                            : 'caret-down'
                        "
                      />
                      <span>
                        {{
                          item.contemChangeRate
                            ? Math.abs(
                                Decimal(item.contemChangeRate)
                                  .mul(Decimal(100))
                                  .toFixed(2, Decimal.ROUND_HALF_UP)
                              ) + "%"
                            : "-"
                        }}
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <span
                      style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                    >
                      不对比
                    </span>
                  </template>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="_flex _c">
                  <span>同比</span>
                  <template v-if="item.isPreviousRate === 'Y'">
                    <div
                      :style="{
                        color: item.previousChangeRate
                          ? item.indexType === '正向'
                            ? item.previousChangeRate.includes('-')
                              ? '#6495F9'
                              : '#f75050'
                            : item.previousChangeRate.includes('-')
                            ? '#f75050'
                            : '#6495F9'
                          : 'rgba(0, 0, 0, 0.8)',
                      }"
                      class="_flex"
                    >
                      <a-icon
                        v-if="item.previousChangeRate"
                        :type="
                          item.indexType === '正向'
                            ? item.previousChangeRate.includes('-')
                              ? 'caret-down'
                              : 'caret-up'
                            : item.previousChangeRate.includes('-')
                            ? 'caret-up'
                            : 'caret-down'
                        "
                      />
                      <span>
                        {{
                          item.previousChangeRate
                            ? Math.abs(
                                Decimal(item.previousChangeRate)
                                  .mul(Decimal(100))
                                  .toFixed(2, Decimal.ROUND_HALF_UP)
                              ) + "%"
                            : "-"
                        }}
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <span
                      style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                    >
                      不对比
                    </span>
                  </template>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </a-drawer>
    <Modal ref="modal" :tableData="tableData" :companyName="companyName" />
  </div>
</template>
<script>
import MDYSModal from "./mdys-modal.vue";
import request from "@/utils/requestHttp";
import * as echarts from "echarts";
// import axios from "axios";
import cloneDeep from "lodash/cloneDeep";
import { dealThousandData, dealCardOriginData } from "../../utils";
import Vue2OrgTree from "vue2-org-tree";
import Decimal from "decimal.js";
import sortBy from "lodash/sortBy";
import Modal from "./modal.vue";
import moment from "moment";
import { findNode, getWeek } from "../../utils";
// import html2Canvas from "html2canvas";
// import JsPDF from "jspdf";

export default {
  name: "IndexCardDetailInfo",
  props: {
    pageClass: String,
    signOrgId: String,
    companyName: String,
    // dataItem: Object,
    sysSign: {
      // 是不是云图自己使用
      type: String,
      default: "smc",
    },
  },
  components: { Vue2OrgTree, Modal, MDYSModal },
  inject: ["businessSegmentsColorMap", "skinStyle"],
  data() {
    return {
      dataItem: {},
      moment,
      chartShowControl: true, // 右边可折叠开关
      Decimal,
      companySign: "", // 公司
      activeIndexId: "", // 点击的某组织id
      activeIndexItem: {}, // 点击的某组织数据
      cardItem: {},
      visible: false,
      SYS_NAME: window.system,
      horizontal: true, //横版 竖版
      collapsable: true,
      expandAll: true, //是否全部展开
      labelClassName: "白色", // 默认颜色
      datas: {},
      wdXTList: [], // 维度下探列表
      dataLoading: false,
      zoom: 1, // 组织树缩放
      // 下方为三个图表的相关数据
      complateChart: null,
      compareChart: null,
      wdChart: null,
      complateChartLoading: false,
      compareChartLoading: false,
      wdChartLoading: false,
      complateChartOptions: {
        title: {
          text: "完成情况",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24,
          },
          padding: 16,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        legend: {
          data: ["实际", "目标", "完成率"],
          top: 19,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt",
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0,
          },
          padding: 0,
          itemGap: 12.5,
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisPointer: {
              type: "shadow",
            },
            axisTick: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter: "{value}",
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            },
          },
          {
            type: "value",
            axisLabel: {
              formatter: "{value} %",
            },
            min: function(value) {
              return value.min;
            },
            max: function(value) {
              return value.max;
            },
          },
        ],
        series: [
          {
            name: "实际",
            type: "bar",
            data: [],
            itemStyle: {
              color: "#00AAA6",
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)",
            },
            barMaxWidth: 33,
          },
          {
            name: "目标",
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgb(255, 192, 0)",
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)",
            },
            barMaxWidth: 33,
          },
          {
            name: "完成率",
            type: "line",
            yAxisIndex: 1,
            data: [],
            symbol: "none",
            itemStyle: {
              color: "rgba(214, 116, 255, 1)",
            },
          },
        ],
      },
      complateChartOriginData: [],
      cloneComplateChartOptions: {},
      wdChartOptions: {
        title: {
          text: "维度对比",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24,
          },
          padding: 16,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        // legend: {
        //   data: ["目标"],
        //   top: 19,
        //   right: 20,
        //   itemWidth: 8,
        //   itemHeight: 8,
        //   itemStyle: {
        //     borderCap: "butt"
        //   },
        //   textStyle: {
        //     color: "#8C8C8C",
        //     fontSize: 12,
        //     lineHeight: 17,
        //     height: 17,
        //     borderRadius: 0
        //   },
        //   padding: 0,
        //   itemGap: 12.5
        // },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: [],
          axisPointer: {
            type: "shadow",
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisTick: {
            show: false,
          },
          axisLabel: {
            formatter: "{value}",
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          },
        },
        series: [
          {
            type: "bar",
            data: [],
            itemStyle: {
              color: "rgba(21, 219, 195, 0.85)",
            },
            label: {
              show: true,
              position: "top",
              color: "rgba(89, 89, 89, 1)",
            },
            barMaxWidth: 33,
          },
        ],
      },
      wdChartSelect: "",
      cloneWdChartOptions: {},
      compareChartOptions: {
        title: {
          text: "同期&实际走势",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24,
          },
          padding: 16,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        legend: {
          data: ["同期", "实际", "目标"],
          top: 19,
          right: 20,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt",
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0,
          },
          padding: 0,
          itemGap: 12.5,
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true,
          },
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            xAxisIndex: [0],
            height: 16,
          },
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            yAxisIndex: [0],
            width: 16,
            orient: "vertical",
          },
        ],
        yAxis: {
          type: "value",
          axisTick: {
            show: false,
          },
          axisLabel: {
            formatter: "{value}",
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          },
        },
        series: [
          {
            name: "同期",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(141, 180, 226)",
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true,
          },
          {
            name: "实际",
            type: "line",
            data: [],
            itemStyle: {
              color: "#00AAA6",
            },
            label: {
              show: true,
              position: "top",
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true,
          },
          {
            name: "目标",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(255, 192, 0)",
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true,
          },
        ],
      },
      complateChartSelect: [],
      cloneCompareChartOptions: {},
      ppResData: [], // 平铺后的查询结果，用来表格展示
      searchForm: {
        // 页面中查询条件
        orgSign: "", // 组织
        mode: "chart", // 模式
        timeType: "month", // 时间类型
        time: moment(new Date().getTime(), this.monthFormat), // 时间
      },
      yearSelectOptions: [], // 周模式下年下拉框
      weekSelectOptions: [], // 周模式下周下拉框
      selectedYearWeek: ["", ""], // 周模式下选中的年周
      selectedYearWeekWatchFlag: false,
      dateFormat: "YYYY-MM-DD", // 日期、周格式化
      monthFormat: "YYYY-MM", // 月格式化
      weekFormat: "YYYY-ww", // 周格式化
      saveLocalSearchForm: true, // 搜索表条本地存储
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: "month",
          value: "按月",
        },
        {
          key: "week",
          value: "按周",
        },
        {
          key: "day",
          value: "按日",
        },
      ],
      getBaseDataFlagSuccess: true, //有值是true
    };
  },
  watch: {
    dataItem: {
      handler(val) {
        // eslint-disable-next-line no-prototype-builtins
        if (val.hasOwnProperty("indexId")) {
          if (this.sysSign !== "smc") {
            console.log(
              dealCardOriginData(
                {
                  ...val,
                  companyName: this.companyName,
                  recommend: false,
                },
                "cardList"
              )
            );
          }
          this.cardItem =
            this.sysSign === "smc"
              ? val
              : dealCardOriginData(
                  {
                    ...val,
                    companyName: this.companyName,
                    recommend: false,
                  },
                  "cardList"
                );
          this.dataLoading = true;
          console.log(this.cardItem, "cardItem");
          // this.getDetectionList();
        } else {
          // 重置数据
          this.cardItem = {};
        }
      },
      immediate: true,
      deep: true,
    },
    // 监听searchForm值改变
    searchForm: {
      handler(n, v) {
        console.log(this.searchForm, "changeWatch");
        // 保存搜索条件到本地
        this.debounce(this.sendRequest());
        // localStorage.setItem(
        //   `${this.signOrgId}-searchForm`,
        //   JSON.stringify(this.searchForm)
        // );
      },
      deep: true,
    },
    // 监听周模式下的selectedYearWeek值改变
    // selectedYearWeek(val) {
    //   console.log(1111);
    //   // 根据选中年份去动态生成周下拉框
    //   if (this.selectedYearWeekWatchFlag) {
    //     this.initWeekSelect(val[0]);
    //     // 如果周下拉框中没有当前已选中的周值，则默认选成第一周
    //     if (!this.weekSelectOptions.includes(val[1])) {
    //       this.selectedYearWeek[1] = this.weekSelectOptions[0];
    //     }
    //     this.searchForm.time = `${val[0]}-${String(val[1]).padStart(2, "0")}`;
    //   }
    // },
  },
  computed: {
    tableData() {
      const arr = [];
      if (Object.keys(this.activeIndexItem).length) {
        // 把同层级的添加到数组中，并把当前选中数据的所有子级添加到数组中
        arr.push(
          ...[
            ...cloneDeep(
              this.ppResData.filter(
                (item) => item.parentSign === this.activeIndexItem.parentSign
              )
            ),
            ...cloneDeep(
              this.extractTree(this.activeIndexItem.children, "children")
            ),
          ]
        );
      }
      arr.forEach((item, index) => {
        // eslint-disable-next-line no-prototype-builtins
        if (item.hasOwnProperty("children")) {
          delete item["children"];
        }
        item["index"] = index + 1;
        const deal100ParamsArr = ["contemChangeRate", "previousChangeRate"];
        // item["signOrg"] = item["signOrg"] || this.companyName;
        deal100ParamsArr.forEach((zitem) => {
          const dataItem = item;
          item[zitem] = dataItem[zitem]
            ? Decimal(dataItem[zitem])
                .mul(Decimal(100))
                .toFixed(2, Decimal.ROUND_HALF_UP) + "%"
            : "";
        });
      });
      return arr;
    },
    complateChartSelectOptions() {
      return this.complateChartOriginData.map((item) => item.org);
    },
  },
  created() {
    // 初始化年周下拉框
    this.initYearSelect();
    this.initWeekSelect();
    // 如果本地搜索条件有存储，获取搜索条件后赋值到searchForm
    // if (localStorage.getItem(`${this.signOrgId}-searchForm`)) {
    //   try {
    //     const { orgSign, timeType, time } = JSON.parse(
    //       localStorage.getItem(`${this.signOrgId}-searchForm`)
    //     );
    //     this.searchForm.orgSign = orgSign;
    //     this.searchForm.timeType = timeType;
    //     this.searchForm.time = time;
    //     if (timeType === "week") {
    //       this.selectedYearWeek[1] = Number(time.split("-")[1] || "01");
    //       this.selectedYearWeek[0] = Number(
    //         time.split("-")[0] || new Date().getFullYear()
    //       );
    //     }
    //     // 修改本地存储标识
    //     this.saveLocalSearchForm = true;
    //   } catch (error) {
    //     console.error(error);
    //   }
    // }
    let urlParams = Object.entries(this.$route.query) || null;
    this.cmimId =
      urlParams[0][1] ||
      "ZBP01058-H0104-M-0000-0000-DIM_959-0000-0000-0000-0000-0000-0000-0000-0000";
    this.urlTime = urlParams[1][1];
  },
  mounted() {
    let body = {};
    body.cmimId = this.cmimId;
    body.indexDt = this.urlTime;
    body.indexDt = this.getBaseData(body);
    // 初始化
    this.complateChart = echarts.init(this.$refs["complateDiv"]);
    this.compareChart = echarts.init(this.$refs["compareDiv"]);
    this.cloneComplateChartOptions = cloneDeep(this.complateChartOptions);
    this.cloneCompareChartOptions = cloneDeep(this.compareChartOptions);
    this.wdChart = echarts.init(this.$refs["wdDiv"]);
    this.cloneWdChartOptions = cloneDeep(this.wdChartOptions);
    setTimeout(() => {
      this.initComplateChart();
      this.initCompareChart();
      this.initWdChart();
      this.complateChart.resize();
      this.compareChart.resize();
      this.wdChart.resize();
    }, 1000);
    window.onresize = () => {
      this.complateChart.resize();
      this.compareChart.resize();
      this.wdChart.resize();
    };
  },

  methods: {
    getBaseData({ cmimId, indexDt }) {
      return request(
        `/api/smc2/newIndexLibrary/bi`,
        {
          method: "POST",
          body: {
            cmimId: cmimId,
            indexDt: indexDt || null,
          },
        },
        {}
      ).then((res) => {
        // 如果首次请求没有数据，那么在次选择时间的时候就需要传递时间
        // 右上角时间同步初始
        if (res) {
          // 如果有值
          this.dataItem = res;
          this.searchForm.time = res.indexDt;
          this.searchForm.timeType = this.getResTimeType(); //这个是展示哪个按钮
          // 周
          if (this.getResTimeType() === "week") {
            this.selectedYearWeek = res.indexDt
              .split("-")
              .map((item) => Number(item));
          } else {
          }
        } else {
          this.getBaseDataFlagSuccess = false;
        }
      });
    },
    yearWeekChange(e) {
      let yearWeekNum = this.selectedYearWeek[0];
      let WeekNum = this.selectedYearWeek[1];

      this.searchForm.time = `${yearWeekNum}-${
        WeekNum <= 10 ? String(WeekNum).padStart(2, "0") : String(WeekNum)
      }`; //拼成字符串
    },
    getResTimeType() {
      console.log(this.dataItem, "dataItem");
      return this.timeTypeOptions.filter(
        (item) =>
          item.key.substring(0, 1).toUpperCase() ===
          this.dataItem.indexFrequencyId
      )[0].key;
    },
    debounce: function(fn, wait = 100) {
      if (this.fun !== null) {
        clearTimeout(this.fun);
      }
      this.fun = setTimeout(fn, wait);
    },
    // 初始化完成情况图表
    initComplateChart(options) {
      this.complateChart.setOption(options || this.complateChartOptions);
    },
    // 初始化同期情况表
    initCompareChart(options) {
      this.compareChart.setOption(options || this.compareChartOptions);
    },
    // 初始化维度对比情况表
    initWdChart(options) {
      this.wdChart.setOption(options || this.wdChartOptions);
    },
    // 树图渲染
    renderContent(h, data) {
      return (
        <div>
          {data.isRoot ? (
            <div>根节点</div>
          ) : (
            <div
              vOn:click_stop_prevent={() => this.cardClick(data)}
              class={["card-in-detection-block"]}
            >
              <div
                class={[
                  "card",
                  data.indexSign === this.activeIndexId
                    ? "active-card"
                    : data.indexSign.includes(this.activeIndexId)
                    ? "child-card"
                    : data.indexSign.split("~!~").length ===
                      this.activeIndexId.split("~!~").length
                    ? "slide-card"
                    : "parent-card",
                ]}
              >
                <div class="top">
                  <div class="_t">{data.org}</div>
                  {data.goDown &&
                    data.goDown.map((item) => {
                      return (
                        <div class="wd-container">
                          <div
                            class="wd-tag"
                            vOn:click_stop_prevent={() =>
                              this.clickWD(data, item)
                            }
                          >
                            {item}
                          </div>
                        </div>
                      );
                    })}
                </div>
                {data.isDelete === null || data.isDelete === "Y" ? (
                  <div
                    class="sjz"
                    style={
                      data.isDelete ? "color: #5f8cc0;" : "color: #a5a5a5;"
                    }
                  >
                    不计算
                  </div>
                ) : (
                  <div
                    class="sjz"
                    style={
                      data.targetCompletionRate
                        ? parseFloat(data.targetCompletionRate) < 100
                          ? "color: rgba(52, 145, 250, 1);"
                          : "color: rgb(245, 63, 63);"
                        : ""
                    }
                  >
                    {data.actualValue}
                    {data.indexUnitId}
                  </div>
                )}

                <div class="intro">
                  <div>
                    目标值：{data.targetValue} {data.indexUnitId}
                  </div>
                  <div>完成率：{data.targetCompletionRate}</div>
                </div>
                <div class="compare _flex">
                  <a-row style="width: 100%;">
                    <a-col span={12}>
                      <div class="_flex _c">
                        <span>同比</span>
                        {data.isContemRate === "Y" ? (
                          <div
                            style={
                              data.contemChangeRate
                                ? data.indexType === "正向"
                                  ? data.contemChangeRate.includes("-")
                                    ? "color: #6495F9;"
                                    : "color: #f75050;"
                                  : data.contemChangeRate.includes("-")
                                  ? "color: #f75050;"
                                  : "color: #6495F9;"
                                : "color: rgba(0, 0, 0, 0.8);"
                            }
                            class="_flex"
                          >
                            {data.contemChangeRate ? (
                              <a-icon
                                type={
                                  data.indexType === "正向"
                                    ? data.contemChangeRate.includes("-")
                                      ? "caret-down"
                                      : "caret-up"
                                    : data.contemChangeRate.includes("-")
                                    ? "caret-up"
                                    : "caret-down"
                                }
                              />
                            ) : null}
                            <span>
                              {data.contemChangeRate
                                ? Math.abs(
                                    Decimal(data.contemChangeRate)
                                      .mul(Decimal(100))
                                      .toFixed(2, Decimal.ROUND_HALF_UP)
                                  ) + "%"
                                : "-"}
                            </span>
                          </div>
                        ) : (
                          <span style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;">
                            不对比
                          </span>
                        )}
                      </div>
                    </a-col>
                    <a-col span={12}>
                      <div class="_flex _c">
                        <span>环比</span>
                        {data.isPreviousRate === "Y" ? (
                          <div
                            style={
                              data.previousChangeRate
                                ? data.indexType === "正向"
                                  ? data.previousChangeRate.includes("-")
                                    ? "color: #6495F9;"
                                    : "color: #f75050;"
                                  : data.previousChangeRate.includes("-")
                                  ? "color: #f75050;"
                                  : "color: #6495F9;"
                                : "color: rgba(0, 0, 0, 0.8);"
                            }
                            class="_flex"
                          >
                            {data.previousChangeRate ? (
                              <a-icon
                                type={
                                  data.indexType === "正向"
                                    ? data.previousChangeRate.includes("-")
                                      ? "caret-down"
                                      : "caret-up"
                                    : data.previousChangeRate.includes("-")
                                    ? "caret-up"
                                    : "caret-down"
                                }
                              />
                            ) : null}
                            <span>
                              {data.previousChangeRate
                                ? Math.abs(
                                    Decimal(data.previousChangeRate)
                                      .mul(Decimal(100))
                                      .toFixed(2, Decimal.ROUND_HALF_UP)
                                  ) + "%"
                                : "-"}
                            </span>
                          </div>
                        ) : (
                          <span style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;">
                            不对比
                          </span>
                        )}
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>
            </div>
          )}
        </div>
      );
    },
    // 开关点击
    controlClick() {
      this.chartShowControl = !this.chartShowControl;
      if (this.chartShowControl) {
        this.$nextTick(() => {
          if (this.$refs["leftpartdiv"]) {
            setTimeout(() => {
              this.$refs["leftpartdiv"].$el.scrollTo(
                (this.activeIndexItem.levelIndex - 1) * 240,
                this.$refs["leftpartdiv"].$el.scrollTop
              );
            }, 800);
          }
        });
      }
    },
    // 卡片点击
    cardClick(indexItem) {
      if (indexItem.isDelete === "Y" || indexItem.isDelete === null) {
        return;
      }
      // 卡片点击的时候永远展开该子级
      this.onExpand(null, indexItem, true);
    },
    // 维度点击
    clickWD(indexItem, wd) {
      this.getDetectionWDList(indexItem.groupId, wd);
    },
    // onlyExpand 只展开操作，在cardClick方法内使用
    onExpand(e, data, onlyExpand = false) {
      if ("expand" in data) {
        data.expand = onlyExpand || data.isRoot ? true : !data.expand;
        if (!data.expand && data.children) {
          this.collapse(data.children);
        }
        if (data.expand && onlyExpand) {
          // 设置位移且获取右侧数据
          this.activeIndexId = data.indexSign;
          this.activeIndexItem = data;
          this.wdChartSelect = "";
          this.$nextTick(() => {
            if (this.$refs["leftpartdiv"]) {
              setTimeout(() => {
                this.getEachrtsDatas();
                this.initMDYSDatas();
                this.$refs["leftpartdiv"].$el.scrollTo(
                  (data.levelIndex - 1) * 240,
                  this.$refs["leftpartdiv"].$el.scrollTop
                );
              }, 800);
            }
          });
        }
      } else {
        this.$set(data, "expand", true);
      }
    },
    // 获取图表数据
    getEachrtsDatas() {
      this.getTopChartDatas();
      this.getMiddleChartDatas();
      if (this.activeIndexItem.goDown.length) {
        this.wdChartSelectChange(this.activeIndexItem.goDown[0]);
      } else {
        this.wdChartOptions = cloneDeep(this.cloneWdChartOptions);
        this.initWdChart();
      }
    },
    // 初始化末端因素分析数据
    initMDYSDatas() {
      this.$refs["MDYSModal"].init(this.activeIndexItem);
    },
    // 获取顶部图表数据
    getTopChartDatas() {
      this.complateChartLoading = true;
      const indexItem = this.activeIndexItem;
      const postData = {
        indexId: indexItem.indexId,
        businessSegmentsId: indexItem.businessSegmentsId,
        fullCode: indexItem.fullCode,
        indexDt: indexItem.indexDt,
        indexFrequencyId: indexItem.indexFrequencyId,
        cmimId: indexItem.cmimId,
        productAtt1Id: indexItem.productAtt1Id,
        productAtt2Id: indexItem.productAtt2Id,
        productAtt3Id: indexItem.productAtt3Id,
        productAtt4Id: indexItem.productAtt4Id,
        productAtt5Id: indexItem.productAtt5Id,
        productAtt6Id: indexItem.productAtt6Id,
        productAtt7Id: indexItem.productAtt7Id,
        org: indexItem.org,
        businessSegments: indexItem.businessSegments,
        sign: indexItem.sign,
        signOrg: indexItem.signOrg,
        indexName: indexItem.postEndIndexName,
      };
      request(
        `/api/smc2/newIndexLibrary/sameLayerCompleteList?BI=1`,
        {
          method: "POST",
          body: postData,
        },
        {}
      )
        .then((res) => {
          if (Array.isArray(res) && res.length) {
            res = sortBy(res, (item) => item.targetCompletionRate || "0");
            res.reverse();
            this.complateChartOriginData = res;
            this.$nextTick(() => {
              this.complateChartSelectChange(res.map((item) => item.org));
            });
          } else {
            this.complateChartOriginData = [];
            this.complateChartOptions = cloneDeep(
              this.cloneComplateChartOptions
            );
            this.initComplateChart();
          }
          this.complateChartLoading = false;
        })
        .catch(() => {
          this.complateChartOriginData = [];
          this.complateChartOptions = cloneDeep(this.cloneComplateChartOptions);
          this.initComplateChart();
          this.complateChartLoading = false;
        });
    },
    // 获取中间图表数据
    getMiddleChartDatas() {
      this.compareChartLoading = true;
      const indexItem = this.activeIndexItem;
      const {
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id,
      } = this.cardItem;
      const arr = [
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id,
      ];
      arr.forEach((item, index) => {
        arr[index] = item
          ? item.includes("卡片名称") || item.includes("卡片标签")
            ? item
            : null
          : null;
      });
      const postData = {
        signOrgId: indexItem.signOrgId,
        businessSegmentsId: indexItem.businessSegmentsId,
        indexId: indexItem.indexId,
        fullCode: indexItem.fullCode,
        indexDt: indexItem.indexDt,
        indexFrequencyId: indexItem.indexFrequencyId,
        org: indexItem.org,
        businessSegments: indexItem.businessSegments,
        sign: indexItem.indexItem,
        signOrg: indexItem.signOrg,
        indexName: indexItem.postEndIndexName,
        productAtt1Id: arr[0],
        productAtt2Id: arr[1],
        productAtt3Id: arr[2],
        productAtt4Id: arr[3],
        productAtt5Id: arr[4],
        productAtt6Id: arr[5],
        productAtt7Id: arr[6],
      };
      request(
        `/api/smc2/newIndexLibrary/searchTrend?BI=1`,
        {
          method: "POST",
          body: postData,
        },
        {}
      )
        .then((res) => {
          if (Array.isArray(res) && res.length > 0) {
            res = sortBy(res, function(item) {
              return item.indexDt;
            });
            const xAxis = res.map((item) => {
              return item.indexDt;
            });
            this.compareChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map((item) => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.compareChartOptions.series[1].data = baseActualData;
            const targetValueData = res.map((item) => {
              return dealThousandData(
                item.targetValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.compareChartOptions.series[2].data = targetValueData;
            const contemValueData = res.map((item) => {
              return dealThousandData(
                item.contemValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.compareChartOptions.dataZoom[0].end = 100;
            // 根据数据条数 判断是否显示dataZoom组件
            this.compareChartOptions.dataZoom[0].show = true;
            this.compareChartOptions.grid.bottom = 52;
            const yearStart = xAxis.indexOf(`${new Date().getFullYear()}-01`);
            const process =
              yearStart === -1
                ? 0
                : Math.floor((100 / (xAxis.length - 1)) * yearStart);
            this.compareChartOptions.dataZoom[0].start = process;

            this.compareChartOptions.series[0].data = contemValueData;
            this.compareChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].indexUnitId === "null" ? "" : res[0].indexUnitId
            }`;
            this.initCompareChart();
          } else {
            this.compareChartOptions = cloneDeep(this.cloneCompareChartOptions);
            this.initCompareChart();
          }
          this.compareChartLoading = false;
        })
        .catch(() => {
          this.compareChartOptions = cloneDeep(this.cloneCompareChartOptions);
          this.initCompareChart();
          this.compareChartLoading = false;
        });
    },
    // 获取底部图表数据
    getBottomChartDatas() {
      this.wdChartLoading = true;
      const indexItem = this.activeIndexItem;
      const postData = {
        indexId: indexItem.indexId,
        businessSegmentsId: indexItem.businessSegmentsId,
        fullCode: indexItem.fullCode,
        indexDt: indexItem.indexDt,
        indexFrequencyId: indexItem.indexFrequencyId,
        cmimId: indexItem.cmimId,
        productAtt1Id: indexItem.productAtt1Id,
        productAtt2Id: indexItem.productAtt2Id,
        productAtt3Id: indexItem.productAtt3Id,
        productAtt4Id: indexItem.productAtt4Id,
        productAtt5Id: indexItem.productAtt5Id,
        productAtt6Id: indexItem.productAtt6Id,
        productAtt7Id: indexItem.productAtt7Id,
        org: indexItem.org,
        businessSegments: indexItem.businessSegments,
        sign: indexItem.sign,
        signOrg: indexItem.signOrg,
        indexName: indexItem.postEndIndexName,
      };
      request("/api/smc2/newIndexLibrary/dimensionContrast", {
        method: "POST",
        body: postData,
      })
        .then((res) => {
          res = res["维度列表"] || [];
          if (Array.isArray(res) && res.length) {
            const xAxis = res.map((item) => {
              let goDownIndex = 0,
                hasGoDownData = false; // 查找下探标题在返回数据的哪个下标字段中
              for (let index = 1; index <= 7; index++) {
                if (
                  item[`productAtt${index}`] &&
                  item[`productAtt${index}`] !== "-" &&
                  !item[`productAtt${index}`].includes("卡片标签") &&
                  !item[`productAtt${index}`].includes("卡片标签")
                ) {
                  goDownIndex = index;
                  hasGoDownData = true;
                  break;
                }
              }
              return hasGoDownData
                ? item[`productAtt${goDownIndex}`].split(
                    item[`productAtt${goDownIndex}Id`] + "-"
                  )[1]
                : "";
            });
            this.wdChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map((item) => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.wdChartOptions.series[0].data = baseActualData;
            this.wdChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].indexUnitId === null ? "" : res[0].indexUnitId
            }`;
            this.initWdChart();
          } else {
            this.wdChartOptions = cloneDeep(this.cloneWdChartOptions);
            this.initWdChart();
          }
          this.wdChartLoading = false;
        })
        .catch(() => {
          this.wdChartOptions = cloneDeep(this.cloneWdChartOptions);
          this.initWdChart();
          this.wdChartLoading = false;
        });
    },
    // 维度图表下拉框改变
    wdChartSelectChange(value) {
      this.wdChartSelect = value;
      this.getBottomChartDatas();
    },
    // 顶部图表下拉框改变
    complateChartSelectChange(value) {
      this.complateChartSelect = value;
      const res = this.complateChartOriginData.filter((item) =>
        value.includes(item.org)
      );
      if (res.length) {
        const xAxis = res.map((item) => {
          return item.org;
        });
        this.complateChartOptions.xAxis[0].data = xAxis;
        const baseActualData = res.map((item) => {
          return dealThousandData(
            item.actualValue,
            item.indexUnitId,
            item.precisions,
            false
          );
        });
        this.complateChartOptions.series[0].data = baseActualData;
        const targetValueData = res.map((item) => {
          return dealThousandData(
            item.targetValue,
            item.indexUnitId,
            item.precisions,
            false
          );
        });
        this.complateChartOptions.series[1].data = targetValueData;
        const completionRateData = res.map((item) => {
          return item.targetCompletionRate
            ? Decimal(parseFloat(item.targetCompletionRate))
                .mul(Decimal.Decimal(100))
                .toFixed(2, Decimal.ROUND_HALF_UP)
            : "0";
        });
        this.complateChartOptions.series[2].data = completionRateData;
        this.complateChartOptions.yAxis[0].axisLabel.formatter = `{value} ${
          res[0].indexUnitId === null ? "" : res[0].indexUnitId
        }`;
      } else {
        this.complateChartOptions = cloneDeep(this.cloneComplateChartOptions);
      }
      this.initComplateChart();
    },
    // 折叠
    collapse(list) {
      var _this = this;
      list.forEach(function(child) {
        if (child.expand) {
          child.expand = false;
        }
        child.children && _this.collapse(child.children);
      });
    },
    // 关闭抽屉
    close() {
      // this.cardItem = {};
      this.wdChartSelect = "";
      this.chartShowControl = true;
      this.datas = {};
      this.zoom = 1;
      this.complateChartSelect = [];
      this.complateChart.setOption(this.cloneComplateChartOptions, true);
      this.compareChart.setOption(this.cloneCompareChartOptions, true);
      this.wdChart.setOption(this.cloneWdChartOptions, true);
      this.$refs.MDYSModal.close();
    },
    /*
     *  扁平化
     *  bossArr  树形数据
     * */
    extractTree(bossArr, children) {
      //如果为空 返回空（防止 children 递归报错）
      if (!Array.isArray(bossArr) && !bossArr.length) return [];
      let list = [];
      const getObj = (arr) => {
        arr.forEach((row) => {
          let obj = {};
          obj = JSON.parse(JSON.stringify(row));
          list.push(obj);
          if (row[children]) {
            getObj(row[children]);
          }
        });
        return list;
      };
      return getObj(bossArr);
    },
    // 循环处理数据
    loopData(arr, wd, index = 0, parentSign = "") {
      index++;
      return arr.map((item) => {
        const {
          dmId,
          indexId,
          indexName,
          indexDt,
          fullCode,
          levelName,
          parentName,
          indexFrequency,
          indexFrequencyId,
          org,
          orgId,
          businessSegments,
          businessSegmentsId,
          signOrgId,
          signOrg,
          actualValue,
          targetValue,
          targetCompletionRate,
          previousChangeRate,
          contemChangeRate,
          indexUnitId,
          indexSort,
          productAtt1,
          productAtt2,
          productAtt3,
          productAtt4,
          productAtt5,
          productAtt6,
          productAtt7,
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id,
          precisions,
          actualMolecule,
          actualDenominator,
          contemValue,
          previousValue,
          indexTypeId,
          indexNameInd,
          isDelete,
          groupId,
          goDown,
          cmimId,
          pj, // 用于拖拽排序标记
        } = item;
        let indexSign = parentSign
          ? parentSign + "~!~" + `${orgId}`
          : `${orgId}`;
        const normalWDList = [
          productAtt1,
          productAtt2,
          productAtt3,
          productAtt4,
        ].filter((item) => item && !item.includes("指标卡"));
        let wdInCardName = normalWDList
          .filter((item) => item.includes("卡片名称"))
          .map((item) => item.split("-")[2]);
        wdInCardName = wdInCardName.join("-");
        const wdInCardTag = normalWDList
          .filter((item) => item.includes("卡片标签"))
          .map((item) => item.split("-")[2]);
        const wdInXT = [
          productAtt1,
          productAtt2,
          productAtt3,
          productAtt4,
          productAtt5,
          productAtt6,
          productAtt7,
        ].filter(
          (item) =>
            item &&
            !item.includes("卡片名称") &&
            !item.includes("卡片标签") &&
            item !== "-" &&
            item.split("-").length === 2
        );
        const displayIndexName = indexNameInd || indexName;
        let fixedNum =
          typeof precisions === "number"
            ? precisions
            : typeof precisions === "string" && precisions
            ? parseInt(precisions)
            : 2;
        return {
          dmId,
          postEndIndexName:
            (wdInCardName ? wdInCardName + " - " : "") +
            displayIndexName +
            (wdInCardTag.length ? " - " + wdInCardTag.join("-") : ""),
          indexId,
          indexSign,
          indexName,
          indexDt,
          fullCode,
          indexFrequency,
          indexFrequencyId,
          org,
          orgId,
          businessSegments,
          businessSegmentsId,
          signOrgId,
          signOrg,
          actualMolecule: actualMolecule
            ? new Decimal(actualMolecule).toFixed(
                fixedNum,
                Decimal.ROUND_HALF_UP
              )
            : "-",
          actualDenominator: actualDenominator
            ? new Decimal(actualDenominator).toFixed(
                fixedNum,
                Decimal.ROUND_HALF_UP
              )
            : "-",
          contemValue: contemValue
            ? new Decimal(contemValue).toFixed(fixedNum, Decimal.ROUND_HALF_UP)
            : "-",
          previousValue: previousValue
            ? new Decimal(previousValue).toFixed(
                fixedNum,
                Decimal.ROUND_HALF_UP
              )
            : "-",
          levelName,
          parentName,
          actualValue: dealThousandData(
            actualValue,
            item.indexUnitId,
            precisions
          ),
          targetValue: dealThousandData(
            targetValue,
            item.indexUnitId,
            precisions
          ),
          targetCompletionRate: targetCompletionRate
            ? `${Decimal(targetCompletionRate)
                .mul(Decimal.Decimal(100))
                .toFixed(2, Decimal.ROUND_HALF_UP)}%`
            : "",
          previousChangeRate,
          isPreviousRate: "Y",
          isContemRate: "Y",
          contemChangeRate,
          indexUnitId,
          indexType: indexTypeId,
          indexNameInd,
          displayIndexName,
          indexSort,
          normalWDList,
          wdInCardName,
          wdInCardTag,
          groupId,
          cmimId,
          isDelete,
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id,
          goDown: goDown ? goDown.split(",") : [],
          wd: wd
            ? wd + "-" + (wdInXT.length ? wdInXT[0].split("-")[1] : "")
            : "",
          companyName: this.companyName,
          label: "同环比恶化,未达标",
          pj,
          parentSign,
          children: this.loopData(item.list || [], wd, index, indexSign),
          levelIndex: index,
          expand: false,
          sign: this.companySign,
        };
      });
    },
    // 获取组织下探列表
    getDetectionList(time) {
      console.log(this.cardItem, "======cardItem");
      const {
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id,
      } = this.cardItem;
      const postData = {
        indexId: this.cardItem.indexId,
        businessSegmentsId: this.cardItem.businessSegmentsId,
        fullCode: this.cardItem.fullCode,
        indexDt: time ? time.time : this.cardItem.indexDt,
        indexFrequencyId: this.cardItem.indexFrequencyId,
        cmimId: this.cardItem.cmimId,
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id,
        org: this.cardItem.org,
        businessSegments: this.cardItem.businessSegments,
        sign: this.cardItem.sign,
        signOrg: this.cardItem.signOrg,
        indexName: this.cardItem.postEndIndexName,
      };
      this.companySign = this.cardItem.sign;
      request(
        "/api/smc2/newIndexLibrary/orgList?BI=1",
        {
          method: "POST",
          body: postData,
        },
        {}
      ).then((res) => {
        console.log(res);
        if (!res.length) {
          this.close();
          return;
        }
        this.dataLoading = false;
        if (Array.isArray(res) && res.length) {
          const data = this.loopData(res);
          this.datas = {
            isRoot: true,
            children: data,
            expand: true,
          };
          this.ppResData = this.extractTree(data, "children");
          // 筛选出点击该卡片的数据
          const indexItem = this.datas.children.filter(
            (item) =>
              `${this.cardItem.indexId}~&~${this.cardItem.orgId}` ===
              `${item.indexId}~&~${item.orgId}`
          )[0];
          this.$nextTick(() => {
            if (indexItem) {
              this.onExpand(null, indexItem, true);
            }
          });
        }
      });
    },
    // 获取维度下探列表
    getDetectionWDList(groupId, wd) {
      request("/api/smc2/newIndexLibrary/dimensionGoDown", {
        method: "POST",
        body: {
          groupId,
          goDown: wd,
          indexFrequencyId: this.cardItem.indexFrequencyId,
          indexDt: this.cardItem.indexDt,
        },
      }).then((res) => {
        if (res.length) {
          this.wdXTList = this.loopData(res, wd);
          this.visible = true;
        } else {
          this.$message.warning("暂无维度下探信息");
        }
      });
    },
    // 放大
    zoomOut() {
      if (this.zoom >= 2) {
        return;
      }
      this.zoom = this.zoom + 0.1;
    },
    // 缩小
    zoomIn() {
      if (this.zoom <= 0.2) {
        return;
      }
      this.zoom = this.zoom - 0.1;
    },
    // 时间类型改变
    timeTypeChange(e) {
      const val = (typeof e === "object" ? e.target.value : e) || "";
      if (val === "day") {
        this.searchForm.time = moment(new Date().getTime() - 86400000).format(
          this.dateFormat
        );
      } else if (val === "week") {
        this.initWeekSelect(new Date().getFullYear());
        this.selectedYearWeek = [
          new Date().getFullYear(),
          getWeek(moment().format("YYYY-MM-DD")),
        ];
      } else {
        this.searchForm.time = moment(new Date().getTime()).format(
          this.monthFormat
        );
      }
    },
    // 初始化年
    initYearSelect() {
      let yearSelectOptions = [];
      // 两年前的年份不允许选择
      let beginningYear = moment().get("year") - 2;
      // 如果当前月份是12月则，年份选择扣除一年
      if (moment().get("month") + 1 === 12) {
        beginningYear++;
      }
      for (let i = beginningYear; i <= new Date().getFullYear(); i++) {
        yearSelectOptions.unshift(i);
      }
      this.yearSelectOptions = yearSelectOptions;
    },
    // 获取自然周
    createWeeks(year) {
      var d = new Date(year, 0, 1);
      while (d.getDay() != 1) {
        d.setDate(d.getDate() + 1);
      }
      var to = new Date(year + 1, 0, 1);
      var i = 1;
      let arr = [];
      for (var from = d; from < to; ) {
        from.setDate(from.getDate() + 6);
        if (from < to) {
          from.setDate(from.getDate() + 1);
        } else {
          to.setDate(to.getDate() - 1);
        }
        arr.push(i);
        i++;
      }
      return arr;
    },
    // 根据年份更新周下拉框选项
    initWeekSelect(year = new Date().getFullYear()) {
      let weekSelectOptions = this.createWeeks(year);
      if (
        year === this.yearSelectOptions[this.yearSelectOptions.length - 1] &&
        this.yearSelectOptions.length === 3
      ) {
        // 如果是最前边的一年且年份下拉框为三个时，要处理月份选择选两年前的周
        this.weekSelectOptions = weekSelectOptions.filter(
          (item) => item > getWeek(moment().format("YYYY-MM-DD"))
        );
      } else {
        this.weekSelectOptions = weekSelectOptions;
      }
    },
    // 下拉框输入搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 处理时间选择后time是moment的工具函数
    formatMoment(momentTimeInstance) {
      let time;
      const timeTypeList = new Map([
        ["day", "dateFormat"],
        ["month", "monthFormat"],
        ["week", "weekFormat"],
      ]);
      if (typeof momentTimeInstance.time === "object") {
        // 判断 this.searchForm.timeType 是否为 "d"、"m" 或 "y"
        if (["day", "month", "week"].includes(momentTimeInstance.timeType)) {
          // 如果是，获取对应的值并打印
          const value = timeTypeList.get(momentTimeInstance.timeType);
          time = momentTimeInstance.time.format(this[value]);
        }
      } else {
        time = momentTimeInstance.time;
      }
      return time;
    },
    sendRequest() {
      if (!this.getBaseDataFlagSuccess) {
        let body = {};
        let type = this.cmimId.split("-")[2];
        body.cmimId = this.cmimId;
        body.indexDt = this.searchForm.time.format(
          type === "M"
            ? this.monthFormat
            : type === "W"
            ? this.weekFormat
            : this.dateFormat
        );
        this.getBaseData(body).then(() => {
          this.getDetectionList({
            time: this.formatMoment(this.searchForm),
            type: this.searchForm.timeType.substring(0, 1).toUpperCase(),
          });
        });
      } else {
        this.getDetectionList({
          time: this.formatMoment(this.searchForm),
          type: this.searchForm.timeType.substring(0, 1).toUpperCase(),
        });
      }
    },
  },
};
</script>
<style lang="less">
@import url("./styles.css");
@active-bc: rgba(198, 255, 221, 0.5);
@parent-bc: rgba(251, 215, 134, 0.5);
@slide-bc: rgba(109, 213, 250, 0.5);
@child-bc: rgba(247, 121, 125, 0.5);
.detection-wd-tooltip {
  .ant-tooltip-inner {
    color: #1d2129;
    background-color: #fff;
  }
  .ant-tooltip-arrow::before {
    background-color: #fff;
  }
}
.card-in-detection-block {
  cursor: pointer;
  display: flex;
  ._flex {
    display: flex;
    align-items: center;
  }
  .card {
    width: 200px;
    height: 143px;
    border-radius: 3px;
    box-sizing: border-box;
    // background: rgba(243, 254, 255, 1);
    // background: rgba(255, 255, 255, 1);
    background-color: rgb(242, 243, 245);
    &.active-card {
      background-color: @active-bc;
    }
    // &.parent-card {
    //   background-color: @parent-bc;
    // }
    // &.child-card {
    //   background-color: @child-bc;
    // }
    // &.slide-card {
    //   background-color: @slide-bc;
    // }
    box-shadow: 2px 0px 6px rgba(0, 21, 41, 0.12);
    position: relative;
    padding: 16px;
    text-align: left;
    .top {
      display: flex;
      align-items: center;
      white-space: nowrap;
      justify-content: space-between;
      ._t {
        font-size: 12px;
        height: 16px;
        line-height: 16px;
        color: rgba(29, 33, 41, 1);
      }
      .wd-container {
        display: flex;
        align-items: center;
        .wd-tag {
          height: 14px;
          font-size: 12px;
          color: #53667a;
          line-height: 14px;
          border-radius: 2px;
          padding: 0 3px;
          background-color: #e0e3ea;
          margin-left: 0 !important;
          cursor: pointer;
          &:hover {
            background-color: #fff !important;
          }
        }
      }
      margin-bottom: 3px;
    }
    .sjz {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 24px;
      font-size: 16px;
      margin-bottom: 3px;
      font-weight: bold;
    }
    .intro {
      padding-bottom: 5px;
      border-bottom: 1px solid rgb(229, 231, 235);
      margin-bottom: 5px;
      & > div {
        font-size: 12px;
        height: 16px;
        line-height: 16px;
        color: rgb(78, 89, 105);
        &:first-child {
          margin-bottom: 5px;
        }
      }
    }
    .compare {
      font-size: 12px;
      ._c > span {
        color: rgba(0, 0, 0, 0.65);
        display: block;
        margin-right: 2px;
        white-space: nowrap;
      }
      ._c > div {
        .anticon {
          position: relative;
          top: 1px;
        }
        white-space: nowrap;
        span {
          white-space: nowrap;
        }
      }
    }
  }
}
.CardDetectionInfo {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  height: 100%;
  display: flex;
  flex-direction: column;
  height: 100vh;
  &.hisense-style.dark {
    .drawerTitle {
      background: #565b60;
      color: #ffffff;
      height: 50px;
      border-bottom: 1px solid #565b60;
    }
    .index-info {
      background: #313335;
      height: 55px;
      border-bottom: none;
      .title {
        color: #e2e8ea;
        font-weight: normal;
        font-size: 18px;
      }
      .tag {
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    .bottom-container {
      height: calc(100% - 105px);
      background-color: #070707;
      display: flex;
      .org-tree-node-btn {
        background-color: #070707;
      }
      .__c .org-tree-node-btn:before,
      .__c .org-tree-node-btn:after {
        background: #ffffff;
      }

      .card-in-detection-block .card {
        background: #313335;
        .top ._t,
        .intro > div {
          color: #a1a6ac;
        }
        .sjz {
          color: #ffffff;
        }
        .compare {
          ._c > span {
            color: #a1a6ac;
          }
        }
      }
    }
  }
  .drawerTitle {
    position: relative;
    padding: 16px 24px;
    padding-right: 48px !important;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 57px;
    ._left {
      display: flex;
      align-items: center;
      .index-info {
        box-sizing: border-box;
        align-items: center;
        .title {
          font-size: 24px;
          font-weight: bold;
        }
        .tag {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 2px;
          margin-left: 8px;
          &.plate {
            background-color: rgba(253, 238, 234, 1);
            color: rgb(238, 115, 79);
          }
          &.wd {
            background-color: rgba(242, 243, 245, 1);
            color: rgb(78, 89, 105);
          }
        }
      }
    }
  }
  ._flex {
    display: flex;
    align-items: center;
  }

  .bottom-container {
    // background-color: rgb(242, 243, 245);
    height: calc(100% - 57px);
    width: 100%;
    overflow: auto;
    display: flex;
    position: relative;
    & > div {
      height: 100%;
      &.left-part {
        overflow: hidden;
        width: calc(100% - 20px) !important;
        transition: width ease-in-out 0.3s;
        position: relative;
        &.open {
          width: calc(100% - 845px) !important;
        }
        .__c {
          min-width: 100%;
          height: 100%;
          overflow: auto;
          display: flex;
          justify-content: center;
          box-sizing: border-box;
          padding: 40px 0 0 0 !important;
          position: relative;
          .org-tree-node-btn:before {
            top: 50%;
            left: 50%;
            right: auto;
            width: 60%;
            height: 2px;
            border-top: 0px;
            background: #000;
            transform: translate(-50%, -50%);
          }
          .org-tree-node-btn:after {
            top: 50%;
            left: 50%;
            bottom: auto;
            width: 2px;
            border-left: 0px;
            height: 60%;
            background: #000;
            transform: translate(-50%, -50%);
          }
          .org-tree-node-btn.expanded:after {
            background: transparent !important;
          }
          .org-tree-container {
            background-color: transparent;
            width: 100%;
            padding: 0 !important;
            overflow-x: auto;
          }
          .org-tree-node-label .org-tree-node-label-inner {
            padding: 0;
            box-shadow: none;
          }
          .org-tree-node-children:before,
          .org-tree-node:not(:first-child):before,
          .org-tree-node:not(:last-child):after,
          .org-tree-node:not(:first-child):before,
          .org-tree-node:not(:last-child):after,
          .org-tree-node:after {
            border-color: rgb(78, 89, 105);
          }
        }
        .switchTable-btn {
          position: absolute;
          right: 10px;
          top: 10px;
          z-index: 10;
        }
        .operation-area {
          position: absolute;
          bottom: 30px;
          right: 10px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          & > button {
            margin-top: 5px;
          }
        }
        .tip-area {
          position: absolute;
          top: 0;
          right: 0;
          height: 40px;
          display: flex;
          align-items: center;
          & > div {
            display: flex;
            align-items: center;
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            color: #53667a;
            margin-right: 8px;
            .color {
              width: 16px;
              height: 10px;
              border-radius: 2px;
              margin-right: 4px;
              &.active {
                background-color: @active-bc;
              }
              &.parent {
                background-color: @parent-bc;
              }
              &.child {
                background-color: @child-bc;
              }
              &.slide {
                background-color: @slide-bc;
              }
            }
          }
        }
      }
      &.right-part {
        overflow-y: auto;
        box-shadow: -1px 0 8px 2px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        &.close {
          .container {
            width: 0;
          }
          .anticon {
            transform: rotateZ(180deg);
          }
        }
        .container {
          width: 820px;
          transition: width ease-in-out 0.3s;
          height: 100%;
          overflow-y: auto;
          & > div {
            width: 800px;
          }
        }
        position: absolute;
        top: 0;
        right: 0;
        z-index: 12;
        background-color: #fff;
        .control {
          cursor: pointer;
          &:hover {
            background-color: #e0e3ea;
          }
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          background-color: #eee;
          // border: 1px solid #9b9b9b;
          border-right: none;
          width: 20px;
          border-radius: 3px 0 0 3px;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.65);
          .anticon {
            transition: all ease-in-out 0.3s;
          }
        }
      }
    }
  }
}
</style>
