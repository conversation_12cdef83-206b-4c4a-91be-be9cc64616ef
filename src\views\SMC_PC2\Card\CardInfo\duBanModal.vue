<template>
  <a-modal
    title="新建督办"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="onOk"
    width="80%"
    okText="创建"
    class="sendMailModal"
    @cancel="close"
  >
    <a-form-model
      ref="dubanForm"
      :model="form"
      :label-col="labelCol"
      :rules="rules"
      :wrapper-col="wrapperCol"
    >
      <a-row>
        <a-col :span="16">
          <a-form-model-item
            :labelCol="{ span: 3 }"
            :wrapper-col="{ span: 20 }"
            label="督办内容"
            prop="content"
          >
            <div style="border: 1px solid #ccc;">
              <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editorId="editorId"
                :mode="mode"
              />
              <Editor
                style="height: 230px; overflow-y: auto;"
                :editorId="editorId"
                :default-config="editorConfig"
                :defaultHtml="form.content"
                :mode="mode"
                @onChange="editorChange"
              />
              <!-- 注意： defaultContent （JSON 格式） 和 defaultHtml （HTML 格式），二选一 -->
              <CardItemHtmlContent
                ref="cardItemHtmlContent"
                :cardItem="cardItem"
              />
            </div>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="督办类型" prop="type">
            <a-input :value="form.type" disabled />
          </a-form-model-item>
          <a-form-model-item label="督办来源" prop="source">
            <a-input :value="form.source" disabled />
          </a-form-model-item>
          <a-form-model-item label="关键词" prop="keyWord">
            <a-input :value="form.keyWord" disabled />
          </a-form-model-item>
          <a-form-model-item label="督办人" prop="dbR">
            <a-select
              :value="form.dbR"
              placeholder="督办人"
              disabled
              style="width: 100%"
              :filter-option="false"
            >
              <a-select-option
                v-for="d in userData"
                :key="`${d.userCode}-${d.ldapDeptId}`"
                :title="
                  `${d.cnName}[${d.userName}]${
                    d.ldapFullName
                      ? '-' + changeLdapFullName(d.ldapFullName)
                      : ''
                  }`
                "
              >
                {{ d.cnName }}[{{ d.userName }}]{{
                  d.ldapFullName ? "-" + changeLdapFullName(d.ldapFullName) : ""
                }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="被督办人" prop="dbBR">
            <a-select
              mode="multiple"
              :value="form.dbBR"
              placeholder="被督办人"
              show-search
              style="width: 100%"
              :filter-option="false"
              :not-found-content="fetching ? undefined : null"
              @search="fetchUser"
              @change="handledbSelectCommonChange('dbBR', $event)"
            >
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option
                v-for="d in userData"
                :key="`${d.userCode}-${d.ldapDeptId}`"
              >
                {{ d.cnName }}[{{ d.userName }}]{{
                  d.ldapFullName ? "-" + changeLdapFullName(d.ldapFullName) : ""
                }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="要求反馈时间" prop="requireFirstDate">
            <a-date-picker
              :disabled-date="disabledDate"
              style="width: 100%"
              @change="requireFirstDateChange"
            />
          </a-form-model-item>
          <a-form-model-item label="共同完成人" prop="dbCoordinators">
            <a-select
              mode="multiple"
              :value="form.dbCoordinators"
              show-search
              placeholder="共同完成人"
              style="width: 100%"
              :filter-option="false"
              :not-found-content="fetching ? undefined : null"
              @search="fetchUser"
              @change="handledbSelectCommonChange('dbCoordinators', $event)"
            >
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option
                v-for="d in userData"
                :key="`${d.userCode}-${d.ldapDeptId}-${d.cnName}`"
              >
                {{ d.cnName }}[{{ d.userName }}]{{
                  d.ldapFullName ? "-" + changeLdapFullName(d.ldapFullName) : ""
                }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="相关领导" prop="dbLeaders">
            <a-select
              mode="multiple"
              :value="form.dbLeaders"
              placeholder="相关领导"
              show-search
              style="width: 100%"
              :filter-option="false"
              :not-found-content="fetching ? undefined : null"
              @search="fetchUser"
              @change="handledbSelectCommonChange('dbLeaders', $event)"
            >
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option
                v-for="d in userData"
                :key="`${d.userCode}-${d.ldapDeptId}`"
              >
                {{ d.cnName }}[{{ d.userName }}]{{
                  d.ldapFullName ? "-" + changeLdapFullName(d.ldapFullName) : ""
                }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="可阅读者" prop="dbReaders">
            <a-select
              mode="multiple"
              :value="form.dbReaders"
              show-search
              placeholder="可阅读者"
              style="width: 100%"
              :filter-option="false"
              :not-found-content="fetching ? undefined : null"
              @search="fetchUser"
              @change="handledbSelectCommonChange('dbReaders', $event)"
            >
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option
                v-for="d in userData"
                :key="`${d.userCode}-${d.ldapDeptId}`"
              >
                {{ d.cnName }}[{{ d.userName }}]{{
                  d.ldapFullName ? "-" + changeLdapFullName(d.ldapFullName) : ""
                }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="预置催办频率" prop="urgeFreq">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-select
                  v-model="form.urgeFreq"
                  style="width: 100%"
                  @change="form.urgeFreqR = ''"
                >
                  <a-select-option v-for="d in frequencyTypeList" :key="d.key">
                    {{ d.value }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12" v-if="form.urgeFreq !== 'N'">
                <template v-if="form.urgeFreq === 'deadDate'">
                  <a-select v-model="form.urgeFreqR" style="width: 100%">
                    <a-select-option v-for="d in 5" :key="d">
                      {{ d }}天
                    </a-select-option>
                  </a-select>
                </template>
                <template v-if="form.urgeFreq === 'month'">
                  <a-input-number
                    v-model="form.urgeFreqR"
                    :min="1"
                    :max="31"
                  />日
                </template>
                <template v-if="form.urgeFreq === 'week'">
                  <a-select v-model="form.urgeFreqR" style="width: 100%">
                    <a-select-option v-for="d in weekList" :key="d.key">
                      {{ d.value }}
                    </a-select-option>
                  </a-select>
                </template>
                <template v-if="form.urgeFreq === 'day'">
                  <a-date-picker
                    :disabled-date="disabledDate"
                    @change="
                      (date, dateString) => {
                        form.urgeFreqR = dateString;
                      }
                    "
                  />
                </template>
              </a-col>
            </a-row>
          </a-form-model-item>
          <a-form-model-item label="评价人" prop="evaPerson">
            <a-select
              show-search
              :value="form.evaPerson"
              placeholder="评价人"
              style="width: 100%"
              :filter-option="false"
              :not-found-content="fetching ? undefined : null"
              @search="fetchUser"
              @change="handledbSelectCommonChange('evaPerson', $event)"
            >
              <a-spin v-if="fetching" slot="notFoundContent" size="small" />
              <a-select-option v-for="d in userData" :key="`${d.userCode}`">
                {{ d.cnName }}[{{ d.userName }}]{{
                  d.ldapFullName ? "-" + changeLdapFullName(d.ldapFullName) : ""
                }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import "@wangeditor/editor/dist/css/style.css";
import debounce from "lodash/debounce";
import {
  httpHeaders,
  genMinioUrl,
  changeLdapFullName,
  dubanConfig,
  getDuBanUserByName
} from "@/utils/utils.js";
import CardItemHtmlContent from "./CardItemHtmlContent.vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import moment from "moment";
export default {
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    let validateUrgeFreq = (rule, value, callback) => {
      if (["N", "halfWeek"].includes(value)) {
        callback();
      } else {
        if (!this.form.urgeFreqR) {
          callback(new Error("请完善催办频率"));
        } else {
          callback();
        }
      }
    };
    return {
      changeLdapFullName,
      editorId: `smc-duban-${Math.random()
        .toString()
        .slice(-5)}`,
      mode: "simple", // or 'simple'
      visible: false,
      confirmLoading: false,
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            fieldName: "file",
            server: "/api/system/oss/upload/smc",
            maxFileSize: 10 * 1024 * 1024,
            maxNumberOfFiles: 2,
            allowedFileTypes: ["image/*"],
            headers: httpHeaders(),
            timeout: 5 * 1000,
            // 自定义插入图片
            customInsert(res, insertFn) {
              // res 即服务端的返回结果
              insertFn(genMinioUrl(res.data), "", res.data);
            }
          }
        }
      },
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      form: {
        content: "", // 督办内容
        type: dubanConfig.defaultParams.type, // 类型名称
        operator: "", // 操作人
        updateDate: "", // 修改时间
        source: "", // 督办来源
        keyWord: "", // 关键词
        urgeFreq: "N", // 是否催办
        urgeFreqR: "",
        dbR: "", // 督办人userCode-orgId
        dbBR: [], // 被督办人选中信息
        requireFirstDate: "", // 要求反馈时间
        dbLeaders: [], // 相关领导
        dbReaders: [], // 可阅读者
        dbCoordinators: [], // 共同完成人
        evaPerson: "" // 评价人
      },
      frequencyTypeList: [
        {
          key: "N",
          value: "不催办"
        },
        {
          key: "day",
          value: "某天"
        },
        {
          key: "week",
          value: "每周"
        },
        {
          key: "halfMonth",
          value: "每半月"
        },
        {
          key: "month",
          value: "每月"
        },
        {
          key: "deadDate",
          value: "项目结束前"
        }
      ],
      weekList: [
        {
          key: 1,
          value: "一"
        },
        {
          key: 2,
          value: "二"
        },
        {
          key: 3,
          value: "三"
        },
        {
          key: 4,
          value: "四"
        },
        {
          key: 5,
          value: "五"
        },
        {
          key: 6,
          value: "六"
        },
        {
          key: 7,
          value: "日"
        }
      ],
      rules: {
        content: [
          {
            required: true,
            message: "请填写督办内容",
            trigger: "blur"
          }
        ],
        dbBR: [
          {
            required: true,
            message: "请选择被督办人",
            trigger: "change"
          }
        ],
        requireFirstDate: [
          {
            required: true,
            message: "请选择反馈时间",
            trigger: "change"
          }
        ],
        urgeFreq: [
          {
            validator: validateUrgeFreq,
            trigger: "change"
          }
        ],
        evaPerson: [
          {
            required: true,
            message: "请选择评价人",
            trigger: "change"
          }
        ]
      },
      cardItem: undefined,
      fetching: false, // 查询被督办人
      userData: [] // ldap用户
    };
  },
  components: { Editor, Toolbar, CardItemHtmlContent },
  methods: {
    disabledDate(current) {
      // Can not select days before today and today
      return current && current < moment().endOf("day");
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 编辑器改变
    editorChange(editor) {
      let html = editor.getHtml();
      html = html.includes('style=""/>')
        ? html.replace('style=""/>', 'style="width: 100%;"/>')
        : html;
      this.form.content = html === "<p><br></p>" ? "" : html;
    },
    // 查询ldap用户
    fetchUser(value, callback) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      getDuBanUserByName(value, res => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        if (res && Array.isArray(res)) {
          this.userData = res;
          typeof callback === "function" && callback(res);
          this.fetching = false;
        }
      });
    },
    // 要求反馈时间改变
    requireFirstDateChange(date, dateString) {
      this.form.requireFirstDate = dateString;
    },
    handledbSelectCommonChange(key, value) {
      this.form[key] = value;
      this.fetching = false;
    },
    show(cardItem, dbBRInfo) {
      this.cardItem = cardItem;
      this.form.source = `${this.cardItem.companyName}-智造云图`;
      this.form.keyWord = `${this.cardItem.org}-${this.cardItem.displayIndexName}-${this.cardItem.indexDt}${this.cardItem.indexFrequency}`;
      this.visible = true;
      this.fetchUser(
        window.vm.$store
          ? window.vm.$store.state.user.info.loginName
          : "yuyongjie.ex",
        () => {
          this.form.dbR = `${this.userData[0].userCode}-${this.userData[0].ldapDeptId}`;
          this.fetchUser(dbBRInfo, () => {
            this.form.dbBR = [
              `${this.userData[0].userCode}-${this.userData[0].ldapDeptId}`
            ];
            this.$nextTick(() => {
              this.userData = [];
            });
          });
        }
      );
    },
    // 取消按钮
    close() {
      this.form = {
        content: "", // 督办内容
        type: dubanConfig.defaultParams.type, // 类型名称
        operator: "", // 操作人
        updateDate: "", // 修改时间
        source: "", // 督办来源
        keyWord: "", // 关键词
        urgeFreq: "N", // 是否催办
        urgeFreqR: "",
        dbR: "", // 督办人userCode-orgId
        dbBR: [], // 被督办人选中信息
        requireFirstDate: "", // 要求反馈时间
        dbLeaders: [], // 相关领导
        dbReaders: [], // 可阅读者
        dbCoordinators: [], // 共同完成人
        evaPerson: "" // 评价人
      };
      this.userData = [];
      this.fetchId = 0;
      this.$refs.dubanForm.resetFields();
      this.visible = false;
    },
    onOk() {
      this.$refs.dubanForm.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          const { form } = this;
          const data = {
            version: "2.0",
            format: "0",
            typeCode: dubanConfig.defaultParams.typeCode, // 类型code
            type: dubanConfig.defaultParams.type,
            typeId: dubanConfig.defaultParams.typeId, // 类型Id
            status: 1, // 单据状态 （1 发布、0 草稿）
            msgUrl: "navigation?status=1&linkId=" // 邮件发送url
          };
          data["content"] = `${form.content} <hr /> ${this.$refs[
            "cardItemHtmlContent"
          ].getCardItemHtmlContent()}`;
          data["operator"] = form.dbR.split("-")[0];
          data["userCode"] = form.dbR.split("-")[0];
          data["supervisor"] = form.dbR.split("-")[0];
          data["updateDate"] = new Date().getTime();
          data["publishDate"] = new Date().getTime();
          data["source"] = form.source;
          data["keyWord"] = form.keyWord;
          data["urgeFreq"] = `${form.urgeFreq}${
            form.urgeFreqR ? "-" + form.urgeFreqR.replaceAll("-", "") : ""
          }`;
          data["evaPerson"] = form.evaPerson;
          data["requireFirstDate"] = form.requireFirstDate;
          if (form.dbBR.length > 1) {
            data["undertakers"] = form.dbBR.map(item => {
              return {
                userCode: item.split("-")[0],
                ldapDeptId: item.split("-")[1]
              };
            });
          } else {
            data["undertaker"] = form.dbBR[0].split("-")[0];
            data["undertakerOrg"] = form.dbBR[0].split("-")[1];
          }
          data["leaders"] = form.dbLeaders.map(item => {
            return {
              userCode: item.split("-")[0],
              ldapDeptId: item.split("-")[1]
            };
          });
          data["readers"] = form.dbReaders.map(item => {
            return {
              userCode: item.split("-")[0],
              ldapDeptId: item.split("-")[1]
            };
          });
          data["coordinators"] = form.dbCoordinators.map(item => {
            return {
              userCode: item.split("-")[0],
              ldapDeptId: item.split("-")[1],
              cnName: item.split("-")[2]
            };
          });
          request(`/api/smc/duban/saveSupervise`, {
            method: "POST",
            body: data
          }).then(() => {
            this.confirmLoading = false;
            this.$message.success("操作成功");
            this.close();
          });
        }
      });
    }
  }
};
</script>
