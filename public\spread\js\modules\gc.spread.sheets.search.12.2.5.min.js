/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Search=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/search/search.entry.js")}({"./dist/plugins/search/search.entry.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/search/search.js");b.SearchResult=d.SearchResult,b.SearchCondition=d.SearchCondition,b.SearchFlags=d.SearchFlags,b.SearchOrder=d.SearchOrder,b.SearchFoundFlags=d.SearchFoundFlags},"./dist/plugins/search/search.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("CalcEngine"),g=!!f,h=e.Common.q,i=null,j=Math.max,k="string";function q(a,b,c){if(!a)return!1;if(a=""+a,b=""+b,0===c)return a.indexOf(b)>-1;var d=(2&c)>0,e=(1&c)>0,f=(4&c)>0,g;return f?(g=d?h.zb(b,!1,!0):h.ub(b),b=g?g:b,g=e?h.sb(b):h.qb(b),g.test(a)):(e&&(b=b.toLowerCase(),a=a.toLowerCase()),d?b===a:a.indexOf(b)>=0)}function r(a,b,c,d,e,f,g,h){var j=i,k=a+1,l=b+1;return 0===c?l>=0&&l<=h?j={r:a,c:l}:k>=0&&k<=f&&(j={r:k,c:d?g:0}):k<=f?j={r:k,c:b}:l<=h&&(j={r:d?e:0,c:l}),j}d.Worksheet.prototype.search=function(a){var b,c,d,e,f,h,l,n,o,p,s,t,u,v,w,x,y,z,A,B;if(!a)return i;if(b=this,c=a.sheetArea,d=a.searchString,e=a.searchTarget,f=a.searchFlags,h=b.getRowCount(c),l=b.getColumnCount(c),n=new m,!d||0===e||h<=0&&l<=0)return n;for(o=j(0,a.rowStart),p=j(0,a.columnStart),s=a.rowEnd,t=a.columnEnd,u=(8&f)>0,(s<0||!u)&&(s=h-1),(t<0||!u)&&(t=l-1),v=a.findBeginRow,w=a.findBeginColumn,x=v<0?o:v,y=w<0?p:w;x>=0&&y>=0;){if(z=b.getCell(x,y,c),A=void 0,(1&e)>0&&(A=a.callback?a.callback(z,b):z.text(),""!==A&&q(A,d,f)&&(n.searchFoundFlag|=1,n.foundString=A)),g&&(8&e)>0&&(A=z.formula(),typeof A===k&&""!==A&&q(A,d,f)&&(n.searchFoundFlag|=8,n.foundString=A)),(4&e)>0&&(A=z.tag(),typeof A===k&&""!==A&&q(A,d,f)&&(n.searchFoundFlag|=4,n.foundString=A)),0!==n.searchFoundFlag)return n.foundRowIndex=x,n.foundColumnIndex=y,n;if(B=r(x,y,a.searchOrder,u,o,s,p,t),!B)break;x=B.r,y=B.c}return n},d.Workbook.prototype.search=function(a){var b,c,d,e,f,g,h,j,k;if(!a)return i;if(b=this,c=0,d=b.getSheetCount(),e=new m,!a.searchString||a.searchTarget===c||d<=0)return e;if(a.startSheetIndex===-1&&(a.startSheetIndex=0),a.endSheetIndex===-1&&(a.endSheetIndex=d-1),j=a.startSheetIndex,k=a.endSheetIndex,k>=j&&0<=j&&j<d&&0<=k&&k<d)for(f=j;f<=k;f++)if(g=b.getSheet(f),h=g.search(a),h&&h.searchFoundFlag!==c)return h.foundSheetIndex=f,h;return e},l=function(){function a(){this.startSheetIndex=-1,this.endSheetIndex=-1,this.searchString=i,this.searchFlags=0,this.searchOrder=0,this.searchTarget=1,this.sheetArea=3,this.rowStart=-1,this.columnStart=-1,this.rowEnd=-1,this.columnEnd=-1,this.findBeginRow=-1,this.findBeginColumn=-1}return a}(),b.SearchCondition=l,m=function(){function a(){this.foundColumnIndex=-1,this.foundRowIndex=-1,this.searchFoundFlag=0,this.foundSheetIndex=-1,this.foundString=i}return a}(),b.SearchResult=m,function(a){a[a.none=0]="none",a[a.ignoreCase=1]="ignoreCase",a[a.exactMatch=2]="exactMatch",a[a.useWildCards=4]="useWildCards",a[a.blockRange=8]="blockRange"}(n=b.SearchFlags||(b.SearchFlags={})),function(a){a[a.zOrder=0]="zOrder",a[a.nOrder=1]="nOrder"}(o=b.SearchOrder||(b.SearchOrder={})),function(a){a[a.none=0]="none",a[a.cellText=1]="cellText",a[a.cellTag=4]="cellTag",a[a.cellFormula=8]="cellFormula"}(p=b.SearchFoundFlags||(b.SearchFoundFlags={}))},CalcEngine:function(a,b){a.exports=GC.Spread.CalcEngine},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});