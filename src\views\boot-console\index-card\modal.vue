<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: y<PERSON><PERSON>gqi.ex
 * @LastEditTime: 2025-02-28 15:42:16
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1300"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
    class="modalClass"
  >
    <a-form-model
      ref="ruleForm"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      class="modalClass"
    >
      <!-- 卡片标题 -->
      <!-- <a-form-model-item label="卡片标题" prop="title">
        <a-input v-model="form.title" />
      </a-form-model-item> -->
      <!-- 选择指标 -->
      <a-form-model-item label="指标名称" prop="indexName">
        <a-input disabled v-model="indexName" />
      </a-form-model-item>
      <!-- 详情链接 -->
      <a-button
        type="primary"
        style="margin-bottom: 20px;"
        @click="addNewTableLine"
        >新增报表链接</a-button
      >
      <a-table
        :pagination="false"
        :columns="columns"
        :data-source="reportSettingList"
      >
        <!-- 序号 -->
        <template slot="index" slot-scope="text, record, index">
          <span>{{ index + 1 }}</span>
        </template>
        <!-- 基地 -->
        <span slot="fullCode" slot-scope="text, record, index">
          <a-tree-select
            v-model="record.fullCode"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择组织"
            allow-clear
            :treeData="orgTreeData"
            @change="getWDList(index, $event)"
          >
          </a-tree-select>
        </span>
        <!-- 维度 -->
        <span slot="wd" slot-scope="text, record">
          <a-select
            v-model="record.wd"
            :dropdownMatchSelectWidth="false"
            style="width: 100%;max-width: 120px;"
          >
            <a-select-option
              :value="item.key"
              v-for="item in record.wdList"
              :key="item.key"
            >
              {{ item.title }}
            </a-select-option>
          </a-select>
        </span>
        <!-- 报表类型 -->
        <span slot="reportType" slot-scope="text, record">
          <a-select
            v-model="record.reportType"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
          >
            <a-select-option
              :value="item.key"
              v-for="item in dict['smc-reportType'] || []"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </span>
        <!-- 报表开发人员 -->
        <span slot="reportDeveloper" slot-scope="text, record">
          <a-input v-model="record.reportDeveloper" />
        </span>
        <!-- 报表名称 -->
        <span slot="reportName" slot-scope="text, record">
          <a-input v-model="record.reportName" />
        </span>
        <!-- 报表链接 -->
        <span slot="reportUrl" slot-scope="text, record">
          <a-input v-model="record.reportUrl" />
        </span>
        <!-- 是否置顶 -->
        <span slot="topFlag" slot-scope="text, record">
          <a-select
            v-model="record.topFlag"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
            @change="record.topSort = ''"
          >
            <a-select-option value="1">
              是
            </a-select-option>
            <a-select-option value="0">
              否
            </a-select-option>
          </a-select>
        </span>
        <!-- 是否置顶 -->
        <span slot="isPrediction" slot-scope="text, record">
          <a-select
            v-model="record.isPrediction"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
          >
            <a-select-option value="1">
              是
            </a-select-option>
            <a-select-option value="0">
              否
            </a-select-option>
          </a-select>
        </span>
        <!-- 置顶排序 -->
        <span slot="topSort" slot-scope="text, record">
          <a-input
            v-model="record.topSort"
            :disabled="record.topFlag === '0'"
          />
        </span>
        <!-- 操作栏 -->
        <span slot="action" slot-scope="text, record, index">
          <a-tooltip placement="top">
            <template slot="title">
              <span>删除</span>
            </template>
            <a-icon @click="delReportItem(index)" type="delete" />
          </a-tooltip>
        </span>
      </a-table>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import uniqBy from "lodash/uniqBy";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      indexName: "",
      form: {
        indexId: "",
      },
      replaceFields: {
        title: "nodeName",
        key: "nodeId",
        value: "nodeId",
        children: "childNodeList",
      },
      rules: {
        indexId: [
          {
            required: true,
            message: "请选择指标",
            trigger: "change",
          },
        ],
      },
      isEdit: false, // 是否编辑状态
      reportSettingList: [], // 表格数据源
      orgTreeData: [], // 基地列表
      columns: [
        {
          title: "序号",
          key: "index",
          scopedSlots: { customRender: "index" },
          width: 80,
        },
        {
          title: "组织",
          dataIndex: "fullCode",
          key: "fullCode",
          scopedSlots: { customRender: "fullCode" },
          width: 120,
        },
        {
          title: "维度",
          dataIndex: "wd",
          key: "wd",
          scopedSlots: { customRender: "wd" },
          width: 120,
        },
        {
          title: "报表类型",
          key: "reportType",
          dataIndex: "reportType",
          scopedSlots: { customRender: "reportType" },
          width: 150,
        },
        {
          title: "报表开发人员",
          key: "reportDeveloper",
          dataIndex: "reportDeveloper",
          scopedSlots: { customRender: "reportDeveloper" },
          width: 180,
        },
        {
          title: "报表名称",
          key: "reportName",
          dataIndex: "reportName",
          scopedSlots: { customRender: "reportName" },
        },
        {
          title: "报表链接",
          key: "reportUrl",
          dataIndex: "reportUrl",
          scopedSlots: { customRender: "reportUrl" },
        },
        {
          title: "是否置顶",
          key: "topFlag",
          dataIndex: "topFlag",
          scopedSlots: { customRender: "topFlag" },
          width: 120,
        },
        {
          title: "是否预测报表",
          key: "isPrediction",
          dataIndex: "isPrediction",
          scopedSlots: { customRender: "isPrediction" },
        },
        {
          title: "置顶排序",
          key: "topSort",
          dataIndex: "topSort",
          scopedSlots: { customRender: "topSort" },
          width: 120,
        },
        {
          title: "操作",
          key: "action",
          scopedSlots: { customRender: "action" },
        },
      ], // 表格列
      orgPPList: [],
      originDataList: [],
      dict: {},
    };
  },
  computed: {
    stateOrgList() {
      return uniqBy(this.orgPPList, "key");
    },
  },
  methods: {
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      this.getDICT();
      if (formValue) {
        const {
          id,
          indexId,
          fullCode,
          signOrgId,
          signOrg,
          businessSegments,
          businessSegmentsId,
          indexName,
        } = formValue;
        this.isEdit = true;
        this.form = {
          id,
          indexId,
          fullCode,
          signOrg,
          signOrgId,
          businessSegments,
          businessSegmentsId,
        };
        this.indexName = indexName;
        this.getOrgList();
        this.getCardReportList({ indexId, signOrgId, businessSegmentsId });
      }
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-Y%2FN%2Csmc-reportType&languageCode=zh_CN"
        )
      ).then((res) => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    close() {
      this.form = {
        indexId: "",
      };
      this.reportSettingList = [];
      this.$refs.ruleForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    getOrgList() {
      request(`/api/smc2/attributes/getTree`, {
        method: "POST",
        body: {
          signOrgId: this.form.signOrgId,
          fullCode: this.form.fullCode,
          businessSegmentsId: this.form.businessSegmentsId,
          indexId: this.form.indexId,
        },
      }).then((res) => {
        this.orgTreeData = this.dealTreeData(res || []);
      });
    },

    dealTreeData(arr) {
      return arr.map((item) => {
        const itemData = {
          key: item.fullCode,
          disabled: item.isSelect === "N",
          children: this.dealTreeData(item.list),
          value: item.fullCode,
          title: `${item.org}`,
        };
        this.orgPPList = uniqBy([itemData, ...this.orgPPList], "key");
        return itemData;
      });
    },
    // 根据卡片ID获取当前卡片上绑定的报表信息
    getCardReportList({ indexId, signOrgId, businessSegmentsId }) {
      request(
        `/api/smc2/cardInfo/searchCardInfoDetail?indexId=${indexId}&signOrgId=${signOrgId}&businessSegmentsId=${businessSegmentsId}&pageNum=1&pageSize=99999`
      ).then((res) => {
        if (Array.isArray(res.rows) && res.rows.length) {
          this.reportSettingList = res.rows.map((item) => {
            return {
              fullCode: item.fullCode,
              reportUrl: item.reportUrl,
              reportType: item.reportType,
              reportDeveloper: item.reportDeveloper,
              reportName: item.reportName,
              isPrediction: item.isPrediction,
              wdList: [],
              wd: item.cmimId && item.wd ? `${item.cmimId}~&~${item.wd}` : "",
              topSort: item.topSort,
              topFlag: item.topFlag,
            };
          });
          this.$nextTick(() => {
            this.reportSettingList.forEach((item, index) => {
              this.getWDList(index, item.fullCode, "init");
            });
          });
        }
      });
    },
    // 报表链接配置表格新增行
    addNewTableLine() {
      this.reportSettingList.push({
        wdList: [],
        reportUrl: "",
        reportName: "",
        reportType: "",
        reportDeveloper: "",
        wd: "",
        fullCode: "",
        topSort: "",
        topFlag: "0",
        isPrediction: "0",
      });
    },
    // 根据组织、计算组织、版块、指标ID获取维度列表
    getWDList(index, fullCode, sign) {
      if (!fullCode) {
        return;
      }
      if (!sign) {
        this.$set(this.reportSettingList[index], "wd", "");
      }
      request(`/api/smc2/cardInfo/getWDforCard`, {
        method: "POST",
        body: {
          businessSegmentsId: this.form.businessSegmentsId,
          indexId: this.form.indexId,
          signOrgId: this.form.signOrgId,
          orgId: fullCode.split("/")[fullCode.split("/").length - 1],
        },
      }).then((res) => {
        if (Array.isArray(res) && res.length) {
          const list = res.map((item) => {
            return {
              key: `${item.key}~&~${item.value}`,
              title: item.value || "空",
            };
          });
          this.$set(this.reportSettingList[index], "wdList", list);
        }
      });
    },
    // 保存卡片信息
    saveCard() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          // 判断报表类型和报表配置列表
          let errorInfo = "";
          for (let i = 0; i < this.reportSettingList.length; i++) {
            const element = this.reportSettingList[i];
            if (
              !(
                element.reportUrl &&
                element.reportType &&
                element.reportDeveloper &&
                element.wd &&
                element.fullCode
              )
            ) {
              errorInfo = `请检查链接配置表格内第${i +
                1}行数据基地、维度、报表类型、报表链接是否填写`;
              break;
            } else if (element.topFlag === "1" && !element.topSort) {
              errorInfo = `请在表格内第${i + 1}行数据填入置顶排序`;
              break;
            }
          }
          if (errorInfo) {
            this.$message.error(errorInfo);
            return;
          }
          // 组装postData url 的数据
          // postData.url = this.reportSettingList;
          // if (this.isEdit) {
          //   postData.info["id"] = this.form.id;
          // }
          let cardLiist = this.reportSettingList.map((item) => {
            return {
              signOrgId: this.form.signOrgId,
              businessSegmentsId: this.form.businessSegmentsId,
              indexId: this.form.indexId,
              fullCode: item.fullCode,
              orgId: item.fullCode.split("-")[
                item.fullCode.split("-").length - 1
              ],
              topFlag: item.topFlag,
              topSort: item.topSort,
              reportDeveloper: item.reportDeveloper,
              reportType: item.reportType,
              reportUrl: item.reportUrl,
              reportName: item.reportName,
              isPrediction: item.isPrediction,
              reportParam: "",
              type: "0",
              cmimId: item.wd.split("~&~")[0],
              wd: item.wd.split("~&~")[1],
            };
          });
          request(`/api/smc2/cardInfo/insertCardInfo1`, {
            method: "POST",
            body: {
              businessSegmentsId: this.form.businessSegmentsId,
              signOrgId: this.form.signOrgId,
              indexId: this.form.indexId,
              signOrg: this.form.signOrg,
              businessSegments: this.form.businessSegments,
              indexName: this.indexName,
              cardLiist,
            },
          }).then((res) => {
            if (res?.code === "5501") {
              return;
            }
            this.close();
            this.$emit("fetchData");
          });
        }
      });
    },
    // 删除表格内某一项
    delReportItem(index) {
      this.reportSettingList.splice(index, 1);
    },
  },
};
</script>
<style lang="less" scoped>
.modalClass {
  overflow: auto;
}
</style>
