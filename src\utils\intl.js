/*
 * @Description: 国际化相关方法
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-23 10:33:21
 * @LastEditors: g<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-07-02 17:58:25
 */
/**
 * @description: 字段alias国际化转换，兼容表格组件
 * @param {String,Object} alias 设置的国际化标题
 * @param {String} name 字段名称
 * @return {String} 要显示的标题
 */
export function convertAlias(alias, name) {
  let aliasT = ""; // 定义columns标题
  if (typeof alias === "object" && alias.hasOwnProperty("val")) {
    // 国际化输入框/国际化key
    if (alias.intlKey || alias.val) {
      aliasT =
        alias.intlKey && window.vm && window.vm.$t
          ? window.vm.$t(alias.intlKey)
          : alias.val;
    } else {
      aliasT =
        window.vm && window.vm.$t
          ? window.vm.$t(name ? name.toLocaleUpperCase() : "")
          : "";
    }
  } else {
    aliasT =
      window.vm && window.vm.$t
        ? window.vm.$t(
            alias
              ? alias.toLocaleUpperCase()
              : name
              ? name.toLocaleUpperCase()
              : ""
          )
        : "";
  }
  return aliasT;
}

/**
 * @description: 翻译国际化文案
 * @param {String,Object} alias 国际化key
 * @param {String} defaultAlias 默认语言文案
 * @return {String} 需显示的翻译后的文案
 */
export function showAlias(alias, defaultAlias) {
  let aliasT = "";
  if (typeof alias === "object" && alias.hasOwnProperty("val")) {
    // 对象
    if (alias.intlKey || alias.val) {
      aliasT =
        alias.intlKey && window.vm && window.vm.$t
          ? window.vm.$t(alias.intlKey)
          : alias.val;
    } else {
      aliasT = defaultAlias || "";
    }
  } else {
    // 字符串
    if (alias && window.vm && window.vm.$t) {
      aliasT = window.vm.$t(alias);
    } else {
      aliasT = defaultAlias || "";
    }
  }
  return aliasT;
}
