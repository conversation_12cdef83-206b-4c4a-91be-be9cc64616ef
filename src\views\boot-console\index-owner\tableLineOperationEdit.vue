<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-09-22 17:40:50
-->
<template>
  <div style="display: inline-block;">
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <!-- <a-button @click="btClick">编辑</a-button> -->
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      const record = this.record;
      // const record =  {"createdBy":null,"createdDate":null,"modifiedBy":null,"modifiedDate":null,"remark":null,"businessSegmentsId":null,"indexId":null,"signOrgId":null,"orgId":null,"businessSegments":"效率","signOrg":null,"indexName":"单实物台制造费","org":"智动精工公司","fullCode":null,"role":null,"sign":null,"leaderOwner":"王圣镇.ex-wangshengzhen.ex","managerLdap":"杨曼-yangman2","cmimId":"ZBY00467-H0104-M-0000-0000-DIM_959-0000-0000-0000-0000-0000-0000-0000-0000","cmimId1":null,"company":"智动精工公司","table":null,"dimension":null,"classKeycompany":"visiblecompany16901791922881690178352000","classKeyindexName":"visibleindexName16901791922961690178352000","classKeybusinessSegments":"visiblebusinessSegments16901791922971690178352000","classKeyorg":"visibleorg16901791922981690178352000","classKeydimension":"visibledimension16901791922981690178352000","classKeymanagerLdap":"visiblemanagerLdap16901791922991690178352000","classKeyleaderOwner":"visibleleaderOwner16901791922991690178352000"}
      // console.log("----->", JSON.stringify(record));
      this.$refs["modal"].show(record);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex
        }
      });
    }
  }
};
</script>
