<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2025-04-24 17:09:49
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="960"
    title="维护"
    :footer="null"
    @cancel="close"
  >
    <a-tabs type="card">
      <a-tab-pane key="1" tab="责任人维护">
        <div style="margin-bottom: 10px;">
          <a-button size="small" type="primary" @click="addResponsiblePerson"
            >新增</a-button
          >
        </div>
        <a-spin :spinning="spinning1">
          <a-table
            size="small"
            :columns="dataColumns"
            :data-source="dataList"
            :pagination="{
              'show-size-changer': true,
              'show-quick-jumper': true,
              total: dataList.length,
              'page-size-options': [15, 30, 50],
              pageSize: 15,
              onChange: this.handleChangeFirstTable,
            }"
          >
            <div slot="userName" slot-scope="text, record, index">
              <template
                v-if="
                  [
                    'litingting21',
                    'wangshengzhen.ex',
                    'yueshengqi.ex',
                    'niuwendan',
                    record.signUserLdap,
                  ].includes(nowLoginUserAccount) || record.isEdit
                "
              >
                <!-- <template v-if="record.remark === 'Y'"> -->
                <a-select
                  style="width: 250px"
                  show-search
                  :value="record.userLdap"
                  :dropdownMatchSelectWidth="false"
                  placeholder="请输入责任人名称"
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  @search="handleAccountSearch($event, 'user', index)"
                  @change="handleAccountChange($event, 'user', index)"
                >
                  <a-select-option
                    v-for="d in userMapList[`user${index}`] || []"
                    :key="`${d.name}-${d.account}`"
                    :value="`${d.name}-${d.account}`"
                  >
                    {{ d.name }}({{ d.account }}) {{ d.o }}
                  </a-select-option>
                </a-select>
              </template>
              <template v-else>
                {{ text }}
              </template>
            </div>
            <div slot="userLeader" slot-scope="text, record, index">
              <template
                v-if="
                  [
                    'litingting21',
                    'wangshengzhen.ex',
                    'yueshengqi.ex',
                    'niuwendan',
                    record.signUserLdap,
                  ].includes(nowLoginUserAccount) || record.isEdit
                "
              >
                <!-- <template v-if="record.remark === 'Y'"> -->
                <a-select
                  style="width: 250px"
                  show-search
                  :value="record.userLeaderLdap"
                  :dropdownMatchSelectWidth="false"
                  placeholder="请输入直属领导名称"
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  @search="handleAccountSearch($event, 'userLeader', index)"
                  @change="handleAccountChange($event, 'userLeader', index)"
                >
                  <a-select-option
                    v-for="d in userLeaderMapList[`userLeader${index}`] || []"
                    :key="`${d.name}~&~${d.account}~&~${d.email}`"
                    :value="`${d.name}~&~${d.account}~&~${d.email}`"
                  >
                    {{ d.name }}({{ d.account }}) {{ d.o }}
                  </a-select-option>
                </a-select>
              </template>
              <template v-else>
                {{ record.userLeaderName }}
              </template>
            </div>
            <div slot="operation" slot-scope="text, record">
              <a-button type="link" size="small" @click="delZrr(record.id)"
                >删除</a-button
              >
            </div>
          </a-table>
        </a-spin>
        <div style="margin-top: 10px;display:flex;justify-content: flex-end;">
          <a-button type="primary" @click="saveCard">
            保存
          </a-button>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="抄送人维护">
        <a-form-model
          style="margin-bottom: 10px;"
          size="small"
          layout="inline"
          :model="searchForm"
        >
          <a-form-model-item label="车间">
            <a-select
              v-model="searchForm.workshop"
              style="width: 160px;"
              allowClear
            >
              <a-select-option
                :value="item"
                v-for="item in workShopList"
                :key="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="姓名">
            <a-input v-model="searchForm.userName" placeholder="姓名">
            </a-input>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" @click="getList2">
              搜索
            </a-button>
          </a-form-model-item>
        </a-form-model>
        <div style="margin-bottom: 10px;">
          <a-button
            type="primary"
            @click="visible2 = true"
            size="small"
            style="margin-right: 10px;"
          >
            新增
          </a-button>
          <a-button
            size="small"
            @click="delClick"
            :disabled="selectedRowKeys.length === 0"
          >
            删除
          </a-button>
        </div>
        <a-spin :spinning="spinning2">
          <a-table
            rowKey="id"
            size="small"
            :pagination="{
              'show-size-changer': true,
              'show-quick-jumper': true,
              total: dataList2.length,
              'page-size-options': [15, 30, 50],
              pageSize: 15,
            }"
            :columns="dataColumns2"
            :data-source="dataList2"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange,
            }"
          >
            <template slot="operation" slot-scope="text, record">
              <a-button type="link" size="small" @click="del([record.id])">
                删除
              </a-button>
            </template>
          </a-table>
        </a-spin>
      </a-tab-pane>
      <a-tab-pane key="3" tab="线长">
        <div style="margin-bottom: 10px;">
          <a-button
            type="primary"
            @click="visible3 = true"
            size="small"
            style="margin-right: 10px;"
          >
            新增
          </a-button>
        </div>
        <a-spin :spinning="spinning3">
          <a-table
            rowKey="id"
            size="small"
            :columns="dataColumnsXz"
            :data-source="dataListXz"
            :pagination="{
              'show-size-changer': true,
              'show-quick-jumper': true,
              total: dataListXz.length || 0,
              'page-size-options': ['15', '30', '50'],
              pageSize: '15',
              onChange: this.handleChangeFirstTable,
            }"
          >
            <template slot="operation" slot-scope="text, record">
              <a-button type="link" size="small" @click="editXt(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="delXt([record.id])">
                删除
              </a-button>
            </template>
          </a-table>
        </a-spin>
      </a-tab-pane>
    </a-tabs>
    <!-- 抄送人dialog -->
    <a-modal
      v-model="visible2"
      :destroyOnClose="true"
      :width="800"
      title="维护"
      @ok="handleOk"
      @cancel="close2"
    >
      <a-form-model
        ref="form"
        :rules="rules"
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="公司" prop="signOrg">
          <a-select v-model="form.signOrg" style="width: 100%;" allowClear>
            <a-select-option
              :value="item.value"
              v-for="item in company"
              :key="item"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="车间" prop="workshop">
          <a-select v-model="form.workshop" style="width: 100%;" allowClear>
            <a-select-option
              :value="item"
              v-for="item in workShopList"
              :key="item"
            >
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="人员" prop="copyUserLdap">
          <a-select
            show-search
            allowClear
            :value="form.copyUserLdap"
            :dropdownMatchSelectWidth="false"
            placeholder="请输入名称"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            @search="handleAccountSearch2($event, 'copyUserLdap')"
            @change="handleAccountChange2($event, 'copyUserLdap')"
          >
            <a-select-option
              v-for="d in copyUserLdapList"
              :key="`${d.account}`"
              :value="`${d.account}`"
            >
              {{ d.name }}({{ d.account }}) {{ d.o }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!-- 线长dialog -->
    <a-modal
      v-model="visible3"
      :destroyOnClose="true"
      :width="800"
      title="维护"
      @ok="handleOkXt"
      @cancel="closeXt"
    >
      <a-form-model
        ref="formXt"
        :rules="rulesXt"
        :model="formXt"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="公司" prop="signOrg">
          <a-select v-model="formXt.signOrg" style="width: 100%;" allowClear>
            <a-select-option
              :value="item.value"
              v-for="item in company"
              :key="item"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="车间" prop="workshop" required>
          <a-input v-model="formXt.workshop" placeholder="请输入车间"></a-input>
        </a-form-model-item>
        <a-form-model-item label="线体" prop="lineBody" required>
          <a-input v-model="formXt.lineBody" placeholder="请输入线体"></a-input>
        </a-form-model-item>
        <a-form-model-item label="班组" prop="shiftcode" required>
          <a-input
            v-model="formXt.shiftcode"
            placeholder="请输入班组"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item label="线长" prop="userLdap" required>
          <a-select
            style="width: 250px"
            show-search
            :value="formXt.userLdap"
            :dropdownMatchSelectWidth="false"
            placeholder="请输入责任人名称"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            @search="handleAccountSearchXt($event)"
            @change="handleAccountChangeXt($event, 'userLdap')"
          >
            <a-select-option
              v-for="d in userMapListXt"
              :key="`${d.name}~&~${d.account}~&~${d.email}`"
              :value="`${d.name}~&~${d.account}~&~${d.email}`"
            >
              {{ d.name }}({{ d.account }}) {{ d.o }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <!-- 责任人维护dialog -->
    <a-modal
      v-model="visible4"
      :destroyOnClose="true"
      :width="800"
      title="维护"
      @ok="handleOkZrr"
      @cancel="closeZrr"
    >
      <a-form-model
        ref="formZrr"
        :rules="rulesZrr"
        :model="formZrr"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="公司" prop="signOrg">
          <a-select v-model="formZrr.signOrg" style="width: 100%;" allowClear>
            <a-select-option
              :value="item.value"
              v-for="item in company"
              :key="item"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="车间" prop="workshop">
          <a-input
            v-model="formZrr.workshop"
            placeholder="请输入车间"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item label="不良描述" prop="des" required>
          <a-textarea
            v-model="formZrr.des"
            placeholder="请输入不良描述"
          ></a-textarea>
        </a-form-model-item>
        <a-form-model-item label="责任人" prop="userLdap" required>
          <a-select
            style="width: 250px"
            show-search
            :value="formZrr.userLdap"
            :dropdownMatchSelectWidth="false"
            placeholder="请输入责任人名称"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            @search="handleAccountSearchZrr($event)"
            @change="handleAccountChangeZrr($event, 'userLdap')"
          >
            <a-select-option
              v-for="d in userMapListZrr"
              :key="`${d.account}`"
              :value="`${d.account}`"
            >
              {{ d.name }}({{ d.account }}) {{ d.o }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          label="责任人直属领导"
          prop="userLeaderLdap"
          required
        >
          <a-select
            style="width: 250px"
            show-search
            :value="formZrr.userLeaderLdap"
            :dropdownMatchSelectWidth="false"
            placeholder="请输入责任人直属领导名称"
            :default-active-first-option="false"
            :show-arrow="false"
            :filter-option="false"
            @search="handleAccountSearchZrr($event)"
            @change="handleAccountChangeZrr($event, 'userLeaderLdap')"
          >
            <a-select-option
              v-for="d in userMapListZrr"
              :key="`${d.account}`"
              :value="`${d.account}`"
            >
              {{ d.name }}({{ d.account }}) {{ d.o }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
export default {
  data() {
    this.handleAccountSearch = debounce(this.handleAccountSearch, 800);
    this.handleAccountSearch2 = debounce(this.handleAccountSearch2, 800);
    return {
      visible: false, // 打开关闭弹窗
      visible2: false,
      dataColumns: [
        {
          title: "公司",
          dataIndex: "signOrg",
          key: "signOrg",
          width: 100,
        },
        {
          title: "不良情况描述",
          dataIndex: "des",
          key: "des",
          width: 100,
        },
        {
          title: "车间",
          dataIndex: "workshop",
          key: "workshop",
          width: 100,
        },
        {
          title: "责任人",
          dataIndex: "userName",
          key: "userName",
          width: 250,
          scopedSlots: { customRender: "userName" },
        },
        {
          title: "直属领导",
          key: "userLeader",
          dataIndex: "userLeader",
          width: 250,
          scopedSlots: { customRender: "userLeader" },
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      userMapListZrr: [], //责任人dialog选择人下拉
      visible4: false,
      dataList: [],
      userMapList: {},
      curPageStartIndex: 0,
      userLeaderMapList: {},
      dataColumns2: [
        {
          title: "公司",
          dataIndex: "signOrg",
          key: "signOrg",
          width: 100,
        },
        {
          title: "车间",
          dataIndex: "workshop",
          key: "workshop",
          width: 100,
        },
        {
          title: "姓名",
          dataIndex: "userName",
          key: "userName",
        },
        {
          title: "账号",
          dataIndex: "copyUserLdap",
          key: "copyUserLdap",
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      searchForm: {
        userName: "",
        workshop: "",
      },
      dataList2: [],
      spinning1: false,
      spinning2: false,
      workShopList: [],
      selectedRowKeys: [],
      selectedRowKeysXt: [],
      copyUserLdapList: [],
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      dataId: "",
      form: {
        workshop: "",
        copyUserLdap: "",
        signOrg: "",
      },
      rules: {
        workshop: [
          {
            required: true,
            message: "请选择车间",
            trigger: "change",
          },
        ],
        copyUserLdap: [
          {
            required: true,
            message: "请选择人员",
            trigger: "change",
          },
        ],
        signOrg: [
          {
            required: true,
            message: "请选择公司",
            trigger: "change",
          },
        ],
      },
      sign: "直通率", //不良率/隔离率
      spinning3: false,
      dataColumnsXz: [
        {
          title: "公司",
          dataIndex: "signOrg",
          key: "signOrg",
          width: 100,
        },
        {
          title: "车间",
          dataIndex: "workshop",
          key: "workshop",
          width: 100,
        },
        {
          title: "线体",
          dataIndex: "lineBody",
          key: "lineBody",
        },
        {
          title: "班组",
          dataIndex: "shiftcode",
          key: "shiftcode",
        },
        {
          title: "线长",
          dataIndex: "userLdap",
          key: "userLdap",
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      dataListXz: [],
      visible3: false,
      formXt: {
        id: "",
        workshop: "",
        lineBody: "",
        shiftcode: "",
        userLdap: "",
        sign: "直通率线长",
        signOrg: "",
      },
      userMapListXt: [],
      isEditXt: true,
      rulesXt: {
        workshop: [{ required: true, message: "请输入车间", trigger: "blur" }],
        lineBody: [{ required: true, message: "请输入线体", trigger: "blur" }],
        shiftcode: [{ required: true, message: "请输入班组", trigger: "blur" }],
        userLdap: [{ required: true, message: "请选择线长", trigger: "blur" }],
        signOrg: [{ required: true, message: "请选择公司", trigger: "blur" }],
      },
      rulesZrr: {
        workshop: [{ required: true, message: "请输入车间", trigger: "blur" }],
        des: [{ required: true, message: "请输入不良描述", trigger: "blur" }],
        userLdap: [
          { required: true, message: "请选择责任人", trigger: "blur" },
        ],
        userLeaderLdap: [
          { required: true, message: "请选择责任人直属领导", trigger: "blur" },
        ],
        signOrg: [{ required: true, message: "请选择公司", trigger: "blur" }],
      },
      formZrr: {
        id: "",
        workshop: "",
        des: "",
        userLdap: undefined,
        userLeaderLdap: undefined,
        sign: "直通率",
        signOrg: "",
      },
      company: [
        {
          item: "视像科技公司",
          value: "视像科技公司",
        },
        {
          item: "激光显示公司",
          value: "激光显示公司",
        },
      ],
    };
  },
  computed: {
    // 当前登陆用户的账号
    nowLoginUserAccount() {
      return window.vm.$store
        ? window.vm.$store.state.user.info?.loginName
        : "yueshengqi.ex";
    },
  },
  methods: {
    // type=0 问题 type=1 车间
    getList() {
      this.spinning1 = true;
      request(
        `/api/smc2/gate/searchNotifyPerson?type=0&sign=${this.sign}`
      ).then((res) => {
        this.spinning1 = false;
        this.dataList = res.map((item) => {
          return {
            ...item,
            userLdap: [
              "litingting21",
              "wangshengzhen.ex",
              "yueshengqi.ex",
              "niuwendan",
              item.userLdap,
            ].includes(this.nowLoginUserAccount)
              ? `${item.userName}~&~${item.userLdap}`
              : item.userLdap,
            signUserLdap: item.userLdap,
            userLeaderLdap: [
              "litingting21",
              "wangshengzhen.ex",
              "yueshengqi.ex",
              "niuwendan",
              item.userLdap,
            ].includes(this.nowLoginUserAccount)
              ? `${item.userLeaderName}~&~${item.userLeaderLdap}`
              : item.userLeaderName,
            isEdit: false,
          };
        });
      });
    },
    getList2() {
      this.spinning2 = true;
      request(
        `/api/smc2/gate/searchWorkShop?pageNum=1&pageSize=9999&sign=${
          this.sign
        }&workshop=${this.searchForm.workshop || ""}&userName=${this.searchForm
          .userName || ""}`
      ).then((res) => {
        this.spinning2 = false;
        this.dataList2 = res.rows || [];
      });
    },
    getList3() {
      request(`/api/smc2/gate/searchNotifyPerson?type=0&sign=直通率线长`).then(
        (res) => {
          this.spinning3 = false;
          this.dataListXz =
            res.map((item) => {
              item.userLdap = [
                "litingting21",
                "wangshengzhen.ex",
                "yueshengqi.ex",
                "niuwendan",
                item.userLdap,
              ].includes(this.nowLoginUserAccount)
                ? `${item.userName}~&~${item.userLdap}`
                : item.userLdap;
              return item;
            }) || [];
        }
      );
    },
    getWorkShopList() {
      request(`/api/smc2/gate/getWorkshop`).then((res) => {
        this.workShopList = res || [];
      });
    },
    delClick() {
      this.del(this.selectedRowKeys);
    },
    del(arr) {
      request(`/api/smc2/gate/delCopy`, {
        method: "POST",
        body: arr,
      }).then(() => {
        this.getList2();
      });
    },
    delXt(arr) {
      const _this = this;
      this.$confirm({
        title: "提示",
        content: "确定要删除吗？",
        onOk() {
          return new Promise((resolve) => {
            request(`/api/smc2/gate/delPerson`, {
              method: "POST",
              body: arr,
            }).then(() => {
              _this.getList3();
              resolve();
            });
          });
        },
        onCancel() {
          _this.$message.info("已取消操作");
        },
      });
    },
    delZrr(arr) {
      const _this = this;
      this.$confirm({
        title: "提示",
        content: "确定要删除吗？",
        onOk() {
          return new Promise((resolve) => {
            request(`/api/smc2/gate/delPerson`, {
              method: "POST",
              body: [arr],
            }).then(() => {
              _this.getList();
              resolve();
            });
          });
        },
        onCancel() {
          _this.$message.info("已取消操作");
        },
      });
    },
    delXtClick() {
      this.delXt(this.selectedRowKeysXt);
    },
    handleAccountSearch(value, key, index) {
      this.getAcountList(value).then((res) => {
        console.log(`${key}MapList`, `${key}${index}`, res);
        this.$set(this[`${key}MapList`], [`${key}${index}`], res);
      });
    },
    handleAccountChange(value, key, index) {
      console.log(this.dataList, key);
      const data = {
        ...this.dataList[this.curPageStartIndex + index],
      };
      data[`${key}Ldap`] = value;
      data["isEdit"] = true;
      this.$set(this.dataList, this.curPageStartIndex + index, data);
    },
    handleAccountSearch2(value, key) {
      this.getAcountList(value).then((res) => {
        this[`${key}List`] = res;
      });
    },
    handleAccountChange2(value, key) {
      this.$set(this.form, key, value);
    },
    getAcountList(account) {
      return new Promise((resolve) => {
        request(`/api/smc2/ldap/searchLdapUser`, {
          method: "POST",
          body: {
            account,
          },
        }).then((res) => {
          resolve(res || []);
        });
      });
    },
    show() {
      this.visible = true;
      this.getList();
      this.getList2();
      this.getList3();
      this.getWorkShopList();
    },
    close() {
      this.close2();
      this.visible = false;
      this.dataList = [];
      this.dataList2 = [];
      this.workShopList = [];
      this.userMapList = {};
      this.userLeaderMapList = {};
      this.selectedRowKeys = [];
    },
    close2() {
      this.form = {
        workshop: "",
        copyUserLdap: "",
        signOrg: "",
      };
      this.copyUserLdapList = [];
      this.$refs.form.resetFields();
      this.visible2 = false;
    },
    // 抄送人维护->新增dialog
    handleOk() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const { workshop, copyUserLdap, signOrg } = this.form;
          request(`/api/smc2/gate/batchSaveCopy`, {
            method: "POST",
            body: {
              workshop,
              copyUserLdap,
              sign: this.sign,
              signOrg,
            },
          }).then(() => {
            this.close2();
            this.getList2();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    onSelectChange(selectedRowKeys) {
      console.log("selectedRowKeys changed: ", selectedRowKeys);
      this.selectedRowKeys = selectedRowKeys;
    },
    // 保存卡片信息
    saveCard() {
      const list = this.dataList
        .filter((item) => item.isEdit)
        .map((item) => {
          const userLdap = item.userLdap.includes("~&~")
            ? item.userLdap.split("~&~")[1]
            : item.userLdap;

          let userLeaderLdapPromise;
          if (item.userLeaderLdap.includes("~&~")) {
            console.log(item.userLeaderLdap, "item.userLeaderLdap1");
            userLeaderLdapPromise = Promise.resolve(
              item.userLeaderLdap.split("~&~")[1]
            );
          } else {
            console.log(item.userLeaderLdap, "item.userLeaderLdap2");
            userLeaderLdapPromise = this.getAcountList(item.userLeaderLdap);
          }

          return userLeaderLdapPromise.then((userLeaderLdap) => ({
            des: item.des,
            workshop: item.workshop,
            userLdap: userLdap.includes("-")
              ? userLdap.split("-")[1]
              : userLdap,
            userLeaderLdap: userLeaderLdap[0].account || userLeaderLdap,
            sign: "直通率",
            signOrg: item.signOrg,
          }));
        });
      // 使用Promise.all等待所有的Promise解决
      Promise.all(list)
        .then((results) => {
          // 所有Promise都已解决，`results`是一个数组，包含了所有解决后的值
          if (results.length === 0) {
            this.$message.warning("请修改数据后提交");
            return;
          }
          request(`/api/smc2/gate/insertOrupdatePerson`, {
            method: "POST",
            body: results.map((item) => {
              return item;
            }),
          }).then((res) => {
            if (!res) {
              this.$message.success("操作成功");
              this.getList();
            }
          });
        })
        .catch((error) => {
          // 如果任何一个Promise失败，这里会捕获到错误
          console.error("An error occurred:", error);
        });
    },
    handleChangeFirstTable(page, pageSize) {
      console.log(page, pageSize);
      // 这里可以拿到分页信息 page*pageSize是之前页的数量
      this.curPageStartIndex = (page - 1) * pageSize;
    },

    handleAccountSearchXt(value) {
      this.getAcountList(value).then((res) => {
        this.userMapListXt = res;
      });
    },
    handleAccountChangeXt(value, key) {
      this.$set(this.formXt, key, value.split("~&~")[1]);
    },
    handleOkXt() {
      this.$refs.formXt.validate((valid) => {
        if (valid) {
          request("/api/smc2/gate/insertOrupdatePerson", {
            method: "POST",
            body: [
              {
                ...Object.keys(this.formXt).reduce(
                  (acc, key) => ({
                    ...acc,
                    [key.trim()]:
                      typeof this.formXt[key] === "string"
                        ? this.formXt[key].trim()
                        : this.formXt[key],
                  }),
                  {}
                ),
              },
            ],
          }).then(() => {
            this.closeXt();
            this.getList3();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    closeXt() {
      this.formXt = {
        workshop: "",
        lineBody: "",
        shiftcode: "",
        userLdap: "",
        signOrg: "",
      };
      this.visible3 = false;
    },
    editXt(record) {
      this.isEditXt = true;
      const { workshop, lineBody, shiftcode, userLdap, id } = record;
      this.formXt = { workshop, lineBody, shiftcode, userLdap, id };
      this.formXt.sign = "直通率线长";
      this.formXt.userLdap = userLdap.split("~&~")[1];
      this.visible3 = true;
    },
    addResponsiblePerson() {
      this.formZrr.sign = "直通率";
      this.visible4 = true;
    },
    handleAccountSearchZrr(value) {
      this.getAcountList(value).then((res) => {
        this.userMapListZrr = res;
      });
    },
    handleAccountChangeZrr(value, key) {
      this.formZrr[key] = value;
    },
    handleOkZrr() {
      request("/api/smc2/gate/insertOrupdatePerson", {
        method: "POST",
        body: [
          Object.keys(this.formZrr).reduce(
            (acc, key) => ({
              ...acc,
              [key.trim()]:
                typeof this.formZrr[key] === "string"
                  ? this.formZrr[key].trim()
                  : this.formZrr[key],
            }),
            {}
          ),
        ],
      }).then(() => {
        this.closeZrr();
        this.getList();
      });
    },
    closeZrr() {
      this.formZrr = {
        workshop: "",
        des: "",
        userLdap: "",
        userLeaderLdap: "",
        signOrg: "",
      };
      this.visible4 = false;
    },
  },
};
</script>
<style>
.ant-modal-body {
  height: calc(560px - 55px);
  overflow: auto;
}
</style>
