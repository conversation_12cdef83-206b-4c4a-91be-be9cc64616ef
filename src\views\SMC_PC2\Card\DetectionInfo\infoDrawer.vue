<!--
 * @Description: 卡片详情
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 10:45:41
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2025-02-11 14:12:44
-->
<template>
  <a-drawer
    placement="right"
    width="100%"
    :visible="visible"
    :closable="false"
    class="CardDetectionDrawerWrap"
  >
    <!-- 线上运行 -->
    <component
      :is="compName"
      v-bind="$attrs"
      :dataItem="dataItem"
      @close="close"
    ></component>
    <!-- 本地调试 -->
    <!-- <DetectionInfo v-bind="$attrs" :dataItem="dataItem" @close="close" /> -->
  </a-drawer>
</template>
<script>
import { publicPath } from "@/utils/utils.js";
// import DetectionInfo from "./info.vue"; // 本地调试
export default {
  // components: { DetectionInfo }, // 本地调试
  inheritAttrs: true,
  data() {
    return {
      indexDetectionInfoJSUrl:
        (window.location.host.includes("localhost")
          ? "http://smc.devapps.hisense.com"
          : "") + "/minio/mombucket/IndexDetectionInfo2.umd.min.1.0.js",
      visible: false,
      dataItem: {},
      compName: "",
    };
  },
  mounted() {
    if (!window["IndexDetectionInfo2"]) {
      const script = document.createElement("script");
      let fileUrl = this.indexDetectionInfoJSUrl;
      if (this.indexDetectionInfoJSUrl.indexOf("http") === -1) {
        fileUrl = `${publicPath}${this.indexDetectionInfoJSUrl}`;
      }
      script.src = fileUrl + `?t=${Date.now()}`;
      script.onload = () => {
        const exportCom = window["IndexDetectionInfo2"].default;
        this.compName = exportCom.myCom;
      };
      document.body.appendChild(script);
    } else {
      const exportCom = window["IndexDetectionInfo2"].default;
      this.compName = exportCom.myCom;
    }
  },
  methods: {
    /**
     * @description: 打开抽屉
     * @param {Object} item 指标详情
     */
    show(item) {
      this.dataItem = item;
      this.visible = true;
    },
    // 关闭抽屉
    close() {
      this.dataItem = {};
      this.visible = false;
    },
  },
};
</script>
<style lang="less">
.CardDetectionDrawerWrap {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  .ant-drawer-content {
    height: 100vh !important;
    overflow: hidden !important;
    .ant-drawer-body {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 0 !important;
      .__center {
        flex: 1;
        overflow-y: auto;
        padding: 0 24px;
        margin-bottom: 10px;
      }
    }
  }
  .ant-drawer-body {
    height: 100%;
    overflow-y: auto;
    position: relative;
  }
}
</style>
