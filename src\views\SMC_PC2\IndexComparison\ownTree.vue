<!--
 * @Description: 定制化tree
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 15:36:53
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-11-24 16:21:29
-->
<template>
  <div class="_ownTree">
    <div class="item plate" v-for="item in treeData" :key="item.key">
      <!-- 版块标题 -->
      <div class="title" @click="palteClick(item)">
        <div class="icon _flex">
          <a-icon
            type="caret-right"
            :class="[developPlate.includes(item.key) ? 'active' : '']"
          />
        </div>
        {{ item.title }}
      </div>
      <!-- 指标列表 -->
      <div
        class="_indexList"
        :class="[developPlate.includes(item.key) ? 'active' : '']"
      >
        <div
          class="item index"
          :class="[activeKey === zitem.key ? 'active' : '']"
          v-for="zitem in item.children"
          :key="zitem.key"
          @click="indexClick(zitem)"
        >
          <a-tooltip placement="right">
            <template slot="title">
              <span>{{ zitem.title }}</span>
            </template>
            <span>{{ zitem.title }}</span>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    treeData: Array
  },
  data() {
    return {
      activeKey: "", // 选中的指标
      activeIndexName: "", // 选中指标名称
      developPlate: [] // 扩展开的版块
    };
  },
  inject: ["treeSelect"],
  methods: {
    /**
     * @description: 版块点击
     * @param {*} item
     * @param {Boolean} fromParentCom 方法执行来自父组件
     */

    palteClick(item, fromParentCom = false) {
      if (this.developPlate.includes(item.key)) {
        if (fromParentCom) {
          return;
        }
        let index = this.developPlate.indexOf(item.key);
        this.developPlate.splice(index, 1);
      } else {
        this.developPlate.push(item.key);
      }
    },
    /**
     * @description: 指标点击
     * @param {*} item
     */
    indexClick(item) {
      this.activeKey = item.key;
      this.activeIndexName = item.title;
      this.treeSelect(this.activeKey);
    },
    // 清空数据
    clearData() {
      this.activeKey = "";
      this.activeIndexName = "";
      this.developPlate = [];
      this.treeSelect();
    },
    getActiveKey() {
      return this.activeKey;
    },
    getIndexName() {
      return this.activeIndexName;
    }
  }
};
</script>
<style lang="less" scoped>
._ownTree {
  .item.plate {
    ._indexList {
      transition: max-height 0.5s ease-in-out;
      max-height: 0;
      overflow: hidden;
      &.active {
        max-height: 1500px;
      }
    }
    & > .title,
    ._indexList .index {
      width: 100%;
      height: 32px;
      border-radius: 4px;
      line-height: 32px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
    }
    ._indexList .index {
      cursor: pointer;
      background-color: #fff;
      transition: all ease-in-out 0.3s;
      span {
        padding-left: 42px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &.active {
        background-color: #00aaa6;
        color: #fff;
      }
    }
    & > .title {
      cursor: pointer;
      .icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        .anticon {
          cursor: pointer;
          transition: all ease-in-out 0.3s;
          &.active {
            transform: rotateZ(90deg);
          }
        }
      }
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
</style>
