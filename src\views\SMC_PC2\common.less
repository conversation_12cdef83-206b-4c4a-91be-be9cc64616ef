// antd 样式重置
.indexGeneralViewPage2.classic-style, .CardDetailDrawerWrap2 {
  .ant-select-selection,
  .ant-input {
    border-radius: 2px;
  }
  .ant-radio-button-wrapper {
    &:first-child {
      border-radius: 2px 0 0 2px;
    }
    &:last-child {
      border-radius: 0 2px 2px 0;
    }
  }

  .ant-drawer-body {
    padding: 20px 24px !important;
  }

  .ant-select-selection {
    border-radius: 2px;
    background-color: rgb(242, 243, 245) !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }
  .ant-radio-group {
    .ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked) {
      border: 1px solid rgb(242, 243, 245) !important;
      background-color: rgb(242, 243, 245) !important;
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      background-color: rgb(242, 243, 245) !important;
    }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none !important;
    }
  }
  .ant-calendar-picker {
    input {
      border-radius: 2px !important;
      background-color: rgb(242, 243, 245) !important;
      border-color: transparent !important;
      box-shadow: none !important;
    }
  }
}
.indexGeneralViewPage2.hisense-style, .CardDetailDrawerWrap2 .CardDetailInfoBox2.hisense-style {
  .ant-select-selection,
  .ant-input {
    font-weight: normal;
    border-radius: 3px;
    border: 1px solid #e5e6eb;
    border-color: #e5e6eb !important;
    background-color: transparent !important;
  }
}
.indexGeneralViewPage2.hisense-style.dark {
  .ant-select-selection,
  .ant-input {
    color: #E2E8EA !important;
    border: 1px solid #4E5969 !important;
  }
  .ant-select-selection__clear, .anticon.anticon-calendar.ant-calendar-picker-icon, .anticon.anticon-down.ant-select-arrow-icon, .ant-select-selection__placeholder {
    background-color: transparent;
    color: rgba(255, 255, 255, 0.8);
  }
}

// 滚动条
::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  min-height: 28px;
  height: 5px;
  min-width: 2px;
  width: px;
  border-radius: 4px;
  padding: 100px 0 0;
  border: 1px dashed transparent;
  background-color: #b2b2b2;
  background-clip: padding-box;
}

::-webkit-scrollbar-corner {
  background: 0 0;
}

::-webkit-scrollbar-button {
  width: 0;
  height: 0;
}

::-webkit-scrollbar {
  width: 8px!important;
  height: 8px!important;
  overflow: visible;
}

::-webkit-scrollbar-thumb:hover {
  width: 12px!important;
}