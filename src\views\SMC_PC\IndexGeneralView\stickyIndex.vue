<!--
 * @Description: 置顶模块
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:42:00
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-09-03 16:32:12
-->
<template>
  <div class="_stickyIndex">
    <!-- 骨架屏 -->
    <a-skeleton active :loading="cardListLoading && enterPageTimes === 1">
      <!-- 置顶卡片 -->
      <a-row :gutter="{ xs: 16, sm: 24, md: 32, lg: 64 }">
        <a-col :span="4" v-for="(item, index) in topCardList" :key="index">
          <!-- 卡片 -->
          <div class="_card">
            <!-- 标题 -->
            <div class="_title">
              <!-- 提示信息 -->
              <a-tooltip placement="top">
                <template slot="title">
                  <span>{{ item.indexName }}</span>
                </template>
                <!-- <a-icon type="question-circle" /> -->
                <span>{{ item.indexName }}</span>
              </a-tooltip>
            </div>
            <!-- 数据 -->
            <div class="_data">
              <span class="num" :ref="`data_num${index}`">
                {{ item.realBaseActual }}
                <span>{{ item.unit }}</span>
              </span>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-skeleton>
  </div>
</template>
<script>
import { thousandSplit } from "./utils";
export default {
  props: {
    cardListLoading: Boolean,
    topCardList: Array
  },
  data() {
    return {
      thousandSplit,
      list: [],
      enterPageTimes: 0 // 进入页面次数，用来控制顶部置顶骨架屏显示的
    };
  },
  watch: {
    cardListLoading: {
      handler(val) {
        val && this.enterPageTimes++;
      },
      immediate: true
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage {
  ._stickyIndex {
    padding-bottom: 20px;
    .ant-row {
      .ant-col {
        border-right: 1px solid #f0f0f0;
        &:last-child {
          border: none;
        }
        ._card {
          ._title {
            height: 22px;
            color: rgba(0, 0, 0, 0.35);
            line-height: 22px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            .anticon {
              cursor: pointer;
            }
            span:not(.anticon) {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          ._data {
            height: 36px;
            font-family: PingFangSC-Medium;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 36px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            span:not(.num) {
              font-size: 16px;
              line-height: 36px;
            }
          }
        }
      }
    }
  }
}
</style>
