/*
 * @Description:
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2020-11-19 13:56:20
 * @LastEditors: gaorubin
 * @LastEditTime: 2020-11-19 18:39:03
 * @FilePath: /pangea-frontend/public/js/vuedraggable.umd.min.js
 */
(function(t, e) { typeof exports === "object" && typeof module === "object" ? module.exports = e(require("sortablejs")) : typeof define === "function" && define.amd ? define(["sortablejs"], e) : typeof exports === "object" ? exports["vuedraggable"] = e(require("sortablejs")) : t["vuedraggable"] = e(t["Sortable"]); })(typeof self !== "undefined" ? self : this, function(t) { return (function(t) { var e = {}; function n(r) { if (e[r]) return e[r].exports; var o = e[r] = { i: r, l: !1, exports: {} }; return t[r].call(o.exports, o, o.exports, n), o.l = !0, o.exports; } return n.m = t, n.c = e, n.d = function(t, e, r) { n.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: r }); }, n.r = function(t) { typeof Symbol !== "undefined" && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t, "__esModule", { value: !0 }); }, n.t = function(t, e) { if (1 & e && (t = n(t)), 8 & e) return t; if (4 & e && typeof t === "object" && t && t.__esModule) return t; var r = Object.create(null); if (n.r(r), Object.defineProperty(r, "default", { enumerable: !0, value: t }), 2 & e && typeof t !== "string") for (var o in t)n.d(r, o, function(e) { return t[e]; }.bind(null, o)); return r; }, n.n = function(t) { var e = t && t.__esModule ? function() { return t["default"]; } : function() { return t; }; return n.d(e, "a", e), e; }, n.o = function(t, e) { return Object.prototype.hasOwnProperty.call(t, e); }, n.p = "", n(n.s = "fb15"); }({ "01f9": function(t, e, n) { "use strict"; var r = n("2d00"); var o = n("5ca1"); var i = n("2aba"); var c = n("32e9"); var u = n("84f2"); var a = n("41a0"); var s = n("7f20"); var f = n("38fd"); var l = n("2b4c")("iterator"); var d = !([].keys && "next" in [].keys()); var p = "@@iterator"; var h = "keys"; var v = "values"; var g = function() { return this; }; t.exports = function(t, e, n, b, m, y, x) { a(n, e, b); var O; var S; var w; var j = function(t) { if (!d && t in _) return _[t]; switch (t) { case h:return function() { return new n(this, t); }; case v:return function() { return new n(this, t); }; } return function() { return new n(this, t); }; }; var M = e + " Iterator"; var C = m == v; var T = !1; var _ = t.prototype; var L = _[l] || _[p] || m && _[m]; var I = L || j(m); var E = m ? C ? j("entries") : I : void 0; var P = e == "Array" && _.entries || L; if (P && (w = f(P.call(new t())), w !== Object.prototype && w.next && (s(w, M, !0), r || typeof w[l] === "function" || c(w, l, g))), C && L && L.name !== v && (T = !0, I = function() { return L.call(this); }), r && !x || !d && !T && _[l] || c(_, l, I), u[e] = I, u[M] = g, m) if (O = { values: C ? I : j(v), keys: y ? I : j(h), entries: E }, x) for (S in O)S in _ || i(_, S, O[S]); else o(o.P + o.F * (d || T), e, O); return O; }; }, "02f4": function(t, e, n) { var r = n("4588"); var o = n("be13"); t.exports = function(t) { return function(e, n) { var i; var c; var u = String(o(e)); var a = r(n); var s = u.length; return a < 0 || a >= s ? t ? "" : void 0 : (i = u.charCodeAt(a), i < 55296 || i > 56319 || a + 1 === s || (c = u.charCodeAt(a + 1)) < 56320 || c > 57343 ? t ? u.charAt(a) : i : t ? u.slice(a, a + 2) : c - 56320 + (i - 55296 << 10) + 65536); }; }; }, "0390": function(t, e, n) { "use strict"; var r = n("02f4")(!0); t.exports = function(t, e, n) { return e + (n ? r(t, e).length : 1); }; }, "0bfb": function(t, e, n) { "use strict"; var r = n("cb7c"); t.exports = function() { var t = r(this); var e = ""; return t.global && (e += "g"), t.ignoreCase && (e += "i"), t.multiline && (e += "m"), t.unicode && (e += "u"), t.sticky && (e += "y"), e; }; }, "0d58": function(t, e, n) { var r = n("ce10"); var o = n("e11e"); t.exports = Object.keys || function(t) { return r(t, o); }; }, 1495: function(t, e, n) { var r = n("86cc"); var o = n("cb7c"); var i = n("0d58"); t.exports = n("9e1e") ? Object.defineProperties : function(t, e) { o(t); var n; var c = i(e); var u = c.length; var a = 0; while (u > a)r.f(t, n = c[a++], e[n]); return t; }; }, "214f": function(t, e, n) { "use strict"; n("b0c5"); var r = n("2aba"); var o = n("32e9"); var i = n("79e5"); var c = n("be13"); var u = n("2b4c"); var a = n("520a"); var s = u("species"); var f = !i(function() { var t = /./; return t.exec = function() { var t = []; return t.groups = { a: "7" }, t; }, "".replace(t, "$<a>") !== "7"; }); var l = (function() { var t = /(?:)/; var e = t.exec; t.exec = function() { return e.apply(this, arguments); }; var n = "ab".split(t); return n.length === 2 && n[0] === "a" && n[1] === "b"; }()); t.exports = function(t, e, n) { var d = u(t); var p = !i(function() { var e = {}; return e[d] = function() { return 7; }, ""[t](e) != 7; }); var h = p ? !i(function() { var e = !1; var n = /a/; return n.exec = function() { return e = !0, null; }, t === "split" && (n.constructor = {}, n.constructor[s] = function() { return n; }), n[d](""), !e; }) : void 0; if (!p || !h || t === "replace" && !f || t === "split" && !l) { var v = /./[d]; var g = n(c, d, ""[t], function(t, e, n, r, o) { return e.exec === a ? p && !o ? { done: !0, value: v.call(e, n, r) } : { done: !0, value: t.call(n, e, r) } : { done: !1 }; }); var b = g[0]; var m = g[1]; r(String.prototype, t, b), o(RegExp.prototype, d, e == 2 ? function(t, e) { return m.call(t, this, e); } : function(t) { return m.call(t, this); }); } }; }, "230e": function(t, e, n) { var r = n("d3f4"); var o = n("7726").document; var i = r(o) && r(o.createElement); t.exports = function(t) { return i ? o.createElement(t) : {}; }; }, "23c6": function(t, e, n) { var r = n("2d95"); var o = n("2b4c")("toStringTag"); var i = r(function() { return arguments; }()) == "Arguments"; var c = function(t, e) { try { return t[e]; } catch (n) {} }; t.exports = function(t) { var e, n, u; return void 0 === t ? "Undefined" : t === null ? "Null" : typeof (n = c(e = Object(t), o)) === "string" ? n : i ? r(e) : (u = r(e)) == "Object" && typeof e.callee === "function" ? "Arguments" : u; }; }, 2621: function(t, e) { e.f = Object.getOwnPropertySymbols; }, "2aba": function(t, e, n) { var r = n("7726"); var o = n("32e9"); var i = n("69a8"); var c = n("ca5a")("src"); var u = n("fa5b"); var a = "toString"; var s = ("" + u).split(a); n("8378").inspectSource = function(t) { return u.call(t); }, (t.exports = function(t, e, n, u) { var a = typeof n === "function"; a && (i(n, "name") || o(n, "name", e)), t[e] !== n && (a && (i(n, c) || o(n, c, t[e] ? "" + t[e] : s.join(String(e)))), t === r ? t[e] = n : u ? t[e] ? t[e] = n : o(t, e, n) : (delete t[e], o(t, e, n))); })(Function.prototype, a, function() { return typeof this === "function" && this[c] || u.call(this); }); }, "2aeb": function(t, e, n) { var r = n("cb7c"); var o = n("1495"); var i = n("e11e"); var c = n("613b")("IE_PROTO"); var u = function() {}; var a = "prototype"; var s = function() { var t; var e = n("230e")("iframe"); var r = i.length; var o = "<"; var c = ">"; e.style.display = "none", n("fab2").appendChild(e), e.src = "javascript:", t = e.contentWindow.document, t.open(), t.write(o + "script" + c + "document.F=Object" + o + "/script" + c), t.close(), s = t.F; while (r--) delete s[a][i[r]]; return s(); }; t.exports = Object.create || function(t, e) { var n; return t !== null ? (u[a] = r(t), n = new u(), u[a] = null, n[c] = t) : n = s(), void 0 === e ? n : o(n, e); }; }, "2b4c": function(t, e, n) { var r = n("5537")("wks"); var o = n("ca5a"); var i = n("7726").Symbol; var c = typeof i === "function"; var u = t.exports = function(t) { return r[t] || (r[t] = c && i[t] || (c ? i : o)("Symbol." + t)); }; u.store = r; }, "2d00": function(t, e) { t.exports = !1; }, "2d95": function(t, e) { var n = {}.toString; t.exports = function(t) { return n.call(t).slice(8, -1); }; }, "2fdb": function(t, e, n) { "use strict"; var r = n("5ca1"); var o = n("d2c8"); var i = "includes"; r(r.P + r.F * n("5147")(i), "String", { includes: function(t) { return !!~o(this, t, i).indexOf(t, arguments.length > 1 ? arguments[1] : void 0); } }); }, "32e9": function(t, e, n) { var r = n("86cc"); var o = n("4630"); t.exports = n("9e1e") ? function(t, e, n) { return r.f(t, e, o(1, n)); } : function(t, e, n) { return t[e] = n, t; }; }, "38fd": function(t, e, n) { var r = n("69a8"); var o = n("4bf8"); var i = n("613b")("IE_PROTO"); var c = Object.prototype; t.exports = Object.getPrototypeOf || function(t) { return t = o(t), r(t, i) ? t[i] : typeof t.constructor === "function" && t instanceof t.constructor ? t.constructor.prototype : t instanceof Object ? c : null; }; }, "41a0": function(t, e, n) { "use strict"; var r = n("2aeb"); var o = n("4630"); var i = n("7f20"); var c = {}; n("32e9")(c, n("2b4c")("iterator"), function() { return this; }), t.exports = function(t, e, n) { t.prototype = r(c, { next: o(1, n) }), i(t, e + " Iterator"); }; }, "456d": function(t, e, n) { var r = n("4bf8"); var o = n("0d58"); n("5eda")("keys", function() { return function(t) { return o(r(t)); }; }); }, 4588: function(t, e) { var n = Math.ceil; var r = Math.floor; t.exports = function(t) { return isNaN(t = +t) ? 0 : (t > 0 ? r : n)(t); }; }, 4630: function(t, e) { t.exports = function(t, e) { return { enumerable: !(1 & t), configurable: !(2 & t), writable: !(4 & t), value: e }; }; }, "4bf8": function(t, e, n) { var r = n("be13"); t.exports = function(t) { return Object(r(t)); }; }, 5147: function(t, e, n) { var r = n("2b4c")("match"); t.exports = function(t) { var e = /./; try { "/./"[t](e); } catch (n) { try { return e[r] = !1, !"/./"[t](e); } catch (o) {} } return !0; }; }, "520a": function(t, e, n) { "use strict"; var r = n("0bfb"); var o = RegExp.prototype.exec; var i = String.prototype.replace; var c = o; var u = "lastIndex"; var a = (function() { var t = /a/; var e = /b*/g; return o.call(t, "a"), o.call(e, "a"), t[u] !== 0 || e[u] !== 0; }()); var s = void 0 !== /()??/.exec("")[1]; var f = a || s; f && (c = function(t) { var e; var n; var c; var f; var l = this; return s && (n = new RegExp("^" + l.source + "$(?!\\s)", r.call(l))), a && (e = l[u]), c = o.call(l, t), a && c && (l[u] = l.global ? c.index + c[0].length : e), s && c && c.length > 1 && i.call(c[0], n, function() { for (f = 1; f < arguments.length - 2; f++) void 0 === arguments[f] && (c[f] = void 0); }), c; }), t.exports = c; }, "52a7": function(t, e) { e.f = {}.propertyIsEnumerable; }, 5537: function(t, e, n) { var r = n("8378"); var o = n("7726"); var i = "__core-js_shared__"; var c = o[i] || (o[i] = {}); (t.exports = function(t, e) { return c[t] || (c[t] = void 0 !== e ? e : {}); })("versions", []).push({ version: r.version, mode: n("2d00") ? "pure" : "global", copyright: "© 2019 Denis Pushkarev (zloirock.ru)" }); }, "5ca1": function(t, e, n) { var r = n("7726"); var o = n("8378"); var i = n("32e9"); var c = n("2aba"); var u = n("9b43"); var a = "prototype"; var s = function(t, e, n) { var f; var l; var d; var p; var h = t & s.F; var v = t & s.G; var g = t & s.S; var b = t & s.P; var m = t & s.B; var y = v ? r : g ? r[e] || (r[e] = {}) : (r[e] || {})[a]; var x = v ? o : o[e] || (o[e] = {}); var O = x[a] || (x[a] = {}); for (f in v && (n = e), n)l = !h && y && void 0 !== y[f], d = (l ? y : n)[f], p = m && l ? u(d, r) : b && typeof d === "function" ? u(Function.call, d) : d, y && c(y, f, d, t & s.U), x[f] != d && i(x, f, p), b && O[f] != d && (O[f] = d); }; r.core = o, s.F = 1, s.G = 2, s.S = 4, s.P = 8, s.B = 16, s.W = 32, s.U = 64, s.R = 128, t.exports = s; }, "5eda": function(t, e, n) { var r = n("5ca1"); var o = n("8378"); var i = n("79e5"); t.exports = function(t, e) { var n = (o.Object || {})[t] || Object[t]; var c = {}; c[t] = e(n), r(r.S + r.F * i(function() { n(1); }), "Object", c); }; }, "5f1b": function(t, e, n) { "use strict"; var r = n("23c6"); var o = RegExp.prototype.exec; t.exports = function(t, e) { var n = t.exec; if (typeof n === "function") { var i = n.call(t, e); if (typeof i !== "object") throw new TypeError("RegExp exec method returned something other than an Object or null"); return i; } if (r(t) !== "RegExp") throw new TypeError("RegExp#exec called on incompatible receiver"); return o.call(t, e); }; }, "613b": function(t, e, n) { var r = n("5537")("keys"); var o = n("ca5a"); t.exports = function(t) { return r[t] || (r[t] = o(t)); }; }, "626a": function(t, e, n) { var r = n("2d95"); t.exports = Object("z").propertyIsEnumerable(0) ? Object : function(t) { return r(t) == "String" ? t.split("") : Object(t); }; }, 6762: function(t, e, n) { "use strict"; var r = n("5ca1"); var o = n("c366")(!0); r(r.P, "Array", { includes: function(t) { return o(this, t, arguments.length > 1 ? arguments[1] : void 0); } }), n("9c6c")("includes"); }, 6821: function(t, e, n) { var r = n("626a"); var o = n("be13"); t.exports = function(t) { return r(o(t)); }; }, "69a8": function(t, e) { var n = {}.hasOwnProperty; t.exports = function(t, e) { return n.call(t, e); }; }, "6a99": function(t, e, n) { var r = n("d3f4"); t.exports = function(t, e) { if (!r(t)) return t; var n, o; if (e && typeof (n = t.toString) === "function" && !r(o = n.call(t))) return o; if (typeof (n = t.valueOf) === "function" && !r(o = n.call(t))) return o; if (!e && typeof (n = t.toString) === "function" && !r(o = n.call(t))) return o; throw TypeError("Can't convert object to primitive value"); }; }, 7333: function(t, e, n) { "use strict"; var r = n("9e1e"); var o = n("0d58"); var i = n("2621"); var c = n("52a7"); var u = n("4bf8"); var a = n("626a"); var s = Object.assign; t.exports = !s || n("79e5")(function() { var t = {}; var e = {}; var n = Symbol(); var r = "abcdefghijklmnopqrst"; return t[n] = 7, r.split("").forEach(function(t) { e[t] = t; }), s({}, t)[n] != 7 || Object.keys(s({}, e)).join("") != r; }) ? function(t, e) { var n = u(t); var s = arguments.length; var f = 1; var l = i.f; var d = c.f; while (s > f) { var p; var h = a(arguments[f++]); var v = l ? o(h).concat(l(h)) : o(h); var g = v.length; var b = 0; while (g > b)p = v[b++], r && !d.call(h, p) || (n[p] = h[p]); } return n; } : s; }, 7726: function(t, e) { var n = t.exports = typeof window !== "undefined" && window.Math == Math ? window : typeof self !== "undefined" && self.Math == Math ? self : Function("return this")(); typeof __g === "number" && (__g = n); }, "77f1": function(t, e, n) { var r = n("4588"); var o = Math.max; var i = Math.min; t.exports = function(t, e) { return t = r(t), t < 0 ? o(t + e, 0) : i(t, e); }; }, "79e5": function(t, e) { t.exports = function(t) { try { return !!t(); } catch (e) { return !0; } }; }, "7f20": function(t, e, n) { var r = n("86cc").f; var o = n("69a8"); var i = n("2b4c")("toStringTag"); t.exports = function(t, e, n) { t && !o(t = n ? t : t.prototype, i) && r(t, i, { configurable: !0, value: e }); }; }, 8378: function(t, e) { var n = t.exports = { version: "2.6.11" }; typeof __e === "number" && (__e = n); }, "84f2": function(t, e) { t.exports = {}; }, "86cc": function(t, e, n) { var r = n("cb7c"); var o = n("c69a"); var i = n("6a99"); var c = Object.defineProperty; e.f = n("9e1e") ? Object.defineProperty : function(t, e, n) { if (r(t), e = i(e, !0), r(n), o) try { return c(t, e, n); } catch (u) {} if ("get" in n || "set" in n) throw TypeError("Accessors not supported!"); return "value" in n && (t[e] = n.value), t; }; }, "9b43": function(t, e, n) { var r = n("d8e8"); t.exports = function(t, e, n) { if (r(t), void 0 === e) return t; switch (n) { case 1:return function(n) { return t.call(e, n); }; case 2:return function(n, r) { return t.call(e, n, r); }; case 3:return function(n, r, o) { return t.call(e, n, r, o); }; } return function() { return t.apply(e, arguments); }; }; }, "9c6c": function(t, e, n) { var r = n("2b4c")("unscopables"); var o = Array.prototype; void 0 == o[r] && n("32e9")(o, r, {}), t.exports = function(t) { o[r][t] = !0; }; }, "9def": function(t, e, n) { var r = n("4588"); var o = Math.min; t.exports = function(t) { return t > 0 ? o(r(t), 9007199254740991) : 0; }; }, "9e1e": function(t, e, n) { t.exports = !n("79e5")(function() { return Object.defineProperty({}, "a", { get: function() { return 7; } }).a != 7; }); }, a352: function(e, n) { e.exports = t; }, a481: function(t, e, n) { "use strict"; var r = n("cb7c"); var o = n("4bf8"); var i = n("9def"); var c = n("4588"); var u = n("0390"); var a = n("5f1b"); var s = Math.max; var f = Math.min; var l = Math.floor; var d = /\$([$&`']|\d\d?|<[^>]*>)/g; var p = /\$([$&`']|\d\d?)/g; var h = function(t) { return void 0 === t ? t : String(t); }; n("214f")("replace", 2, function(t, e, n, v) { return [function(r, o) { var i = t(this); var c = void 0 == r ? void 0 : r[e]; return void 0 !== c ? c.call(r, i, o) : n.call(String(i), r, o); }, function(t, e) { var o = v(n, t, this, e); if (o.done) return o.value; var l = r(t); var d = String(this); var p = typeof e === "function"; p || (e = String(e)); var b = l.global; if (b) { var m = l.unicode; l.lastIndex = 0; } var y = []; while (1) { var x = a(l, d); if (x === null) break; if (y.push(x), !b) break; var O = String(x[0]); O === "" && (l.lastIndex = u(d, i(l.lastIndex), m)); } for (var S = "", w = 0, j = 0; j < y.length; j++) { x = y[j]; for (var M = String(x[0]), C = s(f(c(x.index), d.length), 0), T = [], _ = 1; _ < x.length; _++)T.push(h(x[_])); var L = x.groups; if (p) { var I = [M].concat(T, C, d); void 0 !== L && I.push(L); var E = String(e.apply(void 0, I)); } else E = g(M, d, C, T, L, e); C >= w && (S += d.slice(w, C) + E, w = C + M.length); } return S + d.slice(w); }]; function g(t, e, r, i, c, u) { var a = r + t.length; var s = i.length; var f = p; return void 0 !== c && (c = o(c), f = d), n.call(u, f, function(n, o) { var u; switch (o.charAt(0)) { case "$":return "$"; case "&":return t; case "`":return e.slice(0, r); case "'":return e.slice(a); case "<":u = c[o.slice(1, -1)]; break; default:var f = +o; if (f === 0) return n; if (f > s) { var d = l(f / 10); return d === 0 ? n : d <= s ? void 0 === i[d - 1] ? o.charAt(1) : i[d - 1] + o.charAt(1) : n; }u = i[f - 1]; } return void 0 === u ? "" : u; }); } }); }, aae3: function(t, e, n) { var r = n("d3f4"); var o = n("2d95"); var i = n("2b4c")("match"); t.exports = function(t) { var e; return r(t) && (void 0 !== (e = t[i]) ? !!e : o(t) == "RegExp"); }; }, ac6a: function(t, e, n) { for (var r = n("cadf"), o = n("0d58"), i = n("2aba"), c = n("7726"), u = n("32e9"), a = n("84f2"), s = n("2b4c"), f = s("iterator"), l = s("toStringTag"), d = a.Array, p = { CSSRuleList: !0, CSSStyleDeclaration: !1, CSSValueList: !1, ClientRectList: !1, DOMRectList: !1, DOMStringList: !1, DOMTokenList: !0, DataTransferItemList: !1, FileList: !1, HTMLAllCollection: !1, HTMLCollection: !1, HTMLFormElement: !1, HTMLSelectElement: !1, MediaList: !0, MimeTypeArray: !1, NamedNodeMap: !1, NodeList: !0, PaintRequestList: !1, Plugin: !1, PluginArray: !1, SVGLengthList: !1, SVGNumberList: !1, SVGPathSegList: !1, SVGPointList: !1, SVGStringList: !1, SVGTransformList: !1, SourceBufferList: !1, StyleSheetList: !0, TextTrackCueList: !1, TextTrackList: !1, TouchList: !1 }, h = o(p), v = 0; v < h.length; v++) { var g; var b = h[v]; var m = p[b]; var y = c[b]; var x = y && y.prototype; if (x && (x[f] || u(x, f, d), x[l] || u(x, l, b), a[b] = d, m)) for (g in r)x[g] || i(x, g, r[g], !0); } }, b0c5: function(t, e, n) { "use strict"; var r = n("520a"); n("5ca1")({ target: "RegExp", proto: !0, forced: r !== /./.exec }, { exec: r }); }, be13: function(t, e) { t.exports = function(t) { if (void 0 == t) throw TypeError("Can't call method on  " + t); return t; }; }, c366: function(t, e, n) { var r = n("6821"); var o = n("9def"); var i = n("77f1"); t.exports = function(t) { return function(e, n, c) { var u; var a = r(e); var s = o(a.length); var f = i(c, s); if (t && n != n) { while (s > f) if (u = a[f++], u != u) return !0; } else for (;s > f; f++) if ((t || f in a) && a[f] === n) return t || f || 0; return !t && -1; }; }; }, c649: function(t, e, n) { "use strict"; (function(t) { n.d(e, "c", function() { return s; }), n.d(e, "a", function() { return u; }), n.d(e, "b", function() { return o; }), n.d(e, "d", function() { return a; }); n("a481"); function r() { return typeof window !== "undefined" ? window.console : t.console; } var o = r(); function i(t) { var e = Object.create(null); return function(n) { var r = e[n]; return r || (e[n] = t(n)); }; } var c = /-(\w)/g; var u = i(function(t) { return t.replace(c, function(t, e) { return e ? e.toUpperCase() : ""; }); }); function a(t) { t.parentElement !== null && t.parentElement.removeChild(t); } function s(t, e, n) { var r = n === 0 ? t.children[0] : t.children[n - 1].nextSibling; t.insertBefore(e, r); } }).call(this, n("c8ba")); }, c69a: function(t, e, n) { t.exports = !n("9e1e") && !n("79e5")(function() { return Object.defineProperty(n("230e")("div"), "a", { get: function() { return 7; } }).a != 7; }); }, c8ba: function(t, e) { var n; n = (function() { return this; }()); try { n = n || new Function("return this")(); } catch (r) { typeof window === "object" && (n = window); }t.exports = n; }, ca5a: function(t, e) { var n = 0; var r = Math.random(); t.exports = function(t) { return "Symbol(".concat(void 0 === t ? "" : t, ")_", (++n + r).toString(36)); }; }, cadf: function(t, e, n) { "use strict"; var r = n("9c6c"); var o = n("d53b"); var i = n("84f2"); var c = n("6821"); t.exports = n("01f9")(Array, "Array", function(t, e) { this._t = c(t), this._i = 0, this._k = e; }, function() { var t = this._t; var e = this._k; var n = this._i++; return !t || n >= t.length ? (this._t = void 0, o(1)) : o(0, e == "keys" ? n : e == "values" ? t[n] : [n, t[n]]); }, "values"), i.Arguments = i.Array, r("keys"), r("values"), r("entries"); }, cb7c: function(t, e, n) { var r = n("d3f4"); t.exports = function(t) { if (!r(t)) throw TypeError(t + " is not an object!"); return t; }; }, ce10: function(t, e, n) { var r = n("69a8"); var o = n("6821"); var i = n("c366")(!1); var c = n("613b")("IE_PROTO"); t.exports = function(t, e) { var n; var u = o(t); var a = 0; var s = []; for (n in u)n != c && r(u, n) && s.push(n); while (e.length > a)r(u, n = e[a++]) && (~i(s, n) || s.push(n)); return s; }; }, d2c8: function(t, e, n) { var r = n("aae3"); var o = n("be13"); t.exports = function(t, e, n) { if (r(e)) throw TypeError("String#" + n + " doesn't accept regex!"); return String(o(t)); }; }, d3f4: function(t, e) { t.exports = function(t) { return typeof t === "object" ? t !== null : typeof t === "function"; }; }, d53b: function(t, e) { t.exports = function(t, e) { return { value: e, done: !!t }; }; }, d8e8: function(t, e) { t.exports = function(t) { if (typeof t !== "function") throw TypeError(t + " is not a function!"); return t; }; }, e11e: function(t, e) { t.exports = "constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","); }, f559: function(t, e, n) { "use strict"; var r = n("5ca1"); var o = n("9def"); var i = n("d2c8"); var c = "startsWith"; var u = ""[c]; r(r.P + r.F * n("5147")(c), "String", { startsWith: function(t) { var e = i(this, t, c); var n = o(Math.min(arguments.length > 1 ? arguments[1] : void 0, e.length)); var r = String(t); return u ? u.call(e, r, n) : e.slice(n, n + r.length) === r; } }); }, f6fd: function(t, e) { (function(t) { var e = "currentScript"; var n = t.getElementsByTagName("script"); e in t || Object.defineProperty(t, e, { get: function() { try { throw new Error(); } catch (r) { var t; var e = (/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(r.stack) || [!1])[1]; for (t in n) if (n[t].src == e || n[t].readyState == "interactive") return n[t]; return null; } } }); })(document); }, f751: function(t, e, n) { var r = n("5ca1"); r(r.S + r.F, "Object", { assign: n("7333") }); }, fa5b: function(t, e, n) { t.exports = n("5537")("native-function-to-string", Function.toString); }, fab2: function(t, e, n) { var r = n("7726").document; t.exports = r && r.documentElement; }, fb15: function(t, e, n) { "use strict"; var r; (n.r(e), typeof window !== "undefined") && (n("f6fd"), (r = window.document.currentScript) && (r = r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/)) && (n.p = r[1])); n("f751"), n("f559"), n("ac6a"), n("cadf"), n("456d"); function o(t) { if (Array.isArray(t)) return t; } function i(t, e) { if (typeof Symbol !== "undefined" && Symbol.iterator in Object(t)) { var n = []; var r = !0; var o = !1; var i = void 0; try { for (var c, u = t[Symbol.iterator](); !(r = (c = u.next()).done); r = !0) if (n.push(c.value), e && n.length === e) break; } catch (a) { o = !0, i = a; } finally { try { r || u["return"] == null || u["return"](); } finally { if (o) throw i; } } return n; } } function c(t, e) { (e == null || e > t.length) && (e = t.length); for (var n = 0, r = new Array(e); n < e; n++)r[n] = t[n]; return r; } function u(t, e) { if (t) { if (typeof t === "string") return c(t, e); var n = Object.prototype.toString.call(t).slice(8, -1); return n === "Object" && t.constructor && (n = t.constructor.name), n === "Map" || n === "Set" ? Array.from(t) : n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? c(t, e) : void 0; } } function a() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } function s(t, e) { return o(t) || i(t, e) || u(t, e) || a(); }n("6762"), n("2fdb"); function f(t) { if (Array.isArray(t)) return c(t); } function l(t) { if (typeof Symbol !== "undefined" && Symbol.iterator in Object(t)) return Array.from(t); } function d() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } function p(t) { return f(t) || l(t) || u(t) || d(); } var h = n("a352"); var v = n.n(h); var g = n("c649"); function b(t, e, n) { return void 0 === n || (t = t || {}, t[e] = n), t; } function m(t, e) { return t.map(function(t) { return t.elm; }).indexOf(e); } function y(t, e, n, r) { if (!t) return []; var o = t.map(function(t) { return t.elm; }); var i = e.length - r; var c = p(e).map(function(t, e) { return e >= i ? o.length : o.indexOf(t); }); return n ? c.filter(function(t) { return t !== -1; }) : c; } function x(t, e) { var n = this; this.$nextTick(function() { return n.$emit(t.toLowerCase(), e); }); } function O(t) { var e = this; return function(n) { e.realList !== null && e["onDrag" + t](n), x.call(e, t, n); }; } function S(t) { return ["transition-group", "TransitionGroup"].includes(t); } function w(t) { if (!t || t.length !== 1) return !1; var e = s(t, 1); var n = e[0].componentOptions; return !!n && S(n.tag); } function j(t, e, n) { return t[n] || (e[n] ? e[n]() : void 0); } function M(t, e, n) { var r = 0; var o = 0; var i = j(e, n, "header"); i && (r = i.length, t = t ? [].concat(p(i), p(t)) : p(i)); var c = j(e, n, "footer"); return c && (o = c.length, t = t ? [].concat(p(t), p(c)) : p(c)), { children: t, headerOffset: r, footerOffset: o }; } function C(t, e) { var n = null; var r = function(t, e) { n = b(n, t, e); }; var o = Object.keys(t).filter(function(t) { return t === "id" || t.startsWith("data-"); }).reduce(function(e, n) { return e[n] = t[n], e; }, {}); if (r("attrs", o), !e) return n; var i = e.on; var c = e.props; var u = e.attrs; return r("on", i), r("props", c), Object.assign(n.attrs, u), n; } var T = ["Start", "Add", "Remove", "Update", "End"]; var _ = ["Choose", "Unchoose", "Sort", "Filter", "Clone"]; var L = ["Move"].concat(T, _).map(function(t) { return "on" + t; }); var I = null; var E = { options: Object, list: { type: Array, required: !1, default: null }, value: { type: Array, required: !1, default: null }, noTransitionOnDrag: { type: Boolean, default: !1 }, clone: { type: Function, default: function(t) { return t; } }, element: { type: String, default: "div" }, tag: { type: String, default: null }, move: { type: Function, default: null }, componentData: { type: Object, required: !1, default: null } }; var P = { name: "draggable", inheritAttrs: !1, props: E, data: function() { return { transitionMode: !1, noneFunctionalComponentMode: !1 }; }, render: function(t) { var e = this.$slots.default; this.transitionMode = w(e); var n = M(e, this.$slots, this.$scopedSlots); var r = n.children; var o = n.headerOffset; var i = n.footerOffset; this.headerOffset = o, this.footerOffset = i; var c = C(this.$attrs, this.componentData); return t(this.getTag(), c, r); }, created: function() { this.list !== null && this.value !== null && g["b"].error("Value and list props are mutually exclusive! Please set one or another."), this.element !== "div" && g["b"].warn("Element props is deprecated please use tag props instead. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#element-props"), void 0 !== this.options && g["b"].warn("Options props is deprecated, add sortable options directly as vue.draggable item, or use v-bind. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#options-props"); }, mounted: function() { var t = this; if (this.noneFunctionalComponentMode = this.getTag().toLowerCase() !== this.$el.nodeName.toLowerCase() && !this.getIsFunctional(), this.noneFunctionalComponentMode && this.transitionMode) throw new Error("Transition-group inside component is not supported. Please alter tag value or remove transition-group. Current tag value: ".concat(this.getTag())); var e = {}; T.forEach(function(n) { e["on" + n] = O.call(t, n); }), _.forEach(function(n) { e["on" + n] = x.bind(t, n); }); var n = Object.keys(this.$attrs).reduce(function(e, n) { return e[Object(g["a"])(n)] = t.$attrs[n], e; }, {}); var r = Object.assign({}, this.options, n, e, { onMove: function(e, n) { return t.onDragMove(e, n); } }); !("draggable" in r) && (r.draggable = ">*"), this._sortable = new v.a(this.rootContainer, r), this.computeIndexes(); }, beforeDestroy: function() { void 0 !== this._sortable && this._sortable.destroy(); }, computed: { rootContainer: function() { return this.transitionMode ? this.$el.children[0] : this.$el; }, realList: function() { return this.list ? this.list : this.value; } }, watch: { options: { handler: function(t) { this.updateOptions(t); }, deep: !0 }, $attrs: { handler: function(t) { this.updateOptions(t); }, deep: !0 }, realList: function() { this.computeIndexes(); } }, methods: { getIsFunctional: function() { var t = this._vnode.fnOptions; return t && t.functional; }, getTag: function() { return this.tag || this.element; }, updateOptions: function(t) { for (var e in t) { var n = Object(g["a"])(e); L.indexOf(n) === -1 && this._sortable.option(n, t[e]); } }, getChildrenNodes: function() { if (this.noneFunctionalComponentMode) return this.$children[0].$slots.default; var t = this.$slots.default; return this.transitionMode ? t[0].child.$slots.default : t; }, computeIndexes: function() { var t = this; this.$nextTick(function() { t.visibleIndexes = y(t.getChildrenNodes(), t.rootContainer.children, t.transitionMode, t.footerOffset); }); }, getUnderlyingVm: function(t) { var e = m(this.getChildrenNodes() || [], t); if (e === -1) return null; var n = this.realList[e]; return { index: e, element: n }; }, getUnderlyingPotencialDraggableComponent: function(t) { var e = t.__vue__; return e && e.$options && S(e.$options._componentTag) ? e.$parent : !("realList" in e) && e.$children.length === 1 && "realList" in e.$children[0] ? e.$children[0] : e; }, emitChanges: function(t) { var e = this; this.$nextTick(function() { e.$emit("change", t); }); }, alterList: function(t) { if (this.list)t(this.list); else { var e = p(this.value); t(e), this.$emit("input", e); } }, spliceList: function() { var t = arguments; var e = function(e) { return e.splice.apply(e, p(t)); }; this.alterList(e); }, updatePosition: function(t, e) { var n = function(n) { return n.splice(e, 0, n.splice(t, 1)[0]); }; this.alterList(n); }, getRelatedContextFromMoveEvent: function(t) { var e = t.to; var n = t.related; var r = this.getUnderlyingPotencialDraggableComponent(e); if (!r) return { component: r }; var o = r.realList; var i = { list: o, component: r }; if (e !== n && o && r.getUnderlyingVm) { var c = r.getUnderlyingVm(n); if (c) return Object.assign(c, i); } return i; }, getVmIndex: function(t) { var e = this.visibleIndexes; var n = e.length; return t > n - 1 ? n : e[t]; }, getComponent: function() { return this.$slots.default[0].componentInstance; }, resetTransitionData: function(t) { if (this.noTransitionOnDrag && this.transitionMode) { var e = this.getChildrenNodes(); e[t].data = null; var n = this.getComponent(); n.children = [], n.kept = void 0; } }, onDragStart: function(t) { this.context = this.getUnderlyingVm(t.item), t.item._underlying_vm_ = this.clone(this.context.element), I = t.item; }, onDragAdd: function(t) { var e = t.item._underlying_vm_; if (void 0 !== e) { Object(g["d"])(t.item); var n = this.getVmIndex(t.newIndex); this.spliceList(n, 0, e), this.computeIndexes(); var r = { element: e, newIndex: n }; this.emitChanges({ added: r }); } }, onDragRemove: function(t) { if (Object(g["c"])(this.rootContainer, t.item, t.oldIndex), t.pullMode !== "clone") { var e = this.context.index; this.spliceList(e, 1); var n = { element: this.context.element, oldIndex: e }; this.resetTransitionData(e), this.emitChanges({ removed: n }); } else Object(g["d"])(t.clone); }, onDragUpdate: function(t) { Object(g["d"])(t.item), Object(g["c"])(t.from, t.item, t.oldIndex); var e = this.context.index; var n = this.getVmIndex(t.newIndex); this.updatePosition(e, n); var r = { element: this.context.element, oldIndex: e, newIndex: n }; this.emitChanges({ moved: r }); }, updateProperty: function(t, e) { t.hasOwnProperty(e) && (t[e] += this.headerOffset); }, computeFutureIndex: function(t, e) { if (!t.element) return 0; var n = p(e.to.children).filter(function(t) { return t.style["display"] !== "none"; }); var r = n.indexOf(e.related); var o = t.component.getVmIndex(r); var i = n.indexOf(I) !== -1; return i || !e.willInsertAfter ? o : o + 1; }, onDragMove: function(t, e) { var n = this.move; if (!n || !this.realList) return !0; var r = this.getRelatedContextFromMoveEvent(t); var o = this.context; var i = this.computeFutureIndex(r, t); Object.assign(o, { futureIndex: i }); var c = Object.assign({}, t, { relatedContext: r, draggedContext: o }); return n(c, e); }, onDragEnd: function() { this.computeIndexes(), I = null; } } }; typeof window !== "undefined" && "Vue" in window && window.Vue.component("draggable", P); var $ = P; e["default"] = $; } }))["default"]; });
// # sourceMappingURL=vuedraggable.umd.min.js.map
