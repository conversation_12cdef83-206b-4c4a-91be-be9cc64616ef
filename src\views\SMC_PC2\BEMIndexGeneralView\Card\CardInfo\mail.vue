<!--
 * @Description: 发送邮件
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-28 08:26:27
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-16 09:36:07
-->
<template>
  <a-modal
    title="发送邮件"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="onOk"
    width="80%"
    okText="发送"
    class="sendMailModal"
    @cancel="close"
  >
    <a-form-model
      :model="mailForm"
      labelAlign="left"
      ref="mailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="发件人" prop="addresser">
        <a-input disabled v-model="mailForm.addresser" />
      </a-form-model-item>
      <a-form-model-item label="收件人" prop="consignee">
        <a-select
          mode="multiple"
          :value="mailForm.consignee"
          placeholder="收件人"
          style="width: 100%"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUser"
          @change="handleConsigneeChange"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option v-for="d in userData" :key="d.mail">
            {{ d.cn }}({{ d.uid }}-{{ d.mail }})
            <div>{{ d.o }}</div>
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="抄送">
        <a-select
          mode="multiple"
          :value="mailForm.copy"
          placeholder="抄送"
          style="width: 100%"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUser"
          @change="handleCopyChange"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option v-for="d in userData" :key="d.mail">
            {{ d.cn }}({{ d.uid }}-{{ d.mail }})
            <div>{{ d.o }}</div>
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="主题" prop="subject">
        <a-input v-model="mailForm.subject" />
      </a-form-model-item>
    </a-form-model>
    <!-- 分割线 -->
    <a-divider />
    <div>邮件主题内容：</div>
    <br />
    <a-textarea v-model="mailContent" :rows="10" />
    <CardItemHtmlContent ref="cardItemHtmlContent" :cardItem="cardItem" />
    <!-- 拼接的html -->
    <template v-if="montageHtml">
      <div v-html="montageHtml"></div>
    </template>
  </a-modal>
</template>
<script>
import { thousandSplit } from "@/views/SMC_PC/IndexGeneralView/utils";
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";
import CardItemHtmlContent from "./CardItemHtmlContent.vue";
export default {
  components: { CardItemHtmlContent },
  props: {
    pageClass: String
  },
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    let validateConsingnee = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error("请填写收件人"));
      } else {
        callback();
      }
    };
    return {
      userData: [], // ldap用户下拉框
      fetching: false, // 搜索状态
      thousandSplit,
      visible: false, // 控制弹窗显隐
      confirmLoading: false, // 确定按钮loading
      labelCol: { span: 2 },
      wrapperCol: { span: 22 },
      mailForm: {
        addresser: "", // 发件人
        consignee: [], // 收件人
        copy: [], // 抄送
        subject: "" // 主题
      },
      // 卡片详情
      cardItem: {},
      mailContent: "", // 邮件文本内容
      rules: {
        addresser: [
          {
            required: true,
            message: "请先再用户管理处完善好个人邮箱信息",
            trigger: "blur"
          }
        ],
        consignee: [
          {
            validator: validateConsingnee,
            trigger: "change"
          }
        ],
        subject: [
          {
            required: true,
            message: "请填写邮件主题",
            trigger: "blur"
          }
        ]
      },
      SYS_NAME: window.system,
      montageHtml: "" // 需要拼接的html
    };
  },
  mounted() {
    this.mailForm.addresser = "<EMAIL>";
  },
  methods: {
    // 查询ldap用户
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      request(`/api/system/user/getLdapByName?name=${value}`).then(res => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        this.userData = res;
        this.fetching = false;
      });
    },
    // 收件人改变
    handleConsigneeChange(value) {
      this.mailForm.consignee = value;
      this.fetching = false;
      this.userData = [];
    },
    // 抄送改变
    handleCopyChange(value) {
      this.mailForm.copy = value;
      this.fetching = false;
      this.userData = [];
    },
    // 展示弹窗
    show(item) {
      this.cardItem = item;
      this.mailForm.subject = `${item.businessSegments}-${
        item.wdInCardName ? "(" + item.wdInCardName + ")" : ""
      }${item.displayIndexName}（${item.companyName}-${
        item.org
      }-${item.indexDt + "" + item.indexFrequency}）`;
      if (window.vm.$store) {
        this.mailForm.copy.push(
          window.vm.$store.state.user.info.email ||
            "<EMAIL>"
        );
      }
      this.mailForm.consignee = [item.email];
      if (item.montageHtml) {
        this.montageHtml = item.montageHtml;
      }
      this.visible = true;
    },
    // 发送按钮
    onOk() {
      this.$refs.mailForm.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          let postData = {
            subject: this.mailForm.subject,
            cc: this.mailForm.copy.join(","),
            to: this.mailForm.consignee.join(","),
            content: "",
            menu_name: this.pageClass
              ? `${this.cardItem.companyName}核心KPI${
                  this.pageClass === "indexGeneralViewPage2" ? "概览" : "横比"
                }${
                  this.SYS_NAME === "industrialInternet"
                    ? "-工业互联网平台"
                    : ""
                }`
              : "高管专属定制驾驶仓",
            version: "2.0"
          };
          postData.content = `邮件主题内容：<br />${
            this.mailContent
          } <hr /> ${this.$refs[
            "cardItemHtmlContent"
          ].getCardItemHtmlContent()}`;
          if (this.montageHtml) {
            postData.content += this.montageHtml;
          }
          request(`/api/smc/eMail/sendEmail`, {
            method: "POST",
            body: postData
          }).then(res => {
            if (res && res.hasOwnProperty("code") && res.code !== "0") {
              this.$message.error(res.msg);
              this.confirmLoading = false;
              return;
            }
            this.confirmLoading = false;
            this.close();
          });
        }
      });
    },
    // 取消按钮
    close() {
      this.mailForm = {
        addresser: "<EMAIL>",
        consignee: [],
        copy: [],
        subject: ""
      };
      this.mailContent = "";
      this.cardItem = {};
      this.visible = false;
      this.montageHtml = "";
    }
  }
};
</script>
<style lang="less">
.sendMailModal {
  min-width: 800px;
}
</style>
