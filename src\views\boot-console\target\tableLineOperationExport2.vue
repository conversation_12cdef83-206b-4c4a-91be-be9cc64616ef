<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-11-18 13:59:23
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-25 09:02:18
-->
<template>
  <div style="display: inline-block;">
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "导出指标目标值")
    }}</a-button>
    <a-modal
      v-model="visible"
      :destroyOnClose="true"
      :width="600"
      @ok="handleOk"
      @cancel="close"
    >
      <div class="dialogInner">
        <a-radio-group v-model="radioVal">
          <a-radio-button value="外报">
            外报目标值
          </a-radio-button>
          <a-radio-button value="内控">
            内控目标值
          </a-radio-button>
          <!-- <a-radio-button value="CBG">
            CBG目标值
          </a-radio-button> -->
        </a-radio-group>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import { pureAxios } from "@/utils/requestHttp";
export default {
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
  },
  data() {
    return {
      showAlias,
      visible: false,
      radioVal: "外报",
      flag: "Y",
    };
  },
  methods: {
    btClick() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    handleOk() {
      const { cmimId } = this.record;
      pureAxios({
        url: `/smc2/newTarget/export`,
        method: "post",
        responseType: "blob",
        data: {
          cmimId,
          flag: this.flag,
          val:this.radioVal
        },
      })
        .then((response) => {
          if (!response.data) {
            return;
          }
          const fileName = response.headers["content-disposition"].split(
            "filename="
          )[1];
          let url = window.URL.createObjectURL(new Blob([response.data]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", decodeURIComponent(fileName));
          document.body.appendChild(link);
          link.click();
          this.close();
        })
        .catch((error) => {
          window.vm.$notification.error({
            message: showAlias("ERRORMESSAGE"),
            description: error.message,
          });
        });
    },
  },
};
</script>
<style lang="less" scoped>
.dialogInner {
  width: 100%;
  height: 150px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-around;
}
</style>
