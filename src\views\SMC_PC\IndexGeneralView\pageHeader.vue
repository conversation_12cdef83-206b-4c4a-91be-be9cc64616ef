<!--
 * @Description: 页头
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 15:36:50
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-12-27 09:27:29
-->
<template>
  <div class="_pageHeader">
    <!-- 标题 -->
    <div class="_flex">
      <span class="_title" style="margin-right: 12px;">{{ title }}</span>
      <!-- 引导 -->
      <template v-if="!this.hideGuide">
        <a-tooltip placement="top">
          <template slot="title">
            <span>操作引导</span>
          </template>
          <img
            @click="showZZ = true"
            :src="VideoImg"
            style="display: block;width: 18px;height: 14px;cursor: pointer;"
            alt=""
            srcset=""
          />
        </a-tooltip>
      </template>
      <a-tooltip placement="top">
        <template slot="title">
          <span>个人专属报告</span>
        </template>
        <img
          @click="$refs['selfReports'].show()"
          :src="Report"
          style="display: block;width: 18px;height: 18px;margin-left: 10px;cursor: pointer;"
          alt=""
          srcset=""
        />
      </a-tooltip>
      <!-- logo -->
      <div class="DTlogo"></div>
    </div>
    <div class="_right _flex">
      <!-- 模式 -->
      <div class="mode _flex">
        <span>模式：</span>
        <a-radio-group v-model="searchForm.mode" button-style="solid">
          <a-radio-button
            :value="item.key"
            v-for="item in modeTypeOptions"
            :key="item.key"
          >
            {{ item.value }}
          </a-radio-button>
        </a-radio-group>
      </div>
      <!-- 基地 -->
      <div class="base _flex">
        <span>{{ baseLabelName }}：</span>
        <a-select
          style="width: 138px"
          :dropdownMatchSelectWidth="false"
          v-model="searchForm.base"
        >
          <a-select-option
            :value="item.key"
            v-for="item in baseOptions"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </div>
      <!-- 时间 -->
      <div class="time _flex">
        <span>时间：</span>
        <a-radio-group
          v-model="searchForm.timeType"
          style="margin-right: 8px;"
          button-style="solid"
          @change="timeTypeChange"
        >
          <a-radio-button
            :value="item.key"
            v-for="item in timeTypeOptions"
            :key="item.key"
          >
            {{ item.value }}
          </a-radio-button>
        </a-radio-group>
        <!-- 月选择器 -->
        <a-month-picker
          :allowClear="false"
          v-model="searchForm.time"
          :disabled-date="disabledDate"
          v-if="searchForm.timeType === 'month'"
          :format="monthFormat"
        />
        <!-- 周选择器 -->
        <a-select
          show-search
          placeholder="选择年"
          style="width: 90px"
          :filter-option="filterOption"
          v-model="selectedYearWeek[0]"
          v-if="searchForm.timeType === 'week'"
        >
          <a-select-option
            :value="item"
            v-for="item in yearSelectOptions"
            :key="item"
          >
            {{ item }}年
          </a-select-option>
        </a-select>
        <a-select
          show-search
          placeholder="选择周"
          style="width: 90px"
          :filter-option="filterOption"
          v-model="selectedYearWeek[1]"
          v-if="searchForm.timeType === 'week'"
        >
          <a-select-option
            :value="item"
            v-for="item in weekSelectOptions"
            :key="item"
          >
            {{ item }}周
          </a-select-option>
        </a-select>
        <!-- 日选择器 -->
        <a-date-picker
          :allowClear="false"
          :disabled-date="disabledDate"
          v-model="searchForm.time"
          v-if="searchForm.timeType === 'day'"
          :format="dateFormat"
        />
      </div>
    </div>
    <!-- 引导遮罩 -->
    <div class="guidezz" @keydown.27="closeZZ" @click="closeZZ" v-if="showZZ">
      <div>
        <div class="title">
          <span>功能操作视频引导，点击非视频任意处退出播放</span>
          <a-icon type="close" class="icon" @click="closeZZ" />
        </div>
        <!-- <div class="img"></div> -->
        <div class="video" @click.stop="clickVideo" :style="videoStyle">
          <!-- 线上运行 -->
          <component :is="videoComName" ref="videoCom"></component>
          <!-- 本地调试 -->
          <!-- <video-com ref="videoCom" /> -->
        </div>
      </div>
    </div>
    <!-- 个人专属报告 -->
    <!-- 线上运行 -->
    <component
      :is="selfReportsComName"
      ref="selfReports"
      :companyName="companyName"
      @change="
        data => {
          timeTypeChange(data.frequency);
          setStatus(data.type);
          $emit('reportModalChange', data.type);
        }
      "
    ></component>
    <!-- 本地调试 -->
    <!-- <self-reports
      ref="selfReports"
      :companyName="companyName"
      @change="
        data => {
          timeTypeChange(data.frequency);
          searchForm.timeType = data.frequency;
          setStatus(data.type);
          $emit('reportModalChange', data.type);
        }
      "
    /> -->
  </div>
</template>
<script>
import Report from "@/assets/images/icon-report.png";
import VideoImg from "@/assets/images/shipin.png";
import moment from "moment";
// import VideoCom from "../VideoCom/index.vue"; // 本地调试
// import SelfReports from "./SelfReports/index.vue"; // 本地调试
import { publicPath, covertDate } from "@/utils/utils.js";
import request from "@/utils/requestHttp";
import { getYearWeek } from "./utils";
import { getUrlParam } from "@/utils/utils.js";

export default {
  components: {
    // VideoCom
    // SelfReports
  }, // 本地调试
  props: {
    companyName: String,
    baseList: {
      type: Array,
      default() {
        return [];
      }
    },
    baseLabelName: {
      type: String,
      default: "基地"
    },
    operationGuideJSUrl: String,
    selfReportsJSUrl: String
  },
  computed: {
    videoStyle() {
      //输出当前窗口的宽
      let windowWidth =
        document.documentElement.clientWidth || document.body.clientWidth;
      return {
        width: windowWidth * 0.7 + "px",
        height: (windowWidth * 0.7 * 576) / 1024 + "px"
      };
    }
  },
  data() {
    return {
      moment,
      VideoImg,
      Report,
      showZZ: false, // 展示视频这招
      title: `${this.companyName}核心KPI概览图`,
      searchForm: {
        // 页面中查询条件
        base: "", // 基地
        mode: "chart", // 模式
        timeType: "month", // 时间类型
        time: moment(new Date().getTime(), this.monthFormat) // 时间
      },
      indexStatus: "",
      yearSelectOptions: [], // 周模式下年下拉框
      weekSelectOptions: [], // 周模式下周下拉框
      selectedYearWeek: ["", ""], // 周模式下选中的年周
      baseOptions: [
        // 基地下拉框
      ],
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: "month",
          value: "按月"
        },
        {
          key: "week",
          value: "按周"
        },
        {
          key: "day",
          value: "按日"
        }
      ],
      modeTypeOptions: [
        // 模式下拉框
        {
          key: "chart",
          value: "图表"
        },
        {
          key: "table",
          value: "表格"
        }
      ],
      dateFormat: "YYYY-MM-DD", // 日期、周格式化
      monthFormat: "YYYY-MM", // 月格式化
      weekFormat: "YYYY-ww", // 周格式化
      saveLocalSearchForm: false, // 搜索表条本地存储
      videoComName: "",
      selfReportsComName: "",
      hideGuide: getUrlParam("hideGuide") || false
    };
  },
  created() {
    // 初始化年周下拉框
    this.initYearSelect();
    this.initWeekSelect();
    // 如果本地搜索条件有存储，获取搜索条件后赋值到searchForm
    if (localStorage.getItem(`${this.$route.name}-searchForm`)) {
      try {
        const { base, timeType, time } = JSON.parse(
          localStorage.getItem(`${this.$route.name}-searchForm`)
        );
        this.searchForm.base = base;
        this.searchForm.timeType = timeType;
        this.searchForm.time = time;
        if (timeType === "week") {
          this.selectedYearWeek[1] = Number(time.split("-")[1] || "01");
          this.selectedYearWeek[0] = Number(
            time.split("-")[0] || new Date().getFullYear()
          );
        }
        // 修改本地存储标识
        this.saveLocalSearchForm = true;
      } catch (error) {
        console.error(error);
      }
    }
  },
  mounted() {
    Promise.all([this.loadVideoCom(), this.loadSelfReportCom()]).then(() => {
      // 如果没有看过视频引导，展示视频引导
      if (!localStorage.getItem("IndexGeneralViewGuide") && !this.hideGuide) {
        this.showZZ = true;
      } else {
        // 已读引导视频则，每日首次进入系统展示个人专属报告
        if (
          localStorage.getItem(`${this.companyName}IndexGeneralSeltReports`) !==
          `${covertDate(new Date().getTime(), 0)}`
        ) {
          this.$nextTick(() => {
            this.$refs["selfReports"].show();
          });
        }
      }
    });
  },
  watch: {
    // 监听基地列表变化
    baseList: {
      handler(newVal) {
        if (Array.isArray(newVal) && newVal.length) {
          // 如果不是本地存储，则默认选中基地列表第一个
          if (!this.saveLocalSearchForm) {
            this.searchForm.base = newVal[0];
          }
          const baseList = newVal.map(item => {
            return {
              key: item,
              value: item
            };
          });
          // 设置基地下拉框
          this.baseOptions = baseList;
        }
      },
      deep: true
    },
    // 监听searchForm值改变
    searchForm: {
      handler() {
        // 保存搜索条件到本地
        localStorage.setItem(
          `${this.$route.name}-searchForm`,
          JSON.stringify(this.searchForm)
        );
        // 改变后向父组件发出请求
        this.debounce(this.sendRequest());
      },
      deep: true
    },
    // 监听周模式下的selectedYearWeek值改变
    selectedYearWeek(val) {
      // 根据选中年份去动态生成周下拉框
      this.initWeekSelect(val[0]);
      // 如果周下拉框中没有当前已选中的周值，则默认选成第一周
      if (!this.weekSelectOptions.includes(val[1])) {
        this.selectedYearWeek[1] = this.weekSelectOptions[0];
      }
      // searchFrom.time赋值
      // let firstDay = new Date(val[0], 0, 1).getDay(); // 获取选中年第一天周几
      // let weeks = val[1];
      // if (firstDay !== 0) {
      //   weeks = weeks - 1;
      // }
      // console.log(
      //   `${val[0]}年，第一天${firstDay}周${firstDay +
      //     1}，选中第${weeks}周，${7 - firstDay}, ${weeks * 7}, ${7 -
      //     firstDay +
      //     weeks * 7 -
      //     7}天`
      // );
      this.searchForm.time = `${val[0]}-${String(val[1]).padStart(2, "0")}`;
      console.log(
        "this.searchForm.time---->",
        JSON.stringify(this.searchForm.time)
      );
    }
  },
  methods: {
    // 根据公司获取基地
    getBase(e) {
      return new Promise(resolve => {
        let indexDt = "";
        if (this.searchForm.timeType === "week") {
          let week = getYearWeek(new Date(this.searchForm.time));
          indexDt = `${new Date(this.searchForm.time).getFullYear()}-${
            week < 10 ? "0" + week : week
          }`;
        } else {
          indexDt = moment(this.searchForm.time).format(
            this.searchForm.timeType === "day" ? "YYYY-MM-DD" : "YYYY-MM"
          );
        }
        const str = e
          ? `&frequency=${
              this.searchForm.timeType === "day"
                ? "日"
                : this.searchForm.timeType === "week"
                ? "周"
                : "月"
            }&indexDt=${indexDt}&status=${e}`
          : "";
        request(
          `/api/smc/indexCardInfo/getBaseBycompanyName?companyName=${this.companyName}${str}`
        ).then(res => {
          resolve(res || []);
        });
      });
    },
    // 设置指标状态
    async setStatus(e) {
      const baseList = await this.getBase(e);
      if (baseList.length) {
        this.indexStatus = e || "";
        this.baseOptions = baseList.map(item => {
          return {
            key: item,
            value: item
          };
        });
        if (this.baseOptions[0].key === this.searchForm.base) {
          this.sendRequest();
        } else {
          this.searchForm.base = this.baseOptions[0].key;
        }
      } else {
        this.indexStatus = "";
        if (e === "0") {
          this.$message.warning(`当前订阅指标暂无未达标`);
        } else if (e === "1") {
          this.$message.warning(`暂无未达标同环比恶化指标`);
        }
        this.$emit("reportModalChange", "");
        this.sendRequest();
      }
    },
    loadVideoCom() {
      return new Promise(resolve => {
        // 加载操作引导视频组件
        if (!window["VideoCom"]) {
          const script = document.createElement("script");
          let fileUrl = this.operationGuideJSUrl;
          if (this.operationGuideJSUrl.indexOf("http") === -1) {
            fileUrl = `${publicPath}/minio${this.operationGuideJSUrl}`;
          }
          script.src = fileUrl + `?t=${Date.now()}`;
          script.onload = () => {
            const exportCom = window["VideoCom"].default;
            this.videoComName = exportCom.myCom;
            resolve();
          };
          document.body.appendChild(script);
        } else {
          const exportCom = window["VideoCom"].default;
          this.videoComName = exportCom.myCom;
          resolve();
        }
      });
    },
    loadSelfReportCom() {
      return new Promise(resolve => {
        // 加载个人专属报告组件
        if (!window["SelfReports"]) {
          const script = document.createElement("script");
          let SelfReportsFileUrl = this.selfReportsJSUrl;
          if (this.selfReportsJSUrl.indexOf("http") === -1) {
            SelfReportsFileUrl = `${publicPath}/minio${this.selfReportsJSUrl}`;
          }
          script.src = SelfReportsFileUrl + `?t=${Date.now()}`;
          script.onload = () => {
            const exportCom = window["SelfReports"].default;
            this.selfReportsComName = exportCom.myCom;
            resolve();
          };
          document.body.appendChild(script);
        } else {
          const exportCom = window["SelfReports"].default;
          this.selfReportsComName = exportCom.myCom;
          resolve();
        }
      });
    },
    debounce: function(fn, wait = 1000) {
      if (this.fun !== null) {
        clearTimeout(this.fun);
      }
      this.fun = setTimeout(fn, wait);
    },
    // 下拉框输入搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 禁用时间
    disabledDate(current) {
      // Can not select days before today and today
      // 禁用掉三月之后或者两年前
      return (
        (current && current > moment().add(3, "months")) ||
        // (current && current > moment().endOf("day")) ||
        moment().subtract(2, "years") > current
      );
    },
    // 视频点击
    clickVideo() {
      this.showZZ = true;
    },
    // 关闭遮罩层
    closeZZ() {
      this.showZZ = false;
      this.$refs.videoCom.$refs.videoPlayer.player.pause();
      // 本地存储
      localStorage.setItem("IndexGeneralViewGuide", "read");
    },
    // 时间类型改变
    timeTypeChange(e, callback = null) {
      const val = typeof e === "object" ? e.target.value : e || "";
      this.searchForm.timeType = val;
      if (val === "day") {
        this.searchForm.time = moment(new Date().getTime() - 86400000).format(
          this.dateFormat
        );
      } else if (val === "week") {
        this.initWeekSelect(new Date().getFullYear());
        this.selectedYearWeek = [new Date().getFullYear(), getYearWeek()];
      } else {
        this.searchForm.time = moment(new Date().getTime()).format(
          this.monthFormat
        );
      }
      this.$nextTick(() => {
        typeof callback === "function" && callback();
      });
    },
    // 发起请求
    sendRequest() {
      this.$emit("pageHeaderChange", {
        ...this.searchForm,
        indexStatus: this.indexStatus
      });
    },
    // 根据年份更新周下拉框选项
    initWeekSelect(year = new Date().getFullYear()) {
      let weekSelectOptions = this.createWeeks(year);
      // 部分工厂要求+1周
      weekSelectOptions = [...weekSelectOptions, weekSelectOptions.length + 1];
      if (
        year === this.yearSelectOptions[this.yearSelectOptions.length - 1] &&
        this.yearSelectOptions.length === 3
      ) {
        // 如果是最前边的一年且年份下拉框为三个时，要处理月份选择选两年前的周
        this.weekSelectOptions = weekSelectOptions.filter(
          item => item > getYearWeek()
        );
      } else {
        this.weekSelectOptions = weekSelectOptions;
      }
    },
    // 初始化年
    initYearSelect() {
      let yearSelectOptions = [];
      // 两年前的年份不允许选择
      let beginningYear = moment().get("year") - 2;
      // 如果当前月份是12月则，年份选择扣除一年
      if (moment().get("month") + 1 === 12) {
        beginningYear++;
      }
      for (let i = beginningYear; i <= new Date().getFullYear(); i++) {
        yearSelectOptions.unshift(i);
      }
      this.yearSelectOptions = yearSelectOptions;
    },
    // 获取自然周
    createWeeks(year) {
      var d = new Date(year, 0, 1);
      while (d.getDay() != 1) {
        d.setDate(d.getDate() + 1);
      }
      var to = new Date(year + 1, 0, 1);
      var i = 1;
      let arr = [];
      for (var from = d; from < to; ) {
        from.setDate(from.getDate() + 6);
        if (from < to) {
          from.setDate(from.getDate() + 1);
        } else {
          to.setDate(to.getDate() - 1);
        }
        arr.push(i);
        i++;
      }
      return arr;
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage {
  ._pageHeader {
    padding-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    .DTlogo {
      width: 54px;
      height: 24px;
      margin-left: 24px;
      background-image: url("~@/assets/images/DTlogo.png");
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
    ._title {
      height: 32px;
      font-family: PingFangSC-Semibold;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 32px;
    }
    ._right {
      .base,
      .mode {
        margin-right: 24px;
      }
    }
    .guidezz {
      width: 100vw;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.35);
      z-index: 55;
      display: flex;
      align-items: center;
      justify-content: center;
      & > div {
        background-color: #fff;
        border-radius: 3px;
        overflow: hidden;
        .title {
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            display: block;
            font-weight: bold;
          }
          .icon {
            font-size: 16px;
          }
        }
        .img {
          width: 1024px;
          height: 471px;
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
          background-image: url("/minio/smcbucket/%E4%BA%91%E5%9B%BE%E6%A6%82%E8%A7%88%E5%BD%95%E5%B1%8F%E5%B0%8F%E5%B1%8F%E5%B9%95.gif");
          cursor: pointer;
        }
        .video {
          .video-js .vjs-icon-placeholder {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }
  }
}
</style>
