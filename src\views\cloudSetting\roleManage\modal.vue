<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-08-11 17:17:08
-->
<template>
  <a-modal
    class="roleManageModal"
    v-model="visible"
    :destroyOnClose="true"
    :width="600"
    title="修改角色指标权限"
    @ok="handleOk"
    @cancel="close"
    :confirm-loading="confirmLoading"
  >
    <a-spin :spinning="loadTree">
      <div style="min-height: 500px;">
        <a-tree
          :blockNode="true"
          :selectable="false"
          checkable
          v-model="checkedKeys"
          :auto-expand-parent="false"
          :tree-data="treeData"
          :replaceFields="replaceFields"
          @check="onCheck"
        />
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      checkedKeys: [], // 树形组件选择的nodeId
      treeData: [], // 树形组件数据
      replaceFields: {
        title: "nodeName",
        key: "nodeId",
        children: "childNodeList"
      },
      postTreeData: [], // 要提交的组件数据
      roleCode: "", // 角色ID
      confirmLoading: false, // 确定按钮Loading
      loadTree: false // 加载树形组件loading
    };
  },
  methods: {
    show({ roleCode }) {
      this.visible = true;
      this.roleCode = roleCode;
      this.loadTree = true;
      this.getAllRoleList();
      this.getCurrentRoleList();
    },
    close() {
      this.visible = false;
      this.roleCode = "";
      this.postTreeData = [];
      this.confirmLoading = false;
      this.checkedKeys = [];
      this.treeData = [];
    },
    async handleOk() {
      this.confirmLoading = true;
      await this.saveCurrentRoleList();
      this.close();
    },
    // 获取指标树列表
    getAllRoleList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/roleDetail/getRoleTreeListByRoleId?roleId=`
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          this.treeData = res;
        }
      });
    },
    // 获取当前角色有的指标
    getCurrentRoleList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/roleDetail/getRoleTreeListByRoleId?roleId=${this.roleCode}`
      ).then(res => {
        const dealCurrentRole = arr => {
          arr.forEach(item => {
            this.checkedKeys.push(item.nodeId);
            dealCurrentRole(item.childNodeList);
          });
        };
        if (res && Array.isArray(res)) {
          dealCurrentRole(res);
          this.dealPostTreeData();
          this.checkedKeys = this.postTreeData;
        }
        this.loadTree = false;
      });
    },
    // 保存当前角色选中的指标
    saveCurrentRoleList() {
      return new Promise(resolve => {
        let postData = {
          roleId: String(this.roleCode),
          list: this.postTreeData
        };
        request(`${adminUserUrlPrefix["lxp"]}/roleDetail/saveAll`, {
          method: "POST",
          body: postData
        }).then(() => {
          resolve();
        });
      });
    },
    // 树形控件改变
    onCheck() {
      this.dealPostTreeData();
    },
    // 处理成后端要的值
    dealPostTreeData() {
      this.postTreeData = this.checkedKeys.filter(item => {
        return item.split("==").length - 1 === 3;
      });
    }
  }
};
</script>
