<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-24 08:55:07
-->
<template>
  <!-- 非累加目标值修改 -->
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "新增")
    }}</a-button>
    <Modal
      ref="modal"
      @fetchData="fetchData"
      :targetDisabled="this.targetDisabled"
      :targetCIMDisabled="this.targetCIMDisabled"
      :targetCBGDisabled="this.targetCBGDisabled"
    />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./add-modal.vue";
import request from "@/utils/requestHttp";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
  },
  data() {
    return {
      showAlias,
      targetDisabled: false,
      targetCIMDisabled: false,
      targetCBGDisabled: false,
    };
  },
  methods: {
    btClick() {
      // console.log("this.record----->", JSON.stringify(this.record));
      // const record =  {"createdBy":null,"createdDate":null,"modifiedBy":null,"modifiedDate":null,"remark":null,"pageNum":0,"rows":null,"pageSize":0,"startRow":0,"endRow":0,"total":0,"pages":0,"id":21277658,"ids":null,"loadDt":null,"dmId":"ZBY00555","cmimId":"ZBY00555-CM_TREE002109-D-0000-0000-DIM_1244-0000-0000-0000-0000-0000-0000-0000-0000","businessSegmentsId":"SEG_83","indexId":"ZBY00555","indexNameInd":null,"signOrgId":"H06","orgId":"CM_TREE002109","indexUnitId":"UN_490","precisions":"2","codeValueId":"TYPE_100","indexFrequencyId":"M","isFactoryweek":"N","isPartSum":"Y","isFromSum":"Y","formulaId":"FOR_511","contemFormulaId":"CFOR_481","previousFormulaId":"PFOR_484","contemRateFormulaId":"CRF_477","previousRateFormulaId":"PRF_478","sumValueFormulaId":"SVF_476","sumContemFormulaId":"SCFOR_586","sumPreviousFormulaId":"SPFOR_590","sumRatioFormulaId":null,"sumContemRateFormulaId":"SCRF_593","sumPreviousRateFormulaId":"SPRF_594","targetValueWayId":null,"targetFormulaId":"TFOR_514","sumTargetValueWayId":null,"sumTargetFormulaId":"STFOR_597","classCompareDesc":null,"fillInPerson":"李世东-lishidong","fillInPersonLeader":"沈文辉-shenwenhui","isDelete":null,"productTypeId":null,"brandId":null,"productAtt1Id":"DIM_1244-卡片标签","productAtt2Id":null,"productAtt3Id":null,"productAtt4Id":null,"productAtt5Id":null,"productAtt6Id":null,"productAtt7Id":null,"productAtt8Id":null,"productAtt9Id":null,"perfectId":"5026","hid":null,"fullCode":null,"treeType":null,"productAttList":null,"companyId":null,"company":null,"customIndexName":null,"uuid":null,"isManually":"N","isDel":null,"dimension":null,"businessSegments":"效率","signOrg":"海信宽带公司","indexName":"制造费","org":"OSA车间","indexUnit":"元","codeValue":"反向","indexFrequency":"日","formula":"直接取值","contemFormula":"直接取值","previousFormula":"直接取值","contemRateFormula":"(当期-同期)/|同期|","previousRateFormula":"(当期-上期)/|上期|","sumValueFormula":"直接取累计值","sumContemFormula":"同期值累计","sumPreviousFormula":"上期值累计","sumRatioFormula":null,"sumContemRateFormula":"(累计当期值-累计同期值)/|累计同期值|","sumPreviousRateFormula":"(累计当期值-累计上期值)/|累计上期值|","targetValueWay":null,"targetFormula":"2-实际值比目标值","sumTargetValueWay":null,"sumTargetFormula":"2-累计当期值比累计目标值","productType":null,"brand":null,"productAtt1Dimtp":"仓储物流费","productAtt2Dimtp":null,"productAtt3Dimtp":null,"productAtt4Dimtp":null,"productAtt5Dimtp":null,"productAtt6Dimtp":null,"productAtt7Dimtp":null,"productAtt8Dimtp":null,"productAtt9Dimtp":null,"groupId":null,"role":null,"orgId1":null,"signOrgId1":null,"list":null,"listDis":null,"listOrgId":null,"indexFrequencyList":null,"classKeybusinessSegments":"visiblebusinessSegments16957279468491695278928000","classKeyindexName":"visibleindexName16957279468511695278928000","classKeysignOrg":"visiblesignOrg16957279468521695278928000","classKeyorg":"visibleorg16957279468531695278928000","classKeydmId":"visibledmId16957279468551695278928000","classKeyfillInPerson":"visiblefillInPerson16957279468571695278928000","classKeyindexNameInd":"visibleindexNameInd16957279468581695278928000","classKeyindexUnit":"visibleindexUnit16957279468601695278928000","classKeycodeValue":"visiblecodeValue16957279468631695278928000","classKeyindexFrequency":"visibleindexFrequency16957279468641695278928000","classKeyprecisions":"visibleprecisions16957279468651695278928000","classKeyisManually":"visibleisManually16957279468671695278928000","classKeyisFactoryweek":"visibleisFactoryweek16957279468681695278928000","classKeyisDelete":"visibleisDelete16957279468691695278928000","classKeyclassCompareDesc":"visibleclassCompareDesc16957279468711695278928000","classKeyisPartSum":"visibleisPartSum16957279468731695278928000","classKeyisFromSum":"visibleisFromSum16957279468741695278928000","classKeyformula":"visibleformula16957279468761695278928000","classKeycontemFormula":"visiblecontemFormula16957279468781695278928000","classKeypreviousFormula":"visiblepreviousFormula16957279468801695278928000","classKeycontemRateFormula":"visiblecontemRateFormula16957279468821695278928000","classKeypreviousRateFormula":"visiblepreviousRateFormula16957279468831695278928000","classKeysumValueFormula":"visiblesumValueFormula16957279468851695278928000","classKeysumContemFormula":"visiblesumContemFormula16957279468861695278928000","classKeysumPreviousFormula":"visiblesumPreviousFormula16957279468881695278928000","classKeysumRatioFormula":"visiblesumRatioFormula16957279468891695278928000","classKeysumContemRateFormula":"visiblesumContemRateFormula16957279468901695278928000","classKeysumPreviousRateFormula":"visiblesumPreviousRateFormula16957279468911695278928000","classKeytargetValueWay":"visibletargetValueWay16957279468931695278928000","classKeytargetFormula":"visibletargetFormula16957279468951695278928000","classKeysumTargetValueWay":"visiblesumTargetValueWay16957279468961695278928000","classKeysumTargetFormula":"visiblesumTargetFormula16957279468981695278928000","classKeyproductAtt1Dimtp":"visibleproductAtt1Dimtp16957279468991695278928000","classKeyproductAtt2Dimtp":"visibleproductAtt2Dimtp16957279469021695278928000","classKeyproductAtt3Dimtp":"visibleproductAtt3Dimtp16957279469031695278928000","classKeyproductAtt4Dimtp":"visibleproductAtt4Dimtp16957279469051695278928000","classKeyproductAtt5Dimtp":"visibleproductAtt5Dimtp16957279469071695278928000","classKeyproductAtt6Dimtp":"visibleproductAtt6Dimtp16957279469091695278928000","classKeyproductAtt7Dimtp":"visibleproductAtt7Dimtp16957279469111695278928000"};
      const record = this.record;
      console.log(this.$store.state[this.pageName]);
      request("/api/smc2/newTarget/isModify").then((res) => {
        this.targetDisabled = !res.target;
        this.targetCIMDisabled = !res.targetCIM;
        this.targetCBGDisabled = !res.targetCBG;
        this.$refs["modal"].show(record);
      });
    },
    // 请求数据表格
    fetchData(flag) {
      if (flag == "Y") {
        this.$store.dispatch({
          type: `${this.pageName}/1636938931053/fetch`,
          payload: {
            pageIndex: this.$store.state[this.pageName][1636938931053].data
              .pagination.pageIndex,
          },
        });
      } else {
        this.$store.dispatch({
          type: `${this.pageName}/1677138175000/fetch`,
          payload: {
            pageIndex: this.$store.state[this.pageName][1677138175000].data
              .pagination.pageIndex,
          },
        });
      }
    },
  },
};
</script>
