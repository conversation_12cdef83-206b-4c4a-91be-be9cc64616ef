<template>
  <!-- 自动生成的内容 -->
  <div>
    <a-divider>指标信息</a-divider>
    <div ref="autoContent" style="padding: 10px;line-height: 20px;">
      <div>指标信息展示(系统自动呈现)：</div>
      <br />
      <div>
        <div>
          {{ cardItem.plateName }}-{{ cardItem.indexName }}（{{
            cardItem.companyName
          }}-{{ cardItem.baseName }}-{{
            cardItem.dwppCmTfIndexLibrary.indexDt + "" + cardItem.dataFrequency
          }}）
        </div>
        <div>
          目标值：{{ cardItem.realTargetValue }} {{ cardItem.unit }}；
          实际值：{{ cardItem.realBaseActual }} {{ cardItem.unit }}；
          {{
            cardItem.dwppCmTfIndexLibrary.isContemRate === "Y"
              ? showText(
                  "contemRate",
                  cardItem.dwppCmTfIndexLibrary.contemRate,
                  cardItem.dwppCmTfIndexLibrary.indexType
                )
              : ""
          }}
          {{
            cardItem.dwppCmTfIndexLibrary.isPreviousRate === "Y"
              ? showText(
                  "previousRate",
                  cardItem.dwppCmTfIndexLibrary.previousRate,
                  cardItem.dwppCmTfIndexLibrary.indexType
                )
              : ""
          }}
        </div>
        <br />
        <div class="card" ref="cardItem">
          <div
            style="
				display: inline-block;
				background: #eee;
				border-radius: 12px;
				padding: 20px;
				box-sizing: border-box;
				min-width: 227px;
			"
          >
            <!-- 基地、标题 -->
            <div style="display: flex; align-items: center; margin-bottom: 8px">
              <span
                style="
						height: 20px;
						background: #e0e3ea;
						border-radius: 2px;
						line-height: 20px;
						padding: 0 4px;
						font-size: 12px;
						color: #53667a;
						margin-right: 8px;
					"
              >
                {{ cardItem.plateName }}
              </span>
              <span
                style="font-size: 14px; color: rgba(0, 0, 0, 0.65); line-height: 20px"
              >
                {{ cardItem.indexName }}
              </span>
            </div>
            <!-- 实际值 -->
            <div
              style="
					font-size: 24px;
					color: rgba(0, 0, 0, 0.85);
					line-height: 32px;
					margin-bottom: 8px;
				"
            >
              {{ cardItem.realBaseActual }}
              <span style="font-size: 16px;line-height: 32px;">{{
                cardItem.unit
              }}</span>
            </div>
            <!-- 目标值 -->
            <div
              style="
					font-size: 12px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 18px;
					margin-bottom: 8px;
				"
            >
              <span style="margin-right: 8px">目标值</span>
              {{ cardItem.realTargetValue }}
              {{ cardItem.unit }}
            </div>
            <!-- 完成率 -->
            <div
              style="
					display: flex;
					align-items: center;
					font-size: 12px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 18px;
					padding-bottom: 11px;
					border-bottom: 1px solid rgba(0, 0, 0, 0.1);
					margin-bottom: 12px;
				"
            >
              <span style="margin-right: 8px">完成率</span>
              <div
                style="
						width: 100px;
						height: 8px;
						background: #f0f0f0;
						position: relative;
						margin-right: 16px;
					"
              >
                <span
                  style="
							position: absolute;
							left: 0;
							top: 0;
							height: 8px;
						"
                  :style="{
                    width: `${
                      cardItem.dwppCmTfIndexLibrary.completionRate
                        ? parseFloat(
                            cardItem.dwppCmTfIndexLibrary.completionRate
                          ) *
                            100 >
                          100
                          ? 100
                          : parseFloat(
                              cardItem.dwppCmTfIndexLibrary.completionRate
                            ) * 100
                        : 0
                    }%`,
                    'background-color': cardItem.dwppCmTfIndexLibrary
                      .completionRate
                      ? parseFloat(
                          Decimal(cardItem.dwppCmTfIndexLibrary.completionRate)
                            .mul(Decimal.Decimal(100))
                            .toFixed(2, Decimal.ROUND_HALF_UP)
                        ) < 100
                        ? '#6495f9'
                        : '#f75050'
                      : '#f0f0f0'
                  }"
                ></span>
              </div>
              <span>
                {{
                  `${
                    cardItem.dwppCmTfIndexLibrary.completionRate
                      ? Decimal(cardItem.dwppCmTfIndexLibrary.completionRate)
                          .mul(Decimal(100))
                          .toFixed(2, Decimal.ROUND_HALF_UP)
                      : "-"
                  }%`
                }}
              </span>
            </div>
            <!-- 同环比 -->
            <div
              style="
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 12px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 18px;
				"
            >
              <div style="flex: 1">
                <span style="margin-right: 8px">同比</span>
                <template v-if="cardItem.isContemRate === 'Y'">
                  <template v-if="cardItem.dwppCmTfIndexLibrary.contemRate">
                    <span
                      :style="
                        styleObject(cardItem.dwppCmTfIndexLibrary.contemRate)
                      "
                    ></span>
                  </template>
                  <span
                    :style="{
                      color: cardItem.dwppCmTfIndexLibrary.contemRate
                        ? cardItem.dwppCmTfIndexLibrary.indexType === '正向'
                          ? cardItem.dwppCmTfIndexLibrary.contemRate.includes(
                              '-'
                            )
                            ? '#6495f9'
                            : '#f75050'
                          : cardItem.dwppCmTfIndexLibrary.contemRate.includes(
                              '-'
                            )
                          ? '#f75050'
                          : '#6495f9'
                        : 'rgba(0, 0, 0, 0.8);'
                    }"
                  >
                    {{
                      cardItem.dwppCmTfIndexLibrary.contemRate
                        ? Math.abs(
                            Decimal(cardItem.dwppCmTfIndexLibrary.contemRate)
                              .mul(Decimal(100))
                              .toFixed(2, Decimal.ROUND_HALF_UP)
                          ) + "%"
                        : "-"
                    }}
                  </span>
                </template>
                <span
                  v-if="cardItem.isContemRate !== 'Y'"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                >
                  不对比
                </span>
              </div>
              <div style="flex: 1">
                <span style="margin-right: 8px">环比</span>
                <template v-if="cardItem.isPreviousRate === 'Y'">
                  <template v-if="cardItem.dwppCmTfIndexLibrary.previousRate">
                    <span
                      :style="
                        styleObject(cardItem.dwppCmTfIndexLibrary.previousRate)
                      "
                    ></span>
                  </template>
                  <span
                    :style="{
                      color: cardItem.dwppCmTfIndexLibrary.previousRate
                        ? cardItem.dwppCmTfIndexLibrary.indexType === '正向'
                          ? cardItem.dwppCmTfIndexLibrary.previousRate.includes(
                              '-'
                            )
                            ? '#6495f9'
                            : '#f75050'
                          : cardItem.dwppCmTfIndexLibrary.previousRate.includes(
                              '-'
                            )
                          ? '#f75050'
                          : '#6495f9'
                        : 'rgba(0, 0, 0, 0.8);'
                    }"
                  >
                    {{
                      cardItem.dwppCmTfIndexLibrary.previousRate
                        ? Math.abs(
                            Decimal(cardItem.dwppCmTfIndexLibrary.previousRate)
                              .mul(Decimal(100))
                              .toFixed(2, Decimal.ROUND_HALF_UP)
                          ) + "%"
                        : "-"
                    }}
                  </span>
                </template>
                <span
                  v-if="cardItem.isPreviousRate !== 'Y'"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.7, 0.7, 0.7);line-height: 15px;"
                >
                  不对比
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Decimal from "decimal.js";
export default {
  props: {
    cardItem: {
      type: Object,
      default() {
        return {
          dwppCmTfIndexLibrary: {}
        };
      }
    }
  },
  data() {
    return {
      Decimal
    };
  },
  methods: {
    getCardItemHtmlContent() {
      return this.$refs["autoContent"].innerHTML;
    },
    // 渲染到页面的style内联样式
    styleObject(data) {
      console.log("this.cardItem--------->", this.cardItem);
      const indexType = this.cardItem.dwppCmTfIndexLibrary?.indexType;
      let style = {
        width: 0,
        height: 0,
        "border-right": "5px solid transparent",
        "border-left": "5px solid transparent",
        position: "relative",
        "margin-right": "4px"
      };
      if (data) {
        if (indexType === "正向") {
          if (data.includes("-")) {
            style["border-top"] = "5px solid #6495f9";
            style["top"] = "10px";
          } else {
            style["border-bottom"] = "5px solid #f75050";
            style["top"] = "-10px";
          }
        } else {
          if (data.includes("-")) {
            style["border-bottom"] = "5px solid #f75050";
            style["top"] = "-10px";
          } else {
            style["border-top"] = "5px solid #6495f9";
            style["top"] = "10px";
          }
        }
        return style;
      } else {
        return {};
      }
    },
    // 展示到页面的文本
    showText(type, val, indexType) {
      let str = type === "contemRate" ? "同比" : "环比";
      if (val) {
        if (
          (val.includes("-") && indexType === "反向") ||
          (!val.includes("-") && indexType === "正向")
        ) {
          str += `上涨：`;
        } else {
          str += `下降：`;
        }
        str += `${Math.abs(
          Decimal(val)
            .mul(Decimal(100))
            .toFixed(2, Decimal.ROUND_HALF_UP)
        )}`;
      } else {
        str += `-`;
      }
      str += "%;";
      return str;
    }
  }
};
</script>
