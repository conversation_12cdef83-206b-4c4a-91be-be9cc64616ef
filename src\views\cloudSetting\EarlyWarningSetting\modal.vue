<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-12-10 08:55:07
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1200"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <!-- 基础表单 -->
    <a-form-model
      ref="baseForm"
      :model="baseForm"
      :rules="baseFormRules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <!-- 选择指标 -->
      <a-form-model-item label="选择指标" prop="indexId">
        <div style="display: flex;align-items: center;height: 39.9999px;">
          <a-tree-select
            :disabled="isEdit"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            dropdownClassName="indexTreeSelect"
            v-model="baseForm.indexId"
            :tree-data="treeData"
            :showSearch="true"
            placeholder="请选择"
            :replaceFields="replaceFields"
            @change="treeSelectChange"
            tree-default-expand-all
          />
          <a-tag
            color="blue"
            v-if="baseForm.indexType"
            style="margin-left: 10px;margin-right: 0px;"
          >
            {{ baseForm.indexType }}指标
          </a-tag>
        </div>
      </a-form-model-item>
      <!-- 选择基地 -->
      <a-form-model-item label="基地" prop="base">
        <a-select
          v-model="baseForm.base"
          :disabled="isEdit"
          mode="multiple"
          :dropdownMatchSelectWidth="false"
          style="width: 100%"
        >
          <a-select-option
            :value="item.key"
            v-for="item in baseList"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- 指标频次 -->
      <a-form-model-item label="指标属性频次" prop="indexFrequency">
        <a-select
          v-model="baseForm.indexFrequency"
          :dropdownMatchSelectWidth="false"
          :disabled="isEdit"
          style="width: 100%"
        >
          <a-select-option
            :value="item.key"
            v-for="item in frequencyList"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="预警状态" prop="warnStatus">
        <a-switch
          checked-children="开"
          un-checked-children="关"
          v-model="baseForm.warnStatus"
        />
      </a-form-model-item>
    </a-form-model>
    <a-divider></a-divider>
    <!-- 预警人数组 -->
    <a-form-model
      style="width: 100%; box-size: border-box; padding-left: 100px;"
      layout="inline"
      :model="item"
      v-for="(item, index) in ldapArr"
      :key="`ldap${index}`"
    >
      <a-form-model-item label="预警接收人" prop="ldap">
        <a-select
          mode="multiple"
          :value="item.ldap"
          placeholder="预警接收人"
          style="width: 450px;margin-right: 20px"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUser"
          @change="handleChange(index, $event)"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option v-for="d in userData" :key="d.mail">
            {{ d.cn }}({{ d.uid }}-{{ d.mail }})
            <div>{{ d.o }}</div>
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="预警推送频次" prop="warnFrequency">
        <a-select
          v-model="item.warnFrequency"
          :dropdownMatchSelectWidth="false"
          style="width: 80px"
        >
          <a-select-option
            :value="zitem.key"
            v-for="zitem in newFrequencyList"
            :key="zitem.key"
          >
            {{ zitem.value }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item v-if="!isEdit">
        <a-button
          type="default"
          @click="removeLdap(index)"
          icon="minus"
          style="margin-right: 10px;"
          shape="circle"
          v-if="ldapArr.length > 1"
        >
        </a-button
        ><a-button
          type="primary"
          v-if="index === ldapArr.length - 1"
          @click="pushOneLdap"
          icon="plus"
        >
        </a-button>
      </a-form-model-item>
    </a-form-model>
    <a-divider></a-divider>
    <!-- 预警条件 -->
    <a-button
      type="primary"
      v-if="!isEdit"
      style="margin-bottom: 20px;"
      @click="addOneCondition"
      >新增预警条件</a-button
    >
    <a-table
      :pagination="false"
      :columns="
        !isEdit
          ? [
              ...columns,
              {
                title: '操作',
                key: 'action',
                scopedSlots: { customRender: 'action' }
              }
            ]
          : columns
      "
      :data-source="conditionArr"
    >
      <!-- 序号 -->
      <template slot="index" slot-scope="text, record, index">
        <span>{{ index + 1 }}</span>
      </template>
      <!-- 完成率设置/同比设置/环比设置 -->
      <template slot="completionRate" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`completionRateFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 10px;"
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in equalList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
          <a-input-number
            style="margin-right: 3px;"
            v-model="record['completionRate']"
          />%
        </div>
      </template>
      <template slot="contemRate" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`contemRateFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 10px;"
            allowClear
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in otherEqualList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
          <a-input-number
            :min="0"
            style="margin-right: 3px;"
            v-model="record['contemRate']"
          />%
        </div>
      </template>
      <template slot="previousRate" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`previousRateFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 80px;margin-right: 10px;"
            allowClear
          >
            <a-select-option
              :value="zitem.key"
              v-for="zitem in otherEqualList"
              :key="zitem.key"
            >
              {{ zitem.value }}
            </a-select-option>
          </a-select>
          <a-input-number
            :min="0"
            style="margin-right: 3px;"
            v-model="record['previousRate']"
          />%
        </div>
      </template>
      <template slot="baseActualNum" slot-scope="text, record">
        <a-input-number :min="0" v-model="record['baseActualNum']" />
      </template>
      <template slot="completionRateNum" slot-scope="text, record">
        <a-input-number :min="0" v-model="record['completionRateNum']" />
      </template>
      <!-- 操作栏 -->
      <span slot="action" slot-scope="text, record, index">
        <a-tooltip placement="top">
          <template slot="title">
            <span>删除</span>
          </template>
          <a-icon @click="removeCondition(index)" type="delete" />
        </a-tooltip>
      </span>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import { adminUserUrlPrefix } from "@/utils/utils";
import cloneDeep from "lodash/cloneDeep";
import debounce from "lodash/debounce";
export default {
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      userData: [],
      fetching: false,
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      baseForm: {
        indexId: "",
        base: [],
        indexFrequency: "",
        indexType: "",
        warnStatus: false
      },
      ldapArr: [
        {
          ldap: [],
          warnFrequency: ""
        }
      ], // 预警人数组
      conditionArr: [
        {
          completionRateFlag: "<",
          completionRate: "",
          contemRateFlag: "恶化>",
          contemRate: "",
          previousRateFlag: "恶化>",
          previousRate: "",
          baseActualNum: "",
          completionRateNum: ""
        }
      ],
      replaceFields: {
        title: "nodeName",
        key: "nodeId",
        value: "nodeId",
        children: "childNodeList"
      },
      treeData: [], // 指标数据
      baseFormRules: {
        indexId: [
          {
            required: true,
            message: "请选择指标",
            trigger: "change"
          }
        ],
        base: [
          {
            required: true,
            message: "请选择基地",
            trigger: "change"
          }
        ],
        indexFrequency: [
          {
            required: true,
            message: "请选择指标频次",
            trigger: "change"
          }
        ],
        warnStatus: [
          {
            required: true,
            message: "请选择指标预警开关",
            trigger: "change"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      baseList: [], // 基地列表
      frequencyList: [],
      equalList: [
        {
          key: "<",
          value: "<"
        },
        {
          key: "<=",
          value: "<="
        },
        {
          key: "=",
          value: "="
        },
        {
          key: ">",
          value: ">"
        },
        {
          key: ">=",
          value: ">="
        }
      ],
      otherEqualList: [
        {
          key: "恶化<",
          value: "恶化<"
        },
        {
          key: "恶化<=",
          value: "恶化<="
        },
        {
          key: "恶化=",
          value: "恶化="
        },
        {
          key: "恶化>",
          value: "恶化>"
        },
        {
          key: "恶化>=",
          value: "恶化>="
        },
        {
          key: "改善<",
          value: "改善<"
        },
        {
          key: "改善<=",
          value: "改善<="
        },
        {
          key: "改善=",
          value: "改善="
        },
        {
          key: "改善>",
          value: "改善>"
        },
        {
          key: "改善>=",
          value: "改善>="
        }
      ],
      columns: [
        {
          title: "序号",
          key: "index",
          scopedSlots: { customRender: "index" },
          width: 80
        },
        {
          title: "完成率设置",
          scopedSlots: { customRender: "completionRate" },
          width: 120
        },
        {
          title: "同比设置",
          scopedSlots: { customRender: "contemRate" },
          width: 120
        },
        {
          title: "环比设置",
          scopedSlots: { customRender: "previousRate" },
          width: 120
        },
        {
          title: "实际值连续恶化设置",
          key: "baseActualNum",
          dataIndex: "baseActualNum",
          scopedSlots: { customRender: "baseActualNum" }
        },
        {
          title: "连续未达标设置",
          key: "completionRateNum",
          dataIndex: "completionRateNum",
          scopedSlots: { customRender: "completionRateNum" }
        }
      ], // 表格列
      warnId: null
    };
  },
  computed: {
    newFrequencyList() {
      return this.baseForm.indexFrequency === "月"
        ? [
            {
              key: "月",
              value: "月"
            },
            {
              key: "周",
              value: "周"
            },
            {
              key: "日",
              value: "日"
            }
          ]
        : this.baseForm.indexFrequency === "周"
        ? [
            {
              key: "周",
              value: "周"
            },
            {
              key: "日",
              value: "日"
            }
          ]
        : [
            {
              key: "日",
              value: "日"
            }
          ];
    }
  },
  methods: {
    // 增加一行预警人
    pushOneLdap() {
      this.ldapArr.push({ ldap: [], warnFrequency: "" });
    },
    // 删除预警人
    removeLdap(index) {
      this.ldapArr.splice(index, 1);
    },
    // 增加一行条件
    addOneCondition() {
      this.conditionArr.push({
        completionRateFlag: "<",
        completionRate: "",
        contemRateFlag: "恶化>",
        contemRate: "",
        previousRateFlag: "恶化>",
        previousRate: "",
        baseActualNum: "",
        completionRateNum: ""
      });
    },
    // 删除条件
    removeCondition(index) {
      this.conditionArr.splice(index, 1);
    },
    dealTreeData(treeData) {
      treeData.forEach(i => {
        i.disabled = true;
        if (i?.childNodeList && i.childNodeList.length) {
          i.childNodeList.forEach(ai => {
            ai.disabled = true;
            if (ai?.childNodeList && ai.childNodeList.length) {
              ai.childNodeList.forEach(bi => {
                if (bi?.childNodeList && bi.childNodeList.length) {
                  bi.childNodeList = [];
                }
              });
            }
          });
        }
      });
    },
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      if (formValue) {
        this.isEdit = true;
        this.warnId = formValue.id;
        this.getWarnInfo(formValue.id);
      }
      this.getCurrentRoleList();
    },
    getWarnInfo(id) {
      request(`${adminUserUrlPrefix["lxp"]}/warn/get/${id}`)
        .then(res => {
          if (res) {
            this.baseForm = {
              indexId: res.indexId,
              base: [res.base],
              indexFrequency: res.indexFrequency,
              indexType: res.indexType,
              warnStatus: res.warnStatus === "0" ? false : true
            };
            this.ldapArr = [
              {
                ldap: res.warnUser.split(","),
                warnFrequency: res.warnFrequency
              }
            ]; // 预警人数组
            this.$set(this, "conditionArr", [
              {
                completionRateFlag: res.completionRateFlag || "<",
                completionRate: res.completionRate,
                contemRateFlag: res.contemRateFlag || "恶化>",
                contemRate: res.contemRate,
                previousRateFlag: res.previousRateFlag || "恶化>",
                previousRate: res.previousRate,
                baseActualNum: res.baseActualNum,
                completionRateNum: res.completionRateNum
              }
            ]);
            this.getIndexBaseList(res.indexId);
          }
        })
        .catch(() => {
          this.baseList = [];
          this.frequencyList = [];
        });
    },
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      request(`/api/system/user/getLdapByName?name=${value}`).then(res => {
        console.log(fetchId, this.lastFetchId);
        if (fetchId !== this.lastFetchId) {
          return;
        }
        this.userData = res;
        this.fetching = false;
      });
    },
    handleChange(index, value) {
      this.ldapArr[index].ldap = value;
      this.fetching = false;
      this.userData = [];
    },
    close() {
      this.baseForm = {
        indexId: "",
        base: [],
        indexFrequency: "",
        indexType: "",
        warnStatus: false
      };
      this.baseList = [];
      this.frequencyList = [];
      this.warnId = null;
      this.ldapArr = [
        {
          ldap: [],
          warnFrequency: ""
        }
      ]; // 预警人数组
      this.conditionArr = [
        {
          completionRateFlag: "<",
          completionRate: "",
          contemRateFlag: "恶化>",
          contemRate: "",
          previousRateFlag: "恶化>",
          previousRate: "",
          baseActualNum: "",
          completionRateNum: ""
        }
      ];
      this.$refs.baseForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 指标树选择改变
    treeSelectChange(value) {
      if (value) {
        this.baseForm.base = [];
        this.$refs.baseForm.validateField(["indexId"]);
        this.getIndexBaseList(value);
      }
    },
    // 获取指标底下的基地列表
    getIndexBaseList(indexId) {
      request(`${adminUserUrlPrefix["lxp"]}/warn/getBase`, {
        method: "POST",
        body: {
          indexId: indexId
        }
      })
        .then(res => {
          if (res) {
            this.baseList = res.base;
            this.frequencyList = res.frequency || [];
            this.baseForm.indexType = res.indexType;
          } else {
            this.baseList = [];
            this.frequencyList = [];
            this.baseForm.indexType = "";
          }
          if (!this.isEdit) {
            this.baseForm.indexFrequency = "";
            this.ldapArr[0].warnFrequency = "";
          }
        })
        .catch(() => {
          this.baseList = [];
          this.frequencyList = [];
          this.baseForm.indexFrequency = "";
          this.ldapArr[0].warnFrequency = "";
        });
    },
    // 保存卡片信息
    saveCard() {
      this.$refs.baseForm.validate(async valid => {
        if (valid) {
          const {
            indexId,
            base,
            indexFrequency,
            indexType,
            warnStatus
          } = cloneDeep(this.baseForm);
          let postData = {
            indexFrequency,
            indexType,
            warnStatus: warnStatus ? "1" : "0"
          };
          // 预警人员信息检查
          let ldapArr = cloneDeep(this.ldapArr);
          let ldapHasErr = false;
          for (let i = 0; i < ldapArr.length; i++) {
            const element = ldapArr[i];
            if (element.ldap.length === 0 || element.warnFrequency === "") {
              ldapHasErr = true;
              this.$message.warning(`请完善第${i + 1}行预警接收人信息！`);
              break;
            } else if (
              (indexFrequency === "周" && element.warnFrequency === "月") ||
              (indexFrequency === "日" &&
                ["月", "周"].includes(element.warnFrequency))
            ) {
              ldapHasErr = true;
              this.$message.warning(`第${i + 1}行预警频次有误，请重新选择！`);
              break;
            } else {
              element["ldapStr"] = element.ldap.join(",");
              delete element.ldap;
            }
          }
          if (ldapHasErr) {
            return;
          }
          // 预警条件检查
          let conditionArr = cloneDeep(this.conditionArr);
          let conHasErr = false;
          for (let i = 0; i < conditionArr.length; i++) {
            const element = conditionArr[i];
            console.log(element);
            if (
              !element.completionRate &&
              element.completionRate !== 0 &&
              !element.contemRate &&
              element.contemRate !== 0 &&
              !element.previousRate &&
              element.previousRate !== 0 &&
              !element.baseActualNum &&
              element.baseActualNum !== 0 &&
              !element.completionRateNum &&
              element.completionRateNum !== 0
            ) {
              conHasErr = true;
              this.$message.warning(`请完善第${i + 1}行预警条件信息！`);
              break;
            }
          }
          if (conHasErr) {
            return;
          }
          // completionRate contemRate previousRate
          if (!this.isEdit) {
            postData["indexId"] = indexId;
            postData["base"] = base.join(",");
            postData["conditionArr"] = conditionArr;
            postData["ldapArr"] = ldapArr;
          } else {
            postData["id"] = this.warnId;
            postData["indexId"] = indexId;
            postData["warnUser"] = this.ldapArr[0].ldap.join(",");
            postData["warnFrequency"] = this.ldapArr[0].warnFrequency;
            postData["completionRateFlag"] = conditionArr[0].completionRateFlag;
            postData["completionRate"] = conditionArr[0].completionRate;
            postData["contemRateFlag"] = conditionArr[0].contemRateFlag;
            postData["contemRate"] = conditionArr[0].contemRate;
            postData["previousRateFlag"] = conditionArr[0].previousRateFlag;
            postData["previousRate"] = conditionArr[0].previousRate;
            postData["baseActualNum"] = conditionArr[0].baseActualNum;
            postData["completionRateNum"] = conditionArr[0].completionRateNum;
          }
          request(
            `${adminUserUrlPrefix["zcx"]}/warn/${
              this.isEdit ? "update" : "save"
            }`,
            {
              method: this.isEdit ? "PUT" : "POST",
              body: postData
            }
          ).then(() => {
            this.close();
            this.$emit("fetchData");
          });
        }
      });
    },
    // 获取当前角色有的指标
    getCurrentRoleList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/roleDetail/getRoleTreeListByRoleId?roleId=`
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          this.treeData = res;
          this.dealTreeData(this.treeData);
        }
      });
    }
  }
};
</script>
<style lang="less">
.conditionForm {
  .ant-form-item {
    margin-bottom: 0;
  }
}
</style>
