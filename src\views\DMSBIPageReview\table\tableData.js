/**
 * 异常问题跟进表格模拟数据
 * @returns {Array} 表格数据数组
 */

import { queryWorkOrderData } from '../Api/index.js'
export const getTableData = async (params) => {
  try {
    // 调用API获取数据
    const result = await queryWorkOrderData(params);

    // 检查返回的数据结构
    if (!result) {
      console.error('API返回数据为空');
      return { data: [], total: 0 };
    }

    // 根据您提供的数据结构，处理返回的数据
    if (result.rows) {
      // // 如果数据直接在rows中
      return {
        data: result.rows.map(item=>{
          item.id = item.id||item.ID;
          return item
        }),
        total: result.total
      };
    } else {
      // 如果数据结构不符合预期
      console.error('API返回数据结构不符合预期:', result);
      return { data: [], total: 0 };
    }
  } catch (error) {
    console.error('获取表格数据失败:', error);
    return { data: [], total: 0 };
  }
};

export default getTableData;
