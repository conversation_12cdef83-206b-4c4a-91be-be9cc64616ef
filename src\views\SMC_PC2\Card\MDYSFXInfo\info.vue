<template>
  <div class="IndexMDYSFXInfo" :class="[skinStyle()]">
    <div class="drawerTitle" slot="title">
      <span style="margin-right: 20px;">组织层级下探</span>
      <a-icon type="close" @click="close" />
    </div>
    <div class="drawer-content" :class="orgYSFXList.length ? '' : 'noData'">
      <template v-if="orgYSFXList.length">
        <div class="item">
          <div class="title">管理层级</div>
          <div class="intro" v-html="orgYSFXList[0].des"></div>
          <div
            class="reason"
            v-for="(item, index) in orgYSFXList.slice(1, orgYSFXList.length)"
            :key="index"
          >
            <div class="_title" v-html="item.des"></div>
            <div
              class="_intro"
              v-for="(zitem, zindex) in item.detail || []"
              :key="`${zindex}-${index}`"
              v-html="zitem"
            ></div>
          </div>
        </div>
      </template>
      <template v-if="!orgYSFXList.length">
        <a-empty />
      </template>
    </div>
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
// import axios from "axios";
// import cloneDeep from "lodash/cloneDeep";
// import { dealThousandData } from "../../utils";
import Decimal from "decimal.js";

export default {
  name: "IndexCardDetailInfo",
  props: {
    pageClass: String,
    dataItem: Object,
    companyName: String
  },
  inject: ["skinStyle"],
  data() {
    return {
      Decimal,
      cardItem: {},
      visible: false,
      SYS_NAME: window.system,
      orgYSFXList: [],
      dataLoading: false
    };
  },
  watch: {
    dataItem: {
      handler(val) {
        // eslint-disable-next-line no-prototype-builtins
        if (val.hasOwnProperty("indexId")) {
          this.cardItem = val;
          // this.dataLoading = true;
          this.getMDYFSXList();
        } else {
          // 重置数据
          this.cardItem = {};
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // 初始化
  },
  methods: {
    // 关闭抽屉
    close() {
      this.cardItem = {};
      this.orgYSFXList = [];
      this.$emit("close");
    },
    // 获取组织下探列表
    getMDYFSXList() {
      const {
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id
      } = this.cardItem;
      const arr = [
        productAtt1Id,
        productAtt2Id,
        productAtt3Id,
        productAtt4Id,
        productAtt5Id,
        productAtt6Id,
        productAtt7Id
      ];
      arr.forEach((item, index) => {
        arr[index] = item
          ? item.includes("卡片名称") || item.includes("卡片标签")
            ? item
            : null
          : null;
      });
      const postData = {
        indexId: this.cardItem.indexId,
        businessSegmentsId: this.cardItem.businessSegmentsId,
        fullCode: this.cardItem.fullCode,
        indexDt: this.cardItem.indexDt,
        indexFrequencyId: this.cardItem.indexFrequencyId,
        cmimId: this.cardItem.cmimId,
        productAtt1Id: arr[0],
        productAtt2Id: arr[1],
        productAtt3Id: arr[2],
        productAtt4Id: arr[3],
        productAtt5Id: arr[4],
        productAtt6Id: arr[5],
        productAtt7Id: arr[6],
        indexName: this.cardItem.displayIndexName
      };
      request("/api/smc2/newIndexLibrary/endFactor", {
        method: "POST",
        body: postData
      }).then(res => {
        this.dataLoading = false;
        if (Array.isArray(res) && res.length) {
          this.orgYSFXList = res;
        }
      });
    }
  }
};
</script>
<style lang="less">
.IndexMDYSFXInfo {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  height: 100%;
  display: flex;
  flex-direction: column;
  &.hisense-style {
    .drawerTitle {
      height: 50px;
      color: #fff;
      background-color: #00aaa6;
      text-align: center;
      position: relative;
      & > span {
        display: block;
        width: 100%;
      }
      .anticon-close {
        position: absolute;
        right: 16px;
      }
    }
    .drawer-content {
      height: calc(100% - 50px);
      border-radius: 0 0 4px 4px;
    }
    &.dark {
      .drawerTitle {
        background: #070707;
        color: #ffffff;
        height: 50px;
        border-bottom: 1px solid #565b60;
      }
      .drawer-content {
        background: #070707;
        .item {
          background: #222325;
          .title {
            color: #a1a6ac;
          }
          .intro {
            color: rgba(255, 255, 255, 0.65);
          }
          .reason {
            ._title {
              color: rgba(255, 255, 255, 0.85);
              ._num {
                color: #0b70fe;
              }
            }
            ._intro {
              color: #ffffff;
              ._num {
                color: #0b70fe;
              }
            }
          }
        }
      }
    }
  }
  .drawerTitle {
    position: relative;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 57px;
  }
  ._flex {
    display: flex;
    align-items: center;
  }
  .drawer-content {
    // background-color: rgb(242, 243, 245);
    height: calc(100% - 57px);
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px;
    &.noData {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .item {
      width: 100%;
      mix-blend-mode: normal;
      border-radius: 4px;
      background: #f2f3f5;
      padding: 12px;
      .title {
        height: 22px;
        color: #1d2129;
        font-weight: bold;
        font-size: 14px;
        line-height: 22px;
        position: relative;
        &::before {
          content: "";
          display: block;
          position: absolute;
          left: -12px;
          top: 50%;
          margin-top: -7px;
          height: 14px;
          width: 4px;
          border-radius: 0px 2px 2px 0px;
          background-color: #13bbad;
        }
      }
      .intro {
        height: 20px;
        color: #4e5969;
        font-size: 12px;
        line-height: 20px;
        margin-bottom: 8px;
        ._num {
          color: #0b70fe;
        }
      }
      .reason {
        width: 100%;
        padding-left: 16px;
        font-size: 12px;
        line-height: 20px;
        margin-bottom: 8px;
        ._title {
          color: #4e5969;
          position: relative;
          &::before {
            position: absolute;
            content: "●";
            display: block;
            left: -16px;
            top: 0;
            height: 16px;
            width: 16px;
            text-align: left;
            line-height: 16px;
          }
          ._num {
            color: #0b70fe;
          }
        }
        ._intro {
          color: #1d2129;
          font-weight: bold;
          ._num {
            color: #0b70fe;
          }
        }
      }
    }
  }
}
</style>
