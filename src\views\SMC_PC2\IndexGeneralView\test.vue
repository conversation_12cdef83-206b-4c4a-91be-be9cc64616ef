<template>
  <a-drawer width="100%" placement="right" :closable="false" :visible="true">
    <component
      :is="compName"
      companyName="视像科技"
      signOrgId="H0101"
      sysSign="mom"
      :dataItem="dataItem"
    ></component>
    <!-- <DetectionInfo
      companyName="视像科技"
      signOrgId="H0101"
      sysSign="mom"
      :dataItem="dataItem"
    /> -->
  </a-drawer>
</template>
<script>
import { publicPath } from "@/utils/utils.js";
// import DetectionInfo from "../Card/DetectionInfo/info.vue"; // 本地调试
export default {
  // components: { DetectionInfo },
  provide() {
    return {
      businessSegmentsColorMap: "",
      skinStyle: ""
    };
  },
  data() {
    return {
      indexDetectionInfoJSUrl:
        (window.location.host.includes("localhost")
          ? "https://momtest.hisense.com"
          : "") + "/minio/mombucket/IndexDetectionInfo2.umd.min.1.0.js",
      compName: "",
      dataItem: {
        createdBy: null,
        createdDate: null,
        modifiedBy: null,
        modifiedDate: null,
        remark: "summary",
        id: 2231151,
        businessSegmentsId: "SEG_1300",
        indexId: "ZBY00581",
        cmimId:
          "ZBY00581-H0101-M-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000",
        signOrgId: "H0101",
        orgId: "H0101",
        indexFrequencyId: "M",
        productTypeId: null,
        brandId: null,
        productAtt1Id: null,
        productAtt2Id: null,
        productAtt3Id: null,
        productAtt4Id: null,
        productAtt5Id: null,
        productAtt6Id: null,
        productAtt7Id: null,
        productAtt8Id: null,
        productAtt9Id: null,
        indexDt: "2024-04",
        actualMolecule: null,
        actualDenominator: null,
        actualValue: null,
        previousMolecule: null,
        previousDenominator: null,
        previousValue: null,
        previousChangeRate: null,
        contemMolecule: "111492.0000000000000",
        contemDenominator: "165667.0000000000000",
        contemValue: "0.672988585500000",
        contemChangeRate: null,
        actualMoleculeSum: "61110.00000000000000",
        actualDenominatorSum: "87393.00000000000000",
        actualValueSum: null,
        actualSumRate: null,
        previousMoleculeSum: "168370.0000000000000",
        previousDenominatorSum: "243322.0000000000000",
        previousValueSum: null,
        previousSumChangeRate: null,
        contemMoleculeSum: "421939.0000000000000",
        contemDenominatorSum: "640067.0000000000000",
        contemValueSum: null,
        contemSumChangeRate: null,
        targetValue: null,
        targetCompletionRate: null,
        targetValueSum: null,
        targetSumCompletionRate: null,
        isManual: "Y",
        dataSources: "dwpp_cm_hs_tf_index_",
        deleteFlag: "0",
        attributesId: 2231151,
        indexUnitId: "%",
        precisions: "2",
        indexTypeId: "正向",
        indexNameInd: "合同工占比",
        indexSort: 1,
        businessSegments: "人资",
        indexName: null,
        signOrg: null,
        org: "海信视像科技公司",
        indexFrequency: null,
        productType: null,
        brand: null,
        dimension: null,
        userId: null,
        productAtt1: "-",
        productAtt2: "-",
        productAtt3: "-",
        productAtt4: "-",
        productAtt5: "-",
        productAtt6: "-",
        productAtt7: "-",
        fullCode: "HX-H-H0101",
        lable: null,
        pj:
          "ZBY00581-H0101-M-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000",
        dmId: "ZBY00581",
        parentFullCode: null,
        list: null,
        roleId: null,
        groupId:
          "ZBY00581-H0101-M-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000-0000",
        isShow: null,
        goDown: null,
        table: null,
        isDelete: null,
        indexDtList: null,
        sql: null,
        sql1: null,
        type: null,
        sign: null,
        orgList: null,
        indexList: null,
        idList: null,
        idList1: null,
        zsbg: null,
        levelName: null,
        parentName: null,
        isPrediction: null,
        indexDt1: null,
        indexDt2: null,
        structureEffect: null,
        selfEffect: null,
        sumEffect: null,
        targetEffect: null,
        target: null,
        compareTypes: null,
        currentProportion: null,
        previousProportion: null,
        bak: null,
        klDivergence: null,
        contribution: null,
        language: null,
        indexField: null,
        csuperiorCode: null
      }
    };
  },
  mounted() {
    if (!window["IndexDetectionInfo2"]) {
      const script = document.createElement("script");
      let fileUrl = this.indexDetectionInfoJSUrl;
      if (this.indexDetectionInfoJSUrl.indexOf("http") === -1) {
        fileUrl = `${publicPath}${this.indexDetectionInfoJSUrl}`;
      }
      script.src = fileUrl + `?t=${Date.now()}`;
      script.onload = () => {
        const exportCom = window["IndexDetectionInfo2"].default;
        this.compName = exportCom.myCom;
      };
      document.body.appendChild(script);
    } else {
      const exportCom = window["IndexDetectionInfo2"].default;
      this.compName = exportCom.myCom;
    }
  }
};
</script>
