/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.ContextMenu=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/contextMenu/context-menu.entry.js")}({"./dist/plugins/contextMenu/context-menu-dialog.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=d.Ul.vl,f=d.Go,g=d.GC$,h="gc.command.executed",i="left",j="top",k=function(a){l(b,a);function b(b,c){var d=a.call(this,b,e(b))||this,f=d;return f.Eu=c,d}return b.prototype.Qla=function(a,b,c){var d=this,e=a[0];d.kma&&d.kma.ai&&d.kma.ai(e,b,c,d.Eu)},b.prototype.OT=function(a,b,c,d,e){var f=this,g=f.Sla(c,d);f.kma=a,f.Qla(g,b,e)},b.prototype.Sla=function(b,c){var d=this,e=d.yo();return e.css([i,j],[b,c]),e.empty(),a.prototype.Ao.call(this),d.QQ(),e},b.prototype.Loa=function(a){var b,c,d,e=this,f=e.yo();return"block"!==f.css("display")||(b=f.offset(),b.top+=document.body.clientTop||0,b.left+=document.body.clientLeft||0,c=a.x,d=a.y,c<b.left||c>f.width()+b.left||d<b.top||d>f.height()+b.top)},b.prototype.QQ=function(){var a=this;a.Eu.bind(h,function(){a.close()})},b.prototype.Tla=function(){var a=this;a.Eu.unbind(h)},b.prototype.close=function(){var b=this;b.kma&&b.kma.no(),b.Tla(),b.vo(b.Cj)&&g("#"+b.Cj).remove(),a.prototype.close.call(this)},b}(f),b.ContextMenuViewDialog=k},"./dist/plugins/contextMenu/context-menu.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/contextMenu/context-menu.js")),d(c("./dist/plugins/contextMenu/menu-view.js")),d(c("./dist/plugins/contextMenu/context-menu.ns.js"));var e=c("./dist/plugins/contextMenu/context-menu-dialog.js");b.Cyb=e.ContextMenuViewDialog},"./dist/plugins/contextMenu/context-menu.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./dist/plugins/contextMenu/context-menu-dialog.js"),f=c("./dist/plugins/contextMenu/menu-view.js"),g=c("Core"),h=c("./dist/plugins/contextMenu/context-menu.ns.js"),i=g.GC$,j=d.Common.j.Fa,k=d.Common.j.za,l=g.Ul.nl,m=d.Commands.CommandManager,n="gc.spread.contextMenu.",o="gc.spread.",p="gc-spread-",q="viewport",r="colHeader",s="rowHeader",t="slicer",u="sheetTab",v="corner",w="removeSlicer",x="toggleComment",y="deleteComment",z="editComment",A="unhideSheet",B="hideSheet",C="unhideRows",D="unhideColumns",E="hideRows",F="hideColumns",G="sortAscend",H="sortDescend",I="sort",J="filter",K="insertComment",L="deleteSheet",M="insertSheet",N="insertRows",O="insertColumns",P="deleteRows",Q="deleteColumns",R="clearContents",S="paste",T="pasteOptions",U="pasteFormatting",V="pasteValues",W="pasteFormula",X="pasteAll",Y="cut",Z="copy",$="pasteValuesFormatting",_="pasteFormulaFormatting",aa="separator",ba="groupHeader",ca="slicerSortAscend",da="slicerSortDescend",ea="row",fa="col",ga=void 0,ha=new d.Common.ResourceManager(h.SR),ia=ha.getResource.bind(ha),ja={sheetTab:function(a,b,c){return RegExp("sheetTab","i").test(c.workArea)&&a.tabStripHitInfo&&a.tabStripHitInfo.sheetTab&&"newSheet"!==a.tabStripHitInfo.sheetTab.sheetName},outline:function(a,b,c){return!!(RegExp("outline","i").test(c.workArea)&&a.worksheetHitInfo&&a.worksheetHitInfo.outlineHitInfo)},viewport:function(a,b,c){if(RegExp("viewport","i").test(c.workArea)&&a.worksheetHitInfo&&3===a.worksheetHitInfo.hitTestType&&!a.worksheetHitInfo.floatingObjectHitInfo&&!a.worksheetHitInfo.shapeHitInfo&&!a.worksheetHitInfo.commentHitInfo)return ra(b,c)&&ua(b,c)},rowHeader:function(a,b,c){return RegExp("rowHeader","i").test(c.workArea)&&a.worksheetHitInfo&&2===a.worksheetHitInfo.hitTestType},colHeader:function(a,b,c){return RegExp("colHeader","i").test(c.workArea)&&a.worksheetHitInfo&&1===a.worksheetHitInfo.hitTestType},corner:function(a,b,c){if(RegExp("corner","i").test(c.workArea)&&a.worksheetHitInfo&&0===a.worksheetHitInfo.hitTestType)return ra(b,c)&&ua(b,c)},slicer:function(a,b,c){return RegExp("slicer","i").test(c.workArea)&&a.worksheetHitInfo&&a.worksheetHitInfo.floatingObjectHitInfo&&a.worksheetHitInfo.floatingObjectHitInfo.floatingObject&&"Slicer"===a.worksheetHitInfo.floatingObjectHitInfo.floatingObject.typeName},chart:function(a,b,c){return RegExp("chart","i").test(c.workArea)&&a.worksheetHitInfo&&a.worksheetHitInfo.floatingObjectHitInfo&&a.worksheetHitInfo.floatingObjectHitInfo.floatingObject&&"2"===a.worksheetHitInfo.floatingObjectHitInfo.floatingObject.typeName},shape:function(a,b,c){return RegExp("shape","i").test(c.workArea)&&a.worksheetHitInfo&&a.worksheetHitInfo.shapeHitInfo}},ka=[];function ma(a){var b,c;if(a.slicers){if(b=a.slicers.all(),!b||k(b))return null;for(c in b)if(b[c].isSelected())return!0;return!1}}function na(a,b,c){var d,e,f,g,h=b===ea?"rowCount":"colCount";for(d=0;d<a.length;d++)for(e=a[d],f=e[b]===-1?0:e[b],g=0;g<e[h];g++)c(f+g)}function oa(a,b){var c=[],d=-1,e;for(e=a.row;e<a.row+a.rowCount;e++)b[e]!==!1&&d===-1&&(d=e),b[e]===!1&&d!==-1&&(c.push([d,e-d]),d=-1);return d!==-1&&c.push([d,e-d]),c}function pa(a,b){var c,d,e,f;if(1===b.colCount&&b.row===-1){if(c=b.col,c===a.$q()&&(d=c-1,d>=0&&!a.getColumnVisible(d)))return!0}else if(1===b.rowCount&&b.col===-1&&(e=b.row,e===a.ar()&&(f=e-1,f>=0&&!a.getRowVisible(f))))return!0}function qa(a){var b,c,d;if(a.slicers){if(b=a.slicers.all(),!b||k(b))return null;c=[];for(d in b)b[d].isSelected()&&c.push(b[d]);return c}}function ra(a,b){var c,d,e,f,g,h,i,j=[o+K,o+z,o+x,o+y],k=b.name;if(j.indexOf(k)<0)return!0;if(c=a.getActiveSheet(),d=c.getActiveRowIndex(),e=c.getActiveColumnIndex(),c.comments){if(f=c.comments.get(d,e))return k!==o+K;if(k===o+z||k===o+x)return!1;if(k===o+K)return!0;for(g=c.getSelections(),h=0;h<g.length;h++)if(i=g[h],ta(i,c))return!0;return!1}}function sa(a,b){var c,d,e,f,g,h;if(b.comments)for(c=b.comments.all(),d=void 0,e=void 0,f=0;f<a.length;f++)for(g=a[f],h=0;h<c.length;h++)if(!c[h].locked()&&(e=c[h].nT,d=c[h].oT,e>=g.row&&e<g.row+g.rowCount&&d>=g.col&&d<g.col+g.colCount))return!0;return!1}function ta(a,b){var c,d,e,f;if(b.comments)for(c=b.comments.all(),d=void 0,e=void 0,f=0;f<c.length;f++)if(e=c[f].nT,d=c[f].oT,e>=a.row&&e<a.row+a.rowCount&&d>=a.col&&d<a.col+a.colCount)return!0;return!1}function ua(a,b){var c,d,e,f,g;return[o+I,o+J].indexOf(b.name)<0||(c=a.getActiveSheet(),d=c.getSelections(),!(!d||1!==d.length)&&(e=d[0],f=va(c,e),!(f&&(g=f.range(),e.row<g.row||e.col<g.col||e.row+e.rowCount>g.row+g.rowCount||e.col+e.colCount>g.col+g.colCount))))}function va(a,b){var c,d,e;for(c=b.row;c<b.row+b.rowCount;c++)for(d=b.col;d<b.col+b.colCount;d++)if(a.tables&&(e=a.tables.find(c,d)))return e;return null}function wa(a){var b,c,d,e=a;for(b=0;b<e.length-1;b++)for(c=b+1;c<e.length;c++)e[b].row<e[c].row&&(d=e[b],e[b]=e[c],e[c]=d);return e}function xa(a){var b,c,d,e=a;for(b=0;b<e.length-1;b++)for(c=b+1;c<e.length;c++)e[b].col<e[c].col&&(d=e[b],e[b]=e[c],e[c]=d);return e}function ya(a,b,c,d){var e=a.getSpans(ga,3);b>=0?(Aa(a,e,b,d,3),Aa(a,a.getSpans(ga,2),b,d,2)):c>=0&&(za(a,e,c,d,3),za(a,a.getSpans(ga,1),c,d,1))}function za(a,b,c,d,e){var f,g,h,i,j,k,l,m;for(f=0,g=b.length;f<g;f++)if(h=b[f],c>h.col&&c<h.col+h.colCount)for(i=a.getStyle(h.row,h.col,e),j=c;j<c+d;j++)for(k=h.row;k<h.row+h.rowCount;k++)a.setStyle(k,j,i,e),k===h.row+h.rowCount-1&&(l=a.getCell(h.row+h.rowCount-1,h.col),m=l.borderBottom(),m&&a.getCell(k,j).borderBottom(m))}function Aa(a,b,c,d,e){var f,g,h,i,j,k,l,m;for(f=0,g=b.length;f<g;f++)if(h=b[f],c>=h.row&&c<=h.row+h.rowCount)for(i=a.getStyle(h.row,h.col,e),j=c;j<c+d;j++)for(k=h.col;k<h.col+h.colCount;k++)a.setStyle(j,k,i,e),k===h.col+h.colCount-1&&(l=a.getCell(h.row,h.col+h.colCount-1),m=l.borderRight(),m&&a.getCell(j,k).borderRight(m))}function Ba(a,b,c,d,e){var f,h,i,j,k,l=a.getSheetFromName(b.sheetName);return!!l&&(f=b.considerAllSheets,h=a.sheets,l.suspendPaint(),i=g.Commands.bWa(l.name()),c?(e&&e(l,b),f?g.Commands._xb(h,b):l.ITa.undo(b[i])):(f?g.Commands.Zxb(h):l.ITa.startTransaction(),j=d(l,b),f?g.Commands.$xb(h,b):b[i]=l.ITa.endTransaction()),l.resumePaint(),f&&(k=a.getActiveSheet(),k&&l!==k&&k.repaint()),j)}function Ca(a,b,c){var d;if(c>0){for(d=b+1;d<a.getRowCount();d++)if(c-=a.getRowHeight(d),c<=0)return d-b}else if(c<0)for(d=b-1;d>=0;d--)if(c+=a.getRowHeight(d),c>=0)return d-b;return 0}function Da(a,b,c){var d;if(c>0){for(d=b+1;d<a.getColumnCount();d++)if(c-=a.getColumnWidth(d),c<=0)return d-b}else if(c<0)for(d=b-1;d>=0;d--)if(c+=a.getColumnWidth(d),c>=0)return d-b;return 0}function Ea(a,b,c,d){var e,f,g=a.getSheetFromName(b.sheetName);g&&(e=g.options.clipBoardOptions,g.options.clipBoardOptions=d,f=function(){g.options.clipBoardOptions=e},c.execute({cmd:S,sheetName:g.name(),callback:f}))}function Fa(a,b,c,d){return Ba(a,b,c,function(a){var b,c=qa(a);if(c&&0!==c.length)for(b in c)c.hasOwnProperty(b)&&c[b].sortState(d)})}la=function(){function a(){var a=this;a.menuView=new f.MenuView,a.menuData=[{text:ia().copy,name:o+Z,command:n+Z,iconClass:p+Z,workArea:q+r+s+t+v},{text:ia().cut,name:o+Y,command:n+Y,iconClass:p+Y,workArea:q+r+s+t+v},{text:ia().pasteOptions,name:o+T,iconClass:p+T,type:ba,workArea:q+r+s+t+v},{command:n+X,name:o+X,iconClass:p+X,group:o+T,text:ia().pasteAll,workArea:q+r+s+t+v},{command:n+W,name:o+W,iconClass:p+W,group:o+T,text:ia().pasteFormula,workArea:q+r+s+v},{command:n+V,name:o+V,iconClass:p+V,group:o+T,text:ia().pasteValues,workArea:q+r+s+v},{command:n+U,name:o+U,iconClass:p+U,group:o+T,text:ia().pasteFormatting,workArea:q+r+s+v},{command:n+$,name:o+$,iconClass:p+$,group:o+T,text:ia().pasteValuesFormatting,workArea:q+r+s+v},{command:n+_,name:o+_,iconClass:p+_,group:o+T,text:ia().pasteFormulaFormatting,workArea:q+r+s+v},{type:aa},{text:ia().clearContents,command:n+R,name:o+R,workArea:q+r+s+v},{type:aa},{text:ia().insertRows,name:o+N,command:n+N,workArea:s},{text:ia().insertColumns,name:o+O,command:n+O,workArea:r},{text:ia().deleteRows,name:o+P,command:n+P,workArea:s},{text:ia().deleteColumns,name:o+Q,command:n+Q,workArea:r},{text:ia().insertSheet,name:o+M,command:n+M,workArea:u},{text:ia().deleteSheet,name:o+L,command:n+L,workArea:u},{type:aa},{text:ia().filter,name:o+J,command:n+J,workArea:q+v},{text:ia().sort,name:o+I,subMenu:[{text:ia().sortAscend,name:o+G,command:n+G,iconClass:p+G},{text:ia().sortDescend,name:o+H,command:n+H,iconClass:p+H}],workArea:q+v},{text:ia().slicerSortAscend,name:o+ca,command:n+ca,iconClass:p+G,workArea:t},{text:ia().slicerSortDescend,name:o+da,command:n+da,iconClass:p+H,workArea:t},{type:aa},{text:ia().insertComment,name:o+K,command:n+K,iconClass:p+K,workArea:q+v},{text:ia().editComment,name:o+z,command:n+z,iconClass:p+z,workArea:q+v},{text:ia().deleteComment,name:o+y,command:n+y,iconClass:p+y,workArea:q+v},{text:ia().toggleComment,name:o+x,command:n+x,workArea:q+v},{type:aa},{text:ia().hideRows,name:o+E,command:n+E,workArea:s},{text:ia().unhideRows,name:o+C,command:n+C,workArea:s},{text:ia().hideColumns,name:o+F,command:n+F,workArea:r},{text:ia().unhideColumns,name:o+D,command:n+D,workArea:r},{type:aa},{text:ia().hideSheet,name:o+B,command:n+B,workArea:u},{text:ia().unhideSheet,name:o+A,command:n+A,workArea:u},{type:aa},{text:ia().removeSlicer,name:o+w,command:n+w,workArea:t}]}return a.prototype.ad=function(a){var b=this;b.Mma||(b.wu=a.commandManager(),j(b.Ela)&&(b.Ela=new e.ContextMenuViewDialog(a.xv(),i(a.sv))),b.nla(a),b.kTa={filter:{menuDataDict:[o+J],checkFunction:function(a){if(a.rowFilter)return!0}},comments:{menuDataDict:[o+K,o+z,o+y,o+x],checkFunction:function(a){if(a.comments)return!0}},slicer:{menuDataDict:[o+ca,o+da,o+w],checkFunction:function(a){if(a.slicers)return!0}},floatingObject:{menuDataDict:[o+V,o+U,o+W],checkFunction:function(a){var b,c,d;return!a.BR||(b=a.BR(),c=b.fromSheet,d=c&&c.CR,!d||void 0)}}},b.Mma=!0)},a.prototype.ola=function(a,b){var c,d,e,f,g,h=this;return b.focus(),c=h.pla(a,b),d=b.getActiveSheet(),j(c)||j(d)?void l(a):(e={row:d.getActiveRowIndex(),col:d.getActiveColumnIndex()},void(d.isEditing()&&c.worksheetHitInfo&&c.worksheetHitInfo.col===e.col&&c.worksheetHitInfo.row===e.row||b.getTab()&&b.getTab().FD||(h.ad(b),h.qla(c,b),f=h.Fla(h.menuData,c,b),g=h.onOpenMenu(h.menuData,f,c,b),g||(l(a),f.length&&h.jla(f,c,b)))))},a.prototype.qla=function(a,b){var c=this;c.Ela.Loa(a)&&c.sla(a,b)},a.prototype.sla=function(a,b){var c=this,d=b.getActiveSheet();d.isEditing()&&d.endEdit(),c.tla(a)?c.ula(a,b):c.vla(a)&&c.wla(a,b)},a.prototype.vla=function(a){return!!a.worksheetHitInfo},a.prototype.xla=function(a,b,c,d){var e=!0,f,g;if(1===d){for(f=0;f<a.length;f++)g=a[f],g.row===-1&&c>=g.col&&c<g.col+g.colCount&&(e=!1);return e}if(2===d){for(f=0;f<a.length;f++)g=a[f],g.col===-1&&b>=g.row&&b<g.row+g.rowCount&&(e=!1);return e}for(f=0;f<a.length;f++)if(g=a[f],g.contains(b,c))return!1;return!0},a.prototype.tla=function(a){return!(!a.tabStripHitInfo||!a.tabStripHitInfo.sheetTab)},a.prototype.yla=function(a){return a.tabStripHitInfo.sheetTab.sheetName},a.prototype.wla=function(a,b){var c=this,d=b.getActiveSheet(),e=a.worksheetHitInfo;e.floatingObjectHitInfo||e.commentHitInfo||e.shapeHitInfo||(c.Ala(d,e),c.aPa(d))},a.prototype.aPa=function(a){var b,c=a.getActiveRowIndex(),d=a.getActiveColumnIndex(),e=a.comments;e&&(b=e.get(c,d),b&&e.bU===b&&e.hT(b))},a.prototype.zla=function(a){return 3===a.hitTestType},a.prototype.cs=function(a,b,c){a.cs&&a.cs(b,c)},a.prototype.Ala=function(a,b){var c=this,d=a.ITa.getSelections(),e=b.row,f=b.col;c.xla(d,e,f,b.hitTestType)&&a.Qs(b.rowViewportIndex<0?-1:b.row,b.colViewportIndex<0?-1:b.col)&&(a.mm.Ala(a,b,e,f),a.Au(d))},a.prototype.ula=function(a,b){var c=this.yla(a),d=b.getSheetIndex(c);b.dq(d,2,!1,!0),this.$Oa(this.menuData,a,b)},a.prototype.pla=function(a,b){var c=b.oia(),d=a.pageX-c.left,e=a.pageY-c.top;return b.hitTest(d,e,!0)},a.prototype.onOpenMenu=function(a,b,c,d){},a.prototype.jla=function(a,b,c){var d=this;d.Ela.Loa(b)&&(d.Cla(),d.Dla(a,b,c))},a.prototype.Cla=function(){this.Ela.close()},a.prototype.Dla=function(a,b,c){var d=this;d.Ela.OT(d.menuView,a,b.x,b.y,c)},a.prototype.Fla=function(a,b,c){var d=this,e=[],f={};return i.extend(!0,f,a),i.each(f,function(a,d){j(d)||(d.type===aa&&e.push(d),i.each(ja,function(a,f){f(b,c,d)&&e.push(d)}))}),d.J$a(e,b),d.lTa(e,c),d.UZa(e,c),d.RZa(e),e=d.Bma(e)},a.prototype.J$a=function(a,b){var c=b.worksheetHitInfo,d=c&&(c.row===ga||c.col===ga);d&&a.splice(0,a.length)},a.prototype.lTa=function(a,b){var c=this,d=b.getActiveSheet(),e=c.kTa;return i.each(e,function(b,e){var f,g,h;if(!e.checkFunction(d))for(f=e.menuDataDict,g=0,h=f.length;g<h;g++)c.hma(a,f[g],function(a,b){a[b]=null})}),a},a.prototype.RZa=function(a){var b,c=this,d=a.length;for(b=d-1;b>0;b--)a[b]?a[b].subMenu&&c.RZa(a[b].subMenu):a.splice(b,1);return a},a.prototype.Bma=function(a){var b,c,d,e,f,g=[],h=a.length;for(b=0;b<h;b++)a[b]&&a[b].type===aa&&a[b+1]&&a[b+1].type===aa||g.push(a[b]);for(h=g.length,c=0,d=0,e=!0,f=!0,b=0;b<h&&(e&&g[b].type===aa?c++:e=!1,f&&g[h-1-b].type===aa?d++:f=!1,e||f);b++);return g.splice(h-d,d),g.splice(0,c),g},a.prototype.UZa=function(a,b){var c=this;c.VZa(a,b),c.QZa(a,b)},a.prototype.VZa=function(a,b){var c,d,e=this,f=b.getActiveSheet();for(c=0;c<a.length;c++)if(a[c]){switch(a[c].name){case o+N:f.ZZa()||(a[c].disable=!0);break;case o+P:d=f.tables,(!f.ZZa()||d&&d.Dyb())&&(a[c].disable=!0);break;case o+O:case o+Q:f.$Za()||(a[c].disable=!0)}a[c]&&a[c].subMenu&&e.VZa(a[c].subMenu,b)}},a.prototype.QZa=function(a,b){var c,d,e,f,g=this,h=b.getActiveSheet(),i=h.options.isProtected,j=h.options.protectionOptions;if(i&&j)for(c=void 0,d=0;d<a.length;d++)if(a[d]){switch(a[d].name){case o+N:j.allowInsertRows||(a[d].disable=!0);break;case o+O:j.allowInsertColumns||(a[d].disable=!0);break;case o+P:j.allowDeleteRows||(a[d].disable=!0);break;case o+Q:j.allowDeleteColumns||(a[d].disable=!0);break;case o+J:j.allowFilter||(a[d].disable=!0);break;case o+G:case o+H:j.allowSort||(a[d].disable=!0);break;case o+E:case o+C:case o+F:case o+D:a[d].disable=!0;break;case o+ca:case o+da:j.allowEditObjects||a.splice(0,a.length);break;case o+K:j.allowEditObjects||(a[d]=null);break;case o+z:e=void 0,h.comments&&(c=h.comments.get(h.getActiveRowIndex(),h.getActiveColumnIndex()),e=c&&c.lockText()),!j.allowEditObjects&&e&&(a[d]=null);break;case o+y:f=void 0,h.comments&&(f=!sa(h.getSelections(),h)),!j.allowEditObjects&&f&&(a[d]=null)}a[d]&&a[d].subMenu&&g.QZa(a[d].subMenu,b)}},a.prototype.$Oa=function(a,b,c){var d,e,f,g;if(b.tabStripHitInfo&&(d=this,e=c.sheets,f=d.RS(o+A))){for(g=0;g<e.length;g++)if(!e[g].visible())return void(f.disable=!1);f.disable=!0}},a.prototype.RS=function(a){var b=this;return b.gma(b.menuData,a)},a.prototype.gma=function(a,b){var c,d=this;return d.hma(a,b,function(a,b){c=a[b]}),c},a.prototype.hma=function(a,b,c){for(var d=this,e=a.length,f=0;f<e;f++)if(!j(a[f])){if(b===a[f].name)return void c(a,f);a[f].subMenu&&d.hma(a[f].subMenu,b,c)}},a.prototype.nla=function(a){var b,c,d,e,f,h,i,l,m,o,p,q,r,s,t,u,v,I,T,aa,ba,ga,ha,ia,ja,la,qa,ra,sa,ta,ua=a.commandManager(),za={canUndo:!1,execute:function(a,b){var c=a.getSheetFromName(b.sheetName);c&&(ma(c)?ua.execute({cmd:"copyFloatingObjects",sheetName:c.name()}):ua.execute({cmd:Z,sheetName:c.name(),ignoreClipboard:!0}))}};ua.register(n+Z,za,null,!1,!1,!1,!1),b={canUndo:!1,execute:function(a,b){var c=a.getSheetFromName(b.sheetName);c&&(ma(c)?ua.execute({cmd:"cutFloatingObjects",sheetName:c.name()}):ua.execute({cmd:Y,sheetName:c.name(),ignoreClipboard:!0}))}},ua.register(n+Y,b,null,!1,!1,!1,!1),c={canUndo:!1,execute:function(a,b){var c,d,e=a.getSheetFromName(b.sheetName);e&&(e.isPasteFloatingObject&&e.isPasteFloatingObject()?ua.execute({cmd:"pasteFloatingObjects",sheetName:e.name()}):e.isPasteShapes&&e.isPasteShapes()?ua.execute({cmd:"pasteShapes",sheetName:e.name()}):(c=e.options.clipBoardOptions,e.options.clipBoardOptions=0,d=function(){e.options.clipBoardOptions=c},ua.execute({cmd:S,sheetName:e.name(),callback:d})))}},ua.register(n+X,c,null,!1,!1,!1,!1),d={canUndo:!1,execute:function(a,b){var c=3;Ea(a,b,ua,c)}},ua.register(n+W,d,null,!1,!1,!1,!1),e={canUndo:!1,execute:function(a,b){var c=1;Ea(a,b,ua,c)}},ua.register(n+V,e,null,!1,!1,!1,!1),f={canUndo:!1,execute:function(a,b){var c,d,e=a.getSheetFromName(b.sheetName);e&&(c=e.options.clipBoardOptions,e.options.clipBoardOptions=2,d=function(){e.options.clipBoardOptions=c},ua.execute({cmd:S,sheetName:e.name(),callback:d}))}},ua.register(n+U,f,null,!1,!1,!1,!1),h={canUndo:!1,execute:function(a,b){var c=4;Ea(a,b,ua,c)}},ua.register(n+$,h,null,!1,!1,!1,!1),i={canUndo:!1,execute:function(a,b){var c=5;Ea(a,b,ua,c)}},ua.register(n+_,i,null,!1,!1,!1,!1),l={canUndo:!1,execute:function(a,b){var c=a.getSheetFromName(b.sheetName);c&&ua.execute({cmd:"clear",sheetName:c.name()})}},ua.register(n+R,l,null,!1,!1,!1,!1),m={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q=c.commentState||2,r=c.displayMode,s=c.activeRow,t=c.activeCol,u=b.comments.add(s,t,"");u&&(u.commentState(q),r&&u.displayMode(r),d=b.Er(s),e=b.Fr(t),f=b.getCellRect(s,t,d,e),g=b.am(),h=g.Ft(d,e),i=u.location(),j=u.width(),k=u.height(),l=void 0,m=void 0,n=void 0,o=void 0,p=a.ku,f.y+i.y<h.y?(n=Ca(b,s,f.y+i.y-h.y),l=p.ms(b,n),p.ju(l),b.os()):f.y+i.y+k>h.y+h.height&&(n=Ca(b,s,f.y+i.y+k-(h.y+h.height)),l=p.ms(b,n),p.ju(l),b.os()),f.x+i.x+j>h.x+h.width&&(o=Da(b,t,f.x+i.x+j-(h.x+h.width)),m=p.Sw(b,o),p.lu(m),b.qs()))},function(a,b){var c=b.activeRow,d=b.activeCol,e=a.comments.get(c,d);e&&(b.displayMode=e.displayMode(),b.commentState=e.commentState())})}},ua.register(n+K,m,null,!1,!1,!1,!1),o={canUndo:!1,execute:function(a,b){var c,d,e,f=a.getSheetFromName(b.sheetName);f&&(c=b.activeRow,d=b.activeCol,e=f.comments.get(c,d),e&&e.commentState(2))}},ua.register(n+z,o,null,!1,!1,!1,!1),p={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){var c,d,e,f,g,h=b.selections,i=h.length;if(!(i<=0))for(c=0;c<i;c++)for(d=h[c],e=d.row;e<d.row+d.rowCount;e++)for(f=d.col;f<d.col+d.colCount;f++)g=a.comments.get(e,f),!g||a.options.isProtected&&!a.options.protectionOptions.allowEditObjects&&g.locked()||ua.execute({cmd:y,sheetName:a.name(),row:g.nT,col:g.oT})})}},ua.register(n+y,p,null,!1,!1,!1,!1),q={canUndo:!1,execute:function(a,b){var c,d,e,f=a.getSheetFromName(b.sheetName);f&&(c=b.activeRow,d=b.activeCol,e=f.comments.get(c,d),e&&(2===e.displayMode()?e.displayMode(1):e.displayMode(2)))}},ua.register(n+x,q,null,!1,!1,!1,!1),r={canUndo:!0,execute:function(a,b,c){return b.considerAllSheets=!0,Ba(a,b,c,function(a){var c,d,e,f,g,h,i=wa(b.selections);for(c=0;c<i.length;c++){for(d=a.getRange(i[c].row-1,0,1,i[c].colCount),e=[],f=0;f<d.colCount;f++)a.getCell(d.row,f).locked()||e.push(f);for(a.addRows(i[c].row,i[c].rowCount),g=0;g<e.length;g++)for(h=i[c].row;h<i[c].row+i[c].rowCount;h++)a.getCell(h,e[g]).locked(!1);ya(a,i[c].row,-1,i[c].rowCount)}})}},ua.register(n+N,r,null,!1,!1,!1,!1),s={canUndo:!0,execute:function(a,b,c){return b.considerAllSheets=!0,Ba(a,b,c,function(a){var c,d,e,f,g,h,i=xa(b.selections);for(c=0;c<i.length;c++){for(d=a.getRange(0,i[c].col-1,i[c].rowCount,1),e=[],f=0;f<d.rowCount;f++)a.getCell(f,d.col).locked()||e.push(f);for(a.addColumns(i[c].col,i[c].colCount),g=0;g<e.length;g++)for(h=i[c].col;h<i[c].col+i[c].colCount;h++)a.getCell(e[g],h).locked(!1);ya(a,-1,i[c].col,i[c].colCount)}})}},ua.register(n+O,s,null,!1,!1,!1,!1),t={canUndo:!0,execute:function(a,b,c){return b.considerAllSheets=!0,Ba(a,b,c,function(a){var c,d,e,f,g=wa(b.selections),h=a.filterRowsVisibleInfo&&a.filterRowsVisibleInfo.rowsVisibleInfo,i=h&&Object.keys(h).length>0;for(c=0;c<g.length;c++)if(d=g[c],i)for(e=oa(d,h),f=e.length-1;f>=0;f--)a.deleteRows(e[f][0],e[f][1]);else a.deleteRows(d.row,d.rowCount)})}},ua.register(n+P,t,null,!1,!1,!1,!1),u={canUndo:!0,execute:function(a,b,c){return b.considerAllSheets=!0,Ba(a,b,c,function(a){var c,d=xa(b.selections);for(c=0;c<d.length;c++)a.deleteColumns(d[c].col,d[c].colCount)})}},ua.register(n+Q,u,null,!1,!1,!1,!1),v={canUndo:!1,execute:function(a){var b=a.getActiveSheetIndex(),c=a.getActiveSheet(),d=a.vv(a.wv(b));a.ow(b,2,d),a.Wq(g.Events.ActiveSheetChanged,{oldSheet:c,newSheet:d})}},ua.register(n+M,v,null,!1,!1,!1,!1),I={canUndo:!1,execute:function(a){var b,c,d;a.getSheetCount()>1&&(b=a.getActiveSheetIndex(),c=a.getActiveSheet(),a.removeSheet(b),d=a.getActiveSheet(),a.Wq(g.Events.ActiveSheetChanged,{oldSheet:c,newSheet:d})),a.undoManager().clear()}},ua.register(n+L,I,null,!1,!1,!1,!1),T={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){var c,d,e=b.selections,f=e[0],g=va(a,f),h=b.activeRow,i=b.activeCol;g?(c=g.range(),c.contains(h,i,1,1)&&(d=g.name(),ua.execute({cmd:"contextmenuFilterForTable",sheetName:a.name(),cmdOption:{tableName:d,activeRow:h,activeCol:i,expectedText:a.getText(h,i)}}))):a.rowFilter&&ua.execute({cmd:"contextmenuFilterForSheet",sheetName:a.name(),cmdOption:{activeRow:h,activeCol:i,selection:f,expectedText:a.getText(h,i)}})})}},ua.register(n+J,T,null,!1,!1,!1,!1),aa={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){var c=b.activeCol,d=b.selections,e=va(a,d[0]),f=e?e.dataRange():d[0];a.sortRange(f.row,f.col,f.rowCount,f.colCount,!0,[{index:c,ascending:!0}])})}},ua.register(n+G,aa,null,!1,!1,!1,!1),ba={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){var c=b.activeCol,d=b.selections,e=va(a,d[0]),f=e?e.dataRange():d[0];a.sortRange(f.row,f.col,f.rowCount,f.colCount,!0,[{index:c,ascending:!1}])})}},ua.register(n+H,ba,null,!1,!1,!1,!1),ga={canUndo:!0,execute:function(a,b,c){return Fa(a,b,c,1)}},ua.register(n+ca,ga,null,!1,!1,!1,!1),ha={canUndo:!0,execute:function(a,b,c){return Fa(a,b,c,2)}},ua.register(n+da,ha,null,!1,!1,!1,!1),ia={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){na(b.selections,ea,function(b){a.setRowVisible(b,!1)})})}},ua.register(n+E,ia,null,!1,!1,!1,!1),ja={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){na(b.selections,fa,function(b){a.setColumnVisible(b,!1)})})}},ua.register(n+F,ja,null,!1,!1,!1,!1),la={canUndo:!1,execute:function(a){var b,c;a.getSheetCount()-ka.length<=1||(b=a.getActiveSheet(),b&&(ka.push(b),b.visible(!1),c=a.getActiveSheet(),c&&(a.focus(!0),a.Wq(g.Events.ActiveSheetChanged,{oldSheet:b,newSheet:c}))),a.undoManager().clear())}},ua.register(n+B,la,null,!1,!1,!1,!1),qa={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){var c=b.selections,d=b.isAnyHiddenRowOrColBeforeFirstVisibleRowOrCol;j(d)&&(d=b.isAnyHiddenRowOrColBeforeFirstVisibleRowOrCol=pa(a,c[0])),1===c.length&&d?(a.setRowVisible(c[0].row-1,!0),a.mq(c[0].row-1)):na(c,ea,function(b){a.setRowVisible(b,!0),b===a._t()&&(a.lq=0,a.os())})})}},ua.register(n+C,qa,null,!1,!1,!1,!1),ra={canUndo:!0,execute:function(a,b,c){return Ba(a,b,c,function(a){var c=b.selections,d=b.isAnyHiddenRowOrColBeforeFirstVisibleRowOrCol;j(d)&&(d=b.isAnyHiddenRowOrColBeforeFirstVisibleRowOrCol=pa(a,c[0])),1===c.length&&d?(a.setColumnVisible(c[0].col-1,!0),a.rq(c[0].col-1)):na(c,fa,function(b){a.setColumnVisible(b,!0),b===a.$t()&&(a.qq=0,a.qs())})})}},ua.register(n+D,ra,null,!1,!1,!1,!1),sa={canUndo:!1,execute:function(a){var b,c,d,e;if(0===ka.length)for(b=a.sheets,c=0;c<b.length;c++)b[c].visible()||ka.push(b[c]);d=a.getActiveSheet(),ka.length>0&&(e=ka.pop(),e.visible(!0),a.setActiveSheet(e.name())),a.getActiveSheet()&&(a.focus(!0),a.Wq(g.Events.ActiveSheetChanged,{oldSheet:d,newSheet:a.getActiveSheet()}))}},ua.register(n+A,sa,null,!1,!1,!1,!1),ta={canUndo:!1,execute:function(a,b){var c,d,e,f=a.getSheetFromName(b.sheetName);if(f&&(a.suspendPaint(),c=[],d=f.slicers.all(),d&&!k(d))){for(e in d)d[e].isSelected()&&c.push(d[e].name());c.length>0&&ua.execute({cmd:"deleteFloatingObjects",sheetName:f.name(),floatingObjects:c}),a.resumePaint()}}},ua.register(n+w,ta,null,!1,!1,!1,!1)},a}(),b.ContextMenu=la,g.Workbook.$n("contextmenu",{setHost:function(){var a=this,b=a.qo;a.contextMenu||(a.contextMenu=new la),i(b).bind("contextmenu.gcSheet",function(b){if(a.options.allowContextMenu)return a.contextMenu.ola(b,a)})},dispose:function(){i(this.qo).unbind("contextmenu.gcSheet")},onCultureChanged:function(){var a,b=ia(),c=this.contextMenu;c&&(a=c.menuData,i.each(b,function(b,d){c.hma(a,o+b,function(a,b){a[b].text=d})}))}})},"./dist/plugins/contextMenu/context-menu.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/contextMenu/context-menu.res.en.js");b.SR={en:d}},"./dist/plugins/contextMenu/context-menu.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.copy="Copy",b.cut="Cut",b.pasteOptions="Paste Options:",b.pasteAll="All",b.pasteFormula="Formulas",b.pasteValues="Values",b.pasteFormatting="Formatting",b.pasteValuesFormatting="Values&Formatting",b.pasteFormulaFormatting="Formula&Formatting",b.clearContents="Clear Contents",b.insertRows="Insert",b.insertColumns="Insert",b.deleteRows="Delete",b.deleteColumns="Delete",b.insertSheet="Insert",b.deleteSheet="Delete",b.insertComment="Insert Comment",b.filter="Filter",b.sort="Sort",b.slicerSortAscend="Sort A to Z",b.slicerSortDescend="Sort Z to A",b.sortAscend="Sort A to Z",b.sortDescend="Sort Z to A",b.hideRows="Hide",b.hideColumns="Hide",b.hideSheet="Hide",b.unhideSheet="Unhide",b.unhideColumns="Unhide",b.unhideRows="Unhide",b.editComment="Edit Comment",b.deleteComment="Delete Comment",b.toggleComment="Show/Hide Comment",b.removeSlicer="Remove",b.removeFloatingObject="Remove"},"./dist/plugins/contextMenu/menu-view.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=e.GC$,g=e.Ul.Nl,h=d.Common.j,i=h.Fa,j=h.Aa,k=h.C4,l="mouseenter",m="mouseleave",n="click",o="gc.command.executed",p=".",q="gc-ui-contextmenu",r="-",s="container",t="hover",u="disable-hover",v="text",w="icon",x="menuitem",y="nonselective-menuitem",z="menuitem-content",A="sup-container",B="separator",C="sup-indicator",D="subitems-container",E="group-container",F="title",G="group-header",H="groupitems-container",I="groupitem",J="nonexecutable",K="executable",L="disable",M=q+r+t,N=q+r+x,O=q+r+y,P=q+r+z,Q=q+r+K,R=q+r+A,S=q+r+D,T="gc-statusbar-contextmenu",U=T+r+"check",V=T+r+"check-container",W=T+r+"content",X=T+r+"status",Y=" ui-widget",Z=" ui-state-hover",$=" ui-icon ui-icon-triangle-1-e",_="DIV",aa="SPAN",ba="keydown.ctx.gcSheet",ca=void 0,da="none",ea="inline-block",fa="visible",ga="hidden";function ia(a){if(a instanceof Object)return!0}ha=function(){function a(){}return a.prototype.ai=function(a,b,c,d){var e=this;e.qo=f(a),e.lla=k(b),e.wu=c.commandManager(),e.xc=c,e.Eu=d,e.LQ=[],e.YPa={},e.rQa=[],e.ZPa=null,e.lma()},a.prototype.mma=function(){var a,b,c,d,e,g=this,h=parseInt(g.qo.css("left"),10),j=parseInt(g.qo.css("top"),10),k=g.qo.width(),l=g.qo.height(),m=f(g.xc.getHost()),n=m.width(),o=g.hostInfo;i(o)?(d=m.height(),h+k>n&&(h=h-k>0?h-k:h,g.qo.css("left",h)),j+l>d&&(j-l>0?j-=l:(e=j+l-d,j=j-e>0?j-e:j),g.qo.css("top",j))):(a=o.top,b=o.width,c=o.height,h+k>b&&(h=h-k>0?h-k:h,g.qo.css("left",h)),window.innerHeight-a-c<a&&(j-=l,g.qo.css("top",j)))},a.prototype.lma=function(){var a,b,c,d=this,e=d.lla;i(e)||!ia(e)||e.length<1||(e instanceof Array||(e=[e]),a="menuView",b=d.nma(a,e),d.YPa.menuView=b,c=d.oma(d.lla),c.appendTo(d.qo),d.mma(),d.QQ(),d.rQa.push({name:a,menuView:c}))},a.prototype.nma=function(a,b){for(var c,d=this,e=b.length,f=0,g=[];f<e;f++)c=b[f],i(c)||"separator"===c.type||(c.subMenu?d.YPa[c.name]=d.nma(c.name,c.subMenu):"groupHeader"===c.type&&(d.pma(c,b),d.YPa[c.name]=d.nma(a,c.groups)),g.push(d.qma(a,c)));return g},a.prototype.qma=function(a,b){var c,d,e,g=this;if(c="groupHeader"===b.type?g.$Pa():g.rma(),d=g.createMenuItemElement(b),!i(d))return d instanceof HTMLElement&&(d=f(d)),d.appendTo(c),e={name:b.name,host:c,menuItemData:b,menuName:a},g.LQ.push(e),e},a.prototype.rma=function(){var a=f(g(_));return a.addClass(N),a},a.prototype.$Pa=function(){var a=f(g(_));return a.addClass(O),a},a.prototype.createMenuItemElement=function(a){var b,c=this,d=a.type;return b="groupHeader"===d?c.tma(a):"groupItem"===d?c.uma(a):"statusBar"===d?c.Eyb(a):a.subMenu?c.vma(a):c.wma(a)},a.prototype.wma=function(a){var b,c,d=f(g(_)).addClass(P);return a.command?a.disable?d.addClass(q+r+L):d.addClass(Q):d.addClass(q+r+J),b=f(g(aa)).addClass(q+r+w),a.iconClass&&b.addClass(a.iconClass),b.appendTo(d),a.text&&(c=f(g(aa)).addClass(q+r+v),c[0].innerHTML=a.text,c.appendTo(d)),d},a.prototype.sma=function(a){return f(g(_)).addClass(q+r+B)},a.prototype.tma=function(a){var b,c,d,e,h,i,j,k=this,l=f(g(_)).addClass(q+r+E);if(a.groups.length<=0)return l;for(b=k.wma(a),b.addClass(q+r+G),b.appendTo(l),c=f(g(_)).addClass(q+r+H),
d=a.groups,e=d.length,h=0;h<e;h++)i=k.xma("name",d[h].name),j=i.host,j.addClass(q+r+I),j.appendTo(c);return c.appendTo(l),l},a.prototype.uma=function(a){var b=this,c=b.wma(a),d=c.find(p+q+r+v);return d.remove(),c.attr(F,a.text),c},a.prototype.Eyb=function(a){var b,c,d,e=g("div"),h=f(e).addClass(P),i=a.visible,j=a.menuContent,k=a.status,l=g("div");return f(l).addClass(U),b=g("div"),f(b).addClass(V),b.appendChild(l),b.style.visibility=i?fa:ga,c=g("div"),c.innerText=j,f(c).addClass(W),d=g("div"),f(d).addClass(X),k?(d.innerText=k,d.style.display=ea):d.style.display=da,e.appendChild(b),e.appendChild(c),e.appendChild(d),h},a.prototype.vma=function(a){var b,c,d=this,e=f(g(_)).addClass(R),h=d.wma(a);return a.subMenu.length>0&&(b=f(g(aa)).addClass(q+r+C+$),b.appendTo(h),c=d.oma(a.subMenu),c.addClass(S),c.appendTo(e)),h.appendTo(e),e},a.prototype.oma=function(a){for(var b,c,d,e,h=this,j=f(g(_)).addClass(q+r+s+Y),k=a.length,l=0;l<k;l++)i(a[l])||(b=a[l].name,i(b)?"separator"===a[l].type&&(d=h.$Pa(),e=h.sma(a[l]),e.appendTo(d),d.appendTo(j)):(c=h.xma("name",b),i(c)||c.host.appendTo(j)));return j},a.prototype.xma=function(a,b){for(var c=this,d=c.LQ,e=d.length,g=0;g<e;g++)if(b instanceof f?b[0]===d[g][a][0]:d[g][a]===b)return d[g]},a.prototype.pma=function(a,b){var c,d,e;for(a.groups=[],c=b.length,d=0;d<c;d++)e=b[d],e&&e.group&&e.group===a.name&&(e.type="groupItem",a.groups.push(e),b.splice(d,1),d--)},a.prototype.QQ=function(){var a=this;f(p+N).bind(l,function(){var b,c,d,e=f(this),g=a.xma("host",e);for(a.ZPa=g,b=a.rQa,c=0,d=b.length;c<d&&g.menuName!==a.rQa[d-c-1].name;c++)a.Ooa();a._Pa(),a.cQa(!1)}).bind(m,function(){a.ZPa=null,a._Pa()}).bind(n,function(b){var c,d=f(this),e=a.xma("host",d);a.ZPa=e,c=a.aQa(!1,b),c&&a.WT()}),f(document).bind(ba,function(b){var c,d,e;switch(b.keyCode){case 27:c=a.Ooa(),c||a.WT();break;case 32:case 13:c=a.aQa(!0,b),c&&a.WT();break;case 9:b.shiftKey?a.bQa(!1,!0):a.bQa(!0,!0);break;case 37:d=a.Ooa(),e=a.xma("name",d),e&&(a.ZPa=e),a.ZPa&&a.ZPa.menuItemData.group&&a.bQa(!1,!0);break;case 38:a.bQa(!1,!1);break;case 39:a.ZPa&&a.ZPa.menuItemData.group&&a.bQa(!0,!0),a.cQa(!0);break;case 40:a.bQa(!0,!1)}b.preventDefault?b.preventDefault():b.returnValue=!1})},a.prototype.Noa=function(a){var b,c,d,e,f=a.offset().left,g=a.width(),h=window.scrollX||window.pageXOffset;f+g>h+window.innerWidth&&a.css("left",-g),b=a.offset().top,c=a.height(),d=a.position().top,e=window.scrollY||window.pageYOffset,b+c>e+window.innerHeight&&a.css("top",d-(b+c-(e+window.innerHeight)))},a.prototype.zma=function(a){return a.command},a.prototype.getCommandOptions=function(a,b,c){},a.prototype.aQa=function(a,b){var c,d=this,e=d.ZPa;if(e&&!e.menuItemData.disable)return c=e.menuItemData,c.subMenu?void(c.subMenu.length>0&&d.cQa(a)):(d.h4(b),!0)},a.prototype.h4=function(a){var b,c,d,e=this,f=e.ZPa,g=e.zma(f.menuItemData);i(g)||(b=e.getCommandOptions(f.menuItemData,f.host[0],a),c=e.wu,j(g)?g(e.xc,b):(d=e.xc.getActiveSheet(),c.execute({cmd:g,sheetName:d.name(),selections:d.getSelections(),activeRow:d.getActiveRowIndex(),activeCol:d.getActiveColumnIndex(),commandOptions:b})))},a.prototype.Tla=function(){f(p+N).unbind(l).unbind(m).unbind(n),f(document).unbind(ba)},a.prototype.no=function(){var a=this;a.qo=null,a.lla=null,a.wu=null,a.xc=null,a.Eu=null,a.LQ=null,a.YPa=null,a.ZPa=null,a.Tla()},a.prototype.Ooa=function(){var a,b,c,d=this;if(d.rQa.length>1)return a=d.rQa.pop(),b=a.menuView,c=b.find(p+N),c.removeClass(M+Z),a.menuView.hide(),a.name},a.prototype._Pa=function(){var a,b,c,d=this,e=d.dQa(!0);if(e){for(a=0,b=e.length;a<b;a++)e[a].host.removeClass(M+Z);d.ZPa&&(c=d.ZPa.menuItemData,c.disable?d.ZPa.host.addClass(q+r+u+Z):d.ZPa.host.addClass(M+Z))}},a.prototype.WT=function(){var a=this;a.Eu.trigger(o),a.no()},a.prototype.bQa=function(a,b){var c,d,e,f=this,g=f.dQa(b);f.ZPa&&(c=f.eQa(f.ZPa,g),c||(d=f.YPa[f.ZPa.menuItemData.group],d&&(e=d[0],c=f.eQa(e,g)))),a?(c!==ca&&c!==g.length-1||(c=-1),c="number"==typeof c?c+1:0):(c!==ca&&0!==c||(c=g.length),c="number"==typeof c?c-1:0),f.ZPa=g[c],f._Pa()},a.prototype.dQa=function(a){for(var b,c,d,e=this,f=e.rQa[e.rQa.length-1].name,g=e.YPa[f],h=0,i=g.length,j=[];h<i;h++)if("groupHeader"!==g[h].menuItemData.type)j.push(g[h]);else if(b=e.YPa[g[h].menuItemData.name],a)for(c=0,d=b.length;c<d;c++)j.push(b[c]);else j.push(b[0]);return j},a.prototype.eQa=function(a,b){var c,d=b.length;for(c=0;c<d;c++)if(a.name===b[c].name)return c},a.prototype.cQa=function(a){var b,c,d=this,e=d.ZPa;e&&(b=e.menuItemData,b.subMenu&&b.subMenu.length>0&&(b.name!==d.rQa[d.rQa.length-1].name&&(c=f(e.host.find(p+S)[0]),c.show(),d.Noa(c),d.rQa.push({name:b.name,menuView:c})),d.ZPa=null,a&&d.bQa(!0,!1)))},a}(),b.MenuView=ha},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets}});