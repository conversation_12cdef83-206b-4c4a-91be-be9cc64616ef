/*
 * @Description: 路由文件
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2020-08-05 14:02:37
 * @LastEditors: y<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-11-16 13:58:36
 * @FilePath: /vue-com/src/router/index.js
 */
import Vue from "vue";
import VueRouter from "vue-router";
import layout from "@/layout";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "layout",
    component: layout,
    redirect: "/indexPage",
    children: [
      {
        path: "indexPage",
        name: "indexPage",
        component: () =>
          import(
            /* webpackChunkName: "TestEntry" */ "../views/TestEntry/index.vue"
          )
      }
    ]
  }
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});

export default router;
