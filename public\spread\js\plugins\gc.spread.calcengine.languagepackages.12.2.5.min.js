/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
!function(a){"object"==typeof module&&"object"==typeof module.exports?module.exports=a(require("@grapecity/spread-sheets")):"function"==typeof define&&define.amd?define(["@grapecity/spread-sheets"],a):"object"==typeof exports?exports.Spread=a(require("@grapecity/spread-sheets")):a(GC)}(function(a){a="object"==typeof a?a:{},a.Spread=a.Spread||{},a.Spread.CalcEngine=a.Spread.CalcEngine||{},a.Spread.CalcEngine.LanguagePackages=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./node_modules_local/@grapecity/js-calc-languagepackages/index.js")}({"./node_modules_local/@grapecity/js-calc-languagepackages/dist/gc.spread.calcengine.languagepackages.js":function(a,b,c){var d="object"==typeof d?d:{};d.Spread=d.Spread||{},d.Spread.CalcEngine=d.Spread.CalcEngine||{},d.Spread.CalcEngine.LanguagePackages=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/languagePackages/languagePackages.entry.ts")}({"./src/languagePackages/commonFunction.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=e.Errors.Value,g=d.Common.j,h=g.Fa,i=d.Common.q,j=e.Functions.UAb,k=e.Functions.VAb,l=e.Functions.WAb;function m(a,b,c){var d=-1,e,g;return e=i.ub(a),e?(g=i.sb(e).exec(b),d=h(g)?-1:g.index):d=b.toLowerCase().indexOf(a.toLowerCase(),--c),d===-1?f:d+1}b.tx_search=m;function n(a){for(var b=0;b<a.length;b++)h(a[b])&&a[b-1]&&2!==a[b-1].length&&(a[b]="\u200b");return a.join("")}b.replaceBlankCharter=n;function o(a,b,c){var d,e,f,g,i,k,m=[],n=0,o=0;for(e=0;e<b.length;e++)if(d=b[e],f=j(b[e]),"fourByte"===f){if(n%2===0){n++;continue}n++,d=b[e-1]+b[e],m[m.length]=d}else"doubleByte"===f&&(o++,d=b[e]),g=n>0?n/2:0,m[e-g+o]=d;return a||(m=l(m)),i=m.length,c>=i?b:(a?(k=m.slice(0,c-1<0?0:c),k.length>0&&h(k[k.length-1])&&(k[k.length-1]="\u200b")):(k=m.slice(i-c),k.length>0&&h(k[0])&&(k[0]="\u200b")),k.join(""))}b.tx_leftB_rightB=o;function p(a,b,c,d){var e,f,g,i,k,l,m=[],n=0;for(f=0;f<a.length;f++)e=a[f],g=j(a[f]),"doubleByte"===g&&(n++,e=a[f]),m[f+n]=e;return i=m.length,b=Math.min(b,i+1),c=Math.min(c||0,i-b+1),h(m[b-1])?(m[b-1]=" ",m[b]=" "):h(m[b-2])&&b>=2&&(m[b-2]=" ",m[b-1]=" "),k=m.slice(0,b-1).join(""),l=m.slice(b-1+c).join(""),k.concat(d).concat(l)}b.tx_replaceB=p;function q(a,b){return o(!0,a,b)}b.tx_leftB=q;function r(a,b){return o(!1,a,b)}b.tx_rightB=r;function s(a,b,c){var d,e=k(a),g=e.length;return b--,b<0?f:b>=g?"":g<b+c?e.slice(b).join(""):(d=e.slice(b,b+c),"singleByte"!==j(d[d.length-1])&&(d[d.length-1]="\u200b",h(d[0])&&(d[d.length]="\u200b")),d.join(""))}b.tx_midB=s;function t(a){var b,c,e,f;if(!a)return 0;for(b=0,c=0;c<a.length;c++)if(8203!==a[c].charCodeAt(0)&&(e=j(a[c]),f=d.Common.CultureManager.q4(),"doubleByte"===e&&9390!==a[c].charCodeAt(0))){if("ja-jp"===(f&&f.name().toLocaleLowerCase())&&(711===a[c].charCodeAt(0)||9356===a[c].charCodeAt(0)||8212===a[c].charCodeAt(0)))continue;b++}return a.length+b}b.tx_lenB=t;function u(a,b,c){var d,e,g,h=k(b),i=k(a),j=h.length;return c<1||j<c?f:(d=n(h),e=n(i),g=d.indexOf(e,c-1),g===-1?f:g+1)}b.tx_findB=u;function v(a,b,c){var d,e=k(b),g=k(a),h=e.length;return c<1||h<c?f:(d=m(n(g),n(e),c),d===f?f:d)}b.tx_searchB=v},"./src/languagePackages/languagePackages.entry.ts":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./src/languagePackages/languagePackages.ts"))},"./src/languagePackages/languagePackages.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=c("./src/languagePackages/res.Japanese.ts"),g=c("./src/languagePackages/res.Korean.ts"),h=c("./src/languagePackages/res.Chinese.ts"),i=c("./src/languagePackages/res.French.ts"),j=c("./src/languagePackages/res.Dutch.ts"),k=c("./src/languagePackages/res.German.ts"),l=c("./src/languagePackages/res.Spanish.ts"),m=c("./src/languagePackages/res.Portuguese.ts"),n=c("./src/languagePackages/res.Russian.ts"),o=c("./src/languagePackages/res.Swedish.ts"),p=c("./src/languagePackages/res.Italian.ts"),q=c("./src/languagePackages/res.Danish.ts"),r=c("./src/languagePackages/res.Polish.ts"),s=c("./src/languagePackages/res.Finnish.ts"),t=c("./src/languagePackages/res.Norwegian.ts"),u=c("./src/languagePackages/res.Czech.ts"),v=c("./src/languagePackages/res.Hungarian.ts"),w=c("./src/languagePackages/res.Turkish.ts"),x={zh:h.resource,ja:f.resource,ko:g.resource,fr:i.resource,nl:j.resource,de:k.resource,es:l.resource,pt:m.resource,ru:n.resource,sv:o.resource,it:p.resource,da:q.resource,pl:r.resource,fi:s.resource,nb:t.resource,cs:u.resource,hu:v.resource,tr:w.resource},y="TRUE",z="FALSE";function A(a){var b,c,f;return 0===arguments.length?e.Functions.languageName:(b=x[a],"en"===a?(e.Functions.languageName="en",e.Functions.resourceMapping=void 0,void(d.Common.CultureManager.booleanMapping=void 0)):void(b&&(e.Functions.languageName=a,e.Functions.resourceMapping=b,c=b.booleanMapping,f={boolean_true:c&&c.boolean_true||y,boolean_false:c&&c.boolean_false||z},d.Common.CultureManager.booleanMapping=f)))}b.languagePackages=A,e.Functions.setLanguagepackage=A},"./src/languagePackages/res.Chinese.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./src/languagePackages/commonFunction.ts");b.resource={builtInFunctionsMapping:{FINDB:{specialFun:d.tx_findB},LEFTB:{specialFun:d.tx_leftB},RIGHTB:{specialFun:d.tx_rightB},MIDB:{specialFun:d.tx_midB},LENB:{specialFun:d.tx_lenB},REPLACEB:{specialFun:d.tx_replaceB},SEARCHB:{specialFun:d.tx_searchB}},tableFunctionsMapping:{"#All":"#\u5168\u90e8","#Data":"#\u6570\u636e","#Headers":"#\u6807\u9898","#Totals":"#\u6c47\u603b","#This row":"#\u6b64\u884c"}}},"./src/languagePackages/res.Czech.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACOS:{alias:"ARCCOS"},ACOSH:{alias:"ARCCOSH"},ADDRESS:{alias:"ODKAZ"},AND:{alias:"A"},AREAS:{alias:"PO\u010cET.BLOK\u016e"},ASIN:{alias:"ARCSIN"},ASINH:{alias:"ARCSINH"},ATAN:{alias:"ARCTG"},ATAN2:{alias:"ARCTG2"},ATANH:{alias:"ARCTGH"},AVEDEV:{alias:"PR\u016eMODCHYLKA"},AVERAGE:{alias:"PR\u016eM\u011aR"},CALL:{alias:"VOLAT"},CEILING:{alias:"ZAOKR.NAHORU"},CELL:{alias:"BU\u0147KA"},CHAR:{alias:"ZNAK"},CHOOSE:{alias:"ZVOLIT"},CLEAN:{alias:"VY\u010cISTIT"},CODE:{alias:"K\xd3D"},COLUMN:{alias:"SLOUPEC"},COLUMNS:{alias:"SLOUPCE"},COMBIN:{alias:"KOMBINACE"},COMBINA:{alias:"KOMBINACE.A"},COUNT:{alias:"PO\u010cET"},COUNTA:{alias:"PO\u010cET2"},DATE:{alias:"DATUM"},DATEVALUE:{alias:"DATUMHODN"},DAVERAGE:{alias:"DPR\u016eM\u011aR"},DAY:{alias:"DEN"},DAYS:{alias:"DNY"},DAYS360:{alias:"ROK360"},DB:{alias:"ODPIS.ZRYCH"},DCOUNT:{alias:"DPO\u010cET"},DCOUNTA:{alias:"DPO\u010cET2"},DDB:{alias:"ODPIS.ZRYCH2"},DGET:{alias:"DZ\xcdSKAT"},DOLLAR:{alias:"K\u010c"},DPRODUCT:{alias:"DSOU\u010cIN"},DSTDEV:{alias:"DSMDOCH.V\xddB\u011aR"},DSTDEVP:{alias:"DSMODCH"},DSUM:{alias:"DSUMA"},DVAR:{alias:"DVAR.V\xddB\u011aR"},DVARP:{alias:"DVAR"},"ERROR.TYPE":{alias:"CHYBA.TYP"},EVEN:{alias:"ZAOKROUHLIT.NA.SUD\xc9"},EXACT:{alias:"STEJN\xc9"},FACT:{alias:"FAKTORI\xc1L"},FALSE:{alias:"NEPRAVDA"},FIND:{alias:"NAJ\xcdT"},FIXED:{alias:"ZAOKROUHLIT.NA.TEXT"},FLOOR:{alias:"ZAOKR.DOL\u016e"},FREQUENCY:{alias:"\u010cETNOSTI"},FV:{alias:"BUDHODNOTA"},GETPIVOTDATA:{alias:"Z\xcdSKATKONTDATA"},GROWTH:{alias:"LOGLINTREND"},HLOOKUP:{alias:"VVYHLEDAT"},HOUR:{alias:"HODINA"},HYPERLINK:{alias:"HYPERTEXTOV\xdd.ODKAZ"},IF:{alias:"KDY\u017d"},INDIRECT:{alias:"NEP\u0158\xcdM\xdd.ODKAZ"},INFO:{alias:"O.PROST\u0158ED\xcd"},INT:{alias:"CEL\xc1.\u010c\xc1ST"},IPMT:{alias:"PLATBA.\xdaROK"},IRR:{alias:"M\xcdRA.V\xddNOSNOSTI"},ISBLANK:{alias:"JE.PR\xc1ZDN\xc9"},ISERR:{alias:"JE.CHYBA"},ISERROR:{alias:"JE.CHYBHODN"},ISEVEN:{alias:"JE.SUD\xc9"},ISLOGICAL:{alias:"JE.LOGHODN"},ISNA:{alias:"JE.NEDEF"},ISNONTEXT:{alias:"JE.NETEXT"},ISNUMBER:{alias:"JE.\u010cISLO"},ISODD:{alias:"JE.LICH\xc9"},ISREF:{alias:"JE.ODKAZ"},ISTEXT:{alias:"JE.TEXT"},LEFT:{alias:"VLEVO"},LEFTB:{alias:"LEFTB"},LEN:{alias:"D\xc9LKA"},LENB:{alias:"LENB"},LINEST:{alias:"LINREGRESE"},LOG:{alias:"LOGZ"},LOG10:{alias:"LOG"},LOGEST:{alias:"LOGLINREGRESE"},LOOKUP:{alias:"VYHLEDAT"},LOWER:{alias:"MAL\xc1"},MATCH:{alias:"POZVYHLEDAT"},MDETERM:{alias:"DETERMINANT"},MID:{alias:"\u010c\xc1ST"},MINUTE:{alias:"MINUTA"},MINVERSE:{alias:"INVERZE"},MIRR:{alias:"MOD.M\xcdRA.V\xddNOSNOSTI"},MMULT:{alias:"SOU\u010cIN.MATIC"},MONTH:{alias:"M\u011aS\xcdC"},NOT:{alias:"NE"},NOW:{alias:"NYN\xcd"},NPER:{alias:"PO\u010cET.OBDOB\xcd"},NPV:{alias:"\u010cIST\xc1.SOU\u010cHODNOTA"},ODD:{alias:"ZAOKROUHLIT.NA.LICH\xc9"},OFFSET:{alias:"POSUN"},OR:{alias:"NEBO"},PERCENTILE:{alias:"PERCENTIL"},"PERCENTILE.EXC":{alias:"PERCENTIL.EXC"},"PERCENTILE.INC":{alias:"PERCENTIL.INC"},PERMUT:{alias:"PERMUTACE"},PERMUTATIONA:{alias:"PERMUTACE.A"},PHONETIC:{alias:"ZVUKOV\xc9"},PMT:{alias:"PLATBA"},PPMT:{alias:"PLATBA.Z\xc1KLAD"},PRODUCT:{alias:"SOU\u010cIN"},PROPER:{alias:"VELK\xc12"},PV:{alias:"SOU\u010cHODNOTA"},QUARTILE:{alias:"QUARTIL"},"QUARTILE.EXC":{alias:"QUARTIL.EXC"},"QUARTILE.INC":{alias:"QUARTIL.INC"},RAND:{alias:"N\xc1H\u010c\xcdSLO"},RATE:{alias:"\xdaROKOV\xc1.M\xcdRA"},"REGISTER.ID":{alias:"ID.REGISTRU"},REPLACE:{alias:"NAHRADIT"},REPLACEB:{alias:"NAHRADITB"},REPT:{alias:"OPAKOVAT"},RIGHT:{alias:"VPRAVO"},RIGHTB:{alias:"RIGHTB"},ROUND:{alias:"ZAOKROUHLIT"},ROW:{alias:"\u0158\xc1DEK"},ROWS:{alias:"\u0158\xc1DKY"},RSQ:{alias:"RKQ"},SEARCH:{alias:"HLEDAT"},SECOND:{alias:"SEKUNDA"},SLN:{alias:"ODPIS.LIN"},SQRT:{alias:"ODMOCNINA"},STDEV:{alias:"SMODCH.V\xddB\u011aR"},"STDEV.P":{alias:"SMODCH.P"},"STDEV.S":{alias:"SMODCH.V\xddB\u011aR.S"},STDEVP:{alias:"SMODCH"},SUBSTITUTE:{alias:"DOSADIT"},SUM:{alias:"SUMA"},SUMPRODUCT:{alias:"SOU\u010cIN.SKAL\xc1RN\xcd"},SUMSQ:{alias:"SUMA.\u010cTVERC\u016e"},SYD:{alias:"ODPIS.NELIN"},TAN:{alias:"TG"},TANH:{alias:"TGH"},TIME:{alias:"\u010cAS"},TIMEVALUE:{alias:"\u010cASHODN"},TODAY:{alias:"DNES"},TRANSPOSE:{alias:"TRANSPOZICE"},TREND:{alias:"LINTREND"},TRIM:{alias:"PRO\u010cISTIT"},TRUE:{alias:"PRAVDA"},TRUNC:{alias:"USEKNOUT"},TYPE:{alias:"TYP"},UPPER:{alias:"VELK\xc1"},VALUE:{alias:"HODNOTA"},VAR:{alias:"VAR.V\xddB\u011aR"},VARP:{alias:"VAR"},VDB:{alias:"ODPIS.ZA.INT"},VLOOKUP:{alias:"SVYHLEDAT"},WEBSERVICE:{alias:"WEBOV\xc1SLU\u017dBA"},WEEKDAY:{alias:"DENT\xddDNE"},YEAR:{alias:"ROK"}},tableFunctionsMapping:{"#All":"#V\u0161e","#Data":"#Data","#Headers":"#Z\xe1hlav\xed","#Totals":"#Sou\u010dty","#This row":"#Tento \u0159\xe1dek"},clacErrorMapping:{"#NULL!":"#NULL!","#DIV/0!":"#D\u011aLEN\xcd_NULOU!","#VALUE!":"#HODNOTA!","#REF!":"#ODKAZ!","#NAME?":"#N\xc1ZEV?","#N/A!":"#NEN\xcd_K_DISPOZICI","#NUM!":"#\u010c\xcdSLO!"},booleanMapping:{boolean_true:"PRAVDA",boolean_false:"NEPRAVDA"}}},"./src/languagePackages/res.Danish.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"P\xc5L\xd8BRENTE"},ACCRINTM:{alias:"P\xc5L\xd8BRENTE.UDL\xd8B"},ACOS:{alias:"ARCCOS"},ACOSH:{alias:"ARCCOSH"},ACOT:{alias:"ARCCOT"},ACOTH:{alias:"ARCCOTH"},ADDRESS:{alias:"ADRESSE"},AGGREGATE:{alias:"SAMLING"},AND:{alias:"OG"},ARABIC:{alias:"ARABISK"},AREAS:{alias:"OMR\xc5DER"},ASIN:{alias:"ARCSIN"},ASINH:{alias:"ARCSINH"},ATAN:{alias:"ARCTAN"},ATAN2:{alias:"ARCTAN2"},ATANH:{alias:"ARCTANH"},AVEDEV:{alias:"MAD"},AVERAGE:{alias:"MIDDEL"},AVERAGEA:{alias:"MIDDELV"},AVERAGEIF:{alias:"MIDDEL.HVIS"},AVERAGEIFS:{alias:"MIDDEL.HVISER"},BAHTTEXT:{alias:"BAHTTEKST"},BASE:{alias:"BASIS"},"BETA.DIST":{alias:"BETA.FORDELING"},BETADIST:{alias:"BETAFORDELING"},BIN2DEC:{alias:"BIN.TIL.DEC"},BIN2HEX:{alias:"BIN.TIL.HEX"},BIN2OCT:{alias:"BIN.TIL.OKT"},"BINOM.DIST":{alias:"BINOMIAL.FORDELING"},"BINOM.DIST.RANGE":{alias:"BINOMIAL.DIST.INTERVAL"},"BINOM.INV":{alias:"BINOMIAL.INV"},BINOMDIST:{alias:"BINOMIALFORDELING"},BITAND:{alias:"BITOG"},BITLSHIFT:{alias:"BITLSKIFT"},BITOR:{alias:"BITELLER"},BITRSHIFT:{alias:"BITRSKIFT"},BITXOR:{alias:"BITXELLER"},CALL:{alias:"KALD"},CEILING:{alias:"AFRUND.LOFT"},"CEILING.MATH":{alias:"LOFT.MAT"},"CEILING.PRECISE":{alias:"LOFT.PRECISE"},CELL:{alias:"CELLE"},CHIDIST:{alias:"CHIFORDELING"},"CHISQ.DIST":{alias:"CHI2.FORDELING"},"CHISQ.DIST.RT":{alias:"CHI2.FORD.RT"},"CHISQ.INV":{alias:"CHI2.INV"},"CHISQ.INV.RT":{alias:"CHI2.INV.RT"},"CHISQ.TEST":{alias:"CHI2.TEST"},CHOOSE:{alias:"V\xc6LG"},CLEAN:{alias:"RENS"},CODE:{alias:"KODE"},COLUMN:{alias:"KOLONNE"},COLUMNS:{alias:"KOLONNER"},COMBIN:{alias:"KOMBIN"},COMBINA:{alias:"KOMBINA"},COMPLEX:{alias:"KOMPLEKS"},CONCATENATE:{alias:"SAMMENK\xc6DNING"},CONFIDENCE:{alias:"KONFIDENSINTERVAL"},"CONFIDENCE.NORM":{alias:"KONFIDENS.NORM"},"CONFIDENCE.T":{alias:"KONFIDENST"},CONVERT:{alias:"KONVERTER"},CORREL:{alias:"KORRELATION"},COUNT:{alias:"T\xc6L"},COUNTA:{alias:"T\xc6LV"},COUNTBLANK:{alias:"ANTAL.BLANKE"},COUNTIF:{alias:"T\xc6L.HVIS"},COUNTIFS:{alias:"T\xc6L.HVISER"},COUPDAYBS:{alias:"KUPONDAGE.SA"},COUPDAYS:{alias:"KUPONDAGE.A"},COUPDAYSNC:{alias:"KUPONDAGE.ANK"},COUPNCD:{alias:"KUPONDAG.N\xc6STE"},COUPNUM:{alias:"KUPONBETALINGER"},COUPPCD:{alias:"KUPONDAG.FORRIGE"},COVAR:{alias:"KOVARIANS"},"COVARIANCE.P":{alias:"KOVARIANS.P"},"COVARIANCE.S":{alias:"KOVARIANS.S"},CRITBINOM:{alias:"KRITBINOM"},CUBEKPIMEMBER:{alias:"KUBE.KPI.MEDLEM"},CUBEMEMBER:{alias:"KUBEMEDLEM"},CUBEMEMBERPROPERTY:{alias:"KUBEMEDLEM.EGENSKAB"},CUBERANKEDMEMBER:{alias:"KUBERANGERET.MEDLEM"},CUBESET:{alias:"KUBES\xc6T"},CUBESETCOUNT:{alias:"KUBES\xc6T.ANTAL"},CUBEVALUE:{alias:"KUBEV\xc6RDI"},CUMIPMT:{alias:"AKKUM.RENTE"},CUMPRINC:{alias:"AKKUM.HOVEDSTOL"},DATE:{alias:"DATO"},DATEVALUE:{alias:"DATOV\xc6RDI"},DAVERAGE:{alias:"DMIDDEL"},DAY:{alias:"DAG"},DAYS:{alias:"DAGE"},DAYS360:{alias:"DAGE360"},DCOUNT:{alias:"DT\xc6L"},DCOUNTA:{alias:"DT\xc6LV"},DDB:{alias:"DSA"},DEC2BIN:{alias:"DEC.TIL.BIN"},DEC2HEX:{alias:"DEC.TIL.HEX"},DEC2OCT:{alias:"DEC.TIL.OKT"},DEGREES:{alias:"GRADER"},DEVSQ:{alias:"SAK"},DGET:{alias:"DHENT"},DISC:{alias:"DISKONTO"},DMAX:{alias:"DMAKS"},DOLLAR:{alias:"KR"},DOLLARDE:{alias:"KR.DECIMAL"},DOLLARFR:{alias:"KR.BR\xd8K"},DPRODUCT:{alias:"DPRODUKT"},DSTDEV:{alias:"DSTDAFV"},DSTDEVP:{alias:"DSTDAFVP"},DURATION:{alias:"VARIGHED"},DVAR:{alias:"DVARIANS"},DVARP:{alias:"DVARIANSP"},EDATE:{alias:"EDATO"},EFFECT:{alias:"EFFEKTIV.RENTE"},ENCODEURL:{alias:"KODNINGSURL"},EOMONTH:{alias:"SLUT.P\xc5.M\xc5NED"},ERF:{alias:"FEJLFUNK"},ERFC:{alias:"FEJLFUNK.KOMP"},"ERROR.TYPE":{alias:"FEJLTYPE"},EVEN:{alias:"LIGE"},EXACT:{alias:"EKSAKT"},EXP:{alias:"EKSP"},"EXPON.DIST":{alias:"EKSP.FORDELING"},EXPONDIST:{alias:"EKSPFORDELING"},"F.DIST":{alias:"F.FORDELING"},"F.DIST.RT":{alias:"F.FORDELING.RT"},FACT:{alias:"FAKULTET"},FACTDOUBLE:{alias:"DOBBELT.FAKULTET"},FALSE:{alias:"FALSK"},FDIST:{alias:"FFORDELING"},FILTERXML:{alias:"FILTRERXML"},FIXED:{alias:"FAST"},FLOOR:{alias:"AFRUND.GULV"},"FLOOR.MATH":{alias:"AFRUND.BUND.MAT"},"FLOOR.PRECISE":{alias:"AFRUND.GULV.PRECISE"},FORECAST:{alias:"PROGNOSE"},FORMULATEXT:{alias:"FORMELTEKST"},FREQUENCY:{alias:"FREKVENS"},FVSCHEDULE:{alias:"FVTABEL"},"GAMMA.DIST":{alias:"GAMMA.FORDELING"},GAMMADIST:{alias:"GAMMAFORDELING"},GCD:{alias:"ST\xd8RSTE.F\xc6LLES.DIVISOR"},GEOMEAN:{alias:"GEOMIDDELV\xc6RDI"},GESTEP:{alias:"GETRIN"},GROWTH:{alias:"FOR\xd8GELSE"},HARMEAN:{alias:"HARMIDDELV\xc6RDI"},HEX2BIN:{alias:"HEX.TIL.BIN"},HEX2DEC:{alias:"HEX.TIL.DEC"},HEX2OCT:{alias:"HEX.TIL.OKT"},HLOOKUP:{alias:"VOPSLAG"},HOUR:{alias:"TIME"},"HYPGEOM.DIST":{alias:"HYPGEO.FORDELING"},HYPGEOMDIST:{alias:"HYPGEOFORDELING"},IF:{alias:"HVIS"},IFERROR:{alias:"HVIS.FEJL"},IFNA:{alias:"HVISIT"},IMABS:{alias:"IMAGABS"},IMAGINARY:{alias:"IMAGIN\xc6R"},IMARGUMENT:{alias:"IMAGARGUMENT"},IMCONJUGATE:{alias:"IMAGKONJUGERE"},IMCOS:{alias:"IMAGCOS"},IMCOSH:{alias:"IMAGCOSH"},IMCOT:{alias:"IMAGCOT"},IMCSC:{alias:"IMAGCSC"},IMCSCH:{alias:"IMAGCSCH"},IMDIV:{alias:"IMAGDIV"},IMEXP:{alias:"IMAGEKSP"},IMLN:{alias:"IMAGLN"},IMLOG10:{alias:"IMAGLOG10"},IMLOG2:{alias:"IMAGLOG2"},IMPOWER:{alias:"IMAGPOTENS"},IMPRODUCT:{alias:"IMAGPRODUKT"},IMREAL:{alias:"IMAGREELT"},IMSEC:{alias:"IMAGSEC"},IMSECH:{alias:"IMAGSECH"},IMSIN:{alias:"IMAGSIN"},IMSINH:{alias:"IMAGSINH"},IMSQRT:{alias:"IMAGKVROD"},IMSUB:{alias:"IMAGSUB"},IMSUM:{alias:"IMAGSUM"},IMTAN:{alias:"IMAGTAN"},INDEX:{alias:"INDEKS"},INDIRECT:{alias:"INDIREKTE"},INT:{alias:"HELTAL"},INTERCEPT:{alias:"SK\xc6RING"},INTRATE:{alias:"RENTEFOD"},IPMT:{alias:"R.YDELSE"},IRR:{alias:"IA"},ISBLANK:{alias:"ER.TOM"},ISERR:{alias:"ER.FE"},ISERROR:{alias:"ER.FEJL"},ISEVEN:{alias:"ER.LIGE"},ISFORMULA:{alias:"ER.FORMEL"},ISLOGICAL:{alias:"ER.LOGISK"},ISNA:{alias:"ER.IKKE.TILG\xc6NGELIG"},ISNONTEXT:{alias:"ER.IKKE.TEKST"},ISNUMBER:{alias:"ER.TAL"},"ISO.CEILING":{alias:"ISO.LOFT"},ISODD:{alias:"ER.ULIGE"},ISOWEEKNUM:{alias:"ISOUGE.NR"},ISREF:{alias:"ER.REFERENCE"},ISTEXT:{alias:"ER.TEKST"},KURT:{alias:"TOPSTEJL"},LARGE:{alias:"ST\xd8RSTE"},LCM:{alias:"MINDSTE.F\xc6LLES.MULTIPLUM"},LEFT:{alias:"VENSTRE"},LEFTB:{alias:"VENSTREB"},LEN:{alias:"L\xc6NGDE"},LENB:{alias:"L\xc6NGDEB"},LINEST:{alias:"LINREGR"},LOGEST:{alias:"LOGREGR"},"LOGNORM.DIST":{alias:"LOGNORM.FORDELING"},LOGNORMDIST:{alias:"LOGNORMFORDELING"},LOOKUP:{alias:"SL\xc5.OP"},LOWER:{alias:"/"},MATCH:{alias:"SAMMENLIGN"},MAX:{alias:"MAKS"},MAXA:{alias:"MAKSV"},MDURATION:{alias:"MVARIGHED"},MID:{alias:"MIDT"},MIDB:{alias:"MIDTB"},MINA:{alias:"MINV"},MINUTE:{alias:"MINUT"},MINVERSE:{alias:"MINVERT"},MIRR:{alias:"MIA"},MMULT:{alias:"MPRODUKT"},MOD:{alias:"REST"},MODE:{alias:"HYPPIGST"},"MODE.MULT":{alias:"HYPPIGST.FLERE"},"MODE.SNGL":{alias:"HYPPIGST.ENKELT"},MONTH:{alias:"M\xc5NED"},MROUND:{alias:"MAFRUND"},MUNIT:{alias:"MENHED"},N:{alias:"TAL"},NA:{alias:"IKKE.TILG\xc6NGELIG"},"NEGBINOM.DIST":{alias:"NEGBINOM.FORDELING"},NEGBINOMDIST:{alias:"NEGBINOMFORDELING"},NETWORKDAYS:{alias:"ANTAL.ARBEJDSDAGE"},"NETWORKDAYS.INTL":{alias:"ANTAL.ARBEJDSDAGE.INTL"},NOMINAL:{alias:"NOMINEL"},"NORM.DIST":{alias:"NORMAL.FORDELING"},"NORM.S.DIST":{alias:"STANDARD.NORM.FORDELING"},"NORM.S.INV":{alias:"STANDARD.NORM.INV"},NORMDIST:{alias:"NORMFORDELING"},NORMSDIST:{alias:"STANDARDNORMFORDELING"},NORMSINV:{alias:"STANDARDNORMINV"},NOT:{alias:"IKKE"},NOW:{alias:"NU"},NPV:{alias:"NUTIDSV\xc6RDI"},NUMBERVALUE:{alias:"TALV\xc6RDI"},OCT2BIN:{alias:"OKT.TIL.BIN"},OCT2DEC:{alias:"OKT.TIL.DEC"},OCT2HEX:{alias:"OKT.TIL.HEX"},ODD:{alias:"ULIGE"},ODDFPRICE:{alias:"ULIGE.KURS.P\xc5LYDENDE"},ODDFYIELD:{alias:"ULIGE.F\xd8RSTE.AFKAST"},ODDLPRICE:{alias:"ULIGE.SIDSTE.KURS"},ODDLYIELD:{alias:"ULIGE.SIDSTE.AFKAST"},OFFSET:{alias:"FORSKYDNING"},OR:{alias:"ELLER"},PDURATION:{alias:"PVARIGHED"},PERCENTILE:{alias:"FRAKTIL"},"PERCENTILE.EXC":{alias:"FRAKTIL.UDELAD"},"PERCENTILE.INC":{alias:"FRAKTIL.MEDTAG"},PERCENTRANK:{alias:"PROCENTPLADS"},"PERCENTRANK.EXC":{alias:"PROCENTPLADS.UDELAD"},"PERCENTRANK.INC":{alias:"PROCENTPLADS.MEDTAG"},PHONETIC:{alias:"FONETISK"},PMT:{alias:"YDELSE"},"POISSON.DIST":{alias:"POISSON.FORDELING"},POWER:{alias:"POTENS"},PPMT:{alias:"H.YDELSE"},PRICE:{alias:"KURS"},PRICEDISC:{alias:"KURS.DISKONTO"},PRICEMAT:{alias:"KURS.UDL\xd8B"},PROB:{alias:"SANDSYNLIGHED"},PRODUCT:{alias:"PRODUKT"},PROPER:{alias:"STORT.FORBOGSTAV"},PV:{alias:"NV"},QUARTILE:{alias:"KVARTIL"},"QUARTILE.EXC":{alias:"KVARTIL.UDELAD"},"QUARTILE.INC":{alias:"KVARTIL.MEDTAG"},QUOTIENT:{alias:"KVOTIENT"},RADIANS:{alias:"RADIANER"},RAND:{alias:"SLUMP"},RANDBETWEEN:{alias:"SLUMPMELLEM"},RANK:{alias:"PLADS"},"RANK.AVG":{alias:"PLADS.GNSN"},"RANK.EQ":{alias:"PLADS.LIGE"},RATE:{alias:"RENTE"},RECEIVED:{alias:"MODTAGET.VED.UDL\xd8B"},REPLACE:{alias:"ERSTAT"},REPLACEB:{alias:"ERSTATB"},REPT:{alias:"GENTAG"},RIGHT:{alias:"H\xd8JRE"},RIGHTB:{alias:"H\xd8JREB"},ROMAN:{alias:"ROMERTAL"},ROUND:{alias:"AFRUND"},ROUNDDOWN:{alias:"RUND.NED"},ROUNDUP:{alias:"RUND.OP"},ROW:{alias:"R\xc6KKE"},ROWS:{alias:"R\xc6KKER"},RSQ:{alias:"FORKLARINGSGRAD"},SEARCH:{alias:"S\xd8G"},SEARCHB:{alias:"S\xd8GB"},SECOND:{alias:"sekund"},SERIESSUM:{alias:"SERIESUM"},SHEET:{alias:"ARK"},SHEETS:{alias:"ARK.FLERE"},SIGN:{alias:"FORTEGN"},SKEW:{alias:"SK\xc6VHED"},"SKEW.P":{alias:"SK\xc6VHED.P"},SLN:{alias:"LA"},SLOPE:{alias:"STIGNING"},SMALL:{alias:"MINDSTE"},SQRT:{alias:"KVROD"},SQRTPI:{alias:"KVRODPI"},STANDARDIZE:{alias:"STANDARDISER"},STDEV:{alias:"STDAFV"},"STDEV.P":{alias:"STDAFV.P"},"STDEV.S":{alias:"STDAFV.S"},STDEVA:{alias:"STDAFVV"},STDEVP:{alias:"STDAFVP"},STDEVPA:{alias:"STDAFVPV"},STEYX:{alias:"STFYX"},SUBSTITUTE:{alias:"UDSKIFT"},SUMIF:{alias:"SUM.HVIS"},SUMIFS:{alias:"SUM.HVISER"},SUMPRODUCT:{alias:"SUMPRODUKT"},SUMSQ:{alias:"SUMKV"},SYD:{alias:"\xc5RSAFSKRIVNING"},"T.DIST":{alias:"T.FORDELING"},"T.DIST.2T":{alias:"T.FORDELING.2T"},"T.DIST.RT":{alias:"T.FORDELING.RT"},TBILLEQ:{alias:"STATSOBLIGATION"},TBILLPRICE:{alias:"STATSOBLIGATION.KURS"},TBILLYIELD:{alias:"STATSOBLIGATION.AFKAST"},TDIST:{alias:"Oversigt"},TEXT:{alias:"TEKST"},TIME:{alias:"TID"},TIMEVALUE:{alias:"TIDSV\xc6RDI"},TODAY:{alias:"IDAG"},TRANSPOSE:{alias:"TRANSPONER"},TREND:{alias:"TENDENS"},TRIM:{alias:"FJERN.OVERFL\xd8DIGE.BLANKE"},TRIMMEAN:{alias:"TRIMMIDDELV\xc6RDI"},TRUE:{alias:"SAND"},TRUNC:{alias:"AFKORT"},TYPE:{alias:"V\xc6RDITYPE"},UPPER:{alias:"STORE.BOGSTAVER"},VALUE:{alias:"V\xc6RDI"},VAR:{alias:"VARIANS"},"VAR.P":{alias:"VARIANS.P"},"VAR.S":{alias:"VARIANS.S"},VARA:{alias:"VARIANSV"},VARP:{alias:"VARIANSP"},VARPA:{alias:"VARIANSPV"},VDB:{alias:"VSA"},VLOOKUP:{alias:"LOPSLAG"},WEBSERVICE:{alias:"WEBTJENESTE"},WEEKDAY:{alias:"UGEDAG"},WEEKNUM:{alias:"UGE.NR"},"WEIBULL.DIST":{alias:"WEIBULL.FORDELING"},WORKDAY:{alias:"ARBEJDSDAG"},"WORKDAY.INTL":{alias:"ARBEJDSDAG.INTL"},XIRR:{alias:"INTERN.RENTE"},XNPV:{alias:"NETTO.NUTIDSV\xc6RDI"},XOR:{alias:"Enten-eller"},YEAR:{alias:"\xc5R"},YEARFRAC:{alias:"\xc5R.BR\xd8K"},YIELD:{alias:"AFKAST"},YIELDDISC:{alias:"AFKAST.DISKONTO"},YIELDMAT:{alias:"AFKAST.UDL\xd8BSDATO"}},tableFunctionsMapping:{"#All":"#Alle","#Data":"#Data","#Headers":"#Overskrifter","#Totals":"#Totaler","#This row":"#Denne r\xe6kke"},clacErrorMapping:{"#NULL!":"#NUL!","#DIV/0!":"#DIVISION/0!","#VALUE!":"#V\xc6RDI!","#REF!":"#REFERENCE!","#NAME?":"#NAVN?","#N/A!":"#I/T","#NUM!":"#NUM!"},booleanMapping:{boolean_true:"SAND",boolean_false:"FALSK"}}},"./src/languagePackages/res.Dutch.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"SAMENG.RENTE"},ACCRINTM:{alias:"SAMENG.RENTE.V"},ACOS:{alias:"BOOGCOS"},ACOSH:{alias:"BOOGCOSH"},ACOT:{alias:"BOOGCOT"},ACOTH:{alias:"BOOGCOTH"},ADDRESS:{alias:"ADRES"},AGGREGATE:{alias:"AGGREGAAT"},AND:{alias:"EN"},ARABIC:{alias:"ARABISCH"},AREAS:{alias:"BEREIKEN"},ASIN:{alias:"BOOGSIN"},ASINH:{alias:"BOOGSINH"},ATAN:{alias:"BOOGTAN"},ATAN2:{alias:"BOOGTAN2"},ATANH:{alias:"BOOGTANH"},AVEDEV:{alias:"GEM.DEVIATIE"},AVERAGE:{alias:"GEMIDDELDE"},AVERAGEA:{alias:"GEMIDDELDEA"},AVERAGEIF:{alias:"GEMIDDELDE.ALS"},AVERAGEIFS:{alias:"GEMIDDELDEN.ALS"},BAHTTEXT:{alias:"BAHT.TEKST"},BASE:{alias:"BASIS"},BESSELI:{alias:"BESSEL.I"},BESSELJ:{alias:"BESSEL.J"},BESSELK:{alias:"BESSEL.K"},BESSELY:{alias:"BESSEL.Y"},"BETA.DIST":{alias:"BETA.VERD"},BETADIST:{alias:"BETA.VERD"},BETAINV:{alias:"BETA.INV"},BIN2DEC:{alias:"BIN.N.DEC"},BIN2HEX:{alias:"BIN.N.HEX"},BIN2OCT:{alias:"BIN.N.OCT"},"BINOM.DIST":{alias:"BINOM.VERD"},"BINOM.DIST.RANGE":{alias:"BINOM.VERD.BEREIK"},"BINOM.INV":{alias:"BINOMIALE.INV"},BINOMDIST:{alias:"BINOMIALE.VERD"},BITAND:{alias:"BIT.EN"},BITLSHIFT:{alias:"BIT.VERSCHUIF.LINKS"},BITOR:{alias:"BIT.OF"},BITRSHIFT:{alias:"BIT.VERSCHUIF.RECHTS"},BITXOR:{alias:"BIT.EX.OF"},CALL:{alias:"ROEPEN"},CEILING:{alias:"AFRONDEN.BOVEN"},"CEILING.MATH":{alias:"AFRONDEN.BOVEN.WISK"},"CEILING.PRECISE":{alias:"AFRONDEN.BOVEN.NAUWKEURIG"},CELL:{alias:"CEL"},CHAR:{alias:"TEKEN"},CHIDIST:{alias:"CHI.KWADRAAT"},CHIINV:{alias:"CHI.KWADRAAT.INV"},"CHISQ.DIST":{alias:"CHIKW.VERD"},"CHISQ.DIST.RT":{alias:"CHIKW.VERD.RECHTS"},"CHISQ.INV":{alias:"CHIKW.INV"},"CHISQ.INV.RT":{alias:"CHIKW.INV.RECHTS"},"CHISQ.TEST":{alias:"CHIKW.TEST"},CHITEST:{alias:"CHI.TOETS"},CHOOSE:{alias:"KIEZEN"},CLEAN:{alias:"WISSEN.CONTROL"},COLUMN:{alias:"KOLOM"},COLUMNS:{alias:"KOLOMMEN"},COMBIN:{alias:"COMBINATIES"},COMBINA:{alias:"COMBIN.A"},CONCATENATE:{alias:"TEKST.SAMENVOEGEN"},CONFIDENCE:{alias:"BETROUWBAARHEID"},"CONFIDENCE.NORM":{alias:"BETROUWBAARHEID.NORM"},"CONFIDENCE.T":{alias:"BETROUWBAARHEID.T"},CONVERT:{alias:"CONVERTEREN"},CORREL:{alias:"CORRELATIE"},COUNT:{alias:"AANTAL"},COUNTA:{alias:"AANTALARG"},COUNTBLANK:{alias:"AANTAL.LEGE.CELLEN"},COUNTIF:{alias:"AANTAL.ALS"},COUNTIFS:{alias:"AANTALLEN.ALS"},COUPDAYBS:{alias:"COUP.DAGEN.BB"},COUPDAYS:{alias:"COUP.DAGEN"},COUPDAYSNC:{alias:"COUP.DAGEN.VV"},COUPNCD:{alias:"COUP.DATUM.NB"},COUPNUM:{alias:"COUP.AANTAL"},COUPPCD:{alias:"COUP.DATUM.VB"},COVAR:{alias:"COVARIANTIE"},"COVARIANCE.P":{alias:"COVARIANTIE.P"},"COVARIANCE.S":{alias:"COVARIANTIE.S"},CRITBINOM:{alias:"CRIT.BINOM"},CSC:{alias:"COSEC"},CSCH:{alias:"COSECH"},CUBEKPIMEMBER:{alias:"KUBUSKPILID"},CUBEMEMBER:{alias:"KUBUSLID"},CUBEMEMBERPROPERTY:{alias:"KUBUSLIDEIGENSCHAP"},CUBERANKEDMEMBER:{alias:"KUBUSGERANGSCHIKTLID"},CUBESET:{alias:"KUBUSSET"},CUBESETCOUNT:{alias:"KUBUSSETAANTAL"},CUBEVALUE:{alias:"KUBUSWAARDE"},CUMIPMT:{alias:"CUM.RENTE"},CUMPRINC:{alias:"CUM.HOOFDSOM"},DATE:{alias:"DATUM"},DATEVALUE:{alias:"DATUMWAARDE"},DAVERAGE:{alias:"DBGEMIDDELDE"},DAY:{alias:"DAG"},DAYS:{alias:"DAGEN"},DAYS360:{alias:"DAGEN360"},DCOUNT:{alias:"DBAANTAL"},DCOUNTA:{alias:"DBAANTALALC"},DEC2BIN:{alias:"DEC.N.BIN"},DEC2HEX:{alias:"DEC.N.HEX"},DEC2OCT:{alias:"DEC.N.OCT"},DECIMAL:{alias:"DECIMAAL"},DEGREES:{alias:"GRADEN"},DEVSQ:{alias:"DEV.KWAD"},DGET:{alias:"DBLEZEN"},DISC:{alias:"DISCONTO"},DMAX:{alias:"DBMAX"},DMIN:{alias:"DBMIN"},DOLLAR:{alias:"EURO"},DOLLARDE:{alias:"EURO.DE"},DOLLARFR:{alias:"EURO.BR"},DPRODUCT:{alias:"DBPRODUCT"},DSTDEV:{alias:"DBSTDEV"},DSTDEVP:{alias:"DBSTDEVP"},DSUM:{alias:"DBSOM"},DURATION:{alias:"DUUR"},DVAR:{alias:"DBVAR"},DVARP:{alias:"DBVARP"},EDATE:{alias:"ZELFDE.DAG"},EFFECT:{alias:"EFFECT.RENTE"},ENCODEURL:{alias:"URL.CODEREN"},EOMONTH:{alias:"LAATSTE.DAG"},ERF:{alias:"FOUTFUNCTIE"},"ERF.PRECISE":{alias:"FOUTFUNCTIE.NAUWKEURIG"},ERFC:{alias:"FOUT.COMPLEMENT"},"ERFC.PRECISE":{alias:"FOUT.COMPLEMENT.NAUWKEURIG"},"ERROR.TYPE":{alias:"TYPE.FOUT"},EXACT:{alias:"GELIJK"},"EXPON.DIST":{alias:"T.VERD"},EXPONDIST:{alias:"EXPON.VERD"},"F.DIST":{alias:"F.VERD"},"F.DIST.RT":{alias:"F.VERD.RECHTS"},"F.INV.RT":{alias:"F.INV.RECHTS"},"F.TEST":{alias:"F.TOETS"},FACT:{alias:"FACULTEIT"},FACTDOUBLE:{alias:"DUBBELE.FACULTEIT"},FALSE:{alias:"ONWAAR"},FDIST:{alias:"F.VERDELING"},FILTERXML:{alias:"XML.FILTEREN"},FIND:{alias:"VIND"},FINDB:{alias:"VIND.ALLES.B"},FINV:{alias:"F.INVERSE"},FISHERINV:{alias:"FISHER.INV"},FIXED:{alias:"VAST"},FLOOR:{alias:"AFRONDEN.BENEDEN"},"FLOOR.MATH":{alias:"AFRONDEN.BENEDEN.WISK"},"FLOOR.PRECISE":{alias:"AFRONDEN.BENEDEN.NAUWKEURIG"},FORECAST:{alias:"VOORSPELLEN"},FORMULATEXT:{alias:"FORMULETEKST"},FREQUENCY:{alias:"INTERVAL"},FTEST:{alias:"F.TOETS"},FV:{alias:"TW"},FVSCHEDULE:{alias:"TOEK.WAARDE2"},"GAMMA.DIST":{alias:"GAMMA.VERD"},GAMMADIST:{alias:"GAMMA.VERD"},GAMMAINV:{alias:"GAMMA.INV"},GAMMALN:{alias:"GAMMA.LN"},"GAMMALN.PRECISE":{alias:"GAMMA.LN.NAUWKEURIG"},GCD:{alias:"GGD"},GEOMEAN:{alias:"MEETK.GEM"},GESTEP:{alias:"GROTER.DAN"},GETPIVOTDATA:{alias:"DRAAITABELOPHALEN"},GROWTH:{alias:"GROEI"},HARMEAN:{alias:"HARM.GEM"},HEX2BIN:{alias:"HEX.N.BIN"},HEX2DEC:{alias:"HEX.N.DEC"},HEX2OCT:{alias:"HEX.N.OCT"},HLOOKUP:{alias:"HORIZ.ZOEKEN"},HOUR:{alias:"UUR"},"HYPGEOM.DIST":{alias:"HYPGEOM.VERD"},HYPGEOMDIST:{alias:"HYPERGEO.VERD"},IF:{alias:"ALS"},IFERROR:{alias:"ALS.FOUT"},IFNA:{alias:"ALS.NB"},IMABS:{alias:"C.ABS"},IMAGINARY:{alias:"C.IM.DEEL"},IMARGUMENT:{alias:"C.ARGUMENT"},IMCONJUGATE:{alias:"C.TOEGEVOEGD"},IMCOS:{alias:"C.COS"},IMCOSH:{alias:"C.COSH"},IMCOT:{alias:"C.COT"},IMCSC:{alias:"C.COSEC"},IMCSCH:{alias:"C.COSECH"},IMDIV:{alias:"C.QUOTIENT"},IMEXP:{alias:"C.EXP"},IMLN:{alias:"C.LN"},IMLOG10:{alias:"C.LOG10"},IMLOG2:{alias:"C.LOG2"},IMPOWER:{alias:"C.MACHT"},IMPRODUCT:{alias:"C.PRODUCT"},IMREAL:{alias:"C.REEEL.DEEL"},IMSEC:{alias:"C.SEC"},IMSECH:{alias:"C.SECH"},IMSIN:{alias:"C.SIN"},IMSINH:{alias:"C.SINH"},IMSQRT:{alias:"C.WORTEL"},IMSUB:{alias:"C.VERSCHIL"},IMSUM:{alias:"C.SOM"},IMTAN:{alias:"C.TAN"},INT:{alias:"INTEGER"},INTERCEPT:{alias:"SNIJPUNT"},INTRATE:{alias:"RENTEPERCENTAGE"},IPMT:{alias:"IBET"},IRR:{alias:"IR"},ISBLANK:{alias:"ISLEEG"},ISERR:{alias:"ISFOUT2"},ISERROR:{alias:"ISFOUT"},ISEVEN:{alias:"IS.EVEN"},ISFORMULA:{alias:"ISFORMULE"},ISLOGICAL:{alias:"ISLOGISCH"},ISNA:{alias:"ISNB"},ISNONTEXT:{alias:"ISGEENTEKST"},ISNUMBER:{alias:"ISGETAL"},"ISO.CEILING":{alias:"ISO.AFRONDEN.BOVEN"},ISODD:{alias:"IS.ONEVEN"},ISOWEEKNUM:{alias:"ISO.WEEKNUMMER"},ISPMT:{alias:"ISBET"},ISREF:{alias:"ISVERWIJZING"},ISTEXT:{alias:"ISTEKST"},KURT:{alias:"KURTOSIS"},LARGE:{alias:"GROOTSTE"},LCM:{alias:"KGV"},LEFT:{alias:"LINKS"},LEFTB:{alias:"LINKSB"},LEN:{alias:"LENGTE"},LENB:{alias:"LENGTEB"},LINEST:{alias:"LIJNSCH"},LOGEST:{alias:"LOGSCH"},LOGINV:{alias:"LOG.NORM.INV"},"LOGNORM.DIST":{alias:"LOGNORM.VERD"},LOGNORMDIST:{alias:"LOG.NORM.VERD"},LOOKUP:{alias:"ZOEKEN"},LOWER:{alias:"KLEINE.LETTERS"},MATCH:{alias:"VERGELIJKEN"},MDETERM:{alias:"DETERMINANTMAT"},MDURATION:{alias:"AANG.DUUR"},MEDIAN:{alias:"MEDIAAN"},MID:{alias:"DEEL"},MIDB:{alias:"DEELB"},MINUTE:{alias:"MINUUT"},MINVERSE:{alias:"INVERSEMAT"},MIRR:{alias:"GIR"},MMULT:{alias:"PRODUCTMAT"},MOD:{alias:"REST"},MODE:{alias:"MODUS"},"MODE.MULT":{alias:"MODUS.MEERV"},"MODE.SNGL":{alias:"MODUS.ENKELV"},MONTH:{alias:"MAAND"},MROUND:{alias:"AFRONDEN.N.VEELVOUD"},MULTINOMIAL:{alias:"MULTINOMIAAL"},MUNIT:{alias:"EENHEIDMAT"},NA:{alias:"NB"},"NEGBINOM.DIST":{alias:"NEGBINOM.VERD"},NEGBINOMDIST:{alias:"NEG.BINOM.VERD"},NETWORKDAYS:{alias:"NETTO.WERKDAGEN"},"NETWORKDAYS.INTL":{alias:"NETWERKDAGEN.INTL"},NOMINAL:{alias:"NOMINALE.RENTE"},"NORM.DIST":{alias:"NORM.VERD"},"NORM.S.DIST":{alias:"NORM.S.VERD"},NORMDIST:{alias:"NORM.VERD"},NORMINV:{alias:"NORM.INV"},NORMSDIST:{alias:"STAND.NORM.VERD"},NORMSINV:{alias:"STAND.NORM.INV"},NOT:{alias:"NIET"},NOW:{alias:"NU"},NPV:{alias:"NHW"},NUMBERVALUE:{alias:"NUMERIEKE.WAARDE"},OCT2BIN:{alias:"OCT.N.BIN"},OCT2DEC:{alias:"OCT.N.DEC"},OCT2HEX:{alias:"OCT.N.HEX"},ODD:{alias:"ONEVEN"},ODDFPRICE:{alias:"AFW.ET.PRIJS"},ODDFYIELD:{
alias:"AFW.ET.REND"},ODDLPRICE:{alias:"AFW.LT.PRIJS"},ODDLYIELD:{alias:"AFW.LT.REND"},OFFSET:{alias:"VERSCHUIVING"},OR:{alias:"OF"},PDURATION:{alias:"PDUUR"},PERCENTILE:{alias:"PERCENTIEL"},"PERCENTILE.EXC":{alias:"PERCENTIEL.EXC"},"PERCENTILE.INC":{alias:"PERCENTIEL.INC"},PERCENTRANK:{alias:"PERCENT.RANG"},"PERCENTRANK.EXC":{alias:"PROCENTRANG.EXC"},"PERCENTRANK.INC":{alias:"PROCENTRANG.INC"},PERMUT:{alias:"PERMUTATIES"},PERMUTATIONA:{alias:"PERMUTATIE.A"},PHONETIC:{alias:"FONETISCH"},PMT:{alias:"AFLOSSING"},"POISSON.DIST":{alias:"POISSON.VERD"},POWER:{alias:"MACHT"},PPMT:{alias:"PBET"},PRICE:{alias:"PRIJS.NOM"},PRICEDISC:{alias:"PRIJS.DISCONTO"},PRICEMAT:{alias:"PRIJS.VERVALDAG"},PROB:{alias:"KANS"},PROPER:{alias:"BEGINLETTERS"},PV:{alias:"HW"},QUARTILE:{alias:"KWARTIEL"},"QUARTILE.EXC":{alias:"KWARTIEL.EXC"},"QUARTILE.INC":{alias:"KWARTIEL.INC"},RADIANS:{alias:"RADIALEN"},RAND:{alias:"ASELECT"},RANDBETWEEN:{alias:"ASELECTTUSSEN"},RANK:{alias:"RANG"},"RANK.AVG":{alias:"RANG.GEMIDDELDE"},"RANK.EQ":{alias:"RANG.GELIJK"},RATE:{alias:"RENTE"},RECEIVED:{alias:"OPBRENGST"},"REGISTER.ID":{alias:"REGISTRATIE.ID"},REPLACE:{alias:"VERVANGEN"},REPLACEB:{alias:"VERVANGENB"},REPT:{alias:"HERHALING"},RIGHT:{alias:"RECHTS"},RIGHTB:{alias:"RECHTSB"},ROMAN:{alias:"ROMEINS"},ROUND:{alias:"AFRONDEN"},ROUNDDOWN:{alias:"AFRONDEN.NAAR.BENEDEN"},ROUNDUP:{alias:"AFRONDEN.NAAR.BOVEN"},ROW:{alias:"RIJ"},ROWS:{alias:"RIJEN"},RSQ:{alias:"R.KWADRAAT"},RTD:{alias:"RTG"},SEARCH:{alias:"VIND.SPEC"},SEARCHB:{alias:"VIND.SPEC.B"},SECOND:{alias:"SECONDE"},SERIESSUM:{alias:"SOM.MACHTREEKS"},SHEET:{alias:"BLAD"},SHEETS:{alias:"BLADEN"},SIGN:{alias:"POS.NEG"},SKEW:{alias:"SCHEEFHEID"},"SKEW.P":{alias:"SCHEEFHEID.P"},SLN:{alias:"LIN.AFSCHR"},SLOPE:{alias:"RICHTING"},SMALL:{alias:"KLEINSTE"},SQRT:{alias:"WORTEL"},SQRTPI:{alias:"WORTEL.PI"},STANDARDIZE:{alias:"NORMALISEREN"},STEYX:{alias:"STAND.FOUT.YX"},SUBSTITUTE:{alias:"SUBSTITUEREN"},SUBTOTAL:{alias:"SUBTOTAAL"},SUM:{alias:"SOM"},SUMIF:{alias:"SOM.ALS"},SUMIFS:{alias:"SOMMEN.ALS"},SUMPRODUCT:{alias:"SOMPRODUCT"},SUMSQ:{alias:"KWADRATENSOM"},SUMX2MY2:{alias:"SOM.X2MINY2"},SUMX2PY2:{alias:"SOM.X2PLUSY2"},SUMXMY2:{alias:"SOM.XMINY.2"},"T.DIST":{alias:"T.VERD"},"T.DIST.2T":{alias:"T.VERD.2T"},"T.DIST.RT":{alias:"T.VERD.RECHTS"},TBILLEQ:{alias:"SCHATK.OBL"},TBILLPRICE:{alias:"SCHATK.PRIJS"},TBILLYIELD:{alias:"SCHATK.REND"},TDIST:{alias:"T.VERD"},TEXT:{alias:"TEKST"},TIME:{alias:"TIJD"},TIMEVALUE:{alias:"TIJDWAARDE"},TINV:{alias:"T.INV"},TODAY:{alias:"VANDAAG"},TRANSPOSE:{alias:"TRANSPONEREN"},TRIM:{alias:"SPATIES.WISSEN"},TRIMMEAN:{alias:"GETRIMD.GEM"},TRUE:{alias:"WAAR"},TRUNC:{alias:"GEHEEL"},TTEST:{alias:"T.TOETS"},UNICHAR:{alias:"UNITEKEN"},UPPER:{alias:"HOOFDLETTERS"},VALUE:{alias:"WAARDE"},VLOOKUP:{alias:"VERT.ZOEKEN"},WEEKDAY:{alias:"WEEKDAG"},WEEKNUM:{alias:"WEEKNUMMER"},"WEIBULL.DIST":{alias:"WEIBULL.VERD"},WORKDAY:{alias:"WERKDAG"},"WORKDAY.INTL":{alias:"WERKDAG.INTL"},XIRR:{alias:"IR.SCHEMA"},XNPV:{alias:"NHW2"},XOR:{alias:"EX.OF"},YEAR:{alias:"JAAR"},YEARFRAC:{alias:"JAAR.DEEL"},YIELD:{alias:"RENDEMENT"},YIELDDISC:{alias:"REND.DISCONTO"},YIELDMAT:{alias:"REND.VERVAL"},ZTEST:{alias:"Z.TOETS"}},tableFunctionsMapping:{"#All":"#Alles","#Data":"#Gegevens","#Headers":"#Kopteksten","#Totals":"#Totalen","#This row":"#Denne r\xe6kke"},clacErrorMapping:{"#NULL!":"#NUL!","#DIV/0!":"#DIVISION/0!","#VALUE!":"#V\xc6RDI!","#REF!":"#REFERENCE!","#NAME?":"#NAVN?","#N/A!":"#I/T","#NUM!":"#NUM!"},booleanMapping:{boolean_true:"WAAR",boolean_false:"ONWAAR"}}},"./src/languagePackages/res.Finnish.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ABS:{alias:"ITSEISARVO"},ACCRINT:{alias:"KERTYNYT.KORKO"},ACCRINTM:{alias:"KERTYNYT.KORKO.LOPUSSA"},ADDRESS:{alias:"OSOITE"},AGGREGATE:{alias:"KOOSTE"},AND:{alias:"JA"},ARABIC:{alias:"ARABIA"},AREAS:{alias:"ALUEET"},AVEDEV:{alias:"KESKIPOIKKEAMA"},AVERAGE:{alias:"KESKIARVO"},AVERAGEA:{alias:"KESKIARVOA"},AVERAGEIF:{alias:"KESKIARVO.JOS"},AVERAGEIFS:{alias:"KESKIARVO.JOS.JOUKKO"},BAHTTEXT:{alias:"BAHTTEKSTI"},BASE:{alias:"PERUS"},"BETA.DIST":{alias:"BEETA.JAKAUMA"},"BETA.INV":{alias:"BEETA.K\xc4\xc4NT"},BETADIST:{alias:"BEETAJAKAUMA"},BETAINV:{alias:"BEETAJAKAUMA.K\xc4\xc4NT"},BIN2DEC:{alias:"BINDES"},BIN2HEX:{alias:"BINHEKSA"},BIN2OCT:{alias:"BINOKT"},"BINOM.DIST":{alias:"BINOMI.JAKAUMA"},"BINOM.DIST.RANGE":{alias:"BINOMI.JAKAUMA.ALUE"},"BINOM.INV":{alias:"BINOMIJAKAUMA.K\xc4\xc4NT"},BINOMDIST:{alias:"BINOMIJAKAUMA"},BITAND:{alias:"BITTI.JA"},BITLSHIFT:{alias:"BITTI.SIIRTO.V"},BITOR:{alias:"BITTI.TAI"},BITRSHIFT:{alias:"BITTI.SIIRTO.O"},BITXOR:{alias:"BITTI.EHDOTON.TAI"},CALL:{alias:"KUTSU"},CEILING:{alias:"PY\xd6RIST\xc4.KERR.YL\xd6S"},"CEILING.MATH":{alias:"PY\xd6RIST\xc4.KERR.YL\xd6S.MATEMAATTINEN"},"CEILING.PRECISE":{alias:"PY\xd6RIST\xc4.KERR.YL\xd6S.TARKKA"},CELL:{alias:"SOLU"},CHAR:{alias:"MERKKI"},CHIDIST:{alias:"CHIJAKAUMA"},CHIINV:{alias:"CHIJAKAUMA.K\xc4\xc4NT"},"CHISQ.DIST":{alias:"CHINELI\xd6.JAKAUMA"},"CHISQ.DIST.RT":{alias:"CHINELI\xd6.JAKAUMA.OH"},"CHISQ.INV":{alias:"CHINELI\xd6.K\xc4\xc4NT"},"CHISQ.INV.RT":{alias:"CHINELI\xd6.K\xc4\xc4NT.OH"},"CHISQ.TEST":{alias:"CHINELI\xd6.TESTI"},CHITEST:{alias:"CHITESTI"},CHOOSE:{alias:"VALITSE.INDEKSI"},CLEAN:{alias:"SIIVOA"},CODE:{alias:"KOODI"},COLUMN:{alias:"SARAKE"},COLUMNS:{alias:"SARAKKEET"},COMBIN:{alias:"KOMBINAATIO"},COMBINA:{alias:"KOMBINAATIOA"},COMPLEX:{alias:"KOMPLEKSI"},CONCATENATE:{alias:"KETJUTA"},CONFIDENCE:{alias:"LUOTTAMUSV\xc4LI"},"CONFIDENCE.NORM":{alias:"LUOTTAMUSV\xc4LI.NORM"},"CONFIDENCE.T":{alias:"LUOTTAMUSV\xc4LI.T"},CONVERT:{alias:"MUUNNA"},CORREL:{alias:"KORRELAATIO"},COUNT:{alias:"LASKE"},COUNTA:{alias:"LASKE.A"},COUNTBLANK:{alias:"LASKE.TYHJ\xc4T"},COUNTIF:{alias:"LASKE.JOS"},COUNTIFS:{alias:"LASKE.JOS.JOUKKO"},COUPDAYBS:{alias:"KORKOP\xc4IV\xc4T.ALUSTA"},COUPDAYS:{alias:"KORKOP\xc4IV\xc4T"},COUPDAYSNC:{alias:"KORKOP\xc4IV\xc4T.SEURAAVA"},COUPNCD:{alias:"KORKOP\xc4IV\xc4.SEURAAVA"},COUPNUM:{alias:"KORKOP\xc4IV\xc4.JAKSOT"},COUPPCD:{alias:"KORKOP\xc4IV\xc4.EDELLINEN"},COVAR:{alias:"KOVARIANSSI"},"COVARIANCE.P":{alias:"KOVARIANSSI.P"},"COVARIANCE.S":{alias:"KOVARIANSSI.S"},CRITBINOM:{alias:"BINOMIJAKAUMA.KRIT"},CSC:{alias:"KOSEK"},CSCH:{alias:"KOSEKH"},CUBEKPIMEMBER:{alias:"KUUTIOKPIJ\xc4SEN"},CUBEMEMBER:{alias:"KUUTIONJ\xc4SEN"},CUBEMEMBERPROPERTY:{alias:"KUUTIONJ\xc4SENENOMINAISUUS"},CUBERANKEDMEMBER:{alias:"KUUTIONLUOKITELTUJ\xc4SEN"},CUBESET:{alias:"KUUTIOJOUKKO"},CUBESETCOUNT:{alias:"KUUTIOJOUKKOJENM\xc4\xc4R\xc4"},CUBEVALUE:{alias:"KUUTIONARVO"},CUMIPMT:{alias:"MAKSETTU.KORKO"},CUMPRINC:{alias:"MAKSETTU.LYHENNYS"},DATE:{alias:"P\xc4IV\xc4YS"},DATEVALUE:{alias:"P\xc4IV\xc4YSARVO"},DAVERAGE:{alias:"TKESKIARVO"},DAY:{alias:"P\xc4IV\xc4"},DAYS:{alias:"P\xc4IV\xc4T"},DAYS360:{alias:"P\xc4IV\xc4T360"},DCOUNT:{alias:"TLASKE"},DCOUNTA:{alias:"TLASKEA"},DEC2BIN:{alias:"DESBIN"},DEC2HEX:{alias:"DESHEKSA"},DEC2OCT:{alias:"DESOKT"},DECIMAL:{alias:"DESIMAALI"},DEGREES:{alias:"ASTEET"},DELTA:{alias:"SAMA.ARVO"},DEVSQ:{alias:"OIKAISTU.NELI\xd6SUMMA"},DGET:{alias:"TNOUDA"},DISC:{alias:"DISKONTTOKORKO"},DMAX:{alias:"TMAKS"},DMIN:{alias:"TMIN"},DOLLAR:{alias:"VALUUTTA"},DOLLARDE:{alias:"VALUUTTA.DES"},DOLLARFR:{alias:"VALUUTTA.MURTO"},DPRODUCT:{alias:"TTULO"},DSTDEV:{alias:"TKESKIHAJONTA"},DSTDEVP:{alias:"TKESKIHAJONTAP"},DSUM:{alias:"TSUMMA"},DURATION:{alias:"KESTO"},DVAR:{alias:"TVARIANSSI"},DVARP:{alias:"TVARIANSSIP"},EDATE:{alias:"P\xc4IV\xc4.KUUKAUSI"},EFFECT:{alias:"KORKO.EFEKT"},ENCODEURL:{alias:"URLKOODAUS"},EOMONTH:{alias:"KUUKAUSI.LOPPU"},ERF:{alias:"VIRHEFUNKTIO"},"ERF.PRECISE":{alias:"VIRHEFUNKTIO.TARKKA"},ERFC:{alias:"VIRHEFUNKTIO.KOMPLEMENTTI"},"ERFC.PRECISE":{alias:"VIRHEFUNKTIO.KOMPLEMENTTI.TARKKA"},"ERROR.TYPE":{alias:"VIRHEEN.LAJI"},EVEN:{alias:"PARILLINEN"},EXACT:{alias:"VERTAA"},EXP:{alias:"EKSPONENTTI"},"EXPON.DIST":{alias:"EKSPONENTIAALI.JAKAUMA"},EXPONDIST:{alias:"EKSPONENTIAALIJAKAUMA"},"F.DIST":{alias:"F.JAKAUMA"},"F.DIST.RT":{alias:"F.JAKAUMA.OH"},"F.INV":{alias:"F.K\xc4\xc4NT"},"F.INV.RT":{alias:"F.K\xc4\xc4NT.OH"},"F.TEST":{alias:"F.TESTI"},FACT:{alias:"KERTOMA"},FACTDOUBLE:{alias:"KERTOMA.OSA"},FALSE:{alias:"EP\xc4TOSI"},FDIST:{alias:"FJAKAUMA"},FILTERXML:{alias:"SUODATA.XML"},FIND:{alias:"ETSI"},FINDB:{alias:"ETSIB"},FINV:{alias:"FJAKAUMA.K\xc4\xc4NT"},FISHERINV:{alias:"FISHER.K\xc4\xc4NT"},FIXED:{alias:"KIINTE\xc4"},FLOOR:{alias:"PY\xd6RIST\xc4.KERR.ALAS"},"FLOOR.MATH":{alias:"PY\xd6RIST\xc4.KERR.ALAS.MATEMAATTINEN"},"FLOOR.PRECISE":{alias:"PY\xd6RIST\xc4.KERR.ALAS.TARKKA"},FORECAST:{alias:"ENNUSTE"},FORMULATEXT:{alias:"KAAVA.TEKSTI"},FREQUENCY:{alias:"TAAJUUS"},FTEST:{alias:"FTESTI"},FV:{alias:"TULEVA.ARVO"},FVSCHEDULE:{alias:"TULEVA.ARVO.ERIKORKO"},"GAMMA.DIST":{alias:"GAMMA.JAKAUMA"},"GAMMA.INV":{alias:"GAMMA.JAKAUMA.K\xc4\xc4NT"},GAMMADIST:{alias:"GAMMAJAKAUMA"},GAMMAINV:{alias:"GAMMAJAKAUMA.K\xc4\xc4NT"},"GAMMALN.PRECISE":{alias:"GAMMALN.TARKKA"},GCD:{alias:"SUURIN.YHT.TEKIJ\xc4"},GEOMEAN:{alias:"KESKIARVO.GEOM"},GESTEP:{alias:"RAJA"},GETPIVOTDATA:{alias:"NOUDA.PIVOT.TIEDOT"},GROWTH:{alias:"KASVU"},HARMEAN:{alias:"KESKIARVO.HARM"},HEX2BIN:{alias:"HEKSABIN"},HEX2DEC:{alias:"HEKSADES"},HEX2OCT:{alias:"HEKSAOKT"},HLOOKUP:{alias:"VHAKU"},HOUR:{alias:"TUNNIT"},HYPERLINK:{alias:"HYPERLINKKI"},"HYPGEOM.DIST":{alias:"HYPERGEOM_JAKAUMA"},HYPGEOMDIST:{alias:"HYPERGEOM.JAKAUMA"},IF:{alias:"JOS"},IFERROR:{alias:"JOSVIRHE"},IFNA:{alias:"JOSPUUTTUU"},IMABS:{alias:"KOMPLEKSI.ABS"},IMAGINARY:{alias:"KOMPLEKSI.IMAG"},IMARGUMENT:{alias:"KOMPLEKSI.ARG"},IMCONJUGATE:{alias:"KOMPLEKSI.KONJ"},IMCOS:{alias:"KOMPLEKSI.COS"},IMCOSH:{alias:"KOMPLEKSI.COSH"},IMCOT:{alias:"KOMPLEKSI.COT"},IMCSC:{alias:"KOMPLEKSI.KOSEK"},IMCSCH:{alias:"KOMPLEKSI.KOSEKH"},IMDIV:{alias:"KOMPLEKSI.OSAM"},IMEXP:{alias:"KOMPLEKSI.EKSP"},IMLN:{alias:"KOMPLEKSI.LN"},IMLOG10:{alias:"KOMPLEKSI.LOG10"},IMLOG2:{alias:"KOMPLEKSI.LOG2"},IMPOWER:{alias:"KOMPLEKSI.POT"},IMPRODUCT:{alias:"KOMPLEKSI.TULO"},IMREAL:{alias:"KOMPLEKSI.REAALI"},IMSEC:{alias:"KOMPLEKSI.SEK"},IMSECH:{alias:"KOMPLEKSI.SEKH"},IMSIN:{alias:"KOMPLEKSI.SIN"},IMSINH:{alias:"KOMPLEKSI.SINH"},IMSQRT:{alias:"KOMPLEKSI.NELI\xd6J"},IMSUB:{alias:"KOMPLEKSI.EROTUS"},IMSUM:{alias:"KOMPLEKSI.SUM"},IMTAN:{alias:"KOMPLEKSI.TAN"},INDEX:{alias:"INDEKSI"},INDIRECT:{alias:"EP\xc4SUORA"},INFO:{alias:"KUVAUS"},INT:{alias:"KOKONAISLUKU"},INTERCEPT:{alias:"LEIKKAUSPISTE"},INTRATE:{alias:"KORKO.ARVOPAPERI"},IRR:{alias:"SIS\xc4INEN.KORKO"},ISBLANK:{alias:"ONTYHJ\xc4"},ISERR:{alias:"ONVIRH"},ISERROR:{alias:"ONVIRHE"},ISEVEN:{alias:"ONPARILLINEN"},ISFORMULA:{alias:"ONKAAVA"},ISLOGICAL:{alias:"ONTOTUUS"},ISNA:{alias:"ONPUUTTUU"},ISNONTEXT:{alias:"ONEI_TEKSTI"},ISNUMBER:{alias:"ONLUKU"},"ISO.CEILING":{alias:"ISO.PY\xd6RIST\xc4.KERR.YL\xd6S"},ISODD:{alias:"ONPARITON"},ISOWEEKNUM:{alias:"VIIKKO.ISO.NRO"},ISPMT:{alias:"ONMAKSU"},ISREF:{alias:"ONVIITT"},ISTEXT:{alias:"ONTEKSTI"},LARGE:{alias:"SUURI"},LCM:{alias:"PIENIN.YHT.JAETTAVA"},LEFT:{alias:"VASEN"},LEFTB:{alias:"VASENB"},LEN:{alias:"PITUUS"},LENB:{alias:"PITUUSB"},LINEST:{alias:"LINREGR"},LN:{alias:"LUONNLOG"},LOGEST:{alias:"LOGREGR"},LOGINV:{alias:"LOGNORM.JAKAUMA.K\xc4\xc4NT"},"LOGNORM.DIST":{alias:"LOGNORM_JAKAUMA"},"LOGNORM.INV":{alias:"LOGNORM.K\xc4\xc4NT"},LOGNORMDIST:{alias:"LOGNORM.JAKAUMA"},LOOKUP:{alias:"HAKU"},LOWER:{alias:"PIENET"},MATCH:{alias:"VASTINE"},MAX:{alias:"MAKS"},MAXA:{alias:"MAKSA"},MDURATION:{alias:"KESTO.MUUNN"},MEDIAN:{alias:"MEDIAANI"},MID:{alias:"POIMI.TEKSTI"},MIDB:{alias:"POIMI.TEKSTIB"},MINUTE:{alias:"MINUUTIT"},MINVERSE:{alias:"MK\xc4\xc4NTEINEN"},MIRR:{alias:"MSIS\xc4INEN"},MMULT:{alias:"MKERRO"},MOD:{alias:"JAKOJ"},MODE:{alias:"MOODI"},"MODE.MULT":{alias:"MOODI.USEA"},"MODE.SNGL":{alias:"MOODI.YKSI"},MONTH:{alias:"KUUKAUSI"},MROUND:{alias:"PY\xd6RIST\xc4.KERR"},MULTINOMIAL:{alias:"MULTINOMI"},MUNIT:{alias:"YKSIKK\xd6M"},NA:{alias:"PUUTTUU"},"NEGBINOM.DIST":{alias:"BINOMI.JAKAUMA.NEG"},NEGBINOMDIST:{alias:"BINOMIJAKAUMA.NEG"},NETWORKDAYS:{alias:"TY\xd6P\xc4IV\xc4T"},"NETWORKDAYS.INTL":{alias:"TY\xd6P\xc4IV\xc4T.KANSV\xc4L"},NOMINAL:{alias:"KORKO.VUOSI"},"NORM.DIST":{alias:"NORMAALI.JAKAUMA"},"NORM.INV":{alias:"NORMAALI.JAKAUMA.K\xc4\xc4NT"},"NORM.S.DIST":{alias:"NORM_JAKAUMA.NORMIT"},"NORM.S.INV":{alias:"NORM_JAKAUMA.K\xc4\xc4NT"},NORMDIST:{alias:"NORM.JAKAUMA"},NORMINV:{alias:"NORM.JAKAUMA.K\xc4\xc4NT"},NORMSDIST:{alias:"NORM.JAKAUMA.NORMIT"},NORMSINV:{alias:"NORM.JAKAUMA.NORMIT.K\xc4\xc4NT"},NOT:{alias:"EI"},NOW:{alias:"NYT"},NPER:{alias:"NJAKSO"},NPV:{alias:"NNA"},NUMBERVALUE:{alias:"NROARVO"},OCT2BIN:{alias:"OKTBIN"},OCT2DEC:{alias:"OKTDES"},OCT2HEX:{alias:"OKTHEKSA"},ODD:{alias:"PARITON"},ODDFPRICE:{alias:"PARITON.ENS.NIMELLISARVO"},ODDFYIELD:{alias:"PARITON.ENS.TUOTTO"},ODDLPRICE:{alias:"PARITON.VIIM.NIMELLISARVO"},ODDLYIELD:{alias:"PARITON.VIIM.TUOTTO"},OFFSET:{alias:"SIIRTYM\xc4"},OR:{alias:"TAI"},PDURATION:{alias:"JKESTO"},PERCENTILE:{alias:"PROSENTTIPISTE"},"PERCENTILE.EXC":{alias:"PROSENTTIPISTE.ULK"},"PERCENTILE.INC":{alias:"PROSENTTIPISTE.SIS"},PERCENTRANK:{alias:"PROSENTTIJ\xc4RJESTYS"},"PERCENTRANK.EXC":{alias:"PROSENTTIJ\xc4RJESTYS.ULK"},"PERCENTRANK.INC":{alias:"PROSENTTIJ\xc4RJESTYS.SIS"},PERMUT:{alias:"PERMUTAATIO"},PERMUTATIONA:{alias:"PERMUTAATIOA"},PHI:{alias:"FII"},PHONETIC:{alias:"FONEETTINEN"},PI:{alias:"PII"},PMT:{alias:"MAKSU"},"POISSON.DIST":{alias:"POISSON.JAKAUMA"},POWER:{alias:"POTENSSI"},PRICE:{alias:"HINTA"},PRICEDISC:{alias:"HINTA.DISK"},PRICEMAT:{alias:"HINTA.LUNASTUS"},PROB:{alias:"TODENN\xc4K\xd6ISYYS"},PRODUCT:{alias:"TULO"},PROPER:{alias:"ERISNIMI"},PV:{alias:"NA"},QUARTILE:{alias:"NELJ\xc4NNES"},"QUARTILE.EXC":{alias:"NELJ\xc4NNES.ULK"},"QUARTILE.INC":{alias:"NELJ\xc4NNES.SIS"},QUOTIENT:{alias:"OSAM\xc4\xc4R\xc4"},RADIANS:{alias:"RADIAANIT"},RAND:{alias:"SATUNNAISLUKU"},RANDBETWEEN:{alias:"SATUNNAISLUKU.V\xc4LILT\xc4"},RANK:{alias:"ARVON.MUKAAN"},"RANK.AVG":{alias:"ARVON.MUKAAN.KESKIARVO"},"RANK.EQ":{alias:"ARVON.MUKAAN.TASAN"},RATE:{alias:"KORKO"},RECEIVED:{alias:"SAATU.HINTA"},"REGISTER.ID":{alias:"REKISTERI.TUNNUS"},REPLACE:{alias:"KORVAA"},REPLACEB:{alias:"KORVAAB"},REPT:{alias:"TOISTA"},RIGHT:{alias:"OIKEA"},RIGHTB:{alias:"OIKEAB"},ROUND:{alias:"PY\xd6RIST\xc4"},ROUNDDOWN:{alias:"PY\xd6RIST\xc4.DES.ALAS"},ROUNDUP:{alias:"PY\xd6RIST\xc4.DES.YL\xd6S"},ROW:{alias:"RIVI"},ROWS:{alias:"RIVIT"},RRI:{alias:"TOT.ROI"},RSQ:{alias:"PEARSON.NELI\xd6"},SEARCH:{alias:"K\xc4Y.L\xc4PI"},SEARCHB:{alias:"K\xc4Y.L\xc4PIB"},SEC:{alias:"SEK"},SECH:{alias:"SEKH"},SECOND:{alias:"SEKUNNIT"},SERIESSUM:{alias:"SARJA.SUMMA"},SHEET:{alias:"TAULUKKO"},SHEETS:{alias:"TAULUKOT"},SIGN:{alias:"ETUMERKKI"},SKEW:{alias:"JAKAUMAN.VINOUS"},"SKEW.P":{alias:"POP.VINOUS"},SLN:{alias:"STP"},SLOPE:{alias:"KULMAKERROIN"},SMALL:{alias:"PIENI"},SQRT:{alias:"NELI\xd6JUURI"},SQRTPI:{alias:"NELI\xd6JUURI.PII"},STANDARDIZE:{alias:"NORMITA"},STDEV:{alias:"KESKIHAJONTA"},"STDEV.P":{alias:"KESKIHAJONTA.P"},"STDEV.S":{alias:"KESKIHAJONTA.S"},STDEVA:{alias:"KESKIHAJONTAA"},STDEVP:{alias:"KESKIHAJONTAP"},STDEVPA:{alias:"KESKIHAJONTAPA"},STEYX:{alias:"KESKIVIRHE"},SUBSTITUTE:{alias:"VAIHDA"},SUBTOTAL:{alias:"V\xc4LISUMMA"},SUM:{alias:"SUMMA"},SUMIF:{alias:"SUMMA.JOS"},SUMIFS:{alias:"SUMMA.JOS.JOUKKO"},SUMPRODUCT:{alias:"TULOJEN.SUMMA"},SUMSQ:{alias:"NELI\xd6SUMMA"},SUMX2MY2:{alias:"NELI\xd6SUMMIEN.EROTUS"},SUMX2PY2:{alias:"NELI\xd6SUMMIEN.SUMMA"},SUMXMY2:{alias:"EROTUSTEN.NELI\xd6SUMMA"},SYD:{alias:"VUOSIPOISTO"},"T.DIST":{alias:"T.JAKAUMA"},"T.DIST.2T":{alias:"T.JAKAUMA.2S"},"T.DIST.RT":{alias:"T.JAKAUMA.OH"},"T.INV":{alias:"T.K\xc4\xc4NT"},"T.INV.2T":{alias:"T.K\xc4\xc4NT.2S"},"T.TEST":{alias:"T.TESTI"},TBILLEQ:{alias:"OBLIG.TUOTTOPROS"},TBILLPRICE:{alias:"OBLIG.HINTA"},TBILLYIELD:{alias:"OBLIG.TUOTTO"},TDIST:{alias:"TJAKAUMA"},TEXT:{alias:"TEKSTI"},TIME:{alias:"AIKA"},TIMEVALUE:{alias:"AIKA_ARVO"},TINV:{alias:"TJAKAUMA.K\xc4\xc4NT"},TODAY:{alias:"T\xc4M\xc4.P\xc4IV\xc4"},TRANSPOSE:{alias:"TRANSPONOI"},TREND:{alias:"SUUNTAUS"},TRIM:{alias:"POISTA.V\xc4LIT"},TRIMMEAN:{alias:"KESKIARVO.TASATTU"},TRUE:{alias:"TOSI"},TRUNC:{alias:"KATKAISE"},TTEST:{alias:"TTESTI"},TYPE:{alias:"TYYPPI"},UNICHAR:{alias:"UNICODEMERKKI"},UPPER:{alias:"ISOT"},VALUE:{alias:"ARVO"},VLOOKUP:{alias:"PHAKU"},WEBSERVICE:{alias:"VERKKOPALVELU"},WEEKDAY:{alias:"VIIKONP\xc4IV\xc4"},WEEKNUM:{alias:"VIIKKO.NRO"},"WEIBULL.DIST":{alias:"WEIBULL.JAKAUMA"},WORKDAY:{alias:"TY\xd6P\xc4IV\xc4"},"WORKDAY.INTL":{alias:"TY\xd6P\xc4IV\xc4.KANSV\xc4L"},XIRR:{alias:"SIS\xc4INEN.KORKO.JAKSOTON"},XNPV:{alias:"NNA.JAKSOTON"},XOR:{alias:"EHDOTON.TAI"},YEAR:{alias:"VUOSI"},YEARFRAC:{alias:"VUOSI.OSA"},YIELD:{alias:"TUOTTO"},YIELDDISC:{alias:"TUOTTO.DISK"},YIELDMAT:{alias:"TUOTTO.ER\xc4P"},"Z.TEST":{alias:"Z.TESTI"},ZTEST:{alias:"ZTESTI"}},tableFunctionsMapping:{"#All":"#Kaikki","#Data":"#Tiedot","#Headers":"#Yl\xe4tunnisteet","#Totals":"#Summat","#This row":"#T\xe4m\xe4 rivi"},clacErrorMapping:{"#NULL!":"#TYHJ\xc4!","#DIV/0!":"#JAKO/0!","#VALUE!":"#ARVO!","#REF!":"#VIITTAUS!","#NAME?":"#NIMI?","#N/A!":"#PUUTTUU!","#NUM!":"#LUKU!"},booleanMapping:{boolean_true:"TOSI",boolean_false:"EP\xc4TOSI"}}},"./src/languagePackages/res.French.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"INTERET.ACC"},ACCRINTM:{alias:"INTERET.ACC.MAT"},ADDRESS:{alias:"ADRESSE"},AGGREGATE:{alias:"AGREGAT"},AND:{alias:"ET"},ARABIC:{alias:"CHIFFRE.ARABE"},AREAS:{alias:"ZONES"},AVEDEV:{alias:"ECART.MOYEN"},AVERAGE:{alias:"MOYENNE"},AVERAGEIF:{alias:"MOYENNE.SI"},AVERAGEIFS:{alias:"MOYENNE.SI.ENS"},"BETA.DIST":{alias:"LOI.BETA.N"},"BETA.INV":{alias:"BETA.INVERSE.N"},BETADIST:{alias:"LOI.BETA"},BETAINV:{alias:"BETA.INVERSE"},BIN2DEC:{alias:"BINDEC"},BIN2HEX:{alias:"BINHEX"},BIN2OCT:{alias:"BINOCT"},"BINOM.DIST":{alias:"LOI.BINOMIALE.N"},"BINOM.INV":{alias:"LOI.BINOMIALE.INVERSE"},BINOMDIST:{alias:"LOI.BINOMIALE"},BITAND:{alias:"BITET"},BITOR:{alias:"BITOU"},BITRSHIFT:{alias:"BITDECALD"},BITXOR:{alias:"BITOUEXCLUSIF"},CALL:{alias:"FONCTION.APPELANTE"},CEILING:{alias:"PLAFOND"},"CEILING.MATH":{alias:"PLAFOND.MATH"},"CEILING.PRECISE":{alias:"PLAFOND.PRECIS"},CELL:{alias:"CELLULE"},CHAR:{alias:"CAR"},CHIDIST:{alias:"LOI.KHIDEUX"},CHIINV:{alias:"KHIDEUX.INVERSE"},"CHISQ.DIST":{alias:"LOI.KHIDEUX"},"CHISQ.DIST.RT":{alias:"LOI.KHIDEUX.DROITE"},"CHISQ.INV":{alias:"LOI.KHIDEUX.INVERSE"},"CHISQ.INV.RT":{alias:"LOI.KHIDEUX.INVERSE.DROITE"},CHITEST:{alias:"TEST.KHIDEUX"},CHOOSE:{alias:"CHOISIR"},CLEAN:{alias:"EPURAGE"},COLUMN:{alias:"COLONNE"},COLUMNS:{alias:"COLONNES"},COMPLEX:{alias:"COMPLEXE"},CONCATENATE:{alias:"CONCATENER"},CONFIDENCE:{alias:"INTERVALLE.CONFIANCE"},"CONFIDENCE.NORM":{alias:"INTERVALLE.CONFIANCE.NORMAL"},"CONFIDENCE.T":{alias:"INTERVALLE.CONFIANCE.STUDENT"},CORREL:{alias:"COEFFICIENT.CORRELATION"},COUNT:{alias:"NB"},COUNTA:{alias:"NBVAL"},COUNTBLANK:{alias:"NB.VIDE"},COUNTIF:{alias:"NB.SI"},COUNTIFS:{alias:"NB.SI.ENS"},COUPDAYBS:{alias:"NB.JOURS.COUPON.PREC"},COUPDAYS:{alias:"NB.JOURS.COUPONS"},COUPDAYSNC:{alias:"NB.JOURS.COUPON.SUIV"},COUPNCD:{alias:"DATE.COUPON.SUIV"},COUPNUM:{alias:"NB.COUPONS"},COUPPCD:{alias:"DATE.COUPON.PREC"},COVAR:{alias:"COVARIANCE"},"COVARIANCE.P":{alias:"COVARIANCE.PEARSON"},"COVARIANCE.S":{alias:"COVARIANCE.STANDARD"},CRITBINOM:{alias:"CRITERE.LOI.BINOMIALE"},CUBEKPIMEMBER:{alias:"MEMBREKPICUBE"},CUBEMEMBER:{alias:"MEMBRECUBE"},CUBEMEMBERPROPERTY:{alias:"PROPRIETEMEMBRECUBE"},CUBERANKEDMEMBER:{alias:"RANGMEMBRECUBE"},CUBESET:{alias:"JEUCUBE"},CUBESETCOUNT:{alias:"NBJEUCUBE"},CUBEVALUE:{alias:"VALEURCUBE"},CUMIPMT:{alias:"CUMUL.INTER"},CUMPRINC:{alias:"CUMUL.PRINCPER"},DATEVALUE:{alias:"DATEVAL"},DAVERAGE:{alias:"BDMOYENNE"},DAY:{alias:"JOUR"},DAYS:{alias:"JOURS"},DAYS360:{alias:"JOURS360"},DCOUNT:{alias:"BDNB"},DCOUNTA:{alias:"BDNBVAL"},DEC2BIN:{alias:"DECBIN"},DEC2HEX:{alias:"DECHEX"},DEC2OCT:{alias:"DECOCT"},DEGREES:{alias:"DEGRES"},DEVSQ:{alias:"SOMME.CARRES.ECARTS"},DGET:{alias:"BDLIRE"},DISC:{alias:"TAUX.ESCOMPTE"},DMAX:{alias:"BDMAX"},DMIN:{alias:"BDMIN"},DOLLAR:{alias:"DEVISE"},DOLLARDE:{alias:"PRIX.DEC"},DOLLARFR:{alias:"PRIX.FRAC"},DPRODUCT:{alias:"BDPRODUIT"},DSTDEV:{alias:"BDECARTYPE"},DSTDEVP:{alias:"BDECARTYPEP"},DSUM:{alias:"BDSOMME"},DURATION:{alias:"DUREE"},DVAR:{alias:"BDVAR"},DVARP:{alias:"BDVARP"},EDATE:{alias:"MOIS.DECALER"},EFFECT:{alias:"TAUX.EFFECTIF"},EOMONTH:{alias:"FIN.MOIS"},"ERF.PRECISE":{alias:"ERF.PRECIS"},"ERFC.PRECISE":{alias:"ERFC.PRECIS"},"ERROR.TYPE":{alias:"TYPE.ERREUR"},EVEN:{alias:"PAIR"},"EXPON.DIST":{alias:"LOI.EXPONENTIELLE.N"},EXPONDIST:{alias:"LOI.EXPONENTIELLE"},"F.DIST":{alias:"LOI.F.N"},"F.DIST.RT":{alias:"LOI.F.DROITE"},"F.INV":{alias:"INVERSE.LOI.F.N"},"F.INV.RT":{alias:"INVERSE.LOI.F.DROITE"},FALSE:{alias:"FAUX"},FDIST:{alias:"LOI.F"},FILTERXML:{alias:"FILTRE.XML"},FIND:{alias:"TROUVE"},FINDB:{alias:"TROUVERB"},FINV:{alias:"INVERSE.LOI.F"},FISHERINV:{alias:"FISHER.INVERSE"},FIXED:{alias:"CTXT"},FLOOR:{alias:"PLANCHER"},"FLOOR.MATH":{alias:"PLANCHER.MATH"},"FLOOR.PRECISE":{alias:"PLANCHER.PRECIS"},FORECAST:{alias:"PREVISION"},FREQUENCY:{alias:"FREQUENCE"},FTEST:{alias:"TEST.F"},FV:{alias:"VC"},FVSCHEDULE:{alias:"VC.PAIEMENTS"},"GAMMA.DIST":{alias:"LOI.GAMMA.N"},"GAMMA.INV":{alias:"LOI.GAMMA.INVERSE.N"},GAMMADIST:{alias:"LOI.GAMMA"},GAMMAINV:{alias:"LOI.GAMMA.INVERSE"},GAMMALN:{alias:"LNGAMMA"},"GAMMALN.PRECISE":{alias:"LNGAMMA.PRECIS"},GCD:{alias:"PGCD"},GEOMEAN:{alias:"MOYENNE.GEOMETRIQUE"},GESTEP:{alias:"SUP.SEUIL"},GETPIVOTDATA:{alias:"LIREDONNEESTABCROISDYNAMIQUE"},GROWTH:{alias:"CROISSANCE"},HARMEAN:{alias:"MOYENNE.HARMONIQUE"},HEX2BIN:{alias:"HEXBIN"},HEX2DEC:{alias:"HEXDEC"},HEX2OCT:{alias:"HEXOCT"},HLOOKUP:{alias:"RECHERCHEH"},HOUR:{alias:"HEURE"},HYPERLINK:{alias:"LIEN_HYPERTEXTE"},"HYPGEOM.DIST":{alias:"LOI.HYPERGEOMETRIQUE.N"},HYPGEOMDIST:{alias:"LOI.HYPERGEOMETRIQUE"},IF:{alias:"SI"},IFERROR:{alias:"SIERREUR"},IFNA:{alias:"SI.NON.DISP"},IMABS:{alias:"COMPLEXE.MODULE"},IMAGINARY:{alias:"COMPLEXE.IMAGINAIRE"},IMARGUMENT:{alias:"COMPLEXE.ARGUMENT"},IMCONJUGATE:{alias:"COMPLEXE.CONJUGUE"},IMCOS:{alias:"COMPLEXE.COS"},IMCOSH:{alias:"COMPLEXE.COSH"},IMCSC:{alias:"COMPLEXE.CSC"},IMCSCH:{alias:"COMPLEXE.CSCH"},IMDIV:{alias:"COMPLEXE.DIV"},IMEXP:{alias:"COMPLEXE.EXP"},IMLN:{alias:"COMPLEXE.LN"},IMLOG10:{alias:"COMPLEXE.LOG10"},IMLOG2:{alias:"COMPLEXE.LOG2"},IMPOWER:{alias:"COMPLEXE.PUISSANCE"},IMPRODUCT:{alias:"COMPLEXE.PRODUIT"},IMREAL:{alias:"COMPLEXE.REEL"},IMSEC:{alias:"COMPLEXE.SEC"},IMSECH:{alias:"COMPLEXE.SECH"},IMSIN:{alias:"COMPLEXE.SIN"},IMSINH:{alias:"COMPLEXE.SINH"},IMSQRT:{alias:"COMPLEXE.RACINE"},IMSUB:{alias:"COMPLEXE.DIFFERENCE"},IMSUM:{alias:"COMPLEXE.SOMME"},INFO:{alias:"INFORMATIONS"},INT:{alias:"ENT"},INTERCEPT:{alias:"ORDONNEE.ORIGINE"},INTRATE:{alias:"TAUX.INTERET"},IPMT:{alias:"INTPER"},IRR:{alias:"TRI"},ISBLANK:{alias:"ESTVIDE"},ISERR:{alias:"ESTERR"},ISERROR:{alias:"ESTERREUR"},ISEVEN:{alias:"EST.PAIR"},ISLOGICAL:{alias:"ESTLOGIQUE"},ISNA:{alias:"ESTNA"},ISNONTEXT:{alias:"ESTNONTEXTE"},ISNUMBER:{alias:"ESTNUM"},"ISO.CEILING":{alias:"ISO.PLAFOND"},ISODD:{alias:"EST.IMPAIR"},ISREF:{alias:"ESTREF"},ISTEXT:{alias:"ESTTEXTE"},KURT:{alias:"KURTOSIS"},LARGE:{alias:"GRANDE.VALEUR"},LCM:{alias:"PPCM"},LEFT:{alias:"GAUCHE"},LEFTB:{alias:"GAUCHEB"},LEN:{alias:"NBCAR"},LENB:{alias:"LENB"},LINEST:{alias:"DROITEREG"},LOGEST:{alias:"LOGREG"},LOGINV:{alias:"LOI.LOGNORMALE.INVERSE"},"LOGNORM.DIST":{alias:"LOI.LOGNORMALE.N"},"LOGNORM.INV":{alias:"LOI.LOGNORMALE.INVERSE.N"},LOGNORMDIST:{alias:"LOI.LOGNORMALE"},LOOKUP:{alias:"RECHERCHE"},LOWER:{alias:"MINUSCULE"},MATCH:{alias:"EQUIV"},MDETERM:{alias:"DETERMAT"},MDURATION:{alias:"DUREE.MODIFIEE"},MEDIAN:{alias:"MEDIANE"},MID:{alias:"STXT"},MINVERSE:{alias:"INVERSEMAT"},MIRR:{alias:"TRIM"},MMULT:{alias:"PRODUITMAT"},"MODE.MULT":{alias:"MODE.MULTIPLE"},"MODE.SNGL":{alias:"MODE.SIMPLE"},MONTH:{alias:"MOIS"},MROUND:{alias:"ARRONDI.AU.MULTIPLE"},MULTINOMIAL:{alias:"MULTINOMIALE"},MUNIT:{alias:"MATRICE.UNITAIRE"},"NEGBINOM.DIST":{alias:"LOI.BINOMIALE.NEG.N"},NEGBINOMDIST:{alias:"LOI.BINOMIALE.NEG"},NETWORKDAYS:{alias:"NB.JOURS.OUVRES"},"NETWORKDAYS.INTL":{alias:"NB.JOURS.OUVRES.INTL"},NOMINAL:{alias:"TAUX.NOMINAL"},"NORM.DIST":{alias:"LOI.NORMALE.N"},"NORM.INV":{alias:"LOI.NORMALE.INVERSE.N"},"NORM.S.DIST":{alias:"LOI.NORMALE.STANDARD.N"},"NORM.S.INV":{alias:"LOI.NORMALE.STANDARD.INVERSE.N"},NORMDIST:{alias:"LOI.NORMALE"},NORMINV:{alias:"LOI.NORMALE.INVERSE"},NORMSDIST:{alias:"LOI.NORMALE.STANDARD"},NORMSINV:{alias:"LOI.NORMALE.STANDARD.INVERSE"},NOT:{alias:"NON"},NOW:{alias:"MAINTENANT"},NPER:{alias:"NPM"},NPV:{alias:"VAN"},OCT2BIN:{alias:"OCTBIN"},OCT2DEC:{alias:"OCTDEC"},OCT2HEX:{alias:"OCTHEX"},ODD:{alias:"IMPAIR"},ODDFPRICE:{alias:"PRIX.PCOUPON.IRREG"},ODDFYIELD:{alias:"REND.PCOUPON.IRREG"},ODDLPRICE:{alias:"PRIX.DCOUPON.IRREG"},ODDLYIELD:{alias:"REND.DCOUPON.IRREG"},OFFSET:{alias:"DECALER"},OR:{alias:"OU"},PERCENTILE:{alias:"CENTILE"},"PERCENTILE.EXC":{alias:"CENTILE.EXCLURE"},"PERCENTILE.INC":{alias:"CENTILE.INCLURE"},PERCENTRANK:{alias:"RANG.POURCENTAGE"},"PERCENTRANK.EXC":{alias:"RANG.POURCENTAGE.EXCLURE"},"PERCENTRANK.INC":{alias:"RANG.POURCENTAGE.INCLURE"},PERMUT:{alias:"PERMUTATION"},PHONETIC:{alias:"PHONETIQUE"},PMT:{alias:"VPM"},POISSON:{alias:"LOI.POISSON"},"POISSON.DIST":{alias:"LOI.POISSON.N"},POWER:{alias:"PUISSANCE"},PPMT:{alias:"PRINCPER"},PRICE:{alias:"PRIX.TITRE"},PRICEDISC:{alias:"VALEUR.ENCAISSEMENT"},PRICEMAT:{alias:"PRIX.TITRE.ECHEANCE"},PROB:{alias:"PROBABILITE"},PRODUCT:{alias:"PRODUIT"},PROPER:{alias:"NOMPROPRE"},PV:{alias:"VA"},"QUARTILE.EXC":{alias:"QUARTILE.EXCLURE"},"QUARTILE.INC":{alias:"QUARTILE.INCLURE"},RAND:{alias:"ALEA"},RANDBETWEEN:{alias:"ALEA.ENTRE.BORNES"},RANK:{alias:"RANG"},"RANK.AVG":{alias:"MOYENNE.RANG"},"RANK.EQ":{alias:"EQUATION.RANG"},RATE:{alias:"TAUX"},RECEIVED:{alias:"VALEUR.NOMINALE"},"REGISTER.ID":{alias:"REGISTRE.NUMERO"},REPLACE:{alias:"REMPLACER"},REPLACEB:{alias:"REMPLACERB"},RIGHT:{alias:"DROITE"},RIGHTB:{alias:"DROITEB"},ROMAN:{alias:"ROMAIN"},ROUND:{alias:"ARRONDI"},ROUNDDOWN:{alias:"ARRONDI.INF"},ROUNDUP:{alias:"ARRONDI.SUP"},ROW:{alias:"LIGNE"},ROWS:{alias:"LIGNES"},RRI:{alias:"TAUX.INT.EQUIV"},RSQ:{alias:"COEFFICIENT.DETERMINATION"},SEARCH:{alias:"CHERCHE"},SEARCHB:{alias:"CHERCHERB"},SECOND:{alias:"SECONDE"},SERIESSUM:{alias:"SOMME.SERIES"},SHEET:{alias:"FEUILLE"},SIGN:{alias:"SIGNE"},SKEW:{alias:"COEFFICIENT.ASYMETRIE"},SLN:{alias:"AMORLIN"},SLOPE:{alias:"PENTE"},SMALL:{alias:"PETITE.VALEUR"},SQRT:{alias:"RACINE"},SQRTPI:{alias:"RACINE.PI"},STANDARDIZE:{alias:"CENTREE.REDUITE"},STDEV:{alias:"ECARTYPE"},"STDEV.P":{alias:"ECARTYPE.PEARSON"},"STDEV.S":{alias:"ECARTYPE.STANDARD"},STDEVP:{alias:"ECARTYPEP"},STEYX:{alias:"ERREUR.TYPE.XY"},SUBSTITUTE:{alias:"SUBSTITUE"},SUBTOTAL:{alias:"SOUS.TOTAL"},SUM:{alias:"SOMME"},SUMIF:{alias:"SOMME.SI"},SUMIFS:{alias:"SOMME.SI.ENS"},SUMPRODUCT:{alias:"SOMMEPROD"},SUMSQ:{alias:"SOMME.CARRES"},SUMX2MY2:{alias:"SOMME.X2MY2"},SUMX2PY2:{alias:"SOMME.X2PY2"},SUMXMY2:{alias:"SOMME.XMY2"},"T.DIST":{alias:"LOI.STUDENT.N"},"T.DIST.2T":{alias:"LOI.STUDENT.BILATERALE"},"T.DIST.RT":{alias:"LOI.STUDENT.DROITE"},"T.INV":{alias:"LOI.STUDENT.INVERSE.N"},"T.INV.2T":{alias:"LOI.STUDENT.INVERSE.BILATERALE"},TBILLEQ:{alias:"TAUX.ESCOMPTE.R"},TBILLPRICE:{alias:"PRIX.BON.TRESOR"},TBILLYIELD:{alias:"RENDEMENT.BON.TRESOR"},TDIST:{alias:"LOI.STUDENT"},TEXT:{alias:"TEXTE"},TIME:{alias:"TEMPS"},TIMEVALUE:{alias:"TEMPSVAL"},TINV:{alias:"LOI.STUDENT.INVERSE"},TODAY:{alias:"AUJOURDHUI"},TREND:{alias:"TENDANCE"},TRIM:{alias:"SUPPRESPACE"},TRIMMEAN:{alias:"MOYENNE.REDUITE"},TRUE:{alias:"VRAI"},TRUNC:{alias:"TRONQUE"},TTEST:{alias:"TEST.STUDENT"},UNICHAR:{alias:"UNICAR"},UPPER:{alias:"MAJUSCULE"},VALUE:{alias:"CNUM"},VLOOKUP:{alias:"RECHERCHEV"},WEBSERVICE:{alias:"SERVICEWEB"},WEEKDAY:{alias:"JOURSEM"},WEEKNUM:{alias:"NO.SEMAINE"},WEIBULL:{alias:"LOI.WEIBULL"},"WEIBULL.DIST":{alias:"LOI.WEIBULL.N"},WORKDAY:{alias:"SERIE.JOUR.OUVRE"},"WORKDAY.INTL":{alias:"SERIE.JOUR.OUVRE.INTL"},XIRR:{alias:"TRI.PAIEMENTS"},XNPV:{alias:"VAN.PAIEMENTS"},XOR:{alias:"OUX"},YEAR:{alias:"ANNEE"},YEARFRAC:{alias:"FRACTION.ANNEE"},YIELD:{alias:"RENDEMENT.TITRE"},YIELDDISC:{alias:"RENDEMENT.SIMPLE"},YIELDMAT:{alias:"RENDEMENT.TITRE.ECHEANCE"},ZTEST:{alias:"TEST.Z"}},tableFunctionsMapping:{"#All":"#Tout","#Data":"#Donn\xe9es","#Headers":"#En-t\xeates","#Totals":"#Totaux","#This row":"#Cette ligne"},clacErrorMapping:{"#NULL!":"#NUL!","#DIV/0!":"#DIV/0!","#VALUE!":"#VALEUR!","#REF!":"#REF!","#NAME?":"#NOM?","#N/A!":"#N/A","#NUM!":"#NOMBRE!"},booleanMapping:{boolean_true:"VRAI",boolean_false:"FAUX"}}},"./src/languagePackages/res.German.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"AUFGELZINS"},ACCRINTM:{alias:"AUFGELZINSF"},ACOS:{alias:"ARCCOS"},ACOSH:{alias:"ARCCOSHYP"},ACOT:{alias:"ARCCOT"},ACOTH:{alias:"ARCCOTHYP"},ADDRESS:{alias:"ADRESSE"},AGGREGATE:{alias:"AGGREGAT"},AMORDEGRC:{alias:"AMORDEGRK"},AMORLINC:{alias:"AMORLINEARK"},AND:{alias:"UND"},ARABIC:{alias:"ARABISCH"},AREAS:{alias:"BEREICHE"},ASIN:{alias:"ARCSIN"},ASINH:{alias:"ARCSINHYP"},ATAN:{alias:"ARCTAN"},ATAN2:{alias:"ARCTAN2"},ATANH:{alias:"ARCTANHYP"},AVEDEV:{alias:"MITTELABW"},AVERAGE:{alias:"MITTELWERT"},AVERAGEA:{alias:"MITTELWERTA"},AVERAGEIF:{alias:"MITTELWERTWENN"},AVERAGEIFS:{alias:"MITTELWERTWENNS"},BASE:{alias:"BASIS"},"BETA.DIST":{alias:"BETA.VERT"},BETADIST:{alias:"BETAVERT"},BIN2DEC:{alias:"BININDEZ"},BIN2HEX:{alias:"BININHEX"},BIN2OCT:{alias:"BININOKT"},"BINOM.DIST":{alias:"BINOM.VERT"},"BINOM.DIST.RANGE":{alias:"BINOM.VERT.BEREICH"},BINOMDIST:{alias:"BINOMVERT"},BITAND:{alias:"BITUND"},BITLSHIFT:{alias:"BITLVERSCHIEB"},BITOR:{alias:"BITODER"},BITRSHIFT:{alias:"BITRVERSCHIEB"},BITXOR:{alias:"BITXODER"},CALL:{alias:"AUFRUFEN"},CEILING:{alias:"OBERGRENZE"},"CEILING.MATH":{alias:"OBERGRENZE.MATHEMATIK"},"CEILING.PRECISE":{alias:"OBERGRENZE.GENAU"},CELL:{alias:"ZELLE"},CHAR:{alias:"ZEICHEN"},CHIDIST:{alias:"CHIVERT"},"CHISQ.DIST":{alias:"CHIQU.VERT"},"CHISQ.DIST.RT":{alias:"CHIQU.VERT.RE"},"CHISQ.INV":{alias:"CHIQU.INV"},"CHISQ.INV.RT":{alias:"CHIQU.INV.RE"},"CHISQ.TEST":{alias:"CHIQU.TEST"},CHOOSE:{alias:"WAHL"},CLEAN:{alias:"S\xc4UBERN"},COLUMN:{alias:"SPALTE"},COLUMNS:{alias:"SPALTEN"},COMBIN:{alias:"KOMBINATIONEN"},COMBINA:{alias:"KOMBINATIONEN2"},COMPLEX:{alias:"KOMPLEXE"},CONCATENATE:{alias:"VERKETTEN"},CONFIDENCE:{alias:"KONFIDENZ"},"CONFIDENCE.NORM":{alias:"KONFIDENZ.NORM"},"CONFIDENCE.T":{alias:"KONFIDENZ.T"},CONVERT:{alias:"UMWANDELN"},CORREL:{alias:"KORREL"},COSH:{alias:"COSHYP"},COTH:{alias:"COTHYP"},COUNT:{alias:"ANZAHL"},COUNTA:{alias:"ANZAHL2"},COUNTBLANK:{alias:"ANZAHLLEEREZELLEN"},COUNTIF:{alias:"Z\xc4HLENWENN"},COUNTIFS:{alias:"Z\xc4HLENWENNS"},COUPDAYBS:{alias:"ZINSTERMTAGVA"},COUPDAYS:{alias:"ZINSTERMTAGE"},COUPDAYSNC:{alias:"ZINSTERMTAGNZ"},COUPNCD:{alias:"ZINSTERMNZ"},COUPNUM:{alias:"ZINSTERMZAHL"},COUPPCD:{alias:"ZINSTERMVZ"},COVAR:{alias:"KOVAR"},"COVARIANCE.P":{alias:"KOVARIANZ.P"},"COVARIANCE.S":{alias:"KOVARIANZ.S"},CRITBINOM:{alias:"KRITBINOM"},CSC:{alias:"COSEC"},CSCH:{alias:"COSECHYP"},CUBEKPIMEMBER:{alias:"CUBEKPIELEMENT"},CUBEMEMBER:{alias:"CUBEELEMENT"},CUBEMEMBERPROPERTY:{alias:"CUBEELEMENTEIGENSCHAFT"},CUBERANKEDMEMBER:{alias:"CUBERANGELEMENT"},CUBESET:{alias:"CUBEMENGE"},CUBESETCOUNT:{alias:"CUBEMENGENANZAHL"},CUBEVALUE:{alias:"CUBEWERT"},CUMIPMT:{alias:"KUMZINSZ"},CUMPRINC:{alias:"KUMKAPITAL"},DATE:{alias:"DATUM"},DATEVALUE:{alias:"DATWERT"},DAVERAGE:{alias:"DBMITTELWERT"},DAY:{alias:"TAG"},DAYS:{alias:"TAGE"},DAYS360:{alias:"TAGE360"},DB:{alias:"GDA2"},DBCS:{alias:"JIS"},DCOUNT:{alias:"DBANZAHL"},DCOUNTA:{alias:"DBANZAHL2"},DDB:{alias:"GDA"},DEC2BIN:{alias:"DEZINBIN"},DEC2HEX:{alias:"DEZINHEX"},DEC2OCT:{alias:"DEZINOKT"},DECIMAL:{alias:"DEZIMAL"},DEGREES:{alias:"GRAD"},DEVSQ:{alias:"SUMQUADABW"},DGET:{alias:"DBAUSZUG"},DISC:{alias:"DISAGIO"},DMAX:{alias:"DBMAX"},DMIN:{alias:"DBMIN"},DOLLAR:{alias:"DM"},DOLLARDE:{alias:"NOTIERUNGDEZ"},DOLLARFR:{alias:"NOTIERUNGBRU"},DPRODUCT:{alias:"DBPRODUKT"},DSTDEV:{alias:"DBSTDABW"},DSTDEVP:{alias:"DBSTDABWN"
},DSUM:{alias:"DBSUMME"},DVAR:{alias:"DBVARIANZ"},DVARP:{alias:"DBVARIANZEN"},EDATE:{alias:"EDATUM"},EFFECT:{alias:"EFFEKTIV"},ENCODEURL:{alias:"URLCODIEREN"},EOMONTH:{alias:"MONATSENDE"},ERF:{alias:"GAUSSFEHLER"},"ERF.PRECISE":{alias:"GAUSSF.GENAU"},ERFC:{alias:"GAUSSFKOMPL"},"ERFC.PRECISE":{alias:"GAUSSFKOMPL.GENAU"},"ERROR.TYPE":{alias:"FEHLER.TYP"},EVEN:{alias:"GERADE"},EXACT:{alias:"IDENTISCH"},"EXPON.DIST":{alias:"EXPON.VERT"},EXPONDIST:{alias:"EXPONVERT"},"F.DIST":{alias:"F.VERT"},"F.DIST.RT":{alias:"F.VERT.RE"},"F.INV.RT":{alias:"F.INV.RE"},FACT:{alias:"FAKULT\xc4T"},FACTDOUBLE:{alias:"ZWEIFAKULT\xc4T"},FALSE:{alias:"FALSCH"},FDIST:{alias:"FVERT"},FILTERXML:{alias:"XMLFILTERN"},FIND:{alias:"FINDEN"},FINDB:{alias:"FINDENB"},FIXED:{alias:"FEST"},FLOOR:{alias:"UNTERGRENZE"},"FLOOR.MATH":{alias:"UNTERGRENZE.MATHEMATIK"},"FLOOR.PRECISE":{alias:"UNTERGRENZE.GENAU"},FORECAST:{alias:"PROGNOSE"},FORMULATEXT:{alias:"FORMELTEXT"},FREQUENCY:{alias:"H\xc4UFIGKEIT"},FV:{alias:"ZW"},FVSCHEDULE:{alias:"ZW2"},"GAMMA.DIST":{alias:"GAMMA.VERT"},GAMMADIST:{alias:"GAMMAVERT"},"GAMMALN.PRECISE":{alias:"GAMMALN.GENAU"},GCD:{alias:"GGT"},GEOMEAN:{alias:"GEOMITTEL"},GESTEP:{alias:"GGANZZAHL"},GETPIVOTDATA:{alias:"PIVOTDATENZUORDNEN"},GROWTH:{alias:"VARIATION"},HARMEAN:{alias:"HARMITTEL"},HEX2BIN:{alias:"HEXINBIN"},HEX2DEC:{alias:"HEXINDEZ"},HEX2OCT:{alias:"HEXINOKT"},HLOOKUP:{alias:"WVERWEIS"},HOUR:{alias:"STUNDE"},"HYPGEOM.DIST":{alias:"HYPGEOM.VERT"},HYPGEOMDIST:{alias:"HYPGEOMVERT"},IF:{alias:"WENN"},IFERROR:{alias:"WENNFEHLER"},IFNA:{alias:"WENNNV"},IMAGINARY:{alias:"IMAGIN\xc4RTEIL"},IMCONJUGATE:{alias:"IMKONJUGIERTE"},IMCOSH:{alias:"IMCOSHYP"},IMCSC:{alias:"IMCOSEC"},IMCSCH:{alias:"IMCOSECHYP"},IMPOWER:{alias:"IMAPOTENZ"},IMPRODUCT:{alias:"IMPRODUKT"},IMREAL:{alias:"IMREALTEIL"},IMSECH:{alias:"IMSECHYP"},IMSINH:{alias:"IMSINHYP"},IMSQRT:{alias:"IMWURZEL"},IMSUM:{alias:"IMSUMME"},INDIRECT:{alias:"INDIREKT"},INT:{alias:"GANZZAHL"},INTERCEPT:{alias:"ACHSENABSCHNITT"},INTRATE:{alias:"ZINSSATZ"},IPMT:{alias:"ZINSZ"},IRR:{alias:"IKV"},ISBLANK:{alias:"ISTLEER"},ISERR:{alias:"ISTFEHL"},ISERROR:{alias:"ISTFEHLER"},ISEVEN:{alias:"ISTGERADE"},ISFORMULA:{alias:"ISTFORMEL"},ISLOGICAL:{alias:"ISTLOG"},ISNA:{alias:"ISTNV"},ISNONTEXT:{alias:"ISTKTEXT"},ISNUMBER:{alias:"ISTZAHL"},"ISO.CEILING":{alias:"ISO.OBERGRENZE"},ISODD:{alias:"ISTUNGERADE"},ISOWEEKNUM:{alias:"ISOKALENDERWOCHE"},ISREF:{alias:"ISTBEZUG"},ISTEXT:{alias:"ISTTEXT"},LARGE:{alias:"KGR\xd6SSTE"},LCM:{alias:"KGV"},LEFT:{alias:"LINKS"},LEFTB:{alias:"LINKSB"},LEN:{alias:"L\xc4NGE"},LENB:{alias:"L\xc4NGEB"},LINEST:{alias:"RGP"},LOGEST:{alias:"RKP"},"LOGNORM.DIST":{alias:"LOGNORM.VERT"},LOGNORMDIST:{alias:"LOGNORMVERT"},LOOKUP:{alias:"VERWEIS"},LOWER:{alias:"KLEIN"},MATCH:{alias:"VERGLEICH"},MDETERM:{alias:"MDET"},MID:{alias:"TEIL"},MIDB:{alias:"TEILB"},MINVERSE:{alias:"MINV"},MIRR:{alias:"QIKV"},MOD:{alias:"REST"},MODE:{alias:"MODALWERT"},"MODE.MULT":{alias:"MODUS.VIELF"},"MODE.SNGL":{alias:"MODUS.EINF"},MONTH:{alias:"MONAT"},MROUND:{alias:"VRUNDEN"},MULTINOMIAL:{alias:"POLYNOMIAL"},MUNIT:{alias:"MEINHEIT"},NA:{alias:"NV"},"NEGBINOM.DIST":{alias:"NEGBINOM.VERT"},NEGBINOMDIST:{alias:"NEGBINOMVERT"},NETWORKDAYS:{alias:"NETTOARBEITSTAGE"},"NETWORKDAYS.INTL":{alias:"NETTOARBEITSTAGE.INTL"},"NORM.DIST":{alias:"NORM.VERT"},"NORM.S.DIST":{alias:"NORM.S.VERT"},NORMDIST:{alias:"NORMVERT"},NORMSDIST:{alias:"NORMVERT"},NORMSINV:{alias:"STANDNORMINV"},NOT:{alias:"NICHT"},NOW:{alias:"JETZT"},NPER:{alias:"ZZR"},NPV:{alias:"NBW"},NUMBERVALUE:{alias:"ZAHLENWERT"},OCT2BIN:{alias:"OKTINBIN"},OCT2DEC:{alias:"OKTINDEZ"},OCT2HEX:{alias:"OKTINHEX"},ODD:{alias:"UNGERADE"},ODDFPRICE:{alias:"UNREGER.KURS"},ODDFYIELD:{alias:"UNREGER.REND"},ODDLPRICE:{alias:"UNREGLE.KURS"},ODDLYIELD:{alias:"UNREGLE.REND"},OFFSET:{alias:"BEREICH.VERSCHIEBEN"},OR:{alias:"ODER"},PERCENTILE:{alias:"QUANTIL"},"PERCENTILE.EXC":{alias:"QUANTIL.EXKL"},"PERCENTILE.INC":{alias:"QUANTIL.INKL"},PERCENTRANK:{alias:"QUANTILSRANG"},"PERCENTRANK.EXC":{alias:"QUANTILSRANG.EXKL"},"PERCENTRANK.INC":{alias:"QUANTILSRANG.INKL"},PERMUT:{alias:"VARIATIONEN"},PERMUTATIONA:{alias:"VARIATIONEN2"},PMT:{alias:"RMZ"},"POISSON.DIST":{alias:"POISSON.VERT"},POWER:{alias:"POTENZ"},PPMT:{alias:"KAPZ"},PRICE:{alias:"KURS"},PRICEDISC:{alias:"KURSDISAGIO"},PRICEMAT:{alias:"KURSF\xc4LLIG"},PROB:{alias:"WAHRSCHBEREICH"},PRODUCT:{alias:"PRODUKT"},PROPER:{alias:"GROSS2"},PV:{alias:"BW"},"QUARTILE.EXC":{alias:"QUARTILE.EXKL"},"QUARTILE.INC":{alias:"QUARTILE.INKL"},RADIANS:{alias:"BOGENMASS"},RAND:{alias:"ZUFALLSZAHL"},RANDBETWEEN:{alias:"ZUFALLSBEREICH"},RANK:{alias:"RANG"},"RANK.AVG":{alias:"RANG.MITTELW"},"RANK.EQ":{alias:"RANG.GLEICH"},RATE:{alias:"ZINS"},RECEIVED:{alias:"AUSZAHLUNG"},"REGISTER.ID":{alias:"REGISTER.KENNUMMER"},REPLACE:{alias:"ERSETZEN"},REPLACEB:{alias:"ERSETZENB"},REPT:{alias:"WIEDERHOLEN"},RIGHT:{alias:"RECHTS"},RIGHTB:{alias:"RECHTSB"},ROMAN:{alias:"R\xd6MISCH"},ROUND:{alias:"RUNDEN"},ROUNDDOWN:{alias:"ABRUNDEN"},ROUNDUP:{alias:"AUFRUNDEN"},ROW:{alias:"ZEILE"},ROWS:{alias:"ZEILEN"},RRI:{alias:"ZSATZINVEST"},RSQ:{alias:"BESTIMMTHEITSMASS"},SEARCH:{alias:"SUCHEN"},SEARCHB:{alias:"SUCHENB"},SECH:{alias:"SECHYP"},SECOND:{alias:"SEKUNDE"},SERIESSUM:{alias:"POTENZREIHE"},SHEET:{alias:"BLATT"},SHEETS:{alias:"BL\xc4TTER"},SIGN:{alias:"VORZEICHEN"},SINH:{alias:"SINHYP"},SKEW:{alias:"SCHIEFE"},"SKEW.P":{alias:"SCHIEFE.P"},SLN:{alias:"LIA"},SLOPE:{alias:"STEIGUNG"},SMALL:{alias:"KKLEINSTE"},SQRT:{alias:"WURZEL"},SQRTPI:{alias:"WURZELPI"},STANDARDIZE:{alias:"STANDARDISIERUNG"},STDEV:{alias:"STABW"},"STDEV.P":{alias:"STABW.N"},"STDEV.S":{alias:"STABW.S"},STDEVA:{alias:"STABWA"},STDEVP:{alias:"STABWN"},STDEVPA:{alias:"STABWNA"},STEYX:{alias:"STFEHLERYX"},SUBSTITUTE:{alias:"WECHSELN"},SUBTOTAL:{alias:"TEILERGEBNIS"},SUM:{alias:"SUMME"},SUMIF:{alias:"SUMMEWENN"},SUMIFS:{alias:"SUMMEWENNS"},SUMPRODUCT:{alias:"SUMMENPRODUKT"},SUMSQ:{alias:"QUADRATESUMME"},SUMX2MY2:{alias:"SUMMEX2MY2"},SUMX2PY2:{alias:"SUMMEX2PY2"},SUMXMY2:{alias:"SUMMEXMY2"},SYD:{alias:"DIA"},"T.DIST":{alias:"T.VERT"},"T.DIST.2T":{alias:"T.VERT.2S"},"T.DIST.RT":{alias:"T.VERT.RE"},"T.INV.2T":{alias:"T.INV.2S"},TANH:{alias:"TANHYP"},TBILLEQ:{alias:"TBILL\xc4QUIV"},TBILLPRICE:{alias:"TBILLKURS"},TBILLYIELD:{alias:"TBILLRENDITE"},TDIST:{alias:"TVERT"},TIME:{alias:"ZEIT"},TIMEVALUE:{alias:"ZEITWERT"},TODAY:{alias:"HEUTE"},TRANSPOSE:{alias:"MTRANS"},TRIM:{alias:"GL\xc4TTEN"},TRIMMEAN:{alias:"GESTUTZTMITTEL"},TRUE:{alias:"WAHR"},TRUNC:{alias:"K\xdcRZEN"},TYPE:{alias:"TYP"},UNICHAR:{alias:"UNIZEICHEN"},UPPER:{alias:"GROSS"},VALUE:{alias:"WERT"},VAR:{alias:"VARIANZ"},VARA:{alias:"VARIANZA"},VARP:{alias:"VARIANZEN"},VARPA:{alias:"VARIANZENA"},VLOOKUP:{alias:"SVERWEIS"},WEBSERVICE:{alias:"WEBDIENST"},WEEKDAY:{alias:"WOCHENTAG"},WEEKNUM:{alias:"KALENDERWOCHE"},"WEIBULL.DIST":{alias:"WEIBULL.VERT"},WORKDAY:{alias:"ARBEITSTAG"},"WORKDAY.INTL":{alias:"ARBEITSTAG.INTL"},XIRR:{alias:"XINTZINSFUSS"},XNPV:{alias:"XKAPITALWERT"},XOR:{alias:"XODER"},YEAR:{alias:"JAHR"},YEARFRAC:{alias:"BRTEILJAHRE"},YIELD:{alias:"RENDITE"},YIELDDISC:{alias:"RENDITEDIS"},YIELDMAT:{alias:"RENDITEF\xc4LL"},"Z.TEST":{alias:"G.TEST"},ZTEST:{alias:"GTEST"}},tableFunctionsMapping:{"#All":"#Alle","#Data":"#Daten","#Headers":"#Kopfzeilen","#Totals":"#Ergebnisse","#This row":"#Diese Zeile"},clacErrorMapping:{"#NULL!":"#NULL!","#DIV/0!":"#DIV/0!","#VALUE!":"#WERT!","#REF!":"#BEZUG!","#NAME?":"#NAME?","#N/A!":"#NV","#NUM!":"#ZAHL!"},booleanMapping:{boolean_true:"WAHR",boolean_false:"FALSCH"}}},"./src/languagePackages/res.Hungarian.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"ID\u0150SZAKI.KAMAT"},ACCRINTM:{alias:"LEJ\xc1RATI.KAMAT"},ACOS:{alias:"ARCCOS"},ACOT:{alias:"ARCCOT"},ACOTH:{alias:"ARCCOTH"},ADDRESS:{alias:"C\xcdM"},AGGREGATE:{alias:"\xd6SSZES\xcdT"},AMORDEGRC:{alias:"\xc9RT\xc9KCS\xd6KK.T\xc9NYEZ\u0150VEL"},AMORLINC:{alias:"\xc9RT\xc9KCS\xd6KK"},AND:{alias:"\xc9S"},ARABIC:{alias:"ARAB"},AREAS:{alias:"TER\xdcLET"},ASIN:{alias:"ARCSIN"},ATAN:{alias:"ARCTAN"},ATAN2:{alias:"ARCTAN2"},AVEDEV:{alias:"\xc1TL.ELT\xc9R\xc9S"},AVERAGE:{alias:"\xc1TLAG"},AVERAGEA:{alias:"\xc1TLAGA"},AVERAGEIF:{alias:"\xc1TLAGHA"},AVERAGEIFS:{alias:"\xc1TLAGHAT\xd6BB"},BAHTTEXT:{alias:"BAHTSZ\xd6VEG"},BASE:{alias:"ALAP"},"BETA.DIST":{alias:"B\xc9TA.ELOSZL"},"BETA.INV":{alias:"B\xc9TA.INVERZ"},BETADIST:{alias:"B\xc9TA.ELOSZL\xc1S"},BETAINV:{alias:"INVERZ.B\xc9TA"},BIN2DEC:{alias:"BIN.DEC"},BIN2HEX:{alias:"BIN.HEX"},BIN2OCT:{alias:"BIN.OKT"},"BINOM.DIST":{alias:"BINOM.ELOSZL"},"BINOM.DIST.RANGE":{alias:"BINOM.ELOSZL.TART"},"BINOM.INV":{alias:"BINOM.INVERZ"},BINOMDIST:{alias:"BINOM.ELOSZL\xc1S"},BITAND:{alias:"BIT.\xc9S"},BITLSHIFT:{alias:"BIT.BAL.ELTOL"},BITOR:{alias:"BIT.VAGY"},BITRSHIFT:{alias:"BIT.JOBB.ELTOL"},BITXOR:{alias:"BIT.XVAGY"},CALL:{alias:"H\xcdV\xc1S"},CEILING:{alias:"PLAFON"},"CEILING.MATH":{alias:"PLAFON.MAT"},"CEILING.PRECISE":{alias:"PLAFON.PONTOS"},CELL:{alias:"CELLA"},CHAR:{alias:"KARAKTER"},CHIDIST:{alias:"KHI.ELOSZL\xc1S"},CHIINV:{alias:"INVERZ.KHI"},"CHISQ.DIST":{alias:"KHIN\xc9GYZET.ELOSZL\xc1S"},"CHISQ.DIST.RT":{alias:"KHIN\xc9GYZET.ELOSZL\xc1S.JOBB"},"CHISQ.INV":{alias:"KHIN\xc9GYZET.INVERZ"},"CHISQ.INV.RT":{alias:"KHIN\xc9GYZET.INVERZ.JOBB"},"CHISQ.TEST":{alias:"KHIN\xc9GYZET.PR\xd3BA"},CHITEST:{alias:"KHI.PR\xd3BA"},CHOOSE:{alias:"V\xc1LASZT"},CLEAN:{alias:"TISZT\xcdT"},CODE:{alias:"K\xd3D"},COLUMN:{alias:"OSZLOP"},COLUMNS:{alias:"OSZLOPOK"},COMBIN:{alias:"KOMBIN\xc1CI\xd3K.ISM"},COMBINA:{alias:"KOMBIN\xc1CI\xd3K.ISM"},COMPLEX:{alias:"KOMPLEX"},CONCATENATE:{alias:"\xd6SSZEF\u0170Z"},CONFIDENCE:{alias:"MEGB\xcdZHAT\xd3S\xc1G"},"CONFIDENCE.NORM":{alias:"MEGB\xcdZHAT\xd3S\xc1G.NORM"},"CONFIDENCE.T":{alias:"MEGB\xcdZHAT\xd3S\xc1G.T"},CONVERT:{alias:"KONVERT\xc1L\xc1S"},CORREL:{alias:"KORREL"},COUNT:{alias:"DARAB"},COUNTA:{alias:"DARAB2"},COUNTBLANK:{alias:"DARAB\xdcRES"},COUNTIF:{alias:"DARABTELI"},COUNTIFS:{alias:"DARABHAT\xd6BB"},COUPDAYBS:{alias:"SZELV\xc9NYID\u0150.KEZDETT\u0150L"},COUPDAYS:{alias:"SZELV\xc9NYID\u0150"},COUPDAYSNC:{alias:"SZELV\xc9NYID\u0150.KIFIZET\xc9ST\u0150L"},COUPNCD:{alias:"ELS\u0150.SZELV\xc9NYD\xc1TUM"},COUPNUM:{alias:"SZELV\xc9NYSZ\xc1M"},COUPPCD:{alias:"UTOLS\xd3.SZELV\xc9NYD\xc1TUM"},COVAR:{alias:"KOVAR"},"COVARIANCE.P":{alias:"KOVARIANCIA.S"},"COVARIANCE.S":{alias:"KOVARIANCIA.M"},CRITBINOM:{alias:"KRITBINOM"},CUBEKPIMEMBER:{alias:"KOCKA.F\u0150TELJMUT"},CUBEMEMBER:{alias:"KOCKA.TAG"},CUBEMEMBERPROPERTY:{alias:"KOCKA.TAG.TUL"},CUBERANKEDMEMBER:{alias:"KOCKA.HALM.ELEM"},CUBESET:{alias:"KOCKA.HALM"},CUBESETCOUNT:{alias:"KOCKA.HALM.DB"},CUBEVALUE:{alias:"KOCKA.\xc9RT\xc9K"},CUMIPMT:{alias:"\xd6SSZES.KAMAT"},CUMPRINC:{alias:"\xd6SSZES.T\u0150KER\xc9SZ"},DATE:{alias:"D\xc1TUM"},DATEVALUE:{alias:"D\xc1TUM\xc9RT\xc9K"},DAVERAGE:{alias:"AB.\xc1TLAG"},DAY:{alias:"NAP"},DAYS:{alias:"NAPOK"},DAYS360:{alias:"NAP360"},DB:{alias:"KCS2"},DBCS:{alias:"KBKK"},DCOUNT:{alias:"AB.DARAB"},DCOUNTA:{alias:"AB.DARAB2"},DDB:{alias:"KCSA"},DEC2BIN:{alias:"DEC.BIN"},DEC2HEX:{alias:"DEC.HEX"},DEC2OCT:{alias:"DEC.OKT"},DECIMAL:{alias:"TIZEDES"},DEGREES:{alias:"FOK"},DEVSQ:{alias:"SQ"},DGET:{alias:"AB.MEZ\u0150"},DISC:{alias:"LESZ\xc1M"},DMAX:{alias:"AB.MAX"},DMIN:{alias:"AB.MIN"},DOLLAR:{alias:"FORINT"},DOLLARDE:{alias:"FORINT.DEC"},DOLLARFR:{alias:"FORINT.T\xd6RT"},DPRODUCT:{alias:"AB.SZORZAT"},DSTDEV:{alias:"AB.SZ\xd3R\xc1S"},DSTDEVP:{alias:"AB.SZ\xd3R\xc1S2"},DSUM:{alias:"AB.SZUM"},DURATION:{alias:"KAMAT\xc9RZ"},DVAR:{alias:"AB.VAR"},DVARP:{alias:"AB.VAR2"},EDATE:{alias:"KALK.D\xc1TUM"},EFFECT:{alias:"T\xc9NYLEGES"},ENCODEURL:{alias:"URL.K\xd3DOL"},EOMONTH:{alias:"H\xd3NAP.UTOLS\xd3.NAP"},ERF:{alias:"HIBAF"},"ERF.PRECISE":{alias:"HIBAF.PONTOS"},ERFC:{alias:"HIBAF.KOMPLEMENTER"},"ERFC.PRECISE":{alias:"HIBAFKOMPLEMENTER.PONTOS"},"ERROR.TYPE":{alias:"HIBA.T\xcdPUS"},EVEN:{alias:"P\xc1ROS"},EXACT:{alias:"AZONOS"},EXP:{alias:"KITEV\u0150"},"EXPON.DIST":{alias:"EXP.ELOSZL"},EXPONDIST:{alias:"EXP.ELOSZL\xc1S"},"F.DIST":{alias:"F.ELOSZL"},"F.DIST.RT":{alias:"F.ELOSZL\xc1S.JOBB"},"F.INV":{alias:"F.INVERZ"},"F.INV.RT":{alias:"F.INVERZ.JOBB"},"F.TEST":{alias:"F.PR\xd3B"},FACT:{alias:"FAKT"},FACTDOUBLE:{alias:"FAKTDUPLA"},FALSE:{alias:"HAMIS"},FDIST:{alias:"F.ELOSZL\xc1S"},FILTERXML:{alias:"XMLSZ\u0170R\xc9S"},FIND:{alias:"SZ\xd6VEG.TAL\xc1L"},FINDB:{alias:"SZ\xd6VEG.TAL\xc1L2"},FINV:{alias:"INVERZ.F"},FISHERINV:{alias:"INVERZ.FISHER"},FIXED:{alias:"FIX"},FLOOR:{alias:"PADL\xd3"},"FLOOR.MATH":{alias:"PADL\xd3.MAT"},"FLOOR.PRECISE":{alias:"PADL\xd3.PONTOS"},FORECAST:{alias:"EL\u0150REJELZ\xc9S"},FORMULATEXT:{alias:"K\xc9PLETSZ\xd6VEG"},FREQUENCY:{alias:"GYAKORIS\xc1G"},FTEST:{alias:"F.PR\xd3BA"},FV:{alias:"JB\xc9"},FVSCHEDULE:{alias:"KJ\xc9"},"GAMMA.DIST":{alias:"GAMMA.ELOSZL"},"GAMMA.INV":{alias:"GAMMA.INVERZ"},GAMMADIST:{alias:"GAMMA.ELOSZL\xc1S"},GAMMAINV:{alias:"INVERZ.GAMMA"},"GAMMALN.PRECISE":{alias:"GAMMALN.PONTOS"},GCD:{alias:"LKO"},GEOMEAN:{alias:"M\xc9RTANI.K\xd6Z\xc9P"},GESTEP:{alias:"K\xdcSZ\xd6BN\xc9L.NAGYOBB"},GETPIVOTDATA:{alias:"KIMUTAT\xc1SADATOT.VESZ"},GROWTH:{alias:"N\xd6V"},HARMEAN:{alias:"HARM.K\xd6Z\xc9P"},HEX2BIN:{alias:"HEX.BIN"},HEX2DEC:{alias:"HEX.DEC"},HEX2OCT:{alias:"HEX.OKT"},HLOOKUP:{alias:"VKERES"},HOUR:{alias:"\xd3RA"},HYPERLINK:{alias:"HIPERHIVATKOZ\xc1S"},"HYPGEOM.DIST":{alias:"HIPGEOM.ELOSZL\xc1S"},HYPGEOMDIST:{alias:"HIPERGEOM.ELOSZL\xc1S"},IF:{alias:"HA"},IFERROR:{alias:"HAHIBA"},IFNA:{alias:"HAHI\xc1NYZIK"},IMABS:{alias:"K\xc9PZ.ABSZ"},IMAGINARY:{alias:"K\xc9PZETES"},IMARGUMENT:{alias:"K\xc9PZ.ARGUMENT"},IMCONJUGATE:{alias:"K\xc9PZ.KONJUG\xc1LT"},IMCOS:{alias:"K\xc9PZ.COS"},IMCOT:{alias:"K\xc9PZ.COT"},IMCSCH:{alias:"K\xc9PZ.CSCH"},IMDIV:{alias:"K\xc9PZ.H\xc1NYAD"},IMEXP:{alias:"K\xc9PZ.EXP"},IMLN:{alias:"K\xc9PZ.LN"},IMLOG10:{alias:"K\xc9PZ.LOG10"},IMLOG2:{alias:"K\xc9PZ.LOG2"},IMPOWER:{alias:"K\xc9PZ.HATV"},IMPRODUCT:{alias:"K\xc9PZ.SZORZAT"},IMREAL:{alias:"K\xc9PZ.VAL\xd3S"},IMSECH:{alias:"K\xc9PZ.SECH"},IMSIN:{alias:"K\xc9PZ.SIN"},IMSINH:{alias:"K\xc9PZ.SINH"},IMSQRT:{alias:"K\xc9PZ.GY\xd6K"},IMSUB:{alias:"K\xc9PZ.K\xdcL"},IMSUM:{alias:"K\xc9PZ.\xd6SSZEG"},IMTAN:{alias:"K\xc9PZ.TAN"},INDIRECT:{alias:"INDIREKT"},INFO:{alias:"INF\xd3"},INTERCEPT:{alias:"METSZ"},INTRATE:{alias:"KAMATR\xc1TA"},IPMT:{alias:"RR\xc9SZLET"},IRR:{alias:"BMR"},ISBLANK:{alias:"\xdcRES"},ISERR:{alias:"HIBA.E"},ISERROR:{alias:"HIB\xc1S"},ISEVEN:{alias:"P\xc1ROSE"},ISFORMULA:{alias:"K\xc9PLET"},ISLOGICAL:{alias:"LOGIKAI"},ISNA:{alias:"NINCS"},ISNONTEXT:{alias:"NEM.SZ\xd6VEG"},ISNUMBER:{alias:"SZ\xc1M"},"ISO.CEILING":{alias:"ISO.PLAFON"},ISODD:{alias:"P\xc1RATLANE"},ISOWEEKNUM:{alias:"ISO.H\xc9T.SZ\xc1MA"},ISPMT:{alias:"LR\xc9SZLETKAMAT"},ISREF:{alias:"HIVATKOZ\xc1S"},ISTEXT:{alias:"SZ\xd6VEG.E"},KURT:{alias:"CS\xdaCSOSS\xc1G"},LARGE:{alias:"NAGY"},LCM:{alias:"LKT"},LEFT:{alias:"BAL"},LEFTB:{alias:"BAL2"},LEN:{alias:"HOSSZ"},LENB:{alias:"HOSSZ2"},LINEST:{alias:"LIN.ILL"},LOGEST:{alias:"LOG.ILL"},LOGINV:{alias:"INVERZ.LOG.ELOSZL\xc1S"},"LOGNORM.DIST":{alias:"LOGNORM.ELOSZL\xc1S"},"LOGNORM.INV":{alias:"LOGNORM.INVERZ"},LOGNORMDIST:{alias:"LOG.ELOSZL\xc1S"},LOOKUP:{alias:"KERES"},LOWER:{alias:"KISBET\u0170"},MATCH:{alias:"HOL.VAN"},MAXA:{alias:"MAX2"},MDURATION:{alias:"MKAMAT\xc9RZ"},MEDIAN:{alias:"MEDI\xc1N"},MID:{alias:"K\xd6Z\xc9P"},MIDB:{alias:"K\xd6Z\xc9P2"},MINA:{alias:"MIN2"},MINUTE:{alias:"PERCEK"},MINVERSE:{alias:"INVERZ.M\xc1TRIX"},MIRR:{alias:"MEGT\xc9R\xdcL\xc9S"},MMULT:{alias:"MSZORZAT"},MOD:{alias:"MARAD\xc9K"},MODE:{alias:"M\xd3DUSZ"},"MODE.MULT":{alias:"M\xd3DUSZ.T\xd6BB"},"MODE.SNGL":{alias:"M\xd3DUSZ.EGY"},MONTH:{alias:"H\xd3NAP"},MROUND:{alias:"T\xd6BBSZ.KEREK\xcdT"},MULTINOMIAL:{alias:"SZORH\xc1NYFAKT"},MUNIT:{alias:"MM\xc1TRIX"},N:{alias:"S"},NA:{alias:"HI\xc1NYZIK"},"NEGBINOM.DIST":{alias:"NEGBINOM.ELOSZL\xc1S"},NEGBINOMDIST:{alias:"NEGBINOM.ELOSZL"},NETWORKDAYS:{alias:"\xd6SSZ.MUNKANAP"},"NETWORKDAYS.INTL":{alias:"\xd6SSZ.MUNKANAP.INTL"},NOMINAL:{alias:"N\xc9VLEGES"},"NORM.DIST":{alias:"NORM.ELOSZL\xc1S"},"NORM.INV":{alias:"NORM.INVERZ"},"NORM.S.DIST":{alias:"NORM.S.ELOSZL\xc1S"},"NORM.S.INV":{alias:"NORM.S.INVERZ"},NORMDIST:{alias:"NORM.ELOSZL"},NORMINV:{alias:"INVERZ.NORM"},NORMSDIST:{alias:"STNORMELOSZL"},NORMSINV:{alias:"INVERZ.STNORM"},NOT:{alias:"NEM"},NOW:{alias:"MOST"},NPER:{alias:"PER.SZ\xc1M"},NPV:{alias:"NM\xc9"},NUMBERVALUE:{alias:"SZ\xc1M\xc9RT\xc9K"},OCT2BIN:{alias:"OKT.BIN"},OCT2DEC:{alias:"OKT.DEC"},OCT2HEX:{alias:"OKT.HEX"},ODD:{alias:"P\xc1RATLAN"},ODDFPRICE:{alias:"ELT\xc9R\u0150.E\xc1R"},ODDFYIELD:{alias:"ELT\xc9R\u0150.EHOZAM"},ODDLPRICE:{alias:"ELT\xc9R\u0150.U\xc1R"},ODDLYIELD:{alias:"ELT\xc9R\u0150.UHOZAM"},OFFSET:{alias:"ELTOL\xc1S"},OR:{alias:"VAGY"},PDURATION:{alias:"KAMAT\xc9RZ.PER"},PERCENTILE:{alias:"PERCENTILIS"},"PERCENTILE.EXC":{alias:"PERCENTILIS.KIZ\xc1R"},"PERCENTILE.INC":{alias:"PERCENTILIS.TARTALMAZ"},PERCENTRANK:{alias:"SZ\xc1ZAL\xc9KRANG"},"PERCENTRANK.EXC":{alias:"SZ\xc1ZAL\xc9KRANG.KIZ\xc1R"},"PERCENTRANK.INC":{alias:"SZ\xc1ZAL\xc9KRANG.TARTALMAZ"},PERMUT:{alias:"VARI\xc1CI\xd3K"},PERMUTATIONA:{alias:"VARI\xc1CI\xd3K.ISM"},PHI:{alias:"FI"},PHONETIC:{alias:"FONETIKUS"},PMT:{alias:"R\xc9SZLET"},"POISSON.DIST":{alias:"POISSON.ELOSZL\xc1S"},POWER:{alias:"HATV\xc1NY"},PPMT:{alias:"PR\xc9SZLET"},PRICE:{alias:"\xc1R"},PRICEDISC:{alias:"\xc1R.LESZ\xc1M"},PRICEMAT:{alias:"\xc1R.LEJ\xc1RAT"},PROB:{alias:"VAL\xd3SZ\xcdN\u0170S\xc9G"},PRODUCT:{alias:"SZORZAT"},PROPER:{alias:"TN\xc9V"},PV:{alias:"M\xc9"},QUARTILE:{alias:"KVARTILIS"},"QUARTILE.EXC":{alias:"KVARTILIS.KIZ\xc1R"},"QUARTILE.INC":{alias:"KVARTILIS.TARTALMAZ"},QUOTIENT:{alias:"KV\xd3CIENS"},RADIANS:{alias:"RADI\xc1N"},RAND:{alias:"V\xc9L"},RANDBETWEEN:{alias:"V\xc9LETLEN.K\xd6Z\xd6TT"},RANK:{alias:"SORSZ\xc1M"},"RANK.AVG":{alias:"RANG.\xc1TL"},"RANK.EQ":{alias:"RANG.EGY"},RATE:{alias:"R\xc1TA"},RECEIVED:{alias:"KAPOTT"},"REGISTER.ID":{alias:"K\xdcLS\u0150.AZONOS\xcdT\xd3"},REPLACE:{alias:"CSERE"},REPLACEB:{alias:"CSERE2"},REPT:{alias:"SOKSZOR"},RIGHT:{alias:"JOBB"},RIGHTB:{alias:"JOBB2"},ROMAN:{alias:"R\xd3MAI"},ROUND:{alias:"KEREK\xcdT\xc9S"},ROUNDDOWN:{alias:"KEREK.LE"},ROUNDUP:{alias:"KEREK.FEL"},ROW:{alias:"SOR"},ROWS:{alias:"SOROK"},RRI:{alias:"MR"},RSQ:{alias:"RN\xc9GYZET"},RTD:{alias:"VIA"},SEARCH:{alias:"SZ\xd6VEG.KERES"},SEARCHB:{alias:"SZ\xd6VEG.KERES2"},SECOND:{alias:"MPERC"},SERIESSUM:{alias:"SOR\xd6SSZEG"},SHEET:{alias:"LAP"},SHEETS:{alias:"LAPOK"},SIGN:{alias:"EL\u0150JEL"},SKEW:{alias:"FERDES\xc9G"},"SKEW.P":{alias:"FERDES\xc9G.P"},SLN:{alias:"LCSA"},SLOPE:{alias:"MEREDEKS\xc9G"},SMALL:{alias:"KICSI"},SQRT:{alias:"GY\xd6K"},SQRTPI:{alias:"GY\xd6KPI"},STANDARDIZE:{alias:"NORMALIZ\xc1L\xc1S"},STDEV:{alias:"SZ\xd3R\xc1S"},"STDEV.P":{alias:"SZ\xd3R.S"},"STDEV.S":{alias:"SZ\xd3R.M"},STDEVA:{alias:"SZ\xd3R\xc1SA"},STDEVP:{alias:"SZ\xd3R\xc1SP"},STDEVPA:{alias:"SZ\xd3R\xc1SPA"},STEYX:{alias:"STHIBAYX"},SUBSTITUTE:{alias:"HELYETTE"},SUBTOTAL:{alias:"R\xc9SZ\xd6SSZEG"},SUM:{alias:"SZUM"},SUMIF:{alias:"SZUMHA"},SUMIFS:{alias:"SZUMHAT\xd6BB"},SUMPRODUCT:{alias:"SZORZAT\xd6SSZEG"},SUMSQ:{alias:"N\xc9GYZET\xd6SSZEG"},SUMX2MY2:{alias:"SZUMX2B\u0150LY2"},SUMX2PY2:{alias:"SZUMX2MEGY2"},SUMXMY2:{alias:"SZUMXB\u0150LY2"},SYD:{alias:"\xc9SZ\xd6"},"T.DIST":{alias:"T.ELOSZL"},"T.DIST.2T":{alias:"T.ELOSZL\xc1S.2SZ"},"T.DIST.RT":{alias:"T.ELOSZL\xc1S.JOBB"},"T.INV":{alias:"T.INVERZ"},"T.INV.2T":{alias:"T.INVERZ.2SZ"},"T.TEST":{alias:"T.PR\xd3B"},TBILLEQ:{alias:"KJEGY.EGYEN\xc9RT"},TBILLPRICE:{alias:"KJEGY.\xc1R"},TBILLYIELD:{alias:"KJEGY.HOZAM"},TDIST:{alias:"T.ELOSZL\xc1S"},TEXT:{alias:"SZ\xd6VEG"},TIME:{alias:"ID\u0150"},TIMEVALUE:{alias:"ID\u0150\xc9RT\xc9K"},TINV:{alias:"INVERZ.T"},TODAY:{alias:"MA"},TRANSPOSE:{alias:"TRANSZPON\xc1L\xc1S"},TRIM:{alias:"KIMETSZ"},TRIMMEAN:{alias:"R\xc9SZ\xc1TLAG"},TRUE:{alias:"IGAZ"},TRUNC:{alias:"CSONK"},TTEST:{alias:"T.PR\xd3BA"},TYPE:{alias:"T\xcdPUS"},UNICHAR:{alias:"UNIKARAKTER"},UPPER:{alias:"NAGYBET\u0170S"},VALUE:{alias:"\xc9RT\xc9K"},"VAR.P":{alias:"VAR.S"},"VAR.S":{alias:"VAR.M"},VDB:{alias:"\xc9CSRI"},VLOOKUP:{alias:"FKERES"},WEBSERVICE:{alias:"WEBSZOLG\xc1LTAT\xc1S"},WEEKDAY:{alias:"H\xc9T.NAPJA"},WEEKNUM:{alias:"H\xc9T.SZ\xc1MA"},"WEIBULL.DIST":{alias:"WEIBULL.ELOSZL\xc1S"},WORKDAY:{alias:"KALK.MUNKANAP"},"WORKDAY.INTL":{alias:"KALK.MUNKANAP.INTL"},XIRR:{alias:"XBMR"},XNPV:{alias:"XNJ\xc9"},XOR:{alias:"XVAGY"},YEAR:{alias:"\xc9V"},YEARFRAC:{alias:"T\xd6RT\xc9V"},YIELD:{alias:"HOZAM"},YIELDDISC:{alias:"HOZAM.LESZ\xc1M"},YIELDMAT:{alias:"HOZAM.LEJ\xc1RAT"},"Z.TEST":{alias:"Z.PR\xd3B"},ZTEST:{alias:"Z.PR\xd3BA"}},tableFunctionsMapping:{"#All":"#Mind","#Data":"#Adatok","#Headers":"#Fejl\xe9cek","#Totals":"#\xd6sszegek","#This row":"#Ez a sor"},clacErrorMapping:{"#NULL!":"#NULLA!","#DIV/0!":"#Z\xc9R\xd3OSZT\xd3!","#VALUE!":"#\xc9RT\xc9K!","#REF!":"#HIV!","#NAME?":"#N\xc9V?","#N/A!":"#HI\xc1NYZIK","#NUM!":"#SZ\xc1M!"},booleanMapping:{boolean_true:"IGAZ",boolean_false:"HAMIS"}}},"./src/languagePackages/res.Italian.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ABS:{alias:"ASS"},ACCRINT:{alias:"INT.MATURATO.PER"},ACCRINTM:{alias:"INT.MATURATO.SCAD"},ACOS:{alias:"ARCCOS"},ACOSH:{alias:"ARCCOSH"},ACOT:{alias:"ARCCOT"},ACOTH:{alias:"ARCCOTH"},ADDRESS:{alias:"INDIRIZZO"},AGGREGATE:{alias:"AGGREGA"},AMORDEGRC:{alias:"AMMORT.DEGR"},AMORLINC:{alias:"AMMORT.PER"},AND:{alias:"E"},ARABIC:{alias:"NUMERO.ARABO"},AREAS:{alias:"AREE"},ASIN:{alias:"ARCSEN"},ASINH:{alias:"ARCSENH"},ATAN:{alias:"ARCTAN"},ATAN2:{alias:"ARCTAN.2"},ATANH:{alias:"ARCTANH"},AVEDEV:{alias:"MEDIA.DEV"},AVERAGE:{alias:"MEDIA"},AVERAGEA:{alias:"MEDIA.VALORI"},AVERAGEIF:{alias:"MEDIA.SE"},AVERAGEIFS:{alias:"MEDIA.PI\xd9.SE"},BAHTTEXT:{alias:"BAHTTESTO"},BESSELI:{alias:"BESSEL.I"},BESSELJ:{alias:"BESSEL.J"},BESSELK:{alias:"BESSEL.K"},BESSELY:{alias:"BESSEL.Y"},"BETA.DIST":{alias:"DISTRIB.BETA.N"},"BETA.INV":{alias:"INV.BETA.N"},BETADIST:{alias:"DISTRIB.BETA"},BETAINV:{alias:"INV.BETA"},BIN2DEC:{alias:"BINARIO.DECIMALE"},BIN2HEX:{alias:"BINARIO.HEX"},BIN2OCT:{alias:"BINARIO.OCT,"},"BINOM.DIST":{alias:"DISTRIB.BINOM.N"},"BINOM.DIST.RANGE":{alias:"INTERVALLO.DISTRIB.BINOM.N"},"BINOM.INV":{alias:"INV.BINOM"},BINOMDIST:{alias:"DISTRIB.BINOM"},BITLSHIFT:{alias:"BIT.SPOSTA.SX"},BITRSHIFT:{alias:"BIT.SPOSTA.DX"},CALL:{alias:"RICHIAMA"},CEILING:{alias:"ARROTONDA.ECCESSO"},"CEILING.MATH":{alias:"ARROTONDA.ECCESSO.MAT"},"CEILING.PRECISE":{alias:"ARROTONDA.ECCESSO.PRECISA"},CELL:{alias:"CELLA"},CHAR:{alias:"CODICE.CARATT"},CHIDIST:{alias:"DISTRIB.CHI"},CHIINV:{alias:"INV.CHI"},"CHISQ.DIST":{alias:"DISTRIB.CHIQUAD"},"CHISQ.DIST.RT":{alias:"DISTRIB.CHIQUAD.DS"},"CHISQ.INV":{alias:"INV.CHIQUAD"},"CHISQ.INV.RT":{alias:"INV.CHI.QUAD.DS"},"CHISQ.TEST":{alias:"TEST.CHIQUAD"},CHITEST:{alias:"TEST.CHI"},CHOOSE:{alias:"SCEGLI"},CLEAN:{alias:"LIBERA"},CODE:{alias:"CODICE"},COLUMN:{alias:"RIF.COLONNA"},COLUMNS:{alias:"COLONNE"},COMBIN:{alias:"COMBINAZIONE"},COMBINA:{alias:"COMBINAZIONE.VALORI"},COMPLEX:{alias:"COMPLESSO"},CONCATENATE:{alias:"CONCATENA"},CONFIDENCE:{alias:"CONFIDENZA"},"CONFIDENCE.NORM":{alias:"CONFIDENZA.NORM"},"CONFIDENCE.T":{alias:"CONFIDENZA.T"},CONVERT:{alias:"CONVERTI"},CORREL:{alias:"CORRELAZIONE"},COUNT:{alias:"CONTA.NUMERI"},COUNTA:{alias:"CONTA.VALORI"},COUNTBLANK:{alias:"CONTA.VUOTE"},COUNTIF:{alias:"CONTA.SE"},COUNTIFS:{alias:"CONTA.PI\xd9.SE"},COUPDAYBS:{alias:"GIORNI.CED.INIZ.LIQ"},COUPDAYS:{alias:"GIORNI.CED"},COUPDAYSNC:{alias:"GIORNI.CED.NUOVA"},COUPNCD:{alias:"DATA.CED.SUCC"},COUPNUM:{alias:"NUM.CED"},COUPPCD:{alias:"DATA.CED.PREC"},COVAR:{alias:"COVARIANZA"},"COVARIANCE.P":{alias:"COVARIANZA.P"},"COVARIANCE.S":{alias:"COVARIANZA.C"},CRITBINOM:{alias:"CRIT.BINOM"},CUBEKPIMEMBER:{alias:"MEMBRO.KPI.CUBO"},CUBEMEMBER:{alias:"MEMBRO.CUBO"},CUBEMEMBERPROPERTY:{alias:"PROPRIET\xc0.MEMBRO.CUBO"},CUBERANKEDMEMBER:{alias:"MEMBRO.CUBO.CON.RANGO"},CUBESET:{alias:"SET.CUBO"},CUBESETCOUNT:{alias:"CONTA.SET.CUBO"},CUBEVALUE:{alias:"VALORE.CUBO"},CUMIPMT:{alias:"INT.CUMUL"},CUMPRINC:{alias:"CAP.CUM"},DATE:{alias:"DATA"},DATEVALUE:{alias:"DATA.VALORE"},DAVERAGE:{alias:"DB.MEDIA"},DAY:{alias:"GIORNO"},DAYS:{alias:"GIORNI"},DAYS360:{alias:"GIORNO360"},DB:{alias:"AMMORT.FISSO"},DBCS:{alias:"ORDINAMENTO.DBCS"},DCOUNT:{alias:"DB.CONTA.NUMERI"},DCOUNTA:{alias:"DB.CONTA.VALORI"},DDB:{alias:"AMMORT"},DEC2BIN:{alias:"DECIMALE.BINARIO"},DEC2HEX:{alias:"DECIMALE.HEX"},DEC2OCT:{alias:"DECIMALE.OCT"},DECIMAL:{alias:"DECIMALE"},DEGREES:{alias:"GRADI"},DEVSQ:{alias:"DEV.Q"},DGET:{alias:"DB.VALORI"},DISC:{alias:"TASSO.SCONTO"},DMAX:{alias:"DB.MAX"},DMIN:{alias:"DB.MIN"},DOLLAR:{alias:"VALUTA"},DOLLARDE:{alias:"VALUTA.DEC"},DOLLARFR:{alias:"VALUTA.FRAZ"},DPRODUCT:{alias:"DB.PRODOTTO"},DSTDEV:{alias:"DB.DEV.ST"},DSTDEVP:{alias:"DB.DEV.ST.POP"},DSUM:{alias:"DB.SOMMA"},DURATION:{alias:"DURATA"},DVAR:{alias:"DB.VAR"},DVARP:{alias:"DB.VAR.POP"},EDATE:{alias:"DATA.MESE"},EFFECT:{alias:"EFFETTIVO"},ENCODEURL:{alias:"CODIFICA.URL"},EOMONTH:{alias:"FINE.MESE"},ERF:{alias:"FUNZ.ERRORE"},"ERF.PRECISE":{alias:"FUNZ.ERRORE.PRECISA"},ERFC:{alias:"FUNZ.ERRORE.COMP"},"ERFC.PRECISE":{alias:"FUNZ.ERRORE.COMP.PRECISA"},"ERROR.TYPE":{alias:"ERRORE.TIPO"},EVEN:{alias:"PARI"},EXACT:{alias:"IDENTICO"},"EXPON.DIST":{alias:"DISTRIB.EXP.N"},EXPONDIST:{alias:"DISTRIB.EXP"},"F.DIST":{alias:"DISTRIBF"},"F.DIST.RT":{alias:"DISTRIB.F.DS"},"F.INV":{alias:"INVF"},"F.INV.RT":{alias:"INV.F.DS"},"F.TEST":{alias:"TESTF"},FACT:{alias:"FATTORIALE"},FACTDOUBLE:{alias:"FATT.DOPPIO"},FALSE:{alias:"FALSO"},FDIST:{alias:"DISTRIB.F"},FILTERXML:{alias:"FILTRO.XML"},FIND:{alias:"TROVA"},FINDB:{alias:"TROVA.B"},FINV:{alias:"INV.F"},FISHERINV:{alias:"INV.FISHER"},FIXED:{alias:"FISSO"},FLOOR:{alias:"ARROTONDA.DIFETTO"},"FLOOR.MATH":{alias:"ARROTONDA.DIFETTO.MAT"},"FLOOR.PRECISE":{alias:"ARROTONDA.DIFETTO.PRECISA"},FORECAST:{alias:"PREVISIONE"},FORMULATEXT:{alias:"TESTO.FORMULA"},FREQUENCY:{alias:"FREQUENZA"},FTEST:{alias:"TEST.F"},FV:{alias:"VAL.FUT"},FVSCHEDULE:{alias:"VAL.FUT.CAPITALE"},"GAMMA.DIST":{alias:"DISTRIB.GAMMA.N"},"GAMMA.INV":{alias:"INV.GAMMA.N"},GAMMADIST:{alias:"DISTRIB.GAMMA"},GAMMAINV:{alias:"INV.GAMMA"},GAMMALN:{alias:"LN.GAMMA"},"GAMMALN.PRECISE":{alias:"LN.GAMMA.PRECISO"},GCD:{alias:"MCD"},GEOMEAN:{alias:"MEDIA.GEOMETRICA"},GESTEP:{alias:"SOGLIA"},GETPIVOTDATA:{alias:"INFO.DATI.TAB.PIVOT"},GROWTH:{alias:"CRESCITA"},HARMEAN:{alias:"MEDIA.ARMONICA"},HEX2BIN:{alias:"HEX.BINARIO"},HEX2DEC:{alias:"HEX.DECIMALE"},HEX2OCT:{alias:"HEX.OCT"},HLOOKUP:{alias:"CERCA.ORIZZ"},HOUR:{alias:"ORA"},HYPERLINK:{alias:"COLLEG.IPERTESTUALE"},"HYPGEOM.DIST":{alias:"DISTRIB.IPERGEOM.N"},HYPGEOMDIST:{alias:"DISTRIB.IPERGEOM"},IF:{alias:"SE"},IFERROR:{alias:"SE.ERRORE"},IFNA:{alias:"SE.NON.DISP."},IMABS:{alias:"COMP.MODULO"},IMAGINARY:{alias:"COMP.IMMAGINARIO"},IMARGUMENT:{alias:"COMP.ARGOMENTO"},IMCONJUGATE:{alias:"COMP.CONIUGATO"},IMCOS:{alias:"COMP.COS"},IMCOSH:{alias:"COMP.COSH"},IMCOT:{alias:"COMP.COT"},IMCSC:{alias:"COMP.CSC"},IMCSCH:{alias:"COMP.CSCH"},IMDIV:{alias:"COMP.DIV"},IMEXP:{alias:"COMP.EXP"},IMLN:{alias:"COMP.LN"},IMLOG10:{alias:"COMP.LOG10"},IMLOG2:{alias:"COMP.LOG2"},IMPOWER:{alias:"COMP.POTENZA"},IMPRODUCT:{alias:"COMP.PRODOTTO"},IMREAL:{alias:"COMP.PARTE.REALE"},IMSEC:{alias:"COMP.SEC"},IMSECH:{alias:"COMP.SECH"},IMSIN:{alias:"COMP.SEN"},IMSINH:{alias:"COMP.SENH"},IMSQRT:{alias:"COMP.RADQ"},IMSUB:{alias:"COMP.DIFF"},IMSUM:{alias:"COMP.SOMMA"},IMTAN:{alias:"COMP.TAN"},INDEX:{alias:"INDICE"},INDIRECT:{alias:"INDIRETTO"},INTERCEPT:{alias:"INTERCETTA"},INTRATE:{alias:"TASSO.INT"},IPMT:{alias:"INTERESSI"},IRR:{alias:"TIR.COST"},ISBLANK:{alias:"VAL.VUOTO"},ISERR:{alias:"VAL.ERR"},ISERROR:{alias:"VAL.ERRORE"},ISEVEN:{alias:"VAL.PARI"},ISFORMULA:{alias:"VAL.FORMULA"},ISLOGICAL:{alias:"VAL.LOGICO"},ISNA:{alias:"VAL.NON.DISP"},ISNONTEXT:{alias:"VAL.NON.TESTO"},ISNUMBER:{alias:"VAL.NUMERO"},"ISO.CEILING":{alias:"ISO.ARROTONDA.ECCESSO"},ISODD:{alias:"VAL.DISPARI"},ISOWEEKNUM:{alias:"NUM.SETTIMANA.ISO"},ISPMT:{alias:"INTERESSE.RATA"},ISREF:{alias:"VAL.RIF"},ISTEXT:{alias:"VAL.TESTO"},KURT:{alias:"CURTOSI"},LARGE:{alias:"GRANDE"},LCM:{alias:"MCM"},LEFT:{alias:"SINISTRA"},LEFTB:{alias:"SINISTRAB"},LEN:{alias:"LUNGHEZZA"},LENB:{alias:"LUNGB"},LINEST:{alias:"REGR.LIN"},LOGEST:{alias:"REGR.LOG"},LOGINV:{alias:"INV.LOGNORM"},"LOGNORM.DIST":{alias:"DISTRIB.LOGNORM.N"},"LOGNORM.INV":{alias:"INV.LOGNORM.N"},LOGNORMDIST:{alias:"DISTRIB.LOGNORM"},LOOKUP:{alias:"CERCA"},LOWER:{alias:"MINUSC"},MATCH:{alias:"CONFRONTA"},MAXA:{alias:"MAX.VALORI"},MDETERM:{alias:"MATR.DETERM"},MDURATION:{alias:"DURATA.M"},MEDIAN:{alias:"MEDIANA"},MID:{alias:"STRINGA.ESTRAI"},MIDB:{alias:"MEDIA.B"},MINA:{alias:"MIN.VALORI"},MINUTE:{alias:"MINUTO"},MINVERSE:{alias:"MATR.INVERSA"},MIRR:{alias:"TIR.VAR"},MMULT:{alias:"MATR.PRODOTTO"},MOD:{alias:"RESTO"},MODE:{alias:"MODA"},"MODE.MULT":{alias:"MODA.MULT"},"MODE.SNGL":{alias:"MODA.SNGL"},MONTH:{alias:"MESE"},MROUND:{alias:"ARROTONDA.MULTIPLO"},MULTINOMIAL:{alias:"MULTINOMIALE"},MUNIT:{alias:"MATR.UNIT"},N:{alias:"NUM"},NA:{alias:"NON.DISP"},"NEGBINOM.DIST":{alias:"DISTRIB.BINOM.NEG.N"},NEGBINOMDIST:{alias:"DISTRIB.BINOM.NEG"},NETWORKDAYS:{alias:"GIORNI.LAVORATIVI.TOT"},"NETWORKDAYS.INTL":{alias:"GIORNI.LAVORATIVI.TOT.INTL"},NOMINAL:{alias:"NOMINALE"},"NORM.DIST":{alias:"DISTRIB.NORM.N"},"NORM.INV":{alias:"INV.NORM.N"},"NORM.S.DIST":{alias:"DISTRIB.NORM.ST.N"},"NORM.S.INV":{alias:"INV.NORM.S"},NORMDIST:{alias:"DISTRIB.NORM"},NORMINV:{alias:"INV.NORM"},NORMSDIST:{alias:"DISTRIB.NORM.ST"},NORMSINV:{alias:"INV.NORM.ST"},NOT:{alias:"NON"},NOW:{alias:"ADESSO"},NPER:{alias:"NUM.RATE"},NPV:{alias:"VAN"},NUMBERVALUE:{alias:"NUMERO.VALORE"},OCT2BIN:{alias:"OCT.BINARIO"},OCT2DEC:{alias:"OCT.DECIMALE"},OCT2HEX:{alias:"OCT.HEX"},ODD:{alias:"DISPARI"},ODDFPRICE:{alias:"PREZZO.PRIMO.IRR"},ODDFYIELD:{alias:"REND.PRIMO.IRR"},ODDLPRICE:{alias:"PREZZO.ULTIMO.IRR"},ODDLYIELD:{alias:"REND.ULTIMO.IRR"},OFFSET:{alias:"SCARTO"},OR:{alias:"O"},PDURATION:{alias:"DURATA.P"},"PERCENTILE.EXC":{alias:"ESC.PERCENTILE"},"PERCENTILE.INC":{alias:"INC.PERCENTILE"},PERCENTRANK:{alias:"PERCENT.RANGO"},"PERCENTRANK.EXC":{alias:"ESC.PERCENT.RANGO"},"PERCENTRANK.INC":{alias:"INC.PERCENT.RANGO"},PERMUT:{alias:"PERMUTAZIONE"},PERMUTATIONA:{alias:"PERMUTAZIONE.VALORI"},PHONETIC:{alias:"FURIGANA"},PI:{alias:"PI.GRECO"},PMT:{alias:"RATA"},"POISSON.DIST":{alias:"DISTRIB.POISSON"},POWER:{alias:"POTENZA"},PPMT:{alias:"P.RATA"},PRICE:{alias:"PREZZO"},PRICEDISC:{alias:"PREZZO.SCONT"},PRICEMAT:{alias:"PREZZO.SCAD"},PROB:{alias:"PROBABILIT\xc0"},PRODUCT:{alias:"PRODOTTO"},PROPER:{alias:"MAIUSC.INIZ"},PV:{alias:"VA"},"QUARTILE.EXC":{alias:"ESC.QUARTILE"},"QUARTILE.INC":{alias:"INC.QUARTILE"},QUOTIENT:{alias:"QUOZIENTE"},RADIANS:{alias:"RADIANTI"},RAND:{alias:"CASUALE"},RANDBETWEEN:{alias:"CASUALE.TRA"},RANK:{alias:"RANGO"},"RANK.AVG":{alias:"RANGO.MEDIA"},"RANK.EQ":{alias:"RANGO.UG"},RATE:{alias:"TASSO"},RECEIVED:{alias:"RICEV.SCAD"},"REGISTER.ID":{alias:"IDENTIFICATORE.REGISTRO"},REPLACE:{alias:"RIMPIAZZA"},REPLACEB:{alias:"SOSTITUISCI.B"},REPT:{alias:"RIPETI"},RIGHT:{alias:"DESTRA"},RIGHTB:{alias:"DESTRA.B"},ROMAN:{alias:"ROMANO"},ROUND:{alias:"ARROTONDA"},ROUNDDOWN:{alias:"ARROTONDA.PER.DIF"},ROUNDUP:{alias:"ARROTONDA.PER.ECC"},ROW:{alias:"RIF.RIGA"},ROWS:{alias:"RIGHE"},RRI:{alias:"RIT.INVEST.EFFETT"},RSQ:{alias:"RQ"},RTD:{alias:"DATITEMPOREALE"},SEARCH:{alias:"RICERCA"},SEARCHB:{alias:"CERCA.B"},SECOND:{alias:"SECONDO"},SERIESSUM:{alias:"SOMMA.SERIE"},SHEET:{alias:"FOGLIO"},SHEETS:{alias:"FOGLI"},SIGN:{alias:"SEGNO"},SIN:{alias:"SEN"},SINH:{alias:"SENH"},SKEW:{alias:"ASIMMETRIA"},"SKEW.P":{alias:"ASIMMETRIA.P"},SLN:{alias:"AMMORT.COST"},SLOPE:{alias:"PENDENZA"},SMALL:{alias:"PICCOLO"},SQRT:{alias:"RADQ"},SQRTPI:{alias:"RADQ.PI.GRECO"},STANDARDIZE:{alias:"NORMALIZZA"},STDEV:{alias:"DEV.ST"},"STDEV.P":{alias:"DEV.ST.P"},"STDEV.S":{alias:"DEV.ST.C"},STDEVA:{alias:"DEV.ST.VALORI"},STDEVP:{alias:"DEV.ST.POP"},STDEVPA:{alias:"DEV.ST.POP.VALORI"},STEYX:{alias:"ERR.STD.YX"},SUBSTITUTE:{alias:"SOSTITUISCI"},SUBTOTAL:{alias:"SUBTOTALE"},SUM:{alias:"SOMMA"},SUMIF:{
alias:"SOMMA.SE"},SUMIFS:{alias:"SOMMA.PI\xd9.SE"},SUMPRODUCT:{alias:"MATR.SOMMA.PRODOTTO"},SUMSQ:{alias:"SOMMA.Q"},SUMX2MY2:{alias:"SOMMA.DIFF.Q"},SUMX2PY2:{alias:"SOMMA.SOMMA.Q"},SUMXMY2:{alias:"SOMMA.Q.DIFF"},SYD:{alias:"AMMORT.ANNUO"},"T.DIST":{alias:"DISTRIB.T.N"},"T.DIST.2T":{alias:"DISTRIB.T.2T"},"T.DIST.RT":{alias:"DISTRIB.T.DS"},"T.INV":{alias:"INVT"},"T.INV.2T":{alias:"INV.T.2T"},"T.TEST":{alias:"TESTT"},TBILLEQ:{alias:"BOT.EQUIV"},TBILLPRICE:{alias:"BOT.PREZZO"},TBILLYIELD:{alias:"BOT.REND"},TDIST:{alias:"DISTRIB.T"},TEXT:{alias:"TESTO"},TIME:{alias:"ORARIO"},TIMEVALUE:{alias:"ORARIO.VALORE"},TINV:{alias:"INV.T"},TODAY:{alias:"OGGI"},TRANSPOSE:{alias:"MATR.TRASPOSTA"},TREND:{alias:"TENDENZA"},TRIM:{alias:"ANNULLA.SPAZI"},TRIMMEAN:{alias:"MEDIA.TRONCATA"},TRUE:{alias:"VERO"},TRUNC:{alias:"TRONCA"},TTEST:{alias:"TEST.T"},TYPE:{alias:"TIPO"},UNICHAR:{alias:"CARATT.UNI"},UPPER:{alias:"MAIUSC"},VALUE:{alias:"VALORE"},"VAR.S":{alias:"VAR.C"},VARA:{alias:"VAR.VALORI"},VARP:{alias:"VAR.POP"},VARPA:{alias:"VAR.POP.VALORI"},VDB:{alias:"AMMORT.VAR"},VLOOKUP:{alias:"CERCA.VERT"},WEBSERVICE:{alias:"SERVIZIO.WEB"},WEEKDAY:{alias:"GIORNO.SETTIMANA"},WEEKNUM:{alias:"NUM.SETTIMANA"},"WEIBULL.DIST":{alias:"DISTRIB.WEIBULL"},WORKDAY:{alias:"GIORNO.LAVORATIVO"},"WORKDAY.INTL":{alias:"GIORNO.LAVORATIVO.INTL"},XIRR:{alias:"TIR.X"},XNPV:{alias:"VAN.X"},YEAR:{alias:"ANNO"},YEARFRAC:{alias:"FRAZIONE.ANNO"},YIELD:{alias:"REND"},YIELDDISC:{alias:"REND.TITOLI.SCONT"},YIELDMAT:{alias:"REND.SCAD"},"Z.TEST":{alias:"TESTZ"},ZTEST:{alias:"TEST.Z"}},tableFunctionsMapping:{"#All":"#Tutti","#Data":"#Dati","#Headers":"#Intestazioni","#Totals":"#Totali","#this row":"#Questa riga"},clacErrorMapping:{"#NULL!":"#NULL!","#DIV/0!":"#DIV/0!","#VALUE!":"#VALORE!","#REF!":"#RIF!","#NAME?":"#NOME?","#N/A!":"#N/D","#NUM!":"#NUM!"},booleanMapping:{boolean_true:"VERO",boolean_false:"FALSO"}}},"./src/languagePackages/res.Japanese.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./src/languagePackages/commonFunction.ts");b.resource={builtInFunctionsMapping:{FINDB:{specialFun:d.tx_findB},LEFTB:{specialFun:d.tx_leftB},RIGHTB:{specialFun:d.tx_rightB},MIDB:{specialFun:d.tx_midB},LENB:{specialFun:d.tx_lenB},REPLACEB:{specialFun:d.tx_replaceB},SEARCHB:{specialFun:d.tx_searchB}},tableFunctionsMapping:{"#All":"#\u3059\u3079\u3066","#Data":"#\u30c7\u30fc\u30bf","#Headers":"#\u898b\u51fa\u3057]","#Totals":"#\u96c6\u8a08","#This row":"@"}}},"./src/languagePackages/res.Korean.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./src/languagePackages/commonFunction.ts");b.resource={builtInFunctionsMapping:{FINDB:{specialFun:d.tx_findB},LEFTB:{specialFun:d.tx_leftB},RIGHTB:{specialFun:d.tx_rightB},MIDB:{specialFun:d.tx_midB},LENB:{specialFun:d.tx_lenB},REPLACEB:{specialFun:d.tx_replaceB},SEARCHB:{specialFun:d.tx_searchB}},tableFunctionsMapping:{"#All":"#\ubaa8\ub450","#Data":"#\ub370\uc774\ud130","#Headers":"#\uba38\ub9ac\uae00","#Totals":"#\ud589 \ud5e4\ub354","#This row":"@"}}},"./src/languagePackages/res.Norwegian.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"P\xc5L\xd8PT.PERIODISK.RENTE"},ACCRINTM:{alias:"P\xc5L\xd8PT.FORFALLSRENTE"},ACOS:{alias:"ARCCOS"},ACOSH:{alias:"ARCCOSH"},ADDRESS:{alias:"ADRESSE"},AGGREGATE:{alias:"MENGDE"},AND:{alias:"OG"},ARABIC:{alias:"ARABISK"},AREAS:{alias:"OMR\xc5DER"},ASC:{alias:"STIGENDE"},ASIN:{alias:"ARCSIN"},ASINH:{alias:"ARCSINH"},ATAN:{alias:"ARCTAN"},ATAN2:{alias:"ARCTAN2"},ATANH:{alias:"ARCTANH"},AVEDEV:{alias:"GJENNOMSNITTSAVVIK"},AVERAGE:{alias:"GJENNOMSNITT"},AVERAGEA:{alias:"GJENNOMSNITTA"},AVERAGEIF:{alias:"GJENNOMSNITTHVIS"},AVERAGEIFS:{alias:"GJENNOMSNITT.HVIS.SETT"},BAHTTEXT:{alias:"BAHTTEKST"},BASE:{alias:"GRUNNTALL"},"BETA.DIST":{alias:"BETA.FORDELING"},BETADIST:{alias:"BETA.FORDELING"},BETAINV:{alias:"INVERS.BETA.FORDELING"},BIN2DEC:{alias:"BINTILDES"},BIN2HEX:{alias:"BINTILHEKS"},BIN2OCT:{alias:"BINTILOKT"},"BINOM.DIST":{alias:"BINOM.FORDELING"},"BINOM.DIST.RANGE":{alias:"BINOM.FORDELING.OMR\xc5DE"},BINOMDIST:{alias:"BINOM.FORDELING"},BITAND:{alias:"BITOG"},BITLSHIFT:{alias:"BITVFORSKYV"},BITOR:{alias:"BITELLER"},BITRSHIFT:{alias:"BITHFORSKYV"},BITXOR:{alias:"BITEKSKLUSIVELLER"},CALL:{alias:"ANROP"},CEILING:{alias:"AVRUND.GJELDENDE.MULTIPLUM"},"CEILING.MATH":{alias:"AVRUND.GJELDENDE.MULTIPLUM.OPP.MATEMATISK"},"CEILING.PRECISE":{alias:"AVRUND.GJELDENDE.MULTIPLUM.PRESIS"},CELL:{alias:"CELLE"},CHAR:{alias:"TEGNKODE"},CHIDIST:{alias:"KJI.FORDELING"},CHIINV:{alias:"INVERS.KJI.FORDELING"},"CHISQ.DIST":{alias:"KJIKVADRAT.FORDELING"},"CHISQ.DIST.RT":{alias:"KJIKVADRAT.FORDELING.H"},"CHISQ.INV":{alias:"KJIKVADRAT.INV"},"CHISQ.INV.RT":{alias:"KJIKVADRAT.INV.H"},"CHISQ.TEST":{alias:"KJIKVADRAT.TEST"},CHITEST:{alias:"KJI.TEST"},CHOOSE:{alias:"VELG"},CLEAN:{alias:"RENSK"},CODE:{alias:"KODE"},COLUMN:{alias:"KOLONNE"},COLUMNS:{alias:"KOLONNER"},COMBIN:{alias:"KOMBINASJON"},COMBINA:{alias:"KOMBINASJONA"},COMPLEX:{alias:"KOMPLEKS"},CONCATENATE:{alias:"KJEDE.SAMMEN"},CONFIDENCE:{alias:"KONFIDENS"},"CONFIDENCE.NORM":{alias:"KONFIDENS.NORM"},"CONFIDENCE.T":{alias:"KONFIDENS.T"},CONVERT:{alias:"KONVERTER"},CORREL:{alias:"KORRELASJON"},COUNT:{alias:"ANTALL"},COUNTA:{alias:"ANTALLA"},COUNTBLANK:{alias:"TELLBLANKE"},COUNTIF:{alias:"ANTALL.HVIS"},COUNTIFS:{alias:"ANTALL.HVIS.SETT"},COUPDAYBS:{alias:"OBLIG.DAGER.FF"},COUPDAYS:{alias:"OBLIG.DAGER"},COUPDAYSNC:{alias:"OBLIG.DAGER.NF"},COUPNCD:{alias:"OBLIG.DAGER.EF"},COUPNUM:{alias:"OBLIG.ANTALL"},COUPPCD:{alias:"OBLIG.DAG.FORRIGE"},COVAR:{alias:"KOVARIANS"},"COVARIANCE.P":{alias:"KOVARIANS.P"},"COVARIANCE.S":{alias:"KOVARIANS.S"},CRITBINOM:{alias:"GRENSE.BINOM"},CUBEKPIMEMBER:{alias:"KUBEKPIMEDLEM"},CUBEMEMBER:{alias:"KUBEMEDLEM"},CUBEMEMBERPROPERTY:{alias:"KUBEMEDLEMEGENSKAP"},CUBERANKEDMEMBER:{alias:"KUBERANGERTMEDLEM"},CUBESET:{alias:"KUBESETT"},CUBESETCOUNT:{alias:"KUBESETTANTALL"},CUBEVALUE:{alias:"KUBEVERDI"},CUMIPMT:{alias:"SAMLET.RENTE"},CUMPRINC:{alias:"SAMLET.HOVEDSTOL"},DATE:{alias:"DATO"},DATEVALUE:{alias:"DATOVERDI"},DAVERAGE:{alias:"DGJENNOMSNITT"},DAY:{alias:"DAG"},DAYS:{alias:"DAGER"},DAYS360:{alias:"DAGER360"},DB:{alias:"DAVSKR"},DCOUNT:{alias:"DANTALL"},DCOUNTA:{alias:"DANTALLA"},DDB:{alias:"DEGRAVS"},DEC2BIN:{alias:"DESTILBIN"},DEC2HEX:{alias:"DESTILHEKS"},DEC2OCT:{alias:"DESTILOKT"},DECIMAL:{alias:"DESIMAL"},DEGREES:{alias:"GRADER"},DEVSQ:{alias:"AVVIK.KVADRERT"},DGET:{alias:"DHENT"},DISC:{alias:"DISKONTERT"},DMAX:{alias:"DST\xd8RST"},DOLLAR:{alias:"VALUTA"},DOLLARFR:{alias:"DOLLARBR"},DPRODUCT:{alias:"DPRODUKT"},DSTDEV:{alias:"DSTDAV"},DSTDEVP:{alias:"DSTAVP"},DSUM:{alias:"DSUMMER"},DURATION:{alias:"VARIGHET"},DVAR:{alias:"DVARIANS"},DVARP:{alias:"DVARIANSP"},EDATE:{alias:"DAG.ETTER"},EFFECT:{alias:"EFFEKTIV.RENTE"},EOMONTH:{alias:"M\xc5NEDSSLUTT"},ERF:{alias:"FEILF"},"ERF.PRECISE":{alias:"FEILF.PRESIS"},ERFC:{alias:"FEILFK"},"ERFC.PRECISE":{alias:"FEILFK.PRESIS"},"ERROR.TYPE":{alias:"FEIL.TYPE"},EUROCONVERT:{alias:"EUROKONVERTERING"},EVEN:{alias:"AVRUND.TIL.PARTALL"},EXACT:{alias:"EKSAKT"},EXP:{alias:"EKSP"},"EXPON.DIST":{alias:"EKSP.FORDELING"},EXPONDIST:{alias:"EKSP.FORDELING"},"F.DIST":{alias:"F.FORDELING"},"F.DIST.RT":{alias:"F.FORDELING.H"},"F.INV.RT":{alias:"F.INV.H"},FACT:{alias:"FAKULTET"},FACTDOUBLE:{alias:"DOBBELFAKT"},FALSE:{alias:"USANN"},FDIST:{alias:"FFORDELING"},FILTERXML:{alias:"FILTRERXML"},FIND:{alias:"FINN"},FINDB:{alias:"FINNB"},FINV:{alias:"FFORDELING.INVERS"},FIXED:{alias:"FASTSATT"},FLOOR:{alias:"AVRUND.GJELDENDE.MULTIPLUM.NED"},"FLOOR.MATH":{alias:"AVRUND.GJELDENDE.MULTIPLUM.NED.MATEMATISK"},"FLOOR.PRECISE":{alias:"AVRUND.GJELDENDE.MULTIPLUM.NED.PRESIS"},FORECAST:{alias:"PROGNOSE"},FORMULATEXT:{alias:"FORMELTEKST"},FREQUENCY:{alias:"FREKVENS"},FV:{alias:"SLUTTVERDI"},FVSCHEDULE:{alias:"SVPLAN"},"GAMMA.DIST":{alias:"GAMMA.FORDELING"},GAMMADIST:{alias:"GAMMAFORDELING"},"GAMMALN.PRECISE":{alias:"GAMMALN.PRESIS"},GCD:{alias:"SFF"},GEOMEAN:{alias:"GJENNOMSNITT.GEOMETRISK"},GESTEP:{alias:"GRENSEVERDI"},GETPIVOTDATA:{alias:"HENTPIVOTDATA"},GROWTH:{alias:"VEKST"},HARMEAN:{alias:"GJENNOMSNITT.HARMONISK"},HEX2BIN:{alias:"HEKSTILBIN"},HEX2DEC:{alias:"HEKSTILDES"},HEX2OCT:{alias:"HEKSTILOKT"},HLOOKUP:{alias:"FINN.KOLONNE"},HOUR:{alias:"TIME"},HYPERLINK:{alias:"HYPERKOBLING"},"HYPGEOM.DIST":{alias:"HYPGEOM.FORDELING"},HYPGEOMDIST:{alias:"HYPGEOM.FORDELING"},IF:{alias:"HVIS"},IFERROR:{alias:"HVISFEIL"},IFNA:{alias:"HVIS.IT"},IMAGINARY:{alias:"IMAGIN\xc6R"},IMCONJUGATE:{alias:"IMKONJUGERT"},IMEXP:{alias:"IMEKSP"},IMPOWER:{alias:"IMOPPH\xd8Y"},IMPRODUCT:{alias:"IMPRODUKT"},IMREAL:{alias:"IMREELL"},IMSQRT:{alias:"IMROT"},IMSUM:{alias:"IMSUMMER"},INDEX:{alias:"INDEKS"},INDIRECT:{alias:"INDIREKTE"},INT:{alias:"HELTALL"},INTERCEPT:{alias:"SKJ\xc6RINGSPUNKT"},INTRATE:{alias:"RENTESATS"},IPMT:{alias:"RAVDRAG"},IRR:{alias:"IR"},ISBLANK:{alias:"ERTOM"},ISERR:{alias:"ERF"},ISERROR:{alias:"ERFEIL"},ISEVEN:{alias:"ERPARTALL"},ISFORMULA:{alias:"ERFORMEL"},ISLOGICAL:{alias:"ERLOGISK"},ISNA:{alias:"ERIT"},ISNONTEXT:{alias:"ERIKKETEKST"},ISNUMBER:{alias:"ERTALL"},"ISO.CEILING":{alias:"ISO.AVRUND.GJELDENDE.MULTIPLUM"},ISODD:{alias:"ERODDETALL"},ISOWEEKNUM:{alias:"ISOUKENR"},ISPMT:{alias:"ER.AVDRAG"},ISREF:{alias:"ERREF"},ISTEXT:{alias:"ERTEKST"},LARGE:{alias:"N.ST\xd8RST"},LCM:{alias:"MFM"},LEFT:{alias:"VENSTRE"},LEFTB:{alias:"VENSTREB"},LEN:{alias:"LENGDE"},LENB:{alias:"LENGDEB"},LINEST:{alias:"RETTLINJE"},LOGEST:{alias:"KURVE"},"LOGNORM.DIST":{alias:"LOGNORM.FORDELING"},LOGNORMDIST:{alias:"LOGNORMFORD"},LOOKUP:{alias:"SL\xc5.OPP"},LOWER:{alias:"SM\xc5"},MATCH:{alias:"SAMMENLIGNE"},MAX:{alias:"ST\xd8RST"},MAXA:{alias:"MAKSA"},MDURATION:{alias:"MVARIGHET"},MID:{alias:"DELTEKST"},MIDB:{alias:"DELTEKSTB"},MINUTE:{alias:"MINUTT"},MINVERSE:{alias:"MINVERS"},MIRR:{alias:"MODIR"},MOD:{alias:"REST"},MODE:{alias:"MODUS"},"MODE.MULT":{alias:"MODUS.MULT"},"MODE.SNGL":{alias:"MODUS.SNGL"},MONTH:{alias:"M\xc5NED"},MROUND:{alias:"MRUND"},MULTINOMIAL:{alias:"MULTINOMINELL"},MUNIT:{alias:"MENHET"},NA:{alias:"IT"},"NEGBINOM.DIST":{alias:"NEGBINOM.FORDELING"},NEGBINOMDIST:{alias:"NEGBINOM.FORDELING"},NETWORKDAYS:{alias:"NETT.ARBEIDSDAGER"},"NETWORKDAYS.INTL":{alias:"NETT.ARBEIDSDAGER.INTL"},NOMINAL:{alias:"NOMINELL"},"NORM.DIST":{alias:"NORM.FORDELING"},"NORM.S.DIST":{alias:"NORM.S.FORDELING"},NORMDIST:{alias:"NORMALFORDELING"},NORMSDIST:{alias:"NORMSFORDELING"},NOT:{alias:"IKKE"},NOW:{alias:"N\xc5"},NPER:{alias:"PERIODER"},NPV:{alias:"NNV"},NUMBERVALUE:{alias:"TALLVERDI"},OCT2BIN:{alias:"OKTTILBIN"},OCT2DEC:{alias:"OKTTILDES"},OCT2HEX:{alias:"OKTTILHEKS"},ODD:{alias:"AVRUND.TIL.ODDETALL"},ODDFPRICE:{alias:"AVVIKFP.PRIS"},ODDFYIELD:{alias:"AVVIKFP.AVKASTNING"},ODDLPRICE:{alias:"AVVIKSP.PRIS"},ODDLYIELD:{alias:"AVVIKSP.AVKASTNING"},OFFSET:{alias:"FORSKYVNING"},OR:{alias:"ELLER"},PDURATION:{alias:"PVARIGHET"},PERCENTILE:{alias:"PERSENTIL"},"PERCENTILE.EXC":{alias:"PERSENTIL.EKS"},"PERCENTILE.INC":{alias:"PERSENTIL.INK"},PERCENTRANK:{alias:"PROSENTDEL"},"PERCENTRANK.EXC":{alias:"PROSENTDEL.EKS"},"PERCENTRANK.INC":{alias:"PROSENTDEL.INK"},PERMUT:{alias:"PERMUTER"},PERMUTATIONA:{alias:"PERMUTASJONA"},PHONETIC:{alias:"FURIGANA"},PMT:{alias:"AVDRAG"},"POISSON.DIST":{alias:"POISSON.FORDELING"},POWER:{alias:"OPPH\xd8YD.I"},PPMT:{alias:"AMORT"},PRICE:{alias:"PRIS"},PRICEDISC:{alias:"PRIS.DISKONTERT"},PRICEMAT:{alias:"PRIS.FORFALL"},PROB:{alias:"SANNSYNLIG"},PRODUCT:{alias:"PRODUKT"},PROPER:{alias:"STOR.FORBOKSTAV"},PV:{alias:"N\xc5VERDI"},QUARTILE:{alias:"KVARTIL"},"QUARTILE.EXC":{alias:"KVARTIL.EKS"},"QUARTILE.INC":{alias:"KVARTIL.INK"},QUOTIENT:{alias:"KVOTIENT"},RADIANS:{alias:"RADIANER"},RAND:{alias:"TILFELDIG"},RANDBETWEEN:{alias:"TILFELDIGMELLOM"},RANK:{alias:"RANG"},"RANK.AVG":{alias:"RANG.GJSN"},"RANK.EQ":{alias:"RANG.EKV"},RATE:{alias:"RENTE"},RECEIVED:{alias:"MOTTATT.AVKAST"},"REGISTER.ID":{alias:"REGISTRER.ID"},REPLACE:{alias:"ERSTATT"},REPLACEB:{alias:"ERSTATTB"},REPT:{alias:"GJENTA"},RIGHT:{alias:"H\xd8YRE"},RIGHTB:{alias:"H\xd8YREB"},ROMAN:{alias:"ROMERTALL"},ROUND:{alias:"AVRUND"},ROUNDDOWN:{alias:"AVRUND.NED"},ROUNDUP:{alias:"AVRUND.OPP"},ROW:{alias:"RAD"},ROWS:{alias:"RADER"},RRI:{alias:"REALISERT.AVKASTNING"},RSQ:{alias:"RKVADRAT"},SEARCH:{alias:"S\xd8K"},SEARCHB:{alias:"S\xd8KB"},SECOND:{alias:"SEKUND"},SERIESSUM:{alias:"SUMMER.REKKE"},SHEET:{alias:"ARK"},SHEETS:{alias:"ANTALL.ARK"},SIGN:{alias:"FORTEGN"},SKEW:{alias:"SKJEVFORDELING"},"SKEW.P":{alias:"SKJEVFORDELING.P"},SLN:{alias:"LINAVS"},SLOPE:{alias:"STIGNINGSTALL"},SMALL:{alias:"N.MINST"},SQRT:{alias:"ROT"},SQRTPI:{alias:"ROTPI"},STANDARDIZE:{alias:"NORMALISER"},STDEV:{alias:"STDAV"},"STDEV.P":{alias:"STDAV.P"},"STDEV.S":{alias:"STDAV.S"},STDEVA:{alias:"STDAVVIKA"},STDEVP:{alias:"STDAVP"},STDEVPA:{alias:"STDAVVIKPA"},STEYX:{alias:"STANDARDFEIL"},SUBSTITUTE:{alias:"BYTT.UT"},SUBTOTAL:{alias:"DELSUM"},SUM:{alias:"SUMMER"},SUMIF:{alias:"SUMMERHVIS"},SUMIFS:{alias:"SUMMER.HVIS.SETT"},SUMPRODUCT:{alias:"SUMMERPRODUKT"},SUMSQ:{alias:"SUMMERKVADRAT"},SUMX2MY2:{alias:"SUMMERX2MY2"},SUMX2PY2:{alias:"SUMMERX2PY2"},SUMXMY2:{alias:"SUMMERXMY2"},SYD:{alias:"\xc5RSAVS"},"T.DIST":{alias:"T.FORDELING"},"T.DIST.2T":{alias:"T.FORDELING.2T"},"T.DIST.RT":{alias:"T.FORDELING.H"},TBILLEQ:{alias:"TBILLEKV"},TBILLPRICE:{alias:"TBILLPRIS"},TBILLYIELD:{alias:"TBILLAVKASTNING"},TDIST:{alias:"TFORDELING"},TEXT:{alias:"TEKST"},TIME:{alias:"TID"},TIMEVALUE:{alias:"TIDSVERDI"},TODAY:{alias:"IDAG"},TRANSPOSE:{alias:"TRANSPONER"},TRIM:{alias:"TRIMME"},TRIMMEAN:{alias:"TRIMMET.GJENNOMSNITT"},TRUE:{alias:"SANN"},TRUNC:{alias:"AVKORT"},TYPE:{alias:"VERDITYPE"},UNICHAR:{alias:"UNICODETEGN"},UPPER:{alias:"STORE"},VALUE:{alias:"VERDI"},VAR:{alias:"VARIANS"},"VAR.P":{alias:"VARIANS.P"},"VAR.S":{alias:"VARIANS.S"},VARA:{alias:"VARIANSA"},VARP:{alias:"VARIANSP"},VARPA:{alias:"VARIANSPA"},VDB:{alias:"VERDIAVS"},VLOOKUP:{alias:"FINN.RAD"},WEBSERVICE:{alias:"NETTJENESTE"},WEEKDAY:{alias:"UKEDAG"},WEEKNUM:{alias:"UKENR"},WEIBULL:{alias:"WEIBULL.FORDELING"},"WEIBULL.DIST":{alias:"WEIBULL.DIST.N"},WORKDAY:{alias:"ARBEIDSDAG"},"WORKDAY.INTL":{alias:"ARBEIDSDAG.INTL"},XIRR:{alias:"XIR"},XNPV:{alias:"XNNV"},XOR:{alias:"EKSKLUSIVELLER"},YEAR:{alias:"\xc5R"},YEARFRAC:{alias:"\xc5RDEL"},YIELD:{alias:"AVKAST"},YIELDDISC:{alias:"AVKAST.DISKONTERT"},YIELDMAT:{alias:"AVKAST.FORFALL"}},tableFunctionsMapping:{"#All":"#Alle","#Data":"#Data","#Headers":"#Topptekster","#Totals":"#Totaler","#This row":"#Denne raden"},clacErrorMapping:{"#NULL!":"#NULL!","#DIV/0!":"#DIV/0!","#VALUE!":"#VERDI!","#REF!":"#REF!","#NAME?":"#NAVN?","#N/A!":"#I/T","#NUM!":"#NUM!"},booleanMapping:{boolean_true:"SANN",boolean_false:"USANN"}}},"./src/languagePackages/res.Polish.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ABS:{alias:"MODU\u0141.LICZBY"},ACCRINT:{alias:"NAL.ODS"},ACCRINTM:{alias:"NAL.ODS.WYKUP"},ADDRESS:{alias:"ADRES"},AGGREGATE:{alias:"AGREGUJ"},AMORDEGRC:{alias:"AMORT.NIELIN"},AMORLINC:{alias:"AMORT.LIN"},AND:{alias:"ORAZ"},ARABIC:{alias:"ARABSKIE"},AREAS:{alias:"OBSZARY"},AVEDEV:{alias:"ODCH.\u015aREDNIE"},AVERAGE:{alias:"\u015aREDNIA"},AVERAGEA:{alias:"\u015aREDNIA.A"},AVERAGEIF:{alias:"\u015aREDNIA.JE\u017bELI"},AVERAGEIFS:{alias:"\u015aREDNIA.WARUNK\xd3W"},BAHTTEXT:{alias:"BAT.TEKST"},BASE:{alias:"PODSTAWA"},BESSELI:{alias:"BESSEL.I"},BESSELJ:{alias:"BESSEL.J"},BESSELK:{alias:"BESSEL.K"},BESSELY:{alias:"BESSEL.Y"},"BETA.DIST":{alias:"ROZK\u0141.BETA"},"BETA.INV":{alias:"ROZK\u0141.BETA.ODWR"},BETADIST:{alias:"ROZK\u0141AD.BETA"},BETAINV:{alias:"ROZK\u0141AD.BETA.ODW"},BIN2DEC:{alias:"DW\xd3JK.NA.DZIES"},BIN2HEX:{alias:"DW\xd3JK.NA.SZESN"},BIN2OCT:{alias:"DW\xd3JK.NA.\xd3SM"},"BINOM.DIST":{alias:"ROZK\u0141.DWUM"},"BINOM.DIST.RANGE":{alias:"ROZK\u0141.DWUM.ZAKRES"},"BINOM.INV":{alias:"ROZK\u0141.DWUM.ODWR"},BINOMDIST:{alias:"ROZK\u0141AD.DWUM"},BITLSHIFT:{alias:"BIT.PRZESUNI\u0118CIE.W.LEWO"},BITRSHIFT:{alias:"BIT.PRZESUNI\u0118CIE.W.PRAWO"},CALL:{alias:"WYWO\u0141AJ"},CEILING:{alias:"ZAOKR.W.G\xd3R\u0118"},"CEILING.MATH":{alias:"ZAOKR.W.G\xd3R\u0118.MATEMATYCZNE"},"CEILING.PRECISE":{alias:"ZAOKR.W.G\xd3R\u0118.DOK\u0141"},CELL:{alias:"KOM\xd3RKA"},CHAR:{alias:"ZNAK"},CHIDIST:{alias:"ROZK\u0141AD.CHI"},CHIINV:{alias:"ROZK\u0141AD.CHI.ODW"},"CHISQ.DIST":{alias:"ROZK\u0141.CHI"},"CHISQ.DIST.RT":{alias:"ROZK\u0141.CHI.PS"},"CHISQ.INV":{alias:"ROZK\u0141.CHI.ODWR"},"CHISQ.INV.RT":{alias:"ROZK\u0141.CHI.ODWR.PS"},"CHISQ.TEST":{alias:"CHI.TEST"},CHITEST:{alias:"TEST.CHI"},CHOOSE:{alias:"WYBIERZ"},CLEAN:{alias:"OCZY\u015a\u0106"},CODE:{alias:"KOD"},COLUMN:{alias:"NR.KOLUMNY"},COLUMNS:{alias:"LICZBA.KOLUMN"},COMBIN:{alias:"KOMBINACJE"},COMBINA:{alias:"KOMBINACJE.A"},COMPLEX:{alias:"LICZBA.ZESP"},CONCATENATE:{alias:"Z\u0141\u0104CZ.TEKSTY"},CONFIDENCE:{alias:"UFNO\u015a\u0106"},"CONFIDENCE.NORM":{alias:"UFNO\u015a\u0106.NORM"},"CONFIDENCE.T":{alias:"UFNO\u015a\u0106.T"},CONVERT:{alias:"KONWERTUJ"},CORREL:{alias:"WSP.KORELACJI"},COUNT:{alias:"ILE.LICZB"},COUNTA:{alias:"ILE.NIEPUSTYCH"},COUNTBLANK:{alias:"LICZ.PUSTE"},COUNTIF:{alias:"LICZ.JE\u017bELI"},COUNTIFS:{alias:"LICZ.WARUNKI"},COUPDAYBS:{alias:"WYP\u0141.DNI.OD.POCZ"},COUPDAYS:{alias:"WYP\u0141.DNI"},COUPDAYSNC:{alias:"WYP\u0141.DNI.NAST"},COUPNCD:{alias:"WYP\u0141.DATA.NAST"},COUPNUM:{alias:"WYP\u0141.LICZBA"},COUPPCD:{alias:"WYP\u0141.DATA.POPRZ"},COVAR:{alias:"KOWARIANCJA"},"COVARIANCE.P":{alias:"KOWARIANCJA.POPUL"},"COVARIANCE.S":{alias:"KOWARIANCJA.PR\xd3BKI"},CRITBINOM:{alias:"PR\xd3G.ROZK\u0141AD.DWUM"},CUBEKPIMEMBER:{alias:"ELEMENT.KPI.MODU\u0141U"},CUBEMEMBER:{alias:"ELEMENT.MODU\u0141U"},CUBEMEMBERPROPERTY:{alias:"W\u0141A\u015aCIWO\u015a\u0106.ELEMENTU.MODU\u0141U"},CUBERANKEDMEMBER:{alias:"USZEREGOWANY.ELEMENT.MODU\u0141U"},CUBESET:{alias:"ZESTAW.MODU\u0141\xd3W"},CUBESETCOUNT:{alias:"LICZNIK.MODU\u0141\xd3W.ZESTAWU"},CUBEVALUE:{alias:"WARTO\u015a\u0106.MODU\u0141U"},CUMIPMT:{alias:"SP\u0141AC.ODS"},CUMPRINC:{alias:"SP\u0141AC.KAPIT"},DATE:{alias:"DATA"},DATEVALUE:{alias:"DATA.WARTO\u015a\u0106"},DAVERAGE:{alias:"BD.\u015aREDNIA"},DAY:{alias:"DZIE\u0143"},DAYS:{alias:"DNI"},DAYS360:{alias:"DNI.360"},DCOUNT:{alias:"BD.ILE.REKORD\xd3W"},DCOUNTA:{alias:"BD.ILE.REKORD\xd3W.A"},DEC2BIN:{alias:"DZIES.NA.DW\xd3JK"},DEC2HEX:{alias:"DZIES.NA.SZESN"},DEC2OCT:{alias:"DZIES.NA.\xd3SM"},DECIMAL:{alias:"DZIESI\u0118TNA"},DEGREES:{alias:"STOPNIE"},DELTA:{alias:"CZY.R\xd3WNE"},DEVSQ:{alias:"ODCH.KWADRATOWE"},DGET:{alias:"BD.POLE"},DISC:{alias:"STOPA.DYSK"},DMAX:{alias:"BD.MAX"},DMIN:{alias:"BD.MIN"},DOLLAR:{alias:"KWOTA"},DOLLARDE:{alias:"CENA.DZIES"},DOLLARFR:{alias:"CENA.U\u0141AM"},DPRODUCT:{alias:"BD.ILOCZYN"},DSTDEV:{alias:"BD.ODCH.STANDARD"},DSTDEVP:{alias:"BD.ODCH.STANDARD.POPUL"},DSUM:{alias:"BD.SUMA"},DURATION:{alias:"ROCZ.PRZYCH"},DVAR:{alias:"BD.WARIANCJA"},DVARP:{alias:"BD.WARIANCJA.POPUL"},EDATE:{alias:"NR.SER.DATY"},EFFECT:{alias:"EFEKTYWNA"},EOMONTH:{alias:"NR.SER.OST.DN.MIES"},ERF:{alias:"FUNKCJA.B\u0141"},"ERF.PRECISE":{alias:"FUNKCJA.B\u0141.DOK\u0141"},ERFC:{alias:"KOMP.FUNKCJA.B\u0141"},"ERFC.PRECISE":{alias:"KOMP.FUNKCJA.B\u0141.DOK\u0141"},"ERROR.TYPE":{alias:"NR.B\u0141\u0118DU"},EVEN:{alias:"ZAOKR.DO.PARZ"},EXACT:{alias:"POR\xd3WNAJ"},"EXPON.DIST":{alias:"ROZK\u0141.EXP"},EXPONDIST:{alias:"ROZK\u0141AD.EXP"},"F.DIST":{alias:"ROZK\u0141.F"},"F.DIST.RT":{alias:"ROZK\u0141.F.PS"},"F.INV":{alias:"ROZK\u0141.F.ODWR"},"F.INV.RT":{alias:"ROZK\u0141.F.ODWR.PS"},FACT:{alias:"SILNIA"},FACTDOUBLE:{alias:"SILNIA.DWUKR"},FALSE:{alias:"FA\u0141SZ"},FDIST:{alias:"ROZK\u0141AD.F"},FIND:{alias:"ZNAJD\u0179"},FINDB:{alias:"ZNAJD\u0179B"},FINV:{alias:"ROZK\u0141AD.F.ODW"},FISHER:{alias:"ROZK\u0141AD.FISHER"},FISHERINV:{alias:"ROZK\u0141AD.FISHER.ODW"},FIXED:{alias:"ZAOKR.DO.TEKST"},FLOOR:{alias:"ZAOKR.W.D\xd3\u0141"},"FLOOR.MATH":{alias:"ZAOKR.W.D\xd3\u0141.MATEMATYCZNE"},"FLOOR.PRECISE":{alias:"ZAOKR.W.D\xd3\u0141.DOK\u0141"},FORECAST:{alias:"REGLINX"},FORMULATEXT:{alias:"FORMU\u0141A.TEKST"},FREQUENCY:{alias:"CZ\u0118STO\u015a\u0106"},FTEST:{alias:"TEST.F"},FVSCHEDULE:{alias:"WART.PRZYSZ\u0141.KAP"},"GAMMA.DIST":{alias:"ROZK\u0141.GAMMA"},"GAMMA.INV":{alias:"ROZK\u0141.GAMMA.ODWR"},GAMMADIST:{alias:"ROZK\u0141AD.GAMMA"},GAMMAINV:{alias:"ROZK\u0141AD.GAMMA.ODW"},GAMMALN:{alias:"ROZK\u0141AD.LIN.GAMMA"},"GAMMALN.PRECISE":{alias:"ROZK\u0141AD.LIN.GAMMA.DOK\u0141"},GCD:{alias:"NAJW.WSP.DZIEL"},GEOMEAN:{alias:"\u015aREDNIA.GEOMETRYCZNA"},GESTEP:{alias:"SPRAWD\u0179.PR\xd3G"},GETPIVOTDATA:{alias:"WE\u0179DANETABELI"},GROWTH:{alias:"REGEXPW"},HARMEAN:{alias:"\u015aREDNIA.HARMONICZNA"},HEX2BIN:{alias:"SZESN.NA.DW\xd3JK"},HEX2DEC:{alias:"SZESN.NA.DZIES"},HEX2OCT:{alias:"SZESN.NA.\xd3SM"},HLOOKUP:{alias:"WYSZUKAJ.POZIOMO"},HOUR:{alias:"GODZINA"},HYPERLINK:{alias:"HIPER\u0141\u0104CZE"},"HYPGEOM.DIST":{alias:"ROZK\u0141.HIPERGEOM"},HYPGEOMDIST:{alias:"ROZK\u0141AD.HIPERGEOM"},IF:{alias:"JE\u017bELI"},IFERROR:{alias:"JE\u017bELI.B\u0141\u0104D"},IFNA:{alias:"JE\u017bELI.ND"},IMABS:{alias:"MODU\u0141.LICZBY.ZESP"},IMAGINARY:{alias:"CZ.UROJ.LICZBY.ZESP"},IMARGUMENT:{alias:"ARG.LICZBY.ZESP"},IMCONJUGATE:{alias:"SPRZ\u0118\u017b.LICZBY.ZESP"},IMCOS:{alias:"COS.LICZBY.ZESP"},IMCOSH:{alias:"COSH.LICZBY.ZESP"},IMCOT:{alias:"COT.LICZBY.ZESP"},IMCSC:{alias:"CSC.LICZBY.ZESP"},IMCSCH:{alias:"CSCH.LICZBY.ZESP"},IMDIV:{alias:"ILORAZ.LICZB.ZESP"},IMEXP:{alias:"EXP.LICZBY.ZESP"},IMLN:{alias:"LN.LICZBY.ZESP"},IMLOG10:{alias:"LOG10.LICZBY.ZESP"},IMLOG2:{alias:"LOG2.LICZBY.ZESP"},IMPOWER:{alias:"POT\u0118GA.LICZBY.ZESP"},IMPRODUCT:{alias:"ILOCZYN.LICZB.ZESP"},IMREAL:{alias:"CZ.RZECZ.LICZBY.ZESP"},IMSEC:{alias:"SEC.LICZBY.ZESP"},IMSECH:{alias:"SECH.LICZBY.ZESP"},IMSIN:{alias:"SIN.LICZBY.ZESP"},IMSINH:{alias:"SINH.LICZBY.ZESP"},IMSQRT:{alias:"PIERWIASTEK.LICZBY.ZESP"},IMSUB:{alias:"R\xd3\u017bN.LICZB.ZESP"},IMSUM:{alias:"SUMA.LICZB.ZESP"},IMTAN:{alias:"TAN.LICZBY.ZESP"},INDEX:{alias:"INDEKS"},INDIRECT:{alias:"ADR.PO\u015aR"},INT:{alias:"ZAOKR.DO.CA\u0141K"},INTERCEPT:{alias:"ODCI\u0118TA"},INTRATE:{alias:"STOPA.PROC"},ISBLANK:{alias:"CZY.PUSTA"},ISERR:{alias:"CZY.B\u0141"},ISERROR:{alias:"CZY.B\u0141\u0104D"},ISEVEN:{alias:"CZY.PARZYSTE"},ISFORMULA:{alias:"CZY.FORMU\u0141A"},ISLOGICAL:{alias:"CZY.LOGICZNA"},ISNA:{alias:"CZY.BRAK"},ISNONTEXT:{alias:"CZY.NIE.TEKST"},ISNUMBER:{alias:"CZY.LICZBA"},"ISO.CEILING":{alias:"ISO.ZAOKR.W.G\xd3R\u0118"},ISODD:{alias:"CZY.NIEPARZYSTE"},ISOWEEKNUM:{alias:"ISO.NUM.TYG"},ISREF:{alias:"CZY.ADR"},ISTEXT:{alias:"CZY.TEKST"},KURT:{alias:"KURTOZA"},LARGE:{alias:"MAX.K"},LCM:{alias:"NAJMN.WSP.WIEL"},LEFT:{alias:"LEWY"},LEFTB:{alias:"LEWYB"},LEN:{alias:"D\u0141"},LENB:{alias:"D\u0141.B"},LINEST:{alias:"REGLINP"},LOGEST:{alias:"REGEXPP"},LOGINV:{alias:"ROZK\u0141AD.LOG.ODW"},"LOGNORM.DIST":{alias:"ROZK\u0141.LOG"},"LOGNORM.INV":{alias:"ROZK\u0141.LOG.ODWR"},LOGNORMDIST:{alias:"ROZK\u0141AD.LOG"},LOOKUP:{alias:"WYSZUKAJ"},LOWER:{alias:"LITERY.MA\u0141E"},MATCH:{alias:"PODAJ.POZYCJ\u0118"},MAXA:{alias:"MAX.A"},MDETERM:{alias:"WYZNACZNIK.MACIERZY"},MDURATION:{alias:"ROCZ.PRZYCH.M"},MEDIAN:{alias:"MEDIANA"},MID:{alias:"FRAGMENT.TEKSTU"},MIDB:{alias:"FRAGMENT.TEKSTU.B"},MINA:{alias:"MIN.A"},MINUTE:{alias:"MINUTA"},MINVERSE:{alias:"MACIERZ.ODW"},MMULT:{alias:"MACIERZ.ILOCZYN"},MODE:{alias:"WYST.NAJCZ\u0118\u015aCIEJ"},"MODE.MULT":{alias:"WYST.NAJCZ\u0118\u015aCIEJ.TABL"},"MODE.SNGL":{alias:"WYST.NAJCZ\u0118\u015aCIEJ.WART"},MONTH:{alias:"MIESI\u0104C"},MROUND:{alias:"ZAOKR.DO.WIELOKR"},MULTINOMIAL:{alias:"WIELOMIAN"},MUNIT:{alias:"MACIERZ.JEDNOSTKOWA"},NA:{alias:"BRAK"},"NEGBINOM.DIST":{alias:"ROZK\u0141.DWUM.PRZEC"},NEGBINOMDIST:{alias:"ROZK\u0141AD.DWUM.PRZEC"},NETWORKDAYS:{alias:"DNI.ROBOCZE"},"NETWORKDAYS.INTL":{alias:"DNI.ROBOCZE.NIESTAND"},NOMINAL:{alias:"NOMINALNA"},"NORM.DIST":{alias:"ROZK\u0141.NORMALNY"},"NORM.INV":{alias:"ROZK\u0141.NORMALNY.ODWR"},"NORM.S.DIST":{alias:"ROZK\u0141.NORMALNY.S"},"NORM.S.INV":{alias:"ROZK\u0141.NORMALNY.S.ODWR"},NORMDIST:{alias:"ROZK\u0141AD.NORMALNY"},NORMINV:{alias:"ROZK\u0141AD.NORMALNY.ODW"},NORMSDIST:{alias:"ROZK\u0141AD.NORMALNY.S"},NORMSINV:{alias:"ROZK\u0141AD.NORMALNY.S.ODW"},NOT:{alias:"NIE"},NOW:{alias:"TERAZ"},NUMBERVALUE:{alias:"WARTO\u015a\u0106.LICZBOWA"},OCT2BIN:{alias:"\xd3SM.NA.DW\xd3JK"},OCT2DEC:{alias:"\xd3SM.NA.DZIES"},OCT2HEX:{alias:"\xd3SM.NA.SZESN"},ODD:{alias:"ZAOKR.DO.NPARZ"},ODDFPRICE:{alias:"CENA.PIERW.OKR"},ODDFYIELD:{alias:"RENT.PIERW.OKR"},ODDLPRICE:{alias:"CENA.OST.OKR"},ODDLYIELD:{alias:"RENT.OST.OKR"},OFFSET:{alias:"PRZESUNI\u0118CIE"},OR:{alias:"LUB"},PDURATION:{alias:"O.CZAS.TRWANIA"},PERCENTILE:{alias:"PERCENTYL"},"PERCENTILE.EXC":{alias:"PERCENTYL.PRZEDZ.OTW"},"PERCENTILE.INC":{alias:"PERCENTYL.PRZEDZ.ZAMK"},PERCENTRANK:{alias:"PROCENT.POZYCJA"},"PERCENTRANK.EXC":{alias:"PROC.POZ.PRZEDZ.OTW"},"PERCENTRANK.INC":{alias:"PROC.POZ.PRZEDZ.ZAMK"},PERMUT:{alias:"PERMUTACJE"},PERMUTATIONA:{alias:"PERMUTACJE.A"},POISSON:{alias:"ROZK\u0141AD.POISSON"},"POISSON.DIST":{alias:"ROZK\u0141.POISSON"},POWER:{alias:"POT\u0118GA"},PRICE:{alias:"CENA"},PRICEDISC:{alias:"CENA.DYSK"},PRICEMAT:{alias:"CENA.WYKUP"},PROB:{alias:"PRAWDPD"},PRODUCT:{alias:"ILOCZYN"},PROPER:{alias:"Z.WIELKIEJ.LITERY"},QUARTILE:{alias:"KWARTYL"},"QUARTILE.EXC":{alias:"KWARTYL.PRZEDZ.OTW"},"QUARTILE.INC":{alias:"KWARTYL.PRZEDZ.ZAMK"},QUOTIENT:{alias:"CZ.CA\u0141K.DZIELENIA"},RADIANS:{alias:"RADIANY"},RAND:{alias:"LOS"},RANDBETWEEN:{alias:"LOS.ZAKR"},RANK:{alias:"POZYCJA"},"RANK.AVG":{alias:"POZYCJA.\u015aR"},"RANK.EQ":{alias:"POZYCJA.NAJW"},RECEIVED:{alias:"KWOTA.WYKUP"},"REGISTER.ID":{alias:"REJESTR.KOD"},REPLACE:{alias:"ZAST\u0104P"},REPLACEB:{alias:"ZAST\u0104P.B"},REPT:{alias:"POWT"},RIGHT:{alias:"PRAWY"},RIGHTB:{alias:"PRAWY.B"},ROMAN:{alias:"RZYMSKIE"},ROUND:{alias:"ZAOKR"},ROUNDDOWN:{alias:"ZAOKR.D\xd3\u0141"},ROUNDUP:{alias:"ZAOKR.G\xd3RA"},ROW:{alias:"WIERSZ"},ROWS:{alias:"ILE.WIERSZY"},RRI:{alias:"R\xd3WNOW.STOPA.PROC"},RSQ:{alias:"R.KWADRAT"},RTD:{alias:"DANE.CZASU.RZECZ"},SEARCH:{alias:"SZUKAJ.TEKST"},SEARCHB:{alias:"SZUKAJ.TEKST.B"},SECOND:{alias:"SEKUNDA"},SERIESSUM:{alias:"SUMA.SZER.POT"},SHEET:{alias:"ARKUSZ"},SHEETS:{alias:"ARKUSZE"},SIGN:{alias:"ZNAK.LICZBY"},SKEW:{alias:"SKO\u015aNO\u015a\u0106"},"SKEW.P":{alias:"SKO\u015aNO\u015a\u0106.P"},SLOPE:{alias:"NACHYLENIE"},SMALL:{alias:"MIN.K"},SQRT:{alias:"PIERWIASTEK"},SQRTPI:{alias:"PIERW.PI"},STANDARDIZE:{alias:"NORMALIZUJ"},STDEV:{alias:"ODCH.STANDARDOWE"},"STDEV.P":{alias:"ODCH.STAND.POPUL"},"STDEV.S":{alias:"ODCH.STANDARD.PR\xd3BKI"},STDEVA:{alias:"ODCH.STANDARDOWE.A"},STDEVP:{alias:"ODCH.STANDARD.POPUL"},STDEVPA:{alias:"ODCH.STANDARD.POPUL.A"},STEYX:{alias:"REGB\u0141STD"},SUBSTITUTE:{alias:"PODSTAW"},SUBTOTAL:{alias:"SUMY.CZ\u0118\u015aCIOWE"},SUM:{alias:"SUMA"},SUMIF:{alias:"SUMA.JE\u017bELI"},SUMIFS:{alias:"SUMA.WARUNK\xd3W"},SUMPRODUCT:{alias:"SUMA.ILOCZYN\xd3W"},SUMSQ:{alias:"SUMA.KWADRAT\xd3W"},SUMX2MY2:{alias:"SUMA.X2.M.Y2"},SUMX2PY2:{alias:"SUMA.X2.P.Y2"},SUMXMY2:{alias:"SUMA.XMY.2"},"T.DIST":{alias:"ROZK\u0141.T"},"T.DIST.2T":{alias:"ROZK\u0141.T.DS"},"T.DIST.RT":{alias:"ROZK\u0141.T.PS"},"T.INV":{alias:"ROZK\u0141.T.ODWR"},"T.INV.2T":{alias:"ROZK\u0141.T.ODWR.DS"},TBILLEQ:{alias:"RENT.EKW.BS"},TBILLPRICE:{alias:"CENA.BS"},TBILLYIELD:{alias:"RENT.BS"},TDIST:{alias:"ROZK\u0141AD.T"},TEXT:{alias:"TEKST"},TIME:{alias:"CZAS"},TIMEVALUE:{alias:"CZAS.WARTO\u015a\u0106"},TINV:{alias:"ROZK\u0141AD.T.ODW"},TODAY:{alias:"DZI\u015a"},TRANSPOSE:{alias:"TRANSPONUJ"},TREND:{alias:"REGLINW"},TRIM:{alias:"USU\u0143.ZB\u0118DNE.ODST\u0118PY"},TRIMMEAN:{alias:"\u015aREDNIA.WEWN"},TRUE:{alias:"PRAWDA"},TRUNC:{alias:"LICZBA.CA\u0141K"},TTEST:{alias:"TEST.T"},TYPE:{alias:"TYP"},UNICHAR:{alias:"ZNAK.UNICODE"},UPPER:{alias:"LITERY.WIELKIE"},VALUE:{alias:"WARTO\u015a\u0106"},VAR:{alias:"WARIANCJA"},"VAR.P":{alias:"WARIANCJA.POP"},"VAR.S":{alias:"WARIANCJA.PR\xd3BKI"},VARA:{alias:"WARIANCJA.A"},VARP:{alias:"WARIANCJA.POPUL"},VARPA:{alias:"WARIANCJA.POPUL.A"},VLOOKUP:{alias:"WYSZUKAJ.PIONOWO"},WEEKDAY:{alias:"DZIE\u0143.TYG"},WEEKNUM:{alias:"NUM.TYG"},WEIBULL:{alias:"ROZK\u0141AD.WEIBULL"},"WEIBULL.DIST":{alias:"ROZK\u0141.WEIBULL"},WORKDAY:{alias:"DZIE\u0143.ROBOCZY"},"WORKDAY.INTL":{alias:"DZIE\u0143.ROBOCZY.NIESTAND"},YEAR:{alias:"ROK"},YEARFRAC:{alias:"CZ\u0118\u015a\u0106.ROKU"},YIELD:{alias:"RENTOWNO\u015a\u0106"},YIELDDISC:{alias:"RENT.DYSK"},YIELDMAT:{alias:"RENT.WYKUP"},ZTEST:{alias:"TEST.Z"}},tableFunctionsMapping:{"#All":"#Wszystko","#Data":"#Dane","#Headers":"#Nag\u0142\xf3wki","#Totals":"#Sumy","#This row":"#Ten wiersz"},clacErrorMapping:{"#NULL!":"#ZERO!","#DIV/0!":"#DZIEL/0!","#VALUE!":"#ARG!","#REF!":"#ADR!","#NAME?":"#NAZWA?","#N/A!":"#N/D!","#NUM!":"#LICZBA!"},booleanMapping:{boolean_true:"PRAWDA",boolean_false:"FA\u0141SZ"}}},"./src/languagePackages/res.Portuguese.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"JUROSACUM"},ACCRINTM:{alias:"JUROSACUMV"},ADDRESS:{alias:"ENDERE\xc7O"},AGGREGATE:{alias:"AGREGAR"},AND:{alias:"E"},ARABIC:{alias:"\xc1RABE"},AREAS:{alias:"\xc1REAS"},ASIN:{alias:"ASEN"},ASINH:{alias:"ASENH"},AVEDEV:{alias:"DESV.M\xc9DIO"},AVERAGE:{alias:"M\xc9DIA"},AVERAGEA:{alias:"M\xc9DIAA"},AVERAGEIF:{alias:"M\xc9DIA.SE"},AVERAGEIFS:{alias:"M\xc9DIA.SE.S"},BAHTTEXT:{alias:"TEXTO.BAHT"},"BETA.DIST":{alias:"DIST.BETA"},"BETA.INV":{alias:"INV.BETA"},BETADIST:{alias:"DISTBETA"},BETAINV:{alias:"BETA.ACUM.INV"},BIN2DEC:{alias:"BINADEC"},BIN2HEX:{alias:"BINAHEX"},BIN2OCT:{alias:"BINAOCT"},"BINOM.DIST":{alias:"DISTR.BINOM"},"BINOM.DIST.RANGE":{alias:"DIST.BINOM.INTERVALO"},"BINOM.INV":{alias:"INV.BINOM"},BINOMDIST:{alias:"DISTRBINOM"},BITAND:{alias:"BIT.E"},BITLSHIFT:{alias:"BITDESL.ESQ"},BITOR:{alias:"BIT.OU"},BITRSHIFT:{alias:"BITDESL.DIR"},BITXOR:{alias:"BIT.XOU"},CALL:{alias:"CHAMAR"},CEILING:{alias:"ARRED.EXCESSO"},"CEILING.MATH":{alias:"ARRED.EXCESSO.MAT"},"CEILING.PRECISE":{alias:"ARRED.EXCESSO.PRECISO"},CELL:{alias:"C\xc9L"},CHAR:{alias:"CAR\xc1T"},CHIDIST:{alias:"DIST.CHI"},CHIINV:{alias:"INV.CHI"},"CHISQ.DIST":{alias:"DIST.CHIQ"},"CHISQ.DIST.RT":{alias:"DIST.CHIQ.DIR"},"CHISQ.INV":{alias:"INV.CHIQ"},"CHISQ.INV.RT":{alias:"INV.CHIQ.DIR"},"CHISQ.TEST":{alias:"TESTE.CHIQ"},CHITEST:{alias:"TESTE.CHI"},CHOOSE:{alias:"SELECIONAR"},CLEAN:{alias:"LIMPARB"},CODE:{alias:"C\xd3DIGO"},COLUMN:{alias:"COL"},COLUMNS:{alias:"COLS"},COMBINA:{alias:"COMBIN.R"},COMPLEX:{alias:"COMPLEXO"},CONCATENATE:{alias:"CONCATENAR"},CONFIDENCE:{alias:"INT.CONFIAN\xc7A"},"CONFIDENCE.NORM":{alias:"INT.CONFIAN\xc7A.NORM"},"CONFIDENCE.T":{alias:"INT.CONFIAN\xc7A.T"},CONVERT:{alias:"CONVERTER"},COUNT:{alias:"CONTAR"},COUNTA:{alias:"CONTAR.VAL"},COUNTBLANK:{alias:"CONTAR.VAZIO"},COUNTIF:{alias:"CONTAR.SE"},COUNTIFS:{alias:"CONTAR.SE.S"},COUPDAYBS:{alias:"CUPDIASINLIQ"},COUPDAYS:{alias:"CUPDIAS"},COUPDAYSNC:{alias:"CUPDIASPR\xd3X"},COUPNCD:{alias:"CUPDATAPR\xd3X"},COUPNUM:{alias:"CUPN\xdaM"},COUPPCD:{alias:"CUPDATAANT"},"COVARIANCE.P":{alias:"COVARI\xc2NCIA.P"},"COVARIANCE.S":{alias:"COVARI\xc2NCIA.S"},CRITBINOM:{alias:"CRIT.BINOM"},CUBEKPIMEMBER:{alias:"MEMBROKPICUBO"},CUBEMEMBER:{alias:"MEMBROCUBO"},CUBEMEMBERPROPERTY:{alias:"PROPRIEDADEMEMBROCUBO"},CUBERANKEDMEMBER:{alias:"MEMBROCLASSIFICADOCUBO"},CUBESET:{alias:"CONJUNTOCUBO"},CUBESETCOUNT:{alias:"CONTARCONJUNTOCUBO"},CUBEVALUE:{alias:"VALORCUBO"},CUMIPMT:{alias:"PGTOJURACUM"},CUMPRINC:{alias:"PGTOCAPACUM"},DATE:{alias:"DATA"},DATEVALUE:{alias:"DATA.VALOR"},DAVERAGE:{alias:"BDM\xc9DIA"},DAY:{alias:"DIA"},DAYS:{alias:"DIAS"},DAYS360:{alias:"DIAS360"},DB:{alias:"BD"},DCOUNT:{alias:"BDCONTAR"},DCOUNTA:{alias:"BDCONTAR.VAL"},DDB:{alias:"BDD"},DEC2BIN:{alias:"DECABIN"},DEC2HEX:{alias:"DECAHEX"},DEC2OCT:{alias:"DECAOCT"},DEGREES:{alias:"GRAUS"},DEVSQ:{alias:"DESVQ"},DGET:{alias:"BDOBTER"},DISC:{alias:"DESC"},DMAX:{alias:"BDM\xc1X"},DMIN:{alias:"BDM\xcdN"},DOLLAR:{alias:"MOEDA"},DOLLARDE:{alias:"MOEDADEC"},DOLLARFR:{alias:"MOEDAFRA"},DPRODUCT:{alias:"BDMULTIPL"},DSTDEV:{alias:"BDDESVPAD"},DSTDEVP:{alias:"BDDESVPADP"},DSUM:{alias:"BDSOMA"},DURATION:{alias:"DURA\xc7\xc3O"},DVAR:{
alias:"BDVAR"},DVARP:{alias:"BDVARP"},EDATE:{alias:"DATAM"},EFFECT:{alias:"EFETIVA"},EOMONTH:{alias:"FIMM\xcaS"},ERF:{alias:"FUNCERRO"},"ERF.PRECISE":{alias:"FUNCERRO.PRECISO"},ERFC:{alias:"FUNCERROCOMPL"},"ERFC.PRECISE":{alias:"FUNCERROCOMPL.PRECISO"},"ERROR.TYPE":{alias:"TIPO.ERRO"},EVEN:{alias:"PAR"},EXACT:{alias:"EXATO"},"EXPON.DIST":{alias:"DIST.EXPON"},EXPONDIST:{alias:"DISTEXPON"},"F.DIST":{alias:"DIST.F"},"F.DIST.RT":{alias:"DIST.F.DIR"},"F.INV":{alias:"INV.F"},"F.INV.RT":{alias:"INV.F.DIR"},"F.TEST":{alias:"TESTE.F"},FACT:{alias:"FATORIAL"},FACTDOUBLE:{alias:"FATDUPLO"},FALSE:{alias:"FALSO"},FDIST:{alias:"DISTF"},FILTERXML:{alias:"FILTRARXML"},FIND:{alias:"LOCALIZAR"},FINDB:{alias:"LOCALIZARB"},FINV:{alias:"INVF"},FIXED:{alias:"FIXA"},FLOOR:{alias:"ARRED.DEFEITO"},"FLOOR.MATH":{alias:"ARRED.DEFEITO.MAT"},"FLOOR.PRECISE":{alias:"ARRED.DEFEITO.PRECISO"},FORECAST:{alias:"PREVIS\xc3O"},FORMULATEXT:{alias:"F\xd3RMULA.TEXTO"},FREQUENCY:{alias:"FREQU\xcaNCIA"},FTEST:{alias:"TESTEF"},FV:{alias:"VF"},FVSCHEDULE:{alias:"VFPLANO"},GAMMA:{alias:"GAMA"},"GAMMA.DIST":{alias:"DIST.GAMA"},"GAMMA.INV":{alias:"INV.GAMA"},GAMMADIST:{alias:"DISTGAMA"},GAMMAINV:{alias:"INVGAMA"},GAMMALN:{alias:"LNGAMA"},"GAMMALN.PRECISE":{alias:"LNGAMA.PRECISO"},GCD:{alias:"MDC"},GEOMEAN:{alias:"M\xc9DIA.GEOM\xc9TRICA"},GESTEP:{alias:"DEGRAU"},GETPIVOTDATA:{alias:"OBTERDADOSDIN"},GROWTH:{alias:"CRESCIMENTO"},HARMEAN:{alias:"M\xc9DIA.HARM\xd3NICA"},HEX2BIN:{alias:"HEXABIN"},HEX2DEC:{alias:"HEXADEC"},HEX2OCT:{alias:"HEXAOCT"},HLOOKUP:{alias:"PROCH"},HOUR:{alias:"HORA"},HYPERLINK:{alias:"HIPERLIGA\xc7\xc3O"},"HYPGEOM.DIST":{alias:"DIST.HIPGEOM"},HYPGEOMDIST:{alias:"DIST.HIPERGEOM"},IF:{alias:"SE"},IFERROR:{alias:"SE.ERRO"},IFNA:{alias:"SEND"},IMAGINARY:{alias:"IMAGIN\xc1RIO"},IMARGUMENT:{alias:"IMARG"},IMCONJUGATE:{alias:"IMCONJ"},IMPOWER:{alias:"IMPOT"},IMPRODUCT:{alias:"IMPROD"},IMSIN:{alias:"IMSENO"},IMSINH:{alias:"IMSENOH"},IMSQRT:{alias:"IMRAIZ"},IMSUB:{alias:"IMSUBTR"},IMSUM:{alias:"IMSOMA"},INDEX:{alias:"\xcdNDICE"},INDIRECT:{alias:"INDIRECTO"},INFO:{alias:"INFORMA\xc7\xc3O"},INTERCEPT:{alias:"INTERCETAR"},INTRATE:{alias:"TAXAJUROS"},IPMT:{alias:"IPGTO"},IRR:{alias:"TIR"},ISBLANK:{alias:"\xc9.C\xc9L.VAZIA"},ISERR:{alias:"\xc9.ERROS"},ISERROR:{alias:"\xc9.ERRO"},ISEVEN:{alias:"\xc9PAR"},ISFORMULA:{alias:"\xc9.FORMULA"},ISLOGICAL:{alias:"\xc9.L\xd3GICO"},ISNA:{alias:"\xc9.N\xc3O.DISP"},ISNONTEXT:{alias:"\xc9.N\xc3O.TEXTO"},ISNUMBER:{alias:"\xc9.N\xdaM"},"ISO.CEILING":{alias:"ARRED.EXCESSO.ISO"},ISODD:{alias:"\xc9\xcdMPAR"},ISOWEEKNUM:{alias:"N\xdaMSEMANAISO"},ISPMT:{alias:"\xc9.PGTO"},ISREF:{alias:"\xc9.REF"},ISTEXT:{alias:"\xc9.TEXTO"},KURT:{alias:"CURT"},LARGE:{alias:"MAIOR"},LCM:{alias:"MMC"},LEFT:{alias:"ESQUERDA"},LEFTB:{alias:"ESQUERDAB"},LEN:{alias:"N\xdaM.CARACT"},LENB:{alias:"N\xdaM.CARACTB"},LINEST:{alias:"PROJ.LIN"},LOGEST:{alias:"PROJ.LOG"},LOGINV:{alias:"INVLOG"},"LOGNORM.DIST":{alias:"DIST.NORMLOG"},"LOGNORM.INV":{alias:"INV.NORMALLOG"},LOGNORMDIST:{alias:"DIST.NORMALLOG"},LOOKUP:{alias:"PROC"},LOWER:{alias:"MIN\xdaSCULAS"},MATCH:{alias:"CORRESP"},MAX:{alias:"M\xc1XIMO"},MAXA:{alias:"M\xc1XIMOA"},MDETERM:{alias:"MATRIZ.DETERM"},MDURATION:{alias:"MDURA\xc7\xc3O"},MEDIAN:{alias:"MED"},MID:{alias:"SEG.TEXTO"},MIDB:{alias:"SEG.TEXTOB"},MIN:{alias:"M\xcdNIMO"},MINA:{alias:"M\xcdNIMOA"},MINUTE:{alias:"MINUTO"},MINVERSE:{alias:"MATRIZ.INVERSA"},MIRR:{alias:"MTIR"},MMULT:{alias:"MATRIZ.MULT"},MOD:{alias:"RESTO"},MODE:{alias:"MODO"},"MODE.MULT":{alias:"MODO.M\xdaLT"},"MODE.SNGL":{alias:"MODO.SIMPLES"},MONTH:{alias:"M\xcaS"},MROUND:{alias:"MARRED"},MULTINOMIAL:{alias:"POLINOMIAL"},MUNIT:{alias:"UNIDM"},NA:{alias:"ND"},"NEGBINOM.DIST":{alias:"DIST.BINOM.NEG"},NEGBINOMDIST:{alias:"DIST.BIN.NEG"},NETWORKDAYS:{alias:"DIATRABALHOTOTAL"},"NETWORKDAYS.INTL":{alias:"DIATRABALHOTOTAL.INTL"},"NORM.DIST":{alias:"DIST.NORM"},"NORM.INV":{alias:"INV.NORM"},"NORM.S.DIST":{alias:"DIST.S.NORM"},"NORM.S.INV":{alias:"INV.S.NORM"},NORMDIST:{alias:"DIST.NORM"},NORMINV:{alias:"INV.NORM"},NORMSDIST:{alias:"DIST.NORMP"},NORMSINV:{alias:"INV.NORMP"},NOT:{alias:"N\xc3O"},NOW:{alias:"AGORA"},NPV:{alias:"VAL"},NUMBERVALUE:{alias:"VALOR.N\xdaMERO"},OCT2BIN:{alias:"OCTABIN"},OCT2DEC:{alias:"OCTADEC"},OCT2HEX:{alias:"OCTAHEX"},ODD:{alias:"\xcdMPAR"},ODDFPRICE:{alias:"PRE\xc7OPRIMINC"},ODDFYIELD:{alias:"LUCROPRIMINC"},ODDLPRICE:{alias:"PRE\xc7O\xdaLTINC"},ODDLYIELD:{alias:"LUCRO\xdaLTINC"},OFFSET:{alias:"DESLOCAMENTO"},OR:{alias:"OU"},PDURATION:{alias:"PDURA\xc7\xc3O"},PERCENTILE:{alias:"PERCENTIL"},"PERCENTILE.EXC":{alias:"PERCENTIL.EXC"},"PERCENTILE.INC":{alias:"PERCENTIL.INC"},PERCENTRANK:{alias:"ORDEM.PERCENTUAL"},"PERCENTRANK.EXC":{alias:"ORDEM.PERCENTUAL.EXC"},"PERCENTRANK.INC":{alias:"ORDEM.PERCENTUAL.INC"},PERMUT:{alias:"PERMUTAR"},PERMUTATIONA:{alias:"PERMUTAR.R"},PHI:{alias:"PHII"},PHONETIC:{alias:"FON\xc9TICA"},PMT:{alias:"PGTO"},"POISSON.DIST":{alias:"DIST.POISSON"},POWER:{alias:"POT\xcaNCIA"},PPMT:{alias:"PPGTO"},PRICE:{alias:"PRE\xc7O"},PRICEDISC:{alias:"PRE\xc7ODESC"},PRICEMAT:{alias:"PRE\xc7OVENC"},PRODUCT:{alias:"PRODUTO"},PROPER:{alias:"INICIAL.MAI\xdaSCULA"},PV:{alias:"VA"},QUARTILE:{alias:"QUARTIL"},"QUARTILE.EXC":{alias:"QUARTIL.EXC"},"QUARTILE.INC":{alias:"QUARTIL.INC"},QUOTIENT:{alias:"QUOCIENTE"},RADIANS:{alias:"RADIANOS"},RAND:{alias:"ALEAT\xd3RIO"},RANDBETWEEN:{alias:"ALEAT\xd3RIOENTRE"},RANK:{alias:"ORDEM"},"RANK.AVG":{alias:"ORDEM.M\xc9D"},"RANK.EQ":{alias:"ORDEM.EQ"},RATE:{alias:"TAXA"},RECEIVED:{alias:"RECEBER"},"REGISTER.ID":{alias:"REGISTO.ID"},REPLACE:{alias:"SUBSTITUIR"},REPLACEB:{alias:"SUBSTITUIRB"},REPT:{alias:"REPETIR"},RIGHT:{alias:"DIREITA"},RIGHTB:{alias:"DIREITAB"},ROMAN:{alias:"ROMANO"},ROUND:{alias:"ARREDONDAR"},ROUNDDOWN:{alias:"ARRED.PARA.BAIXO"},ROUNDUP:{alias:"ARRED.PARA.CIMA"},ROW:{alias:"LINHA"},ROWS:{alias:"LINS"},RRI:{alias:"DEVOLVERTAXAJUROS"},RSQ:{alias:"RQUAD"},SEARCH:{alias:"PROCURAR"},SEARCHB:{alias:"PROCURARB"},SECOND:{alias:"SEGUNDO"},SERIESSUM:{alias:"SOMAS\xc9RIE"},SHEET:{alias:"FOLHA"},SHEETS:{alias:"FOLHAS"},SIGN:{alias:"SINAL"},SIN:{alias:"SEN"},SINH:{alias:"SENH"},SKEW:{alias:"DISTOR\xc7\xc3O"},"SKEW.P":{alias:"DISTOR\xc7\xc3O.P"},SLN:{alias:"AMORT"},SLOPE:{alias:"DECLIVE"},SMALL:{alias:"MENOR"},SQRT:{alias:"RAIZQ"},SQRTPI:{alias:"RAIZPI"},STANDARDIZE:{alias:"NORMALIZAR"},STDEV:{alias:"DESVPAD"},"STDEV.P":{alias:"DESVPAD.P"},"STDEV.S":{alias:"DESVPAD.S"},STDEVA:{alias:"DESVPADA"},STDEVP:{alias:"DESVPADP"},STDEVPA:{alias:"DESVPADPA"},STEYX:{alias:"EPADYX"},SUBSTITUTE:{alias:"SUBST"},SUM:{alias:"SOMA"},SUMIF:{alias:"SOMA.SE"},SUMIFS:{alias:"SOMA.SE.S"},SUMPRODUCT:{alias:"SOMARPRODUTO"},SUMSQ:{alias:"SOMARQUAD"},SUMX2MY2:{alias:"SOMAX2DY2"},SUMX2PY2:{alias:"SOMAX2SY2"},SUMXMY2:{alias:"SOMAXMY2"},SYD:{alias:"AMORTD"},"T.DIST":{alias:"DIST.T"},"T.DIST.2T":{alias:"DIST.T.2C"},"T.DIST.RT":{alias:"DIST.T.DIR"},"T.INV":{alias:"INV.T"},"T.INV.2T":{alias:"INV.T.2C"},"T.TEST":{alias:"TESTET"},TBILLEQ:{alias:"OTN"},TBILLPRICE:{alias:"OTNVALOR"},TBILLYIELD:{alias:"OTNLUCRO"},TDIST:{alias:"DISTT"},TEXT:{alias:"TEXTO"},TIME:{alias:"TEMPO"},TIMEVALUE:{alias:"VALOR.TEMPO"},TINV:{alias:"INVT"},TODAY:{alias:"HOJE"},TRANSPOSE:{alias:"TRANSPOR"},TREND:{alias:"TEND\xcaNCIA"},TRIM:{alias:"COMPACTAR"},TRIMMEAN:{alias:"M\xc9DIA.INTERNA"},TRUE:{alias:"VERDADEIRO"},TRUNC:{alias:"TRUNCAR"},TTEST:{alias:"TESTET"},TYPE:{alias:"TIPO"},UNICHAR:{alias:"UNICAR\xc1T"},UPPER:{alias:"MAI\xdaSCULAS"},VALUE:{alias:"VALOR"},VDB:{alias:"BDV"},VLOOKUP:{alias:"PROCV"},WEBSERVICE:{alias:"SERVI\xc7OWEB"},WEEKDAY:{alias:"DIA.SEMANA"},WEEKNUM:{alias:"N\xdaMSEMANA"},"WEIBULL.DIST":{alias:"DIST.WEIBULL"},WORKDAY:{alias:"DIA.TRABALHO"},"WORKDAY.INTL":{alias:"DIATRABALHO.INTL"},XIRR:{alias:"XTIR"},XNPV:{alias:"XVAL"},YEAR:{alias:"ANO"},YEARFRAC:{alias:"FRAC\xc7\xc3OANO"},YIELD:{alias:"LUCRO"},YIELDDISC:{alias:"LUCRODESC"},YIELDMAT:{alias:"LUCROVENC"},"Z.TEST":{alias:"TESTE.Z"},ZTEST:{alias:"TESTEZ"}},tableFunctionsMapping:{"#All":"#Tudo","#Data":"#Dados","#Headers":"#Cabe\xe7alhos","#Totals":"#Totais","#This row":"#Esta Linha"},clacErrorMapping:{"#NULL!":"#NULO!","#DIV/0!":"#DIV/0!","#VALUE!":"#VALOR!","#REF!":"#REF!","#NAME?":"#NOME?","#N/A!":"#N/D","#NUM!":"#NUM!"},booleanMapping:{boolean_true:"VERDADEIRO",boolean_false:"FALSO"}}},"./src/languagePackages/res.Russian.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"\u041d\u0410\u041a\u041e\u041f\u0414\u041e\u0425\u041e\u0414"},ACCRINTM:{alias:"\u041d\u0410\u041a\u041e\u041f\u0414\u041e\u0425\u041e\u0414\u041f\u041e\u0413\u0410\u0428"},ADDRESS:{alias:"\u0410\u0414\u0420\u0415\u0421"},AGGREGATE:{alias:"\u0410\u0413\u0420\u0415\u0413\u0410\u0422"},AMORDEGRC:{alias:"\u0410\u041c\u041e\u0420\u0423\u041c"},AMORLINC:{alias:"\u0410\u041c\u041e\u0420\u0423\u0412"},AND:{alias:"\u0418"},ARABIC:{alias:"\u0410\u0420\u0410\u0411\u0421\u041a\u041e\u0415"},AREAS:{alias:"\u041e\u0411\u041b\u0410\u0421\u0422\u0418"},AVEDEV:{alias:"\u0421\u0420\u041e\u0422\u041a\u041b"},AVERAGE:{alias:"\u0421\u0420\u0417\u041d\u0410\u0427"},AVERAGEA:{alias:"\u0421\u0420\u0417\u041d\u0410\u0427\u0410"},AVERAGEIF:{alias:"\u0421\u0420\u0417\u041d\u0410\u0427\u0415\u0421\u041b\u0418"},AVERAGEIFS:{alias:"\u0421\u0420\u0417\u041d\u0410\u0427\u0415\u0421\u041b\u0418\u041c\u041d"},BAHTTEXT:{alias:"\u0411\u0410\u0422\u0422\u0415\u041a\u0421\u0422"},BASE:{alias:"\u041e\u0421\u041d\u041e\u0412\u0410\u041d\u0418\u0415"},BESSELI:{alias:"\u0411\u0415\u0421\u0421\u0415\u041b\u042c.I"},BESSELJ:{alias:"\u0411\u0415\u0421\u0421\u0415\u041b\u042c.J"},BESSELK:{alias:"\u0411\u0415\u0421\u0421\u0415\u041b\u042c.K"},BESSELY:{alias:"\u0411\u0415\u0421\u0421\u0415\u041b\u042c.Y"},"BETA.DIST":{alias:"\u0411\u0415\u0422\u0410.\u0420\u0410\u0421\u041f"},"BETA.INV":{alias:"\u0411\u0415\u0422\u0410.\u041e\u0411\u0420"},BETADIST:{alias:"\u0411\u0415\u0422\u0410\u0420\u0410\u0421\u041f"},BETAINV:{alias:"\u0411\u0415\u0422\u0410\u041e\u0411\u0420"},BIN2DEC:{alias:"\u0414\u0412.\u0412.\u0414\u0415\u0421"},BIN2HEX:{alias:"\u0414\u0412.\u0412.\u0428\u0415\u0421\u0422\u041d"},BIN2OCT:{alias:"\u0414\u0412.\u0412.\u0412\u041e\u0421\u042c\u041c"},"BINOM.DIST":{alias:"\u0411\u0418\u041d\u041e\u041c.\u0420\u0410\u0421\u041f"},"BINOM.INV":{alias:"\u0411\u0418\u041d\u041e\u041c.\u041e\u0411\u0420"},BINOMDIST:{alias:"\u0411\u0418\u041d\u041e\u041c\u0420\u0410\u0421\u041f"},CALL:{alias:"\u0412\u042b\u0417\u0412\u0410\u0422\u042c"},CEILING:{alias:"\u041e\u041a\u0420\u0412\u0412\u0415\u0420\u0425"},"CEILING.MATH":{alias:"\u041e\u041a\u0420\u0412\u0412\u0415\u0420\u0425.\u041c\u0410\u0422"},"CEILING.PRECISE":{alias:"\u041e\u041a\u0420\u0412\u0412\u0415\u0420\u0425.\u0422\u041e\u0427\u041d"},CELL:{alias:"\u042f\u0427\u0415\u0419\u041a\u0410"},CHAR:{alias:"\u0421\u0418\u041c\u0412\u041e\u041b"},CHIDIST:{alias:"\u0425\u04182\u0420\u0410\u0421\u041f"},CHIINV:{alias:"\u0425\u04182\u041e\u0411\u0420"},"CHISQ.DIST":{alias:"\u0425\u04182.\u0420\u0410\u0421\u041f"},"CHISQ.DIST.RT":{alias:"\u0425\u04182.\u0420\u0410\u0421\u041f.\u041f\u0425"},"CHISQ.INV":{alias:"\u0425\u04182.\u041e\u0411\u0420"},"CHISQ.INV.RT":{alias:"\u0425\u04182.\u041e\u0411\u0420.\u041f\u0425"},"CHISQ.TEST":{alias:"\u0425\u04182.\u0422\u0415\u0421\u0422"},CHITEST:{alias:"\u0425\u04182\u0422\u0415\u0421\u0422"},CHOOSE:{alias:"\u0412\u042b\u0411\u041e\u0420"},CLEAN:{alias:"\u041f\u0415\u0427\u0421\u0418\u041c\u0412"},CODE:{alias:"\u041a\u041e\u0414\u0421\u0418\u041c\u0412"},COLUMN:{alias:"\u0421\u0422\u041e\u041b\u0411\u0415\u0426"},COLUMNS:{alias:"\u0427\u0418\u0421\u041b\u0421\u0422\u041e\u041b\u0411"},COMBIN:{alias:"\u0427\u0418\u0421\u041b\u041a\u041e\u041c\u0411"},COMPLEX:{alias:"\u041a\u041e\u041c\u041f\u041b\u0415\u041a\u0421\u041d"},CONCATENATE:{alias:"\u0421\u0426\u0415\u041f\u0418\u0422\u042c"},CONFIDENCE:{alias:"\u0414\u041e\u0412\u0415\u0420\u0418\u0422"},"CONFIDENCE.NORM":{alias:"\u0414\u041e\u0412\u0415\u0420\u0418\u0422.\u041d\u041e\u0420\u041c"},"CONFIDENCE.T":{alias:"\u0414\u041e\u0412\u0415\u0420\u0418\u0422.\u0421\u0422\u042c\u042e\u0414\u0415\u041d\u0422"},CONVERT:{alias:"\u041f\u0420\u0415\u041e\u0411\u0420"},CORREL:{alias:"\u041a\u041e\u0420\u0420\u0415\u041b"},COUNT:{alias:"\u0421\u0427\u0401\u0422"},COUNTA:{alias:"\u0421\u0427\u0401\u0422\u0417"},COUNTBLANK:{alias:"\u0421\u0427\u0418\u0422\u0410\u0422\u042c\u041f\u0423\u0421\u0422\u041e\u0422\u042b"},COUNTIF:{alias:"\u0421\u0427\u0401\u0422\u0415\u0421\u041b\u0418"},COUNTIFS:{alias:"\u0421\u0427\u0401\u0422\u0415\u0421\u041b\u0418\u041c\u041d"},COUPDAYBS:{alias:"\u0414\u041d\u0415\u0419\u041a\u0423\u041f\u041e\u041d\u0414\u041e"},COUPDAYS:{alias:"\u0414\u041d\u0415\u0419\u041a\u0423\u041f\u041e\u041d"},COUPDAYSNC:{alias:"\u0414\u041d\u0415\u0419\u041a\u0423\u041f\u041e\u041d\u041f\u041e\u0421\u041b\u0415"},COUPNCD:{alias:"\u0414\u0410\u0422\u0410\u041a\u0423\u041f\u041e\u041d\u041f\u041e\u0421\u041b\u0415"},COUPNUM:{alias:"\u0427\u0418\u0421\u041b\u041a\u0423\u041f\u041e\u041d"},COUPPCD:{alias:"\u0414\u0410\u0422\u0410\u041a\u0423\u041f\u041e\u041d\u0414\u041e"},COVAR:{alias:"\u041a\u041e\u0412\u0410\u0420"},"COVARIANCE.P":{alias:"\u041a\u041e\u0412\u0410\u0420\u0418\u0410\u0426\u0418\u042f.\u0413"},"COVARIANCE.S":{alias:"\u041a\u041e\u0412\u0410\u0420\u0418\u0410\u0426\u0418\u042f.\u0412"},CRITBINOM:{alias:"\u041a\u0420\u0418\u0422\u0411\u0418\u041d\u041e\u041c"},CUBEKPIMEMBER:{alias:"\u041a\u0423\u0411\u042d\u041b\u0415\u041c\u0415\u041d\u0422\u041a\u0418\u041f"},CUBEMEMBER:{alias:"\u041a\u0423\u0411\u042d\u041b\u0415\u041c\u0415\u041d\u0422"},CUBEMEMBERPROPERTY:{alias:"\u041a\u0423\u0411\u041f\u041e\u0420\u042d\u041b\u0415\u041c\u0415\u041d\u0422"},CUBERANKEDMEMBER:{alias:"\u041a\u0423\u0411\u041f\u041e\u0420\u042d\u041b\u0415\u041c\u0415\u041d\u0422"},CUBESET:{alias:"\u041a\u0423\u0411\u041c\u041d\u041e\u0416"},CUBESETCOUNT:{alias:"\u041a\u0423\u0411\u0427\u0418\u0421\u041b\u041e\u042d\u041b\u041c\u041d\u041e\u0416"},CUBEVALUE:{alias:"\u041a\u0423\u0411\u0417\u041d\u0410\u0427\u0415\u041d\u0418\u0415"},CUMIPMT:{alias:"\u041e\u0411\u0429\u041f\u041b\u0410\u0422"},CUMPRINC:{alias:"\u041e\u0411\u0429\u0414\u041e\u0425\u041e\u0414"},DATE:{alias:"\u0414\u0410\u0422\u0410"},DATEVALUE:{alias:"\u0414\u0410\u0422\u0410\u0417\u041d\u0410\u0427"},DAVERAGE:{alias:"\u0414\u0421\u0420\u0417\u041d\u0410\u0427"},DAY:{alias:"\u0414\u0415\u041d\u042c"},DAYS:{alias:"\u0414\u041d\u0415\u0419"},DAYS360:{alias:"\u0414\u041d\u0415\u0419360"},DB:{alias:"\u0424\u0423\u041e"},DCOUNT:{alias:"\u0411\u0421\u0427\u0401\u0422"},DCOUNTA:{alias:"\u0411\u0421\u0427\u0401\u0422\u0410"},DDB:{alias:"\u0414\u0414\u041e\u0411"},DEC2BIN:{alias:"\u0414\u0415\u0421.\u0412.\u0414\u0412"},DEC2HEX:{alias:"\u0414\u0415\u0421.\u0412.\u0428\u0415\u0421\u0422\u041d"},DEC2OCT:{alias:"\u0414\u0415\u0421.\u0412.\u0412\u041e\u0421\u042c\u041c"},DECIMAL:{alias:"\u0414\u0415\u0421"},DEGREES:{alias:"\u0413\u0420\u0410\u0414\u0423\u0421\u042b"},DELTA:{alias:"\u0414\u0415\u041b\u042c\u0422\u0410"},DEVSQ:{alias:"\u041a\u0412\u0410\u0414\u0420\u041e\u0422\u041a\u041b"},DGET:{alias:"\u0411\u0418\u0417\u0412\u041b\u0415\u0427\u042c"},DISC:{alias:"\u0421\u041a\u0418\u0414\u041a\u0410"},DMAX:{alias:"\u0414\u041c\u0410\u041a\u0421"},DMIN:{alias:"\u0414\u041c\u0418\u041d"},DOLLAR:{alias:"\u0420\u0423\u0411\u041b\u042c"},DOLLARDE:{alias:"\u0420\u0423\u0411\u041b\u042c.\u0414\u0415\u0421"},DOLLARFR:{alias:"\u0420\u0423\u0411\u041b\u042c.\u0414\u0420\u041e\u0411\u042c"},DPRODUCT:{alias:"\u0411\u0414\u041f\u0420\u041e\u0418\u0417\u0412\u0415\u0414"},DSTDEV:{alias:"\u0414\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b"},DSTDEVP:{alias:"\u0414\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041f"},DSUM:{alias:"\u0411\u0414\u0421\u0423\u041c\u041c"},DURATION:{alias:"\u0414\u041b\u0418\u0422"},DVAR:{alias:"\u0411\u0414\u0414\u0418\u0421\u041f"},DVARP:{alias:"\u0411\u0414\u0414\u0418\u0421\u041f\u041f"},EDATE:{alias:"\u0414\u0410\u0422\u0410\u041c\u0415\u0421"},EFFECT:{alias:"\u042d\u0424\u0424\u0415\u041a\u0422"},ENCODEURL:{alias:"\u041a\u041e\u0414\u0418\u0420.URL"},EOMONTH:{alias:"\u041a\u041e\u041d\u041c\u0415\u0421\u042f\u0426\u0410"},ERF:{alias:"\u0424\u041e\u0428"},"ERF.PRECISE":{alias:"\u0424\u041e\u0428.\u0422\u041e\u0427\u041d"},ERFC:{alias:"\u0414\u0424\u041e\u0428"},"ERFC.PRECISE":{alias:"\u0414\u0424\u041e\u0428.\u0422\u041e\u0427\u041d"},"ERROR.TYPE":{alias:"\u0422\u0418\u041f.\u041e\u0428\u0418\u0411\u041a\u0418"},EUROCONVERT:{alias:"\u041f\u0415\u0420\u0415\u0421\u0427\u0415\u0422\u0415\u0412\u0420\u041e"},EVEN:{alias:"\u0427\u0401\u0422\u041d"},EXACT:{alias:"\u0421\u041e\u0412\u041f\u0410\u0414"},"EXPON.DIST":{alias:"\u042d\u041a\u0421\u041f.\u0420\u0410\u0421\u041f"},EXPONDIST:{alias:"\u042d\u041a\u0421\u041f\u0420\u0410\u0421\u041f"},"F.DIST":{alias:"F.\u0420\u0410\u0421\u041f"},"F.DIST.RT":{alias:"F.\u0420\u0410\u0421\u041f.\u041f\u0425"},"F.INV":{alias:"F\u0420\u0410\u0421\u041f\u041e\u0411\u0420"},"F.INV.RT":{alias:"F.\u041e\u0411\u0420.\u041f\u0425"},"F.TEST":{alias:"\u0424\u0422\u0415\u0421\u0422"},FACT:{alias:"\u0424\u0410\u041a\u0422\u0420"},FACTDOUBLE:{alias:"\u0414\u0412\u0424\u0410\u041a\u0422\u0420"},FALSE:{alias:"\u041b\u041e\u0416\u042c"},FDIST:{alias:"F\u0420\u0410\u0421\u041f"},FILTERXML:{alias:"\u0424\u0418\u041b\u042c\u0422\u0420.XML"},FIND:{alias:"\u041d\u0410\u0419\u0422\u0418"},FINDB:{alias:"\u041d\u0410\u0419\u0422\u0418\u0411"},FINV:{alias:"F\u0420\u0410\u0421\u041f\u041e\u0411\u0420"},FISHER:{alias:"\u0424\u0418\u0428\u0415\u0420"},FISHERINV:{alias:"\u0424\u0418\u0428\u0415\u0420\u041e\u0411\u0420"},FIXED:{alias:"\u0424\u0418\u041a\u0421\u0418\u0420\u041e\u0412\u0410\u041d\u041d\u042b\u0419"},FLOOR:{alias:"\u041e\u041a\u0420\u0412\u041d\u0418\u0417"},"FLOOR.PRECISE":{alias:"\u041e\u041a\u0420\u0412\u041d\u0418\u0417.\u0422\u041e\u0427\u041d"},FORECAST:{alias:"\u041f\u0420\u0415\u0414\u0421\u041a\u0410\u0417"},FREQUENCY:{alias:"\u0427\u0410\u0421\u0422\u041e\u0422\u0410"},FTEST:{alias:"\u0424\u0422\u0415\u0421\u0422"},FV:{alias:"\u0411\u0421"},FVSCHEDULE:{alias:"\u0411\u0417\u0420\u0410\u0421\u041f\u0418\u0421"},GAMMA:{alias:"\u0413\u0410\u041c\u041c\u0410"},"GAMMA.DIST":{alias:"\u0413\u0410\u041c\u041c\u0410.\u0420\u0410\u0421\u041f"},"GAMMA.INV":{alias:"\u0413\u0410\u041c\u041c\u0410.\u041e\u0411\u0420"},GAMMADIST:{alias:"\u0413\u0410\u041c\u041c\u0410\u0420\u0410\u0421\u041f"},GAMMAINV:{alias:"\u0413\u0410\u041c\u041c\u0410\u041e\u0411\u0420"},GAMMALN:{alias:"\u0413\u0410\u041c\u041c\u0410\u041d\u041b\u041e\u0413"},"GAMMALN.PRECISE":{alias:"\u0413\u0410\u041c\u041c\u0410\u041d\u041b\u041e\u0413.\u0422\u041e\u0427\u041d"},GAUSS:{alias:"\u0413\u0410\u0423\u0421\u0421"},GCD:{alias:"\u041d\u041e\u0414"},GEOMEAN:{alias:"\u0421\u0420\u0413\u0415\u041e\u041c"},GESTEP:{alias:"\u041f\u041e\u0420\u041e\u0413"},GETPIVOTDATA:{alias:"\u041f\u041e\u041b\u0423\u0427\u0418\u0422\u042c.\u0414\u0410\u041d\u041d\u042b\u0415.\u0421\u0412\u041e\u0414\u041d\u041e\u0419.\u0422\u0410\u0411\u041b\u0418\u0426\u042b"},GROWTH:{alias:"\u0420\u041e\u0421\u0422"},HARMEAN:{alias:"\u0421\u0420\u0413\u0410\u0420\u041c"},HEX2BIN:{alias:"\u0428\u0415\u0421\u0422\u041d.\u0412.\u0414\u0412"},HEX2DEC:{alias:"\u0428\u0415\u0421\u0422\u041d.\u0412.\u0414\u0415\u0421"},HEX2OCT:{alias:"\u0428\u0415\u0421\u0422\u041d.\u0412.\u0412\u041e\u0421\u042c\u041c"},HLOOKUP:{alias:"\u0413\u041f\u0420"},HOUR:{alias:"\u0427\u0410\u0421"},HYPERLINK:{alias:"\u0413\u0418\u041f\u0415\u0420\u0421\u0421\u042b\u041b\u041a\u0410"},"HYPGEOM.DIST":{alias:"\u0413\u0418\u041f\u0415\u0420\u0413\u0415\u041e\u041c\u0415\u0422"},HYPGEOMDIST:{alias:"\u0413\u0418\u041f\u0415\u0420\u0413\u0415\u041e\u041c\u0415\u0422"},IF:{alias:"\u0415\u0421\u041b\u0418"},IFERROR:{alias:"\u0415\u0421\u041b\u0418\u041e\u0428\u0418\u0411\u041a\u0410"},IFNA:{alias:"\u0415\u0421\u041b\u0418\u041d\u0414"},IMABS:{alias:"\u041c\u041d\u0418\u041c.ABS"},IMAGINARY:{alias:"\u041c\u041d\u0418\u041c.\u0427\u0410\u0421\u0422\u042c"},IMARGUMENT:{alias:"\u041c\u041d\u0418\u041c.\u0410\u0420\u0413\u0423\u041c\u0415\u041d\u0422"},IMCONJUGATE:{alias:"\u041c\u041d\u0418\u041c.\u0421\u041e\u041f\u0420\u042f\u0416"},IMCOS:{alias:"\u041c\u041d\u0418\u041c.COS"},IMCOSH:{alias:"\u041c\u041d\u0418\u041c.COSH"},IMCOT:{alias:"\u041c\u041d\u0418\u041c.COT"},IMCSC:{alias:"\u041c\u041d\u0418\u041c.CSC"},IMCSCH:{alias:"\u041c\u041d\u0418\u041c.CSCH"},IMDIV:{alias:"\u041c\u041d\u0418\u041c.\u0414\u0415\u041b"},IMEXP:{alias:"\u041c\u041d\u0418\u041c.EXP"},IMLN:{alias:"\u041c\u041d\u0418\u041c.LN"},IMLOG10:{alias:"\u041c\u041d\u0418\u041c.LOG10"},IMLOG2:{alias:"\u041c\u041d\u0418\u041c.LOG2"},IMPOWER:{alias:"\u041c\u041d\u0418\u041c.\u0421\u0422\u0415\u041f\u0415\u041d\u042c"},IMPRODUCT:{alias:"\u041c\u041d\u0418\u041c.\u041f\u0420\u041e\u0418\u0417\u0412\u0415\u0414"},IMREAL:{alias:"\u041c\u041d\u0418\u041c.\u0412\u0415\u0429"},IMSEC:{alias:"\u041c\u041d\u0418\u041c.SEC"},IMSECH:{alias:"\u041c\u041d\u0418\u041c.SECH"},IMSIN:{alias:"\u041c\u041d\u0418\u041c.SIN"},IMSINH:{alias:"\u041c\u041d\u0418\u041c.SINH"},IMSQRT:{alias:"\u041c\u041d\u0418\u041c.\u041a\u041e\u0420\u0415\u041d\u042c"},IMSUB:{alias:"\u041c\u041d\u0418\u041c.\u0420\u0410\u0417\u041d"},IMSUM:{alias:"\u041c\u041d\u0418\u041c.\u0421\u0423\u041c\u041c"},IMTAN:{alias:"\u041c\u041d\u0418\u041c.TAN"},INDEX:{alias:"\u0418\u041d\u0414\u0415\u041a\u0421"},INDIRECT:{alias:"\u0414\u0412\u0421\u0421\u042b\u041b"},INFO:{alias:"\u0418\u041d\u0424\u041e\u0420\u041c"},INT:{alias:"\u0426\u0415\u041b\u041e\u0415"},INTERCEPT:{alias:"\u041e\u0422\u0420\u0415\u0417\u041e\u041a"},INTRATE:{alias:"\u0418\u041d\u041e\u0420\u041c\u0410"},IPMT:{alias:"\u041f\u0420\u041f\u041b\u0422"},IRR:{alias:"\u0412\u0421\u0414"},ISBLANK:{alias:"\u0415\u041f\u0423\u0421\u0422\u041e"},ISERR:{alias:"\u0415\u041e\u0428"},ISERROR:{alias:"\u0415\u041e\u0428\u0418\u0411\u041a\u0410"},ISEVEN:{alias:"\u0415\u0427\u0401\u0422\u041d"},ISFORMULA:{alias:"\u0415\u0424\u041e\u0420\u041c\u0423\u041b\u0410"},ISLOGICAL:{alias:"\u0415\u041b\u041e\u0413\u0418\u0427"},ISNA:{alias:"\u0415\u041d\u0414"},ISNONTEXT:{alias:"\u0415\u041d\u0415\u0422\u0415\u041a\u0421\u0422"},ISNUMBER:{alias:"\u0415\u0427\u0418\u0421\u041b\u041e"},"ISO.CEILING":{alias:"ISO.\u041e\u041a\u0420\u0412\u0412\u0415\u0420\u0425"},ISODD:{alias:"\u0415\u041d\u0415\u0427\u0401\u0422"},ISOWEEKNUM:{alias:"ISO.\u041d\u041e\u041c\u041d\u0415\u0414"},ISPMT:{alias:"\u041f\u0420\u041e\u0426\u041f\u041b\u0410\u0422"},ISREF:{alias:"\u0415\u0421\u0421\u042b\u041b\u041a\u0410"},ISTEXT:{alias:"\u0415\u0422\u0415\u041a\u0421\u0422"},KURT:{alias:"\u042d\u041a\u0421\u0426\u0415\u0421\u0421"},LARGE:{alias:"\u041d\u0410\u0418\u0411\u041e\u041b\u042c\u0428\u0418\u0419"},LCM:{alias:"\u041d\u041e\u041a"},LEFT:{alias:"\u041b\u0415\u0412\u0421\u0418\u041c\u0412"},LEFTB:{alias:"\u041b\u0415\u0412\u0411"},LEN:{alias:"\u0414\u041b\u0421\u0422\u0420"},LENB:{alias:"\u0414\u041b\u0418\u041d\u0411"},LINEST:{alias:"\u041b\u0418\u041d\u0415\u0419\u041d"},LOGEST:{alias:"\u041b\u0413\u0420\u0424\u041f\u0420\u0418\u0411\u041b"},LOGINV:{alias:"\u041b\u041e\u0413\u041d\u041e\u0420\u041c\u041e\u0411\u0420"},"LOGNORM.DIST":{alias:"\u041b\u041e\u0413\u041d\u041e\u0420\u041c.\u0420\u0410\u0421\u041f"},"LOGNORM.INV":{alias:"\u041b\u041e\u0413\u041d\u041e\u0420\u041c.\u041e\u0411\u0420"},LOGNORMDIST:{alias:"\u041b\u041e\u0413\u041d\u041e\u0420\u041c\u0420\u0410\u0421\u041f"},LOOKUP:{alias:"\u041f\u0420\u041e\u0421\u041c\u041e\u0422\u0420"},LOWER:{alias:"\u0421\u0422\u0420\u041e\u0427\u041d"},MATCH:{alias:"\u041f\u041e\u0418\u0421\u041a\u041f\u041e\u0417"},MAX:{alias:"\u041c\u0410\u041a\u0421"},MAXA:{alias:"\u041c\u0410\u041a\u0421\u0410"},MDETERM:{alias:"\u041c\u041e\u041f\u0420\u0415\u0414"},MDURATION:{alias:"\u041c\u0414\u041b\u0418\u0422"},MEDIAN:{alias:"\u041c\u0415\u0414\u0418\u0410\u041d\u0410"},MID:{alias:"\u041f\u0421\u0422\u0420"},MIDB:{alias:"\u041f\u0421\u0422\u0420\u0411"},MIN:{alias:"\u041c\u0418\u041d"},MINA:{alias:"\u041c\u0418\u041d\u0410"},MINUTE:{alias:"\u041c\u0418\u041d\u0423\u0422\u042b"},MINVERSE:{alias:"\u041c\u041e\u0411\u0420"},MIRR:{alias:"\u041c\u0412\u0421\u0414"},MMULT:{alias:"\u041c\u0423\u041c\u041d\u041e\u0416"},MOD:{alias:"\u041e\u0421\u0422\u0410\u0422"},MODE:{alias:"\u041c\u041e\u0414\u0410"},"MODE.MULT":{alias:"\u041c\u041e\u0414\u0410.\u041d\u0421\u041a"},"MODE.SNGL":{alias:"\u041c\u041e\u0414\u0410.\u041e\u0414\u041d"},MONTH:{alias:"\u041c\u0415\u0421\u042f\u0426"},MROUND:{alias:"\u041e\u041a\u0420\u0423\u0413\u041b\u0422"},MULTINOMIAL:{alias:"\u041c\u0423\u041b\u042c\u0422\u0418\u041d\u041e\u041c"},MUNIT:{alias:"\u0415\u0414\u041c\u0410\u0422\u0420\u0418\u0426\u0410"},N:{alias:"\u0427"},NA:{alias:"\u041d\u0414"},"NEGBINOM.DIST":{alias:"\u041e\u0422\u0420\u0411\u0418\u041d\u041e\u041c.\u0420\u0410\u0421\u041f"},NEGBINOMDIST:{alias:"\u041e\u0422\u0420\u0411\u0418\u041d\u041e\u041c\u0420\u0410\u0421\u041f"},NETWORKDAYS:{alias:"\u0427\u0418\u0421\u0422\u0420\u0410\u0411\u0414\u041d\u0418"},"NETWORKDAYS.INTL":{alias:"\u0427\u0418\u0421\u0422\u0420\u0410\u0411\u0414\u041d\u0418.\u041c\u0415\u0416\u0414"},NOMINAL:{alias:"\u041d\u041e\u041c\u0418\u041d\u0410\u041b"},"NORM.DIST":{alias:"\u041d\u041e\u0420\u041c.\u0420\u0410\u0421\u041f"},"NORM.INV":{alias:"\u041d\u041e\u0420\u041c.\u041e\u0411\u0420"},"NORM.S.DIST":{alias:"\u041d\u041e\u0420\u041c.\u0421\u0422.\u0420\u0410\u0421\u041f"},"NORM.S.INV":{alias:"\u041d\u041e\u0420\u041c.\u0421\u0422.\u041e\u0411\u0420"},NORMDIST:{alias:"\u041d\u041e\u0420\u041c\u0420\u0410\u0421\u041f"},NORMINV:{alias:"\u041d\u041e\u0420\u041c\u041e\u0411\u0420"},NORMSDIST:{alias:"\u041d\u041e\u0420\u041c\u0421\u0422\u0420\u0410\u0421\u041f"},NORMSINV:{alias:"\u041d\u041e\u0420\u041c\u0421\u0422\u041e\u0411\u0420"},NOT:{alias:"\u041d\u0415"},NOW:{alias:"\u0422\u0414\u0410\u0422\u0410"},NPER:{alias:"\u041a\u041f\u0415\u0420"},NPV:{alias:"\u0427\u041f\u0421"},NUMBERVALUE:{alias:"\u0427\u0418\u0421\u041b\u0417\u041d\u0410\u0427"},OCT2BIN:{alias:"\u0412\u041e\u0421\u042c\u041c.\u0412.\u0414\u0412"},OCT2DEC:{alias:"\u0412\u041e\u0421\u042c\u041c.\u0412.\u0414\u0415\u0421"},OCT2HEX:{alias:"\u0412\u041e\u0421\u042c\u041c.\u0412.\u0428\u0415\u0421\u0422\u041d"},ODD:{alias:"\u041d\u0415\u0427\u0401\u0422"},ODDFPRICE:{alias:"\u0426\u0415\u041d\u0410\u041f\u0415\u0420\u0412\u041d\u0415\u0420\u0415\u0413"},ODDFYIELD:{alias:"\u0414\u041e\u0425\u041e\u0414\u041f\u0415\u0420\u0412\u041d\u0415\u0420\u0415\u0413"},ODDLPRICE:{alias:"\u0426\u0415\u041d\u0410\u041f\u041e\u0421\u041b\u041d\u0415\u0420\u0415\u0413"},ODDLYIELD:{alias:"\u0414\u041e\u0425\u041e\u0414\u041f\u041e\u0421\u041b\u041d\u0415\u0420\u0415\u0413"},OFFSET:{alias:"\u0421\u041c\u0415\u0429"},OR:{alias:"\u0418\u041b\u0418"},PERCENTILE:{alias:"\u041f\u0415\u0420\u0421\u0415\u041d\u0422\u0418\u041b\u042c"},"PERCENTILE.EXC":{alias:"\u041f\u0420\u041e\u0426\u0415\u041d\u0422\u0418\u041b\u042c.\u0418\u0421\u041a\u041b"},"PERCENTILE.INC":{alias:"\u041f\u0420\u041e\u0426\u0415\u041d\u0422\u0418\u041b\u042c.\u0412\u041a\u041b"},PERCENTRANK:{alias:"\u041f\u0420\u041e\u0426\u0415\u041d\u0422\u0420\u0410\u041d\u0413"},"PERCENTRANK.EXC":{alias:"\u041f\u0420\u041e\u0426\u0415\u041d\u0422\u0420\u0410\u041d\u0413.\u0418\u0421\u041a\u041b"},"PERCENTRANK.INC":{alias:"\u041f\u0420\u041e\u0426\u0415\u041d\u0422\u0420\u0410\u041d\u0413.\u0412\u041a\u041b"},PERMUT:{alias:"\u041f\u0415\u0420\u0415\u0421\u0422"},PI:{alias:"\u041f\u0418"},PMT:{alias:"\u041f\u041b\u0422"},POISSON:{alias:"\u041f\u0423\u0410\u0421\u0421\u041e\u041d"},"POISSON.DIST":{alias:"\u041f\u0423\u0410\u0421\u0421\u041e\u041d.\u0420\u0410\u0421\u041f"},POWER:{alias:"\u0421\u0422\u0415\u041f\u0415\u041d\u042c"},PPMT:{alias:"\u041e\u0421\u041f\u041b\u0422"},PRICE:{alias:"\u0426\u0415\u041d\u0410"},PRICEDISC:{alias:"\u0426\u0415\u041d\u0410\u0421\u041a\u0418\u0414\u041a\u0410"},PRICEMAT:{alias:"\u0426\u0415\u041d\u0410\u041f\u041e\u0413\u0410\u0428"},PROB:{alias:"\u0412\u0415\u0420\u041e\u042f\u0422\u041d\u041e\u0421\u0422\u042c"},PRODUCT:{alias:"\u041f\u0420\u041e\u0418\u0417\u0412\u0415\u0414"},PROPER:{alias:"\u041f\u0420\u041e\u041f\u041d\u0410\u0427"},PV:{alias:"\u041f\u0421"},QUARTILE:{alias:"\u041a\u0412\u0410\u0420\u0422\u0418\u041b\u042c"},"QUARTILE.EXC":{alias:"\u041a\u0412\u0410\u0420\u0422\u0418\u041b\u042c.\u0418\u0421\u041a\u041b"},"QUARTILE.INC":{alias:"\u041a\u0412\u0410\u0420\u0422\u0418\u041b\u042c.\u0412\u041a\u041b"},QUOTIENT:{alias:"\u0427\u0410\u0421\u0422\u041d\u041e\u0415"},RADIANS:{alias:"\u0420\u0410\u0414\u0418\u0410\u041d\u042b"},RAND:{alias:"\u0421\u041b\u0427\u0418\u0421"},RANDBETWEEN:{alias:"\u0421\u041b\u0423\u0427\u041c\u0415\u0416\u0414\u0423"},RANK:{alias:"\u0420\u0410\u041d\u0413"},"RANK.AVG":{alias:"\u0420\u0410\u041d\u0413.\u0421\u0420"},"RANK.EQ":{alias:"\u0420\u0410\u041d\u0413.\u0420\u0412"},RATE:{alias:"\u0421\u0422\u0410\u0412\u041a\u0410"},RECEIVED:{alias:"\u041f\u041e\u041b\u0423\u0427\u0415\u041d\u041e"},"REGISTER.ID":{alias:"\u0420\u0415\u0413\u0418\u0421\u0422\u0420\u0410\u0422\u041e\u0420.\u0418\u0414"},REPLACE:{alias:"\u0417\u0410\u041c\u0415\u041d\u0418\u0422\u042c"},REPLACEB:{alias:"\u0417\u0410\u041c\u0415\u041d\u0418\u0422\u042c\u0411"},REPT:{alias:"\u041f\u041e\u0412\u0422\u041e\u0420"},RIGHT:{alias:"\u041f\u0420\u0410\u0412\u0421\u0418\u041c\u0412"},RIGHTB:{alias:"\u041f\u0420\u0410\u0412\u0411"},ROMAN:{alias:"\u0420\u0418\u041c\u0421\u041a\u041e\u0415"},ROUND:{alias:"\u041e\u041a\u0420\u0423\u0413\u041b"},ROUNDDOWN:{alias:"\u041e\u041a\u0420\u0423\u0413\u041b\u0412\u041d\u0418\u0417"},ROUNDUP:{alias:"\u041e\u041a\u0420\u0423\u0413\u041b\u0412\u0412\u0415\u0420\u0425"},ROW:{alias:"\u0421\u0422\u0420\u041e\u041a\u0410"},ROWS:{alias:"\u0427\u0421\u0422\u0420\u041e\u041a"},RSQ:{alias:"\u041a\u0412\u041f\u0418\u0420\u0421\u041e\u041d"},RTD:{alias:"\u0414\u0420\u0412"},SEARCH:{alias:"\u041f\u041e\u0418\u0421\u041a"},SEARCHB:{alias:"\u041f\u041e\u0418\u0421\u041a\u0411"},SECOND:{alias:"\u0421\u0415\u041a\u0423\u041d\u0414\u042b"},SERIESSUM:{alias:"\u0420\u042f\u0414.\u0421\u0423\u041c\u041c"},SIGN:{alias:"\u0417\u041d\u0410\u041a"},SKEW:{alias:"\u0421\u041a\u041e\u0421"},SLN:{alias:"\u0410\u041f\u041b"},SLOPE:{alias:"\u041d\u0410\u041a\u041b\u041e\u041d"},SMALL:{alias:"\u041d\u0410\u0418\u041c\u0415\u041d\u042c\u0428\u0418\u0419"},SQRT:{alias:"\u041a\u041e\u0420\u0415\u041d\u042c"},SQRTPI:{alias:"\u041a\u041e\u0420\u0415\u041d\u042c\u041f\u0418"},STANDARDIZE:{alias:"\u041d\u041e\u0420\u041c\u0410\u041b\u0418\u0417\u0410\u0426\u0418\u042f"},STDEV:{alias:"\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041e\u041d"},"STDEV.P":{alias:"\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041e\u041d\u041f"},"STDEV.S":{alias:"\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041e\u041d.\u0412"},STDEVA:{alias:"\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041e\u041d\u0410"},STDEVP:{alias:"\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041e\u041d\u041f"},STDEVPA:{alias:"\u0421\u0422\u0410\u041d\u0414\u041e\u0422\u041a\u041b\u041e\u041d\u041f\u0410"},STEYX:{alias:"\u0421\u0422\u041e\u0428YX"},SUBSTITUTE:{alias:"\u041f\u041e\u0414\u0421\u0422\u0410\u0412\u0418\u0422\u042c"},SUBTOTAL:{alias:"\u041f\u0420\u041e\u041c\u0415\u0416\u0423\u0422\u041e\u0427\u041d\u042b\u0415.\u0418\u0422\u041e\u0413\u0418"},SUM:{alias:"\u0421\u0423\u041c\u041c"},SUMIF:{alias:"\u0421\u0423\u041c\u041c\u0415\u0421\u041b\u0418"},SUMIFS:{alias:"\u0421\u0423\u041c\u041c\u0415\u0421\u041b\u0418\u041c\u041d"},SUMPRODUCT:{alias:"\u0421\u0423\u041c\u041c\u041f\u0420\u041e\u0418\u0417\u0412"},SUMSQ:{alias:"\u0421\u0423\u041c\u041c\u041a\u0412"},SUMX2MY2:{alias:"\u0421\u0423\u041c\u041c\u0420\u0410\u0417\u041d\u041a\u0412"},SUMX2PY2:{alias:"\u0421\u0423\u041c\u041c\u0421\u0423\u041c\u041c\u041a\u0412"},SUMXMY2:{alias:"\u0421\u0423\u041c\u041c\u041a\u0412\u0420\u0410\u0417\u041d"},SYD:{alias:"\u0410\u0421\u0427"},T:{alias:"\u0422"},"T.DIST":{alias:"\u0421\u0422\u042c\u042e\u0414\u0420\u0410\u0421\u041f"},"T.DIST.2T":{alias:"\u0421\u0422\u042c\u042e\u0414\u0415\u041d\u0422.\u0420\u0410\u0421\u041f.2\u0425"
},"T.DIST.RT":{alias:"\u0421\u0422\u042c\u042e\u0414\u0415\u041d\u0422.\u0420\u0410\u0421\u041f.\u041f\u0425"},"T.INV":{alias:"\u0421\u0422\u042c\u042e\u0414\u0420\u0410\u0421\u041f\u041e\u0411\u0420"},"T.INV.2T":{alias:"\u0421\u0422\u042c\u042e\u0414\u0415\u041d\u0422.\u041e\u0411\u0420.2\u0425"},"T.TEST":{alias:"\u0422\u0422\u0415\u0421\u0422"},TBILLEQ:{alias:"\u0420\u0410\u0412\u041d\u041e\u041a\u0427\u0415\u041a"},TBILLPRICE:{alias:"\u0426\u0415\u041d\u0410\u041a\u0427\u0415\u041a"},TBILLYIELD:{alias:"\u0414\u041e\u0425\u041e\u0414\u041a\u0427\u0415\u041a"},TDIST:{alias:"\u0421\u0422\u042c\u042e\u0414\u0420\u0410\u0421\u041f"},TEXT:{alias:"\u0422\u0415\u041a\u0421\u0422"},TIME:{alias:"\u0412\u0420\u0415\u041c\u042f"},TIMEVALUE:{alias:"\u0412\u0420\u0415\u041c\u0417\u041d\u0410\u0427"},TINV:{alias:"\u0421\u0422\u042c\u042e\u0414\u0420\u0410\u0421\u041f\u041e\u0411\u0420"},TODAY:{alias:"\u0421\u0415\u0413\u041e\u0414\u041d\u042f"},TRANSPOSE:{alias:"\u0422\u0420\u0410\u041d\u0421\u041f"},TREND:{alias:"\u0422\u0415\u041d\u0414\u0415\u041d\u0426\u0418\u042f"},TRIM:{alias:"\u0421\u0416\u041f\u0420\u041e\u0411\u0415\u041b\u042b"},TRIMMEAN:{alias:"\u0423\u0420\u0415\u0417\u0421\u0420\u0415\u0414\u041d\u0415\u0415"},TRUE:{alias:"\u0418\u0421\u0422\u0418\u041d\u0410"},TRUNC:{alias:"\u041e\u0422\u0411\u0420"},TTEST:{alias:"\u0422\u0422\u0415\u0421\u0422"},TYPE:{alias:"\u0422\u0418\u041f"},UPPER:{alias:"\u041f\u0420\u041e\u041f\u0418\u0421\u041d"},VALUE:{alias:"\u0417\u041d\u0410\u0427\u0415\u041d"},VAR:{alias:"\u0414\u0418\u0421\u041f"},"VAR.P":{alias:"\u0414\u0418\u0421\u041f\u0420"},"VAR.S":{alias:"\u0414\u0418\u0421\u041f.\u0412"},VARA:{alias:"\u0414\u0418\u0421\u041f\u0410"},VARP:{alias:"\u0414\u0418\u0421\u041f\u0420"},VARPA:{alias:"\u0414\u0418\u0421\u041f\u0420\u0410"},VDB:{alias:"\u041f\u0423\u041e"},VLOOKUP:{alias:"\u0412\u041f\u0420"},WEEKDAY:{alias:"\u0414\u0415\u041d\u042c\u041d\u0415\u0414"},WEEKNUM:{alias:"\u041d\u041e\u041c\u041d\u0415\u0414\u0415\u041b\u0418"},WEIBULL:{alias:"\u0412\u0415\u0419\u0411\u0423\u041b\u041b"},"WEIBULL.DIST":{alias:"\u0412\u0415\u0419\u0411\u0423\u041b\u041b.\u0420\u0410\u0421\u041f"},WORKDAY:{alias:"\u0420\u0410\u0411\u0414\u0415\u041d\u042c"},"WORKDAY.INTL":{alias:"\u0420\u0410\u0411\u0414\u0415\u041d\u042c.\u041c\u0415\u0416\u0414"},XIRR:{alias:"\u0427\u0418\u0421\u0422\u0412\u041d\u0414\u041e\u0425"},XNPV:{alias:"\u0427\u0418\u0421\u0422\u041d\u0417"},YEAR:{alias:"\u0413\u041e\u0414"},YEARFRAC:{alias:"\u0414\u041e\u041b\u042f\u0413\u041e\u0414\u0410"},YIELD:{alias:"\u0414\u041e\u0425\u041e\u0414"},YIELDDISC:{alias:"\u0414\u041e\u0425\u041e\u0414\u0421\u041a\u0418\u0414\u041a\u0410"},YIELDMAT:{alias:"\u0414\u041e\u0425\u041e\u0414\u041f\u041e\u0413\u0410\u0428"},"Z.TEST":{alias:"Z.\u0422\u0415\u0421\u0422"},ZTEST:{alias:"Z\u0422\u0415\u0421\u0422"}},tableFunctionsMapping:{"#All":"#\u0412\u0441\u0435","#Data":"#\u0414\u0430\u043d\u043d\u044b\u0435","#Headers":"#\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0438","#Totals":"#\u0418\u0442\u043e\u0433\u0438","#This row":"#\u042d\u0442\u0430 \u0441\u0442\u0440\u043e\u043a\u0430"},clacErrorMapping:{"#NULL!":"#\u041f\u0423\u0421\u0422\u041e!","#DIV/0!":"#\u0414\u0415\u041b/0!","#VALUE!":"#\u0417\u041d\u0410\u0427!","#REF!":"#\u0421\u0421\u042b\u041b\u041a\u0410!","#NAME?":"#\u0418\u041c\u042f?","#N/A!":"#\u041d/\u0414","#NUM!":"#\u0427\u0418\u0421\u041b\u041e!"},booleanMapping:{boolean_true:"\u0418\u0421\u0422\u0418\u041d\u0410",boolean_false:"\u041b\u041e\u0416\u042c"}}},"./src/languagePackages/res.Spanish.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"INT.ACUM"},ACCRINTM:{alias:"INT.ACUM.V"},ADDRESS:{alias:"DIRECCION"},AGGREGATE:{alias:"AGREGAR"},AMORDEGRC:{alias:"AMORTIZ.PROGRE"},AMORLINC:{alias:"AMORTIZ.LIN"},AND:{alias:"Y"},ARABIC:{alias:"NUMERO.ARABE"},ASIN:{alias:"ASENO"},ASINH:{alias:"ASENOH"},AVEDEV:{alias:"DESVPROM"},AVERAGE:{alias:"PROMEDIO"},AVERAGEA:{alias:"PROMEDIOA"},AVERAGEIF:{alias:"PROMEDIO.SI"},AVERAGEIFS:{alias:"PROMEDIO.SI.CONJUNTO"},BAHTTEXT:{alias:"TEXTOBAHT"},"BETA.DIST":{alias:"DISTR.BETA"},"BETA.INV":{alias:"INV.BETA"},BETADIST:{alias:"DISTR.BETA"},BETAINV:{alias:"DISTR.BETA.INV"},BIN2DEC:{alias:"BIN.A.DEC"},BIN2HEX:{alias:"BIN.A.HEX"},BIN2OCT:{alias:"BIN.A.OCT"},"BINOM.DIST":{alias:"DISTR.BINOM.N"},"BINOM.DIST.RANGE":{alias:"DISTR.BINOM.SERIE"},"BINOM.INV":{alias:"INV.BINOM"},BINOMDIST:{alias:"DISTR.BINOM"},BITAND:{alias:"BIT.Y"},BITLSHIFT:{alias:"BIT.DESPLIZQDA"},BITXOR:{alias:"BIT.XO"},CALL:{alias:"LLAMAR"},CEILING:{alias:"MULTIPLO.SUPERIOR"},"CEILING.PRECISE":{alias:"MULTIPLO.SUPERIOR.EXACTO"},CELL:{alias:"CELDA"},CHAR:{alias:"CARACTER"},CHIDIST:{alias:"DISTR.CHI"},CHIINV:{alias:"PRUEBA.CHI.INV"},"CHISQ.DIST":{alias:"DISTR.CHICUAD"},"CHISQ.DIST.RT":{alias:"DISTR.CHICUAD.CD"},"CHISQ.INV":{alias:"INV.CHICUAD"},"CHISQ.INV.RT":{alias:"INV.CHICUAD.CD"},"CHISQ.TEST":{alias:"PRUEBA.CHICUAD"},CHITEST:{alias:"PRUEBA.CHI"},CHOOSE:{alias:"ELEGIR"},CLEAN:{alias:"LIMPIAR"},CODE:{alias:"CODIGO"},COLUMN:{alias:"COLUMNA"},COLUMNS:{alias:"COLUMNAS"},COMBIN:{alias:"COMBINAT"},COMPLEX:{alias:"COMPLEJO"},CONCATENATE:{alias:"CONCATENAR"},CONFIDENCE:{alias:"INTERVALO.CONFIANZA"},"CONFIDENCE.NORM":{alias:"INTERVALO.CONFIANZA.NORM"},"CONFIDENCE.T":{alias:"INTERVALO.CONFIANZA.T"},CONVERT:{alias:"CONVERTIR"},CORREL:{alias:"COEF.DE.CORREL"},COUNT:{alias:"CONTAR"},COUNTA:{alias:"CONTARA"},COUNTBLANK:{alias:"CONTAR.BLANCO"},COUNTIF:{alias:"CONTAR.SI"},COUNTIFS:{alias:"CONTAR.SI.CONJUNTO"},COUPDAYBS:{alias:"CUPON.DIAS.L1"},COUPDAYS:{alias:"CUPON.DIAS"},COUPDAYSNC:{alias:"CUPON.DIAS.L2"},COUPNCD:{alias:"CUPON.FECHA.L2"},COUPNUM:{alias:"CUPON.NUM"},COUPPCD:{alias:"CUPON.FECHA.L1"},"COVARIANCE.P":{alias:"COVARIANZA.P"},"COVARIANCE.S":{alias:"COVARIANZA.M"},CRITBINOM:{alias:"BINOM.CRIT"},CUBEKPIMEMBER:{alias:"MIEMBROKPICUBO"},CUBEMEMBER:{alias:"MIEMBROCUBO"},CUBEMEMBERPROPERTY:{alias:"PROPIEDADMIEMBROCUBO"},CUBERANKEDMEMBER:{alias:"MIEMBRORANGOCUBO"},CUBESET:{alias:"CONJUNTOCUBO"},CUBESETCOUNT:{alias:"RECUENTOCONJUNTOCUBO"},CUBEVALUE:{alias:"VALORCUBO"},CUMIPMT:{alias:"PAGO.INT.ENTRE"},CUMPRINC:{alias:"PAGO.PRINC.ENTRE"},DATE:{alias:"FECHA"},DATEVALUE:{alias:"FECHANUMERO"},DAVERAGE:{alias:"BDPROMEDIO"},DAY:{alias:"DIA"},DAYS:{alias:"DIAS"},DAYS360:{alias:"DIAS360"},DCOUNT:{alias:"BDCONTAR"},DCOUNTA:{alias:"BDCONTARA"},DEC2BIN:{alias:"DEC.A.BIN"},DEC2HEX:{alias:"DEC.A.HEX"},DEC2OCT:{alias:"DEC.A.OCT"},DECIMAL:{alias:"CONV.DECIMAL"},DEGREES:{alias:"GRADOS"},DEVSQ:{alias:"DESVIA2"},DGET:{alias:"BDEXTRAER"},DISC:{alias:"TASA.DESC"},DMAX:{alias:"BDMAX"},DMIN:{alias:"BDMIN"},DOLLAR:{alias:"MONEDA"},DOLLARDE:{alias:"MONEDA.DEC"},DOLLARFR:{alias:"MONEDA.FRAC"},DPRODUCT:{alias:"BDPRODUCTO"},DSTDEV:{alias:"BDDESVEST"},DSTDEVP:{alias:"BDDESVESTP"},DSUM:{alias:"BDSUMA"},DURATION:{alias:"DURACION"},DVAR:{alias:"BDVAR"},DVARP:{alias:"BDVARP"},EDATE:{alias:"FECHA.MES"},EFFECT:{alias:"INT.EFECTIVO"},ENCODEURL:{alias:"URLCODIF"},EOMONTH:{alias:"FIN.MES"},ERF:{alias:"FUN.ERROR"},"ERF.PRECISE":{alias:"FUN.ERROR.EXACTO"},ERFC:{alias:"FUN.ERROR.COMPL"},"ERFC.PRECISE":{alias:"FUN.ERROR.COMPL.EXACTO"},"ERROR.TYPE":{alias:"TIPO.DE.ERROR"},EVEN:{alias:"REDONDEA.PAR"},EXACT:{alias:"IGUAL"},"EXPON.DIST":{alias:"DISTR.EXP.N"},EXPONDIST:{alias:"DISTR.EXP"},"F.DIST":{alias:"DISTR.F.RT"},"F.DIST.RT":{alias:"DISTR.F.CD"},"F.INV":{alias:"INV.F"},"F.INV.RT":{alias:"INV.F.CD"},"F.TEST":{alias:"PRUEBA.F.N"},FACTDOUBLE:{alias:"FACT.DOBLE"},FALSE:{alias:"FALSO"},FDIST:{alias:"DISTR.F"},FILTERXML:{alias:"XMLFILTRO"},FIND:{alias:"ENCONTRAR"},FINDB:{alias:"ENCONTRARB"},FINV:{alias:"DISTR.F.INV"},FISHERINV:{alias:"PRUEBA.FISHER.INV"},FIXED:{alias:"CONV.DECIMAL"},FLOOR:{alias:"MULTIPLO.INFERIOR"},"FLOOR.MATH":{alias:"MULTIPLO.INFERIOR.MAT"},"FLOOR.PRECISE":{alias:"MULTIPLO.INFERIOR.EXACTO"},FORECAST:{alias:"PRONOSTICO"},FREQUENCY:{alias:"FRECUENCIA"},FTEST:{alias:"PRUEBA.F"},FV:{alias:"VF"},FVSCHEDULE:{alias:"VF.PLAN"},"GAMMA.DIST":{alias:"DISTR.GAMMA.N"},"GAMMA.INV":{alias:"INV.GAMMA"},GAMMADIST:{alias:"DISTR.GAMMA"},GAMMAINV:{alias:"DISTR.GAMMA.INV"},GAMMALN:{alias:"GAMMA.LN"},"GAMMALN.PRECISE":{alias:"GAMMA.LN.EXACTO"},GCD:{alias:"M.C.D"},GEOMEAN:{alias:"MEDIA.GEOM"},GESTEP:{alias:"MAYOR.O.IGUAL"},GETPIVOTDATA:{alias:"IMPORTARDATOSDINAMICOS"},GROWTH:{alias:"CRECIMIENTO"},HARMEAN:{alias:"MEDIA.ARMO"},HEX2BIN:{alias:"HEX.A.BIN"},HEX2DEC:{alias:"HEX.A.DEC"},HEX2OCT:{alias:"HEX.A.OCT"},HLOOKUP:{alias:"BUSCARH"},HOUR:{alias:"HORA"},HYPERLINK:{alias:"HIPERVINCULO"},"HYPGEOM.DIST":{alias:"DISTR.HIPERGEOM.N"},HYPGEOMDIST:{alias:"DISTR.HIPERGEOM"},IF:{alias:"SI"},IFERROR:{alias:"SI.ERROR"},IFNA:{alias:"SI.ND"},IMABS:{alias:"IM.ABS"},IMAGINARY:{alias:"IMAGINARIO"},IMARGUMENT:{alias:"IM.ANGULO"},IMCONJUGATE:{alias:"IM.CONJUGADA"},IMCOS:{alias:"IM.COS"},IMCOSH:{alias:"IM.COSH"},IMCSC:{alias:"IM.CSC"},IMCSCH:{alias:"IM.CSCH"},IMDIV:{alias:"IM.DIV"},IMEXP:{alias:"IM.EXP"},IMLN:{alias:"IM.LN"},IMLOG10:{alias:"IM.LOG10"},IMLOG2:{alias:"IM.LOG2"},IMPOWER:{alias:"IM.POT"},IMPRODUCT:{alias:"IM.PRODUCT"},IMREAL:{alias:"IM.REAL"},IMSEC:{alias:"IM.SEC"},IMSECH:{alias:"IM.SECH"},IMSIN:{alias:"IM.SENO"},IMSINH:{alias:"IM.SENOH"},IMSQRT:{alias:"IM.RAIZ2"},IMSUB:{alias:"IM.SUSTR"},IMSUM:{alias:"IM.SUM"},IMTAN:{alias:"IM.TAN"},INDEX:{alias:"INDICE"},INDIRECT:{alias:"INDIRECTO"},INT:{alias:"ENTERO"},INTERCEPT:{alias:"INTERSECCION.EJE"},INTRATE:{alias:"TASA.INT"},IPMT:{alias:"PAGOINT"},IRR:{alias:"TIR"},ISBLANK:{alias:"ESBLANCO"},ISERR:{alias:"ESERR"},ISERROR:{alias:"ESERROR"},ISEVEN:{alias:"ES.PAR"},ISFORMULA:{alias:"ESFORMULA"},ISLOGICAL:{alias:"ESLOGICO"},ISNA:{alias:"ESNOD"},ISNONTEXT:{alias:"ESNOTEXTO"},ISNUMBER:{alias:"ESNUMERO"},"ISO.CEILING":{alias:"MULTIPLO.SUPERIOR.ISO"},ISODD:{alias:"ES.IMPAR"},ISOWEEKNUM:{alias:"ISO.NUM.DE.SEMANA"},ISPMT:{alias:"INT.PAGO.DIR"},ISREF:{alias:"ESREF"},ISTEXT:{alias:"ESTEXTO"},KURT:{alias:"CURTOSIS"},LARGE:{alias:"K.ESIMO.MAYOR"},LCM:{alias:"M.C.M"},LEFT:{alias:"IZQUIERDA"},LEFTB:{alias:"IZQUIERDAB"},LEN:{alias:"LARGO"},LENB:{alias:"LARGOB"},LINEST:{alias:"ESTIMACION.LINEAL"},LOGEST:{alias:"ESTIMACION.LOGARITMICA"},LOGINV:{alias:"DISTR.LOG.INV"},"LOGNORM.DIST":{alias:"DISTR.LOGNORM"},"LOGNORM.INV":{alias:"INV.LOGNORM"},LOGNORMDIST:{alias:"DISTR.LOG.NORM"},LOOKUP:{alias:"BUSCAR"},LOWER:{alias:"MINUSC"},MATCH:{alias:"COINCIDIR"},MDURATION:{alias:"DURACION.MODIF"},MEDIAN:{alias:"MEDIANA"},MID:{alias:"EXTRAE"},MIDB:{alias:"EXTRAEB"},MINUTE:{alias:"MINUTO"},MINVERSE:{alias:"MINVERSA"},MIRR:{alias:"TIRM"},MOD:{alias:"RESIDUO"},MODE:{alias:"MODA"},"MODE.MULT":{alias:"MODA.VARIOS"},"MODE.SNGL":{alias:"MODA.UNO"},MONTH:{alias:"MES"},MROUND:{alias:"REDOND.MULT"},NA:{alias:"NOD"},NETWORKDAYS:{alias:"DIAS.LAB"},"NETWORKDAYS.INTL":{alias:"DIAS.LAB.INTL"},NOMINAL:{alias:"TASA.NOMINAL"},"NORM.DIST":{alias:"DISTR.NORM.N"},"NORM.INV":{alias:"INV.NORM"},"NORM.S.DIST":{alias:"DISTR.NORM.ESTAND.N"},"NORM.S.INV":{alias:"INV.NORM.ESTAND"},NORMDIST:{alias:"DISTR.NORM"},NORMINV:{alias:"DISTR.NORM.INV"},NORMSDIST:{alias:"DISTR.NORM.ESTAND"},NORMSINV:{alias:"DISTR.NORM.ESTAND.INV"},NOT:{alias:"NO"},NOW:{alias:"AHORA"},NPV:{alias:"VNA"},NUMBERVALUE:{alias:"VALOR.NUMERO"},OCT2BIN:{alias:"OCT.A.BIN"},OCT2DEC:{alias:"OCT.A.DEC"},OCT2HEX:{alias:"OCT.A.HEX"},ODD:{alias:"REDONDEA.IMPAR"},ODDFPRICE:{alias:"PRECIO.PER.IRREGULAR.1"},ODDFYIELD:{alias:"RENDTO.PER.IRREGULAR.1"},ODDLPRICE:{alias:"PRECIO.PER.IRREGULAR.2"},ODDLYIELD:{alias:"RENDTO.PER.IRREGULAR.2"},OFFSET:{alias:"DESREF"},OR:{alias:"O"},PDURATION:{alias:"P.DURACION"},PERCENTILE:{alias:"PERCENTIL"},"PERCENTILE.EXC":{alias:"PERCENTIL.EXC"},"PERCENTILE.INC":{alias:"PERCENTIL.INC"},PERCENTRANK:{alias:"RANGO.PERCENTIL"},"PERCENTRANK.EXC":{alias:"RANGO.PERCENTIL.EXC"},"PERCENTRANK.INC":{alias:"RANGO.PERCENTIL.INC"},PERMUT:{alias:"PERMUTACIONES"},PERMUTATIONA:{alias:"PERMUTACIONES.A"},PHI:{alias:"FI"},PHONETIC:{alias:"FONETICO"},PMT:{alias:"PAGO"},POWER:{alias:"POTENCIA"},PPMT:{alias:"PAGOPRIN"},PRICE:{alias:"PRECIO"},PRICEDISC:{alias:"PRECIO.DESCUENTO"},PRICEMAT:{alias:"PRECIO.VENCIMIENTO"},PROB:{alias:"PROBABILIDAD"},PRODUCT:{alias:"PRODUCTO"},PROPER:{alias:"NOMPROPIO"},PV:{alias:"VA"},QUARTILE:{alias:"CUARTIL"},"QUARTILE.EXC":{alias:"CUARTIL.EXC"},"QUARTILE.INC":{alias:"CUARTIL.INC"},QUOTIENT:{alias:"COCIENTE"},RADIANS:{alias:"RADIANES"},RAND:{alias:"ALEATORIO"},RANDBETWEEN:{alias:"ALEATORIO.ENTRE"},RANK:{alias:"JERARQUIA"},"RANK.AVG":{alias:"JERARQUIA.MEDIA"},"RANK.EQ":{alias:"JERARQUIA.EQV"},RATE:{alias:"TASA"},RECEIVED:{alias:"CANTIDAD.RECIBIDA"},"REGISTER.ID":{alias:"ID.REGISTRO"},REPLACE:{alias:"REEMPLAZAR"},REPLACEB:{alias:"REEMPLAZARB"},REPT:{alias:"REPETIR"},RIGHT:{alias:"DERECHA"},RIGHTB:{alias:"DERECHAB"},ROMAN:{alias:"NUMERO.ROMANO"},ROUND:{alias:"REDONDEAR"},ROUNDDOWN:{alias:"REDONDEAR.MENOS"},ROUNDUP:{alias:"REDONDEAR.MAS"},ROW:{alias:"FILA"},ROWS:{alias:"FILAS"},RSQ:{alias:"COEFICIENTE.R2"},RTD:{alias:"RDTR"},SEARCH:{alias:"HALLAR"},SEARCHB:{alias:"HALLARB"},SECOND:{alias:"SEGUNDO"},SERIESSUM:{alias:"SUMA.SERIES"},SHEET:{alias:"HOJA"},SHEETS:{alias:"HOJAS"},SIGN:{alias:"SIGNO"},SIN:{alias:"SENO"},SINH:{alias:"SENOH"},SKEW:{alias:"COEFICIENTE.ASIMETRIA"},"SKEW.P":{alias:"COEFICIENTE.ASIMETRIA.P"},SLOPE:{alias:"PENDIENTE"},SMALL:{alias:"K.ESIMO.MENOR"},SQRT:{alias:"RAIZ"},SQRTPI:{alias:"RAIZ2PI"},STANDARDIZE:{alias:"NORMALIZACION"},STDEV:{alias:"DESVEST"},"STDEV.P":{alias:"DESVEST.P"},"STDEV.S":{alias:"DESVEST.M"},STDEVA:{alias:"DESVESTA"},STDEVP:{alias:"DESVESTP"},STDEVPA:{alias:"DESVESTPA"},STEYX:{alias:"ERROR.TIPICO.XY"},SUBSTITUTE:{alias:"SUSTITUIR"},SUBTOTAL:{alias:"SUBTOTALES"},SUM:{alias:"SUMA"},SUMIF:{alias:"SUMAR.SI"},SUMIFS:{alias:"SUMAR.SI.CONJUNTO"},SUMPRODUCT:{alias:"SUMAPRODUCTO"},SUMSQ:{alias:"SUMA.CUADRADOS"},SUMX2MY2:{alias:"SUMAX2MENOSY2"},SUMX2PY2:{alias:"SUMAX2MASY2"},SUMXMY2:{alias:"SUMAXMENOSY2"},"T.DIST":{alias:"DISTR.T.N"},"T.DIST.2T":{alias:"DISTR.T.2C"},"T.DIST.RT":{alias:"DISTR.T.CD"},"T.INV":{alias:"INV.T"},"T.INV.2T":{alias:"INV.T.2C"},"T.TEST":{alias:"PRUEBA.T"},TBILLEQ:{alias:"LETRA.DE.TES.EQV.A.BONO"},TBILLPRICE:{alias:"LETRA.DE.TES.PRECIO"},TBILLYIELD:{alias:"LETRA.DE.TES.RENDTO"},TDIST:{alias:"DISTR.T"},TEXT:{alias:"TEXTO"},TIME:{alias:"TIEMPO"},TIMEVALUE:{alias:"HORANUMERO"},TINV:{alias:"DISTR.T.INV"},TODAY:{alias:"HOY"},TRANSPOSE:{alias:"TRANSPONER"},TREND:{alias:"TENDENCIA"},TRIM:{alias:"ESPACIOS"},TRIMMEAN:{alias:"MEDIA.ACOTADA"},TRUE:{alias:"VERDADERO"},TRUNC:{alias:"TRUNCAR"},TTEST:{alias:"PRUEBA.T"},TYPE:{alias:"TIPO"},UNICHAR:{alias:"UNICAR"},UPPER:{alias:"MAYUSC"},VALUE:{alias:"VALOR"},VDB:{alias:"DVS"},VLOOKUP:{alias:"BUSCARV"},WEBSERVICE:{alias:"SERVICIOWEB"},WEEKDAY:{alias:"DIASEM"},WEEKNUM:{alias:"NUM.DE.SEMANA"},WEIBULL:{alias:"DIST.WEIBULL"},"WEIBULL.DIST":{alias:"DIST.WEIBULL"},WORKDAY:{alias:"DIA.LAB"},"WORKDAY.INTL":{alias:"DIA.LAB.INTL"},XIRR:{alias:"TIR.NO.PER"},XNPV:{alias:"VNA.NO.PER"},XOR:{alias:"XO"},YEAR:{alias:"A\xd1O"},YEARFRAC:{alias:"FRAC.A\xd1O"},YIELD:{alias:"RENDTO"},YIELDDISC:{alias:"RENDTO.DESC"},YIELDMAT:{alias:"RENDTO.VENCTO"},"Z.TEST":{alias:"PRUEBA.Z"},ZTEST:{alias:"PRUEBA.Z"}},tableFunctionsMapping:{"#All":"#Todo","#Data":"#Datos","#Headers":"#Encabezados","#Totals":"#Totales","#This row":"#Esta fila"},clacErrorMapping:{"#NULL!":"#\xa1NULO!","#DIV/0!":"#\xa1DIV/0!","#VALUE!":"#\xa1VALOR!","#REF!":"#\xa1REF","#NAME?":"#\xbfNOMBRE?","#N/A!":"#N/A","#NUM!":"#\xa1NUM!"},booleanMapping:{boolean_true:"VERDADERO",boolean_false:"FALSO"}}},"./src/languagePackages/res.Swedish.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ACCRINT:{alias:"UPPLR\xc4NTA"},ACCRINTM:{alias:"UPPLOBLR\xc4NTA"},ACOS:{alias:"ARCCOS"},ACOSH:{alias:"ARCCOSH"},ACOT:{alias:"ARCCOT"},ACOTH:{alias:"ARCCOTH"},ADDRESS:{alias:"ADRESS"},AGGREGATE:{alias:"M\xc4NGD"},AND:{alias:"OCH"},ARABIC:{alias:"ARABISKA"},AREAS:{alias:"OMR\xc5DEN"},ASIN:{alias:"ARCSIN"},ASINH:{alias:"ARCSINH"},ATAN:{alias:"ARCTAN"},ATAN2:{alias:"ARCTAN2"},ATANH:{alias:"ARCTANH"},AVEDEV:{alias:"MEDELAVV"},AVERAGE:{alias:"MEDEL"},AVERAGEIF:{alias:"MEDELOM"},AVERAGEIFS:{alias:"MEDEL.OMF"},BASE:{alias:"BAS"},"BETA.DIST":{alias:"BETA.F\xd6RD"},BETADIST:{alias:"BETAF\xd6RD"},BIN2DEC:{alias:"BIN.TILL.DEC"},BIN2HEX:{alias:"BIN.TILL.HEX"},BIN2OCT:{alias:"BIN.TILL.OKT"},"BINOM.DIST":{alias:"BINOM.F\xd6RD"},"BINOM.DIST.RANGE":{alias:"BINOM.F\xd6RD.INTERVALL"},BINOMDIST:{alias:"BINOMF\xd6RD"},BITAND:{alias:"BITOCH"},BITLSHIFT:{alias:"BITVSKIFT"},BITOR:{alias:"BITELLER"},BITRSHIFT:{alias:"BITHSKIFT"},BITXOR:{alias:"BITXELLER"},CALL:{alias:"ANROPA"},CEILING:{alias:"RUNDA.UPP"},"CEILING.MATH":{alias:"RUNDA.UPP.MATEMATISKT"},"CEILING.PRECISE":{alias:"RUNDA.UPP.EXAKT"},CHAR:{alias:"TECKENKOD"},CHIDIST:{alias:"CHI2F\xd6RD"},CHIINV:{alias:"CHI2INV"},"CHISQ.DIST":{alias:"CHI2.F\xd6RD"},"CHISQ.DIST.RT":{alias:"CHI2.F\xd6RD.RT"},"CHISQ.INV":{alias:"CHI2.INV"},"CHISQ.INV.RT":{alias:"CHI2.INV.RT"},"CHISQ.TEST":{alias:"CHI2.TEST"},CHITEST:{alias:"CHI2TEST"},CHOOSE:{alias:"V\xc4LJ"},CLEAN:{alias:"RENSA"},CODE:{alias:"KOD"},COLUMN:{alias:"KOLUMN"},COLUMNS:{alias:"KOLUMNER"},COMBIN:{alias:"KOMBIN"},COMBINA:{alias:"KOMBINA"},COMPLEX:{alias:"KOMPLEX"},CONCATENATE:{alias:"SAMMANFOGA"},CONFIDENCE:{alias:"KONFIDENS"},"CONFIDENCE.NORM":{alias:"KONFIDENS.NORM"},"CONFIDENCE.T":{alias:"KONFIDENS.T"},CONVERT:{alias:"KONVERTERA"},CORREL:{alias:"KORREL"},COUNT:{alias:"ANTAL"},COUNTA:{alias:"ANTALV"},COUNTBLANK:{alias:"ANTAL.TOMMA"},COUNTIF:{alias:"ANTAL.OM"},COUNTIFS:{alias:"ANTAL.OMF"},COUPDAYBS:{alias:"KUPDAGBB"},COUPDAYS:{alias:"KUPDAGB"},COUPDAYSNC:{alias:"KUPDAGNK"},COUPNCD:{alias:"KUPNKD"},COUPNUM:{alias:"KUPANT"},COUPPCD:{alias:"KUPFKD"},COVAR:{alias:"KOVAR"},"COVARIANCE.P":{alias:"KOVARIANS.P"},"COVARIANCE.S":{alias:"KOVARIANS.S"},CRITBINOM:{alias:"KRITBINOM"},CSC:{alias:"CSK"},CSCH:{alias:"CSKH"},CUBEKPIMEMBER:{alias:"KUBKPIMEDLEM"},CUBEMEMBER:{alias:"KUBMEDLEM"},CUBEMEMBERPROPERTY:{alias:"KUBMEDLEMSEGENSKAP"},CUBERANKEDMEMBER:{alias:"KUBRANGORDNADMEDLEM"},CUBESET:{alias:"KUBUPPS\xc4TTNING"},CUBESETCOUNT:{alias:"KUBUPPS\xc4TTNINGANTAL"},CUBEVALUE:{alias:"KUBV\xc4RDE"},CUMIPMT:{alias:"KUMR\xc4NTA"},CUMPRINC:{alias:"KUMPRIS"},DATE:{alias:"DATUM"},DATEVALUE:{alias:"DATUMV\xc4RDE"},DAVERAGE:{alias:"DMEDEL"},DAY:{alias:"DAG"},DAYS:{alias:"DAGAR"},DAYS360:{alias:"DAGAR360"},DCOUNT:{alias:"DANTAL"},DCOUNTA:{alias:"DANTALV"},DDB:{alias:"DEGAVSKR"},DEC2BIN:{alias:"DEC.TILL.BIN"},DEC2HEX:{alias:"DEC.TILL.HEX"},DEC2OCT:{alias:"DEC.TILL.OKT"},DEGREES:{alias:"GRADER"},DEVSQ:{alias:"KVADAVV"},DGET:{alias:"DH\xc4MTA"},DISC:{alias:"DISK"},DOLLAR:{alias:"VALUTA"},DOLLARDE:{alias:"DECTAL"},DOLLARFR:{alias:"BR\xc5K"},DPRODUCT:{alias:"DPRODUKT"},DSTDEV:{alias:"DSTDAV"},DSTDEVP:{alias:"DSTDAVP"},DSUM:{alias:"DSUMMA"},DURATION:{alias:"L\xd6PTID"},DVAR:{alias:"DVARIANS"},DVARP:{alias:"DVARIANSP"},EDATE:{alias:"EDATUM"},EFFECT:{alias:"EFFR\xc4NTA"},ENCODEURL:{alias:"KODAWEBBADRESS"},EOMONTH:{alias:"SLUTM\xc5NAD"},ERF:{alias:"FELF"},"ERF.PRECISE":{alias:"FELF.EXAKT"},ERFC:{alias:"FELFK"},"ERFC.PRECISE":{alias:"FELFK.EXAKT"},"ERROR.TYPE":{alias:"FEL.TYP"},EVEN:{alias:"J\xc4MN"},EXACT:{alias:"EXAKT"},"EXPON.DIST":{alias:"EXPON.F\xd6RD"},EXPONDIST:{alias:"EXPONF\xd6RD"},"F.DIST":{alias:"F.F\xd6RD"},"F.DIST.RT":{alias:"F.F\xd6RD.RT"},FACT:{alias:"FAKULTET"},FACTDOUBLE:{alias:"DUBBELFAKULTET"},FALSE:{alias:"FALSKT"},FDIST:{alias:"FF\xd6RD"},FILTERXML:{alias:"FILTRERAXML"},FIND:{alias:"HITTA"},FINDB:{alias:"HITTAB"},FIXED:{alias:"FASTTAL"},FLOOR:{alias:"RUNDA.NED"},"FLOOR.MATH":{alias:"RUNDA.NER.MATEMATISKT"},"FLOOR.PRECISE":{alias:"RUNDA.NER.EXAKT"},FORECAST:{alias:"PREDIKTION"},FORMULATEXT:{alias:"FORMELTEXT"},FREQUENCY:{alias:"FREKVENS"},FV:{alias:"SLUTV\xc4RDE"},FVSCHEDULE:{alias:"F\xd6RR\xc4NTNING"},"GAMMA.DIST":{alias:"GAMMA.F\xd6RD"},GAMMADIST:{alias:"GAMMAF\xd6RD"},"GAMMALN.PRECISE":{alias:"GAMMALN.EXAKT"},GCD:{alias:"SGD"},GEOMEAN:{alias:"GEOMEDEL"},GESTEP:{alias:"SLSTEG"},GETPIVOTDATA:{alias:"H\xc4MTA.PIVOTDATA"},GROWTH:{alias:"EXPTREND"},HARMEAN:{alias:"HARMMEDEL"},HEX2BIN:{alias:"HEX.TILL.BIN"},HEX2DEC:{alias:"HEX.TILL.DEC"},HEX2OCT:{alias:"HEX.TILL.OKT"},HLOOKUP:{alias:"LETAKOLUMN"},HOUR:{alias:"TIMME"},HYPERLINK:{alias:"HYPERL\xc4NK"},"HYPGEOM.DIST":{alias:"HYPGEOM.F\xd6RD"},HYPGEOMDIST:{alias:"HYPGEOMF\xd6RD"},IF:{alias:"OM"},IFERROR:{alias:"OMFEL"},IFNA:{alias:"OMSAKNAS"},IMAGINARY:{alias:"IMAGIN\xc4R"},IMCONJUGATE:{alias:"IMKONJUGAT"},IMEXP:{alias:"IMEUPPH\xd6JT"},IMPOWER:{alias:"IMUPPH\xd6JT"},IMPRODUCT:{alias:"IMPRODUKT"},IMSEC:{alias:"IMSEK"},IMSECH:{alias:"IMSEKH"},IMSQRT:{alias:"IMROT"},IMSUB:{alias:"IMDIFF"},INDIRECT:{alias:"INDIREKT"},INT:{alias:"HELTAL"},INTERCEPT:{alias:"SK\xc4RNINGSPUNKT"},INTRATE:{alias:"\xc5RSR\xc4NTA"},IPMT:{alias:"RBETALNING"},IRR:{alias:"IR"},ISBLANK:{alias:"\xc4RTOM"},ISERR:{alias:"\xc4RF"},ISERROR:{alias:"\xc4RFEL"},ISEVEN:{alias:"\xc4RJ\xc4MN"},ISFORMULA:{alias:"\xc4RFORMEL"},ISLOGICAL:{alias:"\xc4RLOGISK"},ISNA:{alias:"\xc4RSAKNAD"},ISNONTEXT:{alias:"\xc4REJTEXT"},ISNUMBER:{alias:"\xc4RTAL"},"ISO.CEILING":{alias:"ISO.RUNDA.UPP"},ISODD:{alias:"\xc4RUDDA"},ISOWEEKNUM:{alias:"ISOVECKONR"},ISPMT:{alias:"RAL\xc5N"},ISREF:{alias:"\xc4RREF"},ISTEXT:{alias:"\xc4RTEXT"},KURT:{alias:"TOPPIGHET"},LARGE:{alias:"ST\xd6RSTA"},LCM:{alias:"MGM"},LEFT:{alias:"V\xc4NSTER"},LEFTB:{alias:"V\xc4NSTERB"},LEN:{alias:"L\xc4NGD"},LENB:{alias:"L\xc4NGDB"},LINEST:{alias:"REGR"},LOGEST:{alias:"EXPREGR"},"LOGNORM.DIST":{alias:"LOGNORM.F\xd6RD"},LOGNORMDIST:{alias:"LOGNORMF\xd6RD"},LOOKUP:{alias:"LETAUPP"},LOWER:{alias:"GEMENER"},MATCH:{alias:"PASSA"},MDURATION:{alias:"ML\xd6PTID"},MID:{alias:"EXTEXT"},MIDB:{alias:"EXTEXTB"},MINUTE:{alias:"MINUT"},MINVERSE:{alias:"MINVERT"},MIRR:{alias:"MODIR"},MOD:{alias:"REST"},MODE:{alias:"TYPV\xc4RDE"},"MODE.MULT":{alias:"TYPV\xc4RDE.FLERA"},"MODE.SNGL":{alias:"TYPV\xc4RDE.ETT"},MONTH:{alias:"M\xc5NAD"},MROUND:{alias:"MAVRUNDA"},MUNIT:{alias:"MENHET"},NA:{alias:"SAKNAS"},"NEGBINOM.DIST":{alias:"NEGBINOM.F\xd6RD"},NEGBINOMDIST:{alias:"NEGBINOMF\xd6RD"},NETWORKDAYS:{alias:"NETTOARBETSDAGAR"},"NETWORKDAYS.INTL":{alias:"NETTOARBETSDAGAR.INT"},NOMINAL:{alias:"NOMR\xc4NTA"},"NORM.DIST":{alias:"NORM.F\xd6RD"},"NORM.S.DIST":{alias:"NORM.S.F\xd6RD"},NORMDIST:{alias:"NORMF\xd6RD"},NORMSDIST:{alias:"NORMSF\xd6RD"},NOT:{alias:"ICKE"},NOW:{alias:"NU"},NPER:{alias:"PERIODER"},NPV:{alias:"NETNUV\xc4RDE"},NUMBERVALUE:{alias:"TALV\xc4RDE"},OCT2BIN:{alias:"OKT.TILL.BIN"},OCT2DEC:{alias:"OKT.TILL.DEC"},OCT2HEX:{alias:"OKT.TILL.HEX"},ODD:{alias:"UDDA"},ODDFPRICE:{alias:"UDDAFPRIS"},ODDFYIELD:{alias:"UDDAFAVKASTNING"},ODDLPRICE:{alias:"UDDASPRIS"},ODDLYIELD:{alias:"UDDASAVKASTNING"},OFFSET:{alias:"F\xd6RSKJUTNING"},OR:{alias:"ELLER"},PDURATION:{alias:"PL\xd6PTID"},PERCENTILE:{alias:"PERCENTIL"},"PERCENTILE.EXC":{alias:"PERCENTIL.EXK"},"PERCENTILE.INC":{alias:"PERCENTIL.INK"},PERCENTRANK:{alias:"PROCENTRANG"},"PERCENTRANK.EXC":{alias:"PROCENTRANG.EXK"},"PERCENTRANK.INC":{alias:"PROCENTRANG.INK"},PMT:{alias:"BETALNING"},"POISSON.DIST":{alias:"POISSON.F\xd6RD"},POWER:{alias:"UPPH\xd6JT.TILL"},PPMT:{alias:"AMORT"},PRICE:{alias:"PRIS"},PRICEDISC:{alias:"PRISDISK"},PRICEMAT:{alias:"PRISF\xd6RF"},PROB:{alias:"SANNOLIKHET"},PRODUCT:{alias:"PRODUKT"},PROPER:{alias:"INITIAL"},PV:{alias:"NUV\xc4RDE"},QUARTILE:{alias:"KVARTIL"},"QUARTILE.EXC":{alias:"KVARTIL.EXK"},"QUARTILE.INC":{alias:"KVARTIL.INK"},QUOTIENT:{alias:"KVOT"},RADIANS:{alias:"RADIANER"},RAND:{alias:"SLUMP"},RANDBETWEEN:{alias:"SLUMP.MELLAN"},RANK:{alias:"RANG"},"RANK.AVG":{alias:"RANG.MED"},"RANK.EQ":{alias:"RANG.EKV"},RATE:{alias:"R\xc4NTA"},RECEIVED:{alias:"BELOPP"},"REGISTER.ID":{alias:"REGISTRERA.ID"},REPLACE:{alias:"ERS\xc4TT"},REPLACEB:{alias:"ERS\xc4TTB"},REPT:{alias:"REP"},RIGHT:{alias:"H\xd6GER"},RIGHTB:{alias:"H\xd6GERB"},ROMAN:{alias:"ROMERSK"},ROUND:{alias:"AVRUNDA"},ROUNDDOWN:{alias:"AVRUNDA.NED\xc5T"},ROUNDUP:{alias:"AVRUNDA.UPP\xc5T"},ROW:{alias:"RAD"},ROWS:{alias:"RADER"},RRI:{alias:"AVKP\xc5INVEST"},RSQ:{alias:"RKV"},SEARCH:{alias:"S\xd6K"},SEARCHB:{alias:"S\xd6KB"},SEC:{alias:"SEK"},SECH:{alias:"SEKH"},SECOND:{alias:"SEKUND"},SERIESSUM:{alias:"SERIESUMMA"},SHEET:{alias:"BLAD"},SHEETS:{alias:"ANTALBLAD"},SIGN:{alias:"TECKEN"},SKEW:{alias:"SNEDHET"},"SKEW.P":{alias:"SNEDHET.P"},SLN:{alias:"LINAVSKR"},SLOPE:{alias:"LUTNING"},SMALL:{alias:"MINSTA"},"SQL.REQUEST":{alias:"SQL.BEG\xc4R"},SQRT:{alias:"ROT"},SQRTPI:{alias:"ROTPI"},STANDARDIZE:{alias:"STANDARDISERA"},STDEV:{alias:"STDAV"},"STDEV.P":{alias:"STDAV.P"},"STDEV.S":{alias:"STDAV.S"},STDEVP:{alias:"STDAVP"},STEYX:{alias:"STDFELYX"},SUBSTITUTE:{alias:"BYT.UT"},SUBTOTAL:{alias:"DELSUMMA"},SUM:{alias:"SUMMA"},SUMIF:{alias:"SUMMA.OM"},SUMIFS:{alias:"SUMMA.OMF"},SUMPRODUCT:{alias:"PRODUKTSUMMA"},SUMSQ:{alias:"KVADRATSUMMA"},SUMX2MY2:{alias:"SUMMAX2MY2"},SUMX2PY2:{alias:"SUMMAX2PY2"},SUMXMY2:{alias:"SUMMAXMY2"},SYD:{alias:"\xc5RSAVSKR"},"T.DIST":{alias:"T.F\xd6RD"},"T.DIST.2T":{alias:"T.F\xd6RD.2T"},"T.DIST.RT":{alias:"T.F\xd6RD.RT"},TBILLEQ:{alias:"SSVXEKV"},TBILLPRICE:{alias:"SSVXPRIS"},TBILLYIELD:{alias:"SSVXR\xc4NTA"},TDIST:{alias:"TF\xd6RD"},TIME:{alias:"KLOCKSLAG"},TIMEVALUE:{alias:"TIDV\xc4RDE"},TODAY:{alias:"IDAG"},TRANSPOSE:{alias:"TRANSPONERA"},TRIM:{alias:"ST\xc4DA"},TRIMMEAN:{alias:"TRIMMEDEL"},TRUE:{alias:"SANT"},TRUNC:{alias:"AVKORTA"},TYPE:{alias:"V\xc4RDETYP"},UNICHAR:{alias:"UNITECKENKOD"},UPPER:{alias:"VERSALER"},VALUE:{alias:"TEXTNUM"},VAR:{alias:"VARIANS"},"VAR.P":{alias:"VARIANS.P"},"VAR.S":{alias:"VARIANS.S"},VARP:{alias:"VARIANSP"},VARPA:{alias:"VARIANSPA"},VDB:{alias:"VDEGRAVSKR"},VLOOKUP:{alias:"LETARAD"},WEBSERVICE:{alias:"WEBBTJ\xc4NST"},WEEKDAY:{alias:"VECKODAG"},WEEKNUM:{alias:"VECKONR"},WORKDAY:{alias:"ARBETSDAGAR"},"WORKDAY.INTL":{alias:"ARBETSDAGAR.INT"},XNPV:{alias:"XNUV\xc4RDE"},XOR:{alias:"XELLER"},YEAR:{alias:"\xc5R"},YEARFRAC:{alias:"\xc5RDEL"},YIELD:{alias:"NOMAVK"},YIELDDISC:{alias:"NOMAVKDISK"},YIELDMAT:{alias:"NOMAVKF\xd6RF"}},tableFunctionsMapping:{"#All":"#Alla","#Data":"#Data","#Headers":"#Rubriker","#Totals":"#Summor","#This row":"#Denna rad"},clacErrorMapping:{"#NULL!":"#SK\xc4RNING!","#DIV/0!":"#DIVISION/0!","#VALUE!":"#V\xc4RDEFEL!","#REF!":"#REFERENS!","#NAME?":"#NAMN?","#N/A!":"#SAKNAS!","#NUM!":"#OGILTIGT!"},booleanMapping:{boolean_true:"SANT",boolean_false:"FALSKT"}}},"./src/languagePackages/res.Turkish.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.resource={builtInFunctionsMapping:{ABS:{alias:"MUTLAK"},ACCRINT:{alias:"GER\xc7EKFA\u0130Z"},ACCRINTM:{alias:"GER\xc7EKFA\u0130ZV"},ADDRESS:{alias:"ADRES"},AGGREGATE:{alias:"TOPLAMA"},AND:{alias:"VE"},ARABIC:{alias:"ARAP"},AREAS:{alias:"ALANSAY"},ASIN:{alias:"AS\u0130N"},AVEDEV:{alias:"ORTSAP"},AVERAGE:{alias:"ORTALAMA"},AVERAGEA:{alias:"ORTALAMAA"},AVERAGEIF:{alias:"E\u011eERORTALAMA"},AVERAGEIFS:{alias:"\xc7OKE\u011eERORTALAMA"},BAHTTEXT:{alias:"BAHTMET\u0130N"},BASE:{alias:"TABAN"},"BETA.DIST":{alias:"BETA.DA\u011e"},"BETA.INV":{alias:"BETA.TERS"},BETADIST:{alias:"BETADA\u011e"},BETAINV:{alias:"BETATERS"},"BINOM.DIST":{alias:"B\u0130NOM.DA\u011e"},"BINOM.DIST.RANGE":{alias:"BINOM.DA\u011e.ARALIK"},"BINOM.INV":{alias:"B\u0130NOM.TERS"},BINOMDIST:{alias:"B\u0130NOMDA\u011e"},BITAND:{alias:"B\u0130TVE"},BITLSHIFT:{alias:"B\u0130TSOLAKAYDIR"},BITOR:{alias:"B\u0130TVEYA"},BITRSHIFT:{alias:"B\u0130TSA\u011eAKAYDIR"},BITXOR:{alias:"B\u0130T\xd6ZELVEYA"},CALL:{alias:"\xc7A\u011eIR"},CEILING:{alias:"TAVANAYUVARLA"},"CEILING.MATH":{alias:"TAVANAYUVARLA.MATEMAT\u0130K"},CELL:{alias:"H\xdcCRE"},CHAR:{alias:"DAMGA"},CHIDIST:{alias:"K\u0130KAREDA\u011e"},CHIINV:{alias:"K\u0130KARETERS"},"CHISQ.DIST":{alias:"K\u0130KARE.DA\u011e"},"CHISQ.DIST.RT":{alias:"K\u0130KARE.DA\u011e.SA\u011eK"},"CHISQ.INV":{alias:"K\u0130KARE.TERS"},"CHISQ.INV.RT":{alias:"K\u0130KARE.TERS.SA\u011eK"},"CHISQ.TEST":{alias:"K\u0130KARE.TEST"},CHITEST:{alias:"K\u0130KARETEST"},CHOOSE:{alias:"ELEMAN"},CLEAN:{alias:"TEM\u0130Z"},CODE:{alias:"KOD"},COLUMN:{alias:"S\xdcTUN"},COLUMNS:{alias:"S\xdcTUNSAY"},COMBIN:{alias:"KOMB\u0130NASYON"},COMBINA:{alias:"KOMB\u0130NASYONA"},COMPLEX:{alias:"KARMA\u015eIK"},CONCATENATE:{alias:"B\u0130RLE\u015eT\u0130R"},CONFIDENCE:{alias:"G\xdcVEN\u0130RL\u0130K"},"CONFIDENCE.NORM":{alias:"G\xdcVEN\u0130L\u0130RL\u0130K.NORM"},"CONFIDENCE.T":{alias:"G\xdcVEN\u0130L\u0130RL\u0130K.T"},CONVERT:{alias:"\xc7EV\u0130R"},CORREL:{alias:"KORELASYON"},COUNT:{alias:"BA\u011e_DE\u011e_SAY"},COUNTA:{alias:"BA\u011e_DE\u011e_DOLU_SAY"},COUNTBLANK:{alias:"BO\u015eLUKSAY"},COUNTIF:{alias:"E\u011eERSAY"},COUNTIFS:{alias:"\xc7OKE\u011eERSAY"},COUPDAYBS:{alias:"KUPONG\xdcNBD"},COUPDAYS:{alias:"KUPONG\xdcN"},COUPDAYSNC:{alias:"KUPONG\xdcNDSK"},COUPNCD:{alias:"KUPONG\xdcNSKT"},COUPNUM:{alias:"KUPONSAYI"},COUPPCD:{alias:"KUPONG\xdcN\xd6KT"},COVAR:{alias:"KOVARYANS"},"COVARIANCE.P":{alias:"KOVARYANS.P"},"COVARIANCE.S":{alias:"KOVARYANS.S"},CRITBINOM:{alias:"KR\u0130T\u0130KB\u0130NOM"},CUBEKPIMEMBER:{alias:"K\xdcPKPI\xdcYES\u0130"},CUBEMEMBER:{alias:"K\xdcP\xdcYES\u0130"},CUBEMEMBERPROPERTY:{alias:"K\xdcP\xdcYE\xd6ZELL\u0130\u011e\u0130"},CUBERANKEDMEMBER:{alias:"DERECEL\u0130K\xdcP\xdcYES\u0130"},CUBESET:{alias:"K\xdcPK\xdcMES\u0130"},CUBESETCOUNT:{alias:"K\xdcPK\xdcMESAYISI"},CUBEVALUE:{alias:"K\xdcPDE\u011eER\u0130"},CUMIPMT:{alias:"TOP\xd6DENENFA\u0130Z"},CUMPRINC:{alias:"TOPANAPARA"},DATE:{alias:"TAR\u0130H"},DATEVALUE:{alias:"TAR\u0130HSAYISI"},DAVERAGE:{alias:"VSE\xc7ORT"},DAY:{alias:"G\xdcN"},DAYS:{alias:"G\xdcN"},DAYS360:{alias:"G\xdcN360"},DB:{alias:"AZALANBAK\u0130YE"},DCOUNT:{alias:"VSE\xc7SAY"},DCOUNTA:{alias:"VSE\xc7SAYDOLU"},DDB:{alias:"\xc7\u0130FTAZALANBAK\u0130YE"},DECIMAL:{alias:"ONDALIK"},DEGREES:{alias:"DERECE"},DEVSQ:{alias:"SAPKARE"},DGET:{alias:"VAL"},DISC:{alias:"\u0130ND\u0130R\u0130M"},DMAX:{alias:"VSE\xc7MAK"},DMIN:{alias:"VSE\xc7M\u0130N"},DOLLAR:{alias:"L\u0130RA"},DOLLARDE:{alias:"L\u0130RAON"},DOLLARFR:{alias:"L\u0130RAKES"},DPRODUCT:{alias:"VSE\xc7\xc7ARP"},DSTDEV:{alias:"VSE\xc7STDSAPMA"},DSTDEVP:{alias:"VSE\xc7STDSAPMAS"},DSUM:{alias:"VSE\xc7TOPLA"},DURATION:{alias:"S\xdcRE"},DVAR:{alias:"VSE\xc7VAR"},DVARP:{alias:"VSE\xc7VARS"},EDATE:{alias:"SER\u0130TAR\u0130H"},EFFECT:{alias:"ETK\u0130N"},EOMONTH:{alias:"SER\u0130AY"},ERF:{alias:"HATA\u0130\u015eLEV"},"ERF.PRECISE":{alias:"HATA\u0130\u015eLEV.DUYARLI"},ERFC:{alias:"T\xdcMHATA\u0130\u015eLEV"},"ERFC.PRECISE":{alias:"T\xdcMHATA\u0130\u015eLEV.DUYARLI"},"ERROR.TYPE":{alias:"HATA.T\u0130P\u0130"},EVEN:{alias:"\xc7\u0130FT"},EXACT:{alias:"\xd6ZDE\u015e"},EXP:{alias:"\xdcS"},"EXPON.DIST":{alias:"\xdcSTEL.DA\u011e"},EXPONDIST:{alias:"\xdcSTELDA\u011e"},"F.DIST":{alias:"F.DA\u011e"},"F.DIST.RT":{alias:"F.DA\u011e.SA\u011eK"},"F.INV":{alias:"F.TERS"},"F.INV.RT":{alias:"F.TERS.SA\u011eK"},FACT:{alias:"\xc7ARPINIM"},FACTDOUBLE:{alias:"\xc7\u0130FTFAKT\xd6R"},FALSE:{alias:"YANLI\u015e"},FDIST:{alias:"FDA\u011e"},FIND:{alias:"BUL"},FINDB:{alias:"BULB"},FINV:{alias:"FTERS"},FISHERINV:{alias:"FISHERTERS"},FIXED:{alias:"SAYID\xdcZENLE"},FLOOR:{alias:"TABANAYUVARLA"},"FLOOR.MATH":{alias:"TABANAYUVARLA.MATEMAT\u0130K"},FORECAST:{alias:"TAHM\u0130N"},FORMULATEXT:{alias:"FORM\xdcLMETN\u0130"},FREQUENCY:{alias:"SIKLIK"},FV:{alias:"GD"},FVSCHEDULE:{alias:"GDPROGRAM"},GAMMA:{alias:"GAMA"},"GAMMA.DIST":{alias:"GAMA.DA\u011e"},"GAMMA.INV":{alias:"GAMA.TERS"},GAMMADIST:{alias:"GAMADA\u011e"},GAMMAINV:{alias:"GAMATERS"},GAMMALN:{alias:"GAMALN"},GCD:{alias:"OBEB"},GEOMEAN:{alias:"GEOORT"},GESTEP:{alias:"BESINIR"},GETPIVOTDATA:{alias:"\xd6ZETVER\u0130AL"},GROWTH:{alias:"B\xdcY\xdcME"},HARMEAN:{alias:"HARORT"},HLOOKUP:{alias:"YATAYARA"},HOUR:{alias:"SAAT"},HYPERLINK:{alias:"K\xd6PR\xdc"},"HYPGEOM.DIST":{alias:"H\u0130PERGEOM.DA\u011e"},HYPGEOMDIST:{alias:"H\u0130PERGEOMDA\u011e"},IF:{alias:"E\u011eER"},IFERROR:{alias:"E\u011eERHATA"},IFNA:{alias:"E\u011eERYOKSA"},IMABS:{alias:"SANMUTLAK"},IMAGINARY:{alias:"SANAL"},IMARGUMENT:{alias:"SANBA\u011e_DE\u011e\u0130\u015eKEN"},IMCONJUGATE:{alias:"SANE\u015eLENEK"},IMCOS:{alias:"SANCOS"},IMCOSH:{alias:"SANCOSH"},IMCSC:{alias:"SANCSC"},IMCSCH:{alias:"SANCSCH"
},IMDIV:{alias:"SANB\xd6L"},IMEXP:{alias:"SAN\xdcS"},IMLN:{alias:"SANLN"},IMLOG10:{alias:"SANLOG10"},IMLOG2:{alias:"SANLOG2"},IMPOWER:{alias:"SANKUVVET"},IMPRODUCT:{alias:"SAN\xc7ARP"},IMREAL:{alias:"SANGER\xc7EK"},IMSEC:{alias:"SANSEC"},IMSECH:{alias:"SANSECH"},IMSIN:{alias:"SANSIN"},IMSINH:{alias:"SANSINH"},IMSQRT:{alias:"SANKAREK\xd6K"},IMSUB:{alias:"SANTOPLA"},IMSUM:{alias:"SAN\xc7IKAR"},IMTAN:{alias:"SANTAN"},INDEX:{alias:"\u0130ND\u0130S"},INDIRECT:{alias:"DOLAYLI"},INFO:{alias:"B\u0130LG\u0130"},INT:{alias:"TAMSAYI"},INTERCEPT:{alias:"KESMENOKTASI"},INTRATE:{alias:"FA\u0130ZORANI"},IPMT:{alias:"FA\u0130ZTUTARI"},IRR:{alias:"\u0130\xc7_VER\u0130M_ORANI"},ISBLANK:{alias:"EBO\u015eSA"},ISERR:{alias:"EHATA"},ISERROR:{alias:"EHATALIYSA"},ISEVEN:{alias:"\xc7\u0130FTM\u0130"},ISFORMULA:{alias:"EFORM\xdcLSE"},ISLOGICAL:{alias:"EMANTIKSALSA"},ISNA:{alias:"EYOKSA"},ISNONTEXT:{alias:"EMET\u0130NDE\u011e\u0130LSE"},ISNUMBER:{alias:"ESAYIYSA"},"ISO.CEILING":{alias:"ISO.TAVAN"},ISODD:{alias:"TEKM\u0130"},ISOWEEKNUM:{alias:"ISOHAFTASAY"},ISREF:{alias:"EREFSE"},ISTEXT:{alias:"EMET\u0130NSE"},KURT:{alias:"BASIKLIK"},LARGE:{alias:"B\xdcY\xdcK"},LCM:{alias:"OKEK"},LEFT:{alias:"SOLDAN"},LEFTB:{alias:"SOLB"},LEN:{alias:"UZUNLUK"},LENB:{alias:"UZUNLUKB"},LINEST:{alias:"DOT"},LOGEST:{alias:"LOT"},LOGINV:{alias:"LOGTERS"},"LOGNORM.DIST":{alias:"LOGNORM.DA\u011e"},"LOGNORM.INV":{alias:"LOGNORM.TERS"},LOGNORMDIST:{alias:"LOGNORMDA\u011e"},LOOKUP:{alias:"ARA"},LOWER:{alias:"K\xdc\xc7\xdcKHARF"},MATCH:{alias:"KA\xc7INCI"},MAX:{alias:"MAK"},MAXA:{alias:"MAKA"},MDETERM:{alias:"DETERM\u0130NANT"},MDURATION:{alias:"MS\xdcRE"},MEDIAN:{alias:"ORTANCA"},MID:{alias:"PAR\xc7AAL"},MIDB:{alias:"ORTAB"},MIN:{alias:"M\u0130N"},MINA:{alias:"M\u0130NA"},MINUTE:{alias:"DAK\u0130KA"},MINVERSE:{alias:"D\u0130ZEY_TERS"},MIRR:{alias:"D_\u0130\xc7_VER\u0130M_ORANI"},MMULT:{alias:"D\xc7ARP"},MODE:{alias:"EN\xc7OK_OLAN"},"MODE.MULT":{alias:"EN\xc7OK_OLAN.\xc7OK"},"MODE.SNGL":{alias:"EN\xc7OK_OLAN.TEK"},MONTH:{alias:"AY"},MROUND:{alias:"KYUVARLA"},MULTINOMIAL:{alias:"\xc7OKTER\u0130ML\u0130"},MUNIT:{alias:"B\u0130R\u0130MMATR\u0130S"},N:{alias:"S"},NA:{alias:"YOKSAY"},"NEGBINOM.DIST":{alias:"NEGB\u0130NOM.DA\u011e"},NEGBINOMDIST:{alias:"NEGB\u0130NOMDA\u011e"},NETWORKDAYS:{alias:"TAM\u0130\u015eG\xdcN\xdc"},"NETWORKDAYS.INTL":{alias:"TAM\u0130\u015eG\xdcN\xdc.ULUSL"},NOMINAL:{alias:"NOM\u0130NAL"},"NORM.DIST":{alias:"NORM.DA\u011e"},"NORM.INV":{alias:"NORM.TERS"},"NORM.S.DIST":{alias:"NORM.S.DA\u011e"},"NORM.S.INV":{alias:"NORM.S.TERS"},NORMDIST:{alias:"NORMDA\u011e"},NORMINV:{alias:"NORMTERS"},NORMSDIST:{alias:"NORMSDA\u011e"},NORMSINV:{alias:"NORMSTERS"},NOT:{alias:"DE\u011e\u0130L"},NOW:{alias:"\u015e\u0130MD\u0130"},NPER:{alias:"TAKS\u0130T_SAYISI"},NPV:{alias:"NBD"},NUMBERVALUE:{alias:"SAYIDE\u011eER\u0130"},ODD:{alias:"TEK"},ODDFPRICE:{alias:"TEKYDE\u011eER"},ODDFYIELD:{alias:"TEKY\xd6DEME"},ODDLPRICE:{alias:"TEKSDE\u011eER"},ODDLYIELD:{alias:"TEKS\xd6DEME"},OFFSET:{alias:"KAYDIR"},OR:{alias:"YADA"},PDURATION:{alias:"PS\xdcRE"},PERCENTILE:{alias:"Y\xdcZDEB\u0130RL\u0130K"},"PERCENTILE.EXC":{alias:"Y\xdcZDEB\u0130RL\u0130K.HRC"},"PERCENTILE.INC":{alias:"Y\xdcZDEB\u0130RL\u0130K.DHL"},PERCENTRANK:{alias:"Y\xdcZDERANK"},"PERCENTRANK.EXC":{alias:"Y\xdcZDERANK.HRC"},"PERCENTRANK.INC":{alias:"Y\xdcZDERANK.DHL"},PERMUT:{alias:"PERM\xdcTASYON"},PERMUTATIONA:{alias:"PERM\xdcTASYONA"},PHONETIC:{alias:"SES"},PI:{alias:"P\u0130"},PMT:{alias:"DEVRESEL_\xd6DEME"},"POISSON.DIST":{alias:"POISSON.DA\u011e"},POWER:{alias:"KUVVET"},PPMT:{alias:"ANA_PARA_\xd6DEMES\u0130"},PRICE:{alias:"DE\u011eER"},PRICEDISC:{alias:"DE\u011eER\u0130ND"},PRICEMAT:{alias:"DE\u011eERVADE"},PROB:{alias:"OLASILIK"},PRODUCT:{alias:"\xc7ARPIM"},PROPER:{alias:"YAZIM.D\xdcZEN\u0130"},PV:{alias:"BD"},QUARTILE:{alias:"D\xd6RTTEB\u0130RL\u0130K"},"QUARTILE.EXC":{alias:"D\xd6RTTEB\u0130RL\u0130K.HRC"},"QUARTILE.INC":{alias:"D\xd6RTTEB\u0130RL\u0130K.DHL"},QUOTIENT:{alias:"B\xd6L\xdcM"},RADIANS:{alias:"RADYAN"},RAND:{alias:"S_SAYI_\xdcRET"},RANDBETWEEN:{alias:"RASTGELEARADA"},"RANK.AVG":{alias:"RANK.ORT"},"RANK.EQ":{alias:"RANK.E\u015e\u0130T"},RATE:{alias:"FA\u0130Z_ORANI"},RECEIVED:{alias:"GET\u0130R\u0130"},"REGISTER.ID":{alias:"YAZMA\xc7.KODU"},REPLACE:{alias:"DE\u011e\u0130\u015eT\u0130R"},REPLACEB:{alias:"DE\u011e\u0130\u015eT\u0130RB"},REPT:{alias:"Y\u0130NELE"},RIGHT:{alias:"SA\u011eDAN"},RIGHTB:{alias:"SA\u011eB"},ROMAN:{alias:"ROMEN"},ROUND:{alias:"YUVARLA"},ROUNDDOWN:{alias:"A\u015eA\u011eIYUVARLA"},ROUNDUP:{alias:"YUKARIYUVARLA"},ROW:{alias:"SATIR"},ROWS:{alias:"SATIRSAY"},RRI:{alias:"GER\xc7EKLE\u015eENYATIRIMGET\u0130R\u0130S\u0130"},RSQ:{alias:"RKARE"},RTD:{alias:"GZV"},SEARCH:{alias:"MBUL"},SEARCHB:{alias:"ARAB"},SECOND:{alias:"SAN\u0130YE"},SERIESSUM:{alias:"SER\u0130TOPLA"},SHEET:{alias:"SAYFA"},SHEETS:{alias:"SAYFALAR"},SIGN:{alias:"\u0130\u015eARET"},SIN:{alias:"S\u0130N"},SINH:{alias:"S\u0130NH"},SKEW:{alias:"\xc7ARPIKLIK"},"SKEW.P":{alias:"\xc7ARPIKLIK.P"},SLN:{alias:"DA"},SLOPE:{alias:"E\u011e\u0130M"},SMALL:{alias:"K\xdc\xc7\xdcK"},SQRT:{alias:"KAREK\xd6K"},SQRTPI:{alias:"KAREK\xd6KP\u0130"},STANDARDIZE:{alias:"STANDARTLA\u015eTIRMA"},STDEV:{alias:"STDSAPMA"},"STDEV.P":{alias:"STDSAPMA.P"},"STDEV.S":{alias:"STDSAPMA.S"},STDEVA:{alias:"STDSAPMAA"},STDEVP:{alias:"STDSAPMAS"},STDEVPA:{alias:"STDSAPMASA"},STEYX:{alias:"STHYX"},SUBSTITUTE:{alias:"YER\u0130NEKOY"},SUBTOTAL:{alias:"ALTTOPLAM"},SUM:{alias:"TOPLA"},SUMIF:{alias:"ETOPLA"},SUMIFS:{alias:"\xc7OKETOPLA"},SUMPRODUCT:{alias:"TOPLA.\xc7ARPIM"},SUMSQ:{alias:"TOPKARE"},SUMX2MY2:{alias:"TOPX2EY2"},SUMX2PY2:{alias:"TOPX2AY2"},SUMXMY2:{alias:"TOPXEY2"},SYD:{alias:"YAT"},T:{alias:"M"},"T.DIST":{alias:"T.DA\u011e"},"T.DIST.2T":{alias:"T.DA\u011e.2K"},"T.DIST.RT":{alias:"T.DA\u011e.SA\u011eK"},"T.INV":{alias:"T.TERS"},"T.INV.2T":{alias:"T.TERS.2K"},TBILLEQ:{alias:"HTAHE\u015e"},TBILLPRICE:{alias:"HTAHDE\u011eER"},TBILLYIELD:{alias:"HTAH\xd6DEME"},TDIST:{alias:"TDA\u011e"},TEXT:{alias:"METNE\xc7EV\u0130R"},TIME:{alias:"ZAMAN"},TIMEVALUE:{alias:"ZAMANSAYISI"},TINV:{alias:"TTERS"},TODAY:{alias:"BUG\xdcN"},TRANSPOSE:{alias:"DEVR\u0130K_D\xd6N\xdc\u015e\xdcM"},TREND:{alias:"E\u011e\u0130L\u0130M"},TRIM:{alias:"KIRP"},TRIMMEAN:{alias:"KIRPORTALAMA"},TRUE:{alias:"DO\u011eRU"},TRUNC:{alias:"NSAT"},TYPE:{alias:"T\xdcR"},UNICHAR:{alias:"UNICODEKARAKTER\u0130"},UPPER:{alias:"B\xdcY\xdcKHARF"},VALUE:{alias:"SAYIYA\xc7EV\u0130R"},VARP:{alias:"VARS"},VARPA:{alias:"VARSA"},VDB:{alias:"DAB"},VLOOKUP:{alias:"D\xdc\u015eEYARA"},WEBSERVICE:{alias:"WEBH\u0130ZMET\u0130"},WEEKDAY:{alias:"HAFTANING\xdcN\xdc"},WEEKNUM:{alias:"HAFTASAY"},"WEIBULL.DIST":{alias:"WEIBULL.DA\u011e"},WORKDAY:{alias:"\u0130\u015eG\xdcN\xdc"},"WORKDAY.INTL":{alias:"\u0130\u015eG\xdcN\xdc.ULUSL"},XIRR:{alias:"A\u0130\xc7VER\u0130MORANI"},XNPV:{alias:"ANBD"},XOR:{alias:"\xd6ZELVEYA"},YEAR:{alias:"YIL"},YEARFRAC:{alias:"YILORAN"},YIELD:{alias:"\xd6DEME"},YIELDDISC:{alias:"\xd6DEME\u0130ND"},YIELDMAT:{alias:"\xd6DEMEVADE"}},tableFunctionsMapping:{"#All":"#T\xfcm\xfc","#Data":"#Veri","#Headers":"#\xdcst Bilgiler","#Totals":"#Toplamlar","#This row":"#Bu Sat\u0131r"},clacErrorMapping:{"#NULL!":"#BO\u015e!","#DIV/0!":"#SAYI/0!","#VALUE!":"#DE\u011eER!","#REF!":"#BA\u015eV!","#NAME?":"#AD?","#N/A!":"#YOK","#NUM!":"#SAYI!"},booleanMapping:{boolean_true:"DO\u011eRU",boolean_false:"YANLI\u015e"}}},CalcEngine:function(a,b){a.exports=c("@grapecity/js-calc")},Common:function(a,b){a.exports=c("@grapecity/js-sheets-common")}}),a.exports=d.Spread.CalcEngine.LanguagePackages},"./node_modules_local/@grapecity/js-calc-languagepackages/index.js":function(a,b,c){a.exports=c("./node_modules_local/@grapecity/js-calc-languagepackages/dist/gc.spread.calcengine.languagepackages.js")},"@grapecity/js-calc":function(b,c){b.exports=a.Spread.CalcEngine},"@grapecity/js-sheets-common":function(b,c){b.exports=a.Spread}})});