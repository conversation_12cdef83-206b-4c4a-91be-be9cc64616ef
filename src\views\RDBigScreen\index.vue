<template>
  <div class="RD-bigScreen">
    <a-spin :spinning="pageLoading">
      <div class="__box">
        <!-- 顶部选项 -->
        <div class="top-condition">
          <div class="title-box">
            <img src="./images/logo.png" alt="" class="logo" />
            <img src="./images/title.png" alt="" class="title" />
          </div>
          <div class="button-group">
            <div class="GYL-btn btn active">
              研发
            </div>
            <div class="AB-btn btn">
              安全环保
            </div>
            <div class="ZSCQ-btn btn">
              知识产权
            </div>
            <div class="ZDSYS-btn btn">
              重点实验室
            </div>
            <div class="time _flex">
              <span>年月</span>
              <a-month-picker
                :allowClear="false"
                v-model="time"
                :format="monthFormat"
                @change="getTopCardList"
              />
            </div>
          </div>
        </div>
        <!-- 图表区域 -->
        <div class="charts-area">
          <!-- chart1 -->
          <div
            :class="[item.indexName === activeIndexName ? 'active' : '']"
            v-for="item in topCardList"
            :key="item.indexName"
            @click="
              activeIndexName = item.indexName;
              getReasonData();
            "
          >
            <img
              src="./images/details.png"
              @click.stop="
                pageLoading = true;
                $refs['modal'].show({ indexName: item.indexName, time });
              "
              class="details"
              alt=""
              srcset=""
            />
            <div class="_top">
              <div class="_l">
                <span class="title"
                  >{{ item.indexName }}
                  <template v-if="item.indexName === 'TOP机型'">
                    <a-tooltip placement="top">
                      <template slot="title">
                        <span
                          >TOP机型目标值、实际值为TOP5\TOP20\TOP30\TOP50等指标的目标、实际的直接加和，完成率为根据线上、线下、TOP等级的不同权重系数加权计算。</span
                        >
                      </template>
                      <a-icon type="question-circle" />
                    </a-tooltip>
                  </template>
                </span>
                <div class="data-box">
                  <div class="num">{{ item.salesVolume }}</div>
                  <div class="dw">{{ item.salesUnit }}</div>
                </div>
                <div class="target">
                  <span>目标值</span>
                  <span>{{ item.targetValue }}{{ item.salesUnit }}</span>
                </div>
              </div>
              <div class="chart" ref="chart1">
                <a-progress type="circle" :percent="item.completionRate">
                  <template #format="percent">
                    <div>
                      <span>{{ item.completionRateDesc }}</span>
                      <span>完成率</span>
                    </div>
                  </template>
                </a-progress>
              </div>
            </div>
            <div class="THB">
              <div>
                <span class="_title">同比</span>
                <template v-if="item.whetherYearOnYear">
                  <span
                    :style="
                      styleObject(item.yearOnYearRateDesc, item.indexType)
                    "
                  ></span>
                  <span
                    :style="{
                      color: item.yearOnYearRate
                        ? item.indexType === '正向'
                          ? item.yearOnYearRateDesc.includes('-')
                            ? '#6495F9'
                            : '#f75050'
                          : item.yearOnYearRateDesc.includes('-')
                          ? '#f75050'
                          : '#6495F9'
                        : 'rgba(0, 0, 0, 0.8);'
                    }"
                  >
                    {{ item.yearOnYearRateDesc }}
                  </span>
                </template>
                <span
                  v-if="!item.whetherYearOnYear"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.1, 0.1, 0.1);font-size: 0.1rem;"
                >
                  不对比
                </span>
              </div>
              <div>
                <span class="_title">环比</span>
                <template v-if="item.whetherChainRatioOnYear">
                  <span
                    :style="
                      styleObject(item.chainRatioRateDesc, item.indexType)
                    "
                  ></span>
                  <span
                    :style="{
                      color: item.chainRatioRate
                        ? item.indexType === '正向'
                          ? item.chainRatioRateDesc.includes('-')
                            ? '#6495F9'
                            : '#f75050'
                          : item.chainRatioRateDesc.includes('-')
                          ? '#f75050'
                          : '#6495F9'
                        : 'rgba(0, 0, 0, 0.8);'
                    }"
                  >
                    {{ item.chainRatioRateDesc }}
                  </span>
                </template>
                <span
                  v-if="!item.whetherChainRatioOnYear"
                  style="border: 0.5px solid;margin-left: 5px;transform: scale3d(0.1, 0.1, 0.1);font-size: 0.1rem;"
                >
                  不对比
                </span>
              </div>
            </div>
          </div>
        </div>
        <!-- 数据分析 -->
        <a-spin :spinning="reasonLoading">
          <div class="analysis" v-if="!reasonLoading">
            <div class="_center">
              <template v-for="(item, index) in reasonData">
                <div class="item" :key="index">
                  <div class="title-box">
                    <span>{{ item.analysisTitle }}</span>
                    <!-- <span>50%</span> -->
                  </div>
                  <template v-for="(zitm, zidx) in item.detailList">
                    <div class="sub-item" :key="zidx">
                      <div class="_left">
                        <div class="title-div">
                          {{ zitm.dimensionName }} 问题定位
                        </div>
                        <div class="reason">
                          <div
                            v-for="(reason, ridx) in zitm.reasonList"
                            :key="ridx"
                          >
                            <span class="index">{{ ridx + 1 }}</span>
                            <div class="html" v-html="reason"></div>
                          </div>
                        </div>
                      </div>
                      <div class="_right">
                        <div class="_l">
                          <div class="title-div">闭环管理措施</div>
                          <div class="responsible">
                            <div class="_t">责任人：</div>
                            <div class="list">
                              <div
                                v-for="respUser in zitm.respUserList"
                                :key="respUser.loginName"
                              >
                                <span
                                  >{{ respUser.userName }}（{{
                                    respUser.loginName
                                  }}）</span
                                >
                                <img
                                  src="./images/hichat.png"
                                  title="信鸿沟通"
                                  alt="信鸿沟通"
                                  srcset=""
                                  class="hichat"
                                  @click="
                                    chat(
                                      respUser.loginName,
                                      zitm.indexColumn,
                                      zitm.reasonList
                                    )
                                  "
                                />
                                <img
                                  src="./images/mail.png"
                                  title="邮件沟通"
                                  alt="邮件沟通"
                                  srcset=""
                                  class="mail"
                                  @click="
                                    sendMail(
                                      respUser,
                                      zitm.indexColumn,
                                      zitm.reasonList
                                    )
                                  "
                                />
                              </div>
                            </div>
                          </div>
                          <div class="measures">
                            <div class="_t">闭环措施：</div>
                            <div
                              v-html="
                                zitm.commentList
                                  ? zitm.commentList.commentContent
                                  : ''
                              "
                            ></div>
                          </div>
                        </div>
                        <div class="_r">
                          <div class="title-div">指标效果</div>
                          <!-- <div class="_t">指标改善：</div> -->
                          <div
                            class="_chart"
                            :ref="`${zitm.indexColumn}-${index}-${zidx}`"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </template>
            </div>
          </div>
        </a-spin>
      </div>
    </a-spin>
    <Mail ref="mail" />
    <Modal ref="modal" @cancelLoading="cancelLoading" />
  </div>
</template>
<script>
import * as echarts from "echarts";
import moment from "moment";
import Modal from "./modal.vue";
import Mail from "../SMC_PC/Card/CardInfo/mail.vue";
import request from "@/utils/requestHttp";
import axios from "axios";
import { dealThousandData } from "../SMC_PC/IndexGeneralView/utils";
import { styleObject } from "./util";
import Decimal from "decimal.js";

export default {
  components: { Mail, Modal },
  data() {
    return {
      styleObject,
      time: moment(
        new Date(new Date().getFullYear(), new Date().getMonth(), 0).getTime(),
        this.monthFormat
      ), // 时间
      monthFormat: "YYYY-MM", // 月格式化
      hichat_AccessToken: null,
      activeIndexName: "", // 当前查看的指标名称
      topCardList: [
        // 顶部卡片列表
      ],
      reasonData: [], // 数据分析及措施
      pageLoading: true,
      reasonLoading: false,
      chartObject: {},
      chartOption: {
        color: ["#23B5B1"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          }
        },
        legend: {
          show: false,
          lineStyle: {
            type: "dashed"
          },
          data: ["完成率"]
        },
        grid: {
          left: "8%",
          right: "8%",
          bottom: "0",
          top: "13%",
          containLabel: true
        },
        xAxis: [
          {
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed"
              }
            },
            type: "category",
            boundaryGap: false,
            data: []
          }
        ],
        yAxis: [
          {
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            type: "value",
            min: function(value) {
              return value.min - 20;
            }
          }
        ],
        series: [
          {
            name: "完成率",
            type: "line",
            smooth: true,
            lineStyle: {
              width: 3
            },
            showSymbol: true,
            labelLine: {
              show: true
            },
            label: {
              show: true,
              position: "top",
              distance: 0,
              color: "rgba(0, 0, 0, 1)",
              formatter: "{c}%"
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(0, 170, 166, 1)"
                },
                {
                  offset: 1,
                  color: "rgba(0, 170, 166, 0)"
                }
              ])
            },
            data: []
          }
        ]
      }
    };
  },
  mounted() {
    let _this = this;
    this.resetFontsize();
    window.onresize = () => {
      return (() => {
        _this.resetFontsize();
      })();
    };
    this.getHichatAccessToken();
    this.getTopCardList();
  },
  methods: {
    // Modal组件顶部卡片请求结束
    cancelLoading(topCardLengthInModal) {
      this.pageLoading = false;
      if (topCardLengthInModal === 0) {
        this.$message.error("暂无数据");
      }
    },
    // 获取顶部4个卡片
    getTopCardList() {
      this.reasonData = [];
      request(
        `/api/smc/statistics/query?yearAndMonth=${moment(this.time).format(
          "YYYY-MM"
        )}`
      ).then(res => {
        this.pageLoading = false;
        this.topCardList = res;
        // 获取四个卡片中最低完成率的指标进行数据分析
        this.activeIndexName = "单平台平均销量";
        // this.topCardList.filter(card => {
        //   return (
        //     (card.completionRate ? card.completionRate : 0) ===
        //     Math.min(...this.topCardList.map(item => item.completionRate || 0))
        //   );
        // })[0].indexName;
        this.getReasonData();
      });
    },
    // 获取数据分析
    getReasonData() {
      this.reasonLoading = true;
      this.reasonData = [];
      for (const key in this.chartObject) {
        if (Object.hasOwnProperty.call(this.chartObject, key)) {
          const element = this.chartObject[key];
          if (element) {
            element.dispose();
          }
          this.chartObject[key] = null;
          delete this.chartObject[key];
        }
      }
      request(
        `/api/smc/statistics/analysisList?yearAndMonth=${moment(
          this.time
        ).format("YYYY-MM")}&indexName=${this.activeIndexName}`
      )
        .then(res => {
          this.reasonLoading = false;
          this.reasonData = res;
          this.$nextTick(() => {
            res.forEach((item, index) => {
              item.detailList.forEach((zitem, zindex) => {
                if (
                  typeof zitem.indexEffect === "object" &&
                  Object.keys(zitem.indexEffect)
                ) {
                  this.chartObject[
                    `${zitem.indexColumn}-${index}-${zindex}`
                  ] = echarts.init(
                    this.$refs[`${zitem.indexColumn}-${index}-${zindex}`][0]
                  );
                  const option = Object.assign({}, this.chartOption);
                  option.xAxis[0].data = Object.keys(zitem.indexEffect);
                  option.series[0].data = Object.values(zitem.indexEffect).map(
                    valItem => valItem.completionRate || 0
                  );
                  this.chartObject[
                    `${zitem.indexColumn}-${index}-${zindex}`
                  ].setOption(option);
                }
              });
            });
          });
        })
        .catch(() => {
          this.reasonLoading = false;
        });
    },
    // 获取信鸿token
    getHichatAccessToken() {
      request(`/api/smc/hichatx/getAccessToken`)
        .then(res => {
          this.hichat_AccessToken = res.accessToken;
        })
        .catch(() => {});
    },
    // 信鸿聊天
    async chat(loginName, indexId, reasonList) {
      const data = encodeURIComponent(`{array:['${loginName}']}`);
      const cardItem = await this.getCardInfo(indexId);
      if (cardItem) {
        axios({
          url: `https://hichatx.hisense.com/gateway/openimport/open/person/getInfoByJobNo?accessToken=${this.hichat_AccessToken}`,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          method: "post",
          data: `eid=101&data=${data}`
        }).then(res => {
          if (res.data?.success) {
            let defaultMessage = `${cardItem.plateName}-${
              cardItem.indexName
            }（${cardItem.companyName}-${cardItem.baseName}-${
              cardItem.dwppCmTfIndexLibrary.indexDt
            }${cardItem.dataFrequency}）
目标值：${cardItem.realTargetValue} ${cardItem.unit}；实际值：${
              cardItem.realBaseActual
            } ${cardItem.unit}；完成率：${
              cardItem.dwppCmTfIndexLibrary.completionRate
                ? Decimal(cardItem.dwppCmTfIndexLibrary.completionRate)
                    .mul(Decimal(100))
                    .toFixed(2, Decimal.ROUND_HALF_UP)
                : "-"
            }%；
${
  cardItem.isContemRate === "Y"
    ? "同比" +
      (cardItem.dwppCmTfIndexLibrary.contemRate
        ? (cardItem.dwppCmTfIndexLibrary.contemRate.includes("-") &&
            cardItem.dwppCmTfIndexLibrary.indexType === "反向") ||
          (!cardItem.dwppCmTfIndexLibrary.contemRate.includes("-") &&
            cardItem.dwppCmTfIndexLibrary.indexType === "正向")
          ? "上涨"
          : "下降"
        : "") +
      "：" +
      (cardItem.dwppCmTfIndexLibrary.contemRate
        ? Math.abs(
            Decimal(cardItem.dwppCmTfIndexLibrary.contemRate)
              .mul(Decimal(100))
              .toFixed(2, Decimal.ROUND_HALF_UP)
          )
        : "-") +
      "%；"
    : ""
} ${
              cardItem.isPreviousRate === "Y"
                ? "环比" +
                  (cardItem.dwppCmTfIndexLibrary.previousRate
                    ? (cardItem.dwppCmTfIndexLibrary.previousRate.includes(
                        "-"
                      ) &&
                        cardItem.dwppCmTfIndexLibrary.indexType === "反向") ||
                      (!cardItem.dwppCmTfIndexLibrary.previousRate.includes(
                        "-"
                      ) &&
                        cardItem.dwppCmTfIndexLibrary.indexType === "正向")
                      ? "上涨"
                      : "下降"
                    : "") +
                  "：" +
                  (cardItem.dwppCmTfIndexLibrary.previousRate
                    ? Math.abs(
                        Decimal(cardItem.dwppCmTfIndexLibrary.previousRate)
                          .mul(Decimal(100))
                          .toFixed(2, Decimal.ROUND_HALF_UP)
                      )
                    : "-") +
                  "%；"
                : ""
            }`;
            defaultMessage += `
数据分析：
${reasonList.map(
  (item, index) => index + 1 + ". " + item.replace(/<\/?.+?>/g, "") + ";"
)}`;
            if (window.self !== window.top) {
              window.parent.postMessage(
                {
                  sourceType: "smc",
                  msgType: "hiChat",
                  msgContent: JSON.stringify({
                    openId: res.data.data[0],
                    defaultMessage
                  })
                },
                "*"
              );
              console.log(
                JSON.stringify({
                  openId: res.data.data[0],
                  defaultMessage
                })
              );
            }
          }
        });
      }
    },
    // 发送邮件
    async sendMail(user, indexId, reasonList) {
      const card = await this.getCardInfo(indexId);
      if (card) {
        this.$refs["mail"].show({
          ...card,
          email: user.email,
          montageHtml:
            `<br />数据分析：<br /><br />` +
            reasonList
              .map((item, index) => `<span>${index + 1}. ${item};</span>`)
              .join("<br />")
        });
      }
    },
    getCardInfo(indexId) {
      this.pageLoading = true;
      return new Promise(resolve => {
        request(`/api/smc/userIndexRelation/getHBIndexListForAnonymous`, {
          method: "POST",
          body: {
            baseStr: indexId.slice(
              indexId.lastIndexOf("=") + 1,
              indexId.length
            ),
            frequency: "月",
            date: moment(this.time).format("YYYY-MM"),
            indexNodeId: indexId.slice(0, indexId.lastIndexOf("=") - 1)
          }
        }).then(res => {
          this.pageLoading = false;
          console.log(res);
          if (Array.isArray(res) && res.length) {
            res.forEach(item => {
              item["recommend"] = false;
              item["dwppCmTfIndexLibrary"] = {
                baseActual: item.baseActual,
                completionRate: item.completionRate,
                targetValue: item.targetValue,
                contemRate: item.contemRate,
                previousRate: item.previousRate,
                indexType: item.indexType,
                precisions: item.precisions,
                indexDt: moment(this.time).format("YYYY-MM")
              };
              item["dataFrequency"] = item.frequency;
              item["plateName"] = item.businessSegments;
              item["companyName"] = item.company;
              item["baseName"] = item.base;
              // 处理实际值和目标值
              item["realBaseActual"] = dealThousandData(
                item.dwppCmTfIndexLibrary.baseActual,
                item.unit,
                item.dwppCmTfIndexLibrary.precisions
              );
              item["realTargetValue"] = dealThousandData(
                item.dwppCmTfIndexLibrary.targetValue,
                item.unit,
                item.dwppCmTfIndexLibrary.precisions
              );
            });
            resolve(res[0]);
          } else {
            resolve(null);
          }
        });
      });
    },
    resetFontsize() {
      let rootHtml = document.documentElement;
      let deviceWidth =
        rootHtml.clientWidth > 1920
          ? 1920
          : rootHtml.clientWidth < 1280
          ? 1280
          : rootHtml.clientWidth;
      rootHtml.style.fontSize = (deviceWidth / 1920) * 100 + "px";
    },
    // 禁用时间
    disabledDate(current) {
      // Can not select days before today and today
      // 今天之后或者两年前
      return (
        (current && current > moment().endOf("day")) ||
        moment().subtract(2, "years") > current
      );
    }
  }
};
</script>
<style lang="less">
.RD-bigScreen,
.RD-bigScreen-modal .ant-modal-body {
  *::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  *::-webkit-scrollbar-button {
    width: 0px;
    height: 0px;
    display: none;
  }
  *::-webkit-scrollbar-corner {
    background-color: transparent;
  }
  *::-webkit-scrollbar-thumb {
    border: 4px solid rgba(0, 0, 0, 0);
    height: 6px;
    border-radius: 25px;
    background-clip: padding-box;
    background-color: rgba(0, 0, 0, 0.3);
  }
  .charts-area {
    box-sizing: border-box;
    display: flex;
    margin: 0.24rem 0.4rem;
    & > div {
      width: calc((100% - 0.72rem) / 4);
      margin-right: 0.24rem;
      cursor: pointer;
      position: relative;
      &:last-child {
        margin-right: 0;
      }
      // justify-content: center;
      background-color: #fff;
      border-radius: 0.16rem;
      padding: 0.32rem 0.32rem 0 0.32rem;
      box-sizing: border-box;
      border: 0.02rem solid #fff;
      &.active {
        background-color: #f7feff;
        border: 0.02rem solid #23b5b1;
      }
      &:hover {
        .details {
          display: block;
        }
      }
      .details {
        display: none;
        position: absolute;
        right: 0.16rem;
        top: 0.16rem;
        width: 0.24rem;
        height: 0.24rem;
      }
      ._top {
        display: flex;
        justify-content: space-between;
        padding-bottom: 0.18rem;
        border-bottom: 1px solid #e8e8e8;
        ._l {
          .title {
            font-size: 0.2rem;
            font-weight: normal;
            color: rgba(23, 23, 38, 0.45);
            margin-bottom: 0.04rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 2.4rem;
            display: block;
          }
          .data-box {
            display: flex;
            align-items: flex-end;
            margin-bottom: 0.24rem;
            .num {
              height: 0.68rem;
              font-size: 0.48rem;
              font-weight: bold;
              color: #171726;
              line-height: 0.68rem;
            }
            .dw {
              height: 0.4rem;
              font-size: 0.28rem;
              font-weight: bold;
              color: #000000;
              line-height: 0.4rem;
              margin-bottom: 0.1rem;
              margin-left: 0.08rem;
            }
          }
          .target {
            height: 0.28rem;
            line-height: 0.28rem;
            font-size: 0.2rem;
            font-weight: 400;
            display: flex;
            span {
              &:first-child {
                margin-right: 0.08rem;
              }
            }
          }
        }
        .chart {
          display: flex;
          align-items: center;
          justify-content: center;
          .ant-progress {
            .ant-progress-inner {
              width: 1.52rem !important;
              height: 1.52rem !important;
            }
            // .ant-progress-circle-trail {
            //   stroke-width: 11 !important;
            // }
            &.ant-progress-status-normal {
              .ant-progress-inner {
                path {
                  &:last-child {
                    // stroke-width: 11 !important;
                    stroke: #23b5b1 !important;
                  }
                }
              }
            }
            &.ant-progress-status-success {
              .ant-progress-inner {
                path {
                  &:last-child {
                    // stroke-width: 11 !important;
                    stroke: #f5222d !important;
                  }
                }
              }
            }
            .ant-progress-text {
              & > div {
                display: flex;
                flex-direction: column;
                justify-content: center;
                & > span {
                  color: rgba(0, 0, 0, 0.65);
                  margin-bottom: 10px;
                  font-size: 16px;
                  font-weight: bold;
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }

      .THB {
        margin-top: 0.11rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 0.2rem;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 0.25rem;
        & > div {
          width: 50%;
          &:nth-child(2) {
            margin-left: 10px;
          }
          ._title {
            margin-right: 8px;
          }
        }
      }
    }
  }
}
.RD-bigScreen {
  .__box {
    width: 100vw;
    height: 100vh;
    min-width: 1280px;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background-color: #f2f3f5;
    .ant-spin-nested-loading {
      flex: 1;
      overflow-y: auto;
    }
  }
  .top-condition {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background: url("./images/top-bg.png") center center no-repeat;
    background-size: cover;
    height: 1rem;
    position: relative;
    .title-box {
      position: absolute;
      top: 0.26rem;
      left: 0.4rem;
      display: flex;
      align-items: center;
      .logo {
        display: block;
        width: 0.4rem;
        height: 0.4rem;
        margin-right: 0.14rem;
      }
      .title {
        display: block;
        width: 3.96rem;
        height: 0.46rem;
      }
    }
    .button-group {
      .btn {
        height: 0.74rem;
        line-height: 0.74rem;
        text-align: center;
        padding: 0 0.1rem;
        font-size: 0.32rem;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.3);
        position: absolute;
        top: 0.23rem;
        cursor: pointer;
        &.active {
          color: #1f7a8c;
          &::before {
            content: "";
            display: block;
            width: 82%;
            height: 0.03rem;
            background: #1f7a8c;
            position: absolute;
            bottom: 0;
            right: 0;
          }
        }
      }
      .GYL-btn {
        left: 6.58rem;
      }
      .AB-btn {
        left: 8.9rem;
      }
      .ZSCQ-btn {
        right: 6.32rem;
      }
      .ZDSYS-btn {
        right: 3.81rem;
      }
      .time {
        height: 0.44rem;
        font-size: 0.32rem;
        font-weight: bold;
        color: #1f7a8c;
        line-height: 0.44rem;
        position: absolute;
        top: 0.38rem;
        right: 0.4rem;
        display: flex;
        align-items: center;
        .ant-calendar-picker {
          width: 1.7rem;
          margin-left: 0.12rem;
          .ant-input,
          .ant-calendar-picker-icon {
            color: #1f7a8c;
            border-color: #1f7a8c;
            background-color: rgba(0, 0, 0, 0);
          }
        }
      }
    }
  }
  .analysis {
    overflow-y: auto;
    flex: 1;
    padding: 0 0.4rem;
    box-sizing: border-box;
    ._center {
      .item {
        margin-bottom: 0.24rem;
        background: #ffffff;
        border-radius: 0.16rem;
        box-sizing: border-box;
        padding: 0.32rem;
        .title-box {
          margin-bottom: 0.15rem;
          span {
            height: 0.32rem;
            font-size: 0.24rem;
            font-weight: bold;
            color: #000000;
            line-height: 0.32rem;
            margin-right: 0.12rem;
          }
        }
        .sub-item {
          background: #fafafa;
          border-radius: 0.08rem;
          border: 0.01rem solid #e9e9e9;
          display: flex;
          &:not(:last-child) {
            margin-bottom: 0.16rem;
          }
          & > div {
            height: 100%;
            &._left,
            &._right {
              .title-div {
                display: inline-block;
                height: 0.42rem;
                background: #ebf9fb;
                border-radius: 0.08rem;
                line-height: 0.42rem;
                text-align: center;
                padding: 0 0.15rem;
                font-size: 0.2rem;
                font-weight: bold;
                color: #23b5b1;
                margin-bottom: 0.17rem;
              }
            }
            &._left {
              border-right: 1px solid #e8e8e8;
              padding: 0.25rem 0.32rem;
              width: 5.02rem;
              height: auto;
              .reason {
                & > div {
                  display: flex;
                  align-items: flex-start;
                  &:not(:last-child) {
                    margin-bottom: 0.12rem;
                  }
                }
                span.index {
                  display: block;
                  width: 0.2rem;
                  height: 0.2rem;
                  font-size: 0.14rem;
                  font-family: PingFangSC-Semibold, PingFang SC;
                  font-weight: 600;
                  color: #ffffff;
                  line-height: 0.2rem;
                  background-color: #171726;
                  text-align: center;
                  margin-top: 0.03rem;
                  margin-right: 0.08rem;
                  border-radius: 50%;
                }
                .html {
                  flex: 1;
                  overflow: hidden;
                }
                .html > span {
                  display: block;
                  font-size: 0.2rem;
                  color: #171726;
                  line-height: 0.28rem;
                  font-weight: normal;
                  .num {
                    font-weight: bold;
                  }
                }
              }
            }
            &._right {
              display: flex;
              // align-items: center;
              ._l {
                margin: 0.25rem 0.32rem;
                width: 7.2rem;
                border-right: 1px solid #e8e8e8;
                padding-right: 0.32rem;
                .responsible,
                .measures {
                  display: flex;
                  line-height: 0.28rem;
                  font-size: 0.2rem;
                  color: #171726;
                  ._t {
                    font-weight: bold;
                  }
                  & > div:not(._t) {
                    flex: 1;
                  }
                }
                .responsible {
                  margin-bottom: 0.12rem;
                  .list {
                    div {
                      display: flex;
                      align-items: center;
                      &:not(:last-child) {
                        margin-bottom: 0.12rem;
                      }
                      img {
                        display: block;
                        width: 0.2rem;
                        height: 0.2rem;
                        margin-right: 0.16rem;
                        cursor: pointer;
                      }
                    }
                  }
                }
              }
              ._r {
                margin: 0.25rem 0;
                width: 4.48rem;
                ._t {
                  height: 0.28rem;
                  font-size: 0.2rem;
                  font-weight: bold;
                  color: #171726;
                  line-height: 0.28rem;
                }
                ._chart {
                  margin-top: 0.2rem;
                  width: 100%;
                  height: 1.5rem;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
