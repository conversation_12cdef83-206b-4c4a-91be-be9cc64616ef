/*!
 * 
 * SpreadJS Wrapper Components for Vue 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/licensing/grapecity/
 * 
 */
!function a(b,c){"object"==typeof exports&&"object"==typeof module?module.exports=c():"function"==typeof define&&define.amd?define([],c):"object"==typeof exports?exports.SpreadSheetsComponents=c():b.SpreadSheetsComponents=c()}(this,function(){return function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={exports:{},id:d,loaded:!1};return a[d].call(e.exports,e,e.exports,c),e.loaded=!0,e.exports}return c.m=a,c.c=b,c.p="",c(0)}([function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),b.GCVUE=void 0,d=c(1),e=f(d);function f(a){return a&&a.__esModule?a:{default:a}}b.GCVUE=e.default},function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=c(2),s=v(r),t=c(3),u=v(t);function v(a){return a&&a.__esModule?a:{default:a}}d="gc-spread-sheets",e="gc-worksheet",f="gc-column",g="gc-vue-sheet",h="gc-vue-column",i=200,j=20,k=new s.default,l=["hostStyle","hostClass","name","allowUserZoom","allowUserResize","allowUserDragMerge","allowUndo","allowSheetReorder","allowContextMenu","allowUserDeselect","allowUserEditFormula","autoFitType","allowUserDragFill","allowUserDragDrop","allowCopyPasteExcelStyle","allowExtendPasteRange","tabStripVisible","tabEditable","tabStripRatio","tabNavigationVisible","newTabVisible","highlightInvalidData","referenceStyle","resizeZeroIndicator","backColor","cutCopyIndicatorVisible","cutCopyIndicatorBorderColor","copyPasteHeaderOptions","grayAreaBackColor","backgroundImage","backgroundImageLayout","defaultDragFillType","enableFormulaTextbox","hideSelection","showVerticalScrollbar","showHorizontalScrollbar","showDragFillSmartTag","scrollbarShowMax","scrollbarMaxAlign","showScrollTip","showResizeTip","showDragDropTip","showDragFillTip","scrollIgnoreHidden","useTouchLayout"],m=u.default.createWatchData(l,"setSpreadOptions"),n=["name","frozenColumnCount","frozenRowCount","frozenTrailingColumnCount","frozenTrailingRowCount","allowCellOverflow","frozenlineColor","sheetTabColor","selectionPolicy","selectionUnit","zoom","currentTheme","clipBoardOptions","rowHeaderVisible","columnHeaderVisible","rowHeaderAutoText","columnHeaderAutoText","rowHeaderAutoTextIndex","columnHeaderAutoTextIndex","isProtected","showRowOutline","showColumnOutline","selectionBackColor","selectionBorderColor","defaultStyle","rowOutlineInfo","columnOutlineInfo","autoGenerateColumns"],o=u.default.createWatchData(n,"setSheetOptions"),p=["dataField","headerText","width","visible","resizable","autoFit","columnStyle","headerStyle","cellType","formatter"],q=u.default.createWatchData(p,"setColumnOptions"),s.default.component(d,{render:function a(b){return b("div",{class:this.hostClass,style:this.hostStyle,slot:e},[this.$slots.default])},props:l,data:function a(){return{spread:null,sheetCount:0,isInit:!1}},computed:{},mounted:function a(){var b,c=this.$el,d=this.getSheetTagCount(),e=void 0;e=d>0?0:1,b=u.default.createWorkBook(c,e),this.spread=b,this.initSpread(),this.bindSpreadEvent(b)},methods:{getSheetTagCount:function a(){return this.$el.getElementsByClassName(g).length},bindSpreadEvent:function a(b){var c=this,d=["ValidationError","CellClick","CellDoubleClick","EnterCell","LeaveCell","ValueChanged","TopRowChanged","LeftColumnChanged","InvalidOperation","RangeFiltering","RangeFiltered","TableFiltering","TableFiltered","RangeSorting","RangeSorted","ClipboardChanging","ClipboardChanged","ClipboardPasting","ClipboardPasted","ColumnWidthChanging","ColumnWidthChanged","RowHeightChanging","RowHeightChanged","DragDropBlock","DragDropBlockCompleted","DragFillBlock","DragFillBlockCompleted","EditStarting","EditChange","EditEnding","EditEnd","EditEnded","RangeGroupStateChanging","RangeGroupStateChanged","SelectionChanging","SelectionChanged","SheetTabClick","SheetTabDoubleClick","SheetNameChanging","SheetNameChanged","UserZooming","UserFormulaEntered","CellChanged","ColumnChanged","RowChanged","ActiveSheetChanging","ActiveSheetChanged","SparklineChanged","RangeChanged","ButtonClicked","EditorStatusChanged","FloatingObjectChanged","FloatingObjectSelectionChanged","PictureChanged","FloatingObjectRemoving","FloatingObjectRemoved","PictureSelectionChanged","FloatingObjectLoaded","TouchToolStripOpening","CommentChanged","CommentRemoving","CommentRemoved","SlicerChanged"];d.forEach(function(a){u.default.bindEvent(b,a,function(){var b=arguments,d=a.charAt(0).toLocaleLowerCase()+a.substr(1);c.$emit(d,b[0],b[1])})})},getSheetCount:function a(){var b,c=0,d=this.$children.length;for(b=0;b<d;b++)this.$children[b].$options.name===e&&c++;return c},setSpreadOptions:function a(b,c){var d=this.spread;switch(b){case"name":d.name=c;break;case"allowUserZoom":case"allowUserResize":case"tabStripVisible":case"tabEditable":case"tabStripRatio":case"tabNavigationVisible":case"newTabVisible":case"allowUserEditFormula":case"allowUserDragFill":case"allowUserDragDrop":case"allowUserDragMerge":case"allowUndo":case"allowSheetReorder":case"allowContextMenu":case"allowUserDeselect":case"allowCopyPasteExcelStyle":case"allowExtendPasteRange":case"cutCopyIndicatorVisible":case"enableFormulaTextbox":case"highlightInvalidData":case"hideSelection":case"showVerticalScrollbar":case"showHorizontalScrollbar":case"showDragDropTip":case"showDragFillTip":case"showDragFillSmartTag":case"scrollbarShowMax":case"scrollbarMaxAlign":case"scrollIgnoreHidden":case"useTouchLayout":u.default.setSpreadOptions(d,b,!!c);break;case"autoFitType":case"referenceStyle":case"resizeZeroIndicator":case"backColor":case"cutCopyIndicatorBorderColor":case"copyPasteHeaderOptions":case"grayAreaBackColor":case"backgroundImage":case"backgroundImageLayout":case"defaultDragFillType":case"showScrollTip":case"showResizeTip":u.default.setSpreadOptions(d,b,c)}},initSpread:function a(){var b,c=l.length;for(b=0;b<c;b++)void 0!==this[l[b]]&&this.setSpreadOptions(l[b],this[l[b]]);this.$nextTick(function(){this.isInit||(this.$emit("workbookInitialized",this.spread),this.isInit=!0)})}},destroyed:function a(){u.default.destroyedSpread(this.spread)},watch:u.default.extendObejct({},m)}),s.default.component(e,{render:function a(b){return b("div",{class:g},[this.$slots.default])},props:n.concat(["dataSource","rowCount","colCount"]),data:function a(){return{sheet:null,sheetName:void 0}},computed:{spread:function a(){return this.$parent.spread}},methods:{getColCount:function a(){var b=this.$children.length,c=this.colCount,d=void 0;return b>0&&void 0!==c?d=this.colCount-b:b>0&&void 0===c?d=b:0===b&&void 0===c?d=j:0===b&&(d=this.colCount),d},initSheet:function a(){if(this.spread){var b=this.getSheetIndex();this.sheet=u.default.addSheet(this.spread,this.name,b),this.sheetName=u.default.getSheetName(this.sheet),this.initSheetOptions()}},getSheetIndex:function a(){var b,c=0,d=this.$parent.$el,e=this.$el,f=d.getElementsByClassName(g);for(b in f){if(f[b]===e)break;c++}return c},initSheetOptions:function a(){var b,c=n.length,d=void 0;for(b=0;b<c;b++)d=n[b],void 0!==this[d]&&this.setSheetOptions(d,this[d]);this.reSizeSheet(),this.$nextTick(function(){this.setSheetOptionsAfterSheetInit()})},setSheetOptionsAfterSheetInit:function a(){this.rowOutlineInfo&&u.default.setRowOutlineInfo(this.sheet,this.rowOutlineInfo),this.columnOutlineInfo&&u.default.setColumnOutlineInfo(this.sheet,this.columnOutlineInfo),this.frozenColumnCount&&u.default.setSheetAttribute(this.sheet,"frozenColumnCount",parseInt(this.frozenColumnCount,10))},setSheetOptions:function a(b,c){var d=this.sheet;switch(b){case"name":u.default.setSheetAttribute(d,b,c),this.sheetName=c;break;case"frozenColumnCount":case"frozenRowCount":case"frozenTrailingColumnCount":case"frozenTrailingRowCount":u.default.setSheetAttribute(d,b,parseInt(c,10));break;case"selectionPolicy":case"selectionUnit":case"zoom":case"currentTheme":case"showRowOutline":case"showColumnOutline":u.default.setSheetAttribute(d,b,c);break;case"autoGenerateColumns":u.default.setSheetAttributeData(d,b,c);break;case"isProtected":case"allowCellOverflow":case"frozenlineColor":case"sheetTabColor":case"clipBoardOptions":case"rowHeaderVisible":case"selectionBackColor":case"selectionBorderColor":case"rowHeaderAutoText":case"rowHeaderAutoTextIndex":u.default.setSheetOptions(d,b,c);break;case"columnHeaderAutoTextIndex":u.default.setSheetOptions(d,"colHeaderAutoTextIndex",c);break;case"columnHeaderVisible":u.default.setSheetOptions(d,"colHeaderVisible",c);break;case"columnHeaderAutoText":u.default.setSheetOptions(d,"colHeaderAutoText",c);break;case"defaultStyle":u.default.setSheetDefaultStyle(d,c);break;case"rowOutlineInfo":u.default.setRowOutlineInfo(d,c);break;case"columnOutlineInfo":u.default.setColumnOutlineInfo(d,c)}},reSizeSheet:function a(){var b="",c=this.dataSource,d=this.$children.length,e=this.colCount;switch(b+=null===c||void 0===c?"0":"1",b+=d>0?"1":"0",b+=e>0?"1":"0",u.default.setDataSource(this.sheet,null,!0),b){case"000":case"001":u.default.setColCount(this.sheet,this.colCount||j),u.default.setRowCount(this.sheet,this.rowCount||i);break;case"010":case"011":u.default.setColCount(this.sheet,0),u.default.setRowCount(this.sheet,this.rowCount||i);break;case"100":case"101":u.default.setDataSource(this.sheet,c,!0);break;case"110":case"111":u.default.setDataSource(this.sheet,c,!1),u.default.setColCount(this.sheet,0)}},dataSourceChangeHandle:function a(b,c){var d=this.$children.length;c&&!b?(u.default.setDataSource(this.sheet,null,!0),u.default.setRowCount(this.sheet,this.rowCount||i),u.default.setColCount(this.sheet,d||this.colCount||j)):c?u.default.setDataSource(this.sheet,b,!0):b&&(u.default.setDataSource(this.sheet,b,!0),d&&u.default.setColCount(this.sheet,d)),k.$emit("gc-sheet:dataSourceChanged",this.sheet.name()),this.frozenColumnCount&&u.default.setSheetAttribute(this.sheet,"frozenColumnCount",parseInt(this.frozenColumnCount,10))},rowCountChangeHandle:function a(){this.dataSource||u.default.setRowCount(this.sheet,this.rowCount||i)},colCountChangeHandle:function a(){var b=this.$children.length;this.dataSource||u.default.setColCount(this.sheet,b||this.colCount||j)}},mounted:function a(){var b=this;this.initSheet(),k.$on("gc-column:mounted",function(a,c){a===b.sheet&&0===c&&u.default.setColCount(b.sheet,1)}),k.$on("gc-column:destroyed",function(a,c){if(a===b.sheet){var d=b.$children.length;0===d&&(b.dataSource?(u.default.setDataSource(b.sheet,null,!0),u.default.setDataSource(b.sheet,b.dataSource,!0)):u.default.setColCount(b.sheet,b.colCount||j))}})},destroyed:function a(){this.spread.removeSheet(this.spread.getSheetIndex(this.sheet.name()))},watch:u.default.extendObejct({dataSource:function a(b,c){this.dataSourceChangeHandle(b,c)},colCount:function a(b,c){this.colCountChangeHandle(b,c)},rowCount:function a(){this.rowCountChangeHandle()},spread:function a(){this.initSheet()}},o)}),s.default.component(f,{render:function a(b){return b("div",{class:h})},props:p,data:function a(){return{columnIndex:void 0}},computed:{sheet:function a(){return this.$parent.sheet}},methods:{getColumnIndex:function a(){var b,c=0,d=this.$parent.$el,e=this.$el,f=d.getElementsByClassName(h);for(b=0;b<f.length;b++)f[b]===e&&(c=b);return c},setColumnOptions:function a(b,c){var d=this.sheet,e=this.getColumnIndex();switch(u.default.suspendPaint(d),u.default.suspendEvent(d),b){case"dataField":u.default.bindColumn(d,e,{name:c,displayName:this.headerText}),void 0!==this.width&&u.default.setColumnWidth(d,e,this.width),void 0!==this.visible&&u.default.setColumnVisible(d,e,this.visible),void 0!==this.resizable&&u.default.setColumnResizable(d,e,this.resizable);break;case"headerText":c&&u.default.bindColumn(d,e,{name:this.dataField,displayName:c}),void 0!==this.width&&u.default.setColumnWidth(d,e,this.width),void 0!==this.visible&&u.default.setColumnVisible(d,e,this.visible),void 0!==this.resizable&&u.default.setColumnResizable(d,e,this.resizable);break;case"width":u.default.setColumnWidth(d,e,c);break;case"visible":u.default.setColumnVisible(d,e,c);break;case"resizable":u.default.setColumnResizable(d,e,c);break;case"autoFit":c&&u.default.setAutoFitColumn(d,e);break;case"columnStyle":u.default.setColumnStyle(d,e,c);break;case"headerStyle":u.default.setHeaderStyle(d,e,c);break;case"cellType":u.default.setCellType(d,e,c);break;case"formatter":u.default.setFormatter(d,e,c)}u.default.resumeEvent(d),u.default.resumePaint(d)},initColumn:function a(){var b,c,d,e=this.getColumnIndex();for(this.columnIndex=e,u.default.addColumn(this.sheet,e),b=p.length,d=0;d<b;d++)c=p[d],void 0!==this[c]&&this.setColumnOptions(c,this[c])}},created:function a(){var b=this;k.$on("gc-sheet:dataSourceChanged",function(a){if(b.sheet&&(!b.sheet||b.sheet.name()===a)){var c=b.sheet,d=b.getColumnIndex();(b.headerText||b.dataField)&&u.default.bindColumn(b.sheet,b.getColumnIndex(),{name:b.dataField,displayName:b.headerText}),void 0!==b.width&&u.default.setColumnWidth(c,d,b.width),void 0!==b.visible&&u.default.setColumnVisible(c,d,b.visible),void 0!==b.resizable&&u.default.setColumnResizable(c,d,b.resizable)}}),k.$on("gc-column:mounted",function(a){!b.sheet||b.sheet&&b.sheet!==a||(b.columnIndex=b.getColumnIndex())})},beforeMount:function a(){var b,c=this.$parent.$el;c&&(b=c.getElementsByClassName(h),this.beforeMountCloumnTagCount=b.length)},mounted:function a(){this.sheet&&(this.initColumn(),k.$emit("gc-column:mounted",this.sheet,this.beforeMountCloumnTagCount))},destroyed:function a(){var b=this.columnIndex;u.default.deleteColumn(this.sheet,b),k.$emit("gc-column:destroyed",this.sheet,b)},watch:u.default.extendObejct({sheet:function a(){this.sheet&&this.initColumn()}},q)})},function(a,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var c=window.Vue;b.default=c},function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d=c(4),e=f(d);function f(a){return a&&a.__esModule?a:{default:a}}b.default={extendObejct:function a(b,c){for(var d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);return b},createWatchData:function a(b,c){var d,e={},f=b.length;for(d=0;d<f;d++)e[b[d]]=function(a){return function(b){this[c](a,b)}}(b[d]);return e},setSpreadOptions:function a(b,c,d){b.suspendPaint(),b.options[c]=d,b.resumePaint()},createWorkBook:function a(b,c){return new e.default.Spread.Sheets.Workbook(b,{sheetCount:c})},destroyedSpread:function a(b){b.destroy()},bindEvent:function a(b,c,d){var f=e.default.Spread.Sheets.Events;b.bind(f[c],d)},setSheetAttribute:function a(b,c,d){return b[c](d)},setSheetAttributeData:function a(b,c,d){return b[c]=d},setSheetOptions:function a(b,c,d){b.suspendPaint(),b.options[c]=d,b.resumePaint()},setRowCount:function a(b,c){b.suspendPaint(),b.setRowCount(c),b.resumePaint()},setFrozenColumnCount:function a(b,c){b.frozenColumnCount(c)},suspendPaint:function a(b){b.suspendPaint()},resumePaint:function a(b){b.resumePaint()},suspendEvent:function a(b){b.suspendEvent()},resumeEvent:function a(b){b.resumeEvent()},createSheet:function a(b){return new e.default.Spread.Sheets.Worksheet(b)},addSheet:function a(b,c,d){var e=this.createSheet(c);return void 0===d&&(d=b.getSheetCount()),b.addSheet(d,e),e},getSheetName:function a(b){return b.name()},setSheetDefaultStyle:function a(b,c){b.suspendPaint(),b.setDefaultStyle(c),b.resumePaint()},setRowOutlineInfo:function a(b,c){b.suspendPaint(),c.forEach(function(a){b.rowOutlines.group(a.index,a.count)}),b.resumePaint()},setColumnOutlineInfo:function a(b,c){b.suspendPaint(),c.forEach(function(a){b.columnOutlines.group(a.index,a.count)}),b.resumePaint()},setDataSource:function a(b,c,d){b.suspendPaint(),b.setDataSource(c,d),b.resumePaint()},setColCount:function a(b,c){b.suspendPaint(),b.setColumnCount(c),b.resumePaint()},addColumn:function a(b,c){b.suspendPaint(),b.addColumns(c,1),b.resumePaint()},deleteColumn:function a(b,c){b.suspendPaint(),b.deleteColumns(c,1),b.resumePaint()},bindColumn:function a(b,c,d){b.suspendPaint(),b.bindColumn(c,d),b.resumePaint()},setColumnWidth:function a(b,c,d){b.suspendPaint(),d=parseInt(d,10),b.setColumnWidth(c,d),b.resumePaint()},setColumnVisible:function a(b,c,d){b.suspendPaint(),b.setColumnVisible(c,d),b.resumePaint()},setColumnResizable:function a(b,c,d){b.suspendPaint(),b.setColumnResizable(c,d),b.resumePaint()},setAutoFitColumn:function a(b,c){b.autoFitColumn(c)},setColumnStyle:function a(b,c,d){b.suspendPaint(),b.setStyle(-1,c,d),b.resumePaint()},setHeaderStyle:function a(b,c,d){b.suspendPaint(),b.setStyle(-1,c,d,e.default.Spread.Sheets.SheetArea.colHeader),b.resumePaint()},setCellType:function a(b,c,d){b.suspendPaint(),b.setCellType(-1,c,d),b.resumePaint()},setFormatter:function a(b,c,d){b.suspendPaint(),b.setFormatter(-1,c,d,e.default.Spread.Sheets.SheetArea.viewport),b.resumePaint()}}},function(a,b){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var c=window.GC;b.default=c}])});