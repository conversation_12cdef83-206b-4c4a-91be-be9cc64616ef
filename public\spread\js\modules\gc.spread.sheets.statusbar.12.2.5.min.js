/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.StatusBar=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/statusBar/statusBar.entry.js")}({"./dist/plugins/statusBar/baseStatusBar.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("./dist/plugins/statusBar/statusItem.js"),f=d.GC$,g="statusItem",h=null,i="left",j="right",k=function(){function a(a){this.yzb=[],this.zzb=[],this.Azb=[],this.mi=0,this.qo=h,this.ET=a,this.Bzb()}return a.prototype.Bzb=function(){var a=this.qo,b=this.Czb;a||(a=this.qo=document.createElement("div"),b=this.Czb=document.createElement("div")),this.ET.appendChild(a),this.ET.appendChild(b)},a.prototype.Dzb=function(a){a.name||(a.name=g+this.mi,this.mi++)},a.prototype.mL=function(a,b){return a instanceof e.StatusItem&&!this.Jp(a.name)&&(this.Dzb(a),b>=0&&b<this.yzb.length?this.yzb.splice(b,0,a):this.yzb.push(a),a.align=a.align||i,a.Ezb(this.ET),!0)},a.prototype.Fzb=function(a,b){var c,d,e,f,g,i=!1,k=this.yzb,l=b>=0&&b<k.length,m=h,n=h;if(l&&(m=k[b],n=m.Gzb()),this.mL(a,b)){c=a.align===j;try{c?(d=this.Azb,e=d.indexOf(m),f=d.length,g=f-1,n&&e>=0&&e<g?n=d[e+1].Gzb():n===h?n=d[0].Gzb():(n&&e<0||e===g)&&(n=h),this.Azb.push(a)):this.zzb.push(a),this.qo.insertBefore(a.Gzb(),n),this.xc&&a.onBind(this.xc),i=!0}catch(b){c?this.Azb.splice(this.Azb.indexOf(a),1):this.zzb.splice(this.zzb.indexOf(a),1),this.yzb.splice(this.yzb.indexOf(a),1)}}return i},a.prototype.Hzb=function(a){var b=this.yzb,c=!1,d,e;for(d=0;d<b.length;d++)if(e=b[d],a===e.name){c=!0;break}return!!c&&(e.onUnbind(),e.align===j?this.Azb.splice(this.Azb.indexOf(e),1):this.zzb.splice(this.zzb.indexOf(e),1),this.yzb.splice(d,1),this.qo.removeChild(e.Gzb()),!0)},a.prototype.Jp=function(a){var b=this.yzb,c,d;for(c=0;c<b.length;c++)if(d=b[c],a===d.name)return d;return h},a.prototype.Izb=function(a){this.xc=a,this.yzb.forEach(function(b){b.onBind(a),b.onUpdate()})},a.prototype.hZ=function(){this.zzb=[],this.Azb=[],f(this.qo).empty()},a.prototype.Jzb=function(){var a=this,b=this.qo,c=i,d;this.hZ(),this.yzb.forEach(function(e){c=e.align,d=e.Gzb(),d.style.float=c,c===j?a.Azb.unshift(e):(a.zzb.unshift(e),b.appendChild(d))}),this.Azb.forEach(function(a){b.appendChild(a.Gzb())})},a.prototype.Kzb=function(){this.yzb.forEach(function(a){a.onUpdate()}),this.Jzb()},a.prototype.Lzb=function(){var a=[];return this.yzb.forEach(function(b){a.push(b.Mzb())}),a},a.prototype.Nzb=function(a){var b=this.yzb,c=a.name,d,e;for(d=0;d<b.length;d++)if(e=b[d],e.name===c){e.Nzb(a);break}},a.prototype.Ozb=function(){return this.yzb},a.prototype.Pzb=function(){var a=this,b=a.yzb,c,d;for(this.xc=h,c=0;c<b.length;c++)d=b[c],d.onUnbind()},a.prototype.no=function(){var a=this,b=a.yzb,c,d;if(this.qo){for(c=0;c<b.length;c++)d=b[c],a.qo.removeChild(d.Qzb),d.onDispose();this.qo.parentElement&&this.qo.parentElement.removeChild(this.qo),this.qo=h}this.mi=h,this.yzb=h,this.zzb=h,this.Azb=h},a}(),b.BaseStatusBar=k},"./dist/plugins/statusBar/statusBar.entry.js":function(a,b,c){"use strict";var d,e;function f(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d=c("./dist/plugins/statusBar/statusBar.js"),b.StatusBar=d.StatusBar,e=c("./dist/plugins/statusBar/statusItem.js"),b.StatusItem=e.StatusItem,f(c("./dist/plugins/statusBar/statusBar.ns.js"))},"./dist/plugins/statusBar/statusBar.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("CalcEngine"),g=c("SheetsCalc"),h=c("ContextMenu"),i=c("./dist/plugins/statusBar/statusBar.ns.js"),j=c("./dist/plugins/statusBar/statusItem.js"),k=c("./dist/plugins/statusBar/baseStatusBar.js"),l=d.GC$,m=d.Ul,n=m.Nl,o=m.nl,p=e.Common.j,q=e.Common.u.Kb,r=p.Fa,s=d.Commands.ActionBase,t=f.Convert.vf,u=null,v=l.inherit,w="gc-statusbar-",x="slider-",y=w+x+"btn",z=w+x+"add-btn",A=w+x+"back-progress",B=w+x+"back",C=w+x+"middle-line",D=w+x+"drag-bar",E=w+"zoom-panel",F=w+x+"btn-container",G=w+x+"add-btn-container",H="gc-statusbar",I=w+"menu-host",J="gc.spread.",K="100",L="%",M="px",N="cellMode",O="average",P="count",Q="numericalCount",R="min",S="max",T="sum",U="zoomSlider",V="zoomPanel",W="div",X="span",Y="none",Z="inline",$="inline-block",_=15,aa=25,ba=400,ca=100,da=100,ea=10,fa=1,ga=4,ha=120,ia=.7,ja=8,ka=10,la="right",ma="statusbarZoom",na="statusBar",oa="click",pa="mousedown",qa="mouseup",ra="mousemove",sa="contextmenu",ta=".statusbar",ua="invalidateLayout",va=101,wa=103,xa=102,ya=105,za=104,Aa=109,Ba="(",Ca=")",Da="formatter",Ea="_autoFormatter",Fa="Subtotal",Ga=[O,P,Q,R,S,T],Ha=!!h,h&&(Ia=h.MenuView,Ja=h.Cyb),Ka=new e.Common.ResourceManager(i.SR),La=Ka.getResource.bind(Ka);function Ta(a){return a&&a.getActiveSheet()}function Ua(a,b){var c=e.Common.CultureManager.q4().NumberFormat.listSeparator;return Fa+Ba+a+c+b+Ca}function Va(a,b){a.splice(b,1)}function Wa(a,b){var c,d=b,e=b.length,f=[];if(e>1){for(f.push(b[0]),c=1;c<e;c++)f=Xa(a,b[c],f);return f}return d}function Xa(a,b,c){var d,e,f,g,h,i,j=a.getRowCount(3),k=a.getColumnCount(3),l=[],m=[];for(l.push(b),g=c.length,h=0;h<g;h++)for(d=c[h],i=0;i<l.length;)e=l[i],f=e.getIntersect(d,j,k),f?(m=a.v_a(e,f,j,k),Va(l,i),l=m.concat(l),i+=m.length):i++;return l.concat(c)}function Ya(a,b,c){var d=a.tq(b,c,Da);return d||(d=a.tq(b,c,Ea)),d}function Za(a){var b=a;return a>0&&a<=4&&(b=Math.floor(+parseFloat((100*a).toPrecision(12)))),b}function $a(a,b,c){a&&a.parent&&a.parent.options.allowUserZoom&&c!==b&&a.wu().execute({cmd:ma,sheetName:a.name(),newZoomFactor:b,oldZoomFactor:c})}function _a(a){for(var b,c,d,e={top:a.offsetTop,left:a.offsetLeft},f=a.offsetParent;f;)e.top+=f.offsetTop,e.left+=f.offsetLeft,f=f.offsetParent;return b=document.body,c=b.clientTop,d=b.clientLeft,isNaN(c)||(e.top+=c),isNaN(d)||(e.left+=d),e}function ab(a){return""!==a}Ma=function(a){Sa(b,a);function b(b,c){var d=a.call(this,b,c)||this;return d.Rzb=ta+d.name,d}return b.prototype.onBind=function(a){var b=this,c;b.xc=a,a.bind(d.Events.EditorStatusChanged+b.Rzb,function(a,d){c=b.Szb(d.newStatus),b.Tzb(c)}),a.bind(d.Events.ActiveSheetChanged+b.Rzb,function(){b.onUpdate()})},b.prototype.onUpdate=function(){var b,c,d;a.prototype.onUpdate.call(this),b=Ta(this.xc),b&&(c=b.editorStatus(),d=this.Szb(c),this.Tzb(d))},b.prototype.Szb=function(a){var b;switch(a){case 0:b=La().cellModeReady;break;case 1:b=La().cellModeEnter;break;case 2:b=La().cellModeEdit}return b},b.prototype.Tzb=function(a){if(a!==this.value){this.value=a,this.tipText=q(La().toolTipCellMode,[a]);var b=this.TJa;b&&(b.parentElement.title=this.tipText,r(this.value)||(b.innerText=this.value+""))}},b.prototype.onUnbind=function(){this.xc&&(this.xc.unbind(d.Events.EditorStatusChanged+this.Rzb),this.xc.unbind(d.Events.ActiveSheetChanged+this.Rzb),this.xc=u)},b}(j.StatusItem),Na=function(a){Sa(b,a);function b(b,c){var d=a.call(this,b,c)||this;return d.Uzb=": ",d.Uk=c.prefix,d.Rzb=ta+d.name,d}return b.prototype.onBind=function(a){var b=this;b.xc=a,a.bind(d.Events.SelectionChanging+b.Rzb,function(a,c){b.Vzb(c)}),a.bind(d.Events.SelectionChanged+b.Rzb,function(a,c){b.Vzb(c)}),a.bind(d.Events.ValueChanged+b.Rzb,function(a,c){var d=c.sheet,e=c.row,f=c.col;d.vp(e,f)&&b.onUpdate()}),a.bind(d.Events.RangeChanged+b.Rzb,function(){b.onUpdate()}),a.bind(d.Events.DragFillBlockCompleted+b.Rzb,function(){b.onUpdate()}),a.bind(d.Events.ActiveSheetChanged+b.Rzb,function(){b.onUpdate()})},b.prototype.Vzb=function(a){var b,c=this,d=a.sheet,e=a.newSelections,f=g.rangesToFormula(Wa(d,e));ab(f)?(b=c.Wzb(d,f),c.Tzb(b[c.name])):r(this.value)&&this.Xzb(!1)},b.prototype.Wzb=function(a,b){var c,d,e,f,h,i,j,k,l,m,n=g.evaluateFormula,o=0,p=u,q=u,r=u,s=u,v=u;return o=n(a,Ua(wa,b)),o>1&&(p=n(a,Ua(xa,b)),p>0&&(c=n(a,Ua(va,b)),d=n(a,Ua(ya,b)),e=n(a,Ua(za,b)),f=n(a,Ua(Aa,b)),t(c)||t(d)||t(e)||t(f)||(h=a.getActiveRowIndex(),i=a.getActiveColumnIndex(),j=a.getCellType(h,i),k=Ya(a,h,i),l={},r=j.format(c,k,l),s=j.format(d,k,l),v=j.format(e,k,l),q=j.format(f,k,l)))),m={},m.average=r,m.numericalCount=p>0?p:u,m.min=s,m.max=v,m.sum=q,m.count=o>1?o:u,m},b.prototype.Xzb=function(a){this.Qzb&&(this.Qzb.style.display=a?$:Y)},b.prototype.Tzb=function(a){var b=this.Uzb,c=this.TJa,d=this.Uk,e=this.visible,f=!1;this.value=a,c.innerText=d+b+a,!r(a)&&e&&(f=!0),this.Xzb(f)},b.prototype.Av=function(a){if(a){var b=a.menuContent,c=a.tipText;this.menuContent=b,this.Uk=b,this.tipText=c,this.Qzb.title=c}},b.prototype.onUpdate=function(){var b,c,d,e,f;a.prototype.onUpdate.call(this),b=Ta(this.xc),b&&(c=b.getSelections(),d=g.rangesToFormula(Wa(b,c)),ab(d)?(e=this.Wzb(b,d),f=e[this.name],(r(f)||f!==this.value)&&(this.value=f,this.Tzb(f))):r(this.value)&&this.Xzb(!1))},b.prototype.onUnbind=function(){this.xc&&(this.xc.unbind(d.Events.SelectionChanging+this.Rzb),this.xc.unbind(d.Events.SelectionChanged+this.Rzb),this.xc.unbind(d.Events.ValueChanged+this.Rzb),this.xc.unbind(d.Events.RangeChanged+this.Rzb),this.xc.unbind(d.Events.DragFillBlockCompleted+this.Rzb),this.xc.unbind(d.Events.ActiveSheetChanged+this.Rzb),this.xc=u)},b}(j.StatusItem),Oa=function(a){Sa(b,a);function b(b,c,d){var e=a.call(this,c,d)||this;return e.min=aa,e.max=ba,e.middle=ca,e.step=ea,e.Yzb=fa,e.Zzb=ga,e.$zb=ha,e._zb=Math.round((e.$zb+e.Yzb)/2),e.aAb=Math.round((e.$zb+e.Zzb)/2),e.bAb=!1,e.cAb=b,e.Rzb=ta+e.name,e}return b.prototype.onCreateItemView=function(a){var b,c,d,e,f,g,h,i,j=this,k=j._v,m=k+M;a.style.display=this.visible?Z:Y,b=this.dAb,b||(b=this.dAb=n(W)),l(b).addClass(F),b.style.height=m,b.style.lineHeight=m,c=this.eAb,c||(c=this.eAb=n(W)),c.title=La().toolTipZoomOut,l(c).addClass(y),c.style.height=m,c.addEventListener(oa,function(){j.fAb(!1)}),d=this.gAb,d||(d=this.gAb=n(W)),l(d).addClass(G),d.style.height=m,d.style.lineHeight=m,e=this.hAb,e||(e=this.hAb=n(W)),e.title=La().toolTipZoomIn,l(e).addClass(z),e.style.height=m,e.addEventListener(oa,function(){j.fAb(!0)}),f=this.iAb,f||(f=this.iAb=n(W)),l(f).addClass(A),f.style.marginTop=Math.floor(k/2+1)+M,f.addEventListener(oa,function(){return!1}),g=this.jAb,g||(g=this.jAb=n(W)),l(g).addClass(C),g.style.top=Math.floor((k-ja)/2+1)+M,g.style.left=j._zb+M,h=this.kAb,h||(h=this.kAb=n(W)),h.title=La().toolTipSlider,l(h).addClass(D),h.style.top=Math.floor((k-ka)/2+1)+M,h.style.right=j.aAb+M,h.addEventListener(pa,function(){j.bAb=!0}),document.addEventListener(qa,function(){j.bAb=!1}),document.addEventListener(ra,function(a){if(j.bAb){var b=a.pageX,c=_a(f).left,d=f.offsetWidth,e=j.Zzb,g=c+d+e-b;j.lAb(g)}}),i=this.mAb,i||(i=this.mAb=n(W)),i.title=La().toolTipSlider,l(i).addClass(B),i.style.height=m,i.style.lineHeight=m,i.addEventListener(oa,function(a){var b,c,d,e=a.target;e!==h&&(e===g?b=j.aAb:(c=a.offsetX,d=i.offsetWidth,b=d-c),j.lAb(b))}),b.appendChild(c),a.appendChild(b),i.appendChild(g),i.appendChild(f),i.appendChild(h),a.appendChild(i),d.appendChild(e),a.appendChild(d)},b.prototype.M4=function(){this.xc&&this.xc.focus()},b.prototype.onBind=function(a){bb(this,a)},b.prototype.onUpdate=function(){cb(this)},b.prototype.fAb=function(a){var b,c=this,d=this.value;d%10===0?d=a?d+this.step:d-this.step:(b=d/10,d=a?Math.ceil(b):Math.floor(b),d=10*d),d=d>this.max?this.max:d,d=d<this.min?this.min:d,c.nAb(d)},b.prototype.lAb=function(a){var b,c,d,e,f,g,h,i,j,k,l=this;a<0&&(a=0),a>l.$zb&&(a=l.$zb+l.Zzb),c=l.value/100,d=l.aAb,e=l.$zb,f=3*l.Zzb/2,g=l.min,h=l.middle,i=l.max,j=a<=d?(d-a)/d*(i-h)+h:(e-a+f)/d*(h-g)+g,j<=g+5?j=g:j>=i-5&&(j=i),b=j/100,k=Ta(this.xc),$a(k,b,c),l.M4()},b.prototype.nAb=function(a){if(a){a=Za(a);var b=Ta(this.xc);$a(b,a/100,this.value/100),this.M4()}},b.prototype.oAb=function(a){var b=this,c=b.min,d=b.middle,e=b.max,f=b.Zzb,g=b.aAb,h=b.$zb,i;i=a<=d?(a-c)/(d-c)*g-f/2:(a-d)/(e-d)*g+g-f/2,i=h-i,this.pAb(i)},b.prototype.pAb=function(a){var b,c=this,d=c.Zzb/2,e=c.Zzb;a<=e?a+=e:a<c.$zb&&(a+=d),b=c.kAb,b.style.right=a+M},b.prototype.qAb=function(a){a&&(a=Za(a)),a!==this.value&&(this.value=parseInt(a+"",10),this.oAb(this.value))},b.prototype.rAb=function(a){return a===this.iAb||a===this.jAb||a===this.kAb||a===this.mAb},b.prototype.sAb=function(a){var b=a.target;this.rAb(b)&&this.tAb(ia)},b.prototype.uAb=function(a){var b=a.target;this.rAb(b)&&this.tAb(1)},b.prototype.tAb=function(a){this.kAb.style.opacity=a+""},b.prototype.Av=function(a){if(a){var b=a.tipText;this.menuContent=a.menuContent,this.tipText=b,this.eAb.title=b[0],this.hAb.title=b[1],this.kAb.title=b[2],this.mAb.title=b[2]}},b.prototype.onUnbind=function(){db(this)},b.prototype.onDispose=function(){this.cAb=u,j.StatusItem.prototype.onDispose.call(this)},b}(j.StatusItem),Pa=function(a){Sa(b,a);function b(b,c){var d=a.call(this,b,c)||this;return d.Rzb=ta+d.name,d}return b.prototype.onCreateItemView=function(a){a.style.display=this.visible?Z:Y;var b=this.vAb;b||(b=this.vAb=n(X)),l(b).addClass(E),b.innerText=K+L,a.appendChild(b)},b.prototype.onBind=function(a){bb(this,a)},b.prototype.onUpdate=function(){cb(this)},b.prototype.qAb=function(a){var b;a&&(b=Za(a)),b!==parseInt(this.value+"",10)&&(b=parseInt(b,10),this.value=b+L,this.wAb(b))},b.prototype.wAb=function(a){var b=this.vAb;b&&(b.innerText=a+L)},b.prototype.onUnbind=function(){db(this)},b}(j.StatusItem);function bb(a,b){a.xc=b,b.bind(d.Events.UserZooming+a.Rzb,function(b,c){var d=c.newZoomFactor;a.qAb(d)}),b.bind(d.Events.ActiveSheetChanged+a.Rzb,function(){a.onUpdate()})}function cb(a){var b,c;j.StatusItem.prototype.onUpdate.call(a),b=Ta(a.xc),b&&(c=b.zoom(),a.qAb(c))}function db(a){a.xc&&(a.xc.unbind(d.Events.UserZooming+a.Rzb),a.xc.unbind(d.Events.ActiveSheetChanged+a.Rzb),a.xc=u)}Qa=function(){function a(a,b){if(a){var c=this.xAb=new k.BaseStatusBar(a);this.yAb(c,b),this.qo=c.qo,this.Czb=c.Czb,this.zAb={},this.AAb(a)}}return a.prototype.yAb=function(a,b){var c,d,e,f,g,h,i=["formulaAverage","formulaCount","formulaNumericalCount","formulaMin","formulaMax","formulaSum"],j=["toolTipFormulaAverage","toolTipFormulaCount","toolTipFormulaNumericalCount","toolTipFormulaMin","toolTipFormulaMax","toolTipFormulaSum"],k=[La().formulaAverage,La().formulaCount,La().formulaNumericalCount,La().formulaMin,La().formulaMax,La().formulaSum],l=[La().toolTipFormulaAverage,La().toolTipFormulaCount,La().toolTipFormulaNumericalCount,La().toolTipFormulaMin,La().toolTipFormulaMax,La().toolTipFormulaSum],m=[!0,!0,!1,!1,!1,!0],n=[];for(k.forEach(function(a,b){var c={};c.menuContent=a,c.prefix=a,c.align=la,c.tipText=l[b],c.visible=m[b],c.BAb=i[b],c.CAb=j[b],n.push(c)}),c={menuContent:La().cellMode,value:La().cellModeReady,tipText:q(La().toolTipCellMode,[La().cellModeReady]),BAb:"cellMode",CAb:"toolTipCellMode"},d={value:da,menuContent:La().zoomSlider,showStatusInContexMenu:!1,align:la,BAb:"zoomSlider",CAb:["toolTipZoomOut","toolTipZoomIn","toolTipSlider"]},e={value:da+L,tipText:La().toolTipZoomPanel,menuContent:La().zoom,align:la,BAb:"zoom",CAb:"toolTipZoomPanel"},f=[new Ma(N,c),new Na(Ga[0],n[0]),new Na(Ga[1],n[1]),new Na(Ga[2],n[2]),new Na(Ga[3],n[3]),new Na(Ga[4],n[4]),new Na(Ga[5],n[5]),new Oa(this,U,d),new Pa(V,e)],b&&b.items instanceof Array&&(f=f.concat(b.items)),h=0;h<f.length;h++)g=f[h],a.mL(g);a.Jzb()},a.prototype.AAb=function(a){var b,c=this.qo,d=this.Czb,e=a.clientHeight;c&&(l(c).addClass(H),l(d).addClass(I),b=e,e<_&&(a.style.minHeight=_+M,b=_),d.style.top="-"+b+M)},a.prototype.DAb=function(){var a=this.qo;a&&(this.EAb=this.FAb.bind(this),a.addEventListener(sa,this.EAb))},a.prototype.FAb=function(a){var b,c,d,e,f,g;o(a),b=this,c=b.zAb,d=b.xc,Ha&&d&&(b.Ela||(b.Ela=new Ja(b.Czb,l(b.xc.sv))),e=c.menuView,f=b.qo,g=_a(f).left,e.hostInfo={top:f.getBoundingClientRect().top,width:f.clientWidth,height:f.clientHeight},c.menuData=b.xAb.Lzb(),b.Ela.OT(e,c.menuData,a.clientX-g,a.offsetY,d))},a.prototype.GAb=function(a){if(Ha){var b=new eb;this.zAb.menuView=b,this.nla(a)}},a.prototype.nla=function(a){var b=this,c=a.commandManager(),d={canUndo:!1,execute:function(a,c){var d=c.commandOptions;b.xAb.Nzb(d)}};c.register(J+na,d)},a.prototype.bind=function(a){this.unbind(),a&&(this.xc=a,a.cAb=this,this.xAb.Izb(a),this.GAb(a),this.DAb())},a.prototype.unbind=function(){this.xAb&&this.xAb.Pzb(),this.xc&&(this.xc.cAb=u,this.xc=u),this.qo&&(this.qo.removeEventListener(sa,this.EAb),this.EAb=u),this.Ela&&(this.Ela.close(),this.Ela=u)},a.prototype.add=function(a,b){return this.xAb.Fzb(a,b)},a.prototype.remove=function(a){return this.xAb.Hzb(a)},a.prototype.get=function(a){return this.xAb.Jp(a)},a.prototype.all=function(){return this.xAb.Ozb()},a.prototype.update=function(){this.xAb.Kzb()},a.prototype.dispose=function(){this.unbind(),this.zAb=u,this.Ela=u,this.xAb&&(this.xAb.no(),this.xAb=u),this.qo&&(this.qo=u)},a}(),b.StatusBar=Qa,Ha&&v(eb,Ia);function eb(){Ha&&Ia.call(this)}eb.prototype.getCommandOptions=function(a){return a},Ra=function(a){Sa(b,a);function b(b,c){var d=a.call(this)||this;return d.kj=b,d.HAb=c,d}return b.prototype.canExecute=function(){return!0},b.prototype.execute=function(){var a,b=this,c=b.kj,e=b.HAb,f=e.newZoomFactor;if(this.canExecute()){c.ITa.startTransaction();try{c.zoom(f)}finally{a=d.Commands.bWa(c.name()),e[a]=c.ITa.endTransaction()}return!0}return!1},b.prototype.canUndo=function(){return!0},b.prototype.undo=function(){var a=this,b=a.kj,c=a.HAb,e=d.Commands.bWa(b.name()),f=c[e][0].value;return b.zoom(f),!0},b}(s),d.Commands[ma]={canUndo:!0,execute:function(a,b,c){return d.Commands.h4(a,Ra,b,c)}},d.Worksheet.$n(ma,{setHost:function(a){if(a){var b=this.wu();b[ma]||b.register(ma,d.Commands[ma])}},onLayoutChanged:function(a){var b=a.changeType,c,d;b===ua&&(c=this.parent,c&&!c.isPaintSuspended()&&(d=c.cAb,d&&d.update()))}}),d.Workbook.$n(na,{onCultureChanged:function(){var a=this.cAb,b=La(),c=[],d,e;a&&a.xAb&&a.xAb.Ozb().forEach(function(a){d=a.CAb,e=b[d],d instanceof Array&&(d.forEach(function(a){c.push(b[a])}),e=c),a.Av({menuContent:b[a.BAb],tipText:e})})}})},"./dist/plugins/statusBar/statusBar.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/statusBar/statusBar.res.en.js");b.SR={en:d}},"./dist/plugins/statusBar/statusBar.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.cellMode="Cell Mode",b.cellModeReady="Ready",b.cellModeEnter="Enter",b.cellModeEdit="Edit",b.formulaAverage="Average",b.formulaCount="Count",b.formulaNumericalCount="Numerical Count",b.formulaMin="Min",b.formulaMax="Max",b.formulaSum="Sum",b.zoomSlider="Zoom Slider",b.zoom="Zoom",b.toolTipCellMode="In {0} mode",b.toolTipFormulaAverage="Average of selected cells",b.toolTipFormulaCount="Number of selected cells that contain data",b.toolTipFormulaNumericalCount="Number of selected cells that contain numerical data",b.toolTipFormulaMin="Minimum value in selection",b.toolTipFormulaMax="Maximum value in selection",b.toolTipFormulaSum="Sum of selected cells",b.toolTipZoomOut="Zoom Out",b.toolTipZoomIn="Zoom In",b.toolTipSlider="Zoom",b.toolTipZoomPanel="Zoom level"},"./dist/plugins/statusBar/statusItem.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=d.Ul.Nl,g=d.GC$,h=e.Common.j,i=h.Fa,j="gc-statusbar-statusitem-container",k="div",l="span",m="none",n="inline-block",o="mousemove",p="mouseout",q=null,r="left",s="auto",t="gc.spread.",u="statusBar",v="10px",w="px",x=15;function z(a,b,c,d){return{name:a,menuContent:b,visible:c,status:d,command:t+u}}y=function(){function a(a,b){if(this._marginLeft=0,this._marginRight=0,this.Vo=s,this.Qzb=q,this.TJa=q,this.name=a,b){var c=b.align,d=b.menuContent,e=b.tipText,f=b.visible,g=b.showStatusInContexMenu,h=b.value,j=b.BAb,k=b.CAb;this.align=i(c)?r:c,this.menuContent=i(d)?q:d,this.tipText=i(e)?"":e,this.visible=!!i(f)||f,this.showStatusInContexMenu=!!i(g)||g,this.value=i(h)?q:h,this.BAb=i(j)?q:j,this.CAb=i(k)?q:k}}return a.prototype.Ezb=function(a){var b,c,d=this.align===r;this._marginLeft=d?v:0,this._marginRight=d?0:v,b=a.clientHeight,this._v=b>x?b:x,c=this.IAb(),this.Qzb=c,this.onCreateItemView(c),this.QQ(c)},a.prototype.IAb=function(){var a=this.Qzb,b=this._v&&this._v+w;return a||(a=this.Qzb=f(k),a.title=this.tipText,g(a).addClass(j),g(a).css({height:b,lineHeight:b,float:this.align,marginLeft:this._marginLeft+"",marginRight:this._marginRight+""}),this.visible===!1&&(a.style.display=m)),a},a.prototype.onCreateItemView=function(a){var b=this.TJa;b||(b=this.TJa=f(l),b.innerText=this.value+""),a.appendChild(b)},a.prototype.onBind=function(a){},a.prototype.QQ=function(a){a.addEventListener(o,this.sAb.bind(this)),a.addEventListener(p,this.uAb.bind(this))},a.prototype.Gzb=function(){return this.Qzb},a.prototype.sAb=function(a){},a.prototype.uAb=function(a){},a.prototype.Mzb=function(){var a=this.JAb();return a.type=u,a.workArea=u,a},a.prototype.Nzb=function(a){this.visible=!a.visible,this.onUpdate()},a.prototype.JAb=function(){var a=this.showStatusInContexMenu?this.KAb():null;return z(this.name,this.menuContent,this.visible,a)},a.prototype.KAb=function(){return this.value},a.prototype.D4=function(){this.Qzb&&(this.Qzb.style.display=this.visible?n:m)},a.prototype.Av=function(a){a&&(this.menuContent=a.menuContent,this.tipText=a.tipText,this.Qzb.title=this.tipText)},a.prototype.onUpdate=function(){this.D4()},a.prototype.onUnbind=function(){},a.prototype.onDispose=function(){this.onUnbind(),this.TJa=q,this.Qzb&&(this.Qzb.removeEventListener(o,this.sAb),this.Qzb.removeEventListener(p,this.uAb),g(this.Qzb).empty(),this.Qzb=q)},a}(),b.StatusItem=y},CalcEngine:function(a,b){a.exports=GC.Spread.CalcEngine},Common:function(a,b){a.exports=GC.Spread},ContextMenu:function(a,b){a.exports=GC.Spread.Sheets.ContextMenu},Core:function(a,b){a.exports=GC.Spread.Sheets},SheetsCalc:function(a,b){a.exports=GC.Spread.Sheets.CalcEngine}});