<template>
  <a-modal
    v-model="visible"
    :width="500"
    :destroyOnClose="true"
    :maskClosable="true"
    :footer="null"
    :title="null"
    :class="['detail-data-dialog', {'dark-theme': darkTheme}]"
  >
    <div class="modal-title">
      <div class="title-icon"></div>
      <div class="title-text">时段数据详情</div>
    </div>
    <div class="modal-content">
      <table class="detail-data-table">
        <thead>
          <tr>
            <th>时段</th>
            <th>线体</th>
            <th>班组</th>
            <th>计划产量</th>
            <th>实际产量</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ detailData.productHour }}</td>
            <td>{{ detailData.lineName || '-' }}</td>
            <td>{{ detailData.teamName || '-' }}</td>
            <td>{{ detailData.planQty }}</td>
            <td>{{ detailData.productYeild }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'DetailDataDialog',
  props: {
    darkTheme: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      detailData: {
        productHour: '',
        lineName: '',
        teamName: '',
        planQty: 0,
        productYeild: 0
      }
    };
  },
  methods: {
    /**
     * 显示对话框
     * @param {Object} data - 要显示的详细数据
     */
    show(data) {
      if (!data) return;

      this.detailData = {
        productHour: data.productHour || '',
        lineName: data.lineName || '-',
        teamName: data.teamName || '-',
        planQty: data.planQty || 0,
        productYeild: data.productYeild || 0
      };

      this.visible = true;

      // 通知父组件对话框已打开，用于隐藏tooltip
      this.$emit('dialog-opened');
    },

    /**
     * 关闭对话框
     */
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.detail-data-dialog {
  /* 基础样式 */

}

.modal-title {
  padding-left: 20px;
  height: 56px;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(270deg,
      rgba(0, 170, 166, 0) -22%,
      rgba(0, 170, 166, 0.6) 87%);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("~@/assets/images/workShop_standing_meeting/dialog_head_banner.png");
    z-index: 0;
  }

  .title-icon {
    width: 15px;
    height: 15px;
    background: url("~@/assets/images/workShop_standing_meeting/dialog_banner_title_icon.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 8px;
    position: relative;
    z-index: 1;
  }

  .title-text {
    font-size: 20px;
    font-weight: bold;
    line-height: normal;
    letter-spacing: normal;
    color: #1d2129;
    position: relative;
    z-index: 1;
  }
}

.modal-content {
  padding: 20px;
}

.detail-data-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
}

.detail-data-table th,
.detail-data-table td {
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
}

.detail-data-table th {
  background-color: #DDF3F3;
  font-weight: 500;
  color: #333333;
}

.detail-data-table tr {
  background-color: #F0FAFA;
  /* 确保这个样式可以被暗色主题覆盖 */
}

/* 暗色主题样式 - 使用更精确的选择器 */
.workshop-meeting.dark-theme .detail-data-dialog .ant-modal-content,
body.has-dark-theme .detail-data-dialog .ant-modal-content,
.detail-data-dialog.dark-theme .ant-modal-content {
  background-color: #001d2c !important;
  color: #ffffff !important;
}

.workshop-meeting.dark-theme .modal-title .title-text,
body.has-dark-theme .modal-title .title-text,
.detail-data-dialog.dark-theme .modal-title .title-text {
  color: #ffffff !important;
}

.workshop-meeting.dark-theme .detail-data-dialog .ant-modal-close,
body.has-dark-theme .detail-data-dialog .ant-modal-close,
.detail-data-dialog.dark-theme .ant-modal-close {
  color: #ffffff !important;
}

.workshop-meeting.dark-theme .detail-data-table th,
body.has-dark-theme .detail-data-table th,
.detail-data-dialog.dark-theme .detail-data-table th {
  background-color: #015d52 !important;
  color: #ffffff !important;
  border-color: #00fbfb !important;
}

.workshop-meeting.dark-theme .detail-data-table td,
body.has-dark-theme .detail-data-table td,
.detail-data-dialog.dark-theme .detail-data-table td {
  background-color: transparent !important;
  color: #ffffff !important;
  border-color: #00fbfb !important;
}

/* 删除这些重复的样式规则，因为下面已经有更好的实现 */
:deep(.ant-modal-body){
  padding:0;
}

/* 直接覆盖带有scoped属性选择器的样式 */
/* 使用更高权重的选择器，不依赖于特定的data-v属性 */
body.has-dark-theme .detail-data-table tr,
.detail-data-dialog.dark-theme .detail-data-table tr {
  background-color: #001d2c !important;
}

body.has-dark-theme .detail-data-table tbody tr:nth-child(even),
.detail-data-dialog.dark-theme .detail-data-table tbody tr:nth-child(even) {
  background-color: #00464c !important;
}

body.has-dark-theme .detail-data-table tr:hover,
.detail-data-dialog.dark-theme .detail-data-table tr:hover {
  background-color: #008590 !important;
}

/* 增加表头样式 */
body.has-dark-theme .detail-data-table th,
.detail-data-dialog.dark-theme .detail-data-table th {
  background-color: #015d52 !important;
  color: #ffffff !important;
  border-color: #00fbfb !important;
}

/* 增加表格单元格样式 */
body.has-dark-theme .detail-data-table td,
.detail-data-dialog.dark-theme .detail-data-table td {
  color: #ffffff !important;
  border-color: #00fbfb !important;
}
</style>
