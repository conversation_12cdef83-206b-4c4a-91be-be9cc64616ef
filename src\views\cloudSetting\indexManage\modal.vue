<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-11-10 15:47:51
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="ruleForm"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <!-- 卡片标题 -->
      <!-- <a-form-model-item label="卡片标题" prop="title">
        <a-input v-model="form.title" />
      </a-form-model-item> -->
      <!-- 选择指标 -->
      <a-form-model-item label="选择指标" prop="indexId">
        <a-tree-select
          :disabled="isEdit"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          dropdownClassName="indexTreeSelect"
          v-model="form.indexId"
          :tree-data="treeData"
          :showSearch="true"
          placeholder="请选择"
          :replaceFields="replaceFields"
          @change="treeSelectChange"
          tree-default-expand-all
        />
      </a-form-model-item>
      <!-- 是否置顶 -->
      <a-form-model-item label="是否置顶" prop="topFlag">
        <a-radio-group
          v-model="form.topFlag"
          @change="form.topSort = ''"
          button-style="solid"
        >
          <a-radio-button value="1">
            是
          </a-radio-button>
          <a-radio-button value="0">
            否
          </a-radio-button>
        </a-radio-group>
      </a-form-model-item>
      <!-- 置顶排序 -->
      <a-form-model-item
        v-if="form.topFlag === '1'"
        label="置顶排序"
        prop="topSort"
      >
        <a-input style="flex: 1; margin-left: 10px;" v-model="form.topSort" />
      </a-form-model-item>
      <!-- 详情链接 -->
      <a-button
        type="primary"
        style="margin-bottom: 20px;"
        @click="addNewTableLine"
        >新增报表链接</a-button
      >
      <a-table
        :pagination="false"
        :columns="columns"
        :data-source="reportSettingList"
      >
        <!-- 序号 -->
        <template slot="index" slot-scope="text, record, index">
          <span>{{ index + 1 }}</span>
        </template>
        <!-- 基地 -->
        <span slot="baseName" slot-scope="text, record">
          <a-select
            v-model="record.baseName"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
          >
            <a-select-option
              :value="item.key"
              v-for="item in baseList"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </span>
        <!-- 报表类型 -->
        <span slot="detailType" slot-scope="text, record">
          <a-select
            v-model="record.detailType"
            :dropdownMatchSelectWidth="false"
            style="width: 100%"
          >
            <a-select-option
              :value="item.key"
              v-for="item in reportTypeList"
              :key="item.key"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </span>
        <!-- 报表开发人员 -->
        <span slot="developer" slot-scope="text, record">
          <a-input v-model="record.developer" />
        </span>
        <!-- 报表链接 -->
        <span slot="detailUrl" slot-scope="text, record">
          <a-input v-model="record.detailUrl" />
        </span>
        <!-- 链接参数 -->
        <!-- <span slot="urlParams"></span> -->
        <!-- 操作栏 -->
        <span slot="action" slot-scope="text, record, index">
          <a-tooltip placement="top">
            <template slot="title">
              <span>删除</span>
            </template>
            <a-icon @click="delReportItem(index)" type="delete" />
          </a-tooltip>
        </span>
      </a-table>
    </a-form-model>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        // title: "",
        indexId: "",
        topFlag: "0",
        topSort: ""
      },
      replaceFields: {
        title: "nodeName",
        key: "nodeId",
        value: "nodeId",
        children: "childNodeList"
      },
      treeData: [], // 指标数据
      rules: {
        // title: [
        //   {
        //     required: true,
        //     message: "请输入卡片标题",
        //     trigger: "blur"
        //   }
        // ],
        indexId: [
          {
            required: true,
            message: "请选择指标",
            trigger: "change"
          }
        ]
      },
      isEdit: false, // 是否编辑状态
      reportSettingList: [], // 表格数据源
      baseList: [], // 基地列表
      reportTypeList: [
        // 报表类型列表
        {
          key: "finereport",
          value: "帆软Report"
        },
        {
          key: "finerebi",
          value: "帆软BI"
        },
        {
          key: "yonghong",
          value: "永洪"
        },
        {
          key: "yonghong-9",
          value: "永洪9"
        },
        {
          key: "tableau",
          value: "Tableau"
        },
        {
          key: "diy",
          value: "其他"
        }
      ],
      columns: [
        {
          title: "序号",
          key: "index",
          scopedSlots: { customRender: "index" },
          width: 80
        },
        {
          title: "基地",
          dataIndex: "baseName",
          key: "baseName",
          scopedSlots: { customRender: "baseName" },
          width: 120
        },
        {
          title: "报表类型",
          key: "detailType",
          dataIndex: "detailType",
          scopedSlots: { customRender: "detailType" },
          width: 200
        },
        {
          title: "报表开发人员",
          key: "developer",
          dataIndex: "developer",
          scopedSlots: { customRender: "developer" },
          width: 180
        },
        {
          title: "报表链接",
          key: "detailUrl",
          dataIndex: "detailUrl",
          scopedSlots: { customRender: "detailUrl" }
        },
        // {
        //   title: "链接参数",
        //   key: "urlParams",
        //   dataIndex: "urlParams",
        //   scopedSlots: { customRender: "urlParams" }
        // },
        {
          title: "操作",
          key: "action",
          scopedSlots: { customRender: "action" }
        }
      ] // 表格列
    };
  },
  methods: {
    dealTreeData(treeData) {
      treeData.forEach(i => {
        i.disabled = true;
        if (i?.childNodeList && i.childNodeList.length) {
          i.childNodeList.forEach(ai => {
            ai.disabled = true;
            if (ai?.childNodeList && ai.childNodeList.length) {
              ai.childNodeList.forEach(bi => {
                if (bi?.childNodeList && bi.childNodeList.length) {
                  bi.childNodeList = [];
                }
              });
            }
          });
        }
      });
    },
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      if (formValue) {
        const {
          id,
          // title,
          indexId,
          topFlag,
          topSort
        } = formValue;
        this.isEdit = true;
        this.form = {
          id,
          // title,
          indexId,
          topFlag,
          topSort
        };
        this.getAllIndexList(indexId);
        this.getCardReportList(formValue.id);
      }
      this.getCurrentRoleList();
    },
    close() {
      this.form = {
        // title: "",
        indexId: "",
        topFlag: "0",
        topSort: ""
      };
      this.reportSettingList = [];
      this.$refs.ruleForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 根据卡片ID获取当前卡片上绑定的报表信息
    getCardReportList(id) {
      request(
        `${adminUserUrlPrefix["lxp"]}/indexCardUrl/list?cardId=${id}`
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          this.reportSettingList = res;
        }
      });
    },
    // 指标树选择改变
    treeSelectChange(value) {
      if (value) {
        this.getAllIndexList(value);
      }
    },
    // 获取指标树列表
    getAllIndexList(indexId) {
      request(
        `${adminUserUrlPrefix["lxp"]}/indexCardInfo/getBaseByCardIndexId`,
        {
          method: "POST",
          body: {
            indexNodeId: indexId
          }
        }
      )
        .then(res => {
          if (Array.isArray(res) && res.length) {
            this.baseList = res.map(item => {
              return {
                key: item,
                value: item
              };
            });
          } else {
            this.baseList = [];
          }
        })
        .catch(() => {
          this.baseList = [];
        });
    },
    // 报表链接配置表格新增行
    addNewTableLine() {
      this.$refs.ruleForm.validateField("indexId", valid => {
        if (!valid) {
          this.reportSettingList.push({
            id: "",
            baseName: "",
            detailUrl: "",
            detailType: "",
            developer: "",
            urlParams: ""
          });
        }
      });
    },
    // 保存卡片信息
    saveCard() {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          const {
            // title,
            indexId,
            topFlag,
            topSort
          } = this.form;
          let postData = {
            info: {
              // title,
              topFlag,
              topSort,
              companyName: indexId.split("==")[0],
              plateName: indexId.split("==")[1],
              indexId,
              indexName: indexId.split("==")[2]
            },
            url: []
          };
          // 判断报表类型和报表配置列表
          let errorInfo = "";
          let errorBase = "";
          for (let i = 0; i < this.reportSettingList.length; i++) {
            const element = this.reportSettingList[i];
            if (
              !(
                element.baseName &&
                element.detailUrl &&
                element.detailType &&
                element.developer
              )
            ) {
              errorInfo = `请检查链接配置表格内第${i +
                1}行数据基地、报表类型、报表链接是否填写`;
              break;
            }
          }
          let chooseBaseList = [];
          this.reportSettingList.forEach(item =>
            chooseBaseList.push(item.baseName)
          );
          chooseBaseList = Array.from(new Set(chooseBaseList));
          if (chooseBaseList.length !== this.reportSettingList.length) {
            errorBase = "请勿在链接配置表格内选择重复基地进行链接配置！";
          }
          if (errorInfo || errorBase) {
            this.$message.error(errorInfo || errorBase);
            return;
          }
          // 组装postData url 的数据
          postData.url = this.reportSettingList;
          if (this.isEdit) {
            postData.info["id"] = this.form.id;
          }
          request(
            `${adminUserUrlPrefix["zcx"]}/indexCardInfo/${
              this.isEdit ? "update" : "save"
            }`,
            {
              method: this.isEdit ? "PUT" : "POST",
              body: postData
            }
          ).then(res => {
            if (res?.code === "5501") {
              return;
            }
            this.close();
            this.$emit("fetchData");
          });
        }
      });
    },
    // 删除表格内某一项
    delReportItem(index) {
      this.reportSettingList.splice(index, 1);
    },
    // 检查指标是否已经绑定卡片
    checkIndexIsBindCard(indexId) {
      return new Promise(resolve => {
        request(
          `${
            adminUserUrlPrefix["zcx"]
          }/indexCardInfo/checkCardInfo?companyName=${
            indexId.split("==")[0]
          }&indexName=${indexId.split("==")[2]}`
        )
          .then(res => {
            if (res === false) {
              resolve(true);
            } else {
              resolve(false);
            }
          })
          .catch(() => {
            resolve(false);
          });
      });
    },
    // 获取当前角色有的指标
    getCurrentRoleList() {
      request(
        `${adminUserUrlPrefix["lxp"]}/${
          this.isEdit
            ? "roleDetail/getRoleTreeListByRoleId?roleId="
            : "roleDetail/getRoleTreeListForCard"
        }`
      ).then(res => {
        if (Array.isArray(res) && res.length) {
          this.treeData = res;
          this.dealTreeData(this.treeData);
        }
      });
    }
  }
};
</script>
