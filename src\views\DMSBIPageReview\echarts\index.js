import initQualityChart from "./qualityChart";
import initDelivery<PERSON>hart from "./deliveryChart";
import initTime<PERSON>hart from "./timeChart";

// 导入图表数据更新相关方法
import {
  updateDeliveryChartData,
  updateFtyChartData,
  updateWeldingLeakChartData,
  updateOqcChartData,
  updateDowntimeLossChartData,
  updateStationLossChartData
} from "./chartUpdaters";

// 导入tooltip管理相关方法
import {
  initAutoTooltip,
  startAutoTooltip,
  stopAutoTooltip,
  pauseAutoTooltip,
  resumeAutoTooltip,
  resetAutoTooltip
} from "./tooltipManager";

// 导入图表工具函数
import {
  checkChartHasData,
  setupChartMouseEvents,
  cleanupChartMouseEvents,
  setupUserActivityListeners,
  removeUserActivityListeners,
  resetChartTooltip
} from "./chartUtils";

// 导入图表数据获取相关方法
import {
  fetchDailyProductPlanRate,
  fetchPassThroughRateData,
  fetchOqcSpotCheckData,
  fetchWeldingLeakData,
  fetchDowntimeLossData,
  fetchStationLossData
} from "./dataFetchers";

// 导出图表初始化方法
export {
  initQualityChart,
  initDeliveryChart,
  initTimeChart
};

// 导出图表数据更新方法
export {
  updateDeliveryChartData,
  updateFtyChartData,
  updateWeldingLeakChartData,
  updateOqcChartData,
  updateDowntimeLossChartData,
  updateStationLossChartData
};

// 导出tooltip管理方法
export {
  initAutoTooltip,
  startAutoTooltip,
  stopAutoTooltip,
  pauseAutoTooltip,
  resumeAutoTooltip,
  resetAutoTooltip
};

// 导出图表工具函数
export {
  checkChartHasData,
  setupChartMouseEvents,
  cleanupChartMouseEvents,
  setupUserActivityListeners,
  removeUserActivityListeners,
  resetChartTooltip
};

// 导出图表数据获取方法
export {
  fetchDailyProductPlanRate,
  fetchPassThroughRateData,
  fetchOqcSpotCheckData,
  fetchWeldingLeakData,
  fetchDowntimeLossData,
  fetchStationLossData
};

// 默认导出所有方法
export default {
  // 图表初始化
  initQualityChart,
  initDeliveryChart,
  initTimeChart,

  // 图表数据更新
  updateDeliveryChartData,
  updateFtyChartData,
  updateWeldingLeakChartData,
  updateOqcChartData,
  updateDowntimeLossChartData,
  updateStationLossChartData,

  // tooltip管理
  initAutoTooltip,
  startAutoTooltip,
  stopAutoTooltip,
  pauseAutoTooltip,
  resumeAutoTooltip,
  resetAutoTooltip,

  // 图表工具函数
  checkChartHasData,
  setupChartMouseEvents,
  cleanupChartMouseEvents,
  setupUserActivityListeners,
  removeUserActivityListeners,
  resetChartTooltip,

  // 图表数据获取
  fetchDailyProductPlanRate,
  fetchPassThroughRateData,
  fetchOqcSpotCheckData,
  fetchWeldingLeakData,
  fetchDowntimeLossData,
  fetchStationLossData
};
