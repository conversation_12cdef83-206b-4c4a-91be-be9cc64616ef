<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-08 15:14:39
-->
<template>
  <a-modal
    class="roleManageModal"
    v-model="visible"
    :destroyOnClose="true"
    :width="800"
    title="修改角色指标权限"
    @ok="handleOk"
    @cancel="close"
    :confirm-loading="confirmLoading"
  >
    <a-spin :spinning="loadTree">
      <div style="min-height: 500px;display: flex;align-items: flex-start;">
        <div style="height: 500px;overflow-y: auto;">
          <a-tree
            :tree-data="companyTreeData"
            :selected-keys="compantSelectedKeys"
            @select="companySelect"
          />
        </div>
        <a-divider type="vertical" style="height: 500px;margin: 0 30px;" />
        <div style="height: 500px;overflow-y: auto;flex: 1;">
          <a-tree
            checkable
            v-model="orgCheckedKeysData"
            :auto-expand-parent="false"
            :tree-data="orgTreeData"
            @check="onCheck"
          />
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import uniqBy from "lodash/uniqBy";
export default {
  data() {
    return {
      visible: false, // 打开关闭弹窗
      roleCode: "", // 角色ID
      confirmLoading: false, // 确定按钮Loading
      loadTree: false, // 加载树形组件loading
      compantSelectedKeys: [], // 公司选择
      companyTreeData: [], // 公司列表
      orgTreeData: [], // 公司下组织数据
      orgCheckedKeysData: [], // 公司下组织数据选中
      ppTreeDataList: [] // 平铺树数据
    };
  },
  created() {
    this.getCompanyTreeData();
  },
  methods: {
    // 获取公司列表
    getCompanyTreeData() {
      request(`/api/smc2/codeValue/getCompanyNoRole`).then(res => {
        if (Array.isArray(res) && res.length) {
          res.forEach(item => {
            this.companyTreeData.push({
              title: item.value,
              key: item.key,
              children: []
            });
          });
        }
      });
    },
    // 公司选择
    async companySelect(selectedKeys) {
      this.compantSelectedKeys = selectedKeys;
      this.orgCheckedKeysData = [];
      this.orgTreeData = [];
      this.ppTreeDataList = [];
      if (selectedKeys.length) {
        await this.getRoleListByCompany(selectedKeys[0]);
        this.getSelectOrgKeysByCompany(selectedKeys[0]);
      }
    },
    async show({ roleCode }) {
      this.visible = true;
      this.roleCode = roleCode;
    },
    close() {
      this.visible = false;
      this.roleCode = "";
      this.confirmLoading = false;
      this.treeData = [];
      this.compantSelectedKeys = [];
      this.orgTreeData = [];
      this.orgCheckedKeysData = [];
      this.ppTreeDataList = [];
    },
    async handleOk() {
      this.confirmLoading = true;
      await this.saveCurrentRoleList();
      this.close();
    },
    // 数组转树
    arrayToTree(items) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const id = item.key;
        const pid = item.parentKey || "x00001";

        if (!itemMap[id]) {
          itemMap[id] = {
            children: []
          };
        }

        itemMap[id] = {
          ...item,
          children: itemMap[id]["children"]
        };

        const treeItem = itemMap[id];
        if (pid === "x00001") {
          result.push(Object.freeze(treeItem));
        } else {
          if (!itemMap[pid]) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(Object.freeze(treeItem));
        }
      }
      return result;
    },

    // 按公司获取指标树列表
    getRoleListByCompany(signOrgId) {
      return new Promise(resolve => {
        this.loadTree = true;
        request(`/api/smc2/role/getOrgForRole1`, {
          method: "POST",
          body: {
            roleId: this.roleCode,
            signOrgId
          }
        }).then(res => {
          if (Array.isArray(res) && res.length) {
            console.time("orgInCompany");
            this.ppTreeDataList = uniqBy(
              [...this.ppTreeDataList, ...res.map(item => Object.freeze(item))],
              "key"
            );
            this.orgTreeData = Object.freeze(this.arrayToTree(res));
            console.timeEnd("orgInCompany");
          }
          resolve();
        });
      });
    },
    // 获取x公司底下的角色
    getSelectOrgKeysByCompany(signOrgId) {
      request(`/api/smc2/role/getRole1`, {
        method: "POST",
        body: {
          roleId: this.roleCode,
          signOrgId
        }
      }).then(res => {
        if (Array.isArray(res) && res.length) {
          console.time("roleInCompany");
          this.orgCheckedKeysData = Object.freeze(res);
          console.log("this.orgCheckedKeysData--->", this.orgCheckedKeysData);
          console.timeEnd("roleInCompany");
        }
        this.loadTree = false;
      });
    },
    // 保存当前角色选中的指标
    saveCurrentRoleList() {
      if (this.compantSelectedKeys.length === 0) {
        return;
      }
      let postData = [];
      const checkedNodes = this.ppTreeDataList.filter(item => {
        return (
          this.orgCheckedKeysData.includes(item.key) && item.fullCode !== null
        );
      });
      const companyChecked = this.ppTreeDataList.filter(
        item => this.orgCheckedKeysData.includes(item.key) && !item.parentKey
      );
      console.log("companyChecked---->", companyChecked);
      postData = checkedNodes.map(item => {
        return {
          signOrgId: item.key.split("-")[0],
          orgId: item.key.split("-")[3],
          businessSegmentsId: item.key.split("-")[1],
          indexId: item.key.split("-")[2],
          fullCode: item.fullCode,
          roleId: this.roleCode
        };
      });
      return new Promise(resolve => {
        request(`/api/smc2/role/insertRole`, {
          method: "POST",
          body: {
            roleId: this.roleCode,
            sign: this.compantSelectedKeys[0],
            listRole: postData,
            type: companyChecked.length > 0 ? "0" : "1" // 0全选1非全选
          }
        }).then(() => {
          resolve();
        });
      });
    },
    // 树形控件改变
    onCheck(checkedKeys) {
      console.log("checkedNodes---->", checkedKeys);
      console.log(checkedKeys.length);
      this.orgCheckedKeysData = checkedKeys;
    }
  }
};
</script>
