/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.CalcEngine=GC.Spread.CalcEngine||{},GC.Spread.CalcEngine.AdvancedFunctions=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="",c(c.s="./node_modules_local/@grapecity/js-calc-advancedfunctions/index.js")}({"./node_modules_local/@grapecity/js-calc-advancedfunctions/dist/gc.spread.calcEngine.advancedfunctions.js":function(a,b,c){var d="object"==typeof d?d:{};d.Spread=d.Spread||{},d.Spread.CalcEngine=d.Spread.CalcEngine||{},d.Spread.CalcEngine.AdvancedFunctions=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/advancedFunctions.entry.ts")}({"./src/advancedFunctions.entry.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("CalcEngine");b.Functions=d.Functions,c("./src/functions-db.ts"),c("./src/functions-eng.ts"),c("./src/functions-fin.ts"),c("./src/functions-lookup.ts"),c("./src/functions-stat.ts")},"./src/functions-db.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=d.Common.q,g=null,h=e.Convert.Pa,i=e.Convert.Na,j=e.Convert.vf,k=e.Convert.Nh,l=e.Errors.DivideByZero,m=e.Errors.Value,n=e.Functions.ak,o=e.Functions.jk,p=isNaN,q=d.Common.u,r=q.kb,s=e.sR().Exp_InvalidCast,t=function(){function a(a,b,c){this.Gk=a,this.Hk=b,this.Wob=c}return a.prototype.Ik=function(a){var b={"=":9,"<>":10,"<":11,"<=":12,">":13,">=":14},c=e.evaluateOperator(b[this.Gk],a,this.Hk);return!!e.Functions.ik(c)&&e.Convert.Vh(c)},a}();function z(a,b){return[a,b].join(",")}u=function(){function a(a,b,c){var d=this;if(d.cj=0,d.Jk=a,d.Kk=c,d.Xob={},3===arguments.length){if(d.Lk=d.Mk(a,b),a.rowCount<2||a.colCount<1||c.rowCount<2||c.colCount<1||d.Lk<0||d.Jk.colCount<=d.Lk)throw s}else if(2===arguments.length&&(d.Lk=-1,a.rowCount<2||a.colCount<1||c.rowCount<2||c.colCount<1))throw s}return a.prototype.Mk=function(a,b){var c,d;if(o(b)){for(c=0;c<a.colCount;c++)if(d=e.Convert.bc(a[0][c]),d&&this.Nk(d,b))return c;return-1}return e.Convert.Rh(b)-1},a.prototype.Ok=function(){var a=this;if(a.cj<=0||a.Jk.rowCount<=a.cj)throw e.sR().Exp_InvalidOperation;return a.Jk[a.cj][a.Lk]},a.prototype.Pk=function(){for(var a=this,b=!1,c=a.Jk.rowCount;!b&&a.cj<c;)a.cj++,a.cj<c&&(b=a.Qk());return b},a.prototype.Rk=function(a){var b,c,f,h,i;if(o(a)){for(b=["=","<>","<=",">=","<",">"],c=e.Convert.bc(a),f=void 0,h=void 0,f=0;f<b.length;f++)if(d.Common.u.kb(c,b[f]))return c=c.substring(b[f].length),i=d.Common.o.lb(c),p(i)?this.Nk("true",c)?a=!0:this.Nk("false",c)?a=!1:(a=c,h=0===f):a=i,new t(b[f],a,h);return new t("=",a)}return a?new t("=",a):g},a.prototype.Yob=function(a,b){var c,d=this,e=d.Xob,f=z(a,b),g=e[f];return g||(c="_"+f,e[c]||(g=e[f]=d.Rk(d.Kk[a][b]),e[c]=!0)),g},a.prototype.Qk=function(){var a,b,c,d,e,g,h,i,j,k=this,l=!1,m=k.Kk,n=k.cj,p=m.rowCount,q=m.colCount,s=k.Jk,t=k.Zob;if(!t){for(d=m[0],t=[],b=0;b<q;b++){if(c=k.Mk(s,d[b]),c<0)return!1;t[b]=c}k.Zob=t}for(a=1;!l&&a<p;a++)for(l=!0,b=0;l&&b<q;b++)if(e=k.Yob(a,b)){if(g=s[n][t[b]],"="===e.Gk&&(h=e.Hk,o(h)&&o(g))){i=f.ub(h,!0),j=e.Wob,l=i?f.sb("^"+i+(j?"$":"")).test(g):j?g.toLowerCase()===h.toLowerCase():r(g,h,!0);continue}l=e.Ik(g)}return l},a.prototype.Nk=function(a,b){return a.toLowerCase()===b.toLowerCase()},a}();function A(a,b,c,d){for(var e,f=0,g=new u(a,b,c);g.Pk();)e=g.Ok(),e=d?i(e):e,e&&f++;return k(f)}function B(a,b,c,d,e,f,g){for(var n,o,q,r,s=g?1:0,t=0,v=0,w=new u(a,b,c);w.Pk();)if(n=w.Ok(),i(n)){if(o=h(n),p(o))return m;s=g?s*o:s+o,t+=o*o,v++}else if(j(n))return n;return f||g?k(s):(q=Math.max(0,(v*t-s*s)/(v*(v-e))),r=d?Math.sqrt(q):q,v<=1?l:k(r))}function C(a,b,c){for(var d,f,g=0,h=0,n=new u(a,b,c);n.Pk();){if(d=n.Ok(),j(d))return d;if(i(d)){if(f={value:0},!e.Convert.Th(d,f))return m;g+=f.value,h++}}return 0===h?l:k(g/h)}function D(a,b,c){return A(a,b,c,!0)}function E(a,b,c){return A(a,b,c,!1)}function F(a,b,c){var d,f=new u(a,b,c);return f.Pk()?(d=f.Ok(),f.Pk()?e.Errors.Number:d):m}function G(a,b,c){for(var d,f,g=!1,l=-e.Functions.hk,n=new u(a,b,c);n.Pk();){if(d=n.Ok(),j(d))return d;if(i(d)){if(f=h(d),p(f))return m;(!g||f>l)&&(l=f),g=!0}}return g?k(l):0}function H(a,b,c){for(var d,f,g=!1,l=e.Functions.hk,n=new u(a,b,c);n.Pk();){if(d=n.Ok(),j(d))return d;if(i(d)){if(f=h(d),p(f))return m;(!g||d<l)&&(l=d),g=!0}}return g?k(l):0}function I(a,b,c){return B(a,b,c,!1,0,!1,!0)}function J(a,b,c){return B(a,b,c,!0,1)}function K(a,b,c){return B(a,b,c,!0,0)}function L(a,b,c){return B(a,b,c,!1,0,!0)}function M(a,b,c){return B(a,b,c,!1,1)}function N(a,b,c){return B(a,b,c,!1,0)}v="!= 1",w={Yi:!0,Hi:4,Ii:0},x={Yi:!0},y=[w,x,w],n("DAVERAGE",C,3,3,y,v,v),n("DCOUNT",D,3,3,y,v,v),n("DCOUNTA",E,3,3,y,v,v),n("DGET",F,3,3,y,v,v),n("DMAX",G,3,3,y,v,v),n("DMIN",H,3,3,y,v,v),n("DPRODUCT",I,3,3,y,v,v),n("DSTDEV",J,3,3,y,v,v),n("DSTDEVP",K,3,3,y,v,v),n("DSUM",L,3,3,y,v,v),n("DVAR",M,3,3,y,v,v),n("DVARP",N,3,3,y,v,v)},"./src/functions-eng.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=d.Common.k.ac,g=void 0,h=Math.abs,i=Math.sin,j=Math.cos,k=Math.sqrt,l=Math.log,m=Math.pow,n=Math.exp,o=Math.E,p=Math.atan2,q=parseFloat,r=isNaN,s=e.Errors.NotAvailable,t=e.Errors.Number,u=e.Errors.Value,v=e.Functions.ak,w=e.Functions.qk,x=e.Convert.Nh,y=e.Convert.vf,z=e.Functions.MathHelper,A=z.Dk,B=e.Functions.Wi,C=0xffffffffffff,D=e.Convert.Th;function oa(a,b){return new e.Functions.Ui(a,b)}E=oa(1,0);function pa(a,b){return{Ld:a,Sk:b}}function qa(a,b){var c=[],d;for(d=0;d<f(a);d++)c.push(pa(a[d],b[d]));return c.push(pa(null,0)),c}F=qa(["g","sg","lbm","u","ozm"],[1,6852205001e-14,.002204622915,6.02217e23,.035273972]),G=1e4/254,H=G/12,I=H/3,J=I/1760,K=qa(["m","mi","Nmi","in","ft","yd","ang","Pica","km"],[1,J,1/1852,G,H,I,1e10,2834.645669,.001]),L=365.25,M=24*L,N=60*M,O=60*N,P=qa(["yr","day","hr","mn","sec"],[1,L,M,N,O]),Q=.00750061708,R=qa(["Pa","atm","mmHg","p","at"],[1,9869233e-12,Q,1,Q]),S=1e5,T=qa(["N","dyn","lbf","dy"],[1,S,.224808924,S]),U=745.701,V=qa(["HP","W","h","w"],[1,U,1,U]),W=62146e14,X=1/(3600*U),Y=1/3600,Z=947815e-9,$=qa(["J","e","c","cal","eV","HPh","Wh","flb","BTU","ev","hh","wh","btu"],[1,9999995.193,.239006249,.238846191,W,X,Y,23.73042222,Z,W,X,Y,Z]),_=qa(["T","gs"],[1,1e4]),aa=.0049289216,ba=qa(["tsp","tbs","oz","cup","pt","qt","gal","l","uk_pt","us_pt","lt"],[1,1/3,1/6,1/48,1/96,1/192,1/768,aa,.008675585,1/96,aa]),ca=qa(["Y","Z","E","P","T","G","M","k","h","e","d","c","m","u","n","p","f","a","z","y"],[1e24,1e21,1e18,1e15,1e12,1e9,1e6,1e3,100,10,.1,.01,.001,1e-6,1e-9,1e-12,1e-15,1e-18,1e-21,1e-24]);function ra(a,b,c){var d,f,g,h,i,j,k,l=100,n=.5*a,o=n*n;for(n=m(n,q(b)),d=e.Functions.tk(b),f=1,g=0,h=q(b),i=n/d,j=.9*i,k=!1;i!==j&&0!==l;)n*=o,d*=++g,f*=++h,j=i,i=c||k?i+n/d/f:i-n/d/f,l--,k=!k;return i}function sa(a,b){var c=m(b,10),d=parseInt(a,b);return r(d)?t:c/2<=d?d-c:d}function ta(a,b,c){a<0&&(a+=m(b,10));var d=a.toString(b),e=c-f(d),g;for(g=0;g<e;g++)d="0"+d;return d.toUpperCase()}function ua(a,b){var c=sa(a,b);return y(c)?t:c}function va(a,b,c){var d=ta(a,c,b.Qb);return 0<=d&&b.Qb<f(d)&&b.$i?t:d}function wa(a,b,c,d,e){var g,h=sa(a,c);return y(h)?t:e&&(h<-e||e-1<h)?t:(g=ta(h,d,b.Qb),0<=h&&b.Qb<f(g)&&b.$i?t:g)}function xa(a,b){var c=Number.POSITIVE_INFINITY,d=a.Pi(),e=a.Qi();return r(d)||d===c||r(e)||e===c?t:a.bc(b)}function ya(a,b,c,d){var e=a,f=b,g=c.Pi(),h=c.Qi();return a=d?e*g-f*h:e+g,b=d?e*h+f*g:f+h,{Ri:a,Si:b}}function za(a,b){var c,d,g,h,i,j,k,l,m=b?1:0,n=0;for(d=0;d<f(a);d++)if(e.Convert.Ca(a[d]))for(k=a[d],g=0;g<k.rowCount;g++)for(h=0;h<k.columnCount;h++){if(c=B(k.getValue(g,h)),!c)return t;j=ya(m,n,c,b),m=j.Ri,n=j.Si}else if(e.Convert.Fh(a[d]))for(l=a[d],i=0;i<l.getRangeCount();i++)for(g=0;g<l.getRowCount(i);g++)for(h=0;h<l.getColumnCount(i);h++){if(c=B(l.getValue(i,g,h)),!c)return t;j=ya(m,n,c,b),m=j.Ri,n=j.Si}else{if(c=B(a[d]),!c)return t;j=ya(m,n,c,b),m=j.Ri,n=j.Si}return xa(oa(m,n))}function Aa(a,b){return x(ra(a,b,!0))}function Ba(a,b){return x(ra(a,b,!1))}function Ca(a,b){var c,d,e,f,g,i,j,k,m,n=1.7976931348623157e308,o=.5772156649015329,p=0,q=.25*a*a,r=1,s=0,t=1,u=2/a;if(b>0){for(s=-o,f=1;f<b;f++)s+=1/f,r*=f+1;if(t=u,1===b)p=1/a;else{for(g=r/b,i=1,j=1,c=g,f=1;f<b;f++){if(g/=b-f,i*=f,j*=-q,d=g*j/i,c+=d,n-h(d)<h(c)||u>1&&n/u<t)return n;t*=u}if(c*=.5,d=h(c),t>1&&n/t<d||d>1&&n/d<t)return n;p=c*t}}k=2*l(a/2),m=-o,0===b?(s=m,d=1):(s+=1/b,d=1/r),c=(m+s-k)*d,e=1;do d*=q/(e*(e+b)),m+=1/e,s+=1/(e+b),c+=(m+s-k)*d,e++;while(h(d/c)>1.1102230246251565e-16);return c=.5*c/t,0!==(1&b)&&(c=-c),p+=c,x(p)}function Da(a,b){var c=[.0007969367292973471,.08283523921074408,1.239533716464143,5.447250030587687,8.74716500199817,5.303240382353949,1],d=[.0007621256162081731,.07313970569409176,1.1271960812968493,5.112079511468076,8.424045901417724,5.214515986823615,1],e=[.0009244088105588637,.08562884743544745,1.2535274390105895,5.470977403304171,8.761908832370695,5.306052882353947,1],f=[.0005713231280725487,.06884559087544954,1.105142326340617,5.073863861286015,8.399855543276042,5.209828486823619,1],g=[-.011366383889846916,-1.2825271867050931,-19.553954425773597,-93.20601521237683,-177.68116798048806,-147.07750515495118,-51.41053267665993,-6.050143506007285],h=[.05108625947501766,4.982138729512334,75.82382841325453,366.7796093601508,710.8563049989261,597.4896124006136,211.68875710057213,25.207020585802372],m=[64.3178256118178,856.4300259769806,3882.4018360540163,7240.467741956525,5930.727011873169,2062.0933166032783,242.0057402402914],n=[74.23732770356752,1056.4488603826283,4986.410583376536,9562.318924047562,7997.041604473507,2826.1927851763908,336.0936078106983],o=[15592.436785523574,-14663929.59039716,5435264770.518765,-982136065717.9115,87590639439536.7,-0xc5090e8ec6601,0x9d4a5b305a0018,-0x41b52d6418d9ec],p=[1263204747.9017804,-647355876379.1603,114509511541823.73,-8127702555013251,0x2cf35a16e6cd620,-0xacf20aeef158b80],r=[1041.2835366425984,626107.330137135,268919633.39381415,86400248710.3935,20297961275010.555,3171577528429750.5,0x37a4bf79f36ba00],s=[594.3015923461282,235564.09294306856,73481194.44597217,18760131610.870617,3882312774962.3857,620557727146953.8,0xf41f1e2a3d2440,0x3721e321ece8fe00],t=[-4794432209.782018,1956174919465.5657,-249248344360967.72,9708622510473064],u=[-899971225.7055594,452228297998.19403,-72749424522181.83,0xd15a1534be241],v=[499.563147152651,173785.4016763747,48440965.83399621,11185553704.535683,2112775201154.892,310518229857422.56,0x710505f9ecba70,0x17be34c7b662cc00],w=[620.8364781180543,256987.25675774884,83514679.14319493,22151159547.97925,4749141220799.914,784369607876235.9,89522233618462740,0x49de55d9c0a4e400],y=.7978845608028654,z=2.356194490192345,A=.7853981633974483;function B(a,b,c,d){var e=0,f=q(b[e++]),g=c;d&&(f+=a,g--);do f=f*a+q(b[e++]);while(0!==--g);return f}function C(a,b){var l,o,p,q,r,s=b?c:d,x=b?e:f,C=b?g:h,D=b?m:n,E=b?t:u,F=b?v:w,G=b?5.783185962946784:14.681970642123893,H=b?30.471262343662087:49.2184563216946;return a<=5?(o=a*a,b&&a<1e-5?1-o/4:(l=B(o,E,3)/B(o,F,8,!0),p=(o-G)*(o-H),p*=l,b?p:p*a)):(l=5/a,o=b?25/(a*a):l*l,p=B(o,s,6)/B(o,x,6),q=B(o,C,7)/B(o,D,7,!0),r=a-(b?A:z),p=p*j(r)-l*q*i(r),p*y/k(a))}function D(a,b){var q,t,u,v,w,x=b?c:d,D=b?e:f,E=b?g:h,F=b?m:n,G=b?o:p,H=b?r:s,I=b?1:a,J=b?7:5,K=b?7:8;return a<=5?(t=a*a,q=I*B(t,G,J)/B(t,H,K,!0),q+=.6366197723675814*(b?l(a)*C(a,!0):C(a,!1)*l(a)-1/a)):(q=5/a,t=b?25/(a*a):q*q,u=B(t,x,6)/B(t,D,6),v=B(t,E,7)/B(t,F,7,!0),w=a-(b?A:z),u=u*i(w)+q*v*j(w),u*y/k(a))}function E(a,b){var c=D(a,!0),d=D(a,!1),e=1,f=2*e,g;if(0===b)return c;if(1===b)return d;do g=f*d/a-c,c=d,d=g,f+=2,++e;while(e<b);return g}return x(E(a,b))}function Ea(a){return ua(a,2)}function Fa(a){return ua(a,16)}function Ga(a){return ua(a,8)}function Ha(a,b){return wa(a,b,2,16)}function Ia(a,b){return wa(a,b,2,8)}function Ja(a,b){return va(a,b,2)}function Ka(a,b){return va(a,b,16)}function La(a,b){return va(a,b,8)}function Ma(a,b){return wa(a,b,16,2,512)}function Na(a,b){return wa(a,b,8,2,512)}function Oa(a,b){return wa(a,b,16,8,536870912)}function Pa(a,b){return wa(a,b,8,16,549755813888)}function Qa(a,b){var c,d=w(a*k(2)),e=2*d-1;return b.$i&&(d=w(b.Qb*k(2)),c=2*d-1,e=c-e),e}function Ra(a){return Qa(a,{$i:!1})}function Sa(a){return 1-Qa(a,{$i:!1})}function Ta(a,b){return e.Zh.Yh(a,b)?1:0}function Ua(a,b){return a>=b?1:0}function Va(a,b,c){return"i"!==c&&"j"!==c?e.Errors.Value:xa(oa(a,b),c)}function Wa(a){return x(k(a.Ri*a.Ri+a.Si*a.Si))}function Xa(a){return x(a.Si)}function Ya(a){return 0===a.Ri&&0===a.Si?e.Errors.DivideByZero:x(p(a.Si,a.Ri))}function Za(a){return xa(oa(a.Ri,-a.Si))}function $a(a){return xa(oa(j(a.Ri)*A(a.Si,!1),-i(a.Ri)*A(a.Si,!0)))}function _a(a,b){var c=a.Ri,d=a.Si,e=b.Ri,f=b.Si;return xa(oa((c*e+d*f)/(e*e+f*f),(d*e-c*f)/(e*e+f*f)))}function ab(a){return xa(oa(n(a.Ri)*j(a.Si),n(a.Ri)*i(a.Si)))}function bb(a){var b=a.Ri,c=a.Si;return xa(oa(l(k(b*b+c*c)),p(c,b)))}function cb(a){var b=a.Ri,c=a.Si,d=z.Ek(o,10);return xa(oa(d*l(k(b*b+c*c)),d*p(c,b)))}function db(a){var b=a.Ri,c=a.Si,d=z.Ek(o,2);return xa(oa(d*l(k(b*b+c*c)),d*p(c,b)))}function eb(a,b){var c,d,e=a.Ri,f=a.Si;return 0===e&&0===f?b>0?"0":t:(c=k(e*e+f*f),d=p(f,e),xa(oa(m(c,b)*j(b*d),m(c,b)*i(b*d))))}function fb(a){return x(a.Ri)}function gb(a){return xa(oa(i(a.Ri)*A(a.Si,!1),j(a.Ri)*A(a.Si,!0)))}function hb(a){var b,c,d=a.Ri,e=a.Si;return 0===d&&0===e?"0":(b=k(d*d+e*e),c=p(e,d),xa(oa(k(b)*j(c/2),k(b)*i(c/2))))}function ib(a,b){return xa(oa(a.Ri-b.Ri,a.Si-b.Si))}function jb(){return za(arguments,!0)}function kb(){return za(arguments,!1)}function lb(a){var b,c=["cup","mmHg","J","sec","cel","kel","hh","Wh","wh","flb","BTU"];for(b=0;c[b];b++)if(0===a.localeCompare(c[b]))return!1;return!0}function mb(a,b,c){var d,e,g,h,i,j,k=0,l=1;for(d=0;a[d].Ld;d++)if(0===c.localeCompare(a[d].Ld))return k=a[d].Sk,{Tk:!0,Uk:l,Vk:k};if(b)for(d=0;b[d].Ld;d++)e=c.substr(0,1).toLowerCase(),g=b[d].Ld.substr(0,1).toLowerCase(),e===g&&lb(c)&&(l=b[d].Sk);for(d=0;a[d].Ld;d++)if(h=f(a[d].Ld),i=c.substr(1,h),j=a[d].Ld.substr(0,h),i===j&&lb(c))return k=a[d].Sk,{Tk:!0,Uk:l,Vk:k};return{Tk:!1,Uk:l,Vk:k}}function nb(a,b,c){var d,e,g,h,i,j,k,l,m,n,o,p,q,r,t,u,v;function w(a,b){return a===b}if(d=273.15,!b||!c)return s;if(e=w(b,"C")||w(b,"cel"),g=w(c,"C")||w(c,"cel"),h=w(b,"F")||w(b,"fah"),i=w(c,"F")||w(c,"fah"),j=w(b,"K")||w(b,"kel"),k=w(c,"K")||w(c,"kel"),e&&g||h&&i||j&&k)return a;if(e&&i)return 9*a/5+32;if(h&&g)return 5*(a-32)/9;if(h&&k)return 5*(a-32)/9+d;if(j&&i)return 9*(a-d)/5+32;if(e&&k)return a+d;if(j&&g)return a-d;for(l=[F,K,P,R,T,$,V,_,ba,_],m=0;m<f(l);m++)if(n=l[m],o=mb(n,ca,b),p=o.Tk,q=o.Vk,r=o.Uk,o=mb(n,ca,c),t=o.Tk,u=o.Uk,v=o.Vk,p&&t&&0!==q&&0!==u)return a*r/q*v/u;return s}function ob(a,b,c,d){var e,f;if(""===a&&""===b)return u;if(e={value:0},f={value:0},D(a,e)===!0&&D(b,f)===!0){if(a=e.value,b=f.value,a<0||a>C||a%1!==0)return t;if("bitOperator"===c){if(b<0||b>C||b%1!==0)return t}else if("shiftOperator"===c&&0===a)return 0;return d(a,b)}return u}function pb(a,b){return ob(a,b,"bitOperator",function(a,b){return a&b})}function qb(a,b){return ob(a,b,"bitOperator",function(a,b){return a|b})}function rb(a,b){return ob(a,b,"bitOperator",function(a,b){return a^b})}function sb(a,b){return ob(a,b,"shiftOperator",function(a,b){if(b>=48)return t;if(b<0&&a<1<<-b)return 0;var c=a<<b;return c>C?t:c})}function tb(a,b){return ob(a,b,"shiftOperator",function(c,d){return d>53?t:b>=0?a>>b:a<<-b})}function ub(a,b){if(""===a)return t;if(a===!0||a===!1)return u;var c=B(a);return c?b(c):t}function vb(a){return ub(a,function(a){return xa(Gb(a),a.lja())})}function wb(a){return ub(a,function(a){return Nb(Db(a),oa(0,0))?t:xa(Mb(Eb(a),Db(a)),a.lja())})}function xb(a){return ub(a,function(a){return Nb(a,oa(0,0))?t:xa(Ib(a),a.lja())})}function yb(a){return ub(a,function(a){return Nb(Fb(a),oa(0,0))?t:xa(Jb(a),a.lja())})}function zb(a){return ub(a,function(a){return Nb(Eb(a),oa(0,0))?t:xa(Kb(a),a.lja())})}function Ab(a){return ub(a,function(a){return Nb(Gb(a),oa(0,0))?t:xa(Lb(a),a.lja())})}function Bb(a){return ub(a,function(a){return xa(Fb(a),a.lja())})}function Cb(a){return ub(a,function(a){return xa(Hb(a),a.lja())})}function Db(a){var b=a.Pi(),c=a.Qi();return oa(i(b)*A(c,!1),j(b)*A(c,!0))}function Eb(a){var b=a.Pi(),c=a.Qi();return oa(j(b)*A(c,!1),-(i(b)*A(c,!0)))}function Fb(a){var b=a.Pi(),c=a.Qi();return oa(A(b,!0)*j(c),A(b,!1)*i(c))}function Gb(a){var b=a.Pi(),c=a.Qi();return oa(A(b,!1)*j(c),A(b,!0)*i(c))}function Hb(a){return Mb(Db(a),Eb(a))}function Ib(a){return Mb(E,Db(a))}function Jb(a){return Mb(E,Fb(a))}function Kb(a){return Mb(E,Eb(a))}function Lb(a){return Mb(E,Gb(a))}function Mb(a,b){var c,d,e=a.Pi(),f=a.Qi(),g=b.Pi(),h=b.Qi();return Math.abs(h)<Math.abs(g)?(c=h/g,oa((e+f*c)/(g+h*c),(f-e*c)/(g+h*c))):(d=g/h,oa((f+e*d)/(h+g*d),(-e+f*d)/(h+g*d)))}function Nb(a,b){return a.Pi()===b.Pi()&&a.Qi()===b.Qi()}da={Hi:0},ea={Hi:5},fa={Hi:5,aj:["checkLength 10"]},ga={Zi:!0,Hi:2,_i:1,aj:["< 1","> 10"]},ha={Hi:2,aj:"< 0"},ia={Hi:8},ja={Hi:8,Oi:!0},ka=[da,ha],la=[{Hi:0,aj:"<= 0"},ha],ma=[da,{Hi:0,_i:0}],na=[fa,ga],v("BESSELI",Aa,2,2,ka),v("BESSELJ",Ba,2,2,ka),v("BESSELK",Ca,2,2,la),v("BESSELY",Da,2,2,la),v("BIN2DEC",Ea,1,1,fa),v("BIN2HEX",Ha,1,2,na,g,g,{bk:1}),v("BIN2OCT",Ia,1,2,na,g,g,{bk:1}),v("DEC2BIN",Ja,1,2,[{Hi:0,aj:["< -512","> 511"]},ga],g,g,{bk:1}),v("DEC2HEX",Ka,1,2,[{Hi:0,aj:["< -549755813888","> 549755813887"]},ga],g,g,{bk:1}),v("DEC2OCT",La,1,2,[{Hi:0,aj:["< -536870912","> 536870911"]},ga],g,g,{bk:1}),v("HEX2BIN",Ma,1,2,na,g,g,{bk:1}),v("HEX2DEC",Fa,1,1,fa),v("HEX2OCT",Oa,1,2,na,g,g,{bk:1}),v("OCT2BIN",Na,1,2,na,g,g,{bk:1}),v("OCT2DEC",Ga,1,1,fa),v("OCT2HEX",Pa,1,2,na,g,g,{bk:1}),v("ERF",Qa,1,2,[da,{Zi:!0,Hi:0}],g,g,{bk:1}),v("ERF.PRECISE",Ra,1,1,da),v("ERFC",Sa,1,1,da),v("ERFC.PRECISE",Sa,1,1,da),v("DELTA",Ta,1,2,ma,g,g,{bk:1}),v("GESTEP",Ua,1,2,ma,g,g,{bk:1}),v("COMPLEX",Va,2,3,[da,da,{Hi:5,_i:"i"}],g,g,{bk:2}),v("IMABS",Wa,1,1,ia),v("IMAGINARY",Xa,1,1,ia),v("IMARGUMENT",Ya,1,1,ia),v("IMCONJUGATE",Za,1,1,ia),v("IMCOS",$a,1,1,ia),v("IMDIV",_a,2,2,[ia,ja]),v("IMEXP",ab,1,1,ia),v("IMLN",bb,1,1,ja),v("IMLOG10",cb,1,1,ja),v("IMLOG2",db,1,1,ja),v("IMREAL",fb,1,1,ia),v("IMSIN",gb,1,1,ia),v("IMSQRT",hb,1,1,ia),v("IMSUB",ib,2,2,[ia,ia]),v("IMPOWER",eb,2,2,[ia,{Hi:2}]),v("IMPRODUCT",jb,1,255,g,-1,-1),v("IMSUM",kb,1,255,g,-1,-1),v("CONVERT",nb,3,3,[{Hi:1},ea,ea]),v("BITAND",pb,0,2),v("BITLSHIFT",sb,0,2),v("BITOR",qb,0,2),v("BITRSHIFT",tb,0,2),v("BITXOR",rb,0,2),v("IMCOSH",vb,1,1),v("IMCOT",wb,1,1),v("IMCSC",xb,1,1),v("IMCSCH",yb,1,1),v("IMSEC",zb,1,1),v("IMSECH",Ab,1,1),v("IMSINH",Bb,1,1),v("IMTAN",Cb,1,1)},"./src/functions-fin.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=isFinite,g=parseInt,h=d.Common.j,i=h.Ea,j=h.Ca,k=h.Fa,l=d.Common.k.ac,m=e.Convert.Rh,n=e.Convert.vf,o=e.Convert.Nh,p=e.Convert.Ph,q=e.Convert.Pa,r=e.Convert.CalcConvertedError,s=null,t=void 0,u=Math.pow,v=Math.abs,w=Math.ceil,x=Math.log,y=Math.sqrt,z=Math.floor,A=Math.max,B=Math.min,C=e.Errors.DivideByZero,D=e.Errors.Value,E=e.Errors.Number,F=e.Functions.ak,G=e.Functions.MathHelper,H=G.Ak,I=e.Functions.hk,J=e.Functions.lk,K=e.Functions.nk,L=e.Functions.mk,M=e.Functions.pk,N=e.Functions.vk,O=e.Convert.Th;function sa(a){return a.getMonth()}function ta(a){return a.getDate()}function ua(a){return a.getFullYear()}function va(a){return a instanceof Date?d.Common.l.Ra(a):a}function wa(a,b,c){var d=ua(b)-ua(a),f=sa(b)-sa(a)+12*d,g=ta(b)-ta(a),h=-1;return 0===c?h=e.Functions.uk(a,b):i(c,[1,2,3])>=0?h=va(b)-va(a):4===c&&(h=30*f+g),h}function xa(a,b,c,d,e){var g,h=u(1+a,b),i=f(h)?h:E;return 0===a?b:n(i)||(h=(h-1)/a,g=f(h)?h:E,n(g))?E:(h=(-c*i-d)/((1+a*e)*g),f(h)?h:E)}function ya(a,b,c,d){var e=ua(b),f=sa(b),g=ta(b),h=new Date(1,0,1),i=0,j=g===K(e,f),k=12/c,l=e-ua(a);h.setFullYear(1),l>0&&(l=(l-1)*c);do h=new Date(e,f,g),l++,h.setMonth(f-l*k),j&&(i=K(ua(h),sa(h)),h=new Date(ua(h),sa(h),i));while(L(a,h)<0);return d&&(h=new Date(e,f,g),l--,h.setMonth(f-l*k),j&&(i=K(ua(h),sa(h)),h=new Date(ua(h),sa(h),i))),h}function za(a,b,c,d){return M(ya(a,b,c,!1),a,d)}function Aa(a,b,c,d){var e;return e=i(d,[0,2,4,5])>=0?360/c:3===d?365/c:M(ya(a,b,c,!1),ya(a,b,c,!0),1)}function Ba(a,b,c,d){return M(a,ya(a,b,c,!0),d)}function Ca(a,b,c){var d=ua(b),e=sa(b),f=ta(b),h=new Date(d,e,f),i=e-sa(a)+12*(d-ua(a));if(h.setMonth(sa(h)-i),f===K(d,e))for(;ta(h)!==K(ua(h),sa(h));)h.setDate(ta(h)+1);return ta(a)>=ta(h)&&i--,g(""+(1+i/(12/c)))}function Da(a,b,c){return b>0?(c.Wk?c.Xk?v(a-c.Yk)<v(c.Zk-c.Yk)&&(c.Zk=a,c.$k=b):b<c.$k&&(c.Zk=a,c.$k=b):(c.Zk=a,c.$k=b,c.Wk=!0),[!1,c]):b<0?(c.Xk?c.Wk?v(a-c.Zk)<v(c.Zk-c.Yk)&&(c.Yk=a,c._k=b):-b<-c._k&&(c.Yk=a,c._k=b):(c.Yk=a,c._k=b,c.Xk=!0),[!1,c]):(c.al=a,[!0,c])}function Ea(a,b,c,d,e){var f,g=ya(a,c,d,!0),h=ya(a,c,d,!1);if(L(g,b)>=0)return M(a,b,e)/Aa(h,g,d,e);for(f=M(a,g,e)/Aa(h,g,d,e);;){if(h=new Date(ua(g),sa(g),ta(g)),g.setMonth(sa(g)+12/d),L(g,b)>=0)return f+=M(h,b,e)/Aa(h,g,d,e);f+=1}}function Fa(a,b,c,d,e,f,h){var i,j,k,l=za(a,b,f,h),m=Ba(a,b,f,h),n=Aa(a,b,f,h),o=g(""+Ca(a,b,f)),p=0,q=100*c/f,r=1+d/f,s=m/n;for(i=0;i<o;i++)p+=q/u(r,s+i);return j=e/u(r,o-1+m/n),k=l/n*q,j+p-k}function Ga(a,b,c,d,e,f,h,i,j){var k,l,m,n,o,p,q=M(i,a,h),r=M(a,j,h),s=M(i,j,h),t=Aa(a,b,f,h),v=g(""+Ca(a,b,f)),x=100*c/f,y=1+d/f;if(r>t)switch(h){case 0:case 4:k=M(j,b,h),v=1+g(""+w(k/t));break;default:for(l=new Date(ua(j),sa(j),ta(j)),v=0;v<32767;v++)if(m=new Date(ua(l),sa(l),ta(l)),l.setMonth(sa(l)+12/f),L(l,b)>=0){v+=g(""+w(M(m,b,h))/Aa(m,l,f,h))+1;break}q=t*Ea(i,a,j,f,h),r=t*Ea(a,j,j,f,h),s=t*Ea(i,j,j,f,h)}return n=e/u(y,v-1+r/t),o=s/t/u(y,r/t),p=u(y,-r/t)*(u(y,-v)-1/y)/(1/y-1),n+x*(o+p-q/t)}function Ha(a,b){var c=a.substr(0,3),d={ATS:13.7603,BEF:40.3399,DEM:1.95583,ESP:166.386,EUR:1,FIM:5.94573,FRF:6.55957,GRD:340.75,IEP:.787564,ITL:1936.27,LUX:40.3399,LUF:40.3399,NLG:2.20371,PTE:200.482,SIT:245.18};return d[c]?H(d[c],b):-1}function Ia(a,b,c,d,e){var f,g=u(1+a,b);return 0===a?f=(c+d)/b:(f=d*a/(g-1)+c*a/(1-1/g),e>0&&(f/=1+a)),-f}function Ja(a,b,c,d,e){var f=u(1+a,b),g=e>0?1+a:1;return-(0===a?d+c*b:d*f+c*g*(f-1)/a)}function Ka(a,b,c,d,e,f,g){if(f<e||f>c||0!==g&&1!==g)return E;var h=Ia(b,c,d,0,g),i=0,j,k;for(1===e&&(a&&(i=h),g<=0&&(i+=a?d*b:-d),e++),j=e;j<=f;j++)k=g>0?Ja(b,j-2,h,d,1)-h:Ja(b,j-1,h,d,0),i+=a?h-k*b:k;return a?i:i*b}function La(a,b,c,d){var e=u(1+c,d),g=-(a*e*c+b*(e-1));return f(g)?g:E}function Ma(a,b,c,d,e){return i(d,[1,2,4])<0||L(b,c)>=0?E:a(b,c,d,e)}function Na(a,b,c,d,e,f){function g(a,b,c,d){var e=0,f,g=0,h=100;for(a*=h/q(c),b/=c,b++,f=1;f<d;f++)e+=f*a/u(b,f);for(e+=d*(a+h)/u(b,d),f=1;f<d;f++)g+=a/u(b,f);return g+=(a+h)/u(b,d),e/=g,e/=q(c)}if(i(f,[1,2,4])<0||L(b,c)>0)return E;var h=g(d,e,f,Ca(b,c,f));return a?h/(1+e/f):h}function Oa(a,b,c,d,e,f,g,h,j){var k,l,m,n;if(i(g,[1,2,4])<0||L(b,c)>0||L(d,b)>0)return E;k=new Date(ua(d),sa(d),ta(d));do k.setMonth(sa(k)+12/g);while(L(k,c)<0);return l=Ea(d,b,k,g,h),m=Ea(d,c,k,g,h),n=Ea(b,c,k,g,h),a?(f*g+100*e*(m-l*(1+j*n/g)))/(j*n+g):(g*(f-j)+100*e*(m-l))/(n*j+100*e*l*n/g)}function Pa(a,b,c){var d,e,f,g;return 0===c?C:(d=b<0?w(b):z(b),e=u(10,w(G.Ek(c,10))),f=b-d,g=a?f*e/c:f*c/e,o(d+g))}function Qa(a,b,c){var d,e=l(b),f=0;for(d=0;d<e;d++)f+=(c?-d:1)*b[d]*Math.pow(1+a,-d-1-(c?1:0));return f}function Ra(a,b,c,d){var e,g,h=0,i=l(b);for(e=0;e<i;e++){if(g=va(d[e])-va(d[0]),g<0)return[!1,c];h+=b[e]/G.Ck(a,g/365)}return f(h)?c=isNaN(h)?5e-324:h:h===Number.POSITIVE_INFINITY?c=I:h===Number.NEGATIVE_INFINITY&&(c=-I),[!0,c]}function Sa(a,b,c,d){var e,f,g;return a>-1&&0!==a?(e=c,f=u(1+a,e.bl-(d?1:0)),g=(u(1+a,e.bl)-1)/a,b=d?-e.cl*g/a+f*e.bl*(e.dl+e.cl*(e.Nc+1/a)):e.dl*f+e.cl*(1+a*e.Nc)*g+e.el,[!0,b,c]):[!1,b,c]}function Ta(a,b,c,d,e,f,g,h,i,k,l,m,n,o,p){var q,r,s,t,u=0,v=0,w=c-e,x=c+e,y=2===a;if(w<f.fl&&(w=c),x>f.gl&&(x=c),w===x)return y?[!1,d,f,g]:[!1,d,f];if(0===a?(q=b(g,h,i,w,l,m,n,o,p)-k,r=b(g,h,i,x,l,m,n,o,p)-k):1===a?(q=b(w,g,u,h),r=b(x,g,v,h)):(q=Sa(w,u,g),r=Sa(x,v,g)),j(q)){if(t=q[2],u=q[1],s=q[0],!s)return y?[s,d,f,t]:[s,d,f]}else u=q;if(j(r)){if(t=r[2],v=r[1],s=r[0],!s)return y?[s,d,f,t]:[s,d,f]}else v=r;return d=(v-u)/(x-w),y?[!0,d,f,t]:[!0,d,f]}function Ua(a,b,c,d,e,f,g,h,i,j,k,l,m){var n,o;return d<c.fl||d>c.gl?[!1,c]:(n=a?Qa(d,e):b(e,f,g,d,i,j,k,l,m)-h,o=Da(d,n,c),c=o[1],[!!o[0],c])}function Va(a,b,c,d,e){if(b<a.fl||b>a.gl)return[!1,a,c];var f=Ra(b,d,c,e);return c=f[1],f[0]?(f=Da(b,0,a),a=f[1],[!!f[0],a,c]):[f[0],a,c]}function Wa(a,b,c){var d,e,f=0;return b<a.fl||b>a.gl?[!1,a,c]:(d=Sa(b,f,c),c=d[2],f=d[1],d[0]?(e=Da(b,f,a),a=e[1],[!!e[0],a,c]):[d[0],a,c])}function Xa(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q=0,r=a?15:2;if(!c.Wk||!c.Xk)return a?[!1,c,d]:[!1,c];for(l=v(c.Zk-c.Yk)/(v(c.Zk)+v(c.Yk)),k=0;k<100+4*r;k++)if(m=0,n=0,o=void 0,o=k%4===0?1:k%4===2?2:3,p=void 0,p=a?ab(c,d,l,q,n,o):b?bb(!0,c,q,m,n,o,Ra,l,e,d,Ta):bb(!1,c,q,m,n,o,Fa,l,f,g,h,d,e,i,j),c=p[1],d=p[2],q=a?p[3]:p[2],p[0])return a?[!0,c,d]:[!0,c];return a?[!1,c,d]:[!1,c]}function Ya(a,b,c,d,e,f,g,h,i,j,l){var m,n,o,p,q,r,u,w=0,x=a?15:2;if(!b.Wk||!b.Xk)return[!1,b];for(n=v(b.Zk-b.Yk)/(v(b.Zk)+v(b.Yk)),m=0;m<100+4*x;m++)if(o=0,p=0,q=void 0,q=m%4===0?1:m%4===2?2:3,r=a?bb(!0,b,w,o,p,q,Qa,n,t,c,Ta):bb(!1,b,w,o,p,q,Ga,n,g,h,i,c,d,j,l,e,f),b=r[1],w=r[2],o=r[3],!(a&&r[0]===s||!a&&k(r[0]))){if(r[0])return[!0,b];if(p=a?Qa(o,c):Ga(c,d,g,o,i,j,l,e,f)-h,u=Da(o,p,b),b=u[1],u[0])return[!0,b];if(n=v(b.Zk-b.Yk)/(v(b.Zk)+v(b.Yk)),n<b.hl)return b._k<p&&(p=b._k,o=b.Yk),b.$k<p&&(o=b.Zk),b.al=o,[!0,b]}return[!1,b]}function Za(a,b,c,d,e,f,g,h,i,k,l,m,n){var o,p,q,r,s,t,u,w,x,y,z=c.hl/2;for(o=0;o<20;o++){if(d<c.fl||d>c.gl)return[!1,c];if(r=a?Ra(d,e,0,f):b(e,f,g,d,i,k,l,m,n)-h,j(r)){if(q=r[1],p=r[0],!p)return[p,c]}else q=r;if(s=Da(d,q,c),c=s[1],s[0])return[!0,c];if(t=c.Xk&&c.Wk?v(c.Zk-c.Yk):c.gl-c.fl,u=(v(d)<1e-10?t:v(d))/1e6,w=0,s=a?Ta(1,Ra,d,w,u,c,e,f):Ta(0,b,d,w,u,c,e,f,g,h,i,k,l,m,n),c=s[2],w=s[1]<0?s[1]:-s[1],p=s[0],!p)return[p,c];if(0===w)return[!1,c];if(x=d-1.000001*q/w,x===d)return c.al=d,[!0,c];if(y=v(x-d)/(v(d)+v(x)),d=x,y<z)return c.al=d,[!0,c]}return[!1,c]}function $a(a,b,c){var d,e,f,g,h,i,j=a.hl/2;for(d=0;d<20;d++){if(e=void 0,f=void 0,g=void 0,h=void 0,c<a.fl||c>a.gl)return[!1,a];if(g=Qa(c,b),i=Da(c,g,a),a=i[1],i[0])return[!0,a];if(h=Qa(c,b,!0),0===h)return[!1,a];if(e=c-1.000001*g/h,e===c)return a.al=c,[!0,a];if(f=v(e-c)/(v(c)+v(e)),c=e,f<j)return a.al=c,[!0,a]}return[!1,a]}function _a(a,b,c){var d,e,f,g,h,i,j,k,l=a.hl/2;for(d=0;d<100;d++){if(e=void 0,f=void 0,g=void 0,h=0,i=0,c<a.fl||c>a.gl)return[!1,a,b];if(j=Sa(c,h,b),b=j[2],h=j[1],g=j[0],!g)return[g,a,b];if(k=Da(c,h,a),a=k[1],k[0])return[!0,a,b];if(j=Sa(c,i,b,!0),b=j[2],i=j[1],g=j[0],!g)return[g,a,b];if(0===i)return[!1,a,b];if(e=c-1.000001*h/i,e===c)return a.al=c,[!0,a,b];if(f=v(e-c)/(v(c)+v(e)),c=e,f<l)return a.al=c,[!0,a,b]}return[!1,a,b]}function ab(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p;if(1===f){if(i=void 0,g=(a.Zk+a.Yk)/2,j=Sa(g,e,b),b=j[2],e=j[1],h=j[0],!h)return[s,a,b,d];if(0===e)return a=Da(g,e,a)[1],[!0,a,b,d];if(i=y(e*e-a.$k*a._k),0===i)return[s,a,b,d]}else if(2===f){if(k=void 0,l=void 0,m=0,n=0,c>.1)return f=3,ab(a,b,c,d,e,f);if(o=d++%4,0===o)k=a.Zk,m=a.$k;else if(2===o)k=a.Yk,m=a._k;else if(k=(a.Zk+a.Yk)/2,j=Sa(k,m,b),b=j[2],m=j[1],h=j[0],!h)return[s,a,b,d];if(l=v(a.Zk-a.Yk)/1e6,p=Ta(2,void 0,k,n,l,a,b),b=p[3],a=p[2],n=p[1],h=p[0],!h||0===n)return[s,a,b,d];if(g=k-1.01*m/n,g<a.Zk&&g<a.Yk||g>a.Zk&&g>a.Yk)return[s,a,b,d]}return[!1,a,b,d]}function bb(a,b,c,d,e,f,g,h,i,k,l,m,n,o,p,q,r){var t,u,w,x,z,A,B,C;switch(f){case 0:d=b.Zk-b.$k*((b.Yk-b.Zk)/(b._k-b.$k));break;case 1:if(d=(b.Zk+b.Yk)/2,t=a?g(d,k,e,i):g(m,n,i,d,l,o,p,q,r)-k,j(t)){if(e=t[1],!t[0])return[s,b,c,d,e,f]}else e=t;if(0===e)return b=Da(d,e,b)[1],[!0,b,c,d,e,f];if(u=y(e*e-b.$k*b._k),0===u)return[s,b,c,d,e,f];d+=(d-b.Zk)*e/u;break;case 3:d=(b.Zk+b.Yk)/2;break;case 2:if(w=void 0,x=0,z=void 0,A=0,h>.1)return f=3,bb(a,b,c,d,e,f,g,h,i,k,l,m,n,o,p);if(B=c++%4,0===B)w=b.Zk,x=b.$k;else if(2===B)w=b.Yk,x=b._k;else if(w=(b.Zk+b.Yk)/2,t=a?g(w,k,x,i):g(m,n,i,w,l,o,p,q,r)-k,j(t)){if(x=t[1],!t[0])return[s,b,c,d,e,f]}else x=t;if(z=v(b.Zk-b.Yk)/1e6,C=a?l(1,g,w,A,z,b,k,i):Ta(0,g,w,A,z,b,m,n,i,k,l,o,p,q,r),b=C[2],A=C[1],!C[0]||0===A)return[s,b,c,d,e,f];if(d=w-1.01*x/A,d<b.Zk&&d<b.Yk||d>b.Zk&&d>b.Yk)return[s,b,c,d,e,f]}return[!1,b,c,d,e,f]}function cb(a,b,c,d,e){e=e?1:0;var f=u(1+a,b);return f=0===a?c*b+d:d*f+c*(1+a*e)*(f-1)/a,o(-f)}function db(a,b){for(var c=0;c<l(b);c++)b[c]!==r&&(a*=1+b[c]);return a}function eb(a){var b,c,d,e=arguments,f=0,g=1;for(c=1;c<l(e);c++){if(d=p(e[c],1,!0,!0,!1),d.isError)return d[0];for(b=0;b<l(d);b++)d[b]!==r&&(f+=d[b]/u(1+a,g++))}return f}function fb(a,b,c,d,e){if(e=e?1:0,0===a)return o(-(c*b+d));var f=u(1+a,b);return a===-1?C:o(-(d+c*(1+a*e)*(f-1)/a)/f)}function gb(a,b,c,d,e){var f,g,h;return L(a,b)>=0?E:(f=wa(a,b,e),g=J(a,e),f<=0||g<=0?E:(h=1-d*f/g,h<=0?E:c/h))}function hb(a,b,c){var d=l(b),e=l(c),f=0,g;if(d!==e)return E;for(g=0;g<d;g++)f+=b[g]/u(1+a,(c[g]-c[0])/365);return f}function ib(a,b,c,d,e,f){return Ka(!1,a,b,c,d,e,f)}function jb(a,b,c,d,e,f){return Ka(!0,a,b,c,d,e,f)}function kb(a,b,c,d,e,f){if(b>=c+1||0!==f&&1!==f)return E;var g=xa(a,c,d,e,f);return La(d,g,a,b-1)}function lb(a,b,c,d){return o(d*a*(b/c-1))}function mb(a,b,c,d,e){e=e?1:0;var f=u(1+a,b);return f=0===a?(c+d)/b:(c*f+d)/((1+a*e)*(f-1)/a),o(-f)}function nb(a,b,c,d,e,f){var g,h;return f=f?1:0,b>=c+1?E:(g=xa(a,c,d,e,f),n(g)||(h=La(d,g,a,b-1),n(h))?E:g-h)}function ob(a,b,c,d){return Ma(za,a,b,c,d)}function pb(a,b,c,d){return Ma(Aa,a,b,c,d)}function qb(a,b,c,d){return Ma(Ba,a,b,c,d)}function rb(a,b,c){return va(ya(a,b,c,!0))}function sb(a,b,c){return Ma(rb,a,b,c)}function tb(a,b,c){return Ma(Ca,a,b,c)}function ub(a,b,c){return va(ya(a,b,c,!1))}function vb(a,b,c){return Ma(ub,a,b,c)}function wb(a,b,c,d,e){return Na(!1,a,b,c,d,e)}function xb(a,b,c,d,e){return Na(!0,a,b,c,d,e)}function yb(a,b,c,d,e){return e=e?1:0,0===a?0===b?C:o(-(c+d)/b):o(x((b*(1+a*e)-d*a)/(c*a+b*(1+a*e)))/x(1+a))}function zb(a,b,c,d,e,f,g){var h,j,k,l,m,n,o,p,q,r,s;if(i(f,[1,2,4])<0||L(a,b)>0)return E;if(h=Ca(a,b,f),h<=1)return j=za(a,b,f,g),k=Ba(a,b,f,g),
l=Aa(a,b,f,g),m=f*l/k,n=e/100+c/f-(d/100+j/l*c/f),o=d/100+j/l*c/f,n/o*m;if(q=.1,r={fl:0,gl:1e3,hl:1e-10,Wk:!1,Zk:0,$k:0,Xk:!1,Yk:0,_k:0,al:0},s=Za(!1,Fa,r,q,a,b,c,d,e,f,g),r=s[1],p=s[0],!p){for(q=1e-10;q<r.gl;q*=2)r=Ua(!1,Fa,r,q,a,b,c,d,e,f,g)[1];s=Xa(!1,!1,r,a,b,c,d,e,f,g),r=s[1],p=s[0]}return p?r.al:E}function Ab(a,b,c,d,e){return L(a,b)>=0?E:(d/c-1)/N(a,b,e)}function Bb(a,b,c,d,e,f){var g=N(c,b,f),h=N(c,a,f),i=N(a,b,f),j=1+g*d;return j/=e/100+h*d,j--,j/=i}function Cb(a,b,c,d,e,f,g){function h(a,b,c,d,e,f,g){var h,i,j,k,l,m,o=1/f;if(i=o<3?1:o<5?1.5:o<=6?2:2.5,f*=i,l=N(b,c,g),n(l))return 0;for(j=z(l*f*a+.5),a-=j,k=a-d,h=0;h<e;h++){if(j=z(f*a+.5),k-=j,k<0)return m=e-h,0===m||1===m?z(.5*a+.5):0;a-=j}return j}var i=1/f;return i>0&&i<1||i>1&&i<2||i>2&&i<3||i>4&&i<5||L(b,c)>0?E:h(a,b,c,d,e,f,g)}function Db(a,b,c,d,e,f,g){function h(a,b,c,d,e,f,g){var h,i,j,k,l=a*f,o=a-d,p=N(b,c,g);return n(p)?0:(h=p*f*a,i=(a-d-h)/l,j=m((a-d-h)/l),k=0===e?h:e<=j?l*(i<1?i:1):e===j+1?o-l*j-h:0)}return L(b,c)>0?E:h(a,b,c,d,e,f,g)}function Eb(a,b,c,d,e,f,g,h,j){var k,l,m,n=.1;if(i(h,[1,2,4])<0||L(c,a)>0||L(a,d)>0||L(d,b)>0)return E;if(k={fl:0,gl:1e3,hl:1e-10,Wk:!1,Zk:0,$k:0,Xk:!1,Yk:0,_k:0,al:0},l=Za(!1,Ga,k,n,a,b,e,f,g,h,j,c,d),k=l[1],m=l[0]){for(n=1e-10;n<k.gl;n*=2)k=Ua(!1,Ga,k,n,a,b,e,f,g,h,j,c,d)[1];l=Ya(!1,k,a,b,c,d,e,f,g,h,j),k=l[1],m=l[0]}return m?k.al:E}function Fb(a,b,c,d,e,f,g,h){return Oa(!1,a,b,c,d,f,g,h,e)}function Gb(a,b,c,d,e,f,g,h){return Oa(!0,a,b,c,d,f,g,h,e)}function Hb(a,b,c){var d,e,f=va(b)-va(a);return L(a,b)>=0||f>365?E:(d=365*c,e=360-c*f,0===e?C:e<0?E:o(d/e))}function Ib(a,b,c){var d=va(b)-va(a);return L(a,b)>=0||d>365?E:(100-c)/c*(360/d)}function Jb(a,b){var c,d,e,f,g,h=l(a),i=[],j=!1,k=!1,m={fl:-1,gl:1e10,hl:1e-10,Wk:!1,Zk:0,$k:0,Xk:!1,Yk:0,_k:0,al:0};if(h<2)return E;for(v(b)>1&&(b=.1),m.gl=B(m.gl,u(I/1e10,1/h)-1),c=0;c<h;c++)d=a[c],d!==r&&(i[c]=d,d>0&&(j=!0),d<0&&(k=!0));if(!j||!k)return E;if(e=$a(m,i,b),m=e[1],f=e[0],!f){for(g=2;(!m.Xk||!m.Wk)&&g<100;g*=2)m=Ua(!0,t,m,b*g,i)[1],m=Ua(!0,t,m,b/g,i)[1];e=Ya(!0,m,i),m=e[1],f=e[0]}return f?m.al:E}function Kb(a,b,c){var d,e,f,g,h=0,i=0,j=0,k=0,m=0,n=[],o=l(a);if(o<2)return C;for(d=0;d<o;d++)e=a[d],e!==r&&(n[d]=e,e>=0?h++:i++);for(j=i+h,f=0;f<j;f++)g=n[f],g>=0?k+=g/u(1+c,f):m+=g/u(1+b,f);return 0===m||0===k||c<=-1?C:u(-k*u(1+c,j)/(m*(1+c)),1/(j-1))-1}function Lb(a,b,c){var d,e,f,g,h,i,j,k,m,n,o=l(a),p=l(b);if(o!==p)return E;for(f=0;f<o;f++)d=a[f]>0||d,e=a[f]<0||e;if(!d||!e)return E;if(g={fl:-1,gl:1e3,hl:1e-10,Wk:!1,Zk:0,$k:0,Xk:!1,Yk:0,_k:0,al:0},h=Za(!0,Ta,g,c,a,b),g=h[1],h[0])return g.al;for(f=1;f<=1024;f+=f)if(j=f,k=9/(j+9),m=Va(g,c,k,a,b),g=m[1],k=j,m=Va(g,c,k,a,b),g=m[1],n=Xa(!1,!0,g,a,b),g=n[1],i=n[0])return g.al;return E}function Mb(a,b,c,d,e,f){var g,h,i,j,k,l;if(e>1&&(e=1),g=u(1.7976931348623157e298,1/a),h={hl:1e-10,Wk:!1,Zk:0,$k:0,Xk:!1,Yk:0,_k:0,al:0,fl:A(-1e10,-g+1),gl:B(1e10,g-1)},i={bl:a,cl:b,dl:c,el:d,Nc:e},j=_a(h,i,f),i=j[2],h=j[1],k=j[0],!k){for(l=2;(!h.Xk||!h.Wk)&&l<100;l*=2)j=Wa(h,f*l,i),i=j[2],h=j[1],j=Wa(h,f/l,i),i=j[2],h=j[1];j=Xa(!0,!1,h,i),h=j[1],k=j[0]}return k?h.al:E}function Nb(a,b,c,d,e){var f,g,h,i=e/c;return i>=1?(i=1,g=1===d?a:0):g=a*u(1-i,d-1),h=a*u(1-i,d),f=g-(h<b?b:h),f<0?0:f}function Ob(a,b,c,d,e,f){var g,h,i,j=0,k=0,l=w(e),m=l,n=a-b,o=!1;for(i=1;i<=m;i++)o?h=k:(g=Nb(a,b,c,i,f),k=n/(d-(i-1)),k>g?(h=k,o=!0):(h=g,n-=g)),i===m&&(h*=e+1-l),j+=h;return j}function Pb(a,b,c,d,e,f,g){var h,i,j,k,l=z(d),m=l,n=w(e),o=n,p=0;if(g)for(h=m+1;h<=o;h++)i=Nb(a,b,c,h,f),h===m+1?i*=B(e,l+1)-d:h===o&&(i*=e+1-n),p+=i;else j=c,k=void 0,d!==z(d)&&f>1&&d>=c/2&&(k=d-c/2,d=c/2,e-=k,j+=1),a-=Ob(a,b,c,j,d,f),p=Ob(a,b,c,c-d,e-d,f);return p}function Qb(a,b,c,d,e,f,g){return e<d?E:a<b&&0===d&&1===e?a-b:Pb(a,b,c,d,e,f,g)}function Rb(a,b,c,d,e,f,g){var h,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A;if(i(f,[1,2,4])<0||L(a,c)>=0)return E;if(1===f||0===g||4===g)return Sb(a,b,c,d,e,f,g);for(h=a.getFullYear(),j=a.getMonth(),k=a.getDate(),l=c.getFullYear(),m=c.getMonth(),n=c.getDate(),o=b.getMonth(),p=b.getDate(),q=[],r=[],s=[],t=[31,28,31,30,31,30,31,31,30,31,30,31],u=0;u<f;u++)q[u]=(o+12*u/f)%12,r[u]=p<=t[q[u]]?p:t[q[u]],2===g?s[u]=360/f:3===g&&(s[u]=365/f);for(u=0;u<f;u++)v=u===f-1?0:u+1,w=q[v]>q[u]?2001:2002,s[u]=va(new Date(w,q[v],r[v]))-va(new Date(2001,q[u],r[u]));for(x=0,y=va(c);h<l||h===l&&j<m||h===l&&j===m&&k<n;){for(u=0;u<f&&(j<q[u]||j===q[u]&&k<r[u])&&(0===u||q[u]>q[u-1]);)u++;for(;u<f&&(j>q[u]||j===q[u]&&k>=r[u])&&(u++,j!==q[u-1]||k!==r[u-1]););u--,v=u===f-1?0:u+1,w=j+12/f>=12?h+1:h,z=va(new Date(h,j,k)),A=Math.min(va(new Date(w,q[v],r[v])),y),x+=e*d/f*(A-z)/s[u],h=w,j=q[v],k=r[v]}return x}function Sb(a,b,c,d,e,f,g){var h=wa(a,c,g),i=J(a,g);return h<0||i<=0?E:e*d*(h/i)}function Tb(a,b,c,d,e){if(L(a,b)>0)return E;var f=wa(a,b,e),g=J(a,e);return f<0||g<=0?E:d*c*f/g}function Ub(a,b,c,d,e){if(L(a,b)>=0)return E;var f=J(a,e),g=wa(a,b,e);return g<=0||f<=0?E:(d-c)/d*(f/g)}function Vb(a,b){return u(1+a/b,b)-1}function Wb(a,b,c,d,e){if(L(a,b)>=0)return E;var f=wa(a,b,e),g=J(a,e);return f<=0||g<=0?E:(d-c)/c*(g/f)}function Xb(a,b){return b*(u(1+a,1/b)-1)}function Yb(a,b,c,d,e){var f,g,h,i,j=c+(e<12?1:0);if(j<d)return E;if(0===a)return 0;for(f=H(1-u(b/a,1/c),3),g=0,h=0,i=1;i<=d;i++)h=1===i?a*f*e/12:i===c+1?(a-g)*f*(12-e)/12:(a-g)*f,g+=h;return h}function Zb(a,b,c,d,e){var f,g=0,h=0;if(c<d)return E;if(a<=b)return 0;for(f=1;f<=d;f++)h=(a-g)*(e/c),h=B(h,a-g-b),g+=h;return h}function $b(a,b,c){return(a-b)/c}function _b(a,b,c,d){return d>c?E:(a-b)*(c-d+1)*2/(c*(c+1))}function ac(a,b){return Pa(!0,a,b)}function bc(a,b){return Pa(!1,a,b)}function cc(a,b,c,d,e,f,g){return i(f,[1,2,4])<0||L(a,b)>0?E:Fa(a,b,c,d,e,f,g)}function dc(a,b,c,d,e){if(L(a,b)>=0)return E;var f=wa(a,b,e),g=J(a,e);return f<=0||g<=0?E:d-c*d*f/g}function ec(a,b,c,d,e,f){var g,h,i,j,k;return L(a,b)>=0?E:(g=wa(a,b,f),h=wa(c,b,f),i=wa(c,a,f),j=J(a,f),i<=0||j<=0||g<=0||h<=0?E:(k=1+g/j*e,0===k?E:(100+h/j*d*100)/k-i/j*d*100))}function fc(a,b,c,d,e,f,g,h,j){return i(h,[1,2,4])<0||L(c,a)>0||L(a,d)>0||L(d,b)>0?E:Ga(a,b,e,f,g,h,j,c,d)}function gc(a,b,c){var d=va(b)-va(a);return L(a,b)>=0||d>365?E:100*(1-c*d/360)}function hc(a){var b=Ha(a,2);return b>=0?b:E}function ic(a,b,c,d,e){var f,g,h,j;function k(a){var b=a.substr(0,3),c=["BEF","LUF","ESP","ITL","PTE","GRD","LUX"];return i(b,c)>=0?0:2}function l(a){var b=a.substr(0,3),c=["BEF","LUF","ESP","ITL","LUX"];return i(b,c)>=0?0:2}return f=0,d||(f=l(c)),e.$i||(e.Qb=k(b)),h=Ha(b,e.Qb),j=Ha(c,e.Qb),h>=0&&j>=0?(g=a*j/h,d||(g=H(g,f)),g):D}function jc(a,b,c,d){var e,f,g;return""===a&&""===b&&""===c?D:(e={value:0},a=O(a,e),f={value:0},b=O(b,f),g={value:0},c=O(c,g),a===!0&&b===!0&&c===!0?d(e,f,g):D)}function kc(a,b,c){return jc(a,b,c,function(a,b,c){var d,e;return b.value>0&&a.value>0&&c.value>0?(d=(Math.log(c.value)-Math.log(b.value))/Math.log(1+a.value),e=Math.abs(d),e<=1.79769e308?e>=2.2250738585072014e-308?d:0:C):E})}function lc(a,b,c){return jc(a,b,c,function(b,c,d){if(c.value>0&&b.value>0&&d.value>=0){a=Math.pow(d.value/c.value,1/b.value)-1;var e=Math.abs(a);if(e<=1.79769e308)return e>=2.2250738585072014e-308?a:0}return E})}P={Hi:0},Q={_i:0,Hi:0},R={Hi:0,aj:"<= 0"},S={Hi:0,aj:"< 1"},T={Hi:0,aj:"< 0"},U={_i:.1,Hi:0},V={_i:2,Hi:0},W={Hi:0,aj:"= 0"},X={_i:1e3,Hi:0,aj:"<= 0"},Y={Hi:6},Z={Hi:5},$={_i:!1,Hi:7},_={Hi:2},aa={Hi:2,aj:"<= 0"},ba={Hi:2,aj:"< 1"},ca={_i:0,Hi:2},da={_i:0,Hi:2,aj:["< 0","> 4"]},ea={_i:0,Hi:2,aj:["< 0","> 4","= 2"]},fa={Hi:2,aj:"= 0",bj:C},ga={Hi:2,aj:"< 0"},ha={Hi:4,Ii:1,Ji:!0,Ki:!0},ia={Hi:4,Ii:1,Ji:!0,Ki:!0,breakOnConvertError:!0},ja=[Y,Y,_,da],ka=[Y,Y,P,P,_,da],la=[Y,Y,R],ma=[P,Y,Y,P,_,R,ea],na=[P,P,P,Q,Q],oa=[Y,Y,R,R,da],pa=[R,aa,R,ba,_,_],qa=[R,ba],ra=[P,ga],F("FV",cb,3,5,na,t,t,{bk:[3,4]}),F("FVSCHEDULE",db,2,2,[P,ha],1,1),F("NPV",eb,2,t,P,"> 0","> 0"),F("PV",fb,3,5,na,t,t,{bk:[3,4]}),F("RECEIVED",gb,4,5,oa,t,t,{bk:3}),F("XNPV",hb,3,3,[P,ia,ia],"!= 0","!= 0"),F("CUMIPMT",ib,6,6,pa),F("CUMPRINC",jb,6,6,pa),F("IPMT",kb,4,6,[P,S,S,P,Q,ca],t,t,{bk:[4,5]}),F("ISPMT",lb,4,4,[P,_,fa,P]),F("PMT",mb,3,5,[P,{Hi:0,aj:"= 0",bj:C},P,Q,Q],t,t,{bk:[3,4]}),F("PPMT",nb,4,6,[P,S,P,P,Q,Q],t,t,{bk:[4,5]}),F("COUPDAYBS",ob,3,4,ja,t,t,{bk:3}),F("COUPDAYS",pb,3,4,ja,t,t,{bk:3}),F("COUPDAYSNC",qb,3,4,ja,t,t,{bk:3}),F("COUPNCD",sb,3,4,ja,t,t,{bk:3}),F("COUPNUM",tb,3,4,ja,t,t,{bk:3}),F("COUPPCD",vb,3,4,ja,t,t,{bk:3}),F("DURATION",wb,5,6,ka,t,t,{bk:5}),F("MDURATION",xb,5,6,ka,t,t,{bk:5}),F("NPER",yb,3,5,[{Hi:0,aj:"<= -1"},P,P,Q,Q],t,t,{bk:[3,4]}),F("YIELD",zb,6,7,[Y,Y,T,T,R,_,da],t,t,{bk:6}),F("YIELDDISC",Ab,4,5,[Y,Y,R,R,da],t,t,{bk:3}),F("YIELDMAT",Bb,5,6,[Y,Y,Y,T,P,da],t,t,{bk:5}),F("AMORDEGRC",Cb,6,7,ma,t,t,{bk:6}),F("AMORLINC",Db,6,7,ma,t,t,{bk:6}),F("ODDFYIELD",Eb,8,9,[Y,Y,Y,Y,T,R,R,_,da],t,t,{bk:8}),F("ODDLYIELD",Fb,7,8,[Y,Y,Y,T,T,R,P,da],t,t,{bk:7}),F("ODDLPRICE",Gb,7,8,[Y,Y,Y,T,T,R,_,da],t,t,{bk:7}),F("TBILLEQ",Hb,3,3,la),F("TBILLYIELD",Ib,3,3,la),F("IRR",Jb,1,2,[ha,U],0,0,{bk:1}),F("MIRR",Kb,3,3,[ha,P,P],0,0),F("XIRR",Lb,2,3,[ia,{Hi:4,Ii:4,Ji:!0,Ki:!0,breakOnConvertError:!0},U],"!= 2","!= 2",{bk:2}),F("RATE",Mb,3,6,[R,P,P,Q,{_i:0,Hi:2,aj:"< 0",bj:D},U],t,t,{bk:[3,4,5]}),F("VDB",Qb,5,7,[T,T,ga,T,T,V,$],t,t,{bk:[5,6]}),F("ACCRINT",Rb,6,8,[Y,Y,Y,R,X,_,da],t,t,{bk:[4,6,7]}),F("ACCRINTM",Tb,3,5,[Y,Y,R,X,da],t,t,{bk:[3,4]}),F("DISC",Ub,4,5,[Y,Y,R,R,da],t,t,{bk:3}),F("EFFECT",Vb,2,2,qa),F("INTRATE",Wb,4,5,oa,t,t,{bk:3}),F("NOMINAL",Xb,2,2,qa),F("DB",Yb,4,5,[T,P,ba,ba,{_i:12,Hi:2,aj:["< 1","> 12"]}],t,t,{bk:3}),F("DDB",Zb,4,5,[T,P,aa,aa,{_i:2,Hi:0,aj:"<= 0"}],t,t,{bk:3}),F("SLN",$b,3,3,[P,P,fa]),F("SYD",_b,4,4,[P,T,ba,aa]),F("DOLLARDE",ac,2,2,ra),F("DOLLARFR",bc,2,2,ra),F("PRICE",cc,6,7,[Y,Y,T,T,W,_,da],t,t,{bk:6}),F("PRICEDISC",dc,4,5,oa,t,t,{bk:3}),F("PRICEMAT",ec,5,6,[Y,Y,Y,T,T,da],t,t,{bk:5}),F("ODDFPRICE",fc,8,9,[Y,Y,Y,Y,T,T,R,_,da],t,t,{bk:8}),F("TBILLPRICE",gc,3,3,[Y,Y,R]),F("EURO",hc,1,1,Z),F("EUROCONVERT",ic,3,5,[P,Z,Z,$,{Zi:!0,Hi:2,_i:3,aj:"< 3",bj:D}],t,t,{bk:[3,4]}),F("PDURATION",kc,3,3),F("RRI",lc,3,3)},"./src/functions-lookup.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=null,g=void 0,h=d.Common.q,i=d.Common.j,j=d.Common.l.lb,k=i.Fa,l=i.G,m=d.Common.k.ac,n=e.Convert.vf,o=e.Convert.Rh,p=e.Convert.Sh,q=e.Convert.Na,r=e.Convert.Pa,s=e.Convert.bc,t=e.Errors.Value,u=e.Errors.Reference,v=e.Errors.NotAvailable,w=e.Errors.Null,x=e.Functions.ak,y=e.Functions.ik,z=e.Functions.jk,A=1048576,B=16384;function S(a,b,c,d){return{row:a,col:b,rowCount:c,colCount:d}}function T(a,b,c){var d,e;return k(a)?f:a.isArray||a.isReference?(d=a.colCount,b>d&&(e=b,b=c,c=e),a[c*d+b]):a[0]}function U(a,b){return!k(b)&&(!!((k(a)||q(a,!0))&&q(b,!0)||y(a)&&y(b))||!(!z(a)||!z(b)))}function V(a,b,c,d,e){if(a<0||b<0||b<a||c<a||c>b)return-1;if(e)return C=a,D=b,E=c,F=c,G=d,H=d,E;if(G){if(E++,E>D){if(G!==H)return-1;E=F-1,G=!1}}else if(E--,E<C){if(G!==H)return-1;E=F+1,G=!0}return E}function W(a,b,c){var d=j(a),e=c;return d===b?e=0:d>b?e=1:d<b&&(e=2),e}function X(a,b,c){var d,e,f;return a===b?0:z(a)?b||0!==m(""+a)?q(b)&&!q(a)?1:y(b)?2:z(b)?(d=c?(""+a).localeCompare(""+b):a.toLowerCase().localeCompare(b.toLowerCase()),0===d?0:d>0?1:2):b instanceof Date?W(a,b,1):-1:0:z(b)?a||0!==m(""+b)?q(a)?2:y(a)?1:a instanceof Date?W(a,b,2):-1:0:y(a)&&q(b)?1:y(b)&&q(a)?2:(e=r(a),f=r(b),e===f?0:e<f?2:1)}function Y(a,b,c){var d=T(a,b,c);return k(d)?0:d}function Z(a,b,c,d){var e,g,h,i,j=-1,k=0,l=-1,m=-1,n=d?b.rowCount:b.colCount;if(n--,n<k)return-1;for(;k<=n;){for(e=f,g=void 0,h=a[0],c>=1!=(2===j)&&(l=m),m=o((k+n)/2),m=V(k,n,m,c>=0,!0),g=m;!U(h,e)&&m!==-1&&(e=d?T(b,0,m):T(b,m,0),!U(h,e));)m=V(0,0,0,!1,!1),c>=0&&m<g?n=m:c<0&&m>g&&(k=m);if(m===-1&&c>=1!=(2===j))return l;if(m===-1)return-1;if(j=X(h,e,!1),c>=1&&1===j)k=m+1;else if(c>=1&&2===j)n=m-1;else if(c<=-1&&1===j)n=m-1;else if(c<=-1&&2===j)k=m+1;else if(0===j){for(;c<=-1&&m>k||c>=0&&m<n;){if(i=0,i=c>=0?m+1:m-1,e=d?Y(b,0,i):Y(b,i,0),null===e)return-1;if(!U(h,e))break;if(j=X(h,e,!1),0!==j)break;m=i}return m}}return c>=1!=(2===j)?m:l}function $(a,b,c,d){var e,g,i,j,k,l=f,m=-1,n=a[0],o=z(n),p=h.zb(n),q=d?b.rowCount:b.colCount;for(o&&p&&(i=h.sb(p)),g=0;g<q;g++)if(j=d?T(b,0,g):T(b,g,0),U(n,j))if(k=z(j),o&&k&&p?(i.lastIndex=0,e=i.test(j)?0:1):e=X(n,j,!1),c>=1&&1===e)e=-1,m>=0&&(e=X(j,l,!1)),(m<0||m>=0&&1===e)&&(m=g,l=j);else if(c<=-1&&2===e)e=-1,m>=0&&(e=X(j,l,!1)),(m<0||m>=0&&2===e)&&(m=g,l=j);else if(0===e)return g;return m}I=e.CalcReference.prototype,J=function(a){R(b,a);function b(b,c,d,e,f,g){var h,i=a.call(this,b,[S(c,d,e,f)])||this;return i.getValue=function(a,b,c){var d=this,e=d.Nc,f;return f=0===e?d.getRow(0)+1+b:1===e?d.getColumn(0)+1+c:I.getValue.call(d,a,b,c)},h=i,h.cj=c,h.Lk=d,h.Xj=e,h.Yj=f,h.Nc=g,i}return b.prototype.type=function(){return this.Nc},b.prototype.toArray=function(a,b){var c=this,d=[],e=c.cj,f=c.Lk,g=c.Nc,h=c.getRangeCount(),i=c.Xj,j=c.Yj,k,l,m,n,o,p;for(k=0;k<h;k++)for(b||(m=[],d.push(m)),o=0;o<i;o++)for(b||(n=[],m.push(n)),p=0;p<j;p++)l=0===g?e+1+o:1===g?f+1+p:I.getValue.call(c,k,e+o,f+p),b?d.push(l):n.push(l);return b||1!==h||(d=d[0]),d.rowCount=i,d.colCount=j,d.rangeCount=h,d},b}(e.CalcReference),K=function(a){R(b,a);function b(b){var c=a.call(this,g)||this;return c.il=b,c}return b.prototype.getRowCount=function(){return e.CalcArrayHelper.getColumnCount(this.il)},b.prototype.getColumnCount=function(){return e.CalcArrayHelper.getRowCount(this.il)},b.prototype.getValue=function(a,b){return e.CalcArrayHelper.getValue(this.il,b,a)},b}(e.CalcArray);function _(a,b,c){var d,f,h,i,j,k,l,m;if(!a)return t;if(d=a.arrayFormulaMode,f=b!==g,h=c?0:1,b=f?b:a.getReference(a.source,S(a.row,a.column,a.rowCount,a.columnCount)),!b||1!==b.getRangeCount())return t;if(d&&f)return new J(b.getSource(),b.getRow(0),b.getColumn(0),b.getRowCount(0),1,h);if(k=[],c){if(l=b.getRow(0)+1,j=b.getRowCount(0),j>1){for(i=0;i<j;i++)k.push([l+i]);return new e.CalcArray(k)}return l}if(m=b.getColumn(0)+1,j=b.getColumnCount(0),j>1){for(i=0;i<j;i++)k.push(m+i);return new e.CalcArray([k])}return m}function aa(a,b,c,d,e){if(e&&c>b.colCount||!e&&c>b.rowCount)return u;var f=d?Z(a,b,1,e):$(a,b,0,e);return f>=0?e?Y(b,c-1,f):Y(b,f,c-1):v}function ba(a,b){return _(a,b,!0)}function ca(a,b){return _(a,b,!1)}function da(a){return e.CalcArrayHelper.getRowCount(a)}function ea(a){return e.CalcArrayHelper.getColumnCount(a)}function fa(a){return new K(a)}function ga(a,b,c,d){return aa(a,b,c,d,!1)}function ha(a,b,c,d){return aa(a,b,c,d,!0)}function ia(a,b,c){var d,f,h,i,j,l,m,o=[];if(b)for(d=0;d<b.length;d++)k(b[d])||o.push(b[d]);if(b.colCount<=b.rowCount?(o.colCount=b.colCount,o.rowCount=Math.ceil(o.length/o.colCount)):(o.rowCount=b.rowCount,o.colCount=Math.ceil(o.length/o.rowCount)),k(b.isReference)||(o.isReference=b.isReference),k(b.isArray)||(o.isArray=b.isArray),o.rangeCount=b.rangeCount,f=-1,i=o.colCount,j=o.rowCount,l=a,c===g)return m=i>j?ga(l,o,j,!0):ha(l,o,i,!0),n(m)?v:m;if(h=e.Convert.Ph(c,0,!0,!1,!1)){if(h.colCount>1&&h.rowCount>1)return v}else h=o;return f=Z(l,o,1,!(i>j)),f>=0?(i=h.colCount,j=h.rowCount,i>j?Y(h,f,j-1):Y(h,i-1,f)):v}function ja(a){var b=arguments;return a<1||m(b)<=a?t:k(b[a])?0:b[a]}function ka(a,b,c){var d,e,f,g,h;function i(a,b){var c,d,e,f,g;if(!b)for(c=m(a),d=void 0,d=1;d<c;d++)if(e=a[d-1],f=a[d],g=X(e,f,!1),2===g)return!1;return!0}return d=a,e=b,e.isArray||e.isReference?(f=e.colCount,g=e.rowCount,h=-1,f>1&&g>1?v:1===c&&!i(e,!0)||c===-1&&!i(e,!1)?v:(1===c?h=Z(d,e,1,g>1):0===c?h=$(d,e,0,g>1):c===-1?h=Z(d,e,-1,g>1):(h=$(d,e,0,g>1),h===-1&&(h=Z(d,e,1,g>1))),h===-1?v:h+1)):v}function la(a,b,c,d){return a+=b,d?0!==c&&(a+="[",a+=""+c,a+="]"):a+=c,a}function ma(a,b,c){return c||(a+="$"),a+=b}function na(a,b,c){c||(a+="$");for(var e=a.length;b>.1;b=parseInt(""+(b-1)/26,10))a=d.Common.u.Eb(a,e,String.fromCharCode("A".charCodeAt(0)+(b-1)%26));return a}function oa(a,b){if(b&&0<m(b)){var c=void 0,d=!e.ei(b[0])&&"_"!==b[0];for(c=1;!d&&c<m(b);c++)d=!e.fi(b[c])&&"_"!==b[c];d?(a+="'",a+=b.replace("'","''"),a+="'"):a+=b,a+="!"}return a}function pa(a,b,c,d,e){var f=3===c||4===c||7===c||8===c,g=2===c||4===c||6===c||8===c,h="";return a<1&&(d||!f)||a>A||b<1&&(d||!g)||b>B||c<1||8<c?t:(h=oa(h,e),d?(h=na(h,b,g),h=ma(h,a,f)):(h=la(h,"R",a,f),h=la(h,"C",b,g)),h)}function qa(a,b,c,d,h){var i,j,l,n,q,r,s,v,w,x,y,z,A,B;function C(a){return!k(a)&&!a._error}return i=a instanceof e.EvaluateContext?a:g,j=i!==f&&i.acceptsReference,b instanceof e.CalcReference?(l={value:-1},n=b,q=n.getRowCount(0),r=n.getColumnCount(0),s=n.getRow(0),v=n.getColumn(0),w=void 0,x=void 0,3===m(arguments)?1!==n.getRangeCount()||1!==q&&1!==r?u:(y=C(c)&&p(c,l)?l.value:0,0!==y||j||i===f||(y=q>0?i.rowOffset:i.columnOffset),y<0?t:q*r<y?u:0===y?n.create([S(s,v,q,r)]):(w=Math.floor((y-1)/r)+s,x=(y-1)%r+v,n.create([S(w,x,1,1)]))):(c=C(c)&&p(c,l)?l.value-1:-1,d=C(d)&&p(d,l)?l.value-1:-1,h=C(h)&&p(h,l)?l.value-1:0,h>=n.getRangeCount()?u:(q=n.getRowCount(h),r=n.getColumnCount(h),z=n.getRow(h),A=n.getColumn(h),c===-1&&!j&&q>1&&i!==f&&(c=i.rowOffset),d===-1&&!j&&r>1&&i!==f&&(d=i.columnOffset),w=c+z,x=d+A,c<-1||d<-1||h<0?t:q<=c||r<=d||n.getRangeCount()<=h?u:c===-1&&d===-1?n.create([S(z,A,q,r)]):c===-1?n.create([S(z,x,q,1)]):d===-1?n.create([S(w,A,1,r)]):n.create([S(w,x,1,1)])))):e.Convert.Ca(b)?(B=b,q=B.getRowCount(),c=c!==g?o(c):-1,d=d!==g?o(d):-1,h=h!==g?o(h)-1:1,1!==h?u:(c===-1&&d===-1?c=d=0:d===-1&&1===q&&(d=c,c=1),c=c===-1?0:c,d=d===-1?0:d,q=B.getRowCount(),r=B.getColumnCount(),c<0||d<0||h<0?t:(c>q&&1===q&&d===g&&(d=c,c=0),q<c||r<d?u:0===c&&0===d?B.slice(0,0,q,r):0===c?B.slice(0,d-1,q,1):0===d?B.slice(c-1,0,1,r):B.getValue(c-1,d-1)))):void 0}function ra(a,b,c,d,f){var h,i;return a&&a instanceof e.CalcReference&&1===a.getRangeCount()?(d=d!==g?o(d):a.getRowCount(0),f=f!==g?o(f):a.getColumnCount(0),h=a.getRow(0)+b,i=a.getColumn(0)+c,d<=0||f<=0||h<0||i<0?u:a.create([{row:h,col:i,rowCount:d,colCount:f}])):t}function sa(a,b,c){var d,f,g,h,j,k,l,m,n,o;return a?b?(d=a.row,f=a.column,g={row:d,col:f},h=a.parser,h||(h=a.parser=new e.Parser),j=a.source,k=j.parserContext,k?(k.useR1C1=!c,k.row=g&&g.row||0,k.column=g&&g.col||0,k.baseIdentity=g,k.source=a.source):(j.parserContext=k=a.source?a.source.getParserContext(!c,g):new e.ParserContext(a.source,(!c),g),k.onlyValidateSourceNameWithSpecial=!0),c?l=h.parse(k,b):(n=j.expressions,n||(n=j.expressions={}),l=n[b],l||(l=h.parse(k,b),n[b]=l)),l?(8===l.type&&(l.source?(m=l.source,l=m.getCustomName(l.value)):l=a.getName(l.value)),!l||i.Ea(l.type,[1,13])<0?u:(m=l.source||a.source,25===l.type||26===l.type?m.getValueByName(l.nameIdentity):(o=l.getRange(d,f),13===l.type&&(m=l.source),1!==o.rowCount||1!==o.colCount||a.acceptsReference||a.arrayFormulaMode?m.getReference(o):m.getValue(o.row,o.col)))):u):u:v}function ta(a,b){return s(2===arguments.length?b:a)}function ua(){var a,b=0;for(a=0;a<m(arguments);a++){if(0===arguments[a].length)return w;arguments[a].isReference&&(b+=arguments[a].rangeCount)}return b}function va(a,b){var c=b.getRow(0),d=b.getColumn(0),e=b.getSource().getCalcSourceModel().getFormula(c,d);return e?"="+e:v}function wa(a,b){var c=b.getRow(0),d=b.getColumn(0),e=b.getSource().getCalcSourceModel().getFormula(c,d);return!!e}L={_i:!0,Hi:7},M={Hi:2},N={_i:NaN,Hi:2},O={_i:1,Hi:2},P={Hi:2,aj:"<= 0",bj:t},Q={Hi:4,Ii:0,Ji:!0},x("ADDRESS",pa,2,5,[M,M,O,L,{_i:"",Hi:5}],g,g,{bk:[2,3,4]}),x("INDEX",qa,2,4,g,0,0,{bk:[1,2,3],dk:!0,fk:3}),x("OFFSET",ra,3,5,[{},M,M],0,0,{bk:[3,4],ck:!0,fk:2,returnReference:!0}),x("ROW",ba,0,1,g,-1,g,{dk:!0,ek:-1,expandColumns:-1}),x("COLUMN",ca,0,1,g,-1,g,{bk:0,dk:!0,ek:-1,expandRows:-1}),x("ROWS",da,1,1,g,-1,-1,{ek:-1,expandColumns:-1,ck:!0}),x("COLUMNS",ea,1,1,g,-1,-1,{ek:-1,expandRows:-1,ck:!0}),x("TRANSPOSE",fa,1,1,g,-1,-1),x("LOOKUP",ia,2,3,[Q,{Hi:4,Ii:0,Ji:!0,xTa:!0}],"!= 0","!= 0",{fk:1}),x("HLOOKUP",ga,3,4,[Q,Q,P,L],1,1,{bk:3}),x("VLOOKUP",ha,3,4,[Q,Q,P,L],1,1,{bk:3,TAb:!0}),x("CHOOSE",ja,2,255,M,">= 1",">= 1",{gk:">= 1",isBranch:!0,findTestArgument:0,findBranchArgument:function(a){var b={value:-1};return n(a)?-1:(p(a,b),b.value)}}),x("MATCH",ka,2,3,[Q,Q,N],1,1,{bk:2}),x("INDIRECT",sa,1,2,[{Hi:5},L],g,g,{bk:2,ck:!0,dk:!0}),x("HYPERLINK",ta,1,2),x("AREAS",ua,1,g,{Hi:4,Ji:!0,Ki:!0,Xi:!0},-1,-1),x("FORMULATEXT",va,1,1,g,-1,g,{dk:!0}),x("ISFORMULA",wa,1,1,g,-1,g,{dk:!0})},"./src/functions-stat.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa,Ta,Ua,Va,Wa,Xa;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("CalcEngine"),f=d.Common.k.ac,g=null,h=void 0,i=isNaN,j=isFinite,k=parseInt,l=Math.pow,m=Math.max,n=Math.min,o=Math.floor,p=Math.sin,q=Math.tan,r=Math.PI,s=Math.atan,t=Math.exp,u=Math.abs,v=Math.sqrt,w=Math.log,x=Math.round,y=e.Errors.DivideByZero,z=e.Errors.Value,A=e.Errors.NotAvailable,B=e.Errors.Number,C=e.Errors.Null,D=e.Convert.Nh,E=e.Convert.Ph,F=e.Convert.Rh,G=e.Convert.Pa,H=e.Functions.MathHelper,I=H.Bk,J=H.xk,K=e.Convert.Na,L=e.Convert.vf,M=e.Functions.rk,N=e.Functions.sk,O=e.Functions.sja,P=e.Functions.qk,Q=e.Functions.ik,R=e.Functions.jk,S=e.Functions.Na,T=e.Functions.hk,U=e.Convert.CalcConvertedError,V=e.Functions.ak,W=e.Convert.Th,X=e.Convert.Sh;function Ya(a){var b,c=0;for(b=0;b<a.length;b++)a[b]!==U&&c++;return c}function Za(a){var b,c,d,e=6;return 0===a?b=0:(c=.5*Math.abs(a),c>=.5*e?b=1:c<1?(d=c*c,b=((((((((.000124818987*d-.001075204047)*d+.005198775019)*d-.019198292004)*d+.059054035642)*d-.151968751364)*d+.319152932694)*d-.5319230073)*d+.797884560593)*c*2):(c-=2,b=(((((((((((((-45255659e-12*c+.00015252929)*c-19538132e-12)*c-.000676904986)*c+.001390604284)*c-.00079462082)*c-.002034254874)*c+.006549791214)*c-.010557625006)*c+.011630447319)*c-.009279453341)*c+.005353579108)*c-.002141268741)*c+.000535310849)*c+.999936657524)),a>0?.5*(b+1):.5*(1-b)}function $a(a,b,c){var d,e,g,h,i,j=f(a);if(b!==a[0]){for(g=0,h=a[0],e=1;e<j&&a[e]<b;e++)a[e]!==h&&(g=e,h=a[e]);a[e]!==h&&(g=e),b===a[e]?d=c?g/(j-1):(e+1)/(j+1):0===g?d=0:(i=(b-a[g-1])/(a[g]-a[g-1]),d=c?(g-1+i)/(j-1):(g+i)/(j+1))}else d=c?0:1/(j+1);return d}function _a(a,b,c,d){var e,g,h=[];return rb(a,h),e=f(h),0===e||b<h[0]||b>h[e-1]?A:(g=1===e?1:$a(h,b,d),0!==g?x(g*l(10,c))/l(10,c):g)}function ab(a,b,c){var d,e,f,g,h,i=300,j=1e-50,k=1e-20,l=a+b,m=a+1,n=a-1,o=1,p=1-l*c/m;for(u(p)<j&&(p=j),p=1/p,d=p,e=1;e<=i&&(f=e+e,g=(b-e)*e*c/((n+f)*(a+f)),p=1+g*p,u(p)<j&&(p=j),o=1+g/o,u(o)<j&&(o=j),p=1/p,d*=p*o,g=0-(a+e)*(l+e)*c/((a+f)*(m+f)),p=1+g*p,u(p)<j&&(p=j),o=1+g/o,u(o)<j&&(o=j),p=1/p,h=p*o,d*=h,!(u(h-1)<k));e++);return d}function bb(a){var b=[2.2250738585072014e-308,1.7976931348623157e308,l(2,-53),l(2,-52),H.Ek(2,10)];return b[a-1]||0}function cb(a,b,c){var d,e=0,f=0;if(b<1)return 0;for(d=1;d<=b;d++)if(e=b-d,f+=u(G(a[e])),f>c)return e;return e}function db(a,b,c){var d,e,f,g,h;if(c<1||c>1e3||a<-1.1||a>1.1)return NaN;for(d=2*a,f=e=0,g=0,h=1;h<=c;h++)f=e,e=g,g=d*e-f+G(b[c-h]);return.5*(g-f)}function eb(a){var b,c=[.16663894804518634,-1384948176067564e-20,9.81082564692473e-9,-1.809129475572494e-11,6.221098041892606e-14,-3.399615005417722e-16,2.683181998482699e-18,-2.868042435334643e-20,3.9628370610464347e-22,-6.831888753985767e-24,1.4292273559424982e-25,-3.5475981581010704e-27,1.025680058010471e-28,-3.401102254316749e-30,1.276642195630063e-31],d=cb(c,15,bb(3)),e=1/v(bb(3)),f=t(n(w(bb(2)/12),-w(12*bb(1))));return a<10?NaN:a>=f?4.930380657631324e-32:a<e?(b=10/a,db(b*b*2-1,c,d)/a):1/(12*a)}function fb(a){var b=[1.037869356274377,-.13364301504908918,.019408249135520562,-.0030107551127535777,.0004869461479715485,-8105488189317536e-20,13778847799559525e-21,-2380221089435897e-21,4.1640416213865184e-7,-7.359582837807599e-8,1.3117611876241675e-8,-2.3546709317742423e-9,4.2522773276035e-10,-7.71908941348408e-11,1.407574648135907e-11,-2.5769072058024682e-12,4.734240666629442e-13,-8.724901267474264e-14,1.612461490274055e-14,-2.9875652015665774e-15,5.548070120908289e-16,-1.0324619158271569e-16,1.9250239203049852e-17,-3.595507346526515e-18,6.726454253787686e-19,-1.260262416873522e-19,2.364488440860621e-20,-4.4419377050807936e-21,8.354659446403425e-22,-1.5731559416479563e-22,2.9653128740247425e-23,-5.594958348181595e-24,1.056635426883568e-24,-1.9972483680670205e-25,3.778297781883936e-26,-7.153158688908174e-27,1.3552488463674214e-27,-2.5694673048487566e-28,4.8747756066216946e-29,-9.254211253084972e-30,1.757859784176024e-30,-3.341002667773101e-31,6.353393618023618e-32],c=cb(b,43,.1*bb(3));return a<=-1?NaN:u(a)<=.375?a*(1-a*db(a/.375,b,c)):w(a+1)}function gb(a){var b,c,d,e,f,g,h=1,i=[1,.5772156649015329,-.6558780715202538,-.0420026350340952,.1665386113822915,-.0421977345555443,-.009621971527877,.007218943246663,-.0011651675918591,-.0002152416741149,.0001280502823882,-201348547807e-16,-12504934821e-16,1133027232e-15,-2.056338417e-7,6.116095e-9,5.0020075e-9,-1.1812746e-9,1.043427e-10,7.7823e-12,-3.6968e-12,5.1e-13,-2.06e-14,-5.4e-15,1.4e-15];if(a>171)return 1e308;if(a===F(a))if(a>0)for(b=1,e=2;e<a;e++)b*=e;else b=1e308;else{if(u(a)>1){for(d=u(a),f=F(d),h=1,c=1;c<=f;c++)h*=d-c;d-=f}else d=a;for(g=i[24],c=23;c>=0;c--)g=g*d+i[c];b=1/(g*d),u(a)>1&&(b*=h,a<0&&(b=-r/(a*b*p(r*a))))}return b}function hb(a){var b,c,d,e,f,g=0,h=[.08333333333333333,-.002777777777777778,.0007936507936507937,-.0005952380952380952,.0008417508417508418,-.001917526917526918,.00641025641025641,-.02955065359477124,.1796443723688307,-1.3924322169059],i=a;if(a<=0)return 1e308;if(1===a||2===a)return 0;for(a<=7&&(g=F(7-a),i=a+g),c=1/(i*i),d=2*r,e=h[9],b=8;b>=0;b--)e=e*c+h[b];if(f=e/i+.5*w(d)+(i-.5)*w(i)-i,a<=7)for(b=1;b<=g;b++)f-=w(i-1),i-=1;return f}function ib(a,b){var c,d,e,f=d=a;return b<f&&(f=b),b>d&&(d=b),f<0?NaN:0===f?T:f>=10?(c=eb(f)+eb(d)-eb(f+d),w(d)*-.5+.9189385332046728+c+(f-.5)*w(f/(f+d))+d*fb(-f/(f+d))):d>=10?(c=eb(d)-eb(f+d),e=md(f),L(e)?NaN:G(e)+c+f-f*w(f+d)+(d-.5)*fb(-f/(f+d))):w(gb(f)*(gb(d)/gb(f+d)))}function jb(a,b,c){var d,e,f,g,h,i,j,k,l,p,q,r=bb(3),s=w(r),u=bb(1),v=w(u),x=a,y=b,z=c;if(y/(y+z)<a&&(x=1-x,y=c,z=b),(y+z)*x/(y+1)<r)d=0,f=y*w(m(x,u))-w(y)-ib(y,z),f>v&&0!==x&&(d=t(f)),x===a&&y===b||(d=1-d);else{if(i=z-o(z),0===i&&(i=1),f=y*w(x)-ib(i,y)-w(y),d=0,f>=v&&(d=t(f),e=d*y,1!==i))for(h=F(m(s/w(x),4)),j=1;j<=h;j++)g=j,e=e*(g-i)*x/g,d+=e/(y+g);if(z>1){for(f=y*w(x)+z*w(1-x)-ib(y,z)-w(z),k=F(m(f/v,0)),e=t(f-k*v),l=1/(1-x),p=z*l/(y+z-1),q=0,h=F(z),z===h&&(h-=1),j=1;j<=h&&!(p<=1&&e/r<=q);j++)g=j,e=(z-g+1)*l*e/(y+z-g),e>1&&(k-=1,e*=u),0===k&&(q+=e);d+=q}x===a&&y===b||(d=1-d),d=m(n(d,1),0)}return d}function kb(a){var b,c;return a=.7071067811865475*-a,b=G(a),i(b)?z:(c=P(b*v(2)),L(c)?NaN:.5*(2-2*c))}function lb(a,b,c,d){var e,f,g,h,i,j,k,l,n,o,p,q,r,s;function t(a,b){return a<0&&b>0||a>0&&b<0}for(e=1e-307,f=2.22045e-16,g=a.ge(b),h=a.ge(c),j=0;j<1e3&&!t(g,h);j++)u(g)<=u(h)?(i=b,b+=2*(b-c),b<0&&(b=0),c=i,h=g,g=a.ge(b)):(i=c,c+=2*(c-b),b=i,g=h,h=a.ge(c));if(0===g)return b;if(0===h)return c;if(!t(g,h))return d.jl=!0,0;for(k=b,l=g,n=c,o=h,p=b,q=g,r=.5*(b+c),s=!0,j=0;j<500&&u(q)>e&&c-b>m(u(b),u(c))*f;)s&&(l!==o&&o!==q&&q!==l?(r=k*q*o/(q-l)/(o-l)+p*o*l/(o-q)/(l-q)+n*l*q/(l-o)/(q-o),s=b<r&&r<c):s=!1),s||(r=.5*(b+c),n=c,o=h,s=!0),k=n,n=p,p=r,l=o,o=q,q=a.ge(r),t(g,q)?(c=p,h=q):(b=p,g=q),s=s&&2*u(q)<=u(o),++j;return p}function mb(a,b){var c,d,e,g,h=f(b);for(c=0;c<h;c++)d=G(b[c]),e=d-a.M,g=e/(a.N+1),a.M+=g,a.Q+=a.N*e*g,a.N++,a.kl+=d}function nb(a){var b,c=[76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],d=1.000000000190015,e=a,f=a+5.5;for(f-=(a+.5)*w(f),b=0;b<=5;b++)d+=c[b]/++e;return-f+w(2.5066282746310007*d/a)}function ob(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=0===c||1===c?0:t(nb(a+b)-nb(a)-nb(b)+a*w(c)+b*w(1-c)),s=c>=(a+1)/(a+b+2);for(s&&(d=a,a=b,b=d,c=1-c),e=4.450147717014403e-308,f=100,g=a+b,h=a+1,i=a-1,j=1,k=1-g*c/h,u(k)<e&&(k=e),k=1/k,l=k,m=1,n=2;m<=f;m++,n+=2)if(o=m*(b-m)*c/((i+n)*(a+n)),k=1+o*k,u(k)<e&&(k=e),j=1+o/j,u(j)<e&&(j=e),k=1/k,l*=k*j,o=-(a+m)*(g+m)*c/((a+n)*(h+n)),k=1+o*k,u(k)<e&&(k=e),j=1+o/j,u(j)<e&&(j=e),k=1/k,p=k*j,l*=p,u(G(p-1))<5e-324)return q=r*l/a,s?1-q:q;return B}function pb(a,b){var c,d,e,g,h=f(b);for(c=0;c<h;c++)d=b[c],d!==U&&(e=d-a.M,g=e/(a.N+1),a.M+=g,a.Q+=a.N*e*g,a.N++,a.kl+=d)}function qb(a){a.sort(function(a,b){return a-b})}function rb(a,b){var c,d;for(c=0;c<f(a);c++)d=a[c],d!==U&&b.push(d);qb(b)}function sb(a){var b,c=[];for(b=0;b<f(a);b++)rb(a[b],c);return c}function tb(a,b,c){var d,e,f,g,h,i,j,k,m,n,o,p=Math.expm1||function(a){return t(a)-1},q=Math.log1p||function(a){return w(1+a)};return a<=0?0:a>=1?1:1===c?l(a,b):1===b?-p(c*q(-a)):(e=.5-a+.5,f=q(-a),g=a,h=w(a),i=b,j=c,k=a>b/(b+c),k&&(i=c,j=b,g=e,e=a,h=f,f=w(a)),d=ub(g,i,j),d/=i,m=i/(i+j),n=j/(i+j),o=i>1&&j>1&&m<.97&&n<.97?vb(g,i,j)*g*e:t(i*h+j*f-wb(!1,i,j)),d*=o,k&&(d=.5-d+.5),d>1&&(d=1),d<0&&(d=0),d)}function ub(a,b,c){var d,e,f,g,h,i,j,k,l,m,n=2.22045e-16,o=1,p=1,q=1-(b+c)/(b+1)*a;0===q?(d=0,e=1,g=1):(d=1,e=1/q,g=d*e),f=1,h=1,i=5e4,j=!1;do k=b+2*h,l=h*(c-h)*a/((k-1)*k),m=-(b+h)*(b+c+h)*a/(k*(k+1)),o=(d+l*o)*e,p=(q+l*p)*e,d=o+m*d*e,q=p+m*q*e,0!==q&&(e=1/q,f=d*e,j=u(g-f)<u(g)*n),g=f,h+=1;while(h<i&&!j);return g}function vb(a,b,c){var d,e,f,g,h,i,j;return 1===b?1===c?1:2===c?-2*a+2:a<=.01?c+c*t((c-1)*w(1-a))-1:c*l(.5-a+.5,c-1):1===c?2===b?b*a:b*l(a,b-1):a<=0&&!(b<1&&0===a)||a>=1&&!(c<1&&1===a)?0:(d=w(bb(2)),e=w(bb(1)),f=w(a<.1?1+a:1-a),g=w(a),h=(b-1)*g,i=(c-1)*f,j=wb(!1,b,c),h<d&&h>e&&i<d&&i>e&&j<d&&j>e&&h+i<d&&h+i>e?l(a,b-1)*l(.5-a+.5,c-1)/wb(!0,b,c):t(h+i-j))}function wb(a,b,c){var d,e,f,g,h,i,j,k,l,m=c,n=b,o=171.624376956302;return b>c&&(d=m,m=n,n=d),a&&m+n<o?yb(m)/yb(m+n)*yb(n):(e=6.02468004077673,f=e-.5,g=Ab(m),g/=Ab(m+n),g*=Ab(n),h=m+n+f,i=a?g*v(h/(m+f)/(n+f)):w(g)+.5*(w(h)-w(m+f)-w(n+f)),j=n/(m+f),k=m/(n+f),l=-m*w(1+j)-n*w(1+k)-f,a?t(l)*i:l+i)}function xb(a){var b=Ab(a),c=6.02468004077673,d=a+c-.5,e=l(d,a/2-.25);return b*=e,b/=t(d),b*=e,a<=20&&a===H.wk(a)&&(b=x(b)),b}function yb(a){var b,c,d=w(r),e=w(bb(2)),f=171.624376956302;return a>f?B:a>=1?xb(a):a>=.5?xb(a+1)/a:a>=-.5?(b=xb(a+2)-w(a+1)-w(u(a)),b>=e?B:xb(a+2)/(a+1)/a):(c=xb(1-a)+w(u(p(r*a))),c-d>=e?0:c<0&&d-c>e?B:t(d-c)*(p(r*a)<0?-1:1))}function zb(a){var b=171.624376956302;return a>=b?xb(a):a>=1?w(xb(a)):a>=.5?w(xb(a+1)/a):xb(a+2)-w(a+1)-w(a)}function Ab(a){var b,c,d,e,f=[23531376880.41076,42919803642.6491,35711959237.35567,17921034426.03721,6039542586.352028,1439720407.3117216,248874557.86205417,31426415.585400194,2876370.6289353725,186056.26539522348,8071.672002365816,210.82427775157936,2.5066282746310002],g=[0,39916800,120543840,150917976,105258076,45995730,13339535,2637558,357423,32670,1925,66,1];if(a<=1)for(b=f[12],c=g[12],d=11;d>=0;--d)b*=a,b+=f[d],c*=a,c+=g[d];else for(e=1/a,b=f[0],c=g[0],d=1;d<=12;++d)b*=e,b+=f[d],c*=e,c+=g[d];return b/c}function Bb(a,b){return a<=0?0:Cb(b/2,a/2)}function Cb(a,b){var c=a*w(b)-b-zb(a),d=t(c);return b>a+1?1-d*Eb(a,b):d*Db(a,b)}function Db(a,b){var c=.5*bb(3),d=a,e=1/a,f=e,g=1;do d+=1,e=e*b/d,f+=e,g+=1;while(e/f>c&&g<=1e4);return g>1e4?z:f}function Eb(a,b){var c=bb(3),d=.5*c,e=1/c,f=0,g=0,h=1-a,i=b+2-a,j=0,k=b+1,l=1,m=1,n=i*b,o=b,p=k/n,q=!1,r=0;do f+=1,h+=1,g=h*f,i+=2,j=k*i-l*g,m=n*i-o*g,0!==m&&(r=j/m,q=u((p-r)/r)<=d,p=r),l=k,k=j,o=n,n=m,u(j)>e&&(l*=c,k*=c,o*=c,n*=c);while(!q&&f<1e4);return q?p:z}function Fb(a,b){var c,d;if(a<=0)return 0;if(b*a>1391e3)c=t((.5*b-1)*w(.5*a)-.5*a-w(2)-zb(.5*b));else{for(b%2<.5?(c=.5,d=2):(c=1/v(2*a*r),d=1);d<b;)c*=a/d,d+=2;c=a>=1425?t(w(c)-a/2):c*t(-a/2)}return c}function Gb(a,b,c,d){var e,f,g,h=I(c,a);return L(h)?h:(e=G(h),h=I(d-c,b-a),L(h)?h:(f=G(h),h=I(d,b),L(h)?h:(g=G(h),D(e*f/g))))}function Hb(a,b,c){var d,e,f=tb(b/(b+a*a),b/2,.5);switch(c){case 1:
return.5*f;case 2:return f;case 3:return l(1+a*a/b,-(b+1)/2)/(v(b)*wb(!0,.5,b/2));case 4:return d=b/(a*a+b),e=.5*tb(d,.5*b,.5),a<0?e:1-e}return z}function Ib(a,b,c){return{ge:function(d){return a-Hb(d,b,c)}}}function Jb(a,b,c){var d={jl:!1},e=Ib(a,b,c),f=lb(e,.5*b,b,d);return d.jl?A:f}function Kb(a,b){var c=!1,d=0,e,g,h,i,j;for(g=0;g<f(b);g++)for(j=b[g],h=0;h<f(j);h++)if(i=j[h],K(i)||Q(i)||R(i))e=R(i)?0:G(i),(!c||a&&e>d||!a&&e<d)&&(d=e),c=!0;else if(L(i))return i;return d}function Lb(a,b,c,d){var e,g,h,i=0,j=0,k=0;for(e=0;e<f(b);e++)g=b[e],g!==U&&(g<c?i++:c<g?k++:j++);return 0===j?A:(h=(0===d?k:i)+1,a?h:h+(j-1)/2)}function Mb(a,b){var c,d,h,i,j,k;for(i=0;i<f(a);i++)for(c=a[i],j=0;j<f(c);j++)if(h=c[j],h===g)b.n++;else if(R(h)){if(k={value:0},e.Convert.Th(h,k))d=k.value,b.ll+=d,b.ml+=d*d;else if(!c.isArray&&!c.isReference)return z;b.n++}else{if(L(h))return h;K(h)&&(d=G(h),b.ll+=d,b.ml+=d*d,b.n++)}}function Nb(a,b,c){var d,e={ll:0,ml:0,n:0},f=Mb(a,e);return L(f)?f:e.n<=b?y:(d=m(0,(e.n*e.ml-e.ll*e.ll)/(e.n*(e.n-b))),d=c?v(d):d,D(d))}function Ob(a,b,c){var d,e,g,h,i,j=0,k=0,l=0,m=0,n=f(b);if(n!==f(c))return A;for(g=0;g<n;g++)d=b[g],e=c[g],d!==U&&e!==U&&(j+=d,k+=e,m++);if(m<=1)return y;for(h=j/m,i=k/m,g=0;g<n;g++)d=b[g],e=c[g],d!==U&&e!==U&&(l+=(d-h)*(e-i));return D(a?l/m:l/(m-1))}function Pb(a,b){var c,d,e,g,h=a?1:0,i=0;for(e=0;e<f(b);e++)for(c=b[e],g=0;g<f(c);g++)if(d=c[g],d!==U){if(d<=0)return B;h=a?h*d:h+1/d,i++}return a?i<=0?y:D(l(h,1/i)):0===h?y:D(i/h)}function Qb(a,b,c,d,f){var i,j,k,l,m,n,o,p,q,r,s,u,x,y,z,C,D,F,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y=b.rowCount,Z=b.colCount;if(a?c=c!==h?E(c,1,!1,!0,!0):b:(i=c!==h?c:function(a,b){var c=[];for(j=0;j<a;j++)for(c[j]=[],k=0;k<b;k++)c[j][k]=j*b+k+1;return new e.CalcArray(c)}(Y,Z),c=E(i,1,!1,!0,!0)),l=c.rowCount,m=c.colCount,Y===l&&Z===m&&(d||!f)){for(p=a?l*m:G(l*m),F=0,H=0,I=0,J=0,K=0,L=void 0,j=0;j<l;j++)for(k=0;k<m;k++)q=c[j][k],r=b[j][k],a&&(r=w(r)),F+=q,H+=q*q,I+=r,J+=r*r,K+=q*r;return z=p*H-F*F,o=d?(p*K-F*I)/z:K/H,L=d?(I*H-F*K)/z:0,a&&(o=t(o),L=t(L)),x=[[o,L]],f&&(x[1]=[],x[2]=[],x[3]=[],x[4]=[],M=p*H-F*F,N=p*J-I*I,O=p*K-F*I,P=J-L*I-o*K,Q=O*O/(M*N),p<3?(x[1][0]=B,x[1][1]=B,x[2][1]=B,x[3][0]=B):(x[1][0]=v(P*p/(M*(p-2))),x[1][1]=v(P*H/(M*(p-2))),x[2][1]=v((N-O*O/M)/(p*(p-2))),x[3][0]=1===Q?B:Q*(p-2)/(1-Q)),x[2][0]=Q,x[3][1]=p-2,x[4][0]=N/p-P,x[4][1]=P),new e.CalcArray(x)}if(1===Z&&Y===l||1===Y&&Z===m){for(r=[],q=[],z=1===Z,p=l,o=m,j=0;j<p;j++)r[j]=d?b[j][0]:b[0][j],a&&(r[j]=w(r[j]));for(j=0;j<p;j++)for(q[j]=[],k=0;k<o;k++)q[j][k]=z?c[j][k]:c[k][j];for(R=[],n=0;n<o+1;n++)R[n]=[];for(s=0;s<o+1;s++)for(u=0;u<o+2;u++)R[s][u]=0;for(S=[],s=0;s<o+2;s++)S[s]=0;for(T=f?[]:g,n=0;n<p;n++)for(S[o+1]+=r[n]*r[n],R[0][o+1]+=r[n],S[0]=R[0][o+1],j=0;j<o;j++)for(R[0][j+1]+=q[n][j],R[j+1][0]=R[0][j+1],R[j+1][o+1]+=q[n][j]*r[n],S[j+1]=R[j+1][o+1],k=j;k<o;k++)R[k+1][j+1]+=q[n][j]*q[n][k],R[j+1][k+1]=R[k+1][j+1];if(R[0][0]=p,f){for(s=0;s<o+1;s++)for(T[s]=[],u=0;u<o+1;u++)T[s][u]=0;for(j=0;j<o+1;j++)T[j][j]=1}for(U=d?0:1,j=0;j<o+1;j++){if(0===R[j][j]){for(y=!1,k=j+1;!y&&k<o+1;k++)if(0!==R[k][j]){for(n=0;n<o+2;n++)z=R[j][n],R[j][n]=R[k][n],R[k][n]=z;if(f)for(n=0;n<o+1;n++)z=T[j][n],T[j][n]=T[k][n],T[k][n]=z;y=!0}if(!y)return B}for(C=1/R[j][j],n=U;n<o+2;n++)R[j][n]*=C;if(f)for(n=U;n<o+1;n++)T[j][n]*=C;for(k=U;k<o+1;k++)if(k!==j){for(C=-R[k][j],n=U;n<o+2;n++)R[k][n]+=C*R[j][n];if(f)for(n=U;n<o+1;n++)T[k][n]+=C*T[j][n]}d||(R[0][o+1]=0)}for(x=a?[[],o+1]:[[]],j=0;j<o+1;j++)x[0][j]=a?t(R[o-j][o+1]):R[o-j][o+1];if(f){if(a)x[1]=[],x[2]=[],x[3]=[],x[4]=[];else for(s=1;s<5;s++)for(x[s]=[],u=0;u<o+1;u++)x[s][u]=0;for(V=void 0,W=void 0,X=void 0,W=S[o+1]-S[0]*S[0]/p,V=S[o+1],j=0;j<o+1;j++)V-=R[j][o+1]*S[j];if(X=W-V,x[2][0]=0===W?B:X/W,x[4][0]=X,x[4][1]=V,z=d?p-o-1:p-o,0===z){for(j=0;j<o+1;j++)x[1][j]=B;x[2][1]=B}else{for(D=V/G(z),d||(x[1][o]=A),j=U;j<o+1;j++)x[1][o-j]=v(D*T[j][j]);x[2][1]=v(D)}for(x[3][0]=0===V?B:G(z)*X/(V*G(o)),x[3][1]=G(z),j=2;j<5;j++)for(k=2;k<o+1;k++)x[j][k]=A}return a||(x=[x[0]]),new e.CalcArray(x)}return B}function Rb(a,b){var c,d,e,g,h,i=0,j=0,k=0,l=[];for(e=0;e<f(b);e++)for(h=b[e],l.push(h),g=0;g<f(h);g++)c=h[g],c!==U&&(i+=c,k++);for(d=i/k,e=0;e<f(b);e++)for(h=l[e],g=0;g<f(h);g++)c=h[g],c!==U&&(j+=a?(c-d)*(c-d):u(c-d));return a?D(j):0===k?y:D(j/k)}function Sb(a,b,c,f){b=b!==h?E(b,0,!1,!0,!1):a,c=c!==h?E(c,0,!1,!0,!1):b;var g,i;for(g=0;g<a.rowCount;g++)for(i=0;i<a.colCount;i++)if(!K(a[g][i]))return z;for(g=0;g<b.rowCount;g++)for(i=0;i<b.colCount;i++)if(!K(b[g][i]))return z;for(g=0;g<c.rowCount;g++)for(i=0;i<c.colCount;i++)if(!K(c[g][i]))return z;return d.Common.qc(a,b,c,f,G,e.CalcArray,z,A)}function Tb(a,b,c,f){return b=b!==h?E(b,1,!1,!0,!0):a,c=c!==h?E(c,1,!1,!0,!0):b,d.Common.sc(a,b,c,f,e.CalcArray,B,A)}function Ub(a,b,c){var d,e,g,h,i,j=0,k=0,l=0,m=0,n=f(b),o=0;if(n!==f(c))return A;for(d=0;d<n;d++)e=b[d],g=c[d],e!==U&&g!==U&&(j+=e,k+=g,l+=g*g,m+=g*e,o++);return 0===o?y:o*l-k*k===0?y:(h=(o*m-k*j)/(o*l-k*k),i=j/o-h*(k/o),D(i+h*a))}function Vb(){return M(arguments,!0,1)}function Wb(){return N(arguments,!0,7)}function Xb(a,b){return O([a,b],16)}function Yb(){return M(arguments,!0,4)}function Zb(){return Kb(!0,arguments)}function $b(){return M(arguments,!0,5)}function _b(){return Kb(!1,arguments)}function ac(a,b){return O([a,b],14)}function bc(a,b){return O([a,b],15)}function cc(){var a,b,c,d,e=arguments,h=0,i=0;for(a=0;a<f(e);a++)for(d=e[a],b=0;b<f(d);b++)c=d[b],K(c,!0)||Q(c)?(h+=G(c),i++):(c===g||R(c))&&i++;return 0===i?y:D(h/i)}function dc(a,b,c){var d,e,g,h=0,i=0,j=J(b);if(a.rowCount!==c.rowCount||a.colCount!==c.colCount)return z;for(d=0;d<f(a);d++)if(e=a[d],j&&j(e,!0)&&(g=c[d],g!==U)){if(L(g))return g;h+=g,i++}return 0===i?y:D(h/i)}function ec(a,b,c,d){var f,g,h,i=b,j=b,k=a?i.getRowCount():j.getRowCount(0),l=a?i.getColumnCount():j.getColumnCount(0),m=[];for(f=0;f<k;f++)for(m[f]=[],g=0;g<l;g++)h=a?i.getValue(f,g):j.getValue(0,f,g),m[f][g]=dc(c,h,d);return new e.CalcArray(m)}function fc(a,b,c){if(c!==h){if(a=E(a,0,!0,!1,!1),c=E(c,1,!0,!1,!1,!0,h,!0),c.isError)return c[0]}else{if(a=E(a,1,!0,!1,!1,!0,h,!0),a.isError)return a[0];c=a}return e.Convert.Fh(b)?ec(!1,b,a,c):e.Convert.Ca(b)?ec(!0,b,a,c):dc(a,b,c)}function gc(a){var b,c,d,e,f,g,h,i=arguments,j=0,k=0,l=a.length,m=i.length,n=[];for(n.push(a),d=1;d<m;d+=2){if(e=E(i[d],0,!0,!1,!1),e.isError)return e[0];n.push(e),f=i[d+1],g=J(f),n.push(g)}for(c=0;c<l;c++)if(b=a[c],b!==U){for(h=!0,d=1;d<m&&(g=n[d+1],h=g(n[d][c]),h);d+=2);h&&(j+=b,k++)}return 0===k?y:D(j/k)}function hc(){return O(arguments,12)}function ic(){return O(arguments,13)}function jc(){return Pb(!0,arguments)}function kc(){return Pb(!1,arguments)}function lc(a,b){var c,d,e,f=0,g=[];for(rb(a,g),c=Ya(g),d=k(""+c*b/2),e=d;e<c-d;e++)f+=G(g[e]);return f/(c-2*d)}function mc(a,b){var c,d,f,g,h,i,j,k,l,m,n,o,p=0,q=b.rowCount,r=b.colCount;for(c=0;c<q;c++)for(d=0;d<r;d++)b[c][d]!==U&&p++;for(g=a.rowCount,h=a.colCount,i=[],j=[],k=p+1,p=0,c=0;c<q;c++)for(d=0;d<r;d++)f=b[c][d],f!==U&&(i[p++]=G(f));for(l=i.slice(0),qb(i),c=0;c<k;c++)j[c]=[0];for(c=0;c<g;c++)for(d=0;d<h;d++)if(f=a[c][d],K(f)){for(m=G(f),n=!1,o=0;!n&&o<p;o++)m<=i[o]&&(j[l.indexOf(i[o])][0]++,n=!0);n||j[p][0]++}return new e.CalcArray(j)}function nc(a,b,c){var d=e.CalcArrayHelper;if(e.Convert.Fh(a)){if(1!==d.getLength(a))return z;a=d.getValueByIndex(a,0)}return a=G(a),i(a)?z:Lb(!0,b,a,c)}function oc(){var a,b,c,d,e,g,h=arguments,i=0,j=0,k=0,m=0,n=f(h),o=[];if(n>0){for(g=h[0],o.push(g),d=0;d<f(g);d++)a=g[d],a!==U&&(i+=a,j+=a*a,m++);if(m<=3)return y;if(b=i/m,c=v((m*j-i*i)/(m*(m-1))),0===c)return y;for(e=0;e<n;e++)for(g=o[e],d=0;d<f(g);d++)a=g[d],a!==U&&(k+=l((a-b)/c,4));return D(m*(m+1)*k/((m-1)*(m-2)*(m-3))-3*(m-1)*(m-1)/((m-2)*(m-3)))}}function pc(a,b,c){return _a(a,b,c,!0)}function qc(a,b,c){return _a(a,b,c,!1)}function rc(a,b){return O([a,b],17)}function sc(){return M(arguments,!0,2)}function tc(){return M(arguments,!0,3)}function uc(a){var b,c=0;for(b=0;b<f(a);b++)a[b]!==g&&""!==a[b]||c++;return c}function vc(a,b){var c,d,e=0,h=0===b||"*"===b,i=J(b);for(c=0;c<f(a);c++)d=a[c],h&&d===g||!i||!i(d)||e++;return D(e)}function wc(a,b){var c=0,d,e,g;if(b.isArray||b.isReference){for(g=0;g<f(b);g++){if(e=vc(a,b[g]),L(e))return e;if(d=G(e),i(d))return z;c+=d}return c}return vc(a,b[0])}function xc(a){var b,c,d,e,g,h=arguments,i=0,j=f(a),k=f(h),l=[];for(d=0;d<k;d+=2){if(b=0===d?a:E(h[d],0,!0,!1,!1),b.isError)return b[0];j=b.length,l.push(b),e=J(h[d+1]),l.push(e)}for(c=0;c<j;c++){for(g=!0,d=0;d<k&&(e=l[d+1],g=e&&e(l[d][c]),g);d+=2);g&&i++}return D(i)}function yc(){return Rb(!1,arguments)}function zc(){return Nb(arguments,1,!0)}function Ac(){return N(arguments,!0,8)}function Bc(){return Nb(arguments,0,!0)}function Cc(){return N(arguments,!0,10)}function Dc(){return Nb(arguments,1,!1)}function Ec(){return N(arguments,!0,11)}function Fc(){return Nb(arguments,0,!1)}function Gc(a,b){return Ob(!0,a,b)}function Hc(){return Rb(!0,arguments)}function Ic(a,b,c){var d=ud(a/2);return L(d)?d:-G(d)*(b/v(c))}function Jc(a,b,c){return 1===c?y:b*Jb(a,c-1,2)/v(c)}function Kc(a,b){var c,d,e,g,h=0,i=0,j=0,k=0,l=0,m=f(a);if(m!==f(b))return A;for(g=0;g<m;g++)c=a[g],d=b[g],d!==U&&c!==U&&(h+=c,i+=d,j+=d*d,k+=d*c,l++);return 0===l?y:l*j-i*i===0?y:(e=(l*k-i*h)/(l*j-i*i),D(h/l-e*(i/l)))}function Lc(a,b,c,d){return Qb(!1,a,b,c,d)}function Mc(a,b){var c,d,e,g,h,i=0,j=0,k=0,l=0,m=0,n=f(b);if(n!==f(a))return A;for(c=0;c<n;c++)d=a[c],e=b[c],e===U||d===U||L(e)||L(d)||(g=G(d),h=G(e),i+=g,j+=h,k+=h*h,l+=h*g,m++);return m*k-j*j===0?y:D((m*l-j*i)/(m*k-j*j))}function Nc(a,b,c,d){return Qb(!0,a,b,c,d)}function Oc(a,b){var c,d,e,g,h,i=0,j=0,k=0,l=0,m=0,n=0,o=f(a);if(o!==f(b))return A;for(e=0;e<o;e++)g=a[e],h=b[e],g!==U&&h!==U&&(d=g,c=h,i+=d,j+=d*d,k+=c,l+=c*c,m+=c*d,n++);return n*(n-2)===0||n*l-k*k===0?y:v((n*j-i*i-(n*m-k*i)*(n*m-k*i)/(n*l-k*k))/(n*(n-2)))}function Pc(a){var b,c=a,d=c+5.5;return d-=(c+.5)*w(d),b=1.000000000190015+76.18009172947146/(a+1),b-=86.50532032941678/(a+2),b+=24.01409824083091/(a+3),b-=1.231739572450155/(a+4),b+=.001208650973866179/(a+5),b-=5395239384953e-18/(a+6),w(2.506628274631001*b/c)-d}function Qc(a,b,c,d,e){var f,g,h,i,j,k,l,m,n;return a<d||e<a||d===e?B:(f=(a-d)/(e-d),g=Pc(b+c),h=Pc(b),i=Pc(c),j=w(f),k=w(1-f),l=t(g-h-i+b*j+c*k),m=l*ab(c,b,1-f)/c,n=f<(b+1)/(b+c+2)?m:1-m,D(n))}function Rc(a,b,c,d,e,f){if(a<e||f<a||e===f)return B;var g=(a-e)/(f-e);return d?tb(g,b,c):vb(g,b,c)/(f-e)}function Sc(a,b,c,d,e){var f,g,h,i,j,k,n,o,p,q,r,s,x,y,z,A,B,C,E,F=2.30753,G=.27061,H=.99229,I=.04481,J=3e-308,K=1e-300,L=J,M=1-2.22e-16,N=5,O=6,P=2,Q=ib(b,c);for(a<=.5?(i=a,o=b,q=c,f=0):(i=1-a,o=c,q=b,f=1),r=v(-w(i*i)),A=r-(F+G*r)/(1+(H+I*r)*r),o>1&&q>1?(r=(A*A-3)/6,s=1/(o+o-1),x=1/(q+q-1),n=2/(s+x),z=A*v(n+r)/n-(x-s)*(r+N/O-P/(3*n)),E=o/(o+q*t(z+z))):(r=q+q,x=1/(9*q),x=r*l(1-x+A*v(x),3),x<=0?E=1-t((w((1-i)*q)+Q)/q):(x=(4*o+r-P)/x,E=x<=1?t((w(i*o)+Q)/o):1-P/(x+1))),r=1-o,x=1-q,B=0,j=1,E<L?E=L:E>M&&(E=M),C=m(K,l(10,-13-2.5/(o*o)-.5/(i*i))),y=p=0,g=0;g<1e3;g++){for(A=jb(E,o,q),A=(A-i)*t(Q+r*w(E)+x*w(1-E)),A*B<=0&&(p=m(u(j),J)),k=1,h=0;h<1e3;h++){if(j=k*A,u(j)<p&&(y=E-j,y>=0&&y<=1)){if(p<=C)return 0!==f&&(E=1-E),D((e-d)*E+d);if(u(A)<=C)return 0!==f&&(E=1-E),D((e-d)*E+d);if(0!==y&&1!==y)break}k/=3}if(y===E)return 0!==f&&(E=1-E),D((e-d)*E+d);E=y,B=A}return 0!==f&&(E=1-E),D((e-d)*E+d)}function Tc(a,b,c,d){var e,f,g,h;if(b<a)return B;if(!d){if(e=1-c,f=l(e,b),0===f){if(f=l(c,b),0===f)return B;for(g=0;g<b-a&&f>0;g++)f*=G(b-g)/G(g+1)*e/c;return f}for(g=0;g<a&&f>0;g++)f*=G(b-g)/G(g+1)*c/e;return f}if(b===a)return 1;if(e=1-c,f=l(e,b),0===f){if(f=l(c,b),0===f)return B;for(h=1-f,g=0;g<b-a&&f>0;g++)f*=G(b-g)/G(g+1)*e/c,h-=f;return h<0?0:h}for(h=f,g=0;g<a&&f>0;g++)f*=G(b-g)/G(g+1)*c/e,h+=f;return h}function Uc(a,b,c){if(a+b-1<=0)return B;var d=I(a+b-1,b-1);return L(d)?d:D(G(d)*l(c,b)*l(1-c,a))}function Vc(a,b,c,d){if(a+b-1<=0)return B;if(d)return 1-tb(1-c,a+1,b);var e=l(c,b),f;for(f=0;f<a;f++)e*=(f+b)/(f+1)*(1-c);return e}function Wc(a,b,c){var d,e,f=1-b,g=l(f,a);if(0===g){if(g=l(b,a),0===g)return B;for(d=1-g,e=0;e<a&&d>=c;e++)g*=G(a-e)/G((e+1)*f/b),d-=g;return G(a-e)}for(d=g,e=0;e<a&&d<c;e++)g*=G(a-e)/G((e+1)*b/f),d+=g;return G(e)}function Xc(a,b){var c,d,e,f,g,h,i=w(v(r)),j=1/v(r),k=0,l=a,m=.5*l,n=b%2===0;if(b>1&&(k=t(-m)),g=P(-v(l)),L(g))return g;if(h=G(g),d=n?k:2*h,b>2){if(l=.5*(b-1),e=n?1:.5,m>20){for(c=n?0:i,f=w(m);e<=l;)c=w(e)+c,d+=t(f*e-m-c),e+=1;return d}for(c=n?1:j/v(m),f=0;e<=l;)c*=m/e,f+=c,e+=1;return f*k+d}return d}function Yc(a,b,c){return c?Bb(a,b):Fb(a,b)}function Zc(a,b){var c=ld(1-a,.5*b,2);return G(c)}function $c(a,b){return{ge:function(c){return a-Bb(c,b)}}}function _c(a,b){var c=$c(a,b),d={jl:!1},e=lb(c,.5*b,b,d);return d.jl?A:e}function ad(a,b){var c,d,e,g,h=0,i=a.rowCount,j=a.colCount;if(i!==b.rowCount||j!==b.colCount)return A;if(i>1&&j>1)c=(i-1)*(j-1);else if(i>1&&1===j)c=i-1;else{if(!(1===i&&j>1))return A;c=j-1}for(d=0;d<f(a);d++)if(e=a[d],g=b[d],e!==U&&g!==U){if(0===g)return y;h+=(e-g)*(e-g)/g}return Xc(h,c)}function bd(a,b){var c,d,e,g,h,i,j,k,l=0,m=0,n=0,o=0,p=0,q=f(b);if(q!==f(a))return A;for(h=0,i=0;i<q;i++)j=a[i],k=b[i],j!==U&&k!==U&&(l+=j,m+=k,n+=j*j,o+=k*k,h++);if(h<=1)return y;if(c=l/h,d=m/h,e=v((h*n-l*l)/(h*(h-1))),g=v((h*o-m*m)/(h*(h-1))),0===e||0===g)return y;for(i=0;i<q;i++)j=a[i],k=b[i],j!==U&&k!==U&&(p+=(j-c)*(k-d));return D(p/G((h-1)*e*g))}function cd(a,b,c){var d=t(-b*a);return D(c?1-d:b*d)}function dd(a,b,c){var d=b*a/(b*a+c),e=.5*b,f=.5*c,g=Qc(d,e,f,0,1);return L(g)?g:1-G(g)}function ed(a,b,c,d){if(d){var e=tb(c/(c+b*a),c/2,b/2);return 1-e}return l(b/c,b/2)*l(a,b/2-1)/(l(1+a*b/c,(b+c)/2)*wb(!0,b/2,c/2))}function fd(a,b,c){var d=1-a,e=Sc(1-d,c/2,b/2,0,1);return L(e)?e:(1/G(e)-1)*(c/b)}function gd(a,b,c){var d=1-a,e=Sc(d,c/2,b/2,0,1);return L(e)?e:(1/G(e)-1)*(c/b)}function hd(a){return w((1+a)/(1-a))/2}function id(a){var b=t(2*a)-1,c=t(2*a)+1;return!j(b)&&b>0&&!j(c)&&c>0?1:b/c}function jd(a,b){var c,d,e,f,g,h,i={N:0,M:0,Q:0,kl:0};return pb(i,a),f=i.N-1,1===i.N?y:(c=i.Q/(i.N-1),0===c?y:(i.N=0,i.M=0,i.Q=0,i.kl=0,pb(i,b),g=i.N-1,1===i.N?y:(d=i.Q/(i.N-1),0===d?y:(h=dd(c/d,f,g),L(h)?h:(e=2*(1-G(h)),e>1&&(e=2-e),e)))))}function kd(a,b,c,d){var e,f,g,h,k,m,o,p,q,r,s,x,z,A,C,D,E,F,H,I,J,K,M,N;if(d===!1)return e=l(c,b),i(e)||!j(e)?y:(f=1/(e*gb(b)),g=l(a,b-1),h=t(-(a/c)),k=g*h,f*k);if(I=1/3,J=1e8,K=1e37,M=1e3,N=-88,a/=c,a<=0)return B;if(b>M)return m=3*v(b)*(l(a/b,I)+1/(9*b)-1),H=sd(m,0,1,!0),L(H)?H:G(H);if(a>J)return 1;if(a<=1||a<b){if(H=md(b+1),L(H))return H;x=b*w(a)-a-G(H),z=1,F=1,C=b;do C+=1,z=z*a/C,F+=z;while(z>2.220446049250313e-16);x+=w(F),F=0,x>=N&&(F=t(x))}else{if(H=md(b),L(H))return H;for(x=b*w(a)-a-G(H),C=1-b,D=C+a+1,z=0,m=1,o=a,p=a+1,q=a*D,F=p/q;;){if(C+=1,D+=2,z+=1,E=C*z,r=D*p-E*m,s=D*q-E*o,u(s)>0){if(A=r/s,u(F-A)<=n(2.220446049250313e-16,2.220446049250313e-16*A))break;F=A}m=p,o=q,p=r,q=s,u(r)>=K&&(m/=K,o/=K,p/=K,q/=K)}x+=w(F),F=1,x>=N&&(F=1-t(x))}return F}function ld(a,b,c){var d,e,f,g,h,i,j,k,m,n,o,p,q,r,s,x,y,z,A,B=4.67,C=6.66,D=6.73,E=13.32,F=60,H=70,I=84,J=105,K=120,M=127,N=140,O=1175,P=210,Q=252,R=2264,S=294,U=346,V=420,W=462,X=606,Y=672,Z=707,$=735,_=889,aa=932,ba=966,ca=1141,da=1182,ea=1278,fa=1740,ga=2520,ha=5040,ia=5e-7,ja=.01,ka=5e-7,la=20,ma=2e-6,na=.999998;if(a<ma)return 0;if(a>na)return T;if(j=2*b,f=b-1,A=md(b),L(A))return A;if(h=G(A),j<-1.24*w(a)){if(g=l(a*b*t(h+.6931471805599453*b),1/b),g<ia)return NaN}else if(j>.32){if(A=td(a,0,1),L(A))return A;y=G(A),i=.222222/j,g=j*l(y*v(i)+1-i,3),g>2.2*j+6&&(g=-2*(w(1-a)-f*w(.5*g)+h))}else{g=.4,d=w(1-a)+h+.6931471805599453*f;do m=g,i=1+g*(B+g),k=g*(D+g*(C+g)),x=-.5+(B+2*g)/i-(D+g*(E+3*g))/k,g-=(1-t(d+.5*g)*k/i)/x;while(u(m/g-1)>ja)}for(z=1;z<=la;z++){if(m=g,i=.5*g,A=kd(i,b,1,!0),L(A))return A;if(k=a-G(A),x=k*t(.6931471805599453*b+h+i-f*w(g)),e=x/g,d=.5*x-e*f,n=(P+d*(N+d*(J+d*(I+d*(H+F*d)))))/V,o=(V+d*($+d*(ba+d*(ca+ea*d))))/ga,p=(P+d*(W+d*(Z+aa*d)))/ga,q=(Q+d*(Y+da*d)+f*(S+d*(_+fa*d)))/ha,r=(I+R*d+f*(O+X*d))/ga,s=(K+f*(U+M*f))/ha,g+=x*(1+.5*x*n-e*f*(n-e*(o-e*(p-e*(q-e*(r-e*s)))))),u(m/g-1)>ka)return.5*c*g}return.5*c*g}function md(a){return hb(a)}function nd(a,b,c,d){return a>n(b,c)||a<m(0,b-d+c)||b>d||c>d?B:Gb(a,b,c,d)}function od(a,b,c,d,e){var f,g;if(a>n(b,c)||a<m(0,b-d+c)||b>d||c>d)return B;if(e){for(f=0,g=0;g<=a;g++)f+=Gb(g,b,c,d);return f}return Gb(a,b,c,d)}function pd(a,b,c){return P((w(a)-b)/c)}function qd(a,b,c,d){if(d)return P((w(a)-b)/c);var e=(w(a)-b)/c;return.3989422804014327*t(-(e*e)/2)/c/a}function rd(a,b,c){var d,e=ud(a);return L(e)?e:(d=G(e),D(t(b+c*d)))}function sd(a,b,c,d){return d?P((a-b)/c):D(t(-((a-b)*(a-b))/(2*c*c))/(v(2*r)*c))}function td(a,b,c){var d,e,f,g,h,i=a-.5;if(u(i)<=.42)d=i*i,e=i*(((-25.44106049637*d+41.39119773534)*d-18.61500062529)*d+2.50662823884)/((((3.13082909833*d-21.06224101826)*d+23.08336743743)*d+-8.4735109309)*d+1);else{if(d=a,i>0&&(d=1-a),!(d>2.220446049250313e-16))return d>1e-300?(e=-2*w(a),d=w(6.283185307179586*e),d=d/e+(2-d)/(e*e)+(-14+6*d-d*d)/(2*e*e*e),e=v(e*(1-d)),i<0?-e:e):i<0?-T:T;d=v(-w(d)),e=(((2.32121276858*d+4.85014127135)*d-2.29796479134)*d-2.78718931138)/((1.63706781897*d+3.54388924762)*d+1),i<0&&(e=-e)}return f=(e-0)/1,g=.3989422804014327*t(-.5*f*f)/1,h=sd(e,0,1,!0),L(h)?h:(e-=(h-a)/g,b+c*e)}function ud(a){return td(a,0,1)}function vd(a,b){return b?kb(a):t(-l(a,2)/2)/v(2*r)}function wd(a,b){var c,d,e,g,h=0,i=0,j=0,k=0,l=0,m=f(a);if(m!==f(b))return A;for(c=0,d=0;d<m;d++)e=a[d],g=b[d],S(e)&&S(g)&&(h+=e,i+=g,j+=e*e,k+=g*g,l+=e*g,c++);return c*j-h*h===0||c*k-i*i===0?y:(c*l-h*i)/v((c*j-h*h)*(c*k-i*i))}function xd(a,b){var c,d,e,g,h,i=0,j=0,k=0,l=0,m=0,n=0,o=f(b);if(o!==f(a))return A;for(c=0;c<o;c++)d=b[c],e=a[c],S(d)&&S(e)&&(i+=d,j+=e,k+=d*d,l+=e*e,m+=d*e,n++);return g=v((n*k-i*i)*(n*l-j*j)),0===g?y:(h=(n*m-i*j)/g,D(h*h))}function yd(a,b,c){var d,f=0;if(c)for(d=0;d<=a;d++)f+=t(-b)*l(b,d)/e.Functions.tk(d);else f=t(-b)*l(b,a)/e.Functions.tk(a);return D(f)}function zd(a,b,c,d){var e,g,j,k,l,m,n=c;if(d!==h&&(n=G(d),i(n)))return z;if(e=0,g=0,j=f(a),j!==f(b))return A;for(k=0;k<j;k++)if(l=a[k],m=b[k],l!==U&&m!==U){if(m<=0||1<m)return B;c<=l&&l<=n&&(e+=m),g+=m}return 1!==g?B:e}function Ad(){var a,b,c,d,e,g,h=arguments,i=0,j=0,k=0,m=0,n=[];for(c=0;c<f(h);c++)for(g=h[c],n.push(g),d=0;d<f(g);d++)e=g[d],e!==U&&(i+=e,j+=e*e,m++);if(m<=2)return y;if(a=i/m,b=v((m*j-i*i)/(m*(m-1))),0===b)return y;for(c=0;c<f(n);c++)for(g=n[c],d=0;d<f(g);d++)e=g[d],e!==U&&(k+=l((e-a)/b,3));return D(m*k/((m-1)*(m-2)))}function Bd(){var a,b,c,d,e,g,h,i,j,k,m=arguments,n=0,o=0,p=[];for(c=0;c<f(m);c++){if(g=m[c],1===f(m)&&0===f(g))return C;for(d=0;d<f(g);d++)e=g[d],h={value:0},e!==U&&"boolean"!=typeof e&&W(e,h)&&(n+=h.value,o++,p.push(h.value))}if(o<=2)return y;for(a=n/o,i=0,j=0,k=0,c=0;c<p.length;c++)k=p[c]-a,j+=l(k,2),i+=l(k,3);return b=v(j/o),0===b?y:D(i/o/b/b/b)}function Cd(a,b,c){return(a-b)/c}function Dd(a,b,c){var d,e,f,g,h,i,j,k,l,m;if(1!==c&&2!==c)return B;if(d=b,e=a/v(d),f=d/(d+a*a),g=d-2,h=d%2,i=1,j=1,d=1,k=2+h,l=k,g>=2)for(m=F(k);m<=g&&(j=j*f*(l-1)/l,i+=j,i!==d);m+=2)d=i,l+=2;return 1!==h?c*(1-(.5+.5*e*v(f)*i)):(1===b&&(i=0),c*(1-(.5+.3183098862*(e*f*i+s(e)))))}function Ed(a,b,c){return Hb(a,b,c?4:3)}function Fd(a,b){return Dd(a,b,1)}function Gd(a,b){return Dd(a,b,2)}function Hd(a,b){var c,d,e,f,g,h,i,j,k,m,n,o=1e-12,p=a/2,r=p;if(b>1e20)return ud(p);if(r<.5?(m=0,h=2*r):(m=1,h=2*(1-r)),u(b-2)<o)i=h>0?v(2/(h*(2-h))-2):T;else if(G(b)<1+o)h>0?(g=1.5707963267948966*(h+1),i=-q(g)):i=T;else{if(c=1/(b-.5),d=48/(c*c),e=((20700*c/d-98)*c-16)*c+96.36,f=((94.5/(d+e)-3)/d+1)*v(1.5707963267948966*c)*b,k=l(f*h,2/b),k>.05+c){if(n=ud(.5*h),L(n))return n;j=G(n),k=j*j,b<5&&(e+=.3*(b-4.5)*(j+.6)),e=(((.05*f*j-5)*j-7)*j-2)*j+d+e,k=(((((.4*k+6.3)*k+36)*k+94.5)/e-k-3)/d+1)*j,k=c*k*k,k=k>.002?t(k)-1:.5*k*k+k}else k=((1/(((b+6)/(b*k)-.089*f-.822)*(b+2)*3)+.5/(b+4))*k-1)*(b+1)/(b+2)+1/k;i=v(b*k)}return 0!==m&&(i=-i),D(i)}function Id(a,b){return a<.5?-Jb(1-a,b,4):Jb(a,b,4)}function Jd(a,b,c,d){var e,g,h,k,l,m,n,o,p,q,r,s,t,w,x,z,C,D,E,F,H={N:0,M:0,Q:0,kl:0};if(1!==c&&2!==c)return B;if(1===d){if(q=void 0,r=void 0,s=void 0,t=void 0,w=void 0,x=void 0,z=void 0,f(a)!==f(b))return A;for(C=f(a),z=0;z<C;z++)a[z]=a[z]-b[z];for(t=w=x=q=0,z=0;z<C;z++)D=a[z],r=D-t,s=r/(x+1),t+=s,w+=x*r*s,x++,q+=D;if(x-1===0||0===x)return y;if(k=v(w/(x-1)),i(k)||!j(k))return B;e=q/x,h=e/(k/v(x)),n=x-1}else H.N=0,H.M=0,H.Q=0,H.kl=0,mb(H,a),l=H.Q/(H.N-1),e=H.kl/H.N,o=H.N,H.N=0,H.M=0,H.Q=0,H.kl=0,mb(H,b),m=H.Q/(H.N-1),g=H.kl/H.N,p=H.N,2!==d?(E=l/o/(l/o+m/p),n=1/(E*E/G(o-1)+(1-E)*(1-E)/G(p-1))):n=G(o+p-2),h=(e-g)/v(l/G(o)+m/G(p));return h=u(h),F=ob(.5*n,.5,n/(n+h*h)),L(F)?F:.5*c*G(F)}function Kd(a,b,c,d){var e=t(-l(a/c,b));return D(d?1-e:b/l(c,b)*l(a,b-1)*e)}function Ld(a,b,c){var d,e,g,j,k,l,m=0,n=0,o=0,p=0;if(c!==h&&(m=G(c),i(m)))return z;for(j=0;j<f(a);j++)k=a[j],k!==U&&(g=G(k),n+=g,o+=g*g,p++);return 0===p?A:1===p?y:(d=n/p,e=c!==h?m:v((p*o-n*n)/(p*(p-1))),0===e?y:(l=P((d-b)/(e/v(p))),L(l)?l:D(1-G(l))))}function Md(a,b){a=G(F(a)),b=G(F(b));var c=1,d;if(a<0||b<0||a<b)return B;for(d=a-b+1;d<=a;d++)c*=d;return D(c)}function Nd(a,b){return Ob(!1,a,b)}function Od(){var a,b,c,d=0,g=[],h=sb(arguments),i=f(h);for(a=0;a<i;a++){for(b=0,c=a+1;c<i;c++)h[a]===h[c]&&b++;b>d?(d=b,g=[],g.push([h[a]])):b===d&&g.push([h[a]])}return 0===d?A:new e.CalcArray(g)}function Pd(a,b){return O([a,b],18)}function Qd(a,b){return O([a,b],19)}function Rd(a,b,c){return a.rowCount>1||a.colCount>1?z:Lb(!1,b,a[0],c)}function Sd(a,b){var c,d=b;return a>=0&&a<2147483647&&d>=0&&d<2147483647&&(a=Math.pow(k(a),k(d)),c=Math.abs(a),c<=1.79769e308)?c>=2.2250738585072014e-308?a:0:B}function Td(a){var b,c;return""===a?z:(b={value:0},W(a,b)===!0?(a=.3989422804014327*Math.exp(.5*-(b.value*b.value)),c=Math.abs(a),c<=1.79769e308?c>=2.2250738585072014e-308?a:0:B):z)}function Ud(a,b,c,d){var e,f,g,h,i,j,k,l,m;if(""===a)return z;e=[1,1,2,6,24,120,720,5040,40320,362880,3628800,39916800,479001600,6227020800,87178291200,1307674368e3,20922789888e3,355687428096e3,6402373705728e3,0x1b02b9306890000,243290200817664e4,5109094217170944e4,0x3ceea4c2b3e0d80000,2.585201673888498e22,6.204484017332394e23,1.5511210043330986e25,4.0329146112660565e26,1.0888869450418352e28,3.0488834461171387e29,8.841761993739702e30,2.6525285981219107e32,8.222838654177922e33,2.631308369336935e35,8.683317618811886e36,2.9523279903960416e38,1.0333147966386145e40,3.7199332678990125e41,1.3763753091226346e43,5.230226174666011e44,2.0397882081197444e46,8.159152832478977e47,3.345252661316381e49,1.40500611775288e51,6.041526306337383e52,2.658271574788449e54,1.1962222086548019e56,5.502622159812089e57,2.5862324151116818e59,1.2413915592536073e61,6.082818640342675e62,3.0414093201713376e64,1.5511187532873822e66,8.065817517094388e67,4.2748832840600255e69,2.308436973392414e71,1.2696403353658276e73,7.109985878048635e74,4.0526919504877214e76,2.3505613312828785e78,1.3868311854568984e80,8.32098711274139e81,5.075802138772248e83,3.146997326038794e85,1.98260831540444e87,1.2688693218588417e89,8.247650592082472e90,5.443449390774431e92,3.647111091818868e94,2.4800355424368305e96,1.711224524281413e98,1.1978571669969892e100,8.504785885678623e101,6.1234458376886085e103,4.4701154615126844e105,3.307885441519386e107,2.48091408113954e109,1.8854947016660504e111,1.4518309202828587e113,1.1324281178206297e115,8.946182130782976e116,7.156945704626381e118,5.797126020747368e120,4.753643337012842e122,3.945523969720659e124,3.314240134565353e126,2.81710411438055e128,2.4227095383672734e130,2.107757298379528e132,1.8548264225739844e134,1.650795516090846e136,1.4857159644817615e138,1.352001527678403e140,1.2438414054641308e142,1.1567725070816416e144,1.087366156656743e146,1.032997848823906e148,9.916779348709496e149,9.619275968248212e151,9.426890448883248e153,9.332621544394415e155,9.332621544394415e157,9.42594775983836e159,9.614466715035127e161,9.90290071648618e163,1.0299016745145628e166,1.081396758240291e168,1.1462805637347084e170,1.226520203196138e172,1.324641819451829e174,1.4438595832024937e176,1.588245541522743e178,1.7629525510902446e180,1.974506857221074e182,2.2311927486598138e184,2.5435597334721877e186,2.925093693493016e188,3.393108684451898e190,3.969937160808721e192,4.684525849754291e194,5.574585761207606e196,6.689502913449127e198,8.094298525273444e200,9.875044200833601e202,1.214630436702533e205,1.506141741511141e207,1.882677176888926e209,2.372173242880047e211,3.0126600184576594e213,3.856204823625804e215,4.974504222477287e217,6.466855489220474e219,8.47158069087882e221,1.1182486511960043e224,1.4872707060906857e226,1.9929427461615188e228,2.6904727073180504e230,3.659042881952549e232,5.012888748274992e234,6.917786472619489e236,9.615723196941089e238,1.3462012475717526e241,1.898143759076171e243,2.695364137888163e245,3.854370717180073e247,5.5502938327393044e249,8.047926057471992e251,1.1749972043909107e254,1.727245890454639e256,2.5563239178728654e258,3.80892263763057e260,5.713383956445855e262,8.62720977423324e264,1.3113358856834524e267,2.0063439050956823e269,3.0897696138473508e271,4.789142901463394e273,7.471062926282894e275,1.1729568794264145e278,1.853271869493735e280,2.9467022724950384e282,4.7147236359920616e284,7.590705053947219e286,1.2296942187394494e289,2.0044015765453026e291,3.287218585534296e293,5.423910666131589e295,9.003691705778438e297,1.503616514864999e300,2.5260757449731984e302,4.269068009004705e304,7.257415615307999e306];function n(a){return a<171?a<0?NaN:e[a]:1/0}function o(a,b){var c,d,e,f;if(a<=b)return a===b&&b>=0?1:z;if(a<2*b&&(b=a-b),b<2)return b>=0?0!==b?a:1:z;if(a<171)return n(a)/(n(b)*n(a-b));if(b>514)return z;if(a>8192){for(c=a,d=2;d<=b;d++)c*=--a/d;return c}for(e=a,a--,f=2;;)switch(b-f){case-1:return e;case 0:return a/f*e;case 1:return a*(a-1)/(f*(f+1))*e;case 2:return a*(a-1)*(a-2)/(f*(f+1)*(f+2))*e;default:e=a*(a-1)*(a-2)*(a-3)/(f*(f+1)*(f+2)*(f+3))*e,f+=4,a-=4}}if(f={value:0},a=X(a,f),g={value:0},b=W(b,g),h={value:0},c=X(c,h),4===arguments.length&&d?(i={value:0},d=X(d,i)):(d=c,i=h),a===!0&&b===!0&&c===!0&&d===!0){if(a=f.value,b=g.value,c=h.value,d=i.value,a>=0&&a<=2147483646&&b>=0&&b<=1&&c>=0&&c<=a&&d>=c&&d<=a){if(j=a*b*(1-b),j>100)return j=Math.sqrt(j),b*=a,Za((d-b+.5)/j)-Za((c-1-b+.5)/j);if(k=o(a,c)*Math.pow(b,c)*Math.pow(1-b,a-c),0===k)return 0;for(l=k,j=k,b/=1-b,m=c;m<d;m++)j*=(a-m)/(m+1)*b,l+=j;return l<=1?l:1}return B}return z}function Vd(a){var b,c,d;if(a>0)a=Math.exp(hb(a));else{if(b=Math.abs(k(a)),c=a+b,0===c)return B;a=Math.pow(-1,b-1)*Math.exp(hb(-c))*Math.exp(hb(1+c))/Math.exp(hb(b+1-c))}return d=Math.abs(a),d<=1.79769e308?d>=2.2250738585072014e-308?a:0:B}function Wd(a){a=Za(a)-.5;var b=Math.abs(a);return b<=1.79769e308?b>=2.2250738585072014e-308?a:0:B}Y={Hi:7},Z={_i:!0,Hi:7},$={Hi:2},_={Hi:2,aj:"< 1"},aa={Hi:2,aj:"< 0"},ba={Hi:2,aj:["< 1","> 10000000000"]},ca={Hi:0},da={Hi:0,aj:["< 0",">= 1"]},ea={Hi:0,aj:["<= 0",">= 1"]},fa={Hi:0,aj:"<= 0"},ga={_i:0,Hi:0},ha={_i:1,Hi:0},ia={Hi:0,aj:["< 0","> 1"]},ja={Hi:0,aj:"< 0"},ka={Hi:0,aj:["< 1","> 10000000000"]},la={Hi:0,aj:["< 1",">= 10000000000"]},ma={Hi:4,Ii:1,Ji:!0,Ki:!0,Mi:!0},na={Hi:4,Ii:1,Ji:!0,Ki:!0},oa={Hi:4,Ii:1,Ji:!0,Ki:!0,Mi:!0,Xi:!0},pa={Hi:4,Ii:1,Ji:!0,Ki:!0,Xi:!0},qa={Hi:4,Ii:1,Ki:!0,Mi:!0},ra={Hi:4,Ii:0,Ji:!0},sa={Hi:4,Ii:1,Ji:!0},ta={Hi:4,Ii:1,Ji:!0,Ki:!0,Li:!0},ua={Hi:4,Ii:0,Ji:!0,Ki:!0,Xi:!0},va={Hi:4,Ii:0,Ji:!0,Ki:!0,Mi:!0,Xi:!0},wa={Hi:4,Ii:0,Ji:!0,Ki:!0},xa={Hi:4,Ii:1,Ki:!0,Li:!0},ya=[ma,{Hi:2}],za=[na,$],Aa=[ma,ca,{_i:3,Hi:2,aj:"< 1"}],Ba=[ea,fa,_],Ca=[ea,fa,fa,ga,ha],Da=[aa,aa,ia,Y],Ea=[aa,ia,ea],Fa=[ja,ka],Ga=[ia,ba],Ha=[na,na],Ia=[ja,fa,Y],Ja=[ja,la,la],Ka=[ia,la,la],La=[ma,ma],Ma=[ja,fa,fa,Y],Na=[ia,fa,fa],Oa=[ia,ca,fa],Pa=[ca,ca,fa,Y],Qa=[sa,sa],Ra=[aa,ja,Y],Sa=[ja,_],Ta=[ta,ta,$,{Hi:2,aj:["< 1","> 3"]}],Ua=[ja,fa,fa,Y],Va=[ma,ca],Wa=[xa,{},Z,{_i:!1,Hi:7}],Xa=[{},ma,ga],V("TREND",Sb,1,4,[{Hi:4,Ii:0,Ki:!0},{},{},Z],[0,1,2],[0,1,2],{bk:[1,2,3]}),V("GROWTH",Tb,1,4,[xa,{},{},Z],"!= 3","!= 3",{bk:[1,2,3]}),V("FORECAST",Ub,3,3,[ca,na,na],"> 0","> 0"),V("AVERAGE",Vb,1,h,h,-1,-1),V("STDEV",Wb,1,h,h,-1,-1),V("STDEV.S",Wb,1,h,h,-1,-1),V("PERCENTILE",Xb,2,2,h,0,0),V("PERCENTILE.INC",Xb,2,2,h,0,0),V("MAX",Yb,1,h,h,-1,-1),V("MAXA",Zb,1,h,ua,-1,-1),V("MIN",$b,1,h,h,-1,-1),V("MINA",_b,1,h,ua,-1,-1),V("LARGE",ac,2,2,za,0,0),V("SMALL",bc,2,2,za,0,0),V("AVERAGEA",cc,1,h,va,-1,-1),V("AVERAGEIF",fc,2,3,h,[0,2],[0,2],{bk:2}),V("AVERAGEIFS",gc,3,h,ma,[0,"%= 1"],[0,"%= 1"]),V("MEDIAN",hc,1,h,oa,-1,-1),V("MODE",ic,1,h,oa,-1,-1),V("MODE.SNGL",ic,1,h,oa,-1,-1),V("GEOMEAN",jc,1,h,pa,-1,-1),V("HARMEAN",kc,1,h,oa,-1,-1),V("TRIMMEAN",lc,2,2,[ma,da],0,0),V("FREQUENCY",mc,2,2,[qa,qa],-1,-1),V("RANK",nc,2,3,Xa,[0,1],[0,1],{bk:2}),V("RANK.EQ",nc,2,3,Xa,[0,1],1,{bk:2}),V("KURT",oc,1,h,oa,-1,-1),V("PERCENTRANK",pc,2,3,Aa,0,0,{bk:2}),V("PERCENTRANK.INC",pc,2,3,Aa,0,0,{bk:2}),V("PERCENTRANK.EXC",qc,2,3,Aa,0,0,{bk:2}),V("QUARTILE",rc,2,2,ya,0,0),V("QUARTILE.INC",rc,2,2,ya,0,0),V("COUNT",sc,1,h,h,-1,-1,{gk:-1}),V("COUNTA",tc,1,h,h,-1,-1,{gk:-1}),V("COUNTBLANK",uc,1,1,ra,-1,-1),V("COUNTIF",wc,2,2,[ra,ra],0,0),V("COUNTIFS",xc,2,h,wa,"%= 0","%= 0"),V("AVEDEV",yc,1,h,oa,-1,-1),V("STDEVA",zc,1,h,va,-1,-1),V("STDEVP",Ac,1,h,h,-1,-1),V("STDEV.P",Ac,1,h,h,-1,-1),V("STDEVPA",Bc,1,h,va,-1,-1),V("VAR",Cc,1,h,h,-1,-1),V("VAR.S",Cc,1,h,h,-1,-1),V("VARA",Dc,1,h,va,-1,-1),V("VARP",Ec,1,h,h,-1,-1),V("VAR.P",Ec,1,h,h,-1,-1),V("VARPA",Fc,1,h,va,-1,-1),V("COVAR",Gc,2,2,Ha,-1,-1),V("COVARIANCE.P",Gc,2,2,Ha,-1,-1),V("DEVSQ",Hc,1,h,oa,-1,-1),V("CONFIDENCE",Ic,3,3,Ba),V("CONFIDENCE.NORM",Ic,3,3,Ba),V("CONFIDENCE.T",Jc,3,3,Ba),V("INTERCEPT",Kc,2,2,[oa,oa],-1,-1),V("LINEST",Lc,1,4,Wa,[0,1],[0,1],{bk:[1,2,3],fk:1}),V("SLOPE",Mc,2,2,[oa,oa],-1,-1,{fk:1}),V("LOGEST",Nc,1,4,Wa,[0,1],[0,1],{bk:[1,2,3]}),V("STEYX",Oc,2,2,Ha,-1,-1),V("BETADIST",Qc,3,5,[ca,fa,fa,ga,ha],h,h,{bk:[3,4]}),V("BETA.DIST",Rc,4,6,[ca,fa,fa,Y,ga,ha],h,h,{bk:[5,6]}),V("BETAINV",Sc,3,5,Ca,h,h,{bk:[3,4]}),V("BETA.INV",Sc,3,5,Ca,h,h,{bk:[3,4]}),V("BINOMDIST",Tc,4,4,Da),V("BINOM.DIST",Tc,4,4,Da),V("NEGBINOMDIST",Uc,3,3,[$,$,da]),V("NEGBINOM.DIST",Vc,4,4,[aa,_,da,Y]),V("CRITBINOM",Wc,3,3,Ea),V("BINOM.INV",Wc,3,3,Ea),V("CHIDIST",Xc,2,2,Fa),V("CHISQ.DIST.RT",Xc,2,2,Fa),V("CHISQ.DIST",Yc,3,3,[ja,ba,Y]),V("CHIINV",Zc,2,2,Ga),V("CHISQ.INV.RT",Zc,2,2,Ga),V("CHISQ.INV",_c,2,2,Ga),V("CHITEST",ad,2,2,Ha,-1,-1),V("CHISQ.TEST",ad,2,2,Ha,-1,-1),V("CORREL",bd,2,2,[oa,oa],-1,-1),V("EXPONDIST",cd,3,3,Ia),V("EXPON.DIST",cd,3,3,Ia),V("FDIST",dd,3,3,Ja),V("F.DIST",ed,4,4,[ja,ka,ka,Y]),V("F.DIST.RT",dd,3,3,Ja),V("FINV",fd,3,3,Ka),V("F.INV.RT",fd,3,3,Ka),V("F.INV",gd,3,3,[ia,ba,ba]),V("FISHER",hd,1,1,{Hi:0,aj:["<= -1",">= 1"]}),V("FISHERINV",id,1,1,ca),V("FTEST",jd,2,2,La,-1,-1),V("F.TEST",jd,2,2,La,-1,-1),V("GAMMADIST",kd,4,4,Ma),V("GAMMA.DIST",kd,4,4,Ma),V("GAMMAINV",ld,3,3,Na),V("GAMMA.INV",ld,3,3,Na),V("GAMMALN",md,1,1,fa),V("GAMMALN.PRECISE",md,1,1,fa),V("HYPGEOMDIST",nd,4,4,[aa,aa,aa,aa]),V("HYPGEOM.DIST",od,5,5,[aa,aa,aa,aa,Y]),V("LOGNORMDIST",pd,3,3,[fa,ca,fa]),V("LOGNORM.DIST",qd,4,4,[fa,ca,fa,Y]),V("LOGINV",rd,3,3,Oa),V("LOGNORM.INV",rd,3,3,Oa),V("NORMDIST",sd,4,4,Pa),V("NORM.DIST",sd,4,4,Pa),V("NORMINV",td,3,3,Oa),V("NORM.INV",td,3,3,Oa),V("NORMSDIST",P,1,1,ca,h,h),V("NORMSINV",ud,1,1,ia),V("NORM.S.INV",ud,1,1,ia),V("NORM.S.DIST",vd,2,2,[ca,Y]),V("PEARSON",wd,2,2,Qa,-1,-1),V("RSQ",xd,2,2,Qa,[0,1],[0,1]),V("POISSON",yd,3,3,Ra),V("POISSON.DIST",yd,3,3,Ra),V("PROB",zd,3,4,[ta,na,ca],[0,1],[0,1],{bk:3}),V("SKEW",Ad,1,h,oa,-1,-1),V("STANDARDIZE",Cd,3,3,[ca,ca,fa]),V("TDIST",Dd,3,3,[ja,_,$]),V("T.DIST",Ed,3,3,[ja,_,Y]),V("T.DIST.RT",Fd,2,2,Sa),V("T.DIST.2T",Gd,2,2,Sa),V("TINV",Hd,2,2,Ga),V("T.INV.2T",Hd,2,2,Ga),V("T.INV",Id,2,2,[{Hi:0,aj:["<= 0","> 1"]},ba]),V("TTEST",Jd,4,4,Ta,[0,1],[0,1]),V("T.TEST",Jd,4,4,Ta,[0,1],[0,1]),V("WEIBULL",Kd,4,4,Ua),V("WEIBULL.DIST",Kd,4,4,Ua),V("ZTEST",Ld,2,3,Va,0,0,{bk:2}),V("Z.TEST",Ld,2,3,Va,0,0,{bk:2}),V("PERMUT",Md,2,2),V("COVARIANCE.S",Nd,2,2,Ha,-1,-1),V("MODE.MULT",Od,1,h,pa,-1,-1),V("PERCENTILE.EXC",Pd,2,2,[ma,ea],0,0),V("QUARTILE.EXC",Qd,2,2,[ua,$],0,0),V("RANK.AVG",Rd,2,3,[ta,sa,ga],[0,1],1,{bk:2}),V("PERMUTATIONA",Sd,2,2,[ca,ca]),V("PHI",Td,1,1),V("BINOM.DIST.RANGE",Ud,3,4,[{_i:0},{_i:0},{_i:0}]),V("GAMMA",Vd,1,1,fa),V("GAUSS",Wd,1,1,ca),V("SKEW.P",Bd,1,h,va,-1,-1)},CalcEngine:function(a,b){a.exports=c("@grapecity/js-calc");
},Common:function(a,b){a.exports=c("@grapecity/js-sheets-common")}}),a.exports=d.Spread.CalcEngine.AdvancedFunctions},"./node_modules_local/@grapecity/js-calc-advancedfunctions/index.js":function(a,b,c){a.exports=c("./node_modules_local/@grapecity/js-calc-advancedfunctions/dist/gc.spread.calcEngine.advancedfunctions.js")},"@grapecity/js-calc":function(a,b){a.exports=GC.Spread.CalcEngine},"@grapecity/js-sheets-common":function(a,b){a.exports=GC.Spread}});