<!--
 * @Description: 指标选择及推荐
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 14:59:19
 * @LastEditors: yuyong<PERSON>e
 * @LastEditTime: 2021-11-24 16:21:24
-->
<template>
  <div class="_indexTree">
    <!-- 推荐关注 -->
    <a-spin :spinning="recommendListLoading">
      <template v-if="recommendList.length || recommendListLoading">
        <div class="recommend" style="min-height: 157px;">
          <div class="_title">推荐关注</div>
          <div
            class="item _flex"
            v-for="(item, index) in recommendList"
            :key="index"
          >
            <div class="_flex">
              <!-- <a-tooltip placement="top">
                <template slot="title">
                  <span
                    >{{ item.dwppCmTfIndexLibrary.label1 }},{{
                      item.dwppCmTfIndexLibrary.label2
                    }}</span
                  >
                </template>
                <span class="sign">{{ item.dwppCmTfIndexLibrary.label2 }}</span>
              </a-tooltip> -->
              <a-tooltip placement="top">
                <template slot="title">
                  <span class="title"
                    >{{ item.businessSegments }}-{{
                      (item.wdInCardName ? item.wdInCardName + " - " : "") +
                        item.displayIndexName
                    }}</span
                  >
                </template>
                <span class="title"
                  >{{ item.businessSegments }}-{{
                    (item.wdInCardName ? item.wdInCardName + " - " : "") +
                      item.displayIndexName
                  }}</span
                >
              </a-tooltip>
            </div>
            <a-icon
              type="plus-circle"
              theme="filled"
              @click="saveCurrentRoleList(item)"
            />
          </div>
        </div>
      </template>
    </a-spin>
    <!-- 指标选择树 -->
    <div class="indexList">
      <div class="_top">
        <!-- 定制指标 -->
        <a-button
          type="primary"
          style="width: 100%;text-align: left;"
          @click="showModal"
        >
          <a-icon type="setting" theme="filled" />
          <span>定制指标</span>
          <a-icon type="plus" />
        </a-button>
      </div>
      <div class="_bottom">
        <!-- 指标数 -->
        <OwnTree ref="ownTree" :treeData="treeData" />
      </div>
    </div>
    <!-- 指标勾选弹窗 -->
    <Modal
      @savedData="getCurrentUserTree"
      :companyName="companyName"
      :signOrgId="signOrgId"
      pageClass="indexComparison2"
      ref="cAddCardModal"
    />
  </div>
</template>
<script>
import OwnTree from "./ownTree.vue";
import Modal from "../Card/modal.vue";
import request from "@/utils/requestHttp";
import { Empty } from "ant-design-vue";
export default {
  components: { OwnTree, Modal },
  props: {
    companyName: String,
    recommendList: Array,
    recommendListLoading: Boolean,
    signOrgId: String,
    searchForm: Object
  },
  data() {
    return {
      treeData: [],
      SYS_NAME: window.system
    };
  },
  beforeCreate() {
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  },
  mounted() {
    this.getCurrentUserTree();
  },
  methods: {
    // 获取当前角色保存的指标树
    getCurrentUserTree() {
      request(`/api/smc2/newuserIndexRelation/searchUserHB`, {
        method: "POST",
        body: {
          signOrgId: this.signOrgId
        }
      }).then(res => {
        if (Array.isArray(res) && res.length) {
          const indexTreeData = this.arrayToTree(res);
          this.treeData = indexTreeData;
          // 如果当前选中的indexId不在指标树列表里需要重新选择一个指标
          if (
            res.filter(item => item.key === this.searchForm.indexId).length ===
            0
          ) {
            for (let i = 0; i < indexTreeData.length; i++) {
              const element = indexTreeData[i];
              if (Array.isArray(element.children) && element.children.length) {
                this.$refs["ownTree"].palteClick(element, true);
                this.$refs["ownTree"].indexClick(element.children[0]);
                break;
              }
            }
          } else {
            this.$emit("refreshOrgList");
          }
        } else {
          this.treeData = [];
          this.$refs["ownTree"].clearData();
          // 数组为空触发不了index.vue父组件的查询方法，需要手动请求
          this.$emit("clearData");
        }
      });
    },
    arrayToTree(items) {
      const result = []; // 存放结果集
      const itemMap = {}; //
      for (const item of items) {
        const id = item.key;
        const pid = item.parentKey || "x00001";

        if (!itemMap[id]) {
          itemMap[id] = {
            children: []
          };
        }

        itemMap[id] = {
          ...item,
          children: itemMap[id]["children"]
        };

        const treeItem = itemMap[id];
        if (pid === "x00001") {
          result.push(treeItem);
        } else {
          if (!itemMap[pid]) {
            itemMap[pid] = {
              children: []
            };
          }
          itemMap[pid].children.push(treeItem);
        }
      }
      return result;
    },
    // 打开权限勾选弹窗
    showModal() {
      this.$refs["cAddCardModal"].show();
    },
    // 保存当前角色选中的指标
    saveCurrentRoleList(item) {
      request(`/api/smc2/newuserIndexRelation/insertByRecommend`, {
        method: "POST",
        body: {
          sign: `${this.companyName}横比`, //场景
          businessSegmentsId: item.businessSegmentsId, //版块
          indexId: item.indexId, //指标
          fullCode: item.fullCode,
          signOrgId: this.signOrgId,
          type: "1"
        }
      }).then(() => {
        this.$nextTick(() => {
          this.getCurrentUserTree();
          this.$emit("getRecommendList");
        });
      });
    },
    // 处理成后端要的值
    dealPostTreeData(arr) {
      return arr.filter(item => {
        return item.split("==").length - 1 === 2;
      });
    },
    // 获取ownTree当前的选中indexId
    getActiveKey() {
      return this.$refs["ownTree"].getActiveKey();
    },
    // 获取ownTree当前的选中indexId的indexName
    getIndexName() {
      return this.$refs["ownTree"].getIndexName();
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexComparisonPage2 {
  .ant-spin-nested-loading {
    .ant-spin-container {
      background-color: rgba(0, 0, 0, 0) !important;
    }
  }
  ._indexTree {
    box-sizing: border-box;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    .indexList {
      flex: 1;
      overflow-y: auto;
      box-sizing: border-box;
      ._top {
        padding: 8px 8px 0 8px;
        .title {
          background-color: #00aaa6;
          border-radius: 3px;
          padding: 8px;
          cursor: pointer;
          margin-bottom: 8px;
          .anticon {
            color: #fff;
            margin-right: 9px;
          }
          span {
            height: 20px;
            color: #fff;
            line-height: 20px;
          }
        }
      }
      ._bottom {
        padding: 0 16px;
      }
    }
    .recommend {
      box-sizing: border-box;
      padding: 16px 12px;
      border-bottom: 1px solid #f0f0f0;
      & > ._title {
        height: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        margin-bottom: 8px;
      }
      .item {
        justify-content: space-between;
        &:not(:last-child) {
          margin-bottom: 8px;
        }
        .sign {
          height: 18px;
          font-size: 12px;
          padding: 0 5px;
          background: #e5e9ef;
          border-radius: 1px;
          color: #748195;
          line-height: 18px;
          margin-right: 8px;
          width: 34px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .title {
          height: 20px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
          line-height: 20px;
          max-width: 115px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .anticon {
          cursor: pointer;
          color: #00aaa6;
        }
      }
    }
  }
}
</style>
