<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-10-31 18:32:06
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="600"
    title="导入"
    :footer="null"
    @ok="handleOk"
    @cancel="close"
  >
    <div class="dialogInner">
      <a-radio-group v-model="radioVal">
        <a-radio-button value="target" v-if="targetDisabled">
          外报目标值
        </a-radio-button>
        <a-radio-button value="targetCIM" v-if="targetCIMDisabled">
          内控目标值
        </a-radio-button>
        <!-- <a-radio-button value="targetCBG" v-if="targetCBGDisabled">
          CBG目标值
        </a-radio-button> -->
      </a-radio-group>
      <a-radio-group v-model="flag">
        <a-radio-button value="Y" v-if="targetDisabled">
          累加
        </a-radio-button>
        <a-radio-button value="N" v-if="targetCIMDisabled">
          非累加
        </a-radio-button>
      </a-radio-group>
      <a-button type="primary" :loading="loadingFlag" @click="handleUpLoad">
        上传
      </a-button>
      <a-checkbox @change="isReceiveEmailOnChange" v-model="isReceiveEmail">
        是否接收邮件
      </a-checkbox>
      <p v-if="getUpLoadTypeContainer">{{ getUpLoadTypeContainer }}</p>
      <a-alert
        v-if="getUpLoadTypeContainer"
        message="如果不想等待,可以关闭上传窗口，等邮件通知导入结果"
        type="success"
      />
    </div>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
export default {
  props: {
    targetDisabled: {
      type: Boolean,
      default: false,
    },
    targetCIMDisabled: {
      type: Boolean,
      default: false,
    },
    targetCBGDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      radioVal: "target",
      // target: true,
      // targetCIM: true,
      // targetCBG: true,
      pageName: null,
      comKey: null,
      flag: "N",
      loadingFlag: false,
      getUpLoadTypeTimer: null,
      getUpLoadTypeContainer: "",
      upLoadStateGlobalFlag: false,
      upLoadIntervalStartTimeStamp: null,
      isReceiveEmail: false,
    };
  },
  methods: {
    show(record, pageName, comKey) {
      console.log(record, pageName, comKey);
      this.pageName = pageName;
      this.comKey = comKey;
      this.visible = true;
    },
    close() {
      this.visible = false;
      this.radioVal = "target";
      this.getUpLoadTypeContainer = "";
      // clearInterval(this.getUpLoadTypeTimer);
    },
    transformExcel(originalData, formInstanceKeyList) {
      console.log(originalData, "formInstanceKeyList", formInstanceKeyList);
      // 获取header对象中所有的键值对
      const headers = originalData[0].header;

      // 使用map方法遍历原始数组的data属性，并构建新的对象
      const transformedData = originalData[0].data.map((item) => {
        // 创建一个新对象，遍历formInstanceKeyList数组
        const newObj = {};
        formInstanceKeyList.forEach((tv) => {
          const headerKey = Object.keys(headers).find(
            (key) => headers[key] === tv.title
          );
          if (headerKey && item.hasOwnProperty(headerKey)) {
            // 确保找到匹配的headerKey且item对象有这个属性
            newObj[tv.value] = item[headerKey];
          }
        });
        return newObj;
      });

      return transformedData;
    },
    handleUpLoad() {
      this.upLoadIntervalStartTimeStamp = +new Date();
      this.upLoadStateGlobalFlag = true;
      let fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.style.display = "none";
      document.body.appendChild(fileInput);
      fileInput.click();
      fileInput.addEventListener("change", (event) => {
        this.loadingFlag = true;
        var file = event.target.files[0];
        // 获取文件内容或上传文件到服务器等操作
        const formData = new FormData();
        formData.append("file", file);
        formData.append("target", this.radioVal === "target" ? "Y" : null);
        formData.append(
          "targetCIM",
          this.radioVal === "targetCIM" ? "Y" : null
        );
        // this.radioVal === "targetCBG"
        //   ? formData.append("targetCBG", "Y")
        //   : null;
        formData.append("flag", this.flag);
        let token = JSON.parse(localStorage.getItem("pro__Access-Token"));
        // fetch("/api/smc2/common/excels/uploadDecrypt", {
        //   headers: {
        //     Authorization: `Bearer ${token.value}`,
        //   },
        //   credentials: "include",
        //   method: "POST",
        //   body: formData,
        // })
        // .then((res) => res.json())
        // .then((res) => {
        //   if (res.code == 1) {
        //     this.loadingFlag = false
        //     this.$message.error("盘古解析失败,请尝试重新上传文件");
        //     return;
        //   }else{
        // let formInstanceKeyValList = this.$store.state[this.pageName][
        //   this.comKey
        // ].config.columns.map((item) => {
        //   console.log(item, "item====");
        //   return {
        //     value: item.name,
        //     title: item?.alias?.val,
        //   };
        // });
        fetch("/api/smc2/newTarget/imports", {
          headers: {
            Authorization: `Bearer ${token.value}`,
            // "Content-Type": "application/json",
          },
          credentials: "include",
          method: "POST",
          body: formData,
          // JSON.stringify({
          //   list: this.transformExcel(res.data, formInstanceKeyValList),
          //   target: this.radioVal === "target" ? "Y" : null,
          //   targetCIM: this.radioVal === "targetCIM" ? "Y" : null,
          //   targetCBG: this.radioVal === "targetCBG" ? "Y" : null,
          //   flag: this.flag,
          // }),
        })
          .then((res) => res.json())
          .then((res) => {
            if (res.msg == "success") {
              this.upLoadStateGlobalFlag = false;
              this.loadingFlag = false;
              this.$message.success("上传成功");
              // this.close();
              if (this.flag == "Y") {
                this.$store.dispatch({
                  type: `${this.pageName}/1636938931053/fetch`,
                  payload: {
                    pageIndex: this.$store.state[this.pageName][1636938931053]
                      .data.pagination.pageIndex,
                  },
                });
              } else {
                this.$store.dispatch({
                  type: `${this.pageName}/1677138175000/fetch`,
                  payload: {
                    pageIndex: this.$store.state[this.pageName][1677138175000]
                      .data.pagination.pageIndex,
                  },
                });
              }
            } else {
              this.upLoadStateGlobalFlag = false;
              this.loadingFlag = false;
              this.$message.error(`${res.msg}`);
              this.getUpLoadTypeContainer = "";
              clearInterval(this.getUpLoadTypeTimer);
            }
            // });
            // }
          });
      });

      this.getUpLoadTypeTimer = setInterval(() => {
          // 如果当前时间-上传开始时间大于10分钟则停止轮询
          if (+new Date() - this.upLoadIntervalStartTimeStamp > 600000) {
            clearInterval(this.getUpLoadTypeTimer);
          }
          request("/api/smc2/newTarget/process").then((res) => {
            console.log(res);
            if (res) {
              this.getUpLoadTypeContainer = res;
            } else {
              if(!this.upLoadStateGlobalFlag&&!res){
                clearInterval(this.getUpLoadTypeTimer)
                this.getUpLoadTypeContainer = "";
              }
            }
          });
      }, 3000);
    },
    handleOk() {
      console.log([this.pageName], [this.comKey]);
    },
    isReceiveEmailOnChange(e) {
      if (e.target.checked) {
        request(`/api/smc2/newTarget/processEmail?type=receive`);
      } else {
        request(`/api/smc2/newTarget/processEmail?type=noReceive`);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.dialogInner {
  width: 100%;
  height: 220px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-around;
}
</style>
