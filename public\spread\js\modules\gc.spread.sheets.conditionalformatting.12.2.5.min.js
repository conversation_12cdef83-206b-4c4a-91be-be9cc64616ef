/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.ConditionalFormatting=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/conditional/conditional.entry.js")}({"./dist/plugins/conditional/conditional.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/conditional/conditional.js")),d(c("./dist/plugins/conditional/conditional.ns.js"))},"./dist/plugins/conditional/conditional.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa,Ta,Ua,Va,Wa,Xa,Ya,Za,$a=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("CalcEngine"),g=c("./dist/plugins/conditional/conditional.ns.js"),h=c("SheetsCalc"),i=e.GC$,j=d.Common.u,k=null,l=void 0,m="undefined",n="string",o=Math.min,p=Math.max,q=Math.abs,r=Math.floor,s=Math.ceil,t=f.Convert,u=t.Na,v=t.Pa,w=t.Th,x=d.Common.u,y=x.Db,z=x.kb,A=x.jb,B=x.Bb,C=d.Common.j.Fa,D=d.Common.q,E=d.Common.F,F=e.Ul,G=F.T$a,H=F.l_a,I=!!f,J=e.Style,K=d.Common.pc,L=K.bc,M=e.kf,N=e.Range,O=d.Common.l,P=d.Common.k,Q=P.Fb,R=P.Cb,S=P.ac,T=f.Functions,U=h.pf,V=e.Rm,W=new d.Common.ResourceManager(g.SR),X=W.getResource.bind(W),Y=function(a){var b=a.row<0?h.BAND_INDEX_CONST:a.row,c=a.col<0?h.BAND_INDEX_CONST:a.col;return h.uf(k,k,b,c,b+a.rowCount-1,c+a.colCount-1)},Z=function(a){var b;if(a instanceof f.Expression)b=a;else if(a instanceof N)b=Y(a);else{if(!u(a))throw X().Exp_NotSupport;a=v(a),isNaN(a)||(b=new f.Expression(2),b.value=a)}return b};function _a(a,b,c){var d,e;for(d=0;d<a.length;d++)if(e=a[d],e.contains(b,c))return e}function ab(a){return a.rowCount}function bb(a){return a.colCount}$=function(a,b,c,d){var e=a,f=b,g=function(a,b){var g,h,i=this;return i.hasOwnProperty("_ps")||(i._ps={}),g=i._ps,0===arguments.length?g[e]!==l?d?d.call(i,g[e]):g[e]:f:(g[e]!==a&&(h=g[e]!==l?g[e]:f,g[e]=a,b!==!1&&c&&c.call(i,a,h)),i)};return g.isDefault=function(a){return a===f},g},_=function(a,b,c){var d=this;return c||(c={}),b||(b=d),i.each(b,function(b,e){var f,g=d[e];C(g)||(f=g.call(d),"formula"===e?(d.getFormulaString?f=d.getFormulaString(a,l,l,!0):d.condition()&&d.condition().getFormulaString&&(f=d.condition().getFormulaString(a,l,l,!0)),f&&(c[e]=f)):"iconCriteria"===e||"icons"===e?c[e]=f:O.Ska(f)?c[e]=O.Daa(f):f&&f.toJSON?c[e]=f.toJSON(a):C(f)||g.isDefault(f)||(c[e]=f))}),c},aa=function(a,c,d,e){if(d){var f=this,g;c||(c=f),i.each(c,function(c,h){var j,k,l,m,o,p,q,r,s,t;if(g=d[h],!C(g))if("ranges"===h){for(j=[],k=0;k<g.length;k++)l=g[k],j.push(M(l.row,l.col,ab(l),bb(l)));f.ranges(j,!1)}else if("style"===h)f.style(new J,!1),f.style().fromJSON(g,e);else if("iconCriteria"===h)for(m=g.length,o=0;o<m;o++)p=g[o],f.NV[o]=new Va(p.isGreaterThanOrEqualTo,p.iconValueType,p.iconValue);else if("icons"===h)for(q=g.length,r=0;r<q;r++)s=g[r],f.CZa[r]={iconSetType:s.iconSetType,iconIndex:s.iconIndex};else"iconSetType"===h?f[h](g,!0):"item1"===h||"item2"===h?(f[h](b.$V(),!1),f[h]().fromJSON(g,a)):"expected"===h?i.getType(g)!==n||5!==d.conType&&13!==d.conType?f[h](g,!1):"/OADate("===g.substr(0,8)?f[h](O.Rka(g),!1):f[h](new Date(g)):"condition"===h?(t=new Ia,t.fromJSON(g,a),f.condition(t,!1)):i.getType(g)===n&&"/OADate("===g.substr(0,8)?f[h](O.Rka(g),!1):f[h](g,!1)})}};function cb(a,b,c){switch(a){case da.equalsTo:return b===c;case da.notEqualsTo:return b!==c;case da.greaterThan:return b>c;case da.greaterThanOrEqualsTo:return b>=c;case da.lessThan:return b<c;case da.lessThanOrEqualsTo:return b<=c;default:return!1}}ba=e.lUa,i.extend(ba.prototype,{YUa:function(){var a=this,b=a.zTa;b&&!b.ZUa&&(b.ZUa=a.U5.AW.map(function(a){return{rule:a,ranges:a.ranges().slice()}}))},$Ua:function(a){var b=this.U5,c=this.kj;c.suspendPaint(),a&&(b.AW=a.map(function(a){var b=a.rule;return b.ranges(a.ranges),b})),c.resumePaint()},_Ua:function(a,b){this.YUa(),this.U5.rI(a,b)},aVa:function(a,b){this.YUa(),this.U5.GR(a,b)},bVa:function(a,b){this.YUa(),this.U5.tI(a,b)},cVa:function(a,b){this.YUa(),this.U5.HR(a,b)},dVa:function(){this.YUa(),this.U5.Nm()},eVa:function(){this.YUa()}}),ba.$n("conditionalFormat",{init:function(){this.U5=new Za(this.kj)},undo:function(a){var b=a.ZUa;b&&this.$Ua(b)}}),ca={init:function(){this.conditionalFormats=this.ITa.U5},onLayoutChanged:function(a){var b=a.changeType,c=a.row,d=ab(a),e=a.col,f=bb(a),g=a.sheetArea,h=this.ITa;"addRows"===b?h._Ua(c,d):"deleteRows"===b?h.aVa(c,d):"addColumns"===b?h.bVa(e,f):"deleteColumns"===b?h.cVa(e,f):"clear"===b?h.dVa():"setColumnCount"!==b&&"setRowCount"!==b||3!==g&&1!==g||h.eVa()},toJson:function(a,b){var c=this.conditionalFormats,d=b&&b.ignoreStyle;c&&!d&&(a.conditionalFormats=c.toJSON(this))},fromJson:function(a,b,c){var d=c&&c.ignoreStyle;a&&a.conditionalFormats&&!d&&this.conditionalFormats.fromJSON(a.conditionalFormats,this,b)}},e.Worksheet.$n("conditionalFormat",ca),e.Workbook.$n("conditionalFormat",{fromJson:function(a,b,c){this.sheets.forEach(function(a){a.conditionalFormats&&a.conditionalFormats.getRules().forEach(function(a){a.S$a()})})}}),function(a){a[a.equalsTo=0]="equalsTo",a[a.notEqualsTo=1]="notEqualsTo",a[a.greaterThan=2]="greaterThan",a[a.greaterThanOrEqualsTo=3]="greaterThanOrEqualsTo",a[a.lessThan=4]="lessThan",a[a.lessThanOrEqualsTo=5]="lessThanOrEqualsTo"}(da=b.GeneralComparisonOperators||(b.GeneralComparisonOperators={})),function(a){a[a.or=0]="or",a[a.and=1]="and"}(ea=b.LogicalOperators||(b.LogicalOperators={})),function(a){a[a.equalsTo=0]="equalsTo",a[a.notEqualsTo=1]="notEqualsTo",a[a.greaterThan=2]="greaterThan",a[a.greaterThanOrEqualsTo=3]="greaterThanOrEqualsTo",a[a.lessThan=4]="lessThan",a[a.lessThanOrEqualsTo=5]="lessThanOrEqualsTo",a[a.between=6]="between",a[a.notBetween=7]="notBetween"}(fa=b.ComparisonOperators||(b.ComparisonOperators={})),function(a){a[a.contains=0]="contains",a[a.doesNotContain=1]="doesNotContain",a[a.beginsWith=2]="beginsWith",a[a.endsWith=3]="endsWith"}(ga=b.TextComparisonOperators||(b.TextComparisonOperators={})),function(a){a[a.equalsTo=0]="equalsTo",a[a.notEqualsTo=1]="notEqualsTo",a[a.beginsWith=2]="beginsWith",a[a.doesNotBeginWith=3]="doesNotBeginWith",a[a.endsWith=4]="endsWith",a[a.doesNotEndWith=5]="doesNotEndWith",a[a.contains=6]="contains",a[a.doesNotContain=7]="doesNotContain"}(ha=b.TextCompareType||(b.TextCompareType={})),function(a){a[a.backgroundColor=0]="backgroundColor",a[a.foregroundColor=1]="foregroundColor"}(ia=b.ColorCompareType||(b.ColorCompareType={})),function(a){a[a.empty=0]="empty",a[a.nonEmpty=1]="nonEmpty",a[a.error=2]="error",a[a.nonError=3]="nonError",a[a.formula=4]="formula"}(ja=b.CustomValueType||(b.CustomValueType={})),function(a){a[a.equalsTo=0]="equalsTo",a[a.notEqualsTo=1]="notEqualsTo",a[a.before=2]="before",a[a.beforeEqualsTo=3]="beforeEqualsTo",a[a.after=4]="after",a[a.afterEqualsTo=5]="afterEqualsTo"}(ka=b.DateCompareType||(b.DateCompareType={})),function(a){a[a.dateOccurring=0]="dateOccurring",a[a.yearOccurring=1]="yearOccurring",a[a.quarterOccurring=2]="quarterOccurring",a[a.monthOccurring=3]="monthOccurring",a[a.weekOccurring=4]="weekOccurring",a[a.dayOccurring=5]="dayOccurring",a[a.yearTodate=6]="yearTodate"}(la=b.DateExConditionExpectType||(b.DateExConditionExpectType={})),function(a){a[a.top=0]="top",a[a.bottom=1]="bottom"}(ma=b.Top10ConditionType||(b.Top10ConditionType={})),function(a){a[a.today=0]="today",a[a.yesterday=1]="yesterday",a[a.tomorrow=2]="tomorrow",a[a.last7Days=3]="last7Days",a[a.thisMonth=4]="thisMonth",a[a.lastMonth=5]="lastMonth",a[a.nextMonth=6]="nextMonth",a[a.thisWeek=7]="thisWeek",a[a.lastWeek=8]="lastWeek",a[a.nextWeek=9]="nextWeek",a[a.nextQuarter=10]="nextQuarter",a[a.thisQuarter=11]="thisQuarter",a[a.lastQuarter=12]="lastQuarter",a[a.nextYear=13]="nextYear",a[a.thisYear=14]="thisYear",a[a.lastYear=15]="lastYear"}(na=b.DateOccurringType||(b.DateOccurringType={})),function(a){a[a.quarter1=0]="quarter1",a[a.quarter2=1]="quarter2",a[a.quarter3=2]="quarter3",a[a.quarter4=3]="quarter4"}(oa=b.QuarterType||(b.QuarterType={})),function(a){a[a.above=0]="above",a[a.below=1]="below",a[a.equalOrAbove=2]="equalOrAbove",a[a.equalOrBelow=3]="equalOrBelow",a[a.above1StdDev=4]="above1StdDev",a[a.below1StdDev=5]="below1StdDev",a[a.above2StdDev=6]="above2StdDev",a[a.below2StdDev=7]="below2StdDev",a[a.above3StdDev=8]="above3StdDev",a[a.below3StdDev=9]="below3StdDev"}(pa=b.AverageConditionType||(b.AverageConditionType={})),function(a){a[a.number=0]="number",a[a.lowestValue=1]="lowestValue",a[a.highestValue=2]="highestValue",a[a.percent=3]="percent",a[a.percentile=4]="percentile",a[a.automin=5]="automin",a[a.formula=6]="formula",a[a.automax=7]="automax"}(qa=b.ScaleValueType||(b.ScaleValueType={})),function(a){a[a.leftToRight=0]="leftToRight",a[a.rightToLeft=1]="rightToLeft"}(ra=b.BarDirection||(b.BarDirection={})),function(a){a[a.automatic=0]="automatic",a[a.cellMidPoint=1]="cellMidPoint",a[a.none=2]="none"}(sa=b.DataBarAxisPosition||(b.DataBarAxisPosition={})),function(a){a[a.threeArrowsColored=0]="threeArrowsColored",a[a.threeArrowsGray=1]="threeArrowsGray",a[a.threeTriangles=2]="threeTriangles",a[a.threeStars=3]="threeStars",a[a.threeFlags=4]="threeFlags",a[a.threeTrafficLightsUnrimmed=5]="threeTrafficLightsUnrimmed",a[a.threeTrafficLightsRimmed=6]="threeTrafficLightsRimmed",a[a.threeSigns=7]="threeSigns",a[a.threeSymbolsCircled=8]="threeSymbolsCircled",a[a.threeSymbolsUncircled=9]="threeSymbolsUncircled",a[a.fourArrowsColored=10]="fourArrowsColored",a[a.fourArrowsGray=11]="fourArrowsGray",a[a.fourRedToBlack=12]="fourRedToBlack",a[a.fourRatings=13]="fourRatings",a[a.fourTrafficLights=14]="fourTrafficLights",a[a.fiveArrowsColored=15]="fiveArrowsColored",a[a.fiveArrowsGray=16]="fiveArrowsGray",a[a.fiveRatings=17]="fiveRatings",a[a.fiveQuarters=18]="fiveQuarters",a[a.fiveBoxes=19]="fiveBoxes",a[a.noIcons=20]="noIcons"}(ta=b.IconSetType||(b.IconSetType={})),function(a){a[a.number=1]="number",a[a.percent=4]="percent",a[a.formula=7]="formula",a[a.percentile=5]="percentile"}(ua=b.IconValueType||(b.IconValueType={})),function(a){a[a.relationCondition=0]="relationCondition",a[a.numberCondition=1]="numberCondition",a[a.textCondition=2]="textCondition",a[a.colorCondition=3]="colorCondition",a[a.formulaCondition=4]="formulaCondition",a[a.dateCondition=5]="dateCondition",a[a.dateExCondition=6]="dateExCondition",a[a.textLengthCondition=7]="textLengthCondition",a[a.top10Condition=8]="top10Condition",a[a.uniqueCondition=9]="uniqueCondition",a[a.averageCondition=10]="averageCondition",a[a.cellValueCondition=11]="cellValueCondition",a[a.areaCondition=12]="areaCondition",a[a.timeCondition=13]="timeCondition"}(va=b.ConditionType||(b.ConditionType={})),function(a){a[a.conditionRuleBase=0]="conditionRuleBase",a[a.cellValueRule=1]="cellValueRule",a[a.specificTextRule=2]="specificTextRule",a[a.formulaRule=3]="formulaRule",a[a.dateOccurringRule=4]="dateOccurringRule",a[a.top10Rule=5]="top10Rule",a[a.uniqueRule=6]="uniqueRule",a[a.duplicateRule=7]="duplicateRule",a[a.averageRule=8]="averageRule",a[a.twoScaleRule=10]="twoScaleRule",a[a.threeScaleRule=11]="threeScaleRule",a[a.dataBarRule=12]="dataBarRule",a[a.iconSetRule=13]="iconSetRule"}(wa=b.RuleType||(b.RuleType={})),xa=function(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0,0)},ya=function(a){return new Date(a.getFullYear(),a.getMonth(),a.getDate(),23,59,59,999)},za=function(a,b){a.setDate(b)},Aa=function(a){return a.getDate()},Ba=function(a,b){return a.getFullYear()===b.getFullYear()&&a.getMonth()===b.getMonth()&&a.getDate()===b.getDate()},Ca=function(a,b){var c=ya(a);return b>c},Da=function(a,b){var c=xa(a);return b<c},Ea=function(a,b){return a.getHours()===b.getHours()&&a.getMinutes()===b.getMinutes()&&a.getSeconds()===b.getSeconds()&&a.getMilliseconds()===b.getMilliseconds()},Fa=function(a,b){var c=new Date(1899,11,30,a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()),d=new Date(1899,11,30,b.getHours(),b.getMinutes(),b.getSeconds(),b.getMilliseconds());return d>c},Ga=function(a,b){return!Fa(a,b)&&!Ea(a,b)},b.$V=function(a,b,c,d,e,f,g,h,i){return new Ia(a,{compareType:b,expected:c,formula:d,customValueType:e,type:f,ranges:g,item1:h,item2:i})},b.Cxb=new J,Ha=["conType","compareType","item1","item2","ignoreBlank","expected","formula","treatNullValueAsZero","integerValue","forceValue2Text","useWildCards","ignoreCase","customValueType","expectTypeId","type","ranges","isPercent","regex"],Ia=function(){function a(a,b){var c,d;b||(b={}),c=b.formula,d=this,d.offsetRow=0,d.offsetCol=0,d.conType("string"==typeof a?va[a]:a),C(b.compareType)||d.RV(b.compareType),C(b.expected)||d.expected(b.expected),C(c)||d.formula("string"==typeof c?y(i.trim(c),"="):c),C(b.item1)||d.item1(b.item1),C(b.item2)||d.item2(b.item2),C(b.customValueType)||d.customValueType(b.customValueType),C(b.type)||d.type(b.type),d.ranges(b.ranges),d.Lf=k,d.TV=k}return a.prototype.context=function(a){if(1===arguments.length){this.xc=a;var b=this.item1(),c=this.item2();b&&b.context&&b.context(a),c&&c.context&&c.context(a)}return this.xc},a.prototype.j_a=function(a){var b=this,c=b.item1(),d=b.item2();a?b.q$a=!0:b.p$a=!0,c&&c.j_a(a),d&&d.j_a(a)},a.prototype.k_a=function(){var a=this,b=a.item1(),c=a.item2();delete a.q$a,delete a.p$a,b&&b.k_a(),c&&c.k_a()},a.prototype.initExpression=function(a,b,c){var d,e,f,g,h,i=this.item1(),j=this.item2();i&&i.initExpression&&i.initExpression(),j&&j.initExpression&&j.initExpression(),a=a||this.xc,d=this.Yw,a&&d&&!this.Lf&&(this.ranges()?(e=H(this.ranges()),b=e.r,c=e.c):(b=b||0,c=c||0),f=a.getCalcService(),g=a.yj(),f&&g&&(h=!1,this.q$a&&(h=!this.p$a),this.Lf=f.parse(g,d,b,c,!1,!0,h)))},a.prototype.expression=function(a,b,c,d){var e,f,g;return 1===arguments.length?(this.Lf=a,b=b||this.xc,a?b&&(this.ranges()?(e=H(this.ranges()),c=e.r,d=e.c):(c=c||0,d=d||0),f=b.getCalcService(),g=b.yj(),f&&g&&(this.Yw=f.unparse(g,a,c,d))):this.Yw=a,this.Lf):(this.Lf||this.initExpression(b,c,d),this.Lf)},a.prototype.getFormulaString=function(a,b,c,d){var e,f,g;return a=a||this.xc,e=this.Lf,f=this.Yw,a&&f?(e||(this.initExpression(a,b,c),e=this.Lf),g=H(this.ranges()),b=C(b)?g.r:b,c=C(c)?g.c:c,a.getCalcService().unparse(a.yj(),e,b,c,l,!!d)):f},a.prototype.formula=function(a,b,c){if("string"!=typeof a){var d=this.item1(),e=this.item2();return 0===c&&d&&d.formula?d.formula(a,b,c):1===c&&e&&e.formula?e.formula(a,b,c):this.getFormulaString(l,a,b)}this.Yw=a,this.Lf=k},a.prototype.adjustOffset=function(a,b){var c=this,d=c.item1();d&&d.adjustOffset&&d.adjustOffset(a,b),d=c.item2(),d&&d.adjustOffset&&d.adjustOffset(a,b),c.offsetRow=a,c.offsetCol=b},a.prototype.relationConditionEvaluate=function(a,b,c,d,e){var f,g,h,i,j=this;function k(d,e){if(a&&d&&3===d.conType()){var f=a.getActualStyle(b,c);f&&(0===d.RV()?e=f.backColor:1===d.RV()&&(e=f.foreColor))}return e}function l(a,b){if(a)return a.ignoreBlank(j.ignoreBlank()),arguments.length<5?k(a,d):b}function m(d,e){return!C(d)&&d.evaluate(a,b,c,e)}return f=l(j.item1(),d),g=l(j.item2(),e),h=m(j.item1(),f)?1:0,i=m(j.item2(),g)?1:0,h+i>j.RV()},a.prototype.cellValueConditionEvaluate=function(a,b,c,d){var e=this.getExpected(a,b,c);return!(e!==k||!a||!a.lRa)||this.cellValueConditionCheckCondition(e,d)},a.prototype.cellValueConditionCheckCondition=function(a,b){var c,d,e,f=this,g=0,h=!1;if(C(b)&&C(a))switch(f.RV()){case da.equalsTo:case da.greaterThanOrEqualsTo:case da.lessThanOrEqualsTo:return!0;case da.notEqualsTo:case da.greaterThan:case da.lessThan:return!1;default:return!1}return"boolean"==typeof a||"boolean"==typeof b?cb(f.RV(),b,a):(c={},C(b)?f.treatNullValueAsZero()?h=!0:g=b:"boolean"!=typeof b&&w(b,c)&&(g=c.value,h=!isNaN(g)),h?(d=0,e=!1,w(a,c)&&(d=c.value,e=!0),e?cb(f.RV(),g,d):f.RV()===da.notEqualsTo):"string"==typeof b?"string"==typeof a?cb(f.RV(),b,a):1===f.RV():!!C(b)&&f.RV()===da.notEqualsTo)},a.prototype.numberConditionEvaluate=function(a,b,c,d){var e=this.numberConditionGetExpected(a,b,c);return!(e!==k||!a||!a.lRa)||(this.integerValue()&&(e=isNaN(e)?k:e>0?r(e):s(e)),this.numberConditionCheckCondition(e,d))},a.prototype.numberConditionCheckCondition=function(a,b){var c,d,e=this;if((C(b)||""===b)&&e.ignoreBlank())return!0;if(isNaN(b))return!1;if(C(a)){if(e.ignoreBlank())return!0;a=0}try{c=parseFloat(b)}catch(a){return!1}return!(e.integerValue()&&(d=c-r(c)===0,!d))&&cb(e.RV(),c,a)},a.prototype.numberConditionGetExpected=function(a,b,c){var d,e=this;return e.Yw&&e.Yw.length>0?(d=a.Cf(),d?d.evaluate(a.yj(),e.expression(k,a),b,c):e.expected()):e.expected()},a.prototype.textConditionEvaluate=function(a,b,c,d){var e,f,g,h=this,i=h.compareType(),j=h.ignoreCase();function m(a,b){return h.testTextByCondition(b,a,"^","$",function(){return j?a.toLowerCase()===b.toLowerCase():a===b})}function n(a,b){return h.testTextByCondition(b,a,"^","",function(){return z(b,a,j)})}function o(a,b){return h.testTextByCondition(b,a,"","$",function(){return A(b,a,j)})}function p(a,b){return h.testTextByCondition(b,a,"","",function(){return B(b,a===l||a===k?"":a,j)})}if(!h.forceValue2Text()&&E(d,Date))return i!==ha.beginsWith&&i!==ha.endsWith&&i!==ha.contains&&(i===ha.doesNotBeginWith||i===ha.doesNotEndWith||i===ha.doesNotContain);if(e=C(d)?"":""+d,h.ignoreBlank()&&""===e)return!0;if(f=this.getExpected(a,b,c),g=C(f)?"":""+f,h.hasWildcard(g)&&"number"==typeof d)return i===ha.doesNotBeginWith||i===ha.doesNotContain||i===ha.doesNotEndWith||i===ha.notEqualsTo;switch(i){case ha.equalsTo:return m(g,e);case ha.notEqualsTo:return!m(g,e);case ha.beginsWith:return n(g,e);case ha.doesNotBeginWith:return!n(g,e);case ha.endsWith:return o(g,e);case ha.doesNotEndWith:return!o(g,e);case ha.contains:return p(g,e);case ha.doesNotContain:return!p(g,e);default:return!1}},a.prototype.hasWildcard=function(a){return a.indexOf("*")>-1||a.indexOf("?")>-1},a.prototype.testTextByCondition=function(a,b,c,d,e){var f,g=this.useWildCards();return g?this.regex()?D.qb(this.regex()).test(a):(f=D.ub(b))?(f=c+f+d,this.ignoreCase()?D.sb(f).test(a):D.qb(f).test(a)):e():e()},a.prototype.colorConditionEvaluate=function(a,b,c,d){var e,f,g,h;function i(a){return C(a)||""===a?k:K.ec(a)}if(e=this,f=i(e.expected()),C(f)||""===f){if(e.ignoreBlank()||C(f)&&C(d))return!0}else if(g=i(d),(C(g)||""===g)&&a&&a.getDefaultStyle&&(h=a.getDefaultStyle(),e.RV()===ia.backgroundColor?g=h.backColor:e.RV()===ia.foregroundColor&&(g=h.foreColor)),!C(g)&&""!==g)return g=g,f=f,g.a===f.a&&g.r===f.r&&g.g===f.g&&g.b===f.b;return!1},a.prototype.formulaConditionEvaluate=function(a,b,c,d){var e,f,g,h;if(!I)return!1;if(e=this,e.customValueType()===ja.formula)return f=e.getExpected(a,b,c),!(!e.ignoreBlank()||!C(f)&&""!==f)||(g={},!!t.Uh(f,g)&&g.value);switch(h=t.vf,e.customValueType()){case ja.empty:return C(d)||""===d;case ja.nonEmpty:return!C(d)&&""!==d;case ja.error:return h(d);case ja.nonError:return!h(d);default:return!1}},a.prototype.formulaConditionGetExpected=function(a,b,c){var d,e,g,h,i,j,k,m,n,o,p=this;if(p.Yw&&p.Yw.length>0){if(d=a.Cf(),!d)return p.expected();if(e=d.Hg(a.yj(),p.expression(l,a),U(b,c),!0),g=void 0,h=void 0,i=void 0,j=void 0,k=void 0,t.Fh(e)){for(g=e.getRowCount(0),h=e.getColumnCount(0),i=[],j=0;j<g;j++)for(i[j]=[],k=0;k<h;k++)i[j][k]=e.getValue(0,j,k);e=i}else if(t.Ca(e)){for(g=e.getRowCount(),h=e.getColumnCount(),i=[],j=0;j<g;j++)for(i[j]=[],k=0;k<h;k++)i[j][k]=e.getValue(j,k);e=i}return E(e,Array)?(g=S(e),h=S(e[0]),1===g&&1===h?e[0][0]:(m=_a(p.ranges(),b,c),n=b-m.row,o=c-m.col,n<g&&o<h?e[n][o]:f.Errors.NotAvailable)):e}return p.expected()},a.prototype.dateOrTimeEvaluate=function(a,b,c,d,e,f,g){var h,i,j=this;if((C(d)||""===d)&&j.ignoreBlank())return!0;if(!E(d,Date))return j.RV()===ka.notEqualsTo;if(h=k,i=this.getExpected(a,b,c),E(i,Date)?h=i:"string"==typeof i?h=O.Qa(i):"number"==typeof i&&(h=O.Xb(i)),C(h))return!!j.ignoreBlank();switch(j.RV()){case ka.equalsTo:return e(h,d);case ka.notEqualsTo:return!e(h,d);case ka.after:return g(h,d);case ka.afterEqualsTo:return g(h,d)||e(h,d);case ka.before:return f(h,d);case ka.beforeEqualsTo:return f(h,d)||e(h,d);default:return!1}},a.prototype.dateConditionEvaluate=function(a,b,c,d){return this.dateOrTimeEvaluate(a,b,c,d,Ba,Da,Ca)},a.prototype.timeConditionEvaluate=function(a,b,c,d){return this.dateOrTimeEvaluate(a,b,c,d,Ea,Ga,Fa)},a.prototype.dateExConditionEvaluate=function(a,b,c,d){var e=this.A4(a,b,c);return!C(e)&&this.dateExConditionCheckCondition(e,d)},a.prototype.getExConditionDateScope=function(a){var b,c,d,e,f,g,h,i,j,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J=k,K=k,L=new Date,M=new Date(L.getFullYear(),L.getMonth(),L.getDate(),L.getHours(),L.getMinutes(),L.getSeconds(),L.getMilliseconds());switch(a){case na.last7Days:za(M,Aa(M)-6),J=xa(M),K=ya(L);break;case na.yesterday:za(M,Aa(M)-1),J=xa(M),K=ya(M);break;case na.today:J=xa(L),K=ya(L);break;case na.tomorrow:za(M,Aa(M)+1),J=xa(M),K=ya(M);break;case na.lastWeek:b=L,za(b,Aa(L)-L.getDay()-7),c=M,c.setDate(Aa(M)-M.getDay()-1),J=xa(b),K=ya(c);break;case na.thisWeek:d=L,za(d,Aa(L)-L.getDay()),e=M,za(e,Aa(M)-M.getDay()+6),J=xa(d),K=ya(e);break;case na.nextWeek:f=L,za(f,Aa(L)-L.getDay()+7),g=M,za(g,Aa(M)-M.getDay()+13),J=xa(f),K=ya(g);break;case na.lastMonth:h=L,za(h,1),h.setMonth(L.getMonth()-1),i=M,za(i,0),J=xa(h),K=ya(i);break;case na.thisMonth:j=L,za(j,1),l=M,za(l,1),l.setMonth(M.getMonth()+1),za(l,0),J=xa(j),K=ya(l);break;case na.nextMonth:m=L,za(m,1),m.setMonth(L.getMonth()+1),n=M,za(n,1),n.setMonth(M.getMonth()+2),za(n,0),J=xa(m),K=ya(n);break;case na.nextQuarter:o=L,p=o.getMonth(),q=p%3,o.setDate(1),o.setMonth(p-q+3),r=M,s=void 0,r.setMonth(p-q+6),s=2===r.getMonth()||11===r.getMonth()?31:30,r.setDate(s),J=xa(o),K=ya(r);break;case na.thisQuarter:t=L,u=t.getMonth(),v=u%3,t.setDate(1),t.setMonth(u-v),w=M,x=void 0,w.setMonth(u-v+3),x=2===w.getMonth()||11===w.getMonth()?31:30,w.setDate(x),J=xa(t),K=ya(w);break;case na.lastQuarter:y=L,z=y.getMonth(),A=z%3,y.setDate(1),y.setMonth(z-A-3),B=M,C=void 0,B.setMonth(z-A),C=2===B.getMonth()||11===B.getMonth()?31:30,B.setDate(C),J=xa(y),K=ya(B);break;case na.nextYear:D=L,D.setDate(1),D.setFullYear(D.getFullYear()+1),D.setMonth(0),E=M,E.setFullYear(E.getFullYear()+1),E.setMonth(11),E.setDate(31),J=xa(D),K=ya(E);break;case na.thisYear:F=L,F.setDate(1),F.setMonth(0),G=M,G.setMonth(11),G.setDate(31),J=xa(F),K=ya(G);break;case na.lastYear:H=L,H.setFullYear(H.getFullYear()-1),H.setDate(1),H.setMonth(0),I=M,I.setFullYear(I.getFullYear()-1),I.setMonth(11),I.setDate(31),J=xa(H),K=ya(I)}return{from:J,to:K}},a.prototype.dateExConditionCheckCondition=function(a,c){var d,e,f,g,h,i,j,l,m,n,o,p;function q(a,b){switch(a){case oa.quarter1:return b.getMonth()>=0&&b.getMonth()<=2;case oa.quarter2:return b.getMonth()>=3&&b.getMonth()<=5;case oa.quarter3:return b.getMonth()>=6&&b.getMonth()<=8;case oa.quarter4:return b.getMonth()>=9&&b.getMonth()<=11;default:return!1}}if(d=this,e=c,d.ignoreBlank()&&(C(e)||""===e))return!0;try{e=t.Wh(e)}catch(a){return!1}if(g=d.expectTypeId(),g===la.dateOccurring){if(h=this.getExConditionDateScope(a),i=h.from,j=h.to,!C(i)&&!C(j))return l=b.$V(va.dateCondition,ka.afterEqualsTo,i,k),m=b.$V(va.dateCondition,ka.beforeEqualsTo,j,k),n=b.$V(va.relationCondition,ea.and,k,k,k,k,k,l,m),n.evaluate(k,0,0,e)}else if(f=d.A4(k,0,0),!C(f)){if(g===la.yearOccurring)return f===e.getFullYear();if(g===la.quarterOccurring)return q(f,e);if(g===la.monthOccurring)return f===e.getMonth();if(g===la.weekOccurring)return f===e.getDay();if(g===la.dayOccurring)return f===Aa(e);if(g===la.yearTodate)return o=new Date,p=new Date,o.setMonth(0,1),o.setHours(0,0,0,0),p.setHours(23,59,59,59),o<=e&&e<=p}return!1},a.prototype.textLengthConditionEvaluate=function(a,b,c,d){var e,f;return C(d)||""===d?this.ignoreBlank():(e=C(d)?0:(""+d).length,f=this.A4(a,b,c),"number"==typeof f&&cb(this.RV(),e,f))},a.prototype.top10ConditionEvaluate=function(a,b,c,d){var e,f,g,h=this;if(C(d)||""===d)return h.ignoreBlank();if(e=h.A4(a,b,c),!C(e)&&(f=void 0,h.k$a?(C(h.mxb)&&(h.mxb=h.getTopValues(a,e,h.ranges())),f=h.mxb):f=h.getTopValues(a,e,h.ranges()),f)){g=0;try{g=h.Pa(d)}catch(a){return!1}if(P.Bb(f,g))return!0}return!1},a.prototype.A4=function(a,b,c){var d=this.getExpected(a,b,c);return d=parseInt(d,10),isNaN(d)||!isFinite(d)?k:d},a.prototype.adjustRange=function(a,b){return M(a.row,a.col,Math.min(ab(a),b.getRowCount()),Math.min(bb(a),b.getColumnCount()))},a.prototype.getTopValues=function(a,b,c){var d,e,f,g,h,i,j,k,l,m=0===this.type()?1:-1,n=[];if(!c)return n;for(d=this.UV(a,c),e=S(d),j=0;j<e;j++)for(f=this.adjustRange(d[j],a),k=0;k<ab(f);k++)for(g=k+f.row,l=0;l<bb(f);l++)h=l+f.col,i=this.Pa(a.getValue(g,h)),C(i)||n.push(i);return n.sort(function(a,b){return(b-a)*m}),S(n)>b&&(n=n.slice(0,b)),n},a.prototype.Pa=function(a){return("number"==typeof a||E(a,Date))&&I?v(a):k},a.prototype.uniqueConditionEvaluate=function(a,b,c,d){var e,f,g,h,i=this,j=d;return C(j)||""===j?i.ignoreBlank():(I&&u(j)&&(j=v(j)),e=this.getExpected(a,b,c),f={},t.Uh(e,f),g=f.value,!C(g)&&(h=i.duplicatedArrayCached||i.getDuplicated(a,i.ranges()),!C(h)&&P.Bb(h,j)?g===!0:g!==!0))},a.prototype.getDuplicated=function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n=[],o={},p=this.UV(a,b);if(p)for(c=S(p),d=0;d<c;d++)for(e=p[d],e=this.adjustRange(e,a),f=ab(e),g=bb(e),h=0;h<f;h++)for(i=h+e.row,j=0;j<g;j++)k=j+e.col,l=a.getValue(i,k,3),!C(l)&&I&&u(l)&&(l=v(l)),m=o[l],m?1===m&&n.push(l):o[l]=1;return n},a.prototype.VV=function(a,b){var c,d=a.length;for(c=0;c<d;c++)if(a[c].key===b)return!0;return!1},a.prototype.averageConditionEvaluate=function(a,b,c,d){var e,f,g,h,i,j,l,m;if(!I)return!1;if(e=this,e.ignoreBlank()&&(C(d)||""===d))return!0;if(e.WV(),e.k$a?(C(e.mxb)&&(e.mxb=e.getExpected(a,b,c)),f=e.mxb):f=e.getExpected(a,b,c),g={},h=w(f,g)?g.value:NaN,i=k,j=e.TV?e.XV(a,e.TV,b,c):k,C(j)||(g={},i=w(j,g)?g.value:NaN),u(d)&&(l=v(d),!isNaN(h)))switch(m=isNaN(i),e.type()){case pa.above:return l>h;case pa.below:return l<h;case pa.equalOrAbove:return l>=h;case pa.equalOrBelow:return l<=h;case pa.above1StdDev:return!m&&l>h+i;case pa.below1StdDev:return!m&&l<h-i;case pa.above2StdDev:return!m&&l>h+2*i;case pa.below2StdDev:return!m&&l<h-2*i;case pa.above3StdDev:return!m&&l>h+3*i;case pa.below3StdDev:return!m&&l<h-3*i;default:return!1}return!1},a.prototype.WV=function(){var a=this;a.ranges()&&(a.Lf=a.YV("AVERAGE",a.ranges()),a.type()>=4&&a.type()<=9&&(a.TV=a.YV("STDEV",a.ranges())))},a.prototype.YV=function(a,b){var c,d,e,g;if(!I)return k;if(c=T.findGlobalFunction(a)){for(d=[],e=S(b),g=0;g<e;g++)d[g]=Z(b[g]);return f.Jh(c,d)}return k},a.prototype.averageConditionGetExpected=function(a,b,c){var d=a.Cf();return d?d.Hg(a.yj(),this.expression(l,a),U(b,c),!1):k},a.prototype.nxb=function(){var a=this.conType();a!==va.averageCondition&&a!==va.top10Condition||(this.k$a=!0)},a.prototype.oxb=function(){var a=this;a.k$a=!1,a.mxb=l},a.prototype.XV=function(a,b,c,d){var e=a.Cf();return e?e.Hg(a.yj(),b,U(c,d),!1):k},a.prototype.ZV=function(a,b){var c,d;for(d=0;d<S(b);d++)c=b[d],E(c,Array)&&S(c)>0?S(b)>1?a.push(c[0]):a.push.apply(a,c):a.push(c)},a.prototype.getValidList=function(a,b,c){var d,e,f=[],g=this.getValidListImp(a,b,c);for(d=0,e=S(g);d<e;d++)f.push(g[d].value);return f},a.prototype.getValidListImp=function(a,b,c){var d,e,f,g,h,j,k,l,m=this,n=[];if(m.Yw&&m.Yw.length>0)d=m.getExpected(a,b,c),E(d,Array)?m.ZV(n,d):n.push(d);else if(m.expected()&&m.expected().length>0&&(e=m.expected(),f=e.replace(/\\,/g,String.fromCharCode(206)),g=f.split(",")))for(h=0;h<S(g);h++)j=g[h],C(j)||(k=RegExp(String.fromCharCode(206),"g"),l=i.trim(j).replace(k,","),""!==l&&n.push({text:l,value:l}));return n},a.prototype.areaConditionEvaluate=function(a,b,c,d){var e,f,g,h,i,j;function k(a,b){return E(a,Date)&&E(b,Date)?a.valueOf()===b.valueOf():a===b}if(e=this,C(d)||""===d)return e.ignoreBlank()===!0;for(f=e.getValidList(a,b,c),g=0;g<S(f);g++){if(h=f[g],C(h)&&C(d))return!0;if(e.Yw){if(k(h,d))return!0}else if(i=a.getActualStyle(b,c),j=F.ul(i,h,!0),k(j,d))return!0}return!1},a.prototype.areaConditionGetExpected=function(a,b,c){var d,e,f,g,h,i,j,k,m,n,o,p=this,q=[];if(p.Yw){if(d=void 0,e=void 0,f=void 0,g=void 0,h=void 0,i=void 0,j=a.Cf(),!j)return q;if(k=j.Hg(a.yj(),p.expression(l,a),U(b,c),!0),t.Fh(k))for(d=k.getRowCount(0),e=k.getColumnCount(0),m=k.getRow(0),n=k.getColumn(0),o=k.xf&&k.xf.kj,f=0;f<d;f++)for(q[f]=[],g=0;g<e;g++)h=k.getValue(0,f,g),i=o?o.getText(m+f,n+g):h,q[f][g]={value:h,text:i};else if(t.Ca(k))for(d=k.getRowCount(),e=k.getColumnCount(),f=0;f<d;f++)for(q[f]=[],g=0;g<e;g++)h=k.getValue(f,g),q[f][g]={value:h,text:h}}else q.push({value:p.expected(),text:p.expected()});return q},a.prototype.reset=function(){var a=this;switch(a.ignoreBlank(!1),a.RV(1),a.item1(k),a.item2(k),a.value1=k,a.value2=k,a.expected(k),a.RV(0),a.integerValue(!1),a.operator=0,a.text="",a.RV(0),a.useWildCards(!0),a.ignoreCase(!1),a.forceValue2Text(!1),a.customValueType(0),a.Lf=k,a.type(0),this.expectTypeId(0),a.ranges(k),a.isPercent(!1),a.regex(k),a.conType()){case va.relationCondition:a.ignoreBlank(!1),a.RV(1);break;case va.numberCondition:a.RV(0),a.integerValue(!1);break;case va.textCondition:a.RV(0),a.useWildCards(!0),a.forceValue2Text(!1);break;case va.colorCondition:a.RV(0);break;case va.formulaCondition:a.customValueType(0),a.TV=k;break;case va.dateCondition:case va.timeCondition:a.RV(0);break;case va.dateExCondition:a.expectTypeId(0);break;case va.textLengthCondition:a.RV(0);break;case va.top10Condition:a.type(0);break;case va.averageCondition:a.type(0);break;case va.cellValueCondition:a.operator=6;break;case va.areaCondition:}},a.prototype.evaluate=function(a,b,c,d){var e=this.conType();return!C(e)&&this[va[e]+"Evaluate"](a,b,c,d)},a.prototype.getExpectedNormal=function(a,b,c,d,e,f){var g,h=this,i=h.Yw,j=h.expected();return i?(g=a.Cf(),g?g.Hg(a.yj(),h.expression(l,a),U(b,c),!1,f):j):j},a.prototype.getExpected=function(a,b,c){var d=this;switch(d.conType()){case 2:return d.getExpectedNormal(a,b,c,b-d.offsetRow,c-d.offsetCol,!1);case 5:case 6:case 7:case 9:case 11:case 13:return d.getExpectedNormal(a,b,c,b-d.offsetRow,c-d.offsetCol,!1);case 8:return d.getExpectedNormal(a,b,c,b,c);case 0:return k;case 1:return d.numberConditionGetExpected(a,b,c);case 3:return k;case 4:return d.formulaConditionGetExpected(a,b,c);case 10:return d.averageConditionGetExpected(a,b,c);case 12:return d.areaConditionGetExpected(a,b,c);default:return k}},a.prototype.UV=function(a,b){var c,d,e,f=[];for(c=0,d=S(b);c<d;c++)e=a.Tq(b[c]),f.push(e);return f},a.prototype.getExpressions=function(){var a=[],b=this;return 0===b.conType()?(b.item1()&&b.item1().getExpressions&&(a=b.item1().getExpressions()),b.item2()&&b.item2().getExpressions&&(a=a.concat(b.item2().getExpressions())),a):(b.Lf||b.initExpression(),
b.Lf?[b.Lf]:[])},a.prototype.setExpressions=function(a,b){var c=this,d;return b&&b.push({type:"condition",condition:this,expressions:c.getExpressions()}),0===c.conType()?(c.item1()&&c.item1().setExpressions&&(d=c.item1().getExpressions().length,c.item1().setExpressions(a.slice(0,d))),void(c.item2()&&c.item2().setExpressions&&c.item2().setExpressions(a.slice(d)))):void c.expression(a[0])},a.prototype.toJSON=function(a){return _.call(this,a,Ha)},a.prototype.fromJSON=function(a,b,c){if(a){var d=this;aa.call(d,b,Ha,a,c),d.j_a(!0),d.initExpression(),d.k_a()}},a.fromSource=function(a){return b.$V(12,k,a,k)},a.fromFormula=function(a){return b.$V(12,k,"",a)},a.fromDay=function(a){var c=b.$V(6,k,a);return c.expectTypeId(5),c},a.fromMonth=function(a){var c=b.$V(6,k,a);return c.expectTypeId(3),c},a.fromQuarter=function(a){var c=b.$V(6,k,a);return c.expectTypeId(2),c},a.fromWeek=function(a){var c=b.$V(6,k,a);return c.expectTypeId(4),c},a.fromYear=function(a){var c=b.$V(6,k,a);return c.expectTypeId(1),c},a}(),b.Condition=Ia,Ia.prototype.conType=$("conType",k),Ia.prototype.ranges=$("ranges",k,function(a){var b=this.item1(),c=this.item2();b&&b.ranges&&b.ranges(a),c&&c.ranges&&c.ranges(a)}),Ia.prototype.ignoreBlank=$("ignoreBlank",!1),Ia.prototype.compareType=$("compareType",l),Ia.prototype.RV=Ia.prototype.compareType,Ia.prototype.expected=$("expected",l),Ia.prototype.item1=$("item1",k),Ia.prototype.item2=$("item2",k),Ia.prototype.treatNullValueAsZero=$("treatNullValueAsZero",!1),Ia.prototype.integerValue=$("integerValue",!1),Ia.prototype.forceValue2Text=$("forceValue2Text",!1),Ia.prototype.useWildCards=$("useWildCards",!0),Ia.prototype.regex=$("regex",k),Ia.prototype.ignoreCase=$("ignoreCase",!1),Ia.prototype.customValueType=$("customValueType",l),Ia.prototype.expectTypeId=$("expectTypeId",0),Ia.prototype.type=$("type",k),Ia.prototype.isPercent=$("isPercent",!1);function db(a,b,c,d,e,f,g,h,i,j,k){var l,m=arguments;b.ko(c)?(l=b.lo(c),7===S(m)?a.drawImage(l,d,e,f,g):a.drawImage(l,d,e,f,g,m[7],m[8],m[9],m[10])):b.fo(c)}Ja=function(){function a(a,b,c){var d=this;d.xyb=!0,d.ruleType(a),d.style(b),d.ranges(c)}return a.prototype.stopIfTrue=function(a){},a.prototype.evaluate=function(a,c,d,e){var f,g,h=this;return h.contains(c,d)?(h.initCondition(),f={baseRow:0,baseCol:0},h.getBaseCoordinate(f),h.condition().adjustOffset(c-f.baseRow,d-f.baseCol),g=k,h.condition().evaluate(a,c,d,e)&&(g=h.getExpected()||b.Cxb),h.condition().adjustOffset(0,0),g):k},a.prototype.contains=function(a,b){var c,d,e,f=this.ranges();if(f)for(c=S(f),d=void 0,e=0;e<c;e++)if(d=f[e],d.contains(a,b))return!0;return!1},a.prototype.createCondition=function(){return this.xyb=!1,null},a.prototype.initCondition=function(a){var b=this.condition();b||(b=this.createCondition(),b&&(b.ranges(this.ranges()),this.condition(b)),a&&this.context(a),this.context()&&b&&b.context(this.context()))},a.prototype.yyb=function(){var a,b=this;b.xyb&&(a=b.condition(),a||(b.initCondition(),a=b.condition()),a&&!a.Lf&&a.initExpression())},a.prototype.context=function(a){return 1===arguments.length&&(this.xc=a,this.condition()&&this.condition().context(a)),this.xc},a.prototype.getExpected=function(){return this.style()},a.prototype.reset=function(){var a=this;a.ranges(k),a.condition(k),a.style(k),a.stopIfTrue(!1),a.priority(1)},a.prototype.intersects=function(a,b,c,d){var e,f,g,h=this.ranges();if(h)for(e=S(h),f=void 0,g=0;g<e;g++)if(f=h[g],f.intersect(a,b,c,d))return!0;return!1},a.prototype.isScaleRule=function(){return!1},a.prototype.getBaseCoordinate=function(a){var b,c,d,e,f;if(a.baseRow=Number.MAX_VALUE,a.baseCol=Number.MAX_VALUE,b=this,b.ranges()&&S(b.ranges())>0)for(c=0;c<S(b.ranges());c++)d=b.ranges()[c],e=d.row,f=d.col,e=e===-1?0:e,f=f===-1?0:f,a.baseRow=o(e,a.baseRow),a.baseCol=o(f,a.baseCol);else a.baseRow=0,a.baseCol=0},a.prototype._V=function(a,b,c){var d,e,f,g,h,i=this,j=c?b:0,k=c?0:b;if(i.yyb(),i.ranges())for(d=S(i.ranges()),e=0;e<d;e++)f=i.ranges()[e],g=c?f.row:f.col,h=c?ab(f):bb(f),g!==-1&&(g>=a?i.ranges()[e]=M(f.row+j,f.col+k,ab(f),bb(f)):a<g+h&&(i.ranges()[e]=M(f.row,f.col,ab(f)+j,bb(f)+k)))},a.prototype.rI=function(a,b){this._V(a,b,!0)},a.prototype.tI=function(a,b){this._V(a,b,!1)},a.prototype.aW=function(a,b,c){var d,e,f,g,h,i,j,k,l,m,n=this,p=c?a:0,q=c?0:a,r=c?b:0,s=c?0:b;if(n.yyb(),n.ranges()){for(d=[],e=S(n.ranges()),f=0;f<e;f++)g=n.ranges()[f],h=c?g.row:g.col,i=c?ab(g):bb(g),h!==-1&&(h>a?h+i<=a+b?d.push(g):n.ranges()[f]=M(g.row-r,g.col-s,ab(g),bb(g)):a<h+i&&(j=M(g.row,g.col,ab(g)-o(g.row+ab(g)-p,r),bb(g)-o(g.col+bb(g)-q,s)),0===bb(j)||0===ab(j)?d.push(g):n.ranges()[f]=j));for(k=S(d),l=0;l<k;l++)m=d[l],Q(n.ranges(),m)}},a.prototype.GR=function(a,b){this.aW(a,b,!0)},a.prototype.HR=function(a,b){this.aW(a,b,!1)},a.prototype.toJSON=function(a){return _.call(this,a)},a.prototype.fromJSON=function(a,b,c){a&&(aa.call(this,b,k,a,c),this.initCondition(b))},a.prototype.S$a=function(){var a=this.condition();a&&G(a)},a.prototype.hW=function(a){var b,c,d=[],e=S(a);for(c=0;c<e;c++)b=a[c],d.push(M(b.row,b.col,ab(b),bb(b)));return d},a.prototype.iW=function(){var a,b,c,d=this.cW,e=this.ranges();if(!d)return this.cW=this.hW(e),!1;if(a=S(d),b=S(e),a!==b)return!0;for(c=0;c<b;c++)if(!d[c].equals(e[c]))return!0;return!1},a}(),b.ConditionRuleBase=Ja,Ka=Ja.prototype,Ka.ruleType=$("ruleType",k),Ka.ranges=$("ranges",k,function(a){var b,c=this.condition();c&&c.ranges()&&(b=c.xc,b&&b.ITa.YUa(),c.ranges(a))}),Ka.condition=$("condition",k),Ka.style=$("style",k),Ka.priority=$("priority",1),Ka.stopIfTrue=$("stopIfTrue",!1);function eb(a,b){return a===wa.cellValueRule&&(b===fa.between||b===fa.notBetween)}La=["ruleType","style","operator","text","formula","type","rank","priority","stopIfTrue","ranges"],Ma=function(a){$a(c,a);function c(b,c,d,e,f,g,h,j,l,m){var n=a.call(this,b,d)||this,o=n;return o.ranges(c),o.operator(e),o.value1("string"==typeof f?i.trim(f):f),o.value2("string"==typeof g?i.trim(g):g),o.text(h),o.formula(j),o.type(l),o.rank(m),o.cached=!1,o.cW=k,n}return c.prototype.value1=function(a,b){var c,d;return 1!==arguments.length?(c=this.condition(),c&&(d=c.formula(a,b,0),d&&d.length>0)?"="+y(i.trim(""+d),"="):this._Qa):void(this._Qa=a)},c.prototype.value2=function(a,b){var c,d;return 1!==arguments.length?(c=this.condition(),c&&(d=c.formula(a,b,1),d&&d.length>0)?"="+y(i.trim(""+d),"="):this.aRa):void(this.aRa=a)},c.prototype.formula=function(a,b){var c=this,d=c.condition();return"string"!=typeof a?d&&d.formula(a,b)||c.Yw:(c.Yw=a,void(d&&d.formula(a)))},c.prototype.cellValueRuleCreateCondition=function(){var a,c,d,e,f,g,h,i,j=this,m=j.value1(),n=j.value2(),o=j.ranges(),p=j.bW(m)?y(m,"="):k,q=j.bW(n)?y(n,"="):k,r=j.operator();return r===fa.between?(a=b.$V(va.cellValueCondition,3,m,p,l,l,o),a.treatNullValueAsZero(!0),c=b.$V(va.cellValueCondition,5,n,q,l,l,o),c.treatNullValueAsZero(!0),f=b.$V(va.relationCondition,1,k,k,k,k,o,a,c),d=b.$V(va.cellValueCondition,5,m,p,l,l,o),d.treatNullValueAsZero(!0),e=b.$V(va.cellValueCondition,3,n,q,l,l,o),e.treatNullValueAsZero(!0),g=b.$V(va.relationCondition,1,k,k,k,k,o,d,e),b.$V(va.relationCondition,0,k,k,k,k,o,f,g)):r===fa.notBetween?(a=b.$V(va.cellValueCondition,4,m,p,l,l,o),a.treatNullValueAsZero(!0),c=b.$V(va.cellValueCondition,2,n,q,l,l,o),a.treatNullValueAsZero(!0),f=b.$V(va.relationCondition,0,k,k,k,k,o,a,c),d=b.$V(va.cellValueCondition,2,m,p,l,l,o),d.treatNullValueAsZero(!0),e=b.$V(va.cellValueCondition,4,n,q,l,l,o),e.treatNullValueAsZero(!0),g=b.$V(va.relationCondition,0,k,k,k,k,o,d,e),b.$V(va.relationCondition,1,k,k,k,k,o,f,g)):(h=r,i=b.$V(11,h,m,p,l,l,o),i.treatNullValueAsZero(!0),i)},c.prototype.bW=function(a){return!C(a)&&"="===a[0]},c.prototype.specificTextRuleCreateCondition=function(){var a,c,d,e,f=this.operator();switch(f){case 2:a=2;break;case 3:a=4;break;case 0:a=6;break;case 1:a=7;break;default:a=0}return c=this.text(),d=k,c&&"="===c[0]&&(d=c),e=b.$V(2,a,c,d,l,l,this.ranges()),e.ignoreCase(!0),e.useWildCards(2!==f&&3!==f),e},c.prototype.formulaRuleCreateCondition=function(){var a=this,c=a.ranges();return b.$V(4,k,k,a.Yw,4,k,c)},c.prototype.dateOccurringRuleCreateCondition=function(){return b.$V(6,k,this.type(),k,k,l,this.ranges())},c.prototype.top10RuleCreateCondition=function(){return b.$V(8,k,this.rank(),k,k,this.type(),this.ranges())},c.prototype.uniqueRuleCreateCondition=function(){return b.$V(9,k,!1,k,k,k,this.ranges())},c.prototype.duplicateRuleCreateCondition=function(){return b.$V(9,k,!0,k,k,k,this.ranges())},c.prototype.averageRuleCreateCondition=function(){return b.$V(10,k,k,k,k,this.type(),this.ranges())},c.prototype.createCondition=function(){return this[wa[this.ruleType()]+"CreateCondition"]()},c.prototype.reset=function(){var a=this;switch(a.ranges(k),a.condition(k),a.style(k),a.stopIfTrue(!1),a.priority(1),a.operator(6),a.value1(k),a.value2(k),a.text(""),a.formula(k),a.type(0),a.rank(10),a.ruleType()){case 1:a.operator(6);break;case 2:a.operator(0);break;case 4:a.style(k);break;case 5:a.type(0),a.rank(10);break;case 8:a.type(0)}},c.prototype.evaluate=function(b,c,d,e){return this.jW(b),a.prototype.evaluate.call(this,b,c,d,e)},c.prototype.jW=function(a){var b,c=this;c.initCondition(),b=c.condition(),9===b.conType()&&(c.iW()&&(c.Nm(),c.cW=c.hW(c.ranges())),c.cached===!1&&(b.duplicatedArrayCached=b.getDuplicated(a,c.ranges()),c.cached=!0))},c.prototype.Nm=function(){this.cached=!1},c.prototype.rI=function(b,c){a.prototype.rI.call(this,b,c),this.Nm()},c.prototype.tI=function(b,c){a.prototype.tI.call(this,b,c),this.Nm()},c.prototype.GR=function(b,c){a.prototype.GR.call(this,b,c),this.Nm()},c.prototype.HR=function(b,c){a.prototype.HR.call(this,b,c),this.Nm()},c.prototype.toJSON=function(a){var b,c,d,e,f,g=this;return C(g.condition())&&this.initCondition(),b=_.call(g,a,La),c=g.value1(),eb(b.ruleType,b.operator)&&(d=g.value2()),e=a&&a.parent&&1===a.parent.options.referenceStyle,C(c)||(g.bW(c)&&e&&(c=fb(a,g,c,e)),b.value1=c),C(d)||(g.bW(d)&&e&&(d=fb(a,g,d,e)),b.value2=d),f=g.ruleType(),f===wa.conditionRuleBase&&(b.condition=g.condition().toJSON()),b},c.prototype.fromJSON=function(a,b,c){var d=La;C(a.condition)||(d=La.concat(["condition"])),aa.call(this,b,d,a,c),C(a.value1)||this.value1(a.value1),C(a.value2)||this.value2(a.value2),this.initCondition(b)},c}(Ja),b.NormalConditionRule=Ma;function fb(a,b,c,d){var e=b.ranges(),f=H(e),g=h.formulaToExpression(a,c,f.r,f.c,d);return h.expressionToFormula(a,g,f.r,f.c,!d)}Na={operator:$("operator",k),text:$("text",k),type:$("type",k),rank:$("rank",k),priority:$("priority",1)},i.extend(Ma.prototype,Na),Oa=function(){function a(a,b){this.type=a,this.value=b}return a}(),b.ScaleValue=Oa,Pa=["ruleType","ranges","minType","minValue","minColor","maxType","maxValue","maxColor","midType","midValue","midColor","priority"],Qa=function(a){$a(c,a);function c(b,c,d,e,f,g,h,i,j,l,m){var n,o,p,q,r,s,t,u,v,w=a.call(this,b,k,m)||this,x=w;return x.xyb=!1,x.lowestValueCached=k,x.highestValueCached=k,x.zyb={},x.cached=!1,x.cW=k,n=c,o=d,p=e,q=f,r=g,s=h,t=i,u=j,v=l,arguments.length<=1&&(n=1,o=k,p=L(255,248,105,107),q=4,r=50,s=L(255,255,235,132),t=2,u=k,v=L(255,99,190,123)),x.minColor(p),x.minValue(o),x.minType(n),x.midColor(s),x.midValue(r),x.midType(q),x.maxColor(v),x.maxValue(u),x.maxType(t),w}return c.prototype.stopIfTrue=function(a){return!1},c.prototype.isScaleRule=function(){return!0},c.prototype.createCondition=function(){return k},c.prototype.fW=function(a){var c=b.$V(8,k,k,k,k,1).getTopValues(a,1,this.ranges());return S(c)>0?c[0]:k},c.prototype.gW=function(a){var c=b.$V(8,k,k,k,k,0).getTopValues(a,1,this.ranges());return S(c)>0?c[0]:k},c.prototype.jW=function(a){var b=this;b.iW()&&(b.Nm(),b.cW=b.hW(b.ranges())),b.cached===!1&&(b.lowestValueCached=b.fW(a),b.highestValueCached=b.gW(a),b.cached=!0)},c.prototype.Nm=function(){this.lowestValueCached=k,this.highestValueCached=k,this.zyb={},this.cached=!1},c.prototype.kW=function(a,b,c,d){var e,f;return d&&(e=a.Cf())?(f=e.parse(a.yj(),d,b,c),e.Hg(a.yj(),f,U(b,c),!1)):k},c.prototype.bW=function(a){return a&&"="===a[0]},c.prototype.lW=function(a){return C(a)||""===a?k:"="===a[0]?a.substr(1):a},c.prototype.mW=function(a,b,c,d){if(this.bW(d))return this.kW(a,b,c,this.lW(""+d));var e={};return w(d,e)?e.value:NaN},c.prototype.dW=function(a){return this.jW(a),this.highestValueCached},c.prototype.eW=function(a){return this.jW(a),this.lowestValueCached},c.prototype.nW=function(a,b,c,d){var e,f,g=this.mW(a,b,c,d);return!isNaN(g)&&0<=g&&g<=100&&(e=this.eW(a),f=this.dW(a),typeof e!==m&&e!==k&&typeof f!==m&&f!==k)?e+(f-e)*g/100:k},c.prototype.oW=function(a,b,c,d){var e,f,g,h,i,j,l,m,n,o,p,q=this.zyb;if(void 0!==q[d])return q[d];if(e=this.mW(a,b,c,d),!isNaN(e)&&0<=e&&e<=100){for(f=0,g=this.ranges(),h=S(g),i=0;i<h;i++)j=this.YV("PERCENTILE",[a.Tq(g[i]),e/100]),l=a.Cf(),m=l.unparse(k,j,b,c),n=l.parse(a.yj(),m,b,c),o=l.Hg(a.yj(),n,U(b,c),!1),p={},f+=w(o,p)?p.value:0;return q[d]=f/h,q[d]}return q[d]=k,q[d]},c.prototype.pW=function(a,b,c,d,e){var f,g,h=this;switch(d){case qa.formula:return h.bW(e)||(e="="+e),h.mW(a,b,c,e);case qa.highestValue:return h.dW(a);case qa.lowestValue:return h.eW(a);case qa.number:return h.mW(a,b,c,e);case qa.percent:return h.nW(a,b,c,e);case qa.percentile:return h.oW(a,b,c,e);case qa.automax:return f=h.dW(a),f<0?0:f;case qa.automin:return g=h.eW(a),g>0?0:g;default:return k}},c.prototype.qW=function(a,b,c){return a===b&&a===c?1:a<=b?0:a>=c?1:(a-b)/(c-b)},c.prototype.rW=function(a,b,c){var d,e,f,g,h,i;return 0<=a&&a<=1?(d=K.ec(b),e=K.ec(c),f=d.a*(1-a)+e.a*a,g=d.r*(1-a)+e.r*a,h=d.g*(1-a)+e.g*a,i=d.b*(1-a)+e.b*a,L(parseFloat(f/255+""),parseInt(g+"",10),parseInt(h+"",10),parseInt(i+"",10))):k},c.prototype.YV=function(a,b){var c,d,e,g;if(!I)return k;if(c=T.findGlobalFunction(a)){for(d=[],e=S(b),g=0;g<e;g++)d[g]=Z(b[g]);return f.Jh(c,d)}return k},c.prototype.rI=function(b,c){a.prototype.rI.call(this,b,c),this.Nm()},c.prototype.tI=function(b,c){a.prototype.tI.call(this,b,c),this.Nm()},c.prototype.GR=function(b,c){a.prototype.GR.call(this,b,c),this.Nm()},c.prototype.HR=function(b,c){a.prototype.HR.call(this,b,c),this.Nm()},c.prototype.scaleEvaluate=function(a,b,c,d){var e,f,g,h,i=this;if(i.jW(a),i.contains(b,c)){if(C(d))return k;try{if(e=v(d),f=i.pW(a,b,c,i.minType(),i.minValue()),g=i.pW(a,b,c,i.midType(),i.midValue()),h=i.pW(a,b,c,i.maxType(),i.maxValue()),f>h)return k;if(isNaN(g)){if(!isNaN(f)&&!isNaN(h))return i.qW(e,f,h)}else if(!isNaN(f)&&!isNaN(h))return e<f?0:e>=h?2:f<=e&&e<=g?i.qW(e,f,g):1+i.qW(e,f,h)}catch(a){return k}}return k},c.prototype.twoScaleRuleEvaluate=function(a,b,c,d){var e,f,g,h,i,j;if(!C(d)){if(e={},f=u(d)&&w(d,e)?e.value:NaN,isNaN(f))return k;if(g=this,h=g.pW(a,b,c,g.minType(),g.minValue()),i=g.pW(a,b,c,g.maxType(),g.maxValue()),!C(h)&&!C(i))return j=g.qW(f,h,i),g.rW(j,g.minColor(),g.maxColor())}return k},c.prototype.threeScaleRuleEvaluate=function(a,b,c,d){var e,f,g,h,i,j,l=this;if(l.lh=a,!C(d)){if(e={},f=u(d)&&w(d,e)?e.value:NaN,isNaN(f))return k;if(g=l.pW(a,b,c,l.minType(),l.minValue()),h=l.pW(a,b,c,l.midType(),l.midValue()),i=l.pW(a,b,c,l.maxType(),l.maxValue()),!C(g)&&!C(i)&&!C(h))return j=void 0,g>i?k:f<=g||g===i?f>=i?l.maxColor():l.minColor():f>=i?l.maxColor():f<=h?(j=l.qW(f,g,h),l.rW(j,l.minColor(),l.midColor())):(j=l.qW(f,h,i),l.rW(j,l.midColor(),l.maxColor()))}return k},c.prototype.evaluate=function(a,b,c,d){return this[wa[this.ruleType()]+"Evaluate"](a,b,c,d)},c.prototype.ad=function(){var a=this;a.minValue(k),a.minType(1),a.midValue(50),a.midType(4),a.maxValue(k),a.maxType(2),10===a.ruleType()&&(a.minColor(L(0,255,255,255)),a.maxColor(L(255,99,190,123))),11===a.ruleType()&&(a.midValue(50),a.midType(4),a.minColor(L(255,248,105,107)),a.midColor(L(255,255,235,132)),a.maxColor(L(255,99,190,123)))},c.prototype.reset=function(){var a=this;a.ranges(k),a.condition(k),a.style(k),a.ad(),a.lh=k,a.stopIfTrue(!1),a.priority(1)},c.prototype.toJSON=function(a){return _.call(this,a,Pa)},c.prototype.fromJSON=function(a,b,c){aa.call(this,b,Pa,a,c),this.initCondition(b)},c}(Ja),b.ScaleRule=Qa,Ra={minValue:$("minValue",k),minType:$("minType",5),minColor:$("minColor",k),midValue:$("midValue",50,k,function(a){var b=this;if(b.lh){if(2===b.midType())return b.dW(b.lh);if(1===b.midType())return b.eW(b.lh)}return a}),midType:$("midType",k),midColor:$("midColor",k),maxType:$("maxType",7),maxValue:$("maxValue",k),maxColor:$("maxColor",k)},i.extend(Qa.prototype,Ra),Sa=["ruleType","ranges","gradient","color","showBorder","borderColor","dataBarDirection","negativeFillColor","useNegativeFillColor","negativeBorderColor","useNegativeBorderColor","axisPosition","axisColor","showBarOnly","minType","minValue","maxType","maxValue","priority"],Ta=function(a){$a(b,a);function b(b,c,d,e,f,g){var h=this,i=b,j=c,l=d,m=e,n=f;return 0===arguments.length&&(i=5,j=k,l=7,m=k,n=L(255,99,142,198)),h=a.call(this,12,i,j,k,k,k,k,l,m,k,g)||this,h.xyb=!1,h.ad(n),h}return b.prototype.ad=function(a){var b=this;b.gradient(!0),b.color(a),b.showBorder(!1),b.borderColor("black"),b.dataBarDirection(0),b.negativeFillColor("red"),b.useNegativeFillColor(!0),b.negativeBorderColor("black"),b.useNegativeBorderColor(!1),b.axisPosition(0),b.axisColor("black"),b.showBarOnly(!1)},b.prototype.reset=function(){var a=this;a.ranges(k),a.condition(k),a.style(k),a.ad(L(255,99,142,198)),a.stopIfTrue(!1),a.priority(1),a.minValue(k),a.minType(5),a.midValue(k),a.midType(k),a.maxValue(k),a.maxType(7),a.minColor(k),a.midColor(k),a.maxColor(k)},b.prototype.sW=function(a,b,c,d){var e,f,g,h,i=this,j=d?i.maxType():i.minType(),k=d?i.maxValue():i.minValue();if(6!==j&&4!==j)return i.pW(a,b,c,j,k);for(f=0;f<S(i.ranges());f++)if(i.ranges()[f].intersect(b,-1,1,-1)&&(e=i.ranges()[f]))return g=e.row,h=e.col,g=g===-1?0:g,h=h===-1?0:h,i.pW(a,g,h,j,k)},b.prototype.tW=function(a,b,c,d){return d=0,a>=c&&a>b?[1,d]:a<=b&&a<c?[0,d]:c===b?[.5,d]:[(a-b)/(c-b),d]},b.prototype.uW=function(a,b,c,d){var e,f,g;return d=.5,e=q(c-b),c>0&&b>=0?c===b?[.5,d]:a>=c?[.5,d]:a<=b?[b/c*.5,d]:[.5*q(a/c),d]:c>0&&b<0?(f=c>q(b)?.5:c/e,g=c>q(b)?b/e:-.5,a>0?a>=c?[f,d]:[a/c*f,d]:a<0?a<=b?[g,d]:[a/b*g,d]:[0,d]):c<=0&&b<0?c===b?[-.5,d]:a>=c?[-c/b*.5,d]:a<=b?[-.5,d]:[-a/b*.5,d]:0===c&&0===b?[0,d]:(d=-1,[-1,d])},b.prototype.vW=function(a,b,c,d){var e,f,g,h=q(b),i=q(c),j=q(c-b),k=b<0?-1:1;return c>0&&b>=0||c<=0&&b<0?(e=p(h,i),f=o(h,i),g=q(a),d=b<0?1:0,g<=f&&g<e?[0,d]:g>=e&&g>f?[k,d]:f===e?[.5*k,d]:[k*(q(a)-f)/j,d]):c>0&&b<0?(d=q(b)/j,0===a?[0,d]:a>=c?[1-d,d]:[p(a,b)/j,d]):0===c&&0===b?(d=.5,0===a?[0,d]:[.5*k,d]):(d=-1,[-1,d])},b.prototype.wW=function(a,b,c,d){var e=this;return 0===e.axisPosition()?e.vW(a,b,c,d):1===e.axisPosition()?e.uW(a,b,c,d):e.tW(a,b,c,d)},b.prototype.evaluate=function(a,b,c,d){var e,f,g,h,i,j,l,m,n,o,p,q;if(!C(d)){if(e={},f=u(d,!0)&&w(d,e)?e.value:NaN,isNaN(f))return k;if(g=this,h=g.sW(a,b,c,!1),i=g.sW(a,b,c,!0),E(h,Date)&&(h=O.Ra(h)),E(i,Date)&&(i=O.Ra(i)),!C(h)&&!C(i))return h>i&&(j=i,i=h,h=j),l=void 0,m=g.wW(f,h,i,l),!m||S(m)<2?k:(n=m[0],l=m[1],o=f<0&&g.useNegativeFillColor()?g.negativeFillColor():g.color(),p=f<0&&g.useNegativeBorderColor()?g.negativeBorderColor():g.borderColor(),q=g.axisColor(),o=V.Om(a,o),p=V.Om(a,p),q=V.Om(a,q),{fillColor:o,borderColor:p,showBorder:g.showBorder(),axisColor:q,isGradient:g.gradient(),direction:g.dataBarDirection(),axisLocation:l,scale:n,showBarOnly:g.showBarOnly()})}return k},b.prototype.toJSON=function(a){return _.call(this,a,Sa)},b.prototype.fromJSON=function(a,b,c){aa.call(this,b,Sa,a,c),this.initCondition(b)},b.paintDataBar=function(a,b,c,d,f,g){var h,i,j,k,l,m,n,o,p,s,t,u,v,w,x=new e.Rect(c+2,d+2,f-4,g-4),y=x.x,z=x.y,A=r(q(x.width*b.scale)),B=x.height;if(0===b.axisLocation)b.scale<=0&&(A=0);else if(1===b.axisLocation)b.scale<0?y=y+x.width-A:A=0;else{for(h=r(x.width*b.axisLocation+y)+.5,i=x.y,j=1,k=g-2,a.lineWidth=j,a.strokeStyle=b.axisColor,a.beginPath(),l=0;l<=k;l+=2)1===b.direction?(a.moveTo(2*c+f-h,i+l),a.lineTo(2*c+f-h,i+l+1)):(a.moveTo(h,i+l),a.lineTo(h,i+l+1));a.stroke(),b.scale>0?y=h+j:b.scale<0?y=h-A:A=0}b.showBorder&&(y=r(y)+.5,A-=1,z+=.5,B-=1),m=y,n=y+A,1===b.direction&&(m=2*c+f-y-A,n=2*c+f-y),A>=0&&B>=0&&(o=b.fillColor,b.isGradient&&(p=.9,o=a.createLinearGradient(m,z,n,z),s=K.ec(b.fillColor),t=L(s.a,r(255*p+s.r*(1-p)),r(255*p+s.g*(1-p)),r(255*p+s.b*(1-p))),u=b.scale<0?1-p:p,v=b.fillColor,w=b.scale<0?1:0,1===b.direction?(o.addColorStop(u,v),o.addColorStop(w,t)):(o.addColorStop(u,t),o.addColorStop(w,v))),a.fillStyle=o,a.fillRect(m,z,A,B),b.showBorder&&A>0&&B>0&&(a.strokeStyle=b.borderColor,a.strokeRect(m,z,A,B)))},b}(Qa),b.DataBarRule=Ta,Ua={gradient:$("gradient",!0),color:$("color",k),showBorder:$("showBorder",!1),borderColor:$("borderColor","black"),dataBarDirection:$("dataBarDirection",0),negativeFillColor:$("negativeFillColor","red"),useNegativeFillColor:$("useNegativeFillColor",!0),negativeBorderColor:$("negativeBorderColor","black"),useNegativeBorderColor:$("useNegativeBorderColor",!1),axisPosition:$("axisPosition",0),axisColor:$("axisColor","black"),showBarOnly:$("showBarOnly",!1)},i.extend(Ta.prototype,Ua),Va=function(){function a(a,b,c){this.isGreaterThanOrEqualTo=a,this.iconValueType=b,this.iconValue=c}return a}(),b.IconCriterion=Va,Wa=["ruleType","ranges","iconSetType","iconCriteria","showIconOnly","reverseIconOrder","priority","icons"],Xa=function(a){$a(b,a);function b(b,c){var d=this,e=b;return 0===arguments.length&&(e=0),d=a.call(this,13,k,k,k)||this,d.xyb=!1,d.ranges(c),d.ad(e),d}return b.prototype.xW=function(a){var b=this;b.iconSetType(a,!1),b.iconSetType()>=ta.threeArrowsColored&&b.iconSetType()<=ta.threeSymbolsUncircled?(b.NV=[],b.NV[0]=new Va((!0),ua.percent,33),b.NV[1]=new Va((!0),ua.percent,67),b.CZa=[],b.CZa[0]={iconSetType:a,iconIndex:0},b.CZa[1]={iconSetType:a,iconIndex:1},b.CZa[2]={iconSetType:a,iconIndex:2}):b.iconSetType()>=ta.fourArrowsColored&&b.iconSetType()<=ta.fourTrafficLights?(b.NV=[],b.NV[0]=new Va((!0),ua.percent,25),b.NV[1]=new Va((!0),ua.percent,50),b.NV[2]=new Va((!0),ua.percent,75),b.CZa=[],b.CZa[0]={iconSetType:a,iconIndex:0},b.CZa[1]={iconSetType:a,iconIndex:1},b.CZa[2]={iconSetType:a,iconIndex:2},b.CZa[3]={iconSetType:a,iconIndex:3}):b.iconSetType()>=ta.fiveArrowsColored&&b.iconSetType()<=ta.fiveBoxes?(b.NV=[],b.NV[0]=new Va((!0),ua.percent,20),b.NV[1]=new Va((!0),ua.percent,40),b.NV[2]=new Va((!0),ua.percent,60),b.NV[3]=new Va((!0),ua.percent,80),b.CZa=[],b.CZa[0]={iconSetType:a,iconIndex:0},b.CZa[1]={iconSetType:a,iconIndex:1},b.CZa[2]={iconSetType:a,iconIndex:2},b.CZa[3]={iconSetType:a,iconIndex:3},b.CZa[4]={iconSetType:a,iconIndex:4}):b.iconSetType()>ta.fiveBoxes&&(b.NV=[],b.CZa=[])},b.prototype.ad=function(a){this.showIconOnly(!1),this.reverseIconOrder(!1),this.xW(a)},b.prototype.yW=function(a){var b=S(this.NV)+1;return this.reverseIconOrder()&&b>2?b-1-a:a},b.prototype.pW=function(a,b,c,d){var e=this,f=e.NV[d];if(f)switch(f.iconValueType){case ua.formula:return e.mW(a,b,c,f.iconValue);case ua.number:return e.mW(a,b,c,f.iconValue);case ua.percent:return e.nW(a,b,c,f.iconValue);case ua.percentile:return e.oW(a,b,c,f.iconValue);default:return k}},b.prototype.evaluate=function(a,b,c,d){var e,f,g,h,i,j,l,m,n,o,p,q,r=this,s=d;if(C(s))return k;if(e={},f=u(d,!0)&&"boolean"!=typeof s&&w(d,e)?e.value:NaN,isNaN(f))return k;if(g=0,h=r.iconSetType(),i=r.NV,h>=ta.fiveArrowsColored?g=5:h>=ta.fourArrowsColored?g=4:h>=ta.threeArrowsColored&&(g=3),!i)return 0;for(l=0,m=g-1;m>0;m--)if(m<S(i)+1&&(j=i[m-1],!C(j&&j.iconValue)&&(n=r.pW(a,b,c,m-1),!C(n)&&(o=j.isGreaterThanOrEqualTo?f>=n:f>n,f<Number.MAX_VALUE&&o)))){l=m;break}return p=r.yW(l),q=r.CZa,q?{iconSetType:q[p]?q[p].iconSetType:ta.noIcons,iconIndex:q[p]?q[p].iconIndex:0,showIconOnly:r.showIconOnly()}:{iconSetType:ta.noIcons,iconIndex:0,showIconOnly:r.showIconOnly()}},b.prototype.reset=function(){var a=this;a.ranges(k),a.condition(k),a.style(k),a.showIconOnly(!1),a.reverseIconOrder(!1),a.iconSetType(0,k),a.NV=k,a.stopIfTrue(!1),a.priority(1),a.minColor(k),a.minValue(k),a.minType(5),a.midColor(k),a.midValue(k),a.midType(k),a.maxColor(k),a.maxValue(k),a.maxType(7),a.CZa=k},b.prototype.icons=function(a){return 1===arguments.length&&(this.CZa=a),this.CZa},b.prototype.iconCriteria=function(){return this.NV},b.prototype.toJSON=function(a){return _.call(this,a,Wa)},b.prototype.fromJSON=function(a,b,c){aa.call(this,b,Wa,a,c),this.initCondition(b)},b.paintIconSet=function(a,c,d,e,f,g,h,j,k){var l,m=d+1,n=e+2,o=parseInt(16*k+"",10),p=parseInt(16*k+"",10);if(c.showIconOnly&&(1===h.hAlign?m=d+f/2-o/2:2===h.hAlign&&(m=d+f-o-2)),1===h.vAlign?n=e+g/2-p/2:2===h.vAlign&&(n=e+g-p-2),c.iconSetType!==ta.noIcons){l=b.getIcon(c.iconSetType,c.iconIndex);try{l&&j&&("string"===i.getType(l)?db(a,j,l,m,n,o,p):db(a,j,l.image,l.x,l.y,l.w,l.h,m,n,o,p))}catch(a){}}},b.zW=function(){return"data:image/png;base64,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";
},b.getIcon=function(a,c){var d,e,f,g,h,i,j=[[160,180,80,k,k],[100,120,140,k,k],[900,920,880,k,k],[820,840,860,k,k],[780,1e3,420,k,k],[680,940,400,k,k],[800,1020,440,k,k],[740,1040,400,k,k],[720,980,380,k,k],[700,960,360,k,k],[160,40,60,80,k],[100,0,20,140,k],[220,340,460,760,k],[600,620,640,660,k],[200,680,940,400,k],[160,40,180,60,80],[100,0,120,20,140],[580,600,620,640,660],[480,500,520,540,560],[240,260,280,300,320]],l="0,",m=",16,16";for(d=0;d<S(j);d++)for(e=0;e<S(j[d]);e++)f=j[d][e],f!==k&&(j[d][e]=l+f+m);return g=b.zW(),j[a]?(h=j[a][c],h?(i=h.split(","),{image:g,x:parseFloat(i[0]),y:parseFloat(i[1]),w:parseFloat(i[2]),h:parseFloat(i[3])}):k):k},b}(Qa),b.IconSetRule=Xa,Ya={iconSetType:$("iconSetType",ta.threeArrowsColored,function(a){this.xW(a)}),reverseIconOrder:$("reverseIconOrder",!1),showIconOnly:$("showIconOnly",!1)},i.extend(Xa.prototype,Ya);function gb(a){return"string"==typeof a&&(a=i.trim(a),"="!==a[0]&&(a='="'+j.Gb(a,'"','""')+'"')),a}Za=function(){function a(a){this.AW=[],this.BW=k,this.WS=a}return a.prototype.getRule=function(a){return this.AW[a]},a.prototype.count=function(){return S(this.AW)},a.prototype.w4=function(){return this.AW},a.prototype.UV=function(a){var b,c,d=[],e=this.WS,f=S(a);for(b=0;b<f;b++)c=e.Tq(a[b]),d.push(c);return d},a.prototype.EW=function(a){var b,c,d,e,f=[],g=this.WS;for(b=0,c=S(a);b<c;b++)d=a[b],e=new N(d.row,d.col,ab(d),bb(d)),0===d.row&&ab(d)===g.getRowCount()&&(e.row=-1),0===d.col&&bb(d)===g.getColumnCount()&&(e.col=-1),f.push(e);return f},a.prototype.Ayb=function(a,b){var c,d,e=[];for(c=0;c<S(this.AW);c++)d=this.AW[c],d.contains(a,b)&&e.push(d);return e},a.prototype.addSpecificTextRule=function(a,b,c,d){var e=new Ma(2,d,c,a,k,k,b);return this.addRule(e)},a.prototype.addCellValueRule=function(a,b,c,d,e){var f=new Ma(1,e,d,a,gb(b),gb(c));return this.addRule(f)},a.prototype.addDateOccurringRule=function(a,b,c){var d=new Ma(4,c,b,k,k,k,k,k,a);return this.addRule(d)},a.prototype.addFormulaRule=function(a,b,c){var d=new Ma(3,c,b,k,k,k,k,a);return this.addRule(d)},a.prototype.addTop10Rule=function(a,b,c,d){var e=new Ma(5,d,c,k,k,k,k,k,a,b);return this.addRule(e)},a.prototype.addUniqueRule=function(a,b){var c=new Ma(6,b,a);return this.addRule(c)},a.prototype.addDuplicateRule=function(a,b){var c=new Ma(7,b,a);return this.addRule(c)},a.prototype.addAverageRule=function(a,b,c){var d=new Ma(8,c,b,k,k,k,k,k,a);return this.addRule(d)},a.prototype.add3ScaleRule=function(a,b,c,d,e,f,g,h,i,j){var k;return k=0===arguments.length?new Qa(11):new Qa(11,a,b,c,d,e,f,g,h,i,j),this.addRule(k)},a.prototype.add2ScaleRule=function(a,b,c,d,e,f,g){var h;return h=0===arguments.length?new Qa(10):new Qa(10,a,b,c,k,k,k,d,e,f,g),this.addRule(h)},a.prototype.addDataBarRule=function(a,b,c,d,e,f){var g;return g=0===arguments.length?new Ta:new Ta(a,b,c,d,e,f),this.addRule(g)},a.prototype.addIconSetRule=function(a,b){var c=new Xa(a,b);return this.addRule(c)},a.prototype.addRule=function(a){var b,c,d,e,f=this,g=f.WS;if(g){if(!a)throw Error(X().Exp_RuleIsNull);for(g.ITa.YUa(),b=0,c=S(f.AW);b<c;b++)d=f.AW[b]._ps,e=d.priority||1,d.priority=e+1;return a.priority(1),f.AW.push(a),a.context(g),g.$p(),a}},a.prototype.removeRule=function(a){var b=this,c=b.WS;c&&(a&&(c.ITa.YUa(),Q(b.AW,a)),c.$p())},a.prototype.IW=function(a,b,c,d,e){var f,g,h,i,j,l,m,n,q,r,s,t,u;return a.intersect(b,c,d,e)?(f=a.row,g=a.row+ab(a)-1,h=a.col,i=a.col+bb(a)-1,j=b,l=b+d-1,m=c,n=c+e-1,q=[],f!==-1&&h!==-1&&j!==-1&&m!==-1&&(m-h>0&&(r=M(f,h,ab(a),m-h),q.push(r)),i-n>0&&(s=M(f,n+1,ab(a),i-n),q.push(s)),j-f>0&&(t=M(f,p(m,h),j-f,o(n,i)-p(m,h)+1),q.push(t)),g-l>0&&(u=M(l+1,p(m,h),g-l,o(n,i)-p(m,h)+1),q.push(u))),S(q)>0?q:k):[a]},a.prototype.removeRuleByRange=function(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p=this,q=p.WS;if(q){if(e=[],p.AW)for(q.ITa.YUa(),f=0,g=S(p.AW);f<g;f++)if(h=p.AW[f],h&&h.ranges()&&h.intersects(a,b,c,d)){for(i=[],j=p.UV(h.ranges()),k=0,l=S(j);k<l;k++)m=p.IW(j[k],a,b,c,d),m&&(i=i.concat(m));S(i)>0?h.ranges(p.EW(i)):e.push(h)}for(n=0,o=S(e);n<o;n++)Q(p.AW,e[n]);q.$p()}},a.prototype.clearRule=function(){var a=this,b=a.WS;b&&(b.ITa.YUa(),a.AW.length=0,b.$p())},a.prototype.getRules=function(a,b){var c,d,e,f,g,h=this,i=h.AW;if(0===arguments.length||0===S(i))return i;if(a=a===l?-1:a,b=b===l?-1:b,c=h.WS,d=c.getRowCount(),e=c.getColumnCount(),f=[],a!==-1&&b!==-1)hb(f,h.Ayb(a,b));else if(a===-1)for(g=0;g<d;g++)hb(f,h.Ayb(g,b));else for(g=0;g<e;g++)hb(f,h.Ayb(a,g));return f},a.prototype.containsRule=function(a,b,c){return!(!a||!P.Bb(this.AW,a))&&a.contains(b,c)},a.prototype._V=function(a,b,c){var d,e,f,g=this;if(g.AW&&g.WS)for(d=S(g.AW),e=0;e<d;e++)f=g.AW[e],f&&(c?f.rI(a,b):f.tI(a,b))},a.prototype.rI=function(a,b){this._V(a,b,!0)},a.prototype.tI=function(a,b){this._V(a,b,!1)},a.prototype.aW=function(a,b,c){var d,e,f,g=this;if(g.AW&&g.WS)for(d=S(g.AW),e=d-1;e>=0;e--)f=g.AW[e],f&&(c?f.GR(a,b):f.HR(a,b),f.ranges().length<=0&&g.AW.splice(e,1))},a.prototype.GR=function(a,b){this.aW(a,b,!0)},a.prototype.HR=function(a,b){this.aW(a,b,!1)},a.prototype.Nm=function(){var a,b,c,d=this.AW;if(d!==k&&S(d)>0)for(b=0,c=S(d);b<c;b++)a=d[b],a.Nm&&a.Nm()},a.prototype.XA=function(a,b,c,d,e,f,g,i){var j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,I,J,K=[],L=[],N=[],O=this,P=O.WS,Q=[];for(a===-1&&(a=0,d=0,f=P.getRowCount()),b===-1&&(b=0,e=0,g=P.getColumnCount()),j=0;j<f;j++)for(k=0;k<g;k++)for(l=O.getRules(a+j,b+k),m=0;m<S(l);m++){for(n=l[m],o=n.ranges(),p=[],q=0;q<S(o);q++)r=o[q],s=P.Tq(r),t=s.row,u=s.col,v=ab(s),w=bb(s),R(N,r)<=-1&&r.contains(a+j,b+k)&&(N.push(r),x=M(d+j,e+k,-1,-1),t+v>=f+a?x.rowCount=f-j:a<t?x.rowCount=v:x.rowCount=t+v-a,u+w>=g+b?x.colCount=g-k:b<u?x.colCount=w:x.colCount=u+w-b,O===c&&r.containsRange(x)||p.push(x),i&&Q.indexOf(x)===-1&&Q.push(x));p.length>0&&(y=K.indexOf(n),y<0?(K.push(n),L.push(p)):L[y]=L[y].concat(p))}for(z=0,A=K.length;z<A;z++)B=O.iga(K[z].toJSON(),K[z].context()),C=B.ranges(),B.ranges(L[z]),D=B.Yw,D&&(E=1===P.parent.options.referenceStyle,F=H(C),G=h.formulaToExpression(P,D,F.r,F.c,E),I=H(L[z]),J=h.expressionToFormula(P,G,I.r,I.c,E),B.formula(J)),c.addRule(B);return Q},a.prototype.YA=function(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,q,r,s,t,u,v,w,x=[],y=this,z=y.WS,A=z.getRowCount(3),B=z.getColumnCount(3);for(a===-1&&(a=0,c=z.getRowCount()),b===-1&&(b=0,d=z.getColumnCount()),g=0;g<c;g++)for(h=0;h<d;h++)for(i=y.getRules(a+g,b+h),j=0;j<S(i);j++){for(k=i[j],l=k.ranges(),m=[],n=0;n<S(l);n++)q=l[n],r=z.Tq(q),s=r.row,t=r.col,u=ab(r),v=bb(r),R(x,q)<=-1&&q.contains(a+g,b+h)&&(x.push(q),a>s&&m.push(M(s,t,a-s,v)),b>t&&m.push(M(p(a,s),t,o(a+c,s+u)-p(a,s),b-t)),b+d<t+v&&m.push(M(p(a,s),b+d,o(a+c,s+u)-p(a,s),t+v-(b+d))),a+c<s+u&&m.push(M(a+c,t,s+u-(a+c),v)));y.Byb(z,x,e,l,A,B,f),w=l.concat(m),w.length>0?(k.yyb(),k.ranges(w)):y.removeRule(k)}},a.prototype.Byb=function(a,b,c,d,e,f,g){var h,i,j,k,l,m;for(h=0;h<S(b);h++){if(i=b[h],j=[i],g)for(k=0;k<S(c);k++)l=c[k],i.intersect(l.row,l.col,l.rowCount,l.colCount)&&(j=a.v_a(i,l,e,f));for(m=0;m<S(j);m++)Q(d,j[m])}},a.prototype.jp=function(a,b,c,d,e,f,g,h){var i=16,j=!1,k=!1,l=h.sheet,m=h.row,n=h.col,o=l.zoom(),p=this.oRa(l,m,n,b),q=p.iconSet,r=p.dataBar;return(r||q)&&(a.save(),a.beginPath(),r&&(Ta.paintDataBar(a,r,c,d,e,f),j=r.showBarOnly),q&&(i=parseInt(i*o+"",10),(e<i||f<i)&&(a.rect(c,d,e,f),a.clip(),a.beginPath()),Xa.paintIconSet(a,q,c,d,e,f,g,h.imageLoader,o),k=q.showIconOnly),a.restore()),j||k},a.prototype.oRa=function(a,b,c,d){var e,f,g,h,i,j,l,m,n,o=this,p=a.ss,q=p.Um(b,c);if(q)e=q.i,f=q.d;else{if(g=o.getRules(b,c),h=S(g),h>0){for(g.sort(function(a,b){return a.priority()-b.priority()}),i=k,j=k,l=void 0,m=void 0,n=void 0,n=0;n<h&&(!i||!j);n++)l=g[n],l&&(E(l,Ta)&&(i=l),E(l,Xa)&&(j=l));if(i||j)for(i=j=k,n=0;n<h&&(!i||!j)&&(l=g[n],!(l&&(!i&&E(l,Ta)&&(i=l),!j&&E(l,Xa)&&(j=l),l.stopIfTrue()&&(m=l.evaluate(a,b,c,d)))));n++);i&&(f=i.evaluate(a,b,c,d)),j&&(e=j.evaluate(a,b,c,d))}p.Wm(b,c,f,e)}return{dataBar:f,iconSet:e}},a.prototype.bp=function(a,b,c){var d,e,f;if(C(c)||3===c)for(d=this.getRules(a,b),e=0,f=S(d);e<f;e++)if(d[e]instanceof Xa)return!0;return!1},a.prototype.toJSON=function(a){var b,c,d=[];for(b=0;b<S(this.AW);b++)c=this.AW[b],d.push(c?c.toJSON(a):k);return 0===S(d)?l:{rules:d}},a.prototype.fromJSON=function(a,b,c){var d,e,f;if(a&&a.rules)for(this.AW=[],d=0;d<S(a.rules);d++)e=a.rules[d],f=this.iga(e,b,c),f&&this.AW.push(f)},a.prototype.iga=function(a,b,c){var d,e,f=k;return a&&(d=this.JW(),e=d[a.ruleType],e&&(f=new e,f.fromJSON(a,b,c))),f},a.prototype.JW=function(){if(!this.BW){var a={};a[0]=Ma,a[1]=Ma,a[2]=Ma,a[3]=Ma,a[4]=Ma,a[5]=Ma,a[6]=Ma,a[7]=Ma,a[8]=Ma,a[9]=Qa,a[10]=Qa,a[11]=Qa,a[12]=Ta,a[13]=Xa,this.BW=a}return this.BW},a.prototype.Eq=function(a,b,c,d){var e,f,g,h,i,j,m=this;if(m&&m.count()>0&&(e=m.getRules(b,c),f=S(e),g=void 0,h=k,f>0))for(e.sort(function(a,b){return a.priority()-b.priority()}),i=0;i<f&&(g=e[i],!(g&&(j=m.WS,g.isScaleRule()?10!==g.ruleType()&&11!==g.ruleType()||(h=g.evaluate(j,b,c,j.getValue(b,c,d)),h&&(a||(a=new J),a.backColor===l&&(a.backColor=h))):(h=g.evaluate(j,b,c,j.getValue(b,c,d)),h&&(a||(a=new J),a.Yo(h,!1,1))),g.stopIfTrue()&&h)));i++);return a},a.prototype.Fq=function(a,b,c,d){var e,f,g,h,i,j,m,n={v5:!1},o=this;if(o&&o.count()>0&&(e=o.getRules(a,b),f=S(e),g=void 0,h=k,f>0))for(e.sort(function(a,b){return a.priority()-b.priority()}),i=0;i<f;i++)if(g=e[i]){if(j=o.WS,g.isScaleRule()){if(m=g.ruleType(),"backColor"===c&&(10===m||11===m)&&(h=g.evaluate(j,a,b,j.getValue(a,b,d)))){n.Fi=h,n.v5=!0;break}}else if(h=g.evaluate(j,a,b,j.getValue(a,b,d)),h&&h[c]!==l){n.Fi=h[c],n.v5=!0;break}if(g.stopIfTrue()&&h)break}return n},a}(),b.ConditionalFormats=Za;function hb(a,b){if(b)for(var c=0;c<b.length;c++)a.push(b[c])}},"./dist/plugins/conditional/conditional.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/conditional/conditional.res.en.js");b.SR={en:d}},"./dist/plugins/conditional/conditional.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_RuleIsNull="The argument 'rule' is null",b.Exp_NotSupported="NotSupportException"},CalcEngine:function(a,b){a.exports=GC.Spread.CalcEngine},Common:function(a,b){a.exports=GC.Spread},Core:function(a,b){a.exports=GC.Spread.Sheets},SheetsCalc:function(a,b){a.exports=GC.Spread.Sheets.CalcEngine}});