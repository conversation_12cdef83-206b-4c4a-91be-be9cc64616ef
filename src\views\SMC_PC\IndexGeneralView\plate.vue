<!--
 * @Description: 版块
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 16:20:43
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-11-15 09:02:09
-->
<template>
  <div class="_plate _flex">
    <!-- 筛选 -->
    <div class="_left">
      <div class="filter _flex">
        <span>业务版块</span>
        <span
          class="_item"
          :class="[activeIdx === index ? 'active' : '']"
          v-for="(item, index) in newPlateList"
          :key="index"
          @click="plateClick(index)"
        >
          {{ item.businessSegments }}
        </span>
      </div>
      <div class="input-filter">
        <a-select
          show-search
          :value="searchIndexName"
          placeholder="请输入指标名称"
          style="width: 200px"
          @change="handleSelectChange"
          :filter-option="filterOption"
          :allowClear="true"
        >
          <a-select-option v-for="d in newCardList" :key="d">
            {{ d }}
          </a-select-option>
        </a-select>
      </div>
      <div class="indexStatus _flex" style="margin-left: 24px;">
        <span>指标状态：</span>
        <a-select
          style="width: 160px"
          :dropdownMatchSelectWidth="false"
          v-model="indexStatus"
          allowClear
          @change="
            e => {
              this.$emit('indexStatusChnage', e);
            }
          "
        >
          <a-select-option
            :value="item.key"
            v-for="item in statusOption"
            :key="item.key"
          >
            {{ item.value }}
          </a-select-option>
        </a-select>
      </div>
    </div>
    <!-- 分析 -->
    <div class="analysis" @click="showDrawer">
      查看专项分析 &#8594;
    </div>
    <!-- 专项分析抽屉 -->
    <AnalysisDrawer
      :analysisList="analysisList"
      ref="AnalysisDrawer"
      :companyName="companyName"
    />
  </div>
</template>
<script>
import AnalysisDrawer from "./analysisDrawer.vue";
import request from "@/utils/requestHttp";
import { adminUserUrlPrefix } from "@/utils/utils";
import sortBy from "lodash/sortBy";

export default {
  components: { AnalysisDrawer },
  props: {
    // 卡片列表
    cardList: {
      type: Array,
      default() {
        return [];
      }
    },
    // 该公司下的所有版块列表
    plateList: {
      type: Array,
      default() {
        return [];
      }
    },
    companyName: String,
    searchIndexName: String,
    base: String // 搜索条件中的base
  },
  watch: {
    cardList: {
      handler(value) {
        if (value) {
          // 监听卡片列表重新点击版块
          let idx = 0;
          this.newPlateList.forEach((item, index) => {
            if (item.businessSegments === this.activeIdxTxt) {
              idx = index;
            }
          });
          this.plateClick(idx);
        }
      },
      deep: true
    }
  },
  computed: {
    // 增加全部选项
    newPlateList() {
      let plateList = sortBy(this.plateList, item => item.sort);
      plateList = plateList.map(item => item.businessSegments);
      const list = Array.from(
        new Set(this.cardList.map(item => item.plateName))
      );
      for (let i = 0; i < plateList.length; i++) {
        if (!list.includes(plateList[i])) {
          plateList.splice(i, 1);
          i--;
        }
      }
      return [
        { businessSegments: "全部" },
        ...plateList.map(item => {
          return { businessSegments: item };
        })
      ];
    },
    // 下拉框数据
    newCardList() {
      const list =
        this.newPlateList[this.activeIdx]?.businessSegments === "全部"
          ? this.cardList.map(item => item.indexName)
          : this.cardList
              .filter(
                item =>
                  item.plateName ===
                  this.newPlateList[this.activeIdx]?.businessSegments
              )
              .map(item => item.indexName);
      return Array.from(new Set(list));
    }
  },
  data() {
    return {
      activeIdx: 0,
      activeIdxTxt: "",
      indexStatus: "", // 指标状态
      analysisList: [], // 专项分析列表
      statusOption: [
        {
          key: "0",
          value: "订阅KPI未达标数量"
        },
        {
          key: "1",
          value: "未达标同环比恶化"
        }
      ]
    };
  },
  methods: {
    // 设置状态
    setStatus(e) {
      this.indexStatus = e;
    },
    unique(arr1) {
      const res = new Map();
      return arr1.filter(a => !res.has(a.indexName) && res.set(a.indexName, 1));
    },
    /**
     * @description: 选中版块
     * @param {Number} index 下标
     */
    plateClick(index) {
      this.activeIdx = index;
      this.$emit("plateChange", {
        plate: this.newPlateList[index].businessSegments
      });
    },
    // 打开抽屉
    async showDrawer() {
      await this.getAnalysisList();
      this.$refs["AnalysisDrawer"].show();
    },
    // 获取专项分析列表
    getAnalysisList() {
      return new Promise(resolve => {
        request(
          `${
            adminUserUrlPrefix["lxp"]
          }/indexCardUrl/getPlateListByCompany?company=${
            this.companyName === "集团" ? this.base : this.companyName
          }`
        ).then(res => {
          if (Array.isArray(res) && res.length) {
            this.analysisList = res;
          }
          resolve(res);
        });
      });
    },
    // 下拉框搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 下拉框选中
    handleSelectChange(key) {
      this.$emit("update:searchIndexName", key || "");
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage {
  ._plate {
    padding-bottom: 12px;
    justify-content: space-between;
    ._left {
      display: flex;
      align-items: center;
      .input-filter {
        margin-left: 10px;
      }
    }
    .filter {
      height: 32px;
      span {
        &:first-child {
          margin-right: 16px;
        }
        &._item {
          height: 24px;
          font-size: 12px;
          line-height: 24px;
          padding: 0 12px;
          background: #f2f3f7;
          border-radius: 12px;
          cursor: pointer;
          &:not(:last-child) {
            margin-right: 12px;
          }
          transition: all ease-in-out 0.3s;
          cursor: pointer;
          &:hover {
            background: #dfe1e8;
          }
          &.active {
            background: #00aaa6;
            color: #fff;
          }
        }
      }
    }
    .analysis {
      width: 200px;
      height: 32px;
      border-radius: 4px;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      box-sizing: border-box;
      background-image: url("~@/assets/images/plate_bg.png");
      padding-left: 16px;
      color: #ffffff;
      line-height: 32px;
      cursor: pointer;
    }
  }
}
</style>
