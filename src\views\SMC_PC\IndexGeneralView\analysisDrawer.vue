<!--
 * @Description: 专项分析抽屉
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-09 09:08:29
 * @LastEditors: yuy<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-11-22 15:28:46
-->
<template>
  <a-drawer
    title="查看专项分析"
    placement="right"
    :visible="visible"
    @close="close"
    width="400px"
  >
    <div
      class="items"
      v-for="(item, index) in analysisList"
      :key="index"
      @click="toReport(item)"
    >
      {{ item.plateName }}专项分析
    </div>
  </a-drawer>
</template>
<script>
import request from "@/utils/requestHttp";
export default {
  props: {
    // 角色控制的按钮权限
    analysisList: {
      type: Array,
      default() {
        return [];
      }
    },
    companyName: String
  },
  data() {
    return {
      visible: false,
      SYS_NAME: window.system
    };
  },
  methods: {
    // 打开抽屉
    show() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    // 打开报表
    toReport(item) {
      request(`/api/smc/sysOperLog/saveLog`, {
        method: "POST",
        body: {
          menu: `${this.companyName}核心KPI横比${
            this.SYS_NAME === "industrialInternet" ? "-工业互联网平台" : ""
          }`,
          title: "查看专项分析",
          operation: `查看${this.companyName}${item.plateName}专项分析`
        }
      });
      if (window.self !== window.top) {
        // 不是在iframe内，向父级窗口发送数据
        window.parent.postMessage(
          {
            reportId: item.id,
            sourceType: "smc"
          },
          "*"
        );
      } else {
        request(`/api/smc/dt/getDtReportUrl?reportId=${item.id}`).then(res => {
          if (res.url) {
            window.vm.$router.push({
              path: "/smc_pc/reportPage",
              query: { reportUrl: decodeURIComponent(res.url) }
            });
            this.close();
          }
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.items {
  width: 352px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  box-sizing: border-box;
  background-image: url("~@/assets/images/analysis_bg.png");
  padding-left: 16px;
  color: #ffffff;
  line-height: 80px;
  cursor: pointer;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 12px;
}
</style>
