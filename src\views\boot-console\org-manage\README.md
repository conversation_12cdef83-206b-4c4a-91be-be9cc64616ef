<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2024-11-20 17:48:58
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-11-21 09:30:16
 * @FilePath: \localdev\pangea-component\src\views\boot-console\org-manage\README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
右侧点击出来的<基本信息>提示框
分为三个
1. 线体以上 [组织名称],[层级],[所属线体],[上级组织名称]
2. 线体 [工段名称],[上级班组],[层级]
3. 线体一下 [线体名称],[层次],[SAP编码],[DHR编码]
// 1. 集团，2. 公司，3. 中心，4. 基地，5. 工厂，6. 部门，7. 科室，8. 车间，9. 二级车间，10. 线体，
// 11. 二级线体，12. 班组，13. 大班组，14. 小班组 15. 班次
正式smc2Level