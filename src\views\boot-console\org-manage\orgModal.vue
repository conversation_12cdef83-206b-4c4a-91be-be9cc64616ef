<template>
  <a-modal v-model="visible" :title="isEdit ? '编辑' : '新增'" @ok="close">
    <a-form-model
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      ref="form"
    >
      <a-form-model-item label="组织树名称" prop="name">
        <a-input v-model="form.name" />
      </a-form-model-item>
    </a-form-model>
    <template slot="footer">
      <a-button key="back" @click="close">
        取消
      </a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="submit">
        提交
      </a-button>
    </template>
  </a-modal>
</template>
<script>
import request from "../../../utils/requestHttp";
export default {
  data() {
    return {
      visible: false, // 弹框控制
      isEdit: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      form: {
        name: ""
      },
      rules: {
        name: [{ required: true, trigger: "blur", message: "请输入组织树名称" }]
      },
      loading: false
    };
  },
  methods: {
    show(data) {
      this.visible = true;
      if (data) this.isEdit = true;
      this.form = { ...data };
    },
    close() {
      this.form = {
        name: ""
      };
      this.isEdit = false;
      this.visible = false;
    },
    submit() {
      this.$refs["form"].validate(valid => {
        console.log(valid);
        if (valid) {
          request(
            this.isEdit
              ? `/api/smc2/treeOrg/updateCMOrgTree`
              : `/api/smc2/treeOrg/insertCMOrgTree`,
            {
              method: "POST",
              body: {
                ...this.form
              }
            }
          ).then(res => {
            if (res && res.result !== "success") {
              this.$message.error(res.result);
            } else {
              this.close();
              this.$emit("ok");
            }
          });
        }
      });
    }
  }
};
</script>
