import { fetchEventSource } from '@microsoft/fetch-event-source'
// 从主utils.js导入其他功能
import {
  speak,
  pauseSpeech,
  resumeSpeech,
  stopSpeech,
  handleAiCopy,
  renderMarkdown
} from '../utils.js';

// 重新导出这些函数，以便其他组件可以从这个文件导入
export {
  speak,
  pauseSpeech,
  resumeSpeech,
  stopSpeech,
  handleAiCopy,
  renderMarkdown
};

/**
 * 用于请求SSE格式接口的通用工具函数
 * @param {Object} options - SSE请求选项
 * @param {string} options.url - 请求的URL
 * @param {Object} options.method - HTTP方法，默认为'POST'
 * @param {Object} options.headers - 请求头
 * @param {Object} options.body - 请求体，会被自动转为JSON字符串
 * @param {Function} options.onMessage - 接收到消息时的回调函数
 * @param {Function} options.onOpen - 连接打开时的回调函数
 * @param {Function} options.onClose - 连接关闭时的回调函数
 * @param {Function} options.onError - 发生错误时的回调函数
 * @param {Function} options.onComplete - 请求完成时的回调函数
 * @param {number} options.timeout - 请求超时时间(毫秒)，默认为30000
 * @param {boolean} options.withCredentials - 是否携带凭证，默认为true
 * @returns {Object} - 返回一个包含abort方法的控制器对象
 */
export function requestSSE(options) {
  const {
    url,
    method = 'POST',
    headers = {},
    body = {},
    onMessage,
    onOpen,
    onClose,
    onError,
    onComplete,
    timeout = 30000,
    withCredentials = true
  } = options;

  // 创建一个控制器，用于中止请求
  const controller = new AbortController();

  // 设置超时
  const timeoutId = setTimeout(() => {
    controller.abort();
    if (onError) {
      onError(new Error('请求超时'));
    }
  }, timeout);

  // 请求配置
  const fetchOptions = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    body: method !== 'GET' ? JSON.stringify(body) : undefined,
    signal: controller.signal,
    credentials: withCredentials ? 'include' : 'omit'
  };

  // 开始SSE请求
  fetchEventSource(url, {
    ...fetchOptions,
    async onopen(response) {
      clearTimeout(timeoutId);

      if (response.ok && onOpen) {
        onOpen(response);
      } else if (!response.ok && onError) {
        onError(new Error(`请求失败: ${response.status} ${response.statusText}`));
        controller.abort();
      }
    },
    onmessage(event) {
      try {
        if (onMessage) {
          // 尝试解析JSON数据
          let data;
          try {
            data = JSON.parse(event.data);
          } catch (e) {
            data = event.data;
          }
          onMessage(data, event);
        }
      } catch (error) {
        if (onError) {
          onError(error);
        }
      }
    },
    onclose() {
      clearTimeout(timeoutId);
      if (onClose) {
        onClose();
      }
      if (onComplete) {
        onComplete();
      }
    },
    onerror(error) {
      clearTimeout(timeoutId);
      if (onError) {
        onError(error);
      }
      // 返回true以防止自动重试
      return true;
    }
  });

  // 返回控制器对象，允许外部中止请求
  return {
    abort: () => {
      clearTimeout(timeoutId);
      controller.abort();
    }
  };
}



/**
 * 使用示例：
 *
 * // 导入
 * import { requestSSE } from './utils';
 *
 * // 使用
 * const controller = requestSSE({
 *   url: 'https://api.example.com/stream',
 *   method: 'POST',
 *   body: { prompt: '你好，AI' },
 *   onMessage: (data) => {
 *     console.log('收到消息:', data);
 *     // 处理流式响应数据
 *   },
 *   onError: (error) => {
 *     console.error('发生错误:', error);
 *   },
 *   onComplete: () => {
 *     console.log('请求完成');
 *   }
 * });
 *
 * // 如果需要中止请求
 * // controller.abort();
 */
