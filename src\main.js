/*
 * @Description: vue入口js文件
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-28 14:03:15
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2025-04-30 15:35:27
 * @FilePath: /vue-com/src/main.js
 */
import Vue from "vue";
import Antd from "ant-design-vue";
import Storage from "vue-ls";
import App from "./App.vue";
import router from "./router";
import i18n from "./locales";
import "ant-design-vue/dist/antd.less";
import PangeaCom from "pangea-com";
import "pangea-com/lib/index/style.css";
Vue.config.productionTip = false;
Vue.use(PangeaCom);
Vue.use(Antd);
Vue.use(Storage, {
  namespace: "pro__",
  name: "ls",
  storage: "local",
});

window.vm = new Vue({
  router,
  i18n,
  render: (h) => h(App),
}).$mount("#app");
