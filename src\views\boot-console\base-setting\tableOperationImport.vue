<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuyongjie
 * @LastEditTime: 2021-09-22 17:40:50
-->
<template>
  <div style="display: inline-block;">
    <a-upload
      name="file"
      :multiple="false"
      :showUploadList="false"
      :data="{}"
      :headers="headers"
      :action="newAction"
      :before-upload="beforeUpload"
      @change="handleChange"
    >
      <a-button :size="size || 'small'" :type="'primary'" :loading="loading">
        <!-- 导入 -->
        <a-icon type="upload" />导入
      </a-button>
    </a-upload>
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import { httpHeaders } from "../../../utils/utils";
export default {
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String,
    columns: Array
  },
  data() {
    return {
      newAction: "/api/smc2/codeValue/excels/companyWithDis",
      // newAction: ""
      headers: httpHeaders(),
      loading: false,
      showAlias
    };
  },
  methods: {
    beforeUpload() {
      this.loading = true;
    },
    handleChange({ file }) {
      console.log("file------->", file);
      if (file.status === "done") {
        this.loading = false;
        const res = file.response;
        // eslint-disable-next-line no-prototype-builtins
        if (res && res.hasOwnProperty("code") && res.code !== "0") {
          this.$message.error(res.msg);
          return;
        }
        this.$message.success(
          `${file.name}${showAlias(
            "FILEUPLOADEDSUCCESSFULLY",
            "文件上传成功"
          )}`,
          5
        );
      } else if (file.status === "error") {
        this.loading = false;
        let content = `${file.name}${showAlias(
          "FILEUPLOADFAILED",
          "文件上传失败"
        )}`;
        if (file.response && file.response.msg) {
          content = file.response.msg;
        }
        this.$message.error({
          title: `${file.name}${showAlias("ERRORMESSAGE", "错误信息")}`,
          content
        });
      }
    }
  }
};
</script>
