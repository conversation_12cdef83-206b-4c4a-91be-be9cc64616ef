/*
 * @Author: othniel <EMAIL>
 * @Date: 2025-05-13 16:33:06
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-05-13 20:06:20
 * @FilePath: \pangea-component\src\views\DMSBIPageReview\echarts\deliveryChart.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import * as echarts from "echarts";

/**
 * 初始化交付-日生产计划执行率图表
 * @param {HTMLElement} el - 图表容器DOM元素
 * @param {boolean} isDarkTheme - 是否为暗色主题
 * @param {Function} onSeriesClick - 点击series时的回调函数
 * @returns {echarts.ECharts} - echarts实例
 */
export const initDeliveryChart = (el, isDarkTheme = false, onSeriesClick = null) => {
  const chart = echarts.init(el);

  // 文本颜色根据主题设置
  const textColor = isDarkTheme ? "#ffffff" : "#333333";

  const option = {
    title: {
      text: '日生产计划执行率',
      left: 'center',
      textStyle: {
        color: textColor,
        fontSize: 14
      },
      show: false
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: isDarkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: isDarkTheme ? "#08343C" : "#fff",
      borderColor: isDarkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: isDarkTheme ? "#FFFFFF" : "#333",
      },
      z: 1, // 设置非常低的z-index值，确保不会覆盖模态框
      formatter: function (params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          // 获取正确的颜色
          let color;
          if (param.seriesName === '实际产量') {
            color = "#23B5B1"; // 实际产量的颜色
          } else if (param.seriesName === '目标产量') {
            color = "#6495F9"; // 目标产量的颜色
          } else if (param.seriesName === '达成率') {
            color = "#F7B500"; // 达成率的颜色
          } else {
            color = param.color; // 其他情况使用默认颜色
          }

          // 创建颜色标记
          const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;

          // 为达成率添加百分号
          let value = param.value;
          if (param.seriesName === '达成率') {
            value = value + '%';
          }

          result += colorSpan + param.seriesName + ': ' + value + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ["实际产量", "目标产量", "达成率"],
      // right: 50,
      // top: 0,
      textStyle: {
        color: textColor,
      },
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {},
      }
    },
    xAxis: {
      type: "category",
      data: ['暂无详细数据'],
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: [
      {
        type: "value",
        // name: "数量",
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
        axisLabel: {
          color: textColor,
        },
        axisLine: {
          lineStyle: {
            color: textColor,
          },
        },
      },
      {
        type: "value",
        // name: "百分比",
        axisLabel: {
          formatter: "{value}%",
          color: textColor,
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: textColor,
          },
        },
      },
    ],
    series: [
      {
        name: "实际产量",
        type: "bar",
        data: [0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 16,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "目标产量",
        type: "bar",
        data: [0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#6495F9" },
            { offset: 1, color: "rgba(100, 149, 249, 0.1)" },
          ]),
          width: "16px",
        },
        barWidth: 16,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "达成率",
        type: "line",
        yAxisIndex: 1,
        smooth: true,
        symbol: "none",
        data: [0],
        itemStyle: {
          color: "#F7B500",
        },
      },
    ],
  };

  chart.setOption(option);

  // 添加点击事件监听
  if (typeof onSeriesClick === 'function') {
    chart.off('click'); // 移除之前的点击事件
    chart.on('click', function(params) {
      // 只有点击柱状图时才触发回调
      if (params.componentType === 'series' && (params.seriesName === '实际产量' || params.seriesName === '目标产量')) {
        onSeriesClick(params);
      }
    });
  }

  return chart;
};

export default initDeliveryChart;
