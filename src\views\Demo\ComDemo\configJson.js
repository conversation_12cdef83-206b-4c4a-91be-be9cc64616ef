/*
 * @Description: 组件配置描述json
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2020-09-03 16:08:36
 * @LastEditors: gao<PERSON><PERSON>
 * @LastEditTime: 2021-04-25 18:05:53
 * @FilePath: /vue-com/src/views/Demo/ComDemo/configJson.js
 */
const comJson = {
  json: [
    // 输入框属性示例
    {
      type: "input",
      col: "width",
      label: "宽度",
      props: {
        "addon-after": "px" // 后置标签
      }
    },
    // 动态表格属性示例
    {
      type: "dynamicTable",
      col: "buttons",
      label: "",
      props: {
        rowKey: "name", // 表格唯一键字段
        btnText: "添加按钮", // 按钮文案
        // 下拉框数据源类型，dataSource为设计器数据源类型，下拉选项添加字段，custom类型为自定义类型，下拉选项需要在options参数进行设置。
        optionsType: "custom",
        options: [
          {
            key: "add",
            value: "新增"
          },
          {
            key: "delete",
            value: "删除"
          }
        ], // 当optionsType为custom时需要设置
        itemConfigJson: [
          {
            type: "input",
            col: "name",
            label: "按钮名称",
            props: {}
          },
          {
            type: "i18nInputBox",
            col: "title",
            label: "显示名称",
            props: {}
          },
          {
            type: "switch",
            col: "isShow",
            label: "是否展示",
            default: true,
            props: {}
          },
          {
            type: "uploadMinioButton",
            col: "jsUrl",
            label: "上传文件",
            props: {}
          }
        ]
      }
    }
  ],
  // 组件接收到的参数
  props: {
    width: '100',
    buttons: []
  },
  // 对外暴漏的事件
  events: [
    {
      name: '组件事件名称',
      method: 'handleMethod'
    }
  ]
};
export default comJson;
