<!--
 * @Description: 弹窗
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2024-09-30 15:17:55
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <a-form-model
      ref="mainForm"
      :model="mainForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row>
        <a-col :span="8">
          <a-form-model-item label="指标名称" prop="indexId">
            <a-select
              show-search
              :value="mainForm.indexId"
              placeholder="请输入指标名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleSearch"
              @change="handleChange"
              :dropdownMatchSelectWidth="false"
              option-label-prop="label"
            >
              <a-select-option
                v-for="d in indexList"
                :key="
                  `${d.indexId}-${d.signOrgId}-${d.businessSegmentsId}-${d.indexFrequencyId}`
                "
                :label="d.indexName"
              >
                <div style="display: flex;justify-content: space-between;">
                  {{ d.indexName }}
                  <span
                    style="font-size: 12px;color: rgba(0, 0, 0, 0.3);margin-left: 5px;"
                    >{{
                      `${d.signOrgName}/${d.businessSsegments}/${d.indexFrequency}`
                    }}</span
                  >
                </div>
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="注册组织/公司">
            <a-input disabled v-model="indexInfo.signOrgName" />
          </a-form-model-item>
          <a-form-model-item label="计算组织" prop="orgId">
            <OrgTreeSelect
              :activeOrgTreeId="treeType"
              ref="orgTreeSelect"
              :disabled="isEdit"
              @divClick="orgDivClick"
              @choose="
                ({ fullCode, dhrCode }) => {
                  this.mainForm.orgId = dhrCode;
                  this.mainForm['fullCode'] = fullCode;
                }
              "
            ></OrgTreeSelect>
          </a-form-model-item>
          <a-form-model-item label="自定义指标名称">
            <a-input v-model="mainForm.indexNameInd" />
          </a-form-model-item>
          <a-form-model-item label="资产管理平台id">
            <a-input disabled v-model="indexInfo.dmId" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="指标单位">
            <a-select
              v-model="indexInfo.indexUnitId"
              :placeholder="`请选择指标单位`"
              allowClear
            >
              <a-select-option
                :value="item.id"
                v-for="item in unitList"
                :key="item.val"
              >
                {{ item.val }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="指标类型">
            <a-input disabled v-model="indexInfo.indexType" />
          </a-form-model-item>
          <a-form-model-item label="指标频次">
            <a-input disabled v-model="indexInfo.indexFrequency" />
          </a-form-model-item>
          <a-form-model-item label="对应层级描述" prop="classCompareDesc">
            <a-select
              v-model="mainForm['classCompareDesc']"
              :placeholder="`请选择对应层级描述`"
              allowClear
            >
              <a-select-option
                :value="item.id"
                v-for="item in dict['9'] || []"
                :key="item.val"
              >
                {{ item.val }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="责任人" prop="fillInPerson">
            <a-select
              show-search
              :value="mainForm.fillInPerson"
              :dropdownMatchSelectWidth="false"
              placeholder="请输入责任人名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleAccountSearch($event, 'fillInPerson')"
              @change="handleAccountChange($event, 'fillInPerson')"
            >
              <a-select-option
                v-for="d in fillInPersonList"
                :key="`${d.name}-${d.account}`"
                :value="`${d.name}-${d.account}`"
              >
                {{ d.name }}({{ d.account }}) {{ d.o }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="版块">
            <a-input disabled v-model="indexInfo.businessSsegments" />
          </a-form-model-item>
          <a-form-model-item label="精度">
            <a-input disabled v-model="indexInfo.precisions" />
          </a-form-model-item>
          <a-form-model-item label="是否工厂周">
            <a-input
              disabled
              :value="indexInfo.isFactoryWeek === 'Y' ? '是' : '否'"
            />
          </a-form-model-item>
          <a-form-model-item label="是否冻结" prop="isDelete">
            <a-select v-model="mainForm.isDelete" placeholder="请选择是否冻结">
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-Y/N'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item label="分管领导" prop="fillInPersonLeader">
            <a-select
              show-search
              :value="mainForm.fillInPersonLeader"
              :dropdownMatchSelectWidth="false"
              placeholder="请输入分管领导名称"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              @search="handleAccountSearch($event, 'fillInPersonLeader')"
              @change="handleAccountChange($event, 'fillInPersonLeader')"
            >
              <a-select-option
                v-for="d in fillInPersonLeaderList"
                :key="`${d.name}-${d.account}`"
                :value="`${d.name}-${d.account}`"
              >
                {{ d.name }}({{ d.account }}) {{ d.o }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-divider dashed />
      <a-row>
        <a-col :span="8" v-for="(item, index) in mainFormArray" :key="index">
          <a-form-model-item
            v-for="subitem in item"
            :label="subitem.label"
            :key="subitem.key"
            :prop="subitem.key"
            v-show="(subitem.key !== 'isCalculate' && !isEdit) || isEdit"
          >
            <a-select
              :key="subitem.key"
              v-model="mainForm[subitem.key]"
              :placeholder="`请选择${subitem.label}`"
              allowClear
              :dropdownMatchSelectWidth="false"
              :options="
                dict[subitem.dict]?.map((dickItem) => {
                  return {
                    value: subitem.isPangeaDICT ? dickItem.key : dickItem.id,
                    label: subitem.isPangeaDICT ? dickItem.value : dickItem.val,
                  };
                })
              "
            >
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-divider dashed />
    <a-button
      type="primary"
      v-if="!isEdit && mainForm.indexId"
      style="margin-bottom: 20px;"
      @click="addNewData"
      >新增维度值</a-button
    >
    <a-table
      :pagination="false"
      :columns="
        !isEdit
          ? [
              ...columns,
              {
                title: '操作',
                key: 'action',
                scopedSlots: { customRender: 'action' },
              },
            ]
          : columns
      "
      :data-source="productAttArr"
    >
      <!-- 序号 -->
      <template slot="index" slot-scope="text, record, index">
        <span>{{ index + 1 }}</span>
      </template>
      <template
        v-for="i in 3"
        :slot="`productAtt${i}Id`"
        slot-scope="text, record, index"
      >
        <div style="display: flex;align-items: center;width: 100%;" :key="i">
          <a-tree-select
            v-model="record[`productAtt${i}Id`]"
            show-search
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="Please select"
            allow-clear
            style="width: 120px;margin-right: 10px;"
            tree-default-expand-all
            :tree-data="weiDuList"
            @change="treeSelectChange(record, index)"
          >
          </a-tree-select>
          <a-select
            allow-clear
            v-model="record[`productAtt${i}IdFlag`]"
            :dropdownMatchSelectWidth="false"
            style="width: 100px;"
          >
            <a-select-option value="卡片名称">
              卡片名称
            </a-select-option>
            <a-select-option value="卡片标签">
              卡片标签
            </a-select-option>
          </a-select>
        </div>
      </template>
      <template slot="goDown" slot-scope="text, record">
        <div style="display: flex;align-items: center;width: 100%;">
          <a-select
            v-model="record[`goDown`]"
            :dropdownMatchSelectWidth="false"
            style="width: 120px;margin-right: 10px;"
            v-if="!isEdit"
          >
            <!-- mode="multiple" -->
            <a-select-option
              v-for="item in record.goDownSelectList"
              :key="item.value"
              :value="item.value"
              >{{ item.title }}</a-select-option
            >
          </a-select>
          <a-input v-else v-model="record['goDownFlag']" disabled />
        </div>
      </template>
      <!-- 操作栏 -->
      <span slot="action" slot-scope="text, record, index">
        <a-tooltip placement="top">
          <template slot="title">
            <span>删除</span>
          </template>
          <a-icon @click="removeCondition(index)" type="delete" />
        </a-tooltip>
      </span>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
import OrgTreeSelect from "../components/org-tree-select.vue";
import debounce from "lodash/debounce";
import cloneDeep from "lodash.clonedeep";
import { uniqueByKey } from "@/utils/publicCustomTool";
export default {
  components: { OrgTreeSelect },
  data() {
    this.handleSearch = debounce(this.handleSearch, 1000);
    this.handleAccountSearch = debounce(this.handleAccountSearch, 800);
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      mainForm: {
        indexId: "",
        orgId: "",
        indexNameInd: "",
        classCompareDesc: "",
        isDelete: "",
        isManually: null,
        fillInPerson: "",
        fillInPersonLeader: "",
      },
      unitList: [],
      mainFormArray: [
        [
          {
            key: "isPartSum",
            label: "是否参与累加",
            dict: "smc-Y/N",
            isPangeaDICT: true,
          },
          {
            key: "isFromSum",
            label: "是否由累加计算",
            dict: "smc-Y/N",
            isPangeaDICT: true,
          },
          {
            key: "formulaId",
            label: "实际值计算公式",
            dict: "10",
            isPangeaDICT: false,
          },
          {
            key: "contemFormulaId",
            label: "同期值计算公式",
            dict: "18",
            isPangeaDICT: false,
          },
          {
            key: "previousFormulaId",
            label: "上期值计算公式",
            dict: "19",
            isPangeaDICT: false,
          },
          {
            key: "sumTargetFormulaId",
            label: "累计目标完成率计算公式",
            dict: "25",
            isPangeaDICT: false,
          },
          {
            key: "isCalculate",
            label: "是否展示",
            dict: "smc-Y/N",
            isPangeaDICT: true,
          },
        ],
        [
          {
            key: "contemRateFormulaId",
            label: "同比变化率计算公式",
            dict: "16",
            isPangeaDICT: false,
          },
          {
            key: "previousRateFormulaId",
            label: "环比变化率计算公式",
            dict: "17",
            isPangeaDICT: false,
          },
          {
            key: "sumValueFormulaId",
            label: "累计实际值计算公式",
            dict: "15",
            isPangeaDICT: false,
          },
          {
            key: "sumContemFormulaId",
            label: "累计同期值计算公式",
            dict: "20",
            isPangeaDICT: false,
          },
          {
            key: "sumPreviousFormulaId",
            label: "累计上期值计算公式",
            dict: "21",
            isPangeaDICT: false,
          },
          {
            key: "sumTargetValueWayId",
            label: "累计目标值获取方式",
            dict: "13",
            isPangeaDICT: false,
          },
        ],
        [
          {
            key: "sumRatioFormulaId",
            label: "累计占比计算公式",
            dict: "22",
            isPangeaDICT: false,
          },
          {
            key: "sumContemRateFormulaId",
            label: "累计同比变化率计算公式",
            dict: "23",
            isPangeaDICT: false,
          },
          {
            key: "sumPreviousRateFormulaId",
            label: "累计环比变化率计算公式",
            dict: "24",
            isPangeaDICT: false,
          },
          {
            key: "targetValueWayId",
            label: "目标值获取方式",
            dict: "12",
            isPangeaDICT: false,
          },
          {
            key: "targetFormulaId",
            label: "目标完成率计算公式",
            dict: "11",
            isPangeaDICT: false,
          },
          {
            key: "isManually",
            label: "是否手工填报",
            dict: "smc-Y/N",
            isPangeaDICT: true,
          },
        ],
      ],
      rules: {
        orgId: [
          {
            required: true,
            message: "请选择计算组织",
            trigger: "change",
          },
        ],
        indexId: [
          {
            required: true,
            message: "请选择指标名称",
            trigger: "change",
          },
        ],
        fillInPerson: [
          {
            required: true,
            message: "请填写责任人",
            trigger: "blur",
          },
        ],
        fillInPersonLeader: [
          {
            required: true,
            message: "请填写分管领导",
            trigger: "blur",
          },
        ],
        isDelete: [
          {
            required: true,
            message: "请选择是否冻结",
            trigger: "change",
          },
        ],
        isManually: [
          {
            required: true,
            message: "请选择是否手工填报",
            trigger: "change",
          },
        ],
        // dmId: [
        //   {
        //     required: true,
        //     message: "请输入资产管理平台ID",
        //     trigger: "blur"
        //   }
        // ]
      },
      indexList: [], // 指标列表
      isEdit: false, // 是否编辑状态
      dict: {}, // 字典
      indexInfo: {
        perfectId: "",
      }, // 选中指标信息
      treeType: "", // 选中的组织树
      productAttArr: [],
      columns: [
        {
          title: "序号",
          key: "index",
          scopedSlots: { customRender: "index" },
          width: 80,
        },
        {
          title: "维度1",
          scopedSlots: { customRender: "productAtt1Id" },
          width: 260,
        },
        {
          title: "维度2",
          scopedSlots: { customRender: "productAtt2Id" },
          width: 260,
        },
        {
          title: "维度3",
          scopedSlots: { customRender: "productAtt3Id" },
          width: 260,
        },
        {
          title: "下探维度",
          scopedSlots: { customRender: "goDown" },
          width: 140,
        },
      ], // 表格列
      weiDuList: [],
      fillInPersonList: [],
      fillInPersonLeaderList: [],
    };
  },
  methods: {
    handleAccountSearch(value, key) {
      this.getAcountList(value).then((res) => {
        this[`${key}List`] = res;
      });
    },
    handleAccountChange(value, key) {
      this.mainForm[key] = value;
    },
    getAcountList(account) {
      return new Promise((resolve) => {
        request(`/api/smc2/ldap/searchLdapUser`, {
          method: "POST",
          body: {
            account,
          },
        }).then((res) => {
          resolve(res || []);
        });
      });
    },
    treeSelectChange(record, index) {
      if (!this.isEdit) {
        // this.$set(this.productAttArr[index], "goDown", []);
        this.$set(this.productAttArr[index], "goDown", "");
        this.$set(
          this.productAttArr[index],
          "goDownSelectList",
          this.getGoDownSelect(record)
        );
      }
    },
    // 获取码值表数据
    getAllDict() {
      request("/api/smc2/codeValue/searchAllValue").then((res) => {
        this.unitList = res[2];
        for (let i = 9; i <= 25; i++) {
          for (const key in res) {
            if (Object.hasOwnProperty.call(res, key)) {
              const element = res[key];
              if (i.toString() === key) {
                this.dict[key] = element;
              }
            }
          }
        }
      });
    },
    addNewData() {
      const record = {
        productAtt1Id: "",
        productAtt1IdFlag: "",
        productAtt2Id: "",
        productAtt2IdFlag: "",
        productAtt3Id: "",
        productAtt3IdFlag: "",
        // goDown: [],
        goDown: "",
        goDownSelectList: [],
      };
      this.productAttArr.push(record);
      // 新增一条数据的时候更新下对应record的下拉纬度列表
      this.$nextTick(() => {
        this.$set(
          this.productAttArr[this.productAttArr.length - 1],
          "goDownSelectList",
          this.getGoDownSelect(record)
        );
      });
    },
    // 处理树数据
    getGoDownSelect(record) {
      const valuesArr = Object.values(record)
        .filter((s) => typeof s === "string" && s.includes("-"))
        .map((item) => item.split("-")[0]);
      return this.weiDuList.filter((item) => !valuesArr.includes(item.value));
    },
    // 表格下拉框共用change方法
    tableSelectChange(record, dataIndex, index) {
      if (
        record[`productAtt${index}Id`] &&
        record[`productAtt${index}IdFlag`]
      ) {
        this.$set(
          this.productAttArr[dataIndex],
          `productAtt${index + 1}IdDisabled`,
          false
        );
        this.$set(
          this.productAttArr[dataIndex],
          `productAtt${index + 1}IdTreeData`,
          this.dealTreeData(record)
        );
      }
    },
    // 获取指标列表
    getIndexList(indexName, indexId = "") {
      return new Promise((resolve) => {
        request(
          `/api/smc2/indexPerfect/searchIndexPerfect?pageNum=1&pageSize=10000${
            indexName ? "&indexName=" + indexName : ""
          }${indexId ? "&indexId=" + indexId : ""}`
        ).then((res) => {
          resolve(res.rows || []);
        });
      });
    },
    // 组织选中
    orgDivClick() {
      if (this.mainForm.indexId) {
        this.$refs["orgTreeSelect"].showDialog();
        // this.$refs["orgTreeSelect"].getYTOrgList(
        //   this.indexInfo.fullCode,
        //   false,
        //   this.indexInfo.signOrgId
        // );
        // 这里有点疑问，关于选择计算组织需要从注册组织开始选择
        this.$refs["orgTreeSelect"].setTopOrgList([
          {
            name: this.indexInfo.signOrgName,
            code: this.indexInfo.signOrgId,
            dhrCode: this.indexInfo.signOrgId,
            fullCode: this.indexInfo.fullCode,
          },
        ]);
      }
    },
    // 获取视图列表
    getOrgViewList() {
      request(`/api/smc2/treeOrg/searchOrgTree`, {
        method: "POST",
      }).then((res) => {
        this.treeType = Array.isArray(res) ? res[0].id : "";
      });
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-Y%2FN&languageCode=zh_CN"
        )
      ).then((res) => {
        let filterArr = uniqueByKey(res["smc-Y/N"], "key");
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = filterArr;
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    // 删除条件
    removeCondition(index) {
      this.productAttArr.splice(index, 1);
    },
    handleSearch(value) {
      value = value.replace(/^\s*|\s*$/g, ""); // 首尾去空格
      if (value) {
        this.getIndexList(value).then((res) => {
          this.indexList = res;
        });
      }
    },
    handleChange(value) {
      this.mainForm.indexId = value;
      this.indexInfo = this.indexList.filter((d) => {
        return (
          `${d.indexId}-${d.signOrgId}-${d.businessSegmentsId}-${d.indexFrequencyId}` ===
          value
        );
      })[0];
      this.getWeiDuList();
    },
    getWeiDuList() {
      return new Promise((resolve) => {
        this.weiDuList = [];
        this.productAttArr = [];
        function mapTreeList(arr, parentKey, setDisabled = false) {
          return arr.map((item) => {
            return {
              title: item.val,
              parentKey,
              value: parentKey ? `${parentKey}-${item.id}` : item.id,
              key: parentKey ? `${parentKey}-${item.id}` : item.id,
              children: item.list ? mapTreeList(item.list, item.id) : [],
              disabled: setDisabled,
            };
          });
        }
        request(`/api/smc2/indexPerfect/getDimensionVal`, {
          method: "POST",
          body: {
            id: this.indexInfo.id,
          },
        }).then((res) => {
          if (Array.isArray(res) && res.length) {
            this.weiDuList = mapTreeList(res, undefined, true);
          }
          resolve(this.weiDuList);
        });
      });
    },
    show(formValue) {
      this.visible = true;
      this.isEdit = false;
      this.getDICT();
      this.getAllDict();
      if (formValue) {
        const {
          id,
          indexNameInd,
          org,
          orgId,
          classCompareDesc,
          isDelete,
          fillInPerson,
          fillInPersonLeader,
          indexId,
          treeType,
          isPartSum,
          isFromSum,
          formulaId,
          contemFormulaId,
          previousFormulaId,
          sumTargetFormulaId,
          contemRateFormulaId,
          previousRateFormulaId,
          sumValueFormulaId,
          sumContemFormulaId,
          sumPreviousFormulaId,
          sumTargetValueWayId,
          sumRatioFormulaId,
          sumContemRateFormulaId,
          sumPreviousRateFormulaId,
          targetValueWayId,
          targetFormulaId,
          isManually,
          isCalculate,
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id,
          productAtt1Dimtp,
          productAtt2Dimtp,
          productAtt3Dimtp,
          productAtt4Dimtp,
          productAtt5Dimtp,
          productAtt6Dimtp,
          productAtt7Dimtp,
          perfectId,
          signOrgId,
          businessSegmentsId,
          indexFrequencyId,
        } = formValue;
        this.isEdit = true;
        this.mainForm = {
          id,
          indexNameInd,
          orgId,
          classCompareDesc,
          isDelete,
          fillInPerson,
          fillInPersonLeader,
          indexId: `${indexId}-${signOrgId}-${businessSegmentsId}-${indexFrequencyId}`,
          treeType,
          isPartSum,
          isFromSum,
          formulaId,
          contemFormulaId,
          previousFormulaId,
          sumTargetFormulaId,
          contemRateFormulaId,
          previousRateFormulaId,
          sumValueFormulaId,
          sumContemFormulaId,
          sumPreviousFormulaId,
          sumTargetValueWayId,
          sumRatioFormulaId,
          sumContemRateFormulaId,
          sumPreviousRateFormulaId,
          targetValueWayId,
          targetFormulaId,
          isManually,
          perfectId,
          isCalculate,
        };
        fillInPerson &&
          this.handleAccountSearch(fillInPerson.split("-")[0], "fillInPerson");
        fillInPersonLeader &&
          this.handleAccountSearch(
            fillInPersonLeader.split("-")[0],
            "fillInPersonLeader"
          );
        this.getIndexList("", indexId).then((indexList) => {
          this.indexList = indexList;
          this.indexInfo = this.indexList.filter((d) => {
            return (
              `${d.indexId}-${d.signOrgId}-${d.businessSegmentsId}-${d.indexFrequencyId}` ===
              this.mainForm.indexId
            );
          })[0];
          this.getWeiDuList().then(() => {
            this.treeType = treeType;
            // 1,2,3,4,5,6,7个字段里面有值且没有“-”的就是下探维度
            const goDownData = [
              productAtt1Id,
              productAtt2Id,
              productAtt3Id,
              productAtt4Id,
              productAtt5Id,
              productAtt6Id,
              productAtt7Id,
            ].filter((item) => item && !item.includes("-"));
            const normalData = [
              productAtt1Id,
              productAtt2Id,
              productAtt3Id,
              productAtt4Id,
              productAtt5Id,
              productAtt6Id,
              productAtt7Id,
            ].filter((item) => item && item.includes("-"));
            let goDownTextIndex = goDownData.length
              ? [
                  productAtt1Id,
                  productAtt2Id,
                  productAtt3Id,
                  productAtt4Id,
                  productAtt5Id,
                  productAtt6Id,
                  productAtt7Id,
                ]
                  .filter((item) => item)
                  .indexOf(goDownData[0])
              : 0;
            let goDownDataText = [
              productAtt1Dimtp,
              productAtt2Dimtp,
              productAtt3Dimtp,
              productAtt4Dimtp,
              productAtt5Dimtp,
              productAtt6Dimtp,
              productAtt7Dimtp,
            ].filter((item) => item);
            goDownDataText = goDownDataText[goDownTextIndex];
            this.productAttArr = [
              {
                productAtt1Id: normalData[0]
                  ? `${this.getKeyInWeiDuTreeList(
                      normalData[0].split("-")[0]
                    )}-${normalData[0].split("-")[0]}`
                  : "",
                productAtt1IdFlag: normalData[0]
                  ? normalData[0].split("-")[1]
                  : "",
                productAtt2Id: normalData[1]
                  ? `${this.getKeyInWeiDuTreeList(
                      normalData[1].split("-")[0]
                    )}-${normalData[1].split("-")[0]}`
                  : "",
                productAtt2IdFlag: normalData[1]
                  ? normalData[1].split("-")[1]
                  : "",
                productAtt3Id: normalData[2]
                  ? `${this.getKeyInWeiDuTreeList(
                      normalData[2].split("-")[0]
                    )}-${normalData[2].split("-")[0]}`
                  : "",
                productAtt3IdFlag: normalData[2]
                  ? normalData[2].split("-")[1]
                  : "",
                goDown: goDownData.length ? goDownData[0] : "",
                goDownFlag: goDownData.length ? goDownDataText : "",
                goDownSelectList: [],
              },
            ];
            this.$set(
              this.productAttArr[0],
              "goDownSelectList",
              this.getGoDownSelect(this.productAttArr[0])
            );

            // 给计算组织设置disabled
            this.$refs.orgTreeSelect.setOrgText(`${org}(${orgId})`);
          });
        });
      } else {
        this.getOrgViewList();
      }
    },
    getKeyInWeiDuTreeList(key) {
      let parentKey = "";
      this.weiDuList.forEach((item) => {
        item.children.forEach((s) => {
          if (s.value.split("-")[1] === key) {
            parentKey = s.parentKey;
          }
        });
      });
      return parentKey;
    },
    close() {
      this.mainForm = {
        indexId: "",
        orgId: "",
        indexNameInd: "",
        classCompareDesc: "",
        isDelete: "",
        isManually: null,
        fillInPerson: "",
        fillInPersonLeader: "",
      };
      this.indexList = [];
      this.weiDuList = [];
      this.productAttArr = [];
      this.fillInPersonList = [];
      this.indexInfo = {
        perfectId: "",
      };
      this.fillInPersonLeaderList = [];
      this.$refs.mainForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    // 提交
    submit() {
      this.$refs.mainForm.validate(async (valid) => {
        let hasError = false;
        let allValues = [];
        for (let index = 0; index < this.productAttArr.length; index++) {
          const item = this.productAttArr[index];
          // item.goDown.forEach(goDownItem => {
          allValues.push(
            item.productAtt1Id +
              item.productAtt1IdFlag +
              item.productAtt2Id +
              item.productAtt2IdFlag +
              item.productAtt3Id +
              item.productAtt3IdFlag +
              item.goDown
          );
          // });
          let selectWeiduArr = [
            item.productAtt1Id ? item.productAtt1Id.split("-")[0] : "",
            item.productAtt2Id ? item.productAtt2Id.split("-")[0] : "",
            item.productAtt3Id ? item.productAtt3Id.split("-")[0] : "",
          ];
          selectWeiduArr = selectWeiduArr.filter((item) => item);
          if (
            Array.from(new Set([...selectWeiduArr])).length !==
            selectWeiduArr.length
          ) {
            this.$message.error(
              `维度维护第${index + 1}行存在重复维度，请检查修改后提交`
            );
            hasError = true;
            break;
          }
          let selectWeiduArr2 = [
            item.productAtt1Id && !item.productAtt1IdFlag,
            item.productAtt2Id && !item.productAtt2IdFlag,
            item.productAtt3Id && !item.productAtt3IdFlag,
          ];
          if (selectWeiduArr2.includes(true)) {
            this.$message.error(
              `维度维护第${index + 1}行信息维护不全，请检查修改后提交`
            );
            hasError = true;
            break;
          }
        }
        if (Array.from(new Set([...allValues])).length !== allValues.length) {
          this.$message.error(
            `维度维护不同行存在相同维度信息，请检查修改后提交`
          );
          hasError = true;
        }
        if (hasError) {
          return false;
        }
        if (valid) {
          let postData = cloneDeep({ ...this.mainForm });
          postData["indexId"] = postData["indexId"].split("-")[0];
          postData["indexUnitId"] = this.indexInfo.indexUnitId;
          const productAttArr = cloneDeep(this.productAttArr);
          // const finalProductAttArr = [];
          productAttArr.forEach((item) => {
            for (let i = 0; i < 3; i++) {
              item[`productAtt${i + 1}Id`] = item[`productAtt${i + 1}Id`]
                ? item[`productAtt${i + 1}Id`].split("-")[
                    item[`productAtt${i + 1}Id`].split("-").length - 1
                  ] +
                  "-" +
                  item[`productAtt${i + 1}IdFlag`]
                : "";
              delete item[`productAtt${i + 1}IdFlag`];
              delete item["goDownSelectList"];
            }
            // // 下探多选的话需要拆出来多条数据传入后台
            // item.goDown.forEach(goDownItem => {
            //   finalProductAttArr.push({ ...item, goDown: goDownItem });
            // });
          });
          if (!this.isEdit) {
            postData["productAttList"] = productAttArr;
            postData["treeType"] = this.treeType;
            postData["perfectId"] = this.indexInfo.id;
          } else {
            const productAtt = {};
            const {
              productAtt1Id,
              productAtt2Id,
              productAtt3Id,
              goDown,
            } = productAttArr[0];
            let arr = [productAtt1Id, productAtt2Id, productAtt3Id, goDown];
            arr = arr.filter((item) => item);
            for (let i = 0; i < 4; i++) {
              productAtt[`productAtt${i + 1}Id`] = arr[i] || "";
            }
            postData = { ...postData, ...productAtt };
          }
          // console.log("postData----->", postData);
          request(
            `/api/smc2/attributes/${
              this.isEdit ? "updateAttributes" : "insertAttributes"
            }`,
            {
              method: "POST",
              body: postData,
            }
          ).then((res) => {
            if (res.result === "success") {
              this.close();
              this.$emit("fetchData");
            } else {
              this.$message.error(res.result);
            }
          });
        }
      });
    },
  },
};
</script>
