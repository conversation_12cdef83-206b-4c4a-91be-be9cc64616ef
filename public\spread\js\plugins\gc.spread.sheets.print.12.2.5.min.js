/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
!function(a){"object"==typeof module&&"object"==typeof module.exports?module.exports=a(require("@grapecity/spread-sheets")):"function"==typeof define&&define.amd?define(["@grapecity/spread-sheets"],a):"object"==typeof exports?exports.Spread=a(require("@grapecity/spread-sheets")):a(GC)}(function(a){a="object"==typeof a?a:{},a.Spread=a.Spread||{},a.Spread.Sheets=a.Spread.Sheets||{},a.Spread.Sheets.Print=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/print/print.entry.js")}({"./dist/plugins/print/print.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/print/print.js")),d(c("./dist/plugins/print/print.ns.js"))},"./dist/plugins/print/print.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea;Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("Core"),f=c("./dist/plugins/print/print.ns.js"),g=c("Core"),h=g.GC$,i=d.Common.j,j=i.Fa,k=h.extend,l=h.each,m=e.Ul.Ml,n=Math.sqrt,o=Math.pow,p="firstPageNumber",q="margin",r="paperSize",s="pageBreak",t="",u="gc-printPage",v="2d",w="px",x="div",y="canvas",z=null,A=Math.min,B=Math.max,C=Math.floor,D=parseInt,E=parseFloat,F=JSON.stringify,G=JSON.parse,H=".GCPrintLine";function fa(a){return"number"==typeof a&&a%1===0}function ga(a){return j(a)||a<0}I=new d.Common.ResourceManager(f.SR),J=I.getResource.bind(I),k(e.Worksheet.prototype,{printInfo:function(a){var b=this,c;return 0===arguments.length?(b.e3||(b.e3=new T(b)),c=b.e3):(b.e3=a,a.kj||(a.kj=b),c=b),c},getRowPageBreak:function(a){return this.ITa.getPageBreak(!0,3,a)},setRowPageBreak:function(a,b){this.Vr(a,b,s,!0),this.isPrintLineVisible()&&this.repaint()},getColumnPageBreak:function(a){return this.ITa.getPageBreak(!1,3,a)},setColumnPageBreak:function(a,b){this.Vr(a,b,s,!1),this.isPrintLineVisible()&&this.repaint()},isPrintLineVisible:function(a){return void 0===a?this.DBb||!1:void(this.DBb!==a&&(this.DBb=a,this.repaint()))},EBb:function(){var a,b,c=this.ITa.YTa(!0,3),d=c&&c.infos,e=[];if(d)for(a=0;a<d.length;a++)b=d[a],b&&b.pageBreak&&e.push(a);return e},FBb:function(){var a,b,c=this.ITa.YTa(!1,3),d=c&&c.infos,e=[];if(d)for(a=0;a<d.length;a++)b=d[a],b&&b.pageBreak&&e.push(a);return e}});function ha(a){return a.height}function ia(a){return a.width}function ja(a){var b={};return a.forEach(function(a){b[a.row]=a.y}),b}function ka(a){var b={};return a.forEach(function(a){b[a.col]=a.x}),b}K=function(){function a(a,b,c,d,e,f,g){var h,i=f%2;i&&(a!==c?(b-=.5,d-=.5):(a-=.5,c-=.5)),h=this,h.x0=a,h.y0=b,h.x1=c,h.y1=d,h.color=e,h.lineWidth=f,h.pattern=g}return a.prototype.paintLine=function(a){var b,c,d,e,f,g,h=this,i=h.x0,j=h.x1,k=h.y0,l=h.y1,m=n(o(j-i,2)+o(l-k,2)),p={x:(j-i)/m,y:(l-k)/m},q=0,r=0,s=h.pattern;for(s=s&&s.length?s:[4,2],b=[{x0:i,y0:k}];q<m;)c=A(s[r%s.length],m-q),r++,q+=c,i+=c*p.x,k+=c*p.y,b.push({x0:i,y0:k});for(a.save(),a.beginPath(),a.lineWidth=h.lineWidth,a.strokeStyle=h.color[0],r=0;r<b.length-1;r+=2)d=b[r].x0,f=b[r].y0,e=b[r+1].x0,g=b[r+1].y0,a.moveTo(d,f),a.lineTo(e,g);for(a.stroke(),a.restore(),a.save(),a.beginPath(),a.lineWidth=h.lineWidth,a.strokeStyle=h.color[1],r=1;r<b.length-1;r+=2)d=b[r].x0,f=b[r].y0,e=b[r+1].x0,g=b[r+1].y0,a.moveTo(d,f),a.lineTo(e,g);a.stroke(),a.restore()},a}();function la(a,b,c,d,e){var f=a.lineWidth,g=f%2;g&&(0===d?(b-=.5,c-=1,e+=1):(c-=.5,b-=1,d+=1)),a.moveTo(b,c),a.lineTo(b+d,c+e)}function ma(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q;for(j=0;j<b.length;j++)k=b[j]+1,l=d[k],l&&(m=new K(l,f,l,h,["#9f9f9f","#ffffff"],1,[4,2]),m.paintLine(a));for(n=0;n<c.length;n++)o=c[n]+1,p=e[o],p&&(q=new K(g,p,i,p,["#9f9f9f","#ffffff"],1,[4,2]),q.paintLine(a))}function na(a,b,c,d,e,f,g,h,i,j,k){a.save(),a.beginPath(),a.lineWidth=1,a.strokeStyle="blue",b!==-1&&f!==h&&la(a,f,g,0,j),c!==-1&&g!==i&&la(a,f,g,k,0),d!==-1&&f+k!==h&&la(a,f+k,g,0,j),e!==-1&&g+j!==i&&la(a,f,g+j,k,0),a.stroke(),a.restore()}function oa(a,b,c,d,e){var f,g,h,i,j,k,l;for(a.save(),a.beginPath(),a.lineWidth=1,a.strokeStyle="#000000",f=b.EBb()||[],g=b.FBb()||[],h=0;h<f.length;h++)i=f[h],j=c[i],j&&la(a,e.x,j,ia(e),0);for(h=0;h<g.length;h++)k=g[h],l=d[k],l&&la(a,l,e.y,0,ha(e));a.stroke(),a.restore()}e.Worksheet.$n("print",{toJson:function(a){var b,c,d,e,f=this.e3;f&&(b=f.rowStart(),c=f.rowEnd(),ga(b)&&!ga(c)?f.rowStart(0):!ga(b)&&ga(c)&&f.rowEnd(this.getRowCount()),d=f.columnStart(),e=f.columnEnd(),ga(d)&&!ga(e)?f.columnStart(0):!ga(d)&&ga(e)&&f.columnEnd(this.getColumnCount()),a.printInfo=f.toJSON())},fromJson:function(a){var b=a&&a.printInfo;b&&this.printInfo().fromJSON(b)},paintSheetEnd:function(a){var b,c,d,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R=this,S=a.clipRect;if(R.DBb&&S&&(b=R.parent,b.GBb!==!0)){for(b.GBb=!0,c=b.getActiveSheetIndex(),d=b.getActiveSheet(),f=d.printInfo(),g=f.useMax(),f._useMax=!1,h=b.HBb(),i=h.pageInfo(b,c),f._useMax=g,j=i.columns,k=i.rows,l=R.am(),m=a.ctx,n=0;n<=2;n++)for(o=0;o<=2;o++)p=l.Ft(n,o),p=R.yl.VI(n,o,p),p&&(q=p.getIntersectRect(S),q&&(r=ia(l),s=ha(l),t=R.Gr(n),u=R.Hr(o),v=ja(t),w=ka(u),x=t[t.length-1],y=u[u.length-1],s=A(x.y+x.height,s),r=A(y.x+y.width,r),z=l.Cr,C=l.Br,D=f.columnStart(),E=f.columnEnd(),F=f.rowStart(),G=f.rowEnd(),H=D===-1?0:D,I=F===-1?0:F,J=E===-1?d.getColumnCount():E-H+1,K=G===-1?d.getRowCount():G-I+1,L=e.kf(I,H,K,J),M=R.yl.XI(n,o,[L],q)[0],N=void 0,O=void 0,P=void 0,Q=void 0,m.save(),m.rect(q.x,q.y,ia(q),ha(q)),m.clip(),m.beginPath(),M&&(N=B(M.x,C),O=B(M.y,z),P=ia(M)-N+M.x,Q=ha(M)-O+M.y,m.save(),m.rect(N-1,O-1,P+1,Q+1),m.clip(),ma(m,j,k,w,v,z,C,s,r),na(m,D,F,E,G,N,O,C,z,Q,P),m.stroke(),m.restore()),m.restore(),oa(m,d,v,w,p)));delete b.GBb}},onLayoutChanged:function(a){var b,c=this,d=a.changeType,e=this.parent;e&&(b=e.HBb(),"addRows"!==d&&"deleteRows"!==d&&"addColumns"!==d&&"deleteColumns"!==d&&"setColumnCount"!==d&&"setRowCount"!==d||b.layoutChanged(c))}});function pa(a,b){var c,d,e,f=b.pageOrder(),g=a.rows,h=a.columns,i=1===f||0===f&&g.length>=h.length,j={},k=b.rowStart(),l=b.columnStart(),m=k=k===-1?0:k,n=l=l===-1?0:l,o=[];if(i)for(c=0;c<h.length;c++){for(d=0;d<g.length;d++)e={},e.row=k,e.column=l,e.rowCount=g[d]-k+1,e.columnCount=h[c]-l+1,k=g[d]+1,o.push(e);k=m,l=h[c]+1}else for(c=0;c<g.length;c++){for(d=0;d<h.length;d++)e={},e.row=k,e.column=l,e.rowCount=g[c]-k+1,e.columnCount=h[d]-l+1,l=h[d]+1,o.push(e);l=n,k=g[c]+1}return j.pages=o,j}e.Workbook.prototype.print=function(a){var b,c=this;if(!(qa(a)||a===z||fa(a)&&a>=0&&c.getSheetCount()>a))throw Error(J().Exp_InvalidSheetIndex);c.f3||(c.f3=new ea),b=c.f3,b.print(c,a)},e.Workbook.prototype.pageInfo=function(a){var b,c,d,e,f,g,h=this;if(!(qa(a)||a===z||fa(a)&&a>=0&&h.getSheetCount()>a))throw Error(J().Exp_InvalidSheetIndex);if(qa(a)||a===z){for(d=[],e=0;e<h.getSheetCount();e++)b=h.HBb(),f=b.pageInfo(h,e),c=h.getSheet(e).printInfo(),f=pa(f,c),d.push(f);return d}return b=h.HBb(),c=h.getSheet(a).printInfo(),g=b.pageInfo(h,a),g=pa(g,c)},e.Workbook.prototype.HBb=function(){var a=this;return a.IBb||(a.IBb=new da),a.IBb},e.Workbook.$n("print",{dispose:function(){var a=this.f3;a&&a.dispose()}}),function(a){a[a.inherit=0]="inherit",a[a.hide=1]="hide",a[a.show=2]="show",a[a.showOnce=3]="showOnce"}(L=b.PrintVisibilityType||(b.PrintVisibilityType={})),function(a){a[a.none=0]="none",a[a.horizontal=1]="horizontal",a[a.vertical=2]="vertical",a[a.both=3]="both"}(M=b.PrintCentering||(b.PrintCentering={})),function(a){a[a.portrait=1]="portrait",a[a.landscape=2]="landscape"}(N=b.PrintPageOrientation||(b.PrintPageOrientation={})),function(a){a[a.auto=0]="auto",a[a.downThenOver=1]="downThenOver",a[a.overThenDown=2]="overThenDown"}(O=b.PrintPageOrder||(b.PrintPageOrder={})),function(a){a[a.a2=66]="a2",a[a.a3=8]="a3",a[a.a3Extra=63]="a3Extra",a[a.a3ExtraTransverse=68]="a3ExtraTransverse",a[a.a3Rotated=76]="a3Rotated",a[a.a3Transverse=67]="a3Transverse",a[a.a4=9]="a4",a[a.a4Extra=53]="a4Extra",a[a.a4Plus=60]="a4Plus",a[a.a4Rotated=77]="a4Rotated",a[a.a4Small=10]="a4Small",a[a.a4Transverse=55]="a4Transverse",a[a.a5=11]="a5",a[a.a5Extra=64]="a5Extra",a[a.a5Rotated=78]="a5Rotated",a[a.a5Transverse=61]="a5Transverse",a[a.a6=70]="a6",a[a.a6Rotated=83]="a6Rotated",a[a.aPlus=57]="aPlus",a[a.b4=12]="b4",a[a.b4Envelope=33]="b4Envelope",a[a.b4JisRotated=79]="b4JisRotated",a[a.b5=13]="b5",a[a.b5Envelope=34]="b5Envelope",a[a.b5Extra=65]="b5Extra",a[a.b5JisRotated=80]="b5JisRotated",a[a.b5Transverse=62]="b5Transverse",a[a.b6Envelope=35]="b6Envelope",a[a.b6Jis=88]="b6Jis",a[a.b6JisRotated=89]="b6JisRotated",a[a.bPlus=58]="bPlus",a[a.c3Envelope=29]="c3Envelope",a[a.c4Envelope=30]="c4Envelope",a[a.c5Envelope=28]="c5Envelope",a[a.c65Envelope=32]="c65Envelope",a[a.c6Envelope=31]="c6Envelope",a[a.cSheet=24]="cSheet",a[a.custom=0]="custom",a[a.dlEnvelope=27]="dlEnvelope",a[a.dSheet=25]="dSheet",a[a.eSheet=26]="eSheet",a[a.executive=7]="executive",a[a.folio=14]="folio",a[a.germanLegalFanfold=41]="germanLegalFanfold",a[a.germanStandardFanfold=40]="germanStandardFanfold",a[a.inviteEnvelope=47]="inviteEnvelope",a[a.isoB4=42]="isoB4",a[a.italyEnvelope=36]="italyEnvelope",a[a.japaneseDoublePostcard=69]="japaneseDoublePostcard",a[a.japaneseDoublePostcardRotated=82]="japaneseDoublePostcardRotated",a[a.japaneseEnvelopeChouNumber3=73]="japaneseEnvelopeChouNumber3",a[a.japaneseEnvelopeChouNumber3Rotated=86]="japaneseEnvelopeChouNumber3Rotated",a[a.japaneseEnvelopeChouNumber4=74]="japaneseEnvelopeChouNumber4",a[a.japaneseEnvelopeChouNumber4Rotated=87]="japaneseEnvelopeChouNumber4Rotated",a[a.japaneseEnvelopeKakuNumber2=71]="japaneseEnvelopeKakuNumber2",a[a.japaneseEnvelopeKakuNumber2Rotated=84]="japaneseEnvelopeKakuNumber2Rotated",a[a.japaneseEnvelopeKakuNumber3=72]="japaneseEnvelopeKakuNumber3",a[a.japaneseEnvelopeKakuNumber3Rotated=85]="japaneseEnvelopeKakuNumber3Rotated",a[a.japaneseEnvelopeYouNumber4=91]="japaneseEnvelopeYouNumber4",a[a.japaneseEnvelopeYouNumber4Rotated=92]="japaneseEnvelopeYouNumber4Rotated",a[a.japanesePostcard=43]="japanesePostcard",a[a.japanesePostcardRotated=81]="japanesePostcardRotated",a[a.ledger=4]="ledger",a[a.legal=5]="legal",a[a.legalExtra=51]="legalExtra",a[a.letter=1]="letter",a[a.letterExtra=50]="letterExtra",a[a.letterExtraTransverse=56]="letterExtraTransverse",a[a.letterPlus=59]="letterPlus",a[a.letterRotated=75]="letterRotated",a[a.letterSmall=2]="letterSmall",a[a.letterTransverse=54]="letterTransverse",a[a.monarchEnvelope=37]="monarchEnvelope",a[a.note=18]="note",a[a.number10Envelope=20]="number10Envelope",a[a.number11Envelope=21]="number11Envelope",a[a.number12Envelope=22]="number12Envelope",a[a.number14Envelope=23]="number14Envelope",a[a.number9Envelope=19]="number9Envelope",a[a.personalEnvelope=38]="personalEnvelope",a[a.prc16K=93]="prc16K",a[a.prc16KRotated=106]="prc16KRotated",a[a.prc32K=94]="prc32K",a[a.prc32KBig=95]="prc32KBig",a[a.prc32KBigRotated=108]="prc32KBigRotated",a[a.prc32KRotated=107]="prc32KRotated",a[a.prcEnvelopeNumber1=96]="prcEnvelopeNumber1",a[a.prcEnvelopeNumber10=105]="prcEnvelopeNumber10",a[a.prcEnvelopeNumber10Rotated=118]="prcEnvelopeNumber10Rotated",a[a.prcEnvelopeNumber1Rotated=109]="prcEnvelopeNumber1Rotated",a[a.prcEnvelopeNumber2=97]="prcEnvelopeNumber2",a[a.prcEnvelopeNumber2Rotated=110]="prcEnvelopeNumber2Rotated",a[a.prcEnvelopeNumber3=98]="prcEnvelopeNumber3",a[a.prcEnvelopeNumber3Rotated=111]="prcEnvelopeNumber3Rotated",a[a.prcEnvelopeNumber4=99]="prcEnvelopeNumber4",a[a.prcEnvelopeNumber4Rotated=112]="prcEnvelopeNumber4Rotated",a[a.prcEnvelopeNumber5=100]="prcEnvelopeNumber5",a[a.prcEnvelopeNumber5Rotated=113]="prcEnvelopeNumber5Rotated",a[a.prcEnvelopeNumber6=101]="prcEnvelopeNumber6",a[a.prcEnvelopeNumber6Rotated=114]="prcEnvelopeNumber6Rotated",a[a.prcEnvelopeNumber7=102]="prcEnvelopeNumber7",a[a.prcEnvelopeNumber7Rotated=115]="prcEnvelopeNumber7Rotated",a[a.prcEnvelopeNumber8=103]="prcEnvelopeNumber8",a[a.prcEnvelopeNumber8Rotated=116]="prcEnvelopeNumber8Rotated",a[a.prcEnvelopeNumber9=104]="prcEnvelopeNumber9",a[a.prcEnvelopeNumber9Rotated=117]="prcEnvelopeNumber9Rotated",a[a.quarto=15]="quarto",a[a.standard10x11=45]="standard10x11",a[a.standard10x14=16]="standard10x14",a[a.standard11x17=17]="standard11x17",a[a.standard12x11=90]="standard12x11",a[a.standard15x11=46]="standard15x11",a[a.standard9x11=44]="standard9x11",a[a.statement=6]="statement",a[a.tabloid=3]="tabloid",a[a.tabloidExtra=52]="tabloidExtra",a[a.usStandardFanfold=39]="usStandardFanfold"}(P=b.PaperKind||(b.PaperKind={})),Q=function(){function a(a,b){var c=this,d=arguments.length;1===d?c.kind(a):2===d?(c.Vo=a,c._v=b,c.g3=P.custom):c.kind(P.letter)}return a.prototype.height=function(a){var b=this;return 0===arguments.length?b._v:(b._v!==a&&(b.g3=0),b._v=a,b)},a.prototype.width=function(a){var b=this;return 0===arguments.length?b.Vo:(b.Vo!==a&&(b.g3=0),b.Vo=a,b)},a.prototype.kind=function(a){var b,c=this;return 0===arguments.length?c.g3:(c.g3=a,b=c.getPageSize(a),c.Vo=b.width,c._v=b.height,c)},a.prototype.getPageSize=function(a){function b(a,b){return b?100*a:a/25.4*100}function c(a,c){return{width:b(a,!0),height:b(c,!0)}}function d(a,c){return{width:b(a,!1),height:b(c,!1)}}switch(a){case P.custom:return{width:0,height:0};case P.letter:return c(8.5,11);case P.legal:return c(8.5,14);case P.a4:return d(210,297);case P.cSheet:return c(17,22);case P.dSheet:return c(22,34);case P.eSheet:return c(34,44);case P.letterSmall:return c(8.5,11);case P.tabloid:return c(11,17);case P.ledger:return c(17,11);case P.statement:return c(5.5,8.5);case P.executive:return c(7.25,10.5);case P.a3:return d(297,420);case P.a4Small:return d(210,297);case P.a5:return d(148,210);case P.b4:return d(250,353);case P.b5:return d(176,250);case P.folio:return c(8.5,13);case P.quarto:return d(215,275);case P.standard10x14:return c(10,14);case P.standard11x17:return c(11,17);case P.note:return c(8.5,11);case P.number9Envelope:return c(3.875,8.875);case P.number10Envelope:return c(4.125,9.5);case P.number11Envelope:return c(4.5,10.375);case P.number12Envelope:return c(4.75,11);case P.number14Envelope:return c(5,11.5);case P.dlEnvelope:return d(110,220);case P.c5Envelope:return d(162,229);case P.c3Envelope:return d(324,458);case P.c4Envelope:return d(229,324);case P.c6Envelope:return d(114,162);case P.c65Envelope:return d(114,229);case P.b4Envelope:return d(250,353);case P.b5Envelope:return d(176,250);case P.b6Envelope:return d(176,125);case P.italyEnvelope:return d(110,230);case P.monarchEnvelope:return c(3.875,7.5);case P.personalEnvelope:return c(3.625,6.5);case P.usStandardFanfold:return c(14.875,11);case P.germanStandardFanfold:return c(8.5,12);case P.germanLegalFanfold:return c(8.5,13);case P.isoB4:return d(250,353);case P.japanesePostcard:return d(100,148);case P.standard9x11:return c(9,11);case P.standard10x11:return c(10,11);case P.standard15x11:return c(15,11);case P.inviteEnvelope:return d(220,220);case P.letterExtra:return c(9.275,12);case P.legalExtra:return c(9.275,15);case P.tabloidExtra:return c(11.69,18);case P.a4Extra:return d(236,322);case P.letterTransverse:return c(8.275,11);case P.a4Transverse:return d(210,297);case P.letterExtraTransverse:return c(9.275,12);case P.aPlus:return d(227,356);case P.bPlus:return d(305,487);case P.letterPlus:return c(8.5,12.69);case P.a4Plus:return d(210,330);case P.a5Transverse:return d(148,210);case P.b5Transverse:return d(182,257);case P.a3Extra:return d(322,445);case P.a5Extra:return d(174,235);case P.b5Extra:return d(201,276);case P.a2:return d(420,594);case P.a3Transverse:return d(297,420);case P.a3ExtraTransverse:return d(322,445);case P.japaneseDoublePostcard:return d(200,148);case P.a6:return d(105,148);case P.japaneseEnvelopeKakuNumber2:return d(240,332);case P.japaneseEnvelopeKakuNumber3:return d(216,277);case P.japaneseEnvelopeChouNumber3:return d(120,235);case P.japaneseEnvelopeChouNumber4:return d(90,205);case P.letterRotated:return c(11,8.5);case P.a3Rotated:return d(420,297);case P.a4Rotated:return d(297,210);case P.a5Rotated:return d(210,148);case P.b4JisRotated:return d(364,257);case P.b5JisRotated:return d(257,182);case P.japanesePostcardRotated:return d(148,100);case P.japaneseDoublePostcardRotated:return d(148,200);case P.a6Rotated:return d(148,105);case P.japaneseEnvelopeKakuNumber2Rotated:return d(332,240);case P.japaneseEnvelopeKakuNumber3Rotated:return d(277,216);case P.japaneseEnvelopeChouNumber3Rotated:return d(235,120);case P.japaneseEnvelopeChouNumber4Rotated:return d(205,90);case P.b6Jis:return d(128,182);case P.b6JisRotated:return d(182,128);case P.standard12x11:return c(12,11);case P.japaneseEnvelopeYouNumber4:return d(235,105);case P.japaneseEnvelopeYouNumber4Rotated:return d(105,235);case P.prc16K:return d(146,215);case P.prc32K:return d(97,151);case P.prc32KBig:return d(97,151);case P.prcEnvelopeNumber1:return d(102,165);case P.prcEnvelopeNumber2:return d(102,176);case P.prcEnvelopeNumber3:return d(125,176);case P.prcEnvelopeNumber4:return d(110,208);case P.prcEnvelopeNumber5:return d(110,220);case P.prcEnvelopeNumber6:return d(120,230);case P.prcEnvelopeNumber7:return d(160,230);case P.prcEnvelopeNumber8:return d(120,309);case P.prcEnvelopeNumber9:return d(229,324);case P.prcEnvelopeNumber10:return d(324,458);case P.prc16KRotated:return d(146,215);case P.prc32KRotated:return d(97,151);case P.prc32KBigRotated:return d(97,151);case P.prcEnvelopeNumber1Rotated:return d(165,102);case P.prcEnvelopeNumber2Rotated:return d(176,102);case P.prcEnvelopeNumber3Rotated:return d(176,125);case P.prcEnvelopeNumber4Rotated:return d(208,110);case P.prcEnvelopeNumber5Rotated:return d(220,110);case P.prcEnvelopeNumber6Rotated:return d(230,120);case P.prcEnvelopeNumber7Rotated:return d(230,160);case P.prcEnvelopeNumber8Rotated:return d(309,120);case P.prcEnvelopeNumber9Rotated:return d(324,229);case P.prcEnvelopeNumber10Rotated:return d(458,324);default:return{width:0,height:0}}},a.prototype.toJSON=function(){return{width:this.Vo,height:this._v,kind:this.g3}},a.prototype.fromJSON=function(a){var b,c=this,d=!1,e=!1,f=!1;qa(a.width)||(c.Vo=a.width,d=!0),qa(a.height)||(c._v=a.height,e=!0),qa(a.kind)||(c.g3=a.kind,f=!0),d&&e||!f||(b=c.getPageSize(c.g3),c.Vo=b.width,c._v=b.height)},a}(),b.PaperSize=Q,R='{"top":75,"bottom":75,"left":70,"right":70,"header":30,"footer":30}',S={bestFitRows:!1,bestFitColumns:!1,columnStart:-1,columnEnd:-1,rowStart:-1,rowEnd:-1,repeatColumnStart:-1,repeatColumnEnd:-1,repeatRowStart:-1,repeatRowEnd:-1,showBorder:!0,showGridLine:!1,showColumnHeader:0,showRowHeader:0,useMax:!0,centering:0,firstPageNumber:1,headerLeft:t,headerCenter:t,headerRight:t,footerLeft:t,footerCenter:t,footerRight:t,headerLeftImage:t,headerCenterImage:t,headerRightImage:t,footerLeftImage:t,footerCenterImage:t,footerRightImage:t,margin:G(R),orientation:1,pageRange:t,pageOrder:0,blackAndWhite:!1,zoomFactor:1,fitPagesTall:-1,fitPagesWide:-1,paperSize:{},qualityFactor:2,watermark:[]},T=function(){function a(a){var b=this;b.kj=a,l(S,function(a,c){a===q?b["_"+a]=G(R):a===r?b["_"+a]=new Q:b["_"+a]=c})}return a.prototype.toJSON=function(){var a,b,c;function d(a,b,c){var d=!1,e=S[a];return d=a===q?F(e)===F(b):a===r?850===b.width&&1100===b.height&&0===b.kind:a===p?1===b&&!c.fma:e===b}return a=this,b={},l(S,function(e){c=a["_"+e],d(e,c,a)||(e===r?b[e]=c.toJSON():b[e]=c)}),b},a.prototype.fromJSON=function(a){var b=this;l(S,function(c){var d=a[c];qa(d)||(c===r?b["_"+c].fromJSON(d):(c===p&&(b.fma=!0),b["_"+c]=d))})},a}(),b.PrintInfo=T,U={},l(S,function(a){U[a]=function(b){var c,d;return 0===arguments.length?this["_"+a]:("zoomFactor"===a?b<.1?b=.1:b>4&&(b=4):"qualityFactor"===a?(c=D(b,10),c<1?c=1:c>8&&(c=8),b=c):a===p?this.fma=!0:("fitPagesTall"===a||"fitPagesWide"===a)&&b<=0&&(b=-1),d=this.kj,this["_"+a]=b,d&&d.isPrintLineVisible()===!0&&d.repaint(),this)}}),k(T.prototype,U);function qa(a){return void 0===a}function ra(a,b){return a.createElement(b)}function sa(a,b,c){var d=ta(a,b.paperSize(),b.orientation(),!b.showBorder()),f=d.width,g=d.height,h=ua(b.margin()),i=h.left,j=h.top,k=h.right,l=h.bottom;c.paperSize={width:f,height:g},c.pageImageableArea=new e.Rect(i,j,f-i-k,g-j-l)}function ta(a,b,c,d){var e,f=.96,g={},h=b.width()*f,i=b.height()*f;return c===N.landscape?(g.width=i,g.height=D(h+"",10)):(g.width=h,g.height=D(i+"",10)),d&&(e=a.options.sheetAreaOffset,g.width-=e.left,g.height-=e.top),g}function ua(a){var b=.96,c={};return c.left=a.left*b,c.top=a.top*b,c.right=a.right*b,c.bottom=a.bottom*b,c.header=a.header*b,c.footer=a.footer*b,c}function va(a){var b,c,d,e,f,g,h,i,j,k;if(!a)return[];for(b=[],c=a.split(","),j=0,k=c.length;j<k;j++)if(d=c[j].trim())if(e=d.indexOf("-"),e>=0){for(f=D(d.substr(0,e),10),g=D(d.substr(e+1),10),h=g>=f?1:-1,i=f;i!==g;i+=h)b.push(i);b.push(g)}else b.push(D(d,10));return b}function wa(a,b){a.sort(function(a,b){return a-b});for(var c=[],d=0,e=a.length,f;d<e;d++)a[d]-=1,(a[d]>=b||a[d]<0)&&c.push(d);for(f=c.length-1;f>=0;f--)a.splice(c[f],1)}function xa(a){var b,c,d,e,f,g,h,i,j=a.ITa,k=j.getLastNonNullCol(3,!0),l=j.getLastNonNullRow(3,!0),m=a.tables&&a.tables.all();for(b=k;b>=0;b--){if(c=a.Iq(-1,b),Aa(c))return b;if(m)for(e=m.length,d=0;d<e;d++)if(f=m[d].range(),h=f.col,i=f.colCount,b>=h&&b<=h+i-1)return b;for(g=0;g<=l;g++)if(za(a,g,b))return b}return-1}function ya(a){var b,c,d,e,f,g,h,i,j=a.ITa,k=j.getLastNonNullCol(3,!0),l=j.getLastNonNullRow(3,!0),m=a.tables&&a.tables.all();for(b=l;b>=0;b--){if(c=a.Iq(b,-1),Aa(c))return b;if(m)for(e=m.length,d=0;d<e;d++)if(f=m[d].range(),h=f.row,i=f.rowCount,b>=h&&b<=h+i-1)return b;for(g=0;g<=k;g++)if(za(a,b,g))return b}return-1}function za(a,b,c){var d=a.getValue(b,c),e,f;return d!==z&&!qa(d)||(!!(e=a.getSparkline&&a.getSparkline(b,c))||(f=a.Iq(b,c),Aa(f)))}function Aa(a){return!(!a||!(a.backColor||a.backgroundImage||a.borderBottom||a.borderLeft||a.borderRight||a.borderTop||a.diagonalDown||a.diagonalUp))}function Ba(a,b){var c,d=[],e=a.pictures&&a.pictures.all();e&&(d=d.concat(e)),c=a.charts&&a.charts.all(),c&&(d=d.concat(c)),d.forEach(b)}function Ca(a,b){var c=-1;return Ba(a,function(a){a.isVisible()&&a.canPrint()&&(c=B(c,b?a.endRow():a.endColumn()))}),c}function Da(a){var b,c,d,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w=a.ss,x=ya(a)+1,y=0;for(b=0,c=a.getColumnCount(2);b<c;b++)y+=a.Tl(b,2);for(d=0,f=a.getColumnCount();d<f;d++)y+=w._m(d);for(g=0,h=0,i=a.getRowCount(1);h<i;h++)g+=a.Sl(h,1);for(j=0;j<x;j++)g+=w.Sl(j);for(k=new e.Rect(0,0,y,g),a.Us(k),a.invalidateLayout(),l=-1,m=0;m<x;m++)for(n=a.Er(m),o=0;o<=2;o++){for(p=new e.xJ(a,n,o),q=p.nJ(m),r=0,s=q.length;r<s;r++)t=q[r],t.endColumn>l&&(l=t.endColumn);u=q.headingOverflowlayout,u&&u.endColumn>l&&(l=u.endColumn),v=q.trailingOverflowLayout,v&&v.endColumn>l&&(l=v.endColumn)}return l}function Ea(a,b,c){var d=-1,e=-1;return Fa(a,0,0,b,c,function(a){var b=a.col+a.colCount-1,c=a.row+a.rowCount-1;b>d&&(d=b),c>e&&(e=c)}),{colIndex:d,rowIndex:e}}function Fa(a,b,c,d,e,f){var g,h,i,j=a;for(g=0,h=j.length;g<h;g++){if(b>=0||c>=0)for(;g<h&&!j[g].intersect(b,c,d,e);)g++;if(g<h&&f&&(i=f(j[g]),i===!1))break}}function Ga(a){var b,c,d,e=-1,f=a.ITa.getSpans(z,1),g=a.getRowCount(1),h=a.getColumnCount(1);return e=B(e,Ea(f,g,h).colIndex),b=a.ITa.getSpans(z,3),c=a.getRowCount(),d=a.getColumnCount(),e=B(e,Ea(b,c,d).colIndex)}function Ha(a){var b,c,d,e=-1,f=a.ITa.getSpans(z,2),g=a.getRowCount(2),h=a.getColumnCount(2);return e=B(e,Ea(f,g,h).rowIndex),b=a.ITa.getSpans(z,3),c=a.getRowCount(),d=a.getColumnCount(),e=B(e,Ea(b,c,d).rowIndex)}function Ia(a,b){var c,d,e,f,g,h,i,j=b.columnEnd(),k=a.getColumnCount();return j===-1?(d=void 0,b.useMax()?(e=xa(a),a.options.allowCellOverflow&&(f=Da(a),e=B(e,f)),g=Ca(a,!1),h=-1,a.shapes&&(h=a.shapes.Aob()),d=B(e,g,h),i=Ga(a),d=B(d,i)):d=k-1,c=d):c=j,c=A(c,k-1)}function Ja(a,b){var c,d,e,f,g,h,i=b.rowEnd(),j=a.getRowCount();return i===-1?(d=void 0,b.useMax()?(e=ya(a),f=Ca(a,!0),g=-1,a.shapes&&(g=a.shapes.zob()),d=B(e,f,g),h=Ha(a),d=B(d,h)):d=j-1,c=d):c=i,c=A(c,j-1)}function Ka(a,b){l(a,function(a){b[a]=function(b){return 0===arguments.length?this["_"+a]:(this["_"+a]=b,this)}})}V={headerSize:0,contentSize:0,contentOffset:0,itemStart:-1,itemEnd:-1,repeatItemStart:-1,repeatItemEnd:-1},W=function(){function a(){var a=this;l(V,function(b,c){a["_"+b]=c})}return a}(),X={},Ka(V,X),k(W.prototype,X),Y={sheetIndex:-1,pageNumber:-1,columnPageIndex:-1,rowPageIndex:-1,pageNumberInSheet:-1,columnPageIndexInSheet:-1,rowPageIndexInSheet:-1,columnPage:z,rowPage:z,paperSize:z,pageImageableArea:z,workbookName:t,worksheetName:t},Z=function(){function a(){var a=this;l(Y,function(b,c){"columnPage"===b||"rowPage"===b?c=new W:b===r?c={width:0,height:0}:"pageImageableArea"===b&&(c=new e.Rect(0,0,0,0)),a["_"+b]=c})}return a.prototype.getPageSize=function(){var a=this.columnPage(),b=this.rowPage();return{width:a.contentSize()+a.headerSize(),height:b.contentSize()+b.headerSize()}},a}(),b.SheetPageInfo=Z,$={},Ka(Y,$),k(Z.prototype,$),_=function(){function a(a,b,c,d){var e=this;e.OC=a,e.kj=a.getSheet(b),e._sheetIndex=b,e.h3=e.kj.printInfo(),e._paperSize=c,e._pageImageableArea=d,e.i3={width:d.width,height:d.height},e.j3=0,e.l3=[],e.m3=[],e.n3=1,e.o3=1}return a.prototype.paginate=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m=this;if(!m.kj.visible()||m.q3()<=0||m.r3()<=0)return void(m.j3=0);if(a=m.h3,b=a.fitPagesTall(),c=a.fitPagesWide(),b===-1&&c===-1&&(d=a.zoomFactor(),m.horizontalZoomFactor(d),m.verticalZoomFactor(d)),m.$m={},m.an={},m.s3(),b>=1||c>=1){for(e=0,f=0,g=0,h=m.horizontalPageCount();g<h;g++)e+=m.l3[g].contentSize();for(i=0,j=m.verticalPageCount();i<j;i++)f+=m.m3[i].contentSize();k=m.verticalPageCount(),b<k&&b>=1&&m.t3(f,k),l=m.horizontalPageCount(),c<l&&c>=1&&m.u3(e,l),m.j3=m.verticalPageCount()*m.horizontalPageCount()}m.$m={},m.an={}},a.prototype.pageCount=function(){return this.j3},a.prototype.getPage=function(a){var b,c=this,d=0,e=0,f=c.h3.pageOrder(),g=c.verticalPageCount(),h=c.horizontalPageCount();return 1===f||0===f&&g>=h?(d=a%g,e=C(a/g)):(e=a%h,d=C(a/h)),b=new Z,b.sheetIndex(c._sheetIndex),b.pageNumberInSheet(a),b.rowPageIndexInSheet(d),b.columnPageIndexInSheet(e),b.pageNumber(a),b.rowPageIndex(d),b.columnPageIndex(e),b.rowPage(c.m3[d]),b.columnPage(c.l3[e]),b.paperSize(c._paperSize),b.pageImageableArea(c._pageImageableArea),b.workbookName(c.kj.parent.name),b.worksheetName(c.kj.name()),b},a.prototype.verticalZoomFactor=function(a){return 0===arguments.length?this.o3:(this.o3=a,this.$m={},this)},a.prototype.horizontalZoomFactor=function(a){return 0===arguments.length?this.n3:(this.n3=a,this.an={},this)},a.prototype.horizontalPageCount=function(){return this.l3.length},a.prototype.verticalPageCount=function(){return this.m3.length},a.prototype.sheetIndex=function(){return this._sheetIndex},a.prototype.q3=function(){return this.i3.width},a.prototype.r3=function(){return this.i3.height},a.prototype.s3=function(){var a=this;a.v3(),a.w3(),a.j3=a.verticalPageCount()*a.horizontalPageCount()},a.prototype.u3=function(a,b){var c,d,e,f,g,h=this,i=h.h3.fitPagesWide(),j=1;for(i<b&&(j=i/b),c=i*h.q3()/a,d=c,e=15,f=(c-j)/e,g=0;g<=e&&(h.horizontalZoomFactor(d),h.w3(),h.horizontalPageCount()!==i);g++)d-=f},a.prototype.t3=function(a,b){var c,d,e,f,g,h=this,i=h.h3.fitPagesTall(),j=1;for(i<b&&(j=i/b),c=i*h.r3()/a,d=c,e=15,f=(c-j)/e,g=0;g<=e&&(h.verticalZoomFactor(d),h.v3(),h.verticalPageCount()!==i);g++)d-=f},a.prototype.w3=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=this,s=r.h3,t=r.kj,u=s.columnStart(),v=u===-1?0:u,w=Ia(t,s);if(w!==-1){for(a=r.x3(),b=r.q3(),c=s.repeatColumnStart(),d=s.repeatColumnEnd(),e=s.showRowHeader(),f=[],g=v;g<=w;g++)t.getColumnPageBreak(g)&&f.push(g);for(r.l3=[],h=-1,i=La(v,w,f),j=0,k=i.length;j<k-1;j+=2){if(l=i[j],m=i[j+1],n=[],2===e||0===e&&t.options.rowHeaderVisible)for(n=r.y3(l,m,c,d,3,b-a,h),o=0,p=n.length;o<p;o++)n[o].headerSize(a);else 3===e?(q=r.y3(l,m,c,d,3,b-a,h),q.length>0&&(q[0].headerSize(a),n.push(q[0]),q.length>1&&(q=r.y3(q[1].itemStart(),m,c,d,3,b,q[0].itemEnd()),n=n.concat(q)))):n=r.y3(l,m,c,d,3,b,h);r.l3=r.l3.concat(n),n.length>0&&(h=n[n.length-1].itemEnd())}}},a.prototype.v3=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r=this,s=r.h3,t=r.kj,u=s.rowStart(),v=u===-1?0:u,w=Ja(t,s);if(w!==-1){for(a=r.z3(),b=r.r3(),c=s.repeatRowStart(),d=s.repeatRowEnd(),e=s.showColumnHeader(),f=[],g=v;g<=w;g++)t.getRowPageBreak(g)&&f.push(g);for(r.m3=[],h=-1,i=La(v,w,f),j=0,k=i.length;j<k-1;j+=2){if(l=i[j],m=i[j+1],n=[],2===e||0===e&&t.options.colHeaderVisible)for(n=r.B3(l,m,c,d,3,b-a,h),o=0,p=n.length;o<p;o++)n[o].headerSize(a);else 3===e?(q=r.B3(l,m,c,d,3,b-a,h),q.length>0&&(q[0].headerSize(a),n.push(q[0]),q.length>1&&(q=r.B3(q[1].itemStart(),m,c,d,3,b,q[0].itemEnd()),n=n.concat(q)))):n=r.B3(l,m,c,d,3,b,h);r.m3=r.m3.concat(n),n.length>0&&(h=n[n.length-1].itemEnd())}}},a.prototype.x3=function(){var a,b=this,c=b.y3(0,b.kj.getColumnCount(2)-1,-1,-1,2,b.q3(),-1),d=0;for(a=0;a<c.length;a++)d+=c[a].contentSize();return d},a.prototype.z3=function(){var a,b=this,c=b.B3(0,b.kj.getRowCount(1)-1,-1,-1,1,b.r3(),-1),d=0;for(a=0;a<c.length;a++)d+=c[a].contentSize();return d},a.prototype.JBb=function(a,b,c,d,e,f,g){var h,i,j,k,l,m=this;if(3===f){for(i=0,j=d;j<=e;j++)h=a?m.C3(j,f):m.D3(j,f),i+=h;for(k=0,l=b;l<=c;l++)l>e&&(h=a?m.C3(l,f):m.D3(l,f),k<h&&(k=h));if(i+k>g)return!1}return!0},a.prototype.y3=function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y=this;for(y.JBb(!0,a,b,c,d,e,f)||(c=-1,d=-1),h=c<a,i=0,j=0,k=0,l=0,m=f,p=!1,q=z,r=[],a>b&&(s=a,a=b,b=s),t=a;t<=b;t++){if(h&&3===e&&(n=c,o=d,n!==-1&&n<=g)){for(o!==-1&&o>g&&(o=g-1),q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(d),u=n;u<=o;u++)v=y.C3(u,e),u<=g&&(l+=v);m-=l,h=!1}if(i=y.C3(t,e),i>m&&(p||t===a)){for(p=!1,w=0;i-w>=m;)q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(d),q.itemStart(t),q.itemEnd(t),q.contentSize(f),q.contentOffset(w),r.push(q),w+=m,q=z,j=0,h=!0;if(x=i-w,0===x){g=t;continue}if(g=t-1,t===b){q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(d),q.itemStart(t),q.itemEnd(t),q.contentSize(x+l),q.contentOffset(w),r.push(q);break}if(t++,!(t<=b))break;q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(d),j=x,q.contentOffset(w),i=y.C3(t,e)}j+=i,j>m||j===m?(j>m?(k=j-i+l,t-=1):k=j+l,q===z&&(q=new W),g===-1?q.itemStart(a):q.itemStart(g+1),q.itemEnd(t),q.contentSize(k),r.push(q),q=z,j=0,l=0,m=f,h=!0,g=t,i>m&&(p=!0)):t===b&&(q===z&&(q=new W),k=j+l,g===-1?q.itemStart(a):q.itemStart(g+1),q.itemEnd(t),q.contentSize(k),r.push(q),q=z,j=0,l=0,m=f,h=!0)}return r},a.prototype.B3=function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y=this;for(y.JBb(!1,a,b,c,d,e,f)||(c=-1,d=-1),h=c<a,i=0,j=0,k=0,l=0,m=f,o=d,p=!1,q=z,r=[],a>b&&(s=a,a=b,b=s),t=a;t<=b;t++){if(h&&3===e&&(n=c,o=d,n!==-1&&n<=g)){for(o!==-1&&o>g&&(o=g-1),q===z&&(q=new W),q.repeatItemStart(n),q.repeatItemEnd(o),u=n;u<=o;u++)v=y.D3(u,e),u<=g&&(l+=v);m-=l,h=!1}if(i=y.D3(t,e),i>m&&(p||t===a)){for(p=!1,w=0;i-w>=m;)q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(o),q.itemStart(t),q.itemEnd(t),q.contentSize(f),q.contentOffset(w),r.push(q),w+=m,q=z,j=0,h=!0;if(x=i-w,0===x){g=t;continue}if(g=t-1,t===b){q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(d),q.itemStart(t),q.itemEnd(t),q.contentSize(x+l),q.contentOffset(w),r.push(q);break}
if(t++,!(t<=b))break;q===z&&(q=new W),q.repeatItemStart(c),q.repeatItemEnd(o),j=x,q.contentOffset(w),i=y.D3(t,e)}j+=i,j>m||j===m?(j>m?(k=j-i+l,t-=1):k=j+l,q===z&&(q=new W),g===-1?q.itemStart(a):q.itemStart(g+1),q.itemEnd(t),q.contentSize(k),r.push(q),q=z,j=0,l=0,m=f,h=!0,g=t,i>m&&(p=!0)):t===b&&(q===z&&(q=new W),k=j+l,g===-1?q.itemStart(a):q.itemStart(g+1),q.itemEnd(t),q.contentSize(k),r.push(q),q=z,j=0,l=0,m=f,h=!0)}return r},a.prototype.E3=function(a,b){var c,d,f,g=this,h=g.kj,i=h.defaults,j=h.getColumnVisible(a,b);return 3===b&&a<h.getColumnCount()&&h.columnOutlines&&(j=j&&!h.columnOutlines.isCollapsed(a)),c=0,j&&(c=h.getColumnWidth(a,b),g.h3.bestFitColumns()&&(d=2===b,f=e.Ul.xl(a,h,d?2:3,1),c=f<=0?d?i.rowHeaderColWidth:i.colWidth:f)),c},a.prototype.C3=function(a,b){var c,d=this,e=d.an[b];return e||(e=d.an[b]={}),c=e[a],qa(c)&&(c=e[a]=d.E3(a,b)*d.n3),c},a.prototype.F3=function(a,b){var c,d,f,g,h=this,i=h.kj,j=i.defaults,k=j.rowHeight,l=j.colHeaderRowHeight,m=i.getRowVisible(a,b);return 3===b&&a<i.getRowCount()&&i.rowOutlines&&(m=m&&!(i.Ps&&i.Ps(a))&&!i.rowOutlines.isCollapsed(a)),c=0,m&&(c=i.getRowHeight(a,b),h.h3.bestFitRows()&&(d=1===b,f=e.Ul.Fl(a,i,d?1:3,1),f<=0?c=d?l:k:(g=f,d?g<l&&(g=l):g<k&&(g=k),c=g))),c},a.prototype.D3=function(a,b){var c,d=this,e=d.$m[b];return e||(e=d.$m[b]={}),c=e[a],qa(c)&&(c=e[a]=d.F3(a,b)*d.o3),c},a}(),b.WorksheetPaginator=_;function La(a,b,c){var d=[],e,f,g;for(d.push(a),f=c.length,e=0;e<f;e++)g=c[e],g-1>=a&&(d.push(g-1),d.push(g));return d.push(b),d}aa=function(){function a(a){this.OC=a,this.j3=0;var b=[],c,d,e,f;for(d=a.getSheetCount(),c=0;c<d;c++)e=a.getSheet(c),e.visible()&&(f={},sa(e,e.printInfo(),f),b.push(new _(a,c,f.paperSize,f.pageImageableArea)));this.G3=b}return a.prototype.paginate=function(){for(var a=this.G3,b=a.length,c;b;)b--,c=a[b],c.paginate(),this.j3+=c.pageCount()},a.prototype.pageCount=function(){return this.j3},a.prototype.getPage=function(a){var b,c,d,e,f={pageNumberInSheet:-1},g=this.getSheetPaginator(a,f),h=f.pageNumberInSheet,i=z;if(g&&(i=g.getPage(h),i.sheetIndex()>0))for(i.pageNumber(a),b=this.G3,c=0,d=b.length;c<d;c++)e=b[c],i.sheetIndex()<e.sheetIndex()&&(i.rowPageIndex(i.rowPageIndexInSheet()+e.verticalPageCount()),i.columnPageIndex(i.columnPageIndexInSheet()+e.horizontalPageCount()));return i},a.prototype.getSheetPaginator=function(a,b){var c,d,e,f=0,g=-1,h=this.G3;for(c=0,d=h.length;c<d;c++){if(e=h[c],g=a-f,g>=0&&g<e.pageCount())return b.pageNumberInSheet=g,e;f+=e.pageCount()}return z},a}(),b.WorkbookPaginator=aa,ba=function(){function a(a){this.Xva=a}return a.prototype.Yva=function(a,b){var c,d=document.createElement("div");d.style.width=b.width()+"px",d.style.height=b.height()+"px",a.appendChild(d),b.Nva(d,b.sheet().printInfo().qualityFactor(),!1),c=d.getElementsByTagName("canvas")[0],b.no(),b.Xs=c},a.prototype.Zva=function(){var a=this,b=a.xo;b||(b=a.xo=document.createElement("div"),b.style.width="0px",b.style.height="0px",b.style.overflow="hidden",document.body.appendChild(b)),a.Xva.forEach(function(c){a.Yva(b,c)}),b.innerHTML=""},a.prototype.no=function(){var a,b=this;b.Xva=z,a=b.xo,a&&(a.parentElement&&a.parentElement.removeChild(a),b.xo=z)},a}();function Ma(a){var b=a.contentDocument;return b.head||b.write("<head></head>"),b.body||b.write("<body></body>"),b}function Na(a,b){var c;return a instanceof _?c=a:a instanceof aa&&(c=a.getSheetPaginator(b,{})),c}function Oa(a,b,c,d){var e=ra(a,x),f=e.style;return f.border="1px transparent solid",f.boxSizing="border-box",f.width=b+w,f.height=c+w,f.position="relative",e.className=d,a.body.appendChild(e),e}function Pa(a,b,c,d,e,f){var g=ra(a,x),h=g.style;return h.marginLeft=e+w,h.width=c+w,h.marginTop=f+w,h.height=d+w,b.appendChild(g),g}function Qa(a,b,c,d){var e=ra(a,y),f=h(e),g=e.style;return g.height=c+w,g.width=b+w,e.width=f.width()*d,e.height=f.height()*d,e.getContext(v).scale(d,d),e}function Ra(a,b,c,d){var e=ra(a,y),f=h(e),g=e.style;return g.height=c+w,g.width=b+w,e.width=f.width()*d,e.height=f.height()*d,e.getContext(v).scale(d,d),e}function Sa(a,b,c,d){var e=ra(a,x),f=e.style;return f.width=c+w,f.height=d+w,b.appendChild(e),e}function Ta(a,b,c,d,e,f,g,i,j,k){var l,n,o,p,q,r,s,t,u,z=0,A="border-box";if(f?z=1:(l=a.options.sheetAreaOffset,d+=l.left,e+=l.top),n=c.style,1!==g&&3!==g||(n.boxSizing=A,n.paddingLeft=(h(c).width()-d-2*z)/2+w),2!==g&&3!==g||(n.boxSizing=A,n.paddingTop=(h(c).height()-e-2*z)/2+w),o=ra(b,x),p=o.style,p.width=d+w,p.height=e+w,p.border=z+"px black solid",q=ra(b,y),r=q.style,r.margin=z+w,q.width=1/i*(d-2*z),q.height=1/j*(e-2*z),r.width=d-2*z+w,r.height=e-2*z+w,s=k,t=k,m.chrome)for(u=q.width*s*q.height*t;u<65792;)u*=2,t*=2;return q.$va=q.width,q._va=q.height,q.width*=s,q.height*=t,q.getContext(v).scale(s,t),c.appendChild(o),q}function Ua(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r,s,u,v,x,y,z,A,B,C,D,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y=[];if(!b)return Y;for(g="&",h="K",i="S",j="U",k='"',l="B",m="I",n="D",o="T",p="P",q="N",r="G",s="F",u="A",v=g.length,x=/&[0-9]+/,y=/&K[0-9A-Fa-f]{6}/,z=/&".+"/,A=t,B=!1,C=0,D=!1,F=!1,G=!1,H="black",I=t;b;)J=A,K=B,L=C,M=G,N=D,O=F,P=H,Q=b.indexOf(g),Q<0&&(Q=b.length),I+=b.substr(0,Q),R=Q+1<b.length?b.substr(Q+1,1):t,R=R.toUpperCase(),S=-1,T=!1,U=!1,V=void 0,W=void 0,X=new Date,"1234567890".indexOf(R)>-1?(V=x.exec(b.substr(Q)),V&&V.length>0?(W=V[0],L=E(W.substr(1)),T=!0,S=Q+W.length):S=Q+v):R===h?(V=y.exec(b.substr(Q)),V&&V.length>0?(W=V[0],P="#"+W.substr(2,2)+W.substr(4,2)+W.substr(6,2),T=!0,S=Q+W.length):S=Q+v):R===i?(K=!K,T=!0,S=Q+v+i.length):R===j?(M=!M,T=!0,S=Q+v+j.length):R===k?(V=z.exec(b.substr(Q)),V&&V.length>0?(W=V[0],J=W.substr(2,W.length-3),T=!0,S=Q+W.length):S=Q+v):R===l?(N=!N,T=!0,S=Q+v+l.length):R===m?(O=!O,T=!0,S=Q+v+m.length):R===g?(I+=g,T=!0,S=Q+v+v):R===n?(I+=X.getFullYear()+"/"+(X.getMonth()+1)+"/"+X.getDate(),T=!0,S=Q+v+n.length):R===o?(I+=X.getHours()+":"+X.getMinutes()+":"+X.getSeconds(),T=!0,S=Q+v+o.length):R===p?(I+=a,T=!0,S=Q+v+p.length):R===q?(I+=e+f-1,T=!0,S=Q+v+q.length):R===r?(c&&(T=!0,U=!0),S=Q+v+r.length):R===s?(I+=d.workbookName()||t,T=!0,S=Q+v+s.length):R===u?(I+=d.worksheetName()||t,T=!0,S=Q+v+u.length):S=Q+v,S>=b.length?b=t:(S<0&&(S=Q+v),b=b.substr(S)),!I||!T&&b||(Y.push({text:I,underline:G,strikethrough:B,fontFamily:A,fontSize:C>0?C+w:t,fontWeight:D?"bold":t,fontStyle:F?"italic":t,color:H}),I=t),U&&c&&Y.push({image:c}),A=J,B=K,C=L,G=M,D=N,F=O,H=P;return Y}function Va(a,b,c,d,e,f,g,h,i){var j,l,m,n,o,p,q,r,s,t,u=[],v=[];for(j=0,l=b.length;j<l;j++)if(m=b[j],n=m.text)if(o=n.indexOf("\r\n")>=0,o||n.indexOf("\n")>=0){for(p=o?n.split("\r\n"):n.split("\n"),p[0]&&v.push(k({},m,{text:p[0]})),u.push(v),v=[],q=1,r=p.length;q<r-1;q++)p[q]&&u.push([k({},m,{text:p[q]})]);p[p.length-1]&&v.push(k({},m,{text:p[p.length-1]}))}else v.push(m);else v.push(m);for(v.length>0&&u.push(v),s=0,t=u.length;s<t;s++)Wa(a,u[s],c,d+s*f/t,e,f/t,g,h,i)}function Wa(a,b,c,d,f,g,h,i,j){var k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,B,C=[],D=[],F=[],G=A(f,g)-2,H=c;for(1===h?H=c+f/2:2===h&&(H=c+f),k=G,m=b.length,l=0;l<m;l++)p=b[l],q=p.image,r=p.text,s=p.fontFamily,t=p.fontSize,u=p.fontWeight,v=p.fontStyle,q?1===h?H-=G/2:2===h&&(H-=G):r&&(w=Ya(s,t,u,v),C[l]=w,e.Ul.lZa(a,w),o=E(t||"13.3px"),k<o&&(k=o),D[l]=o,n=a.measureText(r).width,F[l]=n,1===h?H-=n/2:2===h&&(H-=n));for(x=d+k/2,1===i?x=d+g/2:2===i&&(x=d+g-k/2),a.save(),a.textAlign="left",a.textBaseline="middle",m=b.length,l=0;l<m;l++)p=b[l],q=p.image,r=p.text,y=p.underline,z=p.strikethrough,B=p.color,q?(Xa(a,j,q,H,x-G/2,G,G),H+=G):r&&(a.beginPath(),a.font=C[l],a.fillStyle=B,a.fillText(r,H,x),o=D[l],n=F[l],(y||z)&&(a.beginPath(),a.moveTo(H,z?x:x+o/2),a.lineTo(H+n,z?x:x+o/2),a.stroke()),H+=n);a.restore()}function Xa(a,b,c,d,e,f,g){if(b.ko(c)){var h=b.lo(c);try{a.drawImage(h,0,0,h.width,h.height,d,e,f,g)}catch(a){}}else b.fo(c)}function Ya(a,b,c,d){var e,f,g,h;return a||(a="Arial"),b||(b="13.3px"),e=t,f="normal",g=" ",h="400",d!==f&&(e=d),c!==f&&c!==h&&(e+=(e?g:t)+c),e+=(e?g:t)+b,e+=g+a}function Za(a,b){var c,d,e=a.getImageData(b.x,b.y,b.width,b.height),f=e.data;for(c=0;c<f.length-4;c+=4)d=C((30*f[c]+59*f[c+1]+11*f[c+2]+50)/100),f[c]=d,f[c+1]=d,f[c+2]=d;a.putImageData(e,b.x,b.y)}function $a(a,b,c,d,e,f){var g=a.ss,h=a.zoom(),i=a.it(e),j=a.jt(d),k=i[0],l=i[i.length-1],m=j[0],n=j[j.length-1];Ba(a,function(a){var d,e,o,p,q,r,s,t,u,v,w,x,y,z;if(a.isVisible()&&a.canPrint()){if(d=0,e=0,o=a.width()*h,p=a.height()*h,q=a.startColumn(),r=i.findCol(q),r)d=r.x;else if(q<k.col)for(d=k.x,s=k.col-1;s>=q;s--)d-=g._m(s);else for(d=l.x,t=l.col+1;t<=q;t++)d+=g._m(t);if(d+=a.startColumnOffset()*h,u=a.startRow(),v=j.findRow(u),v)e=v.y;else if(u<m.row)for(e=m.y,w=m.row-1;w>=u;w--)e-=g.Sl(w);else for(e=n.y,x=n.row+1;x<=u;x++)e+=g.Sl(x);if(e+=a.startRowOffset()*h,c&&c.intersect(d,e,o,p)){if(b.save(),b.rect(c.x,c.y,c.width,c.height),b.clip(),b.beginPath(),"1"===a.typeName)y=a.qmb||a.src(),y&&Xa(b,f,y,d,e,o,p);else if("2"===a.typeName&&(z=a.Xs))try{b.drawImage(z,0,0,z.width,z.height,d,e,o,p)}catch(a){}b.restore()}}})}function _a(a,b){var c=[],d=a.$r(b);return Ba(a,function(a){a.startColumn()<=b&&b<=a.endColumn()&&(c.push({type:"startColumnOffset",floatingObject:a,floatingObjectOffset:a.startColumnOffset()}),a.startColumnOffset(a.startColumnOffset()-d))}),c}function ab(a,b){var c=[],d=a.Yr(b);return Ba(a,function(a){a.startRow()<=b&&b<=a.endRow()&&(c.push({type:"startRowOffset",floatingObject:a,floatingObjectOffset:a.startRowOffset()}),a.startRowOffset(a.startRowOffset()-d))}),c}function bb(a,b,c,d){a.wu().execute({cmd:"autoFitColumn",sheetName:a.name(),columns:b,rowHeader:c,autoFitType:d})}function cb(a,b,c,d){a.wu().execute({cmd:"autoFitRow",sheetName:a.name(),rows:b,columnHeader:c,autoFitType:d})}function db(a,b,c,d){var e=b.toDataURL(),f=a.createElement("img");return d&&(f.style.margin=b.style.margin),f.style.width=b.style.width,f.style.height=b.style.height,f.src=e,c.ko(e)||c.fo(e),f}function eb(a,b){var c,d,e,f,g,h,i,j=[],k=1,l=-1;for(c=0,d=a.pageCount();c<d;c++)e=a.getPage(c),f=e.sheetIndex(),l!==f&&(g=b.getSheet(f),h=g.printInfo(),i=h.firstPageNumber(),(1!==i||h.fma)&&(k=i),l=f),j[c]=k,k++;return j}ca=function(){function a(a,b){this.xn={},this.OC=a,this.H3=b}return a.prototype.initBuild=function(){var a=this,b=Ma(a.I3());b.open(),b.write("<head><style>body{margin:0} .gc-printPage{page-break-after: always} .grayscale{filter: grayscale(100%)}</style></head><body></body>"),b.close()},a.prototype.initContainer=function(a,b){var c=this,d=Ma(c.I3()),e=a.width,f=a.height,g=b.left,h=b.right,i=b.top,j=b.bottom,k=b.header,l=b.footer,m=e-g-h,n=f-A(k,i)-A(l,j),o=Oa(d,e,f,u);c.xo=o,c.c5=Pa(d,o,m,n,g,A(k,i))},a.prototype.processHeader=function(a,b,c,d,e,f,g,h){var i=this,j=i.c5,k=h.qualityFactor,l=Ma(i.I3()),m=Qa(l,a,b,k);i.Pma(m.getContext(v),a,b,c,d,e,f,g),j.appendChild(k<=2?m:db(l,m,f))},a.prototype.Pma=function(a,b,c,d,e,f,g,h){var i,k,l=j(d._firstPageNumber)?1:d._firstPageNumber,m=Ua(h,d.headerLeft(),d.headerLeftImage(),e,f,l);Va(a,m,0,0,b/3,c,0,0,g),i=Ua(h,d.headerCenter(),d.headerCenterImage(),e,f,l),Va(a,i,b/3,0,b/3,c,1,0,g),k=Ua(h,d.headerRight(),d.headerRightImage(),e,f,l),Va(a,k,b/3*2,0,b/3,c,2,0,g)},a.prototype.processBody=function(a,b,c,d,f){var g,h=this,i=h.c5,j=f.qualityFactor,k=f.contentWidth,l=f.bodyContentHeight,m=f.showBorder,n=f.centering,o=Ma(h.I3()),p=Sa(o,i,k,l),q=f.sheetPaginator,r=Ta(a,o,p,b,c,m,n,q.horizontalZoomFactor(),q.verticalZoomFactor(),j),s=r.getContext(v),t=r.$va,u=r._va;h._l(a,s,new e.Rect(0,0,t,u),d,!m),h.Qma(a,s,new e.Rect(0,0,t,u),d),a.shapes&&a.shapes.Bob(a,s,new e.Rect(0,0,t,u)),g=p.children[0],g.appendChild(j<=2?r:db(o,r,d,!0))},a.prototype._l=function(a,b,c,d,e){a.bt=d,a.Us(c),a.invalidateLayout(),a.yl.LI(b,c)},a.prototype.Qma=function(a,b,c,d){var e,f,g,h=a.am();for(e=0;e<=2;e++)for(f=0;f<=2;f++)g=c.getIntersectRect(h.Ft(e,f)),g&&$a(a,b,g,e,f,d)},a.prototype.CBb=function(a,b,c,d){var e=.96,f=this,g=f.xo,h=Ma(f.I3()),i=ra(h,y);i.style.position="absolute",i.style.left="0px",i.style.top="0px",i.style.zIndex="-1",i.width=c,i.height=d,b.forEach(function(b){var c=b.x,d=b.y,f=b.width,g=b.height,h=b.imageSrc;Xa(i.getContext(v),a,h,c*e,d*e,f*e,g*e)}),g.appendChild(i)},a.prototype.KBb=function(a){var b,c,d,e,f,g,h={};for(b=0;b<a.length;b++)if(c=a[b],d=c.page,"all"===d||"odd"===d||"even"===d)h[d]=h[d]||[],h[d].push(c);else if(!j(d))for(e=d.split(","),f=0;f<e.length;f++)g=e[f],h[g]=h[g]||[],h[g].push(c);return h},a.prototype.LBb=function(a,b){var c=[];return b.all&&(c=c.concat(b.all)),a%2===0&&b.even?c=c.concat(b.even):a%2===1&&b.odd?c=c.concat(b.odd):b[a]&&(c=c.concat(b[a])),c},a.prototype.processFooter=function(a,b,c,d,e,f,g,h){var i=this,j=i.c5,k=h.qualityFactor,l=Ma(i.I3()),m=Ra(l,a,b,k);i.Rma(m.getContext(v),a,b,c,d,e,f,g),j.appendChild(k<=2?m:db(l,m,f))},a.prototype.Rma=function(a,b,c,d,e,f,g,h){var i,k,l=j(d._firstPageNumber)?1:d._firstPageNumber,m=Ua(h,d.footerLeft(),d.footerLeftImage(),e,f,l);Va(a,m,0,0,b/3,c,0,2,g),i=Ua(h,d.footerCenter(),d.footerCenterImage(),e,f,l),Va(a,i,b/3,0,b/3,c,1,2,g),k=Ua(h,d.footerRight(),d.footerRightImage(),e,f,l),Va(a,k,b/3*2,0,b/3,c,2,2,g)},a.prototype.processBlackAndWhite=function(){var a,b,c,d,f=this.xo;if(m.mozilla)f.classList.add("grayscale");else for(a=h(y,f),b=0,c=a.length;b<c;b++)d=a[b],Za(d.getContext(v),new e.Rect(0,0,d.width,d.height))},a.prototype.clearAfterBuild=function(){var a=this,b=Ma(a.I3()),c=b.body.lastChild;c&&h(c).removeClass(u)},a.prototype.build=function(){var a,b,c,d,e,f,g,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,C,D,E,F,G,H,I,J,K,L,M,N=this,O=N.OC,P=N.H3,Q=O.options;for("white"===Q.backColor&&O.Sxb===!1&&(a=!0,O.options.backColor="rgba(0,0,0,0)"),N.initBuild(),h[h.sd]({tc:O}),b=P.pageCount(),c=N.vu(),N.awa(),d=eb(P,O),e={},f=0;f<b;f++)g=P.getPage(f),i=O.getSheet(g.sheetIndex()),j=i.printInfo(),k=i.name(),l=void 0,e[k]?l=e[k]:(m=j.watermark(),l=N.KBb(m),e[k]=l),n=g.pageNumberInSheet(),o=j.pageRange(),o&&(p=va(o),wa(p,b),p.indexOf(n)<0)||(q=N.J3(i,g),r=ua(j.margin()),s=r.left,t=r.right,u=r.top,v=r.bottom,w=r.header,x=r.footer,y=j.orientation(),z=ta(i,j.paperSize(),y),C=z.width,D=z.height,E=C-s-t,F=D-A(w,u)-A(x,v),G=B(0,u-w),H=B(0,v-x),I=g.getPageSize(),J=I.width,K=I.height,L={qualityFactor:j.qualityFactor(),marginHeader:w,paperHeight:D,marginFooter:x,contentWidth:E,bodyContentHeight:F-G-H,showBorder:j.showBorder(),centering:j.centering(),marginLeft:s,marginTop:u,sheetPaginator:Na(P,f)},N.initContainer(z,r),M=N.LBb(n,l),N.CBb(c,M,C,D),G>0&&N.processHeader(E,G,j,g,b,c,d[f],L),N.processBody(i,J,K,c,L),H>0&&N.processFooter(E,H,j,g,b,c,d[f],L),j.blackAndWhite()&&N.processBlackAndWhite(),N.H_a(q),i.no(!1));N.clearAfterBuild(),c.mo()&&N.onBuildCompleted(),a===!0&&(O.options.backColor="white",O.Sxb=!1)},a.prototype.print=function(){var a=this.I3(),b,c;m.msie?(b=Ma(a),b.execCommand("print")):(c=a.contentWindow,c.focus(),c.print()),window.focus()},a.prototype.dispose=function(){var a,b=this,c=b.K3,d=c&&c.parentElement;d&&(d.removeChild(c),b.K3=z),b.bt&&(b.bt.no(),b.bt=z),b.bwa&&(b.bwa.no(),b.bwa=z),a=b.OC,a&&(a.destroy(),b.OC=z),b.H3=z,b.xn={},b.c5=z,b.xo=z},a.prototype.I3=function(){var a,b,c;return this.K3||(a=document,b=ra(a,"IFRAME"),c=b.style,c.position="absolute",c.left="-10px",c.width="0px",c.height="0px",a.body.insertBefore(b,z),this.K3=b),this.K3},a.prototype.J3=function(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P=[];if(a.suspendPaint(),a.suspendEvent(),a.suspendCalcService&&a.suspendCalcService(),a.showRowOutline&&(a.showRowOutline(!1),a.showColumnOutline(!1)),a.zoom(1),c=a.options,d=a.printInfo(),e=d.showGridLine(),f=a.options.gridline,f.showHorizontalGridline=e,f.showVerticalGridline=e,g=this,!g.xn[a.name()]){if(h=!1,d.bestFitColumns()){for(i=d.columnStart(),j=i===-1?0:i,k=Ia(a,d),l=[],m=j;m<=k;m++)l.push({col:m});for(bb(a,l,!1,1),n=[],o=a.getColumnCount(2),p=0;p<o;p++)n.push({col:p});bb(a,n,!0,1),h=!0}if(d.bestFitRows()){for(q=d.rowStart(),r=q===-1?0:q,s=Ja(a,d),t=[],u=r;u<=s;u++)t.push({row:u});for(cb(a,t,!1,1),v=[],w=a.getRowCount(1),x=0;x<w;x++)v.push({row:x});cb(a,v,!0,1),h=!0}h&&Ba(a,function(a){a.Toa()}),g.xn[a.name()]=!0}if(y=d.showRowHeader(),0===y||(2===y||3===y&&0===b.columnPageIndexInSheet()?c.rowHeaderVisible=!0:c.rowHeaderVisible=!1),z=d.showColumnHeader(),0===z||(2===z||3===z&&0===b.rowPageIndexInSheet()?c.colHeaderVisible=!0:c.colHeaderVisible=!1),a.frozenRowCount(0),a.frozenColumnCount(0),a.frozenTrailingRowCount(0),a.frozenTrailingColumnCount(0),A=a.xr,A&&A.filterButtonVisible(!1),B=a.tables&&a.tables.all())for(D=B.length,C=0;C<D;C++)B[C].filterButtonVisible(!1);if(E=b.columnPage(),F=E.repeatItemStart(),G=E.repeatItemEnd(),H=0,F!==-1&&G!==-1)for(a.frozenColumnCount(G+1),D=F,C=0;C<D;C++)P.push({type:"colVisible",sheetName:a.name(),index:C,visible:a.getColumnVisible(C)}),a.setColumnVisible(C,!1),H+=a.$r(C),I=_a(a,C),P.push.apply(P,I);if(J=b.rowPage(),K=J.repeatItemStart(),L=J.repeatItemEnd(),M=0,K!==-1&&L!==-1)for(a.frozenRowCount(L+1),D=K,C=0;C<D;C++)P.push({type:"rowVisible",sheetName:a.name(),index:C,visible:a.getRowVisible(C)}),a.setRowVisible(C,!1),M+=a.Yr(C),N=ab(a,C),P.push.apply(P,N);return a.shapes&&(H>0||M>0)&&(O=a.shapes.$ob(a,H,M),P.push.apply(P,O)),a.showCell(J.itemStart(),E.itemStart(),0,0),P},a.prototype.H_a=function(a){var b,c,d,e,f,g,h,i,j,k,l=this.OC;for(b=a.length-1;b>=0;b--)c=a[b],d=c.type,e=c.index,f=c.visible,g=c.floatingObject,h=c.floatingObjectOffset,i=c.offset,j=c.shape,k=l.getSheetFromName(c.sheetName),"colVisible"===d?k.setColumnVisible(e,f):"rowVisible"===d?k.setRowVisible(e,f):"startRowOffset"===d?g.startRowOffset(h):"startColumnOffset"===d?g.startColumnOffset(h):"shapePosition"===d&&j.resize(i.x,i.y,j.width(),j.height())},a.prototype.vu=function(){var a=this;return a.bt||(a.bt=new e.oo(function(){a.bt&&a.build()})),a.bt},a.prototype.awa=function(){var a,b,c,d=this;d.bwa||(a=[],b=d.H3,c=b instanceof _?[b.kj]:d.OC.sheets,c.forEach(function(b){b.charts&&b.charts.all().forEach(function(b){b.isVisible()&&b.canPrint()&&(b.useAnimation(!1),a.push(b))})}),a.length>0&&(d.bwa=new ba(a),d.bwa.Zva()))},a}(),b.tQa=ca,da=function(){function a(){this.printer=new ea,this.isLayoutChanged=!0,this.sheetName=null,this.cachePageInfo=null,this.printInfoString=F((new T).toJSON())}return a.prototype.bindSheetEvent=function(){var a=this;a.sheet.Fu(e.Events.ColumnWidthChanged+H,function(){a.isLayoutChanged=!0,a.sheet.repaint()}),a.sheet.Fu(e.Events.ColumnChanged+H,function(){a.isLayoutChanged=!0}),a.sheet.Fu(e.Events.RowHeightChanged+H,function(){a.isLayoutChanged=!0,a.sheet.repaint()}),a.sheet.Fu(e.Events.RowChanged+H,function(){a.isLayoutChanged=!0}),a.sheet.Fu(e.Events.TableFiltered+H,function(){a.isLayoutChanged=!0}),a.sheet.Fu(e.Events.RangeFiltered+H,function(){a.isLayoutChanged=!0}),a.sheet.Fu(e.Events.TableFilterClearing+H,function(){a.isLayoutChanged=!0}),a.sheet.Fu(e.Events.RangeFilterClearing+H,function(){a.isLayoutChanged=!0}),a.sheet.Fu(e.Events.RangeGroupStateChanged+H,function(){a.isLayoutChanged=!0,a.sheet.repaint()}),a.sheet.Fu(e.Events.OutlineColumnCheckStatusChanged+H,function(){a.isLayoutChanged=!0,a.sheet.repaint()})},a.prototype.unbindSheetEvent=function(){var a=this;a.sheet.Gu(e.Events.ColumnWidthChanged+H),a.sheet.Gu(e.Events.ColumnChanged+H),a.sheet.Gu(e.Events.RowHeightChanged+H),a.sheet.Gu(e.Events.RowChanged+H),a.sheet.Gu(e.Events.TableFiltered+H),a.sheet.Gu(e.Events.RangeFiltered+H),a.sheet.Gu(e.Events.TableFilterClearing+H),a.sheet.Gu(e.Events.RangeFilterClearing+H),a.sheet.Gu(e.Events.RangeGroupStateChanged+H),a.sheet.Gu(e.Events.OutlineColumnCheckStatusChanged+H)},a.prototype.layoutChanged=function(a){var b=this,c=(b.sheet&&b.sheet.name())===a.name();c&&(this.isLayoutChanged=!0)},a.prototype.setSheet=function(a){var b=this,c=(b.sheet&&b.sheet.name())===a.name();c||(b.sheet&&b.unbindSheetEvent(),b.sheet=a,b.bindSheetEvent())},a.prototype.isNeedReCalc=function(a){var b,c,d,e=this;return a.options.allowCellOverflow=!1,b=e.sheetName===(a&&a.name()||void 0),c=e.isLayoutChanged===!1,d=e.printInfoString===F(a.printInfo().toJSON()),!(b&&c&&d)},a.prototype.pageInfo=function(a,b){var c,d,e,f,g,h,i,j=this;return qa(b)||b===z||(c=a.sheets[b],j.setSheet(c)),j.isNeedReCalc(c)?(a.suspendPaint(),a.suspendEvent(),d=j.changeWorkbookOptions(a),e=j.changeSheetOffset(a),f=j.printer.clacPaginator(a,b),a.options=d,j.appleOldSheetOffset(a,e),a.resumePaint(),a.resumeEvent(),g=f.j3,h=j.getRows(f.m3),i=j.getColumns(f.l3),j.isLayoutChanged=!1,j.printInfoString=F(c.printInfo().toJSON()),c&&(this.sheetName=c.name()),j.cachePageInfo={pageCount:g,rows:h,columns:i},j.cachePageInfo):j.cachePageInfo},a.prototype.changeWorkbookOptions=function(a){var b=G(F(a.options)),c=a.options;return c.scrollbarMaxAlign=!1,c.scrollbarShowMax=!0,c.scrollIgnoreHidden=!1,b},a.prototype.changeSheetOffset=function(a){var b,c,d=[],e=a.getSheetCount();for(c=0;c<e;c++)b=a.getSheet(c),d.push(b.options.sheetAreaOffset),b.printInfo().showBorder()?b.options.sheetAreaOffset={left:0,top:0}:b.options.sheetAreaOffset={left:2,top:2};return d},a.prototype.appleOldSheetOffset=function(a,b){var c,d,e=a.getSheetCount();for(d=0;d<e;d++)c=a.getSheet(d),c.options.sheetAreaOffset=b[d]},a.prototype.getRows=function(a){var b,c,d=[];for(b=0;b<a.length;b++)c=a[b]._itemEnd,d.push(c);return d},a.prototype.getColumns=function(a){var b,c,d=[];for(b=0;b<a.length;b++)c=a[b]._itemEnd,d.push(c);return d},a}(),ea=function(){function a(){}return a.prototype.print=function(a,b){var c,d,e=this;e.dispose(),c=e.prepareContext(a,b),d=new ca(c.workbook,c.paginator),e.M3=d,d.onBuildCompleted=function(){var b=d.I3(),c={iframe:b,cancel:!1};a.Wq("BeforePrint",c),c.cancel!==!0&&d.print()},d.build()},a.prototype.clacPaginator=function(a,b){var c=this,d,e,f;do qa(b)||b===z?e=new aa(a):(f={},d=a.getSheet(b),sa(d,d.printInfo(),f),e=new _(a,b,f.paperSize,f.pageImageableArea)),e.paginate();while(!c.L3(e,a));return e},a.prototype.prepareContext=function(a,b){var c,d,f,g,h,i=this,j=new e.Workbook;for(j.GBb=!0,j.suspendPaint(),j.suspendEvent(),j.fromJSON(G(F(a.toJSON({includeBindingSource:!0,ignoreFormula:!0})))),d=j.options,d.scrollbarMaxAlign=!1,d.scrollbarShowMax=!0,d.scrollIgnoreHidden=!1,f=j.getSheetCount(),h=0;h<f;h++)g=j.getSheet(h),g.printInfo().showBorder()?g.options.sheetAreaOffset={left:0,top:0}:g.options.sheetAreaOffset={left:2,top:2};return c=i.clacPaginator(j,b),{workbook:j,paginator:c}},a.prototype.dispose=function(){var a=this.M3;a&&a.dispose(),this.M3=z},a.prototype.L3=function(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.pageCount();for(c=0;c<m;c++){if(d=a.getPage(c),e=void 0,f=void 0,g=void 0,h=d.rowPage(),i=void 0,j=void 0,h.contentOffset()>0&&c-1>=0&&(e=a.getPage(c-1),i=e.rowPage(),f=d.sheetIndex(),g=b.getSheet(f),f===e.sheetIndex()))return j=g.e3&&g.e3._zoomFactor,g.setRowHeight(i.itemEnd(),Math.floor(h.contentOffset()/j),3),!1;if(k=d.columnPage(),l=void 0,k.contentOffset()>0&&c-1>=0&&(e=a.getPage(c-1),l=e.columnPage(),f=d.sheetIndex(),g=b.getSheet(f),f===e.sheetIndex()))return j=g.e3&&g.e3._zoomFactor,g.setColumnWidth(l.itemEnd(),Math.floor(k.contentOffset()/j),3),!1}return!0},a}(),b.uQa=ea},"./dist/plugins/print/print.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/print/print.res.en.js");b.SR={en:d}},"./dist/plugins/print/print.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidSheetIndex="Invalid  sheet index."},Common:function(b,c){b.exports=a.Spread},Core:function(b,c){b.exports=a.Spread.Sheets}})});