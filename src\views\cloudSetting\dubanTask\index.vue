<template>
  <div class="dubanTaskPage">
    <div class="top-search">
      <a-form-model
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="来源">
              <a-select size="small" v-model="form.source" allowClear>
                <a-select-option
                  :value="`${item.key}-智造云图`"
                  v-for="item in companyList"
                  :key="`${item.key}-智造云图`"
                >
                  {{ `${item.key}-智造云图` }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="状态">
              <a-select size="small" v-model="form.status" allowClear>
                <a-select-option
                  :value="`${item.key}`"
                  v-for="item in statusList"
                  :key="`${item.key}`"
                >
                  {{ `${item.value}` }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="进度">
              <a-select size="small" allowClear v-model="form.rate">
                <a-select-option
                  :value="`${item.key}`"
                  v-for="item in monitorStatus"
                  :key="`${item.key}`"
                >
                  {{ `${item.value}` }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="被督办人">
              <a-select
                allowClear
                size="small"
                show-search
                :value="form.undertaker"
                style="width: 100%"
                :filter-option="false"
                :not-found-content="fetching ? undefined : null"
                @search="fetchUser"
                @change="handledbSelectCommonChange('undertaker', $event)"
              >
                <a-spin v-if="fetching" slot="notFoundContent" size="small" />
                <a-select-option
                  v-for="d in userData"
                  :key="`${d.userCode}-${d.ldapDeptId}`"
                >
                  {{ d.cnName }}[{{ d.userName }}]{{
                    d.ldapFullName
                      ? "-" + changeLdapFullName(d.ldapFullName)
                      : ""
                  }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="督办人">
              <a-select
                allowClear
                size="small"
                show-search
                :value="form.supervisor"
                style="width: 100%"
                :filter-option="false"
                :not-found-content="fetching ? undefined : null"
                @search="fetchUser"
                @change="handledbSelectCommonChange('supervisor', $event)"
              >
                <a-spin v-if="fetching" slot="notFoundContent" size="small" />
                <a-select-option
                  v-for="d in userData"
                  :key="`${d.userCode}-${d.ldapDeptId}`"
                >
                  {{ d.cnName }}[{{ d.userName }}]{{
                    d.ldapFullName
                      ? "-" + changeLdapFullName(d.ldapFullName)
                      : ""
                  }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="相关人员">
              <a-select
                allowClear
                size="small"
                show-search
                :value="form.allman"
                style="width: 100%"
                :filter-option="false"
                :not-found-content="fetching ? undefined : null"
                @search="fetchUser"
                @change="handledbSelectCommonChange('allman', $event)"
              >
                <a-spin v-if="fetching" slot="notFoundContent" size="small" />
                <a-select-option
                  v-for="d in userData"
                  :key="`${d.userCode}-${d.ldapDeptId}`"
                >
                  {{ d.cnName }}[{{ d.userName }}]{{
                    d.ldapFullName
                      ? "-" + changeLdapFullName(d.ldapFullName)
                      : ""
                  }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="发布时间">
              <a-range-picker allowClear size="small" @change="onChange" />
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-row>
              <a-col :span="6"></a-col>
              <a-col :span="16">
                <div class="search-btn">
                  <a-button
                    size="small"
                    type="primary"
                    style="margin-right: 10px;"
                    @click="requestData"
                  >
                    搜索
                  </a-button>
                  <a-button size="small" @click="resetRequest">重置</a-button>
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <div class="table">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="tableLoading"
        size="small"
        :pagination="false"
      >
        <!-- 内容 -->
        <span slot="content" slot-scope="text, record">
          <div style="margin-bottom: 5px;">
            <label style="font-weight: bold">关键词:</label>
            {{ record.keyWord ? record.keyWord : "" }}
          </div>
          <div>
            <label style="font-weight: bold">内容:</label>
            <span v-if="text !== '' && text !== null && text !== undefined">
              {{ text.length > 80 ? text.substr(0, 80) + "..." : text }}
            </span>
          </div>
        </span>
        <!-- 人员 -->
        <span slot="peopleName" slot-scope="text, record">
          <div>
            <label style="font-weight: bold">督办人:</label>
            <span>{{
              record.supervisorName ? record.supervisorName : ""
            }}</span>
          </div>
          <div style="margin-top: 5px;">
            <label style="font-weight: bold">被督办人:</label>
            <span>{{
              record.undertakerName ? record.undertakerName : ""
            }}</span>
          </div>
          <div v-if="record.transferPersonName" style="margin-top: 5px;">
            <label style="font-weight: bold">转办人:</label>
            <span>{{
              record.transferPersonName ? record.transferPersonName : ""
            }}</span>
          </div>
          <div v-if="record.coordinatorNameString" style="margin-top: 5px;">
            <label style="font-weight: bold">共同完成人:</label>
            <span>{{
              record.coordinatorNameString ? record.coordinatorNameString : ""
            }}</span>
          </div>
        </span>
        <!-- 督办状态 -->
        <span slot="statusAndProgress" slot-scope="text, record">
          <div>
            <template v-if="record.status == '0'">
              <span style="color: red;">{{ getDictValue(record.status) }}</span>
            </template>
            <template v-else>
              {{
                getDictValue(
                  record.status === "9" && record.lookBack === "0"
                    ? "888"
                    : record.status
                )
              }}
            </template>
          </div>
          <div style="margin-top: 5px;">
            {{ record.progress ? record.progress + "%" : "0%" }}
          </div>
        </span>
        <!-- 发布时间 -->
        <span slot="publishDate" slot-scope="text">
          {{ text ? moment(text).format("YYYY-MM-DD") : "/" }}
        </span>
        <!-- 承诺完成时间 -->
        <span slot="requireDate" slot-scope="text">
          {{ text ? moment(text).format("YYYY-MM-DD") : "/" }}
        </span>
        <!-- 操作 -->
        <span slot="operation" slot-scope="text, record">
          <a-button type="link" size="small" @click="openDubanWindow(record)"
            >查看详情</a-button
          >
        </span>
      </a-table>
      <div style="display: flex;justify-content: flex-end;">
        <a-pagination
          v-model="pageNum"
          style="margin-top: 20px;"
          :total="total"
          :show-total="total => `共 ${total} 条`"
          :page-size="pageSize"
          show-size-changer
          :page-size-options="pageSizeOptions"
          @showSizeChange="onShowSizeChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import request from "@/utils/requestHttp";
import {
  changeLdapFullName,
  getDuBanUserByName,
  dubanConfig
} from "@/utils/utils.js";
import debounce from "lodash/debounce";
import moment from "moment";
export default {
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      moment,
      pageSizeOptions: ["10", "20", "30", "40", "50"],
      changeLdapFullName,
      form: {
        source: "",
        status: "",
        rate: "",
        undertaker: "",
        supervisor: "",
        allman: "",
        startDate: "",
        endDate: ""
      },
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      companyList: [], // 公司列表
      statusList: [
        // 督办状态
        {
          key: "1",
          value: "待接收"
        },
        {
          key: "2",
          value: "督办中"
        },
        {
          key: "3",
          value: "待审核"
        },
        {
          key: "4",
          value: "待评价"
        },
        {
          key: "9",
          value: "已完结"
        },
        {
          key: "999",
          value: "废弃"
        },
        {
          key: "0",
          value: "草稿"
        },
        {
          key: "888",
          value: "督办回头看"
        },
        {
          key: "40",
          value: "待预审"
        }
      ],
      monitorStatus: [
        // 进度列表
        {
          key: "normal",
          value: "正常"
        },
        {
          key: "delay",
          value: "拖期"
        },
        {
          key: "warning",
          value: "预警"
        }
      ],
      userData: [],
      fetching: false,
      columns: [
        {
          title: "督办来源",
          dataIndex: "source",
          key: "source",
          width: 150
        },
        {
          title: "督办内容",
          dataIndex: "content",
          key: "content",
          scopedSlots: { customRender: "content" },
          width: 350
        },
        {
          title: "人员",
          dataIndex: "peopleName",
          key: "peopleName",
          scopedSlots: { customRender: "peopleName" },
          width: 200
        },
        {
          title: "督办状态",
          dataIndex: "statusAndProgress",
          key: "statusAndProgress",
          scopedSlots: { customRender: "statusAndProgress" }
        },
        {
          title: "发布时间",
          dataIndex: "publishDate",
          key: "publishDate",
          scopedSlots: { customRender: "publishDate" }
        },
        {
          title: "承诺完成时间",
          dataIndex: "requireDate",
          key: "requireDate",
          scopedSlots: { customRender: "requireDate" }
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          scopedSlots: { customRender: "operation" }
        }
      ],
      orginTableData: [], // 表格数据
      tableLoading: false
    };
  },
  created() {
    this.getCompanyList();
  },
  computed: {
    dataList() {
      const sourceList = this.companyList.map(item => {
        return `${item.key}-智造云图`;
      });
      const list = this.orginTableData.filter(item => {
        return sourceList.includes(item.source);
      });
      return list;
    },
    tableData() {
      return this.dataList.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageNum * this.pageSize
      );
    },
    total: {
      get() {
        return this.dataList.length;
      }
    }
  },
  methods: {
    onShowSizeChange(current, pageSize) {
      console.log(current, pageSize);
      this.pageNum = current;
      this.pageSize = pageSize;
      this.requestData();
    },
    onChange(date, dateString) {
      this.form.startDate = dateString[0] || "";
      this.form.endDate = dateString[1] || "";
    },
    // 获取当前登录人所拥有的公司列表数据权限
    getCompanyList() {
      request(`/api/smc/indexCardUrl/getPlateCompany`).then(res => {
        if (Array.isArray(res) && res.length) {
          this.companyList = res;
          this.requestData();
        }
      });
    },
    // 查询ldap用户
    fetchUser(value, callback) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      getDuBanUserByName(value, res => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        if (res && Array.isArray(res)) {
          this.userData = res;
          typeof callback === "function" && callback(res);
          this.fetching = false;
        }
      });
    },
    getDictValue(status) {
      return this.statusList.filter(item => item.key === status)[0].value;
    },
    handledbSelectCommonChange(key, value) {
      this.form[key] = value;
      this.fetching = false;
      this.$nextTick(() => {
        this.userData = [];
      });
    },
    resetRequest() {
      this.form = {
        source: "",
        status: "",
        rate: "",
        undertaker: "",
        supervisor: "",
        allman: "",
        startDate: "",
        endDate: ""
      };
      this.requestData();
    },
    // 请求数据
    requestData() {
      this.tableLoading = true;
      this.orginTableData = [];
      const params = {
        userCode: dubanConfig.adminUserCode,
        format: "0",
        pcOrMobileFlag: "pc",
        range: "all",
        code: "",
        type: dubanConfig.defaultParams.type,
        content: "",
        source: this.form.source || "",
        supervisor: this.form.supervisor || "",
        undertaker: this.form.undertaker || "",
        allman: this.form.allman || "",
        status: this.form.status || "",
        publishDate: "",
        rate: this.form.rate || "",
        startDate: this.form.startDate || "",
        endDate: this.form.endDate || "",
        page: 1,
        size: 999999
      };
      request(`/api/smc/duban/search`, {
        methods: "GET",
        params
      })
        .then(res => {
          if (res.hasOwnProperty("list") && res.list.length) {
            res.list.forEach(item => {
              item.content = item.content
                .replace(/<img.*?>/g, "")
                .replace(/<\/?.+?>/g, "")
                .replace(/&nbsp;/g, "");
            });
            this.orginTableData = res.list;
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    // 打开督办窗口
    openDubanWindow(record) {
      window.open(`${dubanConfig.origin}navigation?linkId=${record.id}`);
    }
  }
};
</script>
<style lang="less">
.dubanTaskPage {
  background: #fff;
  padding: 16px;
  box-sizing: border-box;
  .top-search {
    margin-bottom: 24px;
    .ant-form-item {
      margin-bottom: 8px;
    }
    .search-btn {
      display: flex;
      justify-content: flex-end;
      padding-top: 8px;
    }
  }
}
</style>
