<!--
 * @Description: 视像核心KPI概览图
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-07 14:26:03
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2023-02-09 11:25:50
-->
<template>
  <!-- 给这个div绑定公司名称的class是为了拖拽时候找到html元素 -->
  <div
    class="indexGeneralViewPage-inHidata"
    :ref="`${companyName}indexGeneralViewPage-inhidata`"
    :class="companyName"
  >
    <!-- 底部卡片区域 -->
    <div class="_bottom" :style="{ 'padding-bottom': '0' }">
      <CardList
        ref="cardList"
        activePlate="全部"
        :indexCardDetailInfoJSUrl="indexCardDetailInfoJSUrl"
        :cardListLoading="cardListLoading"
        :list="cardList"
        :companyName="companyName"
        pageClass="indexGeneralViewPage"
        :frequency="this.searchForm.indexDt"
        :indexDt="this.searchForm.indexDt"
        :inHiData="true"
      />
    </div>
  </div>
</template>
<script>
import CardList from "../Card/cardList.vue";
import request from "@/utils/requestHttp";
import sortBy from "lodash/sortBy";
import { adminUserUrlPrefix } from "@/utils/utils";
import { dealThousandData } from "../IndexGeneralView/utils";
import { getUrlParam } from "@/utils/utils.js";
export default {
  name: "IndexGeneralView",
  components: { CardList },
  props: {
    data: Object,
    // 是否设计器里
    isDesign: {
      type: Boolean,
      default() {
        return false;
      }
    }
  },
  provide() {
    return {
      isDesign: this.isDesign
    };
  },
  computed: {
    // 当前组件属性props对象
    attribute() {
      if (this.data && this.data.props) {
        return this.data.props;
      } else {
        return {};
      }
    },
    // 公司名称
    companyName() {
      return getUrlParam("company") || "空调";
    },
    // 详情组件地址url
    indexCardDetailInfoJSUrl() {
      return (
        this.attribute.indexCardDetailInfoJSUrl ||
        "http://smc.devapps.hisense.com/minio/smcbucket/IndexCardDetailInfo.umd.min.1.0.js"
      );
    }
  },
  data() {
    return {
      searchForm: {
        indexDt: "",
        frequency: "",
        base: ""
      },
      cardList: [], // 卡片列表
      cardListLoading: true, // 卡片列表Loading
      SYS_NAME: window.system // 系统名称
    };
  },
  async created() {
    /**
     * 概览页面逻辑
     * index.vue请求版块和基地
     * 先获取版块和基地，基地获取成功后，传入到PageHeader组件(A)中，
     * A组件中监听baseList后修改searchForm通过$emit("pageHeaderChange")反馈给当前index.vue组件
     * 的searchConditionChange方法来进行卡片列表的请求
     */
    // 进入日志
    const year = new Date().getFullYear(),
      month = `${new Date().getMonth() + 1}`.padStart(2, "0");
    let base = "",
      indexDt = getUrlParam("indexDt") || `${year}-${month}`,
      frequency = getUrlParam("frequency") || `月`;
    if (this.companyName === "集团") {
      let list = await this.getBase();
      base = getUrlParam("base") || list[0];
    } else {
      base = getUrlParam("base") || "整体";
    }
    this.searchForm = {
      base,
      indexDt,
      frequency
    };
    this.searchConditionChange();
    window.addEventListener("resize", this.getMainHeight);
  },
  destroyed() {
    window.removeEventListener("resize", this.getMainHeight);
  },
  methods: {
    // 根据公司获取基地
    getBase() {
      return new Promise(resolve => {
        request(
          `/api/smc/indexCardInfo/getBaseBycompanyName?companyName=${this.companyName}`
        ).then(res => {
          resolve(res || []);
        });
      });
    },
    getMainHeight() {
      this.$nextTick().then(() => {
        setTimeout(() => {
          window.parent.postMessage(
            {
              contentHeight: this.$refs[
                `${this.companyName}indexGeneralViewPage-inhidata`
              ].offsetHeight,
              sourceType: "smc"
            },
            "*"
          );
        }, 0);
      });
    },
    getAndSetWindowHeight() {
      let height = "";
      if (window.self === window.top) {
        // 在盘古内部使用
        height = document.getElementsByClassName("100heightDiv")[0]
          ?.offsetHeight;
        height = height ? `${height}px` : "100vh";
      } else {
        // 在信数内使用
        height = window.innerHeight + "px";
      }
      this.$refs[
        `${this.companyName}indexGeneralViewPage-inhidata`
      ].style.setProperty("--realHeight", height);
    },
    // 获取卡片列表
    getCardList() {
      return new Promise(resolve => {
        request(
          `${adminUserUrlPrefix["zcx"]}/indexCardInfo/getDwppCmTfIndexLibraryByUserId`,
          {
            method: "POST",
            body: {
              company: this.companyName,
              base: this.searchForm.base,
              frequency: this.searchForm.frequency,
              indexDt: this.searchForm.indexDt,
              type: "0",
              status: ""
            }
          }
        )
          .then(res => {
            if (res && Array.isArray(res)) {
              res.forEach(item => {
                item["recommend"] = false;
                if (item.dwppCmTfIndexLibrary === null) {
                  item.dwppCmTfIndexLibrary = {};
                }
                item["baseName"] = this.searchForm.base;
                // 处理实际值和目标值
                item["realBaseActual"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.baseActual,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
                item["realTargetValue"] = dealThousandData(
                  item.dwppCmTfIndexLibrary?.targetValue,
                  item.unit,
                  item.dwppCmTfIndexLibrary?.precisions
                );
                if (this.companyName === "集团") {
                  item["companyName"] = "集团";
                }
                item["dataFrequency"] =
                  this.searchForm.timeType === "day"
                    ? "日"
                    : this.searchForm.timeType === "week"
                    ? "周"
                    : "月";
                item["indexDt"] = this.searchForm.indexDt;
              });
              // 下边处理数据是为了把没数据的卡片放到最后展示
              const hasAPKDataArr = res.filter(item => {
                return Object.keys(item.dwppCmTfIndexLibrary).length > 0;
              });
              const noAPKDataArr = res.filter(item => {
                return Object.keys(item.dwppCmTfIndexLibrary).length === 0;
              });
              this.cardList = sortBy(hasAPKDataArr, function(item) {
                return item.indexSort;
              });
              this.cardList = [...this.cardList, ...noAPKDataArr];
              // 获取高度
              this.getMainHeight();
            } else {
              this.cardList = [];
              // 获取高度
              this.getMainHeight();
            }
            resolve(res);
          })
          .catch(() => {
            this.cardList = [];
            resolve([]);
          });
      });
    },
    // 查询条件更改
    searchConditionChange() {
      this.cardListLoading = true;
      this.getCardList().then(() => {
        this.cardListLoading = false;
      });
    }
  }
};
</script>
<style lang="less">
@import url("../common.less");
.indexGeneralViewPage-inHidata {
  font-size: 14px;
  font-family: PingFangSC-Regular;
  color: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  // height: var(--realHeight);
  ._flex {
    display: flex;
    align-items: center;
  }
  & > ._bottom {
    background-color: #fff;
    margin: 0 -8px;
    box-sizing: border-box;
    overflow-y: auto;
    flex: 1;
  }
}
</style>
