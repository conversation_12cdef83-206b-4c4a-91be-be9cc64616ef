<template>
  <a-modal title="组织选择" :visible="show" :closable="false">
    <div style="min-height: 400px;">
      <a-tree class="org-list-tree" blockNode show-line :expanded-keys="ytExpandedKeys" :selected-keys="ytSelectedKeys"
        :tree-data="ytOrgList" @select="ytTreeSelect" @expand="onYTExpand">
        <span v-for="item in ytStateOrgList" :key="item.id" :slot="item.slotTitle">{{ item.realTitle }}
          <span style="font-size: 10px;color: red;">{{
            item.state === "N" ? "失效" : ""
          }}</span></span>
      </a-tree>
      <a-divider />
      <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="填写有效时间">
          <a-switch v-model="form.switchSign" />
        </a-form-model-item>
        <template v-if="form.switchSign">
          <a-form-model-item label="开始时间">
            <a-date-picker v-model="form.beginDate" show-time type="date" style="width: 100%;" valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD" />
          </a-form-model-item>
          <a-form-model-item label="结束时间">
            <a-date-picker valueFormat="YYYY-MM-DD" v-model="form.endDate" show-time type="date" style="width: 100%;"
              format="YYYY-MM-DD" />
          </a-form-model-item>
        </template>
      </a-form-model>
    </div>
    <div slot="footer">
      <a-button @click="show = false">取消</a-button>
      <a-button @click="onOk(true)" type="primary">同级添加</a-button>
      <a-button @click="onOk(false)" type="primary">下级添加</a-button>
    </div>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import uniqBy from "lodash/uniqBy";
const YTRquestUrl1 = "/api/smc2/treeOrg/searchTopLevelCM"; // 组织树顶级查询(右侧云图)
const YTRquestUrl2 = "/api/smc2/treeOrg/searchNextLevelCM"; // 组织树逐级查询 (右侧云图)
import moment from "moment";
export default {
  props: {
    searchTime: {
      require: true
    },
    activeOrgTreeId: {
      require: true
    }
  },
  data() {
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 14 },
      parentComOriginData: {}, // 父级弹窗选中数据源数据
      show: false,
      // 云图组织层级相关数据
      ytDataLoading: false,
      ytExpandedKeys: [],
      ytSelectedKeys: [],
      ytOrgList: [],
      ytOrgPPList: [],
      ytOriginDataList: [],
      form: {
        switchSign: false,
        beginDate: "",
        endDate: ""
      },
    };
  },
  computed: {
    ytStateOrgList() {
      return uniqBy(this.ytOrgPPList, "key");
    }
  },
  methods: {
    // YT组织树选择
    ytTreeSelect(selectedKeys) {
      this.ytSelectedKeys = selectedKeys;
      selectedKeys.length && this.getYTOrgList(selectedKeys[0]);
    },
    onYTExpand(expandedKeys) {
      this.ytExpandedKeys = expandedKeys;
    },
    // originData 父级弹窗选中数据源数据
    showDialog(originData) {
      this.show = true;
      this.parentComOriginData = originData;

      // 获取完整的组织路径数组
      const fullCodeArr = this.parentComOriginData.fullCode.split("/").filter(item => item);
      // 构建完整路径数组
      const pathArr = [];
      fullCodeArr.reduce((prev, curr) => {
        const path = prev ? `${prev}/${curr}` : curr;
        pathArr.push(path);
        // 调用getYTOrgList并传入完整路径数组
        return path;
      }, '');
      pathArr.shift('H')
      this.getYTOrgList(null, pathArr);
    },
    clearData() {
      this.ytExpandedKeys = [];
      this.ytSelectedKeys = [];
      this.ytOrgList = [];
      this.ytOrgPPList = [];
      this.ytOriginDataList = [];
      this.parentComOriginData = {};
      this.form = {
        switchSign: false,
        beginDate: "",
        endDate: ""
      };
    },
    onOk(isSameLevel) {
      if (!this.ytSelectedKeys[0]) {
        this.$message.warning("请选择组织");
        return;
      }
      const fullCode = this.ytOrgPPList.filter(
        item => item.key === this.ytSelectedKeys[0]
      )[0].fullCode;
      const { state,id } = this.ytOrgPPList.filter(
        item => item.key === this.ytSelectedKeys[0]
      )[0];
      const originData = this.ytOriginDataList.filter(
        (item) => {
          if (typeof this.ytSelectedKeys[0] === 'string') {
            return item.code === this.ytSelectedKeys[0]
          } else {
            return item.id === this.ytSelectedKeys[0]
          }
        }
      )[0];
      if (state === "N") {
        this.$message.warning("请选择有效组织");
        return;
      }
      if (this.form.switchSign) {
        if (!this.form.beginDate || !this.form.endDate) {
          this.$message.warning("请选择有效起止时间！");
          return;
        }
        // if (
        //   moment(this.form.beginDate).isBefore(this.parentComOriginData.endDate)
        // ) {
        //   this.$message.warning("新调整起始时间应晚于调整前有效结束时间！");
        //   return;
        // }
      }
      this.$emit("choose", {
        dhrCode: this.ytSelectedKeys[0],
        fullCode,
        originData,
        id,
        ...this.form,
        isSameLevel: isSameLevel
      });
      this.show = false;
      this.clearData();
    },
    setTopOrgList(arr) {
      let treeData = arr.map(item => {
        return {
          title: `${item.name}(${item.code})`,
          fullCode: item.fullCode,
          value: item.code,
          key: item.id,
          children: []
        };
      });
      this.ytOrgPPList = [...treeData, ...this.ytOrgPPList];
      this.ytOriginDataList = uniqBy(
        [...arr, ...this.ytOriginDataList],
        "id"
      );
      this.ytOrgList = treeData;
      treeData.length && this.getYTOrgList(treeData[0].key);
    },
    getYTOrgList(parentOrgId, arr = []) {
      // parentOrgCode 接收的是带版本号的code或者空 定义一个realCode来查找到数据的真实dhrCode不带版本号的
      let realCode = "";
      if (parentOrgId) {
        const ytOriginData = this.ytOriginDataList.filter(
          (item) => {
            if (typeof parentOrgId === 'string') {
              return item.code === parentOrgId
            } else {
              return item.id === parentOrgId
            }
          }
        )[0];
        realCode = ytOriginData.code;
      }
      this.ytDataLoading = true;
      request(`${parentOrgId ? YTRquestUrl2 : YTRquestUrl1}`, {
        method: "POST",
        body: parentOrgId
          ? {
            superiorCode: realCode,
            fullCode: this.ytOrgPPList.filter(
              (item) => item.id === parentOrgId
            )[0].fullCode,
            treeType: this.activeOrgTreeId,
            endDate: this.searchTime || "",
          }
          : {
            treeType: this.activeOrgTreeId,//首次请求
            endDate: this.searchTime || "",
          },
      }).then((res) => {
        let treeData = res.map((item) => {
          return {
            title: `${item.name}(${item.code})`,
            fullCode: item.fullCode,
            disabled: false,
            superiorCode: item.superiorCode,
            key: item.id,
            id: item.id,
            code: item.code,
            children: [],
            slots: {
              title: `show-state-${item.id}`,
            },
            slotTitle: `show-state-${item.id}`,
            state: item.invalid,
            realTitle: `${item.name}(${item.code})`,
            levelName: item.levelName,
          };
        });
        this.ytOrgPPList = [...treeData, ...this.ytOrgPPList];
        this.ytOriginDataList = uniqBy(
          [...res, ...this.ytOriginDataList],
          "id"
        );
        // 首次加载两层
        if (!parentOrgId) {
          this.ytOrgList = treeData;
          treeData.length && this.getYTOrgList(treeData[0].id);
        } else {
          // 展开节点
          this.treeDataDeepFillArr(this.ytOrgList, parentOrgId, treeData);
          if (treeData.length) {
            this.ytExpandedKeys = [...this.ytExpandedKeys, parentOrgId];
          }
        }
        if (arr.length) {
          this.midWareFun(arr);
        }
        this.ytDataLoading = false;
      }).catch((err) => {
        console.log(err, 'err')
        this.ytDataLoading = false;
        this.$message.error("获取组织结构失败,请尝试重新点击");
      });
    },
    midWareFun(arr) {
      if (arr.length) {
        const ytOriginData = this.ytOriginDataList.filter(
          (item) => item.fullCode === arr[0]
        )[0];
        if (arr.length === 1 && ytOriginData) {
          this.ytSelectedKeys = [ytOriginData.id];
        }
        arr.splice(0, 1);
        ytOriginData && this.getYTOrgList(ytOriginData.id, arr);
      }
    },
    // 树形组件深层次查找并赋值
    treeDataDeepFillArr(treeData, orgId, children) {
      for (let i = 0; i < treeData.length; i++) {
        const element = treeData[i];
        if (element.id === orgId) {
          treeData[i].children = children;
          break;
        } else {
          this.treeDataDeepFillArr(element.children, orgId, children);
        }
      }
    },
  },
};
</script>
<style lang="less" scoped></style>
