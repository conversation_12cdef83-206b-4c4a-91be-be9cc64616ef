{"name": "vue-com", "version": "0.1.0", "private": true, "scripts": {"start": "vue-cli-service serve", "build:com": "vue-cli-service build --target lib --name myComY src/views/Demo/ComDemo/index.js", "build:buttonDemo": "vue-cli-service build --target lib --name ButtonDemoJs src/views/Demo/ButtonDemo/index.js", "build:queryDataDemo": "vue-cli-service build --target lib --name QueryDataDemo src/views/Demo/QueryDataDemo/index.js", "build:cardDemo": "vue-cli-service build --target lib --name card_demo src/views/Demo/CardDemo/index.vue", "build:layout": "vue-cli-service build --target lib --name myLayout src/layout/index.vue", "build:button": "vue-cli-service build --target lib --name handleButton1 src/views/Demo/CustomButton/index.vue", "build:tableBtn": "vue-cli-service build --target lib --name handleButton2 src/views/Demo/TableButton/index.vue", "build:tableCell": "vue-cli-service build --target lib --name pageCode-columnCode src/views/Demo/TableCell/index.vue", "build:indexGeneralView": "vue-cli-service build --target lib --name IndexGeneralView src/views/SMC_PC/IndexGeneralView/index.js", "build:indexGeneralViewInHiData": "vue-cli-service build --target lib --name CardListInHiData src/views/SMC_PC/CardListInHiData/index.js", "build:indexComparison": "vue-cli-service build --target lib --name IndexComparison src/views/SMC_PC/IndexComparison/index.js", "build:tableLineOperationEdit": "vue-cli-service build --target lib --name handleRoleManageEdit src/views/cloudSetting/roleManage/tableLineOperationEdit.vue", "build:indexTOAdd": "vue-cli-service build --target lib --name handleIndexManageAdd src/views/cloudSetting/indexManage/tableOperationAdd.vue", "build:indexTOEdit": "vue-cli-service build --target lib --name handleIndexManageEdit src/views/cloudSetting/indexManage/tableLineOperationEdit.vue", "build:EarlyWarningSettingTOAdd": "vue-cli-service build --target lib --name handleEarlyWarningSettingAdd src/views/cloudSetting/EarlyWarningSetting/tableOperationAdd.vue", "build:EarlyWarningSettingTOEdit": "vue-cli-service build --target lib --name handleEarlyWarningSettingEdit src/views/cloudSetting/EarlyWarningSetting/tableLineOperationEdit.vue", "build:indexFillInTOAdd": "vue-cli-service build --target lib --name handleIndexFillInTOAdd src/views/cloudSetting/indexFillIn/tableOperationAdd.vue", "build:indexFillInTLOEdit": "vue-cli-service build --target lib --name handleIndexFillInTLOEdit src/views/cloudSetting/indexFillIn/tableLineOperationEdit.vue", "build:indexTargetFillInTLOEdit": "vue-cli-service build --target lib --name handleIndexTargetFillInTLOEdit src/views/cloudSetting/indexTargetFillIn/tableLineOperationEdit.vue", "build:indexTargetFillInTableCell": "vue-cli-service build --target lib --name indexTargetFillIn-nowMonthFill src/views/cloudSetting/indexTargetFillIn/tableCell.vue", "build:indexTargetFillInTLOExport": "vue-cli-service build --target lib --name handleIndexTargetFillInTLOExport src/views/cloudSetting/indexTargetFillIn/tableLineOperationExport.vue", "build:dubanTask": "vue-cli-service build --target lib --name DubanTask src/views/cloudSetting/dubanTask/index.js", "build:indexCardDetailInfo": "vue-cli-service build --target lib --name IndexCardDetailInfo src/views/SMC_PC/Card/CardInfo/index.js", "build:videoCom": "vue-cli-service build --target lib --name VideoCom src/views/SMC_PC/VideoCom/index.js", "build:selfReports": "vue-cli-service build --target lib --name SelfReports src/views/SMC_PC/IndexGeneralView/SelfReports/index.js", "build:RDScreen": "vue-cli-service build --target lib --name RDScreen src/views/RDBigScreen/index.js", "build:orgManage": "vue-cli-service build --target lib --name OrgManage src/views/boot-console/org-manage/index.js", "build:baseSettingTOAddType": "vue-cli-service build --target lib --name handle1663918064780 src/views/boot-console/base-setting/tableOperationAddType.vue", "build:baseSettingTOAddTypeValue": "vue-cli-service build --target lib --name handle1663918356668 src/views/boot-console/base-setting/tableOperationAddTypeValue.vue", "build:baseSettingTLOEdit": "vue-cli-service build --target lib --name handle1663924155634 src/views/boot-console/base-setting/tableLineOperationEdit.vue", "build:baseSettingTOImport": "vue-cli-service build --target lib --name handle1690422058175 src/views/boot-console/base-setting/tableOperationImport.vue", "build:registryTOAdd": "vue-cli-service build --target lib --name handle1664242339687 src/views/boot-console/registry/tableOperationAdd.vue", "build:registryTLOEdit": "vue-cli-service build --target lib --name handle1664244029291 src/views/boot-console/registry/tableLineOperationEdit.vue", "build:registryTOExport": "vue-cli-service build --target lib --name handle1667782927804 src/views/boot-console/registry/tableOperationExport.vue", "build:attributeTOAdd": "vue-cli-service build --target lib --name handle1665361897067 src/views/boot-console/attribute/tableOperationAdd.vue", "build:attributeTOBatchUpdate": "vue-cli-service build --target lib --name handle1672123586204 src/views/boot-console/attribute/tableOperationBatchUpdate.vue", "build:attributeTOExport": "vue-cli-service build --target lib --name handle1667782650519 src/views/boot-console/attribute/tableOperationExport.vue", "build:attributeTLOEdit": "vue-cli-service build --target lib --name handle1665472706973 src/views/boot-console/attribute/tableLineOperationEdit.vue", "build:targetTLOEdit": "vue-cli-service build --target lib --name handle1665553805353 src/views/boot-console/target/tableLineOperationEdit.vue", "build:targetTLOEdit2": "vue-cli-service build --target lib --name handle1672211402037 src/views/boot-console/target/tableLineOperationEdit2.vue", "build:targetTLOExport": "vue-cli-service build --target lib --name handle1665625276632 src/views/boot-console/target/tableLineOperationExport.vue", "build:targetTLOExport2": "vue-cli-service build --target lib --name handle1689665086639 src/views/boot-console/target/tableLineOperationExport2.vue", "build:target-fillTLOAdd": "vue-cli-service build --target lib --name handle1729564785736 src/views/boot-console/target/tableLineOperationAdd.vue", "build:target-fillTLOImport": "vue-cli-service build --target lib --name handle1729731491397 src/views/boot-console/target/tableLineOperationInport.vue", "build:gjyxTargetTLOEdit": "vue-cli-service build --target lib --name handle1718347121379 src/views/boot-console/target-gjyx/tableLineOperationEdit.vue", "build:gjyxTargetTLOEdit2": "vue-cli-service build --target lib --name handle1718347286974 src/views/boot-console/target-gjyx/tableLineOperationEdit2.vue", "build:gjyxTargetTLOExport": "vue-cli-service build --target lib --name handle1718347188215 src/views/boot-console/target-gjyx/tableLineOperationExport.vue", "build:gjyxTargetTLOExport2": "vue-cli-service build --target lib --name handle1718347346302 src/views/boot-console/target-gjyx/tableLineOperationExport2.vue", "build:indexCardTLOEdit": "vue-cli-service build --target lib --name handle1665626118878 src/views/boot-console/index-card/tableLineOperationEdit.vue", "build:roleTLOEdit": "vue-cli-service build --target lib --name handle1665967458452 src/views/boot-console/role/tableLineOperationEdit.vue", "build:gateSearchTableCell": "vue-cli-service build --target lib --name EtUfYfWnbp-org src/views/boot-console/gate-search/tableCell.vue", "build:gate-configTOAdd": "vue-cli-service build --target lib --name handle1684222907519 src/views/boot-console/gate-config/tableOperationAdd.vue", "build:gate-configPerson": "vue-cli-service build --target lib --name handle1684224594013 src/views/boot-console/gate-config/tableOperationPerson.vue", "build:gateConfigTableCell": "vue-cli-service build --target lib --name Bw0cNBXqxR-remark src/views/boot-console/gate-config/tableCell.vue", "build:indexGeneralView2.0": "vue-cli-service build --target lib --name IndexGeneralView2 src/views/SMC_PC2/IndexGeneralView/index.js", "build:indexGeneralView2.0InBEM": "vue-cli-service build --target lib --name IndexGeneralView2InBEM src/views/SMC_PC2/BEMIndexGeneralView/index.js", "build:indexComparison2.0": "vue-cli-service build --target lib --name IndexComparison2 src/views/SMC_PC2/IndexComparison/index.js", "build:indexCardDetailInfo2.0": "vue-cli-service build --target lib --name IndexCardDetailInfo2 src/views/SMC_PC2/Card/CardInfo/index.js", "build:selfReports2.0": "vue-cli-service build --target lib --name SelfReports2 src/views/SMC_PC2/IndexGeneralView/SelfReports/index.js", "build:indexDetectionInfo2.0": "vue-cli-service build --target lib --name IndexDetectionInfo2 src/views/SMC_PC2/Card/DetectionInfo/index.js", "build:indexMDYSFXInfo": "vue-cli-service build --target lib --name IndexMDYSFXInfo src/views/SMC_PC2/Card/MDYSFXInfo/index.js", "build:analysis-settingTOAdd": "vue-cli-service build --target lib --name handle1668998281489 src/views/boot-console/analysis-setting/tableOperationAdd.vue", "build:analysis-settingTLOEdit": "vue-cli-service build --target lib --name handle1668998360965 src/views/boot-console/analysis-setting/tableLineOperationEdit.vue", "build:early-warningTOAdd": "vue-cli-service build --target lib --name handle1669183719033 src/views/boot-console/early-warning/tableOperationAdd.vue", "build:early-warningTLOEdit": "vue-cli-service build --target lib --name handle1669189150330 src/views/boot-console/early-warning/tableLineOperationEdit.vue", "build:early-warning-WarnType-TableCell": "vue-cli-service build --target lib --name E75vGmvpRm-warnType src/views/boot-console/early-warning/tableCell-WarnType.vue", "build:early-warning-WarnLevel-TableCell": "vue-cli-service build --target lib --name E75vGmvpRm-warnLevel src/views/boot-console/early-warning/tableCell-WarnLevel.vue", "build:index-ownerTLOEdit": "vue-cli-service build --target lib --name handle1690178641157 src/views/boot-console/index-owner/tableLineOperationEdit.vue", "build:front-fillTLOEdit": "vue-cli-service build --target lib --name handle1695285223659 src/views/boot-console/front-fill/tableLineOperationEdit.vue", "build:front-fillTLOAdd": "vue-cli-service build --target lib --name handle1695727596883 src/views/boot-console/front-fill/tableLineOperationAdd.vue", "build:gjyx-front-fillTLOEdit": "vue-cli-service build --target lib --name handle1718355514803 src/views/boot-console/front-fill-gjyx/tableLineOperationEdit.vue", "build:gjyx-front-fillTLOAdd": "vue-cli-service build --target lib --name handle1718355585258 src/views/boot-console/front-fill-gjyx/tableLineOperationAdd.vue", "build:SXKJ-fillTLOEdit": "vue-cli-service build --target lib --name handle1700532978457 src/views/boot-console/SXKJ-fillpage/tableLineOperationEdit.vue", "build:SXKJ-fillTLOEdit2": "vue-cli-service build --target lib --name handle1729232486159 src/views/boot-console/SXKJ-fillpage/tableLineOperationEdit2.vue", "build:SXKJ-fillTOEdit1": "vue-cli-service build --target lib --name handle1700620456594 src/views/boot-console/SXKJ-fillpage/tableOperationEdit1.vue", "build:SXKJ-fillTOEdit2": "vue-cli-service build --target lib --name handle1700620567884 src/views/boot-console/SXKJ-fillpage/tableOperationEdit2.vue", "build:SXKJ-fillpage-tableCell": "vue-cli-service build --target lib --name iUbCGXOeWl-warnType src/views/boot-console/SXKJ-fillpage/table-cell.vue", "build:SXKJ-examineBtn": "vue-cli-service build --target lib --name handle1729221529058 src/views/boot-console/SXKJ-fillpage/examineBtn.vue", "build:SXKJ-examineBtn2": "vue-cli-service build --target lib --name handle1729236347670 src/views/boot-console/SXKJ-fillpage/examineBtn2.vue", "build:self-report-pushTOAdd": "vue-cli-service build --target lib --name handle1705046882980 src/views/boot-console/self-report-push/tableOperationAdd.vue", "build:self-report-pushTLOEdit": "vue-cli-service build --target lib --name handle1705049881611 src/views/boot-console/self-report-push/tableLineOperationEdit.vue", "build:DetectionInfoForBI": "vue-cli-service build --target lib --name DetectionInfoForBI src/views/SMC_PC2/Card/DetectionInfoForBI/index.js", "lint": "vue-cli-service lint", "build:DMSBIPageReview": "vue-cli-service build --target lib --name DMSBIPageReview src/views/DMSBIPageReview/index.js"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "ant-design-vue": "^1.7.8", "axios": "^0.20.0", "core-js": "^3.6.5", "crypto-js": "^3.1.9-1", "decimal.js": "^10.4.3", "echarts": "^5.1.2", "html2canvas": "^1.4.1", "js-export-excel": "^1.1.4", "jspdf": "^2.5.1", "lodash": "^4.17.20", "lodash.clonedeep": "^4.5.0", "marked": "^4.3.0", "moment": "^2.30.1", "pangea-com": "0.1.5-beta.6", "vue": "^2.6.11", "vue-i18n": "^8.14.0", "vue-ls": "^3.2.1", "vue-router": "^3.4.3", "vue-video-player": "^5.0.2", "vue2-org-tree": "^1.3.6", "vuex": "^3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-service": "^4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "prettier": "^1.19.1", "qs": "^6.10.1", "vue-template-compiler": "^2.6.11"}}