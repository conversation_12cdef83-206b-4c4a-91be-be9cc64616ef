/**
 * 车间站立会议亮色模式修复样式
 * 本文件专门修复亮色主题下Ant Design下拉选择项文字透明的问题
 * 样式按照组件类型和功能进行分组
 */

/*==================================
  1. 修复亮色模式下拉选择项文字透明问题
==================================*/
/* 修复Ant Design下拉菜单选中项文字消失的问题 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-item-selected,
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-item-selected > a,
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-submenu-title-selected,
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-submenu-title-selected > a,
body:not(.has-dark-theme) .ant-dropdown-menu-item-selected,
body:not(.has-dark-theme) .ant-dropdown-menu-item-selected > a,
body:not(.has-dark-theme) .ant-dropdown-menu-submenu-title-selected,
body:not(.has-dark-theme) .ant-dropdown-menu-submenu-title-selected > a {
  color: rgba(0, 0, 0, 0.85) !important; /* 强制设置深色文字，覆盖透明色 */
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}

/* 修复表格筛选下拉菜单选中项文字消失的问题 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item-selected,
.workshop-meeting:not(.dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item-selected > a,
body:not(.has-dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item-selected,
body:not(.has-dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item-selected > a {
  color: rgba(0, 0, 0, 0.85) !important; /* 强制设置深色文字，覆盖透明色 */
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}

/* 修复所有下拉菜单选中项的文字颜色问题 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-item-selected .ant-dropdown-menu-title-content,
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-submenu-title-selected .ant-dropdown-menu-title-content,
body:not(.has-dark-theme) .ant-dropdown-menu-item-selected .ant-dropdown-menu-title-content,
body:not(.has-dark-theme) .ant-dropdown-menu-submenu-title-selected .ant-dropdown-menu-title-content {
  color: rgba(0, 0, 0, 0.85) !important; /* 强制设置深色文字 */
}

/* 确保下拉菜单项的基本样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-item,
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-submenu-title,
body:not(.has-dark-theme) .ant-dropdown-menu-item,
body:not(.has-dark-theme) .ant-dropdown-menu-submenu-title {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #ffffff !important; /* 白色背景 */
}

/* 下拉菜单项悬停状态 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-item:hover,
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu-submenu-title:hover,
body:not(.has-dark-theme) .ant-dropdown-menu-item:hover,
body:not(.has-dark-theme) .ant-dropdown-menu-submenu-title:hover {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
}

/* 下拉菜单容器样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-dropdown-menu,
body:not(.has-dark-theme) .ant-dropdown-menu {
  background-color: #ffffff !important; /* 白色背景 */
  border: 1px solid #d9d9d9 !important; /* 灰色边框 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important; /* 灰色阴影 */
}

/* 表格筛选下拉菜单容器样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-table-filter-dropdown,
body:not(.has-dark-theme) .ant-table-filter-dropdown {
  background-color: #ffffff !important; /* 白色背景 */
  border: 1px solid #d9d9d9 !important; /* 灰色边框 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important; /* 灰色阴影 */
}

/* 表格筛选下拉菜单项样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item,
body:not(.has-dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #ffffff !important; /* 白色背景 */
}

/* 表格筛选下拉菜单项悬停状态 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item:hover,
body:not(.has-dark-theme) .ant-table-filter-dropdown .ant-dropdown-menu-item:hover {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
}

/*==================================
  2. 修复Select下拉选择框选中项文字透明问题
==================================*/
/* 修复Select组件下拉菜单选中项文字消失的问题 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-select-dropdown-menu-item-selected,
.workshop-meeting:not(.dark-theme) .ant-select-dropdown-menu-item-selected:hover,
body:not(.has-dark-theme) .ant-select-dropdown-menu-item-selected,
body:not(.has-dark-theme) .ant-select-dropdown-menu-item-selected:hover {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}

/* 修复Select组件下拉菜单项基本样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-select-dropdown-menu-item,
body:not(.has-dark-theme) .ant-select-dropdown-menu-item {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #ffffff !important; /* 白色背景 */
}

/* 修复Select组件下拉菜单项悬停状态 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-select-dropdown-menu-item:hover,
body:not(.has-dark-theme) .ant-select-dropdown-menu-item:hover {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
}

/*==================================
  3. 修复级联选择器选中项文字透明问题
==================================*/
/* 修复级联选择器菜单选中项文字消失的问题 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-cascader-menu-item-active,
body:not(.has-dark-theme) .ant-cascader-menu-item-active {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}

/* 修复级联选择器菜单项基本样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-cascader-menu-item,
body:not(.has-dark-theme) .ant-cascader-menu-item {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #ffffff !important; /* 白色背景 */
}

/* 修复级联选择器菜单项悬停状态 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-cascader-menu-item:hover,
body:not(.has-dark-theme) .ant-cascader-menu-item:hover {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
}

/*==================================
  4. 修复分页组件选中项文字透明问题
==================================*/
/* 修复分页组件下拉选择器选中项文字消失的问题 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-pagination-options .ant-select-dropdown-menu-item-selected,
body:not(.has-dark-theme) .ant-pagination-options .ant-select-dropdown-menu-item-selected {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
}

/* 修复分页组件下拉选择器菜单项基本样式 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-pagination-options .ant-select-dropdown-menu-item,
body:not(.has-dark-theme) .ant-pagination-options .ant-select-dropdown-menu-item {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #ffffff !important; /* 白色背景 */
}

/* 修复分页组件下拉选择器菜单项悬停状态 - 亮色模式 */
.workshop-meeting:not(.dark-theme) .ant-pagination-options .ant-select-dropdown-menu-item:hover,
body:not(.has-dark-theme) .ant-pagination-options .ant-select-dropdown-menu-item:hover {
  color: rgba(0, 0, 0, 0.85) !important; /* 深色文字 */
  background-color: #f5f5f5 !important; /* 浅灰色背景 */
}

/*==================================
  5. 全局修复所有可能的选中项文字透明问题
==================================*/
/* 全局修复所有可能出现文字透明的选中项 - 亮色模式 */
.workshop-meeting:not(.dark-theme) [class*="-selected"],
.workshop-meeting:not(.dark-theme) [class*="-active"],
body:not(.has-dark-theme) [class*="-selected"],
body:not(.has-dark-theme) [class*="-active"] {
  color: rgba(0, 0, 0, 0.85) !important; /* 确保所有选中/激活状态的元素都有可见的文字颜色 */
}

/* 特别针对可能被设置为transparent的元素 */
.workshop-meeting:not(.dark-theme) [style*="color: transparent"],
body:not(.has-dark-theme) [style*="color: transparent"] {
  color: rgba(0, 0, 0, 0.85) !important; /* 强制覆盖内联样式中的透明色 */
}
