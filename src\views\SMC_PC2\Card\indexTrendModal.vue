<!--
 * @Description: 指标走势图
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-10-09 14:00:54
 * @LastEditors: yuyong<PERSON>e
 * @LastEditTime: 2021-12-21 14:57:21
-->
<template>
  <a-modal
    class="kpiCompareInModal2"
    :class="[skinStyle()]"
    v-model="visible"
    :footer="null"
    @cancel="close"
  >
    <template v-if="skinStyle().includes('hisense-style')">
      <template slot="title">同期&实际走势</template>
    </template>
    <div ref="kpiCompareInModal" style="width: 655px;height: 281px;"></div>
  </a-modal>
</template>
<script>
import * as echarts from "echarts";
import request from "@/utils/requestHttp";
import cloneDeep from "lodash/cloneDeep";
import sortBy from "lodash/sortBy";
import { dealThousandData } from "../utils";
export default {
  data() {
    return {
      visible: false,
      cardItem: null,
      kpiCompareChart: null,
      kpiCompareChartOptions: {
        title: {
          text: "同期&实际走势",
          textStyle: {
            color: "rgba(0, 0, 0, 0.85)",
            // fontWeight: "normal",
            fontSize: 16,
            height: 24,
            lineHieght: 24
          },
          padding: 16
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          }
        },
        legend: {
          data: ["同期", "实际", "目标"],
          top: 19,
          right: 60,
          itemWidth: 8,
          itemHeight: 8,
          itemStyle: {
            borderCap: "butt"
          },
          textStyle: {
            color: "#8C8C8C",
            fontSize: 12,
            lineHeight: 17,
            height: 17,
            borderRadius: 0
          },
          padding: 0,
          itemGap: 12.5
        },
        grid: {
          left: 20,
          right: 20,
          // bottom: 52,
          bottom: 20,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: [],
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          }
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            xAxisIndex: [0]
          },
          {
            type: "slider",
            show: false,
            start: 0,
            end: 100,
            yAxisIndex: [0]
          }
        ],
        yAxis: {
          type: "value",
          axisTick: {
            show: false
          },
          axisLabel: {
            formatter: "{value}"
          },
          min: function(value) {
            return value.min;
          },
          max: function(value) {
            return value.max;
          }
        },
        series: [
          {
            name: "同期",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(141, 180, 226)"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          },
          {
            name: "实际",
            type: "line",
            data: [],
            itemStyle: {
              color: "#00AAA6"
            },
            label: {
              show: true,
              position: "top"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          },
          {
            name: "目标",
            type: "line",
            data: [],
            itemStyle: {
              color: "rgb(255, 192, 0)"
            },
            symbol: "circle",
            symbolSize: 6,
            smooth: true
          }
        ]
      },
      cloneKpiCompareChartOptions: {}
    };
  },
  props: {
    timeTypeOptionInDict: Array // 事件类型码值表
  },
  inject: ["timeMap", "skinStyle"],
  methods: {
    show(item) {
      this.visible = true;
      this.$nextTick(() => {
        this.kpiCompareChart = echarts.init(this.$refs["kpiCompareInModal"]);
        this.cloneKpiCompareChartOptions = cloneDeep(
          this.kpiCompareChartOptions
        );
        if (this.skinStyle().includes("hisense-style")) {
          this.kpiCompareChartOptions.title["show"] = false;
          this.kpiCompareChartOptions.legend.right = "42%";
          this.kpiCompareChartOptions.legend.top = 13;
          delete this.kpiCompareChartOptions.yAxis.splitLine;
          if (this.skinStyle().includes("dark")) {
            this.kpiCompareChartOptions.yAxis.splitLine = {
              lineStyle: {
                color: ["#565B60"]
              }
            };
          }
        } else if (this.skinStyle().includes("classic-style")) {
          this.kpiCompareChartOptions.title["show"] = true;
          this.kpiCompareChartOptions.legend.right = 60;
          this.kpiCompareChartOptions.legend.top = 19;
          delete this.kpiCompareChartOptions.yAxis.splitLine;
        }
        this.cardItem = item;
        this.getChartData();
      });
    },
    close() {
      this.kpiCompareChartOptions = cloneDeep(this.cloneKpiCompareChartOptions);
      this.initKpiCompare();
      this.visible = false;
    },
    // 初始化kpi同期情况表
    initKpiCompare(options) {
      this.kpiCompareChart.setOption(options || this.kpiCompareChartOptions);
    },
    getChartData() {
      return new Promise(resolve => {
        const {
          signOrgId,
          businessSegmentsId,
          indexId,
          fullCode,
          indexDt,
          indexFrequencyId,
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id
        } = this.cardItem;
        const arr = [
          productAtt1Id,
          productAtt2Id,
          productAtt3Id,
          productAtt4Id,
          productAtt5Id,
          productAtt6Id,
          productAtt7Id
        ];
        arr.forEach((item, index) => {
          arr[index] = item
            ? item.includes("卡片名称") || item.includes("卡片标签")
              ? item
              : null
            : null;
        });
        request(`/api/smc2/newIndexLibrary/searchTrend`, {
          method: "POST",
          body: {
            signOrgId,
            businessSegmentsId,
            indexId,
            fullCode,
            indexDt,
            indexFrequencyId,
            org: this.cardItem.org,
            businessSegments: this.cardItem.businessSegments,
            sign: this.cardItem.sign,
            signOrg: this.cardItem.signOrg,
            indexName: this.cardItem.postEndIndexName,
            productAtt1Id: arr[0],
            productAtt2Id: arr[1],
            productAtt3Id: arr[2],
            productAtt4Id: arr[3],
            productAtt5Id: arr[4],
            productAtt6Id: arr[5],
            productAtt7Id: arr[6]
          }
        }).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            res = sortBy(res, function(item) {
              return item.indexDt;
            });
            const xAxis = res.map(item => {
              return item.indexDt;
            });
            this.kpiCompareChartOptions.xAxis.data = xAxis;
            const baseActualData = res.map(item => {
              return dealThousandData(
                item.actualValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.series[1].data = baseActualData;
            const targetValueData = res.map(item => {
              return dealThousandData(
                item.targetValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.series[2].data = targetValueData;
            const contemValueData = res.map(item => {
              return dealThousandData(
                item.contemValue,
                item.indexUnitId,
                item.precisions,
                false
              );
            });
            this.kpiCompareChartOptions.dataZoom[0].end = 100;
            // 根据数据条数 判断是否显示dataZoom组件
            // if (res.length > 6) {
            this.kpiCompareChartOptions.dataZoom[0].show = true;
            this.kpiCompareChartOptions.grid.bottom = 52;
            const yearStart = xAxis.indexOf(`${new Date().getFullYear()}-01`);
            const process =
              yearStart === -1
                ? 0
                : Math.floor((100 / (xAxis.length - 1)) * yearStart);
            this.kpiCompareChartOptions.dataZoom[0].start = process;
            // }

            this.kpiCompareChartOptions.series[0].data = contemValueData;
            this.kpiCompareChartOptions.yAxis.axisLabel.formatter = `{value} ${
              res[0].indexUnitId === "null" ? "" : res[0].indexUnitId
            }`;
            this.initKpiCompare();
          } else {
            this.kpiCompareChartOptions = cloneDeep(
              this.cloneKpiCompareChartOptions
            );
            this.initKpiCompare();
          }
          resolve(res);
        });
      });
    }
  }
};
</script>
<style lang="less">
.kpiCompareInModal2 {
  &.hisense-style {
    &.dark {
      .ant-modal-header {
        border-bottom: none;
      }
      .ant-modal-body {
        background-color: #222425;
      }
    }
    .ant-modal-close-x {
      color: #fff;
      height: 50px;
      line-height: 50px;
    }
    .ant-modal-header {
      padding: 0;
      height: 50px;
      line-height: 50px;
      text-align: center;
      .ant-modal-title {
        line-height: 50px;
        color: #fff;
      }
      background-color: #00aaa6;
    }
  }
  .ant-modal {
    width: 655px !important;
    height: 281px;
  }
  .ant-modal-body {
    padding: 4px;
  }
}
</style>
