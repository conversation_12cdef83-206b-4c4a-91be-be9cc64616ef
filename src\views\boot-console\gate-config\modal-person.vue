<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="800"
    title="人员查看"
    :footer="null"
    @cancel="close"
  >
    <div
      style="display: flex; align-items: center;justify-content: space-between;margin-bottom: 10px;"
    >
      <a-button
        @click="del"
        type="danger"
        size="small"
        style="margin-right: 5px;"
        >删除</a-button
      >
      <div>
        <a-select
          v-show="showSelect"
          show-search
          :value="receiveList"
          placeholder="接收人员"
          style="width: 300px;"
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="fetchUser"
          @change="handleChange"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option
            v-for="d in userData"
            :key="`${d.cn}-${d.employeeNumber}-${d.uid}-${d.mail}`"
          >
            {{ d.cn }}
            <template v-if="d.uid"> ({{ d.uid }}) </template>
            <template v-if="d.o">
              <div>{{ d.o }}</div>
            </template>
          </a-select-option>
        </a-select>
        <a-button
          @click="showSelect = true"
          type="primary"
          size="small"
          style="margin-left: 5px;"
          >新增</a-button
        >
        <a-button
          @click="save"
          :disabled="!isEdit"
          size="small"
          style="margin-left: 5px;"
          >保存</a-button
        >
      </div>
    </div>

    <a-table
      style="margin-bottom: 10px;"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange
      }"
      :pagination="false"
      rowKey="userName"
      size="small"
      :columns="columns"
      :data-source="personList"
    >
      <div slot="loginName" slot-scope="text, record">
        <template v-if="!record.isEdit">
          <span>{{ record.loginName }}</span>
        </template>
        <template v-else>
          <span>{{ record.loginName }}</span
          ><a-tag color="green" style="margin-left: 5px;">
            新增
          </a-tag>
        </template>
      </div>
    </a-table>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import debounce from "lodash/debounce";

export default {
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      fetching: false,
      visible: false,
      personList: [],
      originData: {},
      selectedRowKeys: [],
      userData: [],
      receiveList: [],
      showSelect: false,
      columns: [
        {
          title: "姓名",
          dataIndex: "loginName",
          key: "loginName",
          scopedSlots: { customRender: "loginName" },
          ellipsis: true,
          width: 200
        },
        {
          title: "工号",
          dataIndex: "userType",
          key: "userType",
          ellipsis: true
        },
        {
          title: "ldap",
          dataIndex: "userName",
          key: "userName",
          ellipsis: true
        },
        {
          title: "邮箱",
          dataIndex: "email",
          key: "email",
          ellipsis: true
        }
      ]
    };
  },
  computed: {
    isEdit() {
      return this.personList.filter(item => item.isEdit).length > 0;
    }
  },
  methods: {
    show(data) {
      this.visible = true;
      this.originData = data;
      // this.getPersonInfoList(10304);
      this.getPersonInfoList(data.id);
      // this.personList = (data.receive ? data.receive.split(" ") : [])
      //   .filter(item => item)
      //   .map(item => {
      //     return {
      //       name: item
      //     };
      //   });
    },
    // 查找用户
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.userData = [];
      this.fetching = true;
      request(`/api/system/user/getLdapByName?name=${value}`).then(res => {
        if (fetchId !== this.lastFetchId) {
          return;
        }
        this.userData = res;
        // this.userData = res.filter(item => item.employeeNumber);
        this.fetching = false;
      });
    },
    handleChange(value) {
      const arr = value.split("-");
      if (this.personList.filter(item => item.userName === arr[2]).length) {
        this.$message.error("请勿重复添加！");
        return;
      }
      this.personList.push({
        loginName: arr[0],
        userType: arr[2],
        email: arr[3],
        userName: arr[2],
        isEdit: true
      });
      this.fetching = false;
    },
    getPersonInfoList(id) {
      request(`/api/smc2/gate/getUserList?id=${id}`).then(res => {
        if (Array.isArray(res) && res.length) {
          this.personList = res.map(item => {
            return {
              loginName: item.loginName,
              userType: item.userType,
              email: item.email,
              userName: item.userName,
              isEdit: false
            };
          });
        }
      });
    },
    handleOk() {
      this.close();
    },
    close() {
      this.personList = [];
      this.selectedRowKeys = [];
      this.showSelect = false;
      this.visible = false;
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    save() {
      request("/api/smc2/gate/addUser", {
        method: "POST",
        body: {
          id: this.originData.id,
          receive: this.personList
            .filter(item => item.isEdit)
            .map(item => item.userName)
            .join(",")
        }
      }).then(res => {
        if (res === null) {
          this.close();
          this.$emit("fetchData");
        } else {
          this.$message(res.msg);
        }
      });
    },
    del() {
      const _that = this;
      const delOriginList = _that.personList
        .filter(
          item => _that.selectedRowKeys.includes(item.userName) && !item.isEdit
        )
        .map(item => item.userName);
      const delLocalList = _that.personList
        .filter(
          item => _that.selectedRowKeys.includes(item.userName) && item.isEdit
        )
        .map(item => item.userName);
      this.$confirm({
        title: "提示",
        content: "确定删除？",
        onOk() {
          const {
            role,
            indexName,
            businessSegments,
            triggers,
            sendType
          } = _that.originData;
          // 删除本地添加的
          console.log("delLocalList------>", delLocalList);
          if (delLocalList.length) {
            for (let i = 0; i < _that.personList.length; i++) {
              const element = _that.personList[i];
              if (delLocalList.includes(element.userName) && element.isEdit) {
                _that.personList.splice(i, 1);
                i--;
              }
            }
          }
          if (!delOriginList.length) {
            return;
          }
          // 删除远程的
          request(`/api/smc2/gate/delReceive`, {
            method: "POST",
            body: {
              role,
              indexName,
              businessSegments,
              triggers,
              sendType,
              receive: delOriginList.join(" ")
            }
          }).then(res => {
            if (res && res.result === "success") {
              _that.getPersonInfoList(_that.originData.id);
              _that.$emit("fetchData");
            } else if (res && res.result !== "success") {
              _that.$message.error(res.result);
            }
          });
        },
        onCancel() {}
      });
    }
  }
};
</script>
