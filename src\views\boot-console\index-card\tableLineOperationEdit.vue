<!--
 * @Description: 角色管理-表格行内操作栏编辑按钮
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:32:21
 * @LastEditors: yuy<PERSON><PERSON>e
 * @LastEditTime: 2021-09-22 17:40:50
-->
<template>
  <div style="display: inline-block;">
    <!-- <a-button @click="btClick">点击</a-button> -->
    <a-button :type="'link'" :size="size" @click="btClick">{{
      showAlias(data.showName, "编辑")
    }}</a-button>
    <Modal ref="modal" @fetchData="fetchData" />
  </div>
</template>
<script>
import { showAlias } from "@/utils/intl.js";
import Modal from "./modal.vue";
export default {
  components: { Modal },
  props: {
    data: Object,
    size: String,
    record: Object,
    pageName: String,
    comKey: String
  },
  data() {
    return {
      showAlias
    };
  },
  methods: {
    btClick() {
      let record = this.record;
      // let record = {"createdBy":null,"createdDate":null,"modifiedBy":null,"modifiedDate":null,"remark":null,"id":null,"signOrgId":"H0201","signOrg":"海信冰箱公司","businessSegmentsId":"SEG_82","businessSegments":"质量","indexId":"ZBY00451","indexName":"直通率","orgId":null,"org":null,"topFlag":null,"topSort":null,"deleteFlag":null,"userId":null,"fullCode":"HX-H-H02-H0201","reportType":null,"reportDeveloper":null,"reportUrl":null,"reportName":null,"reportParam":null,"treeType":null,"list":null,"type":null,"reportId":null,"cmimId":null,"wd":null,"roleId":null,"classKeysignOrg":"visiblesignOrg16843924316831636938931053","classKeybusinessSegments":"visiblebusinessSegments16843924316851636938931053","classKeyindexName":"visibleindexName16843924316871636938931053"}
      // console.log("record----->", JSON.stringify(this.record));
      // this.$refs["modal"].show(this.record);
      this.$refs["modal"].show(record);
    },
    // 请求数据表格
    fetchData() {
      this.$store.dispatch({
        type: `${this.pageName}/${this.comKey}/fetch`,
        payload: {
          pageIndex: this.$store.state[this.pageName][this.comKey].data
            .pagination.pageIndex
        }
      });
    }
  }
};
</script>
