<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: y<PERSON><PERSON><PERSON>qi.ex
 * @LastEditTime: 2025-02-06 16:41:57
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <template v-if="hasOtherFrequency">
      <!--  `本次${isEdit ? '修改' : '提交'}，将会覆盖同公司版块指标的数据信息` -->
      <!-- 本次操作，将会覆盖同公司版块指标的数据信息 -->
      <a-alert
        style="margin-bottom: 10px"
        :message="
          `本次${isEdit ? '修改' : '提交'}，将会覆盖同公司版块指标的数据信息`
        "
        type="warning"
        show-icon
      />
    </template>
    <template v-if="isEdit">
      <!-- 修改时间 -->
      <!-- {{ showAlias("f3963433-1e92-4a14-aad6-f3b9eaec6b92") }} -->
      修改时间 ：
      {{ this.mainForm.modifiedDate }}
    </template>
    <a-form-model
      ref="mainForm"
      :model="mainForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row>
        <a-col :span="12">
          <!--公司 -->
          <a-form-model-item label="公司" prop="company">
            <!-- 请选择 -->
            <a-select
              style="width: 100%"
              :disabled="isEdit ? true : false"
              v-model="mainForm.company"
              placeholder="请选择公司"
              @change="
                () => {
                  this.mainForm.businessSegmentsId = '';
                  // this.mainForm.indexId = '';
                  // this.mainForm.dmId = '';
                  this.mainForm.indexUnitId = '';
                }
              "
            >
              <a-select-option
                :value="item.value"
                v-for="item in companyList"
                :key="item.key"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <!-- 指标单位 -->
          <a-form-model-item label="指标单位" prop="indexUnitId">
            <a-select
              v-model="mainForm.indexUnitId"
              placeholder="请选择指标单位"
            >
              <a-select-option
                :value="item.id"
                v-for="item in dict['DanWei']"
                :key="item.val"
              >
                {{ item.val }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <!-- 业务模块 -->
          <a-form-model-item label="业务模块" prop="businessSegmentsId">
            <a-select
              v-model="mainForm.businessSegmentsId"
              :disabled="isEdit ? true : false"
              placeholder="请选择业务版块"
              @change="bsChange"
            >
              <a-select-option
                :value="item.value"
                v-for="item in businessSegmentsList"
                :key="item.key"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <!-- 精度（小数点位数） -->
          <a-form-model-item label="精度（小数点位数）" prop="precisions">
            <a-input-number
              v-model="mainForm.precisions"
              :min="0"
              :max="5"
              :step="1"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <!-- <a-row>
            <a-col :span="mainForm.indexId ? 20 : 24"> -->
          <!-- 指标名称 -->
          <a-form-model-item label="指标名称" prop="indexId">
            <!-- 请填写数据资产管理平台ID进行查询 -->
            <a-input
              disabled
              v-model="mainForm.indexId"
              placeholder="请选择指标名称"
            />
            <!-- <div style="display: flex;align-items: center;padding-top: 4px;">
              <a-select
                :disabled="isEdit ? true : false"
                v-model="mainForm.indexId"
                placeholder="请选择指标名称"
                @change="indexChange"
                showSearch
                :filter-option="filterOption"
              >
                <a-select-option
                  :value="item.indexId"
                  v-for="item in indexList"
                  :key="item.indexName"
                  :title="
                    `${item.indexName}(${
                      item.indexId.includes('ZBY') ? '原子指标' : '派生指标'
                    })`
                  "
                >
                  {{ item.indexName }}({{
                    item.indexId.includes("ZBY") ? "原子指标" : "派生指标"
                  }})
                </a-select-option>
              </a-select>
              <template v-if="mainForm.indexId">
                <a-tag
                  :color="mainForm.indexId ? 'cyan' : 'blue'"
                  style="margin-left: 5px;"
                >
                  {{ mainForm.indexId.includes("ZBY") ? "原子" : "派生" }}
                </a-tag>
              </template>
            </div> -->
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <!-- 指标频次 -->
          <a-form-model-item label="指标频次" prop="indexFrequencyId">
            <a-select
              v-model="mainForm.indexFrequencyId"
              placeholder="请选择指标频次"
              @change="indexFrequencyChange"
            >
              <a-select-option
                :value="item.id"
                v-for="item in dict['PinCi']"
                :key="item.val"
              >
                {{ item.val }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <!-- 数据资产管理平台ID -->
          <a-form-model-item label="数据资产管理平台ID" prop="dmId">
            <!-- 请输入ID查询 -->
            <a-input-search
              v-model="mainForm.dmId"
              @search="searchIndexIdByName"
              @change="changeDmId"
              enter-button
              placeholder="请输入ID查询"
            />
            <!-- 取消原有方式 -->
            <!-- <a-input
              disabled
              v-model="mainForm.dmId"
              placeholder="请填写数据资产管理平台ID"
            /> -->
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <!-- 是否工厂周 -->
          <a-form-model-item label="是否工厂周" prop="isFactoryWeek">
            <!-- 请选择 -->
            <a-select v-model="mainForm.isFactoryWeek" placeholder="请选择">
              <a-select-option
                :value="item.key"
                v-for="item in dict['smc-Y/N'] || []"
                :key="item.key"
              >
                {{ item.value }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <!-- 指标类型 -->
          <a-form-model-item label="指标类型" prop="indexTypeId">
            <!-- 请选择 -->
            <a-select v-model="mainForm.indexTypeId" placeholder="请选择">
              <a-select-option
                :value="item.id"
                v-for="item in dict['LeiXing']"
                :key="item.val"
              >
                {{ item.val }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <template v-for="(i, idx) in Object.keys(attrForm)">
          <a-col :span="8" :key="i">
            <!-- `产品属性${idx + 1}` -->
            <a-form-model-item :label="`产品属性${idx + 1}`">
              <!-- :placeholder="`请选择产品属性${idx + 1}`" -->
              <a-select
                v-model="attrForm[`productAtt${idx + 1}Dimtp`]"
                placeholder="请选择产品属性"
                @change="attrFormSelectChange"
                :disabled="attrFormDisabled[`productAtt${idx + 1}Dimtp`]"
                allowClear
              >
                <a-select-option
                  :disabled="item.disabled"
                  :value="item.id"
                  v-for="item in attrFormSelect[`productAtt${idx + 1}Dimtp`]"
                  :key="item.val"
                >
                  {{ item.val }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </template>
      </a-row>
    </a-form-model>
    <a-divider dashed />
    <div style="display: flex;">
      <div style="width: 80px;line-height: 30px;">
        数据来源
        <!-- {{ showAlias("7bee2064-ff74-466d-bb86-4aea30a386e6") }} -->
        ：
      </div>
      <!--  请填写 -->
      <a-textarea
        v-model="dataSource"
        placeholder="请填写"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </div>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
import cloneDeep from "lodash/cloneDeep";
import { showAlias } from "@/utils/intl.js";

// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  data() {
    return {
      showAlias,
      hasOtherFrequency: false, // 新增时，检查公司-版块-指标有无其他频次
      visible: false, // 打开关闭弹窗
      labelCol: { span: 8 },
      wrapperCol: { span: 10 },
      mainForm: {
        businessSegmentsId: "",
        indexId: "",
        dmId: "",
        company: "",
        companyId: "",
        indexUnitId: "",
        modifiedDate: "",
        indexFrequencyId: "",
        precisions: 2,
        indexTypeId: "",
        isFactoryWeek: "N",
        fullCode: "",
      },
      attrForm: {
        productAtt1Dimtp: "",
        productAtt2Dimtp: "",
        productAtt3Dimtp: "",
        productAtt4Dimtp: "",
        productAtt5Dimtp: "",
        productAtt6Dimtp: "",
        productAtt7Dimtp: "",
        productAtt8Dimtp: "",
        productAtt9Dimtp: "",
      },
      // 产品属性下拉框map，为满足前下拉框选中后，后续下拉框中禁用掉已选择产品属性的需求
      attrFormSelect: {
        productAtt1Dimtp: [],
        productAtt2Dimtp: [],
        productAtt3Dimtp: [],
        productAtt4Dimtp: [],
        productAtt5Dimtp: [],
        productAtt6Dimtp: [],
        productAtt7Dimtp: [],
        productAtt8Dimtp: [],
        productAtt9Dimtp: [],
      },
      // 编辑时产品属性列表下拉框禁用map
      attrFormDisabled: {
        productAtt1Dimtp: false,
        productAtt2Dimtp: false,
        productAtt3Dimtp: false,
        productAtt4Dimtp: false,
        productAtt5Dimtp: false,
        productAtt6Dimtp: false,
        productAtt7Dimtp: false,
        productAtt8Dimtp: false,
        productAtt9Dimtp: false,
      },
      indexList: [],
      dataSource: "",
      rules: {
        businessSegmentsId: [
          {
            required: true,
            message: "请选择业务版块", // "请选择业务版块",
            trigger: "change",
          },
        ],
        indexId: [
          {
            required: true,
            message: "请选择指标名称", // "请选择指标名称",
            trigger: "change",
          },
        ],
        company: [
          {
            required: true,
            message: "请选择公司", // "请选择公司",
            trigger: "change",
          },
        ],
        indexUnitId: [
          {
            required: true,
            message: "请选择指标单位", // "请选择指标单位",
            trigger: "change",
          },
        ],
        indexFrequencyId: [
          {
            required: true,
            message: "请选择是否冻结", // "请选择是否冻结",
            trigger: "change",
          },
        ],
        indexTypeId: [
          {
            required: true,
            message: "请选择指标类型", // "请选择指标类型",
            trigger: "change",
          },
        ],
        precisions: [
          {
            required: true,
            message: "请输入小数点精度", // "请输入小数点精度",
            trigger: "blur",
          },
        ],
        dmId: [
          {
            required: true,
            message: "请输入资产管理平台ID", // "请输入资产管理平台ID",
            trigger: "blur",
          },
        ],
      },
      isEdit: false, // 是否编辑状态
      dict: {}, // 字典
      companyList: [],
      treeType: "", // 组织树
    };
  },
  computed: {
    // 版块列表
    businessSegmentsList() {
      return this.mainForm.company
        ? this.companyList
          ? this.companyList.filter(
              (item) => item.value === this.mainForm.company
            )[0]?.children
          : []
        : [];
    },
    // mainForm中productAtt1-7Dimtp值
    productAttDimtpArr() {
      const arr = [];
      for (let i = 0; i < 7; i++) {
        arr.push(this.attrForm[`productAtt${i}DimtpArr`]);
      }
      return arr;
    },
  },
  methods: {
    // 下拉框输入搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 维度选择下拉框改变
    attrFormSelectChange() {
      for (const key in this.attrFormSelect) {
        if (Object.hasOwnProperty.call(this.attrFormSelect, key)) {
          const arr = cloneDeep(this.attrFormSelect[key]);
          arr.forEach((it) => {
            it.disabled = Object.values(this.attrForm).includes(it.id);
          });
          this.$set(this.attrFormSelect, key, arr);
        }
      }
    },
    async show(formValue) {
      this.getDICT();
      this.getPinCi();
      this.getDanWei();
      await this.getWeiDu();
      this.getLeiXing();
      await this.getCompanyList();
      this.getOrgViewList();
      this.visible = true;
      this.isEdit = false;
      if (formValue) {
        this.isEdit = true;
        this.hasOtherFrequency = true;
        this.fillForm(formValue);
      }
    },
    // 表单赋值
    fillForm(formValue, isEdit = true) {
      const {
        id,
        businessSegmentsId,
        indexId,
        dmId,
        companyId,
        indexUnitId,
        modifiedDate,
        indexFrequencyId,
        precisions,
        productAtt1Dimtp,
        productAtt2Dimtp,
        productAtt3Dimtp,
        productAtt4Dimtp,
        productAtt5Dimtp,
        productAtt6Dimtp,
        productAtt7Dimtp,
        productAtt8Dimtp,
        productAtt9Dimtp,
        indexTypeId,
        isFactoryWeek,
        fullCode,
        treeType,
        dataSource,
      } = formValue;
      this.dataSource = dataSource;
      this.attrForm = {
        productAtt1Dimtp,
        productAtt2Dimtp,
        productAtt3Dimtp,
        productAtt4Dimtp,
        productAtt5Dimtp,
        productAtt6Dimtp,
        productAtt7Dimtp,
        productAtt8Dimtp,
        productAtt9Dimtp,
      };
      if (isEdit) {
        this.mainForm = {
          id,
          businessSegmentsId,
          indexId,
          dmId,
          companyId,
          indexUnitId,
          modifiedDate,
          indexFrequencyId,
          precisions,
          indexTypeId,
          isFactoryWeek,
          fullCode,
          treeType,
          company: fullCode,
        };
        // 编辑时禁用掉已经维护的下拉框
        for (const key in this.attrForm) {
          if (Object.hasOwnProperty.call(this.attrForm, key)) {
            const element = this.attrForm[key];
            if (element) {
              this.attrFormDisabled[key] = true;
            }
          }
        }
      } else {
        this.mainForm = {
          ...this.mainForm,
          indexUnitId,
          modifiedDate,
          precisions,
          indexTypeId,
          treeType,
        };
      }
      this.attrFormSelectChange();
      this.bsChange(businessSegmentsId, false);
    },
    // 业务版块改变
    bsChange(value, resetFields = true) {
      if (resetFields) {
        // this.mainForm.indexId = "";
        // this.mainForm.dmId = "";
        this.mainForm.indexUnit = "";
      }
      // 取消原有指标名称列表查询，改为数据资产管理平台ID输入查询带出
      // value &&
      //   this.getIndexList(
      //     this.businessSegmentsList.filter(item => item.value === value)[0].key
      //   );
    },
    changeDmId(value) {
      console.log("---changeDmId-", value);
      // 改变时清空指标名称
      this.mainForm.indexId = null;
    },
    // 根据数据资产管理平台ID查询指标名称
    searchIndexIdByName(value) {
      console.log(value);
      this.mainForm.dmId = value;
      this.mainForm.indexId = null;
      if (value) {
        this.getIndexListBydmId(value);
      }
    },
    // 获取指标列表--根据数据资产管理平台ID查询
    getIndexListBydmId(dmId) {
      request(
        `/api/smc2/indexPerfect/searchtIndexRegister?indexName=${dmId}&pageSize=100000&pageNum=1`
      ).then((res) => {
        console.log(res);
        if (res && res.rows) {
          let item = res.rows[0];
          this.mainForm.indexId = item?.indexName;
        }
      });
    },
    // 指标改变
    indexChange(value) {
      this.mainForm.dmId = value;
      this.mainForm.indexId = value;
      this.$nextTick(() => {
        this.$refs.mainForm.validateField(["indexId"]);
      });
      if (
        this.mainForm.company &&
        this.mainForm.businessSegmentsId &&
        this.mainForm.indexId
      ) {
        this.getOtherIndexInfo();
      }
    },
    // 查询是否有其他频次数据
    getOtherIndexInfo() {
      const { company, businessSegmentsId, indexId } = this.mainForm;
      request(
        `/api/smc2/indexPerfect/searchIndexPerfect?signOrgId=${
          company.split("-")[company.split("-").length - 1]
        }&businessSegmentsId=${businessSegmentsId}&indexId=${indexId}`
      ).then((res) => {
        if (res && res.rows.length) {
          this.hasOtherFrequency = true;
          this.fillForm(res.rows[0], false);
        }
      });
    },
    // 获取公司列表
    getCompanyList() {
      return new Promise((resolve) => {
        request(`/api/smc2/codeValue/getRelation`, { method: "POST" }).then(
          (res) => {
            if (res && Object.keys(res).length) {
              const keys = Object.keys(res);
              const list = keys.map((item) => {
                return {
                  key: item.split("~")[0],
                  value: item.split("~")[1],
                  children: res[item].split(",").map((subitem) => {
                    return {
                      key: subitem.split("-")[0],
                      value: subitem.split("-")[1],
                    };
                  }),
                };
              });
              this.companyList = list;
            }
            resolve();
          }
        );
      });
    },
    // 获取指标频次
    getPinCi() {
      request(
        `/api/smc2/codeValue/searchCodeValue?type=3&pageSize=1000&pageNum=1`
      ).then((res) => {
        this.$set(this.dict, "PinCi", res.rows || []);
      });
    },
    // 指标频次修改
    indexFrequencyChange(value) {
      if (value === "W") {
        this.mainForm.isFactoryWeek = "Y";
      } else {
        this.mainForm.isFactoryWeek = "N";
      }
    },
    // 获取指标单位
    getDanWei() {
      request(
        `/api/smc2/codeValue/searchCodeValue?type=2&pageSize=1000&pageNum=1`
      ).then((res) => {
        this.$set(this.dict, "DanWei", res.rows || []);
      });
    },
    // 获取维度
    getWeiDu() {
      return new Promise((resolve) => {
        request(
          `/api/smc2/codeValue/searchCodeValue?type=4&pageSize=1000&pageNum=1`
        ).then((res) => {
          this.$set(this.dict, "WeiDu", res.rows || []);
          for (const key in this.attrFormSelect) {
            if (Object.hasOwnProperty.call(this.attrFormSelect, key)) {
              this.attrFormSelect[key] = (res.rows || []).map((it) => {
                return {
                  id: it.id,
                  val: it.val,
                  disabled: false,
                };
              });
            }
          }
          resolve();
        });
      });
    },
    // 获取指标类型
    getLeiXing() {
      request(
        `/api/smc2/codeValue/searchCodeValue?type=5&pageSize=1000&pageNum=1`
      ).then((res) => {
        this.$set(this.dict, "LeiXing", res.rows || []);
      });
    },
    // 获取字典
    getDICT() {
      request(
        decodeURIComponent(
          "/api/system/dict/type/query?types=smc-Y%2FN&languageCode=zh_CN"
        )
      ).then((res) => {
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const element = res[key];
            this.$set(this.dict, key, element);
          }
        }
      });
    },
    // 获取指标列表
    getIndexList(businessSsegments) {
      request(
        `/api/smc2/indexPerfect/searchtIndexRegister?indexName=&businessSsegments=${businessSsegments}&pageSize=100000&pageNum=1`
      ).then((res) => {
        console.log(res);
        if (res && res.rows) {
          this.indexList = res.rows;
        }
      });
    },
    // 获取视图列表
    getOrgViewList() {
      request(`/api/smc2/treeOrg/searchOrgTree`, {
        method: "POST",
      }).then((res) => {
        // this.OrgTreeList = res || [];
        this.treeType = res ? res[0].id : "";
      });
    },
    close() {
      this.mainForm = {
        businessSegmentsId: "",
        indexId: "",
        dmId: "",
        company: "",
        companyId: "",
        indexUnitId: "",
        modifiedDate: "",
        indexFrequencyId: "",
        precisions: 2,
        indexTypeId: "",
        isFactoryWeek: "N",
        fullCode: "",
      };
      this.attrForm = {
        productAtt1Dimtp: "",
        productAtt2Dimtp: "",
        productAtt3Dimtp: "",
        productAtt4Dimtp: "",
        productAtt5Dimtp: "",
        productAtt6Dimtp: "",
        productAtt7Dimtp: "",
        productAtt8Dimtp: "",
        productAtt9Dimtp: "",
      };
      this.hasOtherFrequency = false;
      this.companyList = [];
      this.indexList = [];
      this.$refs.mainForm.resetFields();
      this.visible = false;
    },
    handleOk() {
      this.submit();
    },
    // 提交
    submit() {
      const attrForm = cloneDeep(this.attrForm);
      // 页面中存在间隔维护产品属性的情况，处理并将后面产品属性提前
      for (let i = 0; i < 6; i++) {
        attrForm[`productAtt${i + 1}Dimtp`] =
          Object.values({
            ...this.attrForm,
          }).filter((it) => it)[i] || "";
      }
      this.$refs.mainForm.validate(async (valid) => {
        const form = {
          ...this.mainForm,
          ...attrForm,
          dataSource: this.dataSource ? JSON.stringify(this.dataSource) : "",
        };
        form.companyId = form.company.split("/")[
          form.company.split("/").length - 1
        ];
        form.fullCode = form.company;
        delete form.company;
        delete form.modifiedDate;
        if (!this.isEdit) {
          form.treeType = this.treeType;
        }
        // 特殊处理---指标名称改为数据资产管理平台ID
        form.indexId = form.dmId;

        if (valid) {
          request(
            `/api/smc2/indexPerfect/${
              this.isEdit ? "updateIndexPerfect" : "insertIndexPerfect"
            }`,
            {
              method: "POST",
              body: form,
            }
          ).then((res) => {
            if (res.result === "success") {
              this.close();
              this.$emit("fetchData");
            } else {
              this.$message.error(res.result);
            }
          });
        }
      });
    },
  },
};
</script>
