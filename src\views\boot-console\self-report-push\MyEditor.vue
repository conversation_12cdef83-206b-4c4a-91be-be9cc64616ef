<template>
  <div style="border: 1px solid #ccc;">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editor"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 500px; overflow-y: hidden;"
      v-model="html"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
      @onChange="onChange"
    />
  </div>
</template>
<script>
import Vue from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { httpHeaders, genMinioUrl } from "@/utils/utils.js";
// import { DomEditor } from "@wangeditor/editor";
import Axios from "axios";
const headers = {
  ...httpHeaders()
  // ...{
  //   Authorization: "Bearer b9460952-0392-46aa-9c9c-60f80c53c8e1",
  //   systemName: "smc",
  //   token: "b9460952-0392-46aa-9c9c-60f80c53c8e1"
  // }
};

export default Vue.extend({
  components: { Editor, Toolbar },
  props: {
    value: String
  },
  data() {
    return {
      editor: null,
      html: "",
      toolbarConfig: {
        excludeKeys: ["group-video"]
      },
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            fieldName: "file",
            maxFileSize: 10 * 1024 * 1024,
            maxNumberOfFiles: 5,
            allowedFileTypes: ["image/*"],
            timeout: 5 * 1000,
            async customUpload(file, insertFn) {
              // file 即选中的文件
              // 自己实现上传，并得到图片 url alt href
              var form = new FormData();
              form.append("file", file);
              Axios.post("/api/system/oss/upload/mom", form, {
                headers
              }).then(res => {
                // 最后插入图片
                console.log("res----->", res);
                insertFn(
                  genMinioUrl(res.data.data || ""),
                  "",
                  res.data.data || ""
                );
              });
            }
          }
        }
      },
      mode: "default" // or 'simple'
    };
  },
  watch: {
    value: {
      handler(val) {
        this.html = val || "";
      },
      immediate: true
    }
  },
  methods: {
    onChange(editor) {
      console.log("editor----->", this.editor);
      const html = editor.getHtml();
      this.$emit("change", html === "<p><br></p>" ? "" : html);
    },
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    }
  },
  mounted() {},
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  }
});
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
