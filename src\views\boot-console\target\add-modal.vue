<!--
 * @Description: 弹窗
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-16 13:38:26
 * @LastEditors: y<PERSON>hengqi.ex
 * @LastEditTime: 2025-02-27 10:04:06
-->
<template>
  <a-modal
    v-model="visible"
    :destroyOnClose="true"
    :width="1100"
    :title="isEdit ? $t('EDIT') : $t('ADD')"
    @ok="handleOk"
    @cancel="close"
  >
    <!-- 月度 -->
    <template v-if="dataItem.indexFrequencyId === 'M'">
      <a-table
        :columns="monthColumns"
        :data-source="monthData"
        bordered
        :pagination="false"
        :scroll="{ x: 1000 }"
      >
        <span slot="year" slot-scope="text, record">
          <span>{{ record["year"] }}</span></span
        >
        <!-- 一月 -->
        <template slot="Jan-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Jan-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Jan-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Jan-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Jan-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Jan-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 二月 -->
        <template slot="Feb-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Feb-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Feb-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Feb-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Feb-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Feb-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 三月 -->
        <template slot="Mar-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Mar-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Mar-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Mar-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Mar-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Mar-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 四月 -->
        <template slot="Apr-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Apr-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Apr-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Apr-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Apr-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Apr-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 五月 -->
        <template slot="May-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['May-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="May-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['May-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="May-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['May-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 六月 -->
        <template slot="Jun-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Jun-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Jun-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Jun-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Jun-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Jun-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 七月 -->
        <template slot="Jul-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Jul-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Jul-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Jul-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Jul-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Jul-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 八月 -->
        <template slot="Aug-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Aug-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Aug-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Aug-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Aug-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Aug-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 九月 -->
        <template slot="Sept-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Sept-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Sept-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Sept-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Sept-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Sept-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 十月 -->
        <template slot="Oct-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Oct-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Oct-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Oct-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Oct-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Oct-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 十一月 -->
        <template slot="Nov-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Nov-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Nov-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Nov-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Nov-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Nov-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 十二月 -->
        <template slot="Dec-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['Dec-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="Dec-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['Dec-denominator']"
            style="width: 100%;background-color: #E8FFFB;"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="Dec-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['Dec-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <template slot="title">
          <h3>{{ indexName }} {{ `(单位：${indexUnit})` }}</h3>
          <!-- 场景应用：<a-select
            style="margin-right: 20px;width: 120px"
            @change="selectChange"
            v-model="valueDataType"
          >
            <a-select-option value="实际">
              实际
            </a-select-option>
            <a-select-option value="还原">
              还原
            </a-select-option>
          </a-select> -->
          是否累计：<a-select style="width: 120px" v-model="flag">
            <a-select-option value="Y">
              累计
            </a-select-option>
            <a-select-option value="N">
              非累计
            </a-select-option>
          </a-select>
        </template>
      </a-table>
    </template>
    <!-- 季度 -->
    <template v-if="dataItem.indexFrequencyId === 'Q'">
      <a-table
        :columns="quarterColumns"
        :data-source="quarterData"
        bordered
        :pagination="false"
        :scroll="{ x: 1000 }"
      >
        <span slot="year" slot-scope="text, record">
          <span>{{ record["year"] }}</span></span
        >
        <!-- 一季度 -->
        <template slot="firstQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstQuarter-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="firstQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstQuarter-denominator']"
            style="width: 100%"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="firstQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['firstQuarter-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 二季度 -->
        <template slot="secondQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondQuarter-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="secondQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondQuarter-denominator']"
            style="width: 100%"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="secondQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['secondQuarter-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 三季度 -->
        <template slot="threeQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['threeQuarter-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="threeQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['threeQuarter-denominator']"
            style="width: 100%"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="threeQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['threeQuarter-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 四季度 -->
        <template slot="fourQuarter-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['fourQuarter-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="fourQuarter-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['fourQuarter-denominator']"
            style="width: 100%"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="fourQuarter-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['fourQuarter-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <template slot="title">
          <h3>{{ indexName }} {{ `(单位：${indexUnit})` }}</h3>
          场景应用：<a-select
            style="margin-right: 20px;width: 120px"
            v-model="valueDataType"
          >
            <a-select-option value="实际">
              实际
            </a-select-option>
            <a-select-option value="还原">
              还原
            </a-select-option>
          </a-select>
          是否累计：<a-select style="width: 120px" v-model="flag">
            <a-select-option value="Y">
              累计
            </a-select-option>
            <a-select-option value="N">
              非累计
            </a-select-option>
          </a-select>
        </template>
      </a-table>
    </template>
    <!-- 年度 -->
    <template v-if="dataItem.indexFrequencyId === 'HF'">
      <a-table
        :columns="yearColumns"
        :data-source="yearData"
        bordered
        :pagination="false"
        :scroll="{ x: 1000 }"
      >
        <!-- 上半年 -->
        <template slot="firstHalfYear-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstHalfYear-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="firstHalfYear-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['firstHalfYear-denominator']"
            style="width: 100%"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="firstHalfYear-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['firstHalfYear-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <!-- 下半年 -->
        <template slot="secondHalfYear-numerator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondHalfYear-numerator']"
            style="width: 100%"
            :disabled="targetDisabled"
          />
        </template>
        <template slot="secondHalfYear-denominator" slot-scope="text, record">
          <a-input-number
            v-model="record['secondHalfYear-denominator']"
            style="width: 100%"
            :disabled="targetCIMDisabled"
          />
        </template>
        <template slot="secondHalfYear-actualValue" slot-scope="text, record">
          <a-input-number
            v-model="record['secondHalfYear-actualValue']"
            style="width: 100%"
            :disabled="targetCBGDisabled"
          />
        </template>
        <template slot="title">
          <h3>{{ indexName }} {{ `(单位：${indexUnit})` }}</h3>
          场景应用：<a-select
            style="margin-right: 20px;width: 120px"
            v-model="valueDataType"
          >
            <a-select-option value="实际">
              实际
            </a-select-option>
            <a-select-option value="还原">
              还原
            </a-select-option>
          </a-select>
          是否累计：<a-select style="width: 120px" v-model="flag">
            <a-select-option value="Y">
              累计
            </a-select-option>
            <a-select-option value="N">
              非累计
            </a-select-option>
          </a-select>
        </template>
      </a-table>
    </template>
  </a-modal>
</template>
<script>
import request from "@/utils/requestHttp";
// import { adminUserUrlPrefix } from "@/utils/utils";
export default {
  props: {
    targetDisabled: {
      type: Boolean,
      default: false,
    },
    targetCIMDisabled: {
      type: Boolean,
      default: false,
    },
    targetCBGDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false, // 打开关闭弹窗
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      isEdit: false, // 是否编辑状态
      indexName: "某某某指标",
      valueDataType: "CBG目标",
      flag: "N",
      indexUnit: "%",
      wd: "",
      monthColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left",
        },
        {
          title: "一月",
          dataIndex: "Jan",
          key: "Jan",
          num: "01",
          scopedSlots: { customRender: "Jan" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Jan-numerator",
              key: "Jan-numerator",
              scopedSlots: { customRender: "Jan-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Jan-denominator",
              key: "Jan-denominator",
              scopedSlots: { customRender: "Jan-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Jan-actualValue",
            //   key: "Jan-actualValue",
            //   scopedSlots: { customRender: "Jan-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "二月",
          dataIndex: "Feb",
          key: "Feb",
          num: "02",
          scopedSlots: { customRender: "Feb" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Feb-numerator",
              key: "Feb-numerator",
              scopedSlots: { customRender: "Feb-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Feb-denominator",
              key: "Feb-denominator",
              scopedSlots: { customRender: "Feb-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Feb-actualValue",
            //   key: "Feb-actualValue",
            //   scopedSlots: { customRender: "Feb-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "三月",
          key: "Mar",
          dataIndex: "Mar",
          num: "03",
          scopedSlots: { customRender: "Mar" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Mar-numerator",
              key: "Mar-numerator",
              scopedSlots: { customRender: "Mar-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Mar-denominator",
              key: "Mar-denominator",
              scopedSlots: { customRender: "Mar-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Mar-actualValue",
            //   key: "Mar-actualValue",
            //   scopedSlots: { customRender: "Mar-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "四月",
          key: "Apr",
          dataIndex: "Apr",
          num: "04",
          scopedSlots: { customRender: "Apr" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Apr-numerator",
              key: "Apr-numerator",
              scopedSlots: { customRender: "Apr-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Apr-denominator",
              scopedSlots: { customRender: "Apr-denominator" },
              key: "Apr-denominator",
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Apr-actualValue",
            //   scopedSlots: { customRender: "Apr-actualValue" },
            //   key: "Apr-actualValue",
            //   width: 120,
            // },
          ],
        },
        {
          title: "五月",
          key: "May",
          dataIndex: "May",
          num: "05",
          scopedSlots: { customRender: "May" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "May-numerator",
              key: "May-numerator",
              scopedSlots: { customRender: "May-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "May-denominator",
              key: "May-denominator",
              scopedSlots: { customRender: "May-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "May-actualValue",
            //   key: "May-actualValue",
            //   scopedSlots: { customRender: "May-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "六月",
          key: "Jun",
          dataIndex: "Jun",
          num: "06",
          scopedSlots: { customRender: "Jun" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Jun-numerator",
              key: "Jun-numerator",
              scopedSlots: { customRender: "Jun-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Jun-denominator",
              key: "Jun-denominator",
              scopedSlots: { customRender: "Jun-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Jun-actualValue",
            //   key: "Jun-actualValue",
            //   scopedSlots: { customRender: "Jun-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "七月",
          key: "Jul",
          dataIndex: "Jul",
          num: "07",
          scopedSlots: { customRender: "Jul" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Jul-numerator",
              key: "Jul-numerator",
              scopedSlots: { customRender: "Jul-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Jul-denominator",
              key: "Jul-denominator",
              scopedSlots: { customRender: "Jul-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Jul-actualValue",
            //   key: "Jul-actualValue",
            //   scopedSlots: { customRender: "Jul-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "八月",
          key: "Aug",
          dataIndex: "Aug",
          num: "08",
          scopedSlots: { customRender: "Aug" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Aug-numerator",
              key: "Aug-numerator",
              scopedSlots: { customRender: "Aug-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Aug-denominator",
              key: "Aug-denominator",
              scopedSlots: { customRender: "Aug-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Aug-actualValue",
            //   key: "Aug-actualValue",
            //   scopedSlots: { customRender: "Aug-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "九月",
          key: "Sept",
          dataIndex: "Sept",
          num: "09",
          scopedSlots: { customRender: "Sept" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Sept-numerator",
              key: "Sept-numerator",
              scopedSlots: { customRender: "Sept-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Sept-denominator",
              key: "Sept-denominator",
              scopedSlots: { customRender: "Sept-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Sept-actualValue",
            //   key: "Sept-actualValue",
            //   scopedSlots: { customRender: "Sept-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "十月",
          key: "Oct",
          dataIndex: "Oct",
          scopedSlots: { customRender: "Oct" },
          num: "10",
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Oct-numerator",
              key: "Oct-numerator",
              scopedSlots: { customRender: "Oct-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Oct-denominator",
              key: "Oct-denominator",
              scopedSlots: { customRender: "Oct-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Oct-actualValue",
            //   key: "Oct-actualValue",
            //   scopedSlots: { customRender: "Oct-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "十一月",
          key: "Nov",
          dataIndex: "Nov",
          num: "11",
          scopedSlots: { customRender: "Nov" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Nov-numerator",
              key: "Nov-numerator",
              scopedSlots: { customRender: "Nov-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Nov-denominator",
              key: "Nov-denominator",
              scopedSlots: { customRender: "Nov-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Nov-actualValue",
            //   key: "Nov-actualValue",
            //   scopedSlots: { customRender: "Nov-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "十二月",
          key: "Dec",
          dataIndex: "Dec",
          num: "12",
          scopedSlots: { customRender: "Dec" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "Dec-numerator",
              key: "Dec-numerator",
              scopedSlots: { customRender: "Dec-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "Dec-denominator",
              key: "Dec-denominator",
              scopedSlots: { customRender: "Dec-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "Dec-actualValue",
            //   key: "Dec-actualValue",
            //   scopedSlots: { customRender: "Dec-actualValue" },
            //   width: 120,
            // },
          ],
        },
      ], // 表格列
      monthData: [],
      monthSubmitData: [],
      quarterColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left",
        },
        {
          title: "第一季度",
          dataIndex: "firstQuarter",
          key: "firstQuarter",
          num: "01",
          scopedSlots: { customRender: "firstQuarter" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "firstQuarter-numerator",
              key: "firstQuarter-numerator",
              scopedSlots: { customRender: "firstQuarter-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "firstQuarter-denominator",
              key: "firstQuarter-denominator",
              scopedSlots: { customRender: "firstQuarter-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "firstQuarter-actualValue",
            //   key: "firstQuarter-actualValue",
            //   scopedSlots: { customRender: "firstQuarter-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "第二季度",
          dataIndex: "secondQuarter",
          key: "secondQuarter",
          num: "02",
          scopedSlots: { customRender: "secondQuarter" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "secondQuarter-numerator",
              key: "secondQuarter-numerator",
              scopedSlots: { customRender: "secondQuarter-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "secondQuarter-denominator",
              key: "secondQuarter-denominator",
              scopedSlots: { customRender: "secondQuarter-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "secondQuarter-actualValue",
            //   key: "secondQuarter-actualValue",
            //   scopedSlots: { customRender: "secondQuarter-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "第三季度",
          key: "threeQuarter",
          dataIndex: "threeQuarter",
          num: "03",
          scopedSlots: { customRender: "threeQuarter" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "threeQuarter-numerator",
              key: "threeQuarter-numerator",
              scopedSlots: { customRender: "threeQuarter-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "threeQuarter-denominator",
              key: "threeQuarter-denominator",
              scopedSlots: { customRender: "threeQuarter-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "threeQuarter-actualValue",
            //   key: "threeQuarter-actualValue",
            //   scopedSlots: { customRender: "threeQuarter-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "第四季度",
          key: "fourQuarter",
          dataIndex: "fourQuarter",
          num: "04",
          scopedSlots: { customRender: "fourQuarter" },
          width: 300,
          children: [
            {
              title: "外报目标值",
              dataIndex: "fourQuarter-numerator",
              key: "fourQuarter-numerator",
              scopedSlots: { customRender: "fourQuarter-numerator" },
              width: 120,
            },
            {
              title: "内控目标值",
              dataIndex: "fourQuarter-denominator",
              key: "fourQuarter-denominator",
              scopedSlots: { customRender: "fourQuarter-denominator" },
              width: 120,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "fourQuarter-actualValue",
            //   key: "fourQuarter-actualValue",
            //   scopedSlots: { customRender: "fourQuarter-actualValue" },
            //   width: 120,
            // },
          ],
        },
      ], // 表格列
      quarterData: [],
      quarterSubmitData: [],
      yearColumns: [
        {
          title: "年",
          dataIndex: "year",
          key: "year",
          scopedSlots: { customRender: "year" },
          width: 100,
          fixed: "left",
        },
        {
          title: "上半年",
          dataIndex: "firstHalfYear",
          key: "firstHalfYear",
          num: "01",
          scopedSlots: { customRender: "firstHalfYear" },
          width: 450,
          children: [
            {
              title: "外报目标值",
              dataIndex: "firstHalfYear-numerator",
              key: "firstHalfYear-numerator",
              scopedSlots: { customRender: "firstHalfYear-numerator" },
              width: 150,
            },
            {
              title: "内控目标值",
              dataIndex: "firstHalfYear-denominator",
              key: "firstHalfYear-denominator",
              scopedSlots: { customRender: "firstHalfYear-denominator" },
              width: 150,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "firstHalfYear-actualValue",
            //   key: "firstHalfYear-actualValue",
            //   scopedSlots: { customRender: "firstHalfYear-actualValue" },
            //   width: 120,
            // },
          ],
        },
        {
          title: "下半年",
          dataIndex: "secondHalfYear",
          key: "secondHalfYear",
          num: "02",
          scopedSlots: { customRender: "secondHalfYear" },
          width: 450,
          children: [
            {
              title: "外报目标值",
              dataIndex: "secondHalfYear-numerator",
              key: "secondHalfYear-numerator",
              scopedSlots: { customRender: "secondHalfYear-numerator" },
              width: 150,
            },
            {
              title: "内控目标值",
              dataIndex: "secondHalfYear-denominator",
              key: "secondHalfYear-denominator",
              scopedSlots: { customRender: "secondHalfYear-denominator" },
              width: 150,
            },
            // {
            //   title: "CBG目标值",
            //   dataIndex: "secondHalfYear-actualValue",
            //   key: "secondHalfYear-actualValue",
            //   scopedSlots: { customRender: "secondHalfYear-actualValue" },
            //   width: 150,
            // },
          ],
        },
      ], // 表格列
      yearData: [],
      yearSubmitData: [],
      dataItem: {},
      recordData: null,
      //targetDisabled:false,
      //targetCIMDisabled:false,
      //targetCBGDisabled:false
    };
  },
  methods: {
    show(record) {
      this.visible = true;
      this.isEdit = true;
      this.recordData = record;
      this.indexName = record.indexName;
      this.indexUnit = record.indexUnit;
      this.dataItem = record;
      this.generateDataItem();
      this.setTableHeaderTitle(this.valueDataType);
      // this.$nextTick(() => {
      //   this.getIndexTarget(cmimId);
      // });
    },
    generateSubmitData() {
      // this.monthSubmitData =
      const year = new Date().getFullYear();
      ["month", "quarter", "year"].forEach((timeItem) => {
        this[`${timeItem}Columns`].forEach((columnsItem) => {
          if (columnsItem.key !== "year") {
            [year - 2, year - 1, year].forEach((yearItem) => {
              let dataItem = {
                cmimId: this.dataItem.cmimId,
                // indexFrequencyId: this.dataItem.indexFrequencyId,
                dataType: "",
                actualValue: "",
                numerator: "",
                denominator: "",
                d: "",
                w: "",
                m: "",
                q: "",
                hf: "",
                businessSegmentsId: this.dataItem.businessSegmentsId,
                companyId: this.dataItem.signOrgId,
                orgId: this.dataItem.orgId,
                indexId: this.dataItem.indexId,
                frequency: this.dataItem.indexFrequencyId,
                target: "",
                unit: this.dataItem.indexUnitId,
                managerLdap: this.dataItem.fillInPerson,
                leaderOwner: this.dataItem.fillInPersonLeader,
                dimension: this.dataItem.dimension,
                isDelete: this.dataItem.isDelete,
              };
              if (timeItem === "month") {
                dataItem["m"] = `${yearItem}-${columnsItem.num}`;
              } else if (timeItem === "quarter") {
                dataItem["q"] = `${yearItem}-${columnsItem.num}`;
              } else if (timeItem === "year") {
                dataItem["hf"] = `${yearItem}-${columnsItem.num}`;
              }
              this[`${timeItem}SubmitData`].push(dataItem);
            });
          }
        });
      });
    },
    // 生成数据每一项
    generateDataItem() {
      const year = new Date().getFullYear();
      const common = {};
      const quarterCommon = {};
      const yearCommon = {};
      ["numerator", "denominator", "actualValue"].forEach((item) => {
        [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sept",
          "Oct",
          "Nov",
          "Dec",
        ].forEach((yearItem) => {
          common[`${yearItem}-${item}`] = "";
        });
        [
          "firstQuarter",
          "secondQuarter",
          "threeQuarter",
          "fourQuarter",
        ].forEach((quarterItem) => {
          quarterCommon[`${quarterItem}-${item}`] = "";
        });
        ["firstHalfYear", "secondHalfYear"].forEach((yearItem) => {
          yearCommon[`${yearItem}-${item}`] = "";
        });
      });
      this.monthData.push({
        year: year - 1,
        ...common,
      });
      this.monthData.push({
        year: year,
        ...common,
      });
      this.monthData.push({
        year: year + 1,
        ...common,
      });
      this.quarterData.push({
        year: year - 1,
        ...quarterCommon,
      });
      this.quarterData.push({
        year: year,
        ...quarterCommon,
      });
      this.quarterData.push({
        year: year + 1,
        ...quarterCommon,
      });
      this.yearData.push({
        year: year - 1,
        ...yearCommon,
      });
      this.yearData.push({
        year: year,
        ...yearCommon,
      });
      this.yearData.push({
        year: year + 1,
        ...yearCommon,
      });
      this.generateSubmitData();
    },
    selectChange(value) {
      this.setTableHeaderTitle(value);
    },
    // 按照dataType动态修改表格表头title
    setTableHeaderTitle(dataType) {
      ["monthColumns", "quarterColumns", "yearColumns"].forEach((item) => {
        this[item].forEach((columnsItem) => {
          // eslint-disable-next-line no-prototype-builtins
          if (columnsItem.hasOwnProperty("children")) {
            columnsItem.children[2]["title"] = `${dataType}值`;
          }
        });
      });
    },
    updateData(year, month, value, sign, itemSign) {
      this[`${sign}Data`].forEach((element) => {
        if (year === String(element.year)) {
          const enKey = this[`${sign}Columns`].filter((item) => {
            return item.num === month.padStart(2, "0");
          })[0].dataIndex;
          element[`${enKey}-${itemSign}`] = value;
        }
      });
    },
    close() {
      this.valueDataType = "实际";
      this.monthData = [];
      this.quarterData = [];
      this.yearData = [];
      this.indexName = "";
      this.indexUnit = "";
      this.dataItem = {};
      this.monthSubmitData = [];
      this.quarterSubmitData = [];
      this.yearSubmitData = [];
      this.visible = false;
    },
    handleOk() {
      this.saveCard();
    },
    // 保存卡片信息
    saveCard() {
      const typeArr = [];
      if (this.dataItem.indexFrequencyId === "M") {
        typeArr.push("month");
      } else if (this.dataItem.indexFrequencyId === "Q") {
        typeArr.push("quarter");
      } else if (this.dataItem.indexFrequencyId === "HF") {
        typeArr.push("year");
      }
      console.log("typeArr----->", typeArr);
      typeArr.forEach((timeType) => {
        this[`${timeType}Data`].forEach((yearElement) => {
          ["numerator", "denominator", "actualValue"].forEach((typeItem) => {
            for (const key in yearElement) {
              if (
                Object.hasOwnProperty.call(yearElement, key) &&
                key.includes(typeItem)
              ) {
                const element = yearElement[key];
                const numKey = this[`${timeType}Columns`].filter(
                  (item) => item.dataIndex === key.split("-")[0]
                )[0]?.num;
                if (numKey) {
                  this[`${timeType}SubmitData`].forEach((submitElement) => {
                    if (
                      timeType === "month" &&
                      submitElement.m === `${yearElement["year"]}-${numKey}`
                    ) {
                      submitElement[typeItem] =
                        element === undefined ||
                        element === null ||
                        element === ""
                          ? ""
                          : String(element);
                    }
                    if (
                      timeType === "quarter" &&
                      submitElement.q === `${yearElement["year"]}-${numKey}`
                    ) {
                      submitElement[typeItem] =
                        element === undefined ||
                        element === null ||
                        element === ""
                          ? ""
                          : String(element);
                    }
                    if (
                      timeType === "year" &&
                      submitElement.hf === `${yearElement["year"]}-${numKey}`
                    ) {
                      submitElement[typeItem] =
                        element === undefined ||
                        element === null ||
                        element === ""
                          ? ""
                          : String(element);
                    }
                  });
                }
              }
            }
          });
        });
      });
      const finalData = [];
      typeArr
        .map((item) => this[`${item}SubmitData`])
        .forEach((item) => {
          finalData.push(...item);
        });
      console.log("finalData---->", finalData);
      let dimensionList = [
        this.recordData.productAtt1Dimtp,
        this.recordData.productAtt2Dimtp,
        this.recordData.productAtt3Dimtp,
        this.recordData.productAtt4Dimtp,
        this.recordData.productAtt5Dimtp,
        this.recordData.productAtt6Dimtp,
        this.recordData.productAtt7Dimtp,
      ];
      let dimensionData = dimensionList.filter((item) => item).toString();
      request("/api/smc2/newTarget/insert", {
        method: "POST",
        body: finalData
          .filter(
            (item) => item.numerator || item.denominator || item.actualValue
          )
          .map((item) => {
            return {
              ...item,
              dataType: this.valueDataType,
              target: item.numerator,
              targetCIM: item.denominator,
              // targetCBG:item.actualValue,
              indexDate: item.m,
              flag: this.flag,
              dimension: dimensionData,
            };
          }),
      }).then(() => {
        // if (res && res.result === "success") {
        this.close();
        this.$emit("fetchData", this.flag);
        // } else {
        //   this.$message.error(res.msg);
        // }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.dataStyle {
  background: black;
}
</style>
