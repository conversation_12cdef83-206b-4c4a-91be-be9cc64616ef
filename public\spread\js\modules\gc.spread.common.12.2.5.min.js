/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="",c(c.s="./node_modules_local/@grapecity/js-sheets-common/index.js")}({"./node_modules_local/@grapecity/js-sheets-common/dist/gc.spread.common.js":function(a,b){var c="object"==typeof c?c:{};c.Spread=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/all.entry.ts")}({"./src/all.entry.ts":function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/common.entry.ts"),b.Common=d,e=c("./src/plugins/commands/commands.entry.ts"),b.Commands=e,f=c("./src/plugins/formatter/formatter.entry.ts"),b.Formatter=f,g=c("./src/plugins/sparkline/sparkline.entry.ts"),b.Sparklines=g,h=c("./src/plugins/slicer/slicer.entry.ts"),b.Slicers=h},"./src/common/common.entry.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;function l(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/colorhelper.ts"),b.pc=d.pc,e=c("./src/common/util/types.ts"),b.j=e.j,f=c("./src/common/util/arrayhelper.ts"),b.k=f.k,g=c("./src/common/util/datetimehelper.ts"),b.l=g.l,h=c("./src/common/util/numberhelper.ts"),b.o=h.o,i=c("./src/common/util/regexhelper.ts"),b.q=i.q,j=c("./src/common/util/stringhelper.ts"),b.u=j.u,l(c("./src/common/util/common.ts")),k=c("./src/common/culture/cultureInfo.ts"),b.CultureInfo=k.CultureInfo,b.CultureManager=k.CultureManager,l(c("./src/common/util/functionhelper.ts"))},"./src/common/culture/cultureInfo.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/types.ts"),e=c("./src/common/util/common.ts"),f=c("./src/common/util/arrayhelper.ts"),g=window,h=void 0,i=null,j=d.j.H,k=d.j.Ia,l=["MM/dd/yyyy","MM/d/yyyy","M/dd/yyyy","M/d/yyyy","yy/MM/dd","yy/MM/d","yy/M/dd","yy/M/d","yyyy/MM/dd","yyyy/MM/d","yyyy/M/dd","yyyy/M/d"],m=["hh:mm:ss","hh:mm:s","hh:m:ss","hh:m:s","h:mm:ss","h:mm:s","h:m:ss","h:m:s","hh:mm:ss tt","hh:mm:s tt","hh:m:ss tt","hh:m:s tt","h:mm:ss tt","h:mm:s tt","h:m:ss tt","h:m:s tt","hh:mm","hh:m","h:mm","h:m","hh:mm tt","hh:m tt","h:mm tt","h:m tt"],n=["MM-dd-yyyy","MM-d-yyyy","M-dd-yyyy","M-d-yyyy","yy-MM-dd","yy-MM-d","yy-M-dd","yy-M-d","yyyy-MM-dd","yyyy-MM-d","yyyy-M-dd","yyyy-M-d","dd-MMMM-yy","dd-MMM-yy"],o=l.concat(m),j(l,function(a,b){j(m,function(a,c){a<m.length-4&&o.push(b+" "+c)})}),o=o.concat(n),j(n,function(a,b){j(m,function(a,c){a<m.length-4&&o.push(b+" "+c)})});function A(a){return a.split("\xa0").join(" ").toUpperCase()}function B(a){var b,c,d=[];for(b=0,c=a.length;b<c;b++)d[b]=A(a[b]);return d}function C(a,b,c){var d=A(a),e=b.indexOf(d);return e===-1&&(e=c.indexOf(d)),e}p=function(){function a(){this.NumberFormat={currencyDecimalDigits:2,currencyDecimalSeparator:".",currencyGroupSeparator:",",currencyGroupSizes:[3],currencyNegativePattern:0,currencyPositivePattern:0,currencySymbol:"\xa4",digitSubstitution:1,isReadOnly:!0,numberGroupSizes:[3],nanSymbol:"NaN",nativeDigits:["0","1","2","3","4","5","6","7","8","9"],numberNegativePattern:1,negativeInfinitySymbol:"-Infinity",negativeSign:"-",numberDecimalDigits:2,numberDecimalSeparator:".",numberGroupSeparator:",",positiveInfinitySymbol:"Infinity",positiveSign:"+",percentDecimalDigits:2,percentDecimalSeparator:".",percentGroupSeparator:",",percentGroupSizes:[3],percentNegativePattern:0,percentPositivePattern:0,percentSymbol:"%",perMilleSymbol:"\u2030",listSeparator:",",arrayListSeparator:",",arrayGroupSeparator:";",dbNumber:{}},this.DateTimeFormat={abbreviatedDayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],abbreviatedMonthGenitiveNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],abbreviatedMonthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""],amDesignator:"AM",calendarIsReadOnly:!0,calendarWeekRule:0,Calendar:{MinSupportedDateTime:"@-62135568000000@",MaxSupportedDateTime:"@253402300799999@",AlgorithmType:1,CalendarType:1,Eras:[1],TwoDigitYearMax:2029,isReadOnly:!0},dateSeparator:"/",dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],defaultDatePattern:"MM/dd/yyyy H:mm:ss",firstDayOfWeek:0,fullDateTimePattern:"dddd, dd MMMM yyyy HH:mm:ss",longDatePattern:"dddd, dd MMMM yyyy",longTimePattern:"HH:mm:ss",monthDayPattern:"MMMM dd",monthGenitiveNames:["January","February","March","April","May","June","July","August","September","October","November","December",""],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December",""],nativeCalendarName:"Gregorian Calendar",pmDesignator:"PM",rfc1123Pattern:"ddd, dd MMM yyyy HH':'mm':'ss 'GMT'",shortDatePattern:"MM/dd/yyyy",shortestDayNames:["Su","Mo","Tu","We","Th","Fr","Sa"],shortTimePattern:"HH:mm",sortableDateTimePattern:"yyyy'-'MM'-'dd'T'HH':'mm':'ss",timeSeparator:":",universalSortableDateTimePattern:"yyyy'-'MM'-'dd HH':'mm':'ss'Z'",yearMonthPattern:"yyyy MMMM",filterDialogDateFormatter:"yyyy/mmmm/dd",eraFormatter:["gee/mm/dd"]},this.name=function(){return""},this.id=-1}return a.prototype.ma=function(){var a,b,c,d;return this.na||(a=this.DateTimeFormat,b=a.shortDatePattern.replace(/m/g,"M"),c=[b,b+" h:mm",b+" h:mm:ss",b+" h:mm:ss.0","MMMdd","MMMd","MMM dd","MMM d"],c=c.concat(o),1041===this.id&&(d=a.eraFormatter,c=c.concat(this._Ab),c=c.concat(d)),this.na=c),this.na},a.prototype.oa=function(a){var b=this;return b.pa||(b.pa=B(b.DateTimeFormat.monthNames),b.qa=B(b.DateTimeFormat.monthGenitiveNames)),C(a,b.pa,b.qa)},a.prototype.ra=function(a){var b=this;return b.sa||(b.sa=B(b.DateTimeFormat.abbreviatedMonthNames),b.ta=B(b.DateTimeFormat.abbreviatedMonthGenitiveNames)),C(a,b.sa,b.ta)},a.prototype.ua=function(a){var b=this;return b.va||(b.va=B(b.DateTimeFormat.dayNames)),b.va.indexOf(A(a))},a.prototype.wa=function(a){var b=this;return b.xa||(b.xa=B(b.DateTimeFormat.abbreviatedDayNames)),b.xa.indexOf(A(a))},a}(),b.CultureInfo=p,q=function(){function a(){}return a.I=function(b){return b>=a.J()&&b<=a.K()},a.L=function(){var a,b,c=p.eras;if(c!==h){for(a=[],b=0;b<c.length;b++)a[b]=new Date(c[b].startDate.replace(/-/g,"/"));return a}return this.O},a.P=function(b){var c,d,e,f,g;switch(b){case"g":c="symbol",d=a.R;break;case"gg":c="abbreviation",d=a.S;break;case"ggg":c="name",d=a.U;break;default:return[]}if(e=p.eras,f=[],e!==h){for(g=0;g<e.length;g++)f[g]=e[g][c];return f}return d},a.K=function(){var a,b=p.eras;return b!==h&&b.length>0?(a=new Date(b[b.length-1].startDate.replace(/-/g,"/")),a.setFullYear(a.getFullYear()+99),a):this.W},a.J=function(){var a=p.eras;return a!==h&&a.length>0?new Date(a[0].startDate.replace(/-/g,"/")):this.Z},a.$=function(){var a=p.eras;return a!==h?a.length:this._},a.aa=function(){var a,b,c,d,e=p.eras;if(e!==h){for(a=[],b=1;b<e.length;b++)c=new Date(e[b-1].startDate.replace(/-/g,"/")),d=new Date(e[b].startDate.replace(/-/g,"/")),a[b-1]=d.getFullYear()-c.getFullYear()+1;return a[b-1]=99,a}return this.ba},a.ca=function(b){var c,d,e={da:-1,ea:-1};if(!a.I(b))return e;for(c=0;c<a.$();c++)if(d=c+1!==a.$()?a.L()[c+1]:a.fa(a.K(),1),b<d){e.da=c,e.ea=b.getFullYear()-a.L()[c].getFullYear()+1;break}return e},a.fa=function(a,b){var c=new Date(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds());return c.setMilliseconds(c.getMilliseconds()+b),new Date(c.valueOf())},a.ga=function(b,c){var d=a.L()[b].getFullYear();return d+c-1},a.ha=function(b,c){var d,e;for(c=c.toUpperCase(),d=a.P(b),e=0;e<d.length;e++)if(d[e]===c)return e;return-1},a.ia=function(b,c){var d,e,f,g=a,h=g.ca(c),i=h.da;if(i>=0&&(d=g.P(b),d.length>0))return d[i];if(e=h.ea,e>=0){if(f=""+e,"1"===f)return f="\u5143";if("ee"===b)return 1===f.length&&(f="0"+f),f;if("e"===b)return f}return""},a.O=[new Date(1868,8,8),new Date(1912,6,30),new Date(1926,11,25),new Date(1989,0,8),new Date(2019,4,1)],a._=5,a.ba=[45,15,64,31,99],a.W=new Date(2117,11,31,23,59,59),a.Z=new Date(1868,8,8),a.ja=["1,m","2,t","3,s","4,h","5,r"],a.ka=[0,1,2,3,0,1,2,3],a.U=["\u660e\u6cbb","\u5927\u6b63","\u662d\u548c","\u5e73\u6210","\u4ee4\u548c"],a.R=["M","T","S","H","R"],a.S=["\u660e","\u5927","\u662d","\u5e73","\u4ee4"],a.la=99,a}(),b.aBb=q,r=function(a){z(b,a);function b(){var b=a.call(this)||this;return D.call(b,"NumberFormat",["currencySymbol","isReadOnly"],["$",!1]),D.call(b,"DateTimeFormat",["fullDateTimePattern","longDatePattern","longTimePattern","shortDatePattern","shortTimePattern","yearMonthPattern","calendarIsReadOnly"],["dddd, MMMM dd, yyyy h:mm:ss tt","dddd, MMMM dd, yyyy","h:mm:ss tt","M/d/yyyy","h:mm tt","MMMM, yyyy",!1]),b.DateTimeFormat.defaultDatePattern=b.DateTimeFormat.shortDatePattern+" H:mm:ss",b.name=function(){return"en-US"},b.id=1033,b}return b}(p),s=function(a){z(b,a);function b(){var b,c,d=a.call(this)||this,e={1:{letters:["\u5146","\u5343","\u767e","\u5341","\u4ebf","\u5343","\u767e","\u5341","\u4e07","\u5343","\u767e","\u5341",""],numbers:["\u3007","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d","\u4e03","\u516b","\u4e5d"]},2:{letters:["\u5146","\u9621","\u767e","\u62fe","\u5104","\u9621","\u767e","\u62fe","\u842c","\u9621","\u767e","\u62fe",""],numbers:["\u3007","\u58f1","\u5f10","\u53c2","\u56db","\u4f0d","\u516d","\u4e03","\u516b","\u4e5d"]},3:{letters:i,numbers:["\uff10","\uff11","\uff12","\uff13","\uff14","\uff15","\uff16","\uff17","\uff18","\uff19"]}};return D.call(d,"NumberFormat",["currencyDecimalDigits","currencyNegativePattern","currencySymbol","isReadOnly","nanSymbol","negativeInfinitySymbol","percentNegativePattern","percentPositivePattern","positiveInfinitySymbol","dbNumber"],[0,1,"\xa5",!1,"NaN (\u975e\u6570\u5024)","-\u221e",1,1,"+\u221e",e]),b=["1","2","3","4","5","6","7","8","9","10","11","12",""],c=["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708",""],D.call(d,"DateTimeFormat",["abbreviatedDayNames","abbreviatedMonthGenitiveNames","abbreviatedMonthNames","amDesignator","calendarIsReadOnly","dayNames","fullDateTimePattern","longDatePattern","longTimePattern","monthDayPattern","monthGenitiveNames","monthNames","nativeCalendarName","pmDesignator","shortDatePattern","shortestDayNames","shortTimePattern","yearMonthPattern","filterDialogDateFormatter","EraFilterDialogDateFormatter"],[["\u65e5","\u6708","\u706b","\u6c34","\u6728","\u91d1","\u571f"],b,b,"\u5348\u524d",!1,["\u65e5\u66dc\u65e5","\u6708\u66dc\u65e5","\u706b\u66dc\u65e5","\u6c34\u66dc\u65e5","\u6728\u66dc\u65e5","\u91d1\u66dc\u65e5","\u571f\u66dc\u65e5"],"yyyy'\u5e74'M'\u6708'd'\u65e5' H:mm:ss","yyyy'\u5e74'M'\u6708'd'\u65e5'","H:mm:ss","M'\u6708'd'\u65e5'",c,c,"\u897f\u66a6 (\u65e5\u672c\u8a9e)","\u5348\u5f8c","yyyy/MM/dd",["\u65e5","\u6708","\u706b","\u6c34","\u6728","\u91d1","\u571f"],"H:mm","yyyy'\u5e74'M'\u6708'","yyyy\u5e74/mmmm/d\u65e5","ggge/mmmm/d\u65e5"]),d.DateTimeFormat.eras=!0,d.DateTimeFormat.defaultDatePattern=d.DateTimeFormat.shortDatePattern+" "+d.DateTimeFormat.longTimePattern,d._Ab=['gggee"\u5e74"MM"\u6708"dd"\u65e5','gggee"\u5e74"M"\u6708"d"\u65e5','ge"\u5e74"M"\u6708"d"\u65e5','ggge"\u5e74"M"\u6708"d"\u65e5','ggee"\u5e74"M"\u6708"d"\u65e5','gge"\u5e74"M"\u6708"d"\u65e5'],d.name=function(){return"ja-JP"},d.id=1041,d.isJCKCulture=!0,d}return b}(p),t=function(a){z(b,a);function b(){var b,c,d=a.call(this)||this,e={1:{letters:["\u5146","\u5343","\u767e","\u5341","\u4ebf","\u5343","\u767e","\u5341","\u4e07","\u5343","\u767e","\u5341",""],numbers:["\u25cb","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d","\u4e03","\u516b","\u4e5d"]},2:{letters:["\u5146","\u4edf","\u4f70","\u62fe","\u4ebf","\u4edf","\u4f70","\u62fe","\u4e07","\u4edf","\u4f70","\u62fe",""],numbers:["\u96f6","\u58f9","\u8d30","\u53c1","\u8086","\u4f0d","\u9646","\u67d2","\u634c","\u7396"]},3:{letters:i,numbers:["\uff10","\uff11","\uff12","\uff13","\uff14","\uff15","\uff16","\uff17","\uff18","\uff19"]}};return D.call(d,"NumberFormat",["currencyNegativePattern","currencySymbol","isReadOnly","nanSymbol","negativeInfinitySymbol","percentNegativePattern","percentPositivePattern","positiveInfinitySymbol","dbNumber"],[2,"\xa5",!1,"\u975e\u6570\u5b57","\u8d1f\u65e0\u7a77\u5927",1,1,"\u6b63\u65e0\u7a77\u5927",e]),b=["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708",""],c=["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708",""],D.call(d,"DateTimeFormat",["abbreviatedDayNames","abbreviatedMonthGenitiveNames","abbreviatedMonthNames","amDesignator","calendarIsReadOnly","dayNames","firstDayOfWeek","fullDateTimePattern","longDatePattern","longTimePattern","monthDayPattern","monthGenitiveNames","monthNames","nativeCalendarName","pmDesignator","shortDatePattern","shortestDayNames","shortTimePattern","yearMonthPattern","filterDialogDateFormatter"],[["\u5468\u65e5","\u5468\u4e00","\u5468\u4e8c","\u5468\u4e09","\u5468\u56db","\u5468\u4e94","\u5468\u516d"],b,b,"\u4e0a\u5348",!1,["\u661f\u671f\u65e5","\u661f\u671f\u4e00","\u661f\u671f\u4e8c","\u661f\u671f\u4e09","\u661f\u671f\u56db","\u661f\u671f\u4e94","\u661f\u671f\u516d"],1,"yyyy'\u5e74'M'\u6708'd'\u65e5' H:mm:ss","yyyy'\u5e74'M'\u6708'd'\u65e5'","H:mm:ss","M'\u6708'd'\u65e5'",c,c,"\u516c\u5386","\u4e0b\u5348","yyyy/M/d",["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],"H:mm","yyyy'\u5e74'M'\u6708'","yyyy\u5e74/mmmm/d\u65e5"]),d.DateTimeFormat.defaultDatePattern=d.DateTimeFormat.shortDatePattern+" "+d.DateTimeFormat.longTimePattern,d.name=function(){return"zh-cn"},d.id=2052,d.isJCKCulture=!0,d}return b}(p),u=function(a){z(b,a);function b(){var b,c,d=a.call(this)||this,e={1:{letters:["\u5146","\u5343","\u767e","\u5341","\u5104","\u5343","\u767e","\u5341","\u4e07","\u5343","\u767e","\u5341",""],numbers:["\uff10","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\uf9d1","\u4e03","\u516b","\u4e5d"]},2:{letters:["\u5146","\u9621","\u767e","\uf973","\u5104","\u9621","\u767e","\uf973","\u842c","\u9621","\u767e","\uf973",""],numbers:["\uf9b2","\u58f9","\u8cb3","\uf96b","\u56db","\u4f0d","\uf9d1","\u4e03","\u516b","\u4e5d"]},3:{letters:["\u5146","\u5343","\u767e","\u5341","\u5104","\u5343","\u767e","\u5341","\u4e07","\u5343","\u767e","\u5341",""],numbers:["\uff10","\uff11","\uff12","\uff13","\uff14","\uff15","\uff16","\uff17","\uff18","\uff19"]}};return D.call(d,"NumberFormat",["currencyDecimalDigits","currencyNegativePattern","currencySymbol","isReadOnly","dbNumber"],[0,1,"\u20a9",!1,e]),b=["1","2","3","4","5","6","7","8","9","10","11","12",""],c=["1\uc6d4","2\uc6d4","3\uc6d4","4\uc6d4","5\uc6d4","6\uc6d4","7\uc6d4","8\uc6d4","9\uc6d4","10\uc6d4","11\uc6d4","12\uc6d4",""],D.call(d,"DateTimeFormat",["abbreviatedDayNames","abbreviatedMonthGenitiveNames","abbreviatedMonthNames","amDesignator","calendarIsReadOnly","dayNames","firstDayOfWeek","fullDateTimePattern","longDatePattern","longTimePattern","monthDayPattern","monthGenitiveNames","monthNames","nativeCalendarName","pmDesignator","shortDatePattern","shortestDayNames","shortTimePattern","yearMonthPattern","filterDialogDateFormatter"],[["\uc77c","\uc6d4","\ud654","\uc218","\ubaa9","\uae08","\ud1a0"],b,b,"\uc624\uc804",!1,["\uc77c\uc694\uc77c","\uc6d4\uc694\uc77c","\ud654\uc694\uc77c","\uc218\uc694\uc77c","\ubaa9\uc694\uc77c","\uae08\uc694\uc77c","\ud1a0\uc694\uc77c"],1,"yyyy'\ub144' M'\uc6d4' d'\uc77c' dddd h:mm:ss","yyyy'\ub144' M'\uc6d4' d'\uc77c' dddd","h:mm:ss","M'\uc6d4' d'\uc77c'",c,c,"\uc11c\uae30","\uc624\ud6c4","yyyy-MM-dd",["\uc77c","\uc6d4","\ud654","\uc218","\ubaa9","\uae08","\ud1a0"],"tt h:mm","yyyy'\ub144' M'\uc6d4'","yyyy\ub144/mmmm/d\uc77c"]),d.DateTimeFormat.defaultDatePattern=d.DateTimeFormat.shortDatePattern+" "+d.DateTimeFormat.longTimePattern,d.name=function(){return"ko-kr"},d.id=1042,d.isJCKCulture=!0,d}return b}(p),v={invariant:new p,"en-us":new r,"ja-jp":new s,"zh-cn":new t,"ko-kr":new u};function D(a,b,c){var d,e,f,g=this;for(e=0,f=b.length;e<f;e++)d=b[e],g[a][d]=c[e]}function E(a){var b;"function"!=typeof CustomEvent?(b=document.createEvent("CustomEvent"),b.initCustomEvent("cultureChanged",!1,!1,void 0)):b=new CustomEvent("cultureChanged",{}),b.cultureInfo=a,g.gcCultureInfo=a,document.dispatchEvent(b)}w={},x=["lsru","lsde","ls1","ls2","ls3","ls4","ls5","ls6"],y=function(){function a(){this.bBb=1}return a.instance=function(){return this.cBb||(this.cBb=new this)},a.prototype.culture=function(b){return 0===arguments.length?a.dBb:void(b&&a.dBb!==b&&(a.dBb=b.toLowerCase(),this.bBb++,E(a.dBb)))},a.prototype.addCultureInfo=function(a,b,c){var d,f,g,h,i;if(b&&b instanceof p){if(d=b.NumberFormat&&b.NumberFormat.numberDecimalSeparator,f=b.NumberFormat.arrayListSeparator,g=b.NumberFormat.arrayGroupSeparator,d===b.NumberFormat.listSeparator||g===f)throw h=new e.ResourceManager(e.SR),h.getResource().Exp_Separator;v[a.toLowerCase()]=b}c&&"object"==typeof c&&(i=a.toLowerCase(),w[i]=k(!0,{},c),this.bBb++)},a.prototype.getCultureInfo=function(b){var c,d,e;if(0===arguments.length)return v[a.dBb];"string"==typeof b&&(b=b.toLowerCase()),c=v,d=i;for(e in c)if(e===b||c[e].id!==h&&c[e].id===b){d=c[e];break}return d},a.prototype.getLanguage=function(a){if("string"==typeof a){var b=a.toLowerCase();return w[b]}return i},a.prototype.eBb=function(a,b){var c,d,e=w[a.toLowerCase()];for(c in b)f.k.Bb(x,c)||(d=this.fBb(e,c),null!==d&&"string"==typeof d&&"string"==typeof b[c]&&(b[c]=d));b._h&&e.Functions&&this.gBb(e.Functions,b._h),b.B2&&e.TableFunctions&&k(!0,b.B2,e.TableFunctions)},a.prototype.fBb=function(a,b){var c,d;if(a.hasOwnProperty(b))return a[b];for(c in a)if(d=a[c],"object"==typeof d&&d.hasOwnProperty(b))return d[b];return null},a.prototype.gBb=function(a,b){var c,d,e,f,g,h,i,j={};for(c in b)if(a.hasOwnProperty(c)){d=b[c],e=a[c],f=d.parameters,g=e.parameters,d.description=e.description;for(h in f)g.hasOwnProperty(h)&&(i=g[h],"string"==typeof i&&(f[h].name=i))}return j},a.prototype.q4=function(b){var c=this.getCultureInfo(b);return c||(c=v[a.dBb],c||(c=new p)),c},a.prototype.LZa=function(a,b){var c=this.getCultureInfo(a)||this.getCultureInfo("en-US"),d=c.DateTimeFormat,e=d.filterDialogDateFormatter;return(b.indexOf("g")>=0||b.indexOf("e")>=0)&&(e=d.EraFilterDialogDateFormatter||e),e},a.dBb="en-us",a}(),b.CultureManager=y.instance()},"./src/common/util/arrayhelper.ts":function(a,b,c){"use strict";var d,e,f;Object.defineProperty(b,"__esModule",{value:!0}),d=null,e=void 0;function g(a,b){var c,d;for(c in b)b.hasOwnProperty(c)&&(d=b[c],Array.isArray(d)?(a[c]=[],g(a[c],b[c])):"object"==typeof d?(a[c]={},g(a[c],b[c])):a[c]=b[c])}f=function(){function a(){}return a.Eb=function(a,b,c){a.splice(b,0,c)},a.Sb=function(a,b){a.push(b)},a.Bb=function(a,b){return a.indexOf(b)>-1},a.Fb=function(a,b){var c=a.indexOf(b);c>-1&&a.splice(c,1)},a.Zb=function(a,b){return a.slice(0,b).concat(a.slice(b+1))},a.Cb=function(a,b,c){return a.indexOf(b,c)},a.$b=function(a,b,c){if(!(b<0))for(var e=0;e<c&&b+e<a.length;e++)a[b+e]=d},a._b=function(a,b){var c,f;for(b<0&&(b=-1),c=b+1,f=c;f<a.length;f++)if(a[f]!==e&&a[f]!==d)return f;return-1},a.ac=function(a){return a&&a.length},a.Uo=function(a){var b=[];return g(b,a),b},a}(),b.k=f},"./src/common/util/colorhelper.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/numberhelper.ts"),e=d.o.pb,f=Math.min,g=Math.max,h=Math.abs,i=parseInt;function l(a,b,c){return c<0&&(c+=240),c>240&&(c-=240),c<40?a+((b-a)*c+20)/40:c<120?b:c<160?a+((b-a)*(160-c)+20)/40:a}function m(a,b,c){var d,e,f,g,h;return 0===c?d=e=f=i(255*b/240+"",10):(g=void 0,h=void 0,h=b<=120?i((b*(240+c)+120)/240+""):b+c-i((b*c+120)/240+""),g=2*b-h,d=n(g,h,a+80),e=n(g,h,a),f=n(g,h,a-80)),{a:255,r:d,g:e,b:f}}function n(a,b,c){return f(i((255*l(a,b,c)+120)/240+""),255)}j=function(){function a(a){var b,c,d,e,h=this,j=a.r,k=a.g,l=a.b,m=g(g(j,k),l),n=f(f(j,k),l),o=m+n;h.kc=i((240*o+255)/510+"",10),b=m-n,0===b?(h.mc=0,h.lc=160):(h.kc<=120?h.mc=i((240*b+o/2)/o+"",10):h.mc=i((240*b+(510-o)/2)/(510-o)+"",10),c=(40*(m-j)+b/2)/b,d=(40*(m-k)+b/2)/b,e=(40*(m-l)+b/2)/b,j===m?h.lc=i(e-d+"",10):k===m?h.lc=i(80+c-e+"",10):h.lc=i(160+d-c+"",10),h.lc<0&&(h.lc+=240),h.lc>240&&(h.lc-=240))}return a.prototype.nc=function(a){var b=this,c=b.kc,d=b.oc(b.kc,500,!0);return m(b.lc,c+(d-c)*a,b.mc)},a.prototype.oc=function(a,b,c){return 0===b?a:c?b>0?(a*(1e3-b)+241*b)/1e3:a*(b+1e3)/1e3:(a+=240*b/1e3,a<0&&(a=0),a>240&&(a=240),a)},a}(),k=function(){function a(){}return a.bc=function(a){var b=a.a,c=a.r,d=a.g,f=a.b;return 3===arguments.length&&(b=255,c=arguments[0],d=arguments[1],f=arguments[2]),4===arguments.length&&(b=arguments[0],c=arguments[1],d=arguments[2],f=arguments[3]),255===b?"#"+e(c,!0,2)+e(d,!0,2)+e(f,!0,2):"rgba("+c+","+d+","+f+","+b+")"},a.Pka=function(a,b){return a.a===b.a&&a.r===b.r&&a.g===b.g&&a.b===b.b},a.dc=function(a){return(299*a.r+587*a.g+114*a.b)/1e3},a.hc=function(a,b){var c,d=255-h(a.r-b.r),e=255-h(a.g-b.g),f=255-h(a.b-b.b),g=h(a.a-b.a);return d/=255,e/=255,f/=255,c=(d+e+f)/3,!!(c>=.9&&g<=.05)},a.ic=function(b){var c=255^b.r,d=255^b.g,e=255^b.b;return a.bc(b.a,c,d,e)},a.hBb=function(b){var c,d,e=a.Yn;return e||(c=document.createElement("canvas"),c&&c.getContext&&(e=a.Yn=c.getContext("2d"))),e?(e.clearRect(1,1,1,1),e.fillStyle=b,e.fillRect(1,1,1,1),d=e.getImageData(1,1,1,1),d?d.data:null):b},a.ec=function(b){var c,d,e,f,g;return b instanceof a?b:(c=0,d=0,e=0,f=0,b&&(g=a.hBb(b),g&&(d=g[0],e=g[1],f=g[2],c=g[3])),{a:c,r:d,g:e,b:f})},a.jc=function(b,c){var d,e;return 0===c?b:(d=new j(b),e=i((c>0?(240-d.kc)*c:d.kc*c)+"",10),a.gc(d.lc,d.kc+e,d.mc))},a.nc=function(b,c){var d=a.ec(b),e=new j(d),f=e.nc(c);return a.bc(f)},a.fc=l,a.gc=m,a}(),b.pc=k},"./src/common/util/common.ts":function(a,b,c){"use strict";var d,e,f,g,h;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/culture/cultureInfo.ts"),e=c("./src/common/util/util.res.en.ts"),f=c("./src/common/util/types.ts"),b.SR={en:e},g=f.j.Ia,h=function(){function a(a){this.res=a,this.cachedResource=this.iBb(a,!0),this.bBb=0}return a.prototype.getResource=function(){var a=this.iBb(this.res,!this.cachedResource);return a&&(this.cachedResource=a),this.cachedResource},a.prototype.iBb=function(a,b){var c,e,f,h=d.CultureManager.culture();if(a&&h){if(c=d.CultureManager.bBb,this.bBb===c&&!b)return;return this.bBb=c,d.CultureManager.getLanguage(h)?(e=g(!0,{},a.en),d.CultureManager.eBb(h,e),e):(f=h.substr(0,2).toLowerCase(),a.hasOwnProperty(f)?a[f]:a.en)}return{}},a}(),b.ResourceManager=h;function i(a,b,c,d){var e,f,g,h,i;if(a&&0<=c&&c<b){for(e=[],f=void 0,g=void 0,g=c;g<b;g++)void 0!==a[g]&&e.push(g);for(f=e.length,g=0;g<f;g++)h=e[f-g-1],i=a[h],a[h]=null,a[Math.floor(h)+d]=i}}b.A=i;function j(a,b,c,d){var e,f,g,h,i,j;if(a&&0<=c&&c<b){for(e=[],f=void 0,g=c+d,h=void 0,h=c;h<b;h++)void 0!==a[h]&&(c<=h&&h<g?a[h]=null:h>=g&&e.push(h));for(f=e.length,h=0;h<f;h++)i=e[h],j=a[i],a[i]=null,a[Math.floor(i)-d]=j}}b.B=j;function k(a,b){return a.hasOwnProperty(b)}b.D=k;function l(a,b){return a instanceof b}b.F=l},"./src/common/util/datetimehelper.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/common.ts"),e=c("./src/common/util/stringhelper.ts"),f=c("./src/common/util/arrayhelper.ts"),g=c("./src/common/culture/cultureInfo.ts"),h=c("./src/common/util/types.ts"),i=h.j.Fa,j=e.u.Sa,k=null,l=parseInt,m=!1,n=!0,o=Math.floor,p=Math.abs,q=new d.ResourceManager(d.SR),r=q.getResource.bind(q),s={};function u(a,b,c){var d,e,f,h,i,k,l,m,q,s,t,w,y,z,A,B,C,D,E,F,G,H,I,J,K=c.DateTimeFormat,L=K.Calendar.convert;if(!b||!b.length)return c&&c.name.length?L?u(a,K.fullDateTimePattern,c):a.toLocaleString():""+a;d=K.eras,e="s"===b,f="",l=/([^d]|^)(d|dd)([^d]|$)/g;function M(){return i||k?i:(i=l.test(b),k=n,i)}m=0,q=x(),!e&&L&&(s=L.fromGregorian(a));function N(a,b){if(s)return s[b];switch(b){case 0:return a.getFullYear();case 1:return a.getMonth();case 2:return a.getDate();default:return}}for(t=-2,w=-2,y={Qb:""},z=0;A=q.lastIndex,B=q.exec(b),C=b.slice(A,B?B.index:b.length),y.Qb="",m+=v(C,y),f+=y.Qb,B;z++)if(m%2!==1)switch(D=a.getFullYear(),E=a.getDay(),F=a.getHours(),G=a.getMinutes(),H=a.getSeconds(),I=a.getMilliseconds(),J=a.getTimezoneOffset(),B[0]){case"dddd":f+=K.dayNames[E];break;case"ddd":f+=K.abbreviatedDayNames[E];break;case"dd":i=n,f+=j(N(a,2),2);break;case"d":i=n,f+=N(a,2);break;case"MMMM":f+=K.monthGenitiveNames&&M()?K.monthGenitiveNames[N(a,1)]:K.monthNames[N(a,1)];break;case"MMM":f+=K.abbreviatedMonthGenitiveNames&&M()?K.abbreviatedMonthGenitiveNames[N(a,1)]:K.abbreviatedMonthNames[N(a,1)];break;case"MM":f+=j(N(a,1)+1,2);break;case"M":f+=N(a,1)+1;break;case"yyyy":case"yyy":f+=t>=0?g.aBb.ia("ee",a):j(s?s[0]:D,4);break;case"yy":f+=t>=0?g.aBb.ia("ee",a):j((s?s[0]:D)%100,2);break;case"y":f+=t>=0?g.aBb.ia("e",a):""+(s?s[0]:D)%100;break;case"hh":h=F%12,0===h&&(h=12),f+=j(h,2);break;case"h":h=F%12,0===h&&(h=12),f+=h;break;case"HH":f+=j(F,2);break;case"H":f+=""+F;break;case"mm":f+=j(G,2);break;case"m":f+=""+G;break;case"ss":f+=j(H,2);break;case"s":f+=""+H;break;case"tt":f+=F<12?K.amDesignator:K.pmDesignator;break;case"t":f+=(F<12?K.amDesignator:K.pmDesignator).charAt(0);break;case"f":case"0":f+=j(I,3).charAt(0);break;case"ff":case"00":f+=j(I,3).substr(0,2);break;case"fff":case"000":f+=j(I,3);break;case"z":h=J/60,f+=(h<=0?"+":"-")+o(p(h));break;case"zz":h=J/60,f+=(h<=0?"+":"-")+j(o(p(h)),2);break;case"zzz":h=J/60,f+=(h<=0?"+":"-")+j(o(p(h)),2)+":"+j(p(J%60),2);break;case"g":case"gg":case"ggg":if(!d)break;if(t===z-1){t=z;break}f+=g.aBb.ia(B[0],a),t=z;break;case"e":case"ee":if(!d){f+=j(s?s[0]:D,4);break}if(w===z-1){w=z;break}f+=g.aBb.ia(B[0],a),w=z;break;case"/":f+=K.dateSeparator;break;case"[h]":case"[hh]":case"[H]":case"[HH]":case"[mm]":case"[m]":case"[ss]":case"[s]":f+=B[0];break;default:throw Error(r().Exp_InvalidDateFormat)}else f+=B[0];return""+f}function v(a,b){var c,d,e,f=0,g=m;for(c=0,d=a.length;c<d;c++)switch(e=a.charAt(c)){case'"':g?b.Qb+="'":f++,g=m;break;case"\\":g&&(b.Qb+="\\"),g=!g;break;default:b.Qb+=e,g=m}return f}function w(a,b){var c,d=new Date,e=a.DateTimeFormat.eras;return e&&b<100&&(c=g.aBb.ca(d).ea,b+=c-c%100,b>a.DateTimeFormat.Calendar.TwoDigitYearMax&&(b-=100)),b}function x(){return/\/|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yyy|yy|y|hh|h|HH|H|mm|m|ss|s|tt|t|fff|ff|f|000|00|0|zzz|zz|z|ggg|gg|g|ee|e|\[H\]|\[HH\]|\[h\]|\[hh\]|\[mm\]|\[m\]|\[ss\]|\[s\]/g}function y(a,b,c){var d,e,g,h,i,j,l,m,n,o,p;if(a.Rb){if(a.Rb[b])return a.Rb[b]}else a.Rb={};for(d=b,d=d.replace("%M","M"),d=d.replace(/([\^\$\.\*\+\?\|\[\]\(\)\{\}])/g,"\\\\$1"),e="^",g={Qb:""},h=[],i=0,j=0,l=x();(m=l.exec(d))!==k;)if(g.Qb="",n=d.slice(i,m.index),i=l.lastIndex,j+=v(n,g),e+=g.Qb,j%2!==1){switch(m[0]){case"dddd":case"ddd":case"MMMM":case"MMM":case"gggg":case"ggg":case"gg":case"g":e+="(\\D+)";break;case"tt":case"t":e+="(\\D*)";break;case"dd":case"d":case"MM":case"M":case"yy":case"y":case"HH":case"H":case"hh":case"h":case"mm":case"m":case"ss":case"s":e+="(\\d\\d?)";break;case"eee":case"ee":case"e":e+=c?"(\\u5143|\\d\\d?)":"(\\d\\d?)";break;case"yyy":case"yyyy":e+="(\\d{2}|\\d{4})";break;case"fff":case"000":e+="(\\d{3})";break;case"ff":case"00":e+="(\\d{2})";break;case"f":case"0":e+="(\\d)";break;case"zzz":e+="([+-]?\\d\\d?:\\d{2})";break;case"zz":case"z":e+="([+-]?\\d\\d?)";break;case"/":e+="(\\"+a.dateSeparator+")";break;default:throw Error(r().Exp_InvalidDateFormat)}f.k.Sb(h,m[0])}else e+=m[0];return g.Qb="",v(d.slice(i),g),e+=g.Qb,e+="$",o=(""+e).replace(/\s+/g,"\\s+"),p={Tb:o,Ub:h,_a:RegExp(o)},a.Rb[b]=p,p}function z(a){var b=a.getTimezoneOffset();return b===-485&&(b=-485-43/60),b===-321&&(b=-321-10/60),b}t=function(){function a(){}return a.mb=function(a,b,c){return c||(c=g.CultureManager.q4()),u(a,b,c)},a.Vb=function(a,b,c){return u(a,b,c||g.CultureManager.q4())},a.gBa=function(b,c,d){var e,f,g,h,i,j=m;for(i=d,i.slice(1).forEach(function(a){a&&(j=!0)}),j||(i=i.concat(c.ma())),e=1,f=i.length;e<f;e++)if(h=i[e],h&&(j=n,g=a.Wb(b,h,c)))return g;return k},a.Qa=function(b,c,d){var e,f;return c||d||(f=s[b],void 0===f)?(e=c?[b,c]:[b],f=a.gBa(b,d||g.CultureManager.q4(),e),c||d||(s[b]=f),f?new Date(f):f):f?new Date(f):f},a.lb=function(b,c){return a.gBa(b,g.CultureManager.q4("invariant"),[b,c])},a.Wb=function(a,b,c){var d,f,h,i,j,n,o,p,q,r,s,t,u,v,x,z,A,B,C,D,E,F,G,H,I,J,K,L,M;if(a=a.trim(),d=c.DateTimeFormat,f=1041===c.id,h=y(d,b,f),i=h._a.exec(a),i===k)return k;for(j=h.Ub,n=k,o=k,p=k,q=k,r=k,s=0,u=0,v=0,x=0,z=k,A=m,B=0,C=j.length;B<C;B++)if(D=i[B+1])switch(j[B]){case"dd":case"d":if(q=l(D,10),q<1||q>31)return k;break;case"MMMM":if(p=c.oa(D),p<0||p>11)return k;break;case"MMM":if(p=c.ra(D),p<0||p>11)return k;break;case"M":case"MM":case"%M":if(p=l(D,10)-1,p<0||p>11)return k;break;case"e":case"ee":if(o=f&&"\u5143"===D?1:w(c,l(D,10)),o<0||o>9999)return k;break;case"y":case"yy":case"yyy":case"yyyy":if(o=l(D,10),o<0||o>9999)return k;break;case"h":case"hh":case"H":case"HH":if(s=l(D,10),s<0)return k;break;case"m":case"mm":if(u=l(D,10),u<0||u>59)return k;break;case"s":case"ss":if(v=l(D,10),v<0||v>59)return k;break;case"tt":case"t":if(E=D.toUpperCase(),A=E===d.pmDesignator.toUpperCase(),!A&&E!==d.amDesignator.toUpperCase())return k;break;case"f":case"0":if(x=100*l(D,10),x<0||x>999)return k;break;case"ff":case"00":if(x=10*l(D,10),x<0||x>999)return k;break;case"fff":case"000":if(x=l(D,10),x<0||x>999)return k;break;case"dddd":if(r=c.ua(D),r<0||r>6)return k;break;case"ddd":if(r=c.wa(D),r<0||r>6)return k;break;case"zzz":if(F=D.split(/:/),2!==F.length)return k;if(t=l(F[0],10),t<-12||t>13)return k;if(G=l(F[1],10),G<0||G>59)return k;z=60*t+(e.u.kb(D,"-")?-G:G);break;case"z":case"zz":if(t=l(D,10),t<-12||t>13)return k;z=60*t;break;case"g":case"gg":case"ggg":if(H=D,!H||!d.eras)return k;if(n=g.aBb.ha(j[B],H),n<0)return k}if(I=/^(\d|\d\d):/.test(a),J=I?new Date(1899,11,30):new Date,
L=d.Calendar.convert,L&&(K=L.fromGregorian(J)),L||(K=[J.getFullYear(),J.getMonth(),J.getDate()]),o===k?o=K[0]:o<100&&(d.eras&&n!==k?o=g.aBb.ga(n||0,o):o+=o>=30?1900:2e3),p===k&&(p=K[1]),q===k&&(q=K[2]),L){if(J=L.toGregorian(o,p,q),J===k)return k}else{if(J.setFullYear(o,p,q),J.getDate()!==q)return k;if(r!==k&&J.getDay()!==r)return k}return A&&s<12&&(s+=12),J.setHours(s,u,v,x),z!==k&&(M=J.getMinutes()-(z+J.getTimezoneOffset()),J.setHours(J.getHours()+M/60,M%60)),J},a.Xb=function(a){var b,c=a-25569,d=new Date(864e5*c),e=c>=0?1:-1,f=z(d),g=(864e5*a*1440+e-3181192704e6+864e5*f)/1440,h=new Date(g),i=f>=0?1:-1,j=new Date(g+36e5*i),k=z(j);if(f!==k)if(b=new Date(g+60*(k-f)*1e3),f>k){if(i===-1||k===z(h))return b=999===b.getMilliseconds()?new Date(b.valueOf()+1):b}else if(f<k&&(1===i||k===z(h)))return b=999===b.getMilliseconds()?new Date(b.valueOf()+1):b;return h=999===h.getMilliseconds()?new Date(h.valueOf()+1):h},a.Rka=function(b){if("/OADate("===b.substr(0,8)){var c=parseFloat(b.substr(8,b.length-10));return a.Xb(c)}},a.Daa=function(b){return"/OADate("+a.Ra(b)+")/"},a.Ra=function(a){if(i(a))return 0;"number"==typeof a&&(a=new Date(a));var b=z(a);return(1440*a.getTime()+3181192704e6-864e5*b)/124416e6},a.Yb=function(b){var c,d,e,f=k,g=n;if(i(b)?f=a.Xb(0):b instanceof Date?f=new Date(b.valueOf()):"string"==typeof b?(c=a.Qa(b),c||(isNaN(b)?a.fvb(b)?g=m:(c=new Date(b),isNaN(c.valueOf())&&(g=m),d=/^[-+=\s]*(\d+)\W+(\d+)\W+(\d+)$/,e=d.exec(b.replace(/ |\n/g,"").trim()),e&&4===e.length&&(e.indexOf(""+c.getFullYear())!==-1&&e.indexOf(""+(c.getMonth()+1))!==-1&&e.indexOf(""+c.getDate())!==-1||(g=m))):(c=a.Xb(parseFloat(b)),c||(g=m))),f=c):"number"==typeof b?f=a.Xb(b):g=m,g)return f;throw r().Exp_InvalidCast},a.fvb=function(a){var b=g.CultureManager.q4().DateTimeFormat,c=function(a,b){var c=0;return b.forEach(function(b){b&&a.indexOf(b)>-1&&c++}),c>1};return c(a,b.monthNames)||c(a,b.abbreviatedMonthNames)||c(a,b.dayNames)||c(a,b.abbreviatedDayNames)},a.Ska=function(a){return a&&(a.constructor===Date||a.getUTCDate&&a.setFullYear)},a}(),b.l=t},"./src/common/util/functionhelper.ts":function(a,b,c){"use strict";var d,e,f;Object.defineProperty(b,"__esModule",{value:!0}),d=isNaN,e="rowCount",f="colCount";function g(a){return a}function h(a,b,c,g,h,i,j,k,l,m,n){var o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L=c[e],M=c[f],N=b[e],O=b[f],P=g[e],Q=g[f],R=1===M,S=1===O;if(N===L&&O===M){for(E=0,F=0,G=0,H=0,u=L*M,o=0;o<L;o++)for(p=0;p<M;p++){if(r=i(c[o][p]),s=i(b[o][p]),a&&(d(r)||d(s)))return l;s=m(s),E+=r,F+=r*r,G+=s,H+=r*s}for(I=u*H-E*G,t=h?I/(u*F-E*E):H/F,v=h?(G*F-E*H)/(u*F-E*E):0,w=[],o=0;o<P;o++)for(w[o]=[],p=0;p<Q;p++){if(r=i(g[o][p]),a&&d(r))return l;w[o][p]=0===I?b[0][0]:n(t*r+v)}return j?new j(w):w}if(S&&N===L||1===N&&O===M){for(r=[],s=[],C=M+1,D=M+2,o=0;o<L;o++){if(y=S?b[o][0]:b[0][o],v=i(y),a&&d(v))return l;s[o]=R?m(v):v}for(o=0;o<L;o++)for(r[o]=[],p=0;p<M;p++){if(v=i(c[o][p]),a&&d(v))return l;r[o][p]=v}for(J=[],t=0;t<C;t++)for(J[t]=[],u=0;u<D;u++)J[t][u]=0;for(q=0;q<L;q++)for(J[0][C]+=s[q],o=0;o<M;o++)for(y=o+1,J[0][y]+=r[q][o],J[y][0]=J[0][y],J[y][C]+=r[q][o]*s[q],p=o;p<M;p++)A=p+1,J[A][y]+=r[q][o]*r[q][p],J[y][A]=J[A][y];for(J[0][0]=L,K=h?0:1,x=K;x<C;x++){if(a){if(0===J[x][x]){for(B=!1,p=x+1;!B&&p<C;p++)if(0!==J[p][x]){for(q=0;q<D;q++)A=J[x][q],J[x][q]=J[p][q],J[p][q]=A;B=!0}if(!B)return k}}else{for(o=x;o<C&&0===J[o][x];)o++;if(o>=C)return k;for(z=K;z<D;z++)y=J[x][z],J[x][z]=J[o][z],J[o][z]=y}for(y=1/J[x][x],z=K;z<D;z++)J[x][z]*=y;for(o=K;o<C;o++)if(o!==x)for(y=-J[o][x],z=0;z<D;z++)J[o][z]+=y*J[x][z];h||(J[0][C]=0)}for(w=[],S||(w[0]=[]),o=0;o<P;o++){for(S&&(w[o]=[]),y=J[0][C],p=0;p<M;p++){if(v=i(S?g[o][p]:g[p][o]),a&&d(v))return l;y+=J[p+1][C]*v}S?w[o][0]=n(y):w[0][o]=n(y)}return j?new j(w):w}return k}function i(a,b,c,d,e,f,i,j){return h(!0,a,b,c,d,e,f,j,i,g,g)}b.qc=i;function j(a,b,c,d,i,j,k){var l,m;for(l=0;l<a[e];l++)for(m=0;m<a[f];m++)if(a[l][m]<=0)return j;return h(!1,a,b,c,d,g,i,k,null,Math.log,Math.exp)}b.sc=j},"./src/common/util/numberhelper.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/common.ts"),e=c("./src/common/util/types.ts"),f=c("./src/common/util/regexhelper.ts"),g=c("./src/common/util/stringhelper.ts"),h=c("./src/common/culture/cultureInfo.ts"),i=e.j.Fa,j=g.u.Sa,k=g.u.Ta,l=null,m=void 0,n=parseInt,o=parseFloat,p=!1,q=!0,r=Math.floor,s=Math.abs,t=Math.pow,u=Math.min,v="#dot#",w="#group#",x=function(a,b,c){return a.substr(b,c)},y=function(a){return f.q.qb(a)},z=new d.ResourceManager(d.SR),A=z.getResource.bind(z),B="##################0",C="################",D={1:"+",0:"","-1":"-"},E=4294967295,F=["0","#","?"],G={"\xa4":"\xa4",Br:"Br",Fdj:"Fdj",Nfk:"Nfk",R:"R",$:"$",FCFA:"FCFA","GH\u20b5":"GH\u20b5","\u1265\u122d":"\u1265\u122d","\u0631.\u0633.\u200f":"\u0631.\u0633.\u200f",XDR:"XDR","\u062f.\u0625.\u200f":"\u062f.\u0625.\u200f","\u062f.\u0628.\u200f":"\u062f.\u0628.\u200f","\u062f.\u062c.\u200f":"\u062f.\u062c.\u200f","\u062c.\u0645.\u200f":"\u062c.\u0645.\u200f","\u20aa":"\u20aa","\u062f.\u0639.\u200f":"\u062f.\u0639.\u200f","\u062f.\u0627.\u200f":"\u062f.\u0627.\u200f","\u0641.\u062c.\u0642.\u200f":"\u0641.\u062c.\u0642.\u200f","\u062f.\u0643.\u200f":"\u062f.\u0643.\u200f","\u0644.\u0644.\u200f":"\u0644.\u0644.\u200f","\u062f.\u0644.\u200f":"\u062f.\u0644.\u200f","\u062f.\u0645.\u200f":"\u062f.\u0645.\u200f","\u0623.\u0645.\u200f":"\u0623.\u0645.\u200f","\u0631.\u0639.\u200f":"\u0631.\u0639.\u200f","\u0631.\u0642.\u200f":"\u0631.\u0642.\u200f","\u062c.\u0633.":"\u062c.\u0633.",S:"S","\xa3":"\xa3","\u0644.\u0633.\u200f":"\u0644.\u0633.\u200f","\u062f.\u062a.\u200f":"\u062f.\u062a.\u200f","\u0631.\u064a.\u200f":"\u0631.\u064a.\u200f","\u20b9":"\u20b9",TSh:"TSh","\u20ac":"\u20ac","\u20bc":"\u20bc","\u20bd":"\u20bd",K:"K","\u043b\u0432.":"\u043b\u0432.","\u20a6":"\u20a6",CFA:"CFA","\u09f3":"\u09f3","\xa5":"\xa5",KM:"KM","\u041a\u041c":"\u041a\u041c",USh:"USh","K\u010d":"K\u010d","kr.":"kr.",Ksh:"Ksh",CHF:"CHF","\u0783.":"\u0783.","Nu.":"Nu.",EC$:"EC$",P:"P",US$:"US$",D:"D",Rp:"Rp",Ar:"Ar",MOP$:"MOP$",Rs:"Rs",MK:"MK",RM:"RM","\u20b1":"\u20b1",RF:"RF",SR:"SR",SDG:"SDG",Le:"Le","NAf.":"NAf.",E:"E",T$:"T$",VT:"VT",WS$:"WS$",Bs:"Bs","\u20a1":"\u20a1",Q:"Q",L:"L",C$:"C$","B/.":"B/.","S/.":"S/.","\u20b2":"\u20b2","Bs.":"Bs.","\u0631\u064a\u0627\u0644":"\u0631\u064a\u0627\u0644",FG:"FG",UM:"UM",kr:"kr",FBu:"FBu",FC:"FC",DA:"DA",G:"G",CF:"CF",DH:"DH",FCFP:"FCFP",LS:"LS",DT:"DT",kn:"kn",HUF:"HUF","\u058f":"\u058f",ISK:"ISK","\u20be":"\u20be","\u200b":"\u200b","\u20b8":"\u20b8","\u17db":"\u17db","\u20a9":"\u20a9","\u0441\u043e\u043c":"\u0441\u043e\u043c",Kz:"Kz","\u20ad":"\u20ad",MTn:"MTn","\u0434\u0435\u043d":"\u0434\u0435\u043d","\u20ae":"\u20ae","\u0930\u0941":"\u0930\u0941","Afl.":"Afl.","\u07d6\u07d5.":"\u07d6\u07d5.","\u0440\u0443\u0431.":"\u0440\u0443\u0431.","z\u0142":"z\u0142","\u060b":"\u060b",R$:"R$",Db:"Db",RON:"RON","\u20b4":"\u20b4",MAD:"MAD","\u0dbb\u0dd4.":"\u0dbb\u0dd4.","Lek\xeb":"Lek\xeb",den:"den",RSD:"RSD","\u0434\u0438\u043d.":"\u0434\u0438\u043d.","\u0720.\u0723.\u200f":"\u0720.\u0723.\u200f","Rs.":"Rs.","\u0441\u043c\u043d":"\u0441\u043c\u043d","\u0e3f":"\u0e3f","m.":"m.","\u20ba":"\u20ba","\u2d37\u2d54":"\u2d37\u2d54","so\u02bbm":"so\u02bbm","\u0441\u045e\u043c":"\u0441\u045e\u043c","\u20ab":"\u20ab",HK$:"HK$",MOP:"MOP",NT$:"NT$"};function H(a,b,c,d){return b&&0!==b.length&&"i"!==b?I(a,b,c.NumberFormat,d):c&&c.name.length>0?a.toLocaleString():""+a}function I(a,b,c,d){var e=L(b,c);return U(a,e,c,d)}function J(a,b){for(var c,d,e,f,g,h,i,j,k=r(s(a)),l={Xa:1,Ya:0};k>=10;)k/=10,l.Xa++;return c=""+a,d=c.search(/e/gi),e=c.indexOf(b),d!==-1?(g=x(c,0,d),h=x(c,d+1),i=0,e!==-1&&(i=x(g,e+1).length),j=o(h),f=i-j,f<0&&(f=0),l.Ya=f):(f=0,e!==-1&&(f=x(c,e+1).length),l.Ya=f),l}function K(a){var b,c={Za:a.charAt(0),$a:0,_a:0},d="";for(b=1;b<a.length;b++)if(d=a.charAt(b),"+"===d)c.$a=1;else{if("-"!==d){if("0"===d){c._a=a.length-b;break}throw Error(A().Exp_InvalidExponentFormat)}c.$a=-1}return c}function L(a,b){var c,d,e,f,g,h,i={ab:l,bb:l,cb:p,eb:0,fb:0,Rja:0,hb:l},j="",k=p,m=p,n=p,o=p,r=p,s=p,t=l,u=[];for(d=0;d<a.length;d++){if(c=a.charAt(d),k)j+=c,'"'===c&&(u.push(j),j="",k=p);else if(m){if("E"===t||"e"===t){if(!(["+","-","0"].indexOf(c)>=0)){m=p,d--;continue}j+=c}else if("+"===t||"-"===t){if("0"!==c){m=p,d--;continue}j+=c}else if("0"===t){if("0"!==c){m=p,r||(r=q,i.hb=K(j)),d--;continue}j+=c}}else if("*"!==t&&"_"!==t&&"\\"!==t||""===j)if("*"===c||"_"===c||"\\"===c)s=p,""!==j&&(u.push(j),j=""),j+=c;else if("'"===c)s=p,""!==j&&(u.push(j),j=""),u.push(c);else if('"'===c)s=p,k=q,""!==j&&(u.push(j),j=""),j+=c;else if(F.indexOf(c)>=0)s=q,F.indexOf(t)<0&&""!==j&&(u.push(j),j=""),j+=c;else if("."!==c||n)if(c===b.percentSymbol)s=p,i.fb++,""!==j&&(u.push(j),j=""),u.push(c);else if(c===b.perMilleSymbol)s=p,i.Rja++,""!==j&&(u.push(j),j=""),u.push(c);else if(c===b.percentGroupSeparator)if(s){for(e=void 0,""!==j&&(u.push(j),j=""),f=q,g="",h=d+1;h<a.length;h++)if(e=a.charAt(h),""===g)if('"'===e)g=e;else{if(F.indexOf(e)>=0){f=p;break}if(e===b.numberDecimalSeparator||";"===e)break}else'"'===e&&(g="");f?i.eb++:n||(e=a.charAt(d+1),e&&F.indexOf(e)>=0&&(o=q))}else j+=c;else"E"===c||"e"===c?(s=p,m=q,""!==j&&(u.push(j),j=""),j+=c):(s=p,F.indexOf(t)>=0&&""!==j&&(u.push(j),j=""),j+=c);else""!==j&&(u.push(j),j=""),i.ab=u,u=[],n=q,s=p;else j+=c,u.push(j),j="";t=c}return""!==j&&(m&&!r&&(i.hb=K(j)),u.push(j)),o&&(i.cb=q),n?i.bb=u:i.ab=u,i}function M(a,b,c,d){var e,f,g,h,i,j=b[0],k=1,l=0,m=p,n=a.length;for(e=0;e<n;e++)if(f=a[e].type,"number"===f){for(g="",h=a[e].value,i=h.length-1;i>=0;){if(!(1<=j&&j<=9||0===j&&k===b.length))throw Error(A().Exp_InvalidNumberGroupSize);/\d/gi.test(h[i])?(m&&(g&&a.push({type:"number",value:g}),a.push({type:"groupSeparator",value:c}),g="",m=p),l++):l=0,g=h[i]+g,l===j&&j>0&&(m=q,l=0,k<b.length&&(j=b[k],k++)),i--}g&&a.push({type:f,value:g})}else"fillingChar"===f&&(d.infillIndex=a.length-n),a.push({type:f,value:a[e].value});return a.splice(0,n)}function N(a,b,c,d,e,f){var g,h,i,l,m=Y(a,-b);return isFinite(m)||(m=a),a=m,g=""+a,l=g.split(/e/i),g=l[0],i=l.length>1?n(l[1],10):0,l=g.split("."),g=l[0],h=l.length>1?l[1]:"",i>0?(h=k(h,i),g+=h.slice(0,i),h=x(h,i)):i<0&&(i=-i,g=a<0?f+j(g.replace(f,""),i+1):j(g,i+1),h=g.slice(-i,g.length)+h,g=g.slice(0,-i)),b>0?(h=h.length>b?h.slice(0,b):k(h,b),h=e+h):h="",g+h}function O(a,b,c,d){var e=a;return b>0&&(e=_(a*t(100,b),15)),c>0&&(e=_(a*t(1e3,c),15)),d>0&&(e=_(a/t(1e3,d),15)),e}function P(a){var b,c,d=l;if(a)for(d="",b=0;b<a.length;b++)c=a[b],/^(0|#|\?)+/g.test(c)&&(d+=c);return d}function Q(a,b,c){var d,e={value:a,exponentValue:0},f=J(a,"."),g=f.Xa,h=s(a),i=c?c.length:1;if(h>=1)g>i?(g-=i,e.value=a/t(10,g),e.exponentValue=g):e.exponentValue=0,b.$a===-1&&(b.$a=0);else if(h<1&&h>0){for(b.$a=-1,d=t(10,i);10*h<d;)h*=10,e.exponentValue++;e.value*=t(10,e.exponentValue)}return e}function R(a,b,c,d,e,f,g,h){var i,k,l,m,n,o,p,r,s,t,u,v,w=h.numberGroupSizes,y=h.numberGroupSeparator,z=h.negativeSign,A=h.percentSymbol,B=h.perMilleSymbol,C=d.hb,E=x(b,0,1);for(E===z&&(b=x(b,1)),i=1===b.length&&"0"===b?0:b.length,b=0===i?"":b,k=0,l="",m=a.length-1;m>=0;m--)if(n="",o=a[m],/^(0|#|\?)+/g.test(o)){if(l!==c){for(p=o.length,r=i-k-1;r>=0&&p>0;r--)s=b.charAt(r),n=s+n,p--,k++;for(t=p-1;t>=0;t--)u=o[t],k++,"0"===u?n=u+n:"?"===u&&(""!==n&&(f.push({type:"number",value:n}),n=""),f.push({type:"numberPlaceholder",value:u}));l=o+l,l===c&&k<i&&(n=b.substr(0,i-k)+n),""!==n&&f.push({type:"number",value:n})}}else d.hb&&!g.replaceExponent&&/^((E(\+|-)?|e(\+|-)?)\d+)/g.test(o)?(g.replaceExponent=q,v="",v+=C.Za,v+=D[C.$a],v+=j(""+e,C._a),f.push({type:"exponent",value:v})):"_"===o[0]?f.push({type:"placeholder",value:o[1]}):"*"===o[0]?g.hasInfilling||(f.push({type:"fillingChar",value:o[1]}),g.hasInfilling=!0,g.infillIndex=f.length-1):"\\"===o[0]?2===o.length&&f.push({type:"text",value:o[1]}):'"'===o[0]&&'"'===o[o.length-1]?o.length>2&&f.push({type:"text",value:o.substr(1,o.length-2)}):o===A?f.push({type:"percent",value:o}):o===B?f.push({type:"permille",value:o}):G[o]?f.push({type:"currency",value:o}):f.push({type:"text",value:o});E===z&&c&&f.push({type:"text",value:E}),d.cb===q&&M(f,w,y,g),f=f.reverse(),g.infillIndex=f.length-1-g.infillIndex}function S(a,b,c,d,e,f,g,h,i){var k,l,m,n,o,p,r,s,t,u,v,w,x=i.numberDecimalSeparator,y=i.percentSymbol,z=i.perMilleSymbol,A=b.indexOf(x),E=e.hb;for((A>0||d!==B||c!==C)&&g.push({type:"decimalSeparator",value:x}),k=A!==-1?b.substring(A+1):"",l=0,m="",n=0;n<a.length;n++)if(o=a[n],/^(0|#|\?)+/g.test(o)){if(p="",m!==c){for(r=o.length,s=0,t=l;r>0&&k.length-l>0;)p+=k.charAt(t),r--,l++,s++,t++;for(u=s;r>0;)v=o[u],u++,l++,r--,"0"===v?p+=v:"?"===v&&(""!==p&&(g.push({type:"number",value:p}),p=""),g.push({type:"numberPlaceholder",value:v}));""!==p&&g.push({type:"number",value:p}),m+=o}}else E&&!h.replaceExponent&&/^((E(\+|-)?|e(\+|-)?)\d+)/g.test(o)?(h.replaceExponent=q,w="",w+=E.Za,w+=D[E.$a],w+=j(""+f,E._a),g.push({type:"exponent",value:w})):"_"===o[0]?g.push({type:"placeholder",value:o[1]}):"*"===o[0]?(g.push({type:"fillingChar",value:o[1]}),h.hasInfilling&&g.splice(h.infillIndex,1),h.hasInfilling=!0,h.infillIndex=g.length-1):"\\"===o[0]?2===o.length&&g.push({type:"text",value:o[1]}):'"'===o[0]&&'"'===o[o.length-1]?o.length>2&&g.push({type:"text",value:o.substr(1,o.length-2)}):o===y?g.push({type:"percent",value:o}):o===z?g.push({type:"permille",value:o}):G[o]?g.push({type:"currency",value:o}):g.push({type:"text",value:o})}function T(a,b){var c=a,d=a.indexOf(b);return d!==-1&&(c=a.replace(/0+$/,"")),c}function U(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,q,r,s,t,v,w=[];return a=O(a,b.fb,b.Rja,b.eb),e=b.ab,f=b.bb,e||f?(g=P(e),h=P(f),h||(h=""),b.hb&&(j=Q(a,b.hb,g),a=j.value,i=j.exponentValue),k=J(a,"."),l=c.numberGroupSizes,m=c.numberGroupSeparator,n=c.numberDecimalSeparator,o=c.negativeSign,q=u(k.Ya,h.length),r=N(a,q,l,m,n,o),d||(r=T(r,n)),""===r?(w.push({type:"text",value:(e?e.join(""):"")+(f?f.join(""):"")}),w):(s={hasInfilling:p,infillIndex:-1,replaceExponent:p},t=r.split(n)[0],"10"!==t||isNaN(i)||(t="1",v=b&&b.hb&&b.hb.$a===-1,i+=v?-1:1),e&&R(e,t,g,b,i,w,s,c),"0"!==t&&""===g&&f&&w.push({type:"number",value:t}),f&&S(f,r,h,g,b,i,w,s,c),w)):w}function V(a,b){var c,d,e,f,h,j,k,m,p,q,r,s,t,u,v,w,y;return a=i(a)?"":g.u.ib(a,""),a.match(/^[+-]?infinity$/i)?o(a):a.match(/^0x[a-f0-9]+$/i)?n(a,10):(c=b.NumberFormat,d=c.numberNegativePattern,e=W(a,c,d),f=e[0],h=e[1],""===f&&1!==d&&(e=W(a,c,1),f=e[0],h=e[1]),""===f&&(f="+"),h[0]===c.currencySymbol&&(h=x(h,1)),m=h.indexOf("e"),m<0&&(m=h.indexOf("E")),m<0?(k=h,j=l):(k=x(h,0,m),j=x(h,m+1)),r=k.indexOf("."),r<0?(p=k,q=l):(p=x(k,0,r),q=x(k,r+1)),p=p.split(",").join(""),s=",".replace(/\u00A0/g," "),","!==s&&(p=p.split(s).join("")),t=f+p,q!==l&&(t+="."+q),u=t[t.length-1],u===c.percentSymbol&&(t=x(t,0,t.length-1),t=g.u.ib(t,""),v=t.indexOf("."),v===-1&&(v=t.length),w="",w+=x(t,0,v-2),w+=".",w+=x(t,v-2,2),w+=x(t,v+1),t=w),j!==l&&(y=W(j,c,1),""===y[0]&&(y[0]="+"),t+="e"+y[0]+y[1]),t.match(/^[+-]?\d*\.?\d*(e[+-]?\d+)?$/)?o(t):NaN)}function W(a,b,c){var d=b.negativeSign,e=b.positiveSign;if(4!==c&&2!==c||(d=" "+d,e=" "+e),4===c||3===c){if(g.u.jb(a,d))return["-",x(a,0,a.length-d.length)];if(g.u.jb(a,e))return["+",x(a,0,a.length-e.length)]}else if(2===c||1===c){if(g.u.kb(a,d))return["-",x(a,d.length)];if(g.u.kb(a,e))return["+",x(a,e.length)]}else{if(0!==c)throw Error("");if(g.u.kb(a,"(")&&g.u.jb(a,")"))return["-",x(a,1,a.length-2)]}return["",a]}function X(a,b,c){if(0!==s(r(a)-a))throw Error(A().Exp_BadFormatSpecifier);var d=a>=0?a.toString(16):(E+a+1).toString(16);return d=b?d.toLowerCase():d.toUpperCase(),!i(c)&&d.length<c?j(d,c):d}function Y(a,b){var c,d;return a=+a,b=+b,isNaN(a)||b%1!==0?NaN:(c=a<0,a=c?-a:a,typeof b===m||0===b?(a=Math.round(a),c?-a:a):(d=(""+a).split("e"),a=Math.round(+(d[0]+"e"+(d[1]?+d[1]-b:-b))),a=c?-a:a,d=(""+a).split("e"),+(d[0]+"e"+(d[1]?+d[1]+b:b))))}function Z(a){var b=h.CultureManager.q4().NumberFormat;return"string"==typeof a&&(b=h.CultureManager.q4(a).NumberFormat),b}function $(a){var b,c,d,e="";if(Array.isArray(a)&&a.length>0)for(b=0;b<a.length;b++)if(c=a[b].type,d=a[b].value,"placeholder"===c)e+=" ";else{if("fillingChar"===c)continue;e+="numberPlaceholder"===c?" ":d}return e}function _(a,b){if("number"==typeof a&&(""+a).length>=b)if(a<1&&a>-1){var c=+a.toFixed(b);a=0===c?+a.toPrecision(b):c}else a=+a.toPrecision(b);return a}b.o={Qa:function(a){return V(a,h.CultureManager.q4())},lb:function(a){return V(a,h.CultureManager.q4("invariant"))},mb:function(a,b,c,d){return c||(c=h.CultureManager.q4()),H(a,b,c,d)},nb:function(a,b){var c,d,e;return"string"!=typeof a?a:(c=Z(b),d=c.numberDecimalSeparator,e=c.numberGroupSeparator,"."!==d&&(a=a.replace(y("[.]"),v)),","!==e&&(a=a.replace(y("[,]"),w)),"."!==d&&(a=a.replace(y(v),d)),","!==e&&(a=a.replace(y(w),e)),a)},Oa:function(a,b){var c,d,e;return"string"!=typeof a?a:(c=Z(b),d=c.numberDecimalSeparator,e=c.numberGroupSeparator,"."!==d&&(a=a.replace(y("["+d+"]"),v)),","!==e&&(a=a.replace(y("["+e+"]"),w)),"."!==d&&(a=a.replace(y(v),".")),","!==e&&(a=a.replace(y(w),",")),a)},Xia:function(a,b){var c,d,e;return!("string"==typeof a&&(c=Z(b),d=c.numberDecimalSeparator,e=c.numberGroupSeparator,"."!==d&&"."!==e&&a.indexOf(".")>=0||","!==d&&","!==e&&a.indexOf(",")>=0))},ob:function(a,b){return o(this.Oa(a,b))},pb:X,Hja:L,Ija:U,Jja:$,eka:B,fka:C,Lma:_}},"./src/common/util/regexhelper.ts":function(a,b,c){"use strict";var d,e,f;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/stringhelper.ts"),e=!0,f=function(){function a(){}return a.qb=function(b){var c=a.rb[b];return c||(c=a.rb[b]=RegExp(b,"g")),c.lastIndex=0,c},a.sb=function(b){var c=a.tb[b];return c||(c=a.tb[b]=RegExp(b,"gi")),c.lastIndex=0,c},a.ub=function(b,c,d){var f,g,h,i,j,k;if(a.vb[b])return a.wb[b];if(f="[~?*]+",a.qb(f).test(b)){for(g=[],h=b.split(""),i=void 0,j={".":e,"+":e,$:e,"^":e,"[":e,"]":e,"(":e,")":e,"{":e,"}":e,"|":e,"/":e},k=0;k<h.length;k++)i=h[k],"~"===i&&k<h.length-1?(k++,i=h[k],"*"===i||"?"===i?g.push("\\"):c&&g.push("~"),g.push(i)):"?"===i?g.push("."):"*"===i?(g.push("."),d?g.push("+"):g.push("*")):j[i]?(g.push("\\"),g.push(i)):g.push(i);return g.join("")}return null},a.zb=function(b,c,d){var e=a.ub(b,c,d);return e&&(e="^"+e+"$"),e},a.xb=function(a,b){for(var c="#"+a+"0#",e=1;b.indexOf(c)>0;)c=d.u.yb(c,"#"+a+(e-1)+"#","#"+a+e+"#"),e++;return c},a.Ab=function(a){return a.replace(/([\~\!\@\#\$\%\^\&\*\(\)\-\_\+\=\[\]\{\}\|\\\;\:\'\"\,\.\/\<\>\?])/,"\\$1")},a.rb={},a.tb={},a.vb={},a.wb={},a}(),b.q=f},"./src/common/util/stringhelper.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./src/common/util/regexhelper.ts");function e(a,b,c){return a.substr(b,c)}function f(a){return null===a||void 0===a}function g(a,b,c,d){var e,g;if(f(b))throw Error();return""===b||!(b.length>a.length)&&(e=a,g=b,c&&(e=e.toLowerCase(),g=g.toLowerCase()),d(e,g))}b.u={Bb:function(a,b,c){return c&&(a=a.toLowerCase(),b=b.toLowerCase()),""===b||a.indexOf(b)>=0},Cb:function(a,b,c){var d,e;return c?(d=a.toLowerCase(),e=b.toLowerCase(),d.indexOf(e)):a.indexOf(b)},Db:function(a,b){if(!b)return a;for(var c=a;e(c,0,b.length)===b;)c=e(c,b.length);return c},ib:function(a,b){if(!b)return a;for(var c=a;e(c,c.length-b.length,b.length)===b;)c=e(c,0,c.length-b.length);return c},Eb:function(a,b,c){var d,g;if(b<0||b>a.length||f(c))throw Error();return d=e(a,0,b),g=e(a,b,a.length-b),d+c+g},Fb:function(a,b,c){var d,g;if(f(c)&&(c=a.length-b),b<0||c<0||b+c>a.length)throw Error();return d=e(a,0,b),g=e(a,b+c,a.length-b-c),d+g},kb:function(a,b,c){return void 0===c&&(c=!1),g(a,b,c,function(a,b){return a.slice(0,b.length)===b})},jb:function(a,b,c){return void 0===c&&(c=!1),g(a,b,c,function(a,b){return a.slice(-b.length)===b})},Gb:function(a,b,c,d){if(!b)throw Error();return c=(""+c).replace(/\$/g,"$$$$"),a.replace(RegExp(b,"g"+(d?"i":"")),c)},Hb:function(a,c,e,f){return c=d.q.Ab(c),b.u.Gb(a,c,e,f)},Ib:function(a,b){var c=a.indexOf(b);return c<0||c>=a.length?a:e(a,0,c)},Jb:function(a,b){for(var c=0,d=a.indexOf(b);d>=0;)c+=1,d=a.indexOf(b,d+1);return c},yb:function(a,b,c){return a.split(b).join(c)},Kb:function(a,b){var c,d,e=a;for(c=0;c<b.length;c++)d=RegExp("\\{"+c+"\\}","g"),e=e.replace(d,b[c]);return e},Lb:function(a,b,c){var d,e=""+a;for(d=e.length;d<b;d++)e=c?"0"+e:e+"0";return e},Sa:function(a,c){return b.u.Lb(a,c,!0)},Ta:function(a,c){return b.u.Lb(a,c,!1)},Mb:function(a,b){return a===b||!a&&!b||a&&b&&a.toLowerCase()===b.toLowerCase()},Nb:function(a){return a.toUpperCase()},Ob:function(a){var b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},c=d.q.qb("(?:&|<|>|\"|'|`)");return c.test(a)?a.replace(c,function(a){return b[a]}):a},Pb:function(a){var b={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x60;":"`"},c=d.q.qb("(?:&amp;|&lt;|&gt;|&quot;|&#x27;|&#x60;)");return c.test(a)?a.replace(c,function(a){return b[a]}):a}}},"./src/common/util/types.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.j={r4:null,H:function(a,c){var d,e,f,g;if(b.j.ya(a))for(e=0,f=a.length;e<f&&(d=c.call(a[e],e,a[e]),d!==!1);e++);else for(g in a)if(a.hasOwnProperty(g)&&(d=c.call(a[g],g,a[g]),d===!1))break;return a},za:function(a){return!a||"object"==typeof a&&0===Object.keys(a).length},Aa:function(a){return"function"===b.j.Ba(a)},Ca:function(a){return Array.isArray?Array.isArray(a):"array"===b.j.Ba(a)},Da:function(a){return b.j.jk(a)&&(a=parseFloat(a)),!isNaN(a)&&isFinite(a)},jk:function(a){return"string"==typeof a},Ba:function(a){var c,d,e,f,g,h;if(null===a)return"null";if(c=b.j.r4,!c)for(c=b.j.r4={},d=["Boolean","Number","String","Function","Array","Date","RegExp","Object","Error"],e=0,f=d.length;e<f;e++)c["[object "+d[e]+"]"]=d[e].toLowerCase();return g=c.toString,h=typeof a,"object"===h||"function"===h?c[g.call(a)]||"object":h},Ea:function(a,c,d){var e,f;if(c){if(f=[].indexOf)return f.call(c,a,d);for(e=c.length,b.j.Fa(d)&&(d=0),d=d<0?Math.max(0,e+d):d;d<e;d++)if(d in c&&c[d]===a)return d}return-1},Ga:function(a,b){var c=b.length,d=a.length,e=0;if("number"==typeof c)for(;e<c;e++)a[d++]=b[e];else for(;void 0!==b[e];)a[d++]=b[e++];return a.length=d,a},Ha:function(a,c,d){var e,f,g,h,i=[];if(b.j.ya(a))for(f=0,g=a.length;f<g;f++)e=c(a[f],f,d),null!==e&&(i[i.length]=e);else{h=void 0;for(h in a)a.hasOwnProperty(h)&&(e=c(a[h],h,d),null!==e&&(i[i.length]=e))}return i.concat([])},Ia:function(){var a,c,d,e,f,g,h,i,j,k,l,m=[];for(a=0;a<arguments.length;a++)m[a]=arguments[a];for(i=m[0]||{},j=1,k=m.length,l=!1,"boolean"==typeof i&&(l=i,i=m[1]||{},j=2),"object"==typeof i||b.j.Aa(i)||(i={}),k===j&&(i=this,--j);j<k;j++)if(g=m[j],!b.j.Fa(g))for(f in g)c=i[f],e=g[f],i!==e&&(d=b.j.Ca(e),l&&e&&(b.j.Ja(e)||d)?(h=d?c&&b.j.Ca(c)?c:[]:c&&b.j.Ja(c)?c:{},i[f]=b.j.Ia(l,h,e)):void 0!==e&&(i[f]=e));return i},G:function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);function d(){this.constructor=a}d.prototype=b.prototype,a.prototype=new d},Ka:function(a){return null!==a&&a===a.window},Ja:function(a){var c,d;if(!a||"object"!==b.j.Ba(a)||a.nodeType||b.j.Ka(a))return!1;c={}.hasOwnProperty;try{if(a.constructor&&!c.call(a,"constructor")&&!c.call(a.constructor.prototype,"isPrototypeOf"))return!1}catch(a){return!1}for(d in a);return void 0===d||c.call(a,d)},ya:function(a){if(b.j.Fa(a))return!1;var c=a.length,d=b.j.Ba(a);return!b.j.Ka(a)&&(!(1!==a.nodeType||!c)||("array"===d||"function"!==d&&(0===c||"number"==typeof c&&c>0&&c-1 in a)))},La:function(a,c){var d=c||[];return null!==a&&(b.j.ya(Object(a))?b.j.Ga(d,"string"==typeof a?[a]:a):[].push.call(d,a)),d},Ma:null,Fa:function(a){return void 0===a||null===a},Na:null,Pa:null,C4:function(a){if(!a)return a;if("number"==typeof a||"string"==typeof a||"boolean"==typeof a||b.j.Fa(a))return a;if(a.clone)return a.clone();if(a instanceof Date)return new Date(a.valueOf());var c,d,e;c=a instanceof Object?new a.constructor:new a.constructor(a.valueOf());for(d in a)a.hasOwnProperty(d)&&(e=a[d],a.hasOwnProperty(d)&&c[d]!==e&&("object"==typeof e?c[d]=b.j.C4(e):c[d]=e));return c.toString=a.toString,c.valueOf=a.valueOf,c}}},"./src/common/util/util.res.en.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_InvalidDateFormat="Invalid date format pattern",b.Exp_InvalidExponentFormat="invalid exponent format",b.Exp_InvalidSemicolons="invalid format : too many semicolons",b.Exp_InvalidNumberGroupSize="NumberGroupSize must be between 1 and 9, except for the last element, which can be zero.",b.Exp_BadFormatSpecifier="Bad Format Specifier",b.Exp_InvalidNumberFormat="Invalid number format pattern",b.Exp_InvalidCast="InvalidCastException",b.Exp_Separator="numberDecimalSeparator, listSeparator and arrayListSeparator should be different in cluture info."},"./src/plugins/commands/commands.entry.ts":function(a,b,c){"use strict";var d,e;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/plugins/commands/commands.ts"),b.Key=d.Key,b.CommandManager=d.CommandManager,e=c("./src/plugins/commands/undomanager.ts"),b.UndoManager=e.UndoManager},"./src/plugins/commands/commands.ts":function(a,b,c){"use strict";var d,e,f;Object.defineProperty(b,"__esModule",{value:!0}),function(a){a[a.left=37]="left",a[a.right=39]="right",a[a.up=38]="up",a[a.down=40]="down",a[a.tab=9]="tab",a[a.enter=13]="enter",a[a.shift=16]="shift",a[a.ctrl=17]="ctrl",a[a.space=32]="space",a[a.altkey=18]="altkey",a[a.home=36]="home",a[a.end=35]="end",a[a.pup=33]="pup",a[a.pdn=34]="pdn",a[a.backspace=8]="backspace",a[a.del=46]="del",a[a.esc=27]="esc",a[a.a=65]="a",a[a.c=67]="c",a[a.v=86]="v",a[a.x=88]="x",a[a.z=90]="z",a[a.y=89]="y"}(d=b.Key||(b.Key={})),e=function(){function a(a,b,c){var d,e;this.tc=a,this.j4=b,this.Cj=c,d=this,Object.defineProperty(d,"shortcutKey",{get:function(){return d.vc},set:function(a){var b,c,f=d.vc;f!==a&&(e=d.tc.wc,b=e[f],b&&(c=b.indexOf(d),c>=0&&b.splice(c,1)),d.vc=a,a&&(b=e[a],b||(e[a]=b=[]),b.splice(0,0,d)))}})}return a.prototype.canUndo=function(){var a=this.j4;return a.canUndo},a.prototype.execute=function(a,b,c){var d,e,f=this,g=!0;try{e=f.j4.execute||f.j4,d=e(a,b,1===c),g=d!==!1}catch(a){g=!1}return b&&g&&f.tc.g4({command:b,result:d,s4:c}),d},a}(),f=function(){function a(a){this.xc=a,this.Ze={},this.wc={}}return a.prototype.register=function(a,b,c,d,f,g,h){var i=this,j=new e(i,b,a);i[a]=j,c&&(j.shortcutKey=i.getShortcutKey(c,d,f,g,h))},a.prototype.getCommand=function(a){return this[a]},a.prototype.execute=function(a){var b=this[a.cmd];if(b)return b.execute(this.xc,a,0)},a.prototype.setShortcutKey=function(a,b,c,d,e,f){var g,h,i,j,k=this;if(a)g=k[a],g&&(g.shortcutKey=k.getShortcutKey(b,c,d,e,f));else if(h=k.getShortcutKey(b,c,d,e,f),i=k.getCommands(h))for(j=i.length-1;j>=0;j--)i[j].shortcutKey=void 0},a.prototype.getShortcutKey=function(a,b,c,e,f){if(a){var g="A".charCodeAt(0)<=a&&a<="Z".charCodeAt(0)?String.fromCharCode(a):d[a];return g||(g=a),""+g+(b?"+ctrl":"")+(c?"+shift":"")+(e?"+alt":"")+(f?"+window":"")}},a.prototype.getCommands=function(a){return this.wc[a]},a.prototype.addListener=function(a,b){this.Ze[a]=b},a.prototype.removeListener=function(a){delete this.Ze[a]},a.prototype.g4=function(a){var b,c=this.Ze;for(b in c)c.hasOwnProperty(b)&&c[b](a)},a.prototype.no=function(){this.xc=null},a}(),b.CommandManager=f},"./src/plugins/commands/undomanager.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=function(){function a(a,b,c){var d=this;d.xc=a,b<0&&(b=2147483647),d.yc=b,d.zc=c,d.Ac=[],d.Bc=[]}return a.prototype.Cc=function(a,b){var c,d,e,f=this;if(a)if(1===b)f.Bc.push(a);else{if(c=f.yc,d=f.Ac.length,c>0&&d>=c)for(e=0;e<d-c+1;e++)f.Ac.shift();f.Ac.push(a),0===b&&(f.Bc=[])}},a.prototype.canUndo=function(){return this.Ac.length>0},a.prototype.undo=function(){var a,b,c=this,d=c.Ac,e=!0;if(c.zc&&c.canUndo()){a=d[d.length-1];try{b=c.xc.commandManager()[a.cmd],b&&(a.yTa=!0,e=b.execute(c.xc,a,1))}catch(a){e=!1}delete a.yTa,e!==!1&&(d.pop(),c.Bc.push(a))}return e},a.prototype.canRedo=function(){return this.Bc.length>0},a.prototype.redo=function(){var a,b,c,d=this,e=d.Bc,f=!0;if(d.zc&&d.canRedo()){a=e[e.length-1];try{b=d.xc.commandManager()[a.cmd],b&&(c=a.zTa,a.yTa=!0,f=b.execute(d.xc,a,2),c&&a.zTa&&!a.zTa.calc&&(a.zTa=c))}catch(a){f=!1}delete a.yTa,f!==!1&&(e.pop(),d.Ac.push(a))}return f},a.prototype.clear=function(){this.Ac=[],this.Bc=[]},a.prototype.no=function(){this.xc=null},a}();b.UndoManager=d},"./src/plugins/formatter/formatter.entry.ts":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./src/plugins/formatter/formatter.ts"))},"./src/plugins/formatter/formatter.res.en.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Exp_TokenIsNull="token is null",b.Exp_InvalidBackslash="the '\\' cannot be evaluated",b.Exp_FormatIllegal="format is illegal.",b.Exp_ValueIsNull="value is null",b.Exp_DuplicatedDescriptor="The type of descriptor was added.",b.Exp_TokenIllegal="token is illegal.",b.Exp_ValueIllegal="value is illegal.",b.Exp_InvalidCast="InvalidCastException"},"./src/plugins/formatter/formatter.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/stringhelper.ts"),e=c("./src/common/util/datetimehelper.ts"),f=c("./src/common/util/types.ts"),g=c("./src/common/util/numberhelper.ts"),h=c("./src/common/culture/cultureInfo.ts"),i=c("./src/common/util/common.ts"),j=c("./src/plugins/formatter/formatter.res.en.ts"),f.j.Ia(i.SR.en,j),k=new i.ResourceManager(i.SR),l=k.getResource.bind(k),f.j.Ma=function(a,b){if(f.j.Fa(a))return"null"===b;if(!b)return!1;if(b instanceof Function&&a instanceof b)return!0;if(typeof a===b)return!0;if("function"===b&&/^\s*\bfunction\b/.test(""+a))return!0;if(Object.prototype.toString.call(a).slice(8,-1).toLowerCase()===b.toLowerCase())return!0;if("DateTime"===b)return a instanceof Date;if("TimeSpan"===b){if(a instanceof Date){var c=e.l.Ra(a);return c>=0&&c<1}return!1}return"string"==typeof b&&"undefined number boolean string".indexOf(b)>-1,!1},f.j.Na=function(a,b){return!!g.o.Xia(a,b)&&(a=g.o.Oa(a,b),f.j.Ma(a,"number")||f.j.Ma(a,"DateTime")||f.j.Ma(a,"TimeSpan")||a&&!f.j.Ma(a,"boolean")&&!isNaN(a)&&!isNaN(parseFloat(a))&&!(a.length>=2&&"0"===a[0]&&"x"===a[1]))},f.j.Pa=function(a){return f.j.Fa(a)||""===a?0:f.j.Ma(a,"number")?a:f.j.Ma(a,"string")&&!isNaN(a)?g.o.Qa(a):f.j.Ma(a,"boolean")?a?1:0:f.j.Ma(a,"DateTime")?e.l.Ra(a):f.j.Ma(a,"TimeSpan")?Math.floor(e.l.Ra(a)):parseFloat(a)},m=null,n=void 0,o=Math.abs,p={Rc:"yy",vd:"y",Qc:"yyyy",jd:"m",hd:"mm",gd:"mmm",fd:"mmmm",dd:"mmmmm",ud:"d",wd:"dd",kd:"aaa",ld:"aaaa",md:"h",nd:"hh",xd:"m",yd:"mm",td:"s",zd:"ss",ea:"e",rd:"AM/PM",sd:"A/P",ed:"@mmmmm",qd:new Date(1899,11,30,0,0,0,0)},q=[p.vd,p.Rc,p.Qc,p.jd,p.hd,p.gd,p.fd,p.dd,p.ud,p.wd,p.kd,p.ld,p.md,p.nd,p.xd,p.yd,p.td,p.zd,"ggg","gg","g","ee","e"],r="\u4e00\u5341",s="\u5341",t={Hc:0,Va:1,Ua:2,_text:3},u=RegExp("([.+?*$^\\[\\](){}|/])","g"),v=[["M/d","MMM/d","MMMM/d","d/M","d/MMM","d/MMMM","M-d","MMM-d","MMMM-d","d-M","d-MMM","d-MMMM"],["M/y","MMM/y","M/yyyy","MMM/yyyy","M-y","MMM-y","M-yyyy","MMM-yyyy"],["M/d/y","MMM/d/y","MMMM/d/y","M/d/yyyy","MMM/d/yyyy","MMMM/d/yyyy","d/M/y","d/MMM/y","d/MMMM/y","d/M/yyyy","d/MMM/yyyy","d/MMMM/yyyy","yyyy/M/d","M-d-y","MMM-d-y","MMMM-d-y","M-d-yyyy","MMM-d-yyyy","MMMM-d-yyyy","d-M-y","d-MMM-y","d-MMMM-y","d-M-yyyy","d-MMM-yyyy","d-MMMM-yyyy","yyyy-M-d"],["H:m","h:m tt"],["H:m:s","h:m:s tt","H:m:s","h:mm:ss tt"],["H:m:s.FFF","h:m:s.FFF tt"],["M/d H:m","MMM/d H:m","MMMM/d H:m","d/M H:m","d/MMM H:m","d/MMMM H:m","M/y H:m","MMM/y H:m","M/yyyy H:m","MMM/yyyy H:m","M/d/y H:m","MMM/d/y H:m","MMMM/d/y H:m","M/d/yyyy H:m","MMM/d/yyyy H:m","MMMM/d/yyyy H:m","M-d H:m","MMM-d H:m","MMMM-d H:m","d-M H:m","d-MMM H:m","d-MMMM H:m","M-y H:m","MMM-y H:m","M-yyyy H:m","MMM-yyyy H:m","M-d-y H:m","MMM-d-y H:m","MMMM-d-y H:m","M-d-yyyy H:m","MMM-d-yyyy H:m","MMMM-d-yyyy H:m","M/d h:m tt","MMM/d h:m tt","MMMM/d h:m tt","d/M h:m tt","d/MMM h:m tt","d/MMMM h:m tt","M/y h:m tt","MMM/y h:m tt","M/yyyy h:m tt","MMM/yyyy h:m tt","M/d/y h:m tt","MMM/d/y h:m tt","MMMM/d/y h:m tt","M/d/yyyy h:m tt","MMM/d/yyyy h:m tt","MMMM/d/yyyy h:m tt","M-d h:m tt","MMM-d h:m tt","MMMM-d h:m tt","d-M h:m tt","d-MMM h:m tt","d-MMMM h:m tt","M-y h:m tt","MMM-y h:m tt","M-yyyy h:m tt","MMM-yyyy h:m tt","M-d-y h:m tt","MMM-d-y h:m tt","MMMM-d-y h:m tt","M-d-yyyy h:m tt","MMM-d-yyyy h:m tt","MMMM-d-yyyy h:m tt"],["M/d H:m:s","MMM/d H:m:s","MMMM/d H:m:s","d/M H:m:s","d/MMM H:m:s","d/MMMM H:m:s","M/y H:m:s","MMM/y H:m:s","M/yyyy H:m:s","MMM/yyyy H:m:s","M/d/y H:m:s","MMM/d/y H:m:s","MMMM/d/y H:m:s","M/d/yyyy H:m:s","MMM/d/yyyy H:m:s","MMMM/d/yyyy H:m:s","d/M/y H:m:s","d/MMM/y H:m:s","d/MMMM/y H:m:s","d/M/yyyy H:m:s","d/MMM/yyyy H:m:s","d/MMMM/yyyy H:m:s","yyyy/M/d H:m:s","M-d H:m:s","MMM-d H:m:s","MMMM-d H:m:s","d-M H:m:s","d-MMM H:m:s","d-MMMM H:m:s","M-y H:m:s","MMM-y H:m:s","M-yyyy H:m:s","MMM-yyyy H:m:s","M-d-y H:m:s","MMM-d-y H:m:s","MMMM-d-y H:m:s","M-d-yyyy H:m:s","MMM-d-yyyy H:m:s","MMMM-d-yyyy H:m:s","d-M-y H:m:s","d-MMM-y H:m:s","d-MMMM-y H:m:s","d-M-yyyy H:m:s","d-MMM-yyyy H:m:s","d-MMMM-yyyy H:m:s","yyyy-M-d H:m:s","M/d h:m:s tt","MMM/d h:m:s tt","MMMM/d h:m:s tt","d/M h:m:s tt","d/MMM h:m:s tt","d/MMMM h:m:s tt","M/y h:m:s tt","MMM/y h:m:s tt","M/yyyy h:m:s tt","MMM/yyyy h:m:s tt","M/d/y h:m:s tt","MMM/d/y h:m:s tt","MMMM/d/y h:m:s tt","M/d/yyyy h:m:s tt","MMM/d/yyyy h:m:s tt","MMMM/d/yyyy h:m:s tt","d/M/y h:m:s tt","d/MMM/y h:m:s tt","d/MMMM/y h:m:s tt","d/M/yyyy h:m:s tt","d/MMM/yyyy h:m:s tt","d/MMMM/yyyy h:m:s tt","yyyy/M/d h:m:s tt","M/d/yyyy h:mm:ss tt","M-d h:m:s tt","MMM-d h:m:s tt","MMMM-d h:m:s tt","d-M h:m:s tt","d-MMM h:m:s tt","d-MMMM h:m:s tt","M-y h:m:s tt","MMM-y h:m:s tt","M-yyyy h:m:s tt","MMM-yyyy h:m:s tt","M-d-y h:m:s tt","MMM-d-y h:m:s tt","MMMM-d-y h:m:s tt","M-d-yyyy h:m:s tt","MMM-d-yyyy h:m:s tt","MMMM-d-yyyy h:m:s tt","d-M-y h:m:s tt","d-MMM-y h:m:s tt","d-MMMM-y h:m:s tt","d-M-yyyy h:m:s tt","d-MMM-yyyy h:m:s tt","d-MMMM-yyyy h:m:s tt","yyyy-M-d h:m:s tt"],["M/d H:m:s.FFF","MMM/d H:m:s.FFF","MMMM/d H:m:s.FFF","d/M H:m:s.FFF","d/MMM H:m:s.FFF","d/MMMM H:m:s.FFF","M/y H:m:s.FFF","MMM/y H:m:s.FFF","M/yyyy H:m:s.FFF","MMM/yyyy H:m:s.FFF","d/M/y H:m","d/MMM/y H:m","d/MMMM/y H:m","d/M/yyyy H:m","d/mmm/yyyy H:m","d/MMMM/yyyy H:m","yyyy/M/d H:m","M/d/y H:m:s.FFF","MMM/d/y H:m:s.FFF","MMMM/d/y H:m:s.FFF","M/d/yyyy H:m:s","MMM/d/yyyy H:m:s.FFF","MMMM/d/yyyy H:m:s.FFF","d/M/y H:m:s.FFF","d/MMM/y H:m:s.FFF","d/MMMM/y H:m:s.FFF","d/M/yyyy H:m:s.FFF","d/MMM/yyyy H:m:s.FFF","d/MMMM/yyyy H:m:s.FFF","yyyy/M/d H:m:s.FFF","M-d H:m:s.FFF","MMM-d H:m:s.FFF","MMMM-d H:m:s.FFF","d-M H:m:s.FFF","d-MMM H:m:s.FFF","d-MMMM H:m:s.FFF","M-y H:m:s.FFF","MMM-y H:m:s.FFF","M-yyyy H:m:s.FFF","MMM-Yyyy H:m:s.FFF","d-M-y H:m","d-MMM-y H:m","d-MMMM-y H:m","d-M-yyyy H:m","d-MMM-yyyy H:m","d-MMMM-yyyy H:m","yyyy-M-d H:m","M-d-y H:m:s.FFF","MMM-d-y H:m:s.FFF","MMMM-d-y H:m:s.FFF","M-d-yyyy H:m:s","MMM-d-yyyy H:m:s.FFF","MMMM-d-yyyy H:m:s.FFF","D-M-y H:m:s.FFF","d-MMM-y H:m:s.FFF","d-MMMM-y H:m:s.FFF","D-M-yyyy H:m:s.FFF","d-MMM-yyyy H:m:s.FFF","d-MMMM-yyyy H:m:s.FFF","yyyy-M-d H:m:s.FFF","M/d h:m:s.FFF tt","MMM/d h:m:s.FFF tt","MMMM/d h:m:s.FFF tt","d/M h:m:s.FFF tt","d/MMM h:m:s.FFF tt","d/MMMM h:m:s.FFF tt","M/y h:m:s.FFF tt","MMM/y h:m:s.FFF tt","M/yyyy h:m:s.FFF tt","MMM/yyyy h:m:s.FFF tt","d/M/y h:m tt","d/MMM/y h:m tt","d/MMMM/y h:m tt","d/M/yyyy h:m tt","d/mmm/yyyy h:m tt","d/MMMM/yyyy h:m tt","yyyy/M/d h:m tt","M/d/y h:m:s.FFF tt","MMM/d/y h:m:s.FFF tt","MMMM/d/y h:m:s.FFF tt","M/d/yyyy h:m:s tt","MMM/d/yyyy h:m:s.FFF tt","MMMM/d/yyyy h:m:s.FFF tt","d/M/y h:m:s.FFF tt","d/MMM/y h:m:s.FFF tt","d/MMMM/y h:m:s.FFF tt","d/M/yyyy h:m:s.FFF tt","d/MMM/yyyy h:m:s.FFF tt","d/MMMM/yyyy h:m:s.FFF tt","yyyy/M/d h:m:s.FFF tt","M-d h:m:s.FFF tt","MMM-d h:m:s.FFF tt","MMMM-d h:m:s.FFF tt","d-M h:m:s.FFF tt","d-MMM h:m:s.FFF tt","d-MMMM h:m:s.FFF tt","M-y h:m:s.FFF tt","MMM-y h:m:s.FFF tt","M-yyyy h:m:s.FFF tt","MMM-Yyyy h:m:s.FFF tt","d-M-y h:m tt","d-MMM-y h:m tt","d-MMMM-y h:m tt","d-M-yyyy h:m tt","d-MMM-yyyy h:m tt","d-MMMM-yyyy h:m tt","yyyy-M-d h:m tt","M-d-y h:m:s.FFF tt","MMM-d-y h:m:s.FFF tt","MMMM-d-y h:m:s.FFF tt","M-d-yyyy H:m:s tt","MMM-d-yyyy H:m:s.FFF tt","MMMM-d-yyyy h:m:s.FFF tt","d-M-y h:m:s.FFF tt","d-MMM-y h:m:s.FFF tt","d-MMMM-y h:m:s.FFF tt","d-M-yyyy h:m:s.FFF tt","d-MMM-yyyy h:m:s.FFF tt","d-MMMM-yyyy h:m:s.FFF tt","yyyy-M-d h:m:s.FFF tt"]],
w=f.j.Ma,x=f.j.Ca,y=g.o,z=y.eka+"."+y.fka,A=y.Jja,B=y.mb,C=y.Lma,D=f.j.Fa,E=isNaN,F=function(a){return h.CultureManager.q4(a)},G=function(a){return F(a).DateTimeFormat},H=function(a){return F(a).NumberFormat},I=function(a){return a&&a.toLowerCase()},J=function(a){return a&&a.toUpperCase()},K=function(){throw Error(l().Exp_FormatIllegal)},L=function(){return h.CultureManager.booleanMapping},M={Dc:"",Kb:function(){var a,b,c,d,e,f,g=[];for(a=0;a<arguments.length;a++)g[a]=arguments[a];for(b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];if(0===arguments.length)return m;for(d=b[0],e=1;e<arguments.length;e++)f=RegExp("\\{"+(e-1)+"\\}","gm"),d=d.replace(f,arguments[e]);return d},Ec:function(a){return!a||a===M.Dc}},N={Fc:function(a){var b=a.charCodeAt(0);return b>=48&&b<=57},Gc:function(a){var b=a.charCodeAt(0);return b>=9&&b<=13||32===b||133===b||160===b}},O=function(){function a(a,b){var c,e,f,g;if(na.call(this,b),c=this,c.Uc=a,e=c.Uc,f=ia(e[0],"$",!1),g=e.indexOf("-"),!(f&&g>-1))throw Error(l().Exp_TokenIllegal);c.Vc=aa(e,1,g-1),e=d.u.Fb(e,0,g+1),e.length>0&&(c.Wc=parseInt(e,16))}return a.prototype.cultureInfo=function(){var a=this;return a.Xc||(a.Xc=F(a.Wc),a.Vc&&a.Vc!==M.Dc&&!a.Xc.NumberFormat.isReadOnly&&(a.Xc.NumberFormat.currencySymbol=a.Vc)),a.Xc},a.prototype.currencySymbol=function(){return this.Vc?d.u.Gb(this.Vc,"\\.",'"."'):M.Dc},a.prototype.allowScience=function(){if(this.Xc)return!(0===this.Xc.name().indexOf("ja")||0===this.Xc.name().indexOf("zh"))},a.prototype.toString=function(){return this.Uc?da(this.Uc):M.Dc},a.Name="LocaleIDFormatPart",a}(),P=function(){function a(a,b){this.formatCached=a,this.cultureName=b,this.typeName=""}return a.prototype.format=function(a){return m},a.prototype.parse=function(a){return m},a.prototype.formatString=function(a){return this.formatCached},a.prototype.toJSON=function(){var a,b={};for(a in this)this.hasOwnProperty(a)&&(b[a]=this[a]);return b},a.prototype.fromJSON=function(a){if(a)for(var b in a)a[b]!==n&&(this[b]=a[b])},a}(),b.FormatterBase=P,Q=function(){function a(a,b){var c,e,f,g,h,i,j,k;for(na.call(this,b),c=this,h=["<=","<>",">=",">","<","="],i=0,j=h.length;i<j;i++)if(e=h[i],d.u.kb(a,e,!0)){f=d.u.Fb(a,0,e.length),k=parseFloat(f),E(k)||(g=k);break}if(g===m&&g===n)throw Error(l().Exp_TokenIllegal);c.value=g,c.Sc=e,c.isMeetCondition=function(a){var b=c.Sc,d=c.value;switch(b){case"<=":return a<=d;case"<>":return a!==d;case">=":return a>=d;case">":return a>d;case"<":return a<d;case"=":return a===d}}}return a.prototype.toString=function(){return da(this.Sc+(""+this.value))},a}(),R=function(){function a(a,b){na.call(this,b);var c=this;if(c.token=a,c.type=parseInt(d.u.Fb(a,0,5),10),c.type<0||c.type>3)throw Error(l().Exp_TokenIllegal)}return a.prototype.Pc=function(b,c,e){var f,g,h,i,j,k,l,m,n,o;if(!b||b===M.Dc)return b;for(f=b,g=b,h=-1,i=-1,j=!1,n=b.length-1;n>=0;n--)o=g[n],!E(o)&&!ia(o," ",!1)||ia(o,".",!1)&&!j?(ia(o,".",!1)&&(j=!0),h===-1&&(h=n),i=n):i>-1&&h>-1&&(k=aa(g,i,h-i+1),l=parseFloat(k),E(l)||(m=a.numberString(k,c,e),f=d.u.Fb(f,i,h-i+1),f=d.u.Eb(f,i,m)),h=-1,i=-1,j=!1);return i>-1&&h>-1&&(k=aa(g,i,h-i+1),l=parseFloat(k),E(l)||(m=a.numberString(k,c,e),f=d.u.Fb(f,i,h-i+1),f=d.u.Eb(f,i,m))),f},a.prototype.toString=function(){if(this.type>-1)return da("DBNum"+this.type);throw Error()},a.numberString=function(b,c,d){var e,f,g=a.formatNumberString,h=b.split(".");if(h){if(1===h.length)return g(h[0],c.numbers,d?c.letters:m);if(2===h.length)return e=g(h[0],c.numbers,d?c.letters:m),f=g(h[1],c.numbers),e+"."+f}throw Error(l().Exp_ValueIllegal)},a.formatNumberString=function(b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t=b,u=0,v=0;if(2===arguments.length){for(f="",u=0;u<t.length;u++)e=aa(t,u,1),v=parseInt(e,10),f+=c[v];return f}if(3===arguments.length){if(!d)return a.formatNumberString(b,c);for(g=0,h="",i=t.length,j=!1,k=[],u=0;u<i;u++)l=d.length-1-u,l>-1?k.push(""+d[l]):k.push(M.Dc);for(m=[],n=k.length-1;n>=0;n--)m[k.length-n-1]=k[n];for(k=m,o=!1,n=0;n<i;n++)e=aa(t,n,1),v=parseInt(e,10),p=void 0,q=M.Dc,i-n-16>0?(p=c[v],q="",o=!0):n!==i-1&&n!==i-5&&n!==i-9&&n!==i-13?"0"===e?(p="",q="",g+=1):"0"!==e&&0!==g?(p=c[0]+c[v],q=k[n],g=0):(p=c[v],q=k[n],g=0):"0"!==e&&0!==g?(p=c[0]+c[v],q=k[n],g=0):"0"!==e&&0===g||o?(p=c[v],q=k[n],g=0,o=!1):"0"===e&&g>=3?(p="",q="",g+=1):i>=11?(p="",g+=1):(p="",q=k[n],g+=1),r=p+q===M.Dc,r||(j=!1),n!==i-13||j||(q=k[n],j=!0),n!==i-9||j||(q=k[n],j=!0),n===i-1&&(q=k[n],j=!0),h=h+p+q;return s=parseInt(b,10),0===s?c[0]:h}},a}(),S=function(){function a(a,b,c,d,e){ma.call(this,b,c,d);var f=this;f.Yc=f.Zc(ja(a)),f.$c=e,f._c=2,f.ad()}return a.prototype.ad=function(){var b,c,d,e=this,f={Qb:e.Yc},g=a;if(g.Lc(f.Qb)){if(b=e.bd(f),e.hasJD=e.Gb(f.Qb,p.dd,'"'+p.ed+'"',!0,!1,f,!1),e.Gb(f.Qb,p.fd,"MMMM",!0,!1,f,!1),e.Gb(f.Qb,p.gd,"MMM",!0,!1,f,!1),e.Gb(f.Qb,p.hd,"MM",!0,!1,f,!1),e.Gb(f.Qb,p.jd,"M",!0,!1,f,!1),e.Gb(f.Qb,p.kd,"ddd",!0,!0,f,!0),e.Gb(f.Qb,p.ld,"dddd",!0,!0,f,!0),b||(e.Gb(f.Qb,p.md,"H",!0,!0,f,!1),e.Gb(f.Qb,p.nd,"HH",!0,!0,f,!1)),e.Jc&&e.Ic&&(e.od=e.od||e.Gb(f.Qb,p.Qc,'"@'+p.Qc+'"',!0,!1,f,!0),e.od=e.od||e.Gb(f.Qb,p.Rc,'"@'+p.Rc+'"',!0,!1,f,!0)),e.$c)for(c=0;c<e.$c.length;c++)d=e.$c[c],e.Gb(f.Qb,d.Kc,"@"+d.Kc,!0,!0,f,!0);e.pd=f.Qb}else K()},a.prototype.formatString=function(){return this.Yc},a.prototype.format=function(a){var b,c,f,g,h,i,j=this,k=M.Dc,l=m;try{if(l=e.l.Yb(a),l&&E(l.valueOf())&&(l=m),l?a=l:k=""+a,l&&(k=e.l.mb(l,j.pd,j.Ic&&j.Ic.cultureInfo()||F(j.cultureName)),j.hasJD&&(b=F(j.cultureName).DateTimeFormat.monthNames[l.getMonth()],k=d.u.Gb(k,p.ed,aa(b,0,1))),j.$c))for(c=24*(e.l.Ra(l)-e.l.Ra(p.qd))*60*60*1e3,f=0;f<j.$c.length;f++)g=j.$c[f],h=g.Tc(c),D(h)||(i=g.Kc.replace("[","\\[").replace("]","\\]"),k=d.u.Gb(k,"@"+i,h))}catch(b){k=ba(a)}return sa(k,a,j.Ic,j.Jc,j.cultureName)},a.prototype.parse=function(a){var b,c,d,f,g;if(!a||a===M.Dc)return m;if(b=this,c=a,d=I(c),"true"===d)return!0;if("false"===d)return!1;if(b.pd&&(f=e.l.Wb(c,b.pd,F(b.cultureName))))return f;try{return g=e.l.Yb(c),g&&!E(g)?g:(g=new Date(c),E(g.valueOf())?c:g)}catch(a){return c}},a.prototype.Zc=function(a){var b,c,d,e=a,f="",g=!1;for(b=0;b<e.length;b++)c=e[b],'"'===c?g=!g:g||("Y"===c||"D"===c||"S"===c||"E"===c||"G"===c?c=I(c):"M"===c&&(d=e[b-1],ia("A",d,!0)||ia("P",d,!0)||(c=I(c)))),f+=c;return f},a.prototype.bd=function(a){var b,c=[p.rd,G(this.cultureName).amDesignator+"/"+G(this.cultureName).pmDesignator,p.sd],e=["tt","tt","t"],f=a.Qb;for(b=0;b<3;b++){if(d.u.Bb(f,e[b]))return!0;if(d.u.Bb(f,c[b],!0))return a.Qb=d.u.Gb(f,c[b],e[b],!0),!0}return!1},a.prototype.Gb=function(b,c,e,f,g,h,i){var j,k,l,m,n,o,p,q,r,s,t;if(f||g){for(j=[],k=!0,!a.hasDate(b)&&a.hasTime(b)&&(k=!1),l=!1,m=0;m<b.length;m++){if(n=b[m],a.hasTime(n)&&!l?k=!1:a.hasDate(n)&&!l&&(k=!0),f&&ia(n,c[0],i)&&k||g&&ia(n,c[0],i)&&!k){for(o=!0,p=0;p<c.length;p++)if(p+m>=b.length||!ia(c[p],b[p+m],i)){o=!1;break}if(q=m+c.length-1,o&&q+1<b.length){for(r=b[q],s=-1,s=q+1;s<b.length&&ia(r,b[s],i);s++);s>q+1&&(m=s,o=!1)}o&&!l&&j.splice(0,0,m)}'"'===n&&(l=!l)}if(h.Qb=b,j.length>0){for(m=0;m<j.length;m++)t=j[m],h.Qb=d.u.Fb(h.Qb,t,c.length),h.Qb=d.u.Eb(h.Qb,t,e);return!0}return!1}return!1},a.hasTime=function(a){return d.u.Cb(a,p.md[0],!0)>-1||d.u.Cb(a,p.td[0],!0)>-1},a.hasDate=function(a){return d.u.Cb(a,p.Rc[0],!0)>-1||d.u.Cb(a,p.ud[0],!0)>-1},a.Lc=function(a,b){return la(a,b||q)},a}(),b.lxb=S,T=function(){function a(a,b){var c,d,e;if(na.call(this,b),c=this,d=I(a)[0],"h"===d)e=3600;else if("m"===d)e=60;else{if("s"!==d)throw Error(l().Exp_TokenIllegal);e=1}c.Tc=function(a){var b=a/1e3/e;return Math.abs(b-Math.round(b))<1e-6?Math.round(b):Math.floor(b)}}return a.Name="ABSTimeFormatPart",a}(),U=function(){function a(a,b){na.call(this,b),this.foreColor=a}return a.prototype.toString=function(){return da(this.foreColor)},a.Name="ColorFormatPart",a}(),V=function(){function a(a,b,c,d){var e,f,g,h,i,j;function k(a,b){var c,d,e,f,g,h=[],i='"';if(a===m||""===a)return h;for(c=!1,d=[],e=!1,f=0;f<a.length;f++)g=a[f],g!==i||c||(e=!e),c||e||g!==b?d.push(g):(h.push(d.join("")),d=[]),c="\\"===g&&!c;return h.push(d.join("")),h}ma.call(this,b,c,d),e=this,e.Ad=!1,e._c=1,e.Bd=ea(a),f=ja(a),b&&(f=ga(f,e.Ic.Kc,e.Ic.currencySymbol())),f=ea(f),g=f.indexOf("/"),g>-1&&(h=k(f,"/"),h&&2===h.length&&(e.Cd=h[1],i=h[0],i&&(j=i.lastIndexOf(" "),j>-1?(e.Dd=aa(i,0,j),e.Ed=aa(i,j+1,i.length-j-1)):e.Ed=i))),e.Fd=f}return a.prototype.formatString=function(){return this.Bd},a.prototype.format=function(b){var c,d,e,g,h,i,j,k,l,n,o,p,q,r,s,t,u,v,x,y,z,C;return w(b,"boolean")?(""+b).toUpperCase():(c=this,d=f.j.Pa(b),E(d)||!isFinite(d)||E(b)?"string"==typeof b?b:m:(e=F(c.cultureName),c.Ed&&c.Cd?(n={value:0},o={value:0},p={value:0},q=c.bPa(),a.getFraction(d,q,n,o,p)?(r=a.getGCD(o.value,p.value),r>1&&(o.value/=r,p.value/=r),c.Dd?(l="",1===p.value&&(n.value+=o.value,o.value=0,p.value=0),s=A(B(n.value,c.Dd,e)),s&&"0"!==s&&(l+=s,l+=" "),0===n.value&&d<0&&(l+=e.NumberFormat.negativeSign),0===d&&(l+="0"),h=c.Cd,i=parseFloat(h),!E(i)&&i>0&&(0!==o.value&&0!==p.value&&(o.value*=i/p.value),p.value=i,h=h.replace(/^\d+/,a.toNumberPlaceholder(i)),j=Math.ceil(o.value),k=j-o.value,k<=.5&&k>=0?o.value=parseFloat(""+j):o.value=parseFloat(""+(j-1))),t=c.Ed,u=parseFloat(t),E(u)||0!==u||(v=t.length,x=""+o.value,y=x.length,v>y?t=t.substr(0,v-(v-y)):v<y&&(x=x.substr(0,y-(y-v)),o.value=parseInt(x,10))),z=c.Mja(o.value,p.value,t,h,e),0===o.value&&(z=z.replace(/./g," "),""===s)?"0 "+z:(l+=z,""===l?"0":l)):(l="",C=n.value*p.value+o.value,h=c.Cd,i=parseFloat(h),i>0?(C*=i/p.value,p.value=i,j=Math.ceil(C),k=j-C,C=k<=.5&&k>=0?parseFloat(""+j):parseFloat(""+(j-1)),l+=C+"/"+p.value):l+=c.Mja(C,p.value,c.Ed,c.Cd,e),0===C?"0":l)):""+d):(g=B(d,c.Fd,e),g=ta(g,c.Ad,c.Ic,c.Jc,c.cultureName))))},a.prototype.parse=function(a){var b,c,e,f,g,h,i,j,k,l,n=this;return a&&a!==M.Dc?(b=J(a),!!("TRUE"===b||b===L()&&L().boolean_true)||!("FALSE"===b||b===L()&&L().boolean_false)&&(c=n.Ed&&n.Cd,e=n.Jd(n.Fd).Kd,(c||e)&&(f=a.indexOf("/"),0<f&&f<a.length-1&&(g=parseInt(a.substr(0,f),10),h=parseInt(a.substr(f+1),10),!isNaN(g)&&!isNaN(h)&&0!==h))?g/h:(a=n.Hd(a),a=n.Id(a),i=n.Jd(a),j=i.Kd,k=i.Ld,n.jBb(k)&&(k=d.u.Hb(k,H(n.cultureName).numberGroupSeparator,"",!1),l=y.ob(k,n.cultureName),!E(l)&&isFinite(l))?(j&&(l=C(l/100,15)),l):m))):m},a.prototype.bPa=function(){var a,b,c=this,d=c.Cd,e=0;if(d)for(a=d.length,b=0;b<a&&d[b].match(/[#?0\d]/);b++)e++;return e},a.prototype.jBb=function(a){var b,c="",d=0,e=0,f=0,g=m,h=H(this.cultureName);for(b=a.length-1;b>-1;b--){if(a[b]===h.numberDecimalSeparator)d++,f>0&&(g=!1),c="";else if("e"===I(a[b])){if(e++,c="",b===a.length-1)return!1}else if(a[b]===h.numberGroupSeparator)g=3===c.length,f++,c="";else if("-"===a[b]||"+"===a[b]){if(b>0&&"e"!==I(a[b-1]))return!1}else{if(!N.Fc(a[b]))return!1;if(c+=a[b],0===b&&0===parseInt(c,10)&&g)return!1}if(d>1||e>1||g===!1)return!1}return!0},a.prototype.Hd=function(a){var b,c,e,f,g,h,i,j=a,k=[];for(b=0;b<j.length;b++)N.Fc(j[b])&&k.push(b);for(c=H(this.cultureName),e=[c.currencyDecimalSeparator,c.currencyGroupSeparator,c.currencySymbol,c.nanSymbol,c.negativeInfinitySymbol,c.negativeSign,c.numberDecimalSeparator,c.numberGroupSeparator,c.percentDecimalSeparator,c.percentGroupSeparator,c.percentSymbol,c.perMilleSymbol,c.positiveInfinitySymbol,c.positiveSign],f=j.length-1;f>-1;f--)if(g=j[f],!N.Gc(g)||e.indexOf(""+g)>-1){if(("-"===g||"+"===g)&&(h=f>0?""+j[f-1]:m,i=["e","E","(",H(this.cultureName).currencySymbol],i.indexOf(h)<0))break}else(f<k[0]||k[k.length-1]<f)&&(j=d.u.Fb(j,f,1));return j},a.prototype.Id=function(a){var b=H(this.cultureName).currencySymbol,c=d.u.kb(a,b)?d.u.Fb(a,0,b.length):a;return c.indexOf(b)<0?c:a},a.prototype.Jd=function(a){var b=H(this.cultureName).percentSymbol,c=!0,e=a;return d.u.kb(a,b)?e=d.u.Fb(a,0,b.length):d.u.jb(a,b)?e=d.u.Fb(a,a.length-b.length,b.length):c=!1,d.u.Bb(e,b)&&(c=!1),{Ld:e,Kd:c}},a.prototype.Mja=function(a,b,c,d,e){var f=A(B(a,c,e)),g="0."+b,h="#."+d,i=A(B(g,h,e,!0)),j=f+"/"+i.substr(i.indexOf(".")+1);return""===c&&(j=a+j),""===d&&(j+=b),j},a.getGCD=function(a,b){var c,d,e;if(0===a)return o(b);if(0===b)return o(a);for(c=Math.max(a,b),d=Math.min(a,b),e=c%d;0!==e;)c=d,d=e,e=c%d;return o(d)},a.getFraction=function(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,p=0,q=0,r=0,s=0,t=Math.ceil;for(a>0?(s=a-t(a)+1,1===s?(s=0,p=a):p=t(a)-1):a<0&&(p=t(a),s=t(a)-a),f=Math.pow(10,b-1),g=Math.pow(10,b)-1,f<2&&(f=2),g<2&&(g=2),h=!1,i=0,j=f;j<=g&&(k=j*s,l=Math.round(k),m=l/j,n=o(m-s),!((!h||n<o(i-s))&&(h=!0,i=m,q=l,r=j,n<1e-5)));j++);return c.value=p,d.value=q,e.value=r,h},a.toNumberPlaceholder=function(a){return(""+a).replace(/\d/g,"?")},a}(),W=function(){function a(b,c,d,e){ma.call(this,c,d,e),arguments.length>0?(a.kBb(b)||K(),this.Bd=b):this.Bd="General",this._c=0}return a.kBb=function(a){var b,c,d="",e=!1,f=-1,g=a.length;for(b=0;b<g;b++)c=a[b],'"'===c?(e=!e,f=e?b:-1):e||(d+=c);return f>0&&(d+=a.substring(f,g)),!(d.indexOf("0")>=0||d.indexOf("#")>=0||d.indexOf(".")>=0||d.indexOf("@")>=0)},a.prototype.Nd=function(){var a,b=this;return b.Od||(a=b.Bd,a=ga(a,"General",z),b.Od=new V(a,b.Ic,b.Jc,b.cultureName),b.Od.Ad=!0),b.Od},a.prototype.Pd=function(){var a=this;return a.Qd||(a.Qd=new V("0.#####E+00",a.Ic,a.Jc,a.cultureName),a.Qd.Ad=!0),a.Qd},a.prototype.formatString=function(){return d.u.Gb(this.Bd,"@NumberFormat","General")},a.prototype.format=function(a){var b,c,e,g=this;if(f.j.Na(a,g.cultureName)){if(b=!g.Ic||g.Ic.allowScience(),c=f.j.Pa(a),c!==n&&c!==m)return o(c)>99999999999&&b||o(c)<1e-11&&0!==c?g.Pd().format(a):g.Nd().format(a)}else{if(w(a,"string"))return e=d.u.Gb(g.formatString(),'"',""),e=ha(e),e?a:a;if(w(a,"boolean"))return(""+a).toUpperCase()}return""},a.prototype.parse=function(a){var b,c,f,g,h,i,j;return M.Ec(a)?m:"number"==typeof a?a:(b=!1,c=d.u.Cb(a,"-",!1),c>0&&!ia(a.charAt(c-1),"E",!0)&&(b=!0),(d.u.Bb(a,"/",!1)||b||d.u.Bb(a,":",!1)||d.u.Bb(a,"-",!1))&&(f=e.l.Qa(a))?f:(h="-"===a.charAt(0),i=h?d.u.Fb(a,0,1):a,j="("===i.charAt(0)&&")"===a.charAt(a.length-1),j&&(i=i.substring(1,i.length-1)),g=this.Nd().parse(i),g!==m&&g!==n?(j||h)&&w(g,"number")?-1*o(g):g:a))},a}(),X=function(){function a(a,b,c,d){var e,f=this;ma.call(f,b,c,d),e=ja(a,!1),b&&(e=ga(e,f.Ic.Kc,f.Ic.currencySymbol())),e=ea(e),e=ha(e),f.Yc=e,f._c=3}return a.prototype.format=function(a){var b,c,d,f=this;try{return b=void 0,c=void 0,c=a instanceof Date?""+e.l.Ra(a):ba(a),d=f.Kja(f.Yc),b=f.Lja(c,d)}catch(a){return""}},a.prototype.parse=function(a){return a?a:""},a.prototype.formatString=function(){return this.Yc},a.prototype.Kja=function(a){var b,c,d="",e=!1,f=m,g=[];for(c=0;c<a.length;c++)b=a.charAt(c),e?('"'!==b?d+=b:(d+=b,g.push(d),d="",e=!1),f=b):"*"!==f&&"_"!==f&&"\\"!==f||""===d?"*"!==b&&"_"!==b&&"\\"!==b?"@"!==b?'"'!==b?(d+=b,f=b):(f=b,""!==d&&g.push(d),d=b,e=!0):(f=b,""!==d&&(g.push(d),d=""),g.push(b)):(f=b,""!==d&&(g.push(d),d=""),d+=b):(d+=b,g.push(d),d="");return""!==d&&g.push(d),g},a.prototype.Lja=function(a,b){var c,d,e=[],f=!1;for(c=b.length-1;c>=0;c--)d=b[c],"*"===d[0]?f||(f=!0,e.push({type:"fillingChar",value:d[1]})):"_"===d[0]?e.push({type:"placeholder",value:d[1]}):'"'===d[0]&&'"'===d[d.length-1]?d.length>2&&e.push({type:"text",value:d.substr(1,d.length-2)}):"@"===d[0]?e.push({type:"text",value:a}):e.push({type:"text",value:d});return e.reverse()},a}();function aa(a,b,c){return a.substr(b,c)}function ba(a){return f.j.Fa(a)?"":"boolean"==typeof a?a?"TRUE":"FALSE":"string"==typeof a?a:""+a}function ca(a,b){if("\\"===a[b])throw Error(l().Exp_InvalidBackslash);if(b-1>0&&b-1<a.length&&"\\"===a[b-1]){if(b-2<0)return!0;if(b-2>0&&b-2<a.length)return"\\"!==a[b-2]}return!1}function da(a){if(!a)throw Error(l().Exp_TokenIsNull);return"["+a+"]"}function ea(a){var b,c,d,e,f;if(a===n||a===m||""===a)return a;for(b="",c=0,d=!1,e=0;e<a.length;e++)f=a[e],'"'===f?(d=!d,b+=f):d?b+=f:"["===f?c++:"]"===f?(c--,c<0&&(c=0)):0===c&&(b+=f);return""+b}function fa(a){return a=ga(a,"[",""),ga(a,"]","")}function ga(a,b,c){return!a||a===M.Dc||ia(b,c,!0)?a:(b=b.replace(u,"\\$1"),a.replace(RegExp(b,"g"),c))}function ha(a){var b,c,d=a.length,e=!1,f="";for(b=0;b<d;b++)c=a.charAt(b),"\\"===c?(e=!e,e||(f+=c)):(e=!1,f+=c);return f}function ia(a,b,c){return c?I(a)===I(b):a===b}function ja(a,b){var c,d,e,f;for(1===arguments.length&&(b=!0),c=!1,d="",e=0;e<a.length;e++){if(f=a[e],'"'===f)c=!c;else if(!c&&!b&&"/"===f&&!ca(a,e))continue;d+=f}return d}function ka(a){var b,c,d,e="",f=!1;for(b=0,c=a.length;b<c;b++)d=a[b],'"'===d&&(f=!f),"E"===d||f||(e+=I(d));return e}function la(a,b){var c,d,e,f;if(!a||a===M.Dc)return!1;for(c=ja(a),d=ka(c),e=0;e<b.length;e++){if(f=d.indexOf(b[e]),0===f)return!0;if(f>0&&"_"!==d[f-1]&&"*"!==d[f-1])return!0}return!1}Y=function(){return{pattern:v,formatter:["d-mmm","mmm-yy",G().shortDatePattern,"h:mm","h:mm:ss","h:mm:ss.0",G().shortDatePattern+" h:mm",G().shortDatePattern+" h:mm:ss",G().shortDatePattern+" h:mm:ss.0"]}};function ma(a,b,c){this.Ic=a,this.Jc=b,this.cultureName=c}function na(a){this.Kc=a}function oa(a){var b,c,e=fa(a);return e&&e!==M.Dc?(c=e[0],["<",">","="].indexOf(c)>-1?(b=Q,b.Name="ConditionalFormatPart"):d.u.kb(e,"DBNum",!0)?(b=R,b.Name="DBNumberFormatPart"):ia(e[0],"$",!1)&&e.indexOf("-")>-1?(b=O,b.Name="LocaleIDFormatPart"):qa(e)?(b=T,b.Name="ABSTimeFormatPart"):e.indexOf("$")<0&&e.length>=3&&(b=U,b.Name="ColorFormatPart"),b):b}function pa(a,b){var c,d=!1;return a=a?""+a:M.Dc,la(a,["general"])?c=W:S.Lc(a)?c=S:la(a,["E+","E-","#",".",",","%","0","/","?"])||b?(d=!0,c=V):(d=!0,c=X),{Mc:d,Nc:c}}function qa(a){var b,c=I(a),d=c[0];if("h"!==d&&"m"!==d&&"s"!==d)return!1;for(b=1;b<c.length;b++)if(d!==c[b])return!1;return!0}function ra(a,b,c){var d,e;return d=a?a.cultureInfo():F(c),e=d.NumberFormat.dbNumber,e?e[b]:m}function sa(a,b,c,d,f){var g,h=a;return!D(d)&&b instanceof Date&&(g=ra(c,d.type,f),h=d.Pc(h,g,!0),1===d.type&&(h=h.replace(RegExp(r,"g"),s)),h=h.replace("@"+p.Qc,e.l.Vb(b,p.Qc)),h=h.replace("@"+p.Rc,e.l.Vb(b,p.Rc)),h=d.Pc(h,g,!1)),h}function ta(a,b,c,d,e){var f,g,h,i,j,k,l;if(!D(d)&&(f=ra(c,d.type,e),!D(f))){for(g=!1,h=void 0,i=F(e),j=void 0,k=0,l=a.length;k<l;k++)"decimalSeparator"===a[k].type&&(g=!0,a[k].value=d.Pc(a[k].value,f,b),h=a[k].value),"number"===a[k].type&&(g?a[k].value=d.Pc(h+a[k].value,f,b).slice(1):a[k].value=d.Pc(a[k].value,f,b),"ja-JP"===i.name()&&a[k].value.length>1&&("\u4e00\u5343"===(j=a[k].value.substr(0,2))||"\u4e00\u767e"===j||"\u4e00\u5341"===j)&&(a[k].value=a[k].value.substr(1)));return a}return a}Z=function(){function a(a,b){var c=this;0===arguments.length?(c.formatCached="General",c.Rd=new W):c.ad(a,b)}return a.prototype.ad=function(b,c){var d,e,f,g,h,i,j,k,l,o,p,q,r,s,t,u;for(b!==m&&b!==n||K(),d=b,e=this,e.formatCached=b,f="",g="",h=!1,i=!1,j=[],k=0;k<b.length;k++)l=b[k],'"'===l?(i=!i,g+=l):i?g+=l:"["===l?(h&&K(),g&&(f||(f=""),f+=g),g=""+l,h=!0):"]"===l?(h||K(),g?(g+=l,o=""+g,p=oa(""+g),p?"ABSTimeFormatPart"===p.Name?(j.push(new p(a.preProcessPart(o),o)),f+=g):e.addPart(p,o):(q=a.partToNormalStr(o),f+=q,d=ga(b,o,q)),g=""):K(),h=!1):g+=l;g?h?K():f+=g:f||(f=e.o$a()),e.localeIDFormatPart&&(c=e.localeIDFormatPart.cultureInfo().name()),r=e.conditionalFormatPart,s=pa(f,r),t=s.Nc,u=s.Mc?d:f,t?e.Rd=new t(u,e.localeIDFormatPart,e.dbNumberFormatPart,c,j.length>0?j:m):K()},a.prototype.o$a=function(){var b,c=this,d="";return c.dbNumberFormatPart&&(b=a.proNames.some(function(a){return"dbNumberFormatPart"!==a&&c[a]}),b||(d="General")),d},a.prototype.formatString=function(){var b,c,d,e=this,f="";for(b=0,c=a.proNames.length;b<c;b++)d=a.proNames[b],e[d]&&(f+=""+e[d]);return f+=e.Rd.formatString()},a.prototype.addPart=function(b,c){var d,e,f,g=this,h=a.proNames,i=a.preProcessPart(c);for(d=0,e=h.length;d<e;d++)if(f=h[d],ia(f,b.Name,!0)){if(g[f])throw Error(l().Exp_DuplicatedDescriptor);g[f]=new b(i,c)}},a.prototype.format=function(a){return this.Rd.format(a)},a.prototype.parse=function(a){return this.Rd.parse(a)},a.checkFormatter=function(a){if(!a||a===M.Dc)throw Error(l().Exp_TokenIllegal)},a.preProcessPart=function(b){a.checkFormatter(b);var c=fa(b);return a.checkFormatter(c),c},a.partToNormalStr=function(b){var c=a.preProcessPart(b);return"$"===c[0]&&(c=c.slice(1)),'"'+c+'"'},a.proNames=["colorFormatPart","conditionalFormatPart","dbNumberFormatPart","localeIDFormatPart"],a}(),$=function(a){_(b,a);function b(b,c){var d=a.call(this,b,c)||this,e=d;return e.Sd=!0,e.PropertyChanged=[],M.Ec(b)&&(b="General"),e.formatCached=b,e.cultureName=c,e.init(),d}return b.prototype.toJSON=function(){var a=this,b={formatCached:a.formatCached};return a.cultureName&&(b.customerCultureName=a.cultureName),"general"===I(a.formatCached)&&delete b.formatCached,b},b.prototype.hasFormatedColor=function(){var a,b,c=this;for(a in t)if(t.hasOwnProperty(a)&&(b=c.getFormatter(t[a]),b&&b.colorFormatPart))return!0;return!1},b.prototype.formatString=function(a){var b,c,d,e,f=this;if(0===arguments.length){for(b=M.Dc,c=0,d=f.formatters.length;c<d;c++)e=f.formatters[c],b+=e.formatString(),c!==d-1&&(b+=";");return b}if(!a)throw Error(l().Exp_ValueIsNull);return f.formatters=m,f.formatCached=a,f.init(),f.Td("formatString"),f},b.prototype.getFormatter=function(a){var b=this;return b.formatters&&b.formatters[a]},b.prototype.getPreferredEditingFormatter=function(a){var c,d,e=G(this.cultureName),g=b.defFormatter;return w(a,"TimeSpan")?g(e.longTimePattern):w(a,"DateTime")?(c=0===a.getHours()&&0===a.getMinutes()&&0===a.getSeconds()&&0===a.getMilliseconds()?"":" h:mm:ss",g(e.shortDatePattern+c)):f.j.Na(a,this.cultureName)?(d=f.j.Pa(a),g(d>=1e20||d<=1e-17&&d>0||d<=-1e20||d<0&&d>=-1e-17?"0.##E+00":z)):g("General")},b.prototype.getPreferredDisplayFormatter=function(a,c){var g,h,i,j,k,l,n,o,p,q,r,s,t,u,v,x,y,z,A,B=this,C=b.defFormatter;if(c||(c={value:m}),c.value=m,M.Ec(a))return new b;if(g=a,h=c.value=B.parse(g),w(h,"DateTime")||w(h,"TimeSpan")){for(i=Y(),j=i.pattern,k=i.formatter,l=0;l<j.length;l++)for(n=j[l],o=0;o<n.length;o++)if(p=n[o],q=e.l.Qa(a,p),q&&q-h===0)return new b(k[l])}else if(f.j.Na(h,B.cultureName)){if(r=H(B.cultureName),s=r.currencySymbol,t=r.numberDecimalSeparator,u=r.percentSymbol,v=r.numberGroupSeparator,x="E",y=d.u.Bb,z=y(g,t),A=z?".00":"",g[0]===s)return C(M.Kb("{0}#,##0{1};[Red]({0}#,##0{1})",s,A));if(y(g,x,!0))return C("0.00E+00");if(""+g[0]===u||""+g[g.length-1]===u)return C(M.Kb("0{0}%",A));if(y(g,v))return C(M.Kb("#,##0{0}",A))}return C("General")},b.prototype.format=function(a,b){var c,d,e,g,h;if(w(a,"boolean"))return L()&&(a=a?L().boolean_true:L().boolean_false),(""+a).toUpperCase();if(d=0,e=this.Ud(a)){g=e.colorFormatPart,b&&g&&(b.conditionalForeColor=b.value=g.foreColor),c=f.j.Na(a,this.cultureName),c&&(d=f.j.Pa(a)),h=M.Dc;try{c&&e===this.getFormatter(t.Va)?h=e.format(e.considerNegSymbol?d:o(d)):(h=e.value,h===n&&(h=e.format(a))),b&&(b.content=x(h)?h:[{type:"text",value:h}]),x(h)&&(h=y.Jja(h))}catch(b){w(a,"string")&&(h=""+a)}return h}return c&&d<0?"-":w(a,"string")?""+a:a===n||a===m?M.Dc:""+a},b.prototype.parse=function(a){var b=this;return b.formatters&&b.formatters.length>0?b.formatters[0].parse(a):m},b.prototype.init=function(){var a,b,c,d=this,e=d.formatCached;for(M.Ec(e)&&K(),d.formatters=[],a=e.split(";"),d.Sd=1===a.length,(!a||a.length<1||a.length>4)&&K(),b=0;b<a.length;b++)c=new Z(a[b],d.cultureName),d.formatters.push(c);d.getFormatter(t.Hc)||K()},b.prototype.Ud=function(a){var b,c,d,e,g,h,i,j,k=this,l=k.getFormatter(t.Hc),n=l&&l.conditionalFormatPart,o=k.getFormatter(t._text);if("string"==typeof a&&E(a))return o?o:n?a:l;if(f.j.Na(a,k.cultureName)||w(a,"boolean")){if(b=k.getFormatter(t.Va),c=k.getFormatter(t.Ua),d=f.j.Pa(a),e=n&&l.conditionalFormatPart.isMeetCondition(d),g=b&&b.conditionalFormatPart,h=g&&b.conditionalFormatPart.isMeetCondition(d),i=void 0,k.Sd||(n?e:d>0||0===d&&!c))i=l;else if(g?h:d<0)i=b;else if(c)i=c;else if(b){if(n&&g)return j=d+"",{value:j};i=b}return i&&(!n||g||i!==b||c||(i.considerNegSymbol=!0),n&&g&&i===b&&b.conditionalFormatPart&&b.conditionalFormatPart.isMeetCondition(1e-10)&&(i.considerNegSymbol=!0)),i}return m},b.prototype.Td=function(a){var b,c,d=this;if(d.PropertyChanged)for(b=0;b<d.PropertyChanged.length;b++)c=d.PropertyChanged[b],"function"==typeof c&&c(d,a)},b.defFormatter=function(a){var c=b[a];return c||(c=new b(a),b[a]=c),c},b}(P),b.GeneralFormatter=$},"./src/plugins/slicer/slicer.entry.ts":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./src/plugins/slicer/slicer.ts"))},"./src/plugins/slicer/slicer.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n;Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/datetimehelper.ts"),e=void 0,f=null,g="number",h="string",i="boolean",j=d.l.Ra,function(a){a[a.all=0]="all",a[a.byCurrentColumn=1]="byCurrentColumn",a[a.byOtherColumns=2]="byOtherColumns"}(k=b.FilteredOutDataType||(b.FilteredOutDataType={})),function(a){a[a.average=1]="average",a[a.count=2]="count",a[a.counta=3]="counta",a[a.max=4]="max",a[a.min=5]="min",a[a.product=6]="product",a[a.stdev=7]="stdev",a[a.stdevp=8]="stdevp",a[a.sum=9]="sum",a[a.vars=10]="vars",a[a.varp=11]="varp"}(l=b.SlicerAggregateType||(b.SlicerAggregateType={}));function o(a){return a&&a.text!==e}m=function(){function a(){}return a.Ra=function(a){return j(a)},a.quickSort=function(b){var c,d=D(b),e=[];for(c=0;c<d;c++)e[c]={index:c,value:b[c]};return a.quickSortImp(e)},a.quickSortImp=function(b){var c,d,e,f,g,h,i,j;if(D(b)<=1)return b;for(c=a,d=Math.floor(D(b)/2),e=b[d],f=[],g=[],h=[],i=0;i<D(b);i++)j=c.sortCompare(b[i].value,e.value),j<0?f.push(b[i]):j>0?g.push(b[i]):h.push(b[i]);return c.quickSortImp(f).concat(h,c.quickSortImp(g))},a.isEquals=function(a,b){return!(!B(a)&&""!==a||!B(b)&&""!==b)||(a instanceof Date&&b instanceof Date?a.valueOf()===b.valueOf():C(a)===h&&C(b)===h?a.toLowerCase()===b.toLowerCase():a===b)},a.isGreaterThan=function(b,c){var d=C(b),e=C(c);return d===i?b=b?1:0:b instanceof Date&&(b=a.Ra(b)),e===i?c=c?1:0:c instanceof Date&&(c=a.Ra(c)),d===e||d!==g&&e!==g?d===h&&e===h?b.toLowerCase()>c.toLowerCase():b>c:C(c)===g},a.sortCompare=function(b,c){var d=a,e=0,f=B(b)||""===b||C(b)===g&&isNaN(b),h=B(c)||""===c||C(c)===g&&isNaN(c);return e=f&&h?0:f?1:h?-1:d.isEquals(b,c)?0:d.isGreaterThan(b,c)?1:-1},a}(),b.jf=m;function p(a,b){var c,d,j;if(a===f||a===e)return!1;if(C(a)===g)b.value=a;else if(C(a)===i)b.value=a?1:0;else if(a instanceof Date)b.value=m.Ra(a);else{if(C(a)!==h)return!1;if(a=a.trim(),0===D(a))return!1;if(c=!1,"%"===a.charAt(D(a)-1)&&(c=!0,a=a.substr(0,D(a)-1)),D(a)>=2&&"0"===a[0]&&"x"===a[1])return!1;if(d=(+a).valueOf(),isNaN(d)||!isFinite(d)){if(j=new Date(a),isNaN(j))return!1;d=m.Ra(j)}c&&(d/=100),b.value=d}return!0}function q(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o=a.Ce;for(o||(a.Ee=[],a.Ie=[]),b=o?a.De:a.Ee,c=a.Fe,d=a.Ge,e=o?a.He:a.Ie,f=a.Je,g=a.Ke,h=a.Le,i=D(a.data),k=0;k<i;k++){for(l=!1,m=void 0,n=0;n<D(f)&&(m=f[n],o&&m===a.Me||(d[m]?(j=a.getExclusiveRowIndex(a.columnNames[m],k),l=!d[m][j]):c[m]&&(l=!c[m][k]),!l));n++);!l&&o&&(g?(j=a.getExclusiveRowIndex(a.columnNames[m],k),l=!g[j]):h&&(l=!h[k])),l||(b[k]=!0)}for(n=0;n<D(b);n++)b[n]&&e.push(n)}function r(a){return"string"==typeof a?a.toLowerCase():a instanceof Date?m.Ra(a):a}function s(a){var b,c,d=a.Ne={};for(a.Oe=[],a.Pe=[],a.Qe=[],a.Re=[],b=a.columnNames,c=0;c<D(b);c++)d[(b[c]+"").toUpperCase()]=c}function t(a,b){var c,d,e,f,g,h,i,j=a.Oe,k=a.Pe,l=a.Se,m=a.Qe,n=a.Re,p=a.data,q=n[b]=[];for(a._e=[],j[b]=[],k[b]=[],l[b]={},m[b]=[],c={},d=0;d<D(p);d++)e=p[d][b],f=o(e)?e.text.trim():e,j[b].push(f),g=c[r(f)],g?(g.list.push(d),q[d]=g.index):(h=[d],k[b].push(f),m[b].push(h),i=D(m[b])-1,l[b][f]=i,q[d]=i,c[r(f)]={list:h,index:i})}function u(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,p,q=a.Te,r=a.getColumnIndex(b),s=[];if(!r)return s;if(d=a.Ce?a.Ue:a.Ve[r],e=a.Ee,!d)return s;for(q[r]||a.We(r),f=[],g=q[r],h=0;h<D(g);h++)i=a.data[h][r],j=o(i)?i.value:i,f[g[h]]=j;for(k=a.af[r],h=0;h<D(d);h++){for(l=d[h],m=!1,n=a.Xe(f,l),p=n.start;p<=n.end;p++)if(e[k[p]]){m=!0;break}m!==c&&s.push(l)}return s}function v(a,b){var c,d,e,f,g=[],h=a.getColumnIndex(b);if(h>=0){for(c=D(a.getData(b)),d=a.Ce?a.De:a.Ee,e=0;e<c;e++)d[e]||(f=a.getExclusiveRowIndex(b,e),g.indexOf(f)===-1&&g.push(f));return g}}function w(a,b){var c,d,e,f,g,h,i,j=[],k={},l=a.getColumnIndex(b);if(l>=0)for(c=a.Ke||a.Le,d=a.Ge[l]||a.Fe[l],e=a.Ce&&l===a.Me?c:d,f=!!a.Fe[l],g=D(f?a.getData(b):a.getExclusiveData(b)),h=0;h<g;h++)e&&!e[h]&&(i=f?a.getExclusiveRowIndex(b,h):h,k[i]||(k[i]=!0,j.push(i)));return j}function x(a,b){var c,d,e,f,g,h,i,j,k,l,m={},n=a.columnNames;for(c=0,d=D(n);c<d;c++)if(n[c]!==b)for(e=w(a,n[c]),f=0;f<D(e);f++)for(g=a.getRowIndexes(n[c],e[f]),h=0;h<D(g);h++)m[g[h]]||(m[g[h]]=!0);for(i={},c=0,d=D(a.data);c<d;c++)j=a.getExclusiveRowIndex(b,c),m[c]||(i[j]=!0);for(k=D(a.getExclusiveData(b)),l=[],c=0;c<k;c++)i[c]||l.push(c);return l}function y(a,b,c,d){var e,f,g,h,i,j,k,l,m=a.getColumnIndex(b),n=a.Te;for(n[m]||a.We(m),e=[],f=n[m],g=0;g<D(f);g++)h=a.data[g][m],i=o(h)?h.value:h,e[f[g]]=i;for(j=[],g=0;g<D(c);g++)for(k=a.Xe(e,c[g]),l=0;l<D(f);l++)f[l]>=k.start&&f[l]<=k.end&&(j[l]=!0);d?(a.Le=j,a.Ue=c):(a.Ve[m]=c,a.Fe[m]=j,delete a.Ge[m],delete a.Ye[m]),q(a)}function z(a,b,c,d){var e,f=a.getColumnIndex(b),g=[];for(e=0;e<D(c);e++)g[c[e]]=!0;d?a.Ke=g:(a.Ge[f]=g,a.Ye[f]=c,delete a.Ve[f],delete a.Fe[f]),q(a)}function A(a,b){a.Ce=!1,a.Ke=[],a.He=[],a.De=[],a.Le=[],b&&a.onFiltered()}n=function(){function a(a,b){var c,d=this;for(d.Ne={},d.Oe=[],d.Pe=[],d.Se=[],d.Qe=[],d.Re=[],d.Ge=[],d.Ye=[],d.Fe=[],d.Ve=[],d.Ee=[],d.Ie=[],d.Je=[],d.He=[],d.De=[],d.Ce=!1,d.Ze=[],d.$e=0,d._e=[],d.af=[],d.Te=[],d.bf(a,b),c=0;c<D(d.data);c++)d.Ee[c]=!0,d.Ie.push(c)}return a.prototype.inPreview=function(){return this.Ce},a.prototype.bf=function(a,b){var c=this;c.data=a,c.columnNames=b,s(c)},a.prototype.onDataChanged=function(a){var b,c,d,e,f,g,h,i,j,k,l=this,m=Array(D(l.columnNames)),n=l.Ye,p=l.Pe,r=l.Ge,s=l.Oe;for(b=0;b<D(a);b++)c=a[b],d=c.data,e=c.row,f=l.getColumnIndex(c.columnName),l.data[e][f]=d,g=o(d)?d.text:d,s[f]&&(s[f][e]=g),m[f]=!0;for(h=0;h<D(m);h++)if(m[h]){for(i=n[h],n[h]=i?[]:void 0,r[h]=i?[]:void 0,j=[],b=0;i&&b<D(i);b++)j.push(p[h][i[b]]);for(t(l,h),b=0;b<D(j);b++)k=l.Se[h][j[b]],n[h].push(k),r[h][k]=!0}q(l),l.cf("onDataChanged",a)},a.prototype.cf=function(a){var b,c,d,e,f=[];for(b=1;b<arguments.length;b++)f[b-1]=arguments[b];for(c=this.Ze||[],d=0;d<D(c);d++)e=c[d],e[a]&&e[a].apply(e,f)},a.prototype.onColumnNameChanged=function(a,b){var c=this,d=c.Ne,e=c.getColumnIndex(a);e<0||(c.columnNames[e]=b,delete d[a.toUpperCase()],d[b.toUpperCase()]=e,c.cf("onColumnNameChanged",a,b))},a.prototype.onRowsAdded=function(a,b,c){var d,g,h,i,j,k,l,n,o,p,q,r,s,t=this,u=D(t.columnNames),v=t.data,w=t.Oe,x=t.Pe,y=t.Qe,z=t.Re,A=t.Ge,B=t.Ye,C=t.Ee,E=t.Ie,F=t.Je;for(t._e=[],d=0;d<b;d++)v.splice(a,0,Array(u));for(g=0;g<u;g++){for(d=0;d<b;d++)w[g]&&w[g].splice(a,0,e);if(i=x[g],j=void 0,i){for(k=f,l=0;l<D(i);l++){for(n=y[g][l],o=0;o<D(n);o++)n[o]>=a&&(n[o]+=b);m.isEquals(i[l],f)&&(k=n,j=l)}for(k||(k=[],i.push(f),j=D(i)-1,t.Se[g][f]=j,y[g].push(k),A[g]&&F.indexOf(g)===-1&&(A[g][j]=!0,B[g].push(j))),p=a;p<a+b;p++)k.push(p);for(h=a;h<a+b;h++)z[g].splice(h,0,j)}}for(q=!0,d=0;d<D(F);d++)g=F[d],r=z[g][a],A[g][r]!==!0&&(q=!1);for(d=0;d<D(E);d++)E[d]>=a&&(E[d]+=b);for(h=D(v)-1;h>=a+b;h--)C[h]=C[h-b];for(s=D(F)>0,h=a;h<a+b;h++)q&&E.push(h),C[h]=!s;c||t.cf("onRowsChanged",a,b,!0)},a.prototype.onRowsRemoved=function(a,b){this.df(a,b,!0)},a.prototype.df=function(a,b,c){var d,e,f,g,h,i,j,k,l=this,m=l.Ye,n=l.Pe,o=l.Ge,p=l.data,r=D(l.columnNames),s=l.Oe;for(p.splice(a,b),e=0;e<r;e++){if(s[e]&&s[e].splice(a,b),f=m[e],g=[],f&&c){for(d=0;d<D(f);d++){for(h=l.getRowIndexes(l.columnNames[e],f[d]),i=!0,j=0;j<D(h);j++)if(h[j]<a||h[j]>=a+b){i=!1;break}i||g.push(n[e][f[d]])}m[e]=[],o[e]=[]}if(t(l,e),f&&c)for(d=0;d<D(g);d++)k=l.Se[e][g[d]],m[e].push(k),o[e][k]=!0}c&&q(l),l.cf("onRowsChanged",a,b,!1)},a.prototype.onColumnsRemoved=function(a,b){var c,d,e,f,g,h,i,j,k=this,l=k.data,m=[],n=k.Je,o=a+b;for(c=a;c<o;c++)d=k.columnNames[c],e=c,m.push(d),n.indexOf(e)!==-1&&k.doUnfilter(d);for(f=k.Ge||k.Fe,g=k.Ye||k.Ve,h=k.Ne,c=0;c<D(l);c++)l[c].splice(a,b);k._e=[],k.columnNames.splice(a,b),
k.Oe.splice(a,b),k.Pe.splice(a,b),k.Se.splice(a,b),k.Qe.splice(a,b),k.Re.splice(a,b),f.splice(a,b),g.splice(a,b);for(i in h)h[i]>=a+b&&(h[i]-=b);for(c=0;c<D(m);c++)d=m[c],delete h[d.toUpperCase()],a=k.getColumnIndex(d),j=n.indexOf(a),j!==-1&&n.splice(j,1),k.cf("onColumnRemoved",m[c])},a.prototype.ef=function(){return this._e},a.prototype.ff=function(){return this.af},a.prototype.We=function(a){var b,c,d,e,f,g,h,i,j,k,l,n=this,p=n.data;for(n.Oe[a]||t(n,a),b=D(p),c=[],d=0;d<b;d++)e=p[d][a],f=o(e)?e.value:e,c.push(f);for(g=m.quickSort(c),h=n._e[a]=[],i=n.af[a]=[],j=n.Te[a]=[],k=0;k<D(g);k++)l=g[k],h.push(l.value),i.push(l.index),j[l.index]=k},a.prototype.getColumnIndex=function(a){var b=this.Ne[a.toUpperCase()];return void 0===b?-1:b},a.prototype.getFilteredRowIndexes=function(){var a=this;return a.Ce?a.He:a.Ie},a.prototype.getFilteredOutRowIndexes=function(){var a,b=this,c=D(b.data),d=b.Ce?b.De:b.Ee,e=[];for(a=0;a<c;a++)d[a]||e.push(a);return e},a.prototype.getData=function(a,b){var c,d,e,f,g,h,i,j=this,k=j.Oe,l=j.getColumnIndex(a);if(l>=0){if(b){for(j._e[l]||j.We(l),c=k[l],d=j._e[l],e=j.Xe(d,b),f=e.start,g=e.end,h=[],i=f;i<=g;i++)h.push(c[j.af[l][i]]);return h}return k[l]||t(j,l),k[l]}return[]},a.prototype.aggregateData=function(a,b,c){var d,f,g,h,i,j,k,l,m,n=this,o=n.getColumnIndex(a);return o<0?e:(n._e[o]||n.We(o),g=n._e[o],h=D(g),i=n.Xe(g,c),d=i.start,f=i.end,5===b?d>=h?e:g[d]:4===b?f<0?e:g[f]:3===b?f<d?0:f-d+1:(j=n.Qka(g,b,d,f),k=j.data1,l=j.data2,m=j.data3,1===b?k/=l:7===b||10===b?(k=(m*l-k*k)/(m*(m-1)),k=7===b?Math.sqrt(k):k):8!==b&&11!==b||(k=(m*l-k*k)/(m*m),k=8===b?Math.sqrt(k):k),k))},a.prototype.Qka=function(a,b,c,d){var g,h,i,j=6===b?1:0,k=0,l=0;for(g=c;g<=d;g++)h=a[g],h===f&&h===e||(i={value:f},p(h,i)&&(h=i.value,1===b?(j+=h,k++):2===b?j++:9===b?j+=h:6===b?j*=h:7!==b&&8!==b&&10!==b&&11!==b||(j+=h,k+=h*h,l++)));return{data1:j,data2:k,data3:l}},a.prototype.Xe=function(a,b){var c=D(a),d,f,g,h;if(b){if(b.min===-(1/0))d=0;else for(g=0;g<c;g++)if(h=a[g],m.isEquals(b.min,h)||m.isGreaterThan(h,b.min)){d=g;break}if(d===e&&(d=c),f===1/0)f=c-1;else{for(g=c-1;g>=0;g--)if(h=a[g],m.isEquals(h,b.max)||m.isGreaterThan(b.max,h)){f=g;break}f===e&&(f=-1)}}else d=0,f=c-1;return{start:d,end:f}},a.prototype.getExclusiveData=function(a){var b=this,c=b.Pe,d=b.getColumnIndex(a);return d>=0?(c[d]||t(b,d),c[d]):[]},a.prototype.getRowIndexes=function(a,b){var c=this,d=c.getColumnIndex(a);return d>=0?(c.Re[d]||t(c,d),c.Qe[d][b]):[]},a.prototype.getExclusiveRowIndex=function(a,b){var c=this,d=c.Re,e=c.getColumnIndex(a);return e>=0?(d[e]||t(c,e),d[e][b]):-1},a.prototype.getFilteredIndexes=function(a){var b=[],c={};return this.gf(a,b,c),b},a.prototype.gf=function(a,b,c){var d,e,f,g=this,h=g.Ce?g.He:g.Ie;for(d=0;d<D(h);d++)e=h[d],f=g.getExclusiveRowIndex(a,e),c[f]?c[f]++:(c[f]=1,b.push(f))},a.prototype.getFilteredRanges=function(a){return u(this,a,!1)},a.prototype.getFilteredOutRanges=function(a){return u(this,a,!0)},a.prototype.getFilteredOutIndexes=function(a,b){var c=this;return 0===b?v(c,a):1===b?w(c,a):x(c,a)},a.prototype.attachListener=function(a){this.Ze.push(a)},a.prototype.detachListener=function(a){var b,c=this.Ze||[];for(b=0;b<D(c);b++)if(c[b]===a){c.splice(b,1);break}},a.prototype.suspendFilteredEvents=function(){this.$e++},a.prototype.resumeFilteredEvents=function(){var a=this;a.$e--,0===a.$e&&a.onFiltered()},a.prototype.doFilter=function(a,b,c){this.hf(a,b,c),this.onFiltered()},a.prototype.hf=function(a,b,c){var d=this,e=d.Je,f=d.getColumnIndex(a);f<0||(c?(d.Ce=!0,d.Me=f):d.Ce&&A(d,!1),e.indexOf(f)<0&&e.push(f),b.ranges?y(d,a,b.ranges,c):z(d,a,b.exclusiveRowIndexes,c))},a.prototype.clearPreview=function(){A(this,!0)},a.prototype.doUnfilter=function(a){this.if(a),this.onFiltered()},a.prototype.if=function(a){var b,c,d=this;d.Ce&&A(d,!1),b=d.getColumnIndex(a),b<0||(delete d.Ve[b],delete d.Fe[b],delete d.Ge[b],delete d.Ye[b],q(d),c=d.Je.indexOf(b),c>=0&&d.Je.splice(c,1))},a.prototype.onFiltered=function(){var a=this;0===a.$e&&a.cf("onFiltered",{rowIndexes:a.getFilteredRowIndexes(),isPreview:a.Ce})},a.prototype.getListener=function(){return this.Ze},a}(),b.GeneralSlicerData=n;function B(a){return a===f||a===e}function C(a){return typeof a}function D(a){return a?a.length:0}},"./src/plugins/sparkline/sparkline.entry.ts":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./src/plugins/sparkline/sparkline.ts"))},"./src/plugins/sparkline/sparkline.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("./src/common/util/types.ts"),e=c("./src/common/util/colorhelper.ts"),f=c("./src/common/util/arrayhelper.ts"),g=d.j.Fa,h=e.pc.nc,i=e.pc.ec,j=f.k.Cb,b.Vd={},k=null,l=void 0,m=Math.floor,n=Math.PI,o=Math.sin,p=Math.cos,q=Math.min,r=Math.max,s=Math.round,t=Math.pow,u=Math.sqrt,v=Math.abs,w=Math.ceil,x="undefined",y="string",z=isNaN,A=parseFloat,B=Number.MAX_VALUE,C=new Date(""),D="#969696",E="#CB0000",F="#646464",G="#DCDCDC",H="white",I="black",J="blue",K="green",L="red",M="left",N="right",O="center",P="top",Q="bottom",R="middle",S="px Arial",T=function(){function a(a){this.x=a.x,this.y=a.y}return a}(),U=function(){function a(a){this.x=a.x,this.y=a.y,this.width=a.width,this.height=a.height}return a}();function ca(a,b){return{x:a,y:b}}function da(a,b,c,d){return{x:a,y:b,width:c,height:d}}function ea(a,b){var c,d,e,f=a[0],g=f&&f.value?f.value:f;for(d=1,e=a.length;d<e;d++)f=a[d],c=f&&f.value?f.value:f,(b&&g>c||!b&&g<c)&&(g=c);return g}function fa(a,b,c,d,e,f,g){f&&(a.strokeStyle=f),g&&(a.lineWidth=g),a.beginPath(),a.moveTo(b,c),a.lineTo(d,e),a.stroke()}function ga(a,b,c,d,e,f,g,h,i,j,k,l){b?fa(a,c,d,e,f,k,l):fa(a,g,h,i,j,k,l)}function ha(a){return e.pc.dc(i(a))<127.5?H:I}function ia(a,b,c,d,e,f,g){var h,i,j,k;return g?(h=b+e+f,j=d-2*e,i=a+e,k=c-2*e):(h=a+e,j=c-2*e,i=b+e,k=d-2*e),{left:h,top:i,width:j,height:k}}function ja(a,b){var c,d,e=0;for(c=0,d=a.length;c<d;c++)e+=b&&!b(a[c])?0:a[c];return e}function ka(a,b,c,d){var e,f,g,h,i=[];for(f=0,g=0,h=a.length;f<h;f++)b?b(a[f])?i[g++]=c?c(a[f]):a[f]:i[g++]=0:(e=A(a[f]),!z(e)&&isFinite(e)&&(i[g++]=e));return d&&d(i),i}function la(a,b,c,d,e,f){var g=s(b+d),h=s(c+e);b=s(b),c=s(c),d=s(g-b),e=s(h-c),a.beginPath(),a.fillStyle=f,a.fillRect(b,c,d,e),a.fill()}function ma(a,b,c){return a=q(a,b),a=r(a,c)}function na(a,b,c,d,e){a.save(),a.rect(b,c,d,e),a.clip()}function oa(a,b,c,d,e,f){var h,i,j,k,l,m,r=c+e/2,s=d+f/2,t=5,u=q(e,f)/2-t,w=-.5*n,x=r+u*p(w),y=s+u*o(w),A=[],B=[];if(!(u<=0)){for(i=ka(b.values,function(a){return!g(a)&&!z(a)&&isFinite(a)},function(a){return v(a)},function(a){1===a.length&&(a[1]=1-a[0])}),j=i.length,k=pa(j,b.colors),l=ja(i),a.save(),m=0;m<j;m++)h=w+i[m]/l*2*n,a.beginPath(),a.moveTo(r,s),a.lineTo(x,y),a.arc(r,s,u,w,h,!1),a.lineTo(r,s),a.fillStyle=k[m],a.fill(),A.push(x),B.push(y),w=h,x=r+u*p(w),y=s+u*o(w);for(m=0;m<j;m++)fa(a,r,s,A[m],B[m],H);a.restore()}}function pa(a,b){var c,d,f,g,h,j,k=[],l=b.length;if(a<=l)k=b.slice(0,a);else{for(0===l?(k.push("darkgray"),l=1):k=b.slice(0),c=[],d=void 0,f=a-l+1,g=void 0,g=0;g<l;g++)c[g]=i(k[g]);for(g=l;g<a;g++){d=c[g%l];for(h in d)d.hasOwnProperty(h)&&(j=d[h],d[h]=m(j-j/f*(g/l)));d.a=255,k[g]=e.pc.bc(d)}}return k}V=function(a){ba(b,a);function b(b){var c=a.call(this,ca(b.x,b.y))||this;return c.value=b.value,c}return b}(T);function qa(a,b,c){return{x:a,y:b,value:c}}function ra(a,b,c,d,e,f){var h,i,j,l,m,n,o,p,t,u,v,w,y,A,B,C,D,F,G,H,I,K,L,M,N,O,P,Q,R,S,T,U;for(a.save(),h=b.points,i=b.mini,j=b.maxi,l=b.line1,m=b.line2,n=b.colorPositive,o=b.colorNegative,p=5,h=ka(h,function(a){return!g(a)&&!z(a)&&isFinite(a)}),n=g(n)?"#787878":n,o=g(o)?E:o,G=ea(h,!1),j=g(j)?G:j,u=r(j,G),H=ea(h,!0),i=g(i)?H:i,t=q(i,H),t=t>0?0:t,u=u<0?0:u,v=0,v=v>u?u:v,v=v<t?t-1:v,w=h.length,I=ia(c,d,e,f,p),y=I.left,A=I.top,B=I.width,C=I.height,D=B/(w-1),F=(u-t)/C,K=[],P=A+(u-v)/F,S=0;S<w;S++)L=h[S],Q=y+D*S,R=A+(u-L)/F,0===S&&K.push(new V(qa(Q,P,v))),S>0&&L*h[S-1]<0&&(N=K[K.length-1],N&&(O=sa(N.x,N.y,Q,R,P),K.push(new V(qa(O,P,v))))),K.push(new V(qa(Q,R,L))),S===w-1&&K.push(new V(qa(Q,P,v)));for(a.beginPath(),T=0,U=K.length;T<U;T++)M=K[T],M&&(0===T?a.moveTo(M.x,M.y):a.lineTo(M.x,M.y),N=K[T-1],M.value===v&&N&&(a.fillStyle=N.value>v?n:o,a.fill(),T!==U-1&&(a.beginPath(),a.moveTo(M.x,M.y))));W(l),W(m),a.restore();function W(b){var c,d,e;b===k&&typeof b!==x||(c=ma(b,u,t),d=0!==F?(u-c)/F:C/2,e=s(A+d)-.5,fa(a,y,e,y+B,e,J))}}function sa(a,b,c,d,e){return((b-e)*c+(e-d)*a)/(b-d)}function ta(a,b,c,d,e,f){var h,i,j,k,l,m,o,p,t,u,v,w,x,y,z,A,C,F,H,I,J,K,L,M,N,O,P=b.points1,Q=b.points2,R=b.minX,S=b.maxX,T=b.minY,U=b.maxY,V=b.hLine,W=b.vLine,X=b.xMinZone,Y=b.xMaxZone,Z=b.yMinZone,$=b.yMaxZone,_=b.tags,aa=b.drawSymbol,ba=b.drawLines,ca=b.color1,da=b.color2,ea=b.dash,fa=4,ga=ia(c,d,e,f,5),ha=ga.left,ja=ga.top,ka=ga.width,ma=ga.height;function oa(b,c,d,e){p=b[c],u=p.x,v=p.y,p=b[c+1],w=p.x,x=p.y,y=ha+(u-l)*ka/(m-l),A=ha+(w-l)*ka/(m-l),z=ja+(k-v)*ma/(k-j),C=ja+(k-x)*ma/(k-j),ba&&wa(a,y,z,A,C,d,ea),aa&&(0===c&&e(y,z),e(A,C))}function pa(a,b,c,d,e){var f=ua(a,!0),h=ua(a,!1);j=g(T)?f.y:b,k=g(U)?h.y:c,l=g(R)?f.x:d,m=g(S)?h.x:e,m=l>=m?l+1:m,k=j>=k?j+1:k}function qa(b,c,d){a.beginPath(),a.arc(b-fa/2,c-fa/2,fa/2,0,2*n,!1),a.fillStyle=d,a.fill()}function ra(b,c){a.beginPath(),a.strokeStyle=ca,a.arc(b-fa/2,c-fa/2,fa/2,0,2*n,!1),a.stroke()}function sa(b,c){a.beginPath(),a.strokeStyle=da,a.strokeRect(b-fa/2,c-fa/2,fa,fa)}if(P&&!(P.length<=0)&&(h=xa(P),!(h.length<=0||Q&&Q.length>0&&(i=xa(Q),i.length<=0)))){for(na(a,c,d,e,f),a.beginPath(),aa=!!g(aa)||aa,ca=g(ca)?D:ca,da=g(da)?E:da,pa(h,T,U,R,S),!g(X)&&!g(Y)&&!g(Z)&&!g($)&&l<=X&&X<=m&&l<=Y&&Y<=m&&j<=Z&&Z<=k&&j<=$&&$<=k&&(L=r(l,X),M=q(m,Y),N=r(j,Z),O=q(k,$),L>=M&&(M=L+1),N>=O&&(O=N+1),la(a,ha+(L-l)*ka/(m-l),ja+(k-O)*ma/(k-j),(M-L)*ka/(m-l),(O-N)*ma/(k-j),G)),F=-B,H=-B,I=B,J=B,o=0,t=h.length;o<t-1;o++)oa(h,o,ca,ra),_&&(0===o&&(z>H&&(F=y,H=z),z<J&&(I=y,J=z)),C>H&&(F=A,H=C),C<J&&(I=A,J=C));if(i&&i.length>0)for(pa(i,j,k,l,m),o=0,t=i.length;o<t-1;o++)oa(i,o,da,sa);_&&(qa(F,H,E),qa(I,J,"#0000FF")),!g(V)&&j<=V&&V<=k&&(K=s(ja+(k-V)*ma/(k-j))-.5,wa(a,ha,K,ha+ka,K,E)),!g(W)&&l<=W&&W<=m&&(K=s(ha+(W-l)*ka/(m-l))-.5,wa(a,K,ja,K,ja+ma,E)),a.restore()}}function ua(a,b){var c,d,e=b?1:-1,f=b?q:r,g=new T(ca(e*B,e*B)),h=a.length;for(d=0;d<h;d++)c=a[d],g.x=f(g.x,c.x),g.y=f(g.y,c.y);return g}function va(a,b,c,d){return c/d*(b-a)+a}function wa(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,v,w,x,y,z;if(g){for(h=u(t(d-b,2)+t(e-c,2)),i=0,j=6,k=2,l=4,m=[],n=void 0,o=void 0,p=void 0,q=void 0,b<=d?(n=b,o=d,p=b,q=c):(n=d,o=b,p=d,q=e),r=(e-c)/(d-b),s=c-r*b,m.push(new T(ca(p,q))),v=[j,l,k,l];i<h;)for(w=0,x=v.length;w<x;w++)i+=v[w],i<=h&&(p=va(n,o,i,h),q=r*p+s,m.push(new T(ca(p,q))));for(y=0,z=m.length;y<z-1;y+=2)fa(a,m[y].x,m[y].y,m[y+1].x,m[y+1].y,f)}else fa(a,b,c,d,e,f)}function xa(a){var b,c,d,e=[],f=a.length;if(f>0)if(b=a[0].length,f<b){if(f>=2)for(c=0;c<b;c++)e.push(new T(ca(a[0][c],a[1][c])))}else if(b>=2)for(d=0;d<f;d++)e.push(new T(ca(a[d][0],a[d][1])));return e}function ya(a,b,c,d,e,f){var i,j,k,l,n,o,p,q,r,t,u=b.measure,v=b.target,w=b.maxi,x=b.good,y=b.bad,z=b.forecast,A=b.tickUnit,B=b.colorScheme,C=b.vertical,D=5;function G(b,g,h,i,j){var m,n,o;a.fillStyle=b,m=k,n=l*(g/h),n>l&&(n=l),o=new U(C?da(c+e*i,m-n,e*j,n):da(m,d+f*i,n,f*j)),a.fillRect(o.x,o.y,o.width,o.height)}if(!(g(w)||w<0)){if(u=g(u)||u<0?0:u,x=g(x)||x<0?0:x,y=g(y)||y<0?0:y,v=g(v)?0:v,z=g(z)?0:z,A=g(A)?0:A,B=g(B)?"#A0A0A0":B,i="#252525",u>w&&(u=w,i=E),x>w&&(x=w,i=E),y>w&&(y=w,i=E),v>w&&(v=0,i=E),z>w&&(z=w,i=E),j=ia(c,d,e,f,D,f-2*D,C),k=j.left,l=j.width,a.save(),G(h(B,1.66),1,1,.2,.6),G(h(B,1.33),x,w,.2,.6),G(B,y,w,.2,.6),G(i,u,w,.375,.25),z>0&&(n=k,o=l*(z/w),o>l&&(o=l),ga(a,C,c+.5*e,n,c+.5*e,n-o,n,d+.5*f,n+o,d+.5*f,"#3690BF",3)),v>0&&(p=s(k-(C?1:-1)*l*(v/w))-.5,ga(a,C,c+.2*e,p,c+.8*e,p,p,d+.2*f,p,d+.8*f,E,1)),A>0)for(q=m(w/A),r=void 0,t=0;t<=q;t++)r=s(k-(C?1:-1)*(l/w*A)*t)-.5,ga(a,C,c,r,c+.05*e,r,r,d+f,r,d+.95*f,F,1);a.restore()}}function za(a,b,c,d,e,f){var h,i,j,k,l,m,n,o,p,t,u,v,w,x,y,z,A,B,C,D,G,H,I=b.spreadData,J=b.showAverage,K=b.scaleStart,L=b.scaleEnd,M=b.style,N=b.colorScheme,O=b.vertical,P=5,Q=I.length;if(!(Q<=0)){for(h=I[0].key,i=I[Q-1].key,K=g(K)?h:K,L=g(L)?i:L,M=g(M)?4:M,N=g(N)?F:N,j=ia(c,d,e,f,P,f-2*P,O),k=j.left,l=j.width,m=j.top,n=j.height,na(a,c,d,e,f),a.beginPath(),a.strokeStyle=N,a.fillStyle=N,a.lineWidth=2,t=ea(I,!1),v=0,w=I.length;v<w;v++)if(x=I[v],y=x.key,z=x.value,o=k+(O?-1:1)*l*(y-K)/(L-K),O?(o=q(o,k),o=r(o,k-l)):(o=r(o,k),o=q(o,k+l)),o=s(o),1===M)u=z/2*n/t,u=u<.5?.5:u,A=m+n/2-u,B=m+n/2+u,ga(a,O,A,o,B,o,o,A,o,B);else if(4===M)u=z*n/t,u=u<1?1:u,ga(a,O,m,o,m+u,o,o,m+n,o,m+n-u);else if(6===M)ga(a,O,m,o,m+n,o,o,m,o,m+n);else for(C=2,D=1;D<=z;D++){switch(M){case 2:p=m+n/2-P-(z/2-D)*n/t;break;case 3:G=Aa(z,n,C+1,x.randomNumbers),p=m+n-P-G[D-1];break;case 5:default:p=m+n-D*n/t}p=s(p),a.beginPath(),a.fillRect(O?p:o,O?o:p,C,C)}J&&(H=Ba(I),o=k-(O?1:-1)*l*(H-K)/(L-K),ga(a,O,m-P,o,m+n+P,o,o,m-P,o,m+n+P,E)),a.restore()}}function Aa(a,b,c,d){for(var e,f=[],g=r(100,10*a),h=0,i=[],j=0;f.length<a;)e=m(d[j++]*b),(h>g||Ca(e,i))&&(f.push(e),i.push([e-c,e+c])),h++;return f}function Ba(a){var b,c,d,e=0,f=0;for(b=0,c=a.length;b<c;b++)d=a[b],f+=d.value,e+=d.key*d.value;return 0===f?0:e/f}function Ca(a,b){var c,d,e;for(c=0,d=b.length;c<d;c++)if(e=b[c],e[0]<=a&&a<=e[1])return!1;return!0}function Da(a,b,c,d,e,f,i){var j,m,n,o,p,q,r,t,u,v,w,x,y,A,B,C,D,G,H,I=b.points,M=b.colorRange,N=b.labelRange,P=b.maximum,Q=b.targetRed,T=b.targetGreen,U=b.targetBlue,V=b.targetYellow,W=b.color,X=b.highlightPosition,Y=b.vertical,Z=b.textOrientation,$=b.textSize,_=5;function aa(b,c,d){var e,f;c!==k&&c!==l&&(c=c>P?P:c,d&&!d(c)||(c=c<0?0:c,f=c/P*q,Y?(e=s(p-f)-.5,fa(a,r,e,r+t,e,b)):(e=s(p+f)-.5,fa(a,e,r,e,r+t,b))))}if(!(g(I)||(j=I.length)<=0)){if(W=g(W)?F:W,g(M)||M.length!==j||Ea(M))for(M=[],m=0;m<j;m++)M.push(h(W,1+m/j));for(n=ja(I,function(a){return a>0}),P=g(P)||P<n?n:P,Z=g(Z)?0:Z,$=g($)||$<=0?10:$,$=z($)?$:$*i.zoomFactor,o=ia(c,d,e,f,_,f-2*_,Y),p=o.left,q=o.width,r=o.top,t=o.height,a.save(),u=p,C=0,D=j;C<D;C++)B=I[C],B<=0||(v=B/P*q,Y?(w=c+.15*e,y=.7*e,x=u-v,A=v):(w=u,y=v,x=d+.15*f,A=.7*f),G=C+1===X?E:M[C],a.save(),a.fillStyle=G,a.fillRect(w,x,y,A),H=N&&N[C],H&&(a.save(),a.fillStyle=ha(G),a.textBaseline=R,a.textAlign=O,a.font=$+S,a.rect(w,x,y,A),a.clip(),1===Z?(a.translate(w+y/2,x),a.rotate(Math.PI/2),a.fillText(H,A/2,0)):a.fillText(H,w+y/2,x+A/2),a.restore()),u+=(Y?-1:1)*v,a.restore());aa(L,Q,function(a){return a>0}),aa(K,T),aa(J,U),aa("yellow",V),a.restore()}}function Ea(a){for(var b=0,c=a.length;b<c;b++)if(g(a[b]))return!0;return!1}function Fa(a,b,c,d,e,f){fa(a,Ia(b),Ia(c),Ia(d),Ia(e),g(f)?I:f,1)}function Ga(a,b,c,d,e){var f,g,h,i,j,k=.4*d;switch(b=Ia(b),c=Ia(c),a.beginPath(),a.moveTo(b,c),f=k/2,g=u(3)*k/2,h=k/u(3),e){case 0:i=[-f,0,f],j=[g,h,g];break;case 2:i=[-f,0,f],j=[-g,-h,-g];break;case 3:i=[g,h,g],j=[-f,0,f];break;case 1:i=[-g,-h,-g],j=[-f,0,f];break;default:i=[],j=[]}i.forEach(function(d,e){a.lineTo(Ia(b+d),Ia(c+j[e]))}),a.lineTo(b,c),a.closePath(),a.fillStyle=H,a.fill()}function Ha(a,b,c,d,e,f,h){var i,j,k,l,m,n,o,p,q,r,s,t,u=c.value,w=c.colorScheme,x=5,y=5,B=.7,C=!1,D=A(u);z(D)||(D>1?(D=1,C=!0):D<-1&&(D=-1,C=!0),w=g(w)?"grey":w,b.save(),0===a?(o=f-2*x,p=h*B,D>=0?(C&&(i=new T(ca(d+x+o-y,e+h/2))),j=new U(da(d+x,e+h*(1-B)/2,o*D,p)),k=new T(ca(d+x,e+1)),l=new T(ca(d+x,e+h)),m=1):(q=v(o*D),C&&(i=new T(ca(d+x+y,e+h/2))),j=new U(da(d+f-x-q,e+h*(1-B)/2,q,p)),k=new T(ca(d+f-x,e+1)),l=new T(ca(d+f-x,e+h)),m=3),n=h):1===a&&(r=h-2*x,s=f*B,D>=0?(t=r*D,j=new U(da(d+(1-B)/2*f,e+h-x-t,s,t)),C&&(i=new T(ca(d+f/2,e+h-x-t+y)),m=0),k=new T(ca(d+1,e+h-x)),l=new T(ca(d+f,e+h-x))):(D=v(D),j=new U(da(d+(1-B)/2*f,e+x,s,r*D)),C&&(i=new T(ca(d+f/2,e+x+r-y)),m=2),k=new T(ca(d+1,e+x)),l=new T(ca(d+f,e+x))),n=f),b.beginPath(),j&&(la(b,j.x,j.y,j.width,j.height,w),na(b,j.x,j.y,j.width,j.height)),i&&Ga(b,i.x,i.y,n,m),b.restore(),k&&Fa(b,k.x,k.y,l.x,l.y),b.restore())}function Ia(a){return s(a)-.5}function Ja(a,b,c,d,e,f){Ha(0,a,b,c,d,e,f)}function Ka(a,b,c,d,e,f){Ha(1,a,b,c,d,e,f)}function La(a,b,c,d,e,f,h){var i,j,k,l,m,n,o,p,q,r,s,t,w,x,y,B,C,F,G,H,J,T=b.variance,U=b.reference,V=b.mini,W=b.maxi,X=b.mark,Y=b.tickUnit,Z=b.legend,$=b.colorPositive,_=b.colorNegative,aa=b.vertical,ba=.5,ca=.5-ba/2,da=5,ea=E,fa=D,ga=13*h.zoomFactor,ia=A(T);function ja(b,g,h,j,k,l,o,q,r,s,t,v,w,z){var A,B,C,D,E,F;aa?(A=c+ca*e,B=0===g?x-y:x,C=e*ba,D=y,la(a,A,B,C,D,b)):(A=0===g?x:x-y,B=d+ca*f,C=y,D=f*ba,la(a,A,B,C,D,b)),p&&(na(a,A,B,C,D),aa?Ga(a,c+e/2,i+h,n,k):Ga(a,i+j,d+f/2,n,l),a.restore()),Z&&(E=void 0,F=0,p&&(F=.4*n*u(3)/2+da+2),a.save(),a.beginPath(),a.font=ga+S,a.fillStyle=I,aa?(a.textAlign=O,E=ga+y+o,ka(E,z,b,q,r,A,B,C,D,aa),a.fillText(m,c+e/2,x+w*(y-F))):(a.textBaseline=R,E=a.measureText(m).width+y+s,ka(E,z,b,t,v,A,B,C,D,aa),a.fillText(m,x+w*(F-y),d+f/2)),a.restore())}function ka(b,c,d,e,f,g,h,i,j,k){k?a.textBaseline=e:a.textAlign=e,b>0&&c<y&&(a.rect(g,h,i,j),a.clip(),k?a.textBaseline=f:a.textAlign=f,a.fillStyle=ha(d))}function oa(b,c,d,e,f,g,h,i,j,k){aa?Fa(a,b,c,d,e,j):Fa(a,f,g,h,i,k)}if(!(z(ia)||(_=g(_)?L:_,$=g($)?K:$,Y=g(Y)?0:Y,W=g(W)?1:W,V=g(V)?-1:V,o=!g(U),o||(U=0),aa?(i=d+f-da,j=f-2*da,k=e-4*da,l=-1):(i=c+da,j=e-2*da,k=f-4*da,l=1),Z&&(q=A(b.variance),z(q)||(r=""+q,s=r.substr(r.indexOf(".")+1).length,m=s>=2?(100*q).toFixed(s-2)+"%":(100*q).toFixed(0)+"%")),ia>W&&(ia=W,p=!0),ia<V&&(ia=V,p=!0),U=ma(U,W,V),p&&(n=ma(k,60,15)),t=v(W-V),w=j/t,x=i+l*v(V-U)*w,y=v(ia-U)*w,y>v(t)*w&&(y=v(t+V)*w),B=i+l*j,aa&&x<B||!aa&&x>B))){if(na(a,c,d,e,f),ia>U?ja($,0,da-j,j-da,0,1,d-x,Q,P,x-c-e,M,N,-1,v(W-ia)*w):(y>v(t)*w&&(y=v(t+V)*w,x=i),ja(_,1,-da,da,2,3,x-d-f,P,Q,c-x,N,M,1,v(V-ia)*w)),o&&(C=i+l*(Math.abs(V-U)*w),oa(c,C,c+e,C,C,d,C,d+f)),Y>0)for(a.beginPath(),F=t/Y,G=0;G<=F;G++)H=i+j/F*G*l,oa(c,H,c+.1*e,H,H,d+.9*f,H,d+f,fa,fa);!g(X)&&V<=X&&X<=W&&(a.beginPath(),J=i-(aa?1:-1)*v(V-X)*w,oa(c,J,c+.33*e,J,J,d+.66*f,J,d+f,ea,ea),a.fill()),a.restore()}}W="7ns",X="5ns",Y="tukey",Z="bowley",$="sigma3";function Ma(a,b,c,d,e,f){var h,i,j,l,m,n,o,p,s,t,u,w,x,B,C,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,_,aa,ba,ca,da,fa,ha,ma,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea=b.points,Fa=b.boxPlotClass,Ga=b.showAverage,Ha=b.scaleStart,Ia=b.scaleEnd,Ja=b.acceptableStart,Ka=b.acceptableEnd,La=b.style,Ma=b.colorScheme,Na=b.vertical,Oa=5,Pa=ea(Ea,!1),Qa=ea(Ea,!0);function Ra(b,c,d,e,f,g,h,i,j){Na?la(a,c,d,e,f,b):la(a,g,h,i,j,b)}function Sa(b,c,d,e){ga(a,Na,T+U*b,d,T+U*c,d,d,T+U*b,d,T+U*c,e)}if(!(g(Ea)||(Ea=ka(Ea),Ea.length<=0))){for(Fa=Fa===k||typeof Fa!==y?X:Fa.toLocaleLowerCase(),Fa!==X&&Fa!==W&&Fa!==Y&&Fa!==Z&&Fa!==$&&(Fa=X),h=A(Ha),h=z(h)?Qa:h,i=A(Ia),i=z(i)?Pa:i,j=A(Ja),l=A(Ka),Ma!==k&&typeof Ma===y||(Ma="#D2D2D2"),(La===k||0!==La&&1!==La)&&(La=0),m=E,h>Qa&&(Ma=m,h=Qa),i<Pa&&(Ma=m,i=Pa),na(a,c,d,e,f),a.lineWidth=2,n=ia(c,d,e,f,Oa,f-2*Oa,Na),o=n.left,p=n.width,s=n.top,t=n.height,u=Na?-1:1,w=b.perc02,x=b.perc09,B=b.perc10,C=b.perc90,G=b.perc91,H=b.perc98,I=b.q1,J=b.q3,K=J-I,L=Qa,M=Pa,N=1.5*K,O=1.5*K,P=b.stDev,Q=ja(Ea)/r(1,Ea.length),R=0,S=0,T=s+.1*t,U=.7*t,V=v(i-h),_=0,aa=Ea.length;_<aa;_++)ba=Ea[_],ba<I&&ba>=I-1.5*K&&ba-(I-1.5*K)<N&&(N=ba-(I-1.5*K),L=ba),ba>J&&ba<=J+1.5*K&&J+1.5*K-ba<O&&(O=J+1.5*K-ba,M=ba),ca=!1,R=o+u*(p*((ba-h)/V)),Fa===Y&&(ba<=I-1.5*K||ba>=J+1.5*K)&&(ca=!0,S=ba<=I-3*K||ba>=J+3*K?0:1),Fa===W&&(ba<=w||ba>=H)&&(ca=!0,S=1),Fa===$&&(ba<=Q-2*P||ba>=Q+2*P)&&(ca=!0,S=ba<=Q-3*P||ba>=Q+3*P?0:1),da=D,ca&&(1===La?1===S?Sa(.2,.8,R,da):Sa(.3,.7,R,da):(fa=.1*t,fa<2&&(fa=2),a.beginPath(),a.strokeStyle=da,ha=s+.45*t,ma=R,a.arc(Na?ha:ma,Na?ma:ha,fa/2,0,2*Math.PI),a.stroke()));switch((h>j||i<l)&&(Ma="#C0FF00"),j=r(h,j),l=q(i,l),j>l?Ma=m:j<l&&(oa=o+u*(p*((j-h)/V)),pa=o+u*(p*((l-h)/V)),qa=s+.9*t,ga(a,Na,qa,oa,qa,pa,oa,qa,pa,qa,F)),ra=o+u*(p*((I-h)/V)),sa=v(o+u*(p*((J-h)/V))-ra),ta=b.median,ua=o+u*(p*((ta-h)/V)),Fa){case W:xa=w,ya=H;break;case Y:xa=L,ya=M;break;case $:ra=o+u*(p*((Q-P-h)/V)),sa=v(o+u*(p*((Q+P-h)/V))-ra),ua=o+u*(p*((Q-h)/V)),za=Q-2*P,xa=za>h?za:Qa,za=Q+2*P,ya=za<i?za:Pa,Ga=!1;break;case X:case Z:default:xa=Qa,ya=Pa}va=o+u*(p*((xa-h)/V)),wa=o+u*(p*((ya-h)/V)),Aa=D,1===La?Ra("#F2F2F2",T,wa,U,va-wa,va,T,wa-va,U):(Ba=s+.45*t,ga(a,Na,Ba,va,Ba,wa,va,Ba,wa,Ba,Aa)),Ra(Ma,T,ra-sa,U,sa,ra,T,sa,U),ga(a,Na,T,ua,T+U,ua,ua,T,ua,T+U,Aa),0===La&&(ga(a,Na,T+.3*U,wa,T+.7*U,wa,wa,T+.3*U,wa,T+.7*U,Aa),ga(a,Na,T+.3*U,va,T+.7*U,va,va,T+.3*U,va,T+.7*U,Aa)),Fa!==W&&Fa!==Z||(Ca=void 0,Da=void 0,Fa===W?(xa=x,ya=G):(xa=B,ya=C),Ca=o+u*(p*((xa-h)/V)),Da=o+u*(p*((ya-h)/V)),Sa(.3,.7,Da,Aa),Sa(.3,.7,Ca,Aa)),Ga&&(R=o+u*(p*((Q-h)/V)),Sa(.2,.8,R,m)),a.restore()}}function Na(a,b,c,d,e){b=s(b),c=s(c),d=s(d),e=s(e),b===d&&(d-=.5,b=d),c===e&&(e-=.5,c=e),a.moveTo(b,c),a.lineTo(d,e)}function Oa(a,b,c,d,e,f,g,h,i,j,k){a.beginPath(),a.strokeStyle=c,b?Na(a,d,e,f,g):Na(a,h,i,j,k),a.stroke()}function Pa(a,b,c,d,e){a.rect(b,c,d,e),a.clip()}function Qa(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o=f.labelText,p=f.fontSize,q=f.startBox,r=f.endBox,s=f.boxColor,t=f.isInRightOrTopOfBox;a.save(),a.beginPath(),a.font=p+S,j=1,m=I,f.vertical?(k=O,t?(g=p+r-(q-c),g>0&&q-r-c<r?(l=P,m=ha(s),i=q-r+j,Pa(a,b,q-r,d,r)):(l=Q,i=q-r-j)):(g=p-(c+e-q),g>0&&c+e-q<r?(Pa(a,b,q-r,d,r),l=Q,m=ha(s),i=q-j):(l=P,i=q+j)),a.textAlign=k,a.textBaseline=l,a.fillStyle=m,a.fillText(o,b+d/2,i)):(l=R,n=a.measureText(o),t?(g=n.width+r-(b+d-q),g>0&&b+d-(q+r)<r?(Pa(a,q,c,r,e),k=N,m=ha(s),h=q+r-j):(k=M,h=q+r+j)):(g=n.width-(q-b),g>0&&q-b<r?(Pa(a,q,c,r,e),h=q+j,k=M,m=ha(s)):(k=N,h=q-j)),a.textAlign=k,a.textBaseline=l,a.fillStyle=m,a.fillText(o,h,c+e/2)),a.restore()}function Ra(a,b,c,d,e,f,i){var j,l,m,n,o,p,s,t,u,w,x,B,C,D,E,F,H,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba=b.points,ca=b.labels,da=b.pointIndex,ea=b.minimum,fa=b.maximum,ga=b.colorPositive,ha=b.colorNegative,ja=b.vertical,ka=5,ma=13*i.zoomFactor;if(!(g(ba)||(j=ba.length,j<=0||(l=parseInt(da,10),z(l)||l<=0||l>j)))){for(ca=g(ca)?[]:ca,ga!==k&&typeof ga===y||(ga="#8CBF64"),ha!==k&&typeof ha===y||(ha="#D6604D"),m=ga,n=h(ga,1.3),o=ha,p=h(ha,1.3),s=1,t=0,u=0,w=0,x=0,B=[],C=0,D=0,E=0,F=ba.length;E<F;E++)D=ba[E],B[s]=[],H=B[s],z(D)?(H[0]=0,H[1]=t,H[2]=0):(H[0]=v(D),t=D+t,H[1]=D>0?u:t,H[2]=D),H[3]=s,w=q(u,w),x=r(u,x),s===j&&(C=w<0?-w:0,H[1]=D>0?0:D),u=D+u,s++;t-=D,L=A(ea),z(L)||L>0||L>w?(J=w,L=w):(J=L,C=-L),M=A(fa),z(M)||M<0||M<x?K=x:(K=M,C=-L),N=ia(c,d,e,f,ka,f-2*ka,ja),O=N.left,P=N.width,Q=N.top,R=N.height,S=ja?-1:1,T=K-J,U=P/T,na(a,c,d,e,f),a.beginPath(),a.lineWidth=1,V=B[l],W=V[2],X=O+S*(V[1]+C)*U,Y=V[0]*U,Z=1===l||l===j?W>=0?m:o:W>=0?n:p,ja?la(a,Q,X-Y,R,Y,Z):la(a,X,Q,Y,R,Z),ja?(1!==l&&(l!==j?Oa(a,W>0,G,c,X,Q+R,X,c,X-Y,Q+R,X-Y):($=O-(t+C)*U,Oa(a,!0,G,c,$,Q+R,$))),l!==j&&Oa(a,W>0,G,Q,X-Y,c+e,X-Y,Q,X,c+e,X)):(1!==l&&(l!==j?Oa(a,W>0,G,X,d,X,Q+R,X+Y,d,X+Y,Q+R):(_=O+(t+C)*U,Oa(a,!0,G,_,d,_,Q+R))),l!==j&&Oa(a,W>0,G,X+Y,Q,X+Y,d+f,X,Q,X,d+f)),aa=ca[l-1],ca.length>0&&!g(aa)&&""!==aa&&Qa(a,c,d,e,f,{labelText:aa,vertical:ja,isInRightOrTopOfBox:W>0,fontSize:ma,startBox:X,endBox:Y,boxColor:Z}),Oa(a,ja,I,c,O-C*U,c+e,O-C*U,O+C*U,d,O+C*U,d+f),a.restore()}}function Sa(a,b,c,d,e,f,h){var i,j,k,l,m,n,o,p,q,r,t,u,v,x,B,C,F,G,H,I,J,K,L,M,N,O,P,Q=b.points,R=b.pointIndex,S=b.colorRange,T=b.target,U=b.target2,V=b.highlightPosition,W=b.label,X=b.vertical,Y=5,Z=13*h.zoomFactor;function $(a){var b=A(a);return b=z(b)?0:b,b=b<0?0:b,b=b>1?1:b}if(!(g(Q)||(i=Q.length,i<=0||(j=parseInt(R,10),z(j)||j<=0||j>i)))){for(S=g(S)?[]:S,k=$(T),l=$(U),m=parseInt(W,10),m=z(m)?0:m,n=1,o=0,p=[],q=0,r=Q.length;q<r;q++)t=Q[q],p[n]=[],u=p[n],t<0||z(t)||g(t)?(u[0]=1===n?0:o,u[1]=0):(o+=t,u[0]=1===n?0:o-t,u[1]=t),n++;v=ia(c,d,e,f,Y,f-2*Y,X),x=v.left,B=v.width,C=v.top,F=v.height,G=X?-1:1,H=B/o,na(a,c,d,e,f),a.beginPath(),a.lineWidth=1,I=p[j],J=I[1],K=x+G*I[0]*H,L=I[1]*H,M=j===V?E:0===S.length||typeof S[j-1]!==y?D:S[j-1],X?la(a,C,K-L,F,L,M):la(a,K,C,L,F,M),1===m?N=(I[0]+J)/o*1e3:2===m&&(N=J/o*1e3),O=s(N)/10+"%",1!==m&&2!==m||""===O||Qa(a,c,d,e,f,{labelText:O,vertical:X,isInRightOrTopOfBox:(I[0]+I[1])*H<B/2,fontSize:Z,startBox:K,endBox:L,boxColor:M}),P=["#8CBF64","#EE5D5D"],[k,l].forEach(function(b,g){var h=w(x+G*B*b);Oa(a,X,P[g],c,h,c+e,h,h,d,h,d+f)}),a.restore()}}function Ta(a,b){switch(b){case 2:var c=a%4===0&&a%100!==0||a%400===0;return c?29:28;case 4:case 6:case 9:case 11:return 30;default:return 31}}function Ua(a,b,c){var d,e,f,g,h,j;return a<0&&(a=0),a>1&&(a=1),d=i(b),e=i(c),f=d.a*(1-a)+e.a*a,g=d.r*(1-a)+e.r*a+"",h=d.g*(1-a)+e.g*a+"",j=d.b*(1-a)+e.b*a+"","rgba("+parseInt(g,10)+","+parseInt(h,10)+","+parseInt(j,10)+","+A(f/255+"")+")"}function Va(a,b,c){return a===b&&a===c?1:a<=b?0:a>=c?1:(a-b)/(c-b)}function Wa(a,b,c,d,e,f){var g=(f+e)/2;return e<=d&&d<=g?Ua(Va(d,e,g),a,b):Ua(Va(d,g,f),b,c)}function Xa(a){var b=B,c=-B;return a.forEach(function(a){a!==k&&a!==l&&(b>a&&(b=a),c<a&&(c=a))}),{min:b,max:c}}function Ya(a,b,c,d,e,f,g){var h,i,j;d?a.forEach(function(a,e){0!==a&&a!==k&&a!==l&&(b[c+e]=d[e]||b[c+e])}):(h=Xa(a),i=h.min,j=h.max,a.forEach(function(a,d){0!==a&&a!==k&&a!==l&&(b[c+d]=Wa(e,f,g,a,i,j))}))}function Za(a,b,c,d,e,f){var g,h,i,j,k=b.year,l=b.month,m=b.values,n=b.emptyColor||"lightgray",o=b.startColor,p=b.middleColor,q=b.endColor,r="white",s=6,t=7,u=2,v=(e-(s+1)*u)/s,w=(f-(t+1)*u)/t,x=[],y=new Date(k,l-1,1).getDay();for(g=0;g<s*t;g++)g<y?x[g]=r:g<y+Ta(k,l)?x[g]=n:x[g]=r;for(Ya(m,x,y,b.colors,o,p,q),a.save(),a.rect(c,d,e,f),a.clip(),a.beginPath(),h=0,i=0;i<s;i++)for(j=0;j<t;j++)a.fillStyle=x[h++],a.fillRect(c+i*v+(i+1)*u,d+j*w+(j+1)*u,v,w);a.restore()}function $a(a){var b,c=a.getFullYear(),d=a.getMonth(),e=a.getDate();for(b=1;b<d+1;b++)e+=Ta(c,b);return e}function _a(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F=b.year,G=b.values,H=b.emptyColor||"lightgray",I=b.startColor,J=b.middleColor,K=b.endColor,L="white",M=7,N=parseInt(366/M+"",10)+2,O=[],P=new Date(F,0,1).getDay(),Q=$a(new Date(F,11,31));for(h=0;h<N*M;h++)h<P?O[h]=L:h<P+Q?O[h]=H:O[h]=L;for(Ya(G,O,P,b.colors,I,J,K),a.save(),a.rect(c,d,e,f),a.clip(),i=g.zoomFactor,j=15*i,k=c+j,l=d,m=e-j,n=f,a.save(),a.translate(c+j/2,l+n),a.rotate(-Math.PI/2),a.font=13*i+S,a.fillStyle="black",a.textBaseline="middle",a.textAlign="center",a.fillText(F+"",n/2,0),a.restore(),o=2,p=(m-(N+1)*o)/N,q=(n-(M+1)*o)/M,r=0,v=[],t=0;t<N;t++)for(s=0;s<M;s++)u={x:k+t*p+(t+1)*o,y:l+s*q+(s+1)*o,w:p,h:q},v[r]=u,a.fillStyle=O[r++],a.fillRect(u.x,u.y,u.w,u.h);for(w=[],x={},y=0;y<12;y++)x[y]=$a(new Date(F,y,1))-1+P;for(z=x[11]+Ta(F,12)-1,r=0,t=0;t<N;t++)for(s=0;s<M;s++)P<=r&&r<P+Q&&(A=new Date(F,0,r-P+1),B=A.getMonth(),C=x[B],u=v[r],C<=r&&r<7+C&&w.push({x1:u.x,y1:u.y-o/2,x2:u.x,y2:u.y+u.h+o/2}),r!==C&&0!==A.getDay()||w.push({x1:u.x-o/2,y1:u.y,x2:u.x+u.w+o/2+(r===C?o:0),y2:u.y}),z-7<r&&r<=z&&w.push({x1:u.x+u.w,y1:u.y-o/2,x2:u.x+u.w,y2:u.y+u.h+o/2}),r!==z&&6!==A.getDay()||w.push({x1:u.x-o/2-(r===z?o:0),y1:u.y+u.h,x2:u.x+u.w+o/2,y2:u.y+u.h})),r++;for(a.strokeStyle="black",a.lineWidth=2,D=0;D<w.length;D++)E=w[D],a.beginPath(),a.moveTo(E.x1,E.y1),a.lineTo(E.x2,E.y2),a.stroke();a.restore()}_=function(){function a(a,b,c,d){this.X=a,this.Y=b,this.Width=c,this.Height=d,this.Left=this.X,this.Right=this.Left+this.Width,this.Top=this.Y,this.Bottom=this.Y+this.Height}return a}(),aa=function(){function a(){this.Wd=2,this.Xd=C,this.Yd=C,this.Zd=B,this.$d=-B}return a.prototype.paint=function(a,b,c,d,e,f){var g,h,i,j=this;j.options=b,j.setting=b.settings,g=b.values,h=b.dateValues,i=b.zoomFactor,a.save(),a.rect(c,d,e,f),a.clip(),a.beginPath(),0===b.sparklineType&&j._d(a,c,d,e,f,g,h,i),j.ae(a,c,d,e,f,g,h,i),j.be(a,c,d,e,f,g,h,i),a.restore()},a.prototype.ce=function(a){return 0===this.options.sparklineType?3+this.de(a)+1:3},a.prototype.ee=function(a,c){var d,e,g,h,i,m,n,o,p,r=this.fe;if(r)return r;if(r=this.fe=[],d=a.length,this.options.displayDateAxis){for(h=c.length,i=q(d,h),m=[],i>0&&(m=c.slice(0,i)),m.sort(function(a,c){return a===c?0:(a===b.Vd&&(a=0),c===b.Vd&&(c=0),a-c)}),n=m.length,o=void 0,p=void 0,e=0;e<n;e++)if(o=m[e],typeof o!==x&&o!==k){for(p=j(c,o);f.k.Bb(r,p);)p=j(c,o,p+1);isNaN(o)||(g=a[p],g!==l&&g!==k&&isNaN(g)&&g!==b.Vd||r.push(p))}}else for(e=0;e<d;e++)g=a[e],typeof g!==x&&g!==k&&isNaN(g)&&g!==b.Vd||r.push(e);return r},a.prototype.ge=function(a,c){var d=c[a];return typeof d===x||d===k?1===this.setting.options.displayEmptyCellsAs&&(d=0):d===b.Vd&&(d=0),d},a.prototype._d=function(a,b,c,d,e,f,g,h){var i,j,l,m,n,o,p,q,r,s,t,u,v,w,y,z=this,A=z.ee(f,g),B=A.length-1;for(B<0&&(B=0),m=z.setting.options,n=z.linePos=[],t=m.displayEmptyCellsAs,i=0;i<B;i++)if(o=z.ge(A[i],f),typeof o!==x&&o!==k){if(q=i+1,p=z.ge(A[q],f),typeof p===x||p===k)if(1===t)p=0;else if(2===t)for(q=i+2;q<=B;q++)if(u=f[A[q]],typeof u!==x&&u!==k){p=u;break}typeof p!==x&&p!==k?(r=z.he(A[i],{Width:d,Height:e},f,g,h),s=z.he(A[q],{Width:d,Height:e},f,g,h),v=r.Width/2,j={X:r.X+v,Y:r.Y+v},l={X:s.X+v,Y:s.Y+v},n[i]={P1:j,P2:l}):i++}if(w=n.length,w>0)for(a.strokeStyle=z.options.getColor(m.seriesColor),a.lineCap="round",a.lineWidth=z.de(h),i=0;i<w;i++)y=n[i],y&&(a.beginPath(),j=y.P1,l=y.P2,a.moveTo(b+j.X,c+j.Y),a.lineTo(b+l.X,c+l.Y),a.stroke(),a.closePath())},a.prototype.ie=function(a,b,c){var d,e,f,g=this,h=g.options,i=g.setting.options,m=k,n=g.ge(a,b),o=g.ee(b,c),p=o.length,q=h.getColor.bind(g);return typeof n!==x&&n!==k&&(g.Zd!==B&&g.$d!==-B||g.je(b),n===g.Zd&&i.showLow&&(m=q(i.lowMarkerColor)),n!==g.$d||!i.showHigh||typeof m!==x&&m!==k||(m=q(i.highMarkerColor)),typeof m!==x&&m!==k||(h.displayDateAxis?(d=j(o,a),0===d&&i.showFirst&&(m=q(i.firstMarkerColor))):0===a&&i.showFirst&&(m=q(i.firstMarkerColor))),typeof m!==x&&m!==k||(h.displayDateAxis?(e=j(o,a),e===p-1&&i.showLast&&(m=q(i.lastMarkerColor))):a===p-1&&i.showLast&&(m=q(i.lastMarkerColor))),n<0&&i.showNegative&&(typeof m===x||m===k)&&(m=q(i.negativeColor)),typeof m!==x&&m!==k||(f=h.sparklineType,0===f?i.showMarkers&&(m=q(i.markersColor)):1===f?m=q(i.seriesColor):2===f&&(m=q(i.seriesColor)))),m===l||m===k?"Transparent":m},a.prototype.ae=function(a,b,c,d,e,f,g,h){var i,j,k,l,o,p,q,r,s,t,u=this,v={Width:d,Height:e},w=u.ee(f,g),x=w.length,y=u.options.sparklineType;for(t=0;t<x;t++)i=w[t],j=u.ie(i,f,g),k=u.he(i,v,f,g,h),a.fillStyle!==j&&(a.fillStyle=j),0===y?(a.save(),l=b+k.X+k.Width/2,o=c+k.Y+k.Height/2,a.translate(l,o),a.rotate(45*n/180),a.fillRect(0-k.Width/2,0-k.Height/2,k.Width,k.Height),a.restore()):(p=b+k.X+k.Width/4,p=m(p),q=c+k.Y,r=k.Width/2,s=k.Height,a.fillRect(p,q,r,s))},a.prototype.be=function(a,b,c,d,e,f,g,h){var i,j,k,l,n,o,p,q=this,r=q.setting&&q.setting.options;r&&r.displayXAxis&&q.ke(f,g)&&(i={Width:d,Height:e},j=q.ce(h),k=i.Width-q.ce(h),l=m(q.le(i,f,h))+.5,n=l,o=q.options.getColor(r.axisColor),p=h,p<1&&(p=1),a.strokeStyle!==o&&(a.strokeStyle=o),a.lineWidth!==p&&(a.lineWidth=p),a.beginPath(),a.moveTo(b+j,c+l),a.lineTo(b+k,c+n),a.stroke())},a.prototype.me=function(a){var b,c=this.ne(a);return c===-B||(b=this.oe(a),b===B)||(c===b||c*b<=0)},a.prototype.ke=function(a,b){var c,d,e,f,g,h=this.me(a);if(2!==this.options.sparklineType)return h;if(c=this.ee(a,b),d=c.length,!h&&d>0)for(g=0;g<d;g++)if(e=c[g],f=a[e],typeof f!==x&&f!==k)return!0;return h},a.prototype.pe=function(a,b){var c=this.Xd;return isNaN(c&&c.valueOf())&&this.qe(a,b),this.Xd},a.prototype.re=function(a,b){var c=this.Yd;return isNaN(c&&c.valueOf())&&this.qe(a,b),this.Yd},a.prototype.qe=function(a,b){var c,d,e,f,g=this,h=new Date(0,0,0),i=B,j=g.ee(a,b),l=j.length;for(f=0;f<l;f++)c=j[f],d=b[c],isNaN(d)||(e=g.ge(c,a),e!==k&&typeof e===x||isNaN(e)||typeof d!==x&&d!==k&&(d>h&&(h=d),d<i&&(i=d)));g.Yd=h,g.Xd=i},a.prototype.se=function(a,b,c,d){var e,f,g,h,i,j,k,l,m=this,n=m.pe(b,c),o=m.re(b,c),p=[],q=m.ee(b,c),r=q.length;for(e=0;e<r;e++)g=q[e],f=c[g],f&&!isNaN(f)&&p.push(f);if(p.sort(function(a,b){
return a-b}),h=p.length,h>1&&n!==o){for(i=B,j=0,k=void 0,e=1;e<h;e++)k=p[e],f=k-p[e-1],f<i&&f>0&&(i=f),j+=f;return l=(a.Width-m.ce(d)-m.ce(d))*i/j/2,l<2&&(l=2),l}return(a.Width-m.ce(d)-m.ce(d))/2},a.prototype.te=function(a,b,c,d){var e,f=this;return f.options.displayDateAxis?f.se(a,b,c,d):(e=f.ee(b,c).length,(a.Width-f.ce(d)-f.ce(d))/e)},a.prototype.ue=function(a,b,c,d,e){var f,g,h,i,k,l,n,o,p=this,q=p.ce(e);return p.options.displayDateAxis?(f=p.te(a,c,d,e),g=p.re(c,d),h=p.pe(c,d),g===h?q+f/2:(i=d[b])?(k=a.Width-q-p.ce(e),k-=f,l=g.valueOf()-h.valueOf(),q+m((i.valueOf()-h.valueOf())/l*k)):0):(f=p.te(a,c,d,e),n=j(p.ee(c,d),b),o=q+f*n,m(o))},a.prototype.ve=function(a,b){var c,d=this,e=a.Width-d.ce(b)-d.ce(b);return e=r(e,0),c=a.Height-d.ce(b)-d.ce(b),c=r(c,0),{Width:e,Height:c}},a.prototype.je=function(a){var b,c,d=this,e=a.length;for(c=0;c<e;c++)b=a[c],typeof b!==x&&b!==k&&("number"!=typeof b&&(b=0),b<d.Zd&&(d.Zd=b),b>d.$d&&(d.$d=b))},a.prototype.ne=function(a){var b,c,d=this;return d.$d!==-B&&d.$d||d.je(a),b=d.setting.options,c=b.maxAxisType,0===c?d.$d:1===c?b.groupMaxValue:2===c?b.manualMax:d.$d},a.prototype.oe=function(a){var b,c,d=this;return d.Zd!==B&&d.Zd||d.je(a),b=d.setting.options,c=b.minAxisType,0===c?d.Zd:1===c?b.groupMinValue:2===c?b.manualMin:void 0},a.prototype.we=function(a,b,c,d){var e=this.ve(a,d),f=this.ne(c),g=this.oe(c),h=f-g,i,j;if(f===g){if(0===f)return 0;h=v(f)}return i=c[b],i||(i=0),j=e.Height/h,i*j},a.prototype.xe=function(a,b,c,d){var e,f,g,h=this,i=h.options.sparklineType;if(0===i)return h.we(a,b,c,d);if(1===i){if(e=c[b],1===h.setting.options.displayEmptyCellsAs&&(typeof e===x||e===k))return 0;if(f=h.we(a,b,c,d),f>-h.Wd&&f<h.Wd){if(e>0)return f+h.Wd;if(e<0)return f-h.Wd}return f}return 2===i?(e=c[b],typeof e===x||e===k||0===e||isNaN(e)?0:(g=h.ve(a,d),e>=0?g.Height/2:-g.Height/2)):void 0},a.prototype.ye=function(a,b,c){var d,e,f=this,g=f.ve(a,c),h=f.ne(b),i=f.oe(b);if(h===-B||i===B)return a.Height/2;if(d=h-i,h===i){if(0===h)return a.Height/2;d=h,h<0&&(h=0)}return e=g.Height/d,f.ce(c)+h*e},a.prototype.le=function(a,b,c){return 2===this.options.sparklineType?a.Height/2:this.ye(a,b,c)},a.prototype.ze=function(a,b,c,d){var e,f,g,h=this,i=h.ve(a,d),j=h.ne(c),l=h.oe(c),m=j-l;if(j===l){if(0===j)return 0;m=j}return e=i.Height/m,f=h.ge(b,c),typeof f!==x&&f!==k||(f=0),j!==l&&j*l>0?(g=0,g=f>=0?(f-l)*e:(f-j)*e):f*e},a.prototype.Ae=function(a,b,c,d){var e,f,g,h=this,i=h.options.sparklineType;return 0===i?h.ze(a,b,c,d):1===i?(e=h.ze(a,b,c,d),f=h.Wd,e>-f&&e<f&&(g=h.ge(b,c),typeof g!==x&&g!==k||(g=0),0!==g)?g>0?e+f:e-f:e):2===i?h.xe(a,b,c,d):void 0},a.prototype.Be=function(a,b,c,d,e){var f,g,h,i,j,k,l,n,o,p=this,q=p.te(b,c,d,e),s=p.ue(b,a,c,d,e);return q<0&&(q=0),q=m(q),q%2===1&&(q+=1),f=p.xe(b,a,c,e),g=p.le(b,c,e),h=p.ne(c),i=p.oe(c),j=0,h<0&&i<0?j=r(p.ce(e),g):(j=g,f>=0&&(j=g-f)),k=p.Ae(b,a,c,e),l=new _(s,j,q,v(k)),0!==f&&(n=p.ce(e),l.Y<n&&l.Bottom<n+1?l.Height=m(l.Height+1):(o=b.Height-p.ce(e),l.Bottom>o&&l.Y>o-1&&(l.Y=o-k,l.Height=k))),l},a.prototype.de=function(a){var b=this.setting.options.lineWeight*a;return b<1&&(b=1),b},a.prototype.he=function(a,b,c,d,e){var f,g,h,i,j,l=this,m=l.de(e);return m++,m<2&&(m=2),f=l.Be(a,b,c,d,e),0===l.options.sparklineType&&(f.X=f.X+(f.Width-m)/2,g=l.ge(a,c),typeof g!==x&&g!==k?(g>=0?f.Y-=m/2:f.Y=f.Bottom-m/2,f.Width=m,f.Height=m):(f.Width=0,f.Height=0)),l.setting.options.rightToLeft&&(h=f.X,i=b.Width-h,j=i-f.Width,f=new _(j,f.Y,f.Width,f.Height)),f},a}(),b.SparklineRender=aa,b.SparklineExRenders={PIESPARKLINE:oa,AREASPARKLINE:ra,SCATTERSPARKLINE:ta,BULLETSPARKLINE:ya,SPREADSPARKLINE:za,STACKEDSPARKLINE:Da,HBARSPARKLINE:Ja,VBARSPARKLINE:Ka,VARISPARKLINE:La,BOXPLOTSPARKLINE:Ma,CASCADESPARKLINE:Ra,PARETOSPARKLINE:Sa,MONTHSPARKLINE:Za,YEARSPARKLINE:_a}}}),a.exports=c.Spread},"./node_modules_local/@grapecity/js-sheets-common/index.js":function(a,b,c){a.exports=c("./node_modules_local/@grapecity/js-sheets-common/dist/gc.spread.common.js")}});