/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.CalcEngine=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="",c(c.s="./node_modules_local/@grapecity/js-calc/index.js")}({"./node_modules_local/@grapecity/js-calc/dist/gc.spread.calcEngine.js":function(a,b,c){var d="object"==typeof d?d:{};d.Spread=d.Spread||{},d.Spread.CalcEngine=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./src/calc.entry.ts")}({"./src/Parser.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./src/calc.common.ts"),f=void 0,g=null,h="string",i="boolean",j="TRUE",k="FALSE",l="ARRAY",m=d.Common.u,n=d.Common.j,o=n.Fa,p="Exp_FormulaInvalidChar",q="Exp_FormulaInvalid",r="Exp_NoSyntax";function F(){throw e.sR()[q]}function G(a,b,c){throw m.Kb(e.sR()[c||p],[a,b])}b.di=["+","-","%","+","-","*","/","^","&","=","<>","<","<=",">",">=",":",","," "],s={":":1,"^":2," ":3,"*":4,"/":4,",":5,"+":6,"-":6,"&":7,"<":8,"=":8,">":8,">=":8,"<=":8,"<>":8},t=".",u=",",v=";",w=",",x=[14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,11,24,24,24,26,24,24,24,20,21,24,25,24,19,24,24,8,8,8,8,8,8,8,8,8,8,24,24,25,25,25,24,24,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,20,24,21,27,18,27,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,20,25,21,25,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,11,24,26,26,26,26,28,28,27,28,1,22,25,19,28,27,28,25,10,10,27,1,28,24,27,10,1,23,10,10,10,24,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,25,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,25,1,1,1,1,1,1,1,1];function H(a){return a<=255}function I(a){return a<=127}function J(a){return a===e.getBoolean().boolean_true||a===j}function K(a){return a===e.getBoolean().boolean_false||a===k}function L(a){return a>=48&&a<=57}b.isDigit=L;function M(a){var b=a.charCodeAt(0);return!H(b)||(I(b)?(b|=32,b>=96&&b<=122):0===x[b]||1===x[b])}b.ei=M;function N(a){var b=a.charCodeAt(0);return!H(b)||(I(b)?b<=57?b>=48:(b|=32,b>=96&&b<=122):0===x[b]||1===x[b])}b.fi=N;function O(a){var b=a.charCodeAt(0);return I(b)?b>=48&&b<=57:8===x[b]||10===x[b]}b.isNumber=O;function P(a,b,c,d,e,f){var g,h,i=a.length,j=c===d?0:1,k=[],l=0;for(g=b+1;g<i;g++)if(h=a[g],h===e&&(k.push(h),g++,l++,h=a[g]),h===c&&j++,h===d)if(j--,c===d&&g+2<i&&a[g+1]===c)k.push(c),g++,l++;else{if(0===j)return{result:k,endIndex:g,skipCount:l};k.push(h)}else k.push(h);f&&G(d,c,r)}function Q(a,b,c){var d,f,g,h,i=a.length,j=i-b;for(d=0;d<e.ErrorList.length;d++)if(f=e.ErrorList[d],g=f.length,b+g<=i&&(h=a.slice(b,b+g),g<=j&&f===h.join("").toUpperCase()))return{result:h,endIndex:b+g-1};c&&G([a[b],b])}function R(a,b,c){var d,e,f=a.length,g=0,h=[];for(d=b;d<f;d++)if(e=a[d],L(e.charCodeAt(0)))0===g?g=2:3===g?g=4:1===g?g=2:5!==g&&6!==g||(g=7),h.push(e);else if(e===c){if(2===g)g=4;else{if(0!==g&&1!==g)return{};g=3}h.push(".")}else if("+"===e||"-"===e){if(0===g)g=1;else{if(5!==g)return{num:h,endIndex:d-1};g=6}h.push(e)}else if("E"===e||"e"===e){if(2!==g&&4!==g)return{};g=5,h.push(e)}else if(2===g||4===g||7===g)return{num:h,endIndex:d-1};return 2===g||4===g||7===g?{num:h,endIndex:f-1}:{}}y=[].push;function S(a,b){return y.apply(a,b),a}z=function(){function a(a,b,c,d,e){var f,g,h;this.oi=[],e=e||0,f=this,f.Qb=a,g=Array.isArray(a)?a.join(""):a,f.Nc=b,f.mi=c,f.li=d||c+a.length-1,0===b&&0===e&&a?(h=g.toUpperCase(),(J(h)||K(h))&&(e=5,g=h)):1===b&&g!==l&&(g=g.toUpperCase()),f.ni=g,f.hi=e}return a.prototype.type=function(){return this.Nc},a.prototype.subType=function(){return this.hi},a.prototype.stringValue=function(){return this.ni},a}(),b.FormulaToken=z,A=function(){function a(a){void 0===a&&(a=5e3),this.priority=a}return a.prototype.resolveToken=function(a,b,c,d){return 0},a.prototype.unparse=function(a,b,c,d){return!0},a}(),b.TokenResolver=A,b.preOperaterTypesMap={"+":0,"-":1},b.operaterTypesMap={":":15,",":16,"+":3,"-":4,"*":5,"/":6,"^":7,"%":2,"&":8,"=":9,"<":11,"<>":10,"<=":12,">":13,">=":14};function T(a,b){for(var c=0;c<a.length;c++)if(m.Mb(a[c],b))return!0;return!1}function U(a,b){var c,d,e,f,g,h,i=[];if(b&&a.indexOf(":")>-1)for(c=a.split(":"),d=b.map(function(a){return a.name()}),e=void 0,f=void 0,g=0;g<c.length;g++)h=c[g],e=T(d,c[g]),f=h.indexOf(" ")>-1,e&&f?i.push("'"+h+"'"):i.push(h);return i}function V(a,b,c,d){var e=[];return S(e,a.slice(0,b)),S(e,d),S(e,a.slice(c+1)),e}B=function(){function a(){}return a.prototype.setParserOption=function(b){var c,e;b!==f&&d.Common.CultureManager.q4()?(e="object"==typeof b&&b.NumberFormat?b.NumberFormat:d.Common.CultureManager.q4().NumberFormat,c=b?e:{}):(c={},e=c),u=a.listSeparator=c.listSeparator||",",t=c.numberDecimalSeparator||".",!b&&a.numberDecimalSeparator||(a.numberGroupSeparator=e.numberGroupSeparator||",",a.numberDecimalSeparator=e.numberDecimalSeparator||"."),v=a.arrayGroupSeparator=c.arrayGroupSeparator||";",w=a.arrayListSeparator=c.arrayListSeparator||","},a.prototype.parse=function(a,b){var c,d;return this.setParserOption(a.culture),c=b.split(""),this.gi=c,d=this.parseToTokens(a,c,!a.ignoreError),this.buildExpressionTree(a,d,!a.ignoreError)},a.prototype.unparse=function(a,b,c){return void 0===c&&(c=[]),this.Cma(a,b,c),c.join("")},a.prototype.Cma=function(a,b,c){var d,e;if(b)for(this.setParserOption(a.culture),d=a.getResolvers(),e=0;e<d.length&&!d[e].unparse(a,this,b,c);e++);},a.prototype.parseToTokens=function(a,c,d,e){var f,g,h,i,j,k,m,n,o,p,q,r,s,x,A;for(void 0===d&&(d=!0),f=this,g=function(a,b,c){return b.length>0&&a.push(new z(b,0,c)),[]},h=c.length,i=[],j=[],k=-1,m=[],p=0;p<h&&" "===c[p];)p++;for("="===c[p]&&p++,o=p,s=function(p){var s,A,B,C,D,E,F,H,I,J,K,M,N,O=c[p],T=O.charCodeAt(0),W=void 0;if(T>=97&&T<=122||T>=65&&T<=90)m.push(O);else if(34===T)W=P(c,p,'"','"',"",d),W?(s=new z(W.result,0,p,p+W.result.length+2,3),W.skipCount&&(s.HTa=c.slice(p,W.endIndex+1).join(""),s.li+=W.skipCount),i.push(s),p=W.endIndex,o=p+1):(S(m,c.slice(p,h)),p=h-1);else if(39===T)W=P(c,p,"'","'","",d),W?(A=a&&a.kj&&a.kj.parent,B=A&&A.sheets,C=U(W.result.join(""),B),D=[],C.length>1&&(E=C.join(":").split(""),D=f.parseToTokens(a,E,d),F=V(c,p,W.endIndex,E),e&&(e.newCharArray=F)),D&&D.length>0?(D.forEach(function(a){a.mi+=p,a.li+=p}),S(i,D)):(m.push("'"),S(m,W.result),m.push("'")),p=W.endIndex):(m.push("'"),p=h-1);else if(91===T)if(W=P(c,p,"[","]","'",d))m.push("["),S(m,W.result),m.push("]"),p=W.endIndex;else{if(f.lda&&0===m.length&&("R"===m[0]||"r"===m[0]||"C"===m[0]||"c"===m[0]))return x=p,"continue";S(m,c.slice(p,h)),p=h-1}else{if(13===T||10===T)return x=p,"continue";if(35===T)H=Q(c,p),H?(i.push(new z(H.result,0,p,p+H.result.length-1,6)),o=p+1,p=H.endIndex):m.push(O);else if(33===T)0===m.length,I=i.length,I>0&&0===i[I-1].Nc?(J=i[I-1],y.apply(J.Qb,m),i[I-1]=new z(J.Qb,0,J.mi,p,12)):i.push(new z(m,0,o,p,12)),m=[],o=p+1;else if(43===T||45===T)K=0===i.length?null:i[i.length-1],0!==m.length?(i.push(new z(m,0,o)),i.push(new z(O,5,p)),m=[],o=p+1):(K&&7===K.Nc&&i.pop(),!K||2!==K.hi&&6!==K.Nc&&0!==K.Nc?(i.push(new z(O,4,p)),o=p+1):(i.push(new z(O,5,p)),o=p+1));else if(O===t||L(T))m.length>0?m.push(O):(M=R(c,p,t),M.num?(q=M.endIndex,N=M.num,q<=h-2&&"!"===c[q+1]?(S(m,N),o=q):(i.push(new z(N,0,p,q,4)),o=q+1),p=q):m.push(O));else if(123===T)m.length>0&&d&&G(O,p),n=new z(l,1,p,p,1),i.push(n),j[++k]=n,o=p+1;else if(O===v&&k>=0&&j[k].value===l)m=g(i,m,o),k<0&&d&&G(O,p),i.push(new z(v,3,p,p)),o=p+1;else if(125===T)m=g(i,m,o),q<0&&d&&G(O,p),r=j[k--],r=new z(O,r.Nc,p,p,2),i.push(r),o=p+1;else if(32===T)m=g(i,m,o),o=p+1;else if(O===u||O===w||O===v)m=g(i,m,o),k<0||1!==j[k].Nc?i.push(new z(u,5,p,p,10)):i.push(new z(O,3,p,p)),o=p+1;else if(37===T)m=g(i,m,o),i.push(new z(c[p],6,p,p)),o=p+1;else if(p+2<=h&&b.operaterTypesMap[O+c[p+1]])m=g(i,m,o),i.push(new z(c.slice(p,p+2),5,p,p+1,5)),p++,o=p+1;else if(b.operaterTypesMap[O])m=g(i,m,o),i.push(new z(O,5,p,p)),o=p+1;else if("("===O)m.length>0?(n=new z(m,1,o,p,1),m=[]):n=new z(O,2,p,p,1),i.push(n),j[++k]=n,o=p+1;else if(")"===O){if(m=g(i,m,o),k<0)return d&&G(O,p),x=p,"continue";r=j[k--],r=new z(O,r.Nc,p,p,2),i.push(r),o=p+1}else if(":"===O)m=g(i,m,o),i.push(new z(O,5,p,p,11)),o=p+1;else if("\r"===O||"\n"===O);else{if("\\"===O){if(p++,p>=h)return d&&G(O,p),x=p,"continue";m.push(O),O=c[p]}m.push(O)}}x=p},A=p;A<h;A++)s(A),A=x;return g(i,m,o),i},a.prototype.buildExpressionTree=function(a,b,c){var d=this.ii(a,b,c),e=this.ji(a,d,c);return c&&(e||F(),this.rSa(e)),e},a.prototype.rSa=function(a){for(var b,c,d,e,f,g,h,i;10===a.type;)a=a.value;if(b=this,9===a.type)a.value||F(),b.rSa(a.value),a.value2&&b.rSa(a.value2);else if(7===a.type)for(c=a.arguments,d=c.length,e=a.function,e&&(f=e.minArgs,g=e.maxArgs,(d<f||d>g)&&F()),h=0;h<d;h++)i=c[h],i||F(),b.rSa(i)},a.prototype.ji=function(a,b,c){var d,f,g,h,i,j,k=this.ki(a,b,c);for(h=3;h<k.length;)if(i=k[h],j=k[h-2],9!==j.type&&F(),i&&9===i.type){for(;h>=3&&s[i.value]>=s[j.value];)f=k[h-3],g=k[h-1],d=e.Kh(j.operatorType,f,g),k.splice(h-3,3,d),h-=2,h>=3&&(j=k[h-2]);h+=2}else h++;if(1===k.length)return k[0];for(h=k.length-2;h>0;h-=2)f=k[h-1],g=k[h+1],9!==k[h].type&&F(),d=e.Kh(k[h].operatorType,f,g),k.splice(h-1,3,d);return d},a.prototype.ki=function(a,b,c){var d,f,g,h,i,j,k,l,m,n,o,p,q,r;for(b=Array.isArray(b)?b:[b],d=[],f=b.length,g=0;g<f;g++){if(h=b[g],i=h,9===h.type){if(0===h.operatorType||1===h.operatorType)if(g===f-1)c&&F();else{for(j=g;9===b[j].type&&b[j].operatorType<=1;)j++;for(k=this.ji(a,b[j]),l=j-1;l>=g;l--)i=e.Kh(b[l].operatorType,k),k=i;i=k,g=j}else if(2===h.operatorType){if(0!==g){i=e.Kh(h.operatorType,this.ji(a,d[d.length-1])),d[d.length-1]=i;continue}c&&F()}}else if(10===h.type)h.value=this.ji(a,h.value),i=h;else if(7===h.type){if(m=0,n=h.arguments,o=[],p=[],n.length>0){for(q=0;q<=n.length;q++)r=n[q],q===n.length||0===r.type&&r.value===u?(m===q?o.push(new e.Expression(11)):o.push(this.ji(a,p)),m=q+1,p=[]):p.push(r);h.arguments=o}i=h}d.push(i)}return d},a.prototype.ii=function(a,b,c){var d,e,f=[];for(d=0,e=b.length;d<e;)d=this.resolveToken(a,b,f,d,c);return f},a.prototype.resolveToken=function(a,b,c,d,h){var i,j,k,l,m,n,o=b[d],p=a.getResolvers();for(i=0;i<p.length;i++)if(j=p[i].resolveToken(a,this,b,d)){k=j.expr,l=d-1,k&&j.index!==f?d=j.index-1:k=j,m=c.length>0?c[c.length-1]:g,m&&0!==k.type&&7!==k.type&&k.type<9&&0!==m.type&&7!==m.type&&m.type<9&&(n=b[l].li,n<o.mi-1&&c.push(e.Kh(17," "))),c.push(k);break}return d+1},a.prototype.resolveSubTokens=function(a,b,c,d){for(var e=[];c<b.length&&2!==b[c].hi;)c=this.resolveToken(a,b,e,c,d);return{exprs:e,index:c+1}},a}(),b.Parser=B,C=function(){function a(a,b,c,d){this.Eh=!1,this.ignoreError=!1;var e=this;e.useR1C1=b,e.baseIdentity=c,e.option=d,e.source=a}return a.prototype.getExternalSource=function(a,b){var c,d=this.source.getCalcService(),e=d.getAllSourceModels();for(c=0;c<e.length;c++)if(m.Mb(e[c].getSource().getName(),b))return e[c].getSource();return g},a.prototype.getExternalSourceToken=function(a){return a.getName()},a.prototype.setSource=function(a){this.source=a},a.prototype.getResolvers=function(){var a=(this.source||this.kj).getCalcService();return a&&a.getResolvers()},a.prototype.addResolver=function(a){var b=(this.source||this.kj).getCalcService();b&&b.addResolver(a)},a.prototype.getFunction=function(a){var b,c=a,d=e.getMapping()&&e.getMapping().builtInFunctionsMapping;if(!o(d))for(b in d)d[b].alias===a&&(c=b);return this.source&&this.source.getCustomFunction(c)||e.Functions.findGlobalFunction(a)},a}(),b.ParserContext=C,e.CalcSource.prototype.getParserContext=function(a,b,c){return new C(this,a,b,c)};function W(a,b,c){var d,e;if(!b||0===b.length||X(a,b)||X(a,c))return[];if(d=b,c)for(d.push(":"),e=0;e<c.length;e++)d.push(c[e]);return d}function X(a,b){if(!b||0===b.length||!a.Eh)return!1;var c="'"===b[0]?1:0;return"["===b[c]&&b.indexOf("]")>c}D=function(a){E(c,a);function c(){return null!==a&&a.apply(this,arguments)||this}return c.prototype.resolveToken=function(a,c,d,f){var g,h,i,j,k,n,o,p,q,r=d[f],s=r.ni,t=r.Qb,u=r.Nc,w=null,x=r.hi;if(0===u&&12===x){if(k=void 0,s&&(k=s,"'"===k.charAt(0)&&(k=k.substring(1,k.length-1))),f++,k&&(w=a.getExternalSource("",k)),r=d[f],!r)return g=new e.Expression(8),g.value=s,g;s=r.ni}if(0===u)0===r.hi?(i=s.toUpperCase(),J(i)||K(i)?(r.hi=5,r.ni=i,g=new e.Expression(4),g.value=J(i)):(g=new e.Expression(8),g.value=s)):4===r.hi?(g=new e.Expression(2),g.value=parseFloat(s),g.originalValue=s):3===r.hi?(g=new e.Expression(3),g.value=s):5===r.hi?(g=new e.Expression(4),g.value=J(s)):6===r.hi?(g=new e.Expression(5),g.value=e.CalcError.parse(s)):7===r.hi&&(g=new e.Expression(8),g.value=s);else if(1===u){if(s===l){for(g=new e.Expression(6),f++,i=[[]],h=0,n=0,o=0;f<d.length&&2!==d[f].hi;){if(r=d[f],f<d.length-1&&4===r.Nc&&"-"===r.ni&&4===d[f+1].hi&&(f++,r=d[f],r.Qb.unshift("-"),r.ni="-"+r.ni),3===r.Nc){if(r.ni===v){if(n!==o)throw m.Kb(e.sR().Exp_InvalidArrayColumns,[r.mi]);i.push([]),h++,o=0}}else{if(0!==r.Nc||4!==r.hi&&3!==r.hi&&5!==r.hi&&6!==r.hi)throw e.sR().Exp_InvalidArray;p=r.ni,q=p,4===r.hi?q=parseFloat(p):5===r.hi?q="TRUE"===p.toUpperCase():6===r.hi&&(q=e.CalcError.parse(p)),i[h].push(q),0===h&&n++,o++}f++}if(n!==o)throw m.Kb(e.sR().Exp_InvalidArrayColumns,[r.mi]);return f++,g.value=new e.CalcArray(i),{expr:g,index:f}}if(t.length>0)return"@"===t[0]&&(r.ni=t.slice(1).join(""),s=r.ni),g=new e.Expression(7),g.functionName=s,g.function=a.getFunction(g.functionName)||e.Functions.findGlobalFunction(g.functionName),(j=c.resolveSubTokens(a,d,f+1))?(g.arguments=j.exprs,{expr:g,index:j.index}):(g.arguments=[],g)}else if(4===u||6===u||5===u)g=e.Kh(4===u?b.preOperaterTypesMap[s]:b.operaterTypesMap[s],s);else if(2===u){if(g=new e.Expression(10),j=c.resolveSubTokens(a,d,f+1))return g.value=j.exprs,{expr:g,index:j.index};F()}else 3===u&&(g=new e.Expression(0),g.value=s);return w&&g&&(g.source=w,g={expr:g,index:f+1}),g},c.prototype.unparse=function(a,b,c,d){var f,g,h,i=this;if(6===c.type||4===c.type||2===c.type||5===c.type||3===c.type)i.qi(a,c,d);else if(9===c.type)i.ri(a,b,c,d);else if(10===c.type)d.push("("),b.Cma(a,c.value,d),d.push(")");else{if(7!==c.type)return(1===c.type||8===c.type)&&(i.unparseRefenceExpressions(a,b,c,d),!0);for(f=e.getMapping()&&e.getMapping().builtInFunctionsMapping,g=f&&f[c.function&&c.function.name||c.functionName],S(d,(g&&g.alias||c.function&&c.function.name||c.functionName).split("")),d.push("("),h=0;h<c.arguments.length;h++)0!==h&&d.push(u),b.Cma(a,c.arguments[h],d);d.push(")")}return!0},c.prototype.unparseSource=function(a,b,c,d){if(a.getExternalSourceToken){if(c!==e.BangSource){var f=W(a,a.getExternalSourceToken(c).split(""),d&&a.getExternalSourceToken(d).split(""));if(!(f.length>0))return!1;y.apply(b,f)}return b.push("!"),!0}return!1},c.prototype.unparseRefenceExpressions=function(a,b,c,d){if(c.source===e.BangSource)d.push("!");else if(c.source){var f=this.unparseSource(a,d,c.source,c.endSource);if(!f)return void d.push("#","R","E","F","!")}8===c.type&&d.push(c.value)},c.prototype.ri=function(a,c,d,e){var f=d.operatorType;2===f?(c.Cma(a,d.value,e),e.push("%")):0===f||1===f?(e.push(b.di[f]),c.Cma(a,d.value,e)):(c.Cma(a,d.value,e),e.push(b.di[f]),c.Cma(a,d.value2,e))},c.prototype.qi=function(a,b,c){var d,j,k,l,n,o,p,q,r=e.sR().Exp_InvalidArray;if(3===b.type)c.push('"'),c.push(m.Gb(b.value,'"','""')),c.push('"');else if(2===b.type)d=b.originalValue||""+b.value,"."!==t&&(d=d.replace(".",t)),c.push(d);else if(4===b.type)c.push(b.value?e.getBoolean().boolean_true:e.getBoolean().boolean_false);else if(6===b.type){if(c.push("{"),j=b.value,j.getRowCount()<=0)throw r;for(k=0,l=j.getRowCount();k<l;k++)for(k>=1&&c.push(v),n=0,o=j.getColumnCount();n<o;n++){if(0!==n&&c.push(w),p=j.getValue(k,n),p===f||p===g)throw r;q=typeof p,q===h?(c.push('"'),c.push(p),c.push('"')):q===i?c.push(p?e.getBoolean().boolean_true:e.getBoolean().boolean_false):"number"===q&&"."!==t?c.push((""+p).replace(".",t)):c.push(""+p)}c.push("}")}else 5===b.type?c.push(""+b.value):11===b.type},c}(A),b.DefaultTokenResolver=D,e.ParserWrapper.XAb=function(){if(B.numberDecimalSeparator===f){var a=new B;a.setParserOption(!0)}return B.numberDecimalSeparator},e.ParserWrapper.YAb=function(){if(B.numberGroupSeparator===f){var a=new B;a.setParserOption(!0)}return B.numberGroupSeparator}},"./src/calc.common.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./src/calcEngine.res.en.ts"),b.SR={en:e.resource},b.ErrorCodeList=[0,7,15,23,29,42,36],f="number",g="string",h="boolean",i="TRUE",j="FALSE",k=d.Common.j,l=k.Ca,m=k.Fa,n=k.Ea,o=d.Common.k.ac,p=d.Common.q,q=parseInt,r=isNaN,s=d.Common.l,t=s.Ra,u=d.Common.u;function ia(a){return a&&a.toUpperCase()}v=new d.Common.ResourceManager(b.SR),b.sR=v.getResource.bind(v),b.getMapping=function(){return ga&&ga.resourceMapping},b.getBoolean=function(){var a=b.getMapping()&&b.getMapping().booleanMapping;return{boolean_true:a&&a.boolean_true||i,boolean_false:a&&a.boolean_false||j}},w="#NULL!",x="#DIV/0!",y="#VALUE!",z="#REF!",A="#NAME?",B="#N/A",C="#NUM!",D=[w,x,y,z,A,B,C],b.ErrorList=D,E=null,F=void 0,G=Math.abs,H=Math.exp,I=Math.log,J=Math.pow,K=Math.floor,L={},b.BangSource=L,M={},b.RefErrorSource=M,b.missingArgument=N,O={},P=function(){function a(c){var d=a.nf(c);if(!d)throw b.sR().Exp_NotSupported;this._error=d.error,this._code=d.code}return a.prototype.toString=function(){var a=b.getMapping()&&b.getMapping().clacErrorMapping,c=a&&a[this._error];return c||this._error},a.prototype.clone=function(){return new a(this._error)},a.prototype.toJSON=function(){return{_calcError:this._error,_code:this._code}},a.parse=function(b){try{return new a(b)}catch(a){return F}},a.nf=function(a){var c,d;if(a)for(c=0;c<D.length;c++)if(d=D[c],d===a||d===ia(a))return{error:d,code:b.ErrorCodeList[c]};return F},a}(),b.CalcError=P,Q={Name:new P(A),Null:new P(w),DivideByZero:new P(x),NotAvailable:new P(B),Value:new P(y),Reference:new P(z),Number:new P(C)},b.Errors=Q,R=Q.Value,S=Q.Reference,T=Q.Number,U=Q.DivideByZero,V=Q.NotAvailable,W=Q.Null,X={JAN:1,FEB:2,MAR:3,APR:4,MAY:5,JUN:6,JUL:7,AUG:8,SEP:9,OCT:10,NOV:11,DEC:12},Y=function(){function a(){}return a}(),b.ParserWrapper=Y;function ja(a){return isNaN(a)||!isFinite(a)}function ka(a,b){var c,d,e,i,j,k,l=E;if(!a)return b.value=0,!0;c=typeof a;try{if(c===f)l=a;else if(c===g){if(a=a.trim(),0===a.length)return b.value=0,!0;if(d=Y.XAb(),e=Y.YAb(),"."!==d&&"."!==e&&a.indexOf(".")>=0||","!==d&&","!==e&&a.indexOf(",")>=0)return!1;if(i=!1,"%"===a.charAt(a.length-1)&&(i=!0,a=a.substr(0,a.length-1)),"$"===a[0]||"$"===a[a.length-1])return!1;if(a.indexOf(d)!==a.lastIndexOf(d))return!1;if(a.length>=2&&"0"===a[0]&&"x"===a[1])return!1;if(l=(+a).valueOf(),ja(l)){if(j=a.charCodeAt(0),j|=32,a.length>4&&j>=96&&j<=122&&(k=a[0]+a[1]+a[2],k=k.toUpperCase(),!X[k]))return!1;if("/"===a[0]||"/"===a[a.length-1])return!1;if("#"===a[0]||"#"===a[a.length-1])return!1;if(l=new Date(a),ja(l.valueOf()))return!1;l=s.Ra(l)}i&&(l/=100)}else if(c===h)l=a?1:0;else{if(!(a instanceof Date))return!1;l=s.Ra(a)}}catch(a){return!1}return b.value=l,!0}function la(a){var c={value:0};if(ka(a,c))return c.value;throw b.sR().Exp_InvalidCast}function ma(a,b){return b&&(a===E||a===F||typeof a===g&&""===a.trim())}function na(a,b){try{if(a)if(typeof a===h);else if(a instanceof Date)a=0!==s.Ra(a);else{if(!pa(a))return!1;a=0!==a}else a=!1}catch(a){return!1}return b.value=a,!0}function oa(a,b,c,e,i){var j,k,l;if(qa(a))return a;if(j={value:E},k=O,0!==b&&ma(a,e))return k;switch(b){case 1:case 5:if(c&&5!==b){if(typeof a===h&&i)return k;a=ka(a,j)?j.value:k}else typeof a===f||(a=a instanceof Date?s.Ra(a):k);break;case 4:typeof a===g?(l=d.Common.l.Qa(a),a=l!==F&&l!==E?s.Ra(l):k):c?a=ka(a,j)?j.value:k:typeof a===f||a instanceof Date||(a=k);break;case 3:c?a=ka(a,j)?j.value:k:typeof a!==h&&(a=k);break;case 2:a=a===E||a===F?"":""+a}return a}Z=function(){function a(a){this.array=a}return a.prototype.getRowCount=function(){return this.array.length},a.prototype.getColumnCount=function(){return this.array[0].length},a.prototype.getValue=function(a,b){var c,d=this.array,e=a;return"number"!=typeof b&&(c=a,e=c.row,b=c.column),e<0||b<0||e>=d.length||b>=d[0].length?R:this.array[e][b]},a.prototype.getValueByIndex=function(a){var b=this.getColumnCount(),c=Math.floor(a/b),d=a%b;return this.getValue(c,d)},a.prototype.toArray=function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p;for(void 0===b&&(b=!0),h=this,i=1,j=1,k=[],n=O,i=h.getRowCount(),j=h.getColumnCount(),o=0;o<i;o++)for(b||(m=[],k.push(m)),p=0;p<j;p++){if(l=h.getValue(o,p),c&&qa(l))return k=[l],k.isError=!0,k;if(l=oa(l,a,!1,f,g),d&&l===n)return k=[l],k.isError=!0,k;b?k.push(l):m.push(l)}return k.rowCount=i,k.colCount=j,k.rangeCount=1,k},a.prototype.slice=function(b,c,d,e){var f,g,h,i=this,j=i.array,k=[];for(b=b<0?0:b,c=c<0?0:c,d=b+d>i.getRowCount()?i.getRowCount()-b:d,e=c+e>i.getColumnCount()?i.getColumnCount()-c:e,g=0;g<d;g++)for(f=[],k.push(f),h=0;h<e;h++)f.push(j[g+b][h+c]);return new a(k)},a}(),b.CalcArray=Z;function pa(a,b){return typeof a===f||a instanceof Date||!b&&(typeof a===h||!isNaN(a)&&!isNaN(parseFloat(a)))}function qa(a){return a instanceof P}function ra(a){return a instanceof Z}function sa(a){return a instanceof ca}$=["o","n","s","b","d"],_=function(){function a(){}return a.jja=function(a){return qa(a)&&a._code===b.ErrorCodeList[5]},a.Nh=function(a){return isNaN(a)||!isFinite(a)?T:a},a.Ph=function(b,c,d,e,f,g,h,i){var j,k,m;if(b&&b.length&&b.rowCount&&b.colCount)return b;if(ra(b))j=b.toArray(c,d,e,f,g),j.isArray=!0;else if(sa(b))j=b.toArray(c,d,e,f,g,h,i),j.isReference=!0;else if(l(b)&&b.length>0){if(j=[],l(b[0]))return a.Oh(b,c,!0);for(k=0;k<b.length;k++)m=oa(b[k],c,!1),m===a.CalcConvertedError&&(j.isConvertError=!0),j.push(m);j.rowCount=1,j.colCount=b.length,j.rangeCount=1}else b=oa(b,c,!0),e&&a.vf(b)?(j=[R],j.isError=!0):b===a.CalcConvertedError?(j=[a.CalcConvertedError],j.isConvertError=!0):d?(j=[],j.push(b)):(j=[[]],j[0].push(b)),j.rowCount=1,j.colCount=1,j.rangeCount=1;return j},a.Oh=function(b,c,d){var e,g,i,j,k,l,m,n,o,p,q,r,s;if(!b||!b.length||!b[0].length)return e=[],e.isError=!0,[[R]];if(0===c)return b;if(g=$[c],i=b.length,j=b[0].length,n={value:E},o=!1,p=!1,q=a.CalcConvertedError,e=b[g],!e)for(e=[],b[g]=e,r=0;r<i;r++)for(k=b[r],l=e[r]=[],s=0;s<j;s++)if(m=k[s],qa(m))o||(e.isError=!0,o=!0),l.push(m);else{switch(c){case 1:case 5:d&&5!==c?ka(m,n)?m=n.value:(p||(p=!0,e.isConvertError=!0),m=0):typeof m===f||m instanceof Date||(p||(p=!0,e.isConvertError=!0),m=q);break;case 3:d?na(m,n)?m=n.value:(p||(p=!0,e.isConvertError=!0),m=!1):typeof m!==h&&(p||(p=!0,e.isConvertError=!0),m=q);break;case 2:m=m===E||m===F?"":""+m}l.push(m)}return e},a.Rh=function(a){var c=la(a);if(G(c)<1e21)return parseInt(""+c,10);throw b.sR().Exp_InvalidCast},a.Sh=function(a,b){var c=ka(a,b);return!!c&&(G(b.value)<1e21&&(b.value=parseInt(""+b.value,10),!0))},a.Vh=function(c){if(!c)return!1;if(typeof c===h)return c;if(c instanceof Date)return 0!==s.Ra(c);if(a.Na(c))return 0!==c;if(a.vf(c))return!1;throw b.sR().Exp_InvalidCast},a.bc=function(a){try{if(a===F||a===E)return"";if(typeof a===h)return a?i:j;if(typeof a===g)return a;if(a instanceof Date)return""+s.Ra(a);if(ra(a))throw b.sR().Exp_InvalidCast;return""+a}catch(a){throw b.sR().Exp_InvalidCast}},a.Wh=function(c){var d={value:E};if(a.Xh(c,d))return d.value;throw b.sR().Exp_InvalidCast},a.Xh=function(a,b){var c,e,h,i;if(a)if(a instanceof Date)b.value=new Date(a.valueOf());else if(typeof a===g){if(c=d.Common.l.Qa(a),!c)if(isNaN(a)){if(c=new Date(a),isNaN(c.valueOf()))return!1;try{if(e=/^[-+=\s]*(\d+)\W+(\d+)\W+(\d+)$/,h=u.ib(u.Db(a.replace(/ |\n/g,"")," ")," "),i=e.exec(h),i&&4===i.length&&(i.indexOf(""+c.getFullYear())===-1||i.indexOf(""+c.getMonth())===-1||i.indexOf(""+c.getDate())===-1))return!1}catch(a){return!1}}else if(c=d.Common.l.Xb(parseFloat(a)),!c)return!1;b.value=c}else{if(typeof a!==f)return!1;b.value=d.Common.l.Xb(a)}else b.value=d.Common.l.Xb(0);return!0},a.CalcConvertedError=O,a.Ca=ra,a.Na=pa,a.vf=qa,a.Fh=sa,a.wf=oa,a.j$a=ma,a.Qh=ja,a.Pa=la,a.Th=ka,a.Uh=na,a}(),b.Convert=_,function(a){a[a.unknow=0]="unknow",a[a.reference=1]="reference",a[a.number=2]="number",a[a.string=3]="string",a[a.boolean=4]="boolean",a[a.error=5]="error",a[a.array=6]="array",a[a.function=7]="function",a[a.name=8]="name",a[a.operator=9]="operator",a[a.parentheses=10]="parentheses",a[a.missingArgument=11]="missingArgument",a[a.expand=12]="expand",a[a.structReference=13]="structReference"}(aa=b.ExpressionType||(b.ExpressionType={})),ba=function(){function a(a){this.type=a}return a.prototype.sf=function(){return E},a.prototype.offset=function(a,b,c,d,e){return E},a.prototype.offsetWhenCopy=function(a,b,c,d,e,f,g,h,i,j){return E},a.prototype.getRange=function(a,b){var c,d=[];for(c=2;c<arguments.length;c++)d[c-2]=arguments[c];return E},a}(),b.Expression=ba,ca=function(){function a(a,b){var c=this;c.xf=a,b?(c.yf=b,c.zf=b.length):c.zf=1}return a.prototype.getRangeCount=function(){return this.zf},a.prototype.getSource=function(){return this.xf},a.prototype.create=function(b){return new a(this.xf,b)},a.prototype.toArray=function(a,b,c,d,e,f,g){var h=this.xf,i=h.referenceToArray;return i&&i.call(h,this,a,b,c,d,e,f,g)},a}(),b.CalcReference=ca,da=function(){function a(b){this.id=a.sourceId++,this.Af=b,this.Bf=null}return a.prototype.getCalcService=function(){return this.Af},a.prototype.Cf=function(){return this.Af},a.prototype.setCalcService=function(a){this.Af=a},a.prototype.getCalcSourceModel=function(){return this.Bf},a.prototype.setCalcSourceModel=function(a){this.Bf=a},a.prototype.getValue=function(){var a,b=[];for(a=0;a<arguments.length;a++)b[a]=arguments[a];return E},a.prototype.getName=function(){return""},a.prototype.setValue=function(){var a,b=[];for(a=0;a<arguments.length;a++)b[a]=arguments[a]},a.prototype.getReference=function(a){return new ca(this,a&&[a])},a.prototype.getSources=function(a){return[this]},a.prototype.referenceToArray=function(a,b,c,d,e,f,g,h){return a.toArray(b,c,d,e,f,g,h)},a.prototype.setFormula=function(){var a,b=[];for(a=0;a<arguments.length;a++)b[a]=arguments[a]},a.prototype.setArrayFormula=function(){var a,b=[];for(a=0;a<arguments.length;a++)b[a]=arguments[a]},a.prototype.getRowCount=function(){return 1e3},a.prototype.getColumnCount=function(){return 100},a.prototype.isHiddenRow=function(a,b){return!1},a.prototype.startCalculation=function(){},a.prototype.endCalculation=function(){},a.prototype.getCustomFunction=function(a){return E},a.prototype.getCustomName=function(a){return E},a.prototype.refresh=function(){},a.prototype.toJSON=function(a,b){var c,d=[];for(c=2;c<arguments.length;c++)d[c-2]=arguments[c]},a.sourceId=1,a}(),b.CalcSource=da;function ta(a,b,c){var d=new ba(5);return b&&(d.source=b),c&&(d.endSource=c),d.value=a,d}b.Hh=ta,b.Ih=ta(S);function ua(a,b){var c=new ba(7);return c.arguments=b,a instanceof ba?(c.function=a.function,c.functionName=a.functionName):(c.function=a.name&&a,c.functionName=a.name||a),c}b.Jh=ua;function va(a,b){var c=new ba(12);return c.value=a,c.needExpendIndexs=b,c}b.createExpandExpression=va;function wa(a,b,c){var d=new ba(9);return d.operatorType=a,d.value=b,d.value2=c,d}b.Kh=wa;function xa(a){var b=new ba(10);return b.value=a,b}b.Lh=xa;function ya(a){var b=new ba(3);return b.value=a,b}b.createStringExpression=ya;function za(a){var b=new ba(2);return b.value=a,b}b.createNumberExpression=za,ea=function(){function a(){}return a.Yh=function(a,b){var c=16777216;return a===b||G(a-b)<G(a)/(c*c)},a.getArrayValue=function(a,b,c){return a?b<a.getRowCount()&&c<a.getColumnCount()?a.getValue(b,c):c>=a.getColumnCount()&&1===a.getColumnCount()&&b<a.getRowCount()?a.getValue(b,0):b>=a.getRowCount()&&1===a.getColumnCount()&&c<a.getColumnCount()?a.getValue(0,c):R:R},a.tryExtractToSingleValue=function(a){var b,c=!0;return(sa(a)||ra(a))&&(b=_.Ph(a,0,!1,!0),b.isError?a=b[0]:1===b.rowCount&&1===b.colCount?a=b[0][0]:(a=b,c=!1)),{value:a,success:c}},a}(),b.Zh=ea;function Aa(a,b){return a.charAt(b)}b.charAtFunc=Aa;function Ba(a,b){for(var c=a.length,d=!1;b<c&&!isNaN(+Aa(a,b));)b++,d=!0;for(b<c&&"."===Aa(a,b)&&b++;b<c&&!isNaN(+Aa(a,b));)b++,d=!0;if(b<c&&("E"===Aa(a,b)||"e"===Aa(a,b)))for(b++,d=!1,b<c&&("+"===Aa(a,b)||"-"===Aa(a,b))&&b++;b<c&&!isNaN(+Aa(a,b));)b++,d=!0;return{Vi:d,mi:b}}function Ca(a){return"string"==typeof a}fa=function(){function a(a){this.Di=a}return a.prototype.$j=function(a){var b,c,d,e,f=this.Di;if(k.Ca(f)||(f=[f]),f[0]===-1)return!0;for(b=0;b<f.length;b++)if(c=f[b],d=void 0,e=void 0,Ca(c)){if(c=c.split(" "),d=c[0],e=q(c[1]),"!="===d&&a!==e||">"===d&&a>e||">="===d&&a>=e||"%="===d&&a%2===e)return!0}else if(a===c)return!0;return!1},a.prototype._j=function(){return this.Di},a}(),function(a){var c,d,e,f,g,h,i,j,l,s,u,v,w,x,y,z,A;!function(a){a[a.normal=0]="normal",a[a.allwaysExpand=1]="allwaysExpand",a[a.neverExpand=2]="neverExpand"}(c=a.ArrayArgumentEvaluateMode||(a.ArrayArgumentEvaluateMode={})),d=function(){function a(a,b,c,d){void 0===b&&(b=0),void 0===c&&(c=0),this.name=a,this.minArgs=b,this.maxArgs=c;var e=this;e.$h=d,e.typeName=""}return a.prototype.description=function(){var a=this,c=b.sR()._h;return a.$h?a.$h:c&&c[a.name]},a.prototype.acceptsArray=function(a){return!1},a.prototype.acceptsReference=function(a){return!1},a.prototype.acceptsError=function(a){return!1},a.prototype.acceptsMissingArgument=function(a){return!1},a.prototype.isVolatile=function(){return!1},a.prototype.isContextSensitive=function(){return!1},a.prototype.isBranch=function(){return!1},a.prototype.findTestArgument=function(){
return-1},a.prototype.findBranchArgument=function(a){return-1},a.prototype.expandRows=function(){return!0},a.prototype.expandColumns=function(){return!0},a.prototype.precedentReference=function(){return!0},a.prototype.returnReference=function(){return!1},a.prototype.evaluate=function(){var a,b=[];for(a=0;a<arguments.length;a++)b[a]=arguments[a]},a.prototype.evaluateWithContext=function(a,b){var c=this,d=b;return c.isContextSensitive()&&(d=[a].concat(d)),c.evaluate.apply(c,d)},a.prototype.toJSON=function(){var a,b={};for(a in this)this.hasOwnProperty(a)&&(b[a]=this[a]);return b},a.prototype.fromJSON=function(a){if(a)for(var b in a)a[b]!==F&&(this[b]=a[b])},a.ai=function(b,c,d){var e,f=new a(b,0,255);if(c&&"function"==typeof c&&(f.evaluate=c),d)for(e in d)d.hasOwnProperty(e)&&"override"!==e&&(f[e]=d[e]);return f},a}(),a.Function=d,e=function(a){ha(b,a);function b(b,c,d,e){return a.call(this,b,c,d,e)||this}return b.prototype.isContextSensitive=function(){return!0},b.prototype.evaluate=function(a,b){return this.evaluateAsync.apply(this,arguments)},b.prototype.evaluateAsync=function(a,b){this.evaluate.apply(this,arguments)},b.prototype.defaultValue=function(){return 0},b.prototype.evaluateMode=function(){return 0},b.prototype.evaluateWhenArgumentsIsSame=function(){return!0},b.prototype.interval=function(){return 1e4},b}(d),a.AsyncFunction=e,a.AsyncFunctionEvaluateMode={onRecalculation:0,calculateOnce:1,onInterval:2},a.bi={},a.ci={};function B(b,c){return a.ci[ia(b)]=c,c}a.defineGlobalCustomFunction=B;function C(c){var d,e,f;return c?(c=ia(c),d=a.bi,e=a.ci,f=b.getMapping()&&b.getMapping().builtInFunctionsMapping,d&&(N(f,c)||d[c])||e&&e[c]):M()}a.findGlobalFunction=C;function D(){return M()}a.findGlobalFunctions=D;function L(b){if(!b)return a.bi={},void(a.ci={});b=ia(b);var c=a.bi,d=a.ci;c[b]?delete c[b]:d[b]&&delete d[b]}a.removeGlobalFunction=L;function M(){var b,c,d=a.bi,e=a.ci,f=[];for(b in d)f.push(d[b]);for(c in e)f.push(e[c]);return f}function N(b,c){var d,e=a.bi;if(!m(b))for(d in b)if(b[d].alias===c)return e[d];return F}f=b.sR().Exp_Format,g=function(){function a(a,b,c){this.Ri=a,this.Si=b,this.kja=c||"i"}return a.prototype.Pi=function(){return this.Ri},a.prototype.Qi=function(){return this.Si},a.prototype.lja=function(){return this.kja},a.prototype.bc=function(a){a=a||"i";var b=this,c=b.Ri,d=b.Si,e="";return 0===c&&0!==d||(e+=""+c),d===-1?e+="-":0!==c&&d>0&&(e+="+"),d!==-1&&0!==d&&1!==d&&(e+=""+d),0!==d&&(e+=a),e},a}(),a.Ui=g;function Q(a){function c(a){var c,d,e,h,i=0,j=0,k=!1,l=0,m=0,n=0,o=a.length;if(!a)throw b.sR().Exp_ArgumentNull;if(0===o)throw f;if(n<o&&("+"===Aa(a,n)||"-"===Aa(a,n))&&n++,h=Ba(a,n),n=h.mi,c=h.Vi,n<o&&("+"===Aa(a,n)||"-"===Aa(a,n))?(l=n,n++,h=Ba(a,n),n=h.mi,d=h.Vi,n<o&&("i"===Aa(a,n)||"j"===Aa(a,n))&&(e=Aa(a,n),n++,k=!0),m=n-l):n<o&&("i"===Aa(a,n)||"j"===Aa(a,n))?(e=Aa(a,n),n++,m=n,d=c,k=!0,c=!1):l=n,n<o)throw f;if(l>0){if(!c)throw f;i=parseInt(a.substr(0,l),10)}if(m>0){if(!k)throw f;if(1===m||2===m&&"+"===Aa(a,l))j=1;else if(2===m&&"-"===Aa(a,l))j=-1;else{if(!d)throw f;j=parseInt(a.substr(l,m-1),10)}}return new g(i,j,e)}try{return a?"number"==typeof a?new g(a,0):"string"==typeof a?c(a):null:new g(0,0)}catch(a){return null}}a.Wi=Q,h=_.Th,i=_.Rh,j=_.Pa,l=_.Na,s=_.Ph,u=_.Nh,v=_.vf,w=ea.Yh;function S(c,e,f){if(m(c))throw b.sR().Exp_InvalidFunctionName;if(c=c.toUpperCase(),a.bi[c])throw b.sR().Exp_InvalidOverrideFunction;var g=a.bi[c];if(g){if(!f||!f.override)throw b.sR().Exp_OverrideNotAllowed}else a.bi[c]=g=new d(c,0,255);return e&&"function"==typeof e&&(g.evaluate=e),k.H(f,function(a,b){var c;n(a,["acceptsReference","acceptsArray","acceptsError","acceptsMissingArgument","precedentReference","expandColumns","expandRows"])>=0?(c=new fa(b),g[a]=function(a){return c.$j(a)}):n(a,["isVolatile","isBranch","findTestArgument","returnReference","isContextSensitive"])>=0?(c=new fa(b),g[a]=function(){return c._j()}):f.hasOwnProperty(a)&&(g[a]=b)}),g}function X(a,b,c,d,e,f,g,h){return h||(h={}),c!==F&&(h.minArgs=c),d!==F&&(h.maxArgs=d),f!==F&&(h.acceptsReference=f),g!==F&&(h.acceptsArray=g),e!==F&&(h.Ci=e),h.bk!==F&&(h.acceptsMissingArgument=h.bk),h.ck!==F&&(h.isVolatile=h.ck),h.dk!==F&&(h.isContextSensitive=h.dk),h.ek!==F&&(h.precedentReference=h.ek),h.fk!==F&&(h.arrayArgumentEvaluateMode=h.fk),h.gk!==F&&(h.acceptsError=h.gk),S(a,b,h)}a.ak=X,a.hk=1.79769e308;function Y(a){return"boolean"==typeof a}a.ik=Y,a.jk=Ca;function Z(a){return"number"==typeof a}a.Na=Z;function $(a){return a%400===0||a%4===0&&a%100!==0||1900===a}a.kk=$,x=$;function aa(a,b){switch(b){case 0:case 2:case 4:return 360;case 1:return x(a.getFullYear())?366:365;case 3:return 365;default:return-1}}a.lk=aa;function ba(a,b){return a-b}a.mk=ba;function ca(a,b){return n(b,[0,2,4,6,7,9,11])>=0?31:n(b,[3,5,8,10])>=0?30:1===b?x(a)?29:28:void 0}a.nk=ca,y=ca;function da(a,b,c,d){var e,f,g=c.getFullYear(),h=c.getMonth(),i=c.getDate(),j=d.getFullYear(),k=d.getMonth(),l=d.getDate();return a?(e=2===h&&y(g,h)===i,f=2===k&&y(j,k)===l,b?(e&&(i=30),f&&(l=30)):e&&f&&(i=30,l=30),31===l&&i>=30&&(l=30)):31===l&&(l=1,b&&k++),31===i&&(i=30),360*(j-g)+30*(k-h)+l-i}function ga(a,b,c){var d,e,f=1;return ba(a,b)>0&&(e=a,a=b,b=e,f=-1),d=n(c,[1,2,3])>=0?f*i(t(b)-t(a)):n(c,[4,5])>=0?f*da(!1,5===c,a,b):f*da(!0,6===c,a,b)}a.pk=ga;function ja(a){var b,c,d;return 0===a?c=0:(d=.5*G(a),d>=3?c=1:d<1?(b=d*d,c=((((((((.000124818987*b-.001075204047)*b+.005198775019)*b-.019198292004)*b+.059054035642)*b-.151968751364)*b+.319152932694)*b-.5319230073)*b+.797884560593)*d*2):(d-=2,c=(((((((((((((-45255659e-12*d+.00015252929)*d-19538132e-12)*d-.000676904986)*d+.001390604284)*d-.00079462082)*d-.002034254874)*d+.006549791214)*d-.010557625006)*d+.011630447319)*d-.009279453341)*d+.005353579108)*d-.002141268741)*d+.000535310849)*d+.999936657524)),a>0?.5*(c+1):.5*(1-c)}a.qk=ja;function ka(a,b,c,d,e){var f,g,h,i,j,k,l,n,p,q,r,t,v,w=c<100,x=2===c||102===c,y=x||3===c||103===c,z=5===c||105===c,A=4===c||104===c||z,B=9===c||109===c,C=1===c||101===c||B||d,D=6===c||106===c,E=D?1:0,F=!1,G=0;for(f=0;f<o(a);f++){if(q=a[f],r=void 0,y?r=s(q,0,!1,!1,!1,x):A?r=s(q,5,!1,!0,!1,!0):C?r=s(q,5,!1,C,!1,!0):D&&(r=s(q,5,!1,!1,!1,!0)),r.isError)return r[0];if(r.isConvertError)return R;for(t=r.rangeCount,v=r.isReference,g=0;g<t;g++)for(h=t>1?r[g]:r,v&&(i=q.getRow(g),j=q.getColumn(g)),k=0;k<o(h);k++)if(b||!v||!q.isHiddenRow(g,k,w))for(n=h[k],l=0;l<o(n);l++)!b&&v&&q.isSubtotal(g,k+i,l+j)||(p=n[l],p!==O&&(y&&!m(p)&&(!x||""!==p&&_.Na(p,!0))?E++:A&&(!F||z&&p<E||!z&&p>E)?E=p:C?d?(e.sumx+=p,e.sumx2+=p*p,e.n++):(E+=p,G++):D&&(E*=p,G++),F=!0))}return C?B?E:d?void 0:0===G?U:u(E/G):D?u(G>0?E:0):E}a.rk=ka;function la(a,b,c){var d,e=c%100,f=7===e||8===e,g=7===e||10===e?1:0,h={sumx:0,sumx2:0,n:0};return ka(a,b,c,!0,h),h.n<=g?U:(d=Math.max(0,(h.n*h.sumx2-h.sumx*h.sumx)/(h.n*(h.n-g))),u(f?Math.sqrt(d):d))}a.sk=la;function ma(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,p,q,r=1===b||3===b||5===b||7===b,t=1===b||2===b||3===b||4===b||0===b,u=2===b||3===b||6===b||7===b,w=2===c,x=w||3===c,y=12===c,z=13===c,A=14===c,B=15===c,C=16===c,D=17===c,E=18===c,F=19===c,G=A||B||C||D||E||F,H=[];if(G&&2!==o(a))return R;for(d=0;d<o(a);d++){if(m=a[d],n=void 0,n=x?s(m,0,!1,!1,!1,w):s(m,5,!1,!1,!1,!0),n.isConvertError)return R;for(p=n.rangeCount,q=n.isReference,e=0;e<p;e++)for(f=p>1?n[e]:n,q&&(g=m.getRow(e),h=m.getColumn(e)),i=0;i<o(f);i++)if(!q||!m.isHiddenRow(e,i,!r))for(k=f[i],j=0;j<o(k);j++)if(!(q&&t&&m.isSubtotal(e,i+g,j+h)||(l=k[j],u&&v(l)))){if(!u&&v(l))return l;"object"==typeof l&&0===Object.keys(l).length||H.push(l)}}return y||z||G?Fa(H,c):ka(H,!0,c)}a.mja=ma;function na(a){var b=a.charCodeAt(0);return b>=0&&b<129||63728===b||b>=65377&&b<65440||b>=63729&&b<63732?"singleByte":b>=55296&&b<=57343?"fourByte":"doubleByte"}a.UAb=na;function oa(a){var b,c=[];for(b=0;b<a.length;b++)m(a[b])?(c[b]=a[b+1],b++):c[b]=a[b];return c.length===a.length-1&&(c[c.length]=F),c}a.WAb=oa;function pa(a){var b,c,d,e,f=[],g=0,h=0;for(c=0;c<a.length;c++){if(b=a[c],d=na(a[c]),"doubleByte"===d||"fourByte"===d)if("fourByte"===d){if(g%2===0){g++;continue}h++,g++,b=a[c-1]+a[c]}else h++,b=a[c];e=g>0?g/2:0,f[c-e+h]=b}return oa(f)}a.VAb=pa;function qa(a){var b,c=[];for(b=0;b<o(a);b++)sa(a[b],c);return c}function ra(a){a.sort(function(a,b){return a-b})}function sa(a,b){var c,d;for(c=0;c<o(a);c++)d=a[c],d!==O&&b.push(d);ra(b)}function ta(){var a,b,c,d=[];for(a=0;a<arguments.length;a++)d[a]=arguments[a];return b=qa(arguments[0]),c=o(b),0===c?T:c%2===0?(j(b[c/2-1])+j(b[c/2]))/2:b[q(""+c/2)]}function ua(){var a,b,c,d,e,f,g,h,i=[];for(a=0;a<arguments.length;a++)i[a]=arguments[a];for(b=E,c=0,d=qa(arguments[0]),e=o(d),f=0;f<e;f++){for(h=0,g=0;g<e;g++)g!==f&&d[g]===d[f]&&h++;h>c&&(c=h,b=d[f])}return 0===c?V:b}function va(a,b){return xa(!0,a,b)}function wa(a,b){return xa(!1,a,b)}function xa(a,b,c){var d,e=[];return sa(b,e),d=o(e),c<=0||d<c?T:a?e[d-c]:e[c-1]}function ya(a,b){var c,d,e;return a=s(a,1,!0,!0,!1,!0),b=j(b),a.isError?a[0]:a.isConvertError||r(b)?R:(c=[],sa(a,c),!o(c)||b<0||b>1?T:(d=b*(o(c)-1),e=d%1,d=q(""+d),0===e?c[d]:j(c[d])+e*(j(c[d+1])-j(c[d]))))}function za(a,b){var c=n(b,[0,1,2,3,4]);return c<0?T:ya(a,.25*c)}function Da(a,b){var c,d,e,f,g,h,i=o(a),j=[];for(c=0;c<i;c++)l(a[c],!0)&&j.push(a[c]);return j.length?(d=j.length,ra(j),e=b*(d+1)-1,f=e%1,e<0||d-1<e?T:(g=K(e),h=j[g],0===f?h:h+f*(j[g+1]-h))):T}function Ea(a,b){var c=n(b,[1,2,3]);return c<0?T:Da(a,.25*(c+1))}function Fa(a,b){var c=12===b,d=13===b,e=14===b,f=15===b,g=16===b,h=17===b,i=18===b,j=19===b;return c?ta(a):d?ua(a):e?va(a[0],a[1]):f?wa(a[0],a[1]):g?ya(a[0],a[1]):h?za(a[0],a[1]):i?Da(a[0],a[1]):j?Ea(a[0],a[1]):W}a.sja=Fa;function Ga(a){var b=1,c;for(c=a;c>1;c--)b*=c;return b}a.tk=Ga;function Ha(a,b,c){var d=a.getDate(),e=b.getDate(),f=a.getMonth(),g=b.getMonth(),h=a.getFullYear(),i=b.getFullYear();return d=31===d?30:d,c?e=31===e?30:e:31===e&&(d<30?(e=1,g++,g>12&&(g=1,i++)):e=30),30*(12*(i-h)+g-f)+e-d}a.uk=Ha;function Ia(a,b,c){var d,e,f,g,h,k,l,m,n,o=ga(a,b,c);return o<0&&(o=-o,e=a,a=b,b=e),1===c?(f=a.getFullYear(),g=b.getFullYear(),h=void 0,k=void 0,l=void 0,m=void 0,h=new Date(a.valueOf()),h.setFullYear(h.getFullYear()+1),ba(b,h)>0?(m=g+1-f,h=new Date(f,0,1),k=new Date(g+1,0,1),l=i(t(k)-t(h))-365*(g+1-f)):(m=1,l=x(f)&&a.getMonth()<3||x(g)&&256*b.getMonth()+b.getDate()>=541?1:0),n=j(l)/j(m),d=365+n):d=aa(new Date,c),o/d}function Ja(a,b,c){return c<0||c>4?T:Ia(a,b,c)}a.vk=Ja;function Ka(a,b,c){var d;return 0===a?d=b<=c:1===a?d=b>=c:2===a?d=b!==c:3===a?d=b<c:4===a?d=b===c:5===a&&(d=b>c),d}function La(a,b){var c,d=-1,e=!0,f={value:0},g=p.zb(b);4===a&&g&&(c=function(a){if(""===a)return!1;var b=p.sb(g);return b.lastIndex=0,b.test(a)});function i(a){return 0===(""+a).trim().length}return m(b)?d=0:g?e=!1:!i(b)&&h(b,f)?d=f.value:e=!1,function(f,i){var j,k;return!(!i&&f instanceof P)&&(m(f)&&(f=""),j={value:0},e&&h(f,j)?""===f?2===a:Ka(a,j.value,d):(!g||!l(f,!0))&&(k=m(b)?"":(""+b).toUpperCase(),g&&c?c(""+f):Ka(a,(""+f).toUpperCase(),k)))}}z={},A=function(){function b(){}return b.wk=function(a){var b=Math.floor(a);return w(a,b+1)?b+1:b},b.xk=function(a){var b,c,d,e,f,g,h=a,i=z;if(i||(z=i={}),b=i[h])return b;if(l(a))return b=i[h]=La(4,a);for(c="=><",d=m(a)?"":(""+a).toUpperCase(),e="\0",f=0;f<2&&f<o(d);f++){if(g=d[f],c.indexOf(g)===-1){if("<"===e)return i[h]=b=La(3,d.substring(1)),b;if(">"===e)return i[h]=b=La(5,d.substring(1)),b;break}if("="===g)return b="<"===e?La(0,d.substring(2)):">"===e?La(1,d.substring(2)):La(4,"\0"===e?d.substring(1):a),i[h]=b,b;if("\0"===e)e=g;else{if("<"===e)return b=">"===g?La(2,d.substring(2)):La(3,d.substring(1)),i[h]=b,b;if(">"===e)return i[h]=b=La(5,d.substring(1)),b}}return i[h]=b=La(4,a),b},b.yk=function(a){var b=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13,1e14,1e15,1e16][a];return m(b)?J(10,parseFloat(a)):b},b.zk=function(a){var b=Math.ceil(a);return w(a,b-1)?b-1:b},b.Ak=function(a,c){var d=b.yk(G(c)),e=b.zk,f=b.wk;return c>0&&c<=15&&(e=Math.ceil,f=K),a=c<0?a/d:a*d,a=a<0?e(a-.5):f(a+.5),a=c<0?a*d:a/d,u(a)},b.Bk=function(a,b){var c,d;if(a<0||b<0||a<b)return T;for(c=1,b=Math.min(a-b,b),d=1;d<=b;d++)c*=a-d+1,c/=d;return u(c)},b.Ck=function(b,c){var d=G(b)>.5?J(1+b,c):H(c*I(1+b));return isFinite(d)?r(d)&&(d=5e-324):d===Number.POSITIVE_INFINITY?d=a.hk:d===Number.NEGATIVE_INFINITY&&(d=-a.hk),d},b.Dk=function(a,b){return(H(a)+(b?-1:1)*H(-a))/2},b.Ek=function(a,b){return r(a)?a:r(b)?b:1!==b&&1===a||0!==b&&b!==Number.POSITIVE_INFINITY?I(a)/I(b):NaN},b}(),a.MathHelper=A}(ga||(ga={})),b.Functions=ga},"./src/calc.entry.ts":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./src/calc.common.ts")),d(c("./src/Parser.ts")),d(c("./src/evaluator.ts")),d(c("./src/calc.ts"))},"./src/calc.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./src/calc.common.ts"),f=c("./src/Parser.ts"),g=c("./src/evaluator.ts"),h=d.Common.u,i=null,j=void 0,k=Math.abs,l=e.Convert.Ca,m=e.Convert.Fh,n=function(){function a(a){this.Ze=[],this.ah=a}return a.prototype.getKey=function(){return""},a.prototype.hasListeners=function(){var a,b,c,d,e,f=this,g=f.Ze;for(a=0,b=g;a<b.length;a++)if(c=b[a],d=this[c])for(e in d)if(d[e])return!0;return!1},a.prototype.gmb=function(a,b,c){var d,e=this[a];e||(this.Ze.push(a),e=this[a]={}),d=b.getKey(),c?e[d]=b:delete e[d]},a.prototype.Yg=function(){var a,b,c,d,e,f,g=[];for(a=0;a<arguments.length;a++)g[a]=arguments[a];for(b=this.Ze,d=0;d<b.length;d++)if(e=this[b[d]])for(f in e)c=e[f],c&&c.bh()},a.prototype.Bg=function(){var a,b,c,d,e,f,g=[];for(a=0;a<arguments.length;a++)g[a]=arguments[a];for(b=this.Ze,d=0;d<b.length;d++)if(e=this[b[d]])for(f in e)c=e[f],!c||c.dh||c.eh||c.fh()},a.prototype.gh=function(){var a,b,c,d,e,f,g,h,i=[];for(a=0;a<arguments.length;a++)i[a]=arguments[a];for(b=i[0],c=this.Ze,e=b?1:-1,f=0;f<c.length;f++)if(g=this[c[f]])for(h in g)d=g[h],d&&(d.delay+=e,b&&d.y$a&&d.y$a())},a.prototype.bh=function(){},a.prototype.fh=function(){},a.prototype.ng=function(){},a.prototype.og=function(){},a.prototype.ZAb=function(){return this.ah.Cf()},a}(),b.BaseCalc=n,o=function(a){s(b,a);function b(b,c){var d=a.call(this,b)||this;return d.name=c,d.delay=0,d}return b.prototype.getKey=function(){var a=this,b=a.ah,c=null===b?"":b.xf.id+",";return c+a.name},b.prototype.og=function(){var a=this;a.ah.Og(a.name,!0)},b.prototype.ng=function(){var a=this;a.ah.Og(a.name,!1)},b.prototype.bh=function(){this.ZAb().onAddAdjust(this,3)},b.prototype.fh=function(){this.ZAb().pg(this,3)},b}(n),b.NameIDCalc=o,p=function(a){s(b,a);function b(b,c,d){var e=a.call(this,b)||this;return e.id=c,e.name=d,e.delay=0,e}return b.prototype.getKey=function(){var a=this;return a.ah.getName()+","+a.id+"#"+a.name},b.prototype.og=function(){var a=this;a.ah.Ig(a.id,a.name,!0)},b.prototype.ng=function(){var a=this;a.ah.Ig(a.id,a.name,!1)},b.prototype.ZAb=function(){return this.ah.Af},b.prototype.bh=function(){this.ZAb().onAddAdjust(this,6)},b.prototype.fh=function(){this.ZAb().pg(this,6)},b}(n),b.PathCalc=p,q=function(){function a(){this.autoCalculation=!0,this.maximumIterations=1e3,this.maximumChange=.01,this.jh={},this.useR1C1=!1,this.mh=0,this.Bka=0,this.hmb=[];var a=this;a.kh=new f.Parser,a.lh=new g.Evaluator(a),a.nh=a.uzb&&a.uzb(),a.addResolver(new f.DefaultTokenResolver)}return a.prototype.getResolvers=function(){return this.hmb},a.prototype.addResolver=function(a){var b,c=this.hmb;for(b=0;;b++)if(!c[b]||a.priority>c[b].priority){c.splice(b,0,a);break}},a.prototype.dispose=function(){var a=this;a.jh={},a.oh=i},a.prototype.cloneFrom=function(a){var b=this;b.useR1C1=a.useR1C1,b.mh=a.mh,b.qh=a.qh,b.calcOnDemand=a.calcOnDemand},a.prototype.ih=function(){return this.nh},a.prototype.getExternalSource=function(a,b){var c,d,e=this,f=e.getAllSourceModels();for(c=0,d=f.length;c<d;c++)if(h.Mb(f[c].getSource().getName(),b))return f[c].getSource();return i},a.prototype.getSourceModel=function(a){var b=this.jh[a.id];return b||(b=a.createSourceModel?a.createSourceModel(this):this.createSourceModel(this,a),this.jh[a.id]=b,a.setCalcSourceModel(b),a.setCalcService(this)),b},a.prototype.getGlobalSourceModel=function(a){return this.oh||(a=a||new e.CalcSource,a.createSourceModel?this.oh=a.createSourceModel(this):this.oh=this.createSourceModel(this,a),a.setCalcSourceModel(this.oh)),this.oh},a.prototype.setSourceModel=function(a,b){this.jh[a.id]=b,b.setCalcService(this)},a.prototype.getAllSourceModels=function(){var a,b=[],c=this.jh;for(a in c)b.push(c[a]);return b},a.prototype.removeSource=function(a){var b,c,d,e,f,g=this.jh[a.id];g&&(this.onRemoveSource(a),delete this.jh[a.id]),this.clearDirties(),b=this.jh;for(d in b)c=b[d],c.addDirtyNodesForCalc(!0);e=this.Dh&&this.Dh.kj,f=a&&a.kj,e&&f&&f===e&&(this.Dh=null)},a.prototype.clearSource=function(){this.jh={},this.Dh=null,this.clearDirties()},a.prototype.ignoreDirty=function(){return this.qh},a.prototype.suspend=function(a){this.mh++,this.qh=!!a},a.prototype.resume=function(a){var b=this;b.mh--,b.mh<0&&(b.mh=0),b.IsSuspended()||(b.rh=i,b.qh=!1,b.recalculateAll(a))},a.prototype.suspendAdjust=function(){this.Bka++},a.prototype.resumeAdjust=function(){var a=this;a.Bka--,a.Bka<0&&(a.Bka=0)},a.prototype.isAdjustSuspended=function(){return this.Bka>0},a.prototype.clearDirties=function(){var a,b,c,d=this,e=d.imb;if(e)for(a in e)if(e.hasOwnProperty(a))for(b=e[a];b;)c=b.eh,c?(b.eh=c.dh=j,b=c):b=i;d.imb=d.jmb=i},a.prototype.resumeWithoutCalc=function(){this.mh=0,this.clearDirties(),this.qh=!1},a.prototype.IsSuspended=function(){return this.mh>0},a.prototype.recalculateAll=function(a,b){var c,d,e,f=this;if(!f.IsSuspended()){b||f.lh.startCache(),d=f.jh;for(e in d)c=d[e],c.getSource().startCalculation(),c.addDirtyNodesForCalc(a,b);if(f.Gg(),!b)for(e in d)c=d[e],c.Fg();for(e in d)c=d[e],c.getSource().endCalculation();b||f.lh.endCache()}},a.prototype.Gg=function(a){var b,c,d,e,f,g,h,i;if(void 0===a&&(a=!0),b=this,c=b.imb){if(a){f=c[1]||c[2];for(d in c)c.hasOwnProperty(d)&&(g=parseInt(d),f&&1!==g&&2!==g||(e=c[d],e&&(e.ah.addListenersToDirty(b,g),e.processed=!0)));for(d in c)c.hasOwnProperty(d)&&(e=c[d],e&&(e.processed?delete e.processed:e.ah.addListenersToDirty(b,parseInt(d))))}for(d in c)c.hasOwnProperty(d)&&c[d].ah.initDelay(b,parseInt(d));for(d in c)c.hasOwnProperty(d)&&c[d].ah.adjustDelayOfListeners(b,parseInt(d),!0);do{h=!1;for(d in c)c.hasOwnProperty(d)&&(i=c[d].ah.calculateDirtyNodes(b,parseInt(d)),i&&(h=!0))}while(h);for(d in c)c.hasOwnProperty(d)&&c[d].ah.calculateIterations(b,parseInt(d));b.imb=b.jmb=j}},a.prototype.Ch=function(a,b){return void 0===a&&(a=0),void 0===b&&(b=0),k(b-a)},a.prototype.initParserContext=function(a){var b=this.Dh;b||(this.Dh=a.getParserContext(this.useR1C1))},a.prototype.getParserContext=function(a){var b=this,c=b.Dh;return c||(a?b.Dh=c=a.getParserContext(b.useR1C1):c=new f.ParserContext(i,b.useR1C1)),c.setSource(a),c},a.prototype.parseWithContext=function(a,b){return this.kh.parse(a,b)},a.prototype.unparseWithContext=function(a,b){return this.kh.unparse(a,b)},a.prototype.pg=function(a,b){var c,d,e,f,g=this;isNaN(b)||(e=g.imb,f=g.jmb,e||(e=g.imb={},f=g.jmb={}),c=e[b],d=f[b],a&&!a.dh&&a!==c&&(c?d.eh=a:e[b]=a,a.dh=d,a.eh=i,f[b]=a))},a.prototype.Bh=function(a,b){var c,d,e,f,g,h=this;isNaN(b)||(d=h.imb,e=h.jmb,d&&(c=h.imb[b]),a&&(a.dh||a===c)&&(f=a.dh,g=a.eh,f?f.eh=g:d&&(g?d[b]=g:delete d[b]),g?g.dh=f:e&&(f?e[b]=f:delete e[b]),a.dh=i,a.eh=i))},a.prototype.evaluateExpression=function(a,b,c){var d=this;return d.lh.evaluateExpression(b,a.getEvaluatorContext(c,!1),!1)},a.prototype.onClearExpr=function(a,b){this.lh&&this.lh.asyncManager.clearCell(a,b)},a}(),b.CalcService=q,r=function(){function a(){}return a.getLength=function(a,b){return l(a)?a.getRowCount()*a.getColumnCount():m(a)?(b=b?b:0,a.getRowCount(b)*a.getColumnCount(b)):void 0},a.getRowCount=function(a,b){return l(a)?a.getRowCount():m(a)?(b=b?b:0,a.getRowCount(b)):void 0},a.getColumnCount=function(a,b){return l(a)?a.getColumnCount():m(a)?(b=b?b:0,a.getColumnCount(b)):void 0},a.getValueByIndex=function(a,b,c){if(l(a))return a.getValueByIndex(b);if(m(a)){c=c?c:0;var d=a.getColumnCount(c);return a.getValue(c,parseInt(""+b/d,10),b%d)}},a.getValue=function(a,b,c,d){return l(a)?a.getValue(b,c):m(a)?(d=d?d:0,a.getValue(d,b,c)):void 0},a}(),b.CalcArrayHelper=r},"./src/calcEngine.res.en.ts":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});function d(a,b){return{description:a,parameters:b}}function e(a,b){return{name:a,repeatable:b}}b.resource={Exp_InvalidCast:"Invalid Cast Exception",Exp_FormulaInvalidChar:"The formula contains an invalid character: '{0}' at index on {1}",Exp_FormulaInvalid:"Invalid Formula",Exp_InvalidFunctionName:"Invalid function name",Exp_InvalidOverrideFunction:"Cannot override built-in function",Exp_InvalidArray:"Invalid array",Exp_OverrideNotAllowed:"Attempt to override function while override is not allowed",Exp_NoSyntax:"No syntax '{0}' to match the syntax '{1}'.",Exp_IsValid:"'{0}' is invalid.",Exp_InvalidParameters:"Invalid function parameter at {0}.",Exp_InvalidArrayColumns:"The length of array columns are not equal at {0}.",Exp_ExprIsNull:"The argument 'expr' is null",Exp_InvalidOperation:"Invalid Operation Exception",Exp_ArgumentNull:"Argument Null Exception",Exp_CriteriaIsNull:"Criteria is null",Exp_Format:"Format",Exp_ArrayFromulaPart:"Cannot change part of an array.",Exp_NotSupported:"Not Supported Exception",_h:{ABS:d("This function calculates the absolute value of the specified value.",[e("value")]),ACCRINT:d("This function calculates the accrued interest for a security that pays periodic interest.",[e("issue"),e("first"),e("settle"),e("rate"),e("par"),e("frequency"),e("basis")]),ACCRINTM:d("This function calculates the accrued interest at maturity for a security that pays periodic interest.",[e("issue"),e("maturity"),e("rate"),e("par"),e("basis")]),ACOS:d("This function calculates the arccosine, that is, the angle whose cosine is the specified value.",[e("value")]),ACOSH:d("This function calculates the inverse hyperbolic cosine of the specified value.",[e("value")]),ADDRESS:d("This function uses the row and column numbers to create a cell address in text.",[e("row"),e("column"),e("absnum"),e("a1style"),e("sheettext")]),AMORDEGRC:d("This function returns the depreciation for an accounting period, taking into consideration prorated depreciation, and applies a depreciation coefficient in the calculation based on the life of the assets.",[e("cost"),e("datepurchased"),e("firstperiod"),e("salvage"),e("period"),e("drate"),e("basis")]),AMORLINC:d("This function calculates the depreciation for an accounting period, taking into account prorated depreciation.",[e("cost"),e("datepurchased"),e("firstperiod"),e("salvage"),e("period"),e("drate"),e("basis")]),AND:d("Check whether all arguments are True, and returns True if all arguments are True.",[e("logical1"),e("logical2")]),ASIN:d("This function calculates the arcsine, that is, the angle whose sine is the specified value.",[e("value")]),ASINH:d("This function calculates the inverse hyperbolic sine of a number.",[e("value")]),ATAN:d("This function calculates the arctangent, that is, the angle whose tangent is the specified value.",[e("value")]),ATAN2:d("This function calculates the arctangent of the specified x- and y-coordinates.",[e("x"),e("y")]),ATANH:d("This function calculates the inverse hyperbolic tangent of a number.",[e("value")]),AVEDEV:d("This function calculates the average of the absolute deviations of the specified values from their mean.",[e("value1"),e("value2",!0)]),AVERAGE:d("This function calculates the average of the specified numeric values.",[e("value1"),e("value2",!0)]),AVERAGEA:d("This function calculates the average of the specified values, including text or logical values as well as numeric values.",[e("value1"),e("value2",!0)]),AVERAGEIF:d("This function calculates the average of the specified numeric values provided that they meet the specified criteria.",[e("value1"),e("value2",!0),e("condition")]),AVERAGEIFS:d("This function calculates the average of all cells that meet multiple specified criteria.",[e("value1"),e("condition1"),e("value2",!0),e("condition2...")]),BESSELI:d("This function calculates the modified Bessel function of the first kind evaluated for purely imaginary arguments.",[e("value"),e("order")]),BESSELJ:d("This function calculates the Bessel function of the first kind.",[e("value"),e("order")]),BESSELK:d("This function calculates the modified Bessel function of the second kind evaluated for purely imaginary arguments.",[e("value"),e("order")]),BESSELY:d("This function calculates the Bessel function of the second kind.",[e("value"),e("order")]),BETADIST:d("This function calculates the cumulative beta distribution function.",[e("x"),e("alpha"),e("beta"),e("lower"),e("upper")]),BETAINV:d("This function calculates the inverse of the cumulative beta distribution function.",[e("prob"),e("alpha"),e("beta"),e("lower"),e("upper")]),BIN2DEC:d("This function converts a binary number to a decimal number",[e("number")]),BIN2HEX:d("This function converts a binary number to a hexadecimal number.",[e("number"),e("places")]),BIN2OCT:d("This function converts a binary number to an octal number.",[e("number"),e("places")]),BINOMDIST:d("This function calculates the individual term binomial distribution probability.",[e("x"),e("n"),e("p"),e("cumulative")]),CEILING:d("This function rounds a number up to the nearest multiple of a specified value.",[e("value"),e("signif")]),CHAR:d("This function returns the character specified by a number.",[e("value")]),CHIDIST:d("This function calculates the one-tailed probability of the chi-squared distribution.",[e("value"),e("deg")]),CHIINV:d("This function calculates the inverse of the one-tailed probability of the chi-squared distribution",[e("prob"),e("deg")]),CHITEST:d("This function calculates the test for independence from the chi-squared distribution.",[e("obs_array"),e("exp_array")]),CHOOSE:d("This function returns a value from a list of values.",[e("index"),e("value1"),e("value2",!0)]),CLEAN:d("This function removes all non-printable characters from text.",[e("text")]),CODE:d("This function returns a numeric code to represent the first character in a text string. The returned code corresponds to the Windows character set (ANSI).",[e("text")]),COLUMN:d("This function returns the column number of a reference.",[e("reference")]),COLUMNS:d("This function returns the number of columns in an array.",[e("array")]),COMBIN:d("This function calculates the number of possible combinations for a specified number of items.",[e("k"),e("n")]),COMPLEX:d("This function converts real and imaginary coefficients into a complex number.",[e("realcoeff"),e("imagcoeff"),e("suffix")]),CONCATENATE:d("This function combines multiple text strings or numbers into one text string.",[e("text1"),e("text2"),e("....")]),CONFIDENCE:d("This function returns confidence interval for a population mean.",[e("alpha"),e("stdev"),e("size")]),CONVERT:d("This function converts a number from one measurement system to its equivalent in another measurement system.",[e("number"),e("from-unit"),e("to-unit")]),CORREL:d("This function returns the correlation coefficient of the two sets of data.",[e("array1"),e("array2")]),COS:d("This function returns the cosine of the specified angle.",[e("angle")]),COSH:d("This function returns the hyperbolic cosine of the specified value.",[e("value")]),COUNT:d("This function returns the number of cells that contain numbers.",[e("value1"),e("value2",!0)]),COUNTA:d("This function returns the number of number of cells that contain numbers, text, or logical values.",[e("value1"),e("value2",!0)]),COUNTBLANK:d("This function returns the number of empty (or blank) cells in a range of cells on a sheet.",[e("cellrange")]),COUNTIF:d("This function returns the number of cells that meet a certain condition",[e("cellrange"),e("condition")]),COUNTIFS:d("This function returns the number of cells that meet multiple conditions.",[e("cellrange"),e("condition")]),COUPDAYBS:d("This function calculates the number of days from the beginning of the coupon period to the settlement date.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPDAYS:d("This function returns the number of days in the coupon period that contains the settlement date.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPDAYSNC:d("This function calculates the number of days from the settlement date to the next coupon date.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPNCD:d("This function returns a date number of the next coupon date after the settlement date.",[e("settlement"),e("maturity"),e("frequency"),e("basi")]),COUPNUM:d("This function returns the number of coupons due between the settlement date and maturity date.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COUPPCD:d("This function returns a date number of the previous coupon date before the settlement date.",[e("settlement"),e("maturity"),e("frequency"),e("basis")]),COVAR:d("This function returns the covariance, which is the average of the products of deviations for each data point pair in two sets of numbers.",[e("array1"),e("array2")]),CRITBINOM:d("This function returns the criterion binomial, the smallest value for which the cumulative binomial distribution is greater than or equal to a criterion value.",[e("n"),e("p"),e("alpha")]),CUMIPMT:d("This function returns the cumulative interest paid on a loan between the starting and ending periods.",[e("rate"),e("nper"),e("pval"),e("startperiod"),e("endperiod"),e("paytype")]),CUMPRINC:d("This function returns the cumulative principal paid on a loan between the start and end periods.",[e("rate"),e("nper"),e("pval"),e("startperiod"),e("endperiod"),e("paytype")]),DATE:d("This function returns the DateTime object for a particular date, specified by the year, month, and day.",[e("year"),e("month"),e("day")]),DATEDIF:d("This function returns the number of days, months, or years between two dates.",[e("date1"),e("date2"),e("outputcode")]),DATEVALUE:d("This function returns a DateTime object of the specified date.",[e("date_string")]),DAVERAGE:d("This function calculates the average of values in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DAY:d("This function returns the day number of the month (integer 1 to 31) that corresponds to the specified date.",[e("date")]),DAYS360:d("This function returns the number of days between two dates based on a 360-day year.",[e("startdate"),e("enddate"),e("method")]),DB:d("This function calculates the depreciation of an asset for a specified period using the fixed\u2011declining balance method",[e("cost"),e("salvage"),e("life"),e("period"),e("month")]),DCOUNT:d("This function counts the cells that contain numbers in a column of a list or database that match the specified conditions",[e("database"),e(" field"),e(" criteria")]),DCOUNTA:d("This function counts the non-blank cells in a column of a list or database that match the specified conditions",[e("database"),e(" field"),e(" criteria")]),DDB:d("This function calculates the depreciation of an asset for a specified period using the double-declining balance method or another method you specify.",[e("cost"),e("salvage"),e("life"),e("period"),e("factor")]),DEC2BIN:d("This function converts a decimal number to a binary number.",[e("number"),e("places")]),DEC2HEX:d("This function converts a decimal number to a hexadecimal number",[e("number"),e("places")]),DEC2OCT:d("This function converts a decimal number to an octal number",[e("number"),e("places")]),
DEGREES:d("This function converts the specified value from radians to degrees",[e("angle")]),DELTA:d("This function identifies whether two values are equal. Returns 1 if they are equal; returns 0 otherwise.",[e("value1"),e("value2")]),DEVSQ:d("This function calculates the sum of the squares of deviations of data points (or of an array of data points) from their sample mean.",[e("value1"),e("value2",!0)]),DGET:d("This function extracts a single value from a column of a list or database that matches the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DISC:d("This function calculates the discount rate for a security.",[e("settle"),e("mature"),e("pricep"),e("redeem"),e("basis")]),DMAX:d("This function returns the largest number in a column of a list or database that matches the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DMIN:d("This function returns the smallest number in a column of a list or database that matches the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DOLLAR:d("This function converts a number to text using currency format, with the decimals rounded to the specified place.",[e("value"),e("digits")]),DOLLARDE:d("This function converts a fraction dollar price to a decimal dollar price.",[e("fractionaldollar"),e("fraction")]),DOLLARFR:d("This function converts a decimal number dollar price to a fraction dollar price.",[e("decimaldollar"),e("fraction")]),DPRODUCT:d("This function multiplies the values in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DSTDEV:d("This function estimates the standard deviation of a population based on a sample by using the numbers in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DSTDEVP:d("This function calculates the standard deviation of a population based on the entire population using the numbers in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DSUM:d("This function adds the numbers in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DURATION:d("This function returns the Macaulay duration for an assumed par value of $100.",[e("settlement"),e("maturity"),e("coupon"),e("yield"),e("frequency"),e("basis")]),DVAR:d("This function estimates the variance of a population based on a sample by using the numbers in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),DVARP:d("This function calculates the variance of a population based on the entire population by using the numbers in a column of a list or database that match the specified conditions.",[e("database"),e(" field"),e(" criteria")]),EDATE:d("This function calculates the date that is the indicated number of months before or after a specified date.",[e("startdate"),e("months")]),EFFECT:d("This function calculates the effective annual interest rate for a given nominal annual interest rate and the number of compounding periods per year.",[e("nomrate"),e("comper")]),EOMONTH:d("This function calculates the date for the last day of the month (end of month) that is the indicated number of months before or after the starting date.",[e("startdate"),e("months")]),ERF:d("This function calculates the error function integrated between a lower and an upper limit.",[e("limit"),e("upperlimit")]),ERFC:d("This function calculates the complementary error function integrated between a lower limit and infinity.",[e("lowerlimit")]),"ERROR.TYPE":d("This function returns a number corresponding to one of the error values.",[e("errorvalue")]),EURO:d("This function returns the equivalent of one Euro based on the ISO currency code.",[e("code")]),EUROCONVERT:d("This function converts currency from a Euro member currency (including Euros) to another Euro member currency (including Euros).",[e("currency"),e("source"),e("target"),e("fullprecision"),e("triangulation")]),EVEN:d("This function rounds the specified value up to the nearest even integer.",[e("value")]),EXACT:d("This function returns true if two strings are the same; otherwise, false.",[e("text1"),e("text2")]),EXP:d("This function returns e raised to the power of the specified value.",[e("value")]),EXPONDIST:d("This function returns the exponential distribution or the probability density.",[e("value"),e("lambda"),e("cumulative")]),FACT:d("This function calculates the factorial of the specified number.",[e("number")]),FACTDOUBLE:d("This function calculates the double factorial of the specified number.",[e("number")]),FALSE:d("This function returns the value for logical FALSE.",[]),FDIST:d("This function calculates the F probability distribution, to see degrees of diversity between two sets of data.",[e("value"),e("degnum"),e("degden")]),FIND:d("This function finds one text value within another and returns the text value\u2019s position in the text you searched.",[e("findtext"),e("intext"),e("start")]),FINV:d("This function returns the inverse of the F probability distribution.",[e("p"),e("degnum"),e("degden")]),FISHER:d("This function returns the Fisher transformation for a specified value.",[e("value")]),FISHERINV:d("This function returns the inverse of the Fisher transformation for a specified value.",[e("value")]),FIXED:d("This function rounds a number to the specified number of decimal places, formats the number in decimal format using a period and commas (if so specified), and returns the result as text.",[e("num"),e("digits"),e("notcomma")]),FLOOR:d("This function rounds a number down to the nearest multiple of a specified value.",[e("value"),e("signif")]),FORECAST:d("This function calculates a future value using existing values.",[e("value"),e("Yarray"),e("Xarray")]),FREQUENCY:d("This function calculates how often values occur within a range of values. This function returns a vertical array of numbers.",[e("dataarray"),e("binarray")]),FTEST:d("This function returns the result of an F-test, which returns the one-tailed probability that the variances in two arrays are not significantly different.",[e("array1"),e("array2")]),FV:d("This function returns the future value of an investment based on a present value, periodic payments, and a specified interest rate.",[e("rate"),e("numper"),e("paymt"),e("pval"),e("type")]),FVSCHEDULE:d("This function returns the future value of an initial principal after applying a series of compound interest rates. Calculate future value of an investment with a variable or adjustable rate.",[e("principal"),e("schedule")]),GAMMADIST:d("This function returns the gamma distribution.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),GAMMAINV:d("This function returns the inverse of the gamma cumulative distribution.",[e("p"),e("alpha"),e("beta")]),GAMMALN:d("This function returns the natural logarithm of the Gamma function, G(x).",[e("value")]),GCD:d("This function returns the greatest common divisor of two numbers.",[e("number1"),e("number2")]),GEOMEAN:d("This function returns the geometric mean of a set of positive data.",[e("value1"),e("value2",!0)]),GESTEP:d("This function, greater than or equal to step, returns an indication of whether a number is equal to a threshold.",[e("number"),e("step")]),GROWTH:d("This function calculates predicted exponential growth. This function returns the y values for a series of new x values that are specified by using existing x and y values.",[e("y"),e("x"),e("newx"),e("constant")]),HARMEAN:d("This function returns the harmonic mean of a data set.",[e("value1"),e("value2",!0)]),HEX2BIN:d("This function converts a hexadecimal number to a binary number.",[e("number"),e(" places")]),HEX2DEC:d("This function converts a hexadecimal number to a decimal number.",[e("number")]),HEX2OCT:d("This function converts a hexadecimal number to an octal number.",[e("number"),e(" places")]),HLOOKUP:d("This function searches for a value in the top row and then returns a value in the same column from a specified row.",[e("value"),e("array"),e("row"),e("approx")]),HOUR:d("This function returns the hour that corresponds to a specified time.",[e("time")]),HYPGEOMDIST:d("This function returns the hypergeometric distribution.",[e("x"),e("n"),e("M"),e("N")]),IF:d("This function performs a comparison and returns one of two provided values based on that comparison.",[e("valueTest"),e("valueTrue"),e("valueFalse")]),IFERROR:d("This function evaluates a formula and returns a value you provide if there is an error or the formula result.",[e("value"),e("error")]),IMABS:d("This function returns the absolute value or modulus of a complex number.",[e("complexnum")]),IMAGINARY:d("This function returns the imaginary coefficient of a complex number.",[e("complexnum")]),IMARGUMENT:d("This function returns the argument theta, which is an angle expressed in radians.",[e("complexnum")]),IMCONJUGATE:d("This function returns the complex conjugate of a complex number.",[e("complexnum")]),IMCOS:d("This function returns the cosine of a complex number.",[e("complexnum")]),IMDIV:d("This function returns the quotient of two complex numbers.",[e("complexnum"),e("complexdenom")]),IMEXP:d("This function returns the exponential of a complex number.",[e("complexnum")]),IMLN:d("This function returns the natural logarithm of a complex number.",[e("complexnum")]),IMLOG2:d("This function returns the base-2 logarithm of a complex number.",[e("complexnum")]),IMLOG10:d("This function returns the common logarithm of a complex number.",[e("complexnum")]),IMPOWER:d("This function returns a complex number raised to a power.",[e("complexnum"),e("powernum")]),IMPRODUCT:d("This function returns the product of up to 29 complex numbers in the x+yi or x+yj text format.",[e("complexnum1"),e("complexnum2",!0)]),IMREAL:d("This function returns the real coefficient of a complex number in the x+yi or x+yj text format.",[e("complexnum")]),IMSIN:d("This function returns the sine of a complex number in the x+yi or x+yj text format.",[e("complexnum")]),IMSQRT:d("This function returns the square root of a complex number in the x+yi or x+yj text format.",[e("complexnum")]),IMSUB:d("This function returns the difference of two complex numbers in the x+yi or x+yj text format.",[e("complexnum1"),e("complexnum2")]),IMSUM:d("This function returns the sum of two or more complex numbers in the x+yi or x+yj text format.",[e("complexnum1"),e("complexnum2",!0)]),INDEX:d("This function returns a value or the reference to a value from within an array or range.",[e("return"),e("row"),e("col"),e("area")]),INDIRECT:d("This function returns the reference specified by a text string. References are immediately evaluated to display their contents.",[e("ref_text"),e("a1_style")]),INT:d("This function rounds a specified number down to the nearest integer.",[e("value")]),INTERCEPT:d("This function returns the coordinates of a point at which a line intersects the y-axis, by using existing x values and y values.",[e("dependent"),e("independent")]),INTRATE:d("This function calculates the interest rate for a fully invested security.",[e("settle"),e("mature"),e("invest"),e("redeem"),e("basis")]),IPMT:d("This function calculates the payment of interest on a loan.",[e("rate"),e("per"),e("nper"),e("pval"),e("fval"),e("type")]),IRR:d("This function returns the internal rate of return for a series of cash flows represented by the numbers in an array.",[e("arrayvals"),e("estimate")]),ISBLANK:d("This function tests whether a value, an expression, or contents of a referenced cell is empty.",[e("cellreference")]),ISERR:d("This function, Is Error Other Than Not Available, tests whether a value, an expression, or contents of a referenced cell has an error other than not available (#N/A).",[e("cellreference")]),ISERROR:d("This function, Is Error of Any Kind, tests whether a value, an expression, or contents of a referenced cell has an error of any kind.",[e("cellreference")]),ISEVEN:d("This function, Is Number Even, tests whether a value, an expression, or contents of a referenced cell is even.",[e("cellreference")]),ISLOGICAL:d("This function tests whether a value, an expression, or contents of a referenced cell is a logical (Boolean) value.",[e("cellreference")]),ISNA:d("This function, Is Not Available, tests whether a value, an expression, or contents of a referenced cell has the not available (#N/A) error value.",[e("cellreference")]),ISNONTEXT:d("This function tests whether a value, an expression, or contents of a referenced cell has any data type other than text.",[e("cellreference")]),ISNUMBER:d("This function tests whether a value, an expression, or contents of a referenced cell has numeric data.",[e("cellreference")]),ISODD:d("This function, Is Number Odd, tests whether a value, an expression, or contents of a referenced cell has numeric data.",[e("cellreference")]),ISPMT:d("This function calculates the interest paid during a specific period of an investment.",[e("rate"),e("per"),e("nper"),e("pv")]),ISREF:d("This function, Is Reference, tests whether a value, an expression, or contents of a referenced cell is a reference to another cell.",[e("cellreference")]),ISTEXT:d("This function tests whether a value, an expression, or contents of a referenced cell has text data.",[e("cellreference")]),KURT:d("This function returns the kurtosis of a data set.",[e("value1"),e("value2"),e("value3"),e("value4",!0)]),LARGE:d("This function returns the nth largest value in a data set, where n is specified.",[e("array"),e("n")]),LCM:d("This function returns the least common multiple of two numbers.",[e("number1"),e("number2")]),LEFT:d("This function returns the specified leftmost characters from a text value, and based on the number of characters you specify.",[e("mytext"),e("num_chars")]),LEN:d("This function returns the length of the number of characters in a text string.",[e("value")]),LINEST:d("This function calculates the statistics for a line.",[e("y"),e("x"),e("constant"),e("stats")]),LN:d("This function returns the natural logarithm of the specified number.",[e("value")]),LOG:d("This function returns the logarithm base Y of a number X.",[e("number"),e("base")]),LOG10:d("This function returns the logarithm base 10 of the number given.",[e("value")]),LOGEST:d("This function calculates an exponential curve that fits the data and returns an array of values that describes the curve.",[e("y"),e("x"),e("constant"),e("stats")]),LOGINV:d("This function returns the inverse of the lognormal cumulative distribution function of x, where LN(x) is normally distributed with the specified mean and standard deviation.",[e("prob"),e("mean"),e("stdev")]),LOGNORMDIST:d("This function returns the cumulative natural log normal distribution of x, where LN(x) is normally distributed with the specified mean and standard deviation. Analyze data that has been logarithmically transformed with this function.",[e("x"),e("mean"),e("stdev")]),LOOKUP:d("This function searches for a value and returns a value from the same location in a second area.",[e("lookupvalue"),e("lookupvector"),e("resultvector")]),LOWER:d("This function converts text to lower case letters.",[e("string")]),MATCH:d("This function returns the relative position of a specified item in a range.",[e("value1"),e("array"),e("type")]),MAX:d("This function returns the maximum value, the greatest value, of all the values in the arguments.",[e("value1"),e("value2",!0)]),MAXA:d("This function returns the largest value in a list of arguments, including text and logical values.",[e("value1"),e("value2",!0)]),MDETERM:d("This function returns the matrix determinant of an array.",[e("array")]),MDURATION:d("This function calculates the modified Macaulay duration of a security with an assumed par value of $100.",[e("settlement"),e("maturity"),e("coupon"),e("yield"),e("frequency"),e("basis")]),MEDIAN:d("This function returns the median, the number in the middle of the provided set of numbers; that is, half the numbers have values that are greater than the median, and half have values that are less than the median.",[e("value1"),e("value2",!0)]),MID:d("This function returns the requested number of characters from a text string starting at the position you specify, and based on the number of characters you specify.",[e("text"),e("start_num"),e("num_chars")]),MIN:d("This function returns the minimum value, the least value, of all the values in the arguments.",[e("value1"),e("value2",!0)]),MINA:d("This function returns the minimum value in a list of arguments, including text and logical values.",[e("value1"),e("value2",!0)]),MINUTE:d("This function returns the minute corresponding to a specified time.",[e("time")]),MINVERSE:d("This function returns the inverse matrix for the matrix stored in an array.",[e("array")]),MIRR:d("This function returns the modified internal rate of return for a series of periodic cash flows.",[e("arrayvals"),e("payment_int"),e("income_int")]),MMULT:d("This function returns the matrix product for two arrays.",[e("array1"),e("array2")]),MOD:d("This function returns the remainder of a division operation.",[e("dividend"),e("divisor")]),MODE:d("This function returns the most frequently occurring value in a set of data.",[e("value1"),e("value2",!0)]),MONTH:d("This function returns the month corresponding to the specified date value.",[e("date")]),MROUND:d("This function returns a number rounded to the desired multiple.",[e("number"),e("multiple")]),MULTINOMIAL:d("This function calculates the ratio of the factorial of a sum of values to the product of factorials.",[e("value1"),e("value2",!0)]),N:d("This function returns a value converted to a number.",[e("value")]),NA:d("This function returns the error value #N/A that means not available.",[]),NEGBINOMDIST:d("This function returns the negative binomial distribution.",[e("x"),e("r"),e("p")]),NETWORKDAYS:d("This function returns the total number of complete working days between the start and end dates.",[e("startdate"),e("enddate"),e("holidays")]),NOMINAL:d("This function returns the nominal annual interest rate for a given effective rate and number of compounding periods per year.",[e("effrate"),e("comper")]),NORMDIST:d("This function returns the normal cumulative distribution for the specified mean and standard deviation.",[e("x"),e("mean"),e("stdev"),e("cumulative")]),NORMINV:d("This function returns the inverse of the normal cumulative distribution for the given mean and standard deviation.",[e("prob"),e("mean"),e("stdev")]),NORMSDIST:d("This function returns the standard normal cumulative distribution function.",[e("value")]),NORMSINV:d("This function returns the inverse of the standard normal cumulative distribution. The distribution has a mean of zero and a standard deviation of one.",[e("prob")]),NOT:d("This function reverses the logical value of its argument.",[e("value")]),NOW:d("This function returns the current date and time.",[]),NPER:d("This function returns the number of periods for an investment based on a present value, future value, periodic payments, and a specified interest rate.",[e("rate"),e("paymt"),e("pval"),e("fval"),e("type")]),NPV:d("This function calculates the net present value of an investment by using a discount rate and a series of future payments and income.",[e("discount"),e("value1"),e("value2",!0)]),OCT2BIN:d("This function converts an octal number to a binary number.",[e("number"),e("places")]),OCT2DEC:d("This function converts an octal number to a decimal number.",[e("number")]),OCT2HEX:d("This function converts an octal number to a hexadecimal number.",[e("number"),e("places")]),ODD:d("This function rounds the specified value up to the nearest odd integer.",[e("value")]),ODDFPRICE:d("This function calculates the price per $100 face value of a security with an odd first period.",[e("settle"),e("maturity"),e("issue"),e("first"),e("rate"),e("yield"),e("redeem"),e("freq"),e("basis")]),ODDFYIELD:d("This function calculates the yield of a security with an odd first period.",[e("settle"),e("maturity"),e("issue"),e("first"),e("rate"),e("price"),e("redeem"),e("freq"),e("basis")]),ODDLPRICE:d("This function calculates the price per $100 face value of a security with an odd last coupon period.",[e("settle"),e("maturity"),e("last"),e("rate"),e("yield"),e("redeem"),e("freq"),e("basis")]),ODDLYIELD:d("This function calculates the yield of a security with an odd last period.",[e("settle"),e("maturity"),e("last"),e("rate"),e("price"),e("redeem"),e("freq"),e("basis")]),OFFSET:d("This function returns a reference to a range. The range is a specified number of rows and columns from a cell or range of cells. The function returns a single cell or a range of cells.",[e("reference"),e("rows"),e("cols"),e("height"),e("width")]),OR:d("This function calculates logical OR. It returns TRUE if any of its arguments are true; otherwise, returns FALSE if all arguments are false.",[e("argument1"),e("argument2...")]),PEARSON:d("This function returns the Pearson product moment correlation coefficient, a dimensionless index between -1.0 to 1.0 inclusive indicative of the linear relationship of two data sets.",[e("array_ind"),e("array_dep")]),PERCENTILE:d("This function returns the nth percentile of values in a range.",[e("array"),e("n")]),PERCENTRANK:d("This function returns the rank of a value in a data set as a percentage of the data set.",[e("array"),e("n"),e("sigdig")]),PERMUT:d("This function returns the number of possible permutations for a specified number of items.",[e("k"),e("n")]),PI:d("This function returns PI as 3.1415926536.",[]),PMT:d("This function returns the payment amount for a loan given the present value, specified interest rate, and number of terms.",[e("rate"),e("nper"),e("pval"),e("fval"),e("type")]),POISSON:d("This function returns the Poisson distribution.",[e("nevents"),e("mean"),e("cumulative")]),POWER:d("This function raises the specified number to the specified power.",[e("number"),e("power")]),PPMT:d("This function returns the amount of payment of principal for a loan given the present value, specified interest rate, and number of terms.",[e("rate"),e("per"),e("nper"),e("pval"),e("fval"),e("type")]),PRICE:d("This function calculates the price per $100 face value of a periodic interest security",[e("settlement"),e("maturity"),e("rate"),e("yield"),e("redeem"),e("frequency"),e("basis")]),PRICEDISC:d("This function returns the price per $100 face value of a discounted security.",[e("settle"),e("mature"),e("discount"),e("redeem"),e("basis")]),PRICEMAT:d("This function returns the price at maturity per $100 face value of a security that pays interest.",[e("settle"),e("mature"),e("issue"),e("rate"),e("yield"),e("basis")]),PROB:d("This function returns the probability that values in a range are between two limits.",[e("array"),e("probs"),e("lower"),e("upper")]),PRODUCT:d("This function multiplies all the arguments and returns the product.",[e("value1"),e("value2",!0)]),PROPER:d("This function capitalizes the first letter in each word of a text string.",[e("text")]),PV:d("This function returns the present value of an investment based on the interest rate, number and amount of periodic payments, and future value. The present value is the total amount that a series of future payments is worth now.",[e("rate"),e("numper"),e("paymt"),e("fval"),e("type")]),QUARTILE:d("This function returns which quartile (which quarter or 25 percent) of a data set a value is.",[e("array"),e("quart")]),QUOTIENT:d("This function returns the integer portion of a division. Use this to ignore the remainder of a division.",[e("numerator"),e("denominator")]),RADIANS:d("This function converts the specified number from degrees to radians.",[e("value")]),RAND:d("This function returns an evenly distributed random number between 0 and 1.",[]),RANDBETWEEN:d("This function returns a random number between the numbers you specify.",[e("lower"),e("upper")]),RANK:d("This function returns the rank of a number in a set of numbers. If you were to sort the set, the rank of the number would be its position in the list.",[e("number"),e("array"),e("order")]),RATE:d("This function returns the interest rate per period of an annuity.",[e("nper"),e("pmt"),e("pval"),e("fval"),e("type"),e("guess")]),RECEIVED:d("This function returns the amount received at maturity for a fully invested security.",[e("settle"),e("mature"),e("invest"),e("discount"),e("basis")]),REPLACE:d("This function replaces part of a text string with a different text string, based on the number of characters you specify.",[e("old_text"),e("start_char"),e("num_chars"),e("new_text")]),REPT:d("This function repeats text a specified number of times.",[e("text"),e("number")]),RIGHT:d("This function returns the specified rightmost characters from a text value, and based on the number of characters you specify.",[e("text"),e("num_chars")]),ROMAN:d("This function converts an Arabic numeral to a Roman numeral text equivalent.",[e("number"),e("style")]),ROUND:d("This function rounds the specified value to the nearest number, using the specified number of decimal places.",[e("value"),e("places")]),ROUNDDOWN:d("This function rounds the specified number down to the nearest number, using the specified number of decimal places.",[e("value"),e("places")]),ROUNDUP:d("This function rounds the specified number up to the nearest number, using the specified number of decimal places.",[e("value"),e("places")]),ROW:d("This function returns the number of a row from a reference.",[e("reference")]),ROWS:d("This function returns the number of rows in an array.",[e("array")]),RSQ:d("This function returns the square of the Pearson product moment correlation coefficient (R-squared) through data points in known y's and known x's.",[e("array_dep"),e("array_ind")]),SEARCH:d("This function finds one text string in another text string and returns the index of the starting position of the found text.",[e("string1"),e("string2")]),SECOND:d("This function returns the seconds (0 to 59) value for a specified time.",[e("time")]),SERIESSUM:d("This function returns the sum of a power series.",[e("x"),e("n"),e("m"),e("coeff")]),SIGN:d("This function returns the sign of a number or expression.",[e("cellreference")]),SIN:d("This function returns the sine of the specified angle.",[e("angle")]),SINH:d("This function returns the hyperbolic sine of the specified number.",[e("value")]),SKEW:d("This function returns the skewness of a distribution.",[e("number1"),e("number2",!0)]),SLN:d("This function returns the straight-line depreciation of an asset for one period.",[e("cost"),e("salvage"),e("life")]),SLOPE:d("This function calculates the slope of a linear regression.",[e("array_dep"),e("array_ind")]),SMALL:d("This function returns the nth smallest value in a data set, where n is specified.",[e("array"),e("n")]),SQRT:d("This function returns the positive square root of the specified number.",[e("value")]),SQRTPI:d("This function returns the positive square root of a multiple of pi (p).",[e("multiple")]),STANDARDIZE:d("This function returns a normalized value from a distribution characterized by mean and standard deviation.",[e("x"),e("mean"),e("stdev")]),STDEVA:d("This function returns the standard deviation for a set of numbers, text, or logical values.",[e("value1"),e("value2",!0)]),STDEVP:d("This function returns the standard deviation for an entire specified population (of numeric values).",[e("value1"),e("value2",!0)]),STDEVPA:d("This function returns the standard deviation for an entire specified population, including text or logical values as well as numeric values.",[e("value1"),e("value2",!0)]),STEYX:d("This function returns the standard error of the predicted y value for each x. The standard error is a measure of the amount of error in the prediction of y for a value of x.",[e("array_dep"),e("array_ind")]),SUBSTITUTE:d("This function substitutes a new string for specified characters in an existing string.",[e("text"),e("old_piece"),e("new_piece"),e("instance")]),SUBTOTAL:d("This function calculates a subtotal of a list of numbers using a specified built-in function.",[e("functioncode"),e("value1"),e("value2",!0)]),SUM:d("This function returns the sum of cells or range of cells.",[e("value1"),e("value2",!0)]),SUMIF:d("This function adds the cells using a given criteria.",[e("array"),e("condition"),e("sumrange")]),SUMIFS:d("This function adds the cells in a range using multiple criteria.",[e("array"),e("conditionarray"),e("condition",!0)]),SUMPRODUCT:d("This function returns the sum of products of cells. Multiplies corresponding components in the given arrays, and returns the sum of those products.",[e("array1"),e("array2",!0)]),SUMSQ:d("This function returns the sum of the squares of the arguments.",[e("value1"),e("value2",!0)]),SUMX2MY2:d("This function returns the sum of the difference of the squares of corresponding values in two arrays.",[e("array_x"),e("array_y")]),SUMX2PY2:d("This function returns the sum of the sum of squares of corresponding values in two arrays.",[e("array_x"),e("array_y")]),SUMXMY2:d("This function returns the sum of the square of the differences of corresponding values in two arrays.",[e("array_x"),e("array_y")]),SYD:d("This function returns the sum-of-years' digits depreciation of an asset for a specified period.",[e("cost"),e("salvage"),e("life"),e("period")]),T:d("This function returns the text in a specified cell.",[e("value")]),TAN:d("This function returns the tangent of the specified angle.",[e("angle")]),TANH:d("This function returns the hyperbolic tangent of the specified number.",[e("value")]),TBILLEQ:d("This function returns the equivalent yield for a Treasury bill (or T-bill)",[e("settle"),e("mature"),e("discount")]),TBILLPRICE:d("This function returns the price per $100 face value for a Treasury bill (or T-bill).",[e("settle"),e("mature"),e("discount")]),TBILLYIELD:d("This function returns the yield for a Treasury bill (or T-bill).",[e("settle"),e("mature"),e("priceper")]),TDIST:d("This function returns the probability for the t-distribution.",[e("x"),e("deg"),e("tails")]),TEXT:d("This function formats a number and converts it to text.",[e("value"),e("text")]),TIME:d("This function returns the TimeSpan object for a specified time.",[e("hour"),e("minutes"),e("seconds")]),TIMEVALUE:d("This function returns the TimeSpan object of the time represented by a text string.",[e("time_string")]),TINV:d("This function returns the t-value of the student's t-distribution as a function of the probability and the degrees of freedom.",[e("prog"),e("deg")]),TODAY:d("This function returns the date and time of the current date.",[]),TRANSPOSE:d("This function returns a vertical range of cells as a horizontal range or a horizontal range of cells as a vertical range.",[e("array")]),TREND:d("This function returns values along a linear trend. This function fits a straight line to the arrays known x and y values. Trend returns the y values along that line for the array of specified new x values.",[e("y"),e("x"),e("newx"),e("constant")]),TRIM:d("This function removes extra spaces from a string and leaves single spaces between words.",[e("text")]),TRIMMEAN:d("This function returns the mean of a subset of data excluding the top and bottom data.",[e("array"),e("percent")]),TRUE:d("This function returns the value for logical TRUE.",[]),TRUNC:d("This function removes the specified fractional part of the specified number.",[e("value"),e("precision")]),TTEST:d("This function returns the probability associated with a t-test.",[e("array1"),e("array2"),e("tails"),e("type")]),TYPE:d("This function returns the type of value.",[e("value")]),UPPER:d("This function converts text to uppercase letters.",[e("string")]),VALUE:d("This function converts a text string that is a number to a numeric value.",[e("text")]),VAR:d("This function returns the variance based on a sample of a population, which uses only numeric values.",[e("value1"),e("value2",!0)]),
VARA:d("This function returns the variance based on a sample of a population, which includes numeric, logical, or text values.",[e("value1"),e("value2",!0)]),VARP:d("This function returns variance based on the entire population, which uses only numeric values.",[e("value1"),e("value2",!0)]),VARPA:d("This function returns variance based on the entire population, which includes numeric, logical, or text values.",[e("value1"),e("value2",!0)]),VDB:d("This function returns the depreciation of an asset for any period you specify using the variable declining balance method.",[e("cost"),e("salvage"),e("life"),e("start"),e("end"),e("factor"),e("switchnot")]),VLOOKUP:d("This function searches for a value in the leftmost column and returns a value in the same row from a column you specify.",[e("value"),e("array"),e("colindex"),e("approx")]),WEEKDAY:d("This function returns the number corresponding to the day of the week for a specified date.",[e("date"),e("type")]),WEEKNUM:d("This function returns a number that indicates the week of the year numerically.",[e("date"),e("weektype")]),WEIBULL:d("This function returns the two-parameter Weibull distribution, often used in reliability analysis.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),WORKDAY:d("This function returns the number of working days before or after the starting date.",[e("startdate"),e("numdays"),e("holidays")]),XIRR:d("This function calculates the internal rate of return for a schedule of cash flows that may not be periodic.",[e("values"),e("dates"),e("guess")]),XNPV:d("This function calculates the net present value for a schedule of cash flows that may not be periodic.",[e("rate"),e("values"),e("dates")]),YEAR:d("This function returns the year as an integer for a specified date.",[e("date")]),YEARFRAC:d("This function returns the fraction of the year represented by the number of whole days between the start and end dates.",[e("startdate"),e("enddate"),e("basis")]),YIELD:d("This function calculates the yield on a security that pays periodic interest.",[e("settle"),e("maturity"),e("rate"),e("price"),e("redeem"),e("frequency"),e("basis")]),YIELDDISC:d("This function calculates the annual yield for a discounted security.",[e("settle"),e("maturity"),e("price"),e("redeem"),e("basis")]),YIELDMAT:d("This function calculates the annual yield of a security that pays interest at maturity.",[e("settle"),e("maturity"),e("issue"),e("issrate"),e("price"),e("basis")]),ZTEST:d("This function returns the significance value of a z-test. The z-test generates a standard score for x with respect to the set of data and returns the two-tailed probability for the normal distribution.",[e("array"),e("x"),e("sigma")]),HBARSPARKLINE:d("This function returns a data set used for representing a Hbar sparkline",[e("value"),e("colorScheme")]),VBARSPARKLINE:d("This function returns a data set used for representing a Vbar sparkline",[e("value"),e("colorScheme")]),VARISPARKLINE:d("This function returns a data set used for representing a variance sparkline",[e("variance"),e("reference"),e("mini"),e("maxi"),e("mark"),e("tickunit"),e("legend"),e("colorPositive"),e("colorNegative"),e("vertical")]),PIESPARKLINE:d("This function returns a data set used for representing a pie sparkline",[e("range|percentage"),e("color",!0)]),AREASPARKLINE:d("This function returns a data set used for representing a area sparkline",[e("points"),e("mini"),e("maxi"),e("line1"),e("line2"),e("colorPositive"),e("colorNegative")]),SCATTERSPARKLINE:d("This function returns a data set used for representing a scatter sparkline",[e("points1"),e("points2"),e("minX"),e("maxX"),e("minY"),e("maxY"),e("hLine"),e("vLine"),e("xMinZone"),e("xMaxZone"),e("yMinZone"),e("yMaxZone"),e("tags"),e("drawSymbol"),e("drawLines"),e("color1"),e("color2"),e("dash")]),LINESPARKLINE:d("This function returns a data set used for representing a line sparkline",[e("data"),e("dataOrientation"),e("dateAxisData"),e("dateAxisOrientation"),e("setting")]),COLUMNSPARKLINE:d("This function returns a data set used for representing a column sparkline",[e("data"),e("dataOrientation"),e("dateAxisData"),e("dateAxisOrientation"),e("setting")]),WINLOSSSPARKLINE:d("This function returns a data set used for representing a win/loss sparkline",[e("data"),e("dataOrientation"),e("dateAxisData"),e("dateAxisOrientation"),e("setting")]),BULLETSPARKLINE:d("This function returns a data set used for representing a bullet sparkline",[e("measure"),e("target"),e("maxi"),e("good"),e("bad"),e("forecast"),e("tickunit"),e("colorScheme"),e("vertical")]),SPREADSPARKLINE:d("This function returns a data set used for representing a spread sparkline",[e("points"),e("showAverage"),e("scaleStart"),e("scaleEnd"),e("style"),e("colorScheme"),e("vertical")]),STACKEDSPARKLINE:d("This function returns a data set used for representing a stacked sparkline",[e("points"),e("colorRange"),e("labelRange"),e("maximum"),e("targetRed"),e("targetGreen"),e("targetBlue"),e("tragetYellow"),e("color"),e("highlightPosition"),e("vertical"),e("textOrientation"),e("textSize")]),BOXPLOTSPARKLINE:d("This function returns a data set used for representing a boxplot sparkline",[e("points"),e("boxPlotClass"),e("showAverage"),e("scaleStart"),e("scaleEnd"),e("acceptableStart"),e("acceptableEnd"),e("colorScheme"),e("style"),e("vertical")]),CASCADESPARKLINE:d("This function returns a data set used for representing a cascade sparkline",[e("pointsRange"),e("pointIndex"),e("labelsRange"),e("minimum"),e("maximum"),e("colorPositive"),e("colorNegative"),e("vertical")]),PARETOSPARKLINE:d("This function returns a data set used for representing a pareto sparkline",[e("points"),e("pointIndex"),e("colorRange"),e("target"),e("target2"),e("highlightPosition"),e("label"),e("vertical")]),MONTHSPARKLINE:d("This function returns a data set used for representing a month sparkline",[e("year"),e("month"),e("dataRange"),e("emptyColor"),e("startColor"),e("middleColor"),e("endColor")]),YEARSPARKLINE:d("This function returns a data set used for representing a year sparkline",[e("year"),e("dataRange"),e("emptyColor"),e("startColor"),e("middleColor"),e("endColor")]),"CEILING.PRECISE":d("This function rounds a number up to the nearest integer or to the nearest multiple of a specified value.",[e("number"),e("signif")]),"COVARIANCE.S":d("This function returns the sample covariance, which is the average of the products of deviations for each data point pair in two sets of numbers.",[e("array1"),e("array2")]),"FLOOR.PRECISE":d("This function rounds a number down to the nearest integer or to the nearest multiple of a specified value.",[e("number"),e("signif")]),"PERCENTILE.EXC":d("This function returns the nth percentile of values in a range.",[e("array"),e("n")]),"QUARTILE.EXC":d("This function returns which quartile (which quarter or 25 percent) of a data set a value is.",[e("array"),e("quart")]),"RANK.AVG":d("This function returns the rank of a number in a set of numbers. If some values have the same rank, it will return the average rank.",[e("number"),e("array"),e("order")]),"MODE.MULT":d("This function returns the most frequently occurring vertical array or the most frequently occurring value in a set of data.",[e("number1"),e("number2",!0)]),"STDEV.P":d("This function returns the standard deviation for an entire specified population (of numeric values).",[e("value1"),e("value2",!0)]),"VAR.P":d("This function returns variance based on the entire population, which uses only numeric values.",[e("value1"),e("value2",!0)]),"COVARIANCE.P":d("This function returns the covariance, which is the average of the products of deviations for each data point pair in two sets of numbers.",[e("array1"),e("array2")]),"MODE.SNGL":d("This function returns the most frequently occurring value in a set of data.",[e("value1"),e("value2",!0)]),"PERCENTILE.INC":d("This function returns the nth percentile of values in a range.",[e("array"),e("n")]),"QUARTILE.INC":d("This function returns which quartile (which quarter or 25 percent) of a data set a value is.",[e("array"),e("quart")]),"RANK.EQ":d("This function returns the rank of a number in a set of numbers. If you were to sort the set, the rank of the number would be its position in the list.",[e("number"),e("array"),e("order")]),STDEV:d("This function returns standard deviation is estimated based on a sample.",[e("number1"),e("number2",!0)]),"STDEV.S":d("This function returns standard deviation is estimated based on a sample.",[e("number1"),e("number2",!0)]),"VAR.S":d("This function returns the variance based on a sample of a population, which uses only numeric values.",[e("value1"),e("value2",!0)]),"BETA.INV":d("This function calculates the inverse of the cumulative beta distribution function.",[e("prob"),e("alpha"),e("beta"),e("lower"),e("upper")]),"BINOM.DIST":d("This function calculates the individual term binomial distribution probability.",[e("x"),e("n"),e("p"),e("cumulative")]),"BINOM.INV":d("This function returns the criterion binomial, the smallest value for which the cumulative binomial distribution is greater than or equal to a criterion value.",[e("n"),e("p"),e("alpha")]),"CHISQ.DIST.RT":d("This function calculates the one-tailed probability of the chi-squared distribution.",[e("value"),e("deg")]),"CHISQ.INV.RT":d("This function calculates the inverse of the one-tailed probability of the chi-squared distribution.",[e("prob"),e("deg")]),"CHISQ.TEST":d("This function calculates the test for independence from the chi-squared distribution.",[e("obs_array"),e("exp_array")]),"CONFIDENCE.NORM":d("This function returns confidence interval for a population mean.",[e("alpha"),e("stdev"),e("size")]),"EXPON.DIST":d("This function returns the exponential distribution or the probability density.",[e("value"),e("lambda"),e("cumulative")]),"F.DIST.RT":d("This function calculates the F probability distribution, to see degrees of diversity between two sets of data.",[e("value"),e("degnum"),e("degden")]),"F.INV.RT":d("This function returns the inverse of the F probability distribution.",[e("p"),e("degnum"),e("degden")]),"F.TEST":d("This function returns the result of an F-test, which returns the one-tailed probability that the variances in two arrays are not significantly different.",[e("array1"),e("array2")]),"GAMMA.DIST":d("This function returns the gamma distribution.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),"GAMMA.INV":d("This function returns the inverse of the gamma cumulative distribution.",[e("p"),e("alpha"),e("beta")]),"LOGNORM.INV":d("This function returns the inverse of the lognormal cumulative distribution function of x, where LN(x) is normally distributed with the specified mean and standard deviation.",[e("prob"),e("mean"),e("stdev")]),"NORM.DIST":d("This function returns the normal cumulative distribution for the specified mean and standard deviation.",[e("x"),e("mean"),e("stdev"),e("cumulative")]),"NORM.INV":d("This function returns the inverse of the normal cumulative distribution for the given mean and standard deviation.",[e("prob"),e("mean"),e("stdev")]),"NORM.S.INV":d("This function returns the inverse of the standard normal cumulative distribution. The distribution has a mean of zero and a standard deviation of one.",[e("prob")]),"PERCENTRANK.INC":d("This function returns the rank of a value in a data set as a percentage of the data set.",[e("array"),e("n"),e("signif")]),"POISSON.DIST":d("This function returns the Poisson distribution.",[e("nevents"),e("mean"),e("cumulative")]),"T.INV.2T":d("This function returns the t-value of the student's t-distribution as a function of the probability and the degrees of freedom.",[e("prog"),e("deg")]),"T.TEST":d("This function returns the probability associated with a t-test.",[e("array1"),e("array2"),e("tails"),e("type")]),"WEIBULL.DIST":d("This function returns the two-parameter Weibull distribution, often used in reliability analysis.",[e("x"),e("alpha"),e("beta"),e("cumulative")]),"Z.TEST":d("This function returns the significance value of a z-test. The z-test generates a standard score for x with respect to the set of data and returns the two-tailed probability for the normal distribution.",[e("array"),e("x"),e("sigma")]),"T.DIST.RT":d("This function returns the right-tailed t-distribution.",[e("x"),e("deg")]),"T.DIST.2T":d("This function returns the two-tailed t-distribution.",[e("x"),e("deg")]),"ISO.CEILING":d("This function returns a number up to the nearest integer or to the nearest multiple of significance, regardless of sign of significance.",[e("number"),e("signif")]),"BETA.DIST":d("This function returns the beta distribution.",[e("x"),e("alpha"),e("beta"),e("cumulative"),e("lower"),e("upper")]),"GAMMALN.PRECISE":d("This function returns the natural logarithm of the gamma function.",[e("value")]),"ERF.PRECISE":d("This function returns the error function.",[e("lowerlimit")]),"ERFC.PRECISE":d("This function returns the complementary ERF function.",[e("lowerlimit")]),"PERCENTRANK.EXC":d("This function returns the percentage rank(0..1, exclusive) of a value in a data set.",[e("array"),e("n"),e("signif")]),"HYPGEOM.DIST":d("This function returns the hypergeometric distribution.",[e("x"),e("n"),e("M"),e("N"),e("cumulative")]),"LOGNORM.DIST":d("This function returns the log normal distribution of x.",[e("x"),e("mean"),e("stdev"),e("cumulative")]),"NEGBINOM.DIST":d("This function returns the negative binomial distribution.",[e("x"),e("r"),e("p"),e("cumulative")]),"NORM.S.DIST":d("This function returns the standard normal distribution.",[e("z"),e("cumulative")]),"T.DIST":d("This function returns the t-distribution.",[e("x"),e("deg"),e("cumulative")]),"F.DIST":d("This function returns the F probability distribution.",[e("x"),e("degnum"),e("degden"),e("cumulative")]),"CHISQ.DIST":d("This function returns the chi-squared distribution.",[e("x"),e("deg"),e("cumulative")]),"F.INV":d("This function returns the inverse of the F probability distribution.",[e("probability"),e("degnum"),e("degden")]),"T.INV":d("This function returns the left-tailed inverse of the t-distribution.",[e("probability"),e("deg")]),"CHISQ.INV":d("This function returns the inverse of left-tailed probability of the chi-squared distribution.",[e("probability"),e("deg")]),"CONFIDENCE.T":d("This function returns the confidence interval for a Student's t distribution.",[e("alpha"),e("stdev"),e("size")]),"NETWORKDAYS.INTL":d("This function returns the number of workdays between two dates using arguments to indicate holidays and weekend days.",[e("startdate"),e("enddate"),e("weekend"),e("holidays")]),"WORKDAY.INTL":d("This function returns the serial number of the date before or after a number of workdays with custom weekend parameters. These parameters indicate weekend days and holidays.",[e("startdate"),e("numdays"),e("weekend"),e("holidays")]),REFRESH:d("This function decides how to re-calculate the formula. Can use the evaluateMode argument to specific the formula re-calculate on the reference value changed, evaluate once , re-calculate or interval.",[e("formula"),e("evaluateMode"),e("interval")]),DAYS:d("This function returns the number of days between two dates.",[e("startdate"),e("enddate")]),ISOWEEKNUM:d("This function returns the number of the ISO week number of the year for a given date.",[e("date")]),BITAND:d('This function returns a bitwise "AND" of two numbers.',[e("number1"),e("number2")]),BITLSHIFT:d('This function returns a bitwise "OR" of two numbers.',[e("number1"),e("number2")]),BITOR:d('This function returns a bitwise "OR" of two numbers.',[e("number1"),e("number2")]),BITRSHIFT:d('This function returns a bitwise "OR" of two numbers.',[e("number1"),e("number2")]),BITXOR:d('This function returns a bitwise "XOR" of two numbers.',[e("number1"),e("number2")]),IMCOSH:d("This function returns the hyperbolic cosine of a complex number in x+yi or x+yj text format.",[e("complexnum")]),IMCOT:d("This function returns the cotangent of a complex number in x+yi or x+yj text format.",[e("complexnum")]),IMCSC:d("This function returns the cosecant of a complex number in x+yi or x+yj text format.",[e("complexnum")]),IMCSCH:d("This function returns the hyperbolic cosecant of a complex number in x+yi or x+yj text format.",[e("complexnum")]),IMSEC:d("This function returns the secant of a complex number in x+yi of x+yj text format.",[e("complexnum")]),IMSECH:d("This function returns the hyperbolic secant of a complex number in x+yi or x+yj text format.",[e("complexnum")]),IMSINH:d("This function returns the hyperbolic sine of a complex number in x+yi of x+yj text format.",[e("complexnum")]),IMTAN:d("This function returns the tangent of a complex number in x+yi or x+yj text format.",[e("complexnum")]),PDURATION:d("This function returns the number of periods required by an investment to reach a specified value.",[e("rate"),e("pval"),e("fval")]),RRI:d("This function returns an equivalent interest rate for the growth of an investment.",[e("nper"),e("pval"),e("fval")]),ISFORMULA:d("This function tests whether contains a formula of a referenced cell.",[e("cellreference")]),IFNA:d("This function returns the value you specify if the formula returns the #N/A error value, otherwise returns the result of the formula.",[e("value"),e("value_if_na")]),IFS:d("This function checks whether one or more conditions are met and returns a value that corresponds to the first TRUE condition.",[e("valueTest"),e("valueTrue"),e("....")]),SWITCH:d("This function evaluates one value for a list of values, and returns the result corresponding to the first matching value, otherwise returns the default value",[e("convertvalue"),e("matchvalue"),e("matchtrue"),e("matchfalse")]),XOR:d("This function returns a logical exclusive or of all arguments.",[e("logical"),e("....")]),AREAS:d("This function returns the number of areas in a reference.",[e("reference")]),FORMULATEXT:d("This function returns a formula as a string.",[e("reference")]),HYPERLINK:d("This function creates a shortcut or jump that opens a document stored on a network server, an intranet, or the Internet.",[e("link_location"),e("friendly_name")]),ACOT:d("This function calculates the inverse arccotangent of a number.",[e("value")]),ACOTH:d("This function calculates the inverse hyperbolic arccotangent of a number.",[e("value")]),ARABIC:d("This function converts a Roman numeral text to an Arabic numeral equivalent.",[e("text")]),BASE:d("This function converts a number into a text representation with the given radix (base).",[e("number"),e("radix"),e("minLength")]),"CEILING.MATH":d("This function round  a number up to the nearest integer or to the nearest multiple of significance.",[e("value"),e("signif"),e("mode")]),COMBINA:d("This function calculates the number of possible combinations with repetitions for a specified number of items.",[e("number"),e("number_choosen")]),COT:d("This function returns the cotangent of the specified angle.",[e("angle")]),COTH:d("This function returns the hyperbolic cotangent of the specified number.",[e("value")]),CSC:d("This function returns the cosecant of the specified angle.",[e("angle")]),CSCH:d("This function returns the hyperbolic cosecant of the specified number.",[e("value")]),DECIMAL:d("This function converts a text representation of a number in a given base into a decimal number.",[e("text"),e("radix")]),"FLOOR.MATH":d("This function round a number down to the nearest integer or to the nearest multiple of significance.",[e("value"),e("signif"),e("mode")]),SEC:d("This function returns the secant of the specified angle.",[e("angle")]),SECH:d("This function returns the hyperbolic secant of the specified value.",[e("value")]),"BINOM.DIST.RANGE":d("This function calculates the probability of a trial result using a binomial distribution.",[e("x"),e("n"),e("p"),e("cumulative")]),GAMMA:d("This function returns the gamma function value.",[e("number")]),MAXIFS:d("This function returns the maximum value among cells specified by a given set of conditions or criteria.",[e("array"),e("conditionarray"),e("condition",!0)]),GAUSS:d("This function calculates the probability that a member of a standard normal population will fall between the mean and z standard deviations from the mean.",[e("number")]),MINIFS:d("This function returns the minimum value among cells specified by a given set of conditions or criteria.",[e("array"),e("conditionarray"),e("condition",!0)]),PERMUTATIONA:d("This function returns the number of permutations for a given number of object that can be selected from the total objects.",[e("k"),e("n")]),PHI:d("This function returns the value of the density function for a standard normal distribution.",[e("value")]),"SKEW.P":d("This function returns the skewness of a distribution base on a poopulation: a characterization of the degree of asymmetry of a distribution around its mean.",[e("number1"),e("number2",!0)]),BAHTTEXT:d('This function converts a number to Thai text and adds a suffix of "Baht"',[e("number")]),CONCAT:d('This function combines multiple text strings or numbers into one text string, the function will stay available for compatibility with "CONCATENATE" function.',[e("text1"),e("text2"),e("....")]),FINDB:d("This function finds one text value within another and returns the text value's position in the text you searched, and counts each double-byte characte as 2 when set DBCS as the default language.",[e("findtext"),e("intext"),e("start")]),LEFTB:d("This function returns the specified leftmost characters from a text value, and based on the number of bytes you specify.",[e("mytext"),e("num_bytes")]),LENB:d("This function returns the length of the number of bytes in a text string.",[e("value")]),MIDB:d("This function returns the requested number of characters from a text string starting at the position you specify, and based on the number of bytes you specify.",[e("text"),e("start_num"),e("num_bytes")]),REPLACEB:d("This function replaces part of a text string with a different text string, based on the number of bytes you specify.",[e("old_text"),e("start_byte"),e("num_bytes"),e("new_text")]),RIGHTB:d("This function returns the specified rightmost characters from a text value, and based on the number of bytes you specify.",[e("text"),e("num_bytes")]),SEARCHB:d("This function finds one text string in another text string and returns the index of the starting position of the found text, and counts each double-byte characte as 2 when set DBCS as the default language.",[e("string1"),e("string2")]),TEXTJOIN:d("This function combines multiple ranges and/or strings into one text, and the text includes a delimiter you specify between each text value.",[e("delimiter"),e("ignore_empty"),e("text1"),e("text2"),e("....")]),UNICHAR:d("This function returns the Unicode character of a given numeric reference.",[e("number")]),UNICODE:d("This function returns the number corresponding to the first character of the text.",[e("text")]),ENCODEURL:d("This function returns a URL-encoded string.",[e("text")]),BC_QRCODE:d("This function returns a data set for representing a QRCode",[e("value"),e("color"),e("backgroundColor"),e("errorCorrectionLevel"),e("model"),e("version"),e("mask"),e("connection"),e("connectionNo"),e("charCode"),e("charset"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_EAN13:d("This function returns a data set for representing a EAN13.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("addOn"),e("addOnLabelPosition"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_EAN8:d("This function returns a data set for representing a EAN8.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODABAR:d("This function returns a data set for representing a CODABAR.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("checkDigit"),e("nwRatio"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE39:d("This function returns a data set for representing a CODE39.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("labelWithStartAndStopCharacter"),e("checkDigit"),e("nwRatio"),e("fullASCII"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE93:d("This function returns a data set for representing a CODE93.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("checkDigit"),e("fullASCII"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE128:d("This function returns a data set for representing a CODE128.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("codeSet"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_GS1_128:d("This function returns a data set for representing a GS1_128.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_CODE49:d("This function returns a data set for representing a CODE49.",[e("value"),e("color"),e("backgroundColor"),e("showLabel"),e("labelPosition"),e("grouping"),e("groupNo"),e("fontFamily"),e("fontStyle"),e("fontWeight"),e("textDecoration"),e("textAlign"),e("fontSize"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_PDF417:d("This function returns a data set for representing a PDF417.",[e("value"),e("color"),e("backgroundColor"),e("errorCorrectionLevel"),e("rows"),e("columns"),e("compact"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")]),BC_DATAMATRIX:d("This function returns a data set for representing a DATAMATRIX.",[e("value"),e("color"),e("backgroundColor"),e("eccMode"),e("ecc200SymbolSize"),e("ecc200EncodingMode"),e("ecc00_140SymbolSize"),e("structuredAppend"),e("structureNumber"),e("fileIdentifier"),e("quietZoneLeft"),e("quietZoneRight"),e("quietZoneTop"),e("quietZoneBottom")])},Fbx_Summary:"Summary",Fbx_TableName_Description:"Table name for ",Fbx_CustomName_Description:"Custom name for ",B2:{All:{name:"#All",description:"Returns the entire contents of the table, or specified table columns including column headers, data and total rows."},Data:{name:"#Data",description:"Returns the data cells of the table or specified table columns."},Headers:{name:"#Headers",description:"Returns the columns headers for the table, or specified table columns."},Totals:{name:"#Totals",description:"Returns the total rows for the table or specified table columns."},thisRow:{name:"#This Row",description:"This row."}}}},"./src/evaluator.ts":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Common"),e=c("./src/calc.common.ts"),f=null,g=void 0,h=Math.pow,i=Math.min,j=Math.max,k="string",l="boolean",m=e.Convert.Th,n=e.Convert.vf,o=e.Convert.Ph,p=e.Convert.Ca,q=e.Convert.Fh,r=e.Errors.Reference,s=e.Errors.Value,t=e.Errors.Name,u=e.Errors.NotAvailable,v=e.Errors.DivideByZero,w=e.Errors.Number;function K(a,b,c,d){return{row:a,col:b,rowCount:c,colCount:d}}x=function(){function a(a,b,c,d){var e,f,g=[];for(e=4;e<arguments.length;e++)g[e-4]=arguments[e];this.si=0,this.ti=0,this.ui=0,f=this,f.source=a,f.arrayFormulaMode=!!c,f.identity=b||f.$Ab(0,0),f.arrayIdentity=d,f.row=d?d.row:f.identity.row||0,f.column=d?d.col:f.identity.col||0,f.rowOffset=d?f.identity.row-d.row:f.row,f.columnOffset=d?f.identity.col-d.col:f.column}return a.prototype.$Ab=function(a,b){return{row:a,col:b}},a.prototype.fga=function(a){return a===e.BangSource?this.source:a},a.prototype.getValue=function(a,b){return a?this.fga(a).getValue(b):r},a.prototype.getReference=function(a,b){return a?this.fga(a).getReference(b):r},a.prototype.getSheetRangeReference=function(a){return a&&a.source&&a.endSource?this.fga(a.source).getSheetRangeReference(a):r},a.prototype.getFunction=function(a){var b=this;return b.source?b.source.getCustomFunction(a):f},a.prototype.getName=function(a){var b,c=this,d=c.source?c.source.getCustomName(a):f;return!d&&c.source&&(b=c.source.Cf().getGlobalSourceModel(),d=b&&b.getSource().getCustomName(a)),d},a.prototype.vi=function(){return this.ui>0},a.prototype.wi=function(){this.ui++},a.prototype.xi=function(){this.ui--},a.prototype.SetAsyncResult=function(a,b){this.source.Cf().lh.SetAsyncResult(a,b)},a.prototype.cloneFrom=function(a){var b=this;b.acceptsReference=a.acceptsReference,b.arrayFormulaMode=a.arrayFormulaMode},a}(),b.EvaluateContext=x,e.CalcSource.prototype.getEvaluatorContext=function(a,b,c){return new x(this,a,b,c)},y=function(){function a(a,b){this.id=a.si++,this.ctx=a,this.row=a.row+(a.arrayFormulaMode&&a.rowOffset||0),this.col=a.column+(a.arrayFormulaMode&&a.columnOffset||0),this.node=b}return a.prototype.setAsyncResult=function(a){this.node.Fi=a,this.node.v5=!0,this.ctx.SetAsyncResult(this,a),this.ctx.source.refresh()},a.prototype.getFormula=function(){return this.ctx.source.getCalcService().unparse(this.ctx.source,this.expression,this.row,this.col)},a}(),b.AsyncEvaluateContext=y,z=function(a){J(b,a);function b(b,c,d,e,g,h,i,j){var k=a.call(this,b,isNaN(j)?f:{row:i,col:j},c,isNaN(h)?f:{row:d,col:e,rowCount:g,colCount:h})||this,l=b.Cf().getGlobalSourceModel(b);return k.yi=l&&l.getSource(),k}return b.prototype.getName=function(a){var b=this;return b.yi?b.yi.getCustomName(a):f},b}(x),b.GloableEvaluateContext=z;function L(a,b){return a&&b?a.row===b.row&&a.col===b.col:a===b}A=function(){function a(a){this.service=a,this.asyncManager=new C(this)}return a.prototype.evaluateExpression=function(a,b,c,e,h){var i,j,k=void 0===a.type&&a.Lf?a.Lf:a,l=this.evaluate(k,b,!!e,!!h);return l===g||l===f?c===!1?l:0:((p(l)&&!e||q(l)&&!h)&&(l=this.getOneValue(b,l)),i=15,"number"==typeof l&&l>-10&&l<10&&(j=""+l,(j.indexOf("000000000000")>1||j.indexOf("999999999999")>1)&&(i=14)),d.Common.o.Lma(l,i))},a.prototype.evaluate=function(a,b,c,d){var h,i,j;if(!a)throw e.sR().Exp_ExprIsNull;if(void 0===a.type){if(a._error)return a;a.Lf&&(a=a.Lf)}for(;10===a.type;)a=a.value;if(h=this,j=f,12===a.type?(j=a.needExpendIndexs,a=a.value):b.Exb&&(c=!0,j=[]),a.type>=2&&a.type<=6)i=h.evaluateConst(a,b,c);else if(1===a.type||13===a.type)i=h.evaluateReference(a,b,d,c);else if(8===a.type)i=h.evaluateName(a,b,c,d);else if(9!==a.type||a.value2){if(9===a.type)i=h.evaluateBinaryOperation(a,b,c,d,j);else if(7===a.type)i=h.evaluateFunction(a,b,c,d,j);else if(11===a.type)i=g;else if(b.evaluate)return b.evaluate(a,c,d)}else i=h.evaluateUnaryOperation(a,b,c,d,j);return i},a.prototype.evaluateConst=function(a,b,c){var d=a.value;return p(d)?c||b&&(b.arrayFormulaMode||b.vi())?d:d.getValue(0,0):d},a.prototype.evaluateReference=function(a,b,c,d){var f,g,h,i,j,k,l,m,n,o,p,q,t;if(!b)return s;if(f=a.source||b.source,g=isNaN(b.row)?0:b.row,h=isNaN(b.column)?0:b.column,
i=a.getRange&&a.getRange(g,h),!i)return 13===a.type?s:r;if(j=i.row<0?0:i.row,k=i.col<0?0:i.col,l=i.rowCount,m=i.colCount,c||b.arrayFormulaMode)return a.endSource?b.getSheetRangeReference({source:a.source,endSource:a.endSource,row:j,col:k,rowCount:l,colCount:m}):b.getReference(f,i);if(d&&(l>1||m>1)){for(n=[],o=0;o<l;o++)for(n[o]=[],p=0;p<m;p++)n[o][p]=f.getValue(o+j,p+k);return new e.CalcArray(n)}if(l>1||m>1)if(q=g>=i.row&&g<i.row+l,t=h>=i.col&&h<i.col+m,q&&t)j=g,k=h;else{if(!q&&!t)return s;if(q&&1===m)j=g;else{if(!t||1!==l)return s;k=h}}else if(i.row===-1&&l===-1){if(1!==m)return s;j=g}else if(i.col===-1&&m===-1){if(1!==l)return s;k=h}return f.getValue(j,k)},a.prototype.evaluateName=function(a,b,c,d){var e,f;return b?(a.source&&(e=a.source,b=e.getEvaluatorContext(b.identity,b.arrayFormulaMode,b.arrayIdentity)),f=b.getName(a.value),f?this.evaluate(f,b,c,d):t):t},a.prototype.evaluateUnaryOperation=function(a,b,c,d,f){var g,h=!1;return f&&f.length>0&&(c=!0,h=!0),g=this.evaluate(a.value,b,c,h),e.Convert.vf(g)?g:g===e.missingArgument?u:this.evaluateWithArgs(a,function(c){return N(a.operatorType,c[0],b.Exb)},b,f,[g],c,d)},a.prototype.evaluateBinaryOperation=function(a,b,c,d,f){var g,h,i,j,k,l,m,n,o=b.Exb;if(o&&(c=!0),g=[c,c],h=a.operatorType>=15,i=[h,h],f&&f.length>0)for(j=0;j<f.length;j++)g[f[j]]=!0,i[f[j]]=!0;for(k=function(a,b){if(0===b){for(;10===a.type;)a=a.value;if(7===a.type&&a.function&&a.function.TAb)return null}return b},l=[a.value,a.value2],m=[],j=0;j<2;j++){if(n=k(l[j],this.evaluate(l[j],b,g[j],i[j])),e.Convert.vf(n))return n;if(n===e.missingArgument)return u;m[j]=n}return this.evaluateWithArgs(a,function(b){return N(a.operatorType,b[0],b[1])},b,f,m,c,d)},a.prototype.startCache=function(){this.Bi={},this.service.nia&&this.asyncManager.startCalc()},a.prototype.endCache=function(){this.Bi=f},a.prototype.evaluateFunction=function(a,b,c,d,g){var h,i,j,k,l,m,n,o,r,s,u,v,w,x,y,z,A,B,C,D,E,F,G;if(!a||!a.function||"string"==typeof a.function)return t;if(h=a.arguments.length,i=this,j=b.ti,k=a.function,n=f,k instanceof e.Functions.AsyncFunction&&this.asyncManager.startCalcFunction(k,a.arguments,b),0===h)l=[];else{for(l=[],r=[],s=[],u=0;u<h;u++)r[u]=k.acceptsArray(u),s[u]=k.acceptsReference(u);if(v=b.Exb,v||1!==k.arrayArgumentEvaluateMode||(v=b.Exb=!0,o=!0),g&&g.length>0||v){for(n=[],u=0;u<h;u++)n[u]=!0;for(w=v?h:g.length,u=0;u<w;u++)x=v?u:g[u],r[x]=!0,s[x]=!0,y=!(k.acceptsReference(x)&&!k.acceptsArray(x)),y&&v&&k.acceptsReference(x)&&(y=!1),n[x]=y}for(k.isBranch()&&(b.arrayFormulaMode||b.vi())&&(r[k.findTestArgument()]=!0,s[k.findTestArgument()]=!0),z=void 0,A=-1,B=-1,k.isBranch()&&(A=k.findTestArgument(),k.acceptsArray(A)&&b.wi(),z=i.evaluate(a.arguments[A],b,r[A],s[A]),k.acceptsArray(A)&&b.xi(),q(z)||p(z)||(B=k.findBranchArgument(z))),u=0;u<h;u++){if(A===u)m=z;else{if(B>=0&&u!==B){l[u]=null;continue}k.acceptsArray(u)&&b.wi(),m=i.evaluate(a.arguments[u],b,r[u],s[u]),k.acceptsArray(u)&&b.xi()}if(!k.acceptsArray(u)&&p(m)||!k.acceptsReference(u)&&q(m))if(C=p(m)?m:f,D=q(m)?m:f,C&&1===C.getRowCount()&&1===C.getColumnCount())m=C.getValue(0,0);else if(D&&1===D.getRowCount(0)&&1===D.getColumnCount(0))m=D.getValue(0,0,0);else{if(E=[],F=!1,g)for(G=0;G<g.length;G++)E.push(g[G]),g[G]===u&&(F=!0);F||(E.push(u),g=E)}if(e.Convert.vf(m)&&!k.acceptsError(u))return m;m!==e.missingArgument||k.acceptsMissingArgument(u)||(m=f),l[u]=m}}return o&&delete b.Exb,i.evaluateWithArgs(a,function(a,c,f){var g,h,l,m,n,o=W(a,k.Ci);return a=o.Di,k.isContextSensitive()?(b.acceptsReference=d||b.vi(),k instanceof e.Functions.AsyncFunction?(i.T4=i.T4||{},k.evaluateWhenArgumentsIsSame()?i.evaluateAsyncFunction(b,c,k,j,a,f):i.evaluateWithCache(k,a,function(){return i.evaluateAsyncFunction(b,c,k,j,a,f)},i.T4)):k.evaluateWithContext(b,a)):(h=b.arrayFormulaMode,h&&(k.y0a={row:b.rowOffset,col:b.columnOffset}),l=e.getMapping()&&e.getMapping().builtInFunctionsMapping,m=l&&l[k.name]&&l[k.name].specialFun,n=function(){return o.Ei?m&&m.apply(k,a)||k.evaluate.apply(k,a):o.Fi},g=h?i.evaluateWithCache(k,a,n,i.Bi):n(),delete k.y0a,g)},b,g,l,c,d,n)},a.prototype.evaluateAsyncFunction=function(a,b,c,d,e,f){var h,i,j=this.asyncManager.getNode();if(j.v5)h=j.Fi;else if(j.canEvaluate()){if(i=new y(a,j),h=c.evaluateWithContext(i,e),i.notAsync)return h;h===g?h=j.Fi!==g?j.Fi:c.defaultValue():1===j.evaluateMode&&(j.v5=!0)}else h=j.Fi!==g?j.Fi:c.defaultValue();return j.Fi=h,f||this.asyncManager.endCalcFunction(),h},a.prototype.evaluateWithCache=function(a,b,c,d){var f,h,i,j,k,l,m,n,o,p,q,r;if(d)if(h=!1,f=d[a.name+b.length])for(i=0;i<f.length;i++){if(j=f[i],h=!1,j.args.length===b.length){for(h=!0,k=0;k<b.length;k++)if(l=j.args[k],m=b[k],l!==m){if(!(l instanceof e.CalcReference&&m instanceof e.CalcReference)){h=!1;break}if(n=l.zf,l.xf!==m.xf||n!==m.zf){h=!1;break}for(o=0;o<n;o++)if(l.getRow(o)!==m.getRow(o)||l.getColumn(o)!==m.getColumn(o)||l.getRowCount(o)!==m.getRowCount(o)||l.getColumnCount(o)!==m.getColumnCount(o)){h=!1;break}}h&&!L(j.y0a,a.y0a)&&(h=!1)}if(h)return j.flag++,j.value}else f=d[a.name+b.length]=[];if(p=c(),d&&p!==g){if(q=f.length,q>=100){for(r=[],i=0;i<q;i++)f[i].flag>0&&r.push(f[i]);r.sort(function(a,b){return b.flag-a.flag}),q=Math.min(50,r.length),f=r.slice(0,q),d[a.name+b.length]=f}f.push({args:b,value:p,flag:0,y0a:a.y0a})}return p},a.prototype.SetAsyncResult=function(a){var b,c,d=[];for(b=1;b<arguments.length;b++)d[b-1]=arguments[b];c=a.ctx,c.source.Cf().recalculate(c.source,a.row,a.col,!0)},a.prototype.evaluateWithArgs=function(a,b,c,d,f,g,h,i){var j,k,l,m,n,o,r,t,v,w,x,y,z,A,B=c.Exb;if(!B||d&&d.length>0||(d=[],j=7===a.type&&a.function,j&&f.forEach(function(a,b){(!j.acceptsArray(b)&&p(a)||!j.acceptsReference(b)&&q(a))&&d.push(b)})),d&&d.length>0){if(l=this.Gi(c,a,f,d,i),!l)return u;for(m=[],n=l[0].length,o=l[0][0].length,r=0;r<n;r++)for(m[r]=[],t=0;t<o;t++)try{for(v=[],w=0;w<f.length;w++)v[w]=l[w][r][t];if(k=b(v,a,r+1!==n||t+1!==o),q(k)&&!B){if(7===a.type&&(j=a.function,j.returnReference&&j.returnReference()))return k;k=this.getOneValue(c,k)}m[r][t]=k}catch(a){m[r][t]=s}return new e.CalcArray(m)}try{if(k=b(f,a),c.arrayFormulaMode&&c.vi())return k;if(p(k)&&!g)return this.getOneValue(c,k);if(q(k)&&!h){if(x=k,g&&1===x.getRangeCount()){for(y=[],z=0;z<x.getRowCount(0);z++)for(y[z]=[],A=0;A<x.getColumnCount(0);A++)y[z][A]=x.getValue(0,z,A);return new e.CalcArray(y)}return this.getOneValue(c,k)}return k}catch(a){return s}},a.prototype.getOneValue=function(a,b){var c,d,e,f,g,h;return q(b)?(e=b,f=e.getRowCount(0),g=e.getColumnCount(0),e.getRangeCount()>1||!a.arrayFormulaMode&&f>1&&g>1?b=s:(a.rowOffset!==-1||a.columnOffset!==-1?(c=1===f?0:a.arrayFormulaMode?a.rowOffset:a.rowOffset-e.getRow(0),d=1===g?0:a.arrayFormulaMode?a.columnOffset:a.columnOffset-e.getColumn(0)):(c=1===f?0:a.row-e.getRow(0),d=1===g?0:a.column-e.getColumn(0)),b=c>=f||d>=g?u:e.getValue(0,c,d))):p(b)&&(h=b,!a.arrayFormulaMode||a.rowOffset===-1&&a.columnOffset===-1?b=h.getValue(0,0):(c=1===h.getRowCount()?0:a.rowOffset,d=1===h.getColumnCount()?0:a.columnOffset,b=c>=h.getRowCount()||d>=h.getColumnCount()?u:h.getValue(c,d))),q(b)||p(b)?this.getOneValue(a,b):b},a.prototype.Gi=function(a,b,c,e,h){var i,j,k,l,m,n,o,r,s,t,v,w,x,y,z,A,B=1,C=1,D=-1,E=!0,F=!0,G=null,H=null,I=1,J=1,L=7===b.type?b:f,M=L&&L.function,N=a.arrayFormulaMode;if(M&&!M.expandRows()&&(E=!1),M&&!M.expandColumns()&&(F=!1),M&&M.isBranch()&&(N||a.vi())){if(D=M.findTestArgument(),i=[],j=!1,e)for(k=0;k<e.length;k++)i.push(e[k]),e[k]===D&&(j=!0);if(j||p(c[D])||q(c[D])){for(l=0;l<L.arguments.length;l++)l!==D&&d.Common.k.Cb(i,l)<0&&(p(c[l])||q(c[l]))&&i.push(l);e=i}}for(m=[],n=0,o=e[n],r=c.length,v=[],s=0;s<r;s++){if(t=c[s],G=f,H=f,I=J=1,s===o){if(G=p(t)?t:f,H=q(t)?t:f,H&&H.getRangeCount()>1)return;(G||H)&&(I=G&&G.getRowCount()||(E?H.getRowCount(0):1),J=G&&G.getColumnCount()||(F?H.getColumnCount(0):1)),n++,o=n<e.length?e[n]:-1}else h===g&&(G=p(t)?t:f,H=q(t)?t:f,(G||H)&&(I=G&&G.getRowCount()||(E?H.getRowCount(0):1),J=G&&G.getColumnCount()||(F?H.getColumnCount(0):1)));v[s]=[G,H,t,I,J],B=I>B?I:B,C=J>C?J:C}for(s=0;s<r;s++){if(w=m[s]=[],x=v[s],G=x[0],H=x[1],t=x[2],I=x[3],J=x[4],(G||H)&&(1!==I&&I!==B||1!==J&&J!==C)&&(D===-1||s===D)&&!N)return;for(y=0;y<B;y++)for(z=w[y]=[],A=0;A<C;A++)1!==I&&y>=I||1!==J&&A>=J?z[A]=u:G?z[A]=G.getValue(1===I?0:y,1===J?0:A):H?!h||h[s]?z[A]=H.getValue(0,1===I?0:y,1===J?0:A):z[A]=H.create([K(H.getRow(0)+(1===I?0:y),H.getColumn(0)+(1===J?0:A),1,1)]):z[A]=t}return m},a}(),b.Evaluator=A,B=function(){function a(a,b){this.id=a,this.Fi=g,this.v5=!1,this.oi=[],b!==g&&(this.evaluateMode=b)}return a.prototype.canEvaluate=function(){var a,b=this.oi;for(a=0;a<b.length;a++)if(!b[a].v5)return!1;return!0},a}(),b.AsyncFunctionNode=B,C=function(){function a(a){var b=this;b.w5=[],b.x5=-1,b.y5={},b.z5=-1,b.lh=a,b.vQa=0}return a.prototype.startCalc=function(){var a,b,c,d,e,f,g,h,i;if(this.vQa)for(b in this.y5)if(c=this.y5[b])for(d in c)if(e=c[d])for(f in e)if(g=e[f],g&&(a=g.length,a>0))for(h=0;h<a;h++)i=g[h],0===i.evaluateMode&&(i.v5=!1)},a.prototype.startCalcCell=function(a,b){var c,d,e,f,g=this,h=a.getName(),i=b.row,j=b.col;return g.z5=-1,g.source=a,c=g.y5[h],c||(c=g.y5[h]={}),d=c[i],d||(d=c[i]={}),(e=d[j])?void(g.cellNodes=e):(f=new B((-1)),e=d[j]=[],g.vQa++,g.x5=-1,g.cellNodes=e,g.row=i,g.col=j,e.root=f,g.w5[++g.x5]=f,void(g.evaluateMode=-1))},a.prototype.startCalcFunction=function(a,b,c){var d,e,f,g,h,i,j,k,l,m,n=this;if(n.z5++,n.isNewNode=!1,d=n.cellNodes[n.z5])return n.currentNode=d,d;if(n.isNewNode=!0,e=n.evaluateMode,f=a.evaluateMode(),"REFRESH"===a.name?(f=b[1]?n.lh.evaluate(b[1],c,!1,!1):0,n.evaluateMode=f):e!==-1&&(f=e),d=new B(n.z5,f),n.cellNodes[n.z5]=d,n.w5[n.x5].oi.push(d),d.parent=n.w5[n.x5],n.w5.push(d),n.x5++,n.currentNode=d,2===f&&e===-1){for(g=6e4,h=n.intervals,i=a.interval()||g,"REFRESH"===a.name&&(d.isRefresh=!0,i=b[2]?n.lh.evaluate(b[2],c,!1,!1):g),h||(n.intervals=h={}),j=h[i],j||(j=h[i]=[],j.intervalObj=window.setInterval(function(){var a,b,c=n.intervals[i];for(k=0;k<c.length;k++)a=c[k],b=a.node,b.v5=!1,b.isRefresh&&M(b),n.lh.service.recalculate(a.source,a.row,a.col,!0),n.source.refresh()},i)),l=!1,k=0;k<j.length;k++)if(m=j[k],m.source===n.source&&m.node===d&&m.row===n.row&&m.col===n.col){l=!0;break}l||j.push({source:n.source,node:d,row:n.row,col:n.col})}return d},a.prototype.getNode=function(){return this.currentNode},a.prototype.endCalcFunction=function(){this.isNewNode&&(this.w5.pop(),this.x5--),this.currentNode=this.currentNode.parent},a.prototype.endCalcCell=function(){var a,b=[];for(a=0;a<arguments.length;a++)b[a]=arguments[a]},a.prototype.clearCell=function(a,b){var c,d,e,f,h,i,j,k,l,m,n,o,p,q,r=this;if(r.vQa){c=a.getName(),d=b.row,e=b.col,r.z5=-1,r.source=a,f=r.y5[c],f&&(h=f[d],h&&(i=h[e],i&&(j=i[0],k=j&&j.h_a,k&&(delete k.nia,delete k.z$a),h[e]=g,r.vQa--))),l=r.intervals;for(m in l)if(n=l[m]){for(o=[],p=0;p<n.length;p++)q=n[p],q.source===a&&q.row===b.row&&q.col===b.col||o.push(q);o.length?o.length!==n.length&&(o.intervalObj=n.intervalObj,l[m]=o):(l[m]=g,window.clearInterval(n.intervalObj))}}},a.prototype.setAsyncResult=function(a,b){a.Fi=b},a.prototype.addRows=function(a,b,c){this.GQa(a,b,c,!0)},a.prototype.deleteRows=function(a,b,c){this.GQa(a,b,c,!1)},a.prototype.GQa=function(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,q,r=this;if(r.vQa){if(e=a.getName(),f=r.y5[e]){g={};for(h in f)i=f[h],i&&(j=parseInt(h,10),d?(k=j>=b?j+c:j,g[k]=i):j<b?g[j]=i:j>=b+c&&(g[j-c]=i));r.y5[e]=g}l=r.intervals;for(m in l)if(n=l[m]){for(o=[],p=0;p<n.length;p++)q=n[p],q.source===a&&d&&q.row>=b?q.row+=c:q.source===a&&!d&&(q.row<b||q.row>=b+c)&&(q.row=q.row<b?q.row:q.row-c,o.push(q));d||o.length===n.length||(o.intervalObj=n.intervalObj,l[m]=o)}}},a.prototype.addColumns=function(a,b,c){this.HQa(a,b,c,!0)},a.prototype.deleteColumns=function(a,b,c){this.HQa(a,b,c,!1)},a.prototype.HQa=function(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t=this;if(t.vQa){if(e=a.getName(),f=t.y5[e])for(g in f)if(h=f[g]){i={};for(j in h)k=h[j],k&&k.length&&(l=parseInt(j,10),d?(m=l>=b?l+c:l,i[m]=k):l<b?i[l]=k:l>=b+c&&(i[l-c]=k));f[g]=i}n=t.intervals;for(o in n)if(p=n[o]){for(q=[],r=0;r<p.length;r++)s=p[r],s.source===a&&d&&s.col>=b?s.col+=c:s.source===a&&!d&&(s.col<b||s.col>=b+c)&&(s.col=s.col<b?s.col:s.col-c,q.push(s));d||q.length===p.length||(q.intervalObj=p.intervalObj,n[o]=q)}}},a.prototype.changeSourceName=function(a,b){var c,d=this;d.vQa&&(c=d.y5[a],c&&(d.y5[b]=c,delete d.y5[a]))},a}();function M(a){var b,c;for(c=0;c<a.oi.length;c++)b=a.oi[c],b.v5=!1,b.oi.length>0&&M(b)}D=function(a){var b,c=!0;return a&&a.getValue&&(b=o(a,0,!1,!0),b.isError?a=b[0]:1===b.rowCount&&1===b.colCount?a=b[0][0]:(a=b,c=!1)),{value:a,success:c}};function N(a,b,c){return n(b)?b:n(c)?c:a<=2?O(a,b,c):a<=14?P(a,b,c):T(a,b,c)}b.evaluateOperator=N;function O(a,b,c){var d,f,g,h,i,j,k,l;if(!b)return 0;if(d=D(b),d.success)return Q(a,d.value);if(c){for(f=[],g=d.value,h=g.rowCount,i=g.colCount,j=0;j<h;j++)for(k=f[j]=[],l=0;l<i;l++)k[l]=Q(a,g[j][l]);return new e.CalcArray(f)}}function P(a,b,c){var d,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;if(d=a<=7?Q:8===a?R:S,f=D(b),g=D(c),f.success&&g.success)return d(a,f.value,g.value);if(b=f.value,c=g.value,h=f.success?-1:b.rowCount,i=f.success?-1:b.colCount,j=g.success?-1:c.rowCount,k=g.success?-1:c.colCount,l=-1,m=-1,f.success||g.success)f.success?(l=j,m=k):(l=h,m=i);else{if(1!==h&&1!==j&&h!==j||1!==i&&1!==k&&i!==k)return u;l=1===h?j:h,m=1===i?k:i}for(n=[],o=0;o<l;o++)for(n[o]=[],p=0;p<m;p++)!f.success&&(1!==h&&h<l||1!==i&&i<m)||!g.success&&(1!==j&&j<l||1!==k&&k<m)?n[o][p]=u:(q=1===h?0:o,r=1===i?0:p,s=1===j?0:o,t=1===k?0:p,n[o][p]=d(a,f.success?b:b[q][r],g.success?c:c[s][t]));return new e.CalcArray(n)}function Q(a,b,c){var d,e,f,i;function j(a){return"string"==typeof a&&0===a.trim().length}if(j(b)||j(c))return s;if(d={value:0},!m(b,d))return 0===a&&"string"==typeof b?b:s;if(e=d.value,c!==g){if(!m(c,d))return s;f=d.value}else f=0;return 0===a?e:1===a?-e:2===a?e/100:3===a?e+f:4===a?e-f:5===a?e*f:6===a?f?e/f:v:7===a?(i=h(e,f),!e&&f<0?v:i===Number.POSITIVE_INFINITY||i===Number.NEGATIVE_INFINITY?w:i):void 0}function R(a,b,c){if(8===a)return n(b)?b:n(c)?c:e.Convert.bc(b)+e.Convert.bc(c)}function S(a,b,c){var d,h,i,j,o,p,q,r,t,u;if(n(b))return b;if(n(c))return c;if(h=typeof b===l,i=typeof c===l,h||i)d=h&&i?b===c?0:b>c?1:-1:h?1:-1;else{if(j=typeof b===k,o=typeof c===k,p=j||o,q=p?"":0,b=b===f||b===g?q:b,c=c===f||c===g?q:c,r=function(){return b===c?0:j&&o?b.toUpperCase().localeCompare(c.toUpperCase()):j?1:o?-1:e.Zh.Yh(b,c)?0:b-c},!p){if(t={value:0},u={value:0},!m(b,t)||!m(c,u))return s;b=t.value,c=u.value}d=r()}return 9===a&&0===d||10===a&&0!==d||11===a&&d<0||12===a&&d<=0||13===a&&d>0||14===a&&d>=0}function T(a,b,c){var d,f,g,h,k,l,m,n,o,p,q,r;if(!b||!c||(1!==b.getRangeCount()||1!==c.getRangeCount())&&16!==a||b.endSource||c.endSource)return s;if(d=b.getSource(),!d||d!==c.getSource())return s;if(16===a)f=b.yf.concat(c.yf);else{if(g=15===a?i:j,h=15===a?j:i,k=b.getRow(0),l=b.getColumn(0),m=c.getRow(0),n=c.getColumn(0),o=g(k,m),p=g(l,n),q=h(k+b.getRowCount(0),m+c.getRowCount(0))-o,r=h(l+b.getColumnCount(0),n+c.getColumnCount(0))-p,q<1||r<1)return e.Errors.Null;f=[K(o,p,q,r)]}return new e.CalcReference(d,f)}E=e.Convert.Pa,F=e.Convert.Rh,G=e.Convert.bc,H=e.Convert.Xh,I=e.Convert.Vh;function U(a,b){var c,d,g,h=!0;switch(b.Hi){case 0:c=E(a),d=!0;break;case 1:c=parseFloat(a),d=!0;break;case 2:c=F(a),d=!0;break;case 3:c=parseInt(a,10),d=!0;break;case 4:c=o(a,b.Ii,b.Ji||!1,b.Ki||!1,b.Li||!1,b.Mi,b.xTa),c.isError&&(c=c[0],h=!1),c.isConvertError&&(c=s,h=!1);break;case 5:c=G(a),b.Ni&&""===c&&(c=s,h=!1);break;case 6:g={value:f},H(a,g)?c=g.value:(c=s,h=!1);break;case 7:c=I(a);break;case 8:c=e.Functions.Wi(a),!c||b.Oi&&0===c.Pi()&&0===c.Qi()?(c=e.Errors.Number,h=!1):c={Ri:c.Pi(),Si:c.Qi()};break;default:c=a}return d&&isNaN(c)&&(c=s,h=!1),{Ti:c,Ei:h}}function V(a,b,c){var d,f,h,i,j;for(Array.isArray(b)||(b=[b]),d=a.Ti,f=0;f<b.length;f++)if(h=b[f].split(" "),i=h[0],j=parseInt(h[1],10),"<"===i&&d<j||">"===i&&d>j||"<="===i&&d<=j||">="===i&&d>=j||"="===i&&d===j||"!="===i&&d!==j||"checkLength"===i&&d.length>j)return a.Ti=c===g?e.Errors.Number:c,void(a.Ei=!1)}function W(a,b){var c,d,f,h,i,j=[];if(!b)return{Di:a,Ei:!0};if(b.Xi)for(f=0;f<a.length;f++)j.push(b);else j=Array.isArray(b)?b:[b];for(d=0;d<j.length;d++){if(h=j[d],h.Yi&&!a[d])throw e.sR().Exp_ArgumentNull;if(i=a[d]!==g){if(c=U(a[d],h),h.aj&&V(c,h.aj,h.bj),!c.Ei)break;a[d]=h.Zi?{$i:i,Qb:c.Ti}:c.Ti}else a[d]=h.Zi?{$i:i,Qb:h._i}:h._i}return c?{Di:a,Ei:c.Ei,Fi:c.Ti}:void 0}},Common:function(a,b){a.exports=c("@grapecity/js-sheets-common")}}),a.exports=d.Spread.CalcEngine},"./node_modules_local/@grapecity/js-calc/index.js":function(a,b,c){a.exports=c("./node_modules_local/@grapecity/js-calc/dist/gc.spread.calcEngine.js")},"@grapecity/js-sheets-common":function(a,b){a.exports=GC.Spread}});