/*!
 * 
 * SpreadJS Library 12.2.5
 * 
 * Copyright(c) GrapeCity, Inc.  All rights reserved.
 * 
 * Licensed under the SpreadJS Commercial License.
 * <EMAIL>
 * http://www.grapecity.com/en/licensing/grapecity/
 * 
 * 
 */
var GC="object"==typeof GC?GC:{};GC.Spread=GC.Spread||{},GC.Spread.Sheets=GC.Spread.Sheets||{},GC.Spread.Sheets.Slicers=function(a){var b={};function c(d){if(b[d])return b[d].exports;var e=b[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,c),e.l=!0,e.exports}return c.m=a,c.c=b,c.d=function(a,b,d){c.o(a,b)||Object.defineProperty(a,b,{enumerable:!0,get:d})},c.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},c.t=function(a,b){var d,e;if(1&b&&(a=c(a)),8&b)return a;if(4&b&&"object"==typeof a&&a&&a.__esModule)return a;if(d=Object.create(null),c.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:a}),2&b&&"string"!=typeof a)for(e in a)c.d(d,e,function(b){return a[b]}.bind(null,e));return d},c.n=function(a){var b=a&&a.__esModule?function b(){return a.default}:function b(){return a};return c.d(b,"a",b),b},c.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},c.p="/assets/",c(c.s="./dist/plugins/slicer/slicer.entry.js")}({"./dist/plugins/slicer/slicer-actions.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=d.Commands.ActionBase;function k(a){if(a.canUndo()){var b=a.kj,c=d.Commands.bWa(b.name()),e=a.VQ[c];return a.Lz(b,!0),b.ITa.undo(e),a.Mz(b,!0),!0}return!1}f=function(a){j(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!!this.VQ.slicerData},b.prototype.execute=function(){var a,b,c=this,e=c.VQ;return!!c.canExecute()&&(a=c.kj,a.ITa.startTransaction(),c.Lz(a,!0),e.slicerData.doFilter(e.columnName,{exclusiveRowIndexes:e.newValue}),c.Mz(a,!0),b=d.Commands.bWa(a.name()),c.VQ[b]=a.ITa.endTransaction(),!0)},b.prototype.canUndo=function(){return!!this.VQ.slicerData},b.prototype.undo=function(){return k(this)},b}(e),g=function(a){j(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!!this.VQ.slicerData},b.prototype.execute=function(){var a,b,c=this,e=c.VQ;return!!c.canExecute()&&(a=c.kj,a.ITa.startTransaction(),c.Lz(a,!0),e.slicerData.doUnfilter(e.columnName),c.Mz(a,!0),b=d.Commands.bWa(a.name()),c.VQ[b]=a.ITa.endTransaction(),!0)},b.prototype.canUndo=function(){return!!this.VQ.slicerData},b.prototype.undo=function(){return k(this)},b}(e),h=function(a){j(b,a);function b(b,c){return a.call(this,b,c)||this}return b.prototype.canExecute=function(){return!!this.VQ.slicer},b.prototype.execute=function(){var a,b=this,c=b.VQ;return!!b.canExecute()&&(a=b.kj,b.Lz(a,!0),c.slicer[c.propertyName](c.newValue),b.Mz(a,!0),!0)},b.prototype.canUndo=function(){return!!this.VQ.slicer},b.prototype.undo=function(){var a,b=this,c=b.VQ;return!!b.canUndo()&&(a=b.kj,b.Lz(a,!0),c.slicer[c.propertyName](c.oldValue),b.Mz(a,!0),!0)},b}(e),i=d.Commands.h4,d.Commands.filterSlicer={canUndo:!0,execute:function(a,b,c){return i(a,f,b,c)}},d.Commands.unfilterSlicer={canUndo:!0,execute:function(a,b,c){return i(a,g,b,c)}},d.Commands.changeSlicerProperty={canUndo:!0,execute:function(a,b,c){return i(a,h,b,c)}},d.Commands.unfilterSlicerByKey={canUndo:!1,execute:function(a,b){var c,e,f,g=d.Commands.bT(a,b),h=g.slicers.UX();if(1===h.length&&(c=h[0],e=c.YX()))return f=e.getFilteredIndexes(c.columnName()),g.wu().execute({cmd:"unfilterSlicer",sheetName:g.name(),slicerData:e,columnName:c.columnName(),value:f})}}},"./dist/plugins/slicer/slicer.entry.js":function(a,b,c){"use strict";function d(a){for(var c in a)b.hasOwnProperty(c)||(b[c]=a[c])}Object.defineProperty(b,"__esModule",{value:!0}),d(c("./dist/plugins/slicer/slicer.ns.js")),c("./dist/plugins/slicer/slicer-actions.js"),d(c("./dist/plugins/slicer/tableSlicer.js"))},"./dist/plugins/slicer/slicer.ns.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d=c("./dist/plugins/slicer/slicer.res.en.js");b.SR={en:d}},"./dist/plugins/slicer/slicer.res.en.js":function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.Blank="(blank)",b.Exp_SlicerNameInvalid="The slicer name is not valid.",b.Exp_SlicerNameExist="The slicer name is already in use; please enter a unique name."},"./dist/plugins/slicer/tableSlicer.js":function(a,b,c){"use strict";var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ba,ca,da,ea,fa,ga,ha,ia,ja,ka,la,ma,na,oa,pa,qa,ra,sa,ta,ua,va,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,La,Ma,Na,Oa,Pa,Qa,Ra,Sa,Ta,Ua,Va,Wa,Xa,Ya,Za,$a,_a,ab,bb,cb,db,eb,fb,gb,hb,ib,jb,kb,lb,mb,nb,ob,pb,qb,rb,sb,tb,ub,vb,wb,xb,yb,zb,Ab,Bb,Cb,Db,Eb,Fb,Gb,Hb,Ib,Jb,Kb,Lb,Mb,Nb,Ob,Pb,Qb,Rb,Sb,Tb,Ub,Vb,Wb,Xb,Yb,Zb,$b,_b,ac,bc,cc,dc,ec,fc,gc,hc,ic,jc,kc,lc=this&&this.__extends||function(){var a=function(b,c){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])})(b,c)};return function(b,c){a(b,c);function d(){this.constructor=b}b.prototype=null===c?Object.create(c):(d.prototype=c.prototype,new d)}}();for(Object.defineProperty(b,"__esModule",{value:!0}),d=c("Core"),e=c("Common"),f=c("FloatingObject"),g=c("Tables"),h=c("ConditionalFormatting"),i=c("./dist/plugins/slicer/slicer.ns.js"),j=new e.Common.ResourceManager(i.SR),k=j.getResource.bind(j),l=e.Common.D,m=e.Common.k,n=d.Ul.Nl,o=d.Ul.Pl,p=d.Ul.Ol,q=m.ac,r=d.Ul.hZa,s=d.GC$.isArray,t=d.GC$.extend,u=document,v=void 0,w=null,x="normal 11pt calibri",y=parseFloat,z=e.Slicers.GeneralSlicerData,function(a){a[a.CONTAINER=0]="CONTAINER",a[a.H_SPACE=1]="H_SPACE",a[a.H_CAPTION=2]="H_CAPTION",a[a.H_SPLITLINE=3]="H_SPLITLINE",a[a.H_CLEARFILTER=4]="H_CLEARFILTER",a[a.B_ITEM=5]="B_ITEM",a[a.B_ITEMSPACER=6]="B_ITEMSPACER",a[a.B_TAIL=7]="B_TAIL",a[a.B_SCROLLBAR=8]="B_SCROLLBAR"}(A||(A={})),function(a){a[a.HOVERED=1]="HOVERED",a[a.SELECTED=2]="SELECTED",a[a.UNSELECTED=4]="UNSELECTED",a[a.HASDATA=8]="HASDATA",a[a.NODATA=16]="NODATA"}(B||(B={})),function(a){a[a.NORMAL=0]="NORMAL",a[a.ACTIVE=1]="ACTIVE",a[a.HOVERED=2]="HOVERED"}(C||(C={})),D="name",E="style",F="Style",G="light",H="dark",I="white",J="black",K="solid",L="color",M="Color",N="background-color",O="width",P="Width",Q="height",R="font",S="setBorders",T="back"+M,U="fore"+M,V="border",W=V+"Width",X=V+F,Y=V+M,Z=V+"Left",$=V+"Top",_=V+"Right",aa=V+"Bottom",ba="textDecoration",ca="WithData"+F,da="WithNoData"+F,ea="electedItem",fa="hovered",ga="wholeSlicer"+F,ha="header"+F,ia="s"+ea+ca,ja="s"+ea+da,ka="unS"+ea+ca,la="unS"+ea+da,ma=fa+"S"+ea+ca,na=fa+"S"+ea+da,oa=fa+"UnS"+ea+ca,pa=fa+"UnS"+ea+da,qa="undoFilter",ra="undoAdd",sa="undoRemove",ta="undoUpdateTableSlicer",ua="div",va="px",wa="default",xa="cursor",ya="position",za="absolute",Aa="padding",Ba="top",Ca="margin-"+Ba,Da="left",Ea="right",Fa="text-align",Ga="overflow",Ha="hidden",Ia="auto",Ja="text-overflow",Ka="ellipsis",La="white-space",Ma="nowrap",Na="unselectable",Oa="on",Pa="font-weight",Qa="font-size",Ra="border-radius",Sa="box-sizing",Ta="content-box",Ua="button",Va="\u2717",Wa="mark",Xa="itemValue",Ya="Accent ",Za="Slicer"+F,$a="#999999",_a="#828282",ab="#CCCCCC",bb="gc-slicer-",cb="container",db=bb+cb,eb="header",fb=bb+eb,gb=bb+"header-borderDiv",hb=bb+"caption",ib="clearfilter",jb=bb+ib,kb="body",lb=bb+kb,mb=bb+"table",nb=bb+"tr",ob=bb+"td1",pb=bb+"td2",qb="itemscontainer",rb=bb+qb,sb="item",tb=bb+sb,ub="gc-no-user-select",vb=function(){function a(b,c,d,e,f,g,h,i){for(var j=0,k=q(a.properties);j<k;j++)this[a.properties[j]](arguments[j])}return a.prototype.setBorders=function(a){var b=this;b[Z](a)[$](a)[_](a)[aa](a)},a.prototype.fromJSON=function(b){if(b&&!d.GC$.isEmptyObject(b)){var c=this;a.properties.forEach(function(a){var d,e=b[a];p(e)&&(a.indexOf(V)>=0?(d=nc(),d.fromJSON(e),c[a](d,!1)):c[a](e,!1))})}},a.prototype.toJSON=function(){var b=this,c={};return a.properties.forEach(function(a){var d=b[a]();b[a].isDefault(d)||(c[a]=d&&d.toJSON?d.toJSON():d)}),c},a.properties=[T,U,R,Z,$,_,aa,ba],a}(),b.SlicerStyleInfo=vb,wb=0,xb=q(vb.properties);wb<xb;wb++)vb.prototype[vb.properties[wb]]=o(vb.properties[wb]);function mc(a){var b,c;return a&&0!==q(a)?0===a.indexOf(G)?(c=parseInt(a.replace(G,""),10),b=Eb[G+c]()):0===a.indexOf(H)?(c=parseInt(a.replace(H,""),10),b=Eb[H+c]()):Eb[a]&&(b=Eb[a]()):b=new vb,b}for(yb=function(){function a(b,c,d){for(var e=0;e<a.properties.length;e++)this[a.properties[e]](arguments[e])}return a.prototype.fromJSON=function(b){var c,e;if(b&&!d.GC$.isEmptyObject(b))for(c=0;c<a.properties.length;c++)e=a.properties[c],p(b[e])&&this[e](b[e],!1)},a.prototype.toJSON=function(){var b,c,d,e={};for(b=0;b<a.properties.length;b++)e[a.properties[b]]=this[a.properties[b]]();c={};for(d in e)e[d]!==v&&e[d]!==w&&(c[d]=e[d]);return c},a.properties=[W,X,Y],a}(),b.SlicerBorder=yb,zb=function(a,b,c){return o(a,b,c,function(b){var c=typeof b;return a===W?"number"===c&&b>=0:"string"===c})},wb=0;wb<yb.properties.length;wb++)yb.prototype[yb.properties[wb]]=zb(yb.properties[wb],v,v);function nc(a,b,c){return new yb(a,b,c)}function oc(){var a=new yb(0,"",""),b=new vb(I,J,x);return b.setBorders(a),b}function pc(a){var b=0,c=17,d=16;return a.indexOf(Za+"Light")>-1&&q(a)===c&&(b=parseInt(a[c-1],10)),a.indexOf(Za+"Dark")>-1&&q(a)===d&&(b=parseInt(a[d-1],10)),b>=1&&b<=6}for(Ab=function(){function a(){var a=this;a[D]("",!1),a[ga](oc(),!1)}return a.prototype.fromJSON=function(b){var c,e,f,g;if(b&&!d.GC$.isEmptyObject(b))return c=this,e=b.name,p(e)&&(c.name(e),pc(e))?(f=e.toLocaleLowerCase().replace("slicerstyle","").replace(" ",""),g=mc(f),void a.properties.forEach(function(a){c[a](g[a](),!1)})):void a.properties.forEach(function(a){var d,e=b[a];a!==D&&p(e)&&(d=mc(),d.fromJSON(e),c[a](d,!1))})},a.prototype.toJSON=function(){var a,b,c,e=this;a=pc(e.name())?{name:e.name()}:e.toJSONInternal(),b={};for(c in a)a[c]===w||a[c]===v||d.GC$.isEmptyObject(a[c])||(b[c]=a[c]);return b},a.prototype.toJSONInternal=function(){var b,c,d,e=this,f={},g="toJSON";for(f[D]=e.name(),b=1,c=q(a.properties);b<c;b++)d=a.properties[b],f[d]=e[d]()?e[d]()[g]():w;return f},a.properties=[D,ga,ha,ia,ja,ka,la,ma,na,oa,pa],a}(),b.SlicerStyle=Ab,Bb=function(a,b,c,d){return o(a,b,c,d)},wb=1,Cb=q(Ab.properties);wb<Cb;wb++)Ab.prototype[Ab.properties[wb]]=Bb(Ab.properties[wb]);Ab.prototype.name=Bb(D,"");function qc(){return new Ab}Db=function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v){var w,x,y=qc(),z=Za+a+b;return y[D](z),w=mc(),w.font(c),w.backColor(d),w.setBorders(e),y.wholeSlicerStyle(w),x=mc(),x.borderLeft(f),x.borderTop(f),x.borderRight(f),x.borderBottom(g),y.headerStyle(x),y[ia](sc(h,i,j)),y[ja](sc(k,l,m)),y[ka](sc(n,o,p)),y[la](sc(q,r,s)),rc(y,sc(t,u,v)),y};function rc(a,b){a[ma](b)[na](b)[oa](b)[pa](b)}function sc(a,b,c){var d=mc();return d[U](a)[T](b)[S](c),d}function tc(a){var b=Ya+a;return Db("Light",a,x,I,nc(1,K,b),nc(0,"",""),nc(1,K,b),J,b+",Lighter 60%",nc(1,K,$a),_a,b+",Lighter 80%",nc(1,K,ab),J,I,nc(1,K,ab),_a,I,nc(1,K,"#E0E0E0"),J,"#F9E36F",nc(1,K,$a))}function uc(a){var b=Ya+a,c=",Lighter 60%";return Db("Dark",a,x,I,nc(1,K,b),nc(0,"",""),nc(1,K,b),I,b,nc(1,K,b),b+",Darker 25%",b+c,nc(1,K,b+c),J,"#C0C0C0",nc(1,K,"#C0C0C0"),"#959595","#DFDFDF",nc(1,K,"#DFDFDF"),J,"#F9E36F",nc(1,K,$a))}for(Eb=function(){function a(){}return a}(),b.SlicerStyles=Eb,Fb=1;Fb<=6;Fb++)Eb["light"+Fb]=function(a){return function(){return tc(a)}}(Fb),Eb["dark"+Fb]=function(a){return function(){return uc(a)}}(Fb);Eb.other1=function(){return Db("Other",1,x,I,nc(1,K,"#808080"),nc(0,"",""),nc(1,K,"#A6A6A6"),J,"#BFBFBF",nc(1,K,$a),J,"#D9D9D9",nc(1,K,ab),J,I,nc(1,K,ab),"#959595",I,nc(1,K,"#E0E0E0"),J,"#F9E36F",nc(1,K,$a))},Eb.other2=function(){return Db("Other",2,x,I,nc(1,K,"#4F81BD"),nc(0,"",""),nc(1,K,"#4F81BD"),J,"#A9C1E3",nc(1,K,$a),_a,"#D5E2F6",nc(1,K,ab),J,I,nc(1,K,ab),_a,I,nc(1,K,"#E0E0E0"),J,"#F9E36F",nc(1,K,$a))};function vc(a,b){var c,d,e,f,g,h,i=a.KW,j=a._caption,k=zc(a.slicerData),m=a.zoomFactor();if(b){b[ga]&&(Bc(a.xo,b[ga],k,j,m),$c(a,cb)),c=a.LW,d=xc(b[ga],b[ha]),Cc(b)?(e=d[aa],f=e&&e[W]||0,c[E][Q]=(a.MW-f)*Dc(b)*m+va,Ac(c,e,k,aa),d[Z]=d[$]=d[_]=d[aa]=w):(c[E][aa+O]=0+va,c[E][Q]=i[E][Q]),Bc(i,d,k,j,m),$c(a,eb);for(g in a.NW)l(a.NW,g)&&(h=parseInt(g,10),Xc(a,h))}}function wc(a,b,c){var e,f,g={};if(b&&c&&q(b)===q(c)){for(e=0,f=q(b);e<f;e++)g[b[e]]=c[e];d.GC$(a).css(g)}}function xc(a,b){var c={},d;if(b)for(d in b)l(b,d)&&(c[d]=b[d]);if(a)for(d in a)c[d]===v&&a[d]!==v&&(c[d]=a[d]);return c}function yc(a,b){if(!b)return a;var c;return a&&b.getColor&&(c=b.getColor(a)),c}function zc(a){var b=a&&a.OW&&a.OW();return b&&b.currentTheme()}function Ac(a,b,c,d){if(a){var e=b&&b[W]||0,f=b&&b[X]||"",g=b&&b[Y]||"";a[E][d+P]=e+va,a[E][d+F]=f,a[E][d+M]=yc(g,c)}}function Bc(a,b,c,f,g){var h,i,j,k;a&&b&&(h=b[U]||J,i=b[T]||I,j=b[R]||x,a[E][N]=yc(i,c),a[E][L]=yc(h,c),a[E][R]=r(d.To.No(j,g)[R]),Ac(a,b[Z],c,Z),Ac(a,b[$],c,$),Ac(a,b[_],c,_),Ac(a,b[aa],c,aa),k=d.To.So(b[ba]),e.Common.u.Bb(a.className,fb)?f[E][ba]=k:e.Common.u.Bb(a.className,tb)&&(a[E][ba]=k))}function Cc(a){var b,c=a[ha];return!c||!(c[Z]||c[$]||c[_]||c[_])||(b=xc(a[ga],c),!(!(b[aa]&&b[aa][W]>0)||b[Z]&&0!==b[Z][W]||b[$]&&0!==b[$][W]||b[_]&&0!==b[_][W]))}function Dc(a){var b,c,e,f,g,h,i,j,k;return a?(b=xc(a[ga],a[ha]),b&&b[R]?(c=b[R],e=1,f=d.To.Ko,g=d.To.Jo,h=f(g(x)).fontSize,i=f(g(c)).fontSize,h&&i&&(j=y(h),k=y(i),isNaN(k)||(e=k/j)),e):1):1}function Ec(a){var b,c,d,e,f,g=0,h=0;return a&&(b=a[E],c=b[Z+P],d=b[$+P],e=b[_+P],f=b[aa+P],c&&(g+=y(c)),d&&(h+=y(d)),e&&(g+=y(e)),f&&(h+=y(f))),{widthOffset:g,heightOffset:h}}function Fc(a){var b,c,d=[];if(a)for(c=0;c<q(a);c++)b=a[c]===v||a[c]===w||""===a[c]?k().Blank:a[c]+"",d.push(b);return d}function Gc(a){var b=n(ua);return wc(b,[ya,Aa,Sa,Ga],[za,6*a.zoomFactor(),Ta,Ha]),d.GC$(b).addClass(db),$c(a,cb),d.GC$(b).append(Hc(a)).append(Ic(a)),b}function Hc(a){var b,c,e,f,g,h,i,j,k=n(ua),l=n(ua),m=n("span"),o=n(ua),p=a.zoomFactor(),q=a.style();return wc(k,[O,ya,Ba,Da,Ea,Sa,Ga],[Ia,za,0,0,0,Ta,Ha]),d.GC$(k).addClass(fb),a.KW=k,b=a.PW*p,wc(l,[O,ya,Da,Ea,Sa,Ga],[Ia,za,b,b,Ta,Ha]),d.GC$(l).addClass(gb),a.LW=l,d.GC$(k).append(l),c=a.QW*Dc(q)*p,e=Lc(a)*p,wc(m,[Sa,ya,Ba,Ga,O,Ja,La],[Ta,za,c,Ha,e,Ka,Ma]),d.GC$(m).text(a.captionName()).attr(Na,Oa).addClass(hb+" "+ub),a._caption=m,f=a.RW*p,g=a.SW*p,h=a.TW*Dc(q)*p,i=a.UW*p,j=a.VW*p+va,wc(o,[O,Q,xa,ya,Ba,Ea,Fa,Sa,Pa,Ra,Ga,Qa],[f,g,wa,za,h,i,"center",Ta,"normal",2,Ha,j]),d.GC$(o).attr("title","Clear Filter (Alt+C)").attr(Na,Oa).text(Va).addClass(jb+" "+ub),a.WW=o,$c(a,eb),d.GC$(l).append(m).append(o),k}function Ic(a){var b,c=n(ua),e=n("table"),f=n("tr"),g=n("td"),h=n("td"),i=(a.MW-a.PW+a.XW)*Dc(a.style())*a.zoomFactor();return wc(c,[O,Ga,Ca,Sa],[Ia,Ha,i,Ta]),d.GC$(c).attr(Na,Oa).addClass(lb+" "+ub),a.YW=c,wc(e,[Sa,"border-spacing"],[Ta,0+va]),d.GC$(e).attr(Na,Oa).addClass(mb+" "+ub),a.ZW=e,d.GC$(f).css(Sa,Ta).addClass(nb),a.$W=f,wc(g,[Aa,Sa],[0,Ta]),d.GC$(g).addClass(ob),a._W=g,wc(h,[Aa,Sa,xa],[0,Ta,wa]),d.GC$(h).addClass(pb),a.aX=h,d.GC$(g).append(Jc(a)),b=Rc(a),d.GC$(h).append(b),a.bX=b,d.GC$(f).append(g).append(h),d.GC$(e).append(f),d.GC$(c).append(e),c}function Jc(a){var b,c,e,f,g,h,i,j=n(ua),k=a.zoomFactor();for(a.cX=j,c=Mc(a),a.dX=c,b=Nc(a),a.eX=b,wc(j,[O,Q,Qa],[b*k,c*k,a.VW*k+va]),d.GC$(j).attr(Na,Oa).addClass(rb+" "+ub),e=Qc(a),a.fX=e,f=Math.min(Pc(a),q(e)),g=0;g<f;g++)h=e[g],i=Kc(a,h,g),d.GC$(j).append(i),a._items.push(i),a.NW[a.gX.indexOf(h)]=i;return j}function Kc(a,b,c){var e=b,f=n(Ua),g=a.zoomFactor(),h=a.hX*g+va;return wc(f,[Fa,La,Ca,xa,Sa,Ra,Aa,Ga,Ja,N],[Da,Ma,h,wa,Ta,5,"1px 6px",Ha,Ka,I]),d.GC$(f).attr("type",Ua).attr(Na,Oa).text(e).addClass(tb+" "+ub),(c+1)%a.columnCount()!==0&&d.GC$(f).css("margin-right",a.hX*g+va),$c(a,sb,f),f}function Lc(a){var b,c,d=a.xo,e=a.KW,f=1,g=1,h=0,i=0,j=function(a){return a?y(a):0},k=V+"Left"+P,l=V+"Right"+P;return d&&(b=d.style,f=j(b[k]),g=j(b[l])),e&&(c=e.style,h=j(c[k]),i=j(c[l])),a.width()-(f+g)-2*a.PW-(h+i)-a.RW}function Mc(a){var b=Ec(a.xo),c=a.height()-2*a.PW-b.heightOffset;return a.showHeader()?c-a.MW*Dc(a.style())-a.XW:c}function Nc(a){var b=Ec(a.xo),c=a.width()-2*a.PW-b.widthOffset-1;return a.iX()?c-a.jX:c}function Oc(a,b){var c=a.columnCount();return(b-(c-1)*a.hX)/c}function Pc(a){return Math.ceil(a.dX/(a.itemHeight()+2*(a.kX+a.lX)+a.hX))*a.columnCount()}function Qc(a){var b,c,d,e,f,g,h=a.gX,i=a.mX(h.slice(0),a.sortState()),j=a.slicerData.getFilteredOutIndexes(a.columnName,2);if(a.showNoDataItems()){if(a.visuallyNoDataItems()&&a.showNoDataItemsInLast()){for(b=[],d=q(i),e=void 0,f=void 0,e=0;e<d;e++)f=i[e],m.Bb(j,h.indexOf(f))||b.push(f);for(e=0;e<d;e++)f=i[e],m.Bb(j,h.indexOf(f))&&b.push(f);i=b}}else for(b=i.concat(),c=0;c<q(b);c++)m.Bb(j,h.indexOf(b[c]))&&m.Fb(i,b[c]);return g=k().Blank,m.Bb(i,g)&&(m.Fb(i,g),i.push(g)),i}function Rc(a){var b,c=new d.hv((!1));return a.nX=c,b=c.Bv(),d.GC$(b).bind("scroll.gcScrollbar",function(b,c){var d,e=c.scrollEventType,f=c.scrollOrientation;b.data=a,1===f&&(d=[0,1,2,3,5],d.indexOf(e)>=0&&a.Bw(b,c))}),b}function Sc(a){var b=a.slicerData;a.data=b.getData(a.columnName),a.exclusiveDatas=b.getExclusiveData(a.columnName),a.oX=Fc(a.data),a.gX=Fc(a.exclusiveDatas),a.fX=Qc(a),Zc(a),Wc(a)}function Tc(a,b){var c,e=a.style(),f=a.xo;f&&(b!==v&&b!==a.zoomFactor()&&(a.zoomFactor(b),vc(a,e)),d.GC$(f).css(Aa,6*b),$c(a,cb),Uc(a),c=a.showHeader()?(a.MW+a.XW-a.PW)*Dc(e)*b:0,d.GC$(a.YW).css(Ca,c),Vc(a),Zc(a))}function Uc(a){var b,c,e,f,g,h,i,j,k,l=a.zoomFactor(),m=a.KW,n=a.YW,o=a._caption,p=a.WW,r=a.style();a.showHeader()?(a.xo.firstChild!==m&&(d.GC$(m).insertBefore(n),d.GC$(n).css(Ca,(a.MW+a.XW)*Dc(r)*l)),d.GC$(m).css(Q,a.MW*Dc(r)*l),b=a.PW*l,wc(a.LW,[Da,Ea],[b,b]),o.innerHTML!==a.captionName()&&d.GC$(o).text(a.captionName()),c=a.QW*Dc(r)*l,e=Lc(a)*l,wc(o,[Ba,O,Pa],[c,e,"bold"]),f=a.pX,g=f&&q(f)>0?1:0,id(a,g),h=a.RW*l,i=h,j=a.TW*Dc(r)*l,k=a.UW*l,wc(p,[O,Q,Ba,Ea],[h,i,j,k]),$c(a,eb)):d.GC$(m).remove()}function Vc(a){var b,c=a.zoomFactor(),d=Mc(a);a.dX=d,b=Nc(a),a.eX=b,wc(a.cX,[O,Q,Qa],[b*c,d*c,a.VW*c]),Wc(a)}function Wc(a){var b,c,e,f,g,h,i,j,k=a.fX;if(k){for(b=a._items,b&&q(b)>0&&d.GC$(b).remove(),a._items=[],b=a._items,a.NW={},c=Math.min(Pc(a),q(k)),e=a.iX()?a.nX.value()*a.columnCount():0,f=Math.min(e+c,q(k)),g=e;g<f;g++)h=k[g],i=Kc(a,h,g),d.GC$(a.cX).append(i),b.push(i),a.NW[a.gX.indexOf(h)]=i;if(0!==q(b))for(j in a.NW)l(a.NW,j)&&Xc(a,parseInt(j,10))}}function Xc(a,b){var c=a.qX,d=c?a.gX.indexOf(c[Xa]):-1,e=a.pX,f=a.rX,g=a.NW[b],h=0;b===d&&(h|=1),h|=m.Bb(e,b)?4:2,h|=m.Bb(f,b)&&a.visuallyNoDataItems()?16:8,Yc(a,g,h)}function Yc(a,b,c){var d=a.style(),e;if(b&&d){switch(c){case 10:e=ia;break;case 18:e=ja;break;case 12:e=ka;break;case 20:e=la;break;case 11:e=ma;break;case 19:e=na;break;case 13:e=oa;break;case 21:e=pa}Bc(b,xc(d[ga],d[e]),zc(a.slicerData),a._caption,a.zoomFactor()),$c(a,sb,b)}}function Zc(a){var b,c,e,f,g,h=a.bX,i=a.aX;a.iX()?(h||(h=Rc(a)),i.firstChild!==h&&(d.GC$(i).append(h),Vc(a)),b=a.zoomFactor(),c=a.dX,e=Math.floor(c/(a.itemHeight()+2*a.kX+a.hX)),f=a.nX,f.value(a.sX),f.Vo(a.jX*b,!1),f._v(c*b,!1),f.bw(0),f.cw(Math.ceil(q(a.gX)/a.columnCount())-e),f.dw(e),f.qB(1),f.rB(e-1),f.ew()):(i.firstChild===h&&(g=h.parentElement,g&&g.removeChild(h),Vc(a)),a.sX=0)}function $c(a,b,c){var e,f,g,h,i,j,k,l,m,n,o=a.zoomFactor();switch(b){case cb:h=a.xo,i=Ec(h),j=a.PW,e=(a.width()-2*j)*o-i.widthOffset,f=(a.height()-2*j)*o-i.heightOffset,g=h;break;case eb:k=a.KW,l=Ec(k),m=a.MW*Dc(a.style())*o-l.heightOffset,d.GC$(k).css(Q,m),e=a.RW*o,f=a.SW*o,g=a.WW;break;case sb:n=Ec(c),e=Oc(a,a.eX)*o-2*a.tX-n.widthOffset,f=a.itemHeight()*o-n.heightOffset,g=c}wc(g,[O,Q],[e,f])}function _c(a){var b=a.xo,c=".slicer",e="mouse",f="wheel",g="DOMMouseScroll";b&&(d.GC$(b).bind(e+"down"+c,function(b){a.AD(b)}).bind(e+"move"+c,function(b){a.BD(b)}).bind(e+"out"+c,function(b){a.uX()}),b.addEventListener(e+f,function(b){a.Rw(b)},!1),b.addEventListener(g,function(b){a.Rw(b)},!1),d.GC$(u).bind(e+"up"+c,function(b){hd(a,b)}).bind("keydown"+c,function(b){a.nm(b)}).bind("keyup"+c,function(b){a.om(b)}),u.addEventListener(e+f,function(b){a.Rw(b)},!1),u.addEventListener(g,function(b){a.Rw(b)},!1))}function ad(a){var b=a.xo;b&&d.GC$(b).unbind(".slicer")}function bd(a,b){var c,d,e,f=a.exclusiveDatas,g=a.pX,h=fd(a,b[Xa]),i=a.vX,j=a.wX;for(m.Bb(i,h)?m.Fb(i,h):i.push(h),c=0;c<q(f);c++)m.Bb(g,c)||j.push(c);for(d=0;d<q(i);d++)m.Bb(j,i[d])?m.Fb(j,i[d]):j.push(i[d]);if(0===q(j))for(e=0;e<q(a.gX);e++)j.push(e);a.xX=h}function cd(a,b){var c,d,e,f,g,h,i,j,k=a.fX,l=a.gX,m=l[a.xX];for(ed(a),c=k.indexOf(m),d=k.indexOf(b[Xa]),e=Math.min(c,d),f=Math.max(c,d),g=e;g<=f;g++)h=l.indexOf(k[g]),i=a.vX,j=a.wX,i.indexOf(h)===-1&&i.push(h),j.indexOf(h)===-1&&j.push(h)}function dd(a,b){ed(a);var c=fd(a,b[Xa]);Xc(a,c),a.vX.push(c),a.wX.push(c),a.xX=c}function ed(a){var b,c,d,e,f,g,h=a._items;if(h&&0!==q(h))for(b=a.rX,c=0,d=q(h);c<d;c++)e=h[c],f=a.gX.indexOf(h[c].innerHTML),g=m.Bb(b,f)?20:12,Yc(a,e,g)}function fd(a,b){var c,d,e=a.gX;if(e)for(c=0,d=q(e);c<d;c++)if(e[c]===b)return c;return-1}function gd(a,b){var c,d,e=a.pX,f=a.rX;for(c=0;c<q(b);c++)d=0,d|=a.yX?m.Bb(e,b[c])?2:4:2,d|=m.Bb(f,b[c])&&a.visuallyNoDataItems()?16:8,Yc(a,a.NW[b[c]],d)}function hd(a,b){var c=a.qX;a.CD(b),c=c?new Jb(c[Wa],c[Xa]):w,a.qX=w,jd(a,c)}function id(a,b){var c,e,f,g=a.WW;if(g)switch(c=a.style(),e=xc(c[ga],c[ha]),f=e&&e[T],f||(f=I),a.zX=f,b){case 0:a.AX=!1,wc(g,[L,N],[a.BX,f]);break;case 1:a.AX=!0,wc(g,[L,N],[a.CX,f]);break;case 2:d.GC$(g).css(N,a.DX)}}function jd(a,b){var c,d;if(b)switch(c=a.vX,b[Wa]){case 4:if(!a.WW)return;a.AX?id(a,1):id(a,0);break;case 5:d=a.gX.indexOf(b[Xa]),c&&m.Bb(c,d)?gd(a,c):Xc(a,d)}}function kd(a,b){if(b)switch(b[Wa]){case 4:if(!a.WW||!a.AX)return;id(a,2);break;case 5:var c=a.gX.indexOf(b[Xa]);Xc(a,c);break;case 6:a.cX.style.cursor=wa}}function ld(a,b){var c,d,e=a._items;if(e&&b!==w)for(c=0;c<q(e);c++)if(d=e[c],d.innerHTML.toLowerCase()===b.toLowerCase())return d;return w}function md(a,b){var c,d,e,f,g,h,i,j,k,l,m,n=nd(a,cb),o=w,p=w;if(!n)return w;if(c=b.pageX,d=b.pageY,e=nd(a,eb),f=nd(a,ib),g=nd(a,kb),h=nd(a,qb),e&&e.contains(c,d))o=f&&f.contains(c,d)?4:1;else if(g&&g.contains(c,d))if(i=a._items,h&&h.contains(c,d)&&i){for(j=void 0,j=0;j<q(i);j++)if(k=nd(a,sb,j),k&&k.contains(c,d)){o=5;for(l in a.NW)if(a.NW[l]===i[j]){p=a.gX[l];break}break}j>=q(i)&&(m=nd(a,sb,q(i)-1),o=d<m.y+a.itemHeight()*a.zoomFactor()?6:7)}else o=8;else o=0;return new Jb(o,p)}function nd(a,b,c){var e,f,g,h,i,j,k,l,m,n,o,p=b===cb?v:nd(a,cb),r=a.xo,s=a.EX,t=a.PW,u=a.zoomFactor(),x=a.style(),y=a.showHeader(),z=a.MW,A=a.RW,B=a.gX;switch(b){case cb:e=0,f=0,g=r.offsetWidth,h=r.offsetHeight;do e+=r.offsetLeft,f+=r.offsetTop,r=r.offsetParent;while(r);break;case eb:if(!p||!y)return w;e=p.x+s*u,f=p.y+s*u,g=a.width()*u,h=z*Dc(x)*u;break;case ib:if(!p||!y)return w;e=p.x+p.width-(s+t+A)*u,f=p.y+s+a.QW*Dc(x)*u,g=A*u,h=a.SW*u;break;case kb:if(!p)return w;i=s+t,j=0,k=z*Dc(x)+a.XW,e=p.x+i*u,f=p.y+j*u+(y?k*u:0),g=(a.width()-2*i)*u,h=p.height-2*j*u-(y?k*u:0);break;case qb:if(l=nd(a,kb),!l)return w;if(!a.iX())return l;e=l.x,f=l.y,g=l.width-a.jX*a.zoomFactor(),h=l.height;break;case sb:if(!p||0===q(B)||c>=q(B))return w;if(m=nd(a,qb),!m)return w;n=a.columnCount(),o=a.hX,g=(m.width-(n-2)*o*u)/n,h=a.itemHeight()*u+2*a.lX,e=m.x+c%n*(g+o*u),f=m.y+Math.floor(c/n)*(h+o*u)}return new d.Rect(e,f,g,h)}function od(a){return a>0}Gb=function(){function a(a,b,c){var d=this;d.name(a,!1),d.xo=w,d.PW=6,d.EX=1,d.VW=14,d.KW=w,d.MW=27,d.XW=5,d._caption=w,d.WW=w,d.RW=28,d.SW=25,d.TW=3,d.UW=0,d.AX=!1,d.QW=3,d.YW=w,d.ZW=w,d.$W=w,d._W=w,d.aX=w,d.cX=w,d.eX=0,d.dX=0,d.bX=w,d.FX=2,d._items=[],d.NW={},d.lX=1,d.kX=1,d.tX=6,d.hX=2,d.jX=17,d.sX=0,d.zX=I,d.DX="#F9E578",d.CX="red",d.BX="#B0ADB0",d.GX=!1,d.HX=!1,d.vX=[],d.wX=[],d.xX=0,d.fX=[],d.pX=[],d.rX=[],d.gX=[],d.oX=[],d.slicerData=b,d.columnName=c,d.data=b.getData(c),d.exclusiveDatas=b.getExclusiveData(c),d.slicerData.attachListener(d),d.IX()}return a.getDefaultStyle=function(){var a=function(a,b,c,d,e,f,g,h){var i={};return i[T]=a,i[U]=b,i[R]=c,i[Z]=d,i[$]=e,i[_]=f,i[aa]=g,i[ba]=h,i},b=function(a,b,c){var d={};return d[W]=a,d[X]=b,d[Y]=c,d},c=b(1,K,"#808080"),d=b(0,"",""),e=b(1,K,"#999999"),f=b(1,K,"#CCCCCC"),g=b(1,K,"#E0E0E0"),h=a("#F9E36F",J,x,e,e,e,e),i={};return i[ga]=a(I,J,x,c,c,c,c),i[ha]=a(I,J,"bold 11pt calibri",d,d,d,b(1,K,"#A6A6A6")),i[ia]=a("#BFBFBF",J,x,e,e,e,e),i[ja]=a("#D9D9D9",J,x,f,f,f,f),i[ka]=a(I,J,x,f,f,f,f),i[la]=a(I,"#959595",x,g,g,g,g),i[ma]=h,i[na]=h,i[oa]=h,i[pa]=h,i},a.prototype.IX=function(){var a=this;a.oX=Fc(a.data),a.gX=Fc(a.exclusiveDatas),a.xo=Gc(a),a.captionName(a.columnName,!1),_c(a),a.onFiltered(),vc(a,a.style()),Tc(a)},a.prototype.getDOMElement=function(){return this.xo},a.prototype.mX=function(a,b){var c,d,f=[];if(0!==b){for(c=e.Slicers.jf.quickSort(a),d=0;d<q(c);d++)f[d]=c[d].value;return 2===b&&f.reverse(),f}},a.prototype.iX=function(){var a=this,b=a.gX,c=a.showNoDataItems()?q(b):q(b)-q(a.rX),d=Math.ceil(c/a.columnCount())*(a.itemHeight()+2*(a.lX+a.kX)+a.hX);return d>a.dX},a.prototype.Bw=function(a,b){this.JX(a,b)},a.prototype.JX=function(a,b){var c=this,d=b.newValue;d!==c.sX&&d<=c.nX.cw()&&(c.sX=d,Zc(c),Wc(c))},a.prototype.onDataChanged=function(){Sc(this)},a.prototype.onRowsChanged=function(){Sc(this)},a.prototype.onColumnNameChanged=function(a,b){var c=this;c.columnName===a&&c._caption&&(c.columnName=b,c.captionName(b,!1),d.GC$(c._caption).text(b))},a.prototype.onColumnRemoved=function(a){var b=this;a===b.columnName&&(b.slicerData.doUnfilter(a),ad(b),d.GC$(b.xo).remove(),b.xo=w)},a.prototype.onFiltered=function(){var a=this,b=a.slicerData,c=a.columnName;a.fX=Qc(a),a.rX=b.getFilteredOutIndexes(c,2),a.pX=b.getFilteredOutIndexes(c,1),Tc(a)},a.prototype.xmb=function(a){var b,c=md(this,a);return!!c&&(b=c.mark,5===b||6===b||8===b||4===b)},a.prototype.AD=function(a){var b=this,c=md(b,a);c&&(8===c[Wa]&&(b.HX=!0),5===c[Wa]&&(b.wX=[],b.GX=!0,a.ctrlKey?bd(b,c):a.shiftKey?cd(b,c):dd(b,c)),gd(b,b.vX),(5===c[Wa]||6===c[Wa]||8===c[Wa]||4===c[Wa]&&b.AX)&&a.stopPropagation())},a.prototype.CD=function(a){var b,c,d,e=this,f=md(e,a);f&&(b=e.slicerData,c=e.vX,d=!1,4===f[Wa]?e.AX&&(b.doUnfilter(e.columnName),id(e,0),d=!0):c&&q(c)>0&&!e.yX&&!e.KX&&(b.doFilter(e.columnName,{exclusiveRowIndexes:e.wX}),d=!0),e.GX=!1,e.HX=!1,d&&(e.vX=[],e.wX=[]))},a.prototype.BD=function(a){var b,c,d,e,f,g,h,i,j,k,l,n,o,p=this,r=p.qX,s=r?new Jb(r[Wa],r[Xa]):w,t=md(p,a),u=p.gX,v=p.wX;if(t&&(!s||s[Wa]!==t[Wa]||5===s[Wa]&&s[Xa]!==t[Xa])){if(!p.GX)return p.qX=t,jd(p,s),void kd(p,t);if(5===t[Wa])if(b=u.indexOf(t[Xa]),c=p.rX,d=void 0,e=void 0,f=void 0,m.Bb(v,b)){for(g=p.fX,h=g.indexOf(u[p.xX]),i=g.indexOf(t[Xa]),j=Math.min(h,i),k=Math.max(h,i),l=[],n=q(v),d=0;d<n;d++)o=v[d],e=g.indexOf(u[o]),(e>k||e<j)&&l.push(o);for(n=q(l),d=0;d<n;d++)e=l[d],f=ld(p,u[e]),m.Bb(c,e)?Yc(p,f,20):Yc(p,f,12),m.Fb(v,e)}else f=ld(p,u[b]),m.Bb(c,b)?Yc(p,f,18):Yc(p,f,10),m.Sb(v,b);p.qX=t}},a.prototype.uX=function(){var a=this,b=a.qX,c=b?new Jb(b[Wa],b[Xa]):w;a.qX=w,jd(a,c)},a.prototype.Rw=function(a){var b=md(this,a);return 8===b[Wa]&&(a.stopPropagation(),!0)},a.prototype.nm=function(a){var b=this;17!==a.keyCode||b.yX||(b.yX=!0),16!==a.keyCode||b.KX||(b.KX=!0)},a.prototype.om=function(a){var b=this,c=b.slicerData;17===a.keyCode&&b.yX&&(b.yX=!1),16===a.keyCode&&b.KX&&(b.KX=!1),!b.yX&&!b.KX&&q(b.vX)>0&&(c.doFilter(b.columnName,{exclusiveRowIndexes:b.wX}),b.vX=[],b.wX=[])},a.prototype.onPropertyChanged=function(a,b,c){var d,e=this;e.slicerData.OW&&(d=e.slicerData.OW(),d.ITa.xVa(e,a,c))},a}(),b.ItemSlicer=Gb;function pd(a){return function(b,c){Tc(this),this.onPropertyChanged(a,b,c)}}function qd(a){return function(b,c){var d=this;d.fX=Qc(d),Tc(d),d.onPropertyChanged(a,b,c)}}function rd(a,b,c,e){return c||(c=pd(a)),d.Ul.Pl(a,b,c,e)}for(Hb=[[D],[O,180],[Q,210],["captionName",""],["columnCount",1,w,od],["itemHeight",21,w,od],["showHeader",!0],["sortState",1,qd("sortState")],["showNoDataItems",!0,qd("showNoDataItems")],["showNoDataItemsInLast",!0,qd("showNoDataItemsInLast")],["visuallyNoDataItems",!0,qd("visuallyNoDataItems")],[E,Gb.getDefaultStyle(),function(a,b){vc(this,a),Tc(this),this.onPropertyChanged("visuallyNoDataItems",a,b)}],["zoomFactor",1,w,od],["isLocked"],["disableResizingAndMoving"]],wb=0,Ib=q(Hb);wb<Ib;wb++)Gb.prototype[Hb[wb][0]]=rd(Hb[wb][0],Hb[wb][1],Hb[wb][2],Hb[wb][3]);Jb=function(){function a(a,b){var c=this;c[Wa]=a===v?w:a,c[Xa]=b===v?w:b}return a}();function sd(a,b,c){var d,e,f,g,h=a.kj,i=a.dataRange(),j=i.row+i.rowCount-1,k=i.col+i.colCount-1;for(d=i.row;d<=j;d++)for(e=[],b.push(e),f=i.col;f<=k;f++)e.push({value:h.getValue(d,f),text:h.getText(d,f)});for(f=0;f<i.colCount;f++)g=a.getColumnName(f)||"",c.push(g)}function td(a,b){var c,d,e=a.kj,f=a.sj.rowFilter();for(e.suspendPaint(),c=f&&f.Je||[],d=0;d<q(c);d++)ud(a,b,[c[d]]);e.resumePaint()}function ud(a,b,c){var d,e,f,g,h,i,j,k,l,n,o,p,r,s,t,u;for(d=0;d<c.length;d++){if(e=a.sj,f=a.xr,g=c[d]-e.range().col,h=e.dataRange(),i=g+h.col,j=a.columnNames[g],k=[],l={},m.Bb(f.Je,i)){for(n=[],o=h.row;o<h.row+h.rowCount;o++)f.E0a(o,i)||n.push(o);for(p=0;p<q(n);p++)r=n[p]-h.row,s=a.getExclusiveRowIndex(j,r),l[s]||(l[s]=!0,k.push(s))}else for(t=q(a.getExclusiveData(j)),u=0;u<t;u++)k.push(u);vd(wd(a,j),k)||(b.prototype.doFilter.call(a,j,{exclusiveRowIndexes:k}),a.onFiltered(),a.kj.$p())}}function vd(a,b){var c,d=e.Common.j.Fa;if(d(a)&&d(b))return!0;if(d(a)&&!d(b)||!d(a)&&d(b)||!d(a)&&!d(b)&&(!s(a)||!s(b))||a.length!==b.length)return!1;for(c=0;c<a.length;c++)if(a[c]!==b[c])return!1;return!0}function wd(a,b){var c,d=a.getExclusiveData(b),e=a.getFilteredOutIndexes(b,1),f=[];for(c=0;c<q(d);c++)m.Bb(e,c)&&f.push(c);return f}Kb=function(a){lc(b,a);function b(b){var c,d=this,e=[],f=[],g=b.rowFilter();return sd(b,e,f),d=a.call(this,e,f)||this,c=d,c.sj=b,c.kj=b.kj,c.xr=g,c.MX=w,td(c,z),g&&(g.filterHandler=function(a){ud(c,z,a.columns)}),d}return b.prototype.OW=function(){return this.kj},b.prototype.getTable=function(){return this.sj},b.prototype.doFilter=function(b,c,d){var e,f,g,i,j,k,l,m,n,o,p,r,s,t,u,v=this,w=v.kj,x=v.sj,y=v.xr,z=c.ranges,A=c.exclusiveRowIndexes,B=!!A;if(w.ITa.xVa(v,qa,{columnName:b,oldValue:v.getFilteredIndexes(b)}),e=v.getColumnIndex(b),!(e<0||!B&&!z))if(d)a.prototype.doFilter.call(this,b,c,d);else{if(f=B?v.getExclusiveData(b):v.getData(b),g=[],B)g=A;else for(i=v.ef(),j=v.ff(),i[e]||v.We(e),k=i[e],l=0;l<q(z);l++)for(m=z[l],n=v.Xe(k,m),o=n.start;o<=n.end;o++)g.push(j[e][o]);if(w.suspendPaint(),p=x.startColumn()+e,y&&y.removeFilterItems(p,!0),w.AR(),h.$V)for(r=0;r<q(g);r++)s=h.$V(2,0,f[g[r]]),s.useWildCards(!1),y&&y.addFilterItem(p,s);a.prototype.hf.call(this,b,c),t=[],u={},v.gf(b,t,u),y&&(y.filter(p,!0),y.onFilter(y.SX(0,[p]))),v.onFiltered(),w.resumePaint()}},b.prototype.doUnfilter=function(b){var c,d=this,e=d.kj,f=d.sj,g=d.getColumnIndex(b),h=d.xr;g!==-1&&(e.ITa.xVa(d,qa,{columnName:b,oldValue:d.getFilteredIndexes(b)}),c=f.startColumn()+g,h.removeFilterItems(c),a.prototype.if.call(this,b),h.unfilter(c,!0),h.onFilter(h.SX(0,[c])),d.onFiltered(),d.kj.$p())},b.prototype.refresh=function(){var a=this,b=[],c=[];sd(a.sj,b,c),a.bf(b,c)},b.prototype.onColumnNameChanged=function(b,c){var d=this;a.prototype.onColumnNameChanged.call(this,b,c),d.kj.slicers.all().forEach(function(a){a.columnName(c)})},b.prototype.onRowsAdded=function(b,c){var d,e,f=this,g=f.kj,h=f.sj,i=h.dataRange();for(a.prototype.onRowsAdded.call(this,b,c,!0),d=b;d<b+c;d++)for(e=0;e<q(f.columnNames);e++)f.data[d][e]={value:g.getValue(i.row+d,i.col+e),text:g.getText(i.row+d,i.col+e)};f.bf(f.data,f.columnNames),f.cf("onRowsChanged",b,c,!0)},b.prototype.onRowsRemoved=function(b,c){a.prototype.df.call(this,b,c,!1)},b.prototype.onColumnsAdded=function(a,b){var c,d,e,f,g=this,h=g.kj,i=g.sj,j=i.dataRange(),k=g.data,l=g.columnNames;for(c=0;c<q(k);c++)for(d=a;d<a+b;d++)k[c].splice(d,0,{
value:h.getValue(j.row+c,j.col+d),text:h.getText(j.row+c,j.col+d)});for(e=a;e<a+b;e++)f=i.getColumnName(e)||"",l.splice(e,0,f);g.bf(k,l)},b.prototype.onColumnsRemoved=function(b,c){var d,e,f=this,g=f.kj,h=f.sj.name(),i=f.columnNames,j=[];for(d=b;d<b+c;d++)j.push(i[d]);a.prototype.onColumnsRemoved.call(this,b,c),e=g&&g.slicers,j.forEach(function(a){e.all(h,a).forEach(function(a){e.remove(a.name())})})},b.prototype.onTableRemoved=function(a){var b,c,d,e=this;a&&a===e.sj&&(e.MX=[],b=e.kj,c=b.slicers,d=a.name(),c.all(d).forEach(function(a){e.MX.push(a),c.remove(a.name())}),c&&zd(c,e),e.Ze=[])},b.prototype.onTableAdded=function(a){var b,c=this,d=c.kj,e=d.slicers;e&&a&&a.Aq()&&yd(e,a.getSlicerData()),b=c.MX||[],b.forEach(function(a){var b=a.name(),c=a.ZX.sj.name(),d=a.columnName(),f=a.style();e.add(b,c,d,f)})},b}(z),b.TableSlicerData=Kb,g.Table&&d.GC$.extend(g.Table.prototype,{getSlicerData:function(){var a=this;return a.ZX||(a.ZX=new Kb(a)),a.ZX}});function xd(a){var b=a.sj,c=b.kj,d=b&&b.rowFilter();return!c.options.isProtected||d&&d.TX(c)}Lb=function(a){lc(b,a);function b(b,c,d){var e=a.call(this,b,c,d)||this;return e.isSelected(!1),e}return b.prototype.IX=function(){var b=this,c=b.slicerData;c&&c instanceof Kb&&(b.sheet(c.OW()),b.sj=c.getTable()),a.prototype.IX.call(this)},b.prototype.onColumnRemoved=function(a){var b,c=this,d=c.sheet(),e=c.sj,f=c.slicerData;a===c.columnName&&(b=f.getFilteredOutIndexes(a,1),0!==q(b)&&f.doUnfilter(a),d&&e&&d.wr.Fb(c.name()))},b.prototype.mX=function(a,b){var c,d,f,g,h,i,j=this,k=j.slicerData,l=k.getColumnIndex(j.columnName),m=j.sj,n=m.dataRange(),o=[];if(0===b)return a;for(c=[],d=k.OW(),f=0;f<q(a);f++)g=k.getRowIndexes(j.columnName,f),c.push(d.getValue(n.row+g[0],n.col+l));for(h=e.Slicers.jf.quickSort(c),i=0;i<q(h);i++)o[i]=a[h[i].index];return 2===b&&o.reverse(),o},b.prototype.Bw=function(b,c){var d,e,f=this,g=f.sheet(),h=f.slicer();for(h&&g.ER(h)||(c.newValue=c.oldValue),d=Fd(h),e=0;e<q(d);e++)a.prototype.JX.call(this,b,c)},b.prototype.AD=function(b){var c,d,e,f=this,g=f.sheet(),h=f.slicer();if(h&&g.ER(h)){if(c=md(f,b),!xd(f)&&(5===c[Wa]||6===c[Wa]||8===c[Wa]))return void b.stopPropagation();for(d=Fd(h),e=0;e<q(d);e++)a.prototype.AD.call(this,b)}},b.prototype.CD=function(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p=this,r=p.sheet(),s=p.slicer();if(s&&r.ER(s)&&xd(p)&&(b=md(p,a))){for(c=p.slicerData,d=p.vX,e=p.wX,f=c.getExclusiveData(p.columnName),g=!1,h=w,4===b[Wa]||5===b[Wa]&&q(e)===q(f)&&!p.yX&&!p.KX?(i=c.getFilteredIndexes(p.columnName),h=r.wu().execute({cmd:"unfilterSlicer",sheetName:r.name(),slicerData:c,columnName:p.columnName,value:i}),g=!0):d&&q(d)>0&&!p.yX&&!p.KX&&(j=c.getFilteredIndexes(p.columnName),k=e.concat(),h=r.wu().execute({cmd:"filterSlicer",sheetName:r.name(),slicerData:c,columnName:p.columnName,oldValue:j,newValue:k}),g=!0),l=Fd(s),m=0,n=q(l);m<n;m++)o=l[m],o.GX=!1,o.HX=!1,g&&(o.vX=[],o.wX=[]);return!!h}},b.prototype.BD=function(b){var c,d,e=this,f=e.sheet(),g=e.slicer();if(g&&!e.HX&&f.ER(g))for(c=Fd(g),d=0;d<q(c);d++)a.prototype.BD.call(this,b)},b.prototype.uX=function(){var b,c,d=this,e=d.sheet(),f=d.slicer();if(f&&e.ER(f))for(b=Fd(f),c=0;c<q(b);c++)a.prototype.uX.call(this)},b.prototype.Rw=function(b){var c,d,e,f,g=this;return!!a.prototype.Rw.call(this,b)||(c=g.sheet(),d=c.slicers,e=d&&d.UX(),f=g.nX,!!(e&&1===q(e)&&g.isSelected()&&g.iX()&&f)&&(f.sC(b),b.stopPropagation(),!0))},b.prototype.om=function(a){var b,c,d,e,f=this,g=f.slicerData,h=f.sheet(),i=f.wX;17===a.keyCode&&f.yX&&(f.yX=!1),16===a.keyCode&&f.KX&&(f.KX=!1),!f.yX&&!f.KX&&q(f.vX)>0&&(b=q(g.getExclusiveData(f.columnName)),q(i)===b?(c=g.getFilteredIndexes(f.columnName),h.wu().execute({cmd:"unfilterSlicer",sheetName:h.name(),slicerData:g,columnName:f.columnName,value:c})):(d=g.getFilteredIndexes(f.columnName),e=i.concat(),h.wu().execute({cmd:"filterSlicer",sheetName:h.name(),slicerData:g,columnName:f.columnName,oldValue:d,newValue:e})),f.vX=[],f.wX=[])},b}(Gb),Lb.prototype.isSelected=o("isSelected",!1),Lb.prototype.sheet=o("sheet"),Lb.prototype.slicer=o("slicer");function yd(a,b){var c,d,e=a.VX;if(b){for(c=b.getTable(),d=0;d<q(e);d++)if(e[d].getTable()===c)return;e.push(b)}}function zd(a,b){m.Fb(a.VX,b)}function Ad(a,b){var c,d,e,f=a.VX;for(c=0;c<q(f);c++)if(d=f[c],e=d.getTable(),d&&e&&e.tableName()===b)return d;return w}function Bd(a,b,c){var d;return b?l(Cd(a),b)&&(d=k().Exp_SlicerNameExist):d=k().Exp_SlicerNameInvalid,d&&c&&(c.error=Error(d)),!d}function Cd(a){var b,c,d,e=a.kj,f={},g=e&&e.parent&&e.parent.sheets;if(g)for(b=0;b<q(g);b++){c=g[b].slicers.XX();for(d in c)l(c,d)&&(f[d]=c[d])}return f}Mb=function(){function a(a){var b=this;b.kj=a,b.VX=[],b.WX={}}return a.prototype.add=function(a,b,c,d){var e,f=this,g=f.kj,h=g.parent,i=h.jga(b);return i&&i.getColumnIndexInTable(c)!==-1?(g.suspendPaint(),e=new gc(a,i,c),d&&e.style(d),f.Cz(e),g.wr.Sb(e),g.resumePaint(),e):w},a.prototype.Cz=function(a){var b,c,d=this,e=d.kj;if(a){if(b=a.name(),c={},!Bd(d,b,c))throw c.error;d.WX[b]=a,yd(d,a.YX()),e.ITa.xVa(d,ra,b)}},a.prototype.get=function(a){return a===w||a===v?w:this.WX[a]},a.prototype.remove=function(a){var b=this,c=b.kj;b.vV(a),c.wr.Fb(a)},a.prototype.vV=function(a){var b,c,d,e=this,f=e.kj,g=e.WX,h=e.get(a);if(h&&l(g,a)){for(b=h.YX(),c=Fd(h),d=0;d<q(c);d++)b.detachListener(c[d]);f.ITa.xVa(e,sa,h),delete e.WX[a]}},a.prototype.U3=function(a,b){var c,d=this,e=d.kj,f={};if(!Bd(d,b,f))throw f.error;c=d.WX[a],delete d.WX[a],d.WX[b]=c,e.wr.U3(a,b)},a.prototype.clear=function(){var a=this,b=a.kj;b.suspendPaint(),d.GC$.each(this.WX,function(b,c){a.remove(c.name())}),b.resumePaint()},a.prototype.all=function(a,b){var c=[];return d.GC$.each(this.WX,function(d,e){a&&(Ed(e).name()!==a||b&&e.columnName()!==b)||c.push(e)}),c},a.prototype.tTa=function(a,b,c){f.FloatingObjectCollection.prototype.tTa.call(this,a,b,c)},a.prototype.XX=function(){return this.WX},a.prototype.HS=function(a){var b,c,d,e=Cd(this);if(!l(e,a))return a;for(b=1,c=" ",d=a.split(c)[0];l(e,d+c+b);)b++;return d+c+b},a.prototype.UX=function(){var a,b,c,e=this,f=e.WX;if(!f||d.GC$.isEmptyObject(f))return[];a=[];for(b in f)l(f,b)&&(c=f[b],c&&c.isSelected()&&a.push(c));return a},a.prototype.W3=function(a){var b,c,d=this,e=d.UX();if(1!==q(e))return!1;if(b=Fd(e[0]),q(b)>0){for(c=0;c<q(b);c++)b[c].Rw(a);return!0}return!1},a.prototype.toJSON=function(){var a,b=this,c=[],e=b.WX;if(e&&!d.GC$.isEmptyObject(e))for(a in e)l(e,a)&&c.push(e[a].toJSON());return c},a.prototype.fromJSON=function(a){var b,c,d,e,f,g,h,i;if(a&&0!==q(a))for(b=this,c=b.kj,d=c.parent,b.WX={},e=0;e<q(a);e++)f=a[e].tableName,g=a[e].columnName,f&&g&&(h=d.jga(f),h&&h.getColumnIndexInTable(g)!==-1&&(i=new gc(a[e].name,h,g),i.sheet(c),i.fromJSON(a[e]),c.slicers.Cz(i),c.wr.Sb(i)))},a}(),b.SlicerCollection=Mb;function Dd(a,b,c){var d,e,f=a.ZX;f&&(d=f.OW(),e=d.slicers,e.all().forEach(function(d){d===a&&(e.U3(b,c),a.onPropertyChanged(D,c,b))}))}function Ed(a){return a.ZX.getTable()}function Fd(a){return a.$X}Nb="nameInFormula",Ob="captionName",Pb="columnCount",Qb="columnName",Rb="itemHeight",Sb="showHeader",Tb="sortState",Ub="disableResizingAndMoving",Vb="showNoDataItems",Wb="showNoDataItemsInLast",Xb="visuallyNoDataItems",Yb="isSelected",Zb="dynamicMove",$b="dynamicSize",_b="tableName",ac="Slicer",bc=100,cc=100,dc=192,ec=250,fc=[],gc=function(a){lc(b,a);function b(b,c,d){var e,f,g=a.call(this,b,bc,cc,dc,ec)||this,h=g;return h.typeName=ac,h.g3=ac,c&&(e=c.kj,h.sheet(e),f=Ad(e.slicers,c.tableName())),f||(f=c.getSlicerData()),h.ZX=f,h[Qb](d),h.$X=[],h._X=d,h.nameInFormula(ac+"_"+d),h[Ob](d),h[$b](!1),h[Zb](!1),g}return b.prototype.sourceName=function(){return this._X},b.prototype.cloneContent=function(){var a=this,b=a.sheet(),c=a.ZX,d=new Lb(a.name(),c,a.columnName());return d.width(a.width(),!1),d.height(a.height(),!1),d.captionName(a.captionName()),d.columnCount(a.columnCount()),d.itemHeight(a.itemHeight()),d.showHeader(a.showHeader()),d.sortState(a.sortState()),d.isLocked(a.isLocked()),d.disableResizingAndMoving(a.disableResizingAndMoving()),d.showNoDataItems(a.showNoDataItems()),d.showNoDataItemsInLast(a.showNoDataItemsInLast()),d.visuallyNoDataItems(a.visuallyNoDataItems()),d.style(a.style().toJSONInternal()),d.zoomFactor(b.zoom()),d.isSelected(a.isSelected()),d.slicer(a),d.sheet()!==b&&d.sheet(b),a.$X.push(d),d.getDOMElement()},b.prototype.YX=function(){return this.ZX},b.prototype.hga=function(){return!1},b.prototype.refresh=function(a){var b,c=this.$X;for(b=0;b<q(c);b++)Tc(c[b],a)},b.prototype.refreshContent=function(a){var b=this,c=b.sheet().zoom();c!==b.MP&&(b.MP=c,b.refresh(c))},b.prototype.clone=function(a){var c,d=this;return!d.sheet()&&a&&d.sheet(a,!1),c=new b(d.name(),Ed(d),d.columnName()),c.sheet(d.sheet(),!1),c.fromJSON(d.toJSON(),!1,d.ZX),c},b.prototype.fromJSON=function(b,c,e){var f,g,h,i,j,k,l,m,n,o,p;b&&(f=this,g=b.tableName,h=b[Qb],g&&h&&(a.prototype.fromJSON.call(this,b),i=f.sheet(),j=i.slicers,k=i.parent,l=e?e:Ad(j,g),l||(m=k.jga(g),l=m.getSlicerData(),yd(j,l)),f.ZX=l,f[Qb](h),n=b.x!==v?b.x:bc,o=b.y!==v?b.y:cc,f.position(new d.Point(n,o)),f[O](b[O]!==v?b[O]:dc,!1),f[Q](b[Q]!==v?b[Q]:ec,!1),b.sourceName!==v&&(f._X=b.sourceName),fc.forEach(function(a){var c=b[a];c!==v&&f[a](c,!1)}),b.style!==v&&(p=qc(),p.fromJSON(b.style),f.style(p,!1))))},b.prototype.toJSON=function(){var a,b,c,d=this,e={},f=["x","y",O,Q,Zb,$b,"isLocked","fixedPosition"];return f.forEach(function(a){var b=d[a]();d[a].isDefault(b)&&a!==Zb&&a!==$b||(e[a]=b)}),a=d._X,a&&(e.sourceName=a),d[E]()&&(e[E]=d[E]().toJSON()),b=d.ZX,c=b.getTable(),c[_b]&&(e[_b]=c[_b]()),fc.forEach(function(a){var b=d[a]();a===E||d[a].isDefault(b)||(e[a]=b)}),e},b.prototype.Wq=function(a){var b=this.sheet();b&&b.Wq(d.Events.SlicerChanged,a)},b}(f.FloatingObject),b.Slicer=gc;function Gd(a,b,c,d){return fc.push(a),o(a,b,function(b,d){c.call(this,a,b,d)},d)}function Hd(a){var b,c,d,e,f;if(!a)return!1;if(b=this,c=b.ZX,a===b.name())return!1;if(c&&(d=c.OW(),e=d&&d.slicers,e&&(f={},!Bd(e,a,f))))throw f.error;return!0}function Id(a,b,c){var d,e,f=this,g=f.$X||[];for(d=0;d<q(g);d++)g[d][a]&&("style"===a?g[d][a](b.toJSONInternal()):g[d][a](b));e=f.sheet(),e&&(e.ITa.xVa(f,a,c),f.Wq({sheet:e,sheetName:e.name(),slicer:f,propertyName:a}))}for(gc.prototype.nameInFormula=Gd(Nb,"",Id),gc.prototype.onPropertyChanged=Id,hc=[[D,"",function(a,b,c){Dd(this,c,b)},Hd],[Ob,"",Id],[Pb,1,Id,od],[Rb,21,Id,od],[Sb,!0,Id],[Tb,1,Id],[Ub,!1,function(a,b,c){var d,e=this;e.allowMove(!b,!1),e.allowResize(!b,!1),Id.call(e,Ub,b,c),d=e.sheet(),d&&d.$p()}],[Vb,!0,Id],[Wb,!0,Id],[Xb,!0,Id],[E,Eb.light1(),Id],[Qb,"",function(a,b,c){var d,e=this;e._X=b,e.captionName(b,!1),d=e.sheet(),d.ITa.xVa(e,a,c)}]],wb=0,Ib=q(hc);wb<Ib;wb++)gc.prototype[hc[wb][0]]=Gd(hc[wb][0],hc[wb][1],hc[wb][2],hc[wb][3]);t(d.Worksheet.prototype,{t4:function(a,b,c,d,e){var f,g,h,i,j,k,l,m,n;if(e===v&&(e=3),f=this,g=f.tables){for(h=0;h<c;h++)for(i=0;i<d;i++)j=h+a,k=i+b,l=g.find(j,k),3===e&&l&&l.Aq()&&(m=l.dataRange(),m.contains(j,k)&&(n=l.getSlicerData(),n&&n.onDataChanged([{columnName:l.getColumnName(k-m.col),row:j-m.row,data:{value:f.getValue(j,k),text:f.getText(j,k)}}])));f.ITa.xVa(null,ta,{row:a,col:b,rowCount:c,colCount:d,sheetArea:e})}}}),ic={priority:900,init:function(){var a=this;a.slicers=new Mb(a)},setHost:function(){var a=this;a.bind("tableRemoved",function(b,c){var d,e,f,g=c.tables;for(d=0;d<q(g);d++)e=a.slicers,f=Ad(e,g[d].name()),f&&zd(e,f)})},dispose:function(a){a.clearCache!==!1&&this.unbind("tableRemoved")},toJson:function(a,b){var c,d=b&&b.ignoreStyle;d||(c=this.slicers.toJSON(),c.length>0&&(a.slicers=c))},preProcessMouseWheel:function(a){var b=this.slicers;b&&b.W3(a.e)&&(a.r=!0)},onGroupChanged:function(a){this.slicers.tTa(a.start,a.end,a.isRow)}},d.Worksheet.$n("slicer",ic),jc=function(a){var b="unfilterSlicerByKey",c="filterSlicer",e="unfilterSlicer",f="changeSlicerProperty";a.register(b,d.Commands[b],67,!1,!1,!0,!1),a.register(c,d.Commands[c]),a.register(e,d.Commands[e]),a.register(f,d.Commands[f])},kc={init:function(){jc(this.commandManager())},fromJson:function(a,b,c){var d,e,f,g,h,i,j=c&&c.ignoreStyle;if(!j)for(d=this,e=0,f=d.getSheetCount();e<f;e++)g=d.getSheet(e),h=a.sheets&&a.sheets[g.name()],i=h&&h.slicers,i&&g.slicers.fromJSON(i)}},d.Workbook.$n("slicer",kc),d.GC$.extend(d.lUa.prototype,{xVa:function(a,b,c){var d,e=this.zTa;e&&(d=e.EUa,d||(d=e.EUa=[]),d.push({slicerItem:a,name:b,value:c}))},yVa:function(a){var b,c,d,e,f,g,h,i,j;if(a)for(b=a.length-1;b>=0;b--)if(c=a[b],d=c.slicerItem,e=c.name,f=c.value,e===qa)g=f.columnName,h=f.oldValue,d.doFilter(g,{exclusiveRowIndexes:h});else if(e===ra)d.vV(f);else if(e===sa){if(!f)return;d.Cz(f),i=f.ZX.MX,i&&(j=i.indexOf(f),j>=0&&i.splice(j,1))}else if(e===ta){if(!f)return;this.kj.t4(f.row,f.col,f.rowCount,f.colCount,f.sheetArea)}else d[e](f)}}),d.lUa.$n("SLICER_PROPERTYCHANGE",{priority:5500,undo:function(a){var b=a.EUa;b&&this.yVa(b)}})},Common:function(a,b){a.exports=GC.Spread},ConditionalFormatting:function(a,b){a.exports=GC.Spread.Sheets.ConditionalFormatting},Core:function(a,b){a.exports=GC.Spread.Sheets},FloatingObject:function(a,b){a.exports=GC.Spread.Sheets.FloatingObjects},Tables:function(a,b){a.exports=GC.Spread.Sheets.Tables}});